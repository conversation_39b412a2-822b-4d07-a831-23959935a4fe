{"id": "e9f31283e7c44a288451e772ca2a6528", "label": "待整理", "attrs": {}, "children": [{"id": "cuuo3rpkf540", "label": "xpath表达式语法", "attrs": {}, "type": "2", "children": []}, {"id": "cuuoceenj740", "label": "xpath表达式和模型定义合法性", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pgy4p39k0", "label": "分支主题", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pef2j7js0", "label": "容器过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfup9leo0", "label": "叶子过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfxk9n1s0", "label": "内容过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pg37c3rk0", "label": "三种过滤组合嵌套", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfup9leo0", "label": "选择过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pef2j7js0", "label": "包含节点过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfup9leo0", "label": "选择节点过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfxk9n1s0", "label": "匹配过滤", "attrs": {}, "type": "2", "children": []}]}