{"id": "8952ac0a9eb6473c", "label": "可靠性交互分析", "children": [{"label": "组件运行环境（OS)", "id": "ac1b2c9991bb1c95", "children": [{"label": "进程", "id": "69ee231ac97bbc86", "children": [{"id": "MCR_1", "label": "启动-依赖进程未启动", "type": 2, "attrs": {"description": "启动依赖进程未启动", "steps": [{"step": "1、分析被测试进程的依赖进程有哪些，不启动这些进程，或直接将进程kill掉；"}, {"step": "2、启动被验证进程，有预期结果1；", "expect": "1、被测试进程无法启动，有清晰的提示和日志记录；"}]}}, {"id": "MCR_2", "label": "启动-OS资源不足导致新进程无法启动", "type": 2, "attrs": {"description": "OS资源不足导致新进程无法启动", "steps": [{"step": "1、通过故障注入工具在运行start.sh文件前中将内存资源（大页内存、操作系统内存）可用内存资源小于组件启动所需要的内存资源；"}, {"step": "2、启动被验证进程，有预期结果1；", "expect": "1、被测试进程无法启动，内存资源不足有清晰的提示和日志记录；"}]}}, {"id": "MCR_4", "label": "运行中-进程死循环故障", "type": 2, "attrs": {"description": "进程死循环故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入组件进程死循环故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_5", "label": "运行中-进程D状态故障", "type": 2, "attrs": {"description": "进程D状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入组件进程D状态故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_6", "label": "运行中-进程Z状态故障", "type": 2, "attrs": {"description": "进程Z状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入组件进程Z状态故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_7", "label": "运行中-进程挂死状态故障", "type": 2, "attrs": {"description": "进程挂死状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入进程挂起故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_8", "label": "运行中-进程僵尸状态故障", "type": 2, "attrs": {"description": "进程僵尸状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入进程僵尸状态故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_9", "label": "运行中-进程句柄耗尽故障", "type": 2, "attrs": {"description": "进程句柄耗尽故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入进程句柄耗尽故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_12", "label": "退出-运行依赖进程非正常退出", "type": 2, "attrs": {"description": "运行依赖进程非正常退出", "steps": [{"step": "1、通过故障注入工具，或手工kill方式强行将被测试进程依赖的进程退出，有预期结果1；", "expect": "1、检测到依赖进程退出事件，并记录日志。根据架构规则，被测试组件清理已经申请的资源，进程退出。"}]}}, {"id": "MCR_13", "label": "退出-运行依赖进程正常退出", "type": 2, "attrs": {"description": "运行依赖进程正常退出", "steps": [{"step": "1、通过故障注入工具，或手工方式将被测试进程依赖进程退出，有预期结果1；", "expect": "1、检测进程退出事件，并记录日志。根据架构规则，被测试组件清理已经申请的资源，进程退出。"}]}}, {"id": "MCR_15", "label": "重启-自动重启(检测或定时)", "type": 2, "attrs": {"description": "自动重启(检测或定时)", "steps": [{"step": "1、分析被测试进程自动重启条件，用故障注入工具模拟，向故障进程发送重启信号，如task任务堆积、达到自愈条件等，触发进程自动重启，有预期结果1；", "expect": "1、被测试进程可以正常启动，业务正常，进程重启前占用的资源释放无残留，日志记录进程重启原因；"}]}}, {"id": "MCR_16", "label": "重启-手工重启", "type": 2, "attrs": {"description": "手工重启", "steps": [{"step": "1、被测试进程正常运行，通过手工restart该进程，有预期结果1；", "expect": "1、被测试经常可以正常启动，业务正常，进程重启前占用的资源释放无残留，日志记录进程重启原因；"}]}}, {"id": "MCR_17", "label": "重启-升级重启", "type": 2, "attrs": {"description": "升级重启", "steps": [{"step": "1、被测试进程正常运行，升级软件版本或补丁，升级重启后，有预期结果1；", "expect": "1、被测试经常可以正常启动，业务正常，进程重启前占用的资源释放无残留，日志记录进程重启原因；"}]}}, {"id": "MCR_18", "label": "Kill信号量-Kill信号遍历测试", "type": 2, "attrs": {"description": "信号量遍历测试", "steps": [{"step": "1、进程已经启动，按照规格模式，各种模式逐一进行kill测试；", "expect": "1、可以检测到信号被kill事件并记录日志；根据架构规则，触发组件线程重新运行或进程退出。"}]}}]}, {"label": "线程", "id": "3ae03a2a571acd43", "children": [{"id": "MCR_20", "label": "线程异常退出故障", "type": 2, "attrs": {"description": "线程异常退出故障", "steps": [{"step": "1、通过故障注入工具，kill线程，有预期结果1", "expect": "1、线程正常退出，日志记录线程退出原因。根据架构规则，被测试组件清理已经申请的资源，进程退出。"}]}}, {"id": "MCR_21", "label": "线程死循环故障", "type": 2, "attrs": {"description": "线程死循环故障", "steps": [{"step": "1、通过故障注入工具 ，注入线程死循环故障，有预期结果1", "expect": "1、可以检测到线程死循环并记录日志；根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_22", "label": "线程挂死故障", "type": 2, "attrs": {"description": "线程挂死故障", "steps": [{"step": "1、通过故障注入工具 ，注入5s线程挂死故障，有预期结果1；", "expect": "1、可以检测到线程挂死并记录日志；根据架构规则，触发组件线程重新运行或进程退出。；"}, {"step": "2、通过故障注入工具 ，注入3分钟线程挂死故障，有预期结果1；", "expect": "1、可以检测到线程挂死并记录日志；根据架构规则，触发组件线程重新运行或进程退出。；"}]}}, {"id": "MCR_23", "label": "线程间死锁", "type": 2, "attrs": {"description": "线程间死锁", "steps": [{"step": "1、通过故障注入工具 ，注入线程间死锁；", "expect": "1、可以检测到线程死锁并记录日志；根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_24", "label": "线程句柄耗尽故障", "type": 2, "attrs": {"description": "线程句柄耗尽故障", "steps": [{"step": "1、通过故障注入工具 ，耗尽进程内的线程句柄，创建新线程返回失败，有预期结果1；", "expect": "1、检测到线程句柄耗尽并记录日志，根据架构规则，触发组件线程重新运行或进程退出。"}]}}]}, {"label": "信号量", "id": "fd996550f28859e9", "children": [{"id": "MCR_26", "label": "信号量申请失败", "type": 2, "attrs": {"description": "信号量申请失败", "steps": [{"step": "1、通过故障注入工具，注入信号量值为0，线程申请信号量，有预期结果1", "expect": "1、检测到信号量申请失败并记录日志，根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_27", "label": "信号量死锁", "type": 2, "attrs": {"description": "信号量死锁", "steps": [{"step": "1、通过故障注入工具，注入使线程A占用资源x去申请y和线程B占用资源y去申请x，产生信号量死锁，有预期结果1", "expect": "1、检测到信号量死锁并记录日志，根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_28", "label": "信号量海量中断", "type": 2, "attrs": {"description": "信号量海量中断", "steps": [{"step": "暂时无需测试", "expect": "暂时无需测试"}]}}]}, {"label": "内存", "id": "ca97042293389e12", "children": [{"id": "MCR_30", "label": "内存泄漏检测", "type": 2, "attrs": {"description": "内存泄漏检测", "steps": [{"step": "1、组件自身有内存管理机制，通过故障注入工具注入使组件内存泄漏，验证内存监测机制是否生效", "expect": "1、检测到内存泄漏并记录日志。根据架构原则，组件内存限制机制生效或重启。"}, {"step": "2、组件利用公共的内存泄漏检查机制，通过故障注入工具注入使组件内存泄漏，验证内存监测机制是否生效"}]}}, {"id": "MCR_31", "label": "组件申请内存失败", "type": 2, "attrs": {"description": "组件申请内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件内存限制机制生效或重启。"}]}}, {"id": "MCR_32", "label": "组件申请共享内存失败", "type": 2, "attrs": {"description": "组件申请共享内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请共享内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件重启。"}]}}, {"id": "MCR_33", "label": "组件申请私有内存失败", "type": 2, "attrs": {"description": "组件申请私有内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请私有内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件重启。"}]}}, {"id": "MCR_35", "label": "组件申请大页内存失败", "type": 2, "attrs": {"description": "组件申请大页内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请大页内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件重启。"}]}}, {"id": "MCR_36", "label": "表项耗尽（申请资源失败）", "type": 2, "attrs": {"description": "表项耗尽（申请资源失败）", "steps": [{"step": "1、利用故障注入工具接管表项申请、释放的接口，组件申请表项失败，有预期结果1；", "expect": "1、检测到表项申请资源失败并记录日志。根据架构原则，组件重启。"}]}}]}, {"label": "CPU", "id": "c646ac61543d152d", "children": [{"id": "MCR_38", "label": "CPU过载", "type": 2, "attrs": {"description": "CPU过载", "steps": [{"step": "1、利用故障注入工具注入故障，CPU使用率在<90%波动，运行组件测试用例，对比前后结果", "expect": "1、组件业务运行正常，测试用例能正常通过。"}]}}, {"id": "MCR_39", "label": "核失效", "type": 2, "attrs": {"description": "核失效", "steps": [{"step": "1、未绑定具体CPU核的情况下，通过故障注入工具，注入组件\\进程\\线程使用的CPU核失效，有预期结果1；", "expect": "1、能检测CPU核失效并记录日志，业务正常运行；"}, {"step": "2、绑定具体CPU核的情况下，通过故障注入工具，注入组件\\进程\\线程使用的CPU核失效，有预期结果2；", "expect": "2、能检测CPU核失效并记录日志，根据架构原理通知客户选择相关操作；"}]}}, {"id": "MCR_40", "label": "CPU挂死", "type": 2, "attrs": {"description": "CPU挂死", "steps": [{"step": "1、通过故障注入工具注入cpu单个核挂死5s以内；", "expect": "1、能检测到CPU挂死故障，并记录日志；"}, {"step": "2、通过故障注入工具注入cpu部分核挂死超过3分钟；", "expect": "2、能检测CPU挂死并记录日志，根据架构原理通知客户选择相关操作；"}, {"step": ""}]}}, {"id": "MCR_41", "label": "CPU正弦曲线式过载", "type": 2, "attrs": {"description": "CPU正弦曲线式过载", "steps": [{"step": "1、注入故障，CPU使用率在<90%波动，运行组件测试用例，对比前后结果", "expect": "1、组件业务运行正常，测试用例能正常通过。"}]}}]}, {"label": "定时器", "id": "850d2622f016e8cd", "children": [{"id": "MCR_44", "label": "定时器申请失败", "type": 2, "attrs": {"description": "定时器申请失败", "steps": [{"step": "1、运行xx组件业务，组件进程申请定时器，通过软件故障注入组件申请不到定时器，有预期结果1", "expect": "1、能检测到定时器申请失败，并记录日志；"}]}}, {"id": "MCR_45", "label": "定时器超时未触发", "type": 2, "attrs": {"description": "定时器超时未触发", "steps": [{"step": "1、运行xx组件业务，软件故障注入组件进程的某个定时器超时未触发（未发送定时器超时消息、定时器超时消息丢失、回调函数未被执行），有预期结果1。", "expect": "1、能检测到定时器超时未触发，并记录日志；"}]}}]}, {"label": "时间", "id": "36dfede5f4ec53e8", "children": [{"id": "MCR_47", "label": "夏令时跳变", "type": 2, "attrs": {"description": "夏令时跳变", "steps": [{"step": "1、夏令时跳变，有预期结果1", "expect": "1、能检测到夏令时跳变，并记录日志；组件能定时同步时间 。"}]}}, {"id": "MCR_48", "label": "系统时间跳变", "type": 2, "attrs": {"description": "系统时间跳变", "steps": [{"step": "1、系统时间跳变，有预期结果1", "expect": "1、能检测到系统时间跳变，并记录日志；组件能定时同步时间 。"}]}}]}, {"label": "SELinux", "id": "910f54042cd62998", "children": []}, {"label": "文件", "id": "6049f4610da25400", "children": [{"id": "MCR_51", "label": "文件句柄耗尽", "type": 2, "attrs": {"description": "文件句柄耗尽", "steps": [{"step": "1、通过故障注入工具注入文件句柄耗尽故障，可以分别构造文件句柄泄漏80%、90%和100%情况，有预期结果1；", "expect": "1、能检测到文件句柄耗尽并记录日志，可以通过命令行查询文件句柄的占用率，设计告警阀值，可以是85%，超过阀值时会产生告警,80%无告警、90%告警、100%开始进行自愈如重启进程，可以设置超时回收机制，超时后自动回收文件句柄资源；"}, {"step": "2、恢复故障，再次触发流程；"}]}}, {"id": "MCR_52", "label": "文件不可读", "type": 2, "attrs": {"description": "文件不可读", "steps": [{"step": "1、通过故障注入工具注入xx文件不可读的故障，xx业务读xx文件，有预期结果1；", "expect": "1、文件读取失败，产生xx文件故障的告警，并记录读取失败的记录；"}, {"step": "2、通过故障注入工具撤销文件不可读的故障，xx业务读xx文件，有预期结果2；", "expect": "2、故障撤销后，可以正常读取文件，业务正常，xx文件不可用的告警恢复，并记录日志；"}]}}, {"id": "MCR_53", "label": "文件不可写", "type": 2, "attrs": {"description": "文件不可写", "steps": [{"step": "1、通过故障注入工具注入xx文件不可写的故障，xx业务写xx文件，有预期结果1；", "expect": "1、文件写失败，产生xx文件故障的告警，并记录写失败的记录；"}, {"step": "2、通过故障注入工具撤销文件不可写的故障，xx业务写xx文件，有预期结果2；", "expect": "2、故障撤销后，可以正常写文件，业务正常，xx文件不可用的告警恢复，并记录日志；"}]}}, {"id": "MCR_54", "label": "读数据慢或超时", "type": 2, "attrs": {"description": "读数据慢或超时", "steps": [{"step": "1、xx业务开始读文件，在数据传输过程中（未读完），通过故障注入工具，注入回复消息慢或不回应，有预期结果1；", "expect": "1、读的慢或超时，会有超时重传机制，不能挂死，记录日志和告警。其他业务不受影响，不会有内存泄漏或资源泄漏问题；"}, {"step": "2、撤销该故障注入，重新读该文件，有预期结果2；", "expect": "2、文件可以正常读取；"}]}}, {"id": "MCR_55", "label": "写数据慢或超时", "type": 2, "attrs": {"description": "写数据慢或超时", "steps": [{"step": "1、xx业务开始写文件，在数据传输过程中（未写完），通过故障注入工具，注入回复消息慢或不回应，有预期结果1；", "expect": "1、写的慢或超时，会有超时重传机制，不能挂死，记录日志和告警。其他业务不受影响，不会有内存泄漏或资源泄漏问题；"}, {"step": "2、撤销该故障注入，重新写该文件，有预期结果2；", "expect": "2、文件可以正常写；"}]}}, {"id": "MCR_56", "label": "重命名失败", "type": 2, "attrs": {"description": "重命名失败", "steps": [{"step": "1、通过故障注入工具注入，或将该文件打开，使其该文件不能重命名，对文件进程重命名操作，有预期结果1；", "expect": "1、文件重命名失败，有正确的提升信息和日志记录，无内存等资源泄漏或其他异常；"}, {"step": "2、撤销故障注入，或将该文件关闭，对文件进行重命名，有预期结果2；", "expect": "2、重命名成功；"}]}}, {"id": "MCR_57", "label": "文件丢失", "type": 2, "attrs": {"description": "文件丢失", "steps": [{"step": "1、通过故障注入工具 ，将文件删除，模拟文件丢失，组件运行，需要读写该文件，有预期结果1；", "expect": "1、对启动文件等关键文件有备份机制，主文件丢失无法访问会自动切换到备份文件，对没有备份机制的文件丢失，业务能检测出文件不存在，产生告警和日志，不对已开展的业务产生影响；"}]}}, {"id": "MCR_58", "label": "文件只读", "type": 2, "attrs": {"description": "文件只读", "steps": [{"step": "1、设置文件为只读，被测试组件对文件进行读写，有预期结果1；", "expect": "1、文件可以读成功，不能写，有正确的错误提示，组件能正确处理写失败事件，已有业务运行正常；"}]}}, {"id": "MCR_59", "label": "文件访问死锁", "type": 2, "attrs": {"description": "文件访问死锁", "steps": [{"step": "1、通过故障注入工具，注入文件/目录死锁访问冲突，有预期结果1；", "expect": "1、能够检测出死锁的位置和原因并进行告警和日志记录，系统对访问文件产生死锁的进程进行撤销或重启；"}, {"step": "2、撤销故障注入，解除死锁，有预期结果2；", "expect": "2、文件可以正常访问；"}]}}, {"id": "MCR_60", "label": "文件不可删除", "type": 2, "attrs": {"description": "文件不可删除", "steps": [{"step": "1、通过故障注入工具，将指定文件/目录设置成不可删除，对文件进行删除操作，有预期结果1；", "expect": "1、文件不能删除，有文件删除失败日志记录；"}, {"step": "2、撤销故障注入，有预期结果2", "expect": "2、文件可以正常删除；"}]}}, {"id": "MCR_61", "label": "文件内容随机变异", "type": 2, "attrs": {"description": "文件内容随机变异", "steps": [{"step": "1、通过故障注入工具，随机生成文件内容，并对文件进行 读、写、保存，有预期结果1；", "expect": "1、文件可以正常读、写、保存，业务正常；"}]}}, {"id": "MCR_62", "label": "0字节文件", "type": 2, "attrs": {"description": "0字节文件", "steps": [{"step": "1、通过故障注入工具，注入指定文件为0字节，对该文件进行操作，如打开、读取等，有预期结果1", "expect": "1、可以正常打开和读，进程不产生异常退出等异常，可以正常写入内容；"}]}}, {"id": "MCR_63", "label": "超大文件", "type": 2, "attrs": {"description": "超大文件", "steps": [{"step": "1、获取硬盘或文件存放空间大小，及剩余空间统计，通过故障注入工具模拟创建大文件，占满空间，对文件进行打开、读、写等操作，有预期结果1；", "expect": "1、文件在空间范围内可以创建、打开、读、写，不能出现异常，文件占用率超过百分比（参考版本的规格）进行告警和日志记录；"}]}}, {"id": "MCR_64", "label": "存储满", "type": 2, "attrs": {"description": "存储满", "steps": [{"step": "1、通过故障注入工具，创建多个文件，使存储空间占满，在存储空间满的情况下，创建新的文件及对已经存在的文件进行读、写等操作，有预期结果1", "expect": "1、存储空间满，会产生告警和日志，创建新的文件失败，对已存在文件操作正常，对已有文件写的内容超过存储空间时，写失败，有正确的告警和日志记录；"}]}}]}]}, {"label": "组件接口", "id": "03fe36c5418fb7dc", "children": [{"label": "通信接口", "id": "e56f99bbde3d9c98", "children": [{"id": "MCR_67", "label": "接收端缓存满", "type": 2, "attrs": {"description": "接收端缓存满", "steps": [{"step": "1、使用故障注入工具，朝接收端不断发送消息，使端口缓存占满，有预期结果1；", "expect": "1、端口缓存满，可以通过命令行查看缓存使用情况，会产生缓存满的告警和日志，会发送反压帧给上游；"}, {"step": "2、使用故障注入工具，减少消息发送，是端口缓存不断释放，有预期结果2；", "expect": "2、在消息变少后，缓存可以得到相应的释放，缓存告警恢复，停止发送后，缓存全部释放，没有内存泄漏；"}]}}, {"id": "MCR_68", "label": "发送端收到反压", "type": 2, "attrs": {"description": "发送端收到反压", "steps": [{"step": "1、使用故障注入工具，触发发送端不断发送消息，使接受端口缓存占满并发送反压帧给发送端，给发送有预期结果1；", "expect": "1、接收端口收到反压帧可以正确处理，减少流量发送，直至接收端不再发送反压帧；"}, {"step": ""}]}}]}, {"label": "消息接口", "id": "250a6c463fce7ffe", "children": [{"label": "MESH", "id": "cec69c472c3be1d9", "children": [{"id": "MCR_71", "label": "MESH-消息格式不符合约定", "type": 2, "attrs": {"description": "消息格式不符合约定", "steps": [{"step": "1、分析MESH消息格式，通过故障注入工具发送不符合约定的消息，比如字段大小、多少不一致等，有预期结果1；", "expect": "1、接收端能够检测消息格式的正确性，不符合格式的进行丢弃，有手段查看丢弃计数，有告警和日志记录报文丢弃及丢弃的原因。"}]}}, {"id": "MCR_72", "label": "MESH-发失败", "type": 2, "attrs": {"description": "发失败", "steps": [{"step": "1、通过故障注入，注入消息发送失败故障，有预期结果1；", "expect": "1、消息发送失败的场景下，有重传机制，消息会重新发送，有日志和统计，记录消息发送失败事件。"}]}}, {"id": "MCR_73", "label": "MESH-发超时", "type": 2, "attrs": {"description": "发超时", "steps": [{"step": "1、通过故障注入，注入消息发送超时（发送队列堆积或函数处理慢、消息未及时得到调度等），有预期结果1；", "expect": "1、可以设置消息外发超时时间和重发次数，对发送超时的消息，到达超时时间会重新发送，有命令行可以查看超时消息，有日志记录消息发送超时事件；"}]}}, {"id": "MCR_74", "label": "MESH-发流控", "type": 2, "attrs": {"description": "发流控", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_75", "label": "MESH-收过载", "type": 2, "attrs": {"description": "收过载", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制，日志记录过载事件；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_76", "label": "MESH-收错包", "type": 2, "attrs": {"description": "收错包", "steps": [{"step": "1、组件间通过X消息通信，通过故障注入工具 ，注入消息错包，有预期结果1", "expect": "1、错包丢弃，丢包可以查看，记录日志；"}]}}, {"id": "MCR_77", "label": "MESH-收乱序", "type": 2, "attrs": {"description": "收乱序", "steps": [{"step": "1、组件间通过消息通信，通过故障注入工具 ，注入消息乱序，有预期结果1", "expect": "1、接收乱序的报文，可以进行保序，乱序报文个数可以查看"}]}}, {"id": "MCR_78", "label": "MESH-收兼容的消息", "type": 2, "attrs": {"description": "收兼容的消息", "steps": [{"step": "1、收到消息版本号的兼容的消息，有预期结果1；", "expect": "1、可以正确处理版本兼容的消息；"}]}}]}, {"label": "PUB/SUB", "id": "d512e0e74ac6ee22", "children": [{"id": "MCR_80", "label": "PUB/SUB-消息格式不符合约定", "type": 2, "attrs": {"description": "消息格式不符合约定", "steps": [{"step": "1、分析MESH消息格式，通过故障注入工具发送不符合约定的消息，比如字段大小、多少不一致等，有预期结果1；", "expect": "1、接收端能够检测消息格式的正确性，不符合格式的进行丢弃，有手段查看丢弃计数，有告警和日志记录报文丢弃及丢弃的原因。"}]}}, {"id": "MCR_81", "label": "PUB/SUB-收过载", "type": 2, "attrs": {"description": "收过载", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制，日志记录过载事件；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_82", "label": "PUB/SUB-发失败", "type": 2, "attrs": {"description": "发失败", "steps": [{"step": "1、通过故障注入，注入消息发送失败故障，有预期结果1；", "expect": "1、消息发送失败的场景下，有重传机制，消息会重新发送，有日志和统计，记录消息发送失败事件。"}]}}, {"id": "MCR_83", "label": "PUB/SUB-收错包", "type": 2, "attrs": {"description": "收错包", "steps": [{"step": "1、组件间通过X消息通信，通过故障注入工具 ，注入消息错包，有预期结果1", "expect": "1、错包丢弃，丢包可以查看，记录日志；"}]}}, {"id": "MCR_84", "label": "PUB/SUB-收兼容的消息", "type": 2, "attrs": {"description": "收兼容的消息", "steps": [{"step": "1、收到消息版本号的兼容的消息，有预期结果1；", "expect": "1、可以正确处理版本兼容的消息；"}]}}]}, {"label": "CROS(RPC)", "id": "b0b573de3f9e3e79", "children": [{"id": "MCR_86", "label": "CROS(RPC)-消息格式不符合约定", "type": 2, "attrs": {"description": "消息格式不符合约定", "steps": [{"step": "1、分析MESH消息格式，通过故障注入工具发送不符合约定的消息，比如字段大小、多少不一致等，有预期结果1；", "expect": "1、接收端能够检测消息格式的正确性，不符合格式的进行丢弃，有手段查看丢弃计数，有告警和日志记录报文丢弃及丢弃的原因。"}]}}, {"id": "MCR_87", "label": "CROS(RPC)-发失败", "type": 2, "attrs": {"description": "发失败", "steps": [{"step": "1、通过故障注入，注入消息发送失败故障，有预期结果1；", "expect": "1、消息发送失败的场景下，有重传机制，消息会重新发送，有日志和统计，记录消息发送失败事件。"}]}}, {"id": "MCR_88", "label": "CROS(RPC)-发超时", "type": 2, "attrs": {"description": "发超时", "steps": [{"step": "1、通过故障注入，注入消息发送超时（发送队列堆积或函数处理慢、消息未及时得到调度等），有预期结果1；", "expect": "1、可以设置消息外发超时时间和重发次数，对发送超时的消息，到达超时时间会重新发送，有命令行可以查看超时消息，有日志记录消息发送超时事件；"}]}}, {"id": "MCR_89", "label": "CROS(RPC)-发流控", "type": 2, "attrs": {"description": "发流控", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_90", "label": "CROS(RPC)-收过载", "type": 2, "attrs": {"description": "收过载", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制，日志记录过载事件；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_91", "label": "CROS(RPC)-收错包", "type": 2, "attrs": {"description": "收错包", "steps": [{"step": "1、组件间通过X消息通信，通过故障注入工具 ，注入消息错包，有预期结果1", "expect": "1、错包丢弃，丢包可以查看，记录日志；"}]}}, {"id": "MCR_92", "label": "CROS(RPC)-收乱序", "type": 2, "attrs": {"description": "收乱序", "steps": [{"step": "1、组件间通过消息通信，通过故障注入工具 ，注入消息乱序，有预期结果1", "expect": "1、接收乱序的报文，可以进行保序，乱序报文个数可以查看；"}]}}, {"id": "MCR_93", "label": "CROS(RPC)-收超时", "type": 2, "attrs": {"description": "收超时", "steps": [{"step": "1、通过故障注入，注入消息接收超时（发送队列堆积或函数处理慢、消息未及时得到调度等），有预期结果1；", "expect": "1、收消息超时失败，有error信息报错，可以有命令行查看统计并记录日志；对可靠连接，接收端收超时未收到该报文，会通知发端重传；"}]}}, {"id": "MCR_94", "label": "CROS(RPC)-收兼容的消息", "type": 2, "attrs": {"description": "收兼容的消息", "steps": [{"step": "1、收到消息版本号的兼容的消息，有预期结果1；", "expect": "1、可以正确处理版本兼容的消息；"}]}}]}]}, {"label": "函数接口", "id": "4a558b60d06ac88e", "children": [{"id": "MCR_97", "label": "参数异常-空参数", "type": 2, "attrs": {"description": "空参数", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数为空参数", "expect": "1、函数能进行非空判断，对空参数打印日志，返回错误；"}]}}, {"id": "MCR_98", "label": "参数异常-参数越界", "type": 2, "attrs": {"description": "参数越界", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数越界", "expect": "1、函数要对对入参数进行合法性校验，不导致内存越界，对参数越界打印日志和返回错误信息；"}]}}, {"id": "MCR_99", "label": "参数异常-参数格式不符合约定", "type": 2, "attrs": {"description": "参数格式不符合约定", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数格式不符合要求", "expect": "1、对参数格式做校验，对参数不符合规范的，打印日志和返回错误信息；"}]}}, {"id": "MCR_100", "label": "参数异常-长度与实际不符", "type": 2, "attrs": {"description": "长度与实际不符", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数长度与实际长度不符（大于或小于）", "expect": "1、对参数长度做校验，不导致数组或内存越界，对参数越界打印日志和返回错误信息；"}]}}, {"id": "MCR_102", "label": "API处理异常-依赖的外部API处理超时", "type": 2, "attrs": {"description": "依赖的外部API处理超时", "steps": [{"step": "1、同步请求的响应消息超时到达_成功响应，通过测试桩模拟周边组件延迟返回接口响应消息，有预期结果1；", "expect": "1、本地会话或协议不能挂死，记录日志和告警；"}, {"step": ""}]}}, {"id": "MCR_103", "label": "API处理异常-依赖的外部API处理返回错误", "type": 2, "attrs": {"description": "依赖的外部API处理返回错误", "steps": [{"step": "1、同步请求的响应消息超时到达_失败响应，通过测试桩模拟周边组件延迟返回接口响应消息，有预期结果1；", "expect": "1、本地会话或协议不能挂死，再次发起请求成，记录日志和告警；"}]}}, {"id": "MCR_104", "label": "API处理异常-调用过载", "type": 2, "attrs": {"description": "调用过载", "steps": [{"step": "1、通过故障注入 方式，注入调用过载；", "expect": "1、能检测到调用过载事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "数据接口", "id": "eb404e3ba237619e", "children": [{"label": "命令行", "id": "a95cbe2c7294aa12", "children": [{"id": "MCR_107", "label": "命令行-参数越界", "type": 2, "attrs": {"description": "命令行参数越界", "steps": [{"step": "1、通过故障注入 方式，注入传递命令行的参数越界", "expect": "1、命令行会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_108", "label": "命令行-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入传递命令行格式不符合约定", "expect": "1、命令行会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_109", "label": "命令行-模式错误", "type": 2, "attrs": {"description": "模式错误", "steps": [{"step": "1、通过故障注入 方式，注入传递命令行模式错误", "expect": "1、命令行会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_110", "label": "命令行-命令行依赖不满足", "type": 2, "attrs": {"description": "命令行依赖不满足", "steps": [{"step": "1、通过故障注入 方式，注入，命令行依赖不满足", "expect": "1、命令行会做合法性校验，返回错误信息，提示需要先配置什么信息；"}]}}, {"id": "MCR_111", "label": "命令行-批量执行阻塞", "type": 2, "attrs": {"description": "批量执行阻塞", "steps": [{"step": "1、通过故障注入 方式，注入批量执行阻塞；", "expect": "1、能检测批量执行受阻事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "YANG", "id": "a0d1b24bee9ea478", "children": [{"id": "MCR_113", "label": "YANG-参数越界", "type": 2, "attrs": {"description": "参数越界", "steps": [{"step": "1、通过故障注入 方式，注入yang的参数越界", "expect": "1、yang会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_114", "label": "YANG-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入yang格式不符合约定", "expect": "1、yang会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_115", "label": "YANG-消息过载", "type": 2, "attrs": {"description": "消息过载", "steps": [{"step": "1、通过故障注入 方式，注入yang消息过载；", "expect": "1、能检测到消息过载事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "MIB", "id": "a12422ceda16ecd3", "children": [{"id": "MCR_117", "label": "MIB-参数越界", "type": 2, "attrs": {"description": "参数越界", "steps": [{"step": "1、通过故障注入 方式，注入MIB的参数越界", "expect": "1、MIB会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_118", "label": "MIB-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入MIB格式不符合约定", "expect": "1、MIB会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_119", "label": "MIB-消息过载", "type": 2, "attrs": {"description": "消息过载", "steps": [{"step": "1、通过故障注入 方式，注入MIB消息过载；", "expect": "1、能检测到消息过载事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "配置文件", "id": "fc1b7df04b2c6302", "children": [{"id": "MCR_122", "label": "配置文件-参数值越界", "type": 2, "attrs": {"description": "参数值越界", "steps": [{"step": "1、通过故障注入 方式，注入配置文件的参数越界", "expect": "1、做合法性校验，返回错误信息；"}]}}, {"id": "MCR_121", "label": "配置文件-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入配置文件格式不符合约定", "expect": "1、做合法性校验，返回错误信息；"}]}}, {"id": "MCR_123", "label": "配置文件-数据完整性校验失败", "type": 2, "attrs": {"description": "数据完整性校验失败", "steps": [{"step": "1、通过故障注入 方式，注入配置文件完整性校验失败故障", "expect": "1、下载配置文件时会对配置文件进行完整性校验，对校验失败的配置文件，进行报错，并下载不成功"}]}}, {"id": "MCR_124", "label": "配置文件-读写权限错误", "type": 2, "attrs": {"description": "读写权限错误", "steps": [{"step": "1、用户无读配置文件的权限，该用户启动进程去读配置文件，有预期结果1；", "expect": "1、不能读取配置文件，如果进程启动时需要读配置文件但无权限读，进程启动失败，有错误信息打印，记录日志；"}, {"step": "2、用户无写配置文件权限，进行写配置文件，有预期结果2；", "expect": "2、写配置文件失败，有错误信息提示，记录日志；"}]}}]}]}]}, {"label": "组件基础环境", "id": "9aa97be409faf5fc", "children": [{"label": "中间件", "id": "e5295fb69a0ca202", "children": [{"label": "DB", "id": "6ac19c303768c7e1", "children": [{"id": "MCR_127", "label": "DB-进程主动重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理DB重启的事件，已有业务不受影响"}]}}, {"id": "MCR_126", "label": "DB-进程被动重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、强制中断进程（如kill -9)，有预期结果1", "expect": "1、被测组件能力处理DB重启的事件，已有业务不受影响"}]}}, {"id": "MCR_128", "label": "DB-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、DB挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_129", "label": "DB-读数据失败", "type": 2, "attrs": {"description": "读数据失败", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具或者软件函数注入，构造DB数据组件无法读故障，有预期结果1", "expect": "1、被测组件能力处理DB的接口失败，已有业务不受影响，进程不会异常退出，并记录读DB失败日志"}]}}, {"id": "MCR_130", "label": "DB-写数据失败", "type": 2, "attrs": {"description": "写数据失败", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具或者软件函数注入，构造DB数据组件无法写故障，有预期结果1", "expect": "1、被测组件能力处理DB的接口失败，已有业务不受影响，进程不会异常退出，并记录写DB失败日志"}]}}]}, {"label": "DOPHI", "id": "fcb4fe799408cbd0", "children": [{"id": "MCR_132", "label": "DOPHI-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理DOPHI重启的事件，已有业务不受影响"}]}}, {"id": "MCR_133", "label": "DOPHI-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、DOPHI挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_134", "label": "DOPHI-队列反压", "type": 2, "attrs": {"description": "队列反压", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "1、被测试组件发送消息速率大于DOPHI处理能力，有预期结果1；", "expect": "1、DOPHI发送一个反压信息给被测试组件，被测试组件进行流控，DOPHI如果有缓存队列，会把超出部分先放缓存队列，根据接收端能处理的速率进行调度，缓存满了后再进行丢包；"}]}}]}, {"label": "DOCUE", "id": "a20d94f09d2758fd", "children": [{"id": "MCR_136", "label": "DOCUE-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理DOCUE重启的事件，已有业务不受影响"}]}}, {"id": "MCR_137", "label": "DOCUE-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、DOCUE挂起不会导致被测组件退出，已有业务保持"}]}}]}]}, {"label": "监控诊断", "id": "e75fe2601f3da511", "children": [{"label": "日志服务", "id": "52c1e39026e0f6cd", "children": [{"id": "MCR_139", "label": "日志-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理日志重启的事件，已有业务不受影响"}]}}, {"id": "MCR_140", "label": "日志-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、日志挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_141", "label": "日志-写失败", "type": 2, "attrs": {"description": "写失败", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入写日志失败，有预期结果1", "expect": "1、被测试组件写失败，有写日志失败告警，被测试组件能处理日志写失败的事件，已有业务不受影响；"}]}}, {"id": "MCR_142", "label": "日志-存储空间满", "type": 2, "attrs": {"description": "存储空间满", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，构造存储空间满，产生新的日志需要记录的场景，有预期结果1 ；", "expect": "1、能对日志内容进行压缩，存储满时，对日志摸了中时间戳最老的压缩文件进行删除。被测试组件已有业务不受影响，可以正常记录日志；"}]}}, {"id": "MCR_143", "label": "日志-海量日志检测", "type": 2, "attrs": {"description": "海量日志检测", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，被测试组件朝日志进程发送海量日志（1s上报2条，1小时50条定义为海量日志，鹰眼上报海量日志错误），有预期结果1", "expect": "1、日志能检测出海量日志，并能抑制，被测试组件能处理日志抑制事件，已有业务不受影响；"}]}}, {"id": "MCR_144", "label": "日志-ERROR日志检测", "type": 2, "attrs": {"description": "ERROR日志检测", "steps": [{"step": "1、准备被测组件基础业务（或功能）", "expect": "1、日志进程记录error日志，被测试组件能处理error事件，已有业务不受影响；"}, {"step": "2、通过故障注入工具 ，被测试进程朝日志进程记录error日志；"}]}}]}, {"label": "SAID", "id": "dc8ac88613ab646e", "children": []}]}]}, {"label": "组件公共能力", "id": "8d2cce4674b5f02a", "children": [{"label": "HA", "id": "5d7cf4971707adf6", "children": [{"id": "MCR_146", "label": "HA-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理HA重启的事件，已有业务不受影响"}]}}, {"id": "MCR_147", "label": "HA-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入HA组件进程挂起故障，有预期结果1", "expect": "1、HA挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_148", "label": "HA-状态迁移", "type": 2, "attrs": {"description": "HA状态迁移", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具，注入HA状态迁移，有预期结果1", "expect": "1、被测试组件可以正确处理HA状态迁移事件，已有业务不受影响"}]}}, {"id": "MCR_149", "label": "HA-状态震荡", "type": 2, "attrs": {"description": "HA状态震荡", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具，注入HA状态震荡，有预期结果1", "expect": "1、被测试组件可以正确处理HA状态震荡事件，已有业务不受影响"}]}}]}, {"label": "能力定制", "id": "04c8a9859018912c", "children": [{"id": "MCR_151", "label": "能力集加载失败", "type": 2, "attrs": {"description": "能力集加载失败", "steps": [{"step": "1、通过故障注入工具 ，注入能力集加载失败，有预期结果1", "expect": "1、能力集加载失败有告警和日志记录，无资源残留，再次下载可以成功"}]}}, {"id": "MCR_152", "label": "能力集动态刷新", "type": 2, "attrs": {"description": "能力集动态刷新", "steps": [{"step": "1、通过故障注入工具 ，注入能力集动态刷新，有预期结果1", "expect": "1、检测出能力集动态刷新事件，并记录日志，根据架构规则做对应的处理。"}]}}]}, {"label": "平滑对账", "id": "e726f39260216982", "children": [{"id": "MCR_157", "label": "平滑-传输通道建立失败", "type": 2, "attrs": {"description": "平滑传输通道建立失败", "steps": [{"step": "1、构造平滑传输通道打开失败故障（如对端不回应），有预期结果1；", "expect": "1、有平滑管道打开失败故障告警或日志记录；"}, {"step": "2、构造持续平滑管道打开失败故障，持续xx时间（x分钟以上，触发自愈的时间，如5分钟），有预期结果2；", "expect": "2、长时间打开失败则复位进程恢复触发自愈；"}, {"step": "3、平滑管道可以正常建立，有预期结果3。", "expect": "3、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_158", "label": "平滑-传输通道中断", "type": 2, "attrs": {"description": "平滑传输通道建立失败", "steps": [{"step": "1、在平滑过程中触发传输通道中断，有预期结果1；", "expect": "1、有平滑管道中断故障告警或日志记录；"}, {"step": "2、构造持续平滑管道中断故障，持续xx时间（x分钟以上，触发自愈的时间，如5分钟），有预期结果2；", "expect": "2、长时间中断败则复位进程恢复触发自愈；"}, {"step": "3、平滑管道中断后恢复，有预期结果3。", "expect": "3、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_159", "label": "平滑-过程中消息随机丢失", "type": 2, "attrs": {"description": "平滑过程中消息随机丢失", "steps": [{"step": ""}, {"step": "1、（韧性）构造xx平滑消息延时300s以上（也可构造平滑通道拥塞）或持续丢弃xx平滑消息，有预期结果1；", "expect": "1、有平滑消息不可达故障告警或日志记录，连续一段时间(如5min)没收到平滑的数据，且也没收到平滑结束消息，则尝试再发起一轮平滑；"}, {"step": "2、（韧性）对xx业务撤销xx平滑消息故障，有预期结果2。", "expect": "2、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_160", "label": "平滑-不结束", "type": 2, "attrs": {"description": "平滑不结束", "steps": [{"step": "1、触发平滑结束消息持续丢弃，触发平滑不结束持续x时间（如60s），有预期结果1；", "expect": "1、有平滑长时间不结束告警或日志记录，会尝试再发起一轮平滑；"}]}}, {"id": "MCR_161", "label": "平滑-处理失败", "type": 2, "attrs": {"description": "平滑处理失败", "steps": [{"step": ""}, {"step": "1、收到平滑处理失败消息，有预期结果1；", "expect": "1、能检测出平滑失败事件并记录日志；"}]}}, {"id": "MCR_163", "label": "对账-传输通道建立失败", "type": 2, "attrs": {"description": "对账传输通道建立失败", "steps": [{"step": "1、构造入对账传输通道打开失败故障（如对端不回应），有预期结果1；", "expect": "1、有对账管道打开失败故障告警或日志记录；"}, {"step": "2、构造持续对账管道打开失败故障，持续xx时间（5分钟以上），有预期结果2；", "expect": "2、长时间打开失败则复位进程恢复触发自愈；"}, {"step": "3、对账管道正常建立，有预期结果3。", "expect": "3、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_164", "label": "对账-传输通道中断", "type": 2, "attrs": {"description": "对账传输通道中断", "steps": [{"step": "1、在对账过程中触发对账传输通道中断，有预期结果1；", "expect": "1、有对账通道中断故障告警或日志记录；"}, {"step": "2、构造持续对账管道中断故障，持续xx时间（x分钟以上，触发自愈的时间，如5分钟），有预期结果2；", "expect": "2、长时间中断败则复位进程恢复触发自愈；"}, {"step": "3、对账通道恢复正常，有预期结果3。", "expect": "3、告警消除或日志记录故障消除。"}]}}, {"id": "MCR_165", "label": "对账-过程中消息随机丢失", "type": 2, "attrs": {"description": "对账过程中消息随机丢失", "steps": [{"step": ""}, {"step": "1、（韧性）构造xx对账消息延时300s以上（也可构造对账通道拥塞）或持续丢弃xx平滑消息，有预期结果1；", "expect": "1、有对账消息不可达故障告警或日志记录，连续一段时间(如5min)没收到对账的数据，且也没收到对账结束消息，则尝试再发起一轮对账；"}, {"step": "2、（韧性）对xx业务撤销xx对账消息故障，有预期结果2。", "expect": "2、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_166", "label": "对账-处理时间大于对账周期", "type": 2, "attrs": {"description": "对账处理时间大于对账周期", "steps": [{"step": "1、将一轮对账时间延长到触发下一次对账周期开始，比如对账周期是1小时一次，在新的对账开始时，上一次的对账消息还未处理完，有预期结果1；", "expect": "1、到对账周期开始新的一轮对账，停止上一轮的对账，资源释放，业务无异常"}]}}]}]}]}