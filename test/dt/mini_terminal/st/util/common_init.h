/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef COMMON_INIT_H
#define COMMON_INIT_H
#include "stub.h"
#include "adpt_sleep.h"
#include "db_common_init.h"
#include "db_instance.h"
#include "test_mini_util.h"

#define UT_ADPT_MAX_READ_BUF_LEN 64
#define UT_MAX_READ_BUF_LEN 64
#define TEST_FILE_PATH_LEN (PATH_MAX + 2)  // this value must be greater than (PATH_MAX + 1)
#define UT_FILE_USED_TO_MMAP "./utmmap"
#define UT_SLEEP_US 10
#define TEST_COUNT 10
#define UT_OPEN_CLOSE_OK 0
#define MAX_CONFIG_JSON_LENGTH 1024
#define ERRORCODE_REGISTERED GMERR_CONNECTION_EXCEPTION
#define ERRORCODE_NOT_EXIST 10000000
#define ITEM_NAME_LEN 128
#define HASH_MAP_SIZE 500

#define UT_LABEL_CURSOR_NUM 50
#define UT_LABEL_CURSOR_PREFIX "ExecutorLabelCursorTable"
#define UT_EXECUTOR_MAX_CONN_NUM 10

#ifdef HARMONY_OS
// 鸿蒙自测试框架目前没有超时系统，因此设置为默认最多等待100s，防止中间步骤失败导致用例卡死
#define MAX_SLEEP_TIME (100 * 100)
#else
// 为方便写用例调试，设置超时时间1200s，即20min
#define MAX_SLEEP_TIME (1200 * 100)
#endif

int32_t CommonInit(const char *configJson = nullptr);
void CommonRelease(bool isClearStub = true);
DbInstanceT *UtGetDbInstance();
Status UtLogVerifyFileLineCnt(const char *path, const char *cmd, uint32_t expLineCnt, uint32_t expLineLen);
int *GetProcessSyncMemory();
void ClearProcessSyncMemory(int *buffer);
void WaitCondition(const int *variable, int expect, int maxSleepTime = MAX_SLEEP_TIME);
#endif
