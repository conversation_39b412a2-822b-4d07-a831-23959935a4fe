/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: st_emd_gql_vector_inverted_hybrid.cc
 * Description:
 * Create: 2025-05-06
 */
#ifndef FEATURE_INVERTED
#define FEATURE_INVERTED
#endif

#include <cfloat>
#include <cstdlib>
#include <ctime>
#include <iostream>
#include <string>
#include <thread>
#include <vector>
#include "st_emb_gql_com.h"
#include "gme_gql_api.h"
#include "stub.h"
#include "ee_cmd_state_fusion.h"
#include "se_index.h"
#include "ee_session.h"

using namespace testing::ext;
using namespace std;

class StEmbGqlVectorInvertedHybrid : public StEmbGqlCommon {
    void SetUp() override
    {
        GmeConnT *conn = StEmbGqlGetConn();
        const char *gql = "CREATE GRAPH myGraph {"
                          "(person:<PERSON>ER<PERSON><PERSON> {name STRING, id INT, profileVec FLOATVECTOR(2)}),"
                          "(event:EVENT {title STRING, rank INT, brief STRING, briefVec FLOATVECTOR(2)}),"
                          "(person)-[:WORK_RELATION{role STRING}]->(person),"
                          "(person)-[:PARTICIPATE{comment STRING, commentVec FLOATVECTOR(2)}]->(event)"
                          "};";

        ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, gql));
    }

    void TearDown() override
    {
        const char *gql = "DROP GRAPH IF EXISTS myGraph;";
        GmeConnT *conn = StEmbGqlGetConn();
        ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, gql));
    }
};

static Status CreateAllIndex(GmeConnT *conn)
{
    vector<string> createIdxs = {"CREATE INDEX IF NOT EXISTS idx01 ON PERSON(id)",
        "CREATE INDEX IF NOT EXISTS idx02 ON EVENT(rank)",
        "CREATE INDEX IF NOT EXISTS idx03 ON EVENT USING GSDISKANN(briefVec L2)",
        "CREATE INDEX IF NOT EXISTS idx04 ON PERSON USING GSDISKANN(profileVec L2)",
        "CREATE INDEX IF NOT EXISTS idx05 ON EVENT USING GIN(brief)",
        "CREATE INDEX IF NOT EXISTS idx06 ON PARTICIPATE USING GSDISKANN(commentVec L2);"};

    Status ret = GMERR_OK;
    for (string createIdx : createIdxs) {
        ret = StEmbGqlExecCmdReturningStatus(conn, createIdx.c_str());
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

static void InsertVertex(GmeConnT *conn)
{
    std::vector<std::string> eventData = {
        "INSERT (e1:EVENT {title:'EA', rank:1, brief:'AA BB CC', briefVec:'[0,1]'});",
        "INSERT (e2:EVENT {title:'EB', rank:2, brief:'BB CC DD', briefVec:'[0,2]'});",
        "INSERT (e3:EVENT {title:'EC', rank:3, brief:'CC DD FF', briefVec:'[0,3]'});",
        "INSERT (e4:EVENT {title:'ED', rank:4, brief:'DD EE II', briefVec:'[0,4]'});",
        "INSERT (e5:EVENT {title:'EE', rank:5, brief:'EE FF AA', briefVec:'[0,5]'});",
        "INSERT (e6:EVENT {title:'EF', rank:6, brief:'FF GG', briefVec:'[0,6]'});",
        "INSERT (e7:EVENT {title:'EG', rank:7, brief:'GG HH', briefVec:'[0,7]'});",
        "INSERT (e8:EVENT {title:'EH', rank:8, brief:'HH II', briefVec:'[0,8]'});",
        "INSERT (e9:EVENT {title:'EI', rank:9, brief:'II JJ', briefVec:'[0,9]'});",
        "INSERT (e10:EVENT {title:'EJ', rank:10, brief:'JJ AA BB CC DD EE FF GG', briefVec:'[0,10]'});",
    };
    for (auto &event : eventData) {
        ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, event.c_str()));
        ASSERT_EQ(1u, GmeGqlChanges(conn));
    }

    std::vector<std::string> personData = {
        "INSERT (pb:PERSON {name:'PA', id:1, profileVec:'[0,11]'});",
        "INSERT (pc:PERSON {name:'PB', id:2, profileVec:'[0,12]'});",
        "INSERT (pd:PERSON {name:'PC', id:3, profileVec:'[0,13]'});",
        "INSERT (pe:PERSON {name:'PD', id:4, profileVec:'[0,14]'});",
        "INSERT (pa:PERSON {name:'PE', id:5, profileVec:'[0,15]'});",
        "INSERT (pb:PERSON {name:'PF', id:6, profileVec:'[0,16]'});",
        "INSERT (pc:PERSON {name:'PG', id:7, profileVec:'[0,17]'});",
        "INSERT (pd:PERSON {name:'PH', id:8, profileVec:'[0,18]'});",
        "INSERT (pe:PERSON {name:'PI', id:9, profileVec:'[0,19]'});",
        "INSERT (pa:PERSON {name:'PJ', id:10, profileVec:'[0,20]'});",
    };
    for (auto &person : personData) {
        ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, person.c_str()));
        ASSERT_EQ(1u, GmeGqlChanges(conn));
    }
}

static void InsertEdge(GmeConnT *conn)
{
    std::vector<std::string> workRelations = {
        "MATCH (p1:PERSON {name: 'PA'}), (p2:PERSON {name: 'PB'}) "
        "INSERT (p1)-[:WORK_RELATION {role:'leader'}]->(p2);",  // A 是 B 的领导

        "MATCH (p1:PERSON {name: 'PB'}), (p2:PERSON {name: 'PA'}) "
        "INSERT (p1)-[:WORK_RELATION {role:'subordinate'}]->(p2);",  // B 是 A 的下属

        "MATCH (p1:PERSON {name: 'PB'}), (p2:PERSON {name: 'PD'}) "
        "INSERT (p1)-[:WORK_RELATION {role:'PL'}]->(p2);",  // B 是 D 的PL

        "MATCH (p1:PERSON {name: 'PD'}), (p2:PERSON {name: 'PE'}) "
        "INSERT (p1)-[:WORK_RELATION {role:'SE'}]->(p2);",  // D 是 E 的SE

        "MATCH (p1:PERSON {name: 'PC'}), (p2:PERSON {name: 'PE'}) "
        "INSERT (p1)-[:WORK_RELATION {role:'XM'}]->(p2);",  // C 是 E 的XM
    };
    for (auto &relation : workRelations) {
        ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, relation.c_str()));
        ASSERT_EQ(1u, GmeGqlChanges(conn));
    }

    // 从Person到Event的关系
    vector<pair<vector<string>, string>> edges = {{{"PA", "PB", "PC"}, "EA"}, {{"PD", "PB", "PC"}, "EB"},
        {{"PC", "PD", "PF"}, "EC"}, {{"PD", "PE", "PI"}, "ED"}, {{"PE", "PF", "PA"}, "EE"}, {{"PF", "PG"}, "EF"},
        {{"PG", "PH"}, "EG"}, {{"PH", "PI"}, "EH"}, {{"PI", "PJ"}, "EI"},
        {{"PA", "PB", "PC", "PD", "PE", "PF", "PG", "PJ"}, "EJ"}};
    int cnt = 0;
    for (int i = 0; i < edges.size(); i++) {
        for (auto person : edges[i].first) {
            cnt++;
            std::stringstream ss;
            ss << "MATCH (p:PERSON {name: '" << person << "'}), (m:EVENT {title: '" << edges[i].second
               << "'}) INSERT (p)-[:PARTICIPATE {comment:'good', commentVec:'[1," << to_string(cnt) << "]'}]->(m);";
            string s = ss.str();
            ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, s.c_str()));
            EXPECT_EQ(1u, GmeGqlChanges(conn)) << s << endl;
        }
    }
}

static void InsertData(GmeConnT *conn)
{
    InsertVertex(conn);
    InsertEdge(conn);
}

static void CheckVectorProperty(GmeConnT *conn, std::string matchGqlStr, StEmbGqlQryResultSetExtendT exptQryResult)
{
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = (uint32_t)(matchGqlStr.size() + 1);
    Status ret = GmeGqlPrepare(conn, matchGqlStr.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret) << matchGqlStr << endl;
    void *data = nullptr;
    StEmbGqlGetActQryResultSetEx(data, stmt);
    StEmbGqlCheckActQryResultSet(data, exptQryResult);
    StEmbGqlClearActQryResultSet(nullptr);
    ret = GmeGqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

INSTANTIATE_TEST_CASE_P(, StEmbGqlVectorInvertedHybrid, testing::Values(0));

// 测试0跳
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(conn);

    StEmbGqlQryResultSetExtendT exptQryResult;
    std::string matchGql;

    // 会按照向量进行排序
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1) return a.rank order by a.briefVec <-> "
               "'[0,0]' limit 3;";
    exptQryResult = {{"a.rank"}, {{"2"}, {"3"}, {"10"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // limit k
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1) return a.rank order by a.briefVec <-> "
               "'[0,0]' limit 2;";
    exptQryResult = {{"a.rank"}, {{"2"}, {"3"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 多个WHERE过滤
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1) WHERE a.rank<5 return a.rank order by "
               "a.briefVec <-> '[0,0]' limit 3;";
    exptQryResult = {{"a.rank"}, {{"2"}, {"3"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 组合向量过滤
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1) WHERE a.rank<5 AND a.briefVec <-> '[0,0]'<=2 "
               "return a.rank order by a.briefVec <-> '[0,0]' limit 3;";
    exptQryResult = {{"a.rank"}, {{"2"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 倒排查询项Boolean
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC OR EE' AND a.rank > 1) return a.rank order by a.briefVec <-> "
               "'[0,0]' limit 4;";
    exptQryResult = {{"a.rank"}, {{"2"}, {"3"}, {"4"}, {"5"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 倒排索引后置
    matchGql = "match (a:EVENT WHERE a.rank > 1 AND a.brief CONTAIN 'CC OR EE') return a.rank order by a.briefVec <-> "
               "'[0,0]' limit 5;";
    exptQryResult = {{"a.rank"}, {{"2"}, {"3"}, {"4"}, {"5"}, {"10"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);
}

// 测试1跳
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(conn);

    StEmbGqlQryResultSetExtendT exptQryResult;
    std::string matchGql;

    // 会按照向量进行排序
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON) return b.id order "
               "by b.profileVec <-> '[0,0]' limit 20;";
    exptQryResult = {
        {"b.id"}, {{"1"}, {"2"}, {"2"}, {"3"}, {"3"}, {"3"}, {"4"}, {"4"}, {"4"}, {"5"}, {"6"}, {"6"}, {"7"}, {"10"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // limit k
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON) return b.id order "
               "by b.profileVec <-> '[0,0]' limit 6;";
    exptQryResult = {{"b.id"}, {{"1"}, {"2"}, {"2"}, {"3"}, {"3"}, {"3"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 多个WHERE过滤
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON WHERE b.id<6) "
               "WHERE b.id>3 return b.id order by b.profileVec <-> '[0,0]' limit 6;";
    exptQryResult = {{"b.id"}, {{"4"}, {"4"}, {"4"}, {"5"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 组合向量过滤
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON) WHERE b.id>3 AND "
               "b.profileVec <-> '[0,0]' < 16 return b.id order by b.profileVec <-> '[0,0]' limit 6;";
    exptQryResult = {{"b.id"}, {{"4"}, {"4"}, {"4"}, {"5"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 倒排查询项Boolean
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC OR EE' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON) WHERE "
               "b.id>5 return b.id order by b.profileVec <-> '[0,0]' limit 5;";
    exptQryResult = {{"b.id"}, {{"6"}, {"6"}, {"6"}, {"7"}, {"9"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 倒排索引后置
    matchGql = "match (a:EVENT WHERE a.rank > 1 AND a.brief CONTAIN 'CC NOT EE')<-[:PARTICIPATE]-(b:PERSON) return "
               "b.id order by b.profileVec <-> '[0,0]' limit 5;";
    exptQryResult = {{"b.id"}, {{"2"}, {"3"}, {"3"}, {"4"}, {"4"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);
}

// 测试2跳3跳
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_003, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(conn);

    StEmbGqlQryResultSetExtendT exptQryResult;
    std::string matchGql;

    // 2跳
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC OR EE' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON WHERE "
               "b.id<7)-[:PARTICIPATE]->(c:EVENT WHERE c.rank>2) "
               "WHERE c.briefVec<->'[0,0]' < 8 OR b.profileVec<->'[0,0]'<18 return c.rank order by c.briefVec <-> "
               "'[0,0]' limit 20;";
    exptQryResult = {{"c.rank"}, {{"3"}, {"3"}, {"3"}, {"3"}, {"3"}, {"3"}, {"3"}, {"4"}, {"4"}, {"4"}, {"4"}, {"4"},
                                     {"5"}, {"5"}, {"5"}, {"5"}, {"5"}, {"6"}, {"6"}, {"6"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 3跳
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN '(CC OR FF) NOT EE' AND a.rank > 1)<-[:PARTICIPATE]-(b:PERSON "
               "WHERE b.id<7)"
               "-[:PARTICIPATE]->(c:EVENT WHERE c.rank>2)<-[:PARTICIPATE]-(d:PERSON WHERE d.profileVec<->'[0,0]'<17) "
               "WHERE (c.briefVec<->'[0,0]' < 8 OR b.profileVec<->'[0,0]'<18) AND d.id!=3 return d.id order by "
               "d.profileVec <-> '[0,0]' limit 20;";
    exptQryResult = {{"d.id"}, {{"1"}, {"1"}, {"1"}, {"1"}, {"1"}, {"1"}, {"1"}, {"1"}, {"1"}, {"2"}, {"2"}, {"2"},
                                   {"2"}, {"2"}, {"2"}, {"4"}, {"4"}, {"4"}, {"4"}, {"4"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);

    // 变长跳
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN '(CC OR FF) NOT EE' AND a.rank > 1)<-[]-{1,3}(b:PERSON) "
               "WHERE b.id>3 return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    exptQryResult = {{"b.id"}, {{"4"}, {"4"}, {"6"}, {"6"}, {"7"}}};
    CheckVectorProperty(conn, matchGql, exptQryResult);
}

// 负面用例
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_004, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(conn);

    std::string matchGql;
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = 0;

    // 倒排索引没有直接应用在起点上
    matchGql = "match (a:EVENT)<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT WHERE c.brief CONTAIN 'CC' AND "
               "c.rank > 1) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.rank>1)<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
               "WHERE a.brief CONTAIN 'CC' return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.rank>1)<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
               "WHERE c.brief CONTAIN 'CC' return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    // 起点使用了倒排索引，但是使用了OR
    matchGql =
        "match (a:EVENT WHERE a.brief CONTAIN 'CC' OR a.rank>1)<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
        "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    // 起点使用多个倒排索引
    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' AND a.brief CONTAIN "
               "'EE')<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
               "WHERE a.rank>7 OR a.rank < 4 return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC' OR a.brief CONTAIN "
               "'EE')<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
               "WHERE a.rank>7 OR a.rank < 4 return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    // 起点不显式指定label
    matchGql =
        "match (a WHERE NOT (a.brief CONTAIN 'CC') AND a.rank>1)<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
        "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;
}

// 负面用例:NULL值和不正确的引用
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_005, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);

    string matchGql;
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = 0;

    matchGql = "match (a:EVENT WHERE a.brief contain NULL)<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.brief contain 234)<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.brief contain 3.456)<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.brief contain a.rank)<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.brief contain c.www)<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE c.brief contain 'LLM')<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE b.brief contain 'LLM')<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE c.brief contain 'LLM')<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret) << matchGql << endl;

    matchGql = "match (a:EVENT WHERE a.www contain 'LLM')<-[:PARTICIPATE]-(b:PERSON)-[:PARTICIPATE]->(c:EVENT) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret) << matchGql << endl;
}

// 负面用例:DDL
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_006, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    string matchGql;
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = 0;

    string createIndex = "CREATE INDEX SET ON EVENT USING GIN(brief)";
    Status ret = StEmbGqlExecCmdReturningStatus(conn, createIndex.c_str());
    ASSERT_EQ(GMERR_OK, ret) << createIndex << endl;

    matchGql = "match (a:EVENT WHERE a.brief CONTAIN 'CC')<-[:PARTICIPATE]-(b:PERSON) "
               "return b.id order by b.profileVec <-> '[0,0]' limit 20;";
    len = (uint32_t)(matchGql.size() + 1);
    ret = GmeGqlPrepare(conn, matchGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret) << matchGql << endl;
}

// 门槛用例
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_007, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    std::string gql;
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = 0;
    gql = "CREATE INDEX IF NOT EXISTS idx01 ON EVENT USING GIN(brief);";
    ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, gql.c_str())) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM' AND a.rank > 10)-[r]->(b:PERSON) WHERE b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM' AND a.rank > 10 AND a.briefVec <-> '[0.1,0.3]' < "
          "0.8)-[r]->(b:PERSON)  return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM' AND a.rank > 10)-[r WHERE r.commentVec <-> '[0.1,0.3]' < "
          "0.8]->(b:PERSON) return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM' AND a.rank > 10)-[r]->(b:PERSON WHERE b.profileVec <-> "
          "'[0.1,0.3]' < 0.8) return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM' AND a.rank > 10)-[r]->(b:PERSON) return a,r,b,b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 AS vec;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;
}

// 用例补充
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_008, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(conn);

    std::string gql;
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = 0;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' AND a.rank > 10)-[r]-(b:PERSON) WHERE "
          "b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' AND a.rank > '10')-[r]-(b:PERSON) WHERE "
          "b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' AND a.rank < 13)-[r]-(b:PERSON) WHERE "
          "b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 return a,r,b;";
    len = (uint32_t)(gql.size() + 1);
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' AND a.brief like '%qwe%')-[r]-(b:PERSON) "
          "WHERE b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 return a,r,b;";
    CheckVectorProperty(conn, gql, {});

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' AND a.briefVec <-> '[0,0]' < "
          "'0')-[r]-(b:PERSON) WHERE b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 return a,r,b;";
    CheckVectorProperty(conn, gql, {});

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' )-[r]-(b:PERSON) WHERE b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 AND a.briefVec <-> '[0,0]' < '0' return a,r,b;";
    CheckVectorProperty(conn, gql, {});

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM AND (NULL NOT DEFAULT)' )-[r]-(b:PERSON) WHERE b.profileVec <-> "
          "'[0.1,0.3]' < 0.8 AND a.rank < '10' return a,r,b;";
    CheckVectorProperty(conn, gql, {});

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM')-[r]-(b:PERSON) "
          "WHERE b.profileVec <-> '[0.1,0.3]' return b;";
    CheckVectorProperty(conn, gql, {});

    // 多个排序项
    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM')-[r]-(b:PERSON) "
          "WHERE b.profileVec <-> '[0.1,0.3]' > 0 return b order by a.briefVec,b.profileVec;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT WHERE a.brief CONTAIN 'LLM')-[r]-(b:PERSON) "
          "WHERE b.profileVec <-> '[0.1,0.3]' > 0 return b order by a.rank,b.profileVec limit 10;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;
}

// 用例补充:倒排插入空值或NULL
HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_hybrid_009, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    std::string gql;

    gql = "INSERT (e1:EVENT {title:'EA', rank:1, briefVec:'[0,1]'});";
    ASSERT_EQ(GMERR_INVALID_VALUE, StEmbGqlExecCmdReturningStatus(conn, gql.c_str()));

    gql = "INSERT (e1:EVENT {title:'EA', rank:1, brief:'', briefVec:'[0,1]'});";
    ASSERT_EQ(GMERR_OK, StEmbGqlExecCmdReturningStatus(conn, gql.c_str()));

    gql = "INSERT (e1:EVENT {title:'EA', rank:1, brief:NULL, briefVec:'[0,1]'});";
    ASSERT_EQ(GMERR_INVALID_VALUE, StEmbGqlExecCmdReturningStatus(conn, gql.c_str()));
}

HWTEST_P(StEmbGqlVectorInvertedHybrid, gql_basic_orderby_unsortable_key, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    Status ret = CreateAllIndex(conn);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(conn);
    std::string gql;
    GmeGqlStmtT *stmt = NULL;
    uint32_t len = 0;

    gql = "match (a:EVENT where a.rank > 5) return a.rank order by a.briefVec limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5) return a.rank order by a.rank,a.briefVec limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5) return a.rank order by a.rank,a.brief,a.briefVec limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5)-[:PARTICIPATE]-(b:PERSON) return a.rank order by b.profileVec limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5)-[:PARTICIPATE]-(b:PERSON) return a.rank order by b.id,a.brief,b.profileVec "
          "limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5)-[r:PARTICIPATE]-(b:PERSON) return a.rank order by "
          "b.id,a.brief,r.commentVec limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5) return a.rank order by a,a.rank,a.brief limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a:EVENT where a.rank > 5) return a.rank order by a.rank,a.briefVec limit 3;";
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;

    gql = "match (a) return a.rank order by a.briefVec limit 3;";
    ASSERT_EQ(GMERR_OK, GmeGqlPrepare(conn, gql.c_str(), len, &stmt, NULL)) << gql << endl;
    ASSERT_NE(GMERR_OK, GmeGqlStep(stmt)) << gql << endl;
}
