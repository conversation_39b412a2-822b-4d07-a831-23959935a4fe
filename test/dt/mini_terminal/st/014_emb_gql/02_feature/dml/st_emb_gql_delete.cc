/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_gql_set.cc
 * Description:
 * Create: 2024-10-24
 */

#include <iostream>
#include <string>
#include "st_emb_gql_com.h"
#include "gme_gql_api.h"

using namespace std;
using namespace testing::ext;

class StEmbGqlDelete : public StEmbGqlCommon {
    void SetUp() override
    {
        StEmbGqlCommon::SetUp();
    }

    void TearDown() override
    {
        StEmbGqlCommon::TearDown();
    }

protected:
    void PrepareGraphData();
    void PrepareBaseCaseData(std::string &gql, GmeConnT *conn);
    void ExecGqlWithExpectChanges(string gql, uint32_t expectChanges);
};

void StEmbGqlDelete::PrepareGraphData()
{
    GmeConnT *conn = StEmbGqlGetConn();
    const int vertexNum = 10;
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
        ASSERT_EQ(1u, GmeGqlChanges(conn));

        gqlStr = GetInsertEventGql(i, true);
        StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
        ASSERT_EQ(1u, GmeGqlChanges(conn));
    }
    string gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_2'}) "
                    "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_2'}), (b: Person {name: 'name_3'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_3'}), (b: Person {name: 'name_4'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_4'}), (b: Person {name: 'name_5'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_5'}), (b: Person {name: 'name_6'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_6'}), (b: Person {name: 'name_7'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_7'}), (b: Person {name: 'name_8'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_8'}), (b: Person {name: 'name_7'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_8'}), (b: Person {name: 'name_9'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_10'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_3'}), (b: Person {name: 'name_9'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_9'}), (b: Person {name: 'name_1'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_9'}), (b: Person {name: 'name_10'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
}
INSTANTIATE_TEST_CASE_P(, StEmbGqlDelete, testing::Values(0));

/**
 * usecase design:
 * 1. delete node
 * 2. delete edge
 * 3. delete edge and node
 * 4. match delete with where
 */
HWTEST_P(StEmbGqlDelete, TestGqlNormalDelete, TestSize.Level0)
{
    PrepareGraphData();

    GmeConnT *conn = StEmbGqlGetConn();
    // single node
    string gqlStr = "MATCH (a: Person {name: 'name_1'}) DETACH DELETE a;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(4u, GmeGqlChanges(conn));

    // node not exist
    gqlStr = "MATCH (a: Person {name: 'name_11'}) DETACH DELETE a;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(0u, GmeGqlChanges(conn));

    gqlStr = GetInsertPersonGql(1, true);
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_2'}) "
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(1u, GmeGqlChanges(conn));

    gqlStr = "MATCH (a: Person {name: 'name_1'})-[r]->(b: Person {name: 'name_123'}) DETACH DELETE a, b;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(0u, GmeGqlChanges(conn));

    gqlStr = "MATCH (a: Person {name: 'name_1'}) DETACH DELETE a;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(2u, GmeGqlChanges(conn));

    // single edge
    gqlStr = "Match (a: Person {name: 'name_2'})-[r:直系亲属 {roletype:'dad'}]->(b:Person {name: 'name_3'})"
             "DETACH DELETE r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(1u, GmeGqlChanges(conn));

    // edge not exist
    gqlStr = "Match (a: Person {name: 'name_2'})-[r:直系亲属]->(b:Person {name: 'name_1'}) DETACH DELETE r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(0u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_3'})-[r:直系亲属]->(b:Person)-[s]->(c:Person {name: 'name_5'}) \
            DETACH DELETE a, r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(3u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_7'})<-[r:直系亲属]-(b:Person)-[s:直系亲属]->(c:Person {name:'name_9'}) \
            DETACH DELETE b;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(4u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_5'})-[r]->(b:Person)-[s:直系亲属]->(c:Person {name: 'name_7'}) \
        DETACH DELETE a, s;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(4u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person) Where a.name = 'name_6' \
    DETACH DELETE a;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(1u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person)-[r:直系亲属]->(b:Person) Where a.name = 'name_9' and b.name = 'name_10' \
        DETACH DELETE a, r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(2u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person)-[r:直系亲属]->(b:Person) Where a.name > 'name_3' \
        DETACH DELETE a, r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(0u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person) Where a.name > 'name_3' \
        DETACH DELETE a;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(2u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person) DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(2u, GmeGqlChanges(conn));

    PrepareGraphData();
    gqlStr = "Match (a: Person {name: 'name_4'})-[]->{0,2}(c:Person) DETACH DELETE c";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(7u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_1'})-[]->{1,2}(c:Person) where c.name > 'name_2' DETACH DELETE c";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(3u, GmeGqlChanges(conn));

    // duplicate delete
    gqlStr = "Match (a: Person {name: 'name_1'})-[]->{1,2}(c:Person) where c.name > 'name_2' DETACH DELETE c";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(0u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_7'})-[]->{1,2}(c:Person) DETACH DELETE a, c, a";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(8u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person) DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(5u, GmeGqlChanges(conn));

    PrepareGraphData();
    gqlStr = "Match (a: Person {name: 'name_4'})-[]->{0,2}(c:Person) set a.name = 'name_111'";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(1u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_4'})-[]->{0,2}(c:Person) DETACH DELETE c";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(0u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person {name: 'name_111'})-[]->{0,2}(c:Person) DETACH DELETE c";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(7u, GmeGqlChanges(conn));

    gqlStr = "Match (a:Person) DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(16u, GmeGqlChanges(conn));

    PrepareGraphData();
    gqlStr = "Match (a: Person {name: 'name_1'})-[r:直系亲属]->(b:Person) WHERE r.roletype = 'dad'"
             "DETACH DELETE r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(2u, GmeGqlChanges(conn));

    gqlStr = "Match (a: Person)-[r:直系亲属]->(b:Person) WHERE r.roletype = 'dad'"
             "DETACH DELETE r;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    ASSERT_EQ(11u, GmeGqlChanges(conn));
}

/**
 * usecase design:
 * 1. variable not exist
 * 2. delete path
 * 3. delete other expression
 */
HWTEST_P(StEmbGqlDelete, TestGqlErrorDelete, TestSize.Level0)
{
    PrepareGraphData();

    GmeConnT *conn = StEmbGqlGetConn();
    string gqlStr = "Match (a: Person {name: 'name_2'})-[r]->(b:Person {name: 'name_3'}) DETACH DELETE c;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_UNDEFINED_OBJECT);

    gqlStr = "Match p = (a: Person {name: 'name_2'})-[r]->(b:Person {name: 'name_3'}) DETACH DELETE p;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_FEATURE_NOT_SUPPORTED);

    gqlStr = "Match p = (a: Person {name: 'name_2'})-[r]->(b:Person {name: 'name_3'}) DETACH DELETE 1 + 1;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_SYNTAX_ERROR);

    gqlStr = "Match (a: Person {name: 'name_5'})-[r:直系亲属]->{0,2}(c:Person) DETACH DELETE r";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_SYNTAX_ERROR);

    gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_123'}) DETACH DELETE a, b;";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_FEATURE_NOT_SUPPORTED);
}

void StEmbGqlDelete::PrepareBaseCaseData(std::string &gql, GmeConnT *conn)
{
    const int maxVertexNum = 100;
    for (int i = 0; i < maxVertexNum; i++) {
        string name = "name_" + to_string(i);
        int age = i;
        double salary = 3000.0 + i;
        gql = "INSERT (a:Staff {name:\'" + name + "\', age:" + to_string(age) + ", salary:" + to_string(salary) + "})";
        StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    }
    gql = "MATCH (a:Staff {name: 'name_1'}), (b:Staff {name: 'name_3'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_1'}), (b:Staff {name: 'name_4'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_1'}), (b:Staff {name: 'name_6'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_1'}), (b:Staff {name: 'name_5'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_1'}), (b:Staff {name: 'name_8'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_10'}), (b:Staff {name: 'name_4'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_11'}), (b:Staff {name: 'name_4'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_12'}), (b:Staff {name: 'name_4'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_4'}), (b:Staff {name: 'name_10'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "MATCH (a:Staff {name: 'name_13'}), (b:Staff {name: 'name_15'}) INSERT (a)-[:FRIENDS]->(b)";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
}

void StEmbGqlDelete::ExecGqlWithExpectChanges(string gql, uint32_t expectChanges)
{
    GmeConnT *conn = StEmbGqlGetConn();
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    ASSERT_EQ(expectChanges, GmeGqlChanges(conn));
}

HWTEST_P(StEmbGqlDelete, BaseCase, TestSize.Level0)
{
    string gql = "Drop Graph myGraph";
    GmeConnT *conn = StEmbGqlGetConn();
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "CREATE GRAPH myGraph {       \
        (a:Staff {name STRING, age INT, salary DOUBLE}),       \
        (a)-[:FRIENDS]->(a)}";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);

    PrepareBaseCaseData(gql, conn);
    ExecGqlWithExpectChanges("MATCH (a:Staff) Where a.age = 20 DETACH DELETE a", 1u);

    ExecGqlWithExpectChanges("MATCH (a:Staff) Where a.age + 5 > 30 DETACH DELETE a", 74u);

    ExecGqlWithExpectChanges("MATCH (a:Staff) DETACH DELETE a", 35u);

    PrepareBaseCaseData(gql, conn);
    ExecGqlWithExpectChanges("MATCH (a:Staff)-[b:FRIENDS]->(c) DETACH DELETE a", 16u);
    ExecGqlWithExpectChanges("MATCH (a:Staff) DETACH DELETE a", 94u);

    PrepareBaseCaseData(gql, conn);
    ExecGqlWithExpectChanges("MATCH (a:Staff)-[b:FRIENDS]->(c) where c.age - 10 < 18 DETACH DELETE a, c", 21u);

    ExecGqlWithExpectChanges("MATCH (a:Staff) DETACH DELETE a", 89u);

    PrepareBaseCaseData(gql, conn);
    ExecGqlWithExpectChanges("MATCH (a:Staff)-[b:FRIENDS]->(c) DETACH DELETE b", 10u);

    ExecGqlWithExpectChanges("MATCH (a:Staff) DETACH DELETE a", 100u);

    PrepareBaseCaseData(gql, conn);
    ExecGqlWithExpectChanges(
        "MATCH (a:Staff)-[b:FRIENDS]->(c) WHERE c.age * 10 > 60 AND c.salary / 1000 < 60 DETACH DELETE b", 3u);

    ExecGqlWithExpectChanges("MATCH (a:Staff)-[b:FRIENDS]->(c) DETACH DELETE a,b", 11u);

    ExecGqlWithExpectChanges("MATCH (a:Staff) DETACH DELETE a", 96u);

    PrepareBaseCaseData(gql, conn);
    ExecGqlWithExpectChanges("MATCH (a:Staff)-[b:FRIENDS]->(c) WHERE a.age % 2 = 1 DETACH DELETE b, c", 16u);
}

HWTEST_P(StEmbGqlDelete, TestGqlNoLabelDelete, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();

    vector<string> gqlStrs = {
        "MATCH (a) DETACH DELETE a",                      // Person + Event + edges = 10 + 10 + 13 = 33
        "MATCH (a)-[e]->(b) DETACH DELETE a",             // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a)-[e]->(b) DETACH DELETE a,e",           // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a)-[e1]->(b)-[e2]->(c) DETACH DELETE a",  // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a)-[e]->{1,2}(b) DETACH DELETE a",        // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a) WHERE a.title IS NULL DETACH DELETE a",        // 所有Person + 边 = 10 + 13 = 23
        "MATCH (a) WHERE a.title IS NOT NULL DETACH DELETE a",    // 所有EVENT
        "MATCH (a where a.name > 'name_1') DETACH DELETE a",      // 9个person + 13条边
        "MATCH (a) where a.name > 'name_1' DETACH DELETE a",      // 9个person + 13条边
        "MATCH (a) where a.invalid IS NULL DETACH DELETE a",      // 10个person + 13条边 + 10个Event
        "MATCH (a) where a.invalid IS NOT NULL DETACH DELETE a",  // 0
        "MATCH (a)-[e1]->(b)-[e2]->(c) DETACH DELETE a, b, c",  // 7，8为自环，测试同时删除。所有Person + 边 = 23
        "MATCH (a)-[e]->(b) DETACH DELETE a, e, b",             // 多条相同边。所有Person + 边 + 1 = 24
    };
    vector<uint32_t> resultNums = {33u, 22u, 22u, 22u, 22u, 23u, 10u, 22u, 22u, 33u, 0u, 23u, 24u};
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        PrepareGraphData();

        if (i == gqlStrs.size() - 1) {
            string gqlStr =
                "MATCH (a: Person {name: 'name_9'}), (b: Person {name: 'name_10'}) INSERT (a)-[:直系亲属]->(b);";
            StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
        }

        StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
        ASSERT_EQ(resultNums[i], GmeGqlChanges(conn)) << "Failed: " << gqlStr;

        const char *gql = "MATCH (a) DETACH DELETE a";
        StEmbGqlExecCmdWithStatus(conn, gql, GMERR_OK);
        i++;
    }
}

HWTEST_P(StEmbGqlDelete, SpecialCase, TestSize.Level0)
{
    string gql = "Drop Graph myGraph";
    GmeConnT *conn = StEmbGqlGetConn();
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
    gql = "CREATE GRAPH myGraph {       \
        (a:Staff {name STRING, age INT, salary DOUBLE}),       \
        (a)-[:FRIENDS]->(a)}";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);

    PrepareBaseCaseData(gql, conn);
    gql = "DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_SYNTAX_ERROR);

    gql = "MATCH () DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_UNDEFINED_OBJECT);

    gql = "MATCH ()-[a]->() DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);

    gql = "MATCH ()-[]->(a) DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);

    gql = "MATCH (a)-[]->() DETACH DELETE a";
    StEmbGqlExecCmdWithStatus(conn, gql.c_str(), GMERR_OK);
}

// 匹配Person点标签多个点, 带where表达式+比较运算符+逻辑运算符
HWTEST_P(StEmbGqlDelete, TDStest, TestSize.Level0)
{
    // 预置
    PrepareGraphData();

    // 删除多个点
    const char *gql = "MATCH (p:Person) WHERE element_id(p)>='8' AND element_id(p)-'6' <='2' DETACH DELETE p;";
    GmeConnT *conn = StEmbGqlGetConn();
    StEmbGqlExecCmdWithStatus(conn, gql, GMERR_INVALID_VALUE);
}
