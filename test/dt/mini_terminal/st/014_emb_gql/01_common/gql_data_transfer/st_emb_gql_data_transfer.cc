/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_gql_data_transfer.cc
 * Description: UserGraph data transfer implementation class
 * Create: 2024-10-17
 */

#include <fstream>
#include <algorithm>
#include "st_emb_gql_data_transfer.h"

static void SetStrValue(std::string key, std::string &targetStrVal, std::string &strVal)
{
    if (!strVal.empty()) {
        targetStrVal += key + ": '" + strVal + "', ";
    }
}

static void SetBoolValue(std::string key, std::string &targetStrVal, bool boolVal)
{
    std::string strVal = boolVal ? "true" : "false";
    targetStrVal += key + ": " + strVal + ", ";
}

void UserGraphHandler::AssignStringValue(cJSON *node, std::string key, std::string &toAssingStr)
{
    cJSON *json = cJSON_GetObjectItem(node, key.c_str());
    if (json != nullptr) {
        if (json->type == cJSON_String) {
            toAssingStr = (std::string)json->valuestring;
        } else if (json->type == cJSON_Array) {
            // Convert array to std::string and connected with comma
            int arrayLen = cJSON_GetArraySize(json);
            std::string arrayStrVal;
            for (int i = 0; i < arrayLen; ++i) {
                cJSON *arrayItem = cJSON_GetArrayItem(json, i);
                arrayStrVal += (std::string)arrayItem->valuestring;
                arrayStrVal += ((i != arrayLen - 1) ? "," : "");
            }
            toAssingStr = arrayStrVal;
        } else {
            ASSERT_EQ(true, false);
        }
    }
}

void UserGraphHandler::ParsePersonNodeJson(cJSON *node)
{
    PersonVertex person;
    cJSON *nodeName = cJSON_GetObjectItem(node, "nodeName");
    ASSERT_NE(nullptr, nodeName);
    // To avoid name empty, set nodeName as name firstly
    AssignStringValue(node, "nodeName", person.name);
    // Person attributes abstraction
    cJSON *personAttrJson = cJSON_GetObjectItem(node, "attributes");
    ASSERT_NE(nullptr, personAttrJson);
    ASSERT_EQ(false, person.HasUnknownFieldInAttrJson(personAttrJson));

    AssignStringValue(personAttrJson, "name", person.name);
    AssignStringValue(personAttrJson, "gender", person.gender);
    AssignStringValue(personAttrJson, "namePinyin", person.namePinyin);
    AssignStringValue(personAttrJson, "nickname", person.nickname);
    AssignStringValue(personAttrJson, "phoneNumbers", person.phoneNumbers);
    AssignStringValue(personAttrJson, "emails", person.emails);
    AssignStringValue(personAttrJson, "familyAddress", person.familyAddress);
    AssignStringValue(personAttrJson, "organization", person.organization);
    AssignStringValue(personAttrJson, "occupations", person.occupations);
    AssignStringValue(personAttrJson, "position", person.position);
    std::string tmpBoolVal;
    AssignStringValue(personAttrJson, "isUser", tmpBoolVal);
    // 布尔值转小写
    person.isUser = tmpBoolVal == "True" ? true : false;
    cJSON *isUser = cJSON_GetObjectItem(node, "isUser");
    if (cJSON_IsBool(isUser)) {
        person.isUser = cJSON_IsTrue(isUser) ? true : false;
    }
    AssignStringValue(personAttrJson, "birthdate", person.birthdate);
    AssignStringValue(personAttrJson, "id_number", person.id_number);
    AssignStringValue(personAttrJson, "father", person.father);
    AssignStringValue(personAttrJson, "mother", person.mother);
    AssignStringValue(personAttrJson, "aliases", person.aliases);

    person.vertexType = PERSON_TYPE;
    personNodes.push_back(person);
}

void UserGraphHandler::ParseEventNodeJson(cJSON *node)
{
    EventVertex event;
    cJSON *nodeName = cJSON_GetObjectItem(node, "nodeName");
    ASSERT_NE(nullptr, nodeName);
    // To avoid title empty, set nodeName as title firstly
    AssignStringValue(node, "nodeName", event.title);
    // Event attributes abstraction
    cJSON *eventAttrJson = cJSON_GetObjectItem(node, "attributes");
    ASSERT_NE(nullptr, eventAttrJson);
    ASSERT_EQ(false, event.HasUnknownFieldInAttrJson(eventAttrJson));
    // title should not be null
    cJSON *title = cJSON_GetObjectItem(eventAttrJson, "title");
    ASSERT_NE(nullptr, title);
    AssignStringValue(eventAttrJson, "title", event.title);
    AssignStringValue(eventAttrJson, "timeStart", event.timeStart);
    AssignStringValue(eventAttrJson, "timeEnd", event.timeEnd);
    AssignStringValue(eventAttrJson, "participants", event.participants);
    AssignStringValue(eventAttrJson, "participantEmails", event.participantEmails);
    AssignStringValue(eventAttrJson, "eventLocation", event.eventLocation);
    AssignStringValue(eventAttrJson, "description", event.description);

    event.vertexType = EVENT_TYPE;
    eventNodes.push_back(event);
}

void UserGraphHandler::ParseNodesJson(cJSON *nodes)
{
    cJSON *child = nodes->child;
    while (child) {
        std::string nodeStr = child->string;
        cJSON *node = cJSON_GetObjectItem(nodes, nodeStr.c_str());
        ASSERT_NE(nullptr, node);
        cJSON *nodeTypeJson = cJSON_GetObjectItem(node, "nodeType");
        ASSERT_NE(nullptr, nodeTypeJson);
        cJSON *nodeType = cJSON_GetArrayItem(nodeTypeJson, 0);
        ASSERT_NE(nullptr, nodeType);
        STVertexType vertexType = GetVertexType((std::string)nodeType->valuestring);
        if (vertexType == EVENT_TYPE) {
            ParseEventNodeJson(node);
        } else if (vertexType == PERSON_TYPE) {
            ParsePersonNodeJson(node);
        } else {
            ASSERT_EQ(true, false);
            std::cerr << "Unknown nodeType:" << (std::string)nodeType->valuestring << std::endl;
        }
        child = child->next;
    }
}

void UserGraphHandler::ParseEdgesJson(cJSON *edges)
{
    cJSON *child = edges->child;
    while (child) {
        std::string edgeStr = child->string;
        cJSON *edge = cJSON_GetObjectItem(edges, edgeStr.c_str());
        ASSERT_NE(nullptr, edge);
        cJSON *edgeName = cJSON_GetObjectItem(edge, "edgeName");
        ASSERT_NE(nullptr, edgeName);
        cJSON *edgeType = cJSON_GetObjectItem(edge, "edgeType");
        ASSERT_NE(nullptr, edgeType);
        ASSERT_EQ((std::string)edgeName->valuestring, (std::string)edgeType->valuestring);

        cJSON *from = cJSON_GetObjectItem(edge, "from");
        ASSERT_NE(nullptr, from);
        cJSON *to = cJSON_GetObjectItem(edge, "to");
        ASSERT_NE(nullptr, to);

        cJSON *attributes = cJSON_GetObjectItem(edge, "attributes");
        if (attributes != nullptr) {
            if (attributes->valuestring != nullptr) {
                std::cout << edgeStr << "==>" << cJSON_Print(attributes) << std::endl;
            }
        }

        EdgeTopo edgeTopo;
        edgeTopo.from = (std::string)from->valuestring;
        edgeTopo.to = (std::string)to->valuestring;
        edgeTopo.edgeName = (std::string)edgeName->valuestring;
        edgeTopo.edgeType = (std::string)edgeType->valuestring;
        // Edge schema check
        ASSERT_TRUE(IsVertexValid(edgeTopo.from));
        ASSERT_TRUE(IsVertexValid(edgeTopo.to));
        ASSERT_TRUE(IsEdgeValid(edgeTopo.edgeType, edgeTopo.from, edgeTopo.to));
        edgesArray.push_back(edgeTopo);
        child = child->next;
    }
}

void UserGraphHandler::ParserUserGraphData(const char *jsonFilePath)
{
    std::ifstream file(jsonFilePath);
    if (!file.is_open()) {
        std::cerr << "Failed to open file" << std::endl;
        return;
    }
    std::string jsonStr;
    std::string line;
    while (std::getline(file, line)) {
        jsonStr += line;
    }
    cJSON *root = cJSON_Parse(jsonStr.c_str());
    ASSERT_NE(nullptr, root);

    cJSON *content = cJSON_GetObjectItem(root, "content");
    ASSERT_NE(nullptr, content);

    cJSON *nodes = cJSON_GetObjectItem(content, "nodes");
    ASSERT_NE(nullptr, nodes);

    // Parse nodes
    ParseNodesJson(nodes);

    cJSON *edges = cJSON_GetObjectItem(content, "edges");
    ASSERT_NE(nullptr, edges);
    ParseEdgesJson(edges);

    cJSON_Delete(root);
}

std::string EdgeTopo::GenMatchInsertGql()
{
    std::string aVertexName = "Person {name: '";
    std::string bVertexName = "Person {name: '";
    if (edgeType == "参与者") {
        aVertexName = "Event {title: '";
    }
    if (edgeType == "参加" || edgeType == "参与") {
        bVertexName = "Event {title: '";
    }
    std::string matchInsertGql = "MATCH (a: " + aVertexName + this->from + "'}), (b: " + bVertexName + this->to +
                                 "'}) INSERT (a)-[:" + this->edgeName + "]->(b);";
    // std::cout << matchInsertGql << std::endl;
    return matchInsertGql;
}

std::string PersonVertex::GenInsertGql()
{
    std::string insertGql = "INSERT (:Person {";
    SetStrValue("name", insertGql, name);
    SetStrValue("gender", insertGql, gender);
    SetStrValue("namePinyin", insertGql, namePinyin);
    SetStrValue("nickname", insertGql, nickname);
    SetStrValue("phoneNumbers", insertGql, phoneNumbers);
    SetStrValue("emails", insertGql, emails);
    SetStrValue("familyAddress", insertGql, familyAddress);
    SetStrValue("organization", insertGql, organization);
    SetStrValue("occupations", insertGql, occupations);
    SetStrValue("position", insertGql, position);
    SetBoolValue("isUser", insertGql, isUser);
    SetStrValue("birthdate", insertGql, birthdate);
    SetStrValue("id_number", insertGql, id_number);
    SetStrValue("father", insertGql, father);
    SetStrValue("mother", insertGql, mother);
    SetStrValue("aliases", insertGql, aliases);
    insertGql.pop_back();  // pop space
    insertGql.pop_back();  // pop comma
    insertGql += "})";
    return insertGql;
}

bool PersonVertex::HasUnknownFieldInAttrJson(cJSON *attrJson)
{
    cJSON *child = attrJson->child;
    while (child) {
        std::string attrName = (std::string)child->string;
        if (attrName != "name" && attrName != "gender" && attrName != "namePinyin" && attrName != "nickname" &&
            attrName != "phoneNumbers" && attrName != "emails" && attrName != "familyAddress" &&
            attrName != "organization" && attrName != "occupations" && attrName != "position" && attrName != "isUser" &&
            attrName != "birthdate" && attrName != "id_number" && attrName != "aliases" && attrName != "father" &&
            attrName != "mother") {
            std::cerr << "Found unknown Person attrName:" << attrName << std::endl;
            return true;
        }
        child = child->next;
    }
    return false;
}

std::string EventVertex::GenInsertGql()
{
    std::string insertGql = "INSERT (:Event {";
    SetStrValue("title", insertGql, title);
    SetStrValue("timeStart", insertGql, timeStart);
    SetStrValue("timeEnd", insertGql, timeEnd);
    SetStrValue("participants", insertGql, participants);
    SetStrValue("participantEmails", insertGql, participantEmails);
    SetStrValue("eventLocation", insertGql, eventLocation);
    SetStrValue("description", insertGql, description);
    insertGql.pop_back();  // pop space
    insertGql.pop_back();  // pop comma
    insertGql += "})";
    return insertGql;
}

bool EventVertex::HasUnknownFieldInAttrJson(cJSON *attrJson)
{
    cJSON *child = attrJson->child;
    while (child) {
        std::string attrName = (std::string)child->string;
        if (attrName != "title" && attrName != "timeStart" && attrName != "timeEnd" && attrName != "participants" &&
            attrName != "participantEmails" && attrName != "eventLocation" && attrName != "description") {
            std::cerr << "Found unknown Event attrName:" << attrName << std::endl;
            return true;
        }
        child = child->next;
    }
    return false;
}
