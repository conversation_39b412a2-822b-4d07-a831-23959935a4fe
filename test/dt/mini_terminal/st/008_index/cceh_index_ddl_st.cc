/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test for mini kv ddl
 * Author:
 * Create: 2023-12-12
 */

#include "gtest/gtest.h"
#include "cstring"
#include "gme_api.h"
#include "gme_kv.h"
#include "test_mini_util.h"
#include "dm_data_define.h"
#include "se_ckpt.h"
#include "se_ckpt_inner.h"
#include "se_recovery_inner.h"
#include "stub.h"

using namespace testing::ext;
using namespace std;

static inline uint32_t StGetDataModelStrLen(const char *str)
{
    return (uint32_t)(strlen(str) + 1);
}

static char g_cfgPath[128] = R"(
{
    "dataFilePath": "./data/gmdb/datafile"
})";

class CcehIndexDdlSt : public testing::Test {
public:
    GmeConnT *conn;
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    virtual void SetUp()
    {
        InitStub();
        system("rm -rf ./data/gmdb*");
        DbTestMakeDirectory("./data/gmdb", PERM_USRRWX);
        int ret = GmeOpen(g_cfgPath, GME_OPEN_CREATE, &conn);
        ASSERT_EQ(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        int ret = GmeClose(conn);
        EXPECT_EQ(GMERR_OK, ret);
        conn = nullptr;
        system("rm -rf ./data/gmdb*");
        ClearAllStub();
    }
};

static void *ThreadKvDropTable(void *arg)
{
    int thrIndex = *(static_cast<int *>(arg));
    GmeConnT *threadConn = nullptr;
    int ret = GmeOpen(g_cfgPath, GME_OPEN_CREATE, &threadConn);
    EXPECT_EQ(ret, 0);
    for (int i = 0; i < 100; i++) {
        int taleIdx = thrIndex * 100 + i;
        string tableName = "table" + std::to_string(taleIdx);
        EXPECT_EQ(GmeKvDropTable(threadConn, tableName.c_str()), GMERR_OK);
    }
    EXPECT_EQ(GmeClose(threadConn), GMERR_OK);
    return nullptr;
}

HWTEST_F(CcehIndexDdlSt, MultiThreadDropTable, TestSize.Level1)
{
    string name = "table";
    for (int i = 0; i < 1000; ++i) {
        string tablename = name + std::to_string(i);
        ASSERT_EQ(GmeKvCreateTable(conn, tablename.c_str(), R"({"mode":"kv", "indextype":"hash"})"), GMERR_OK);
    }
    int thrNum = 10;
    pthread_t thrKvSet[thrNum];
    int thrIndex[thrNum];
    for (int i = 0; i < thrNum; i++) {
        thrIndex[i] = i;
    }
    for (int i = 0; i < thrNum; i++) {
        EXPECT_EQ(GMERR_OK, pthread_create(&thrKvSet[i], nullptr, ThreadKvDropTable, (void *)&thrIndex[i]));
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thrKvSet[i], nullptr);
    }
}

HWTEST_F(CcehIndexDdlSt, CreateTableTestWithoutConfigTest001, TestSize.Level0)
{
    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, R"({"mode":"kv", "indextype":"hash"})");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(ret, GMERR_OK);
}

HWTEST_F(CcehIndexDdlSt, CreateTableTest01, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100,"mode":"kv", "indextype":"hash"})";

    /**
     * @tc.steps: step1. create a table with valid name
     * @tc.expected: step1. GMERR_OK
     */
    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. create another table with a different valid name
     * @tc.expected: step2. GMERR_OK
     */
    char kvTableName2[] = "student2";
    ret = GmeKvCreateTable(conn, kvTableName2, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step3. create same table with same configJson.
     * @tc.expected: step3. GMERR_DUPLICATE_TABLE
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    /**
     * @tc.steps: step4. create same table with same configJson.
     * @tc.expected: step4. GMERR_DUPLICATE_TABLE
     */
    ret = GmeKvCreateTable(conn, kvTableName2, cfgJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    /**
     * @tc.steps: step5. Clean up
     * @tc.expected: step5. GMERR_OK
     */
    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeKvDropTable(conn, kvTableName2);
    EXPECT_EQ(ret, GMERR_OK);
}

HWTEST_F(CcehIndexDdlSt, CreateTableTest03, TestSize.Level0)
{
    char cfgJsonError1[] = R"({"max_record_count":ad, "mode":"kv", "indextype":"hash"})";
    char cfgJsonError2[] = R"({\"max_record_count\":21029749586429587691238476123, "mode":"kv", "indextype":"hash"})";
    char cfgJsonError3[] = R"({\"max_record_count\":-10, "mode":"kv", "indextype":"hash"})";
    char cfgJsonError4[] = R"({\"max_record_count\":2.3, "mode":"kv", "indextype":"hash"})";
    char cfgJsonError5[] = R"({\"max_record_count\":-2.4, "mode":"kv", "indextype":"hash"})";

    /**
     * @tc.steps: step1. create a table with invalid configJson (use chars, expected positive int)
     * @tc.expected: step1. GMERR_INVALID_JSON_CONTENT
     */
    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJsonError1);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    /**
     * @tc.steps: step2. create a table with invalid configJson (use over-limit int, expected positive int)
     * @tc.expected: step2. GMERR_INVALID_JSON_CONTENT
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJsonError2);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    /**
     * @tc.steps: step3. create a table with invalid configJson (use negative int, expected positive int)
     * @tc.expected: step3. GMERR_INVALID_JSON_CONTENT
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJsonError3);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    /**
     * @tc.steps: step3. create a table with invalid configJson (use decimal, expected positive int)
     * @tc.expected: step3. GMERR_INVALID_JSON_CONTENT
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJsonError4);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    /**
     * @tc.steps: step3. create a table with invalid configJson (use negative decimal, expected positive int)
     * @tc.expected: step3. GMERR_INVALID_JSON_CONTENT
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJsonError5);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

HWTEST_F(CcehIndexDdlSt, CreateTableTest04, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100,"mode":"kv", "indextype":"hash"})";

    /**
     * @tc.steps: step1. create a table with all lower case characters.
     * @tc.expected: step1. GMERR_OK
     */
    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. create a table with all upper case characters.
     * @tc.expected: step2. GMERR_OK
     */
    char kvTableName2[] = "STUDENT";
    ret = GmeKvCreateTable(conn, kvTableName2, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step3. create a table with mix cases characters.
     * @tc.expected: step3. GMERR_OK
     */
    char kvTableName3[] = "StUDENt";
    ret = GmeKvCreateTable(conn, kvTableName3, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step4. clean u
     * @tc.expected: step4. GMERR_OK
     */
    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeKvDropTable(conn, kvTableName2);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeKvDropTable(conn, kvTableName3);
    EXPECT_EQ(ret, GMERR_OK);
}

HWTEST_F(CcehIndexDdlSt, CreateTableTest05, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    /**
     * @tc.steps: step1. create a connection
     * @tc.expected: step1. GMERR_OK
     */
    GmeConnT *conn2 = nullptr;
    int32_t ret = GmeOpen(g_cfgPath, GME_OPEN_CREATE, &conn2);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. create a table with name same as systable using conn1
     * @tc.expected: step2. GMERR_RESTRICT_VIOLATION
     */
    char kvTableName[] = "GM_SYS";
    ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    /**
     * @tc.steps: step3. create a table with valid name using conn2
     * @tc.expected: step3. GMERR_OK
     */
    char kvTableName2[] = "STUDENT";
    ret = GmeKvCreateTable(conn2, kvTableName2, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step4. drop a table with name same as systable using conn2
     * @tc.expected: step4. GMERR_RESTRICT_VIOLATION
     */
    ret = GmeKvDropTable(conn2, kvTableName);
    EXPECT_EQ(ret, GMERR_RESTRICT_VIOLATION);

    /**
     * @tc.steps: step5. drop a table with valid name as systable using conn1
     * @tc.expected: step5. GMERR_OK
     */
    ret = GmeKvDropTable(conn, kvTableName2);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn2);
    ASSERT_EQ(GMERR_OK, ret);
}

/**
 * @tc.name: StMiniKvCcehDDLGetCollProp001
 * @tc.desc: test to get type of kv collection
 * @tc.type: FUNC
 * @tc.author: caihaoting
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLGetCollProp001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create a kv collection
     * @tc.expected: step1. GMERR_OK
     */
    const char *kvTableName = "StMiniKvCcehDDLGetCollProp001";
    const char *cfgJson = R"({"max_record_count":10, "mode":"kv", "indextype":"hash"})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    /**
     * @tc.steps: step2. get collection type
     * @tc.expected: step2. GMERR_OK
     */
    GmeCollectionPropertyT collProp = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(conn, kvTableName, &collProp), GMERR_OK);
    EXPECT_EQ(collProp.type, COLLECTION_KV);

    uint8_t key = 123;
    uint16_t val = 456;
    GmeKvItemT insertKey = {&key, sizeof(uint8_t)};
    GmeKvItemT insertValue = {&val, sizeof(uint16_t)};
    /**
     * @tc.steps: step3. insert a data
     * @tc.expected: step3. GMERR_OK
     */
    EXPECT_EQ(GmeKvSet(conn, kvTableName, &insertKey, &insertValue), GMERR_OK);
    /**
     * @tc.steps: step4. get result
     * @tc.expected: step4. GMERR_OK
     */
    GmeKvItemT actualItem = {0};
    EXPECT_EQ(GmeKvGet(conn, kvTableName, &insertKey, &actualItem), GMERR_OK);
    /**
     * @tc.steps: step5. check result
     * @tc.expected: step5. GMERR_OK
     */
    EXPECT_EQ(insertValue.dataLen, actualItem.dataLen);
    EXPECT_EQ(memcmp(insertValue.data, actualItem.data, insertValue.dataLen), 0);
    (void)GmeKvFreeItem(&actualItem);
    (void)GmeKvDropTable(conn, kvTableName);
}

/**
 * @tc.name: StMiniKvCcehDDLGetCollProp002
 * @tc.desc: test to get type of document collection
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLGetCollProp002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create a document collection
     * @tc.expected: step1. GMERR_OK
     */
    const char *kvTableName = "StMiniKvCcehDDLGetCollProp002";
    const char *cfgJson = R"({"max_record_count":10, "mode":"kv", "indextype":"hash"})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    /**
     * @tc.steps: step2. get collection type
     * @tc.expected: step2. GMERR_OK
     */
    GmeCollectionPropertyT collProp = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(conn, kvTableName, &collProp), GMERR_OK);
    EXPECT_EQ(collProp.type, COLLECTION_KV);

    uint8_t key = 123;
    uint16_t val = 456;
    GmeKvItemT insertKey = {&key, sizeof(uint8_t)};
    GmeKvItemT insertValue = {&val, sizeof(uint16_t)};
    /**
     * @tc.steps: step3. insert a data
     * @tc.expected: step3. GMERR_OK
     */
    EXPECT_EQ(GmeKvSet(conn, kvTableName, &insertKey, &insertValue), GMERR_OK);
    /**
     * @tc.steps: step4. get result
     * @tc.expected: step4. GMERR_OK
     */
    GmeKvItemT actualItem = {0};
    EXPECT_EQ(GmeKvGet(conn, kvTableName, &insertKey, &actualItem), GMERR_OK);
    /**
     * @tc.steps: step5. check result
     * @tc.expected: step5. GMERR_OK
     */
    EXPECT_EQ(insertValue.dataLen, actualItem.dataLen);
    EXPECT_EQ(memcmp(insertValue.data, actualItem.data, insertValue.dataLen), 0);
    (void)GmeKvFreeItem(&actualItem);
    (void)GmeKvDropTable(conn, kvTableName);
}

/**
 * @tc.name: StMiniKvCcehDDLGetCollProp003
 * @tc.desc: test to get collection property wrong
 * @tc.type: FUNC
 * @tc.require: DTS2023112714690
 * @tc.author: caihaoting
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLGetCollProp003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create a document collection
     * @tc.expected: step1. GMERR_OK
     */
    const char *kvTableName = "StMiniKvCcehDDLGetCollProp002";
    const char *cfgJson = R"({"max_record_count":10, "mode":"kv", "indextype":"hash"})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);

    /**
     * @tc.steps: step2. get collection type while conn is nullptr
     * @tc.expected: step2. GMERR_UNEXPECTED_NULL_VALUE
     */
    GmeCollectionPropertyT collProp = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(nullptr, kvTableName, &collProp), GMERR_UNEXPECTED_NULL_VALUE);

    /**
     * @tc.steps: step3. get collection type while tableName is nullptr
     * @tc.expected: step3. GMERR_INVALID_NAME
     */
    ASSERT_EQ(GmeGetCollectionProperty(conn, nullptr, &collProp), GMERR_INVALID_NAME);

    /**
     * @tc.steps: step4. get collection type while collProp is nullptr
     * @tc.expected: step4. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ASSERT_EQ(GmeGetCollectionProperty(conn, kvTableName, nullptr), GMERR_NULL_VALUE_NOT_ALLOWED);

    /**
     * @tc.steps: step5. drop table
     */
    ASSERT_EQ(GmeKvDropTable(conn, kvTableName), GMERR_OK);
}

/**
 * @tc.name: StMiniKvCcehDDLGetCollProp004
 * @tc.desc: test to get collection property from persist file
 * @tc.type: FUNC
 * @tc.author: caihaoting
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLGetCollProp004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create a document collection and kv collection
     * @tc.expected: step1. GMERR_OK
     */
    const char *kvTableName = "kvTable";
    const char *cfgKvJson = R"({"max_record_count":10, "mode":"kv", "indextype":"hash"})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgKvJson), GMERR_OK);
    const char *docTableName = "docTable";
    const char *cfgDocJson = R"({"max_record_count":10})";
    ASSERT_EQ(GmeKvCreateTable(conn, docTableName, cfgDocJson), GMERR_OK);
    GmeCollectionPropertyT collPropKv = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(conn, kvTableName, &collPropKv), GMERR_OK);
    EXPECT_EQ(collPropKv.type, COLLECTION_KV);
    GmeCollectionPropertyT collPropDoc = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(conn, docTableName, &collPropDoc), GMERR_OK);
    EXPECT_EQ(collPropDoc.type, COLLECTION_DOCUMENT);
    /**
     * @tc.steps: step2. close and then open
     * @tc.expected: step2. GMERR_OK
     */
    ASSERT_EQ(GmeClose(conn), GMERR_OK);
    ASSERT_EQ(GmeOpen(g_cfgPath, 0x00, &conn), GMERR_OK);
    /**
     * @tc.steps: step3. get collection type while tableName is nullptr
     * @tc.expected: step3. GMERR_INVALID_NAME
     */
    collPropKv = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(conn, kvTableName, &collPropKv), GMERR_OK);
    EXPECT_EQ(collPropKv.type, COLLECTION_KV);
    collPropDoc = {.type = COLLECTION_UNDEFINED};
    ASSERT_EQ(GmeGetCollectionProperty(conn, docTableName, &collPropDoc), GMERR_OK);
    EXPECT_EQ(collPropDoc.type, COLLECTION_DOCUMENT);
}

/**
 * @tc.name: StMiniKvCcehDDLCreateTableTestWithType001
 * @tc.desc: test to create collection with type
 * @tc.type: FUNC
 * @tc.author: sunhan
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLCreateTableTestWithType001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create a document collection
     * @tc.expected: step1. GMERR_OK
     */
    const char *kvTableName = "table";
    const char *cfgJson = R"({"max_record_count":10})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    /**
     * @tc.steps: step2. create a kv collection with same name
     * @tc.expected: step1. GMERR_OK
     */

    const char *cfgJsonKv = R"({"max_record_count":10, "mode":"kv", "indextype":"hash"})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJsonKv), GMERR_DUPLICATE_TABLE);
}

/**
 * @tc.name: StMiniKvCcehDDLCreateTableTestWithType001
 * @tc.desc: test to create collection with type
 * @tc.type: FUNC
 * @tc.author: sunhan
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLCreateTableTestWithType002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create a document collection
     * @tc.expected: step1. GMERR_OK
     */
    const char *kvTableName = "table";
    const char *cfgJsonKv = R"({"max_record_count":10, "mode":"kv", "indextype":"hash"})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJsonKv), GMERR_OK);
    /**
     * @tc.steps: step2. create a kv collection with same name
     * @tc.expected: step1. GMERR_OK
     */
    const char *cfgJson = R"({"max_record_count":10})";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_DUPLICATE_TABLE);
}

static Status CkptTriggerMock(SeInstanceT *seInsPtr, CkptModeT mode, bool wait, uint32_t timeout)
{
    return GMERR_OK;
}

static StatusInter CkptTriggerImplMock(SeInstanceT *seInsPtr, CkptModeT mode, bool wait, uint32_t timeout)
{
    return STATUS_OK_INTER;
}

static StatusInter RecoveryRedoStub(RedoMgrT *redoMgr, const RedoPointT *startPoint, const RedoPointT *lrpPoint)
{
    return STATUS_OK_INTER;
}

/**
 * @tc.name: StMiniKvCcehDDLFlushData001_1
 * @tc.desc: test to flush data
 * @tc.type: FUNC
 * @tc.author: sunhan
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLFlushData001_1, TestSize.Level0)
{
    SetStubC(reinterpret_cast<void *>(RecoveryRedo), reinterpret_cast<void *>(RecoveryRedoStub));
    const char *cfgJson = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    /**
     * @tc.steps: step1. Create a table
     * @tc.expected: step1. GMERR_OK
     */
    int idx = SetStubC(reinterpret_cast<void *>(CkptTrigger), reinterpret_cast<void *>(CkptTriggerMock));
    EXPECT_GT(idx, 0);
    int idx1 = SetStubC(reinterpret_cast<void *>(CkptTriggerImpl), reinterpret_cast<void *>(CkptTriggerImplMock));
    EXPECT_GT(idx1, 0);
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. Create table again
     * @tc.expected: step2. GMERR_DUPLICATE_TABLE
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    /**
     * @tc.steps: step3. Disable ckpt perform on shutdown server and close server
     * @tc.expected: step3. GMERR_OK
     */

    ret = GmeClose(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ClearStub(idx);
    ClearStub(idx1);
    /**
     * @tc.steps: step4. Restart server
     * @tc.expected: step4. GMERR_OK
     */
    ret = GmeOpen(g_cfgPath, GME_OPEN_ONLY, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    /**
     * @tc.steps: step5. Drop table
     * @tc.expected: step5. GMERR_UNDEFINED_TABLE
     */
    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    /**
     * @tc.steps: step6. Create a table
     * @tc.expected: step6. GMERR_OK
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step7. Flush data, set timeout as 2 seconds
     * @tc.expected: step7. GMERR_OK
     */
    ret = GmeFlushData(conn, 0);
    EXPECT_EQ(GMERR_OK, ret);
    idx = SetStubC(reinterpret_cast<void *>(CkptTrigger), reinterpret_cast<void *>(CkptTriggerMock));
    EXPECT_GT(idx, 0);
    idx1 = SetStubC(reinterpret_cast<void *>(CkptTriggerImpl), reinterpret_cast<void *>(CkptTriggerImplMock));
    EXPECT_GT(idx1, 0);
    /**
     * @tc.steps: step8. Restart server again.
     * @tc.expected: step8. GMERR_OK
     */
    ret = GmeClose(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ClearStub(idx);
    ClearStub(idx1);
    ret = GmeOpen(g_cfgPath, GME_OPEN_ONLY, &conn);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step9. Drop this table.
     * @tc.expected: step9. GMERR_OK
     */
    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(ret, GMERR_OK);
}

/**
 * @tc.name: StMiniKvCcehDDLFlushData001_2
 * @tc.desc: test to flush data
 * @tc.type: FUNC
 * @tc.author: sunhan
 */
HWTEST_F(CcehIndexDdlSt, StMiniKvCcehDDLFlushData001_2, TestSize.Level0)
{
    SetStubC(reinterpret_cast<void *>(RecoveryRedo), reinterpret_cast<void *>(RecoveryRedoStub));
    const char *cfgJson = R"({"max_record_count":100,"mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    /**
     * @tc.steps: step1. Create a table
     * @tc.expected: step1. GMERR_OK
     */
    int idx = SetStubC(reinterpret_cast<void *>(CkptTrigger), reinterpret_cast<void *>(CkptTriggerMock));
    EXPECT_GT(idx, 0);
    int idx1 = SetStubC(reinterpret_cast<void *>(CkptTriggerImpl), reinterpret_cast<void *>(CkptTriggerImplMock));
    EXPECT_GT(idx1, 0);
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. Create table again
     * @tc.expected: step2. GMERR_DUPLICATE_TABLE
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    /**
     * @tc.steps: step3. Disable ckpt perform on shutdown server and close server
     * @tc.expected: step3. GMERR_OK
     */

    ret = GmeClose(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ClearStub(idx);
    ClearStub(idx1);
    /**
     * @tc.steps: step4. Restart server
     * @tc.expected: step4. GMERR_OK
     */
    ret = GmeOpen(g_cfgPath, GME_OPEN_ONLY, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    /**
     * @tc.steps: step5. Drop table
     * @tc.expected: step5. GMERR_UNDEFINED_TABLE
     */
    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    /**
     * @tc.steps: step6. Create a table
     * @tc.expected: step6. GMERR_OK
     */
    ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    idx = SetStubC(reinterpret_cast<void *>(CkptTrigger), reinterpret_cast<void *>(CkptTriggerMock));
    EXPECT_GT(idx, 0);
    idx1 = SetStubC(reinterpret_cast<void *>(CkptTriggerImpl), reinterpret_cast<void *>(CkptTriggerImplMock));
    EXPECT_GT(idx1, 0);
    /**
     * @tc.steps: step8. Restart server again.
     * @tc.expected: step8. GMERR_OK
     */
    ret = GmeClose(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ClearStub(idx);
    ClearStub(idx1);
    ret = GmeOpen(g_cfgPath, GME_OPEN_ONLY, &conn);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step9. Drop this table.
     * @tc.expected: step9. GMERR_OK
     */
    ret = GmeKvDropTable(conn, kvTableName);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
}
