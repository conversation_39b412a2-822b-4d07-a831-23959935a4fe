/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author: yangyongji
 * Create: 2023.3.11
 */

#ifndef ST_INDEX_COMMON_H
#define ST_INDEX_COMMON_H

#include "gtest/gtest.h"
#include "gme_api.h"
#include "gme_kv.h"
#include "securec.h"
#include "se_index.h"
#include "systable_kv.h"
#include "stub.h"

class StIndexCommon : public testing::Test {
public:
    static std::string g_openCfgJson;
    static const char *g_kvTableName;
    static char g_cfgJson[256];
    GmeConnT *m_gmeConn = nullptr;

    virtual void SetUp()
    {
        InitStub();
        ASSERT_EQ(GmeOpen(g_openCfgJson.c_str(), GME_OPEN_CREATE, &m_gmeConn), GMERR_OK);
    }

    virtual void TearDown()
    {
        if (m_gmeConn) {
            EXPECT_EQ(GmeClose(m_gmeConn), GMERR_OK);
            m_gmeConn = nullptr;
        }
        ClearAllStub();
    }

    static void SetUpTestCase(void)
    {
        // first create data file directory
        system("mkdir -p ./data/gmdb/");
    }

    static void TearDownTestCase(void)
    {
        // remove resource
        system("rm ./data/gmdb/ -rf");
    }
};

typedef struct {
    GmeKvItemT *key;
    GmeScanModeE mode;
    GmeMoveDirectionE direction;
} StScanParamsT;

int32_t ScanAllData(GmeConnT *conn, const char *tableName, const StScanParamsT &params, uint32_t *count);

void CompareValue(const GmeKvItemT value, const GmeKvItemT targetValue);

#ifdef ST_OPEN_DEBUG_
#define ST_PRINTF(format, ...) printf(format, ##__VA_ARGS__)
#else
#define ST_PRINTF(format, ...)
#endif

#endif  // ST_INDEX_COMMON_H
