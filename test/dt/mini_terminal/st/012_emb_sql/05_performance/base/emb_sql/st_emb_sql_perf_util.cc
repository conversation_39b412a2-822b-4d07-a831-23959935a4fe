/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: util perf test for sql
 * Author: SQL
 * Create: 2024-03-12
 */
#include "st_emb_sql_perf_util.h"

static const char *TESTSTR_10K = R"(
{"$type":"root","width":"1260.000000","height":"2720.000000","$resolution":"3.2500
00","pageUrl":"pages/home/<USER>","navDstName":"bluetooth_entry","$children":[{"$ID":5,"$type":"Navigation","$rec
t":"[0.00, 0.00],[1260.00,2720.00]","$attrs":{"clickable":false},"$children":[{"$ID":10,"$type":"NavigationContent","$r
ect":"[0.00, 0.00],[1260.00,2720.00]","$attrs":{"clickable":false},"$children":[{"$ID":539,"$type":"NavDestination","$r
ect":"[0.00, 0.00],[1260.00,2720.00]","$attrs":{"id":"Setting.Bluetooth","clickable":false},"$children":[{"$ID":540,"$t
ype":"TitleBar","$rect":"[0.00, 0.00],[1260.00,305.00]","$attrs":{"clickable":false},"$children":[{"$ID":541,"$type":"B
ackButton","$rect":"[52.00, 149.00],[182.00,279.00]","$attrs":{"content":"","id":"__NavdestinationField__BackButton__Ba
ck__Setting.Bluetooth","clickable":true},"$children":[{"$ID":542,"$type":"SymbolGlyph","$rect":"[78.00, 175.00],[156.00
,253.00]","$attrs":{"clickable":false}}]},{"$ID":544,"$type":"Text","$rect":"[208.00, 176.00],[339.00,252.00]","$attrs"
:{"content":"蓝牙","id":"__NavdestinationField__Text__MainTitle__Setting.Bluetooth","clickable":false}}]},{"$ID":543,"$
type":"NavDestinationContent","$rect":"[0.00, 305.00],[1260.00,2720.00]","$attrs":{"clickable":false},"$children":[{"$I
D":547,"$type":"Column","$rect":"[0.00, 305.00],[1260.00,683.00]","$attrs":{"clickable":false},"$children":[{"$ID":582,
"$type":"Column","$rect":"[52.00, 357.00],[1208.00,539.00]","$attrs":{"id":"Setting.Bluetooth.SwitchGroup","clickable":
false},"$children":[{"$ID":591,"$type":"Column","$rect":"[65.00, 370.00],[1195.00,526.00]","$attrs":{"clickable":false}
,"$children":[{"$ID":593,"$type":"Row","$rect":"[65.00, 370.00],[1195.00,526.00]","$attrs":{"id":"Setting.Bluetooth.Swi
tchGroup.SwitchItem","clickable":true},"$children":[{"$ID":595,"$type":"Row","$rect":"[91.00, 370.00],[1169.00,526.00]"
,"$attrs":{"clickable":false},"$children":[{"$ID":598,"$type":"Text","$rect":"[91.00, 418.00],[222.00,479.00]","$attrs"
:{"content":"蓝牙","id":"Setting.Bluetooth.SwitchGroup.SwitchItem.title","clickable":false}},{"$ID":601,"$type":"Toggle
","$rect":"[1052.00, 416.00],[1169.00,481.00]","$attrs":{"id":"Setting.Bluetooth.SwitchGroup.SwitchItem.result","click
able":true}}]}]}]}]},{"$ID":587,"$type":"Column","$rect":"[52.00, 539.00],[1208.00,657.00]","$attrs":{"clickable":fals
e},"$children":[{"$ID":605,"$type":"Text","$rect":"[91.00, 565.00],[1169.00,657.00]","$attrs":{"content":"可被附近的蓝牙
设备发现为“叶客的Mate 60 Pro+”，修改设备名称","clickable":true},"$children":[{"$attrs":{"content":"可被附近的蓝牙设备发现为
“"},"$ID":606,"$type":"Span","$rect":"[91.00, 565.00],[1169.00,657.00]"},{"$attrs":{"content":"叶客的Mate 60 Pro+"},
"$ID":607,"$type":"Span","$rect":"[91.00, 565.00],[1169.00,657.00]"},{"$attrs":{"content":"”，"},"$ID":
608,"$type":"Span","$rect":"[91.00, 565.00],[1169.00,657.00]"},{"$attrs":{"content":"修改设备名称"},"$ID":609,"$type":
"Span","$rect":"[91.00, 565.00],[1169.00,657.00]"}]}]}]},{"$ID":555,"$type":"Stack","$rect":"[0.00, 683.00],[1260.00,
2629.00]","$attrs":{"clickable":false},"$children":[{"$ID":556,"$type":"List","$rect":"[0.00, 683.00],[1260.00,
1861.00]","$attrs":{"clickable":false},"$children":[{"$ID":570,"$type":"ListItem","$rect":"[52.00, 683.00],[1208.00,
865.00]","$attrs":{"clickable":false},"$children":[{"$ID":620,"$type":"Column","$rect":"[52.00, 683.00],[1208.00,
865.00]","$attrs":{"clickable":false},"$children":[{"$ID":625,"$type":"Column","$rect":"[91.00, 761.00],[1169.00,
839.00]","$attrs":{"clickable":false},"$children":[{"$ID":626,"$type":"Row","$rect":"[91.00, 761.00],[1169.00,839.00]",
"$attrs":{"id":"AvailableDeviceHeaderComponent_row","clickable":false},"$children":[{"$ID":627,"$type":"Text","$rect":"
[91.00, 769.00],[274.00,831.00]","$attrs":{"content":"其他设备","clickable":false}},{"$ID":628,"$type":"Column","$rect":
"[274.00, 761.00],[1169.00,839.00]","$attrs":{"clickable":false},"$children":[{"$ID":629,"$type":"LoadingProgress"
,"$rect":"[1091.00, 761.00],[1169.00,839.00]","$attrs":{"id":"loading_progress_","clickable":false}}]}]}]}]}]},{"$ID":
624,"$type":"ListItemGroup","$rect":"[52.00, 865.00],[1208.00,1809.00]","$attrs":{"id":"Setting.Bluetooth.AvailableDev
iceGroup","clickable":false},"$children":[{"$ID":647,"$type":"ListItem","$rect":"[65.00, 878.00],[1195.00,1062.00]","$
attrs":{"clickable":false},"$children":[{"$ID":650,"$type":"Column","$rect":"[65.00, 878.00],[1195.00,1062.00]","$attrs
":{"clickable":false},"$children":[{"$ID":652,"$type":"Row","$rect":"[65.00, 878.00],[1195.00,1060.00]","$attrs":{"id":
"Setting.Bluetooth.AvailableDeviceGroup.1729846017323E07D1C","clickable":true},"$children":[{"$ID":655,"$type":"SymbolG
lyph","$rect":"[91.00, 930.00],[170.00,1008.00]","$attrs":{"id":"Setting.Bluetooth.AvailableDeviceGroup.1729846017323E0
7D1C.symbolIcon","clickable":false}},{"$ID":656,"$type":"Row","$rect":"[222.00, 878.00],[1169.00,1060.00]","$attrs":{"c
lickable":false},"$children":[{"$ID":659,"$type":"Text","$rect":"[222.00, 939.00],[428.00,1000.00]","$attrs":{"content"
:"xBeacon","id":"Setting.Bluetooth.AvailableDeviceGroup.1729846017323E07D1C.title","clickable":false}}]}]},{"$ID":663,"
$type":"Divider","$rect":"[195.00, 1060.00],[1195.00,1062.00]","$attrs":{"clickable":false}}]}]},{"$ID":630,"$type":"Li
stItem","$rect":"[65.00, 1062.00],[1195.00,1246.00]","$attrs":{"clickable":false},"$children":[{"$ID":633,"$type":"Colu
mn","$rect":"[65.00, 1062.00],[1195.00,1246.00]","$attrs":{"clickable":false},"$children":[{"$ID":635,"$type":"Row","$r
ect":"[65.00, 1062.00],[1195.00,1244.00]","$attrs":{"id":"Setting.Bluetooth.AvailableDeviceGroup.1729846017237E07D7C","
clickable":true},"$children":[{"$ID":638,"$type":"SymbolGlyph","$rect":"[91.00, 1114.00],[170.00,1192.00]","$attrs":{"i
d":"Setting.Bluetooth.AvailableDeviceGroup.1729846017237E07D7C.symbolIcon","clickable":false}},{"$ID":639,"$type":"Row"
,"$rect":"[222.00, 1062.00],[1169.00,1244.00]","$attrs":{"clickable":false},"$children":[{"$ID":642,"$type":"Text","$re
ct":"[222.00, 1123.00],[605.00,1184.00]","$attrs":{"content":"CCBBFE35B400","id":"Setting.Bluetooth.AvailableDeviceGrou
p.1729846017237E07D7C.title","clickable":false}}]}]},{"$ID":646,"$type":"Divider","$rect":"[195.00, 1244.00],[1195.00,1
246.00]","$attrs":{"clickable":false}}]}]},{"$ID":698,"$type":"ListItem","$rect":"[65.00, 1246.00],[1195.00,1430.00]","
$attrs":{"clickable":false},"$children":[{"$ID":701,"$type":"Column","$rect":"[65.00, 1246.00],[1195.00,1430.00]","$att
rs":{"clickable":false},"$children":[{"$ID":703,"$type":"Row","$rect":"[65.00, 1246.00],[1195.00,1428.00]","$attrs":{"i
d":"Setting.Bluetooth.AvailableDeviceGroup.172984601893164680C","clickable":true},"$children":[{"$ID":706,"$type":"Symb
olGlyph","$rect":"[91.00, 1298.00],[170.00,1376.00]","$attrs":{"id":"Setting.Bluetooth.AvailableDeviceGroup.17298460189
3164680C.symbolIcon","clickable":false}},{"$ID":707,"$type":"Row","$rect":"[222.00, 1246.00],[1169.00,1428.00]","$attrs
":{"clickable":false},"$children":[{"$ID":710,"$type":"Text","$rect":"[222.00, 1307.00],[524.00,1368.00]","$attrs":{"co
ntent":"EDIFIER BLE","id":"Setting.Bluetooth.AvailableDeviceGroup.172984601893164680C.title","clickable":false}}]}]},{"
$ID":714,"$type":"Divider","$rect":"[195.00, 1428.00],[1195.00,1430.00]","$attrs":{"clickable":false}}]}]},{"$ID":681,"
$type":"ListItem","$rect":"[65.00, 1430.00],[1195.00,1614.00]","$attrs":{"clickable":false},"$children":[{"$ID":684,"$t
ype":"Column","$rect":"[65.00, 1430.00],[1195.00,1614.00]","$attrs":{"clickable":false},"$children":[{"$ID":686,"$type"
:"Row","$rect":"[65.00, 1430.00],[1195.00,1612.00]","$attrs":{"id":"Setting.Bluetooth.AvailableDeviceGroup.172984601845
0D4BBA7","clickable":true},"$children":[{"$ID":689,"$type":"SymbolGlyph","$rect":"[91.00, 1482.00],[170.00,1560.00]","$
attrs":{"id":"Setting.Bluetooth.AvailableDeviceGroup.1729846018450D4BBA7.symbolIcon","clickable":false}},{"$ID":690,"$t
ype":"Row","$rect":"[222.00, 1430.00],[1169.00,1612.00]","$attrs":{"clickable":false},"$children":[{"$ID":693,"$type":"
Text","$rect":"[222.00, 1491.00],[774.00,1552.00]","$attrs":{"content":"海阔天空的Mate 40 Pro","id":"Setting.Bluetooth.Av
ailableDeviceGroup.1729846018450D4BBA7.title","clickable":false}}]}]},{"$ID":697,"$type":"Divider","$rect":"[195.00, 16
12.00],[1195.00,1614.00]","$attrs":{"clickable":false}}]}]},{"$ID":664,"$type":"ListItem","$rect":"[65.00, 1614.00],[11
95.00,1796.00]","$attrs":{"clickable":false},"$children":[{"$ID":667,"$type":"Column","$rect":"[65.00, 1614.00],[1195.0
0,1796.00]","$attrs":{"clickable":false},"$children":[{"$ID":669,"$type":"Row","$rect":"[65.00, 1614.00],[1195.00,1796.
00]","$attrs":{"id":"Setting.Bluetooth.AvailableDeviceGroup.17298460180216021DE","clickable":true},"$children":[{"$ID":
672,"$type":"SymbolGlyph","$rect":"[91.00, 1666.00],[170.00,1744.00]","$attrs":{"id":"Setting.Bluetooth.AvailableDevice
Group.17298460180216021DE.symbolIcon","clickable":false}},{"$ID":673,"$type":"Row","$rect":"[222.00, 1614.00],[1169.00,
1796.00]","$attrs":{"clickable":false},"$children":[{"$ID":676,"$type":"Text","$rect":"[222.00, 1675.00],[636.00,1736.0
0]","$attrs":{"content":"OPPO R11 Pluskt","id":"Setting.Bluetooth.AvailableDeviceGroup.17298460180216021DE.title","clic
kable":false}}]}]}]}]}]}]},{"$ID":576,"$type":"Row","$rect":"[52.00, 683.00],[1208.00,748.00]","$attrs":{"clickable":fa
lse}},{"$ID":577,"$type":"Row","$rect":"[52.00, 1796.00],[1208.00,1861.00]","$attrs":{"clickable":false}}]}]}]}]}]}]}
)";

static Status StEmbSqlBlockingStep(GmeSqlStmtT *stmt)
{
    Status ret;
    do {
        ret = GmeSqlStep(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            break;
        }
        DbSleep(1);
    } while (true);
    return ret;
}

static void StEmbSqlStartTrans(GmeConnT *conn)
{
    if (!g_isAutoTrans) {
        Status ret = GmeSqlExecute(conn, "BEGIN;", NULL, NULL, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
}

static void StEmbSqlEndTrans(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    if (!g_isAutoTrans) {
        StEmbSqlSetTransTimestamp(worker);
        Status ret = GmeSqlExecute(conn, "COMMIT;", NULL, NULL, NULL);
        EXPECT_EQ(ret, GMERR_OK);
        StEmbSqlCalcTransExecuteTimeCost(worker);
    }
}

static void StEmbSqlInsertOneBatch(StEmbSqlWorkerT *worker, GmeConnT *conn, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    Status ret;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        const char *sql = StEmbSqlBuildInsertTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, index_val);
        ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

static void StEmbSqlInsertData(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlInsertOneBatch(worker, conn, index, vals);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d insert data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetInsertCostTime(worker);
}

static void StEmbSqlInsertOneBatchByStmt(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    const uint32_t count = 10;
    char thread[count] = {0};
    int32_t snLen1 = snprintf_s(thread, count, count - 1, "w%u", worker->workerId);
    EXPECT_GE(snLen1, 0);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        Status ret = GmeSqlBindInt64(stmt, 1, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        char buf[count] = {0};
        int32_t snLen2 = snprintf_s(buf, count, count - 1, "t%u", index_val);
        EXPECT_GE(snLen2, 0);
        ret = GmeSqlBindText(stmt, 2, buf, (uint32_t)strlen(buf), NULL);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindInt64(stmt, 3, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindText(stmt, 4, thread, (uint32_t)strlen(thread), NULL);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindDouble(stmt, 5, 0.2);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindText(stmt, 6, "?", 1, NULL);
        ASSERT_EQ(ret, GMERR_OK);
        ret = StEmbSqlBlockingStep(stmt);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlInsertDataByStmt(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    // 统计时间之前预置数据, 离开当前接口时数据占用内存自动销毁
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t preChanges = GmeSqlTotalChanges(conn);
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildInsertTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlInsertOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_EQ(g_maxDmlNum, GmeSqlTotalChanges(conn) - preChanges);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d insert data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetInsertCostTime(worker);
}

static void StEmbSqlUpdateOneBatch(StEmbSqlWorkerT *worker, GmeConnT *conn, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    Status ret;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        int32_t total = g_maxDmlNum + worker->workerId * g_maxDmlNum - index_val;
        const char *sql = StEmbSqlBuildUpdateTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "weight1 =", total, index_val);
        ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

static void StEmbSqlUpdateData(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlUpdateOneBatch(worker, conn, index, vals);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d update all data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetUpdateCostTime(worker);
}

static void StEmbSqlUpdateOneBatchByStmt(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        int32_t total = g_maxDmlNum + worker->workerId * g_maxDmlNum - index_val;
        Status ret = GmeSqlBindInt64(stmt, 2, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindInt64(stmt, 1, total);
        ASSERT_EQ(ret, GMERR_OK);
        ret = StEmbSqlBlockingStep(stmt);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlUpdateDataByStmt(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t preChanges = GmeSqlTotalChanges(conn);
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildUpdateTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlUpdateOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_EQ(g_maxDmlNum, GmeSqlTotalChanges(conn) - preChanges);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d update data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetUpdateCostTime(worker);
}

static void StEmbSqlRangeUpdateBatch(StEmbSqlWorkerT *worker, GmeConnT *conn, uint32_t index)
{
    StEmbSqlStartTrans(conn);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    const char *sql = StEmbSqlBuildRangeUpdateTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "weight2 =", begin, end);
    Status ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    StEmbSqlEndTrans(conn, worker);
}

static void StEmbSqlRangeUpdateData(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlSetBatchTimestamp(worker);
    StEmbSqlClearTransExecTime(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlRangeUpdateBatch(worker, conn, index);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range update data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeUpdateCostTime(worker);
}

// 这里假定更新的范围，和事务提交条数保持一致，隐示事务默认10条数据为一个范围
static void StEmbSqlRangeUpdateOneBatchByStmt(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index)
{
    StEmbSqlStartTrans(conn);
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    Status ret = GmeSqlBindDouble(stmt, 1, begin);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindInt64(stmt, 2, begin);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindInt64(stmt, 3, end);
    ASSERT_EQ(ret, GMERR_OK);
    ret = StEmbSqlBlockingStep(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlRangeUpdateDataByStmt(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildRangeUpdateTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);

    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlRangeUpdateOneBatchByStmt(worker, conn, stmt, index);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range update data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeUpdateCostTime(worker);
}

static void StEmbSqlDeleteBatch(StEmbSqlWorkerT *worker, GmeConnT *conn, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    Status ret;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        const char *sql = StEmbSqlBuildDeleteTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "id =", index_val);
        ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

static void StEmbSqlDeleteData(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlDeleteBatch(worker, conn, index, vals);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d delete all data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetDeleteCostTime(worker);
}

static void StEmbSqlDeleteOneBatchByStmt(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        Status ret = GmeSqlBindInt64(stmt, 1, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        ret = StEmbSqlBlockingStep(stmt);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlDeleteDataByStmt(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t preChanges = GmeSqlTotalChanges(conn);
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildDeleteTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);

    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlDeleteOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_EQ(g_maxDmlNum, GmeSqlTotalChanges(conn) - preChanges);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d delete data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetDeleteCostTime(worker);
}

static Status StEmbSqlReadDataCallback(void *data, uint16_t colCount, char **colValues, char **colNames)
{
    uint32_t threadId = 0;
    if (data != NULL) {
        threadId = (static_cast<StEmbSqlUserDataT *>(data))->threadId;
    }
    StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[threadId];
    StEmbSqlDqlCallback(worker, colCount, colValues, colNames);
    return 0;
}

static void StEmbSqlPointQueryBatch(
    StEmbSqlWorkerT *worker, GmeConnT *conn, uint32_t index, StEmbSqlUserDataT *data, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        const char *sql = StEmbSqlBuildSelectTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "id =", index_val);
        Status ret = GmeSqlExecute(conn, sql, StEmbSqlReadDataCallback, data, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

static void StEmbSqlPointQueryData(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    StEmbSqlUserDataT *data = static_cast<StEmbSqlUserDataT *>(malloc(sizeof(StEmbSqlUserDataT)));
    data->threadId = worker->workerId;
    EXPECT_EQ((data != NULL), true);
    // 读取之前先将统计计数清0
    StEmbSqlResetCount(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlPointQueryBatch(worker, conn, index, data, vals);
    }
    free(data);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Point read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetSelectCostTime(worker);
}

static void StEmbSqlPrintDataByStmt(GmeSqlStmtT *stmt)
{
    if (!g_isPrintData) {
        return;
    }
    int32_t id = (int32_t)GmeSqlColumnInt64(stmt, 0);
    const char *name = GmeSqlColumnText(stmt, 1);
    int32_t weight1 = (int32_t)GmeSqlColumnInt64(stmt, 2);
    const char *note1 = GmeSqlColumnText(stmt, 3);
    double real = GmeSqlColumnDouble(stmt, 4);
    const char *note2 = GmeSqlColumnText(stmt, 5);
    printf("%-15d %-15s %-15d %-15s %-15f %-15s\n", id, name, weight1, note1, real, note2);
}

static void StEmbSqlPointQueryOneBatchByStmt(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index);
        Status ret = GmeSqlBindInt64(stmt, 1, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
            StEmbSqlIncreaseCount(worker);
            uint32_t colCount = GmeSqlColumnCount(stmt);
            ASSERT_EQ(colCount, 6u);
            StEmbSqlPrintDataByStmt(stmt);
        }
        ASSERT_EQ(ret, GMERR_NO_DATA);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlPointQueryDataByStmt(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);

    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildSelectTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    StEmbSqlResetCount(worker);
    StEmbSqlPrintTable();
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlPointQueryOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Point read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetSelectCostTime(worker);
}

static void StEmbSqlRangeReadBatch(StEmbSqlWorkerT *worker, GmeConnT *conn, uint32_t index, StEmbSqlUserDataT *data)
{
    StEmbSqlStartTrans(conn);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    const char *sql = StEmbSqlBuildRangeSelectTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, begin, end);
    Status ret = GmeSqlExecute(conn, sql, StEmbSqlReadDataCallback, static_cast<void *>(data), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    StEmbSqlEndTrans(conn, worker);
}

static void StEmbSqlRangeQueryData(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlSetBatchTimestamp(worker);
    StEmbSqlClearTransExecTime(worker);
    // need fetch every tuple
    StEmbSqlUserDataT *data = static_cast<StEmbSqlUserDataT *>(malloc(sizeof(StEmbSqlUserDataT)));
    EXPECT_EQ((data != NULL), true);
    data->threadId = worker->workerId;
    // 读取之前先将统计计数清0
    StEmbSqlResetCount(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlRangeReadBatch(worker, conn, index, data);
    }
    free(data);
    // 依赖配置事务的隔离级别，多线程场景下可能会有脏读，这里仅判断范围
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeSelectCostTime(worker);
}

// 这里假定查询的范围，和事务提交条数保持一致，隐示事务默认10条数据为一个范围
static void StEmbSqlRangeQueryOneBatchByStmt(StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index)
{
    StEmbSqlStartTrans(conn);
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    Status ret = GmeSqlBindInt64(stmt, 1, begin);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindInt64(stmt, 2, end);
    ASSERT_EQ(ret, GMERR_OK);
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        StEmbSqlIncreaseCount(worker);
        uint32_t colCount = GmeSqlColumnCount(stmt);
        ASSERT_EQ(colCount, 6u);
        StEmbSqlPrintDataByStmt(stmt);
    }
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlRangeQueryDataByStmt(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildRangeSelectTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);

    StEmbSqlResetCount(worker);
    StEmbSqlPrintTable();

    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlRangeQueryOneBatchByStmt(worker, conn, stmt, index);
    }

    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeSelectCostTime(worker);
}

static void *StEmbSqlPerfThreadEntry(void *args)
{
    StEmbSqlUserDataT *data = static_cast<StEmbSqlUserDataT *>(args);
    uint32_t workerId = data->threadId;
    uint32_t workerNum = data->threadNum;
    uint32_t mask = data->mask;

    StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[workerId];
    worker->workerNum = workerNum;

    GmeConnT *conn = NULL;
    StEmbSqlCreateConn(&conn);

    // 根据操作码进行不同的数据操作，实现不组合
    if ((mask & ST_SQL_TEST_INSERT) != 0) {
        StEmbSqlInsertData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_UPDATE) != 0) {
        StEmbSqlUpdateData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_UPDATE) != 0) {
        StEmbSqlRangeUpdateData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_READ) != 0) {
        StEmbSqlPointQueryData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_READ) != 0) {
        StEmbSqlRangeQueryData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_DELETE) != 0) {
        StEmbSqlDeleteData(conn, worker);
    }

    StEmbSqlDestroyConn(conn);
    return NULL;
}

void *StEmbSqlPerfByStmtEntry(void *args)
{
    StEmbSqlUserDataT *data = static_cast<StEmbSqlUserDataT *>(args);
    uint32_t workerId = data->threadId;
    uint32_t workerNum = data->threadNum;
    uint32_t mask = data->mask;

    StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[workerId];
    worker->workerNum = workerNum;

    GmeConnT *conn = NULL;
    StEmbSqlCreateConn(&conn);

    // 根据操作码进行不同的数据操作，实现不组合
    if ((mask & ST_SQL_TEST_INSERT) != 0) {
        StEmbSqlInsertDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_UPDATE) != 0) {
        StEmbSqlUpdateDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_UPDATE) != 0) {
        StEmbSqlRangeUpdateDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_READ) != 0) {
        StEmbSqlPointQueryDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_READ) != 0) {
        StEmbSqlRangeQueryDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_DELETE) != 0) {
        StEmbSqlDeleteDataByStmt(conn, worker);
    }

    StEmbSqlDestroyConn(conn);
    return NULL;
}

void StEmbSqlMultiPerfByStmtWithType(StEmbSqlUserDataT *datas, uint32_t type)
{
    pthread_t pthread[ST_SQL_MAX_THREAD_NUM];
    for (uint32_t i = 0; i < ST_SQL_MAX_THREAD_NUM; i++) {
        datas[i].threadId = i;
        datas[i].threadNum = ST_SQL_MAX_THREAD_NUM;
        datas[i].mask = type;
        pthread_create(&pthread[i], NULL, StEmbSqlPerfByStmtEntry, &datas[i]);
    }
    for (uint32_t i = 0; i < ST_SQL_MAX_THREAD_NUM; i++) {
        pthread_join(pthread[i], NULL);
    }
}

static void StEmbSqlInsertOneBatchByStmtViewTree(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    const uint32_t count = 10240;
    uint32_t textIdxComp = 2;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        Status ret = GmeSqlBindInt64(stmt, 1, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        char buf[count] = {0};
        int32_t snLen2 = snprintf_s(buf, count, count - 1, "%s", TESTSTR_10K);
        EXPECT_GE(snLen2, 0);
        ret = GmeSqlBindText(stmt, textIdxComp, buf, (uint32_t)strlen(buf), NULL);
        ASSERT_EQ(ret, GMERR_OK);
        ret = StEmbSqlBlockingStep(stmt);
        ASSERT_EQ(ret, GMERR_OK);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlInsertDataByStmtViewTree(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    // 统计时间之前预置数据, 离开当前接口时数据占用内存自动销毁
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t preChanges = GmeSqlTotalChanges(conn);
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildInsertTableSqlByStmtViewTree(ST_VIEWTREE_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    printf("batchNum = %d\n", batchNum);
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlInsertOneBatchByStmtViewTree(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_EQ(g_maxDmlNum, GmeSqlTotalChanges(conn) - preChanges);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d insert data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetInsertCostTime(worker);
}

static void StEmbSqlPointQueryOneBatchByStmtViewTree(
    StEmbSqlWorkerT *worker, GmeConnT *conn, GmeSqlStmtT *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqlStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index);
        Status ret = GmeSqlBindInt64(stmt, 1, index_val);
        ASSERT_EQ(ret, GMERR_OK);
        while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
            StEmbSqlIncreaseCount(worker);
            uint32_t colCount = GmeSqlColumnCount(stmt);
            ASSERT_EQ(colCount, 2u);
            const char *name = GmeSqlColumnText(stmt, 1);
            EXPECT_NE(name, nullptr);
            StEmbSqlPrintDataByStmt(stmt);
        }
        ASSERT_EQ(ret, GMERR_NO_DATA);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(ret, GMERR_OK);
    }
    StEmbSqlEndTrans(conn, worker);
}

void StEmbSqlPointQueryDataByStmtViewTree(GmeConnT *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);

    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    const char *sql = StEmbSqlBuildSelectTableSqlByStmt(ST_VIEWTREE_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = GmeSqlPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    StEmbSqlResetCount(worker);
    StEmbSqlPrintTable();
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    printf("batchNum = %d\n", batchNum);
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlPointQueryOneBatchByStmtViewTree(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(GmeSqlFinalize(stmt), GMERR_OK);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Point read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetSelectCostTime(worker);
}
