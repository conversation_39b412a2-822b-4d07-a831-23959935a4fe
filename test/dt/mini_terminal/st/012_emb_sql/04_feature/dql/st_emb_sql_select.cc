/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_select.cc
 * Description:
 * Create: 2024-02-05
 */
#include "gme_sql_api.h"
#include "st_emb_sql_com.h"
#include "st_emb_sql_util.h"
#include "gme_sql_api.h"

using namespace std;
using namespace testing::ext;

class StEmbSqlDQL : public StEmbSqlTestSuitExtend {};

#define ST_T1_COLUMN_LEN 32
typedef struct StEmbSqlT1Data {
    int32_t id;
    int32_t rank;
    char name[ST_T1_COLUMN_LEN];
    char comments[ST_T1_COLUMN_LEN];
} StEmbSqlT1DataT;

typedef struct EmbSqlSelectCase {
    const char *sql;                            // sql语句
    Status expectRet = GMERR_OK;                // 预期返回结果
    StEmbSqlQryResultSetExtendT exptQryResult;  // 预期查询结果
    StEmbSqlBindValueT bindValue;               // 绑定值
} EmbSqlSelectCaseT;

static void StEmbSqlVerifyT1ColumnData(GmeSqlStmtT *stmt, StEmbSqlT1DataT *expect)
{
    int32_t id = (int32_t)GmeSqlColumnInt64(stmt, 0);
    EXPECT_EQ(id, expect->id);
    const char *name = GmeSqlColumnText(stmt, 1);
    EXPECT_EQ(strcmp(name, expect->name), 0);
    int32_t rank = (int32_t)GmeSqlColumnInt64(stmt, 2);
    EXPECT_EQ(rank, expect->rank);
    const char *comments = GmeSqlColumnText(stmt, 3);
    EXPECT_EQ(strcmp(comments, expect->comments), 0);
}

static void StEmbSqlVerifyT1ColumnName(GmeSqlStmtT *stmt)
{
    const char *idText = GmeSqlColumnName(stmt, 0);
    EXPECT_EQ(strcmp(idText, "id"), 0);
    const char *nameText = GmeSqlColumnName(stmt, 1);
    EXPECT_EQ(strcmp(nameText, "name"), 0);
    const char *rankText = GmeSqlColumnName(stmt, 2);
    EXPECT_EQ(strcmp(rankText, "rank"), 0);
    const char *commentsText = GmeSqlColumnName(stmt, 3);
    EXPECT_EQ(strcmp(commentsText, "comments"), 0);
}

static void StEmbSqlInsertT1ColumnData(GmeConnT *conn, const uint32_t insertNum, StEmbSqlT1DataT *insertData)
{
    for (uint32_t i = 0; i < insertNum; i++) {
        StEmbSqlT1DataT *v = &insertData[i];
        v->id = (int32_t)i;
        v->rank = (int32_t)insertNum;
        (void)sprintf_s(v->name, ST_T1_COLUMN_LEN, "name%d", v->id);
        (void)sprintf_s(v->comments, ST_T1_COLUMN_LEN, "comments%d", v->id);
        char insertSql[ST_EMB_SQL_MAX_TEXT_LEN] = {0};
        (void)sprintf_s(insertSql, ST_EMB_SQL_MAX_TEXT_LEN, "insert into t1 values(%d, '%s', %d, '%s');", v->id,
            v->name, v->rank, v->comments);
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, insertSql, NULL, NULL, NULL));
    }
}

TEST_F(StEmbSqlDQL, TestSelectEmptyStmt)
{
    int32_t int32Val = GmeSqlColumnInt(NULL, 1);
    ASSERT_EQ(0, int32Val);
    int64_t int64Val = GmeSqlColumnInt64(NULL, 1);
    ASSERT_EQ(0, int64Val);
    double doubleVal = GmeSqlColumnDouble(NULL, 1);
    ASSERT_EQ(0, doubleVal);
    const char *textVal = GmeSqlColumnText(NULL, 1);
    ASSERT_EQ(NULL, textVal);
    const void *blobVal = GmeSqlColumnBlob(NULL, 1);
    ASSERT_EQ(NULL, blobVal);
    uint32_t dim = 0;
    const float *vectorVal = GmeSqlColumnFloatVector(NULL, 1, &dim);
    ASSERT_EQ(NULL, vectorVal);
    ASSERT_EQ(0, dim);
    uint32_t bytes = GmeSqlColumnBytes(NULL, 1);
    ASSERT_EQ(0, bytes);

    const char *name = GmeSqlColumnName(NULL, 1);
    ASSERT_EQ(NULL, name);
    uint32_t count = GmeSqlColumnCount(NULL);
    ASSERT_EQ(0, count);
    GmeDbDataTypeE type = GmeSqlColumnType(NULL, 1);
    ASSERT_EQ(GME_DB_DATATYPE_NULL, type);
    GmeDbValueT value = GmeSqlColumnValue(NULL, 1);
    ASSERT_EQ(GME_DB_DATATYPE_NULL, value.type);
}

HWTEST_F(StEmbSqlDQL, TestSelectEmptyTable, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *sql = "create table t1(id int, name text);";
    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    Status ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(strcmp(unused, ""), 0);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(ret, GMERR_OK);

    sql = "SELECT * FROM t1;";

    stmt = NULL;
    ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(strcmp(unused, ""), 0);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(ret, GMERR_OK);

    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 校验数据正确性.
HWTEST_F(StEmbSqlDQL, TestSelectCheckData, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据
    const uint32_t insertNum = 30;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);

    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t count = 0;
    for (int32_t i = 0; (ret = GmeSqlStep(stmt)) == GMERR_OK; i++, count++) {
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(4u, columnCount);

        ret = StEmbSqlColumnCheckInt(stmt, 0, insertData[i].id);
        EXPECT_EQ(GMERR_OK, ret);

        ret = StEmbSqlColumnCheckText(stmt, 1, insertData[i].name, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = StEmbSqlColumnCheckInt(stmt, 2, insertData[i].rank);
        EXPECT_EQ(GMERR_OK, ret);

        ret = StEmbSqlColumnCheckText(stmt, 3, insertData[i].comments, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ASSERT_EQ(insertNum, count);

    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestSelectDataByPrimaryKey, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用主键id列查询的是insertData[1]的数据，预期只有1条
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where id = ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindInt64(stmt, 1, insertData[1].id);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[1]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestSelectDataByUniqueKey, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用二级索引name列查询的是insertData[9]的数据，预期只有1条
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where name = ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    const char *name = static_cast<const char *>(insertData[9].name);
    ret = GmeSqlBindText(stmt, 1, name, strlen(name), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[9]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用Int类型的列进行查询
HWTEST_F(StEmbSqlDQL, TestSelectDataByNormalColumn_001, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列rank查询的是insertData[0-9]的数据,因为所有数据的rank字段都是10
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where rank = ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给?绑定查询值10，查询到的是insertData[0-9]的数据
    ret = GmeSqlBindInt64(stmt, 1, 10);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[qryCount]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 10u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用Text类型的列进行查询
HWTEST_F(StEmbSqlDQL, TestSelectDataByNormalColumn_002, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列comments查询的是insertData[9]的数据，预期只有1条
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where comments = ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给?绑定查询值comments9,对应的数组下标是9
    const char *comments = static_cast<const char *>(insertData[9].comments);
    ret = GmeSqlBindText(stmt, 1, comments, strlen(comments), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[9]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用复合索引进行查询
HWTEST_F(StEmbSqlDQL, TestSelectDataByCompositeKey_001, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int, name text, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建复合索引(定长&变长字段)
    const char *createIdx = "create unique index idx1 on t1(rank, id, name);";
    ret = GmeSqlExecute(conn, createIdx, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用复合索引列查询的是insertData[9]的数据，预期只有1条
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where rank = ? and id = ? and name = ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给三个?分别绑定查询值insertData[1]的值
    ret = GmeSqlBindInt64(stmt, 1, insertData[1].rank);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindInt64(stmt, 2, insertData[1].id);
    EXPECT_EQ(ret, GMERR_OK);
    const char *name = static_cast<const char *>(insertData[1].name);
    ret = GmeSqlBindText(stmt, 3, name, strlen(name), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[1]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestSelectDataByCompositeKey_002, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int, name text, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建复合索引(定长&变长字段)
    const char *createIdx = "create unique index idx1 on t1(rank, id, name);";
    ret = GmeSqlExecute(conn, createIdx, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用复合索引列查询的是insertData[2]的数据，预期只有1条
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where id = ? and name = ? and rank = ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给三个?分别绑定查询值insertData[1]的值
    ret = GmeSqlBindInt64(stmt, 1, insertData[2].id);
    EXPECT_EQ(ret, GMERR_OK);
    const char *name = static_cast<const char *>(insertData[2].name);
    ret = GmeSqlBindText(stmt, 2, name, strlen(name), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindInt64(stmt, 3, insertData[2].rank);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[2]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用Int类型的主键列进行范围查询
HWTEST_F(StEmbSqlDQL, TestSelectRangeDataByInt1, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列rank查询的是insertData[0-9]的数据,因为所有数据的rank字段都是10
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where id <= ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给?绑定查询值10，查询到的是insertData[0-9]的数据
    ret = GmeSqlBindInt64(stmt, 1, 1);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[qryCount]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 2u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用Int类型的主键列进行范围查询
HWTEST_F(StEmbSqlDQL, TestSelectRangeDataByInt2, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列rank查询的是insertData[0-9]的数据,因为所有数据的rank字段都是10
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where id >= ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给?绑定查询值10，查询到的是insertData[0-9]的数据
    ret = GmeSqlBindInt64(stmt, 1, 0);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[qryCount]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 10u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用Int类型的一般列进行范围查询
HWTEST_F(StEmbSqlDQL, TestSelectRangeDataByInt3, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列rank查询的是insertData[0-9]的数据,因为所有数据的rank字段都是10
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where rank < ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给?绑定查询值10，查询到的是insertData[0-9]的数据
    ret = GmeSqlBindInt64(stmt, 1, 11);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[qryCount]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 10u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 使用Int类型的一般列进行范围查询
HWTEST_F(StEmbSqlDQL, TestSelectRangeDataByInt4, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-9]
    const uint32_t insertNum = 10;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列rank查询的是insertData[0-9]的数据,因为所有数据的rank字段都是10
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where rank > ?;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    // 给?绑定查询值10，查询到的是insertData[0-9]的数据
    ret = GmeSqlBindInt64(stmt, 1, 9);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt);
        EXPECT_EQ(colCount, 4u);
        // 校验每列的名称
        StEmbSqlVerifyT1ColumnName(stmt);
        // 校验每列的数据
        StEmbSqlVerifyT1ColumnData(stmt, &insertData[qryCount]);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 10u);
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 看护带 ? 的 sql 误用Exec执行
HWTEST_F(StEmbSqlDQL, TestSelectMisusedExec, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GmeSqlExecute(conn, "create table misusedTbl(a int, b text);", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmeSqlExecute(conn, "select ? from misusedTbl;", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    ret = GmeSqlExecute(conn, "select * from misusedTbl where a = ?;", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    ret = GmeSqlExecute(conn, "drop table misusedTbl;", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 看护null约束和null值操作
HWTEST_F(StEmbSqlDQL, SelectNullValue, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 1. 建表、建索引，对id字段添加not null约束
    const char *createTableSql = "create table testNullTable(id int not null, name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *createIdxSql = "create index idxId on testNullTable(id);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 2. 对id字段插入null值，预期失败
    const char *insertNullFailed = "insert into testNullTable values(null, 't0');";
    ret = GmeSqlExecute(conn, insertNullFailed, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // 3. 插入2条数据，其中name字段插入"t0"和null值，预期成功
    const char *insertData = "insert into testNullTable values(0, 't0');"
                             "insert into testNullTable values(1, null);";
    ret = GmeSqlExecute(conn, insertData, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 4. 查询name不为null的数据并校验，预期查到1条数据
    const char *notNullSelectSql = "select * from testNullTable where name is not null;";
    ret = GmeSqlExecute(conn, notNullSelectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "name"}, {{"0", "t0"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    // 5. 查询name为null的数据并校验，预期查到5条数据
    const char *nullSelectSql = "select * from testNullTable where name is null;";
    ret = GmeSqlExecute(conn, nullSelectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult2{{"id", "name"}, {{"1", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult2);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table testNullTable;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 不带from的select查询.err场景:元素不能包含column.
HWTEST_F(StEmbSqlDQL, TestSelectNoFromErr, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    const EmbSqlSelectCaseT cases[] = {
        {
            .sql = "select *;",
            .expectRet = GMERR_SEMANTIC_ERROR,
        },
        {
            .sql = "select count(a);",
            .expectRet = GMERR_SEMANTIC_ERROR,
        },
        {
            .sql = "select a + 1;",
            .expectRet = GMERR_SEMANTIC_ERROR,
        },
        {
            .sql = "select sum(a + 1);",
            .expectRet = GMERR_SEMANTIC_ERROR,
        },
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }
}

HWTEST_F(StEmbSqlDQL, TestSelectNoFrom2, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    const EmbSqlSelectCaseT cases[] = {
        {
            .sql = "select 1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"1"}, {{"1"}}},
        },
        {
            .sql = "select 1 , 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"1", "2"}, {{"1", "2"}}},
        },
        {
            .sql = "select 1+1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"2"}}},
        },
        {
            .sql = "select 1+1 as a, 1-1 as b;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"2", "0"}}},
        },
        {
            .sql = "select count();",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"1"}}},
        },
        {
            .sql = "select sum(1);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"1"}}},
        },
        {
            .sql = "select sum(1), 'xx';",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "xx"}, {{"1", "xx"}}},
        },
        {
            .sql = "select max(1,3,2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"3"}}},
        },
        {
            .sql = "select min(3,2,4);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"2"}}},
        },
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }
}

// 问题单:DTS2024051515106 补充 not null实现
HWTEST_F(StEmbSqlDQL, TestSelectWhereExprNotNull, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    const EmbSqlSelectCaseT cases[] = {
        {
            .sql = "create table t1( a int , b int);",
        },
        {
            .sql = "insert into t1 values (1,2), (2,3);",
        },
        {
            .sql = "select * from t1 where a is not null;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "2"}, {"2", "3"}}},
        },
        {
            .sql = "select * from t1 where a not null;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "2"}, {"2", "3"}}},
        },
        {
            .sql = "drop table t1;",
        },
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }
}

HWTEST_F(StEmbSqlDQL, TestSelectWithDoubleType_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "CREATE TABLE employee (id INT PRIMARY KEY, name TEXT, age REAL DEFAULT (3.14 + "
                                 "3.5), height INT, weight INT);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into employee(id, name, height, weight) values(1, 'zhangsan001', 159, 45);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql = "select * from employee where age = 6.64;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "age", "height", "weight"}, {{"1", "zhangsan001", "6.64", "159", "45"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table employee;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 设置的Double精度为(DB_EPSILON_LOW，DB_EPSILON_UP)
HWTEST_F(StEmbSqlDQL, TestSelectWithDoubleType_02, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "CREATE TABLE employee (id INT PRIMARY KEY, name TEXT, age REAL DEFAULT "
                                 "(6.6400000000000006), height INT, weight INT);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into employee(id, name, height, weight) values(1, 'zhangsan001', 159, 45);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql1[] = {"select * from employee where age = 6.6400000000000002;",
        "select * from employee where age = 6.6400000000000003;",
        "select * from employee where age = 6.6400000000000004;",
        "select * from employee where age = 6.6400000000000005;",
        "select * from employee where age = 6.6400000000000006;",
        "select * from employee where age = 6.6400000000000007;",
        "select * from employee where age = 6.6400000000000008;",
        "select * from employee where age = 6.6400000000000009;",
        "select * from employee where age = 6.6400000000000010;"};
    uint32_t count = sizeof(selectSql1) / sizeof(selectSql1[0]);
    for (uint32_t i = 0; i < count; ++i) {
        ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        StEmbSqlQryResultSetExtendT exptQryResult{
            {"id", "name", "age", "height", "weight"}, {{"1", "zhangsan001", "6.64", "159", "45"}}};
        StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
        StEmbSqlClearActQryResultSet(NULL);
    }

    const char *dropTableSql = "drop table employee;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestSelectWithDoubleType_03, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "CREATE TABLE employee (id INT PRIMARY KEY, name TEXT, age REAL DEFAULT "
                                 "(6.6400000000000006), height INT, weight INT);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into employee(id, name, height, weight) values(1, 'zhangsan001', 159, 45);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql1[] = {"select * from employee where age > 6.6399999999999900;",
        "select * from employee where age < 6.6400000000000100;"};
    uint32_t count = sizeof(selectSql1) / sizeof(selectSql1[0]);
    for (uint32_t i = 0; i < count; ++i) {
        ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        StEmbSqlQryResultSetExtendT exptQryResult{
            {"id", "name", "age", "height", "weight"}, {{"1", "zhangsan001", "6.64", "159", "45"}}};
        StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
        StEmbSqlClearActQryResultSet(NULL);
    }

    const char *dropTableSql = "drop table employee;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// DTS2024052908714:超过51张表关联查询core.
HWTEST_F(StEmbSqlDQL, TestSelectMulitTable, TestSize.Level1)
{
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t len;
    Status ret;
    uint32_t joinLimit = 64;
    // 创建表 + 插入数据
    for (uint32_t i = 0; i < joinLimit; i++) {
        char sqlCreate[ST_EMB_SQL_MAX_TEXT_LEN] = {0};
        len = snprintf_s(sqlCreate, sizeof(sqlCreate), sizeof(sqlCreate),
            "create table query_table_%03u (a int, b int, c text);", i);
        ASSERT_GT(len, 0);
        ret = GmeSqlExecute(conn, sqlCreate, NULL, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);

        char sqlInsert[ST_EMB_SQL_MAX_TEXT_LEN] = {0};
        len = snprintf_s(
            sqlInsert, sizeof(sqlInsert), sizeof(sqlInsert), "insert into query_table_%03u  values (1, 1, 'test');", i);
        ASSERT_GT(len, 0);
        ret = GmeSqlExecute(conn, sqlInsert, NULL, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    errno_t err;
    char sqlQuery[2048] = "select * from ";
    for (uint32_t i = 0; i < joinLimit; i++) {
        // 限定当前用例的表名长度最大为20.
        char tableName[20] = {0};
        len = snprintf_s(tableName, sizeof(tableName), sizeof(tableName), "query_table_%03u", i);
        ASSERT_GT(len, 0);
        err = strcat_s(sqlQuery, sizeof(sqlQuery), tableName);
        ASSERT_EQ(GMERR_OK, err);
        if (i < joinLimit - 1) {
            err = strcat_s(sqlQuery, sizeof(sqlQuery), " join ");
            ASSERT_EQ(GMERR_OK, err);
        }
    }
    err = strcat_s(sqlQuery, sizeof(sqlQuery), ";");
    ASSERT_EQ(GMERR_OK, err);

    // 查询数据.
    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    ret = GmeSqlPrepare(conn, sqlQuery, strlen(sqlQuery) + 1, &stmt, &unused);
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(strcmp(unused, ""), 0);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(ret, GMERR_OK);
    // 查询结果列数 64*3
    uint32_t columnCount = GmeSqlColumnCount(stmt);
    EXPECT_EQ(192u, columnCount);

    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(ret, GMERR_OK);

    // 删除表
    for (uint32_t i = 0; i < joinLimit; i++) {
        char sqlDrop[ST_EMB_SQL_MAX_TEXT_LEN] = {0};
        len = snprintf_s(sqlDrop, sizeof(sqlDrop), sizeof(sqlDrop), "drop table query_table_%03u;", i);
        ASSERT_GT(len, 0);
        ret = GmeSqlExecute(conn, sqlDrop, NULL, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

HWTEST_F(StEmbSqlDQL, TestAlterAndSelect, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "CREATE TABLE alterAndSelectTb1 (id INT , age int , name TEXT, addr TEXT);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into alterAndSelectTb1 values(1, 2, 'xxx', 'www');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *alterSql = "alter table alterAndSelectTb1 drop column name;";
    ret = GmeSqlExecute(conn, alterSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *selectSql1[] = {"select * from alterAndSelectTb1;"};
    uint32_t count = sizeof(selectSql1) / sizeof(selectSql1[0]);
    for (uint32_t i = 0; i < count; ++i) {
        ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        StEmbSqlQryResultSetExtendT exptQryResult{{"id", "age", "addr"}, {{"1", "2", "www"}}};
        StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
        StEmbSqlClearActQryResultSet(NULL);
    }
    const char *dropSql = "drop table alterAndSelectTb1;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestAlterSimpleVL, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "CREATE TABLE simpleTb1 (id INT , age int , count int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into simpleTb1 values(1, 2, 3);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *alterSql = "alter table simpleTb1 drop column age;";
    ret = GmeSqlExecute(conn, alterSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *selectSql1[] = {"select * from simpleTb1;"};
    uint32_t count = sizeof(selectSql1) / sizeof(selectSql1[0]);
    for (uint32_t i = 0; i < count; ++i) {
        ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        StEmbSqlQryResultSetExtendT exptQryResult{{"id", "count"}, {{"1", "3"}}};
        StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
        StEmbSqlClearActQryResultSet(NULL);
    }
    const char *dropSql = "drop table simpleTb1;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 普通查询下，投影列支持绑定参数
HWTEST_F(StEmbSqlDQL, TestSelectProjectBind1, TestSize.Level0)
{
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table TSPB1(id int, value int, name text);",
        },
        {
            .sql = "insert into TSPB1 values(1, 3, 'a'), (1, 4, 'b'), (2, 3, 'a'), (2, 4, 'c');",
        },
        {
            .sql = "select ? from TSPB1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"15"}, {"15"}, {"15"}, {"15"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select ? + 1 from TSPB1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"16"}, {"16"}, {"16"}, {"16"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ? from TSPB1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"16"}, {"16"}, {"16"}, {"16"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ? from TSPB1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"16.50"}, {"16.50"}, {"16.50"}, {"16.50"}}},
            .bindValue = {{"15.5"}, {GME_DB_DATATYPE_FLOAT}},
        },
        {
            .sql = "select ? + ? from TSPB1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"16"}, {"16"}, {"16"}, {"16"}}},
            .bindValue = {{"15", "1"}, {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "drop table TSPB1;",
            .expectRet = GMERR_OK,
        },
    };
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

HWTEST_F(StEmbSqlDQL, TestSelectProjectBind2, TestSize.Level0)
{
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table TSPB2(id int, value int, name text);",
        },
        {
            .sql = "insert into TSPB2 values(1, 3, 'a'), (1, 4, 'b'), (2, 3, 'a'), (2, 4, 'c');",
        },
        {
            .sql = "select name, ? from TSPB2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"name", "?column?"}, {{"a", "15"}, {"b", "15"}, {"a", "15"}, {"c", "15"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select ? + 1, name from TSPB2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "b"}, {"16", "a"}, {"16", "c"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ?, name from TSPB2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "b"}, {"16", "a"}, {"16", "c"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ?, name from TSPB2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16.50", "a"}, {"16.50", "b"}, {"16.50", "a"}, {"16.50", "c"}}},
            .bindValue = {{"15.5"}, {GME_DB_DATATYPE_FLOAT}},
        },
        {
            .sql = "select ? + ?, name from TSPB2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "b"}, {"16", "a"}, {"16", "c"}}},
            .bindValue = {{"15", "1"}, {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "drop table TSPB2;",
            .expectRet = GMERR_OK,
        },
    };
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

// 带limit
HWTEST_F(StEmbSqlDQL, TestSelectProjectBind3, TestSize.Level0)
{
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table TSPB3(id int, value int, name text);",
        },
        {
            .sql = "insert into TSPB3 values(1, 3, 'a'), (1, 4, 'b'), (2, 3, 'a'), (2, 4, 'c');",
        },
        {
            .sql = "select name, ? from TSPB3 limit 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"name", "?column?"}, {{"a", "15"}, {"b", "15"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select ? + 1, name from TSPB3 limit 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "b"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ?, name from TSPB3 limit 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "b"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ?, name from TSPB3 limit 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16.50", "a"}, {"16.50", "b"}}},
            .bindValue = {{"15.5"}, {GME_DB_DATATYPE_FLOAT}},
        },
        {
            .sql = "select ? + ?, name from TSPB3 limit 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "b"}}},
            .bindValue = {{"15", "1"}, {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "drop table TSPB3;",
            .expectRet = GMERR_OK,
        },
    };
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

// 带orderby
HWTEST_F(StEmbSqlDQL, TestSelectProjectBind4, TestSize.Level0)
{
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table TSPB4(id int, value int, name text);",
        },
        {
            .sql = "insert into TSPB4 values(1, 3, 'a'), (1, 4, 'b'), (2, 3, 'a'), (2, 4, 'c');",
        },
        {
            .sql = "select name, ? from TSPB4 order by value ;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"name", "?column?"}, {{"a", "15"}, {"a", "15"}, {"b", "15"}, {"c", "15"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select ? + 1, name from TSPB4 order by value;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "a"}, {"16", "b"}, {"16", "c"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ?, name from TSPB4 order by value;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "a"}, {"16", "b"}, {"16", "c"}}},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "select 1 + ?, name from TSPB4 order by value;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16.50", "a"}, {"16.50", "a"}, {"16.50", "b"}, {"16.50", "c"}}},
            .bindValue = {{"15.5"}, {GME_DB_DATATYPE_FLOAT}},
        },
        {
            .sql = "select ? + ?, name from TSPB4 order by value;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "name"}, {{"16", "a"}, {"16", "a"}, {"16", "b"}, {"16", "c"}}},
            .bindValue = {{"15", "1"}, {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "drop table TSPB4;",
            .expectRet = GMERR_OK,
        },
    };
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

HWTEST_F(StEmbSqlDQL, TestSelectInBind1, TestSize.Level0)
{
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table TSPB4(id int, value int, name text);",
        },
        {
            .sql = "create index value_idx on TSPB4(value);",
        },
        {
            .sql = "insert into TSPB4 values(1, 3, 'a'), (1, 4, 'b'), (2, 3, 'a'), (2, 4, 'c');",
        },
        {
            .sql = "select id, name from TSPB4 where value in (?) ;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "name"}, {{"1", "a"}, {"2", "a"}}},
            .bindValue = {{"3"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "drop table TSPB4;",
            .expectRet = GMERR_OK,
        },
    };
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

// 普通字段缺省绑定.
HWTEST_F(StEmbSqlDQL, TestSelectWithoutBind001, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table tWithoutBind(id int, name text, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    const char *insertSql = "insert into tWithoutBind values(null, null, 3, 'test');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    // 普通字段未绑定1
    const char *queries[] = {
        "SELECT * FROM tWithoutBind WHERE id = ?;",
        "SELECT * FROM tWithoutBind WHERE id > ?;",
        "SELECT * FROM tWithoutBind WHERE id > ?;",
        "SELECT * FROM tWithoutBind WHERE id >= ?;",
        "SELECT * FROM tWithoutBind WHERE id <= ?;",
        "SELECT * FROM tWithoutBind WHERE id >= 1 and id <= ?;",
        "SELECT * FROM tWithoutBind WHERE id >= ? and id <= ?;",
        "SELECT * FROM tWithoutBind WHERE 10 = id + ?;",
        "SELECT * FROM tWithoutBind WHERE 10 = id * ?;",
        "SELECT * FROM tWithoutBind WHERE 10 = id % ?;",
    };

    uint32_t count = sizeof(queries) / sizeof(queries[0]);
    for (uint32_t i = 0; i < count; i++) {
        GmeSqlStmtT *stmt = NULL;
        const char *unused = NULL;
        const char *selectSql = queries[i];
        uint32_t len = (uint32_t)(strlen(selectSql) + 1);
        ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
        EXPECT_EQ(ret, GMERR_OK);
        // 不执行bind, 直接step
        ret = GmeSqlStep(stmt);
        EXPECT_EQ(ret, GMERR_NO_DATA);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    // 普通字段未绑定2
    GmeSqlStmtT *stmt1 = NULL;
    const char *unused1 = NULL;
    const char *selectSql1 = "select * from tWithoutBind where id is ?;";
    uint32_t len1 = (uint32_t)(strlen(selectSql1) + 1);
    ret = GmeSqlPrepare(conn, selectSql1, len1, &stmt1, &unused1);
    EXPECT_EQ(ret, GMERR_OK);
    // 不执行bind, 直接step
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt1), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt1);
        EXPECT_EQ(colCount, 4u);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ret = GmeSqlFinalize(stmt1);
    EXPECT_EQ(ret, GMERR_OK);

    // 删除表
    const char *dropTableSql = "drop table tWithoutBind;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 二级索引未绑定
HWTEST_F(StEmbSqlDQL, TestSelectWithoutBind002, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql =
        "create table tWithoutBind(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    const char *insertSql = "insert into tWithoutBind values(null, null, 3, 'test');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    // 二级索引未绑定1
    const char *queries[] = {
        "SELECT * FROM tWithoutBind WHERE name = ?;",
        "SELECT * FROM tWithoutBind WHERE name > ?;",
        "SELECT * FROM tWithoutBind WHERE name > ?;",
        "SELECT * FROM tWithoutBind WHERE name >= ?;",
        "SELECT * FROM tWithoutBind WHERE name <= ?;",
        "SELECT * FROM tWithoutBind WHERE name >= 1 and name <= ?;",
        "SELECT * FROM tWithoutBind WHERE name >= ? and name <= ?;",
    };

    uint32_t count = sizeof(queries) / sizeof(queries[0]);
    for (uint32_t i = 0; i < count; i++) {
        GmeSqlStmtT *stmt = NULL;
        const char *unused = NULL;
        const char *selectSql = queries[i];
        uint32_t len = (uint32_t)(strlen(selectSql) + 1);
        ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
        EXPECT_EQ(ret, GMERR_OK);
        // 不执行bind, 直接step
        ret = GmeSqlStep(stmt);
        EXPECT_EQ(ret, GMERR_NO_DATA);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    // 二级字段未绑定2
    GmeSqlStmtT *stmt1 = NULL;
    const char *unused1 = NULL;
    const char *selectSql1 = "select * from tWithoutBind where name is ?;";
    uint32_t len1 = (uint32_t)(strlen(selectSql1) + 1);
    ret = GmeSqlPrepare(conn, selectSql1, len1, &stmt1, &unused1);
    EXPECT_EQ(ret, GMERR_OK);
    // 不执行bind, 直接step
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt1), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt1);
        EXPECT_EQ(colCount, 4u);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ret = GmeSqlFinalize(stmt1);
    EXPECT_EQ(ret, GMERR_OK);

    // 删除表
    const char *dropTableSql = "drop table tWithoutBind;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 主键索引未绑定
HWTEST_F(StEmbSqlDQL, TestSelectWithoutBind003, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql =
        "create table tWithoutBind(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    const char *insertSql = "insert into tWithoutBind values(null, null, 3, 'test');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    // 主键索引字段未绑定1
    const char *queries[] = {
        "SELECT * FROM tWithoutBind WHERE id = ?;",
        "SELECT * FROM tWithoutBind WHERE id > ?;",
        "SELECT * FROM tWithoutBind WHERE id > ?;",
        "SELECT * FROM tWithoutBind WHERE id >= ?;",
        "SELECT * FROM tWithoutBind WHERE id <= ?;",
        "SELECT * FROM tWithoutBind WHERE id >= 1 and id <= ?;",
        "SELECT * FROM tWithoutBind WHERE id >= ? and id <= ?;",
    };

    uint32_t count = sizeof(queries) / sizeof(queries[0]);
    for (uint32_t i = 0; i < count; i++) {
        GmeSqlStmtT *stmt = NULL;
        const char *unused = NULL;
        const char *selectSql = queries[i];
        uint32_t len = (uint32_t)(strlen(selectSql) + 1);
        ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
        EXPECT_EQ(ret, GMERR_OK);
        // 不执行bind, 直接step
        ret = GmeSqlStep(stmt);
        EXPECT_EQ(ret, GMERR_NO_DATA);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    // 主键索引字段未绑定1
    GmeSqlStmtT *stmt1 = NULL;
    const char *unused1 = NULL;
    const char *selectSql1 = "select * from tWithoutBind where id is ?;";
    uint32_t len1 = (uint32_t)(strlen(selectSql1) + 1);
    ret = GmeSqlPrepare(conn, selectSql1, len1, &stmt1, &unused1);
    EXPECT_EQ(ret, GMERR_OK);
    // 不执行bind, 直接step
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt1), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt1);
        EXPECT_EQ(colCount, 4u);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ret = GmeSqlFinalize(stmt1);
    EXPECT_EQ(ret, GMERR_OK);

    // 删除表
    const char *dropTableSql = "drop table tWithoutBind;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
// 复合索引未绑定
HWTEST_F(StEmbSqlDQL, TestSelectWithoutBind004, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql =
        "create table tWithoutBind(id int, name text, rank int, comments text, primary key(id , name));";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    const char *insertSql = "insert into tWithoutBind values(null, null, 3, 'test');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    // 主键索引字段未绑定1
    const char *queries[] = {
        "SELECT * FROM tWithoutBind WHERE id = ? and name = ?;",
        "SELECT * FROM tWithoutBind WHERE id = 1 and name = ?;",
        "SELECT * FROM tWithoutBind WHERE id = ? and name = 1;",
        "SELECT * FROM tWithoutBind WHERE id = ? and name >= ? and name <= ?;",
        "SELECT * FROM tWithoutBind WHERE id = 1 and name >= ? and name <= ?;",
        "SELECT * FROM tWithoutBind WHERE id = ? and name >= 1 and name <= ?;",
    };

    uint32_t count = sizeof(queries) / sizeof(queries[0]);
    for (uint32_t i = 0; i < count; i++) {
        GmeSqlStmtT *stmt = NULL;
        const char *unused = NULL;
        const char *selectSql = queries[i];
        uint32_t len = (uint32_t)(strlen(selectSql) + 1);
        ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
        EXPECT_EQ(ret, GMERR_OK);
        // 不执行bind, 直接step
        ret = GmeSqlStep(stmt);
        EXPECT_EQ(ret, GMERR_NO_DATA);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    // 主键索引字段未绑定1
    GmeSqlStmtT *stmt1 = NULL;
    const char *unused1 = NULL;
    const char *selectSql1 = "select * from tWithoutBind where id is ? and name is ?;";
    uint32_t len1 = (uint32_t)(strlen(selectSql1) + 1);
    ret = GmeSqlPrepare(conn, selectSql1, len1, &stmt1, &unused1);
    EXPECT_EQ(ret, GMERR_OK);
    // 不执行bind, 直接step
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt1), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt1);
        EXPECT_EQ(colCount, 4u);
        qryCount++;
    }
    EXPECT_EQ(qryCount, 1u);
    ret = GmeSqlFinalize(stmt1);
    EXPECT_EQ(ret, GMERR_OK);

    // 删除表
    const char *dropTableSql = "drop table tWithoutBind;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestCurdWithoutBind001, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表
    const char *creatTableSql = "CREATE TABLE IF NOT EXISTS tWithoutBind (col1 int primary key , col2 double, col3 "
                                "text unique, col4 blob, col5 floatvector(2));";
    EXPECT_EQ(GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL), GMERR_OK);

    // 插入数据
    const char *insertDataSql = R"(
        INSERT INTO tWithoutBind (col1, col2, col3, col4, col5) VALUES
            (1, 3.14, 'example text 1', '4A6F686E', '[1.0, 2.0]'),
            (2, 1.23, 'example text 2', '4D61726961', '[3.0, 4.0]'),
            (3, 4.56, 'example text 3', '5374726F6E67', '[5.0, 6.0]');
    )";
    EXPECT_EQ(GmeSqlExecute(conn, insertDataSql, NULL, NULL, NULL), GMERR_OK);

    const char *curdSql[] = {
        "INSERT INTO tWithoutBind VALUES (100, ?, 'example text 100', '4A6F686E', '[1.0, 2.0]');",
        "SELECT * FROM tWithoutBind WHERE col2 = ?;",                       // 查询无返回数据, on data
        "SELECT * FROM tWithoutBind WHERE col2 is ?;",                      // 查询到一条数据.
        "UPDATE tWithoutBind SET col3 = 'example text 3' where col2 = ?;",  // 无更新数据, 但返回 OK.
        "UPDATE tWithoutBind SET col3 = 'example text 4' where col2 is ? and col1 = 100;",  // 更新一条数据(若有多条,col3报唯一性冲突)
        "DELETE FROM tWithoutBind WHERE col2 = ?;",
        "DELETE FROM tWithoutBind WHERE col2 is ?;",
    };

    uint32_t count = sizeof(curdSql) / sizeof(curdSql[0]);
    for (uint32_t i = 0; i < count; i++) {
        GmeSqlStmtT *stmt = NULL;
        const char *unused = NULL;
        const char *selectSql = curdSql[i];
        uint32_t len = (uint32_t)(strlen(selectSql) + 1);
        ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
        EXPECT_EQ(ret, GMERR_OK);
        // 不执行bind, 直接step
        ret = GmeSqlStep(stmt);
        if (i == 1) {
            EXPECT_EQ(ret, GMERR_NO_DATA);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    // 删除表
    const char *dropTableSql = "drop table tWithoutBind;";
    EXPECT_EQ(GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL), GMERR_OK);
}

// 测试查询过程中，有其它DDL操作
HWTEST_F(StEmbSqlDQL, TestSelectAndCreateNewTable, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据[0-25]
    const uint32_t insertNum = 25u;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
    // 使用普通列id查询的是insertData的所有数据
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *selectSql = "select * from t1 where id < 25;";
    uint32_t len = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, &unused);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    const uint32_t qryNumber = 15u;  // 当查询到第15条的时候，跳出当前查询流程，去执行建表动作
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        qryCount++;
        if (qryCount == qryNumber) {
            break;
        }
    }
    EXPECT_EQ(qryNumber, qryCount);
    EXPECT_EQ(ret, GMERR_OK);
    creatTableSql = "create table t2(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 建完表以后继续使用stmt查询数据
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        qryCount++;
    }
    EXPECT_EQ(25u, qryCount);  // 预期是查出来25条数据
    EXPECT_EQ(ret, GMERR_NO_DATA);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表
    const char *dropTableSql = "drop table t1;drop table t2;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestGmeSqlBindTextApi, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *sql = "create table tbl1(name text);";
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, sql, NULL, NULL, NULL));

    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    sql = "update tbl1 set name = ?;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    EXPECT_EQ(GMERR_OK, GmeSqlPrepare(conn, sql, len, &stmt, &unused));

    // 构造字符串长度与实际传入长度不符, 是否会越界core
    const char *newText = "12345";
    len = strlen(newText) + 1;
    EXPECT_EQ(GMERR_INVALID_VALUE, GmeSqlBindText(stmt, 1u, newText, len, NULL));

    sql = "drop table tbl1;";
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, sql, NULL, NULL, NULL));
}

HWTEST_F(StEmbSqlDQL, TestTargetListColLimit, TestSize.Level0)
{
    const uint32_t count = 1024;
    string columnDef = "";
    string columnRef = "";
    string columnRef1 = "";
    string columnRef2 = "";
    for (uint32_t i = 0; i < count; i++) {
        if (i == 0) {
            columnDef = "a_" + to_string(i) + " int";
            columnRef = "a_" + to_string(i);
            columnRef1 = "1";
            columnRef2 = "a_" + to_string(i);
        } else {
            columnDef = columnDef + ", a_" + to_string(i) + " int";
            columnRef = columnRef + ", a_" + to_string(i);
            columnRef1 = columnRef1 + ", 1";
            if (i < count - 1) {
                columnRef2 = columnRef2 + ", a_" + to_string(i);
            }
        }
    }
    columnRef2 = columnRef2 + ", sum(a_254)";
    const string createTblSql = "CREATE TABLE TTLCL(" + columnDef + ");";
    const string selectTblSqlNormal1 = "SELECT " + columnRef + " FROM TTLCL;";
    const string selectTblSqlNormal2 = "SELECT " + columnRef1 + ";";
    const string selectTblSqlNormal3 = "SELECT " + columnRef2 + " FROM TTLCL;";
    const string selectTblSqlNormal4 = "SELECT " + columnRef2 + " FROM TTLCL group by a_253;";
    const string selectTblSqlAbNormal1 = "SELECT " + columnRef + ", a_254 FROM TTLCL;";
    const string selectTblSqlAbNormal2 = "SELECT " + columnRef1 + ", 1;";
    GmeConnT *conn = StEmbSqlGetConn();
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, createTblSql.c_str(), NULL, NULL, NULL));
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, selectTblSqlNormal1.c_str(), NULL, NULL, NULL));
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, selectTblSqlNormal2.c_str(), NULL, NULL, NULL));
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, selectTblSqlNormal3.c_str(), NULL, NULL, NULL));
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, selectTblSqlNormal4.c_str(), NULL, NULL, NULL));
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, GmeSqlExecute(conn, selectTblSqlAbNormal1.c_str(), NULL, NULL, NULL));
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, GmeSqlExecute(conn, selectTblSqlAbNormal2.c_str(), NULL, NULL, NULL));
    const char *dropSql = "drop table TTLCL;";
    EXPECT_EQ(GMERR_OK, GmeSqlExecute(conn, dropSql, NULL, NULL, NULL));
}

HWTEST_F(StEmbSqlDQL, TestSelectVector4ColumnCount, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTableSql = "CREATE TABLE test (id INTEGER PRIMARY KEY, data1 TEXT, data2 floatvector(2));";
    Status ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    std::string insertVal = "INSERT INTO test (id, data1, data2) VALUES (?, ?, ?);";
    ret = GmeSqlPrepare(conn, insertVal.c_str(), insertVal.size() + 1, &stmt, &unused);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t idx = 1;  // bind的index是从1开始
    ret = GmeSqlBindInt64(stmt, idx++, 1);
    EXPECT_EQ(GMERR_OK, ret);
    std::string text = "vall";
    ret = GmeSqlBindText(stmt, idx++, text.c_str(), text.size(), nullptr);
    EXPECT_EQ(GMERR_OK, ret);
    std::vector<float> vecs = {1, 2};
    ret = GmeSqlBindFloatVector(stmt, idx++, vecs.data(), vecs.size(), nullptr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    stmt = nullptr;
    unused = nullptr;
    std::string selectSql = "SELECT * FROM test where id = ?;";
    ret = GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size() + 1, &stmt, &unused);
    EXPECT_EQ(GMERR_OK, ret);
    idx = 1;  // bind的index是从1开始
    ret = GmeSqlBindInt(stmt, idx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = GmeSqlColumnCount(stmt);
    EXPECT_EQ(3, count);  // 期望有3列，分别是id, data1, data2
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    const char *sqlDrop = "DROP TABLE test;";
    ret = GmeSqlExecute(conn, sqlDrop, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlDQL, TestSelectWhereClauseUsedAlias1, TestSize.Level0)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据
    const uint32_t insertNum = 30;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);

    const char *selectSql = "select id, rank as f1 from t1 where id + f1 = 33;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "f1"}, {{"3", "30"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

static void UtSqlPrepareData(GmeConnT *conn)
{
    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    Status ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    creatTableSql = "create table t2(id int primary key, comments text);";
    ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入测试数据
    const char *insertSql = "insert into t2 values(1, '');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const uint32_t insertNum = 30;
    StEmbSqlT1DataT insertData[insertNum] = {0};
    StEmbSqlInsertT1ColumnData(conn, insertNum, insertData);
}

// 测试能否正确判断是否是仅count(1)的场景
HWTEST_F(StEmbSqlDQL, TestSelectCountOne, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    UtSqlPrepareData(conn);
    const EmbSqlSelectCaseT cases[] = {
        {
            .sql = "select count(1) from t1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
        // 不支持count(*)的优化
        {
            .sql = "select count(*) from t1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
        // 不支持count(id)的优化
        {
            .sql = "select count(id) from t1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
        // 不支持多个item
        {
            .sql = "select count(1), id from t2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?", "id"}, {{"1", "1"}}},
        },
        // 不支持having
        {
            .sql = "SELECT count(*) FROM t1 HAVING 1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
        // 不支持group by
        {
            .sql = "select count(1) from t2 group by id;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"1"}}},
        },
    };
    for (const auto &testCase : cases) {
        Status ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret) << testCase.sql << endl;
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }

    // 删除表
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "drop table t1;drop table t2;", NULL, NULL, NULL));
}

HWTEST_F(StEmbSqlDQL, TestSelectSubQueryCountOne, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    UtSqlPrepareData(conn);

    const EmbSqlSelectCaseT cases[] = {
        // 不支持where
        {
            .sql = "select count(1) from t1 where 1 = 1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
        // 不支持where里的子查询
        {
            .sql = "SELECT count(1) FROM t1 WHERE (id) IN (SELECT id from t2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"1"}}},
        },
        {
            .sql = "SELECT id FROM t2 WHERE (1) IN (SELECT count(1) from t2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id"}, {{"1"}}},
        },
        // 不支持from有多个表
        {
            .sql = "select count(1) from t1, t2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
        // 不支持from里的子查询
        {
            .sql = "SELECT count(1) from (select avg(id) from t1) a;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"1"}}},
        },
        {
            .sql = "SELECT * from (select count(1) from t1) a;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"?column?"}, {{"30"}}},
        },
    };
    for (const auto &testCase : cases) {
        Status ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret) << testCase.sql << endl;
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }

    // 删除表
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "drop table t1;drop table t2;", NULL, NULL, NULL));
}

// 测试空表count(1)的结果
HWTEST_F(StEmbSqlDQL, TestSelectEmptyTableCountOne, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // 创建表和索引
    const char *creatTableSql = "create table t1(id int primary key, name text unique, rank int, comments text);";
    Status ret = GmeSqlExecute(conn, creatTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const EmbSqlSelectCaseT cases[] = {
        {
            .sql = "select count(1) from t1;",
            .expectRet = GMERR_OK,
        },
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }

    // 删除表
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "drop table t1;", NULL, NULL, NULL));
}

HWTEST_F(StEmbSqlDQL, TestSelectNotBindReturnNoData, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTable = "CREATE TABLE test (id INTEGER PRIMARY KEY AUTOINCREMENT, data1 floatvector(2));";
    Status ret = GmeSqlExecute(conn, createTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertsql = "INSERT INTO test (id, data1) VALUES (1, '[1.2, 2.3]');";
    ret = GmeSqlExecute(conn, insertsql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *selectSql =
        "select id, data1 <-> ? as distance from test where data1 <-> ? < 1 ORDER BY data1 <-> ?  limit 10;";
    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, &unused);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_NO_DATA, GmeSqlStep(stmt));
    EXPECT_EQ(GMERR_OK, GmeSqlFinalize(stmt));

    const char *dropTable = "drop table test;";
    ret = GmeSqlExecute(conn, (char *)dropTable, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtSqlTestCountOneWithUpdate(GmeConnT *conn)
{
    // 更新10条测试数据
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "update t1 set rank = 203 where id < 10;", NULL, NULL, NULL));
    uint32_t updateCnt = 10;
    ASSERT_EQ(GmeSqlChanges(conn), updateCnt);

    const EmbSqlSelectCaseT cases[] = {{
        .sql = "select count(1) from t1;",
        .expectRet = GMERR_OK,
        .exptQryResult = {{"?column?"}, {{"30"}}},
    }};
    for (const auto &testCase : cases) {
        Status ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret) << testCase.sql << endl;
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }
}

static void UtSqlTestCountOneWithDelete(GmeConnT *conn)
{
    Status ret = GmeSqlExecute(conn, "delete from t1 where id >= 25;", NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t deleteCnt = 5;
    ASSERT_EQ(GmeSqlChanges(conn), deleteCnt);

    // 查询数据
    const EmbSqlSelectCaseT cases[] = {{
        .sql = "select count(1) from t1;",
        .expectRet = GMERR_OK,
        .exptQryResult = {{"?column?"}, {{"25"}}},
    }};
    for (const auto &testCase : cases) {
        Status ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret) << testCase.sql << endl;
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }
}

// 测试增删改查后，count(1)的值是否仍然正确
HWTEST_F(StEmbSqlDQL, TestSelectCountOneWithDml, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 插入30条测试数据
    UtSqlPrepareData(conn);
    // 更新10条测试数据并查询数据
    UtSqlTestCountOneWithUpdate(conn);
    // 删除5条测试数据并查询数据
    UtSqlTestCountOneWithDelete(conn);
    // 删除表
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "drop table t1;drop table t2;", NULL, NULL, NULL));
}
