/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_select_groupby.cc
 * Description: test group aggregation functions
 * Create: 2024-03-28
 */

#include "st_emb_sql_com.h"
#include "st_emb_sql_util.h"

using namespace std;
using namespace testing::ext;

class StEmbSqlSelectGroupBy : public StEmbSqlTestSuitExtend {};

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForSumAggFunc_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into t1 values(1,2,100,90),(2,1,100,100),(3,4,90,80),(4,3,90,90),"
                            "(5,4,70,70),(6,3,70,80),(7,2,80,80),(8,1,80,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 根据class进行聚合，包括sum函数
    const char *selectSql = "select class, sum(score0), sum(score1) from t1 group by class order by class;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy{{"class", "?column?", "?column?"},
        {{"1", "180", "190"}, {"2", "180", "170"}, {"3", "160", "170"}, {"4", "160", "150"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy);
    StEmbSqlClearActQryResultSet(NULL);

    //  根据class, id进行聚合，包括sum函数
    selectSql = "select id, class, sum(score0), sum(score1) from t1 group by class, id order by class, id;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{{"id", "class", "?column?", "?column?"},
        {{"2", "1", "100", "100"}, {"8", "1", "80", "90"}, {"1", "2", "100", "90"}, {"7", "2", "80", "80"},
            {"4", "3", "90", "90"}, {"6", "3", "70", "80"}, {"3", "4", "90", "80"}, {"5", "4", "70", "70"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForSumAggFunc_02, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into t1 values(1,1,100,90),(1,1,100,100),(2,2,90,80),(2,2,90,90),"
                            "(4,4,70,70),(4,4,70,80),(3,3,80,80),(3,3,80,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 根据class进行聚合，包括sum函数
    const char *selectSql = "select class, sum(score0) * 2, sum(score1) from t1 group by class order by class;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy{{"class", "?column?", "?column?"},
        {{"1", "400", "190"}, {"2", "360", "170"}, {"3", "320", "170"}, {"4", "280", "150"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy);
    StEmbSqlClearActQryResultSet(NULL);

    //  根据class, id进行聚合，包括sum函数
    selectSql = "select id, class, sum(score0), sum(score1) + sum(score1)  from t1 group by class, id order by "
                "class, id;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{{"id", "class", "?column?", "?column?"},
        {{"1", "1", "200", "380"}, {"2", "2", "180", "340"}, {"3", "3", "160", "340"}, {"4", "4", "140", "300"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForAvgAggFunc_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into t1 values(1,2,100,90),(2,1,100,100),(3,4,90,80),(4,3,90,90),"
                            "(5,4,70,70),(6,3,70,80),(7,2,80,80),(8,1,80,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    //  根据class, id进行聚合，包括avg函数
    const char *selectSql = "select class, avg(score0), avg(score1) from t1 group by class order by class;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy1{{"class", "?column?", "?column?"},
        {{"1", "90", "95"}, {"2", "90", "85"}, {"3", "80", "85"}, {"4", "80", "75"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy1);
    StEmbSqlClearActQryResultSet(NULL);

    //  根据class, id进行聚合，包括avg函数
    selectSql = "select id, class, avg(score0), avg(score1) from t1 group by class, id order by class, id;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{{"id", "class", "?column?", "?column?"},
        {{"2", "1", "100", "100"}, {"8", "1", "80", "90"}, {"1", "2", "100", "90"}, {"7", "2", "80", "80"},
            {"4", "3", "90", "90"}, {"6", "3", "70", "80"}, {"3", "4", "90", "80"}, {"5", "4", "70", "70"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForAvgAggFunc_02, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into t1 values(1,1,100,90),(1,1,100,100),(2,2,90,80),(2,2,90,90),"
                            "(4,4,70,70),(4,4,70,80),(3,3,80,80),(3,3,80,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 根据class进行聚合，包括sum函数
    const char *selectSql = "select class, avg(score0) * 2, avg(score1) from t1 group by class order by class;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy{{"class", "?column?", "?column?"},
        {{"1", "200", "95"}, {"2", "180", "85"}, {"3", "160", "85"}, {"4", "140", "75"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy);
    StEmbSqlClearActQryResultSet(NULL);

    //  根据class, id进行聚合，包括sum函数
    selectSql = "select id, class, avg(score0), avg(score1) + avg(score1)  from t1 group by class, id order by "
                "class, id;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{{"id", "class", "?column?", "?column?"},
        {{"1", "1", "100", "190"}, {"2", "2", "90", "170"}, {"3", "3", "80", "170"}, {"4", "4", "70", "150"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForMixAggFunc_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into t1 values(1,1,100,90),(1,1,100,100),(2,2,90,80),(2,2,90,90),"
                            "(4,4,70,70),(4,4,70,80),(3,3,80,80),(3,3,80,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 根据class进行聚合，包括sum函数
    const char *selectSql = "select class, sum(score0), avg(score0) * 2, avg(score1), sum(score1) from t1 group by "
                            "class order by class;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy{{"class", "?column?", "?column?", "?column?", "?column?"},
        {{"1", "200", "200", "95", "190"}, {"2", "180", "180", "85", "170"}, {"3", "160", "160", "85", "170"},
            {"4", "140", "140", "75", "150"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy);
    StEmbSqlClearActQryResultSet(NULL);

    //  根据class, id进行聚合，包括sum函数
    selectSql = "select id, class, sum(score0), avg(score0), avg(score1) + avg(score1), sum(score1)  from t1 "
                "group by class, id order by class, id;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{{"id", "class", "?column?", "?column?", "?column?", "?column?"},
        {{"1", "1", "200", "100", "190", "190"}, {"2", "2", "180", "90", "170", "170"},
            {"3", "3", "160", "80", "170", "170"}, {"4", "4", "140", "70", "150", "150"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByWithoutAggFunc_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into t1 values(1,1,100,90),(1,1,100,100),(2,2,90,80),(2,2,90,90),"
                            "(4,4,70,70),(4,4,70,80),(3,3,80,80),(3,3,80,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 根据class进行聚合，包括sum函数
    const char *selectSql = "select id, class, score0, score1 from t1 group by class order by class;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryGroupBy{{"id", "class", "score0", "score1"},
        {{"1", "1", "100", "90"}, {"2", "2", "90", "80"}, {"3", "3", "80", "80"}, {"4", "4", "70", "70"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByUsePrepareHandle_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *creatTable = "create table company(id int, name text, age int, address char(50), salary real);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into company values(1, 'Paul', 32, 'California', 20000.00),"
                            "(2, 'Allen', 25, 'Texas', 15000.00),(3, 'Teddy', 23, 'Norway', 20000.00),"
                            "(4, 'Mark', 25, 'Rich-Mond ', 65000.00),(5, 'David', 27, 'Texas', 85000.00),"
                            "(6, 'Kim', 22, 'South-Hall', 45000.00),(7, 'James', 24, 'Houston', 10000.00);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 因为没有携带order by，扫描上来的数据顺序不可预期，保证执行成功即可
    const char *selectSql = "select name, sum(salary) from company group by name;";
    ret = GmeSqlExecute(conn, selectSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    GmeSqlStmtT *stmt = NULL;
    uint32_t len;
    // 根据class进行聚合，包括sum函数
    selectSql = "select name, sum(salary) from company group by name order by name;";
    len = (uint32_t)strlen(selectSql);

    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryGroupBy1{
        {"name", "?column?"}, {{"Allen", "15000.00"}, {"David", "85000.00"}, {"James", "10000.00"}, {"Kim", "45000.00"},
                                  {"Mark", "65000.00"}, {"Paul", "20000.00"}, {"Teddy", "20000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy1);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    insertSql = "insert into company values(8, 'Paul', 24, 'Houston', 20000.00),"
                "(9, 'James', 44, 'Norway', 5000.00), (10, 'James', 45, 'Texas', 5000.00);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    selectSql = "select name, sum(salary) from company group by name order by name desc;";
    len = (uint32_t)strlen(selectSql);
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{
        {"name", "?column?"}, {{"Teddy", "20000.00"}, {"Paul", "40000.00"}, {"Mark", "65000.00"}, {"Kim", "45000.00"},
                                  {"James", "20000.00"}, {"David", "85000.00"}, {"Allen", "15000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    selectSql = "select name, sum(salary) from company group by name order by name asc;";
    len = (uint32_t)strlen(selectSql);
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryGroupBy3{
        {"name", "?column?"}, {{"Allen", "15000.00"}, {"David", "85000.00"}, {"James", "20000.00"}, {"Kim", "45000.00"},
                                  {"Mark", "65000.00"}, {"Paul", "40000.00"}, {"Teddy", "20000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy3);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除表
    const char *dropTable = "drop table company;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByUsePrepareHandle_02, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    char *errMsg = NULL;

    const char *creatTable = "create table company(id int, name text, age int, address char(50), salary real);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into company values(1, 'Paul', 32, 'California', 20000.00),"
                            "(2, 'Allen', 25, 'Texas', 15000.00),(3, 'Teddy', 23, 'Norway', 20000.00),"
                            "(4, 'Mark', 25, 'Rich-Mond ', 65000.00),(5, 'David', 27, 'Texas', 85000.00),"
                            "(6, 'Kim', 22, 'South-Hall', 45000.00),(7, 'James', 24, 'Houston', 10000.00),"
                            "(8, 'Paul', 24, 'Houston', 20000.00), (9, 'James', 44, 'Norway', 5000.00),"
                            "(10, 'James', 45, 'Texas', 5000.00);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);

    const char *selectSql = NULL;
    GmeSqlStmtT *stmt = NULL;
    uint32_t len;
    // 因为没有携带order by，扫描上来的数据顺序不可预期，保证执行成功即可
    selectSql = "select name, sum(salary) from company where salary > 80000.0 group by name;";
    len = (uint32_t)strlen(selectSql);
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryGroupBy1{{"name", "?column?"}, {{"David", "85000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy1);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据class进行聚合，包括sum函数
    selectSql = "select id, name, sum(salary) from company where id < 5 group by name, id order by id desc;";
    len = (uint32_t)strlen(selectSql);
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryGroupBy2{{"id", "name", "?column?"},
        {{"4", "Mark", "65000.00"}, {"3", "Teddy", "20000.00"}, {"2", "Allen", "15000.00"}, {"1", "Paul", "20000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy2);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // group by的列可以不在target list中，但是order by的列要求必须在target list中
    selectSql = "select name, avg(salary), sum(salary) from company group by age order by name desc;";
    len = (uint32_t)strlen(selectSql);
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    // 这里的name并不是唯一的，当前返回的是最先遇到的数据
    StEmbSqlQryResultSetExtendT exptQryGroupBy3{{"name", "?column?", "?column?"},
        {{"Teddy", "20000.00", "20000.00"}, {"Paul", "20000.00", "20000.00"}, {"Kim", "45000.00", "45000.00"},
            {"James", "5000.00", "5000.00"}, {"James", "5000.00", "5000.00"}, {"James", "15000.00", "30000.00"},
            {"David", "85000.00", "85000.00"}, {"Allen", "40000.00", "80000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy3);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    selectSql = "select id, name, avg(age), sum(salary) from company group by id order by id asc, name desc;";
    len = (uint32_t)strlen(selectSql);
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryGroupBy4{{"id", "name", "?column?", "?column?"},
        {{"1", "Paul", "32.00", "20000.00"}, {"2", "Allen", "25.00", "15000.00"}, {"3", "Teddy", "23.00", "20000.00"},
            {"4", "Mark", "25.00", "65000.00"}, {"5", "David", "27.00", "85000.00"}, {"6", "Kim", "22.00", "45000.00"},
            {"7", "James", "24.00", "10000.00"}, {"8", "Paul", "24.00", "20000.00"}, {"9", "James", "44.00", "5000.00"},
            {"10", "James", "45.00", "5000.00"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryGroupBy4);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除表
    const char *dropTable = "drop table company;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForErrorStatement_01, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    char *errMsg = NULL;

    const char *creatTable = "create table t1(id int, class int, score0 int, score1 double);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    const char *insertSql = "insert into t1 values(1,2,100,90),(2,1,100,100),(3,4,90,80),(4,3,90,90);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);

    const char *testCase[] = {"select class, sum(score0), sum(score1) from t1 group by class + 1 order by class;",
        "select class, sum(score0), sum(score1) from t1 group by 1 order by class;",
        "select class, sum(score0), sum(score1) from t1 group by 1 + 1 order by class;",
        "select class, sum(score0), sum(score1) from t1 group by NULL order by class;",
        "select class, sum(score0), sum(score1) from t1 group by sum(score0) order by class;"};
    uint32_t count = ELEMENT_COUNT(testCase);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmeSqlExecute(conn, testCase[i], NULL, NULL, NULL);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);
}

// 测试分组数据内存超过operatorMemory上下，需要使用磁盘DIST_HASH_AGGREGATE算法
HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForDiskHashAgg_01, TestSize.Level1)
{
    GmeConnT *conn = StEmbSqlGetConn();
    char *errMsg = NULL;

    const char *creatTable = "create table t1(id int, lob1 text, lob2 text, lob3 text, lob4 text, lob5 text);";
    Status ret = GmeSqlExecute(conn, creatTable, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);

    const char *sql = "INSERT INTO t1(id, lob1, lob2, lob3, lob4, lob5) VALUES(?, ?, ?, ?, ?, ?);";
    GmeSqlStmtT *stmt = NULL;
    ret = GmeSqlPrepare(conn, sql, strlen(sql), &stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const uint32_t total = 1000;
    const int32_t group = total / 2;
    int32_t id = 0;
    char lob[1024] = {0};
    (void)memset_s(lob, sizeof(lob), 'a', sizeof(lob) - 1);
    for (uint32_t i = 0; i < total; i++) {
        id = (id > group) ? 0 : id;
        ret = GmeSqlBindInt(stmt, 1, id++);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindText(stmt, 2, lob, strlen(lob), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindText(stmt, 3, lob, strlen(lob), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindText(stmt, 4, lob, strlen(lob), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindText(stmt, 5, lob, strlen(lob), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindText(stmt, 6, lob, strlen(lob), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlStep(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    const char *selectSql = "select id, length(lob1), count(lob2) from t1 group by id order by id;";
    uint32_t len = (uint32_t)strlen(selectSql);
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, len, &stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlCheckActQryResultSetSize(NULL, 3, 501);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 删除表
    const char *dropTable = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTable, NULL, NULL, &errMsg);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByBindNoSupported, TestSize.Level0)
{
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table t1(id int, class int, score0 int, score1 double);",
        },
        {
            .sql = "insert into t1 values(1,2,100,90),(2,1,100,100),(3,4,90,80),(4,3,90,90);",
        },
        {
            .sql = "select * from t1 group by ?;",
            .expectRet = GMERR_SYNTAX_ERROR,
            .exptQryResult = {},
            .bindValue = {{"15"}, {GME_DB_DATATYPE_INTEGER}},
        },
        {
            .sql = "drop table t1;",
            .expectRet = GMERR_OK,
        },
    };

    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

// group by后带向量
HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByForVector, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table Tb1 (id INT, val INT, repr floatvector(3), repr1 floatvector(3));",
        },
        {
            .sql = "insert into Tb1 values "
                   "(1, 2, '[1.2, 3.4, 5.6]', '[1.2, 3.4, 5.6]'), (5, 6, '[0.8, 2.1, 4.9]', '[1.2, 3.4, 5.6]'), "
                   "(3, 4, '[1.0, 3.0, 5.0]', '[1.2, 3.4, 5.6]'), (6, 7, '[1.0, 3.0, 5.0]', '[1.2, 3.4, 5.6]'), "
                   "(5, 7, '[2.0, 3.0, 5.0]', '[1.2, 3.4, 5.6]');",
        },
        {
            .sql = "select id, val, repr from Tb1 group by repr;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "val", "repr"},
                {{"3", "4", "[1.000000, 3.000000, 5.000000]"}, {"1", "2", "[1.200000, 3.400000, 5.600000]"},
                    {"5", "6", "[0.800000, 2.100000, 4.900000]"}, {"5", "7", "[2.000000, 3.000000, 5.000000]"}}},
        },
        {
            .sql = "select id, val, repr from (select * from Tb1) as t1 group by repr;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "val", "repr"},
                {{"3", "4", "[1.000000, 3.000000, 5.000000]"}, {"1", "2", "[1.200000, 3.400000, 5.600000]"},
                    {"5", "6", "[0.800000, 2.100000, 4.900000]"}, {"5", "7", "[2.000000, 3.000000, 5.000000]"}}},
        },
        {
            .sql = "select * from (select id, val, repr from Tb1 group by repr) as t1;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "val", "repr"},
                {{"3", "4", "[1.000000, 3.000000, 5.000000]"}, {"1", "2", "[1.200000, 3.400000, 5.600000]"},
                    {"5", "6", "[0.800000, 2.100000, 4.900000]"}, {"5", "7", "[2.000000, 3.000000, 5.000000]"}}},
        },
        {
            .sql = "DROP TABLE Tb1;",
            .expectRet = GMERR_OK,
        },
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(NULL, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(NULL);
        }
    }
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByNullForVector, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *sql = "create table Tb1 (id INT, val INT, repr floatvector(3), name Text);";
    Status ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "insert into Tb1 values "
          "(1, 2, '[1.2, 3.4, 5.6]', null), (5, 6, '[0.8, 2.1, 4.9]', null), (3, 4, '[1.0, 3.0, 5.0]', null), "
          "(6, 7, '[1.0, 3.0, 5.0]', null), (5, 7, '[2.0, 3.0, 5.0]', null);";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "select * from Tb1 group by name;";
    ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    StEmbSqlQryResultSetExtendT exptQryResult1{
        {"id", "val", "repr", "name"}, {{"1", "2", "[1.200000, 3.400000, 5.600000]", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    sql = "drop table Tb1;";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByWithMutilTable, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *sql = "Drop Table IF EXISTS t1; Drop Table IF EXISTS t2;";
    Status ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "CREATE TABLE t1(id int unique, repr floatvector(3));";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "CREATE TABLE t2(id int unique, repr floatvector(3), name varchar(10));";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "INSERT INTO t1 VALUES "
          "(1, '[2.4,3.3,4.1]'), (2, '[7.1,20.1,5.1]'), (3, '[4.4,3.3,7.1]'), (4, '[3.7,46.7,24.1]');";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "INSERT INTO t2 VALUES "
          "(1, '[3.0,4.1,8.1]' , 'Amily'), (2, '[3.2,4.3,8.5]', 'AMILY'), (3, '[3.0,4.1,58.1]', 'John'), "
          "(4, '[3.0,4.1,8.1]', 'John'), (5, '[3.2,4.3,8.5]', NULL), (6, '[3.7,46.7,24.1]', NULL);";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    sql = "SELECT * FROM t1 a, t2 b GROUP BY a.repr, b.repr order by id;";
    ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "repr", "id", "repr", "name"},
        {{"1", "[2.400000, 3.300000, 4.100000]", "3", "[3.000000, 4.100000, 58.099998]", "John"},
            {"1", "[2.400000, 3.300000, 4.100000]", "2", "[3.200000, 4.300000, 8.500000]", "AMILY"},
            {"1", "[2.400000, 3.300000, 4.100000]", "1", "[3.000000, 4.100000, 8.100000]", "Amily"},
            {"1", "[2.400000, 3.300000, 4.100000]", "6", "[3.700000, 46.700001, 24.100000]", "null"},
            {"2", "[7.100000, 20.100000, 5.100000]", "3", "[3.000000, 4.100000, 58.099998]", "John"},
            {"2", "[7.100000, 20.100000, 5.100000]", "2", "[3.200000, 4.300000, 8.500000]", "AMILY"},
            {"2", "[7.100000, 20.100000, 5.100000]", "6", "[3.700000, 46.700001, 24.100000]", "null"},
            {"2", "[7.100000, 20.100000, 5.100000]", "1", "[3.000000, 4.100000, 8.100000]", "Amily"},
            {"3", "[4.400000, 3.300000, 7.100000]", "3", "[3.000000, 4.100000, 58.099998]", "John"},
            {"3", "[4.400000, 3.300000, 7.100000]", "2", "[3.200000, 4.300000, 8.500000]", "AMILY"},
            {"3", "[4.400000, 3.300000, 7.100000]", "6", "[3.700000, 46.700001, 24.100000]", "null"},
            {"3", "[4.400000, 3.300000, 7.100000]", "1", "[3.000000, 4.100000, 8.100000]", "Amily"},
            {"4", "[3.700000, 46.700001, 24.100000]", "2", "[3.200000, 4.300000, 8.500000]", "AMILY"},
            {"4", "[3.700000, 46.700001, 24.100000]", "1", "[3.000000, 4.100000, 8.100000]", "Amily"},
            {"4", "[3.700000, 46.700001, 24.100000]", "6", "[3.700000, 46.700001, 24.100000]", "null"},
            {"4", "[3.700000, 46.700001, 24.100000]", "3", "[3.000000, 4.100000, 58.099998]", "John"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    sql = "Drop Table t1; Drop Table t2;";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByUsingVectorIndex, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;
    const EmbSqlCommonCaseT cases[] = {
        {
            .sql = "create table Tb1 (id INT, val INT, repr floatvector(3));",
        },
        {
            .sql = "create index idx_val on Tb1 (val);",
        },
        {
            .sql = "insert into Tb1 values "
                   "(1, 1, '[1.0, 3.0, 5.0]'), (1, 1, '[9.0, 8.0, 7.0]'), (1, 1, '[7.0, 8.0, 9.0]'),"
                   "(2, 1, '[1.0, 3.0, 5.0]'), (2, 1, '[9.0, 8.0, 7.0]'), (2, 1, '[7.0, 8.0, 9.0]'),"
                   "(3, 1, '[1.0, 3.0, 5.0]'), (3, 1, '[9.0, 8.0, 7.0]'), (3, 1, '[7.0, 8.0, 9.0]'),"
                   "(4, 1, '[1.0, 3.0, 5.0]'), (4, 1, '[9.0, 8.0, 7.0]'), (4, 1, '[7.0, 8.0, 9.0]'),"
                   "(5, 1, '[1.0, 3.0, 5.0]'), (5, 1, '[9.0, 8.0, 7.0]'), (5, 1, '[7.0, 8.0, 9.0]');",
        },
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql, NULL, NULL, NULL);
        EXPECT_EQ(testCase.expectRet, ret);
    }
    // repr非索引，val是索引，因此走纯标量索引
    const char *sql =
        "select id, val, repr <=> '[1.0, 3.0, 5.0]' from Tb1 where repr <=> '[1.0, 3.0, 5.0]' < 0.1 and val = 1 "
        "group by id having min(repr <=> '[1.0, 3.0, 5.0]') order by repr <=> '[1.0, 3.0, 5.0]' limit 4 offset 0;";
    ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult1{
        {"id", "val", "?column?"}, {{"2", "1", "0"}, {"1", "1", "0"}, {"4", "1", "0"}, {"3", "1", "0"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    // repr非索引，id也非索引，因此走全表扫描
    sql = "select id, val, repr <=> '[1.0, 3.0, 5.0]' from Tb1 where repr <=> '[1.0, 3.0, 5.0]' < 0.1 and id != 0 "
          "group by id having min(repr <=> '[1.0, 3.0, 5.0]') order by repr <=> '[1.0, 3.0, 5.0]' limit 4 offset 0;";
    ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    sql = "select id, val, repr <=> '[1.0, 3.0, 5.0]' from Tb1 where repr <=> '[1.0, 3.0, 5.0]' < 0.1 "
          "group by id having min(repr <=> '[1.0, 3.0, 5.0]') order by repr <=> '[1.0, 3.0, 5.0]' limit 4 offset 0;";
    ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult1);
    StEmbSqlClearActQryResultSet(NULL);

    sql = "Drop Table Tb1;";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_OK);
}

HWTEST_F(StEmbSqlSelectGroupBy, TestGroupByUsingWhereWithMutilTable, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *sql = "Drop Table IF EXISTS t1; Drop Table IF EXISTS t2;";
    Status ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    sql = "CREATE TABLE t1(id int unique, repr floatvector(3));";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    sql = "CREATE TABLE t2(id int unique, repr floatvector(3), name varchar(10));";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    sql = "INSERT INTO t1 VALUES "
          "(1, '[2.4,3.3,4.1]'), (2, '[7.1,20.1,5.1]'), (3, '[4.4,3.3,7.1]'), (4, '[3.7,46.7,24.1]');";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    sql = "INSERT INTO t2 VALUES "
          "(1, '[3.0,4.1,8.1]', 'Amily'), (2, '[3.2,4.3,8.5]', 'AMILY'), (3, '[3.0,4.1,58.1]', 'John'), "
          "(4, '[3.0,4.1,8.1]', 'John'), (5, '[3.2,4.3,8.5]', NULL), (6, '[3.7,46.7,24.1]', NULL);";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    sql = "SELECT t1.repr, max(t1.repr <-> '[2,3,4]'), t2.repr, max(t2.repr <-> '[2,3,4]') from t1,t2 "
          "WHERE t1.repr <-> '[2,3,4]' > 40 GROUP BY t1.repr,t2.repr;";
    ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    StEmbSqlQryResultSetExtendT exptQryResult{{"repr", "?column?", "repr", "?column?"},
        {{"[3.700000, 46.700001, 24.100000]", "48.131", "[3.000000, 4.100000, 8.100000]", "4.36119"},
            {"[3.700000, 46.700001, 24.100000]", "48.131", "[3.000000, 4.100000, 58.099998]", "54.1204"},
            {"[3.700000, 46.700001, 24.100000]", "48.131", "[3.200000, 4.300000, 8.500000]", "4.83529"},
            {"[3.700000, 46.700001, 24.100000]", "48.131", "[3.700000, 46.700001, 24.100000]", "48.131"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    sql = "Drop Table t1; Drop Table t2;";
    ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}
