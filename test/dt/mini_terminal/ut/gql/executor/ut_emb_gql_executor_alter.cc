/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Alter Commands UT
 * Author: EE Team
 * Create: 2025-04-01
 */

#ifdef GAUSSPD_DISABLED_TDD
#include <string>
#include <vector>
#include "ee_plan_node_ddl.h"
#include "ut_emb_gql_common.h"
#include "ut_emb_gql_executor_common.h"

using namespace std;
using namespace testing::ext;

extern "C" {
Status SysTableInit(void *dbInstance);
Status SysTableStartImpl(void *dbInstance);
}

class UtEmbGqlExecutorAlter : public UtEmbGqlExecutorCommon {
public:
    void ExecuteAndCheckRst(vector<UtGqlAnalyzerErrT> inputs)
    {
        for (auto input : inputs) {
            NodeT *createIdxStmt = UtMakeCmdStmtByGql(memCtx, session, string(input.gql));
            ASSERT_NE(nullptr, createIdxStmt);
            Status ret = UtGqlCmdExecutor(memCtx, session, createIdxStmt);
            ASSERT_EQ(ret, input.ret);
        }
    }
    void SetUp() override
    {
        UtEmbGqlExecutorCommon::SetUp();
        UtExecuteGqlStmt(memCtx, session, R"(
CREATE GRAPH MYGRAPH {
    (person:Person {name string not null, gender string DEFAULT 'super', namePinyin string,
        nickname string, phoneNumbers string, emails string, familyAddress string, organization string,
        occupations string, position string, isUser BOOL not null DEFAULT 29, birthdate string,
        rowidNumber string, aliases string,
        updatedAt string, createdAt string DEFAULT current_timestamp}),
    (event:Event {title string, timeStart string, timeEnd string, participants string,
        participantEmails string, eventLocation string, description string,
        updatedAt string, createdAt string DEFAULT current_time}),
    (student:Student),
    (student) -[:同学]-> (student),
    (person) -[:直系亲属]-> (person),
    (person) -[:参与{createdAt string DEFAULT current_date}]-> (event),
    (event) -[:参与者{name string not null DEFAULT 'city', price double DEFAULT 0.3}]-> (person)
};)");
    }
    void TearDown() override
    {
        UtExecuteGqlStmt(memCtx, session, "DROP GRAPH IF EXISTS myGraph;");
        UtEmbGqlExecutorCommon::TearDown();
    }
};

// add column for edgeLabel 正向用例
HWTEST_F(UtEmbGqlExecutorAlter, DISABLED_AlterAddColumn001, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = GqlUtGetVertexLabel("Person", session);
    ASSERT_NE(vertexLabel, nullptr);
    void *dbInstance = session->sessionPool->dbInstance;
    uint32_t srcPropNum = vertexLabel->metaVertexLabel->schema->propeNum;

    for (uint32_t i = 1; i < srcPropNum; i++) {
        // 构造待插入的属性列
        DmEdgeLabelT *edgeLabel = GqlUtGetEdgeLabel("同学", session);
        ASSERT_NE(edgeLabel, nullptr);
        DmPropertySchemaT *tmpSchema =
            (DmPropertySchemaT *)DbDynMemCtxAlloc(edgeLabel->memCtx, sizeof(DmPropertySchemaT));
        ASSERT_NE(tmpSchema, nullptr);
        (void)memset_s(tmpSchema, sizeof(DmPropertySchemaT), 0, sizeof(DmPropertySchemaT));
        Status ret = DmCopyPropertySchema(&vertexLabel->metaVertexLabel->schema->properties[i], tmpSchema);
        ASSERT_EQ(ret, GMERR_OK);
        CataReleaseEdgeLabel(edgeLabel, dbInstance);

        // 执行添加列操作
        AlterGraphStmtT alterStmt = {};
        alterStmt.node.tag = T_ALTER_GRAPH_STMT;
        alterStmt.graphName = (char *)g_utGraphName.c_str();
        alterStmt.alterType = T_GQL_ADD_COLUMN;
        alterStmt.alterLabelName = (char *)"同学";
        alterStmt.column = tmpSchema;
        alterStmt.nspId = session->namespaceId;
        alterStmt.dbId = session->dbId;
        ret = UtGqlCmdExecutor(memCtx, session, static_cast<NodeT *>(static_cast<void *>(&alterStmt)));
        ASSERT_EQ(GMERR_OK, ret);

        // 检查添加列是否成功
        edgeLabel = GqlUtGetEdgeLabel("同学", session);
        ASSERT_NE(edgeLabel, nullptr);
        ASSERT_EQ(edgeLabel->schema->propeNum, i + 1);
        ASSERT_STREQ(edgeLabel->schema->properties[i].name, vertexLabel->metaVertexLabel->schema->properties[i].name);
        CataReleaseEdgeLabel(edgeLabel, dbInstance);
    }
    CataReleaseVertexLabel(vertexLabel, dbInstance);
}

// add column for vertexLabel 正向用例
HWTEST_F(UtEmbGqlExecutorAlter, DISABLED_AlterAddColumn002, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = GqlUtGetVertexLabel("Person", session);
    ASSERT_NE(vertexLabel, nullptr);
    void *dbInstance = session->sessionPool->dbInstance;
    uint32_t srcPropNum = vertexLabel->metaVertexLabel->schema->propeNum;

    for (uint32_t i = 1; i < srcPropNum; i++) {
        // 构造待插入的属性列
        DmVertexLabelT *destVertexLabel = GqlUtGetVertexLabel("Student", session);
        ASSERT_NE(destVertexLabel, nullptr);
        DmPropertySchemaT *tmpSchema =
            (DmPropertySchemaT *)DbDynMemCtxAlloc(destVertexLabel->memCtx, sizeof(DmPropertySchemaT));
        ASSERT_NE(tmpSchema, nullptr);
        (void)memset_s(tmpSchema, sizeof(DmPropertySchemaT), 0, sizeof(DmPropertySchemaT));
        Status ret = DmCopyPropertySchema(&vertexLabel->metaVertexLabel->schema->properties[i], tmpSchema);
        ASSERT_EQ(ret, GMERR_OK);
        CataReleaseVertexLabel(destVertexLabel, dbInstance);

        // 执行添加列操作
        AlterGraphStmtT alterStmt = {};
        alterStmt.node.tag = T_ALTER_GRAPH_STMT;
        alterStmt.graphName = (char *)g_utGraphName.c_str();
        alterStmt.alterType = T_GQL_ADD_COLUMN;
        alterStmt.alterLabelName = (char *)"Student";
        alterStmt.column = tmpSchema;
        alterStmt.nspId = session->namespaceId;
        alterStmt.dbId = session->dbId;
        ret = UtGqlCmdExecutor(memCtx, session, static_cast<NodeT *>(static_cast<void *>(&alterStmt)));
        ASSERT_EQ(GMERR_OK, ret);

        // 检查添加列是否成功
        destVertexLabel = GqlUtGetVertexLabel("Student", session);
        ASSERT_NE(destVertexLabel, nullptr);
        ASSERT_EQ(destVertexLabel->metaVertexLabel->schema->propeNum, i + 1);
        ASSERT_STREQ(destVertexLabel->metaVertexLabel->schema->properties[i].name,
            vertexLabel->metaVertexLabel->schema->properties[i].name);
        CataReleaseVertexLabel(destVertexLabel, dbInstance);
    }
    CataReleaseVertexLabel(vertexLabel, dbInstance);
}
#endif
