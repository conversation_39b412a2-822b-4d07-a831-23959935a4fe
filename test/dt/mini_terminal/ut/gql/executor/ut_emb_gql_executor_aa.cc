/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: AA UT
 * Author: EE Team
 * Create: 2024-09-25
 */

#include <string>
#include "ee_associative_array_slot.h"
#include "ee_associative_array.h"
#include "ut_emb_gql_common.h"
#include "ut_emb_gql_executor_common.h"

using namespace std;
using namespace testing::ext;

class UtEmbGqlExecutorAA : public UtEmbGqlExecutorCommon {
    void SetUp() override
    {
        UtEmbGqlExecutorCommon::SetUp();
        UtExecuteGqlStmt(memCtx, session, g_utCreateGraphGql);
    }
    void TearDown() override
    {
        UtExecuteGqlStmt(memCtx, session, g_utDropGraphSql);
        UtEmbGqlExecutorCommon::TearDown();
    }
};

HWTEST_F(UtEmbGqlExecutorAA, CreateAndDropAAWithTopo_001, TestSize.Level0)
{
    AASlotSchemaInfoT info = {};
    info.edgeLabel = GqlUtGetEdgeLabel("直系亲属", session);
    AASlotT *slot = nullptr;
    Status ret = NewAASlotWithInfo(memCtx, info, &slot);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(slot != nullptr);
    DeleteAASlot(slot);
    CataReleaseEdgeLabel(info.edgeLabel, UtGetDbInstance());
}

HWTEST_F(UtEmbGqlExecutorAA, CreateAndDropAAWithTopo_002, TestSize.Level0)
{
    AASlotSchemaInfoT info = {};
    info.edgeLabel = GqlUtGetEdgeLabel("姻亲", session);
    AASlotT *slot = nullptr;
    Status ret = NewAASlotWithInfo(memCtx, info, &slot);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(slot != nullptr);
    DmValueT value = {};
    char str[MAX_STR_VAL_LEN] = "1";
    value.type = DB_DATATYPE_STRING;
    value.value.strAddr = str;
    value.value.length = DM_STR_LEN(str);
    ret = AASlotSetPrope(slot, 0, value);
    EXPECT_EQ(GMERR_OK, ret);
    DmValueT outValue = {};
    ret = AASlotGetPrope(slot, 0, &outValue);
    EXPECT_EQ(value.type, outValue.type);
    EXPECT_STREQ((const char *)value.value.strAddr, (const char *)outValue.value.strAddr);
    EXPECT_EQ(GMERR_OK, ret);
    DeleteAASlot(slot);
    CataReleaseEdgeLabel(info.edgeLabel, UtGetDbInstance());
}

HWTEST_F(UtEmbGqlExecutorAA, CreateAndDropAAWithTopo_003, TestSize.Level0)
{
    AAInfoT aaInfo = {.memCtx = memCtx, .batchSize = 0, .needTopo = true};
    AAT *aa = nullptr;
    Status ret = NewAAWithInfo(&aaInfo, &aa);
    EXPECT_EQ(GMERR_OK, ret);
    AASlotSchemaInfoT info = {};
    info.edgeLabel = GqlUtGetEdgeLabel("朋友", session);
    AASlotT *slot = nullptr;
    char str[MAX_STR_VAL_LEN] = "1";
    while (!AAPropSlotsIsFull(aa)) {
        ret = AAPopPropSlotWithInfo(aa, info, &slot);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(slot != nullptr);
        DmValueT value = {};
        value.type = DB_DATATYPE_STRING;
        value.value.strAddr = str;
        value.value.length = DM_STR_LEN(str);
        ret = AASlotSetPrope(slot, 0, value);
        EXPECT_EQ(GMERR_OK, ret);
        ret = AAPushPropSlot(aa, slot);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while ((slot = AAGetNextPropSlot(aa)) != nullptr) {
        DmValueT outValue = {};
        ret = AASlotGetPrope(slot, 0, &outValue);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(DB_DATATYPE_STRING, outValue.type);
        EXPECT_STREQ((const char *)str, (const char *)outValue.value.strAddr);
    }
    DeleteAA(aa);
    CataReleaseEdgeLabel(info.edgeLabel, UtGetDbInstance());
}

HWTEST_F(UtEmbGqlExecutorAA, CreateAndDropAAWithTopo_004, TestSize.Level0)
{
    AAInfoT aaInfo = {.memCtx = memCtx, .batchSize = 0, .needTopo = true};
    AAT *aa = nullptr;
    Status ret = NewAAWithInfo(&aaInfo, &aa);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *person = GqlUtGetVertexLabel("Person", session);
    AASlotT *slot = NULL;
    ret = NewAASlotWithVertexLabel(memCtx, person, &slot);
    EXPECT_EQ(GMERR_OK, ret);
    ret = AAPushPropSlot(aa, slot);
    EXPECT_EQ(GMERR_OK, ret);
    EdgeElementT *edgeElement = nullptr;
    uint32_t edgeNum = 50;
    for (uint32_t i = 0; i < edgeNum; i++) {
        ret = AAPopEdgeElementByIndex(aa, i, &edgeElement);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(edgeElement != nullptr);
        edgeElement->srcElem = (VertexElementT *)slot->elem;
        edgeElement->dstElem = (VertexElementT *)slot->elem;
        ret = AAPushEdgeElementByIndex(aa, i, edgeElement);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < edgeNum; i++) {
        edgeElement = AAGetEdgeElementByIndex(aa, i);
        if (edgeElement != nullptr) {
            EXPECT_EQ((VertexElementT *)slot->elem, edgeElement->srcElem);
            EXPECT_EQ((VertexElementT *)slot->elem, edgeElement->dstElem);
        }
    }
    DeleteAA(aa);
}
