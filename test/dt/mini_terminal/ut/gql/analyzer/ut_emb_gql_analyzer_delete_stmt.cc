/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test for analyzer of GQL delete stmt
 * Author: QE Team
 * Create: 2024-01-30
 */

#include <vector>
#include "gtest/gtest.h"
#include "ut_emb_gql_common.h"
#include "ut_emb_gql_analyzer_common.h"
#include "adpt_define.h"
#include "ee_session.h"
#include "cpl_gql_compiler.h"
#include "cpl_gql_analyzer_utils.h"

using namespace std;
using namespace testing::ext;

class UtEmbGqlAnalyzerDeleteStmt : public UtEmbGqlAnalyzerCommon {
protected:
    void SetUp() override
    {
        UtEmbGqlAnalyzerCommon::SetUp();
        DbMemCtxT *memCtx = UtGqlGetMemCtx();
        UtExecuteGqlStmt(memCtx, session,
            "CREATE GRAPH myGraph {"
            "    (a:A {id INT, name STRING}),"
            "    (b:B {id INT, name STRING}),"
            "    (c:C {id INT, name STRING}),"
            "    (d:D {no STRING, acct_type int default 199}),"
            "    (e:E {no STRING, acct_type int default 199}),"
            "    (c)-[:CE ]->(e),"
            "    (e)-[:EC ]->(c),"
            "    (b)-[:BC1]->(c),"
            "    (b)-[:BC2]->(c),"
            "    (b)-[:BA ]->(a),"
            "    (e)-[:EA ]->(a),"
            "    (d)-[:DA ]->(a),"
            "    (a)-[:AB ]->(b),"
            "    (c)-[:CA ]->(a)"
            "};");
    }
    void TearDown() override
    {
        DbMemCtxT *memCtx = UtGqlGetMemCtx();
        UtExecuteGqlStmt(memCtx, session, "DROP GRAPH IF EXISTS myGraph;");
        UtEmbGqlAnalyzerCommon::TearDown();
    }
};

// Detach Delete vertex
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DetachDeleteMatchUserNodeLabel, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE c;"},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE c, a;"},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE c, a, e;"},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Detach Delete edge
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DetachDeleteMatchUserNodeLabe2, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE ec;"},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE ec, ea;"},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE ca, ea, ec;"},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Detach Delete vertex && edge
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DetachDeleteMatchUserNodeLabe3, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE a, ec;"},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE a, c, ea;"},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE e, e, ec;"},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Detach Delete vertex && edge which not appear in the MATCH
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DetachDeleteMatchUserNodeLabe4, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE dc;", GMERR_UNDEFINED_OBJECT},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE q, c, ea;", GMERR_UNDEFINED_OBJECT},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE c, c, f;", GMERR_UNDEFINED_OBJECT},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Detach Delete supports only supports vertex or edge and cannot match more than one result
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DetachDeleteMatchUserNodeLabe5, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH p = (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DETACH DELETE p;", GMERR_FEATURE_NOT_SUPPORTED},
        {"MATCH p = (c:C) DETACH DELETE c, p;", GMERR_FEATURE_NOT_SUPPORTED},
        {"MATCH (c:C)<-[ec]-{1,3}() DETACH DELETE ec;", GMERR_FEATURE_NOT_SUPPORTED},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// NoDetach Delete vertex
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, NoDetachDeleteMatchUserNodeLabel, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE c;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE d;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE c, a;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE c, a, e;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// NoDetach Delete edge
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, NoDetachDeleteMatchUserNodeLabe2, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE ec;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE hc;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE ec, ea;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE ca, ea, ec;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// NoDetach Delete vertex && edge
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, NoDetachDeleteMatchUserNodeLabe3, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE a, ec;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE a, c, ea;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE e, e, ec;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// NoDetach Delete vertex && edge which not appear in the MATCH
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, NoDetachDeleteMatchUserNodeLabe4, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE dc;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE q, c, ea;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE c, c, f;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// NoDetach Delete supports only supports vertex or edge and cannot match more than one result
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, NoDetachDeleteMatchUserNodeLabe5, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH p = (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) NODETACH DELETE p;", GMERR_SYNTAX_ERROR},
        {"MATCH p = (c:C) NODETACH DELETE c, p;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec]-{1,3}() NODETACH DELETE ec;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Delete vertex
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DeleteMatchUserNodeLabel, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE c;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE d;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE c, a;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE c, a, e;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Delete edge
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DeleteMatchUserNodeLabe2, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE ec;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE hc;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE ec, ea;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE ca, ea, ec;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Delete vertex && edge
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DeleteMatchUserNodeLabe3, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE a, ec;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE a, c, ea;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE e, e, ec;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Delete vertex && edge which not appear in the MATCH
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DeleteMatchUserNodeLabe4, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE dc;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE q, c, ea;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE c, c, f;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}

// Delete supports only supports vertex or edge and cannot match more than one result
HWTEST_F(UtEmbGqlAnalyzerDeleteStmt, DeleteMatchUserNodeLabe5, TestSize.Level0)
{
    DbMemCtxT *memCtx = UtGqlGetMemCtx();
    UtGqlAnalyzerErrT input[] = {
        {"MATCH p = (c:C)<-[ec:EC]-(e:E)-[ea:EA]->(a:A)<-[ca:CA]-(c:C) DELETE p;", GMERR_SYNTAX_ERROR},
        {"MATCH p = (c:C) DELETE c, p;", GMERR_SYNTAX_ERROR},
        {"MATCH (c:C)<-[ec]-{1,3}() DELETE ec;", GMERR_SYNTAX_ERROR},
    };
    UtGqlParseAndAnalyze(session, memCtx, input, ELEMENT_COUNT(input));
}
