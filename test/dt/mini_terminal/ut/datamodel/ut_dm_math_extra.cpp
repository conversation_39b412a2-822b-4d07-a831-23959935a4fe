/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: extra ut file for data model math
 * Author: zhaoleyu
 * Create: 2025-5-22
 */

#include "ut_dm_common.h"

#include "gtest/gtest.h"
#include "climits"
#include "cfloat"
#include "cmath"
#include "dm_data_math_sql.h"

#include "dm_data_basic.h"
#include "dm_data_math.h"
using namespace testing::ext;

class UtDmMathExtra : public testing::Test {
protected:
    virtual void SetUp()
    {}

    virtual void TearDown()
    {}
};

HWTEST_F(UtDmMathExtra, diff_type, TestSize.Level0)
{
    DmValueT valueA;
    DmValueT valueB;
    DmValueT valueOutput;

    valueA.type = DB_DATATYPE_BOOL;
    valueB.type = DB_DATATYPE_BOOL;
    valueA.value.boolValue = true;
    valueB.value.boolValue = true;
    ASSERT_EQ(GMERR_OK, DmValueAndDiffType(&valueA, &valueB, &valueOutput));
    ASSERT_EQ(true, valueOutput.value.boolValue);

    valueA.type = DB_DATATYPE_RESOURCE;
    valueB.type = DB_DATATYPE_RESOURCE;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueAndDiffType(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_BOOL;
    valueB.type = DB_DATATYPE_BOOL;
    valueA.value.boolValue = true;
    valueB.value.boolValue = true;
    ASSERT_EQ(GMERR_OK, DmValueOrDiffType(&valueA, &valueB, &valueOutput));
    ASSERT_EQ(true, valueOutput.value.boolValue);

    valueA.type = DB_DATATYPE_RESOURCE;
    valueB.type = DB_DATATYPE_RESOURCE;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueOrDiffType(&valueA, &valueB, &valueOutput));
}

HWTEST_F(UtDmMathExtra, left_shift, TestSize.Level0)
{
    DmValueT valueA;
    DmValueT valueB;
    DmValueT valueOutput;

    valueA.type = DB_DATATYPE_UCHAR;
    valueB.type = DB_DATATYPE_FLOAT;
    valueA.value.ucharValue = 50;
    valueB.value.ucharValue = 50;
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT8;
    valueB.type = DB_DATATYPE_UINT8;
    valueA.value.ubyteValue = 50;
    valueB.value.ubyteValue = 4;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT8;
    valueB.type = DB_DATATYPE_UINT8;
    valueA.value.ubyteValue = 50;
    valueB.value.ubyteValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = -1;
    valueB.value.byteValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = 50;
    valueB.value.byteValue = 4;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = 50;
    valueB.value.byteValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT16;
    valueB.type = DB_DATATYPE_UINT16;
    valueA.value.ushortValue = 50;
    valueB.value.ushortValue = 12;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT16;
    valueB.type = DB_DATATYPE_UINT16;
    valueA.value.ushortValue = 50;
    valueB.value.ushortValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = -1;
    valueB.value.shortValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = 50;
    valueB.value.shortValue = 12;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = 50;
    valueB.value.shortValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT32;
    valueB.type = DB_DATATYPE_UINT32;
    valueA.value.uintValue = 50;
    valueB.value.uintValue = 28;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT32;
    valueB.type = DB_DATATYPE_UINT32;
    valueA.value.uintValue = 50;
    valueB.value.uintValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = -1;
    valueB.value.intValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = 50;
    valueB.value.intValue = 31;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = 50;
    valueB.value.intValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT64;
    valueB.type = DB_DATATYPE_UINT64;
    valueA.value.ulongValue = 50;
    valueB.value.ulongValue = 60;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT64;
    valueB.type = DB_DATATYPE_UINT64;
    valueA.value.ulongValue = 50;
    valueB.value.ulongValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = -1;
    valueB.value.longValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = 50;
    valueB.value.longValue = 60;
    ASSERT_EQ(GMERR_FIELD_OVERFLOW, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = 50;
    valueB.value.longValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueLshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_TIME;
    valueB.type = DB_DATATYPE_TIME;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueLshift(&valueA, &valueB, &valueOutput));
}

HWTEST_F(UtDmMathExtra, right_shift, TestSize.Level0)
{
    DmValueT valueA;
    DmValueT valueB;
    DmValueT valueOutput;

    valueA.type = DB_DATATYPE_UCHAR;
    valueB.type = DB_DATATYPE_FLOAT;
    valueA.value.ucharValue = 50;
    valueB.value.ucharValue = 50;
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT8;
    valueB.type = DB_DATATYPE_UINT8;
    valueA.value.ubyteValue = 50;
    valueB.value.ubyteValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = -1;
    valueB.value.byteValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = 50;
    valueB.value.byteValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT16;
    valueB.type = DB_DATATYPE_UINT16;
    valueA.value.ushortValue = 50;
    valueB.value.ushortValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = -1;
    valueB.value.shortValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = 50;
    valueB.value.shortValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT32;
    valueB.type = DB_DATATYPE_UINT32;
    valueA.value.uintValue = 50;
    valueB.value.uintValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = -1;
    valueB.value.intValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = 50;
    valueB.value.intValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT64;
    valueB.type = DB_DATATYPE_UINT64;
    valueA.value.ulongValue = 50;
    valueB.value.ulongValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = -1;
    valueB.value.longValue = -1;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = 50;
    valueB.value.longValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueRshift(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_TIME;
    valueB.type = DB_DATATYPE_TIME;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueRshift(&valueA, &valueB, &valueOutput));
}

HWTEST_F(UtDmMathExtra, bit_and, TestSize.Level0)
{
    DmValueT valueA;
    DmValueT valueB;
    DmValueT valueOutput;

    valueA.type = DB_DATATYPE_UCHAR;
    valueB.type = DB_DATATYPE_FLOAT;
    valueA.value.ucharValue = 50;
    valueB.value.ucharValue = 50;
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT8;
    valueB.type = DB_DATATYPE_UINT8;
    valueA.value.ubyteValue = 50;
    valueB.value.ubyteValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = -1;
    valueB.value.byteValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT16;
    valueB.type = DB_DATATYPE_UINT16;
    valueA.value.ushortValue = 50;
    valueB.value.ushortValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = -1;
    valueB.value.shortValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT32;
    valueB.type = DB_DATATYPE_UINT32;
    valueA.value.uintValue = 50;
    valueB.value.uintValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = -1;
    valueB.value.intValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT64;
    valueB.type = DB_DATATYPE_UINT64;
    valueA.value.ulongValue = 50;
    valueB.value.ulongValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = -1;
    valueB.value.longValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitAnd(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_TIME;
    valueB.type = DB_DATATYPE_TIME;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueRshift(&valueA, &valueB, &valueOutput));
}

HWTEST_F(UtDmMathExtra, bit_or, TestSize.Level0)
{
    DmValueT valueA;
    DmValueT valueB;
    DmValueT valueOutput;

    valueA.type = DB_DATATYPE_UCHAR;
    valueB.type = DB_DATATYPE_FLOAT;
    valueA.value.ucharValue = 50;
    valueB.value.ucharValue = 50;
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT8;
    valueB.type = DB_DATATYPE_UINT8;
    valueA.value.ubyteValue = 50;
    valueB.value.ubyteValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT8;
    valueB.type = DB_DATATYPE_INT8;
    valueA.value.byteValue = -1;
    valueB.value.byteValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT16;
    valueB.type = DB_DATATYPE_UINT16;
    valueA.value.ushortValue = 50;
    valueB.value.ushortValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT16;
    valueB.type = DB_DATATYPE_INT16;
    valueA.value.shortValue = -1;
    valueB.value.shortValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT32;
    valueB.type = DB_DATATYPE_UINT32;
    valueA.value.uintValue = 50;
    valueB.value.uintValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT32;
    valueB.type = DB_DATATYPE_INT32;
    valueA.value.intValue = -1;
    valueB.value.intValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_UINT64;
    valueB.type = DB_DATATYPE_UINT64;
    valueA.value.ulongValue = 50;
    valueB.value.ulongValue = 1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_INT64;
    valueB.type = DB_DATATYPE_INT64;
    valueA.value.longValue = -1;
    valueB.value.longValue = -1;
    ASSERT_EQ(GMERR_OK, DmValueBitOr(&valueA, &valueB, &valueOutput));

    valueA.type = DB_DATATYPE_TIME;
    valueB.type = DB_DATATYPE_TIME;
    ASSERT_EQ(GMERR_INVALID_VALUE, DmValueBitOr(&valueA, &valueB, &valueOutput));
}
