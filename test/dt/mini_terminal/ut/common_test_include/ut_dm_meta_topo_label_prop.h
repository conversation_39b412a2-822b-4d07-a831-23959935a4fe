/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file of meta topo label
 * Author: wangjingyi
 * Create: 2023-9-11
 */

#ifndef GMDBV5_UT_DM_META_TOPO_LABEL_PROP_H
#define GMDBV5_UT_DM_META_TOPO_LABEL_PROP_H

#include "dm_meta_topo_label.h"
#include "dm_meta_subscription.h"
/*
 * 创建一个空的edgeLabel结构体，该edgeLabel所有的内存申请和释放依赖运行时Memory Context
 * 如果内存的申请和释放不在同一个Memory Context下，则会出错，依赖外部保证相关操作在同一个Memory Context下
 * 后续优化方案：创建时，获取当前运行时Current Memory Context，然后设置进edgeLabel中，该接口Common还未提供
 */
Status DmCreateEmptyEdgeLabel(DmEdgeLabelT **edgeLabel);

/*
 * 释放edgeLabel结构体中的所有内存，含该结构体自身的内存
 */
void DmEdgeLabelDestroy(DmEdgeLabelT *edgeLabel);

void DmDestroySubscription(DmSubscriptionT *subs);

Status DmCreateEmptySubscriptionWithMemCtx(DbMemCtxT *memCtx, DmSubscriptionT **subs);
#endif  // GMDBV5_UT_DM_META_TOPO_LABEL_PROP_H
