/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <climits>
#include <csignal>
#include <pthread.h>
#include <securec.h>
#include "gtest/gtest.h"
#include "stub.h"
#include "adpt_thread.h"
#include "adpt_sleep.h"
#include "adpt_string.h"
#include "adpt_memory.h"

using namespace testing::ext;

#define PTHREAD_STACK_MIN_DAP (12 * 1024)
#define DB_THREAD_NAME_MAX_LEN 32

volatile uint32_t g_gmdbExitFunc = 0;
volatile uint32_t g_gmdbFuncCnt = 0;

static void ThreadEntryFunc(void *args)
{
    int *tempPtr = static_cast<int *>(args);
    int temp = *tempPtr;
    for (; temp < 10000; temp++) {
        g_gmdbFuncCnt++;
        if (g_gmdbFuncCnt >= 100) {
            g_gmdbFuncCnt = 100;
            DbUsleep(1);
        }
        if (g_gmdbExitFunc != 0) {
            g_gmdbFuncCnt = 0;
            DbUsleep(2);
            break;
        }
    }
}
class UtDbThread : public testing::Test {
protected:
    static void SetUpTestCase(void)
    {
        InitStub();
    }
    static void TearDownTestCase(void)
    {}
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
};

Status GetThreadAttrsStub(const ThreadAttrsT *userInputs, ThreadAttrsForRtosT *threadAttrs)
{
    return GMERR_DATA_EXCEPTION;
}

Status ThreadFuncAttrsCheckStub(const ThreadAttrsForRtosT *threadAttrs, const DbThreadHandle *handle)
{
    return GMERR_DATA_EXCEPTION;
}

Status SetThreadAttrsStub(const ThreadAttrsForRtosT *threadAttrs, pthread_attr_t *pthreadAttrs)
{
    return GMERR_DATA_EXCEPTION;
}

static int snprintf_sStub(char *strDest, size_t destMax, size_t count, const char *format, ...)
{
    return -1;
}

HWTEST_F(UtDbThread, UtDbThreadCreate, TestSize.Level0)
{
    ThreadAttrsT threadAttrs;
    uint32_t input = 10;
    strcpy_s(threadAttrs.name, DB_THREAD_NAME_MAX_LEN, "JoinThread");
    threadAttrs.entryFunc = (DbThreadEntryProc)ThreadEntryFunc;
    threadAttrs.entryArgs = &input;
    threadAttrs.type = DB_THREAD_JOINABLE;
    threadAttrs.stackSize = PTHREAD_STACK_MIN_DAP;
    DbThreadHandle handle = 0;

    int stubIdx1 = SetStubC((void *)GetThreadAttrs, (void *)GetThreadAttrsStub);
    EXPECT_EQ(DbThreadCreate(&threadAttrs, &handle), GMERR_DATA_EXCEPTION);
    ClearStub(stubIdx1);

    int stubIdx2 = SetStubC((void *)SetThreadAttrs, (void *)SetThreadAttrsStub);
    EXPECT_EQ(DbThreadCreate(&threadAttrs, &handle), GMERR_DATA_EXCEPTION);
    ClearStub(stubIdx2);

    int stubIdx3 = SetStubC((void *)snprintf_s, (void *)snprintf_sStub);
    EXPECT_EQ(GetThreadAttrs(NULL, NULL), GMERR_FIELD_OVERFLOW);
    ClearStub(stubIdx3);

    ThreadAttrsForRtosT threadAttrs1 = {0};
    EXPECT_EQ(ThreadFuncAttrsCheck(NULL, NULL), GMERR_UNEXPECTED_NULL_VALUE);
    EXPECT_EQ(ThreadFuncAttrsCheck(NULL, &handle), GMERR_UNEXPECTED_NULL_VALUE);
    EXPECT_EQ(ThreadFuncAttrsCheck(&threadAttrs1, NULL), GMERR_UNEXPECTED_NULL_VALUE);
    EXPECT_EQ(SetThreadAttrs(NULL, NULL), GMERR_UNEXPECTED_NULL_VALUE);
}
