#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# used for build db entry
#
set -e
CURRENT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
GMDB_DIR=${CURRENT_DIR}/../../../..
echo $GMDB_DIR
mkdir -p ${GMDB_DIR}/asan_log/
source ${GMDB_DIR}/scripts/build_scripts/asan_env.sh
./ut_executor
source ${GMDB_DIR}/scripts/build_scripts/asan_clean_env.sh
