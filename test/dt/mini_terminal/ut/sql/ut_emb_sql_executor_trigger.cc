/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Trigger Commands UT
 * Author: EE Team
 * Create: 2024-03-26
 */

#include <string>
#include "ut_emb_sql_common.h"
#include "dm_data_trigger_info_sql.h"

using namespace std;
using namespace testing::ext;

class UtEmbSqlExecutorTrigger : public testing::Test {
protected:
    static void SetUpTestCase()
    {
#ifndef HARMONY_OS
        system("ipcrm -a");
#endif
        Status ret = BaseInit();
        ASSERT_EQ(ret, GMERR_OK);
    }

    static void TearDownTestCase()
    {
        BaseUninit();
#ifndef HARMONY_OS
        system("ipcrm -a");
#endif
    }

    virtual void SetUp()
    {
        ClearAllStub();
        DbMemCtxArgsT args = {0};
        memCtx =
            DbCreateDynMemCtx(DbSrvGetSysDynCtx(UtGetDbInstance()->instanceId), true, "dynamic memory context", &args);
        ASSERT_NE(nullptr, memCtx);
        oldMemCtx = DbMemCtxSwitchTo(memCtx);
        EXPECT_EQ(
            GMERR_OK, QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, UtGetDbInstance(), &session));
    }

    virtual void TearDown()
    {
        QrySessionRelease(session);
        DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbDeleteDynMemCtx(memCtx);
        ClearAllStub();
    }

protected:
    DbMemCtxT *memCtx;
    DbMemCtxT *oldMemCtx;
    DrtConnectionT *conn;
    SessionT *session;
};

HWTEST_F(UtEmbSqlExecutorTrigger, CreateAndDropTriggerBasic, TestSize.Level0)
{
    // 1. 建表
    SqlUtExecuteStmt(memCtx, session, "CREATE TABLE trigTbl1(id int, age int, name text, height int);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("trigTbl1", session);
    ASSERT_STRCASEEQ("trigTbl1", vertexLabel->metaCommon.metaName);
    uint32_t vertexLabelId = vertexLabel->metaCommon.metaId;

    // 2. 创建触发器
    SqlUtExecuteStmt(memCtx, session,
        "create trigger trig1 after update of id, age on trigTbl1 when name = 'n1' "
        "begin insert into trigTbl2(id, name) values(1, 'n1'); update trigTbl2 set id = 10; end;");

    // 3. 检查触发器存在
    DmTriggerInfoT *trigInfo{nullptr};
    Status ret = DmGetTriggerInfoByVertexLabelId(vertexLabelId, &trigInfo, UtGetDbInstance());
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_NE(nullptr, trigInfo);
    ASSERT_EQ(vertexLabelId, trigInfo->metaCommon.metaId);
    ASSERT_STRCASEEQ("trigTbl1", trigInfo->metaCommon.metaName);

    // 4. 删除触发器
    SqlUtExecuteStmt(memCtx, session, "DROP TRIGGER trig1;");

    // 5. 检查触发器不存在
    trigInfo = nullptr;
    ret = DmGetTriggerInfoByVertexLabelId(vertexLabelId, &trigInfo, UtGetDbInstance());
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret);
    ASSERT_EQ(nullptr, trigInfo);

    // 5. 删除表
    (void)CataReleaseVertexLabel(vertexLabel, UtGetDbInstance());
    SqlUtExecuteStmt(memCtx, session, "DROP TABLE trigTbl1;");
}

HWTEST_F(UtEmbSqlExecutorTrigger, CreateAndDropTriggerExtend, TestSize.Level0)
{
    // 1. 建表
    SqlUtExecuteStmt(memCtx, session, "CREATE TABLE trigTbl1(id int, age int, name text, height int);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("trigTbl1", session);
    ASSERT_STRCASEEQ("trigTbl1", vertexLabel->metaCommon.metaName);
    uint32_t vertexLabelId = vertexLabel->metaCommon.metaId;

    // 2. 创建触发器
    SqlUtExecuteStmt(memCtx, session,
        "create trigger trig1 after update of id, age on trigTbl1 when name = 'n1' "
        "begin insert into trigTbl2(id, name) values(1, 'n1'); update trigTbl2 set id = 10; end;");
    SqlUtExecuteStmt(memCtx, session, "create trigger trig2 insert on trigTbl1 begin delete from trigTbl2; end;");

    // 3. 删除表
    (void)CataReleaseVertexLabel(vertexLabel, UtGetDbInstance());
    SqlUtExecuteStmt(memCtx, session, "DROP TABLE trigTbl1;");

    // 4. 检查触发器不存在
    DmTriggerInfoT *trigInfo{nullptr};
    Status ret = DmGetTriggerInfoByVertexLabelId(vertexLabelId, &trigInfo, UtGetDbInstance());
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret);
    ASSERT_EQ(nullptr, trigInfo);
}
