/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: fusion executor dql ut for vector
 * Author: GMDBv5 EE Team
 * Create: 2024-02-22
 */

#include <algorithm>
#include "ee_concurrency_control.h"
#include "ee_plan_state.h"
#include "ut_emb_sql_executor_common.h"

static int g_stubIndex = 0;
static uint32_t g_maxTupleNum = 0;
static const uint32_t g_maxProbe = 32768;

using namespace std;
using namespace testing::ext;

extern "C" {
Status ExecInitScalarScan(VectorIndexScanT *indexScan, EStateT *eState, VectorIndexScanStateT *newState);
}

typedef struct CreateMixedQueryPlanCtx {
    DmVertexLabelT *vertexLabel;
    string indexName;
    uint32_t limit;
    uint32_t offset;
    ExprOpTypeE distType;
    bool hasScalarScan;
    bool isSeqScan;
} CreateMixedQueryPlanCtxT;

class VectorUtExecutorDQL : public UtEmbSqlExecutorCommon {
protected:
    void SetUp() override
    {
        UtEmbSqlExecutorCommon::SetUp();
        SqlUtExecuteStmt(memCtx, session, "CREATE TABLE VectorUtDQL_B(id int, name text, vec floatvector(128));");
    }
    void TearDown() override
    {
        // UT 里的 DML 用例执行中加了表的读锁，需要先释放，再进行 DDL 操作
        QryReleaseAllLabelLatch(session);
        SqlUtExecuteStmt(memCtx, session, "DROP TABLE VectorUtDQL_B;");
        UtEmbSqlExecutorCommon::TearDown();
        EXPECT_EQ(nullptr, session->vaccumList);
    }

    PlanT *CreateMixedQueryPlan(CreateMixedQueryPlanCtxT *ctx);
    ExprT *CreateTupleAndExprAsTree(
        const vector<uint32_t> &propId, const vector<DmValueT *> &constValue, const vector<ExprOpTypeE> &opType);
    void TestOfMixedQueryWithIncrementalDelete(bool isSeqScan);
    void TestOfMixedQueryWithIncrementalInsert(bool isSeqScan);
    void TestOfFilterAfterVectorWithIncrementalInsert(uint32_t insertNum, ExprOpTypeE distType);
    void TestOfFilterAfterVectorWithIncrementalDelete(uint32_t insertNum, ExprOpTypeE distType);
    void CheckIdByExecPlan(PlanT *execPlan, const vector<int64_t> &expectIdList);
};

// 用例描述: 验证带向量字段的近似查询，使用 IVF 索引
// 预置条件: 表 VectorUtDQL_B 有元组
// 输入数据: 无。
// 执行步骤: 略。
// 预期结果: 返回近似查询到的元组
HWTEST_F(VectorUtExecutorDQL, IVFIndexScan_001, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 1. 插入数据
    uint32_t insertNum = 200;
    InsertTuples(vertexLabel, insertNum, 0);

    // 2. 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_ivf ON VectorUtDQL_B USING GSIVFFLAT(vec COSINE);");

    // 3. 使用 IVF 索引进行索引扫描
    uint32_t limit = 100;
    uint32_t offset = 30;
    VectorIndexScanT *indexScan = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_ivf"), limit, offset, EXPR_OP_VECTOR_DIST_COSINE);
    PlanStateT *planState = nullptr;
    CheckByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), false, &planState, indexScan->limit, limit);
    // rescan
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), true, &planState, indexScan->limit, limit);

    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 用例描述: 验证带向量字段的近似查询, 使用 DiskANN 索引
// 预置条件: 表 VectorUtDQL_B 有元组
// 输入数据: 无。
// 执行步骤: 略。
// 预期结果: 返回近似查询到的元组
HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_001, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 1. 插入数据
    uint32_t insertNum = 100;
    InsertTuples(vertexLabel, insertNum, 0);

    // 2. 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann ON VectorUtDQL_B USING GSDISKANN(vec COSINE);");

    // 3. 使用 DISKANN 索引进行索引扫描
    uint32_t limit = 100;
    VectorIndexScanT *indexScan = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_diskann"), limit, 0, EXPR_OP_VECTOR_DIST_COSINE);
    PlanStateT *planState = nullptr;
    CheckByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), false, &planState, indexScan->limit, limit);
    // rescan
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), true, &planState, indexScan->limit, limit);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

ExprT *VectorUtExecutorDQL::CreateTupleAndExprAsTree(
    const vector<uint32_t> &propId, const vector<DmValueT *> &constValue, const vector<ExprOpTypeE> &opType)
{
    ExprT *qualExpr = nullptr;
    ExprT *curExpr = nullptr;
    for (uint32_t i = 0; i < propId.size(); i++) {
        curExpr = CreateBinaryFilterExpr(propId[i], *constValue[i], opType[i]);
        if (qualExpr == nullptr) {
            qualExpr = curExpr;
        } else {
            qualExpr = ExprMakeBinary(memCtx, qualExpr, curExpr, EXPR_OP_AND);
        }
        EXPECT_NE(qualExpr, nullptr);
    }
    return qualExpr;
}

// 构造向标混合查询执行计划
// select vec distType '[xx]' from VectorUtDQL_B where id > 1 and name < 'name_7' order by vec distType '[xx]' limit
// x,y;
PlanT *VectorUtExecutorDQL::CreateMixedQueryPlan(CreateMixedQueryPlanCtxT *ctx)
{
    DmVertexLabelT *vertexLabel = ctx->vertexLabel;
    VectorIndexScanT *indexScan = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, ctx->indexName), ctx->limit, ctx->offset, ctx->distType);
    indexScan->scan.projectionExpr = nullptr;  // 投影在父亲节点 sort 中

    // 构造标量索引查询计划
    DmValueT constValue1 = {};
    constValue1.type = DB_DATATYPE_INT64;
    constValue1.value.longValue = 1;
    DmValueT constValue2 = {};
    constValue2.type = DB_DATATYPE_STRING;
    constValue2.value.constStrAddr = static_cast<const void *>("name_7");
    constValue2.value.length = DM_STR_LEN(static_cast<const char *>(constValue2.value.constStrAddr));
    vector<uint32_t> propId{0, 1};
    vector<DmValueT *> constValue{&constValue1, &constValue2};
    vector<ExprOpTypeE> opType{EXPR_OP_GT, EXPR_OP_LT};
    ExprT *qualExpr = CreateTupleAndExprAsTree(propId, constValue, opType);  // id > 1 and name < 'name_7'

    if (ctx->hasScalarScan) {
        if (ctx->isSeqScan) {
            SeqScanT *seqScan = CreateSeqScanPlan(vertexLabel);
            seqScan->qualExpr = qualExpr;
            indexScan->scalarScan = static_cast<PlanT *>(static_cast<void *>(seqScan));
        } else {
            vector<ExprT *> indexQualExpr{CreateBinaryFilterExpr(0, constValue1, EXPR_OP_GT)};  // id > 1
            ExprT *filterExpr = CreateBinaryFilterExpr(1, constValue2, EXPR_OP_LT);             // name < 'name_7'
            indexScan->scalarScan = static_cast<PlanT *>(static_cast<void *>(
                CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, filterExpr)));
        }
        EXPECT_NE(indexScan->scalarScan, nullptr);
    } else {
        indexScan->scan.qualExpr = qualExpr;
    }

    LimitT *limitOp = static_cast<LimitT *>(DbDynMemCtxAlloc(memCtx, sizeof(LimitT)));
    EXPECT_NE(limitOp, nullptr);
    (void)memset_s(limitOp, sizeof(LimitT), 0, sizeof(LimitT));
    limitOp->plan.tagType = T_LIMIT;
    limitOp->offset = ctx->offset;
    limitOp->count = ctx->limit;
    limitOp->projectionExpr = nullptr;
    SortT *sort = static_cast<SortT *>(DbDynMemCtxAlloc(memCtx, sizeof(SortT)));
    EXPECT_NE(sort, nullptr);
    (void)memset_s(sort, sizeof(SortT), 0, sizeof(SortT));
    sort->plan.tagType = T_EXTEND_SORT;
    sort->plan.leftTree = static_cast<PlanT *>(static_cast<void *>(indexScan));
    SortDirectionE direction[1] = {SORT_ASC};
    SortNullPosE nullPosition[1] = {SORT_NULL_FIRST};
    sort->direction = direction;
    sort->nullPosition = nullPosition;
    sort->keyNumber = 1;
    sort->orderByList = indexScan->orderByExpr;

    // sort 的左孩子复用表的 schema
    uint32_t propNum = vertexLabel->metaVertexLabel->schema->propeNum;
    if (ctx->hasScalarScan) {
        sort->plan.leftTree->aaSlotDesc = indexScan->scalarScan->aaSlotDesc;
    } else {
        sort->plan.leftTree->aaSlotDesc.propNum = propNum;
        sort->plan.leftTree->aaSlotDesc.propSchema =
            static_cast<DmPropertySchemaT **>(DbDynMemCtxAlloc(memCtx, propNum * sizeof(DmPropertySchemaT *)));
        EXPECT_NE(sort->plan.leftTree->aaSlotDesc.propSchema, nullptr);
        for (uint32_t i = 0; i < propNum; i++) {
            sort->plan.leftTree->aaSlotDesc.propSchema[i] = &vertexLabel->metaVertexLabel->schema->properties[i];
        }
    }
    vector<uint32_t> propIds{2, 0, 1, 2};
    vector<bool> isVector{true, false, false, false};
    sort->projectionExpr = CreateTupleExpr(memCtx, propIds, isVector, ctx->distType);
    sort->plan.aaSlotDesc.propNum = propNum + 1;  // 比表的列数多一个距离列
    sort->plan.aaSlotDesc.propSchema = static_cast<DmPropertySchemaT **>(
        DbDynMemCtxAlloc(memCtx, sort->plan.aaSlotDesc.propNum * sizeof(DmPropertySchemaT *)));
    EXPECT_NE(sort->plan.aaSlotDesc.propSchema, nullptr);
    sort->plan.aaSlotDesc.propSchema[0] =
        static_cast<DmPropertySchemaT *>(DbDynMemCtxAlloc(memCtx, sizeof(DmPropertySchemaT)));
    EXPECT_NE(sort->plan.aaSlotDesc.propSchema[0], nullptr);
    InitBasicProperty(sort->plan.aaSlotDesc.propSchema[0], 0, DB_DATATYPE_DOUBLE, "vec_dist");
    for (uint32_t i = 1; i < sort->plan.aaSlotDesc.propNum; i++) {
        sort->plan.aaSlotDesc.propSchema[i] = &vertexLabel->metaVertexLabel->schema->properties[i - 1];
    }
    limitOp->plan.leftTree = static_cast<PlanT *>(static_cast<void *>(sort));
    return static_cast<PlanT *>(static_cast<void *>(limitOp));
}

Status ExecInitScalarScanStub(VectorIndexScanT *indexScan, EStateT *eState, VectorIndexScanStateT *newState)
{
    ClearStub(g_stubIndex);
    newState->maxTupleNum = g_maxTupleNum;
    Status ret = ExecInitScalarScan(indexScan, eState, newState);
    g_stubIndex =
        SetStubC(reinterpret_cast<void *>(ExecInitScalarScan), reinterpret_cast<void *>(ExecInitScalarScanStub));
    return ret;
}

// 用例描述: 验证带向量字段的向标混合查询的执行计划，使用 DISKANN 索引，增量插入数据，验证不同策略的混合查询
void VectorUtExecutorDQL::TestOfMixedQueryWithIncrementalInsert(bool isSeqScan)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann ON VectorUtDQL_B USING GSDISKANN(vec COSINE);");

    if (!isSeqScan) {
        // 创建标量索引
        SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");
    }

    // 插入数据
    uint32_t insertNum = 30;
    InsertTuples(vertexLabel, insertNum, 0);

    // 创建向标混合查询的执行计划
    CreateMixedQueryPlanCtxT ctx = {0};
    ctx.vertexLabel = vertexLabel;
    ctx.limit = 10;
    ctx.offset = 3;
    ctx.distType = EXPR_OP_VECTOR_DIST_COSINE;
    ctx.hasScalarScan = true;
    ctx.isSeqScan = isSeqScan;
    ctx.indexName = "idx_diskann";
    PlanT *execPlan = CreateMixedQueryPlan(&ctx);

    g_maxTupleNum = insertNum * 2;
    g_stubIndex =
        SetStubC(reinterpret_cast<void *>(ExecInitScalarScan), reinterpret_cast<void *>(ExecInitScalarScanStub));
    PlanStateT *planState = nullptr;
    CheckByExecPlan(execPlan, false, &planState, 10, 10);  // 标量过滤 + 标量排序

    // 增量插入数据
    uint32_t incrementalInsertNum1 = insertNum;
    InsertTuples(vertexLabel, incrementalInsertNum1, insertNum);

    // 重新查询
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(execPlan, true, &planState, 10, 10);  // 标量过滤产生 dataIds, 再走标量排序

    // 增量插入数据
    uint32_t incrementalInsertNum2 = insertNum;
    InsertTuples(vertexLabel, incrementalInsertNum2, incrementalInsertNum1 + insertNum);

    // 重新查询
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(execPlan, true, &planState, 10, 10);  // 标量过滤产生 dataIds, 再走向量索引

    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
    ClearStub(g_stubIndex);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_002, TestSize.Level0)
{
    TestOfMixedQueryWithIncrementalInsert(false);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_003, TestSize.Level0)
{
    TestOfMixedQueryWithIncrementalInsert(true);
}

// 用例描述: 验证带向量字段的向标混合查询的执行计划，使用 DISKANN 索引，增量删除数据，验证不同策略的混合查询
void VectorUtExecutorDQL::TestOfMixedQueryWithIncrementalDelete(bool isSeqScan)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann ON VectorUtDQL_B USING GSDISKANN(vec COSINE);");

    if (!isSeqScan) {
        // 创建标量索引
        SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");
    }

    // 插入数据
    uint32_t insertNum = 90;
    InsertTuples(vertexLabel, insertNum, 0);

    // 创建向标混合查询的执行计划
    CreateMixedQueryPlanCtxT ctx = {0};
    ctx.vertexLabel = vertexLabel;
    ctx.limit = 10;
    ctx.offset = 3;
    ctx.distType = EXPR_OP_VECTOR_DIST_COSINE;
    ctx.hasScalarScan = true;
    ctx.isSeqScan = isSeqScan;
    ctx.indexName = "idx_diskann";
    PlanT *execPlan = CreateMixedQueryPlan(&ctx);

    g_maxTupleNum = 30;
    g_stubIndex =
        SetStubC(reinterpret_cast<void *>(ExecInitScalarScan), reinterpret_cast<void *>(ExecInitScalarScanStub));
    PlanStateT *planState = nullptr;
    CheckByExecPlan(execPlan, false, &planState, 10, 10);  // 标量过滤产生 dataIds, 再走向量索引

    // 增量删除数据, id > (2 * insertNum / 3)
    uint32_t deleteNum = insertNum / 3;
    DmValueT constValue = {};
    constValue.type = DB_DATATYPE_INT64;
    constValue.value.longValue = insertNum - deleteNum;
    DeleteTuples(vertexLabel, CreateBinaryFilterExpr(0, constValue, EXPR_OP_GE), deleteNum);

    // 重新查询
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(execPlan, true, &planState, 10, 10);  // 标量过滤产生 dataIds, 再走标量排序

    // 增量删除数据, id > (insertNum / 3)
    constValue.value.longValue = deleteNum;
    DeleteTuples(vertexLabel, CreateBinaryFilterExpr(0, constValue, EXPR_OP_GE), deleteNum);

    // 重新查询
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(execPlan, true, &planState, 10, 10);  // 标量过滤 + 标量排序

    // 增量删除所有数据
    DeleteTuples(vertexLabel, nullptr, deleteNum);
    // 重新查询
    EXPECT_EQ(GMERR_OK, ExecReScan(planState));
    CheckByExecPlan(execPlan, true, &planState, 10, 0);  // 标量过滤 + 标量排序

    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
    ClearStub(g_stubIndex);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_004, TestSize.Level0)
{
    TestOfMixedQueryWithIncrementalDelete(false);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_005, TestSize.Level0)
{
    TestOfMixedQueryWithIncrementalDelete(true);
}

// 用例描述: 验证带向量字段的向标混合查询的执行计划，使用 DISKANN 索引，随机预置数据，使用此计划查询
HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_006, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann ON VectorUtDQL_B USING GSDISKANN(vec COSINE);");

    // 创建标量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");

    srand((unsigned)time(nullptr));
    // 插入数据，数据量 [18, 18 + 100)
    uint32_t insertNum = rand() % 100 + 18;
    InsertTuples(vertexLabel, insertNum, 0);

    // 创建向标混合查询的执行计划
    CreateMixedQueryPlanCtxT ctx = {0};
    ctx.vertexLabel = vertexLabel;
    ctx.limit = 10;
    ctx.offset = 3;
    ctx.distType = EXPR_OP_VECTOR_DIST_COSINE;
    ctx.hasScalarScan = true;
    ctx.isSeqScan = false;
    ctx.indexName = "idx_diskann";
    PlanT *execPlan = CreateMixedQueryPlan(&ctx);

    g_maxTupleNum = rand() % 100;
    printf("g_maxTupleNum = %u, insertNum = %u\n", g_maxTupleNum, insertNum);  // 打印以便复现
    g_stubIndex =
        SetStubC(reinterpret_cast<void *>(ExecInitScalarScan), reinterpret_cast<void *>(ExecInitScalarScanStub));
    PlanStateT *planState = nullptr;
    CheckByExecPlan(execPlan, false, &planState, 10, 10);

    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
    ClearStub(g_stubIndex);
}

// 用例描述: 验证带向量字段的向标混合查询的执行计划，使用 DISKANN 索引，随机预置数据，限制距离阈值 < 0.004
HWTEST_F(VectorUtExecutorDQL, DISKANNIndexScan_007, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann ON VectorUtDQL_B USING GSDISKANN(vec COSINE);");

    // 创建标量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");

    srand((unsigned)time(nullptr));
    // 插入数据，数据量 [18, 18 + 100)
    uint32_t insertNum = rand() % 100 + 18;
    InsertTuples(vertexLabel, insertNum, 0);

    // 创建向标混合查询的执行计划
    CreateMixedQueryPlanCtxT ctx = {0};
    ctx.vertexLabel = vertexLabel;
    ctx.limit = 10;
    ctx.offset = 3;
    ctx.distType = EXPR_OP_VECTOR_DIST_COSINE;
    ctx.hasScalarScan = true;
    ctx.isSeqScan = false;
    ctx.indexName = "idx_diskann";
    PlanT *execPlan = CreateMixedQueryPlan(&ctx);
    // 重建 scalarScan->qualExpr, 添加 vec <=> '[xx]' < 0.004 的条件
    (static_cast<VectorIndexScanT *>(static_cast<void *>(execPlan->leftTree->leftTree)))->distanceUpperBound = 0.004;
    ExprT *preQualExpr =
        (static_cast<ScanT *>(static_cast<void *>(
             (static_cast<VectorIndexScanT *>(static_cast<void *>(execPlan->leftTree->leftTree)))->scalarScan)))
            ->qualExpr;
    DmValueT vectorValue = {};
    vectorValue.type = DB_DATATYPE_FLOATVECTOR;
    float vector[VECTOR_UT_DIMENSION] = {0};
    for (uint32_t j = 0; j < VECTOR_UT_DIMENSION; j++) {
        vector[j] = (float)(j + 1);
    }
    vectorValue.value.strAddr = static_cast<void *>(vector);
    vectorValue.value.length = VECTOR_UT_DIMENSION * sizeof(float);
    DmValueT constValue = {};
    constValue.type = DB_DATATYPE_DOUBLE;
    constValue.value.doubleValue =
        (static_cast<VectorIndexScanT *>(static_cast<void *>(execPlan->leftTree->leftTree)))->distanceUpperBound;
    ExprT *newPredicate = ExprMakeBinary(memCtx, CreateBinaryFilterExpr(2, vectorValue, EXPR_OP_VECTOR_DIST_COSINE),
        ExprMakeConst(memCtx, constValue), EXPR_OP_LT);
    (static_cast<ScanT *>(static_cast<void *>(
         (static_cast<VectorIndexScanT *>(static_cast<void *>(execPlan->leftTree->leftTree)))->scalarScan)))
        ->qualExpr = ExprMakeBinary(memCtx, preQualExpr, newPredicate, EXPR_OP_AND);

    g_maxTupleNum = rand() % 100;
    printf("g_maxTupleNum = %u, insertNum = %u\n", g_maxTupleNum, insertNum);  // 打印以便复现
    g_stubIndex =
        SetStubC(reinterpret_cast<void *>(ExecInitScalarScan), reinterpret_cast<void *>(ExecInitScalarScanStub));
    PlanStateT *planState = nullptr;
    CheckByExecPlan(execPlan, false, &planState, 10, 8);

    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
    ClearStub(g_stubIndex);
}

// 用例描述: 使用 IVFFLAT 索引，预置数据，验证同一个连接，多次设置随机 probe，再进行查询
// 预置条件: 表 VectorUtDQL_B 有元组
// 输入数据: 无。
// 执行步骤: 略。
// 预期结果: 返回近似查询到的元组
HWTEST_F(VectorUtExecutorDQL, IVFFLATIndexScanWithParameter_001, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 1. 插入数据
    uint32_t insertNum = 200;
    InsertTuples(vertexLabel, insertNum, 0);

    // 2. 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_ivf_cosine ON VectorUtDQL_B USING GSIVFFLAT(vec COSINE);");
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_ivf_l2 ON VectorUtDQL_B USING GSIVFFLAT(vec L2);");

    // 3. 构造两种距离度量的向量索引扫描的执行计划
    uint32_t limit = 100;
    uint32_t offset = 30;
    VectorIndexScanT *indexScanCosine = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_ivf_cosine"), limit, offset, EXPR_OP_VECTOR_DIST_COSINE);
    PlanStateT *planStateCosine = nullptr;
    VectorIndexScanT *indexScanL2 = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_ivf_l2"), limit, offset, EXPR_OP_VECTOR_DIST_L2);
    PlanStateT *planStateL2 = nullptr;

    SetParameterStmtT stmt = {};
    stmt.node.tag = T_SET_PARAMETER_STMT;
    DmValueT tmpValue = {};
    tmpValue.type = DB_DATATYPE_UINT32;
    srand((unsigned)time(nullptr));
    // 4. 循环 10 次，设置 probe 为随机值，使用 IVFFLAT 索引进行近似查询
    for (uint32_t i = 0; i < 10; i++) {
        tmpValue.value.uintValue = (rand() % g_maxProbe) + 1;  // [1, 32768]
        stmt.parameter = {.parameterType = IVFFLAT_PROBES, .parameterValue = tmpValue};
        ASSERT_EQ(GMERR_OK, SqlUtCmdExecutor(memCtx, session, (NodeT *)(&stmt))) << "i = " << i;
        ASSERT_EQ(tmpValue.value.uintValue, QrySqlSessionGetIvfflatProbe(session->sessionPool->dbInstance))
            << "i = " << i;
        CheckByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScanCosine)), false, &planStateCosine,
            indexScanCosine->limit, limit);
        CheckByExecPlan(
            static_cast<PlanT *>(static_cast<void *>(indexScanL2)), false, &planStateL2, indexScanL2->limit, limit);
        ASSERT_EQ(GMERR_OK, ExecReScan(planStateCosine)) << "i = " << i;
        ASSERT_EQ(GMERR_OK, ExecReScan(planStateL2)) << "i = " << i;
    }

    ExecEndNode(planStateCosine);
    ExecEndNode(planStateL2);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

#ifdef GAUSSPD_DISABLED_TDD
// 用例描述: 使用 DISKANN 索引，预置数据，验证同一个连接，多次设置随机 probe，再进行查询
// 预置条件: 表 VectorUtDQL_B 有元组
// 输入数据: 无。
// 执行步骤: 略。
// 预期结果: 返回近似查询到的元组
HWTEST_F(VectorUtExecutorDQL, DISABLED_DISKANNIndexScanWithParameter_001, TestSize.Level0)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    // 1. 插入数据
    uint32_t insertNum = 200;
    InsertTuples(vertexLabel, insertNum, 0);

    // 2. 创建向量索引
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann_cosine ON VectorUtDQL_B USING GSDISKANN(vec COSINE);");
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_diskann_l2 ON VectorUtDQL_B USING GSDISKANN(vec L2);");

    // 3. 构造两种距离度量的向量索引扫描的执行计划
    uint32_t limit = 100;
    uint32_t offset = 30;
    VectorIndexScanT *indexScanCosine = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_ivf_cosine"), limit, offset, EXPR_OP_VECTOR_DIST_COSINE);
    PlanStateT *planStateCosine = nullptr;
    VectorIndexScanT *indexScanL2 = CreateVectorIndexScan(
        vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_diskann_l2"), limit, offset, EXPR_OP_VECTOR_DIST_L2);
    PlanStateT *planStateL2 = nullptr;

    SetParameterStmtT stmt = {};
    stmt.node.tag = T_SET_PARAMETER_STMT;
    DmValueT tmpValue = {};
    tmpValue.type = DB_DATATYPE_UINT32;
    srand((unsigned)time(nullptr));
    // 4. 循环 10 次，设置 probe 为随机值，使用 DISKANN 索引进行近似查询
    for (uint32_t i = 0; i < 10; i++) {
        tmpValue.value.uintValue = (rand() % g_maxProbe) + 1;  // [1, 32768]
        stmt.parameter = {.parameterType = DISKANN_PROBE_NCANDIDATES, .parameterValue = tmpValue};
        ASSERT_EQ(GMERR_OK, SqlUtCmdExecutor(memCtx, session, (NodeT *)(&stmt))) << "i = " << i;
        ASSERT_EQ(tmpValue.value.uintValue, QrySqlSessionGetDiskAnnProbe(session->sessionPool->dbInstance))
            << "i = " << i;
        CheckByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScanCosine)), false, &planStateCosine,
            indexScanCosine->limit, limit);
        CheckByExecPlan(
            static_cast<PlanT *>(static_cast<void *>(indexScanL2)), false, &planStateL2, indexScanL2->limit, limit);
        ASSERT_EQ(GMERR_OK, ExecReScan(planStateCosine)) << "i = " << i;
        ASSERT_EQ(GMERR_OK, ExecReScan(planStateL2)) << "i = " << i;
    }

    ExecEndNode(planStateCosine);
    ExecEndNode(planStateL2);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}
#endif  // GAUSSPD_DISABLED_TDD

// 用例描述: 验证带向量字段的向标混合查询的执行计划（先向量再标量），使用 DISKANN 索引，增量插入数据，重复查询
void VectorUtExecutorDQL::TestOfFilterAfterVectorWithIncrementalInsert(uint32_t insertNum, ExprOpTypeE distType)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    const char *dist = (distType == EXPR_OP_VECTOR_DIST_L2 ? "L2" : "COSINE");
    string idxSql =
        (string) "CREATE INDEX idx_diskann_" + dist + " ON VectorUtDQL_B USING GSDISKANN(vec " + dist + ");";
    // 创建向量索引
    SqlUtExecuteStmt(memCtx, session, idxSql.c_str());

    // 创建向标混合查询的执行计划
    CreateMixedQueryPlanCtxT ctx = {0};
    ctx.vertexLabel = vertexLabel;
    ctx.limit = 10;
    ctx.offset = 3;
    ctx.distType = distType;
    ctx.hasScalarScan = false;
    ctx.isSeqScan = true;
    ctx.indexName = (string) "idx_diskann_" + dist;
    PlanT *execPlan = CreateMixedQueryPlan(&ctx);

    // 空表查询
    PlanStateT *planState = nullptr;
    CheckByExecPlan(execPlan, false, &planState, 10, 0);

    // 插入数据
    InsertTuples(vertexLabel, insertNum, 0);

    // 重新查询
    ExecEndNode(planState);
    CheckByExecPlan(execPlan, false, &planState, 10, 10);

    // 增量插入数据
    uint32_t incrementalInsertNum1 = insertNum;
    InsertTuples(vertexLabel, incrementalInsertNum1, insertNum);

    // 重新查询
    ExecEndNode(planState);
    CheckByExecPlan(execPlan, false, &planState, 10, 10);

    // 增量插入数据
    uint32_t incrementalInsertNum2 = insertNum;
    InsertTuples(vertexLabel, incrementalInsertNum2, incrementalInsertNum1 + insertNum);

    // 重新查询
    ExecEndNode(planState);
    CheckByExecPlan(execPlan, false, &planState, 10, 10);
    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 用例描述: 验证带向量字段的向标混合查询的执行计划（先向量再标量），使用 DISKANN 索引，增量删除数据，重复查询
void VectorUtExecutorDQL::TestOfFilterAfterVectorWithIncrementalDelete(uint32_t insertNum, ExprOpTypeE distType)
{
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);

    const char *dist = (distType == EXPR_OP_VECTOR_DIST_L2 ? "L2" : "COSINE");
    string idxSql =
        (string) "CREATE INDEX idx_diskann_" + dist + " ON VectorUtDQL_B USING GSDISKANN(vec " + dist + ");";
    // 创建向量索引
    SqlUtExecuteStmt(memCtx, session, idxSql.c_str());

    // 插入数据
    InsertTuples(vertexLabel, insertNum, 0);

    // 创建向标混合查询的执行计划
    CreateMixedQueryPlanCtxT ctx = {0};
    ctx.vertexLabel = vertexLabel;
    ctx.limit = 10;
    ctx.offset = 3;
    ctx.distType = distType;
    ctx.hasScalarScan = false;
    ctx.isSeqScan = true;
    ctx.indexName = (string) "idx_diskann_" + dist;
    PlanT *execPlan = CreateMixedQueryPlan(&ctx);

    PlanStateT *planState = nullptr;
    CheckByExecPlan(execPlan, false, &planState, 10, 10);

    // 增量删除数据, id > (2 * insertNum / 3)
    uint32_t deleteNum = insertNum / 3;
    DmValueT constValue = {};
    constValue.type = DB_DATATYPE_INT64;
    constValue.value.longValue = insertNum - deleteNum;
    DeleteTuples(vertexLabel, CreateBinaryFilterExpr(0, constValue, EXPR_OP_GE), deleteNum);

    // 重新查询
    ExecEndNode(planState);
    CheckByExecPlan(execPlan, false, &planState, 10, 10);

    // 增量删除数据, id > (insertNum / 3)
    constValue.value.longValue = deleteNum;
    DeleteTuples(vertexLabel, CreateBinaryFilterExpr(0, constValue, EXPR_OP_GE), deleteNum);

    // 重新查询
    ExecEndNode(planState);
    CheckByExecPlan(execPlan, false, &planState, 10, 10);

    // 增量删除所有数据
    DeleteTuples(vertexLabel, nullptr, deleteNum);

    // 重新查询
    ExecEndNode(planState);
    CheckByExecPlan(execPlan, false, &planState, 10, 0);
    ExecEndNode(planState);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNFilterAfterVector_001, TestSize.Level0)
{
    uint32_t insertNum = 100;
    ExprOpTypeE distType = EXPR_OP_VECTOR_DIST_L2;
    TestOfFilterAfterVectorWithIncrementalInsert(insertNum, distType);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNFilterAfterVector_002, TestSize.Level0)
{
    uint32_t insertNum = 100;
    ExprOpTypeE distType = EXPR_OP_VECTOR_DIST_COSINE;
    TestOfFilterAfterVectorWithIncrementalInsert(insertNum, distType);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNFilterAfterVector_003, TestSize.Level0)
{
    uint32_t insertNum = 3 * 100;
    ExprOpTypeE distType = EXPR_OP_VECTOR_DIST_L2;
    TestOfFilterAfterVectorWithIncrementalDelete(insertNum, distType);
}

HWTEST_F(VectorUtExecutorDQL, DISKANNFilterAfterVector_004, TestSize.Level0)
{
    uint32_t insertNum = 3 * 100;
    ExprOpTypeE distType = EXPR_OP_VECTOR_DIST_COSINE;
    TestOfFilterAfterVectorWithIncrementalDelete(insertNum, distType);
}

// (start, start + 1, ..., start + number - 1)
static ExprT *MakeIdInOpTupleExpr(DbMemCtxT *memCtx, const vector<int64_t> &idList)
{
    ExprT *inTuple = ExprMakeTuple(memCtx, idList.size());
    EXPECT_NE(inTuple, nullptr);
    ExprArrayT *array = ExprGetExprArray(inTuple);
    DmValueT value = {};
    value.type = DB_DATATYPE_INT64;
    for (uint32_t i = 0; i < idList.size(); i++) {
        value.value.longValue = idList[i];
        array->expr[i] = ExprMakeConst(memCtx, value);
        EXPECT_NE(array->expr[i], nullptr);
    }
    return inTuple;
}

static ExprT *MakeNameInOpTupleExpr(DbMemCtxT *memCtx, const vector<string> &nameList)
{
    ExprT *inTuple = ExprMakeTuple(memCtx, nameList.size());
    EXPECT_NE(inTuple, nullptr);
    ExprArrayT *array = ExprGetExprArray(inTuple);
    DmValueT value = {};
    value.type = DB_DATATYPE_STRING;
    for (uint32_t i = 0; i < nameList.size(); i++) {
        value.value.strAddr = const_cast<char *>(nameList[i].c_str());
        value.value.length = DM_STR_LEN(nameList[i].c_str());
        array->expr[i] = ExprMakeConst(memCtx, value);
        EXPECT_NE(array->expr[i], nullptr);
    }
    return inTuple;
}

void VectorUtExecutorDQL::CheckIdByExecPlan(PlanT *execPlan, const vector<int64_t> &expectIdList)
{
    EXPECT_EQ(GMERR_OK, SeTransBegin(session->seInstance, NULL));
    AAT *aa = NULL;
    EXPECT_EQ(GMERR_OK, NewAA(memCtx, &aa));
    Status ret;
    uint32_t count = 0;
    PlanStateT *planState = nullptr;
    EXPECT_EQ(GMERR_OK, ExecInitNode(execPlan, estate, &planState));
    while (ret = ExecProcNode(planState, aa), ret == GMERR_OK) {
        AASlotT *newSlot;
        while (newSlot = AAGetNextPropSlot(aa), newSlot != NULL) {
            DmValueT resValue = {};
            ASSERT_EQ(GMERR_OK, AASlotGetPrope(newSlot, 0, &resValue));
            ASSERT_EQ(DB_DATATYPE_INT64, resValue.type);
            ASSERT_LT(count, expectIdList.size());
            ASSERT_EQ(expectIdList[count], resValue.value.longValue) << "count = " << count;
            count++;
        }
    }
    EXPECT_EQ(GMERR_NO_DATA, ret);
    EXPECT_EQ(expectIdList.size(), count);
    EXPECT_EQ(GMERR_OK, SeTransCommit(session->seInstance));
}

// 测试 id in (1, 2, 3) 使用索引, id 不重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_001, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t insertNum = 10;
    InsertTuples(vertexLabel, insertNum, 0);

    vector<int64_t> idList = {1, 2, 3};
    ExprT *inTuple = MakeIdInOpTupleExpr(memCtx, idList);
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), inTuple, EXPR_OP_IN)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);
    PlanT *plan = static_cast<PlanT *>(static_cast<void *>(indexScan));
    CheckExplain(plan, "  ->  IndexScan on Label(VECTORUTDQL_B) Using Index[0]=idx_btree\n"
                       "        Output: id, name, vec\n"
                       "        Index Key: (id in (1, 2, 3))\n");

    // expect : [1, 2, 3]
    CheckIdByExecPlan(plan, idList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id in (1, 2, 3) 使用索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_002, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<int64_t> idList = {1, 2, 3};
    ExprT *inTuple = MakeIdInOpTupleExpr(memCtx, idList);
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), inTuple, EXPR_OP_IN)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);

    // expect : [1(repeat 25), 2(repeat 25), 3(repeat 25)]
    vector<int64_t> expectIdList;
    for (uint32_t i = 0; i < idList.size(); i++) {
        for (uint32_t j = 0; j < repeat; j++) {
            expectIdList.push_back(idList[i]);
        }
    }
    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id in (1, 1, 1) 使用索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_003, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<int64_t> idList = {1, 1, 1};
    ExprT *inTuple = MakeIdInOpTupleExpr(memCtx, idList);
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), inTuple, EXPR_OP_IN)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);
    indexScan->indexKeyNeedDistinct = true;

    // expect : [1(repeat 25)]
    vector<int64_t> expectIdList;
    for (uint32_t i = 0; i < repeat; i++) {
        expectIdList.push_back(1);
    }
    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id = 2 and name in ('name_2', 'name_3', 'name_4') 使用联合索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_004, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id, name);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<string> nameList = {"name_2", "name_3", "name_4"};
    ExprT *inTuple = MakeNameInOpTupleExpr(memCtx, nameList);
    uint32_t id = 2;
    DmValueT value = {};
    value.type = DB_DATATYPE_INT64;
    value.value.longValue = id;
    vector<ExprT *> indexQualExpr{
        ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), ExprMakeConst(memCtx, value), EXPR_OP_EQ),
        ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 1), inTuple, EXPR_OP_IN)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);

    // expect : [2(repeat 25)]
    vector<int64_t> expectIdList(repeat, value.value.longValue);
    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id in (1, 2, 3) and name in ('name_2', 'name_3', 'name_4') 使用联合索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_005, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id, name);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<int64_t> idList = {1, 2, 3};
    ExprT *idTuple = MakeIdInOpTupleExpr(memCtx, idList);
    vector<string> nameList = {"name_2", "name_3", "name_4"};
    ExprT *nameTuple = MakeNameInOpTupleExpr(memCtx, nameList);
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), idTuple, EXPR_OP_IN),
        ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 1), nameTuple, EXPR_OP_IN)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);

    // expect : [2(repeat 25), 3(repeat 25)]
    vector<int64_t> expectIdList;
    for (int64_t i = 2; i <= *max_element(idList.begin(), idList.end()); i++) {
        for (uint32_t j = 0; j < repeat; j++) {
            expectIdList.push_back(i);
        }
    }

    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id in (1, 2, 2, 3, 2) and name in ('name_2', 'name_3', 'name_2', 'name_4') 使用联合索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_006, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id, name);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<int64_t> idList = {1, 2, 2, 3, 2};
    ExprT *idTuple = MakeIdInOpTupleExpr(memCtx, idList);
    vector<string> nameList = {"name_2", "name_3", "name_2", "name_4"};
    ExprT *nameTuple = MakeNameInOpTupleExpr(memCtx, nameList);
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), idTuple, EXPR_OP_IN),
        ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 1), nameTuple, EXPR_OP_IN)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);
    indexScan->indexKeyNeedDistinct = true;

    // expect : [2(repeat 25), 3(repeat 25)]
    vector<int64_t> expectIdList;
    for (int64_t i = 2; i <= *max_element(idList.begin(), idList.end()); i++) {
        for (uint32_t j = 0; j < repeat; j++) {
            expectIdList.push_back(i);
        }
    }

    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id in (1, 2, 3) and name > 'name_2' 使用联合索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_007, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id, name);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<int64_t> idList = {1, 2, 3};
    ExprT *inTuple = MakeIdInOpTupleExpr(memCtx, idList);
    DmValueT value = {};
    value.type = DB_DATATYPE_STRING;
    value.value.constStrAddr = "name_2";
    value.value.length = DM_STR_LEN(static_cast<const char *>(value.value.constStrAddr));
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), inTuple, EXPR_OP_IN),
        ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 1), ExprMakeConst(memCtx, value), EXPR_OP_GT)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);

    // expect : [3(repeat 25)]
    vector<int64_t> expectIdList(repeat, 3);
    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}

// 测试 id in (1, 2, 3, 1, 2, 4, 3, 1) and name > 'name_2' 使用联合索引, 每个 id 有 25 条重复
HWTEST_F(VectorUtExecutorDQL, IndexScanWithInExpr_008, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "CREATE INDEX idx_btree ON VectorUtDQL_B(id, name);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("VectorUtDQL_B", session);
    uint32_t repeat = 25;
    uint32_t insertNum = 5;
    for (uint32_t i = 0; i < repeat; i++) {
        InsertTuples(vertexLabel, insertNum, 0);
    }

    vector<int64_t> idList = {1, 2, 3, 1, 2, 3, 1, 4};
    ExprT *inTuple = MakeIdInOpTupleExpr(memCtx, idList);
    DmValueT value = {};
    value.type = DB_DATATYPE_STRING;
    value.value.constStrAddr = "name_2";
    value.value.length = DM_STR_LEN(static_cast<const char *>(value.value.constStrAddr));
    vector<ExprT *> indexQualExpr{ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 0), inTuple, EXPR_OP_IN),
        ExprMakeBinary(memCtx, ExprMakeVar(memCtx, 1), ExprMakeConst(memCtx, value), EXPR_OP_GT)};
    IndexScanT *indexScan =
        CreateIndexScan(vertexLabel, UtGetIndexIdByName(vertexLabel, "idx_btree"), indexQualExpr, nullptr);
    indexScan->indexKeyNeedDistinct = true;

    // expect : [3(repeat 25), 4(repeat 25)]
    vector<int64_t> expectIdList;
    for (int64_t i = 3; i <= *max_element(idList.begin(), idList.end()); i++) {
        for (uint32_t j = 0; j < repeat; j++) {
            expectIdList.push_back(i);
        }
    }
    CheckIdByExecPlan(static_cast<PlanT *>(static_cast<void *>(indexScan)), expectIdList);
    CataReleaseVertexLabel(vertexLabel, session->sessionPool->dbInstance);
}
