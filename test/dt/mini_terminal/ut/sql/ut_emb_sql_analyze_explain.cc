/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test for analyze explain of SQL
 * Author: QE Team
 * Create: 2024-03-12
 */

#include "gtest/gtest.h"
#include "cpl_ir_explain.h"
#include "cpl_sql_compiler.h"
#include "cpl_public_sql_parser_common.h"
#include "ut_emb_sql_common.h"
using namespace testing::ext;

#ifdef __cplusplus
extern "C" {
#endif

extern "C" Status SqlServiceEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx);

#ifdef __cplusplus
}
#endif

static DbMemCtxT *g_sqlBasicMem;
static SessionT *g_sqlSession;

typedef struct SqlExplainErrCase {
    const char *sql;
    Status ret;
} SqlExplainErrCaseT;

class UTEmbSqlAnalyzeExplain : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        g_sqlBasicMem =
            DbCreateDynMemCtx(DbSrvGetSysDynCtx(UtGetDbInstance()->instanceId), false, "dynamic memory context", &args);
        EXPECT_NE(nullptr, g_sqlBasicMem);
        QrySessionAlloc(SESSION_TYPE_NO_CONN, 0, UtGetDbInstance(), &g_sqlSession);
        EXPECT_NE(nullptr, g_sqlSession);
        Status ret = FixBufCreate(g_sqlSession->rsp, g_sqlBasicMem, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        EXPECT_EQ(ret, GMERR_OK);
        const char *input = "create table A(X int, Y text, Z text);"
                            "create table B(U text, V int);"
                            "create table C(X int, U text);"
                            "create table D(age int default 18, id text);";
        SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, input);
    }
    static void TearDownTestCase()
    {
        const char *input = "drop table A;"
                            "drop table B;"
                            "drop table C;"
                            "drop table D;";
        SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, input);
        QrySessionRelease(g_sqlSession);
        DbDeleteDynMemCtx(g_sqlBasicMem);
        BaseUninit();
    }
    virtual void SetUp()
    {
        sb.memCtx = g_sqlBasicMem;
    }
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    void UtEmbSqlCheckIRExplainPlanEqual(DbListT *irStmtList, NodeTagT tag)
    {
        SqlIrStmtT *plan1 = *static_cast<SqlIrStmtT **>(DbListItem(irStmtList, 0));
        SqlIrStmtT *plan2 = *static_cast<SqlIrStmtT **>(DbListItem(irStmtList, 1));
        ASSERT_TRUE(plan1->isExplainStmt);
        ASSERT_FALSE(plan2->isExplainStmt);
        ASSERT_EQ(plan1->utilityStmt, nullptr);
        ASSERT_EQ(plan2->utilityStmt, nullptr);
        ASSERT_EQ(plan1->tag, tag);
        ASSERT_EQ(plan2->tag, tag);
#ifndef NDEBUG
        DmSbReset(&sb);
        IRExplainPlan(plan1->irPlan, &sb);
        char *dumpStr1 = DmSbDump(&sb);
        DmSbReset(&sb);
        IRExplainPlan(plan2->irPlan, &sb);
        char *dumpStr2 = DmSbDump(&sb);
        EXPECT_STRCASEEQ(dumpStr1, dumpStr2);
        DbDynMemCtxFree(g_sqlBasicMem, dumpStr1);
        DbDynMemCtxFree(g_sqlBasicMem, dumpStr2);
#endif
    }

protected:
    StringBuilderT sb = {};
};

/*
 * TestCase for normal and abnormal explain create table stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainCreateTable, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain create table tableName(name text);",
        "explain create table tableName(name text primary key, age int);",
        "explain create table tableName(id int default 1, name text unique);",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        SqlIrStmtT *explainStmt = *static_cast<SqlIrStmtT **>(DbListItem(&irStmtList, 0));
        ASSERT_EQ(explainStmt->tag, T_SQL_CREATE_TABLE_STMT);
        ASSERT_EQ(explainStmt->irPlan, nullptr);
        ASSERT_NE(explainStmt->utilityStmt, nullptr);
        ASSERT_EQ(explainStmt->isExplainStmt, true);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain create table A(name text);", GMERR_DUPLICATE_TABLE},
        {"explain create table undefined.tableName(name text);", GMERR_UNDEFINED_OBJECT},
        {"explain create table tableName(name);", GMERR_SYNTAX_ERROR},
        {"explain create table tableName(name undefinedType);", GMERR_SYNTAX_ERROR},
        {"explain create table tableName(name text, name int);", GMERR_DUPLICATE_COLUMN},
        {"explain create table "
         "tableName("
         "nameaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa text);",
            GMERR_NAME_TOO_LONG},
        {"explain create table tableName(name text primary key AUTOINCREMENT);", GMERR_DATATYPE_MISMATCH},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
}

/*
 * TestCase for normal and abnormal explain create index stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainCreateIndex, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain create index indexName on A(X);",
        // 当前create index 不支持指定where，语法错误
        /*
                "explain create index indexName on A(X) where X = 1;",
                "explain create index indexName on A(X) where X = 1 and Y = '2' and Z = '3';",
        */
        "explain create unique index if not exists indexName on A(X);",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        SqlIrStmtT *explainStmt = *static_cast<SqlIrStmtT **>(DbListItem(&irStmtList, 0));
        ASSERT_EQ(explainStmt->tag, T_SQL_CREATE_INDEX_STMT);
        ASSERT_EQ(explainStmt->irPlan, nullptr);
        ASSERT_NE(explainStmt->utilityStmt, nullptr);
        ASSERT_EQ(explainStmt->isExplainStmt, true);
    }

    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "create index defaultIndex on D(age);");

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain create index indexName on t3(id) ;", GMERR_UNDEFINED_TABLE},
        {"explain create index A on D(X);", GMERR_DUPLICATE_OBJECT},
        {"explain create index defaultIndex on D(id);", GMERR_DUPLICATE_OBJECT},
        {"explain create index indexName on A(test);", GMERR_UNDEFINE_COLUMN},
        {"explain create index "
         "nameaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa on A(X);",
            GMERR_NAME_TOO_LONG},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }

    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "drop index D.defaultIndex;");
}

/*
 * TestCase for normal and abnormal explain create trigger stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainCreateTrigger, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain create trigger trigger_name update on A "
        " begin delete from A where X = 1;update A set X=1 where Y = '1'; end;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        SqlIrStmtT *explainStmt = *static_cast<SqlIrStmtT **>(DbListItem(&irStmtList, 0));
        ASSERT_EQ(explainStmt->tag, T_SQL_CREATE_TRIGGER_STMT);
        ASSERT_EQ(explainStmt->irPlan, nullptr);
        ASSERT_NE(explainStmt->utilityStmt, nullptr);
        ASSERT_EQ(explainStmt->isExplainStmt, true);
    }

    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "create trigger trig1 insert on A begin delete from A; end;");

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain create trigger trig1 insert on A begin delete from A; end;", GMERR_DUPLICATE_OBJECT},
        {"explain create trigger trig2 insert on M begin delete from A; end;", GMERR_UNDEFINED_TABLE},
        {"explain create temporary trigger trig2 insert on A begin delete from A; end;", GMERR_FEATURE_NOT_SUPPORTED},
        {"explain create trigger trigger_name instead of delete on A begin delete from A; end;",
            GMERR_FEATURE_NOT_SUPPORTED},
        {"explain create trigger trig2 insert on A begin insert into A default values; end;",
            GMERR_FEATURE_NOT_SUPPORTED},
        {"explain create trigger trig2 insert on A begin delete from A indexed by X; end;",
            GMERR_FEATURE_NOT_SUPPORTED},
        {"explain create trigger trig2 insert on A begin update A not indexed set X = 100; end;",
            GMERR_FEATURE_NOT_SUPPORTED},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }

    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "drop trigger trig1;");
}

#ifdef GAUSSPD_DISABLED_TDD
/*
 * TestCase for normal and abnormal explain create view stmt analyzer
 */
// 当前语法不支持view
HWTEST_F(UTEmbSqlAnalyzeExplain, DISABLED_SqlExplainCreateView, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain create view testView as select * from A;",
        "explain create view if not exists testView as select * from A;",
        "explain create view test2 as select * from A where X > 0;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        SqlIrStmtT *explainStmt = *static_cast<SqlIrStmtT **>(DbListItem(&irStmtList, 0));
        ASSERT_EQ(explainStmt->tag, T_SQL_CREATE_VIEW_STMT);
        ASSERT_EQ(explainStmt->irPlan, nullptr);
        ASSERT_NE(explainStmt->utilityStmt, nullptr);
        ASSERT_EQ(explainStmt->isExplainStmt, true);
    }

    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "create view test1 as select * from A;");

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain create view test1 as select * from A;", GMERR_DUPLICATE_TABLE},
        {"explain create view A as select * from B;", GMERR_DUPLICATE_TABLE},
        {"explain create view test2 as select W from B;", GMERR_UNDEFINE_COLUMN},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "drop view test1;");
}

/*
 * TestCase for normal and abnormal explain alter table stmt analyzer
 */
// 当前语法不支持alter table
HWTEST_F(UTEmbSqlAnalyzeExplain, DISABLED_SqlExplainAlterTable, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain alter table A rename to test2;",
        "explain alter table A rename column X to date;",
        "explain alter table A add age int;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        SqlIrStmtT *explainStmt = *static_cast<SqlIrStmtT **>(DbListItem(&irStmtList, 0));
        ASSERT_EQ(explainStmt->tag, T_SQL_ALTER_TABLE_STMT);
        ASSERT_EQ(explainStmt->irPlan, nullptr);
        ASSERT_NE(explainStmt->utilityStmt, nullptr);
        ASSERT_EQ(explainStmt->isExplainStmt, true);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain alter table test1 rename to test2;", GMERR_UNDEFINED_TABLE},
        {"explain alter table A rename to A;", GMERR_DUPLICATE_TABLE},
        {"explain alter table test1 rename column W to R;", GMERR_UNDEFINED_TABLE},
        {"explain alter table test3 add id int;", GMERR_UNDEFINED_TABLE},
        {"explain alter table A add id int primary key;", GMERR_INVALID_COLUMN_DEFINITION},
        {"explain alter table A add id int unique;", GMERR_INVALID_COLUMN_DEFINITION},
        {"explain alter table A rename to "
         "nameaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa;",
            GMERR_NAME_TOO_LONG},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
}
#endif  // GAUSSPD_DISABLED_TDD

/*
 * TestCase for normal and abnormal explain drop stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainDrop, TestSize.Level0)
{
    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "create index indexA on A(X);"
        /*
            "create trigger triggerA update on A "
            " begin delete from A where X = 1;update A set X=1 where Y = '1'; end;"
            "create view viewA as select * from B;"
        */
    );

    const char *normalInput[] = {
        "explain drop table A;", "explain drop index A.indexA;", "explain drop index if exists A.indexA;",
        /*
                "explain drop trigger triggerA;",
                "explain drop trigger if exists triggerA;",
                "explain drop view viewA;",
                "explain drop view if exists viewA;",
        */
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        SqlIrStmtT *explainStmt = *static_cast<SqlIrStmtT **>(DbListItem(&irStmtList, 0));
        ASSERT_EQ(explainStmt->tag, T_SQL_DROP_STMT);
        ASSERT_EQ(explainStmt->irPlan, nullptr);
        ASSERT_NE(explainStmt->utilityStmt, nullptr);
        ASSERT_EQ(explainStmt->isExplainStmt, true);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain drop table notExist;", GMERR_UNDEFINED_TABLE},
        {"explain drop index A.notExist;", GMERR_UNDEFINED_OBJECT},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
    SqlUtExecuteStmt(g_sqlBasicMem, g_sqlSession, "drop index A.indexA;");
}

/*
 * TestCase for normal and abnormal explain select stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainSelect, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain select X from A as AA;select X from A as AA;", "explain select * from A as AA;select * from A as AA;",
        "explain select * from A where X = 22 and Z = 'a';select * from A where X = 22 and Z = 'a';",
        "explain select * from A where Y = Z;select * from A where Y = Z;",
        // 当前不支持join
        /*
                "explain select X, B.U from B natural join C;select X, B.U from B natural join C;",
                "explain select * from A, B;select * from A, B;",
                "explain select * from A, B natural join C cross join D;select * from A, B natural join C cross join
           D;",
        */
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        UtEmbSqlCheckIRExplainPlanEqual(&irStmtList, T_SQL_SELECT_STMT);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain select * from E;", GMERR_UNDEFINED_TABLE},
        // 待确定，目前修改预期
        {"explain select A from db.B;", GMERR_UNDEFINE_COLUMN},
        {"explain select W from A;", GMERR_UNDEFINE_COLUMN},
        {"explain select B.X from A;", GMERR_UNDEFINE_COLUMN},
        {"explain select A.X from A as tmp;", GMERR_UNDEFINE_COLUMN},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
}

/*
 * TestCase for normal and abnormal explain insert stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainInsert, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain insert into D values (12345, '12345');"
        " insert into D values (12345, '12345');",
        "explain insert into D as aliasTable(id, age) values (12345, 'Tommy'), (12345, 'Tommy');"
        " insert into D as aliasTable(id, age) values (12345, 'Tommy'), (12345, 'Tommy');",
        "explain insert into D default values;"
        "insert into D default values;",
        // 当前语法不支持conflict
        /*
                "explain insert into D values(12345, 'JOY') on conflict do nothing;"
                " insert into D values(12345, 'JOY') on conflict do nothing;",
                "explain insert into D values(12345, 'JOY') on conflict(id) do update set id = id + 1;"
                " insert into D values(12345, 'JOY') on conflict(id) do update set id = id + 1;",
                "explain insert into D values(12345, 'JOY') on conflict(id) where id = 12345 do nothing;"
                " insert into D values(12345, 'JOY') on conflict(id) where id = 12345 do nothing;",
        */
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        UtEmbSqlCheckIRExplainPlanEqual(&irStmtList, T_SQL_INSERT_STMT);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain insert into table_not_exist(id, age, name) VALUES(1, 19, 'Catty');", GMERR_UNDEFINED_TABLE},
        {"explain insert into D(id, age, name) VALUES(1, 19, 'Catty');", GMERR_UNDEFINE_COLUMN},
        {"explain insert into D(id, age) VALUES(1, 19, 'Catty');", GMERR_SYNTAX_ERROR},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
}

/*
 * TestCase for normal and abnormal explain delete stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainDelete, TestSize.Level0)
{
    const char *normalInput[] = {
        "explain delete from A;"
        "delete from A;",
        "explain delete from A as aliasTbl where aliasTbl.X = 1 and Y = 'constStr';"
        "delete from A as aliasTbl where aliasTbl.X = 1 and Y = 'constStr';",
        "explain delete from A where Y || Z = 'stu1100';"
        "delete from A where Y || Z = 'stu1100';",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        UtEmbSqlCheckIRExplainPlanEqual(&irStmtList, T_SQL_DELETE_STMT);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain delete from W;", GMERR_UNDEFINED_TABLE},
        {"explain delete from A where H = 1 and Y = 'constStr';", GMERR_UNDEFINE_COLUMN},
        {"explain delete from A as aliasTbl where aliasTblx.id = 1;", GMERR_UNDEFINE_COLUMN},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
}

/*
 * TestCase for normal and abnormal explain update stmt analyzer
 */
HWTEST_F(UTEmbSqlAnalyzeExplain, SqlExplainUpdate, TestSize.Level0)
{
    const char *normalInput[] = {
        // 当前语法不支持
        /*
                "explain update or fail A set Y = 'exprSet';"
                "update or fail A set Y = 'exprSet';",
                "explain update or abort A indexed by idx set Y = 'exprSet';"
                "update or abort A indexed by idx set Y = 'exprSet';",
                "explain update A set (Z, Y) = ('exprSet', 'exprSet2'), (Z, Y) = ('exprSet3', 'exprSet4');"
                "update A set (Z, Y) = ('exprSet', 'exprSet2'), (Z, Y) = ('exprSet3', 'exprSet4');",
        */
        "explain update A set Y = 'exprSet', X = 'exprSet2';"
        "update A set Y = 'exprSet', X = 'exprSet2';",
        "explain update A set Y = 'exprSet' where X = 1 and Y = 'constStr';"
        "update A set Y = 'exprSet' where X = 1 and Y = 'constStr';",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);

        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(ret, GMERR_OK);

        UtEmbSqlCheckIRExplainPlanEqual(&irStmtList, T_SQL_UPDATE_STMT);
    }

    const SqlExplainErrCaseT abnormalInput[] = {
        {"explain update W set id = 1;", GMERR_UNDEFINED_TABLE},
        {"explain update A set id = 1;", GMERR_UNDEFINE_COLUMN},
        {"explain update A set X = 1 where id = 3;", GMERR_UNDEFINE_COLUMN},
    };

    uint32_t count = ELEMENT_COUNT(abnormalInput);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedList = {0};
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        DbListT irStmtList;
        ret = SqlUtAnalyze(g_sqlSession, g_sqlBasicMem, &parsedList, &irStmtList);
        ASSERT_EQ(abnormalInput[i].ret, ret);
    }
}
