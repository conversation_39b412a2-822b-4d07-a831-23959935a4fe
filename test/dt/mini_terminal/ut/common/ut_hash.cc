/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "gtest/gtest.h"
#include "db_hash.h"
#include "adpt_memory.h"
#include "db_mini_common_init.h"
#include "common_init.h"

using namespace testing::ext;

class UtHash : public testing ::Test {
protected:
    static void SetUpTestCase()
    {
        CommonInit(nullptr);
    };
    static void TearDownTestCase()
    {
        CommonRelease();
    };
};

HWTEST_F(UtHash, UtHashTest, TestSize.Level0)
{
    const char *str = "aab";
    uint32_t firstHash = DbStrToHash32(str);
    uint32_t secondHash = DbStrToHash32(str);
    ASSERT_EQ(firstHash, secondHash);
    const char *str2 = "abc";
    firstHash = DbStrToHash32(str);
    secondHash = DbStrToHash32(str2);
    ASSERT_NE(firstHash, secondHash);
}
