/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "gtest/gtest.h"
#include "common_init.h"
#include "db_error.h"

using namespace testing::ext;

class UtError : public testing::Test {
public:
    void SetUp(){};
    void TearDown(){};
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
};

/**
 * @tc.name: UtError_001
 * @tc.desc: Test the registration of error codes.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IREQ02816373.011.011
 * @tc.author: lian<PERSON><PERSON><PERSON>
 */
HWTEST_F(UtError, UtError_001, TestSize.Level0)
{
#ifndef NDEBUG
    /**
     * @tc.steps: step1. Get format string of a registered error code.
     * @tc.expected: step1. Return value is not nullptr.
     */
    const char *fmtStr = DbErrGetFmtStr((Status)ERRORCODE_REGISTERED);
    EXPECT_TRUE(fmtStr != nullptr);

    /**
     * @tc.steps: step2. Get format string of an error code that does not exist.
     * @tc.expected: step2. Return value is nullptr.
     */
    fmtStr = DbErrGetFmtStr((Status)ERRORCODE_NOT_EXIST);
    EXPECT_TRUE(fmtStr == nullptr);
#endif
}
