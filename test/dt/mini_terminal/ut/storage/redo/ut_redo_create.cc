/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <fstream>
#include "gtest/gtest.h"
#include "gmock/gmock.h"
#include "se_define.h"
#include "db_mem_context.h"
#include "db_instance.h"
#include "se_redo.h"
#include "se_redo_file.h"
#include "se_redo_buf.h"
#include "stub.h"
#include "common_init.h"
#include "storage_common.h"

static const char *g_defaultCfg = R"(
    "deviceSize": 4096,
    "pageSize": 4,
    "maxConnNum": 100,
    "redoPubBufSize": 4096,
    "redoFlushByTrx": 1,
    "redoFileSize": 96,
    "dataFilePath": "./data/gmdb/datafile",
    "bufferPoolSize": 1200
)";

static const char *g_redoDataFilePath = "./data/gmdb/";
static const char *g_dataFileDirPath = "./data/gmdb/datafile";
extern "C" {
Status StorageGetPersistConfig(SeInstanceT *seInsPtr);
StatusInter RedoMgrMallocMem(RedoMgrT *redoMgr);
StatusInter RedoDefaultConfig(RedoMgrT *redoMgr);
}
using namespace testing::ext;
using namespace std;
class UtRedoCreate : public StorageCommon, public testing::TestWithParam<ConfigMode> {
public:
    static SeInstanceT *seIns;
    static DbInstanceT *dbInstance;

protected:
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
        DbMakeDirectory("./data/gmdb", PERM_USRRWX);
        InitStub();
        Status ret = CommonInit(GetConfigStr(g_defaultCfg, GetParam()).c_str());
        DB_ASSERT(ret == GMERR_OK);
        seIns = new SeInstanceT{0};
        dbInstance = UtGetDbInstance();
        RedoDmlInitSeInstance();
    }

    virtual void TearDown()
    {
        delete seIns;
        seIns = nullptr;
        CommonRelease();
        ClearAllStub();
    }

    void InitSingleProcessResource(SeInstanceT *seIns)
    {
        // init lock
        DbInterProcLockFnInit(&seIns->seLockFn);
        DbInterProcRWLockFnInit(&seIns->seRWLockFn);

        seIns->memUtils.memAlloc = DbDynShmemMemCtxAlloc;
        seIns->memUtils.memFree = DbDynShmemMemCtxFree;
        seIns->memUtils.shmPtr2addr = DbDynShmemPtrToAddr;
        seIns->memUtils.memCtxShmPtr2addr = DbMemCtxDynShmemPtrToAddr;
        seIns->memUtils.dynPtr2addr = DynamicPtr2MemAddr;
        seIns->memUtils.dynPtrShift = DynamicMemAddrShift;
        seIns->memUtils.memCtx = reinterpret_cast<DbMemCtxT *>(seIns->seServerMemCtx);
        seIns->memUtils.sharedMemory = false;
    }

    void RedoDmlInitSeInstance()
    {
        SeConfigT config;
        memset_s(&config, sizeof(SeConfigT), 0, sizeof(SeConfigT));
        Status ret = StorageConfigGet(dbInstance, &config);
        DB_ASSERT(ret == GMERR_OK);
        DbMemCtxArgsT args = {0};
        seIns->seServerMemCtx =
            DbCreateDynMemCtx(reinterpret_cast<DbMemCtxT *>(dbInstance->topDynMemCtx), true, "SeTopMemCtx", &args);
        InitSingleProcessResource(seIns);
        seIns->redoCtxShm = DB_INVALID_SHMPTR;
        seIns->seConfig = config;
        seIns->dbInstance = dbInstance;
        StorageGetPersistConfig(seIns);
        seIns->seConfig.redoPubBufSize = SIZE_M(1);
        seIns->seConfig.pageSize = 4;
        seIns->seConfig.redoFlushByTrx = false;
        seIns->seConfig.redoFileSize = SIZE_M(256);
        seIns->seConfig.multiZoneNum = 1;
        seIns->seConfig.persMode = (uint32_t)PERS_INCREMENT;
        RedoAmInit(seIns);
    }
};

INSTANTIATE_TEST_CASE_P(
    CreateRedo, UtRedoCreate, testing::Values(ConfigMode::ENCRYPTED_ENA, ConfigMode::ENCRYPTED_DIS));

SeInstanceT *UtRedoCreate::seIns = nullptr;
DbInstanceT *UtRedoCreate::dbInstance = nullptr;
/**
 * @tc.name: RedoMgrCreate_001
 * @tc.desc: test RedoMgrCreate
 * @tc.type: FUNC
 * @tc.require: AR.SR.IREQ02816373.010.002
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. first boot
     * @tc.expected: step2. seConfig.redoDir exist
     * @tc.expected: step3. redo log file exist
     */
    DbSetStartMode(UtRedoCreate::seIns, DB_INIT);
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    bool exist = DbDirExist(g_redoDataFilePath);
    EXPECT_EQ(exist, true);
    const uint32_t fileNameSize = 1024;
    char fileName[fileNameSize] = {0};
    sprintf_s(fileName, fileNameSize, "%s%s", g_dataFileDirPath, ".redo");
    exist = DbFileExist(fileName);
    EXPECT_EQ(exist, true);
    RedoMgrDestroy(seIns->redoMgr);
}

/**
 * @tc.name: RedoMgrCreate_002
 * @tc.desc: test RedoMgrCreate repeat
 * @tc.type: FUNC
 * @tc.require: AR.SR.IREQ02816373.010.002
 * @tc.author: xiaoda
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. repeat create redo manager
     * @tc.expected: oldRedoMgr is equal newRedoMgr;
     */
    StatusInter ret = RedoMgrCreate(seIns);
    ASSERT_EQ(ret, STATUS_OK_INTER);
    RedoMgrT *oldRedoMgr = seIns->redoMgr;
    ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoMgrT *newRedoMgr = seIns->redoMgr;
    EXPECT_EQ(oldRedoMgr, newRedoMgr);

    /**
     * @tc.steps: step2. clean resource
     **/
    RedoMgrDestroy(seIns->redoMgr);
}

/**
 * @tc.name: RedoMgrCreate_004
 * @tc.desc: test RedoMgrCreate malloc redo manager fail
 * @tc.type: FUNC
 * @tc.require: AR.SR.IREQ02816373.010.002
 * @tc.author: xiaoda
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. malloc redo manager fail
     * @tc.expected: step1. return GMERR_MEMORY_OPERATE_FAILED
     */
    DbMemCtxT *oldMemCtx = reinterpret_cast<DbMemCtxT *>(seIns->seServerMemCtx);
    seIns->seServerMemCtx = nullptr;
    seIns->memUtils.memCtx = nullptr;
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, OUT_OF_MEMORY_MEM_FAILED);
    seIns->seServerMemCtx = oldMemCtx;
    seIns->memUtils.memCtx = oldMemCtx;
}

/*
 * @tc.name: RedoCheckConfigValid
 * @tc.desc: create redoMgr when pribufsize < pubbufsize
 * @tc.type: FUNC
 * @tc.require: AR.SR.IREQ02816373.010.002
 * @tc.author: xiaoda
 */
HWTEST_P(UtRedoCreate, RedoCheckConfigValid, TestSize.Level2)
{
    /**
     * @tc.steps: step1. set mode DB_OPEN, file is not exist
     * @tc.expected: step1. return STATUS_OK_INTER
     */
    StatusInter ret = RedoCheckConfigValid(seIns->seConfig.redoPubBufSize, 32);
    EXPECT_EQ(ret, CONFIG_ERROR_INTER);
}

/**
 * @tc.name: RedoMgrCreate_007
 * @tc.desc: test RedoMgrCreate by default
 * @tc.type: FUNC
 * @tc.require: AR.SR.IREQ02816373.010.002
 * @tc.author: linzhuobin
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_007, TestSize.Level0)
{
    DbSetStartMode(UtRedoCreate::seIns, DB_INIT);
    seIns->seConfig.isRedoConfigInit = false;
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(STATUS_OK_INTER, ret);
}

/*
 * @tc.name: RedoMgrCreate_008
 * @tc.desc: test malloc wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_008, TestSize.Level0)
{
    StatusInter ret = OUT_OF_MEMORY_INTER;
    int i = 1;
    while (ret == OUT_OF_MEMORY_INTER || ret == OUT_OF_MEMORY_MEM_FAILED) {
        gStubMap["DbDynMemCtxAlloc"][0] =
            SetStubC(reinterpret_cast<void *>(DbDynMemCtxAlloc), reinterpret_cast<void *>(DbDynMemCtxAllocTimesMock));
        gStubMap["DbDynMemCtxAlloc"][1] = i;
        ret = RedoMgrCreate(seIns);
        i++;
    }
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ClearStub(gStubMap["DbDynMemCtxAlloc"][0]);
}

/*
 * @tc.name: RedoMgrCreate_009
 * @tc.desc: test convert redoMgr->pubCtx memory wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_009, TestSize.Level2)
{
    seIns->redoCtxShm = {0, 0};
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, MEMORY_OPERATE_FAILED_INTER);
}

/*
 * @tc.name: RedoMgrCreate_010
 * @tc.desc: test convert redoMgr->redoFiles.files[i].info memory wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_010, TestSize.Level2)
{
    RedoPubCtxT *pubCtx = reinterpret_cast<RedoPubCtxT *>(
        DbWrappedMemCtxAllocAddr(&seIns->memUtils, sizeof(RedoPubCtxT), &seIns->redoCtxShm));
    ASSERT_NE(pubCtx, nullptr);
    pubCtx->fileSet.logFileShm = {0, 0};
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, MEMORY_OPERATE_FAILED_INTER);
}

/*
 * @tc.name: RedoMgrCreate_011
 * @tc.desc: test seIns->seConfig.redoPubBufSize > DB_MAX_FILE_MAP_ALLOC_SIZE
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_011, TestSize.Level0)
{
    seIns->seConfig.sharedModeEnable = true;
    seIns->seConfig.redoPubBufSize = DB_MAX_FILE_MAP_ALLOC_SIZE + 1;
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(seIns->redoMgr->cfg.pubBufSize, DB_MAX_FILE_MAP_ALLOC_SIZE);
}

/*
 * @tc.name: RedoMgrCreate_012
 * @tc.desc: test seIns->seConfig.redoBufParts = 0
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_012, TestSize.Level0)
{
    seIns->seConfig.redoBufParts = 0;
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, INT_ERR_REDO_PART_INVALID);
}

/*
 * @tc.name: RedoMgrCreate_013
 * @tc.desc: test convert redoBuf->flushBuf memory wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_P(UtRedoCreate, RedoMgrCreate_013, TestSize.Level0)
{
    seIns->fileMapMgr = new FileMapMgrT{0};
    seIns->fileMapMgr->isFirstBoot = false;
    StatusInter ret = RedoMgrCreate(seIns);
    EXPECT_EQ(ret, MEMORY_OPERATE_FAILED_INTER);
}
