/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Implementation of diskann index ut common
 * Author: wanyi
 * Create: 2024-02-22
 */

#include "storage_diskann_ut_common.h"

#include <chrono>
#include <cstdio>
#include <cstdlib>
#include <cxxabi.h>
#include <execinfo.h>
#include <random>
#include <sys/mman.h>
#include "gtest/gtest.h"

#include "common_init.h"
#include "db_dynmem_algo.h"
#include "db_table_space.h"
#include "se_ckpt.h"
#include "se_diskann_index.h"
#include "se_diskann_iterator.h"
#include "se_diskann_utils.h"
#include "se_heap_base.h"
#include "se_redo_inner.h"
#include "storage_common.h"
#include "stub.h"

extern "C" {
IndexCtxT *GetDiskAnnIdxCtx(TrxT *trx, HeapRunCtxT *heapCtx, uint32_t indexId, PageIdT idxPageId);
Status GetDiskAnnOpenCfg(
    TrxT *trx, HeapRunCtxT *heapCtx, SeTrxContainerCtxT *heapCntr, uint32_t indexId, IndexOpenCfgT *idxOpenCfg);
StatusInter TrxGetHeapHandleByCntrWithoutAlloc(
    const TrxT *trx, SeTrxContainerCtxT *seTrxCntrCtx, HpRunHdlT *heapRunHdl);
Status DiskAnnIndexVaccum(IndexCtxT *idxCtx);
}

constexpr int DEFAULT_QUEUE = 20;

uint32_t GetRandNum()
{
    auto now = std::chrono::high_resolution_clock::now();
    auto ns = std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
    unsigned int randomNumberSeed = static_cast<unsigned int>(ns);
    return rand_r(&randomNumberSeed);
}

void ConstructDiskAnnIndexMetaCfg(IndexMetaCfgT *diskAnnIdxMetaCfg, uint16_t vecDim)
{
    AnnVaccumParaT deleteParam = {.delRateLimHigh = DEFAULT_DISKANN_DEL_RATE_LIM_HIGH,
        .delRateLimLow = DEFAULT_DISKANN_DEL_RATE_LIM_LOW,
        .vaccumTimeLong = DEFAULT_DISKANN_VACCUM_TIME_LONG_MS,
        .vaccumTimeShort = DEFAULT_DISKANN_VACCUM_TIME_SHORT_MS};

    AnnMetaCfgT *metaCfg = new AnnMetaCfgT;
    metaCfg->vectorDim = vecDim;
    metaCfg->outDegree = OUTDEGREE;
    metaCfg->robustAlpha = INDEXINGALPHA;
    metaCfg->numFrozens = NUM_FORZEN_POINTS;
    metaCfg->distType = DIST_TYPE_L2;
    metaCfg->queueSize = 128;  // 128为默认quene大小
    metaCfg->reserveDist = true;
    metaCfg->acceleratePrune = false;
    metaCfg->enableAdaptive = false;
    metaCfg->maxClusterVariance = 0.0;
    metaCfg->clustersMinDist = 0.0;
    metaCfg->vaccumParaCfg = deleteParam;
    metaCfg->numQuantBit = 32;  // 32为默认numQuantBit大小
    metaCfg->quantMode = ANN_QUANT_NONE;

    diskAnnIdxMetaCfg->indexId = 0;
    diskAnnIdxMetaCfg->idxType = DISKANN_INDEX;
    diskAnnIdxMetaCfg->idxConstraint = PRIMARY;
    diskAnnIdxMetaCfg->indexMultiVersionType = INDEX_ONE_VERSION_TYPE;
    diskAnnIdxMetaCfg->isUseClusteredHash = false;
    diskAnnIdxMetaCfg->indexCap = 0;
    diskAnnIdxMetaCfg->isLabelLatchMode = false;
    diskAnnIdxMetaCfg->tableSpaceId = 0;
    diskAnnIdxMetaCfg->tableSpaceIndex = 0;
    diskAnnIdxMetaCfg->instanceId = GET_INSTANCE_ID;
    diskAnnIdxMetaCfg->nullInfoBytes = 0;
    diskAnnIdxMetaCfg->hasVarchar = false;
    diskAnnIdxMetaCfg->isHcGlobalLatch = false;
    diskAnnIdxMetaCfg->hcLatch = NULL;
    diskAnnIdxMetaCfg->extendParam = metaCfg;
}

void DiskAnnCreateAndOpen(IndexMetaCfgT indexCfg, ShmemPtrT *idxShmAddr, IndexCtxT **idxCtx)
{
    DB_POINTER2(idxShmAddr, idxCtx);
    UtDiskAnnBegin();
    ASSERT_EQ(GMERR_OK, IdxCreate(StorageCommon::seRunCtx, indexCfg, idxShmAddr));

    ASSERT_EQ(GMERR_OK, IdxAlloc(StorageCommon::seRunCtx, DISKANN_INDEX, idxCtx));

    IndexOpenCfgT indexOpenCfg = CreateDiskAnnIndexOpenCfg();
    (*idxCtx)->idxOpenCfg = indexOpenCfg;
    (*idxCtx)->idxMetaCfg = indexCfg;
    (*idxCtx)->idxShmAddr = *idxShmAddr;
    ASSERT_EQ(GMERR_OK, IdxOpen(*idxShmAddr, &indexOpenCfg, *idxCtx));
    UtDiskAnnCommit();
}

void ReliableTransferVector2Float(vector<float> &floatVector, vector<vector<float>> &vectors)
{
    for (const auto &innerVec : vectors) {
        floatVector.insert(floatVector.end(), innerVec.begin(), innerVec.end());
    }
}

void ReliableAnnStartDiskAnnBuild(IndexCtxT *idxCtx, bool sample, uint16_t dim, uint32_t count, uint64_t *dataId,
    DmVecDistTypeE distType, float *vectors)
{
    VecBuildStateParaT buildStatePara = {
        .count = count, .distType = distType, .dataId = dataId, .vectors = vectors, .sample = sample, .dim = dim};
    UtDiskAnnBegin();
    EXPECT_EQ(GMERR_OK, AnnIdxBuild(idxCtx, &buildStatePara));
    UtDiskAnnCommit();
}

void CreateVectorsAndDataId(uint32_t numPerClass, uint32_t numSamples, uint16_t dimensions,
    std::vector<std::vector<float>> &vectors, uint64_t *dataId)
{
    unsigned int randomNumberSeed = time(NULL);
    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> tmpVector;
        int base = i / numPerClass;
        for (uint16_t j = 0; j < dimensions; j++) {
            // 10为生成随机向量值的起始值
            tmpVector.push_back(base * 10 + ((float)rand_r(&randomNumberSeed) / (float)RAND_MAX));
        }
        vectors.push_back(tmpVector);
        dataId[i] = i;
    }
}

void ScanAndCompareResult(IndexCtxT *idxCtx, IndexScanCfgT *cfg, uint64_t dataId[], int findNum)
{
    IndexScanItrT iter = NULL;
    EXPECT_EQ(GMERR_OK, IdxBeginScan(idxCtx, *cfg, &iter));

    int pos = 0;
    uint64_t resultId;
    bool found = true;
    Status ret = GMERR_OK;
    while (found) {
        ret = IdxScan(idxCtx, iter, &resultId, &found);
        if (ret == GMERR_NO_DATA) {
            break;
        }
        ASSERT_EQ(ret, GMERR_OK);
        if (found) {
            ASSERT_EQ(resultId, dataId[pos]);
            if (cfg->scanDirect == INDEX_SCAN_ASCEND) {
                pos += 1;
            } else {
                pos -= 1;
            }
        }
        if (pos == findNum) {
            break;
        }
    }
    IdxEndScan(idxCtx, iter);
}

void DiskAnnCloseAndDrop(ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    IdxClose(idxCtx);
    IdxRelease(idxCtx);
    UtDiskAnnBegin();
    IdxDrop(StorageCommon::seRunCtx, DISKANN_INDEX, idxShmAddr);
    UtDiskAnnCommit();
}

void DestroyDiskAnnIndexMetaCfg(IndexMetaCfgT *diskAnnIdxMetaCfg)
{
    delete static_cast<AnnMetaCfgT *>(diskAnnIdxMetaCfg->extendParam);
}

IndexOpenCfgT CreateDiskAnnIndexOpenCfg(void)
{
    HeapRunCtxT *heapRunCtx = new HeapRunCtxT();
    HeapT heap = {0};
    heapRunCtx->heap = &heap;
    heapRunCtx->heap->constInfo.labelId = 0;
    IndexOpenCfgT indexOpenCfg = {0};
    indexOpenCfg.seRunCtx = StorageCommon::seRunCtx;
    indexOpenCfg.vertex = nullptr;
    indexOpenCfg.heapHandle = heapRunCtx;
    indexOpenCfg.chHandle = nullptr;
    indexOpenCfg.indexLabel = StorageCommon::indexLabel;
    indexOpenCfg.indexLabel->nullInfoBytes = 1;  // idxKey之前含有1个bytes的偏移
    return indexOpenCfg;
}

void SetIdxGlobalMetaCfg(IndexMetaCfgT &idxMetaCfgForDiskAnn, AnnMetaCfg metaCfgPara)
{
    AnnMetaCfgT *metaCfg = new AnnMetaCfgT;
    *metaCfg = metaCfgPara;
    idxMetaCfgForDiskAnn.indexId = 0;
    idxMetaCfgForDiskAnn.idxType = DISKANN_INDEX;
    idxMetaCfgForDiskAnn.idxConstraint = PRIMARY;
    idxMetaCfgForDiskAnn.indexMultiVersionType = INDEX_ONE_VERSION_TYPE;
    idxMetaCfgForDiskAnn.isUseClusteredHash = false;
    idxMetaCfgForDiskAnn.indexCap = 0;
    idxMetaCfgForDiskAnn.isLabelLatchMode = false;
    idxMetaCfgForDiskAnn.tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID;
    idxMetaCfgForDiskAnn.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    idxMetaCfgForDiskAnn.instanceId = GET_INSTANCE_ID;
    idxMetaCfgForDiskAnn.nullInfoBytes = 0;
    idxMetaCfgForDiskAnn.hasVarchar = false;
    idxMetaCfgForDiskAnn.isHcGlobalLatch = false;
    idxMetaCfgForDiskAnn.hcLatch = nullptr;
    idxMetaCfgForDiskAnn.keyDataType = SE_INDEX_DATATYPE_DM_VALUE;
    idxMetaCfgForDiskAnn.extendParam = metaCfg;
}

void FreeGlobalMetaCfg(IndexMetaCfgT &idxMetaCfgForDiskAnn)
{
    delete static_cast<AnnMetaCfgT *>(idxMetaCfgForDiskAnn.extendParam);
}

IndexOpenCfgT GenIndexOpenCfg(SeRunCtxHdT seRunCtx)
{
    HeapRunCtxT *heapRunCtx = new HeapRunCtxT();
    HeapT heap = {0};
    heapRunCtx->heap = &heap;
    heapRunCtx->heap->constInfo.labelId = 0;
    heapRunCtx->heapCfg.isPersistent = true;
    DmIndexLabelT *label = new DmIndexLabelT();
    label->nullInfoBytes = 0;
    IndexOpenCfgT indexOpenCfg = {0};
    indexOpenCfg.seRunCtx = seRunCtx;
    indexOpenCfg.vertex = nullptr;
    indexOpenCfg.heapHandle = heapRunCtx;
    indexOpenCfg.chHandle = nullptr;
    indexOpenCfg.indexLabel = label;

    return indexOpenCfg;
}

void ReleaseIdxOpenCfg(IndexCtxT *idxCtx)
{
    IndexOpenCfgT indexOpenCfg = idxCtx->idxOpenCfg;
    if (indexOpenCfg.heapHandle != nullptr) {
        delete (indexOpenCfg.heapHandle);
    }
    if (indexOpenCfg.indexLabel != nullptr) {
        delete (indexOpenCfg.indexLabel);
    }
}

std::vector<float> TransferVector2Float(const vector<vector<float>> &vectors)
{
    std::vector<float> floatVector = {};
    for (const auto &innerVec : vectors) {
        floatVector.insert(floatVector.end(), innerVec.begin(), innerVec.end());
    }
    return floatVector;
}

std::vector<std::vector<float>> CreateRandVector(uint32_t numClass, uint32_t numPerClass, uint16_t dimensions)
{
    std::vector<std::vector<float>> res = {};
    uint32_t numSamples = numClass * numPerClass;
    for (uint32_t i = 0; i < numSamples; i++) {
        int base = i / numPerClass;
        res.push_back(GenRandVector(dimensions, base));
    }
    return res;
}

std::vector<float> CreateRandVectors(uint32_t numClass, uint32_t numPerClass, uint16_t dimensions)
{
    return TransferVector2Float(CreateRandVector(numClass, numPerClass, dimensions));
}

static void GetQuantPara(DiskAnnQuantTypeE quantType, uint16_t *numQuantBit, QuantModeE *quantMode)
{
    switch (quantType) {
        case DiskAnnQuantTypeE::DISKANN_QUANT_NONE:
            *numQuantBit = BITS_NUM_FLOAT;
            *quantMode = ANN_QUANT_NONE;
            return;
        case DiskAnnQuantTypeE::DISKANN_QUANT_LVQ_8BIT:
            *numQuantBit = BITS_NUM_8;
            *quantMode = ANN_QUANT_LVQ;
            return;
        case DiskAnnQuantTypeE::DISKANN_QUANT_LVQ_4BIT:
            *numQuantBit = BITS_NUM_4;
            *quantMode = ANN_QUANT_LVQ;
            return;
        default:
            *numQuantBit = BITS_NUM_FLOAT;
            *quantMode = ANN_QUANT_NONE;
    }
}

Status DiskAnnVertexSet(DiskAnnCtxT *ctxt, const DiskAnnVertexT *vertexData)
{
    DB_POINTER2(ctxt, vertexData);
    uint8_t *vertexHead = nullptr;
    EXPECT_EQ(STATUS_OK_INTER, RedoLogBegin(SeGetCurRedoCtx()));
    Status ret = DiskAnnGetVertexPoint(ctxt, vertexData->vertexId, true, &vertexHead);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)memcpy_s(vertexHead, sizeof(DiskAnnVertexT), vertexData, sizeof(DiskAnnVertexT));
    DiskAnnLeaveVertexPoint(ctxt, vertexData->vertexId, true);
    EXPECT_EQ(STATUS_OK_INTER, RedoLogEnd(SeGetCurRedoCtx(), true));
    return GMERR_OK;
}

void VertexIdPrint(DiskAnnAddrT vertexId, const char *str, bool changeLine)
{
    printf("%s", str);
    printf("-|%d-%d-%d|-", vertexId.pageId.deviceId, vertexId.pageId.blockId, vertexId.slotId);
    if (changeLine) {
        printf("\n");
    }
}

void PageListPrint(DiskAnnCtxT *ctxt, const DiskAnnNodeMgrT *mgr)
{
    DB_POINTER2(ctxt, mgr);
    printf("=========================================\n");
    printf("[Page debug] Page List, end [%d-%d]: \n", mgr->dataListEnd.deviceId, mgr->dataListEnd.blockId);
    DiskAnnPageIdT pageId = mgr->dataListHead;
    while (!IsPageUnused(&pageId)) {
        uint8_t *pageHead = NULL;
        Status ret = DiskAnnGetPage(ctxt, pageId, false, &pageHead);
        if (ret != GMERR_OK) {
            printf("***** [ShowPageList] debug error *****");
            return;
        }
        DiskAnnNodeHdrT *pageHead2 = GetDiskAnnNodeHdrFromPageHead(pageHead);
        DiskAnnPageIdT nextPageId = pageHead2->nextPageId;
        DiskAnnLeavePage(ctxt, pageId, false);
        printf("[%u - %u] (%u / %u) isFree %d prev[%u - %u]-next[%u - %u]", pageId.deviceId, pageId.blockId,
            pageHead2->numSlots, pageHead2->maxSlotNums, pageHead2->isFreePage, pageHead2->prevPageId.deviceId,
            pageHead2->prevPageId.blockId, pageHead2->nextPageId.deviceId, pageHead2->nextPageId.blockId);
        printf("{");
        for (int i = 0; i < pageHead2->maxSlotNums; ++i) {
            if (pageHead2->isSlotFree[i])
                printf("0");
            else
                printf("1");
            if (i > 0 && i % 5 == 0)
                printf("-");
        }
        printf("}\n");
        pageId = nextPageId;
    }
    printf("\n");
    printf("[Page debug] Free Page List: head-[%u - %u] \n", mgr->freeListHead.deviceId, mgr->freeListHead.blockId);
    DiskAnnPageIdT pageIdF = mgr->freeListHead;
    while (!IsPageUnused(&pageIdF)) {
        uint8_t *pageHead = NULL;
        Status ret = DiskAnnGetPage(ctxt, pageIdF, false, &pageHead);
        if (ret != GMERR_OK) {
            printf("***** [ShowPageList] debug error *****");
            return;
        }
        DiskAnnNodeHdrT *pageHead2 = GetDiskAnnNodeHdrFromPageHead(pageHead);
        DiskAnnPageIdT nextPageId = pageHead2->nextFreePage;
        DiskAnnLeavePage(ctxt, pageIdF, false);
        printf("[%u - %u] (%u / %u) prev[%u - %u]-next[%u - %u] ", pageIdF.deviceId, pageIdF.blockId,
            pageHead2->numSlots, pageHead2->maxSlotNums, pageHead2->prevFreePage.deviceId,
            pageHead2->prevFreePage.blockId, pageHead2->nextFreePage.deviceId, pageHead2->nextFreePage.blockId);
        printf("{");
        for (int i = 0; i < pageHead2->maxSlotNums; ++i) {
            if (pageHead2->isSlotFree[i])
                printf("0");
            else
                printf("1");
            if (i > 0 && i % 5 == 0)
                printf("-");
        }
        printf("} \n");
        pageIdF = nextPageId;
    }
    printf("===============================================\n");
}

static bool IsMasterVertex(const DiskAnnVertexT *vertex)
{
    DB_POINTER(vertex);
    return (vertex->flag & DISKANN_SLAVE_NODE) != DISKANN_SLAVE_NODE;
}

typedef struct DiskAnnStat {
    uint32_t numAlloced;
    uint32_t numFree;
    uint32_t numMaster;
    uint32_t numSlave;
    uint32_t numDead;
    uint32_t numAlive;
    uint32_t numDeadSlave;
} DiskAnnStatT;

// 统计节点数量, 返回节点总数
static void DiskAnnStatData(const DiskAnnVertexT *vertex, DiskAnnStatT *stat)
{
    DB_POINTER2(vertex, stat);
    stat->numAlloced++;
    if (IsVertexUnused(&(vertex->vertexId))) {
        stat->numFree++;
        return;
    }
    if (IsMasterVertex(vertex)) {
        stat->numMaster++;
    } else {
        stat->numSlave++;
    }

    if ((vertex->flag & DISKANN_DELETED_NODE) == DISKANN_DELETED_NODE) {
        stat->numDead++;
        if (!IsMasterVertex(vertex)) {
            stat->numDeadSlave++;
        }
    } else {
        stat->numAlive++;
    }
}

static void DiskAnnStatLog(const DiskAnnStatT *stat)
{
    DB_POINTER(stat);
    LOG_RUN_INFO_CUSTOM_UNFOLD("|SE-DISKANN||STAT| numAlloced %u, numFree %u, numMaster %u, numSlave %u, numDead %u, "
                               "numAlive %u, numDeadSlave %u\n",
        stat->numAlloced, stat->numFree, stat->numMaster, stat->numSlave, stat->numDead, stat->numAlive,
        stat->numDeadSlave);
}

Status CheckAllData(DiskAnnCtxT *adpt, DiskAnnPageIdT firtPageToFree)
{
    LOG_RUN_INFO_CUSTOM_UNFOLD("[SE-DISKANN]CHECK ALL DATA BEGIN frozens is [%d-%d-%d]",
        adpt->meta.frozens[0].pageId.deviceId, adpt->meta.frozens[0].pageId.blockId, adpt->meta.frozens[0].slotId);
    DiskAnnLogMeta(&(adpt->meta));
    DiskAnnStatT stat = {0};
    Status finalRet = GMERR_OK;
    DiskAnnPageIdT pageToChkDataId = adpt->meta.vertexNodeMgr.dataListHead;
    while (!IsPageUnused(&pageToChkDataId)) {
        uint8_t *pageHead = NULL;
        Status ret = DiskAnnGetPage(adpt, pageToChkDataId, false, &pageHead);
        if (ret != GMERR_OK) {
            LOG_RUN_ERROR_CUSTOM(ret, "[SE-DISKANN] [CheckAllData] An exception occurred in DiskAnnGetPage");
            return ret;
        }
        DiskAnnNodeHdrT *nodeHdr = GetDiskAnnNodeHdrFromPageHead(pageHead);
        ret = DiskAnnNodeHdrCorruptCheck(&(adpt->meta.vertexNodeMgr), nodeHdr, false);
        if (ret != GMERR_OK) {
            finalRet = ret;
            break;
        }
        DiskAnnPageIdT nextPageToChkDataId = nodeHdr->nextPageId;
        for (uint16_t i = 1; i <= nodeHdr->maxSlotNums; i++) {
            if (nodeHdr->isSlotFree[i - 1]) {
                continue;
            }
            DiskAnnAddrT addr = {.pageId = pageToChkDataId, .slotId = i};
            uint8_t *nodeHead = GetNodeHead(pageHead, i, adpt->meta.vertexNodeMgr.nodeSize);
            ret = DiskAnnVertexCheck(adpt, nodeHead, addr, false);
            if (ret != GMERR_OK) {
                finalRet = ret;
                break;
            }
            DiskAnnStatData(GetVertexFromPage(nodeHead), &stat);
        }
        DiskAnnLeavePage(adpt, pageToChkDataId, false);
        if (IsPageEqual(pageToChkDataId, nextPageToChkDataId)) {
            break;
        }
        pageToChkDataId = nextPageToChkDataId;
    }
    DiskAnnStatLog(&stat);
    return finalRet;
}

Status UtDiskAnnBegin(void)
{
    TrxCfgT trxCfg = {
        .readOnly = false,
        .isLiteTrx = false,
        .isBackGround = false,
        .isInteractive = false,
        .connId = 8888,
        .trxType = PESSIMISTIC_TRX,
        .isolationLevel = READ_COMMITTED,
    };
    return SeTransBegin(StorageCommon::seRunCtx, &trxCfg);
}

SeTrxContainerCtxT *TrxGetContainerCtxByIdMock(TrxT *trx, uint32_t containerId)
{
    auto trxContainer = new SeTrxContainerCtxT();
    trxContainer->ctxHead.type = (uint16_t)TRX_HEAP_HANDLE;
    trxContainer->heapTrxCtx.heapRunHdl = new HeapRunCtxT();  // DiskAnnUtCtxT::indexOpenCfg_.heapHandle;
    return trxContainer;
}

Status GetDiskAnnOpenCfgMock(
    TrxT *trx, HeapRunCtxT *heapCtx, SeTrxContainerCtxT *heapCntr, uint32_t indexId, IndexOpenCfgT *idxOpenCfg)
{
    *idxOpenCfg = DiskAnnUtCtxT::indexOpenCfg_;
    idxOpenCfg->seRunCtx = StorageCommon::seRunCtx;
    return GMERR_OK;
}

StatusInter TrxGetHeapHandleByCntrWithoutAllocMock(
    const TrxT *trx, SeTrxContainerCtxT *seTrxCntrCtx, HpRunHdlT *heapRunHdl)
{
    *heapRunHdl = new struct TagHeapOpCtxT();
    return STATUS_OK_INTER;
}

void UtDiskAnnCommit(bool isRollBack)
{
    if (isRollBack) {
        auto stubIdx1 = SetStubC((void *)TrxGetContainerCtxById, (void *)TrxGetContainerCtxByIdMock);
        auto stubIdx2 = SetStubC((void *)GetDiskAnnOpenCfg, (void *)GetDiskAnnOpenCfgMock);
        auto stubIdx3 =
            SetStubC((void *)TrxGetHeapHandleByCntrWithoutAlloc, (void *)TrxGetHeapHandleByCntrWithoutAllocMock);
        ASSERT_EQ(SeTransRollback(StorageCommon::seRunCtx, false), GMERR_OK);
        ClearStub(stubIdx1);
        ClearStub(stubIdx2);
        ClearStub(stubIdx3);
    } else {
        ASSERT_EQ(SeTransCommit(StorageCommon::seRunCtx), GMERR_OK);
    }
}

DiskAnnUtCtxT::DiskAnnUtCtxT()
{
    CtxtInitParaT ctxtInitPara = {.dim = DEFAULT_DIM,
        .outdegree = OUTDEGREE,
        .alpha = INDEXINGALPHA,
        .quantType = DiskAnnQuantTypeE::DISKANN_QUANT_NONE};
    metaCfg_.vectorDim = ctxtInitPara.dim;
    metaCfg_.outDegree = ctxtInitPara.outdegree;
    metaCfg_.robustAlpha = ctxtInitPara.alpha;
    metaCfg_.distType = DIST_TYPE_L2;
    metaCfg_.numFrozens = NUM_FORZEN_POINTS;
    metaCfg_.queueSize = DEFAULT_QUEUE;
    metaCfg_.reserveDist = true;
    metaCfg_.vaccumParaCfg.delRateLimLow = 20;    // 20%触发低速真删除
    metaCfg_.vaccumParaCfg.delRateLimHigh = 60;   // 60%触发高速真删除
    metaCfg_.vaccumParaCfg.vaccumTimeLong = 30;   // 每一次整理超过30ms后停止
    metaCfg_.vaccumParaCfg.vaccumTimeShort = 10;  // 每一次整理超过10ms后停止
    GetQuantPara(ctxtInitPara.quantType, &metaCfg_.numQuantBit, &metaCfg_.quantMode);
    SetIdxGlobalMetaCfg(idxMetaCfg_, metaCfg_);
}

DiskAnnUtCtxT::DiskAnnUtCtxT(const DiskAnnInitParamT *param)
{
    DiskAnnUtCtxT();
    metaPage_ = param->metaPage;
}

DiskAnnUtCtxT::~DiskAnnUtCtxT()
{
    FreeGlobalMetaCfg(idxMetaCfg_);
}

void DiskAnnUtCtxT::DiskAnnUtIndexCreate(SeRunCtxHdT seRunCtx)
{
    EXPECT_EQ(GMERR_OK, DiskAnnIndexCreate(seRunCtx, idxMetaCfg_, &metaPage_));
}

void DiskAnnUtCtxT::DiskAnnUtIndexDrop(SeRunCtxHdT seRunCtx)
{
    EXPECT_EQ(STATUS_OK_INTER, RedoLogBegin(SeGetCurRedoCtx()));
    DiskAnnIndexDrop(seRunCtx, metaPage_);
    EXPECT_EQ(STATUS_OK_INTER, RedoLogEnd(SeGetCurRedoCtx(), false));
}

// 不使用const是因为idxKey.keyData不是const
static inline IndexKeyT GetIdxKeyFromVecs(std::vector<float> vecs)
{
    IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(vecs.data()),
        .keyLen = (uint32_t)(vecs.size() * sizeof(float)),
        .prefixPropeNum = 0};
    return idxKey;
}

void DiskAnnUtCtxT::DiskAnnUtInsert(uint64_t id, const std::vector<float> &vecs)
{
    ASSERT_NE(indexCtx_, nullptr);
    EXPECT_NE(GMERR_DATA_CORRUPTION, DiskAnnIndexInsert(indexCtx_, GetIdxKeyFromVecs(vecs), id));
    lastInsertVertexId_ = *(DiskAnnAddrT *)(indexCtx_->vertexIdBuffer);
}

void DiskAnnUtCtxT::DiskAnnUtDelete(uint64_t id, const std::vector<float> &vecs, DiskAnnAddrT delVertexId)
{
    ASSERT_NE(indexCtx_, nullptr);
    auto idxKey = GetIdxKeyFromVecs(vecs);
    IndexRemoveParaT removePara = {0};
    removePara.isGc = false;
    *(DiskAnnAddrT *)(indexCtx_->vertexIdBuffer) = delVertexId;
    EXPECT_NE(GMERR_DATA_CORRUPTION, DiskAnnIndexDelete(indexCtx_, idxKey, id, removePara));
}

Status DiskAnnUtCtxT::DiskAnnUtVaccum(void)
{
    EXPECT_NE(indexCtx_, nullptr);
    Status ret = DiskAnnIndexVaccum(indexCtx_);
    EXPECT_NE(GMERR_DATA_CORRUPTION, ret);
    return ret;
}

std::unordered_set<uint64_t> DiskAnnUtCtxT::DiskAnnUtSearch(
    const std::vector<float> &vecs, uint32_t topN, uint32_t probe)
{
    EXPECT_NE(indexCtx_, nullptr);
    VecScanParaT params = {
        .dim = static_cast<uint16_t>(vecs.size()),
        .probe = probe,
        .topN = topN,
        .vector = const_cast<float *>(vecs.data()),
        .distanceUpperBound = DB_MAX_DOUBLE,
    };
    IndexScanCfgT cfg;
    cfg.scanDirect = INDEX_SCAN_DESCEND;
    cfg.scanPara = (void *)&params;
    cfg.scanDirect = INDEX_SCAN_ASCEND;
    AnnIter *iter = nullptr;
    EXPECT_EQ(GMERR_OK, DiskAnnIndexBeginScan(indexCtx_, cfg, (void **)&iter));
    std::unordered_set<uint64_t> resIds = {};
    for (auto i = 0; i < iter->count; i++) {
        resIds.insert(iter->dataIdRes[i]);
    }
    return resIds;
}

void DiskAnnUtCtxT::DiskAnnUtGC()
{
    ASSERT_NE(indexCtx_, nullptr);
    IndexRemoveParaT removePara = {0};
    removePara.isGc = true;
    *(DiskAnnAddrT *)(indexCtx_->vertexIdBuffer) = SE_INVALID_VERTEX_ID;
    IndexKeyT idxKey = {0};
    ASSERT_EQ(GMERR_OK, DbGetExternalErrno(RedoLogBegin(SeGetCurRedoCtx())));
    EXPECT_NE(GMERR_DATA_CORRUPTION, DiskAnnIndexDelete(indexCtx_, idxKey, 0, removePara));
    ASSERT_EQ(GMERR_OK, DbGetExternalErrno(RedoLogEnd(SeGetCurRedoCtx(), true)));
}

std::vector<DiskAnnAddrT> DiskAnnUtCtxT::DiskAnnUtGetAllPreSlaves(DiskAnnAddrT vertexId)
{
    std::vector<DiskAnnAddrT> preSlaves = {};
    DiskAnnVertexT *vertexData = nullptr;
    DiskAnnAddrT iterVertex = vertexId;
    do {
        EXPECT_EQ(GMERR_OK, DiskAnnVertexGet(ctxt_, iterVertex, false, &vertexData));
        preSlaves.push_back(iterVertex);
        iterVertex = vertexData->preSlaveId;
        DiskAnnVertexFree(ctxt_, &vertexData);
    } while (!IsVertexUnused(&iterVertex));
    return preSlaves;
}

std::vector<DiskAnnAddrT> DiskAnnUtCtxT::DiskAnnUtGetAllSlaves(DiskAnnAddrT masterId)
{
    std::vector<DiskAnnAddrT> slaveIds = {};
    DiskannAliveSlaveIterT slaveIter = {0};
    EXPECT_EQ(GMERR_OK, CreateSlaveIterator(masterId, ctxt_, true, &slaveIter));
    for (DiskannSlaveIterBegin(&slaveIter); !DiskannSlaveIterIsFinished(&slaveIter); DiskannSlaveIterNext(&slaveIter)) {
        slaveIds.push_back(slaveIter.curSlaveId);
    }
    ReleaseSlaveIterator(&slaveIter);
    return slaveIds;
}

IndexOpenCfgT DiskAnnUtCtxT::indexOpenCfg_ = {0};
IndexCtxT *DiskAnnUtCtxT::DiskAnnUtIndexOpen(SeRunCtxHdT seRunCtx)
{
    EXPECT_EQ(GMERR_OK, IdxAlloc(seRunCtx, DISKANN_INDEX, &indexCtx_));
    indexOpenCfg_ = GenIndexOpenCfg(seRunCtx);
    indexCtx_->idxOpenCfg = indexOpenCfg_;
    indexCtx_->idxMetaCfg = idxMetaCfg_;
    indexCtx_->idxShmAddr = metaPage_;
    EXPECT_EQ(GMERR_OK, DiskAnnIndexOpen(indexCtx_));
    ctxt_ = GetDiskAnnCtxt(indexCtx_);
    return indexCtx_;
}

void DiskAnnUtCtxT::DiskAnnUtIndexClose(void)
{
    ASSERT_NE(indexCtx_, nullptr);
    DiskAnnIndexClose(indexCtx_);
    IdxRelease(indexCtx_);
}

#ifndef NDEBUG
FitHelper::FitHelper(PrepareFunc prepare, FaultExecutorFunc faultExecutor, FaultHandlerFunc faultHandler)
    : shareMemName_("./mysharedmemory.txt"),
      prepare_(prepare),
      faultExecutor_(faultExecutor),
      faultHandler_(faultHandler)
{}

std::string FitHelper::curFaultFuncName_ = "";
double FitHelper::prob_ = 0.0;

void FitHelper::Run(const std::string &faultFuncName, uint32_t repeatNum, double faultProbability)
{
    prob_ = faultProbability;
    curFaultFuncName_ = faultFuncName;
    for (uint32_t i = 0; i < repeatNum; i++) {
        InnerRun();
    }
    curFaultFuncName_ = "";
    prob_ = 0.0;
}

static void StacktracePrint()
{
    constexpr int backTraceSize = 1024;
    void *buffer[backTraceSize];
    size_t size = backtrace(buffer, backTraceSize);
    if (size == 0) {
        std::cerr << "Failed to get stack trace" << std::endl;
        return;
    }
    char **strings = backtrace_symbols(buffer, size);
    for (size_t i = 0; i < size; ++i) {
        std::cerr << " " << strings[i] << std::endl;
    }
    free(strings);
}

void FitHelper::InnerRun()
{
    int fd = open(shareMemName_, O_CREAT | O_RDWR, 0666);
    EXPECT_GT(fd, 0);
    auto sharedMemSize = sizeof(DiskAnnUtCtxT::DiskAnnInitParamT);
    truncate(shareMemName_, sharedMemSize);
    void *shareParamPtr = mmap(0, sharedMemSize * 4, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);

    pid_t pid1 = fork();
    if (pid1 < 0) {
        ASSERT_TRUE(false);
        return;
    }
    if (pid1 == 0) {
        FaultExecutor(shareParamPtr);
    }
    int status = GMERR_OK;
    waitpid(pid1, &status, 0);
    if (WIFEXITED(status) && (WEXITSTATUS(status) == 255)) {  // 故障注入的进程退出的返回值是255
        FaultHandler(shareParamPtr);
    } else if (status != 0) {
        ASSERT_TRUE(false);
    }
    munmap(shareParamPtr, sharedMemSize);
    close(fd);
    DbRemoveFile(shareMemName_);
}

static void UtFlushRedoLog()
{
    RedoPointT startPoint = {0};
    StatusInter intRet = RedoLogFlush(StorageCommon::seIns->redoMgr, &startPoint);
    ASSERT_EQ(STATUS_OK_INTER, intRet);
}

Status FitHelper::StubErrorMock(Status ret, const char *funcName)
{
    UtFlushRedoLog();
    // 创建一个随机数生成器
    std::random_device rd;
    // 使用随机数生成器初始化一个Mersenne Twister引擎
    std::mt19937 gen(rd());
    // 创建一个分布，范围是0到1，均匀分布
    std::uniform_real_distribution<double> dis(0.0, 1.0);
    double randomNumber = dis(gen);
    if (std::string(funcName) == curFaultFuncName_ && (randomNumber < prob_)) {
        std::cout << "Current Crash function: " << funcName << " Target func name :" << curFaultFuncName_ << std::endl;
        StacktracePrint();
        exit(-1);
    }
    return ret;
}

void FitHelper::Prepare(void *args)
{
    if (prepare_ != nullptr) {
        prepare_(args);
    }
    CkptTrigger(StorageCommon::seIns, CKPT_MODE_FULL, true, WAIT_SECONDS_FOR_CHECKPOINT);
}

void FitHelper::FaultExecutor(void *args)
{
    StorageCommon::ConstructSeInsAndSeRun(nullptr);
    Prepare(args);
    InitStub();
    auto stubIdx = SetStubC((void *)StubError, (void *)StubErrorMock);
    if (faultExecutor_ != nullptr) {
        faultExecutor_(args);
    }
    ClearStub(stubIdx);
    StorageCommon::DestroySeIns();
    exit(0);
}

void FitHelper::FaultHandler(void *args)
{
    InitStub();
    auto stubIdx1 = SetStubC((void *)TrxGetContainerCtxById, (void *)TrxGetContainerCtxByIdMock);
    auto stubIdx2 = SetStubC((void *)GetDiskAnnOpenCfg, (void *)GetDiskAnnOpenCfgMock);
    auto stubIdx3 =
        SetStubC((void *)TrxGetHeapHandleByCntrWithoutAlloc, (void *)TrxGetHeapHandleByCntrWithoutAllocMock);
    StorageCommon::ConstructSeInsAndSeRun(nullptr, true);
    ClearStub(stubIdx1);
    ClearStub(stubIdx2);
    ClearStub(stubIdx3);

    if (faultHandler_ != nullptr) {
        faultHandler_(args);
    }
    StorageCommon::DestroySeIns();
}
#endif  // NDEBUG

IndexCtxT *InitIdxCtx(const CtxtInitParaT *ctxtInitPara, SeRunCtxHdT seRunCtx)
{
    IndexMetaCfgT indexCfg = {0};
    AnnMetaCfg metaCfg = {0};
    metaCfg.vectorDim = ctxtInitPara->dim;
    metaCfg.outDegree = ctxtInitPara->outdegree;
    metaCfg.robustAlpha = ctxtInitPara->alpha;
    metaCfg.distType = DIST_TYPE_L2;
    metaCfg.numFrozens = NUM_FORZEN_POINTS;
    metaCfg.queueSize = DEFAULT_QUEUE;
    metaCfg.reserveDist = true;

    GetQuantPara(ctxtInitPara->quantType, &metaCfg.numQuantBit, &metaCfg.quantMode);
    SetIdxGlobalMetaCfg(indexCfg, metaCfg);
    ShmemPtrT idxShmAddr = {0};
    EXPECT_EQ(GMERR_OK, DiskAnnIndexCreate(seRunCtx, indexCfg, &idxShmAddr));
    IndexCtxT *idxCtx = nullptr;
    EXPECT_EQ(GMERR_OK, IdxAlloc(seRunCtx, DISKANN_INDEX, &idxCtx));
    auto indexOpenCfg = GenIndexOpenCfg(seRunCtx);
    idxCtx->idxOpenCfg = indexOpenCfg;
    idxCtx->idxMetaCfg = indexCfg;
    idxCtx->idxShmAddr = idxShmAddr;
    EXPECT_EQ(GMERR_OK, DiskAnnIndexOpen(idxCtx));
    return idxCtx;
}

void ReleaseAndDropCtx(SeRunCtxHdT seRunCtx, IndexCtxT *idxCtx)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    DiskAnnIndexClose(idxCtx);
    FreeGlobalMetaCfg(idxCtx->idxMetaCfg);
    IdxRelease(idxCtx);
    DiskAnnIndexDrop(seRunCtx, idxCtx->idxShmAddr);
    ReleaseIdxOpenCfg(idxCtx);
    RedoLogEnd(redoCtx, false);
}
