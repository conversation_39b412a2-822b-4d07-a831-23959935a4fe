/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <vector>
#include "gtest/gtest.h"
#include "storage_common.h"
#include "gmock/gmock.h"
#include "adpt_sleep.h"
#include "se_index.h"
#include "stub.h"

using namespace std;
using namespace testing::ext;
extern "C" {
Status SeCheckAndSetBackUpMark(uint32_t dbInstanceId);
Status SeBackUpMgrRecovery(void *seIns);
}

class UtStorageBackup : public StorageCommon, public testing::Test {
public:
protected:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}
    void SetUp() override
    {
        ConstructSeInsAndSeRun(nullptr);
    }
    void TearDown() override
    {
        DestroySeIns();
    }
};

/**
 * @tc.name: SeCheckAndSetBackUpMark
 * @tc.desc: test SeCheckAndSetBackUpMark when DbWrappedShmPtrToAddr wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStorageBackup, SeCheckAndSetBackUpMark, TestSize.Level0)
{
    ShmemPtrT oldShm = seIns->backUpMgrShm;
    seIns->backUpMgrShm = {0, 0};
    Status ret = SeCheckAndSetBackUpMark(seIns->instanceId);
    EXPECT_EQ(ret, GMERR_MEMORY_OPERATE_FAILED);
    seIns->backUpMgrShm = oldShm;
}

/**
 * @tc.name: SeBackUpMgrRecovery_001
 * @tc.desc: test SeBackUpMgrRecovery when seIns is null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStorageBackup, SeBackUpMgrRecovery_001, TestSize.Level0)
{
    Status ret = SeBackUpMgrRecovery(nullptr);
    EXPECT_EQ(ret, GMERR_FATAL);
}

/**
 * @tc.name: SeBackUpMgrRecovery_002
 * @tc.desc: test SeBackUpMgrRecovery when seInsPtr->backUpMgrShm convert to null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStorageBackup, SeBackUpMgrRecovery_002, TestSize.Level0)
{
    ShmemPtrT oldShm = seIns->backUpMgrShm;
    seIns->backUpMgrShm = {0, 0};
    Status ret = SeBackUpMgrRecovery(seIns);
    EXPECT_EQ(ret, GMERR_UNEXPECTED_NULL_VALUE);
    seIns->backUpMgrShm = oldShm;
}
