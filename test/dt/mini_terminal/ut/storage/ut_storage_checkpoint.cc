/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: ut test for checkpoint
 * Author:wangyixuan
 * Create: 2024-2-4
 */
#include <vector>
#include "gtest/gtest.h"
#include "storage_common.h"
#include "gmock/gmock.h"
#include "adpt_sleep.h"
#include "se_ckpt_inner.h"
#include "se_space.h"
#include "se_redo_inner.h"
#include "se_page_mgr.h"
#include "se_redo.h"
#include "se_datafile.h"
#include "se_database.h"
#include "stub.h"

using namespace std;
using namespace testing::ext;
extern "C" {
StatusInter CkptSaveCtrl(SeInstanceT *seInsPtr);
}
const uint32_t SPACE_INDEX = 0;
const uint32_t CKPT_TRM_ID = 3;
typedef struct TagPageCkptTest {
    uint8_t checkValid8;
    uint16_t checkValid16;
    uint32_t checkValid32;
    uint64_t checkValid64;
} PageCkptTestT;

static void ModifyPage(uint8_t *page)
{
    PageCkptTestT *ckptPage = reinterpret_cast<PageCkptTestT *>(page + sizeof(PageHeadT));
    ckptPage->checkValid8 = 8;
    ckptPage->checkValid16 = 16;
    ckptPage->checkValid32 = 32;
    ckptPage->checkValid64 = 64;
}

static void ResetPage(uint8_t *page)
{
    PageCkptTestT *ckptPage = reinterpret_cast<PageCkptTestT *>(page + sizeof(PageHeadT));
    ckptPage->checkValid8 = 0;
    ckptPage->checkValid16 = 0;
    ckptPage->checkValid32 = 0;
    ckptPage->checkValid64 = 0;
}

static void AddDirtyPagesToCkpt(
    SeInstanceT *seInsPtr, int pageNum, vector<PageIdT> &addrVector, void (*func)(uint8_t *) = nullptr)
{
    uint8_t *page;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    PageMgrT *pageMgr = reinterpret_cast<PageMgrT *>(seInsPtr->pageMgr);
    PageIdT pageAddr;
    for (int index = 0; index < pageNum; index++) {
        RedoLogBegin(redoCtx);
        SeAllocPage(pageMgr, SPACE_INDEX, CKPT_TRM_ID, &pageAddr);
        addrVector.push_back(pageAddr);
        SeGetPage(pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, true);
        if (func != nullptr) {
            func(page);
        }
        SeLeavePage(pageMgr, pageAddr, true);
        RedoLogEnd(redoCtx, true);
    }
}

static void FreeUsedPage(
    SeInstanceT *seInsPtr, const vector<PageIdT> &addrVector, uint32_t pageNum, void (*func)(uint8_t *) = nullptr)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    PageMgrT *pageMgr = reinterpret_cast<PageMgrT *>(seInsPtr->pageMgr);
    uint8_t *page;
    for (size_t index = 0; index < pageNum; index++) {
        RedoLogBegin(redoCtx);
        SeGetPage(pageMgr, addrVector[index], &page, ENTER_PAGE_NORMAL, true);
        if (func != nullptr) {
            func(page);
        }
        SeLeavePage(pageMgr, addrVector[index], true);
        SeFreePage(pageMgr, SPACE_INDEX, addrVector[index]);
        RedoLogEnd(redoCtx, true);
    }
}

class UtStorageCheckpoint : public StorageCommon, public testing::Test {
public:
protected:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}
    void SetUp() override
    {
        ConstructSeInsAndSeRun(nullptr);
        pageSize = seIns->seConfig.pageSize * DB_KIBI;
    }
    void TearDown() override
    {
        DestroySeIns();
    }
    uint32_t pageSize;
};

StatusInter SpcWriteDatafileMock(DatafileT *df, int64_t offset, int32_t handle, BufT buf)
{
    buf.size = sizeof(PageHeadT) + sizeof(uint8_t) + sizeof(uint16_t);
    if (df->ctrl->used) {
        EXPECT_EQ(STATUS_OK_INTER, SeFileWrite(handle, offset, buf));
    }

    return INT_ERR_DATAFILE_ALREADY_CLOSE;
}

// 刷入5个页失败，双写成功，数据库起来后恢复数据成功
HWTEST_F(UtStorageCheckpoint, ckptPartWrite_001, TestSize.Level0)
{
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    // 添加5个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 5;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    int index = SetStubC(reinterpret_cast<void *>(SpcWriteDatafile), reinterpret_cast<void *>(SpcWriteDatafileMock));
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    DestroySeIns();
    ClearStub(index);
    ConstructSeInsAndSeRun(nullptr, true);
    RedoSetEnable(seIns->redoMgr, true);
    PageMgrT *pageMgr = reinterpret_cast<PageMgrT *>(seIns->pageMgr);
    for (size_t i = 0; i < addrVector.size(); i++) {
        uint8_t *page;
        SeGetPage(pageMgr, addrVector[i], &page, ENTER_PAGE_NORMAL, false);
        PageCkptTestT *ckptPage = reinterpret_cast<PageCkptTestT *>(page + sizeof(PageHeadT));
        uint8_t first = ckptPage->checkValid8;
        EXPECT_EQ((uint16_t)first * 2, ckptPage->checkValid16);
        EXPECT_EQ((uint32_t)first * 4, ckptPage->checkValid32);
        EXPECT_EQ((uint64_t)first * 8, ckptPage->checkValid64);
        SeLeavePage(pageMgr, addrVector[i], false);
    }
}

// 刷入5个页（其中两个页是被释放页）失败，双写成功，数据库起来后恢复数据成功
HWTEST_F(UtStorageCheckpoint, ckptPartWrite_002, TestSize.Level0)
{
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    // 添加5个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 5;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 释放前两个页
    FreeUsedPage(seIns, addrVector, 2, ResetPage);
    int index = SetStubC(reinterpret_cast<void *>(SpcWriteDatafile), reinterpret_cast<void *>(SpcWriteDatafileMock));
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    DestroySeIns();
    ClearStub(index);
    ConstructSeInsAndSeRun(nullptr, true);
    RedoSetEnable(seIns->redoMgr, true);
    PageMgrT *pageMgr = reinterpret_cast<PageMgrT *>(seIns->pageMgr);
    // 从第三个页开始检查
    for (size_t i = 2; i < addrVector.size(); i++) {
        uint8_t *page;
        SeGetPage(pageMgr, addrVector[i], &page, ENTER_PAGE_NORMAL, false);
        PageCkptTestT *ckptPage = reinterpret_cast<PageCkptTestT *>(page + sizeof(PageHeadT));
        uint8_t first = ckptPage->checkValid8;
        EXPECT_EQ((uint16_t)first * 2, ckptPage->checkValid16);
        EXPECT_EQ((uint32_t)first * 4, ckptPage->checkValid32);
        EXPECT_EQ((uint64_t)first * 8, ckptPage->checkValid64);
        SeLeavePage(pageMgr, addrVector[i], false);
    }
}

/**
 * @tc.name: RecordRedoLog
 * @tc.desc: 5个脏页，刷入4个页，双写成功，数据库起来后恢复数据成功
 * @tc.type: FUNC
 * @tc.require: DTS2024041705073
 * @tc.author: zengchuanrui
 */
HWTEST_F(UtStorageCheckpoint, ckptPartWrite_003, TestSize.Level0)
{
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    // 添加5个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 5;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    int index = SetStubC(reinterpret_cast<void *>(SpcWriteDatafile), reinterpret_cast<void *>(SpcWriteDatafileMock));

    // 第一个页面不做ckpt
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    BufDescT *desc =
        reinterpret_cast<BufDescT *>(DbWrappedShmPtrToAddr(&seIns->memUtils, ckptCtx->pubCtx->queue.first));
    DbSpinLock(&desc->lock);

    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    DestroySeIns();
    ClearStub(index);
    ConstructSeInsAndSeRun(nullptr, true);
    RedoSetEnable(seIns->redoMgr, true);
    PageMgrT *pageMgr = reinterpret_cast<PageMgrT *>(seIns->pageMgr);
    for (size_t i = 0; i < addrVector.size(); i++) {
        uint8_t *page;
        SeGetPage(pageMgr, addrVector[i], &page, ENTER_PAGE_NORMAL, false);
        PageCkptTestT *ckptPage = reinterpret_cast<PageCkptTestT *>(page + sizeof(PageHeadT));
        uint8_t first = ckptPage->checkValid8;
        EXPECT_EQ((uint16_t)first * 2, ckptPage->checkValid16);
        EXPECT_EQ((uint32_t)first * 4, ckptPage->checkValid32);
        EXPECT_EQ((uint64_t)first * 8, ckptPage->checkValid64);
        SeLeavePage(pageMgr, addrVector[i], false);
    }
}

/**
 * @tc.desc: 刷入5个页失败，双写成功。此时配置项关闭双写功能，数据库起来后恢复数据成功。
 * @tc.type: FUNC
 * @tc.author: lishiya
 */
HWTEST_F(UtStorageCheckpoint, ckptPartWrite_004, TestSize.Level0)
{
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    // 添加5个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 5;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    int index = SetStubC(reinterpret_cast<void *>(SpcWriteDatafile), reinterpret_cast<void *>(SpcWriteDatafileMock));
    (void)CkptTriggerImpl(seIns, CKPT_MODE_INC, true, WAIT_SECONDS_FOR_CHECKPOINT);
    DestroySeIns();
    ClearStub(index);

    const char *config = R"({
        "deviceSize": 4096,
        "pageSize": 4,
        "latchDeadlockDebugTimeout": 10000000,
        "maxConnNum": 100,
        "redoPubBufSize": 4096,
        "redoFlushByTrx": 1,
        "redoFileSize": 100,
        "isEncrypted": 1,
        "hexPassword": "absc12345",
        "dataFilePath": "./data/gmdb/dbDataFile",
        "dwrEnable": 0
    })";
    ConstructSeInsAndSeRun(config, true);
    RedoSetEnable(seIns->redoMgr, true);
    PageMgrT *pageMgr = reinterpret_cast<PageMgrT *>(seIns->pageMgr);
    for (size_t i = 0; i < addrVector.size(); i++) {
        uint8_t *page;
        SeGetPage(pageMgr, addrVector[i], &page, ENTER_PAGE_NORMAL, false);
        PageCkptTestT *ckptPage = reinterpret_cast<PageCkptTestT *>(page + sizeof(PageHeadT));
        uint8_t first = ckptPage->checkValid8;
        EXPECT_EQ((uint16_t)first * 2, ckptPage->checkValid16);
        EXPECT_EQ((uint32_t)first * 4, ckptPage->checkValid32);
        EXPECT_EQ((uint64_t)first * 8, ckptPage->checkValid64);
        SeLeavePage(pageMgr, addrVector[i], false);
    }
}
