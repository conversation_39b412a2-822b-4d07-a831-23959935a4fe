/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <vector>
#include "gtest/gtest.h"
#include "storage_common.h"
#include "se_page_mgr.h"
#include "gmock/gmock.h"
#include "adpt_sleep.h"
#include "stub.h"

using namespace std;
using namespace testing::ext;
extern "C" {
void *DbDynLoadGetFunc(const char *feature, const char *subsystem);
}

class UtStoragePageMgr : public StorageCommon, public testing::Test {
public:
protected:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}
    void SetUp() override
    {
        ConstructSeInsAndSeRun(nullptr);
    }
    void TearDown() override
    {
        DestroySeIns();
    }
};

/**
 * @tc.name: UtStoragePageMgr
 * @tc.desc: test SePageMgrInit when DbDynLoadGetFunc return null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStoragePageMgr, SePageMgrInit_001, TestSize.Level0)
{
    gStubMap["DbDynMemCtxAlloc"][0] =
        SetStubC(reinterpret_cast<void *>(DbDynLoadGetFunc), reinterpret_cast<void *>(DbDynLoadGetFuncMock));
    gStubMap["DbDynMemCtxAlloc"][1] = 1;
    StatusInter ret = SePageMgrInit(seIns, reinterpret_cast<DbMemCtxT *>(seIns->seServerMemCtx));
    EXPECT_EQ(ret, UNEXPECTED_NULL_VALUE_INTER);
}

/**
 * @tc.name: UtStoragePageMgr
 * @tc.desc: test SePageMgrInit when BufpoolInit wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
StatusInter BufpoolInitMock(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    return INTERNAL_ERROR_INTER;
}
HWTEST_F(UtStoragePageMgr, SePageMgrInit_002, TestSize.Level0)
{
    SetStubC(reinterpret_cast<void *>(BufpoolInit), reinterpret_cast<void *>(BufpoolInitMock));
    StatusInter ret = SePageMgrInit(seIns, reinterpret_cast<DbMemCtxT *>(seIns->seServerMemCtx));
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
}

/**
 * @tc.name: SeCreateSrvPageMgrCtx_001
 * @tc.desc: test SeCreateSrvPageMgrCtx when DbDynLoadGetFunc return null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStoragePageMgr, SeCreateSrvPageMgrCtx_001, TestSize.Level0)
{
    gStubMap["DbDynMemCtxAlloc"][0] =
        SetStubC(reinterpret_cast<void *>(DbDynLoadGetFunc), reinterpret_cast<void *>(DbDynLoadGetFuncMock));
    gStubMap["DbDynMemCtxAlloc"][1] = 1;
    StatusInter ret = SeCreateSrvPageMgrCtx(seIns, reinterpret_cast<DbMemCtxT *>(seIns->seServerMemCtx));
    EXPECT_EQ(ret, UNEXPECTED_NULL_VALUE_INTER);
}
/**
 * @tc.name: SeCreateSrvPageMgrCtx_002
 * @tc.desc: test SeCreateSrvPageMgrCtx when BufpoolCreatePageMgr wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
StatusInter BufpoolCreatePageMgrMock(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr)
{
    return INTERNAL_ERROR_INTER;
}

HWTEST_F(UtStoragePageMgr, SeCreateSrvPageMgrCtx_002, TestSize.Level0)
{
    SetStubC(reinterpret_cast<void *>(BufpoolCreatePageMgr), reinterpret_cast<void *>(BufpoolCreatePageMgrMock));
    StatusInter ret = SeCreateSrvPageMgrCtx(seIns, reinterpret_cast<DbMemCtxT *>(seIns->seServerMemCtx));
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
}
/**
 * @tc.name: SeReleasePageMgr
 * @tc.desc: test SeReleasePageMgr when DbDynLoadGetFunc return null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStoragePageMgr, SeReleasePageMgr, TestSize.Level2)
{
    gStubMap["DbDynMemCtxAlloc"][0] =
        SetStubC(reinterpret_cast<void *>(DbDynLoadGetFunc), reinterpret_cast<void *>(DbDynLoadGetFuncMock));
    gStubMap["DbDynMemCtxAlloc"][1] = 1;
    SeReleasePageMgr(seIns);
}

/**
 * @tc.name: SeFreePage
 * @tc.desc: test SeFreePage when PageMgr is null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStoragePageMgr, SeFreePage, TestSize.Level0)
{
    StatusInter ret = SeFreePage(nullptr, 0, (PageIdT){0, 0});
    EXPECT_EQ(ret, UNEXPECTED_NULL_VALUE_INTER);
}

/**
 * @tc.name: SeFreeFsmPage
 * @tc.desc: test SeFreeFsmPage when PageMgr is null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStoragePageMgr, SeFreeFsmPage, TestSize.Level0)
{
    StatusInter ret = SeFreeFsmPage(nullptr, 0, (PageIdT){0, 0});
    EXPECT_EQ(ret, UNEXPECTED_NULL_VALUE_INTER);
}

/**
 * @tc.name: SeGetPageWithArg
 * @tc.desc: test SeGetPageWithArg when pageMgr->getPageWithArgFunc is null
 * @tc.type: FUNC
 * @tc.require: DTS2024080204182
 * @tc.author: xujun
 */
HWTEST_F(UtStoragePageMgr, SeGetPageWithArg, TestSize.Level0)
{
    PageIdT id = {0, 0};
    uint8_t *page = nullptr;
    StatusInter ret =
        SeGetPageWithArg(reinterpret_cast<PageMgrT *>(seIns->pageMgr), id, ENTER_PAGE_NORMAL, nullptr, &page);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    SeLeavePage(reinterpret_cast<PageMgrT *>(seIns->pageMgr), id, false);
}
