/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * File Name: storage_hac_hash_index_ut.cc
 * Description: Implementation of hac ut
 * Author: lijianchuan
 * Create: 2022/9/1
 */
#include "common_init.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "se_hac_common.h"
#include "db_dynmem_algo.h"
#include "db_top_shmem_ctx.h"

class UtDaf : public testing::Test {};

void DafHacPrepare(DbMemCtxT **topShmMemCtx, DbMemCtxT **dynMemCtx)
{
    *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(topShmMemCtx);

    DbMemCtxArgsT hashArgs = {0};
    hashArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    hashArgs.memType = DB_DYNAMIC_MEMORY;
    hashArgs.init = DynamicAlgoInit;

    *dynMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "se dynamic", &hashArgs);
    DB_POINTER(dynMemCtx);

    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = SE_DEFAULT_PAGE_SIZE;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
    config.maxTrxNum = MAX_TRX_NUM;
    config.bufferPoolSize = 4096;

    SeInstanceHdT sePtr = nullptr;
    int32_t ret = SeCreateInstance(NULL, (DbMemCtxT *)topShmMemCtx, &config, &sePtr);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(UtDaf, test_trx_begin_commit)
{
    int32_t ret = CommonInit();
    EXPECT_EQ(GMERR_OK, ret);
    DbMemCtxT *topShmMemCtxForDaf = NULL;
    DbMemCtxT *topDynMemCtxForDaf = NULL;
    DafHacPrepare(&topShmMemCtxForDaf, &topDynMemCtxForDaf);
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)topDynMemCtxForDaf, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = {0};
    trxCfg.connId = 8888;
    trxCfg.trxType = PESSIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(0, ret);
    EXPECT_TRUE(((TrxT *)(seRunCtx->trx))->trx.base.dafCommitActionCollection != NULL);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
    clearAllStub();
    DbDestroyTopShmemCtx(GET_INSTANCE_ID);
    DbClearTopShmCtx(GET_INSTANCE_ID);
    CommonRelease();
}
TEST_F(UtDaf, test_trx_begin_commit_memory_not_enough)
{
    int32_t ret = CommonInit();
    EXPECT_EQ(GMERR_OK, ret);
    DbMemCtxT *topShmMemCtxForDaf = NULL;
    DbMemCtxT *topDynMemCtxForDaf = NULL;
    DafHacPrepare(&topShmMemCtxForDaf, &topDynMemCtxForDaf);
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)topDynMemCtxForDaf, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = {0};
    trxCfg.connId = 8888;
    trxCfg.trxType = PESSIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    int stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocWithErr);
    ASSERT_TRUE(stubIdx >= 0);
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    EXPECT_TRUE(((TrxT *)(seRunCtx->trx))->trx.base.dafCommitActionCollection == NULL);
    clearStub(stubIdx);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
    clearAllStub();
    DbDestroyTopShmemCtx(GET_INSTANCE_ID);
    DbClearTopShmCtx(GET_INSTANCE_ID);
    CommonRelease();
}
