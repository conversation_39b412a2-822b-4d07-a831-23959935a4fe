#include <stdlib.h>
#include <sys/types.h>
#include <unistd.h>
#include <vector>
#include <thread>
#include <stdio.h>
#include "gtest/gtest.h"
#include "se_table_space_pub.h"
#include "db_table_space.h"
#include "se_trx.h"
#include "stub.h"
#include "securec.h"
#include "adpt_types.h"
#include "gmc_errno.h"
#include "adpt_spinlock.h"
#include "se_lfsmgr.h"
#include "db_mem_context.h"
#include "adpt_mem_segment_euler.h"
#include "db_dynmem_algo.h"
#include "se_heap.h"
#include "se_heap_hc.h"
#include "se_heap_access_inner.h"
#include "se_heap_inner.h"
#include "se_heap_utils.h"
#include "se_heap_hc_inner.h"
#include "adpt_thread.h"
#include "common_init.h"
#include "dm_data_basic.h"
#include "dm_meta_prop_label.h"
#include "dm_data_prop.h"
#include "se_index.h"
#include "se_page_mgr.h"
#include "se_hash_index.h"
#include "db_internal_error.h"
#include "se_define.h"
#include "storage_session.h"
#include "se_heap_batch.h"
#include "se_heap_fetch.h"
#include "storage_ut_common.h"
#include "db_label_latch_mgr.h"
#include "se_trx.h"
#include "se_page_latch_sorter.h"
#include "se_undo.h"
#include "se_trx_inner.h"
#include "se_undo_trx_resource.h"
#include "db_server_dfgmt_task.h"
#include "dm_meta_vertex_label.h"
#include "ee_key_cmp.h"
#include "db_internal_error.h"
#include "se_heap_common_am.h"
#include "se_heap_trx_acc.h"
#include "se_heap_inner.h"
#include "se_heap_stats.h"
#include "se_heap_batch_common.h"
#include "se_heap_slice_row.h"
#include "se_heap_utils_common.h"
#include "se_heap_scan_common.h"
#include "se_heap_am_inner.h"
using namespace std;

#define UT_DEBUG
#ifdef UT_DEBUG
#define UT_PRINTF(format, ...) printf(format, ##__VA_ARGS__)
#else
#define UT_PRINTF(format, ...)
#endif

#define PAGE_SIZE_TYPE 4
#define SE_BLK_PER_DEV 128

#ifdef __cplusplus
extern "C" {
#endif
extern int32_t HeapUpdateNormalRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);
extern StatusInter UndoVertexHandleIndex(
    TrxT *trx, const UndoRowOpInfoT *rowOpInfo, HeapRunCtxT *heapCtx, HeapTupleBufT *heapTupleBuf);
extern Status HeapAmVertexExtraKeyBySerialBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl,
    HeapConstBufT *serialBuf, uint32_t indexId, HeapTupleBufT *buf);
extern Status DmGetKeyBufFromVertex(const DmVertexT *vertex, uint32_t indexId, uint8_t **keyBuf, uint32_t *length);
extern HeapCommonAm g_gmdbHeapCommonAm;
#ifdef __cplusplus
}
#endif
static const uint32_t g_cpuFrequency = 2600000000;  // 通过命令 cat /proc/cpuinfo | grep 'cpu MHz' 获得
using std::chrono::high_resolution_clock;
using std::chrono::microseconds;
using std::chrono::milliseconds;
class PerfRecord {
public:
    high_resolution_clock::time_point timeBegin;
    high_resolution_clock::time_point timeEnd;
    // struct timeval timeBegin = {0};
    // struct timeval timeEnd = {0};
    uint64_t allTime = 0;  // 单位是微秒
    void Begin()
    {
        // gettimeofday(&timeBegin, nullptr);
        timeBegin = high_resolution_clock::now();
    }

    void End()
    {
        // gettimeofday(&timeEnd, nullptr);
        timeEnd = high_resolution_clock::now();
        allTime = CalcCost();
    }

    void CalcOpsAndCycles(
        const char *outputStr, float operationCnt, float *ops = nullptr, uint32_t *cycles = nullptr) const
    {
        float tmpOps = operationCnt / ((float)allTime / 1000000);
        auto tmpCycles = (uint32_t)(g_cpuFrequency / tmpOps);
        if (outputStr) {
            printf("%s opCnt %.1f, allCost %" PRIu64 " ms, %.2f Kops, per op cost %u cycles \n", outputStr,
                operationCnt, allTime / 1000, tmpOps / 1000, tmpCycles);
        }
        if (ops) {
            *ops = tmpOps;
        }
        if (cycles) {
            *cycles = tmpCycles;
        }
    }

private:
    uint64_t CalcCost() const  // 单位是微妙
    {
        // uint64_t costInUs = (timeEnd.tv_sec - timeBegin.tv_sec) * 1000000 + (timeEnd.tv_usec - timeBegin.tv_usec);
        uint64_t costInUs = std::chrono::duration_cast<microseconds>(timeEnd - timeBegin).count();
        return costInUs;
    }
};

SeRunCtxHdT g_seRunCtx;
SeRunCtxHdT g_seRunCtx2;
SeRunCtxHdT g_seRunCtx3;
SeRunCtxHdT g_seRunCtx4;
ShmemPtrT g_VarHeapShmAddr;
ShmemPtrT g_VarHeapShmAddrOptiTrx;
ShmemPtrT g_FixHeapShmAddr;
VertexLabelCommonInfoT stubCommonInfo = {0};
DmVertexLabelT *stubVtxLabelOptiTrx = NULL;
DmVertexLabelT *stubVtxLabel = NULL;
static const uint32_t FIX_ROW_UINT32_T_NUM = 20;
static const uint32_t FIX_ROW_LEN = sizeof(uint32_t) * FIX_ROW_UINT32_T_NUM;

typedef enum EnumFixDemoPos {  // 自定义的一个定长tuple的格式
    FIX_DEMO_POS_HEAD = 0,
    FIX_DEMO_POS_PK,
    FIX_DEMO_POS_TAIL = FIX_ROW_UINT32_T_NUM - 1,
} FixDemoPosE;

vector<HpTupleAddr> g_HeapItemList;
DbSpinLockT g_HeapItemListLock;
typedef struct UtHeapItem {
    HpTupleAddr addr;
    uint32_t pk;
} UtHeapItemT;
vector<UtHeapItemT> g_HeapItemWithPkList;
static const uint32_t NUM = 1000;
static const uint32_t MAX_ITEM_NUM = 1000000;
static const int MAX_RANDOM_LEN = HEAP_CALC_ALIGN_SIZE(sizeof(uint32_t) * 1024);
// 需要>=sizeof(HpLinkRowHeadT) - sizeof(HpNormalRowHeadT)
static const int MIN_RANDOM_LEN = HEAP_CALC_ALIGN_SIZE(sizeof(uint32_t) * 4);
static const uint32_t MAGIC_HEAD = 0xcafefade;
static const uint32_t MAGIC_TAIL = 0xfacedeaf;
static const int INSERT_THREAD_NUM = 5;
static const int UPDATE_THREAD_NUM = 10;
static const int DELETE_THREAD_NUM = 2;
static const int FETCH_THREAD_NUM = 2;
static const int SCAN_THREAD_NUM = 3;
static uint32_t deleteThreadCnt = 0;

inline uint32_t UtHeapGeneratePk()
{
    static uint32_t pkSeq = 0;
    static DbSpinLockT lock = DB_SPINLOCK_INIT_VAL;
    DbSpinLock(&lock);
    uint32_t newPk = pkSeq++;
    DbSpinUnlock(&lock);
    return newPk;
}

typedef struct tagUtShmCommT {
    volatile uint32_t writeCnt;
    volatile bool isCommitted;
    volatile uint32_t readCnt;
    volatile uint32_t readOkCnt;
    HpTupleAddr addr[NUM];
} UtShmCommT;

void UtHeapAmBasicPrepareSeInstance(PageTotalSizeT pageSize)
{
    g_topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    if (g_topShmMemCtx == nullptr) {
        ASSERT_EQ(0, 1);
    }

    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = pageSize;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
    config.deadlockCheckPeriod = 4000;
    config.lockJumpQueuePeriod = 5000;
    config.lockWakeupPeriod = 1000;
    config.lockTimeOut = 10000;
    config.maxTrxNum = MAX_TRX_NUM;
    config.maxLockShareCnt = MAX_CONN_NUM / 2;

    SeInstanceHdT sePtr = nullptr;
    int32_t ret = SeCreateInstance(NULL, (DbMemCtxT *)g_topShmMemCtx, &config, &sePtr);
    ASSERT_EQ(ret, GMERR_OK);

    DbMemCtxArgsT topDyncCtxCfg = {0};
    topDyncCtxCfg.ctxSize = sizeof(DbDynamicMemCtxT);
    topDyncCtxCfg.memType = DB_DYNAMIC_MEMORY;
    topDyncCtxCfg.init = DynamicAlgoInit;
    g_seTopDynCtx = (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "se dynamic", &topDyncCtxCfg);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    ASSERT_EQ(ret, 0);
    ShmemPtrT shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabel, &shmPtr, sizeof(DmVertexLabelT));
    memset_s(stubVtxLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    stubVtxLabel->metaCommon.metaShmPtr = shmPtr;
    size_t size = sizeof(MetaVertexLabelT) + sizeof(DmVlIndexLabelT) + sizeof(DmSchemaT) + sizeof(DmPropertySchemaT);
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabel->metaVertexLabel, &shmPtr, size);
    memset_s(stubVtxLabel->metaVertexLabel, size, 0, size);
    stubVtxLabel->metaCommon.metaShmPtr = shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabel->commonInfo, &stubVtxLabel->commonInfoShmPtr,
        sizeof(VertexLabelCommonInfoT));
    srand(time(nullptr));
}

void UtHeapAMBasicCreateVarLabel(int slotExtend = 0)
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = static_cast<PageSizeT>(slotExtend),
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_VarHeapShmAddr = heapShmAddr;
    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
}

void UtHeapAMBasicCreateLabelOptiTrx()
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_VarHeapShmAddrOptiTrx = heapShmAddr;
    ShmemPtrT shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabelOptiTrx, &shmPtr, sizeof(DmVertexLabelT));
    memset_s(stubVtxLabelOptiTrx, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabelOptiTrx->commonInfo,
        &stubVtxLabelOptiTrx->commonInfoShmPtr, sizeof(VertexLabelCommonInfoT));
    stubVtxLabelOptiTrx->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
}

void UtHeapAMBasicCreateFixLabel(int fixRowLen, int slotExtend)
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = static_cast<PageSizeT>(fixRowLen),
        .slotExtendSize = static_cast<PageSizeT>(slotExtend),
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_FixHeapShmAddr = heapShmAddr;

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
}

void UtHeapAMBasicDrop()
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_VarHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    int32_t ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_VarHeapShmAddr = DB_INVALID_SHMPTR;
}

void UtHeapAMBasicDropOptiTrx()
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    int32_t ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_VarHeapShmAddrOptiTrx = DB_INVALID_SHMPTR;
}

void UtHeapAutoCommitAndBegin(SeRunCtxHdT seRunCtx, bool isRollBack = false)
{
    int32_t ret;
    if (isRollBack) {
        ret = SeTransRollback(seRunCtx, false);
        ASSERT_EQ(ret, 0);
    } else {
        ret = SeTransCommit(seRunCtx);
        ASSERT_EQ(ret, 0);
    }

    ret = SeTransBegin(seRunCtx, nullptr);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(seRunCtx);
    ASSERT_EQ(ret, 0);
}

static uint64_t g_namespaceTrxIdArray[100] = {0};

static Status UtGetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance)
{
    if (trxId == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *trxId = g_namespaceTrxIdArray[labelId];
    *trxIdLastModify = g_namespaceTrxIdArray[labelId];
    return GMERR_OK;
}

static Status UtSetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance)
{
    g_namespaceTrxIdArray[labelId] = trxId;
    return GMERR_OK;
}

static Status UtGetLabelNameByEdgeLabelId(uint32_t elId, char *labelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelName);
    return GMERR_DATA_EXCEPTION;
}

class UtStorageHeapAM : public testing::Test {
protected:
    void SetUp()
    {
        init();
        int stub1 = setStubC((void *)HeapCreate, (void *)HeapCreateMock);
        EXPECT_GE(stub1, 0);
        int stub2 = setStubC((void *)MdCreateCachedPageList, (void *)MdCreateCachedPageListMock);
        EXPECT_GE(stub2, 0);
        HeapCntrAcsInfoT info = {
            .heapShmAddr = g_VarHeapShmAddr,
            .isPersistent = false,
            .isUseRsm = false,
            .instanceId = GET_INSTANCE_ID,
        };
        int32_t ret = HeapLabelTruncate(NULL, &info, NULL);
        ASSERT_EQ(0, ret);
        UtHeapAutoCommitAndBegin(g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int32_t ret = TrxCommit((TrxT *)(g_seRunCtx)->trx);
        EXPECT_EQ(GMERR_OK, ret);
        clearAllStub();
    }

    virtual int AllocObjects()
    {
        return 0;
    }

    virtual void FreeObjects()
    {}

    static void SetUpTestCase()
    {
        init();
        int stub1 = setStubC((void *)HeapCreate, (void *)HeapCreateMock);
        EXPECT_GE(stub1, 0);
        int32_t ret = CommonInit();
        if (ret != STATUS_OK_INTER) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        g_VarHeapShmAddr = DB_INVALID_SHMPTR;
        g_FixHeapShmAddr = DB_INVALID_SHMPTR;
        UtHeapAmBasicPrepareSeInstance(SE_DEFAULT_PAGE_SIZE);
        UtHeapAMBasicCreateVarLabel();
        UtHeapAutoCommitAndBegin(g_seRunCtx);
        OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelLastTrxIdAndTrxCommitTimeById, UtGetLabelLastTrxIdAndTrxCommitTimeById,
            UtGetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[TRX_CHECK_READVIEW_NUM] = {
            UtSetLabelLastTrxIdAndTrxCommitTimeById, UtSetLabelLastTrxIdAndTrxCommitTimeById,
            UtSetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId};
        SeInitTrxMgrCheckFunc(GET_INSTANCE_ID, setFunc, getFunc, getLabelName);
    };

    static void TearDownTestCase()
    {
        UtHeapAMBasicDrop();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
        clearAllStub();
    };
};

void HeapHandleClose(HpRunHdlT heapHdl)
{
    HeapLabelResetCtx(heapHdl);
    HeapLabelReleaseRunctx(heapHdl);
}

StatusInter HeapFetchHpTupleProc(HpReadRowInfoT *readRowInfo, void *userData)
{
    char *cBuf = (char *)readRowInfo->buf;
    HpItemPointerT *addr = (HpItemPointerT *)userData;
    EXPECT_EQ('[', cBuf[0]);
    EXPECT_EQ(']', cBuf[readRowInfo->bufSize - 2]);
    if ('[' == cBuf[0] && ']' == cBuf[readRowInfo->bufSize - 2]) {
        return STATUS_OK_INTER;
    }
    (void)addr;
    UT_PRINTF("Addr(%u|%u):len(%u):%s\n", addr->pageId, addr->slotId, readRowInfo->bufSize, cBuf);
    return INTERNAL_ERROR_INTER;
}

StatusInter HeapFetchHpTupleWithStrcmp(HpReadRowInfoT *readRowInfo, void *userData)
{
    char *buf = (char *)readRowInfo->buf;
    char *str = (char *)userData;
    if (strcmp(buf, str) == 0) {
        return STATUS_OK_INTER;
    }
    return INTERNAL_ERROR_INTER;
}

StatusInter HeapScanRowMatchProc(const HeapTupleBufAndRowInfoT *heapBuffAndRowInfo, uint32_t rowIndex, void *userData,
    HpScanSubsActT *subsequentAction)
{
    int *matchCount = (int *)userData;
    // 扫描到一半中止
    if (++(*matchCount) == NUM / 2) {
        subsequentAction->isScanBreak = true;
    }

    return STATUS_OK_INTER;
}

StatusInter HeapScanRowRollbackProc(const HeapTupleBufAndRowInfoT *heapBuffAndRowInfo, uint32_t rowIndex,
    void *userData, HpScanSubsActT *subsequentAction)
{
    int *callCount = (int *)userData;
    // 偶数回滚，奇数成功
    if (((*callCount) & 1) == 0) {
        subsequentAction->isRollBackScan = true;
    } else {
        subsequentAction->isMatched = true;
    }
    subsequentAction->isScanBreak = true;
    ++(*callCount);
    return STATUS_OK_INTER;
}

/*
测试点：savePoint的创建、回滚，且回滚到savePoint校验labelView的情况（此ut用例不校验重复labelId，正常由流程保证不会重复）
1. 创建1个命名为"abc"的savepoint1
2. 标记5张表，记录labelView
3. 再创建一个命名为"abcd"的savePoint2
4. 激活事务提交检查
5. 再标记5张不同的表，记录labelView
6. 再创建一个命名为"abcde"的savePoint3
7. 再标记5张不同的表，记录labelView
8. 回滚到savePoint2 "abcd"，预期labelView还剩5张表，预期事务提交检查被关闭
9. 回滚到savePoint2 "abc"，预期labelView还剩0张表
*/
TEST_F(UtStorageHeapAM, Savepoint_labelView)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    TrxCfgT trxCfg = GetOptimisticTrxCfg();
    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);

    SeTrxSavepointNameT name = {0};
    char *spname = (char *)"abc";
    name.length = strlen(spname) + 1;
    strcpy_s(name.name, name.length, spname);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name);
    ASSERT_EQ(GMERR_OK, ret);

    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);

    uint32_t labelId = 10000;
    uint32_t testLabelNum = 5;
    for (uint32_t i = 0; i < testLabelNum; i++) {
        ret = SeTransSetLabelModified(g_seRunCtx, labelId++, TRX_VERTEXLABEL_HEAP, true);
        ASSERT_EQ(0, ret);
    }

    SeTrxSavepointNameT name2 = {0};
    char *spname2 = (char *)"abcd";
    name2.length = strlen(spname2) + 1;
    strcpy_s(name2.name, name2.length, spname2);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name2);
    ASSERT_EQ(GMERR_OK, ret);

    SeTransSetLabelModifiedActive(g_seRunCtx);

    for (uint32_t i = 0; i < testLabelNum; i++) {
        ret = SeTransSetLabelModified(g_seRunCtx, labelId++, TRX_VERTEXLABEL_HEAP, true);
        ASSERT_EQ(0, ret);
    }

    SeTrxSavepointNameT name3 = {0};
    char *spname3 = (char *)"abcde";
    name3.length = strlen(spname3) + 1;
    strcpy_s(name3.name, name3.length, spname3);

    for (uint32_t i = 0; i < testLabelNum; i++) {
        ret = SeTransSetLabelModified(g_seRunCtx, labelId++, TRX_VERTEXLABEL_HEAP, true);
        ASSERT_EQ(0, ret);
    }

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name2);
    EXPECT_EQ(GMERR_OK, ret);

    TrxT *trx = ((TrxT *)g_seRunCtx->trx);
    EXPECT_EQ((uint32_t)trx->trx.optTrx.labelReadView.curItemCnt, 5u);
    EXPECT_EQ((uint32_t)trx->trx.optTrx.labelReadView.isTrxCommitCheckActive, 0u);

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)trx->trx.optTrx.labelReadView.curItemCnt, 0u);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
}

void SeBeginOptiTrx()
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAMBasicCreateLabelOptiTrx();
    TrxCfgT trxCfg = GetOptimisticTrxCfg();

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);
}

int32_t UtStubDmGetKeyBufFromVertex(DmVertexT *vertex, uint32_t indexId, uint8_t **keyBuf, uint32_t *length)
{
    return GMERR_OK;
}

int32_t UtStubDmDeSerializeVertexWithMemCtx(
    DbMemCtxT *memCtx, uint8_t *buf, uint32_t length, DmVertexLabelT *vertexLabel, DmVertexT **vertex)
{
    return GMERR_OK;
}

Status UtStubAmVertexExtraKeyBySerialBufInner(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl,
    HeapConstBufT *serialBuf, uint32_t indexId, HeapTupleBufT *buf)
{
    return GMERR_OK;
}

Status UtStubHashIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    return GMERR_OK;
}

void UtStubHeapDebugCheckPriKeyIdxEntryUsed(HpRunHdlT heapHandle)
{
    return;
}

int32_t UtStubHtUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    return GMERR_OK;
}

int32_t UtStubHtUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    return GMERR_OK;
}

Status UtStubIdxInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    return GMERR_OK;
}

int32_t UtStubHeapLabelSerialHpTuple(Handle heapRunHdl, HeapTupleT *heapTuple, uint8_t **buf, uint32_t *bufSize)
{
    static char str[100] = "Roll Back data";
    *buf = (uint8_t *)&str;
    *bufSize = strlen(str);
    return GMERR_OK;
}

StatusInter UtStubHeapLabelDeSerialHpTuple(const uint8_t *buf, uint32_t bufSize, void *userData)
{
    return STATUS_OK_INTER;
}

int32_t UtStubHeapLabelFreeHpTuple(Handle heapRunHdl, HeapTupleT *heapTuple)
{
    return 0;
}

void StubSetUndoTransaction(vector<int> &stubIndex)
{
    int32_t stubIdx1 = setStub(HeapLabelDeSerialHpTuple, UtStubHeapLabelDeSerialHpTuple);
    if (stubIdx1 < 0) {
        ASSERT_EQ(0, 1);
    }
    int32_t stubIdx2 = setStub(DmGetKeyBufFromVertex, UtStubDmGetKeyBufFromVertex);
    if (stubIdx2 < 0) {
        ASSERT_EQ(0, 1);
    }
    int32_t stubIdx3 = setStub(DmDeSerializeVertexWithMemCtx, UtStubDmDeSerializeVertexWithMemCtx);
    if (stubIdx3 < 0) {
        ASSERT_EQ(0, 1);
    }
    int32_t stubIdx5 = setStub(IdxUndoRemove, UtStubHtUndoRemove);
    if (stubIdx5 < 0) {
        ASSERT_EQ(0, 1);
    }
    int32_t stubIdx6 = setStub(IdxUndoInsert, UtStubHtUndoInsert);
    if (stubIdx6 < 0) {
        ASSERT_EQ(0, 1);
    }
    int32_t stubIdx7 = setStub(HeapDebugCheckPriKeyIdxEntryUsed, UtStubHeapDebugCheckPriKeyIdxEntryUsed);
    if (stubIdx7 < 0) {
        ASSERT_EQ(0, 1);
    }
    int32_t stubIdx8 = setStub(IdxInsert, UtStubIdxInsert);  // 轻量化事务更新回滚，重新插入旧版本entry
    if (stubIdx8 < 0) {
        ASSERT_EQ(0, 1);
    }
    stubIndex.push_back(stubIdx1);
    stubIndex.push_back(stubIdx2);
    stubIndex.push_back(stubIdx3);
    stubIndex.push_back(stubIdx5);
    stubIndex.push_back(stubIdx6);
    stubIndex.push_back(stubIdx7);
    stubIndex.push_back(stubIdx8);
}

void StubClearUndoTransaction(vector<int> stubIndex)
{
    for (auto index : stubIndex) {
        clearStub(index);
    }
}

/*
测试点：同名savePoint的创建、回滚，且回滚到savePoint后不失效、DFX的校验
1. 创建1个命名为"abc"的savepoint, 插入1w条记录，
2. 再创建一个同名的savePoint，再次插入1w条记录，
3. 回滚到savePoint "abc", 预期回滚1w条记录，还剩1w条
4. 再次回滚到savePoint "abc"，因为回滚后savepoint不失效，本次回滚0条，预期还剩1w条
5. 事务提交，校验DFX
*/
TEST_F(UtStorageHeapAM, Savepoint1)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    SeBeginOptiTrx();
    SeTrxSavepointNameT name = {0};
    char *spname = (char *)"abc";
    name.length = strlen(spname) + 1;
    strcpy_s(name.name, name.length, spname);

    int32_t ret = SeTrxCreateSavepoint(g_seRunCtx, &name);
    ASSERT_EQ(GMERR_OK, ret);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    uint64_t groupWriteBytes1 = 0;
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < 10000; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple_1 (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        groupWriteBytes1 += strlen(tupleBuf) + 1;
    }
    HeapPerfStatT *perfStat = heapHdl->perfStat;
    int cnt1 = perfStat->phyItemNum;
    EXPECT_EQ(10000, cnt1);

    SeTrxSavepointNameT name2 = {0};
    char *spname2 = (char *)"abc";
    name2.length = strlen(spname2) + 1;
    strcpy_s(name2.name, name2.length, spname2);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name2);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    int cnt2 = perfStat->phyItemNum;
    EXPECT_EQ(20000, cnt2);
    EXPECT_EQ((uint64_t)0, perfStat->writeBytes);  // 事务提交后才会刷新dfx

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name2);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt3 = perfStat->phyItemNum;
    EXPECT_EQ(10000, cnt3);

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt4 = perfStat->phyItemNum;
    EXPECT_EQ(10000, cnt4);  // 回滚到重名savePoint，条数预期还是10000

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    EXPECT_EQ(groupWriteBytes1, perfStat->writeBytes);
    StubClearUndoTransaction(stubIndex);
}

/*
测试点：savePoint的创建、回滚，DFX的校验
1. 开启事务，先插入2条记录
2. 创建1个命名为"abc"的savepoint, 再插入2条记录，
3. 回滚到savePoint "abc", 预期回滚2条记录，还剩2条
4. 事务提交，校验DFX
*/
TEST_F(UtStorageHeapAM, Savepoint2)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    SeBeginOptiTrx();
    SeTrxSavepointNameT name = {0};
    char *spname = (char *)"abc";
    name.length = strlen(spname) + 1;
    strcpy_s(name.name, name.length, spname);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    uint64_t groupWriteBytes1 = 0;
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple_1 (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        groupWriteBytes1 += strlen(tupleBuf) + 1;
    }
    HeapPerfStatT *perfStat = heapHdl->perfStat;
    int cnt1 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt1);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }

    int cnt2 = perfStat->phyItemNum;
    EXPECT_EQ(4, cnt2);

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt3 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt3);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    EXPECT_EQ(groupWriteBytes1, perfStat->writeBytes);
    StubClearUndoTransaction(stubIndex);
}

/*
测试点：匿名savePoint的创建、回滚，回滚不存在的savepoint，DFX的校验
1. 开启事务
2. 创建1个匿名的savepoint, 插入2条记录，
3. 再次创建1个匿名的savepoint, 再插入2条记录
4. 回滚到匿名savePoint, 预期回滚2条记录，还剩2条
5. 再次回滚到匿名savePoint, 因为回滚后savepoint不失效，预期回滚0条记录，还剩2条
6. 测试回滚到不存在的savePoint，预期报错
7. 事务提交，校验DFX
*/
TEST_F(UtStorageHeapAM, Savepoint3)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    SeBeginOptiTrx();

    SeTrxSavepointNameT name1 = {0};
    char *spname1 = (char *)"abc";
    name1.length = strlen(spname1) + 1;
    strcpy_s(name1.name, name1.length, spname1);

    // 匿名
    SeTrxSavepointNameT name2 = {0};
    char *spname2 = nullptr;
    name2.length = 1;
    strcpy_s(name2.name, name2.length, spname2);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name2);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t groupWriteBytes1 = 0;
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        groupWriteBytes1 += strlen(tupleBuf) + 1;
    }
    HeapPerfStatT *perfStat = heapHdl->perfStat;
    int cnt1 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt1);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name2);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }

    int cnt2 = perfStat->phyItemNum;
    EXPECT_EQ(4, cnt2);

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name2);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt3 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt3);

    // 回滚重复存在的匿名savepoint
    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name2);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt4 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt4);

    // 回滚不存在的savepoint
    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name1);
    EXPECT_NE(GMERR_OK, ret);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    EXPECT_EQ(groupWriteBytes1, perfStat->writeBytes);
    StubClearUndoTransaction(stubIndex);
}

// 创建savepoint、删除savepoint、再次创建savepoint，可以savepoint回滚成功
/*
测试点：匿名savePoint的创建、回滚，回滚不存在的savepoint，DFX的校验
1. 开启事务, 插入2条记录
2. 创建1个命名为"abc"的savepoint, 插入2条记录，
3. 释放命名为"abc"的savepoint, 预期成功
4. 再次插入2条记录
5. 连续创建2个匿名的savepoint, 预期成功, 再次插入2条记录
6. 回滚到匿名savePoint, 预期回滚2条记录，还剩6条
7. 事务提交，校验DFX
*/
TEST_F(UtStorageHeapAM, Savepoint4)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    SeBeginOptiTrx();
    SeTrxSavepointNameT name1 = {0};
    char *spname1 = (char *)"abc";
    name1.length = strlen(spname1) + 1;
    strcpy_s(name1.name, name1.length, spname1);

    SeTrxSavepointNameT name2 = {0};
    char *spname2 = nullptr;
    name2.length = 0;
    strcpy_s(name2.name, name2.length, spname2);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    uint64_t groupWriteBytes1 = 0;
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        groupWriteBytes1 += strlen(tupleBuf) + 1;
    }
    HeapPerfStatT *perfStat = heapHdl->perfStat;
    int cnt1 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt1);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name1);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        groupWriteBytes1 += strlen(tupleBuf) + 1;
    }

    int cnt2 = perfStat->phyItemNum;
    EXPECT_EQ(4, cnt2);

    ret = SeTrxReleaseSavepoint(g_seRunCtx, &name1);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        groupWriteBytes1 += strlen(tupleBuf) + 1;
    }

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name2);
    ASSERT_EQ(GMERR_OK, ret);
    // 连续相同的savepoint
    ret = SeTrxCreateSavepoint(g_seRunCtx, &name2);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name2);
    EXPECT_EQ(GMERR_OK, ret);
    // 回滚到连续相同的savepoint
    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name2);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt4 = perfStat->phyItemNum;
    EXPECT_EQ(6, cnt4);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    EXPECT_EQ(groupWriteBytes1, perfStat->writeBytes);
    StubClearUndoTransaction(stubIndex);
}

/*
测试点：空UndoLog的回滚
1. 开启事务，创建1个命名为"abc"的savepoint, 再插入2条记录
2. 回滚到savePoint "abc", 预期回滚2条记录，还剩2条
3. 事务回滚，校验DFX
*/
TEST_F(UtStorageHeapAM, Savepoint5)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    SeBeginOptiTrx();
    SeTrxSavepointNameT name = {0};
    char *spname = (char *)"abc";
    name.length = strlen(spname) + 1;
    strcpy_s(name.name, name.length, spname);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name);
    ASSERT_EQ(GMERR_OK, ret);

    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple_1 (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapPerfStatT *perfStat = heapHdl->perfStat;
    int cnt1 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt1);

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt2 = perfStat->phyItemNum;
    EXPECT_EQ(0, cnt2);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx, true);
    EXPECT_EQ(0ull, perfStat->writeBytes);
    StubClearUndoTransaction(stubIndex);
}

/*
测试点：abort状态的事务，回滚到不存在的savePoint，事务状态保持不变，回滚到正确的savePoint，变成active
1. 开启事务，创建1个命名为"abc"的savepoint, 再插入2条记录
2. 回滚到savePoint "abc", 预期回滚2条记录，还剩2条
3. 事务回滚，校验DFX
*/
TEST_F(UtStorageHeapAM, Savepoint6)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    SeBeginOptiTrx();
    SeTrxSavepointNameT name = {0};
    char *spname = (char *)"abc";
    name.length = strlen(spname) + 1;
    strcpy_s(name.name, name.length, spname);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    ret = SeTrxCreateSavepoint(g_seRunCtx, &name);
    ASSERT_EQ(GMERR_OK, ret);

    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple_1 (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapPerfStatT *perfStat = heapHdl->perfStat;
    int cnt1 = perfStat->phyItemNum;
    EXPECT_EQ(2, cnt1);

    TrxAbort((TrxT *)g_seRunCtx->trx);

    SeTrxSavepointNameT nameErr = {0};
    spname = (char *)"bcd";
    nameErr.length = strlen(spname) + 1;
    strcpy_s(nameErr.name, nameErr.length, spname);
    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &nameErr);  // 回滚到不存在的savePoint
    EXPECT_EQ(GMERR_NO_DATA, ret);

    TrxStateE trxState = SeTransGetState(g_seRunCtx);
    EXPECT_EQ(trxState, TRX_STATE_ABORT);

    ret = SeTrxRollbackToSavepoint(g_seRunCtx, &name);  // 回滚到正确的savePoint
    EXPECT_EQ(GMERR_OK, ret);

    trxState = SeTransGetState(g_seRunCtx);
    EXPECT_EQ(trxState, TRX_STATE_ACTIVE);

    int cnt2 = perfStat->phyItemNum;
    EXPECT_EQ(0, cnt2);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx, true);
    EXPECT_EQ(0ull, perfStat->writeBytes);
    StubClearUndoTransaction(stubIndex);
}

TEST_F(UtStorageHeapAM, HeapAmCreateDrop1024)
{
    int32_t ret;
    vector<ShmemPtrT> heapShmVector;
    for (uint32_t i = 0; i < NUM; ++i) {
        HeapAccessCfgT heapCfg = {
            .pageType = HEAP_VAR_LEN_ROW_PAGE,
            .tupleType = HEAP_TUPLE_TYPE_VERTEX,
            .fixRowSize = 0,
            .slotExtendSize = 0,
            .seInstanceId = GET_INSTANCE_ID,
            .isYangBigStore = false,
            .isStatusMergeSubs = false,
            .isPersistent = false,
            .isLabelLockSerializable = true,
            .isUseRsm = false,
#ifdef FEATURE_GQL
            .skipRowLockPessimisticRR = false,
#endif
            .ccType = CONCURRENCY_CONTROL_NORMAL,
            .trxType = PESSIMISTIC_TRX,
            .isolation = READ_COMMITTED,
            .labelId = i + 1,
            .heapFileId = 0,
            .heapFsmFileId = 0,
            .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
            .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
            .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
            .maxItemNum = MAX_ITEM_NUM,
        };
        ShmemPtrT heapShmAddr;
        ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
        if (ret == 0) {
            heapShmVector.push_back(heapShmAddr);
        }
        EXPECT_EQ(0, ret);
    }

    for (auto heapShmAddr : heapShmVector) {
        HeapCntrAcsInfoT heapCntrAcsInfo = {
            .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
        ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
        EXPECT_EQ(0, ret);
    }
}

TEST_F(UtStorageHeapAM, HeapAmBasicHeapAmOpenClose)
{
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_EmptyPage)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        ASSERT_EQ(0, ret);
        if (auxInfo.isEof) {
            break;
        } else {
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
        }
    }

    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Insert_Perf)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后scan, 然后批量查询, 删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < MAX_ITEM_NUM; ++i) {  //
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_InsertBatch_Scan_Delete)
{
    setStubC((void *)UndoKeepThreadAlive, (void *)StubUndoKeepThreadAlive);
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    int batchNum = 10;
    const int batchSize = 16384;
    int tupleSize = 100;
    uint8_t *dataBuf = (uint8_t *)DbDynMemCtxAlloc(usrMemCtx, batchNum * batchSize * tupleSize);
    HeapTupleBufT tuples[batchSize] = {0};
    HpBatchOut batchOut[batchSize] = {0};
    std::vector<HpTupleAddr> addrs;

    uint8_t data = 0;
    int bytes = 0;
    // insert 4 batch to Heap
    for (int i = 0; i < batchNum; ++i) {
        for (auto &tuple : tuples) {
            tuple.bufSize = tupleSize;
            tuple.buf = dataBuf + bytes;
            memset_s(tuple.buf, tupleSize, data++, tuple.bufSize);
            bytes += tuple.bufSize;
        }
        ret = HeapLabelInsertTupleBatch(heapHdl, batchSize, tuples, batchOut);
        EXPECT_EQ(GMERR_OK, ret);
        for (auto out : batchOut) {
            addrs.push_back(out.addrOut);
        }
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    // check written data
    bytes = 0;
    for (int i = 0; i < batchNum; ++i) {
        for (int j = 0; j < batchSize; ++j) {
            int idx = i * batchSize + j;
            ret = HeapFetchHpTupleBuffer(heapHdl, addrs[idx], &heapTupleBuf);
            ASSERT_EQ(GMERR_OK, ret);
            int rs = memcmp((dataBuf + bytes), heapTupleBuf.buf, tupleSize);
            ASSERT_EQ(0, rs);
            bytes += tupleSize;
        }
    }
    DbDynMemCtxFree(usrMemCtx, dataBuf);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    TupleBufT heapTupleBufScan;
    TupleBufInit(&heapTupleBufScan, (DbMemCtxT *)usrMemCtx);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(GMERR_OK, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers(
            (HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBufScan, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(GMERR_OK, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(batchNum * batchSize, (int32_t)scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &addr : addrs) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    HeapHandleClose(heapHdl);
}

StatusInter TrxUndoReportRowOperation_STUB(
    SeUndoCtxT *undoCtx, TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t *rollPtr)
{
    // 测试异常分支处理
    return OUT_OF_MEMORY_INTER;
}

TEST_F(UtStorageHeapAM, InsertBatch_failed)
{
    void *usrMemCtx = g_seTopDynCtx;
    int tupleSize = 100;
    UtHeapAMBasicCreateFixLabel(tupleSize, 16);
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_FixHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    int batchNum = 1;
    const int batchSize = 16384;
    uint8_t *dataBuf = (uint8_t *)DbDynMemCtxAlloc(usrMemCtx, batchNum * batchSize * tupleSize);
    HeapTupleBufT tuples[batchSize] = {0};
    HpBatchOut batchOut[batchSize] = {0};
    std::vector<HpTupleAddr> addrs;

    setStubC((void *)TrxUndoReportRowOperation, (void *)TrxUndoReportRowOperation_STUB);
    uint8_t data = 0;
    int bytes = 0;
    // insert 4 batch to Heap
    for (int i = 0; i < batchNum; ++i) {
        for (auto &tuple : tuples) {
            tuple.bufSize = tupleSize;
            tuple.buf = dataBuf + bytes;
            memset_s(tuple.buf, tupleSize, data++, tuple.bufSize);
            bytes += tuple.bufSize;
        }
        ret = HeapLabelInsertTupleBatch(heapHdl, batchSize, tuples, batchOut);
        EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
}

void HeapConfirmForRealDel_STUB(const HeapRunCtxT *ctx, const HpPageFetchRowInfo *fetchRowInfo)
{
    return;
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_DeleteBatch_Scan)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(GMERR_OK, ret);

    int batchNum = 10;
    const int batchSize = 20;
    int tupleSize = 10000;
    uint8_t *dataBuf = (uint8_t *)DbDynMemCtxAlloc(usrMemCtx, batchNum * batchSize * tupleSize);
    HeapTupleBufT tuples[batchSize] = {0};
    HpBatchOut batchOut[batchSize] = {0};
    std::vector<HpTupleAddr> addrs;

    uint8_t data = 0;
    int bytes = 0;
    for (int i = 0; i < batchNum; ++i) {  // insert batch to Heap
        for (auto &tuple : tuples) {
            tuple.bufSize = tupleSize;
            tuple.buf = dataBuf + bytes;
            memset_s(tuple.buf, tupleSize, data++, tuple.bufSize);
            bytes += tuple.bufSize;
        }
        ret = HeapLabelInsertTupleBatch(heapHdl, batchSize, tuples, batchOut);
        EXPECT_EQ(GMERR_OK, ret);
        for (auto out : batchOut) {
            addrs.push_back(out.addrOut);
        }
    }
    HeapLabelReleaseRunctx(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    opType = HEAP_OPTYPE_DELETE;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    HpBatchOutT *addrArr = new HpBatchOutT[addrs.size()];
    for (uint32_t i = 0; i < addrs.size(); ++i) {
        addrArr[i].addrOut = addrs[i];
    }
    ret = HeapDeleteTupleBatch(heapHdl, addrs.size(), addrArr, true);
    EXPECT_EQ(GMERR_OK, ret);
    heapHdl->hpOperation = HEAP_OPTYPE_UNDO_PURGER;  // 物理删除，vertex行回收
    setStubC((void *)HeapConfirmForRealDel, (void *)HeapConfirmForRealDel_STUB);
    ret = HeapDeleteTupleBatch(heapHdl, addrs.size(), addrArr, false);
    EXPECT_EQ(GMERR_OK, ret);
    HeapHandleClose(heapHdl);
    clearAllStub();

    opType = HEAP_OPTYPE_NORMALREAD;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);

    for (uint32_t i = 0; i < addrs.size(); ++i) {
        HpTupleAddr addr = addrArr[i].addrOut;
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, nullptr, nullptr);
        ASSERT_EQ(NO_DATA_HEAP_PAGE_NOT_EXIST, ret);
    }
    HeapLabelReleaseRunctx(heapHdl);
    delete[] addrArr;
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Insert_Scan_Delete)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后scan, 然后批量查询, 删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(NUM, scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Insert_Delete_Scan)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后批量查询, 删除, 然后再scan */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    for (auto &heapAddr : heapItemList) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }

    UtHeapAutoCommitAndBegin(g_seRunCtx);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(0u, scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Insert_DeletePart_Scan_Delete)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {
        .containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
    };
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后批量查询, 删除部分, 然后再scan, 然后再删除 */
    vector<HpTupleAddr> heapItemList1;
    vector<HpTupleAddr> heapItemList2;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        if (i % 2 == 0) {
            heapItemList1.push_back(addr);
        } else {
            heapItemList2.push_back(addr);
        }
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList2) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList2) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(NUM / 2, scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList1) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Insert_ScanBreak_ContinueScan_Delete)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后批量查询一部分, 使用上一次查询的逻辑地址再继续查询, 然后再删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    for (auto &heapAddr : heapItemList) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t scanCnt = 0;
    bool isEOF = false;
    HeapScanCursorT *heapCursor;
    HpFetchedAuxInfoT auxInfo;
    HeapFetchedRowInfoT fetchedRowInfo = {0};
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (scanCnt < NUM / 2) {
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        isEOF = auxInfo.isEof;
        ASSERT_FALSE(isEOF);
        ASSERT_EQ(0, ret);
        EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
        scanCnt++;
    }
    printf("scanCnt: %d\n", scanCnt);
    EXPECT_EQ(NUM / 2, scanCnt);

    HeapLabelEndScan(heapHdl, heapCursor);

    beginScanCfg = {.isDefragmentation = false};
    // 保存一下扫描的位置
    beginScanCfg.beginAddr.tupleAddr = fetchedRowInfo.curHeapTupleAddr.tupleAddr;
    beginScanCfg.beginAddr.blockId = fetchedRowInfo.curHeapTupleAddr.blockId;
    printf("blockId: %u, addr: %" PRIu64, beginScanCfg.beginAddr.blockId, beginScanCfg.beginAddr.tupleAddr);
    beginScanCfg.maxFetchNum = 1;
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(NUM, scanCnt);

    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Insert_ScanWithCondBreak_Delete)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后批量查询, 在回调内计数并进行中断，理由辅助信息内逻辑地址，再继续查询, 最后删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    for (auto &heapAddr : heapItemList) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    bool isEOF = false;
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    int matchCount = 0;
    HpFetchedAuxInfoT auxInfo = {0};
    HpScanSubsCondDataT condData = {.userScanRowProc = HeapScanRowMatchProc, .userData = &matchCount, .auxInfo = {{0}}};
    ret = HeapFetchNextWithCond((HeapRunCtxT *)heapHdl, heapCursor, &condData, &auxInfo);
    isEOF = auxInfo.isEof;
    ASSERT_FALSE(isEOF);
    ASSERT_EQ(0, ret);
    printf("matchCount: %d\n", matchCount);
    EXPECT_EQ((int)NUM / 2, matchCount);
    ASSERT_FALSE(condData.auxInfo.subAction.isMatched);
    ASSERT_FALSE(condData.auxInfo.subAction.isRollBackScan);
    ASSERT_TRUE(condData.auxInfo.subAction.isScanBreak);  // 是回调设置中断导致的返回
    HeapLabelEndScan(heapHdl, heapCursor);

    beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.maxFetchNum = 1;
    // 保存一下扫描的位置
    beginScanCfg.beginAddr.tupleAddr = *(HpTupleAddr *)&condData.auxInfo.curHeapTupleAddr.tupleAddr;
    beginScanCfg.beginAddr.blockId = condData.auxInfo.curHeapTupleAddr.blockId;

    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);

    ret = HeapFetchNextWithCond((HeapRunCtxT *)heapHdl, heapCursor, &condData, &auxInfo);
    isEOF = auxInfo.isEof;
    ASSERT_TRUE(isEOF);
    ASSERT_EQ(0, ret);

    printf("Scan result: %d\n", ret);
    printf("matchCount: %d\n", matchCount);
    EXPECT_EQ((int)NUM, matchCount);
    ASSERT_FALSE(condData.auxInfo.subAction.isMatched);
    ASSERT_FALSE(condData.auxInfo.subAction.isRollBackScan);
    ASSERT_FALSE(condData.auxInfo.subAction.isScanBreak);  // 这里回调没有中断，是扫描到尾部导致返回

    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_ScanWithCondRollback)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后批量查询, 在回调内计数并进行中断，理由辅助信息内逻辑地址，再继续查询, 最后删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    for (auto &heapAddr : heapItemList) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    int callCount = 0;
    HpFetchedAuxInfoT auxInfo = {0};
    HpScanSubsCondDataT condData = {
        .userScanRowProc = HeapScanRowRollbackProc, .userData = &callCount, .auxInfo = {{0}}};
    int matchCount = 0;
    while (!auxInfo.isEof) {
        ret = HeapFetchNextWithCond((HeapRunCtxT *)heapHdl, heapCursor, &condData, &auxInfo);
        ASSERT_EQ(GMERR_OK, ret);
        if (condData.auxInfo.subAction.isMatched) {
            matchCount++;
        }
    }
    ASSERT_TRUE(auxInfo.isEof);
    ASSERT_EQ((int)NUM, matchCount);
    ASSERT_EQ((int)NUM * 2, callCount);  // 每行第一次扫描都回滚重扫，第二次成功，回调次数为行数的两倍
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapAmInsertFetchDelete_basic)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), 1);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, HeapFetchHpTupleProc, (void *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, HeapFetchHpTupleProc, (void *)&addr);
    EXPECT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapAmInsertFetchDelete01)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    /* 插入查询删除顺序执行 */
    for (uint32_t i = 0; i < NUM; ++i) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, HeapFetchHpTupleProc, (void *)&addr);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, HeapFetchHpTupleProc, (void *)&addr);
        ASSERT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
        HeapHandleClose(heapHdl);
    }
}

TEST_F(UtStorageHeapAM, HeapAmInsertFetchDelete02)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    /* 逐条插入, 然后批量查询, 删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    for (auto &heapAddr : heapItemList) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);

        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret =
            HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (uint8_t *)&heapAddr);
        ASSERT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
        HeapHandleClose(heapHdl);
    }
}

TEST_F(UtStorageHeapAM, HeapAmInsertFetch_FULL)  // 锁池目前不会扩展，无法测试MAX_ITEM_NUM
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    /* 逐条插入, 然后查询 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < MAX_ITEM_NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
    ASSERT_EQ(0, ret);  // 锁池目前不会扩展，无法测试MAX_ITEM_NUM
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, HeapFetchHpTupleProc, (void *)&heapAddr);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
}

void HeapAmShmReader(UtShmCommT *shmComm)
{
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    while (!shmComm->isCommitted) {
        DbSleep(1);
    }
    EXPECT_EQ(shmComm->writeCnt, NUM);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    HeapLabelOpenForDirectRead(heapHdl, (DbMemCtxT *)usrMemCtx);
    for (HpTupleAddr i : shmComm->addr) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&i, HeapFetchHpTupleProc, (void *)&i);
        if (ret == STATUS_OK_INTER) {
            shmComm->readOkCnt++;
        }
        EXPECT_EQ(0, ret);
        shmComm->readCnt++;
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);

    exit(0);
}

void HeapAmShmWrite(UtShmCommT *shmComm)
{
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf[50] = "";
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        ret = HeapLabelInsertHpTupleBuffer(heapHdl, &heapTupleBuf, &shmComm->addr[i]);  // 使用该接口, 提高下覆盖率
        ASSERT_EQ(0, ret);
        shmComm->writeCnt++;
    }
    printf("HeapAmShmWrite done\n");

    UtHeapAutoCommitAndBegin(seRunCtx);
    shmComm->isCommitted = true;
    while (shmComm->readCnt < NUM) {
        DbSleep(1);
    }
    EXPECT_EQ(NUM, shmComm->readOkCnt);
    printf("HeapAmShmRead done, read success num %u \n", shmComm->readOkCnt);
    HeapHandleClose(heapHdl);

    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageHeapAM, HeapAmShmRead)
{
    ShmemPtrT shmCommAddr = DbShmemCtxAlloc((DbMemCtxT *)g_topShmMemCtx, sizeof(UtShmCommT));
    UtShmCommT *shmComm = (UtShmCommT *)DbShmPtrToAddr(shmCommAddr);
    memset_s(shmComm, sizeof(UtShmCommT), 0, sizeof(UtShmCommT));
    pid_t pid = fork();
    printf("pid: %d\n", pid);
    if (pid == 0) {
        HeapAmShmReader(shmComm);
    } else {
        HeapAmShmWrite(shmComm);
    }
}

uint32_t UtHeapGetRandomSize()
{
    uint32_t size = rand() % MAX_RANDOM_LEN;

    size = size < MIN_RANDOM_LEN ? MIN_RANDOM_LEN : size;
    size = HEAP_CALC_ALIGN_SIZE(size);
    // printf("%u, ", size);
    return size;
}

int32_t UtHeapInsertVarRandomRecord(HeapRunCtxT *ctx, HpTupleAddr *addr)
{
    int32_t ret;
    uint32_t *tupleBuf = (uint32_t *)malloc(MAX_RANDOM_LEN);
    if (tupleBuf) {
        uint32_t size = UtHeapGetRandomSize();
        tupleBuf[0] = MAGIC_HEAD;
        tupleBuf[1] = size;
        tupleBuf[(size / sizeof(uint32_t)) - 1] = MAGIC_TAIL;
        ret = HeapInsert(ctx, (uint8_t *)tupleBuf, size, (HpItemPointerT *)addr);
        free(tupleBuf);
        if (ret != GMERR_OK) {
            printf("size: %u\n", size);
        }
        return ret;
    }
    return OUT_OF_MEMORY_MEM_FAILED;
}

int32_t UtHeapUpdateVarRandomRecord(HeapRunCtxT *ctx, HpTupleAddr addr)
{
    int32_t ret;
    uint32_t *tupleBuf = (uint32_t *)malloc(MAX_RANDOM_LEN);
    HeapTupleBufT heapTupleBuf = {0};
    if (tupleBuf) {
        uint32_t size = UtHeapGetRandomSize();
        tupleBuf[0] = MAGIC_HEAD;
        tupleBuf[1] = size;
        tupleBuf[(size / sizeof(uint32_t)) - 1] = MAGIC_TAIL;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        heapTupleBuf.bufSize = size;
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapUpdate(ctx, &heapTupleBuf, (HpItemPointerT *)&addr, &out);
        free(tupleBuf);

        return ret;
    }
    return OUT_OF_MEMORY_MEM_FAILED;
}

int32_t UtHeapInsertVarRecord(HeapRunCtxT *ctx, HpTupleAddr *addr, uint32_t size)
{
    // 当申请内存大小由外部输入时，内存申请前，要求对申请内存大小进行合法性校验，防止申请0长度内存，或过多地申请内存
    if (size == 0 || size > 10 * MAX_RANDOM_LEN) {
        return INT_ERR_HEAP_INVALID_PARAMETER;
    }
    int32_t ret;
    size = HEAP_CALC_ALIGN_SIZE(size);
    uint32_t *tupleBuf = (uint32_t *)malloc(size);
    if (tupleBuf) {
        tupleBuf[0] = MAGIC_HEAD;
        tupleBuf[1] = size;
        tupleBuf[(size / sizeof(uint32_t)) - 1] = MAGIC_TAIL;
        ret = HeapInsert(ctx, (uint8_t *)tupleBuf, size, (HpItemPointerT *)addr);
        free(tupleBuf);
        if (ret != GMERR_OK) {
            printf("size: %u\n", size);
        }
        return ret;
    }
    return OUT_OF_MEMORY_MEM_FAILED;
}

int32_t UtHeapUpdateVarRecord(HeapRunCtxT *ctx, HpTupleAddr addr, uint32_t size)
{
    // 当申请内存大小由外部输入时，内存申请前，要求对申请内存大小进行合法性校验，防止申请0长度内存，或过多地申请内存
    if (size == 0 || size > 10 * MAX_RANDOM_LEN) {
        return INT_ERR_HEAP_INVALID_PARAMETER;
    }
    int32_t ret;
    size = HEAP_CALC_ALIGN_SIZE(size);
    uint32_t *tupleBuf = (uint32_t *)malloc(size);
    HeapTupleBufT heapTupleBuf = {0};
    if (tupleBuf) {
        tupleBuf[0] = MAGIC_HEAD;
        tupleBuf[1] = size;
        tupleBuf[(size / sizeof(uint32_t)) - 1] = MAGIC_TAIL;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        heapTupleBuf.bufSize = size;
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapUpdate(ctx, &heapTupleBuf, (HpItemPointerT *)&addr, &out);
        free(tupleBuf);

        return ret;
    }
    return OUT_OF_MEMORY_MEM_FAILED;
}

#define UNDO_LEN 5
#define FIRST_ADDR_LEN 3
int32_t UtHeapInsertVarUndoRecord(HeapRunCtxT *ctx, HpTupleAddr *addr)
{
    int32_t ret;
    uint32_t *tupleBuf = (uint32_t *)malloc(UNDO_LEN);
    if (tupleBuf) {
        uint32_t size = UNDO_LEN;

        memset_s(tupleBuf, UNDO_LEN, 0, UNDO_LEN);
        uint8_t *typeHead = (uint8_t *)tupleBuf;
        typeHead[0] = 1;
        typeHead[1] = UNDO_LEN - FIRST_ADDR_LEN - typeHead[0];
        ret = HeapInsert(ctx, (uint8_t *)tupleBuf, size, (HpItemPointerT *)addr);
        free(tupleBuf);
        return ret;
    }
    return OUT_OF_MEMORY_MEM_FAILED;
}

int32_t UtHeapUpdateUndoVarRecord(HeapRunCtxT *ctx, HpTupleAddr addr)
{
    int32_t ret;
    HeapTupleBufT heapTupleBuf = {0};
    uint32_t *tupleBuf = (uint32_t *)malloc(UNDO_LEN);
    if (tupleBuf) {
        uint32_t *topoBuf = (uint32_t *)malloc(FIRST_ADDR_LEN);
        if (topoBuf == nullptr) {
            free(tupleBuf);
            return OUT_OF_MEMORY_MEM_FAILED;
        }
        uint32_t size = UNDO_LEN;
        tupleBuf[0] = MAGIC_HEAD;
        tupleBuf[1] = size;
        tupleBuf[size - 1] = MAGIC_TAIL;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        heapTupleBuf.bufSize = size;
        ((TrxT *)ctx->seRunCtx->trx)->base.state = TRX_STATE_ROLLBACK;  // 测试首边地址更新的回滚流程
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapUpdate(ctx, &heapTupleBuf, (HpItemPointerT *)&addr, &out);
        ((TrxT *)ctx->seRunCtx->trx)->base.state = TRX_STATE_ACTIVE;
        free(tupleBuf);
        return ret;
    }
    return OUT_OF_MEMORY_MEM_FAILED;
}

StatusInter UtHeapFetchRandomLen(HpReadRowInfoT *readRowInfo, void *userData)
{
    HpItemPointerT *addr = (HpItemPointerT *)userData;
    uint32_t *cBuf = (uint32_t *)readRowInfo->buf;

    if (MAGIC_HEAD == cBuf[0] && readRowInfo->bufSize == cBuf[1] &&
        MAGIC_TAIL == cBuf[(readRowInfo->bufSize / sizeof(uint32_t)) - 1]) {
        return STATUS_OK_INTER;
    }
    UT_PRINTF("Error: Addr(%u|%u):len(%u):%08x %08x %08x \n", addr->pageId, addr->slotId, readRowInfo->bufSize, cBuf[0],
        cBuf[1], cBuf[(readRowInfo->bufSize / sizeof(uint32_t)) - 1]);
    EXPECT_EQ(MAGIC_HEAD, cBuf[0]);
    EXPECT_EQ(readRowInfo->bufSize, cBuf[1]);
    EXPECT_EQ(MAGIC_TAIL, cBuf[(readRowInfo->bufSize / sizeof(uint32_t)) - 1]);
    DB_ASSERT(false);
    (void)addr;
    return INTERNAL_ERROR_INTER;
}

int32_t UtHeapVerifyRandomRecord(HeapRunCtxT *ctx, HpTupleAddr addr)
{
    return HeapFetch(ctx, *(HpItemPointerT *)&addr, UtHeapFetchRandomLen, (void *)&addr);
}

/* UtHeapInsertTask_VarLen 和 UtHeapFetchDelTask_VarLen 结合使用,
 * 由于是无更新场景, 不存在跨页读写, 所以可以不加pk锁 */
void *UtHeapInsertTask_VarLen(void *args)
{
    DbSetServerThreadFlag();
    int32_t ret = 0, retInsert;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    EXPECT_EQ(ret, 0);
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    HpTupleAddr addr;
    uint32_t okNum = 0;
    for (uint32_t i = 0; i < NUM; ++i) {
        UtHeapAutoCommitAndBegin(seRunCtx);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        retInsert = UtHeapInsertVarRandomRecord((HeapRunCtxT *)heapHdl, &addr);
        EXPECT_EQ(0, retInsert);
        HeapHandleClose(heapHdl);
        if (retInsert == 0) {
            ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
            EXPECT_EQ(0, ret);
            ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
            EXPECT_EQ(0, ret);
            ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr);
            EXPECT_EQ(0, ret);
            DB_ASSERT(ret == 0);  // 似乎用例不稳定. 让进程core, 然后分析原因
            HeapHandleClose(heapHdl);

            ret = SeTransCommit(seRunCtx);
            EXPECT_EQ(0, ret);

            DbSpinLock(&g_HeapItemListLock);
            g_HeapItemList.push_back(addr);
            DbSpinUnlock(&g_HeapItemListLock);
            okNum++;
        } else {
            ret = SeTransRollback(seRunCtx, false);
            EXPECT_EQ(0, ret);
        }
    }
    EXPECT_EQ(okNum, NUM);
    UtHeapAutoCommitAndBegin(seRunCtx);
    ret = SeClose(seRunCtx);
    EXPECT_EQ(0, ret);
    return nullptr;
}

/* UtHeapInsertTask_VarLen 和 UtHeapFetchDelTask_VarLen 结合使用,
 * 由于是无更新场景, 不存在跨页读写, 所以可以不加pk锁 */
void *UtHeapFetchDelTask_VarLen(void *args)
{
    DbSetServerThreadFlag();
    int32_t delCnt = 0;
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    EXPECT_EQ(ret, 0);
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    EXPECT_EQ(0, ret);
    while (true) {
        int randNum = rand();
        DbSpinLock(&g_HeapItemListLock);
        uint32_t size = g_HeapItemList.size();
        if (size == 0) {
            DbSpinUnlock(&g_HeapItemListLock);
            break;
        }
        randNum %= size;
        HpTupleAddr addr = g_HeapItemList[randNum];
        swap(g_HeapItemList[randNum], g_HeapItemList.back());
        g_HeapItemList.pop_back();
        DbSpinUnlock(&g_HeapItemListLock);

        UtHeapAutoCommitAndBegin(seRunCtx);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr);
        EXPECT_EQ(0, ret);
        DB_ASSERT(ret == 0);  // 用例不稳定. 让进程core, 然后分析原因
        HeapHandleClose(heapHdl);

        UtHeapAutoCommitAndBegin(seRunCtx);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);
        UtHeapAutoCommitAndBegin(seRunCtx);
        delCnt++;
    }
    printf("delCnt %d\n", delCnt);
    ret = SeTransCommit(seRunCtx);
    EXPECT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    EXPECT_EQ(0, ret);
    return nullptr;
}

// Heap行RollbackReserveSize字段和页FreeSize字段更新的维护
// 情况一：normal行更新成normal行，buf变大
TEST_F(UtStorageHeapAM, HeapPageRollbackReserveSizeAndFreeSizeChange1)
{
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(ret, 0);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(ret, 0);
    HpTupleAddr addr;
    HeapRunCtxT *ctx = (HeapRunCtxT *)heapHdl;
    ret = UtHeapInsertVarRecord(ctx, &addr, MIN_RANDOM_LEN);
    ASSERT_EQ(ret, 0);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    // 获取旧信息
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(ret, 0);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = *(HpItemPointerT *)&addr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    ret = HeapFetchRow(ctx, &opInfo);
    ASSERT_EQ(ret, 0);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    uint16_t oldRollbackReserveSize = fetchRowInfo.srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize;
    EXPECT_EQ(oldRollbackReserveSize, MIN_RANDOM_LEN);
    uint16_t oldFreeSize = fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead->freeSize;
    HeapHandleClose(heapHdl);
    // 更新+验证更新后的信息
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(ret, 0);
    ret = UtHeapUpdateVarRecord(ctx, addr, MAX_RANDOM_LEN);
    ASSERT_EQ(ret, 0);
    ret = HeapFetchRow(ctx, &opInfo);
    ASSERT_EQ(ret, 0);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize, MAX_RANDOM_LEN);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead->freeSize, oldFreeSize + MIN_RANDOM_LEN - MAX_RANDOM_LEN);
    HeapHandleClose(heapHdl);
    // 提交+验证提交后的信息
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(ret, 0);
    ret = HeapFetchRow(ctx, &opInfo);
    ASSERT_EQ(ret, 0);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize, MAX_RANDOM_LEN);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead->freeSize, oldFreeSize + MIN_RANDOM_LEN - MAX_RANDOM_LEN);
    HeapHandleClose(heapHdl);
}

// 情况二：normal行更新成normal行，buf变小
TEST_F(UtStorageHeapAM, HeapPageRollbackReserveSizeAndFreeSizeChange2)
{
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(ret, 0);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(ret, 0);
    HpTupleAddr addr;
    HeapRunCtxT *ctx = (HeapRunCtxT *)heapHdl;
    ret = UtHeapInsertVarRecord(ctx, &addr, MAX_RANDOM_LEN);
    ASSERT_EQ(ret, 0);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    // 获取旧信息
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(ret, 0);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = *(HpItemPointerT *)&addr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    ret = HeapFetchRow(ctx, &opInfo);
    ASSERT_EQ(ret, 0);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    uint16_t oldRollbackReserveSize = fetchRowInfo.srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize;
    EXPECT_EQ(oldRollbackReserveSize, MAX_RANDOM_LEN);
    uint16_t oldFreeSize = fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead->freeSize;
    HeapHandleClose(heapHdl);
    // 更新+验证更新后的信息
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(ret, 0);
    ret = UtHeapUpdateVarRecord(ctx, addr, MIN_RANDOM_LEN);
    ASSERT_EQ(ret, 0);
    ret = HeapFetchRow(ctx, &opInfo);
    ASSERT_EQ(ret, 0);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize, MAX_RANDOM_LEN);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead->freeSize, oldFreeSize);
    HeapHandleClose(heapHdl);
    // 提交+验证提交后的信息
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(ret, 0);
    ret = HeapFetchRow(ctx, &opInfo);
    ASSERT_EQ(ret, 0);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize, MIN_RANDOM_LEN);
    EXPECT_EQ(fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead->freeSize, oldFreeSize - MIN_RANDOM_LEN + MAX_RANDOM_LEN);
    HeapHandleClose(heapHdl);
}

/* UtHeapInsertTask_VarLen 和 UtHeapFetchDelTask_VarLen 结合使用 */
/* 多线程场景，ASSERT在线程函数中，非无效用例*/
#ifndef HPE
TEST_F(UtStorageHeapAM, HeapConcurrencyVarLenRandom)
{
    g_HeapItemList.clear();
    const int numThreads = 8;
    pthread_t threads[numThreads * 2];
    int32_t ret;
    // 使用C语言的线程创建函数代替C++的线程类更加简洁，降低当前进程的内存空间
    for (int i = 0; i < numThreads; ++i) {
        ret = pthread_create(&threads[i], nullptr, UtHeapInsertTask_VarLen, nullptr);
        EXPECT_EQ(0, ret);
    }
    for (int i = numThreads; i < numThreads * 2; ++i) {
        ret = pthread_create(&threads[i], nullptr, UtHeapFetchDelTask_VarLen, nullptr);
        EXPECT_EQ(0, ret);
    }
    for (int i = 0; i < numThreads * 2; ++i) {
        ret = pthread_join(threads[i], nullptr);
        EXPECT_EQ(0, ret);
    }
    // g_HeapItemList是全局变量，在UT用例连跑期间如果不销毁的话会占用较高内存空间，影响后续用例的执行
    vector<HpTupleAddr>().swap(g_HeapItemList);
}
#endif

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_Truncate)
{
    int32_t ret = 0;

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    uint32_t insertNum = 2;
    for (uint32_t i = 0; i < insertNum; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    uint32_t scanCnt = 0;
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(insertNum, scanCnt);  // truncate后插入insertNum条，再scan
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, HeapAmUpdateVarLenBasic)
{
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    vector<HpTupleAddr> tpAddrList;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    HpTupleAddr addr;
    uint32_t okNum = 0;
    for (uint32_t i = 0; i < NUM / 4; ++i) {
        ret = UtHeapInsertVarRandomRecord((HeapRunCtxT *)heapHdl, &addr);
        if (ret == 0) {
            tpAddrList.push_back(addr);
            okNum++;
        }
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    int32_t count = -1;
    for (auto addr2 : tpAddrList) {
        count++;
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr2);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapUpdateVarRandomRecord((HeapRunCtxT *)heapHdl, addr2);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        UtHeapAutoCommitAndBegin(g_seRunCtx);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr2);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);
    }

    for (auto addr2 : tpAddrList) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);

        ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr2);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr2, true);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);
        UtHeapAutoCommitAndBegin(g_seRunCtx);
    }
}

int32_t HeapPageGetMinRowSize_STUB(PageSizeT bufSize, HpPageRowTypeE rowType)
{
    return DB_SUCCESS;
}

void HeapVarPageInitNormalRow_STUB(const HeapRunCtxT *ctx, HpNormalRowHeadT *rowHead, const HpPageRowOpInfoT *opInfo)
{
    return;
}

extern "C" void HeapVarPageInitNormalRowImpl(
    const HeapRunCtxT *ctx, HpNormalRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo);

TEST_F(UtStorageHeapAM, HeapAmUpdateVarLenFirstEdge)
{
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    vector<HpTupleAddr> tpAddrList;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    HpTupleAddr addr;
    uint32_t okNum = 0;
    for (uint32_t i = 0; i < NUM / 4; ++i) {
        ret = UtHeapInsertVarRandomRecord((HeapRunCtxT *)heapHdl, &addr);
        if (ret == 0) {
            tpAddrList.push_back(addr);
            okNum++;
        }
    }
    HeapHandleClose(heapHdl);

    HpPageFetchRowInfo fetchedRowInfo = {0};
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = {.fetchRowInfo = &fetchedRowInfo, .allocRowInfo = &pageAllocRowInfo};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    setStubC((void *)HeapPageGetMinRowSize, (void *)HeapPageGetMinRowSize_STUB);
    setStubC((void *)HeapVarPageInitNormalRowImpl, (void *)HeapVarPageInitNormalRow_STUB);
    HpNormalRowHeadT theHead = {0};
    HVPageHeadT thePageHead = {0};
    opInfo.fetchRowInfo->srcRowInfo.rowHeadPtr.normalRowHead = &theHead;
    opInfo.fetchRowInfo->srcRowInfo.rowHeadPtr.normalRowHead->size = 8;
    opInfo.allocRowInfo->bufSize = 0;
    opInfo.fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead = &thePageHead;
    opInfo.updInPageOldStoreSize = 0xabcd;
    uint16_t oldFreeSize = opInfo.fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead->baseHead.pageHead.freeSize;
    ret = HeapUpdateNormalRow((HeapRunCtxT *)heapHdl, &opInfo);
    EXPECT_EQ(0, ret);
    uint16_t newFreeSize = opInfo.fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead->baseHead.pageHead.freeSize;
    EXPECT_EQ(oldFreeSize, newFreeSize);  // 更新后, 长度是0, 相当于size 全部加上
    HeapHandleClose(heapHdl);
    clearAllStub();
}

#ifndef HPE
TEST_F(UtStorageHeapAM, HeapAmUpdateUndoVarLenBasic)
{
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    vector<HpTupleAddr> tpAddrList;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    HpTupleAddr addr;
    uint32_t okNum = 0;
    for (uint32_t i = 0; i < 1; ++i) {
        ret = UtHeapInsertVarUndoRecord((HeapRunCtxT *)heapHdl, &addr);
        if (ret == 0) {
            tpAddrList.push_back(addr);
            okNum++;
        }
    }
    HeapHandleClose(heapHdl);

    int32_t count = -1;
    for (auto addr2 : tpAddrList) {
        count++;
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        HeapRunCtxT *ctx = (HeapRunCtxT *)heapHdl;
        uint64_t beforeWriteBytes = ctx->perfStat->writeBytes;
        uint64_t beforeDelBytes = ctx->perfStat->deleteBytes;
        HeapLabelResetCtx(heapHdl);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapUpdateUndoVarRecord(ctx, addr2);
        ASSERT_EQ(0, ret);
        uint64_t afterWriteBytes = ctx->perfStat->writeBytes;
        uint64_t afterDelBytes = ctx->perfStat->deleteBytes;
        ASSERT_EQ(afterWriteBytes - beforeWriteBytes, afterDelBytes - beforeDelBytes);
        HeapHandleClose(heapHdl);
    }
}
#endif

void UtHeapInsertTask_VarLen_withPkLock()
{
    DbSetServerThreadFlag();
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    HpTupleAddr addr;
    uint32_t okNum = 0;
    for (uint32_t i = 0; i < NUM / 10; ++i) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapInsertVarRandomRecord((HeapRunCtxT *)heapHdl, &addr);  // t 22
        HeapHandleClose(heapHdl);
        if (ret == 0) {
            ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
            ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
            EXPECT_EQ(0, ret);
            ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr);
            EXPECT_EQ(0, ret);
            DbSpinLock(&g_HeapItemListLock);  // t 23,t 24,t 25,t 26
            g_HeapItemWithPkList.push_back({addr, UtHeapGeneratePk()});
            DbSpinUnlock(&g_HeapItemListLock);
            okNum++;
            HeapHandleClose(heapHdl);
        }
    }
    EXPECT_EQ(okNum, NUM / 10);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
}

void UtHeapUpdTask_VarLen_withPkLock()
{
    DbSetServerThreadFlag();
    int32_t updCnt = 0;
    int32_t ret, ret1;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    while (deleteThreadCnt) {
        int randNum = rand();
        DbSpinLock(&g_HeapItemListLock);
        uint32_t size = g_HeapItemWithPkList.size();
        if (size == 0) {
            DbSpinUnlock(&g_HeapItemListLock);
            break;
        }
        randNum %= size;
        HpTupleAddr addr = g_HeapItemWithPkList[randNum].addr;
        DbSpinUnlock(&g_HeapItemListLock);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret1 = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr);
        HeapHandleClose(heapHdl);
        if (ret1 == STATUS_OK_INTER) {
            ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
            EXPECT_EQ(0, ret);
            ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
            EXPECT_EQ(0, ret);
            ret = UtHeapUpdateVarRandomRecord((HeapRunCtxT *)heapHdl, addr);
            EXPECT_EQ(STATUS_OK_INTER, ret);
            if (ret == 0) {
                updCnt++;
            }
            HeapHandleClose(heapHdl);
        } else {
            EXPECT_EQ(STATUS_OK_INTER, ret);
        }
    }
    printf("updCnt %d\n", updCnt);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
}

void UtHeapFetchDelTask_VarLen_withPkLock()
{
    DbSetServerThreadFlag();
    int32_t delCnt = 0;
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    while (true) {
        int randNum = rand();
        DbSpinLock(&g_HeapItemListLock);
        uint32_t size = g_HeapItemWithPkList.size();
        if (size == 0) {
            deleteThreadCnt--;
            DbSpinUnlock(&g_HeapItemListLock);
            break;
        }
        randNum %= size;
        HpTupleAddr addr = g_HeapItemWithPkList[randNum].addr;
        swap(g_HeapItemWithPkList[randNum], g_HeapItemWithPkList.back());
        g_HeapItemWithPkList.pop_back();
        DbSpinUnlock(&g_HeapItemListLock);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);
        delCnt++;
    }
    printf("delCnt %d\n", delCnt);

    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
}

void UtHeapFetchTask_VarLen_withPkLock()
{
    int32_t fetchCnt = 0;
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_NORMALREAD;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    while (deleteThreadCnt) {
        int randNum = rand();
        DbSpinLock(&g_HeapItemListLock);
        uint32_t size = g_HeapItemWithPkList.size();
        if (size == 0) {
            DbSpinUnlock(&g_HeapItemListLock);
            break;
        }
        randNum %= size;
        HpTupleAddr addr = g_HeapItemWithPkList[randNum].addr;
        DbSpinUnlock(&g_HeapItemListLock);
        ret = UtHeapVerifyRandomRecord((HeapRunCtxT *)heapHdl, addr);
        EXPECT_EQ(0, ret);
        fetchCnt++;
    }
    printf("fetchCnt %d\n", fetchCnt);

    HeapHandleClose(heapHdl);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
}

void UtHeapScanTask_VarLen()
{
    DbSetServerThreadFlag();
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_NORMALREAD;
    SeRunCtxHdT seRunCtx = nullptr;
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    SeRunCtxT *runCtx = seRunCtx;
    ReadViewPrepare((TrxT *)runCtx->trx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    uint32_t scanCnt = 0;
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);
}

/* 由于有更新场景, 所以需要加pk锁 */
/* 多线程场景，ASSERT在线程函数中，非无效用例*/
TEST_F(UtStorageHeapAM, DISABLED_HeapAmConcurrencyUpdateVarLen)  // DISABLED_
{
    deleteThreadCnt = DELETE_THREAD_NUM;
    vector<std::thread> threads;
    threads.reserve(INSERT_THREAD_NUM + UPDATE_THREAD_NUM + DELETE_THREAD_NUM + FETCH_THREAD_NUM + SCAN_THREAD_NUM);
    for (int i = 0; i < INSERT_THREAD_NUM; ++i) {
        threads.emplace_back(UtHeapInsertTask_VarLen_withPkLock);  // 最大每个线程插入 5000? 个记录
    }
    for (int i = 0; i < UPDATE_THREAD_NUM; ++i) {
        threads.emplace_back(UtHeapUpdTask_VarLen_withPkLock);  // 循环更新, 直到所有记录被删除
    }
    for (int i = 0; i < FETCH_THREAD_NUM; ++i) {
        threads.emplace_back(UtHeapFetchTask_VarLen_withPkLock);  // 持续从列表中查询记录, 直到所有记录被删除
    }
    for (int i = 0; i < SCAN_THREAD_NUM; ++i) {
        threads.emplace_back(UtHeapScanTask_VarLen);  // scan 三次
    }
    for (int i = 0; i < DELETE_THREAD_NUM; ++i) {
        threads.emplace_back(UtHeapFetchDelTask_VarLen_withPkLock);  // 持续从列表中删除记录
    }
    for (auto &thread : threads) {
        thread.join();
    }
}

StatusInter UtHeapFetchFixLenRowProc(HpReadRowInfoT *readRowInfo, void *userData)
{
    uint32_t *tupleBuf = (uint32_t *)readRowInfo->buf;
    EXPECT_EQ(FIX_ROW_LEN, readRowInfo->bufSize);
    EXPECT_EQ(MAGIC_HEAD, tupleBuf[0]);
    EXPECT_EQ(MAGIC_TAIL, tupleBuf[FIX_ROW_UINT32_T_NUM - 1]);

    if (FIX_ROW_LEN == readRowInfo->bufSize && MAGIC_HEAD == tupleBuf[FIX_DEMO_POS_HEAD] &&
        MAGIC_TAIL == tupleBuf[FIX_DEMO_POS_TAIL]) {
        return STATUS_OK_INTER;
    }
    return INTERNAL_ERROR_INTER;
}

TEST_F(UtStorageHeapAM, HeapAmFixLenRow)
{
    int32_t ret;
    ShmemPtrT heapShmAddr;
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = FIX_ROW_LEN,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_FixHeapShmAddr = heapShmAddr;

    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(ret, 0);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_FixHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后批量查询, 删除, 然后再scan */
    vector<HpTupleAddr> heapItemList;
    uint32_t tupleBuf[FIX_ROW_UINT32_T_NUM] = {0};
    tupleBuf[0] = MAGIC_HEAD;

    tupleBuf[FIX_ROW_UINT32_T_NUM - 1] = MAGIC_TAIL;

    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM / 10; ++i) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        tupleBuf[0] = MAGIC_HEAD;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, FIX_ROW_LEN, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapLabelDeleteHpTuple(heapHdl, addr);
        ASSERT_EQ(0, ret);
        ret = HeapDelete(heapHdl, *(HpItemPointerT *)&addr, true);
        ASSERT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
        HeapHandleClose(heapHdl);
    }

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    for (uint32_t i = 0; i < NUM / 10; ++i) {
        tupleBuf[0] = MAGIC_HEAD;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, FIX_ROW_LEN, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    HeapTupleBufT heapTupleBuf = {.bufSize = FIX_ROW_LEN, .buf = (uint8_t *)tupleBuf};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (auto &heapAddr : heapItemList) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapFetchFixLenRowProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &heapTupleBuf, (HpItemPointerT *)&heapAddr, &out);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);
    }

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBufScan;
    TupleBufInit(&heapTupleBufScan, (DbMemCtxT *)g_seTopDynCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers(
            (HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBufScan, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapFetchFixLenRowProc, (void *)&heapAddr);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);
    ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(0, ret);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelTruncate(seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);

    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

TEST_F(UtStorageHeapAM, HeapCoverFunction)  // 补充一些函数 覆盖测试
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    /* 插入查询顺序执行 */
    for (uint32_t i = 0; i < NUM / 4; ++i) {
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        uint32_t tupleSize = strlen(tupleBuf) + 1;
        HeapTupleBufT heapTupleBuf = {.bufSize = tupleSize, .buf = (uint8_t *)tupleBuf};
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, tupleSize, (HpItemPointerT *)&addr);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        TupleBufT heapFetchedTupleBuf;
        TupleBufInit(&heapFetchedTupleBuf, (DbMemCtxT *)usrMemCtx);
        ret = HeapFetchHpTupleBuffer(heapHdl, addr, &heapFetchedTupleBuf);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        heapTupleBuf = TupleBufGet(&heapFetchedTupleBuf);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapLabelUpdateWithHpTupleBuf(heapHdl, &heapTupleBuf, addr, &out);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapLabelDeleteHpTuple(heapHdl, addr);
        EXPECT_EQ(0, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, HeapFetchHpTupleProc, (void *)&addr);
        EXPECT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
        HeapHandleClose(heapHdl);

        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
        ASSERT_EQ(0, ret);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, tupleSize, (HpItemPointerT *)&addr);
        EXPECT_EQ(0, ret);

        uint32_t rowSize = 0;
        (void)HeapLabelGetMaxRowCapacity(heapHdl, &rowSize);

        HeapHandleClose(heapHdl);
    }
}

uint32_t UtHeapStorageDemoGetPkKey(uint32_t seqId)
{
    return seqId;
}

uint32_t UtHeapStorageDemoGetHashKey(uint32_t seqId)
{
    return seqId % 200;
}

StatusInter UtHeapStorageDemoFetchProc(HpReadRowInfoT *readRowInfo, void *userData)
{
    return (StatusInter)memcpy_s(userData, FIX_ROW_LEN, readRowInfo->buf, readRowInfo->bufSize);
}

// 比较方法 (从heap获取内容, 比较 FIX_DEMO_POS_PK 是否相同
Status UtHeapStorageDemoCompareStub(
    IndexCtxT *idxCtx, IndexKeyT hashKey, const HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    Handle hpHandle = idxCtx->idxOpenCfg.heapHandle;

    // fetch, 把内容拷贝到 tupleBuf中;
    uint32_t tupleBuf[FIX_ROW_UINT32_T_NUM] = {0};
    int32_t ret =
        HeapFetch((HeapRunCtxT *)hpHandle, *(HpItemPointerT *)&addr, UtHeapStorageDemoFetchProc, (void *)&tupleBuf);
    EXPECT_EQ(0, ret);

    EXPECT_EQ(MAGIC_HEAD, tupleBuf[0]);
    EXPECT_EQ(MAGIC_TAIL, tupleBuf[FIX_ROW_UINT32_T_NUM - 1]);

    // 比较PK, 相同返回true
    if (tupleBuf[FIX_DEMO_POS_PK] == *(uint32_t *)hashKey.keyData) {
        *isMatch = true;
        *cmpRet = 0;
        return GMERR_OK;
    }
    *isMatch = false;
    return GMERR_OK;
}

TEST_F(UtStorageHeapAM, HeapPerfStat)
{
    DbSrvDfgmtTaskMgrInit(NULL);
    HeapPerfStatT perfStat = {0};
    HeapCfgStatT heapCfgStat = {0};
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_VarHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    int32_t ret = HeapLabelGetPerfStat(&heapCntrAcsInfo, &perfStat, &heapCfgStat);
    EXPECT_EQ(0, ret);  // 视图展示部分的ut写在query模块
}

TEST_F(UtStorageHeapAM, FsmPerfStat)
{
    FsmStat fsm = {0};
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_VarHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    int32_t ret = HeapLabelGetLfsFsmStat(&heapCntrAcsInfo, &fsm);
    EXPECT_EQ(0, ret);  // 视图展示部分的ut写在query模块
}

/*
TEST_F(UtStorageHeapAM, StructConfirm)
{
    uint64_t addr = 0x1234567890ABCDEF;

    HpItemPointerT *hpItemPrt = (HpItemPointerT *)(&addr);

    DmAddrT *dmAddrPrt = (DmAddrT *)(&addr);
    EXPECT_EQ(hpItemPrt->blockId, dmAddrPrt->blockId);
    EXPECT_EQ(hpItemPrt->slotId, dmAddrPrt->slotId);

}
*/

TEST_F(UtStorageHeapAM, HeapScanTupleBuffer_ReturnAddr)
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    // 逐条插入, 然后scan, 然后把scan到的地址存储起来，该地址部分查询删除有误，需要加锁
    vector<HpTupleAddr> heapItemList;
    vector<HpTupleAddr> ScanList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t scanCnt = 0;
    bool isEOF = false;
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    HpFetchedAuxInfoT auxInfo = {0};
    HeapFetchedRowInfoT fetchedRowInfo = {0};
    while (true) {
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        ScanList.push_back(fetchedRowInfo.curHeapTupleAddr.tupleAddr);
        isEOF = auxInfo.isEof;
        if (isEOF) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %d\n", scanCnt);
    EXPECT_EQ(NUM, scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);
}

TEST_F(UtStorageHeapAM, OP_NONE)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {.pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM};
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

    HpRunHdlT heapHdl, heapHdl2;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx2);
    ASSERT_EQ(ret, 0);

    TrxCfgT trxCfg = GetOptimisticTrxCfg();
    TrxT *trx1 = (TrxT *)(g_seRunCtx)->trx;  // 开启事务1

    ret = TrxBegin(trx1, &trxCfg);
    ASSERT_EQ(0, ret);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx);

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    TrxT *trx2 = (TrxT *)(g_seRunCtx2)->trx;  // 开启事务1
    ret = TrxBegin(trx2, &trxCfg);
    ASSERT_EQ(0, ret);
    ReadViewPrepare(trx2);

    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx2;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_NONE_OP, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    HeapHandleClose(heapHdl);
    HeapHandleClose(heapHdl2);

    ret = TrxCommit(trx1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = TrxCommit(trx2);
    EXPECT_EQ(GMERR_OK, ret);

    UtHeapAutoCommitAndBegin(g_seRunCtx);
    UtHeapAutoCommitAndBegin(g_seRunCtx2);
}

static int g_stubUndoDeleteHandleIndexCallCount = 0;
StatusInter StubUndoVertexDeleteHandleIndex(
    TrxT *trx, const UndoRowOpInfoT *rowOpInfo, HeapRunCtxT *heapCtx, HeapTupleBufT *heapTupleBuf)
{
    StatusInter ret = STATUS_OK_INTER;
    if (g_stubUndoDeleteHandleIndexCallCount % 2 == 0) {
        ret = MEMORY_OPERATE_FAILED_INTER;
    }
    g_stubUndoDeleteHandleIndexCallCount++;
    return ret;
}

StatusInter StubHeapVarPageConfirmSize(
    HeapRunCtxT *ctx, HVPageHeadT *pageHead, PageSizeT requireSize, bool *isCompressed, bool *isSizeEnough)
{
    DB_POINTER(isCompressed);
    *isCompressed = false;
    PageSizeT continueSize = HeapVarPageGetContinueFreeSize(pageHead);
    if (continueSize >= requireSize) {
        *isSizeEnough = true;
        return STATUS_OK_INTER;
    }
    uint16_t oldFreeSize = pageHead->baseHead.pageHead.freeSize;

    if (oldFreeSize >= requireSize) {
        *isCompressed = true;
        StatusInter ret = HeapVarPageCompress(ctx, pageHead);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        // 页面整理后freeSize不应该变化
        EXPECT_EQ(oldFreeSize, pageHead->baseHead.pageHead.freeSize);
        continueSize = HeapVarPageGetContinueFreeSize(pageHead);
        if (continueSize >= requireSize) {
            *isSizeEnough = true;
            return STATUS_OK_INTER;
        }
    }
    *isSizeEnough = false;
    return STATUS_OK_INTER;
}

int g_stubPageCompressTimes = 0;

// 模拟页面整理由于内存不足报错
StatusInter StubHeapVarPageConfirmSize2(
    HeapRunCtxT *ctx, HVPageHeadT *pageHead, PageSizeT requireSize, bool *isCompressed, bool *isSizeEnough)
{
    g_stubPageCompressTimes++;
    *isCompressed = false;
    PageSizeT continueSize = HeapVarPageGetContinueFreeSize(pageHead);
    if (continueSize >= requireSize && g_stubPageCompressTimes > 25) {
        *isSizeEnough = true;
        return STATUS_OK_INTER;
    }
    uint16_t oldFreeSize = pageHead->baseHead.pageHead.freeSize;

    if (oldFreeSize >= requireSize) {
        *isCompressed = true;
        if (g_stubPageCompressTimes <= 25 && g_stubPageCompressTimes % 2 == 1) {
            return OUT_OF_MEMORY_INTER;
        }
        StatusInter ret = HeapVarPageCompress(ctx, pageHead);
        if (ret != STATUS_OK_INTER) {
            *isSizeEnough = false;
            return ret;
        }
        continueSize = HeapVarPageGetContinueFreeSize(pageHead);
        if (continueSize >= requireSize) {
            *isSizeEnough = true;
            return STATUS_OK_INTER;
        }
    }
    *isSizeEnough = false;
    return STATUS_OK_INTER;
}

TEST_F(UtStorageHeapAM, TrxUndoDeleteWithEscapeMemCtx)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[50] = "123";
    HpTupleAddr addr;
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx, true);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    int stubIdx = setStubC((void *)UndoVertexHandleIndex, (void *)StubUndoVertexDeleteHandleIndex);
    ASSERT_TRUE(stubIdx >= 0);
    UtHeapAutoCommitAndBegin(g_seRunCtx, true);
    clearAllStub();
}

/*
Trx1 测试可见性判断、读已提交
场景：
1. 事务1插入 NUM/10 条数据，不提交
2. 事务2开启，插入 NUM/2 条数据，然后开始扫描，预期读到 NUM/2 条数据
3. 事务2提交，事务1开始扫描，预期读到 NUM/10 + NUM/2 条数据
4. 事务1提交
*/
TEST_F(UtStorageHeapAM, Trx1)
{
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl, heapHdl2;
    vector<HpTupleAddr> ScanList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    /* 逐条插入, 然后scan, 然后把scan到的地址存储起来，该地址部分查询删除有误，需要加锁 */
    vector<HpTupleAddr> heapItemList;
    for (uint32_t i = 0; i < NUM / 10; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx2);
    ASSERT_EQ(ret, 0);
    TrxT *trx = (TrxT *)(g_seRunCtx2)->trx;  // 开启事务2
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = TrxBegin(trx, &trxCfg);
    ReadViewPrepare(trx);

    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx2;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;
    for (uint32_t i = 0; i < NUM / 2; ++i) {  // 事务2插入500行
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl2, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl2);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl2, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret =
            HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl2, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(NUM / 2, scanCnt);  // 预期只读到事务2插入的500行，事务1此时还没提交
    HeapLabelEndScan(heapHdl2, heapCursor);
    HeapHandleClose(heapHdl2);

    ret = TrxCommit(trx);
    EXPECT_EQ(GMERR_OK, ret);

    trx = (TrxT *)(g_seRunCtx)->trx;
    ReadViewClose(trx);
    ReadViewPrepare(trx);
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    scanCnt = 0;
    beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(NUM / 2 + NUM / 10, scanCnt);  // 预期只读到事务1插入的100行和事务2插入的500行，事务2已提交
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    UtHeapAutoCommitAndBegin(g_seRunCtx2);
}

/*
Trx2 测试读取undo历史版本
场景：
1. 事务1插入 NUM/10 条数据，不提交
2. 事务2开启，插入 NUM/2 条数据，事务2提交
3. 事务1对事务2插入的数据进行删除
4. 事务3开启，开启扫描，预期读到 事务2插入的 NUM/2 条数据，不感知事务1的更新删除操作，事务3提交
4. 事务1提交
*/
TEST_F(UtStorageHeapAM, Trx2)
{
    int32_t ret;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl, heapHdl2, heapHdl3;
    vector<HpTupleAddr> ScanList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后scan, 然后把scan到的地址存储起来，该地址部分查询删除有误，需要加锁 */
    vector<HpTupleAddr> heapItemList;
    for (uint32_t i = 0; i < NUM / 10; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx2);
    ASSERT_EQ(ret, 0);
    TrxT *trx = (TrxT *)(g_seRunCtx2)->trx;  // 开启事务2
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = TrxBegin(trx, &trxCfg);
    ReadViewPrepare(trx);

    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx2;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;
    vector<HpTupleAddr> heapItemList2;
    for (uint32_t i = 0; i < NUM / 2; ++i) {  // 事务2插入500行
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl2, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        if (i % 2 == 0) {
            heapItemList1.push_back(addr);
        } else {
            heapItemList2.push_back(addr);
        }
    }
    HeapHandleClose(heapHdl2);

    ret = TrxCommit(trx);  // 事务2提交
    EXPECT_EQ(GMERR_OK, ret);

    trx = (TrxT *)(g_seRunCtx)->trx;  // 事务1开始更新、删除
    ReadViewClose(trx);
    ReadViewPrepare(trx);

    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx;

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto addr2 : heapItemList1) {
        ret = UtHeapUpdateVarRandomRecord((HeapRunCtxT *)heapHdl, addr2);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto addr2 : heapItemList2) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr2, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);  // 开启事务3
    ASSERT_EQ(ret, 0);
    TrxT *trx3 = (TrxT *)(g_seRunCtx3)->trx;
    trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    ret = TrxBegin(trx3, &trxCfg);
    ReadViewPrepare(trx3);

    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx3;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl3);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl3, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl3, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret =
            HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl3, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            EXPECT_EQ(ret, 0);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(NUM / 2, scanCnt);  // 预期只读到和事务2插入的500行，事务2已提交
    HeapLabelEndScan(heapHdl3, heapCursor);
    HeapHandleClose(heapHdl3);

    ret = TrxCommit(trx3);  // 事务3提交
    EXPECT_EQ(GMERR_OK, ret);
}

/*
测试事务更新回滚操作
场景：
1. 事务1插入500条记录 并提交
2. 事务2更新前100条记录后回滚，然后再begin，继续更新后续101~500条记录,最后提交
3. 默认事务读取这500条记录
4. 每读取一条记录和预期的进行比较, 预期结果： 前100条记录和事务1插入得到记录相同，后面的记录与事务2更新的记录相同
*/
TEST_F(UtStorageHeapAM, TrxtUpdateRoLLBack)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    /* *************************************开启事务1 **********************************************/
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);
    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    DmVertexLabelT *dmVertexLabel = (DmVertexLabelT *)(&heapRunCtxAllocCfg.dmInfo);
    MetaVertexLabelT metaVL = {0};
    dmVertexLabel->metaVertexLabel = &metaVL;
    dmVertexLabel->metaCommon.metaId = 0;
    DmVlIndexLabelT indexLabel[10];
    (void)memset_s(indexLabel, sizeof(DmVlIndexLabelT) * 10, 0, sizeof(DmVlIndexLabelT) * 10);
    dmVertexLabel->metaVertexLabel->secIndexes = (DmVlIndexLabelT *)&indexLabel;
    dmVertexLabel->metaVertexLabel->secIndexNum = sizeof(indexLabel) / sizeof(indexLabel[0]);

    /* 事务1先插入500条记录 */
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[100] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM / 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[origin_insert (Thread %u) (transaction 1) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl1);

    /* ********************************** 事务1提交 **********************************************************/
    ret = TrxCommit((TrxT *)(g_seRunCtx3)->trx);
    EXPECT_EQ(GMERR_OK, ret);

    /*********************************** 开启事务2 ***********************************************************/
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx2 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx2, &trxCfg);
    ReadViewPrepare(trx2);

    /* 事务2 更新前100条记录并在中途回滚 */
    HpRunHdlT heapHdl2;
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    const uint32_t changedItemNums = 100;
    HeapTupleBufT heapTupleBuf = {0};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (uint32_t i = 0; i < changedItemNums; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[current_update (Thread %u) (transaction 2) %u]", pthread_self(), i);
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        ret = HeapUpdate((HeapRunCtxT *)heapHdl2, &heapTupleBuf, (HpItemPointerT *)&heapItemList[i], &out);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl2);
    /* 事务2进行回滚操作 */
    ret = TrxRollback(trx2);
    ASSERT_EQ(0, ret);

    ret = TrxBegin(trx2, &trxCfg);
    ASSERT_EQ(0, ret);
    ReadViewPrepare(trx2);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    for (uint32_t i = changedItemNums; i < NUM / 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[current_update (Thread %u) (transaction 2) %u]", pthread_self(), i);
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        ret = HeapUpdate((HeapRunCtxT *)heapHdl2, &heapTupleBuf, (HpItemPointerT *)&heapItemList[i], &out);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl2);

    ret = TrxCommit((TrxT *)(g_seRunCtx4)->trx);
    EXPECT_EQ(GMERR_OK, ret);
    /*********************************** 事务2提交 ***********************************************************/

    /******************************* 现在由默认事务去读取 ************************************************/
    HpRunHdlT heapHdl;
    TrxT *trxt = (TrxT *)(g_seRunCtx)->trx;
    ReadViewClose(trxt);
    ReadViewPrepare(trxt);
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    EXPECT_EQ(0, ret);
    for (uint32_t i = 0; i < NUM / 2; ++i) {
        TupleBufT heapFetchedTupleBuf;
        TupleBufInit(&heapFetchedTupleBuf, (DbMemCtxT *)usrMemCtx);
        ret = HeapFetchHpTupleBuffer(heapHdl, heapItemList[i], &heapFetchedTupleBuf);
        ASSERT_EQ(0, ret);
        if (i < changedItemNums) {
            sprintf_s(tupleBuf, sizeof(tupleBuf), "[origin_insert (Thread %u) (transaction 1) %u]", pthread_self(), i);
        } else {
            sprintf_s(tupleBuf, sizeof(tupleBuf), "[current_update (Thread %u) (transaction 2) %u]", pthread_self(), i);
        }
        ret = strncmp(tupleBuf, (char *)heapFetchedTupleBuf.buf, heapFetchedTupleBuf.len);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);

    StubClearUndoTransaction(stubIndex);
}

TEST_F(UtStorageHeapAM, InvalidAddr)
{
    // 上层接口可能会使用这个值作为不可能出现的地址，修改的时候注意这里有耦合！
    ASSERT_EQ(HEAP_INVALID_ADDR, 0u);
}

TEST_F(UtStorageHeapAM, addrCompression)
{
    EXPECT_EQ(sizeof(TupleAddr), sizeof(uint64_t));  // 如修改地址长度，需考虑序列化时编码的方式是否需要同步修改
    EXPECT_EQ(SE_ADDR_ITEM_NUM, SE_THIRD_ITEM + 1);

    for (uint64_t i = 0; i < DB_MAX_UINT32; i += 500) {
        HpItemPointerT addr = {
            .pageId = (uint32_t)i,
            .slotId = (uint32_t)(i % SE_MAX_TUPLE_COUNT),
        };
        TuplePointerOrTupleAddr in = {.ptr = addr};

        TuplePointerT addrCommon = {
            .pageId = (uint32_t)i,
            .slotId = (uint32_t)(i % SE_MAX_TUPLE_COUNT),
        };
        EXPECT_EQ(addr.slotId, addrCommon.slotId);
        EXPECT_EQ(addr.pageId, addrCommon.pageId);
        // 压缩成32位地址
        if (i <= SE_MAX_PAGE_COUNT_32) {
            TuplePointer32T addr32 = HeapCompressTupleAddr32(in.addr);
            TuplePointerOrTupleAddr addr1 = {.addr = HeapUncompressTupleAddr32(addr32)};
            EXPECT_EQ(*(HpTupleAddr *)&addr, addr1.addr);
            EXPECT_EQ(addr.pageId, addr1.ptr.pageId);
            EXPECT_EQ(addr.slotId, addr1.ptr.slotId);
        }

        // 压缩成48位地址
        TuplePointer48T addr48 = HeapCompressTupleAddr48(in.addr);
        TuplePointerOrTupleAddr addr2 = {.addr = HeapUncompressTupleAddr48(addr48)};
        EXPECT_EQ(*(HpTupleAddr *)&addr, addr2.addr);
        EXPECT_EQ(addr.pageId, addr2.ptr.pageId);
        EXPECT_EQ(addr.slotId, addr2.ptr.slotId);
    }
}

TEST_F(UtStorageHeapAM, HeapBatchScanTuplebuffer)
{
    setStubC((void *)UndoKeepThreadAlive, (void *)StubUndoKeepThreadAlive);
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    int batchNum = 10;
    const int batchSize = 16384;
    int tupleSize = 100;
    uint8_t *dataBuf = (uint8_t *)DbDynMemCtxAlloc(usrMemCtx, batchNum * batchSize * tupleSize);
    HeapTupleBufT tuples[batchSize] = {0};
    HpBatchOut batchOut[batchSize] = {0};
    std::vector<HpTupleAddr> addrs;

    uint8_t data = 0;
    int bytes = 0;
    // insert 4 batch to Heap
    for (int i = 0; i < batchNum; ++i) {
        for (auto &tuple : tuples) {
            tuple.bufSize = tupleSize;
            tuple.buf = dataBuf + bytes;
            memset_s(tuple.buf, tupleSize, data++, tuple.bufSize);
            bytes += tuple.bufSize;
        }
        ret = HeapLabelInsertTupleBatch(heapHdl, batchSize, tuples, batchOut);
        EXPECT_EQ(GMERR_OK, ret);
        for (auto out : batchOut) {
            addrs.push_back(out.addrOut);
        }
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);

    // check written data
    bytes = 0;
    for (int i = 0; i < batchNum; ++i) {
        for (int j = 0; j < batchSize; ++j) {
            int idx = i * batchSize + j;
            ret = HeapFetchHpTupleBuffer(heapHdl, addrs[idx], &heapTupleBuf);
            ASSERT_EQ(GMERR_OK, ret);
            int rs = memcmp((dataBuf + bytes), heapTupleBuf.buf, tupleSize);
            ASSERT_EQ(0, rs);
            bytes += tupleSize;
        }
    }
    DbDynMemCtxFree(usrMemCtx, dataBuf);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    const uint32_t fetchBatchSize = 100;
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.maxFetchNum = fetchBatchSize;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    TupleBufT heapTupleBufs[fetchBatchSize] = {0};
    for (int i = 0; i < fetchBatchSize; i++) {
        TupleBufInit(&heapTupleBufs[i], (DbMemCtxT *)usrMemCtx);
    }
    uint64_t beginCycle = DbRdtsc();
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(GMERR_OK, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo[fetchBatchSize] = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, heapTupleBufs, fetchedRowInfo, &auxInfo);
        scanCnt += auxInfo.actualFetchRowsCnt;
        // printf("Scan result: %d fetchBatchSize: %u\n", ret, fetchBatchSize);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(GMERR_OK, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(batchNum * batchSize, (int32_t)scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);

    uint64_t endCycle = DbRdtsc();
    uint64_t useCycle = endCycle - beginCycle;
    printf("{Heap Fectch}: totalRows: 16384 one fetch batch size: %u, total cycle: %" PRIu64 "\n", fetchBatchSize,
        useCycle);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(g_seRunCtx);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &addr : addrs) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    HeapHandleClose(heapHdl);
}

/*
测试乐观事务回滚操作
*/
TEST_F(UtStorageHeapAM, OptiTrxtRoLLBack)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    /* *************************************开启事务1 **********************************************/
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    /* 事务1先插入500条记录 */
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[100] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM / 2; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[origin_insert (Thread %u) (transaction 1) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapHandleClose(heapHdl1);

    /* ********************************** 事务1提交 **********************************************************/
    ret = TrxCommit((TrxT *)(g_seRunCtx3)->trx);
    EXPECT_EQ(GMERR_OK, ret);

    /*********************************** 开启事务2 ***********************************************************/
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx2 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx2, &trxCfg);
    ReadViewPrepare(trx2);
    SeTransSetLabelModifiedActive(g_seRunCtx4);

    /* 事务2 更新前100条记录并回滚 */
    HpRunHdlT heapHdl2;
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    const uint32_t changedItemNums = 100;
    HeapTupleBufT heapTupleBuf = {0};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (uint32_t i = 0; i < changedItemNums; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[current_update (Thread %u) (transaction 2) %u]", pthread_self(), i);
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        ret = HeapUpdate((HeapRunCtxT *)heapHdl2, &heapTupleBuf, (HpItemPointerT *)&heapItemList[i], &out);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl2);

    /*********************************** 事务2回滚 ***********************************************************/
    ret = TrxRollback(trx2);
    ASSERT_EQ(0, ret);

    /*********************************** 开启事务3 ***********************************************************/
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx3 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx3, &trxCfg);
    ReadViewPrepare(trx3);
    SeTransSetLabelModifiedActive(g_seRunCtx4);

    /* 事务3 删除前100条记录并回滚 */
    HpRunHdlT heapHdl3;
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl3);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl3, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    heapTupleBuf = {0};
    for (uint32_t i = 0; i < changedItemNums; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[current_delete (Thread %u) (transaction 3) %u]", pthread_self(), i);
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        ret = HeapDelete((HeapRunCtxT *)heapHdl3, *(HpItemPointerT *)&heapItemList[i], true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl3);

    /*********************************** 事务3回滚 ***********************************************************/
    ret = TrxRollback(trx3);
    ASSERT_EQ(0, ret);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);

    StubClearUndoTransaction(stubIndex);
}

HpRunHdlT UtHeapOpen(SeRunCtxHdT seRunCtx, HpOpTypeE opType)
{
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    auto ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    return heapHdl;
}

class UtHeapScanCursor {
public:
    HpRunHdlT resultHeapHdl;
    vector<TupleBufT> result;
    UtHeapScanCursor() : resultHeapHdl(nullptr)
    {}
    ~UtHeapScanCursor()
    {
        EmptyResults();
    }
    void EmptyResults()
    {
        if (result.empty()) {
            return;
        }
        DB_ASSERT(resultHeapHdl != nullptr);
        for (auto tupleBuf : result) {
            TupleBufRelease(&tupleBuf);
        }
        result.clear();
        resultHeapHdl = nullptr;
    }

    int ScanAll(HpRunHdlT _heapHdl)
    {
        EmptyResults();
        resultHeapHdl = _heapHdl;
        HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
        HeapScanCursorT *heapCursor;
        HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
        beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
        beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
        beginScanCfg.maxFetchNum = 1;
        beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
        auto ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)resultHeapHdl, &beginScanCfg, &heapCursor);
        if (ret != 0) {
            return ret;
        }
        while (true) {
            TupleBufT heapTupleBufScan;
            TupleBufInit(&heapTupleBufScan, (DbMemCtxT *)g_seTopDynCtx);
            HeapFetchedRowInfoT fetchedRowInfo = {};
            ret = HeapFetchNextHpTupleBuffers(
                (HeapRunCtxT *)resultHeapHdl, heapCursor, &heapTupleBufScan, &fetchedRowInfo, &auxInfo);
            EXPECT_EQ(0, ret);
            if (auxInfo.isEof) {
                break;
            } else {
                EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
                result.push_back(heapTupleBufScan);
            }
        }
        HeapLabelEndScan(resultHeapHdl, heapCursor);
        return 0;
    }
};

// DTS2023052704355
// 客户端在复用HeapRunCtx进行扫描操作时，有可能使用了上一次的缓存，导致加页latch流程失败，从而漏扫描数据
// 修改后从缓存中获取页后如果校验不成功，会重置缓存，重试一次不使用缓存直接获取页，此时加页latch不会报错
TEST_F(UtStorageHeapAM, HeapFetchTryGetPageWhenLastPageCacheExpired)
{
    PERSISTENCE_NOT_SUPPORT;
    // 1.创建heapLabel
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT,
        .trxType = PESSIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ShmemPtrT heapShmAddr2;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr2);
    ASSERT_EQ(0, ret);
    g_FixHeapShmAddr = heapShmAddr;

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    // 2.开启事务
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.isolationLevel = READ_UNCOMMITTED;
    trxCfg.isLiteTrx = true;
    ret = SeTransBegin(seRunCtx, &trxCfg);
    EXPECT_EQ(ret, 0);
    ret = SeTransAssignReadView(seRunCtx);
    EXPECT_EQ(ret, 0);

    // 3.插入一条记录
    HpTupleAddr addr;
    HpRunHdlT heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    const int dataSize = 100;
    char heapItem[dataSize] = "HelloWorld";
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)heapItem, dataSize, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    ret = SeTransCommit(seRunCtx);
    EXPECT_EQ(ret, 0);

    // 4.进行直连扫描，成功得到该条记录
    SeRunCtxHdT readRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &readRunCtx);
    EXPECT_EQ(0, ret);
    ret = SeTransAssignReadView(readRunCtx);
    EXPECT_EQ(ret, 0);
    HpRunHdlT readHeapHdl = UtHeapOpen(readRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, readHeapHdl);
    UtHeapScanCursor cursor;
    cursor.ScanAll(readHeapHdl);
    EXPECT_EQ(1u, cursor.result.size());
    auto tupleBuf = cursor.result[0];
    EXPECT_EQ(dataSize, (int)tupleBuf.len);
    EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, heapItem));
    cursor.EmptyResults();

    // 5.对记录进行删除
    HeapLabelResetCtx(heapHdl);
    ret = SeTransBegin(seRunCtx, &trxCfg);
    EXPECT_EQ(ret, 0);
    ret = SeTransAssignReadView(seRunCtx);
    EXPECT_EQ(ret, 0);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    ret = HeapDelete(heapHdl, *(HpItemPointerT *)&addr, false);
    EXPECT_EQ(0, ret);
    ret = SeTransCommit(seRunCtx);
    EXPECT_EQ(ret, 0);

    // 6.删除记录后另外一张表再插入一条记录
    HeapHandleClose(heapHdl);
    seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeTransBegin(seRunCtx, &trxCfg);
    EXPECT_EQ(ret, 0);
    ret = SeTransAssignReadView(seRunCtx);
    EXPECT_EQ(ret, 0);
    g_FixHeapShmAddr = heapShmAddr2;

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr2;
    ret = SeTransBegin(seRunCtx, &trxCfg);
    EXPECT_EQ(ret, 0);
    ret = SeTransAssignReadView(seRunCtx);
    EXPECT_EQ(ret, 0);
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)heapItem, dataSize, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    ret = SeTransCommit(seRunCtx);
    EXPECT_EQ(ret, 0);

    // 7.复用句柄再次进行直连扫描，预计不会报错且扫描不到数据
    HeapLabelResetCtx(readHeapHdl);
    ret = HeapLabelOpen(readHeapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    cursor.ScanAll(readHeapHdl);
    EXPECT_EQ(0u, cursor.result.size());

    HeapHandleClose(heapHdl);
    HeapHandleClose(readHeapHdl);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelTruncate(seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);

    heapCntrAcsInfo.heapShmAddr = heapShmAddr2;
    ret = HeapLabelTruncate(seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * 创建Heap，长度100；
 * 插入1条记录。
 * Heap升级，新长度200。
 * 插入1条记录。
 * 扫描记录，得到2条长度200的记录。
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_insertMakePageUpgrade)
{
    const int oldFixLen = 100;
    const int newFixLen = 200;
    char newFixItem[newFixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HpTupleAddr addr;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, newFixLen, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    UtHeapScanCursor cursor;
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(2u, cursor.result.size());

    int i = 0;
    for (auto tupleBuf : cursor.result) {
        if (i == 0) {
            EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
            EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
            i++;
        } else {
            EXPECT_EQ(newFixLen, (int)tupleBuf.len);
            EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
        }
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo1 = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelTruncate(seRunCtx, &heapCntrAcsInfo1, NULL);
    EXPECT_EQ(0, ret);

    ret = HeapLabelDrop(seRunCtx, &heapCntrAcsInfo1, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}
TEST_F(UtStorageHeapAM, HeapFixUpgrageDowngradeDelete)
{
    const int initLen = 1024;
    char fixItem[initLen] = "efg";
    UtHeapAMBasicCreateFixLabel(initLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    const int insertCnt = 20;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    HpTupleAddr addr;
    for (int i = 0; i < insertCnt; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)fixItem, initLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    uint32_t upgradeLen = 2048;
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, upgradeLen);
    EXPECT_EQ(0, ret);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    const int maxLen = 2048;
    char upgradeItem[maxLen] = "efg";
    // insert触发了页面升级检查，发现不能升级，此时pageHead->isUpgNotSatisfy = true;
    for (int i = 0; i < 1; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)upgradeItem, 2048, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 降级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf = {initLen, (uint8_t *)fixItem};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&addr, &out);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapDowngradeInfo.isHeapLabelDowngrade = true;
    beginScanCfg.heapDowngradeInfo.fixRowSize = initLen;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);

    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    // 触发降级后，不要刷新页面
    heapCursor->isScanEnd = true;
    ret = HeapLabelDowngradeFetchPage(heapHdl, heapCursor, &heapCursor->isScanEnd);
    EXPECT_EQ(0, ret);
    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    // 删空 预期触发core
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    for (auto &addr : addrs) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * 创建Heap，长度1024（1Kb）；
 * 插入31条记录。（总共31Kb）。（编号0~30）
 * Heap升级，新长度2Kb。
 * 删除前面10条记录。（编号0~9，删除）
 * 扫描记录， 得到21条长度1Kb的记录。
 * 从后面的记录往前删除，删除编号30到编号15。一共16条
 * 扫描记录，得到5条记录。长度是1kb。
 * 删除编号14的记录。
 * 扫描记录， 得到4条记录， 长度是2Kb。
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_deleteMakePageUpgrade)
{
    const int oldFixLen = 1024;   // 一个page 32 kb, 应该可以放 31 个记录
    const int newFixLen = 2048;   // 一个page 32 kb, 应该可以放 15 个记录
    const int newMaxRowCnt = 15;  // 所以在新格式页面下, 最大的 15 个记录
    char newFixItem[newFixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 31;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 删除前面10条记录。（编号0~9，删除）, 页面不能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    const int deleteCnt1 = 10;
    for (int i = 0; i < deleteCnt1; i++) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&(addrs[i]), true);
        EXPECT_EQ(0, ret);
        rowCnt--;
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    UtHeapScanCursor cursor;
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 从后面的记录往前删除，删除编号30到编号15, 页面不能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = newMaxRowCnt + 1; i < (int)addrs.size(); i++) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&(addrs[i]), true);
        EXPECT_EQ(0, ret);
        rowCnt--;
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 删除编号14的记录, 然后回滚, 页面不能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&(addrs[newMaxRowCnt]), true);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);

    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    UtHeapAutoCommitAndBegin(seRunCtx, true);  // 回滚!
    StubClearUndoTransaction(stubIndex);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 删除编号14的记录, 提交, 页面能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&(addrs[newMaxRowCnt]), true);
    EXPECT_EQ(0, ret);
    rowCnt--;
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(oldFixLen, (int)tupleBuf.len);  // 删除流程读的是行头里面的bufSize
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);

    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * 创建Heap，长度1024（1Kb）；
 * 插入31条记录。（总共31Kb）。（编号0~30）
 * Heap升级，新长度2Kb。
 *
 * 批量删除记录（编号0~9，14~30）
 * 扫描记录， 得到4条记录， 长度是2Kb。
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_batchDeleteMakePageUpgrade)
{
    const int oldFixLen = 1024;   // 一个page 32 kb, 应该可以放 31 个记录
    const int newFixLen = 2048;   // 一个page 32 kb, 应该可以放 15 个记录
    const int newMaxRowCnt = 15;  // 所以在新格式页面下, 最大的 15 个记录
    char newFixItem[newFixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 31;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 删除前面10条记录。（编号0~9，删除）, 页面不能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    HpBatchOutT batchOut[insertCnt] = {{0}};
    uint32_t batchSize = 0;
    for (int i = 0; i <= 9; i++, batchSize++) {
        batchOut[batchSize].addrOut = addrs[i];
    }

    ret = HeapLabelBatchDeleteHpTuples((HeapRunCtxT *)heapHdl, batchSize, batchOut);
    EXPECT_EQ(0, ret);
    rowCnt -= batchSize;
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    UtHeapScanCursor cursor;
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 删除后面条记录。（编号14~30，删除）, 页面能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    batchSize = 0;
    for (int i = newMaxRowCnt - 1; i < (int)addrs.size(); i++, batchSize++) {
        batchOut[batchSize].addrOut = addrs[i];
    }

    ret = HeapLabelBatchDeleteHpTuples((HeapRunCtxT *)heapHdl, batchSize, batchOut);
    EXPECT_EQ(0, ret);
    rowCnt -= batchSize;
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * 创建Heap，长度1024（1Kb）；
 * 插入10条记录。
 * Heap升级，新长度2Kb。
 *
 * 更新其中一条记录。
 * 扫描记录，得到10条长度是2Kb的记录。
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_updateMakePageUpgrade)
{
    UtHeapScanCursor cursor;
    const int oldFixLen = 1024;  // 一个page 32 kb, 应该可以放 31 个记录
    const int newFixLen = 2048;  // 一个page 32 kb, 应该可以放 15 个记录
    char newFixItem[newFixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 10;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 更新一条记录, 页面能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf = {newFixLen, (uint8_t *)newFixItem};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&(addrs[0]), &out);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    int i = 0;
    for (auto tupleBuf : cursor.result) {
        if (i == 0) {
            EXPECT_EQ(newFixLen, (int)tupleBuf.len);  // 页面升级完成
            EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
            i++;
        } else {
            EXPECT_EQ(oldFixLen, (int)tupleBuf.len);  // 页面升级完成
            EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
        }
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);

    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * Heap定长升级，更新（需要跳转行），再次对已经是跳转行的页更新，更新后行仍为跳转行
 * 构造undo存定长页跳转行，当前行也为跳转行的情况，然后回滚
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_updateMakeLinkJmp_rollBack)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    UtHeapScanCursor cursor;
    const int oldFixLen = 1024;  // 一个page 32 kb, 应该可以放 31 个记录
    const int newFixLen = 2048;  // 一个page 32 kb, 应该可以放 15 个记录
    char newFixItem1[oldFixLen] = "abc";
    char newFixItem2[newFixLen] = "def";
    char newFixItem3[newFixLen] = "ghi";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 31;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem1, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 更新全部记录。（新长度2kb）
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf2 = {newFixLen, (uint8_t *)newFixItem2};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf2, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }
    // 再次更新，linksrc --> linksrc, 构造Undo存linksrc的情况
    HeapTupleBufT newTupleBuf3 = {newFixLen, (uint8_t *)newFixItem3};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf3, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx, true);
    StubClearUndoTransaction(stubIndex);
}

/*
 * Heap定长升级，更新（需要跳转行），再次对已经是跳转行的页更新，提交
 * 构造定长页的历史版本里也有跳转行的情况。
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_updateMakeLinkJmp_commit)
{
    UtHeapScanCursor cursor;
    const int oldFixLen = 1024 * 15;  // 一个page 32 kb, 应该可以放 2 个记录
    const int newFixLen = 1024 * 30;  // 一个page 32 kb, 放不下2个记录, 生成跳转行
    char newFixItem1[oldFixLen] = "abc";
    char newFixItem2[newFixLen] = "def";
    char newFixItem3[newFixLen] = "ghi";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 2;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem1, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 更新记录，产生跳转行
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf2 = {newFixLen, (uint8_t *)newFixItem2};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf2, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }
    // 再次更新，linksrc --> linksrc
    HeapTupleBufT newTupleBuf3 = {newFixLen, (uint8_t *)newFixItem3};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf3, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
}

/*
 * Heap定长升级，更新（需要跳转行），伴随删除整理页面，跳转行迁回
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_updateMakeLinkJmp)
{
    UtHeapScanCursor cursor;
    const int oldFixLen = 1024;   // 一个page 32 kb, 应该可以放 31 个记录
    const int newFixLen = 2048;   // 一个page 32 kb, 应该可以放 15 个记录
    const int newMaxRowCnt = 15;  // 所以在新格式页面下, 最大的 15 个记录
    char newFixItem[newFixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 31;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    EXPECT_EQ(1, (int)heapHdl->heap->fsm.usedPageCnt);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 更新全部记录。（新长度2kb）
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf = {newFixLen, (uint8_t *)newFixItem};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }

    EXPECT_EQ(4, (int)heapHdl->heap->fsm.usedPageCnt);
    HeapHandleClose(heapHdl);

    // 删除前面10条记录。（编号0~9，删除）, 页面不能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    HpBatchOutT batchOut[insertCnt] = {{0}};
    uint32_t batchSize = 0;
    for (int i = 0; i <= 9; i++, batchSize++) {
        batchOut[batchSize].addrOut = addrs[i];
    }

    ret = HeapLabelBatchDeleteHpTuples((HeapRunCtxT *)heapHdl, batchSize, batchOut);
    EXPECT_EQ(0, ret);
    rowCnt -= (int)batchSize;
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(newFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    EXPECT_EQ(4, (int)heapHdl->heap->fsm.usedPageCnt);  // 有跳转行, 需要21行 需要4个page
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 删除后面条记录。（编号14~31，删除）, 页面能升级
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_DELETE);
    ASSERT_NE(nullptr, heapHdl);
    batchSize = 0;
    for (int i = newMaxRowCnt - 1; i < (int)addrs.size(); i++, batchSize++) {
        batchOut[batchSize].addrOut = addrs[i];
    }

    ret = HeapLabelBatchDeleteHpTuples((HeapRunCtxT *)heapHdl, batchSize, batchOut);
    EXPECT_EQ(0, ret);
    rowCnt -= (int)batchSize;
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(newFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    EXPECT_EQ(2, (int)heapHdl->heap->fsm.usedPageCnt);  // 没有跳转行, 需要1个page
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * Heap定长升级，更新（需要跳转行），伴随删除整理页面，跳转行迁回
 */
TEST_F(UtStorageHeapAM, HeapFixUpgrade_verySmallHeapUpgrade)
{
    UtHeapScanCursor cursor;
    const int oldFixLen = 4;    // 一个page 32 kb, 应该可以放 8K 个记录
    const int newFixLen = 100;  // 一个page 32 kb
    char newFixItem[newFixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(oldFixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 10000;  // 1万个记录, 一个page放不下
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    EXPECT_LE(1, (int32_t)heapHdl->heap->fsm.maxBlockCnt);  // 1万个记录, 一个page放不下
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 更新全部记录。
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf = {newFixLen, (uint8_t *)newFixItem};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(newFixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

// 表升级场景，slotExtend大小不为0，升级后搬迁成功
// 一个slotExtendSize大小为24 kb, 旧页插入一条记录升级
TEST_F(UtStorageHeapAM, HeapFixUpgrade_moveSlotExtend)
{
    UtHeapScanCursor cursor;
    const int oldFixLen = 100;
    const int newFixLen = 200;
    const int slotExtendSize = HeapGetHcSlotSpace(2);
    char newFixItem[newFixLen] = "asd";
    UtHeapAMBasicCreateFixLabel(oldFixLen, slotExtendSize);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HpTupleAddr addr;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, oldFixLen, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, newFixLen, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(2u, cursor.result.size());

    int i = 0;
    for (auto tupleBuf : cursor.result) {
        if (i == 0) {
            EXPECT_EQ(oldFixLen, (int)tupleBuf.len);
            EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
            i++;
        } else {
            EXPECT_EQ(newFixLen, (int)tupleBuf.len);
            EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
        }
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelTruncate(seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);

    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

// DTS2023110905365 测试fsmMgr->availSize与requireSize的设置是否正确维护
TEST_F(UtStorageHeapAM, HeapVarRequireSizeCheck)
{
    UtHeapScanCursor cursor;
    const int newLen = 27000;
    const int slotExtendSize = HeapGetHcSlotSpace(200);
    char newItem[newLen] = "asd";
    UtHeapAMBasicCreateVarLabel(slotExtendSize);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HpTupleAddr addr;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newItem, newLen, (HpItemPointerT *)&addr);
    Status retStatus = DbGetExternalErrno((StatusInter)ret);
    EXPECT_EQ(0, retStatus);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
}

void *UtHeapConcurrencyInsert(void *args)
{
    DbSetServerThreadFlag();
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    EXPECT_EQ(ret, 0);
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    HpTupleAddr addr;

    for (uint32_t i = 0; i < NUM; i++) {
        UtHeapAutoCommitAndBegin(seRunCtx);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
        EXPECT_EQ(0, ret);

        ret = UtHeapInsertVarRandomRecord((HeapRunCtxT *)heapHdl, &addr);
        EXPECT_EQ(0, ret);

        HeapLabelResetOpType(heapHdl, HEAP_OPTYPE_DELETE, false);

        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, true);
        EXPECT_EQ(0, ret);

        HeapHandleClose(heapHdl);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(0, ret);
    }
    SeClose(seRunCtx);
    return nullptr;
}

void *UtHeapConcurrencyScan(void *args)
{
    DbSetServerThreadFlag();
    int32_t ret = 0;
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    EXPECT_EQ(ret, 0);
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);

    for (uint32_t i = 0; i < NUM; i++) {
        UtHeapAutoCommitAndBegin(seRunCtx);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);  // 带着删除的扫描
        EXPECT_EQ(0, ret);
        uint32_t scanCnt = 0;
        HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
        HeapScanCursorT *heapCursor;
        HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
        beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
        beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
        beginScanCfg.maxFetchNum = 1;
        beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
        TupleBufT heapTupleBufScan;
        TupleBufInit(&heapTupleBufScan, (DbMemCtxT *)usrMemCtx);
        ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
        EXPECT_EQ(GMERR_OK, ret);
        while (true) {
            HeapFetchedRowInfoT fetchedRowInfo = {};
            ret = HeapFetchNextHpTupleBuffers(
                (HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBufScan, &fetchedRowInfo, &auxInfo);
            if (auxInfo.isEof) {
                EXPECT_EQ((uint32_t)0, scanCnt);  // 并发插入新数据且在同一个事务内删除，这里扫描无法读取到任何数据
                break;
            } else {
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
                scanCnt++;
            }
        }
        HeapLabelEndScan(heapHdl, heapCursor);
        HeapHandleClose(heapHdl);
        UtHeapAutoCommitAndBegin(seRunCtx);
    }
    SeClose(seRunCtx);
    return nullptr;
}

/*
 * V2降级到V1, v2长度200，v1长度100
 * 创建V2的Heap，长度200；
 * 插入1条记录。
 * Heap降级为v1，长度100。
 * 插入1条记录。
 * 扫描记录，得到2条长度100的记录。
 */

TEST_F(UtStorageHeapAM, HeapFixDowngrade)
{
    PERSISTENCE_NOT_SUPPORT;  // 持久化对外不支持升降级
    const int v2FixLen = 200;
    const int v1FixLen = 100;
    char newFixItem[v1FixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(v2FixLen, HeapGetHcSlotSpace(1));

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HpTupleAddr addr;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, v2FixLen, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf = {v1FixLen, (uint8_t *)newFixItem};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&addr, &out);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapDowngradeInfo.isHeapLabelDowngrade = true;
    beginScanCfg.heapDowngradeInfo.fixRowSize = v1FixLen;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);

    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);

    bool isScanEnd = false;
    ret = HeapLabelDowngradeFetchPage(heapHdl, heapCursor, &isScanEnd);
    EXPECT_EQ(0, ret);
    HeapLabelEndScan(heapHdl, heapCursor);

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, v1FixLen, (HpItemPointerT *)&addr);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    UtHeapScanCursor cursor;
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(2u, cursor.result.size());

    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(v1FixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
 * 创建Heap，长度2048（2Kb）；
 * 插入15条记录。（总共30Kb）。（编号0~14）
 * Heap降级，降级为长度1Kb。（更新+页面压缩）
 * 扫描记录，得到15条长度1Kb的记录。
 * 插入16条1kb的记录
 * 扫描记录，得到31条记录。长度是1kb。
 */
TEST_F(UtStorageHeapAM, HeapFixDowngrade2)
{
    const int v2FixLen = 2048;  // 一个page 32 kb, 应该可以放 15 个记录
    const int v1FixLen = 1024;  // 一个page 32 kb, 应该可以放 31 个记录
    // const int v2RowCnt = 15;  // 所以在v2格式页面下, 最大有 15 个记录
    // const int v1RowCnt = 31;  // 所以在v2格式页面下, 最大有 31 个记录
    char newFixItem[v1FixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(v2FixLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    int rowCnt = 0;
    const int insertCnt = 15;
    vector<HpTupleAddr> addrs;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, v2FixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 模拟降级 update + 压缩
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_UPDATE);
    ASSERT_NE(nullptr, heapHdl);
    HeapTupleBufT newTupleBuf = {v1FixLen, (uint8_t *)newFixItem};
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (auto addr : addrs) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&addr, &out);
        EXPECT_EQ(0, ret);
    }
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 页面压缩
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    HeapScanCursorT *heapCursor;
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapDowngradeInfo.isHeapLabelDowngrade = true;
    beginScanCfg.heapDowngradeInfo.fixRowSize = v1FixLen;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);

    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);

    bool isScanEnd = false;
    ret = HeapLabelDowngradeFetchPage(heapHdl, heapCursor, &isScanEnd);
    EXPECT_EQ(0, ret);
    HeapLabelEndScan(heapHdl, heapCursor);
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 降级后，扫描，预期有15条长度1Kb的记录
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    UtHeapScanCursor cursor;
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(15u, cursor.result.size());

    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(v1FixLen, (int)tupleBuf.len);
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    //  插入16条1kb的记录
    const int insertCnt2 = 1;
    // vector<HpTupleAddr> addrs;
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    for (int i = 0; i < insertCnt2; i++) {
        HpTupleAddr addr;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)newFixItem, v1FixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        addrs.push_back(addr);
        rowCnt++;
    }
    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    // 扫描记录，得到31条记录。长度是1kb。
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    cursor.ScanAll(heapHdl);
    EXPECT_EQ(rowCnt, (int)cursor.result.size());
    for (auto tupleBuf : cursor.result) {
        EXPECT_EQ(v1FixLen, (int)tupleBuf.len);  // 删除流程读的是行头里面的bufSize
        EXPECT_EQ(0, strcmp((char *)tupleBuf.buf, newFixItem));
    }
    cursor.EmptyResults();
    HeapHandleClose(heapHdl);

    EXPECT_EQ(1, (int)heapHdl->heap->fsm.maxBlockCnt);  // 应该始终只有一个页
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

// 升级7次，总共8个版本，随机执行任意版本(长度)的插入, 预期都能成功
TEST_F(UtStorageHeapAM, HeapFixUpgrage)
{
    const int initLen = 1024;
    char fixItem[initLen] = "efg";
    UtHeapAMBasicCreateFixLabel(initLen, 0);

    SeRunCtxHdT seRunCtx = nullptr;
    auto ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    const int insertCnt = 15;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    HpTupleAddr addr;
    for (int i = 0; i < insertCnt; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)fixItem, initLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    uint32_t upgradeLen = initLen;
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    for (uint32_t i = 0; i < 8; i++) {
        upgradeLen += 100;  // 总值不超过2048
        ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, upgradeLen);
        EXPECT_EQ(0, ret);
    }
    EXPECT_LE(upgradeLen, 2048u);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    const int maxLen = 2048;
    char upgradeItem[maxLen] = "efg";
    for (int i = 0; i < 500; i++) {
        uint32_t randSize = rand() % (1024) + 800;
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)upgradeItem, randSize, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

/*
测试点：扫描线程无法读到insert刚刚插入的那条记录（即使扫描抢先拿到了事务锁）
第1类事务：开启事务，insert+delete，提交事务
第2类事务：扫描，扫描到的数据始终为0
*/
TEST_F(UtStorageHeapAM, HeapConcurrencyScanAndInsert)
{
    const int NUM_THREADS = 8;
    pthread_t threads[NUM_THREADS * 2];
    int32_t ret;
    for (int i = 0; i < NUM_THREADS; ++i) {
        ret = pthread_create(&threads[i], nullptr, UtHeapConcurrencyInsert, nullptr);
        EXPECT_EQ(0, ret);
    }
    for (int i = NUM_THREADS; i < NUM_THREADS * 2; ++i) {
        ret = pthread_create(&threads[i], nullptr, UtHeapConcurrencyScan, nullptr);
        EXPECT_EQ(0, ret);
    }
    for (int i = 0; i < NUM_THREADS * 2; ++i) {
        ret = pthread_join(threads[i], nullptr);
        EXPECT_EQ(0, ret);
    }
}

StatusInter UtHeapGetOldestVisibleTuple(HpReadRowInfoT *readRowInfo, void *userData)
{
    return (StatusInter)memcpy_s(userData, FIX_ROW_LEN, readRowInfo->buf, readRowInfo->bufSize);
}

/*
测试点，事务对一条记录连续更新多次、本事务自己插入多条数据
设置 isToGetOldestVisibleBuf 为 true
对于连续更新的，预期读到第一次更新前的buf；
对于本事务自己插入的，预期返回NO_DATA_HEAP_ITEM_NOT_EXIST
*/
TEST_F(UtStorageHeapAM, HeapGetOldestVisibleTuple1)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    UtHeapAMBasicCreateLabelOptiTrx();

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabelOptiTrx,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;

    char tupleBuf[FIX_ROW_LEN] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t i = 0;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (uint32_t j = 0; j < 3; j++) {  // 连续更新3次
        for (auto &heapAddr : heapItemList1) {
            sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple-update (Thread%u) %u]", pthread_self(), i++);
            HeapTupleBufT newTupleBuf = {sizeof(tupleBuf), (uint8_t *)tupleBuf};
            ret = HeapUpdate((HeapRunCtxT *)heapHdl, &newTupleBuf, (HpItemPointerT *)&heapAddr, &out);
            ASSERT_EQ(0, ret);
        }
    }
    HeapResetOpType(heapHdl, HEAP_OPTYPE_INSERT, false);  // 本事务自己插入多条数据
    vector<HpTupleAddr> heapItemList2;
    for (uint32_t k = 0; k < NUM; ++k) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), k);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList2.push_back(addr);
    }

    HeapResetOpType(heapHdl, HEAP_OPTYPE_NORMALREAD, false);
    i = 0;
    heapHdl->hpControl.isToGetOldestVisibleBuf = true;
    char tmpTupleBuf[FIX_ROW_LEN] = "";
    for (auto &heapAddr : heapItemList1) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i++);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(0, ret);
        EXPECT_STREQ(tmpTupleBuf, tupleBuf);  // 读取到本事务最老的可见buf
    }
    for (auto &heapAddr : heapItemList2) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i++);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);  // 新插入的tuple，没有旧版本可读
    }
    heapHdl->hpControl.isToGetOldestVisibleBuf = false;
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList1) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAMBasicDropOptiTrx();
}

/*
测试点，事务对部分记录未操作、对部分记录执行删除
设置 isToGetOldestVisibleBuf 为 true
对未操作的部分，预期读到正常的buf
对删除的部分，预期读到删除前的buf
*/
TEST_F(UtStorageHeapAM, HeapGetOldestVisibleTuple2)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    UtHeapAMBasicCreateLabelOptiTrx();

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabelOptiTrx,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;  // 对里面的add执行删除
    vector<HpTupleAddr> heapItemList2;  // 对里面的add不执行操作

    char tupleBuf[FIX_ROW_LEN] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        if (i % 2 == 0) {
            heapItemList1.push_back(addr);
        } else {
            heapItemList2.push_back(addr);
        }
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t i = 0;
    for (auto &heapAddr : heapItemList1) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }

    HeapResetOpType(heapHdl, HEAP_OPTYPE_NORMALREAD, false);
    i = 0;
    heapHdl->hpControl.isToGetOldestVisibleBuf = true;
    char tmpTupleBuf[FIX_ROW_LEN] = "";
    for (auto &heapAddr : heapItemList1) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(0, ret);
        EXPECT_STREQ(tmpTupleBuf, tupleBuf);  // 读取到本事务最老的可见buf
        i += 2;
    }
    i = 1;
    for (auto &heapAddr : heapItemList2) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(0, ret);
        EXPECT_STREQ(tmpTupleBuf, tupleBuf);  // 读取到本事务最老的可见buf
        i += 2;
    }
    heapHdl->hpControl.isToGetOldestVisibleBuf = false;
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList2) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAMBasicDropOptiTrx();
}

/*
测试点，事务对删除过的记录，去读取
*/
TEST_F(UtStorageHeapAM, HeapGetSelfDeletedTuple1)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    UtHeapAMBasicCreateLabelOptiTrx();

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabelOptiTrx,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;  // 对里面的add执行删除

    char tupleBuf[FIX_ROW_LEN] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t i = 0;
    for (auto &heapAddr : heapItemList1) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }

    HeapResetOpType(heapHdl, HEAP_OPTYPE_NORMALREAD, false);
    i = 0;
    heapHdl->hpControl.isCanAccessSelfDeletedBuf = true;
    char tmpTupleBuf[FIX_ROW_LEN] = "";
    for (auto &heapAddr : heapItemList1) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(0, ret);
        EXPECT_STREQ(tmpTupleBuf, tupleBuf);  // 能读取到本事务自己删除的buf
        i++;
    }
    heapHdl->hpControl.isCanAccessSelfDeletedBuf = false;
    for (auto &heapAddr : heapItemList1) {
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);  // 关掉开关，不能读取到本事务自己删除的buf了
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    UtHeapAMBasicDropOptiTrx();
}

extern "C" {
StatusInter MdSpaceCreateTableSpace(const SeInstanceT *seIns, TableSpaceCfgT *tableSpaceCfg, uint32_t *tableSpaceIndex);
StatusInter MdSpaceDropTableSpace(SeInstanceT *seIns, uint32_t tableSpaceId);
};

TEST_F(UtStorageHeapAM, HeapTableSpaceStat)
{
    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    uint32_t tableSpaceIndex = 0;
    uint32_t tableSpaceId = 3;
    TableSpaceCfgT tableSpaceCfg = {
        .initSize = 4, .stepSize = 4, .maxMemSize = 100, .reserve = 0, .isUseRsm = false, .tableSpaceId = tableSpaceId};
    int32_t ret = MdSpaceCreateTableSpace(sePtr, &tableSpaceCfg, &tableSpaceIndex);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = tableSpaceId,
        .tableSpaceIndex = tableSpaceIndex,  // 指定tableSpace
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    char tupleBuf[100] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0;; ++i) {  // 一直插入数据，直到插满tableSpace
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[origin_insert (Thread %u) (transaction 1) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        if (ret != 0) {
            ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
            break;
        }
    }
    HeapHandleClose(heapHdl1);

    double usedRatio = 0;
    ret = SeGetUserTableSpaceAlmRatio(NULL, &usedRatio);
    ASSERT_EQ(ret, 0);
    ASSERT_EQ(usedRatio, (double)1);  // 使用率达到100%

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    ASSERT_EQ(0, ret);

    ret = MdSpaceDropTableSpace(sePtr, tableSpaceId);
    ASSERT_EQ(ret, 0);
}

TEST_F(UtStorageHeapAM, MaxRowSize)
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    char tupleBuf[LITTLE_LOB_SIZE + 100] = "";
    HpTupleAddr addr;
    memset_s(tupleBuf, sizeof(tupleBuf), 1, sizeof(tupleBuf));
    ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
    ASSERT_EQ(PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE, ret);

    HeapHandleClose(heapHdl1);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageHeapAM, TestTrxtWithVarPageConfirmSize)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    // 开启事务1
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    // 打桩页面整理
    int stubIdx = setStubC((void *)HeapVarPageConfirmSize, (void *)StubHeapVarPageConfirmSize);
    ASSERT_TRUE(stubIdx >= 0);

    // 事务1插入123456
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[100] = "";
    HpTupleAddr addr1;
    sprintf_s(tupleBuf, sizeof(tupleBuf), "123456");
    ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr1);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    // 开启事务2
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx2 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx2, &trxCfg);
    ReadViewPrepare(trx2);
    SeTransSetLabelModifiedActive(g_seRunCtx4);

    // 事务2插入123456
    HpRunHdlT heapHdl2;
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf2[100] = "";
    sprintf_s(tupleBuf2, sizeof(tupleBuf2), "123456");
    HpTupleAddr addr2;
    ret = HeapInsert((HeapRunCtxT *)heapHdl2, (uint8_t *)tupleBuf2, strlen(tupleBuf2) + 1, (HpItemPointerT *)&addr2);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl2);

    // 事务1 更新记录为123
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx3;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf3[100] = "";
    sprintf_s(tupleBuf3, sizeof(tupleBuf3), "123");
    heapTupleBuf.bufSize = strlen(tupleBuf3) + 1;
    heapTupleBuf.buf = (uint8_t *)tupleBuf3;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr1, &out);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    // 事务1提交
    ret = TrxCommit(trx1);
    EXPECT_EQ(0, ret);

    // 事务2 更新记录为456，预期更新成功
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf2 = {0};
    char tupleBuf4[100] = "";
    sprintf_s(tupleBuf4, sizeof(tupleBuf4), "456");
    heapTupleBuf2.bufSize = strlen(tupleBuf4) + 1;
    heapTupleBuf2.buf = (uint8_t *)tupleBuf4;
    ret = HeapUpdate((HeapRunCtxT *)heapHdl2, &heapTupleBuf2, (HpItemPointerT *)&addr2, &out);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl2);

    // 事务2回滚
    ret = TrxRollback(trx2);
    ASSERT_EQ(0, ret);

    clearAllStub();
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    StubClearUndoTransaction(stubIndex);
}

TEST_F(UtStorageHeapAM, TestTrxtWithVarPageConfirmSize2)
{
    PERSISTENCE_NOT_SUPPORT;  // heap_mem和heap对HeapUpdateNormalRow实现不同，该用例针对heap_mem流程定制打桩
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    // 开启事务1
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    // 事务1插入123456
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[100] = "";
    HpTupleAddr addr1;
    sprintf_s(tupleBuf, sizeof(tupleBuf), "123456");
    ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr1);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    // 事务1 更新记录为12345678
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx3;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf3[100] = "";
    sprintf_s(tupleBuf3, sizeof(tupleBuf3), "12345678910123etsdflsekyfgh");
    heapTupleBuf.bufSize = strlen(tupleBuf3) + 1;
    heapTupleBuf.buf = (uint8_t *)tupleBuf3;
    // 打桩页面整理
    g_stubPageCompressTimes = 0;
    int stubIdx = setStubC((void *)HeapVarPageConfirmSize, (void *)StubHeapVarPageConfirmSize2);
    // 页面整理内存不足，超过重试次数之后返回 OUT_OF_MEMORY_INTER
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr1, &out);
    EXPECT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    HeapHandleClose(heapHdl1);

    // 事务1回滚
    ret = TrxRollback(trx1);
    EXPECT_EQ(0, ret);

    // 开启事务2
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx2 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx2, &trxCfg);
    ReadViewPrepare(trx2);
    SeTransSetLabelModifiedActive(g_seRunCtx4);

    // 事务2插入123456
    HpRunHdlT heapHdl2;
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf2[100] = "";
    sprintf_s(tupleBuf2, sizeof(tupleBuf2), "123456");
    HpTupleAddr addr2;
    ret = HeapInsert((HeapRunCtxT *)heapHdl2, (uint8_t *)tupleBuf2, strlen(tupleBuf2) + 1, (HpItemPointerT *)&addr2);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl2);

    // 事务2 更新记录为23456789
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    EXPECT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf2 = {0};
    char tupleBuf4[100] = "";
    sprintf_s(tupleBuf4, sizeof(tupleBuf4), "23456789");
    heapTupleBuf2.bufSize = strlen(tupleBuf4) + 1;
    heapTupleBuf2.buf = (uint8_t *)tupleBuf4;
    // 打桩页面整理
    stubIdx = setStubC((void *)HeapVarPageConfirmSize, (void *)StubHeapVarPageConfirmSize2);
    ret = HeapUpdate((HeapRunCtxT *)heapHdl2, &heapTupleBuf2, (HpItemPointerT *)&addr2, &out);
    // 页面整理内存不足，在重试次数之内成功整理，预期更新成功
    EXPECT_EQ(0, ret);
    clearStub(stubIdx);
    HeapHandleClose(heapHdl2);

    // 事务2提交
    ret = TrxRollback(trx2);
    ASSERT_EQ(0, ret);

    clearAllStub();
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    StubClearUndoTransaction(stubIndex);
}

static const uint32_t MAX_KEY_LEN = 8;
DmVlIndexLabelT g_stubPkIndex = {0};
static char g_data[MAX_KEY_LEN] = "";
IndexKey g_pkHashKey;
static DmVlIndexLabelT g_hashIndexLabel;
ShmemPtrT g_heapShmAddr;
ShmemPtrT g_indexShmAddr;
HeapAccessCfgT g_heapCfg;
HeapRunCtxAllocCfgT g_heapRunCtxAllocCfg = {};
TrxCfgT g_trxCfg;
TrxT *g_trx1;
TrxT *g_trx2;
IndexCtxT *g_pkIdxCtx;
DmSchemaT *g_stubSchema;
DmPropertySchemaT *g_stubProperties;

static IndexMetaCfgT g_pkIdxMetaCfg = {
    .indexId = 123,
    .idxType = HASH_INDEX,
    .realIdxType = HASH_INDEX,
    .idxConstraint = PRIMARY,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

IndexKeyT HashConstructKey(HpTupleAddr addr, uint8_t *keydataPtr, uint32_t keyDataLen)
{
    uint8_t *addrPtr = (uint8_t *)&addr;
    errno_t ret = memcpy_s(keydataPtr, keyDataLen, addrPtr, keyDataLen);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT key = {.keyData = keydataPtr, .keyLen = keyDataLen};
    return key;
}

Status StubKeyCompare(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    uint8_t keydata[MAX_KEY_LEN] = "";
    IndexKeyT existingKey = HashConstructKey(addr, keydata, MAX_KEY_LEN);
    const char *str1 = (const char *)existingKey.keyData;
    const char *str2 = (const char *)hashKey.keyData;
    *isMatch = (memcmp(str1, str2, hashKey.keyLen) == 0);
    *cmpRet = 0;
    return GMERR_OK;
}

Status StubCheckAddr(const Handle heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    return HeapFetchAndCheckExist(heapRunHdl, addr, isExist);
}

Status StubCheckAddrAndFetch(IndexCtxT *idxCtx, const HpRunHdlT heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    return QryLpmCheckAddrAndFetch(idxCtx, heapRunHdl, addr, isExist);
}

static Status UtIndexOpen(IndexOpenCfgT openCfg, ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = openCfg;
    SeRunCtxT *hashRunCtxPtr = openCfg.seRunCtx;
    idxCtx->idxHandle = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(hashRunCtxPtr == nullptr || idxCtx->idxHandle == nullptr)) {
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionCtxT *ctx = &hashRunCtxPtr->resSessionCtx;
    if (ctx->isDirectRead && !IdxIsConstructed(idxCtx->idxHandle)) {
        DB_LOG_DBG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "[SE-lpm] open unconstructed when DirectRead");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    idxCtx->idxMetaCfg = idxCtx->idxHandle->indexCfg;
    return HashIndexOpen(idxCtx);
}

void RollbackMemCtxAllocWithErrInsertDml(HpTupleAddr *addr, SeRunCtxHdT seRunCtx)
{
    HpRunHdlT heapHdl;
    g_heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_heapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&g_heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);

    // 1.开启HeapLabel
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    // 2.开启索引
    ret = IdxAlloc(seRunCtx, g_pkIdxMetaCfg.idxType, &g_pkIdxCtx);
    ASSERT_EQ(0, ret);
    IndexOpenCfgT indexOpenCfg = {.seRunCtx = seRunCtx, .vertex = nullptr, .heapHandle = heapHdl};
    indexOpenCfg.callbackFunc.keyCmp = StubKeyCompare;
    indexOpenCfg.callbackFunc.addrCheck = StubCheckAddr;
    indexOpenCfg.callbackFunc.addrCheckAndFetch = StubCheckAddrAndFetch;
    g_hashIndexLabel.idxLabelBase.indexConstraint = PRIMARY;
    g_hashIndexLabel.idxLabelBase.indexType = HASH_INDEX;
    g_hashIndexLabel.maxKeyLen = MAX_KEY_LEN;
    indexOpenCfg.indexLabel = (DmIndexLabelBaseT *)&g_hashIndexLabel;

    ret = UtIndexOpen(indexOpenCfg, g_indexShmAddr, g_pkIdxCtx);
    ASSERT_EQ(0, ret);
    TupleBufInit(&g_pkIdxCtx->tupleBuf, (DbMemCtxT *)seRunCtx->sessionMemCtx);
    ret = SeSetTrxCtxForIndex((const TrxParamT *)(const void *)&indexOpenCfg);
    ASSERT_EQ(0, ret);

    // 3.插入heap
    ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)g_data, strlen(g_data) + 1, (HpItemPointerT *)addr);
    ASSERT_EQ(0, ret);

    // 4.插入索引
    IndexKeyT hashKey = HashConstructKey(*addr, (uint8_t *)g_data, MAX_KEY_LEN);
    g_pkHashKey = hashKey;
    ret = HashIndexInsert(g_pkIdxCtx, hashKey, (TupleAddr)*addr);
    EXPECT_EQ(GMERR_OK, ret);

    HpTupleAddr addrReturned;
    bool isFound = false;
    ret = HashIndexLookup(g_pkIdxCtx, hashKey, &addrReturned, &isFound);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(*addr, addrReturned);
    uint64_t count = 0;
    ret = HashIndexGetKeyCount(g_pkIdxCtx, hashKey, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);

    HeapHandleClose(heapHdl);
}

void RollbackMemCtxAllocWithErrPrepareLabelAndTrx(
    IsolationLevelE isolationLevel, TrxTypeE trxType, HpTupleAddr *addr, bool isLiteTrx)
{
    int stub1 = setStubC((void *)HeapCreate, (void *)HeapCreateMock);
    EXPECT_GE(stub1, 0);
    int stub2 = setStubC((void *)MdCreateCachedPageList, (void *)MdCreateCachedPageListMock);
    EXPECT_GE(stub2, 0);
    // 1.初始化事务1
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);
    g_heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = trxType,
        .isolation = isolationLevel,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    if (isLiteTrx) {
        g_heapCfg.ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT;
    }

    // 2.创建HeapLabel
    ret = HeapLabelCreate(NULL, &g_heapCfg, &g_heapShmAddr);
    ASSERT_EQ(0, ret);

    // 3.创建主键索引
    ret = HashIndexCreate(g_seRunCtx, g_pkIdxMetaCfg, &g_indexShmAddr);
    ASSERT_EQ(0, ret);

    // 4.写元数据
    DmMetaCommonT metaCommon = {0};
    stubVtxLabel->metaCommon = metaCommon;
    stubVtxLabel->memCtx = (DbMemCtxT *)g_seTopDynCtx;

    uint8_t *buffer = (uint8_t *)stubVtxLabel->metaVertexLabel;
    uint8_t *bufCursor = buffer;
    bufCursor += sizeof(MetaVertexLabelT);
    stubVtxLabel->metaVertexLabel->pkIndex = (DmVlIndexLabelT *)bufCursor;
    stubVtxLabel->metaVertexLabel->pkIndexOffset = (uint32_t)(bufCursor - buffer);
    bufCursor += sizeof(DmVlIndexLabelT);
    stubVtxLabel->metaVertexLabel->schema = (DmSchemaT *)bufCursor;
    stubVtxLabel->metaVertexLabel->schema->propeNum = 1;
    stubVtxLabel->metaVertexLabel->schemaOffset = (uint32_t)(bufCursor - buffer);
    bufCursor += sizeof(DmSchemaT);
    stubVtxLabel->metaVertexLabel->schema->properties = (DmPropertySchemaT *)bufCursor;
    stubVtxLabel->metaVertexLabel->schema->propertiesOffset =
        (uint32_t)(bufCursor - (uint8_t *)stubVtxLabel->metaVertexLabel->schema);

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = g_heapShmAddr;
    stubVtxLabel->memCtx = (DbMemCtxT *)g_seTopDynCtx;
    stubVtxLabel->metaVertexLabel->pkIndex->idxLabelBase.shmAddr = g_indexShmAddr;
    stubVtxLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId = g_pkIdxMetaCfg.indexId;
    stubVtxLabel->metaVertexLabel->pkIndex->maxKeyLen = MAX_KEY_LEN;
    stubVtxLabel->metaVertexLabel->pkIndex->idxLabelBase.indexType = g_pkIdxMetaCfg.idxType;

    // 5.开启事务1
    g_trx1 = (TrxT *)(g_seRunCtx3)->trx;
    g_trxCfg = GetDefaultTrxCfg();
    if (isLiteTrx) {
        g_trxCfg.isolationLevel = READ_UNCOMMITTED;
        g_trxCfg.isLiteTrx = true;
    } else {
        g_trxCfg.trxType = trxType;
        g_trxCfg.isolationLevel = isolationLevel;
    }
    ret = TrxBegin(g_trx1, &g_trxCfg);
    ReadViewPrepare(g_trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);

    // 6.事务1插入
    RollbackMemCtxAllocWithErrInsertDml(addr, g_seRunCtx3);

    // 7.初始化事务2
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    g_trx2 = (TrxT *)(g_seRunCtx4)->trx;
}

void RollbackMemCtxAllocWithErrReleaseLabelAndTrx()
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    int32_t ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    HashIndexClose(g_pkIdxCtx);
    IdxRelease(g_pkIdxCtx);
    HashIndexDrop(g_seRunCtx, g_indexShmAddr);
    free(g_stubSchema);
    g_stubSchema = NULL;
    free(g_stubProperties);
    g_stubProperties = NULL;
}

Status UtStubHeapAmVertexExtraKey(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapConstBufT *serialBuf,
    uint32_t indexId, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    if (indexId == g_pkIdxMetaCfg.indexId) {
        buf->bufSize = MAX_KEY_LEN;
        buf->buf = (uint8_t *)g_data;
    }
    return GMERR_OK;
}

Status UtStubDmGetKeyBufFromVertexWithKey(const DmVertexT *vertex, uint32_t indexId, uint8_t **keyBuf, uint32_t *length)
{
    DB_UNUSED(vertex);
    if (indexId == g_pkIdxMetaCfg.indexId) {
        *length = MAX_KEY_LEN;
        *keyBuf = (uint8_t *)g_data;
    }
    return GMERR_OK;
}

Status UtStubDmDeSerialize2ExistsVertex(uint8_t *buf, uint32_t length, DmVertexT *vertex, bool checkFlag)
{
    return GMERR_OK;
}

void TestTrxtRollbackMemCtxAllocWithErrInsert(
    IsolationLevelE isolationLevel, TrxTypeE trxType, bool isLiteTrx, bool isRollback)
{
    int stubIdx0 = setStub(HeapAmVertexExtraKeyBySerialBuf, UtStubHeapAmVertexExtraKey);
    ASSERT_TRUE(stubIdx0 >= 0);
    stubIdx0 = setStub(DmGetKeyBufFromVertex, UtStubDmGetKeyBufFromVertexWithKey);
    ASSERT_TRUE(stubIdx0 >= 0);
    stubIdx0 = setStub(DmDeSerialize2ExistsVertex, UtStubDmDeSerialize2ExistsVertex);
    ASSERT_TRUE(stubIdx0 >= 0);

    HpTupleAddr addr;
    RollbackMemCtxAllocWithErrPrepareLabelAndTrx(isolationLevel, trxType, &addr, isLiteTrx);

    // 打桩内存申请
    g_stubDbDynMemCtxAllocCallCount = 0;
    int stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocCount);
    ASSERT_TRUE(stubIdx >= 0);

    int32_t ret = 0;
    // 事务1回滚/提交
    if (isRollback) {
        ret = TrxRollback(g_trx1);
    } else {
        ret = TrxCommit(g_trx1);
    }

    EXPECT_EQ(0, ret);
    clearStub(stubIdx);

    for (int i = 0; i < g_stubDbDynMemCtxAllocCallCount; i++) {
        g_stubDbDynMemCtxAllocCallWithErr = i;
        g_stubDbDynMemCtxAllocCallIndex = 0;

        // 不断循环重新开启事务2
        ret = TrxBegin(g_trx2, &g_trxCfg);
        ReadViewPrepare(g_trx2);
        SeTransSetLabelModifiedActive(g_seRunCtx4);

        // 事务2插入
        HpTupleAddr addr2;
        RollbackMemCtxAllocWithErrInsertDml(&addr2, g_seRunCtx4);

        // 打桩内存申请模拟不同位置的内存申请报错
        stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocWithErr);
        ASSERT_TRUE(stubIdx >= 0);

        // 事务2回滚/提交
        if (isRollback) {
            ret = TrxRollback(g_trx2);
        } else {
            ret = TrxCommit(g_trx2);
        }
        ASSERT_EQ(0, ret);
        clearStub(stubIdx);
    }
    clearAllStub();
    RollbackMemCtxAllocWithErrReleaseLabelAndTrx();
}

void TestTrxtRollbackMemCtxAllocWithErrDelete(IsolationLevelE isolationLevel, TrxTypeE trxType, bool isRollback)
{
    int stubIdx0 = setStub(HeapAmVertexExtraKeyBySerialBuf, UtStubHeapAmVertexExtraKey);
    ASSERT_TRUE(stubIdx0 >= 0);
    stubIdx0 = setStub(DmGetKeyBufFromVertex, UtStubDmGetKeyBufFromVertexWithKey);
    ASSERT_TRUE(stubIdx0 >= 0);
    stubIdx0 = setStub(DmDeSerialize2ExistsVertex, UtStubDmDeSerialize2ExistsVertex);
    ASSERT_TRUE(stubIdx0 >= 0);

    HpTupleAddr addr;
    RollbackMemCtxAllocWithErrPrepareLabelAndTrx(isolationLevel, trxType, &addr, false);

    // 事务1提交
    int32_t ret = TrxCommit(g_trx1);
    EXPECT_EQ(0, ret);

    // 重新开启事务1并删除记录
    ret = TrxBegin(g_trx1, &g_trxCfg);
    ReadViewPrepare(g_trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);
    HpRunHdlT heapHdl1;
    ret = HeapLabelAllocAndInitRunctx(&g_heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    ret = HeapDelete((HeapRunCtxT *)heapHdl1, *(HpItemPointerT *)&addr, true);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = HashIndexDelete(g_pkIdxCtx, g_pkHashKey, addr, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    // 打桩内存申请
    g_stubDbDynMemCtxAllocCallCount = 0;
    int stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocCount);
    ASSERT_TRUE(stubIdx >= 0);

    // 事务1回滚/提交
    if (isRollback) {
        ret = TrxRollback(g_trx1);
    } else {
        ret = TrxCommit(g_trx1);
    }
    EXPECT_EQ(0, ret);
    clearStub(stubIdx);

    for (int i = 0; i < g_stubDbDynMemCtxAllocCallCount; i++) {
        g_stubDbDynMemCtxAllocCallWithErr = i;
        g_stubDbDynMemCtxAllocCallIndex = 0;

        // 不断循环重新开启事务2
        ret = TrxBegin(g_trx2, &g_trxCfg);
        ReadViewPrepare(g_trx2);
        SeTransSetLabelModifiedActive(g_seRunCtx4);

        // 事务2删除记录
        if (!isRollback) {
            // 如果先前提交过，需要重新插入
            HpTupleAddr addr2;
            RollbackMemCtxAllocWithErrInsertDml(&addr2, g_seRunCtx4);
            addr = addr2;
            ret = TrxCommit(g_trx2);
            ASSERT_EQ(0, ret);
            ret = TrxBegin(g_trx2, &g_trxCfg);
            ReadViewPrepare(g_trx2);
            SeTransSetLabelModifiedActive(g_seRunCtx4);
        }
        HpRunHdlT heapHdl2;
        g_heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
        ret = HeapLabelAllocAndInitRunctx(&g_heapRunCtxAllocCfg, &heapHdl2);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
        ASSERT_EQ(0, ret);
        ret = HeapDelete((HeapRunCtxT *)heapHdl2, *(HpItemPointerT *)&addr, true);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl2);

        IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
        ret = HashIndexDelete(g_pkIdxCtx, g_pkHashKey, addr, removePara);
        EXPECT_EQ(GMERR_OK, ret);

        // 打桩内存申请模拟不同位置的内存申请报错
        stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocWithErr);
        ASSERT_TRUE(stubIdx >= 0);

        // 事务2回滚/提交
        if (isRollback) {
            ret = TrxRollback(g_trx2);
        } else {
            ret = TrxCommit(g_trx2);
        }
        ASSERT_EQ(0, ret);
        clearStub(stubIdx);
    }
    clearAllStub();
    RollbackMemCtxAllocWithErrReleaseLabelAndTrx();
}

void TestTrxtRollbackMemCtxAllocWithErrUpdate(
    IsolationLevelE isolationLevel, TrxTypeE trxType, bool isLiteTrx, bool isRollback)
{
    int stubIdx0 = setStub(HeapAmVertexExtraKeyBySerialBuf, UtStubHeapAmVertexExtraKey);
    ASSERT_TRUE(stubIdx0 >= 0);
    stubIdx0 = setStub(DmGetKeyBufFromVertex, UtStubDmGetKeyBufFromVertexWithKey);
    ASSERT_TRUE(stubIdx0 >= 0);
    stubIdx0 = setStub(DmDeSerialize2ExistsVertex, UtStubDmDeSerialize2ExistsVertex);
    ASSERT_TRUE(stubIdx0 >= 0);

    HpTupleAddr addr;
    RollbackMemCtxAllocWithErrPrepareLabelAndTrx(isolationLevel, trxType, &addr, isLiteTrx);

    // 事务1提交
    int32_t ret = TrxCommit(g_trx1);
    EXPECT_EQ(0, ret);

    // 重新开启事务1并更新记录
    ret = TrxBegin(g_trx1, &g_trxCfg);
    ReadViewPrepare(g_trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);
    HpRunHdlT heapHdl1;
    ret = HeapLabelAllocAndInitRunctx(&g_heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf1[100] = "";
    sprintf_s(tupleBuf1, sizeof(tupleBuf1), "123");
    heapTupleBuf.bufSize = strlen(tupleBuf1) + 1;
    heapTupleBuf.buf = (uint8_t *)tupleBuf1;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr, &out);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    IndexKeyT delHashKey = g_pkHashKey;
    g_pkHashKey = HashConstructKey(addr, (uint8_t *)g_data, MAX_KEY_LEN);
    IndexUpdateInfoT updateInfo = {.oldIdxKey = delHashKey, .newIdxKey = g_pkHashKey, .oldAddr = addr, .newAddr = addr};
    ret = HashIndexUpdate(g_pkIdxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    // 打桩内存申请
    g_stubDbDynMemCtxAllocCallCount = 0;

    // 事务1回滚/提交
    if (isRollback) {
        ret = TrxRollback(g_trx1);
    } else {
        ret = TrxCommit(g_trx1);
    }
    EXPECT_EQ(0, ret);

    for (int i = 0; i < g_stubDbDynMemCtxAllocCallCount; i++) {
        g_stubDbDynMemCtxAllocCallWithErr = i;
        g_stubDbDynMemCtxAllocCallIndex = 0;

        // 不断重新开启事务1并更新记录
        if (!isRollback) {
            // 如果先前提交过，需要重新插入
            HpTupleAddr addr2;
            ret = TrxBegin(g_trx1, &g_trxCfg);
            ReadViewPrepare(g_trx1);
            SeTransSetLabelModifiedActive(g_seRunCtx3);
            RollbackMemCtxAllocWithErrInsertDml(&addr2, g_seRunCtx3);
            addr = addr2;
            ret = TrxCommit(g_trx1);
            ASSERT_EQ(0, ret);
        }
        ret = TrxBegin(g_trx1, &g_trxCfg);
        ReadViewPrepare(g_trx1);
        SeTransSetLabelModifiedActive(g_seRunCtx3);
        HpRunHdlT heapHdl1;
        ret = HeapLabelAllocAndInitRunctx(&g_heapRunCtxAllocCfg, &heapHdl1);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
        ASSERT_EQ(0, ret);
        HeapTupleBufT heapTupleBuf = {0};
        char tupleBuf2[100] = "";
        sprintf_s(tupleBuf2, sizeof(tupleBuf2), "123");
        heapTupleBuf.bufSize = strlen(tupleBuf2) + 1;
        heapTupleBuf.buf = (uint8_t *)tupleBuf2;
        ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr, &out);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl1);

        IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
        IndexKeyT delHashKey = g_pkHashKey;
        g_pkHashKey = HashConstructKey(addr, (uint8_t *)g_data, MAX_KEY_LEN);
        IndexUpdateInfoT updateInfo = {
            .oldIdxKey = delHashKey, .newIdxKey = g_pkHashKey, .oldAddr = addr, .newAddr = addr};
        ret = HashIndexUpdate(g_pkIdxCtx, updateInfo, removePara);
        EXPECT_EQ(GMERR_OK, ret);

        // 事务1回滚/提交
        if (isRollback) {
            ret = TrxRollback(g_trx1);
        } else {
            ret = TrxCommit(g_trx1);
        }
        ASSERT_EQ(0, ret);
    }
    ret = TrxRollback(g_trx2);
    ASSERT_EQ(0, ret);
    clearAllStub();
    RollbackMemCtxAllocWithErrReleaseLabelAndTrx();
}

// RR+乐观事务 insert
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr1)
{
    TestTrxtRollbackMemCtxAllocWithErrInsert(REPEATABLE_READ, OPTIMISTIC_TRX, false, true);
    TestTrxtRollbackMemCtxAllocWithErrInsert(REPEATABLE_READ, OPTIMISTIC_TRX, false, false);
}

// RR+乐观事务 delete
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr2)
{
    TestTrxtRollbackMemCtxAllocWithErrDelete(REPEATABLE_READ, OPTIMISTIC_TRX, true);
    TestTrxtRollbackMemCtxAllocWithErrDelete(REPEATABLE_READ, OPTIMISTIC_TRX, false);
}

// RR+乐观事务 update
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr3)
{
    TestTrxtRollbackMemCtxAllocWithErrUpdate(REPEATABLE_READ, OPTIMISTIC_TRX, false, true);
    TestTrxtRollbackMemCtxAllocWithErrUpdate(REPEATABLE_READ, OPTIMISTIC_TRX, false, false);
}

// RC+悲观事务 insert
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr4)
{
    TestTrxtRollbackMemCtxAllocWithErrInsert(READ_COMMITTED, PESSIMISTIC_TRX, false, true);
    TestTrxtRollbackMemCtxAllocWithErrInsert(READ_COMMITTED, PESSIMISTIC_TRX, false, false);
}

// RC+悲观事务 delete
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr5)
{
    TestTrxtRollbackMemCtxAllocWithErrDelete(READ_COMMITTED, PESSIMISTIC_TRX, true);
    TestTrxtRollbackMemCtxAllocWithErrDelete(READ_COMMITTED, PESSIMISTIC_TRX, false);
}

// RC+悲观事务 update
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr6)
{
    TestTrxtRollbackMemCtxAllocWithErrUpdate(READ_COMMITTED, PESSIMISTIC_TRX, false, true);
    TestTrxtRollbackMemCtxAllocWithErrUpdate(READ_COMMITTED, PESSIMISTIC_TRX, false, false);
}

// 轻量化事务 insert
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr7)
{
    TestTrxtRollbackMemCtxAllocWithErrInsert(REPEATABLE_READ, PESSIMISTIC_TRX, true, true);
    TestTrxtRollbackMemCtxAllocWithErrInsert(REPEATABLE_READ, PESSIMISTIC_TRX, true, false);
}

// 轻量化事务 update
TEST_F(UtStorageHeapAM, TrxtRollbackMemCtxAllocWithErr8)
{
    TestTrxtRollbackMemCtxAllocWithErrUpdate(REPEATABLE_READ, PESSIMISTIC_TRX, true, true);
    TestTrxtRollbackMemCtxAllocWithErrUpdate(REPEATABLE_READ, PESSIMISTIC_TRX, true, false);
}

TEST_F(UtStorageHeapAM, TestTrxUndoRoolback)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    // 开启事务1
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    // 事务1先插入
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[100] = "";
    HpTupleAddr addr;
    sprintf_s(tupleBuf, sizeof(tupleBuf), "how are you?!");
    ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    // 事务1提交
    ret = TrxCommit(trx1);
    EXPECT_EQ(0, ret);

    // 重新开启事务1并更新
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx3);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf1[100] = "";
    sprintf_s(tupleBuf1, sizeof(tupleBuf1), "how are you?");
    heapTupleBuf.bufSize = strlen(tupleBuf1) + 1;
    heapTupleBuf.buf = (uint8_t *)tupleBuf1;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr, &out);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl1);

    // 开启事务2
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx2 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx2, &trxCfg);
    ReadViewPrepare(trx2);
    SeTransSetLabelModifiedActive(g_seRunCtx4);

    // 事务2更新
    HpRunHdlT heapHdl2;
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx4;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf2 = {0};
    char tupleBuf2[100] = "";
    sprintf_s(tupleBuf2, sizeof(tupleBuf2), "hello world");
    heapTupleBuf2.bufSize = strlen(tupleBuf2) + 1;
    heapTupleBuf2.buf = (uint8_t *)tupleBuf2;
    ret = HeapUpdate((HeapRunCtxT *)heapHdl2, &heapTupleBuf2, (HpItemPointerT *)&addr, &out);
    ASSERT_EQ(0, ret);
    HeapHandleClose(heapHdl2);

    // 事务1回滚
    ret = TrxRollback(trx1);
    EXPECT_EQ(0, ret);

    // 事务2回滚
    ret = TrxRollback(trx2);
    ASSERT_EQ(0, ret);

    // 回滚完后校验数据的正确性
    HpRunHdlT heapHdl;
    SeTransSetLabelModifiedActive(g_seRunCtx);
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&addr, HeapFetchHpTupleWithStrcmp, (void *)tupleBuf);
    EXPECT_EQ(0, ret);
    HeapHandleClose(heapHdl);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    StubClearUndoTransaction(stubIndex);
}

// 测试fixHeap的跳转行回滚逻辑
TEST_F(UtStorageHeapAM, TestTrxUndoRoolback_fixLinkRow)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    // 开启事务1
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 15000,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_UNCOMMITTED,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.isLiteTrx = true;
    trxCfg.isolationLevel = READ_UNCOMMITTED;
    ret = TrxBegin(trx1, &trxCfg);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    // 事务1先插入
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[15000] = "";
    uint32_t insertNum = 2;
    HpTupleAddr addr[insertNum] = {0};
    sprintf_s(tupleBuf, sizeof(tupleBuf), "how are you?!");
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, sizeof(tupleBuf), (HpItemPointerT *)&addr[i]);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl1);

    // 事务1提交
    ret = TrxCommit(trx1);
    EXPECT_EQ(0, ret);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, 16000);  // 表结构升级，构造fix的跳转行
    EXPECT_EQ(0, ret);

    // 重新开启事务1并更新
    ret = TrxBegin(trx1, &trxCfg);
    SeTransSetDmlUndoLiteSize(g_seRunCtx3, 10, true);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf1[16000] = "";
    sprintf_s(tupleBuf1, sizeof(tupleBuf1), "how are you?");
    heapTupleBuf.bufSize = sizeof(tupleBuf1);
    heapTupleBuf.buf = (uint8_t *)tupleBuf1;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (uint32_t j = 0; j < 2; j++) {  // 更新2次，构造跳转行->跳转行的场景，测试回滚流程
        for (uint32_t i = 0; i < insertNum; i++) {
            ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr[i], &out);
            ASSERT_EQ(0, ret);
        }
    }
    HeapHandleClose(heapHdl1);

    // 事务1回滚
    ret = TrxRollback(trx1);
    ASSERT_EQ(0, ret);

    ret = TrxBegin(trx1, &trxCfg);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl1, *(HpItemPointerT *)&addr[i], false);
        ASSERT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl1);

    // 事务1回滚
    ret = TrxCommit(trx1);
    ASSERT_EQ(0, ret);
    // 删除数据后，预期不占用heap页，看护回滚流程处理fixDstLinkRow是否正确
    EXPECT_EQ(((HeapRunCtxT *)heapHdl1)->heap->fsm.usedPageCnt, 0u);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    StubClearUndoTransaction(stubIndex);
}

uint32_t GetErrorNumInLog()
{
    // 查看日志中的error数量
    FILE *fp = popen("cat ./log/run/rut_storage/* | grep ERROR | wc -l", "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    return atoi(errorNum);
}

// 测试fixHeap的跳转行回滚,误设置freeSize，导致错误归还页的问题
TEST_F(UtStorageHeapAM, TestTrxUndoRoolback_fixLinkRow_freeSize)
{
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    // 开启事务1
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 32,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_UNCOMMITTED,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    TrxT *trx1 = (TrxT *)(g_seRunCtx3)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.isLiteTrx = true;
    trxCfg.isolationLevel = READ_UNCOMMITTED;
    ret = TrxBegin(trx1, &trxCfg);
    ASSERT_EQ(0, ret);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx3,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    SeTransSetDmlHint4RangeUpdate(g_seRunCtx3);  // 扩展轻量化事务的undo，非正确用法
    // 事务1先插入
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    char tupleBuf[32] = "";
    uint32_t insertNum = 582;
    HpTupleAddr addr[insertNum] = {0};
    sprintf_s(tupleBuf, sizeof(tupleBuf), "how are you?!");
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, sizeof(tupleBuf), (HpItemPointerT *)&addr[i]);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl1);

    // 事务1提交
    ret = TrxCommit(trx1);
    EXPECT_EQ(0, ret);
    HeapCntrAcsInfoT heapCntrAcsInfo1 = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, 40);  // 表结构升级，构造fix的跳转行
    EXPECT_EQ(0, ret);

    // 重新开启事务1并更新
    ret = TrxBegin(trx1, &trxCfg);
    ASSERT_EQ(0, ret);
    SeTransSetDmlUndoLiteSize(g_seRunCtx3, 10, true);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf1[40] = "";
    sprintf_s(tupleBuf1, sizeof(tupleBuf1), "how are you?");
    heapTupleBuf.bufSize = sizeof(tupleBuf1);
    heapTupleBuf.buf = (uint8_t *)tupleBuf1;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr[i], &out);
        ASSERT_EQ(0, ret);
    }
    HeapHandleClose(heapHdl1);
    ret = TrxCommit(trx1);
    ASSERT_EQ(0, ret);

    ret = TrxBegin(trx1, &trxCfg);
    ASSERT_EQ(0, ret);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);
    for (uint32_t i = insertNum - 1; i >= 1; i--) {  // 预留1条不删，构造freeSize接近全空的情况
        ret = HeapDelete((HeapRunCtxT *)heapHdl1, *(HpItemPointerT *)&addr[i], false);
        ASSERT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl1);
    ret = TrxCommit(trx1);
    ASSERT_EQ(0, ret);
    HeapCntrAcsInfoT heapCntrAcsInfo2 = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo2, 64);
    EXPECT_EQ(0, ret);

    ret = TrxBegin(trx1, &trxCfg);
    ASSERT_EQ(0, ret);
    SeTransSetDmlHint4RangeUpdate(g_seRunCtx3);  // 扩展轻量化事务的undo，非正确用法
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    char tupleBuf2[64] = "";
    sprintf_s(tupleBuf1, sizeof(tupleBuf2), "how are you1111?");
    heapTupleBuf.bufSize = sizeof(tupleBuf2);
    heapTupleBuf.buf = (uint8_t *)tupleBuf2;
    HpTupleAddr addr2[insertNum] = {0};
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf2, sizeof(tupleBuf2), (HpItemPointerT *)&addr2[i]);
        ASSERT_EQ(0, ret);
    }

    HeapResetOpType(heapHdl1, HEAP_OPTYPE_DELETE, false);
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl1, *(HpItemPointerT *)&addr2[i], false);
        ASSERT_EQ(0, ret);
    }

    HeapHandleClose(heapHdl1);
    ret = TrxCommit(trx1);
    ASSERT_EQ(0, ret);

    uint32_t errNumBeforeTest = GetErrorNumInLog();
    for (uint32_t j = 0; j < 4; j++) {
        ret = TrxBegin(trx1, &trxCfg);
        ASSERT_EQ(0, ret);
        SeTransSetDmlUndoLiteSize(g_seRunCtx3, 10, true);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
        ASSERT_EQ(0, ret);
        ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr[0], &out);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl1);
        ret = TrxRollback(trx1);
        ASSERT_EQ(0, ret);
    }

    uint32_t errNumAfterTest = GetErrorNumInLog();
    EXPECT_EQ(errNumAfterTest, errNumBeforeTest);  // 预期回滚流程，fsm不会有错误日志产生

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    StubClearUndoTransaction(stubIndex);
}

TEST_F(UtStorageHeapAM, OptiTrxtWithReadViewCheck)
{
    int32_t ret = TrxRollback((TrxT *)(g_seRunCtx)->trx);
    EXPECT_EQ(GMERR_OK, ret);
    vector<int> stubIndex;
    StubSetUndoTransaction(stubIndex);
    // 开启事务1, 构造老版本事务
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx2);
    ASSERT_EQ(ret, 0);
    TrxT *trx1 = (TrxT *)(g_seRunCtx2)->trx;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    trxCfg.readOnly = true;
    ret = TrxBegin(trx1, &trxCfg);
    ReadViewPrepare(trx1);
    SeTransSetLabelModifiedActive(g_seRunCtx2);

    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    // 开启事务2
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx3);
    ASSERT_EQ(ret, 0);

    TrxT *trx2 = (TrxT *)(g_seRunCtx3)->trx;
    trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    HpTupleAddr addr1;
    for (uint32_t i = 0; i < 10; i++) {
        ret = TrxBegin(trx2, &trxCfg);
        ReadViewPrepare(trx2);
        SeTransSetLabelModifiedActive(g_seRunCtx3);

        HpRunHdlT heapHdl1;
        HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
            .heapShmAddr = heapShmAddr,
            .seRunCtx = g_seRunCtx3,
            .dmInfo = stubVtxLabel,
            .isBackGround = false};

        // 事务1插入123456
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
        ASSERT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
        ASSERT_EQ(0, ret);
        char tupleBuf[100] = "";
        sprintf_s(tupleBuf, sizeof(tupleBuf), "123456");
        ret = HeapInsert((HeapRunCtxT *)heapHdl1, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr1);
        ASSERT_EQ(0, ret);
        HeapHandleClose(heapHdl1);

        // 事务1提交
        ret = TrxCommit(trx2);
        EXPECT_EQ(0, ret);
    }

    // 开启事务3
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx4);
    ASSERT_EQ(ret, 0);
    TrxT *trx3 = (TrxT *)(g_seRunCtx4)->trx;
    ret = TrxBegin(trx3, &trxCfg);
    ReadViewPrepare(trx3);
    SeTransSetLabelModifiedActive(g_seRunCtx4);

    HpRunHdlT heapHdl1;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = g_seRunCtx4,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};

    // 事务3更新
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl1);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl1, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)g_seTopDynCtx);
    ASSERT_EQ(0, ret);

    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf1[160] = "";
    sprintf_s(tupleBuf1, sizeof(tupleBuf1), "how are you?");
    heapTupleBuf.bufSize = sizeof(tupleBuf1);
    heapTupleBuf.buf = (uint8_t *)tupleBuf1;
    bool isUndoBypass = false;
    HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
    ret = HeapUpdate((HeapRunCtxT *)heapHdl1, &heapTupleBuf, (HpItemPointerT *)&addr1, &out);
    ASSERT_EQ(0, ret);

    HeapHandleClose(heapHdl1);

    clearAllStub();
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    StubClearUndoTransaction(stubIndex);
}

StatusInter UtStubHeapPageAddLatch(HeapRunCtxT *ctx, const HeapRowInfoT *curRowInfo)
{
    return STATUS_OK_INTER;
}
StatusInter UtStubHeapPageDeleteRow(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo)
{
    return STATUS_OK_INTER;
}
void UtStubSePgLatchSorterReset(SeRunCtxT *seRunCtx, SePgLatchSorterT *pageSorter)
{
    return;
}

extern "C" StatusInter HeapPageDeleteRowImpl(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo);
// 保证在HeapAcquireRowLockForInsertFailedClear不错误释放表锁
TEST_F(UtStorageHeapAM, TestHeapAcquireRowLockForInsertFailedClear)
{
    HeapRunCtxT ctx;
    ctx.hpControl.isNeedRowLock = false;
    SeRunCtxT seRunCtx = {0};
    ctx.seRunCtx = &seRunCtx;
    TrxT trx;
    trx.trx.pesTrx.lockAcqInfo.isNewAllocLock = true;
    trx.trx.pesTrx.holdLockAcqId = 0;
    ctx.seRunCtx->trx = &trx;
    ctx.hpControl.isPageReadOnly = false;

    SeLockAcqPoolT lockAcqPool = {0};
    lockAcqPool.poolEntryNum = 1;
    lockAcqPool.acqEntry[0] = {0};
    SeLockT lock = {0};
    lockAcqPool.acqEntry[0].acqInfo.lock = &lock;
    lockAcqPool.acqEntry[0].acqInfo.lock->lockId.lockType = SE_LOCK_TYPE_LABEL_LOCK;
    lockAcqPool.acqEntry[0].acqInfo.lockMode = SE_LOCK_MODE_S;
    seRunCtx.lockAqcPool = &lockAcqPool;
    HpPageRowOpInfoT opInfo = {0};
    HpPageAllocRowInfoT pageAllocRowInfo = {0};
    opInfo.allocRowInfo = &pageAllocRowInfo;
    opInfo.allocRowInfo->sliceRowInfo.isSliceRow = false;

    setStub(HeapPageAddLatch, UtStubHeapPageAddLatch);
    setStub(HeapPageDeleteRowImpl, UtStubHeapPageDeleteRow);
    setStub(SePgLatchSorterReset, UtStubSePgLatchSorterReset);

    ASSERT_NO_FATAL_FAILURE(HeapAcquireRowLockForInsertFailedClear(&ctx, &opInfo));
}

#ifdef FEATURE_GQL  // 目前只有gql使用无行锁的RR
ShmemPtrT g_FixHeapShmAddrNRLPessRRTrx;
DmVertexLabelT *stubVtxLabelNRLPessRRTrx = NULL;

// 创建一个无行锁悲观可重复读的表
void UtHeapAMBasicCreateFixLabelNRLPessRRTrx(int fixRowLen, int slotExtend)
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = static_cast<PageSizeT>(fixRowLen),
        .slotExtendSize = static_cast<PageSizeT>(slotExtend),
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
        .skipRowLockPessimisticRR = true,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_FixHeapShmAddrNRLPessRRTrx = heapShmAddr;
    ShmemPtrT shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabelNRLPessRRTrx, &shmPtr, sizeof(DmVertexLabelT));
    memset_s(stubVtxLabelNRLPessRRTrx, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabelNRLPessRRTrx->commonInfo,
        &stubVtxLabelNRLPessRRTrx->commonInfoShmPtr, sizeof(VertexLabelCommonInfoT));
    stubVtxLabelNRLPessRRTrx->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
}
void UtHeapAMBasicDropFixLabelNRLPessRRTrx()
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = g_FixHeapShmAddrNRLPessRRTrx,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    int32_t ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddrNRLPessRRTrx = DB_INVALID_SHMPTR;
}

/*
测试点，无锁RR下，设置getMasterBuf为true，读取其他未提交事务已删除的tuple，期望能读取到
*/
TEST_F(UtStorageHeapAM, HeapGetMasterBuf1InNonRowLockPessimisticRRTrx)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    void *usrMemCtx = g_seTopDynCtx;
    int tupleSize = 100;
    UtHeapAMBasicCreateFixLabelNRLPessRRTrx(tupleSize, 16);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = PESSIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    trxCfg.skipRowLockPessimisticRR = true;
    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);

    HpRunHdlT heapHdl, heapHdl2;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_FixHeapShmAddrNRLPessRRTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabelNRLPessRRTrx,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;  // 对里面的add执行删除

    char tupleBuf[FIX_ROW_LEN] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t i = 0;
    for (auto &heapAddr : heapItemList1) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx2);
    TrxT *trx2 = (TrxT *)(g_seRunCtx2)->trx;  // 开启事务1
    ret = TrxBegin(trx2, &trxCfg);
    ASSERT_EQ(0, ret);
    ReadViewPrepare(trx2);
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx2;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    i = 0;
    char tmpTupleBuf[FIX_ROW_LEN] = "";
    for (auto &heapAddr : heapItemList1) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl2, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(0, ret);
        EXPECT_STREQ(tmpTupleBuf, tupleBuf);  // 能读取到其他未提交事务已删除的数据
        i++;
    }
    // 提交事务1
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    // 提交事务2
    HeapHandleClose(heapHdl2);
    ret = TrxCommit(trx2);
    EXPECT_EQ(GMERR_OK, ret);
    // 再次开启事务2，检查已提交事务删除的数据，期望读取不到
    ret = TrxBegin(trx2, &trxCfg);
    ASSERT_EQ(0, ret);
    ReadViewPrepare(trx2);
    heapRunCtxAllocCfg.seRunCtx = g_seRunCtx2;
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl2);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl2, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    i = 0;
    for (auto &heapAddr : heapItemList1) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapFetch(
            (HeapRunCtxT *)heapHdl2, *(HpItemPointerT *)&heapAddr, UtHeapGetOldestVisibleTuple, (void *)&tmpTupleBuf);
        EXPECT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
        i++;
    }
    HeapHandleClose(heapHdl2);
    ret = TrxCommit(trx2);
    EXPECT_EQ(GMERR_OK, ret);
    UtHeapAutoCommitAndBegin(g_seRunCtx2);
    UtHeapAutoCommitAndBegin(g_seRunCtx);
    UtHeapAMBasicDropFixLabelNRLPessRRTrx();
}
/*
测试点，无锁RR下，设置getMasterBuf为true，读取其他未提交事务已删除的tuple，期望能读取到
*/
TEST_F(UtStorageHeapAM, HeapInitVarLabelFailedInNonRowLockPessimisticRRTrx)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
        .skipRowLockPessimisticRR = true,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = MAX_ITEM_NUM,
    };
    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_FixHeapShmAddrNRLPessRRTrx = heapShmAddr;
    ShmemPtrT shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabelNRLPessRRTrx, &shmPtr, sizeof(DmVertexLabelT));
    memset_s(stubVtxLabelNRLPessRRTrx, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabelNRLPessRRTrx->commonInfo,
        &stubVtxLabelNRLPessRRTrx->commonInfoShmPtr, sizeof(VertexLabelCommonInfoT));
    stubVtxLabelNRLPessRRTrx->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = PESSIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    trxCfg.skipRowLockPessimisticRR = true;
    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);

    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_FixHeapShmAddrNRLPessRRTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabelNRLPessRRTrx,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(1015000, ret);

    UtHeapAutoCommitAndBegin(g_seRunCtx);
    UtHeapAMBasicDropFixLabelNRLPessRRTrx();
}
#endif

// 轻量化事务直连读heap_mem场景用缓存减少访问页头性能优化场景
#ifndef FEATURE_SIMPLEREL
StatusInter UtHeapGetTupleLite(HpReadRowInfoT *readRowInfo, void *userData)
{
    return (StatusInter)memcpy_s(userData, readRowInfo->bufSize, readRowInfo->buf, readRowInfo->bufSize);
}

TEST_F(UtStorageHeapAM, TestHeapFetchLiteFix)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    const int v1FixLen = FIX_ROW_LEN;
    char fixItem[v1FixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(v1FixLen, HeapGetHcSlotSpace(1));
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    const int insertCnt = 15;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    HpTupleAddr addr;
    vector<HpTupleAddr> heapItemList1;
    for (int i = 0; i < insertCnt; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)fixItem, v1FixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);
    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    char tmp[200] = "";
    heapHdl->hpControl.accessMethodType = HEAP_ACCESS_METHOD_LITE;
    for (HpTupleAddr &heapAddr : heapItemList1) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetTupleLite, (void *)&tmp);
        ((HeapRunCtxT *)heapHdl)->staticPageInfo.isUseCache = true;
        ASSERT_EQ(0, ret);
        EXPECT_STREQ(tmp, fixItem);
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

TEST_F(UtStorageHeapAM, TestHeapFetchLiteFixWithUpGrade)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    const int v1FixLen = 100;
    const int newFixLen = 200;
    char fixItem[v1FixLen] = "efg";
    UtHeapAMBasicCreateFixLabel(v1FixLen, HeapGetHcSlotSpace(1));
    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);
    const int insertCnt = 15;
    auto heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_INSERT);
    ASSERT_NE(nullptr, heapHdl);
    HpTupleAddr addr;
    vector<HpTupleAddr> heapItemList1;
    for (int i = 0; i < insertCnt; i++) {
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)fixItem, v1FixLen, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo1 = {.heapShmAddr = stubVtxLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo1, newFixLen);
    EXPECT_EQ(0, ret);
    UtHeapAutoCommitAndBegin(seRunCtx);

    heapHdl = UtHeapOpen(seRunCtx, HEAP_OPTYPE_NORMALREAD);
    ASSERT_NE(nullptr, heapHdl);
    char tmp[200] = "";
    heapHdl->hpControl.accessMethodType = HEAP_ACCESS_METHOD_LITE;
    heapHdl->staticPageInfo.isUseCache = false;
    for (HpTupleAddr &heapAddr : heapItemList1) {
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetTupleLite, (void *)&tmp);
        ASSERT_EQ(0, ret);
        EXPECT_STREQ(tmp, fixItem);
    }

    HeapHandleClose(heapHdl);
    UtHeapAutoCommitAndBegin(seRunCtx);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_FixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelDrop(g_seRunCtx, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_FixHeapShmAddr = DB_INVALID_SHMPTR;
}

TEST_F(UtStorageHeapAM, TestHeapFetchLiteVar)
{
    int32_t ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    UtHeapAMBasicCreateLabelOptiTrx();

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);
    SeTransSetLabelModifiedActive(g_seRunCtx);

    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddrOptiTrx,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabelOptiTrx,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    vector<HpTupleAddr> heapItemList1;

    char tupleBuf[FIX_ROW_LEN] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList1.push_back(addr);
    }
    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);

    ret = SeTransBegin(g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(g_seRunCtx);
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t tmp = 0;
    char tmpTupleBuf[FIX_ROW_LEN] = "";
    heapHdl->hpControl.accessMethodType = HEAP_ACCESS_METHOD_LITE;
    for (auto &heapAddr : heapItemList1) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), tmp++);
        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapGetTupleLite, (void *)&tmpTupleBuf);
        EXPECT_EQ(0, ret);
        EXPECT_STREQ(tmpTupleBuf, tupleBuf);  // 读取到本事务最老的可见buf
    }

    HeapHandleClose(heapHdl);
    ret = SeTransCommit(g_seRunCtx);
    ASSERT_EQ(0, ret);
    UtHeapAMBasicDropOptiTrx();
}
#endif

// 该用例主要用于提高异常分支行覆盖率
TEST_F(UtStorageHeapAM, heap_am_lcov)
{
    HeapCommonAm tempCommonAm = g_gmdbHeapCommonAm;
    g_gmdbHeapCommonAm = {0};
    HpItemPointerT itemPointer;
    PageTotalSizeT pageSize;
    HpTupleAddr heapTupleAddr;
    ShmemPtrT heapShmAddr;
    PageSizeT fixRowSize;
    HpRowHeadPtrT rowHeadPtr;
    StatusInter ret;
    HpOpTypeE optype;
    HpPerfOpTypeE perOpType;
    HeapAllocRunctx(NULL, NULL);
    HeapBeginScanHpTupleBuffer(NULL, NULL, NULL);
    HeapCloseTrxCntrCtx(NULL);
    HeapDelete(NULL, itemPointer, false);
    HeapDeleteInnerRow(NULL, itemPointer, NULL);
    HeapDeleteLinkDstRow(NULL, NULL, false, false);
    HeapDeleteRowSetMark(NULL, NULL);
    HeapDeleteTupleBatch(NULL, 0, NULL, false);
    HeapDfxHandleOnTrxCommit(NULL);
    HeapDfxHandleWhenRrTrxCommit(NULL, NULL, 0, 0, 0);
    HeapFetchGetPage(NULL, NULL);
    HeapFetchTryGetPage(NULL, NULL);
    HeapFetchNextLoop(NULL, NULL, NULL);
    HeapFetchRowsLoop(NULL, NULL, NULL);
    HeapFetchSliceRows(NULL, NULL);
    HeapFetchTryGetPageUseLastAddr(NULL, NULL, NULL, NULL);
    HeapFixedRowInitCfg(pageSize, NULL);
    HeapFixPageGetRowBySlotId(NULL, 0, NULL, NULL);
    HeapFixPageInitLinkSrcRow(NULL, NULL);
    HeapFixPageInitNormalRow(NULL, NULL);
    HeapFixPageIsRowExist(NULL);
    HeapFixPageReadPrevVersion(NULL, NULL);
    HeapGetLastTruncateTrxId(NULL);
    HeapHcGetPage(NULL, itemPointer, NULL);
    HeapInitAllocRowInfo(NULL, NULL, 0, NULL);
    HeapInsert(NULL, NULL, 0, NULL);
    HeapLabelAllocAndInitRunctx(NULL, NULL);
    HeapLabelCommitDrop(NULL, NULL);
    HeapLabelCommitTruncate(NULL, NULL);
    HeapLabelCreate(NULL, NULL, NULL);
    HeapLabelDeleteHpTuple(NULL, heapTupleAddr);
    HeapLabelDowngradeFetchPage(NULL, NULL, NULL);
    HeapLabelDrop(NULL, NULL, NULL);
    HeapLabelEndScan(NULL, NULL);
    HeapLabelGetDefragStat(NULL, NULL);
    HeapLabelGetHacInfo(NULL);
    HeapLabelGetLfsFsmStat(NULL, NULL);
    HeapLabelGetMaxRowCapacity(NULL, NULL);
    HeapLabelGetMemoryStat(NULL, NULL);
    HeapLabelGetPerfStat(NULL, NULL, NULL);
    HeapLabelGetPhyItemNum(NULL, NULL);
    HeapLabelInitMemFields(NULL, heapShmAddr);
    HeapLabelInitRunctx(NULL, NULL);
    HeapLabelInsertTupleBatch(NULL, 0, NULL, NULL);
    HeapLabelRecordLongTermCursor(NULL, NULL);
    HeapLabelRecoveryInfoCreate(NULL, NULL);
    HeapLabelScanDecCursorNumAndUnrecordLongCursor(NULL, false, NULL);
    HeapLabelScanIncCursorNum(NULL, NULL);
    HeapLabelTruncate(NULL, NULL, NULL);
    HeapLabelTryInitRunctx(NULL, NULL);
    HeapLabelUpgradeFixRowSize(NULL, fixRowSize);
    HeapMarkDeleteRollBack(NULL, itemPointer, 0, NULL);
    HeapMdGetMemPage(NULL, NULL, NULL, NULL, NULL);
    HeapOpenByCntr(NULL, optype, NULL);
    HeapOpenCheckTrxInfoIsOk(NULL, NULL);
    HeapOpenSaveTrxInfoAndAcquireLock(NULL);
    HeapPageBatchPhyDelete(NULL, NULL, NULL);
    HeapPageDeleteRow(NULL, NULL);
    HeapPageReadUndo(NULL, NULL, NULL);
    HeapPerfOpTimeStat(NULL, perOpType, ret, 0, 0);
    HeapProcOnTrxCommit(NULL, false, NULL, NULL);
    HeapReleaseRunctx(NULL);
    HeapResetOpType(NULL, optype, false);
    HeapRollback2LinkSrcRow(NULL, rowHeadPtr, NULL, false);
    HeapRunCtxOpenLockStat(NULL, false);
    HeapRunCtxUpdateLockStat(NULL, NULL, ret);
    HeapSetblockFreeSizeAfterDelete(NULL, NULL);
    HeapSetFetchRowInfoWithFixLinkSrcRow(NULL);
    HeapSetLastPageHeadInvalid(NULL);
    HeapSliceRowInfoFreeDirMem(NULL, NULL);
    HeapStatGetRecCnt(NULL, NULL);
    HeapStatisticsForBatchDelete(NULL, NULL);
    HeapStatWriteRecCnt(NULL, 0, 0);
    HeapUnlinkRollPtr(NULL, heapTupleAddr, 0, NULL);
    HeapUpdate(NULL, NULL, NULL, NULL);
    HeapUpdateCommitClean(NULL, NULL, false);
    HeapUpdateCommitOrRollbackCleanForLob(NULL, itemPointer);
    HeapUpdateLockStat(NULL, NULL);
    HeapUpdatePartial(NULL, NULL, NULL, 0);
    HeapUpdateRollback(NULL, itemPointer, 0, NULL);
    HeapUpdateRow(NULL, NULL);
    HeapUpdLogUndoAndSetTrxInfo(NULL, NULL);
    HeapVarPageGetRowState(NULL, NULL);
    HeapVarPageGetSlotBySlotId(NULL, 0, NULL, NULL);
    HeapVarPageInitLinkDstRow(NULL, NULL, NULL);
    HeapVarPageInitLinkDstSliceDirRow(NULL, NULL, NULL);
    HeapVarPageInitLinkSrcRow(NULL, NULL, NULL, false);
    HeapVarPageInitNormalRow(NULL, NULL, NULL);
    HeapVarPageReadPrevVersion(NULL, NULL);
    HeapLabelIsExceedBatchInsertSizeLimit(NULL, 0);
    HeapLabelProcAfterDfgmt(NULL);
    HeapLabelChangeFsmListIdx(NULL);
    HeapFreeCachedPage(NULL, NULL);
    HeapLabelGetDefragmentationPage(NULL, NULL, NULL);
    HeapLabelKeyXLockAcquire(NULL, 0, NULL, 0);
    HeapLabelHcListDeleteWithPrefetch(NULL, NULL, 0, NULL);
    HeapLabelHcListPrefetch4Delete(NULL, NULL, 0);
    HeapLabelHcLisPrefetch4Insert(NULL, NULL, 0, heapTupleAddr);
    HeapLabelInfoRecovery(NULL, heapShmAddr, false, NULL);
    HeapLabelResetFixRow(NULL, 0);
    HeapLabelRelabelId(NULL, 0, 0);
    HeapLabelRollbackRelabelId(NULL, 0, 0);

    g_gmdbHeapCommonAm = tempCommonAm;
}
