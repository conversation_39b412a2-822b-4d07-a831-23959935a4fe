project(GMDB_STORAGE_UT)

# 生成可执行文件
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

aux_source_directory(. STORAGE_UT_LIST)
aux_source_directory(./ann_diskann STORAGE_UT_LIST)

if(FEATURE_HAC)
    aux_source_directory(./accelerator STORAGE_UT_LIST)
    add_definitions(-DFEATURE_HAC)
endif()
if(FEATURE_DAF)
    add_definitions(-DFEATURE_DAF)
else ()
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_daf_ut.cc)
endif()

if(EXPERIMENTAL_GUANGQI)
    # ut中 LfsSetBlockFreeSpace接口待在多态场景下适配
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_lfs_mgr_ut.cc)
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_fixed_heap_ut.cc)
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_edge_topo_access_ut.cc)
    # ut中 art申请页走到内存态，会被断言
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_art_container_ut.cc)
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_art_mem_ut.cc)
    # 光启未加载聚簇容器和hash cluster特性
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_clustered_hash_hc_index_ut.cc)
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_clustered_hash_label_ut.cc)
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_hc_index_ut.cc)
    # 待分析
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_pagemgr_device.cc)
    # 光启未加载respool特性
    list(REMOVE_ITEM STORAGE_UT_LIST ./storage_resource_column.cc)
endif()

#btree
list(REMOVE_ITEM STORAGE_UT_LIST ./storage_btree_index_ut.cc)
#diskann
list(REMOVE_ITEM STORAGE_UT_LIST ./ann_diskann/storage_diskann_adpt_ut.cc ./ann_diskann/storage_diskann_ut_common.cc ./ann_diskann/storage_diskann_graph.cc
    ./ann_diskann/storage_diskann_filter_ut.cc ./ann_diskann/storage_diskann_nmq_ut.cc ./ann_diskann/storage_diskann_index_ut.cc
    ./ann_diskann/storage_diskann_iter_to_fixed_point_ut.cc ./ann_diskann/storage_diskann_iterator_ut.cc ./ann_diskann/storage_diskann_robust_prune_ut.cc)


aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/../common_test_include COMMON_TEST_INCLUDE_LIST)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common_test_include ./)
add_executable(ut_storage ${STORAGE_UT_LIST} ${COMMON_TEST_INCLUDE_LIST})

if(FEATURE_HAC)
    target_link_libraries(ut_storage securec stub gmhac)
else()
    target_link_libraries(ut_storage securec stub)
endif()
