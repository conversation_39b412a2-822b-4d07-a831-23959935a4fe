/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: storage_hash_index_ut.cc
 * Description: Implementation of hash index ut
 * Author: tang<PERSON> qipengcheng (q00574683) liangtingting (l00596614)
 * Create: 2020/7/15
 */
#include <map>
#include <iostream>
#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "db_shm_array.h"
#include "se_define.h"
#include "se_instance.h"
#include "db_table_space.h"
#include "se_memdata.h"
#include "gmc_errno.h"
#include "stub.h"
#include "adpt_mem_segment_euler.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#include "se_hash_index.h"
#include "db_common_init.h"
#include "common_init.h"
#include "se_index.h"
#include "se_page_mgr.h"
#include "dm_meta_index_label.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "se_list_localhash_index.h"
#include "xxh3.h"

using namespace std;

#ifdef __cplusplus
extern "C" {
#endif

static const uint32_t MULTI_NUM = 5000;
static const uint32_t MULTI_LARGE_NUM_1 = 60000;
static const uint32_t MULTI_LARGE_NUM_2 = 65000;
static const uint32_t THREAD_COUNTS = 100;
static const uint32_t KEY_DATA_LEN = 8;
static const uint32_t AVERAGE_ENTRY_NUM_PER_SEGMENT_FOR_TEST = 2000;  // 2000 < hashEntryNumPerPage, approximate 2045
static const uint32_t SEGMENT_NUMBER_FOR_TEST = 128;                  // it means the segment depth is 7
static const uint32_t TOTAL_ENTRIES_FOR_TEST = (SEGMENT_NUMBER_FOR_TEST * AVERAGE_ENTRY_NUM_PER_SEGMENT_FOR_TEST);
static const uint32_t STASH_PAGE_TEST_NUM = 3000;
static int32_t g_pageCount = 0;

typedef struct TagUtShmCommT {
    volatile uint32_t writeCnt;
    volatile uint32_t readCnt;
    volatile uint32_t readOkCnt;
} UtShmCommT;

#ifdef __cplusplus
}
#endif

static DbMemCtxT *g_topShmMemCtxForHash = nullptr;
static SeRunCtxHdT g_seRunCtxForHash;
static ShmemPtrT g_htConcurrencyShmAddr;

static IndexMetaCfgT g_idxMetaCfgForHash = {
    .indexId = 0,
    .idxType = HASH_INDEX,
    .realIdxType = HASH_INDEX,
    .idxConstraint = PRIMARY,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

static IndexMetaCfgT g_idxMetaCfgForHashMutiVersion = {
    .indexId = 0,
    .idxType = LIST_LOCALHASH_INDEX,
    .realIdxType = LIST_LOCALHASH_INDEX,
    .idxConstraint = PRIMARY,
    .indexMultiVersionType = INDEX_MULTI_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

static void DestroySysStorageShmemGroup(void)
{
    Status res = DbAdptDestroyShmemGroup(SYS_SHM_GROUP_ID);
    EXPECT_EQ(GMERR_OK, res);
    res = DbAdptDestroyShmemGroup(STORAGE_SHM_GROUP_ID);
    EXPECT_EQ(GMERR_OK, res);
}

int32_t HashCreateAndOpenStorageEngine(
    DbMemCtxT **topShmMemCtx, SeRunCtxHdT *seRunCtx, uint16_t deviceSize, uint16_t memSize)
{
    *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(topShmMemCtx);

    SeConfigT hashConfig = {0};
    hashConfig.deviceSize = deviceSize * DB_KIBI;
    hashConfig.pageSize = SE_DEFAULT_PAGE_SIZE;
    hashConfig.instanceId = 1;
    hashConfig.maxSeMem = memSize * DB_KIBI;  // 单位 K
    hashConfig.maxTrxNum = MAX_TRX_NUM;

    SeInstanceT *hashPtr = nullptr;
    Status ret = SeCreateInstance(NULL, *topShmMemCtx, &hashConfig, (SeInstanceHdT *)&hashPtr);
    EXPECT_EQ(GMERR_OK, ret);
    SeInstanceT *cmpPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_EQ(hashPtr, cmpPtr);

    PageMgrT *pageMgr = (PageMgrT *)cmpPtr->mdMgr;
    PageIdT addr = SE_INVALID_PAGE_ADDR;
    uint8_t *page = NULL;
    // index不使用rsmUndo
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    SeAllocPage(pageMgr, &allocPageParam, &addr);
    EXPECT_EQ(true, DbIsPageIdValid(addr));
    SeGetPage(pageMgr, addr, &page, ENTER_PAGE_NORMAL, false);
    EXPECT_EQ(true, DbIsPageIdEqual(addr, ((PageHeadT *)page)->addr));

    DbMemCtxArgsT hashArgs = {0};
    hashArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    hashArgs.memType = DB_DYNAMIC_MEMORY;
    hashArgs.init = DynamicAlgoInit;

    void *seTopDynamicCtxTop =
        (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "se dynamic", &hashArgs);
    return SeOpenWithNewSession(1, (DbMemCtxT *)seTopDynamicCtxTop, seRunCtx);
}

// key and addr are related, and addr can be used to reconstructe key
IndexKeyT HashUtConstructKey(HpTupleAddr addr, uint8_t *keydataPtr, uint32_t keyDataLen)
{
    uint8_t *addrPtr = (uint8_t *)&addr;
    errno_t ret = memcpy_s(keydataPtr, keyDataLen, addrPtr, keyDataLen);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT key = {.keyData = keydataPtr, .keyLen = keyDataLen};
    return key;
}

Status HtCompareStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    uint8_t keydata[KEY_DATA_LEN] = "";
    IndexKeyT existingKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
    const char *str1 = (const char *)existingKey.keyData;
    const char *str2 = (const char *)hashKey.keyData;
    *isMatch = (memcmp(str1, str2, hashKey.keyLen) == 0);
    *cmpRet = 0;
    return GMERR_OK;
}

Status HtCompareAgedStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    *isMatch = false;
    return GMERR_OK;
}

class UtHashIndex : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = HashCreateAndOpenStorageEngine(&g_topShmMemCtxForHash, &g_seRunCtxForHash, 4, 1024);
        EXPECT_EQ(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        clearAllStub();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

TEST_F(UtHashIndex, storage_hash_index_create_and_drop_001)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

inline void *SeShmAllocStub(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    DB_UNUSED(shmCtx);
    DB_UNUSED(size);
    DB_UNUSED(shmPtr);
    return nullptr;
}

TEST_F(UtHashIndex, storage_hash_index_drop_empty_002)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    int32_t stubId = setStubC((void *)SeShmAlloc, (void *)SeShmAllocStub);
    ShmemPtrT htShmAddr;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_NE(GMERR_OK, ret);
    EXPECT_EQ(htShmAddr.offset, DB_INVALID_SHMPTR.offset);
    EXPECT_EQ(htShmAddr.segId, DB_INVALID_SHMPTR.segId);

    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearStub(stubId);
}

static Status UtHashIndexOpen(IndexOpenCfgT openCfg, ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = openCfg;
    SeRunCtxT *hashRunCtxPtr = openCfg.seRunCtx;
    idxCtx->idxHandle = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(hashRunCtxPtr == nullptr || idxCtx->idxHandle == nullptr)) {
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionCtxT *ctx = &hashRunCtxPtr->resSessionCtx;
    if (ctx->isDirectRead && !IdxIsConstructed(idxCtx->idxHandle)) {
        DB_LOG_DBG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "[SE-lpm] open unconstructed when DirectRead");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    idxCtx->idxMetaCfg = idxCtx->idxHandle->indexCfg;
    return HashIndexOpen(idxCtx);
}

static inline IndexOpenCfgT GenerateHashIndexOpenCfg()
{
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = g_seRunCtxForHash,
        .vertex = nullptr,
        .heapHandle = nullptr,
    };
    indexOpenCfg.callbackFunc.keyCmp = HtCompareStub;
    return indexOpenCfg;
}

static void HashIndexInit(IndexMetaCfgT indexCfg, ShmemPtrT *htShmAddr, IndexCtxT **idxCtx)
{
    DB_POINTER2(htShmAddr, idxCtx);
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    ret = UtHashIndexOpen(indexOpenCfg, *htShmAddr, *idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtHashIndex, storage_hash_index_alloc_and_release_003)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

static void HashIndexTruncateStatView(ShmemPtrT htShmAddr, bool withoutInsert = false)
{
    // get statistical information of hash table, before it is truncated
    IndexStatisticsT idxStatUntrunc;
    Status ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStatUntrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate the hash table
    HashIndexTruncate(g_seRunCtxForHash, htShmAddr);

    // get statistical information of hash table, after it is truncated
    IndexStatisticsT idxStatTrunc;
    ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStatTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // expect that some statistical information of hash table after being truncated is 0.
    EXPECT_EQ(0u, idxStatTrunc.hashIndex.entryUsed);
    EXPECT_EQ(
        idxStatUntrunc.hashIndex.indexHashPerfStat.perPageSize, idxStatTrunc.hashIndex.indexHashPerfStat.perPageSize);
    if (withoutInsert) {
        EXPECT_EQ(idxStatUntrunc.hashIndex.entryUsed, idxStatTrunc.hashIndex.entryUsed);
        EXPECT_EQ(idxStatUntrunc.hashIndex.segmentCnt, idxStatTrunc.hashIndex.segmentCnt);
        EXPECT_EQ(
            idxStatUntrunc.hashIndex.indexHashPerfStat.pageCount, idxStatTrunc.hashIndex.indexHashPerfStat.pageCount);
        EXPECT_EQ(
            idxStatUntrunc.hashIndex.indexHashPerfStat.pageSize, idxStatTrunc.hashIndex.indexHashPerfStat.pageSize);
        EXPECT_EQ(idxStatUntrunc.hashIndex.indexHashPerfStat.usedMemSize,
            idxStatTrunc.hashIndex.indexHashPerfStat.usedMemSize);
    }
}

TEST_F(UtHashIndex, storage_hash_index_truncate_without_insert_004)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    int32_t ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexTruncateStatView(htShmAddr, true);

    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_open_and_truncate_with_insert_005)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    HashIndexTruncateStatView(htShmAddr);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

static Status UtHashIndexInsert(IndexCtxT *idxCtx, uint32_t num, uint32_t base = 0xabcd)
{
    DB_POINTER(idxCtx);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status UtHashIndexLookup(IndexCtxT *idxCtx, uint32_t num, bool canBeFound = true, uint32_t base = 0xabcd)
{
    DB_POINTER(idxCtx);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        HpTupleAddr addrReturned;
        bool isFound = false;
        Status ret = HashIndexLookup(idxCtx, hashKey, &addrReturned, &isFound);
        if (ret != GMERR_OK) {
            return ret;
        }
        EXPECT_EQ(canBeFound, isFound);
        if (canBeFound != isFound) {
            return GMERR_NO_DATA;
        }
        if (isFound) {
            EXPECT_EQ(addr, addrReturned);
        }
        uint64_t count = 0;
        ret = HashIndexGetKeyCount(idxCtx, hashKey, &count);
        if (ret != GMERR_OK) {
            return ret;
        }
        EXPECT_EQ((isFound ? 1u : 0u), count);
    }
    return ret;
}

static Status UtHashIndexDelete(IndexCtxT *idxCtx, uint32_t num, IndexRemoveParaT removePara, uint32_t base = 0xabcd)
{
    DB_POINTER(idxCtx);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexDelete(idxCtx, hashKey, addr, removePara);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status UtHashIndexUpdate(IndexCtxT *idxCtx, uint32_t num, IndexRemoveParaT removePara, uint32_t delBase = 0xabcd,
    uint32_t insertBase = 0xabcc)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; ++i) {
        HpTupleAddr delAddr = delBase + i;
        uint8_t delKeydata[KEY_DATA_LEN] = "";
        IndexKeyT delHashKey = HashUtConstructKey(delAddr, delKeydata, KEY_DATA_LEN);
        HpTupleAddr insertAddr = delBase + i;
        uint8_t insertKeydata[KEY_DATA_LEN] = "";
        IndexKeyT insertHashKey = HashUtConstructKey(insertAddr, insertKeydata, KEY_DATA_LEN);
        IndexUpdateInfoT updateInfo = {
            .oldIdxKey = delHashKey, .newIdxKey = insertHashKey, .oldAddr = delAddr, .newAddr = insertAddr};
        ret = HashIndexUpdate(idxCtx, updateInfo, removePara);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static void HashCreateAndTruncateWithInsert(uint32_t opNum = 1, uint32_t multiNum = 1)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    IndexCtxT *idxCtx;

    for (uint32_t i = 0; i < opNum; ++i) {
        ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
        EXPECT_EQ(GMERR_OK, ret);

        ret = UtHashIndexInsert(idxCtx, multiNum, 0xabcd + i);
        EXPECT_EQ(GMERR_OK, ret);
        ret = UtHashIndexLookup(idxCtx, multiNum, true, 0xabcd + i);
        EXPECT_EQ(GMERR_OK, ret);

        IndexStatisticsT idxStatUntrunc;
        ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStatUntrunc);
        EXPECT_EQ(GMERR_OK, ret);

        HashIndexClose(idxCtx);
        IdxRelease(idxCtx);

        HashIndexTruncate(g_seRunCtxForHash, htShmAddr);

        IndexStatisticsT idxStatTrunc;
        ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStatTrunc);
        EXPECT_EQ(GMERR_OK, ret);

        ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
        EXPECT_EQ(GMERR_OK, ret);

        ret = UtHashIndexLookup(idxCtx, multiNum, false, 0xabcd + i);
        EXPECT_EQ(GMERR_OK, ret);

        HashIndexClose(idxCtx);
        IdxRelease(idxCtx);
    }

    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_truncate_with_insert_006)
{
    HashCreateAndTruncateWithInsert();
}

TEST_F(UtHashIndex, storage_hash_index_truncate_with_multi_insert_007)
{
    HashCreateAndTruncateWithInsert(MULTI_NUM);
}

TEST_F(UtHashIndex, storage_hash_index_truncate_with_insert_multi_times_008)
{
    HashCreateAndTruncateWithInsert(1, MULTI_NUM);
}

static void HashInsertAndLookup(uint32_t num, uint32_t cap = 0)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    indexCfg.indexCap = cap;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num);
    EXPECT_EQ(GMERR_OK, ret);

    IndexStatisticsT idxStat;
    ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStat);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_lookup_one_009)
{
    HashInsertAndLookup(1);
}

TEST_F(UtHashIndex, storage_hash_index_lookup_multi_010)
{
    HashInsertAndLookup(MULTI_NUM);
}

TEST_F(UtHashIndex, storage_hash_index_lookup_large_num_011)
{
    HashInsertAndLookup(MULTI_LARGE_NUM_1);
}

TEST_F(UtHashIndex, storage_hash_index_lookup_large_num2_012)
{
    HashInsertAndLookup(MULTI_LARGE_NUM_2);
}

TEST_F(UtHashIndex, storage_hash_index_lookup_multi_with_hash_cap_013)
{
    HashInsertAndLookup(MULTI_LARGE_NUM_1, 1024000);
}

TEST_F(UtHashIndex, storage_hash_index_lookup_multi_with_hash_cap_014)
{
    HashInsertAndLookup(MULTI_LARGE_NUM_2, 1024000);
}

void HashAmShmRead(UtShmCommT *shmComm, ShmemPtrT htShmAddr)
{
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();

    IndexCtxT *idxCtx;
    Status ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    while (shmComm->writeCnt < MULTI_NUM) {
        DbSleep(1);
    }
    for (uint32_t i = 0; i < MULTI_NUM; ++i) {
        HpTupleAddr addr = 0xabcd + i;
        HpTupleAddr addrReturned;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        bool isFound;
        ret = HashIndexLookup(idxCtx, hashKey, &addrReturned, &isFound);
        EXPECT_TRUE(isFound);
        EXPECT_EQ(GMERR_OK, ret);
        if (addr == addrReturned) {
            shmComm->readOkCnt++;
        }
        shmComm->readCnt++;
    }

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    exit(0);
}

void HashAmShmWrite(UtShmCommT *shmComm, ShmemPtrT htShmAddr)
{
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();

    IndexCtxT *idxCtx;
    Status ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < MULTI_NUM; ++i) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
        shmComm->writeCnt++;
    }
    while (shmComm->readCnt < MULTI_NUM) {
        DbSleep(1);
    }
    EXPECT_EQ(MULTI_NUM, shmComm->readOkCnt);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
}

TEST_F(UtHashIndex, storage_hash_index_read_write_share_mem_015)
{
    ShmemPtrT shmCommAddr = DbShmemCtxAlloc((DbMemCtxT *)g_topShmMemCtxForHash, sizeof(UtShmCommT));
    UtShmCommT *shmComm = (UtShmCommT *)DbShmPtrToAddr(shmCommAddr);
    memset_s(shmComm, sizeof(UtShmCommT), 0, sizeof(UtShmCommT));
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;

    ShmemPtrT htShmAddr;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    pid_t pid = fork();
    if (pid == 0) {
        HashAmShmRead(shmComm, htShmAddr);
    } else {
        HashAmShmWrite(shmComm, htShmAddr);
    }
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    DbShmemCtxFree((DbMemCtxT *)g_topShmMemCtxForHash, shmCommAddr);
}

TEST_F(UtHashIndex, storage_hash_index_insert_duplicate_016)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

static void HashUniqueIndexRemove(uint32_t base = 1)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, base);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, base);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, base, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, base);
    EXPECT_EQ(GMERR_OK, ret);

    removePara.isGc = true;
    ret = UtHashIndexDelete(idxCtx, base, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, base, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexInsert(idxCtx, base);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, base);
    EXPECT_EQ(GMERR_OK, ret);

    removePara.isGc = false;
    ret = UtHashIndexDelete(idxCtx, base, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, base);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_remove_017)
{
    HashUniqueIndexRemove();
}

TEST_F(UtHashIndex, storage_hash_index_insert_and_remove_multi_018)
{
    HashUniqueIndexRemove(MULTI_LARGE_NUM_2);
}

void *MsUniqueIndexInsertReadThread(void *hashArgs)
{
    DbSetServerThreadFlag();
    IndexCtxT *idxCtx;
    Status ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    ret = UtHashIndexOpen(indexOpenCfg, g_htConcurrencyShmAddr, idxCtx);
    uint32_t keyValue = *(uint32_t *)hashArgs;

    ret = UtHashIndexInsert(idxCtx, MULTI_NUM, 0xabcd + keyValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, MULTI_NUM, true, 0xabcd + keyValue);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, MULTI_NUM, removePara, 0xabcd + keyValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, MULTI_NUM, true, 0xabcd + keyValue);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    return nullptr;
}

TEST_F(UtHashIndex, DISABLED_storage_hash_index_concurrency_019)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;

    int32_t ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &g_htConcurrencyShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t wrth[THREAD_COUNTS];
    uint32_t a[THREAD_COUNTS] = {0};
    for (uint32_t i = 0; i < THREAD_COUNTS; i++) {
        a[i] = i * MULTI_NUM;
        ret = pthread_create(&wrth[i], nullptr, MsUniqueIndexInsertReadThread, &a[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < THREAD_COUNTS; i++) {
        pthread_join(wrth[i], nullptr);
    }

    // there should is only 2 pages and no entry
    IndexStatisticsT statisticInfo;
    ret = HashIndexStatView(g_htConcurrencyShmAddr, DbGetProcGlobalId(), &statisticInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(
        statisticInfo.hashIndex.indexHashPerfStat.pageCount, 2u);  // there's only one segment page and one entry page
    EXPECT_EQ(0u, statisticInfo.hashIndex.entryUsed);              // all entries should be deleted

    HashIndexDrop(g_seRunCtxForHash, g_htConcurrencyShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_insert_duplicate_fail_020)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    // 插入第一个元素
    Status ret = UtHashIndexInsert(idxCtx, 1, 0xabe0 + 19);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0 + 19);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入目标元素
    ret = UtHashIndexInsert(idxCtx, 1, 0xabe4 + 23);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe4 + 23);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第一个元素
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, 1, removePara, 0xabe0 + 19);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, false, 0xabe0 + 19);
    EXPECT_EQ(GMERR_OK, ret);

    // 再次插入目标元素
    ret = UtHashIndexInsert(idxCtx, 1, 0xabe4 + 23);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe4 + 23);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

// 插入1w条数据，不删除，没有可以缩容的segA和segB
TEST_F(UtHashIndex, storage_hash_index_no_scale_in_021)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, 10000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 10000);
    EXPECT_EQ(GMERR_OK, ret);

    IndexScaleInCfgT idxScaleCfg = {
        .splitTime = 1000,
        .startTime = DbRdtsc(),  // 入参，分片开始的时间
        .isOverTime = false      // 出参，运行是否超过时间片
    };
    do {
        idxScaleCfg.isOverTime = false;
        ret = HashIndexScaleIn(idxCtx, &idxScaleCfg);
        EXPECT_EQ(GMERR_OK, ret);
    } while (idxScaleCfg.isOverTime);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

// 插入1w条数据，erase删除后缩容，再插入10w条数据
TEST_F(UtHashIndex, storage_hash_index_scale_in_022)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    uint32_t num = 10000;
    Status ret = UtHashIndexInsert(idxCtx, num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num);
    EXPECT_EQ(GMERR_OK, ret);

    // erase删除插入的9999条数据后手动触发一次缩容
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, num - 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num - 1, false);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t scaleInNum = num / 1000;
    IndexScaleInCfgT idxScaleCfg = {
        .splitTime = 1000,
        .startTime = DbRdtsc(),  // 入参，分片开始的时间
        .isOverTime = false      // 出参，运行是否超过时间片
    };
    for (int32_t i = 0; i < scaleInNum; i++) {
        do {
            idxScaleCfg.isOverTime = false;
            ret = HashIndexScaleIn(idxCtx, &idxScaleCfg);
            EXPECT_EQ(GMERR_OK, ret);
        } while (idxScaleCfg.isOverTime);
    }

    // 重新插入10w条数据，会自动扩容一次
    ret = UtHashIndexInsert(idxCtx, num * 10, 0xabcd + 10000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num * 10, true, 0xabcd + 10000);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_mem_out_023)
{
    Status res = SeClose(g_seRunCtxForHash);
    EXPECT_EQ(GMERR_OK, res);
    SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
    CommonRelease();
    CommonInit();
#ifdef FEATURE_PERSISTENCE
    res = HashCreateAndOpenStorageEngine(&g_topShmMemCtxForHash, &g_seRunCtxForHash, 4, 16);
#else
    res = HashCreateAndOpenStorageEngine(&g_topShmMemCtxForHash, &g_seRunCtxForHash, 4, 8);
#endif
    EXPECT_EQ(GMERR_OK, res);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, MULTI_LARGE_NUM_2 * 4);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);

    // another hash index HashIndexDrop success after UtHashIndexOpenAm fail
    ShmemPtrT htShmAddr1;
    IndexCtxT *idxCtx1;
    ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx1);
    EXPECT_EQ(GMERR_OK, ret);

    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr1, idxCtx1);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);

    HashIndexDrop(g_seRunCtxForHash, htShmAddr1);

    HashIndexDrop(g_seRunCtxForHash, htShmAddr);

    ret = SeClose(g_seRunCtxForHash);
    EXPECT_EQ(GMERR_OK, ret);
    SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
    CommonRelease();
    CommonInit();
    ret = HashCreateAndOpenStorageEngine(&g_topShmMemCtxForHash, &g_seRunCtxForHash, 4, 1024);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtHashIndex, storage_hash_index_remove_gc_024)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    uint32_t operNums = 500;

    Status ret = UtHashIndexInsert(idxCtx, operNums * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums * 2);
    EXPECT_EQ(GMERR_OK, ret);

    // 标记删除前oper_nums的索引
    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, operNums, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums);
    EXPECT_EQ(GMERR_OK, ret);

    // GC回收，只会回收前面标记删除的索引
    removePara.isGc = true;
    ret = UtHashIndexDelete(idxCtx, operNums * 2, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums, true, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);

    // 标记删除后oper_nums的索引
    removePara.isGc = false;
    ret = UtHashIndexDelete(idxCtx, operNums, removePara, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums, true, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexInsert(idxCtx, operNums, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums, true, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);

    // 标记删除后oper_nums的索引
    removePara.isGc = false;
    ret = UtHashIndexDelete(idxCtx, operNums, removePara, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums, true, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);

    // GC删除后oper_nums的索引，应该Lookup不到了
    removePara.isGc = true;
    ret = UtHashIndexDelete(idxCtx, operNums, removePara, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, operNums, false, 0xabcd + operNums);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_sysview_about_hash_collision_and_truncate_025)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    // 初始都为0
    IndexStatisticsT idxStat;
    Status ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStat);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, idxStat.hashIndex.indexHashPerfStat.hashInsertCnt);
    EXPECT_EQ(0u, idxStat.hashIndex.indexHashPerfStat.hashCollisionCnt);
    EXPECT_EQ(0u, idxStat.hashIndex.indexHashPerfStat.hashCollisionRate);

    ret = UtHashIndexInsert(idxCtx, MULTI_NUM);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, MULTI_NUM);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStat);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(idxStat.hashIndex.indexHashPerfStat.hashInsertCnt, MULTI_NUM);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_get_estimate_mem_size_026)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    uint64_t size = 0;
    Status ret = HashIndexGetEstimateMemSize(DbGetProcGlobalId(), indexCfg, 10000, 8, &size);
    EXPECT_EQ(GMERR_OK, ret);

#ifdef HPE
    EXPECT_LE(size, 557328u);
#else
    EXPECT_EQ(557328u, size);
#endif
}

uint64_t g_trxId = 0;
TrxIdT UtSeTransGetTrxId(const SeRunCtxHdT seRunCtx)
{
    return (TrxIdT)g_trxId;
}

bool UtSeTransIsCommit(const SeRunCtxHdT seRunCtx, TrxIdT trxId)
{
    return true;
}

map<unsigned long, int> g_rowMap;

Status HtCompareOldStub(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, KeyCmpResT *keyCmpRes)
{
    DB_POINTER3(idxCtx, idxKey.keyData, keyCmpRes);
    uint8_t keyData[KEY_DATA_LEN] = "abcd";
    const char *str1 = (const char *)keyData;
    const char *str2 = (const char *)idxKey.keyData;
    keyCmpRes->isMatch = (memcmp(str1, str2, idxKey.keyLen) == 0);
    keyCmpRes->cmpRet = 0;
    keyCmpRes->isSelfDelete = !(bool)g_rowMap[addr];
    return GMERR_OK;
}

static Status UtHashIndexInsertSameKey(IndexCtxT *idxCtx, HpTupleAddr base = 0xabcd)
{
    DB_POINTER(idxCtx);
    Status ret = GMERR_OK;
    HpTupleAddr addr = base;
    uint8_t keydata[KEY_DATA_LEN] = "abcd";
    g_rowMap[addr] = 1;
    IndexKeyT hashKey = {.keyData = keydata, .keyLen = (uint32_t)strlen((char *)keydata)};
    ret = HashIndexInsert(idxCtx, hashKey, addr);
    return ret;
}

static Status UtHashIndexDeleteWithSameKey(IndexCtxT *idxCtx, IndexRemoveParaT removePara, HpTupleAddr base = 0xabcd)
{
    DB_POINTER(idxCtx);
    Status ret = GMERR_OK;
    HpTupleAddr addr = base;
    uint8_t keydata[KEY_DATA_LEN] = "abcd";
    IndexKeyT hashKey = {.keyData = keydata, .keyLen = (uint32_t)strlen((char *)keydata)};
    ret = HashIndexDelete(idxCtx, hashKey, addr, removePara);
    g_rowMap[addr] = 0;
    return ret;
}

TEST_F(UtHashIndex, storage_hash_index_mutil_version_insert_and_delete_027)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    Status ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    removePara.isGc = true;
    ret = UtHashIndexDelete(idxCtx, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    removePara.isGc = false;
    ret = UtHashIndexDelete(idxCtx, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_hash_index_mutil_version_insert_and_delete_028)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    // 模拟事务1 插入 删除 插入数据，然后获取oldAddr
    HpTupleAddr row1 = 0xab1;
    Status ret = UtHashIndexInsertSameKey(idxCtx, row1);
    EXPECT_EQ(row1, idxCtx->oldRowId);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = UtHashIndexDeleteWithSameKey(idxCtx, removePara, row1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(row1, idxCtx->oldRowId);

    HpTupleAddr row2 = 0xab2;
    ret = UtHashIndexInsertSameKey(idxCtx, row2);
    EXPECT_EQ(row1, idxCtx->oldRowId);
    EXPECT_EQ(GMERR_OK, ret);

    // 已提交
    g_trxId = 1;
    // 模拟事务2  删除 插入数据，然后获取oldAddr
    idxCtx->oldRowId = HEAP_INVALID_ADDR;
    removePara.isGc = false;
    ret = UtHashIndexDeleteWithSameKey(idxCtx, removePara, row2);
    EXPECT_EQ(GMERR_OK, ret);

    HpTupleAddr row3 = 0xab3;
    ret = UtHashIndexInsertSameKey(idxCtx, row3);
    EXPECT_EQ(row2, idxCtx->oldRowId);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexDeleteWithSameKey(idxCtx, removePara, row3);
    g_rowMap[row3] = 0;
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(row2, idxCtx->oldRowId);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_hash_index_truncate_check_version_029)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    Status ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    EXPECT_EQ(((HashIndexCtxT *)idxCtx->idxRunCtx)->hashIndexVersion, ht->htVersion);

    HashIndexTruncate(g_seRunCtxForHash, htShmAddr);
    EXPECT_NE(((HashIndexCtxT *)idxCtx->idxRunCtx)->hashIndexVersion, ht->htVersion);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

uint32_t HashIndexDbHash32Stub(const uint8_t *key, uint32_t len)
{
    return 1;
}

TEST_F(UtHashIndex, storage_hash_index_insert_same_hashcode_overflow_and_lookup_030)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32Stub);
    HashInsertAndLookup(STASH_PAGE_TEST_NUM);
    clearStub(stubId);
}

TEST_F(UtHashIndex, storage_hash_index_insert_same_hashcode_overflow_and_remove_031)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32Stub);
    HashUniqueIndexRemove(STASH_PAGE_TEST_NUM);
    clearStub(stubId);
}

TEST_F(UtHashIndex, storage_hash_index_insert_same_hashcode_overflow_and_truncate_032)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32Stub);
    HashCreateAndTruncateWithInsert(STASH_PAGE_TEST_NUM);
    clearStub(stubId);
}

TEST_F(UtHashIndex, storage_hash_index_update_033)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    // 插入第一个元素
    Status ret = UtHashIndexInsert(idxCtx, 1, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新目标元素
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = UtHashIndexUpdate(idxCtx, 1, removePara, 0xabe0, 0xabe0 + 10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除更新个元素
    ret = UtHashIndexDelete(idxCtx, 1, removePara, 0xabe0 + 10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, false, 0xabe0 + 10);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_hash_index_mutil_version_insert_and_delete_034)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    // 正常插入数据，读取数据
    Status ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 非undo 合并 删除数据，读取数据
    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // undo 合并 删除数据，读取数据
    removePara.isGc = false;
    removePara.isErase = true;
    ret = UtHashIndexDelete(idxCtx, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, false);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

Status UtHtFreePageToMd(HashMemMgrT *memMgr, PageMgrT *pageMgr, const PageIdT *addr)
{
    g_pageCount--;
    return GMERR_OK;
}

Status UtHtAllocMemPage(HashMemRunCtxT *memRunCtx, HashAllocPageParamT *allocParam, uint32_t *blockId,
    DbMemAddrT *pageInfo, uint32_t *pageId)
{
    g_pageCount++;
    return GMERR_OK;
}

extern "C" Status HashInitDirSegPage(
    HashTableT *ht, uint32_t initDirBlockRemain, uint32_t blockId, uint32_t dirDepth, uint32_t *segPageId);
extern "C" void HashFreeSegPages(HashTableT *ht);
extern "C" void HashFreeHashDirPage(HashTableT *ht, uint32_t freeCount);

Status UtHashInitDirSegPage(HashTableT *ht, uint32_t initDirBlockRemain, uint32_t dirDepth, uint32_t *segPageId)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    for (uint32_t i = 0; i < initDirBlockRemain; i++) {
        UtHtAllocMemPage(NULL, NULL, NULL, NULL, NULL);
        ccehMeta->segPageCount++;
    }
    ccehMeta->dirPageCount++;
    UtHtAllocMemPage(NULL, NULL, NULL, NULL, NULL);
    return GMERR_INSUFFICIENT_RESOURCES;
}

void UtHashFreeSegPages(HashTableT *ht)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashDirectoryHeadT *dir = &(ccehMeta->dir);
    uint32_t freeSegCount = 0;
    for (uint32_t i = 0; i < ccehMeta->dirPageCount; i++) {
        for (uint32_t segSlotId = 0; segSlotId < ccehMeta->hashSegNumPerPage; segSlotId++) {
            if (freeSegCount >= dir->dirCap) {
                break;
            }
            g_pageCount--;
            freeSegCount++;
        }
    }
}

void UtHashFreeHashDirPage(HashTableT *ht, uint32_t freeCount)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    for (uint32_t i = 0; i < ccehMeta->dirPageCount; i++) {
        g_pageCount--;
    }
    ccehMeta->dirPageCount -= freeCount;
}

TEST_F(UtHashIndex, storage_hash_index_open_fail_034)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    setStubC((void *)HashFreeHashDirPage, (void *)UtHashFreeHashDirPage);
    setStubC((void *)HtAllocMemPage, (void *)UtHtAllocMemPage);
    setStubC((void *)HashInitDirSegPage, (void *)UtHashInitDirSegPage);
    setStubC((void *)HashFreeSegPages, (void *)UtHashFreeSegPages);
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    IndexCtxT *idxCtx;

    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(g_pageCount, 0);
    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexTruncate(g_seRunCtxForHash, htShmAddr);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

static Status UtHashIndexLookupWithRange(
    IndexCtxT *idxCtx, uint32_t startNum, uint32_t endNum, bool canBeFound = true, uint32_t base = 0xabcd)
{
    DB_POINTER(idxCtx);
    Status ret = GMERR_OK;
    for (uint32_t i = startNum; i < endNum; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        HpTupleAddr addrReturned;
        bool isFound = false;
        Status ret = HashIndexLookup(idxCtx, hashKey, &addrReturned, &isFound);
        if (ret != GMERR_OK) {
            return ret;
        }
        EXPECT_EQ(canBeFound, isFound);
        if (isFound) {
            EXPECT_EQ(addr, addrReturned);
        }
        uint64_t count = 0;
        ret = HashIndexGetKeyCount(idxCtx, hashKey, &count);
        if (ret != GMERR_OK) {
            return ret;
        }
        EXPECT_EQ((isFound ? 1u : 0u), count);
    }
    return ret;
}

TEST_F(UtHashIndex, storage_hash_index_scalein_lookup)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    uint32_t num = 10000;
    double factor = 0.7;
    Status ret = UtHashIndexInsert(idxCtx, num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, num * factor, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num * factor, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookupWithRange(idxCtx, num * factor, num, true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t scaleInNum = num / 1000;
    IndexScaleInCfgT idxScaleCfg = {
        .splitTime = 1000,
        .startTime = DbRdtsc(),  // 入参，分片开始的时间
        .isOverTime = false      // 出参，运行是否超过时间片
    };
    for (int32_t i = 0; i < scaleInNum; i++) {
        do {
            idxScaleCfg.isOverTime = false;
            ret = HashIndexScaleIn(idxCtx, &idxScaleCfg);
            EXPECT_EQ(GMERR_OK, ret);
        } while (idxScaleCfg.isOverTime);
    }

    ret = UtHashIndexLookup(idxCtx, num * factor, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookupWithRange(idxCtx, num * factor, num, true);
    EXPECT_EQ(GMERR_OK, ret);

    // 重新插入10w条数据，会自动扩容一次
    ret = UtHashIndexInsert(idxCtx, num * 10, 0xabcd + 10000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num * 10, true, 0xabcd + 10000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, num * factor, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookupWithRange(idxCtx, num * factor, num, true);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

static StatusInter SeAllocPageStub(PageMgrT *pageMgr, AllocPageParamT *allocPageParam, PageIdT *addr)
{
    return OUT_OF_MEMORY_INTER;
}

TEST_F(UtHashIndex, storage_hash_index_truncate_cache)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;

    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    IndexCtxT *idxCtx;

    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(g_pageCount, 0);
    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);

    setStubC((void *)SeAllocPage, (void *)SeAllocPageStub);

    HashIndexTruncate(g_seRunCtxForHash, htShmAddr);

    clearAllStub();

    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

uint32_t HashIndexDbHash32SameStub(const uint8_t *key, uint32_t len)
{
    DB_UNUSED(len);
    if (*key < 221) {
        return 0b00000000000000000000000000000010;
    } else {
        return 0b00000000000000000000000000000011;
    }
}

TEST_F(UtHashIndex, storage_hash_index_scalein_scan)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32SameStub);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    indexOpenCfg.callbackFunc.keyCmp = HtCompareStub;
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *metaData = &ht->memMgr.ccehMeta;

    uint32_t base = 0xabcd;
    for (uint32_t i = 0; i < 16; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, 16);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 16; ++i) {
        HpTupleAddr addr = base + 16 + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, 32);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = UtHashIndexDelete(idxCtx, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexLookup(idxCtx, 1, false, base);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 31, true, base + 1);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_TRUE(metaData->scaleInCnt == 0);

    IndexScaleInCfgT idxScaleCfg = {.splitTime = 1000, .startTime = DbRdtsc(), .isOverTime = false};
    do {
        idxScaleCfg.isOverTime = false;
        ret = HashIndexScaleIn(idxCtx, &idxScaleCfg);
        EXPECT_EQ(GMERR_OK, ret);
    } while (idxScaleCfg.isOverTime);

    EXPECT_TRUE(metaData->scaleInCnt == 1);

    ret = UtHashIndexLookup(idxCtx, 1, false, base);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 31, true, base + 1);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);

    clearStub(stubId);
}

uint32_t HashIndexDbHash32TwoStub(const uint8_t *key, uint32_t len)
{
    DB_UNUSED(key);
    DB_UNUSED(len);
    return 0b00000000000000000000000000111111;
}

// 测试 HashStashPageInsert 方法
TEST_F(UtHashIndex, storage_hash_index_stash_page)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32TwoStub);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    indexOpenCfg.callbackFunc.keyCmp = HtCompareStub;
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    // 插入10条bucket bit一样的数据，插满两个bucket
    uint32_t base = 0xabcd;
    for (uint32_t i = 0; i < 10; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, 10);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验stash page数量为0
    EXPECT_EQ(ht->stashPageNum, 0u);

    // 再插入一条bucket bit一样的数据，这是应该插入stash page中
    HpTupleAddr addr = base + 10;
    uint8_t keydata[KEY_DATA_LEN] = "";
    IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
    ret = HashIndexInsert(idxCtx, hashKey, addr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexLookup(idxCtx, 1, true, base + 10);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验stash page数量为1
    EXPECT_EQ(ht->stashPageNum, 1u);
    // dir page与seg page不会扩容
    EXPECT_EQ(ht->memMgr.ccehMeta.dirPageCount, 1u);
    EXPECT_EQ(ht->memMgr.ccehMeta.segPageCount, 1u);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);

    clearStub(stubId);
}

uint32_t HashIndexDbHash32ThreeStub(const uint8_t *key, uint32_t len)
{
    uint32_t hashCode = XXH32(key, len, DB_XXHASH_SEED);
    return hashCode |= 15;  // 保证hashcode低四位一致
}

// 测试 HashExpandHashTable 扩容方法
TEST_F(UtHashIndex, storage_hash_index_dir_expand)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32ThreeStub);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    indexOpenCfg.callbackFunc.keyCmp = HtCompareStub;
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    // 插入1000条hashcode低四位相同的数据，触发dir与seg扩容
    uint32_t base = 0xabcd;
    for (uint32_t i = 0; i < 1000; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, 1000);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(ht->stashPageNum, 0u);
    EXPECT_EQ(ht->memMgr.ccehMeta.dirPageCount, 1u);
    // seg page扩容，校验segPageCount大于1
    EXPECT_GT(ht->memMgr.ccehMeta.segPageCount, 1u);
    // dir扩容，hashcode低四位一致，dirDepth应扩容至5
    EXPECT_EQ(ht->memMgr.ccehMeta.dir.dirDepth, 5u);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);

    clearStub(stubId);
}

uint32_t HashIndexDbHash32FourStub(const uint8_t *key, uint32_t len)
{
    DB_UNUSED(len);
    if (*key >= 205 && *key < 213) {
        // 指向 seg page 的 最后一个bucket
        return 0b00000111111111000000000000000001;
    } else if (*key >= 213 && *key < 221) {
        // 指向 seg page 的 第一个bucket
        return 0b00000000000000000000000000000001;
    } else if (*key >= 221 && *key < 229) {
        // 指向 seg page 的 第二个bucket
        return 0b00000000000001000000000000000001;
    } else if (*key >= 229 && *key < 237) {
        // 指向 seg page 的 第三个bucket
        return 0b00000000000010000000000000000001;
    } else {
        // 指向 seg page 的 第一个bucket
        return 0b00000000000000000000000000000011;
    }
}

// 测试 seg page 特殊扩容场景
TEST_F(UtHashIndex, storage_hash_index_dir_segpage_expand)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32FourStub);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    seInstance->seConfig.heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_32;

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    Status ret = HashIndexCreate(g_seRunCtxForHash, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IdxAlloc(g_seRunCtxForHash, HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    indexOpenCfg.callbackFunc.keyCmp = HtCompareStub;
    ret = UtHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    uint32_t bucketEntryNums = 8;  // 普通bucket有8个entry容量

    uint32_t base = 0xabcd;
    // 插入最后一个bucket数据，实际插满第一个bucket容量
    for (uint32_t i = 0; i < bucketEntryNums; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, bucketEntryNums);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入第一个bucket数据，实际插满第二个bucket容量
    base = base + bucketEntryNums;
    for (uint32_t i = 0; i < bucketEntryNums; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, bucketEntryNums, true, base);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入第二个bucket数据，实际插满第三个bucket容量
    base = base + bucketEntryNums;
    for (uint32_t i = 0; i < bucketEntryNums; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, bucketEntryNums, true, base);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入第三个bucket数据，实际插满第四个bucket容量
    base = base + bucketEntryNums;
    for (uint32_t i = 0; i < bucketEntryNums; ++i) {
        HpTupleAddr addr = base + i;
        uint8_t keydata[KEY_DATA_LEN] = "";
        IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
        ret = HashIndexInsert(idxCtx, hashKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = UtHashIndexLookup(idxCtx, bucketEntryNums, true, base);
    EXPECT_EQ(GMERR_OK, ret);

    // 往第一个bucket插入一条数据，触发扩容
    base = base + bucketEntryNums;
    HpTupleAddr addr = base;
    uint8_t keydata[KEY_DATA_LEN] = "";
    IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
    ret = HashIndexInsert(idxCtx, hashKey, addr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexLookup(idxCtx, 1, true, base);
    EXPECT_EQ(GMERR_OK, ret);

    // 不会将数据存入stash page
    EXPECT_EQ(ht->stashPageNum, 0u);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);

    clearStub(stubId);
}

uint32_t HashIndexDbHash32FiveStub(const uint8_t *key, uint32_t len)
{
    DB_UNUSED(key);
    DB_UNUSED(len);
    return 0;
}

TEST_F(UtHashIndex, storage_hash_index_lookup_one_hashcode_zero)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HashIndexDbHash32FiveStub);
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    indexCfg.indexCap = 0;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, 1, 0xabce);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, false);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearStub(stubId);
}

TEST_F(UtHashIndex, storage_hash_index_expand_local_depth)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, 300000);
    EXPECT_EQ(GMERR_OK, ret);

    IndexScaleInCfgT idxScaleCfg = {
        .splitTime = 1000,
        .startTime = DbRdtsc(),  // 入参，分片开始的时间
        .isOverTime = false      // 出参，运行是否超过时间片
    };
    do {
        idxScaleCfg.isOverTime = false;
        ret = HashIndexScaleIn(idxCtx, &idxScaleCfg);
        EXPECT_EQ(GMERR_OK, ret);
    } while (idxScaleCfg.isOverTime);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_hash_index_insert_aged_data)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    Status ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    idxCtx->idxOpenCfg.callbackFunc.keyCmp = HtCompareAgedStub;
    ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    EXPECT_EQ(ht->entryUsed, 1u);
    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}

TEST_F(UtHashIndex, storage_list_localhash_stat_view)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    Status ret = UtHashIndexInsert(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1);
    EXPECT_EQ(GMERR_OK, ret);

    IndexStatisticsT idxStatTrunc;
    ret = ListLocalhashIndexStatView(htShmAddr, DbGetProcGlobalId(), &idxStatTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t idxPageSize;
    ret = ListLocalhashIndexViewGetPageSize(htShmAddr, DbGetProcGlobalId(), &idxPageSize);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t size = 0;
    ret = ListLocalhashIndexGetEstimateMemSize(DbGetProcGlobalId(), indexCfg, 10000, 8, &size);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_list_localhash_undo_remove)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    HpTupleAddr addr = 0xabcd;
    uint8_t keydata[KEY_DATA_LEN] = "";
    IndexKeyT hashKey = HashUtConstructKey(addr, keydata, KEY_DATA_LEN);
    Status ret = ListLocalhashIndexUndoRemove(idxCtx, hashKey, addr);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_list_localhash_undo_update)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    Status ret = UtHashIndexInsert(idxCtx, 1, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    HpTupleAddr delAddr = 0xabe0;
    uint8_t delKeydata[KEY_DATA_LEN] = "";
    IndexKeyT delHashKey = HashUtConstructKey(delAddr, delKeydata, KEY_DATA_LEN);
    HpTupleAddr insertAddr = 0xabe0;
    uint8_t insertKeydata[KEY_DATA_LEN] = "";
    IndexKeyT insertHashKey = HashUtConstructKey(insertAddr, insertKeydata, KEY_DATA_LEN);
    IndexUpdateInfoT updateInfo = {
        .oldIdxKey = delHashKey, .newIdxKey = insertHashKey, .oldAddr = delAddr, .newAddr = insertAddr};
    ret = ListLocalhashIndexUndoUpdate(idxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_list_localhash_undo_batch_update)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    Status ret = UtHashIndexInsert(idxCtx, 1, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    HpTupleAddr delAddr = 0xabe0;
    uint8_t delKeydata[KEY_DATA_LEN] = "";
    IndexKeyT delHashKey = HashUtConstructKey(delAddr, delKeydata, KEY_DATA_LEN);
    HpTupleAddr insertAddr = 0xabe0;
    uint8_t insertKeydata[KEY_DATA_LEN] = "";
    IndexKeyT insertHashKey = HashUtConstructKey(insertAddr, insertKeydata, KEY_DATA_LEN);
    IndexUpdateInfoT updateInfo = {
        .oldIdxKey = delHashKey, .newIdxKey = insertHashKey, .oldAddr = delAddr, .newAddr = insertAddr};
    ret = ListLocalhashIndexBatchUpdate(idxCtx, &updateInfo, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_list_localhash_undo_batch_delete)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    Status ret = UtHashIndexInsert(idxCtx, 1, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, true, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    HpBatchOutT addr = {.addrOut = 0xabe0};
    uint8_t keydata[KEY_DATA_LEN] = "";
    IndexKeyT hashKey = HashUtConstructKey(0xabe0, keydata, KEY_DATA_LEN);
    ret = ListLocalhashIndexBatchDelete(idxCtx, &hashKey, &addr, 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtHashIndexLookup(idxCtx, 1, false);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

TEST_F(UtHashIndex, storage_list_localhash_undo_batch_insert)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHashMutiVersion;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    setStubC((void *)SeTransGetTrxId, (void *)UtSeTransGetTrxId);
    setStubC((void *)SeTransIsCommit, (void *)UtSeTransIsCommit);
    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    idxCtx->idxOpenCfg.callbackFunc.multiVersionKeyCmp = HtCompareOldStub;

    HpBatchOutT addr = {.addrOut = 0xabe0};
    uint8_t keydata[KEY_DATA_LEN] = "";
    IndexKeyT hashKey = HashUtConstructKey(0xabe0, keydata, KEY_DATA_LEN);
    Status ret = ListLocalhashIndexBatchInsert(idxCtx, &hashKey, &addr, 1);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtx);
    IdxRelease(idxCtx);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
    clearAllStub();
}

extern "C" {
void HashRedoForUpdateMeta(PageIdT pageId, uint8_t *meta, RedoRunCtxT *redoCtx);
void HashRedoForSetSegment(PageIdT pageId, uint16_t slotId, HashDirSegmentT *seg, RedoRunCtxT *redoCtx);
void HashRedoForSegmentPageInit(PageIdT pageId, RedoRunCtxT *redoCtx);
void HashRedoForFreeDirPage(PageIdT pageId, PageIdT dirPageId, uint32_t segIndex, RedoRunCtxT *redoCtx);
void HashRedoForSetMetaStashPage(PageIdT pageId, PageIdT stashPageId, RedoRunCtxT *redoCtx);
void HashRedoForSetSegmentEntry(PageIdT pageId, uint16_t slotId, HashEntryT *entry, RedoRunCtxT *redoCtx);
void HashRedoForSetDirNextPageId(PageIdT pageId, PageIdT nextPageId, RedoRunCtxT *redoCtx);
void HashRedoForSetMetaDepth(PageIdT pageId, uint32_t depth, RedoRunCtxT *redoCtx);
void HashRedoForStashPageInit(PageIdT pageId, HtStashPageHeadT *head, RedoRunCtxT *redoCtx);
void HashRedoForSetStashEntry(PageIdT pageId, uint16_t slotId, HashEntryT *entry, RedoRunCtxT *redoCtx);
void HashRedoForSetSegmentEntryProbeLen(PageIdT pageId, uint16_t slotId, uint32_t probeLen, RedoRunCtxT *redoCtx);
void HashRedoForSegmentPageMarkDelete(PageIdT pageId, uint16_t slotId, RedoRunCtxT *redoCtx);
void HashRedoForStashPageMarkDelete(PageIdT pageId, uint16_t slotId, RedoRunCtxT *redoCtx);
void HashRedoForSetStashPrevPageId(PageIdT pageId, PageIdT prevPageId, RedoRunCtxT *redoCtx);
void HashRedoForSetStashBitMap(PageIdT pageId, uint16_t slotId, bool set, RedoRunCtxT *redoCtx);
void HashRedoForSetStashProbeLen(PageIdT pageId, uint32_t probeLen, RedoRunCtxT *redoCtx);
void HashRedoForCopyPageData(PageIdT pageId, uint32_t len, uint8_t *data, RedoRunCtxT *redoCtx);
Status HashDirConstructor(HashTableT *newHt, uint32_t dirDepth);
}

// 该用例主要用于提高行覆盖率
TEST_F(UtHashIndex, HashIndex_persistence_lines_coverage)
{
    PageIdT pageId;
    uint16_t slotId;
    uint32_t segIndex;
    bool set;
    HashRedoForUpdateMeta(pageId, NULL, NULL);
    HashRedoForSetSegment(pageId, slotId, NULL, NULL);
    HashRedoForSegmentPageInit(pageId, NULL);
    HashRedoForFreeDirPage(pageId, pageId, segIndex, NULL);
    HashRedoForSetMetaStashPage(pageId, pageId, NULL);
    HashRedoForSetSegmentEntry(pageId, slotId, NULL, NULL);
    HashRedoForSetDirNextPageId(pageId, pageId, NULL);
    HashRedoForSetMetaDepth(pageId, segIndex, NULL);
    HashRedoForStashPageInit(pageId, NULL, NULL);
    HashRedoForSetStashEntry(pageId, slotId, NULL, NULL);
    HashRedoForSetSegmentEntryProbeLen(pageId, slotId, segIndex, NULL);
    HashRedoForSegmentPageMarkDelete(pageId, slotId, NULL);
    HashRedoForStashPageMarkDelete(pageId, slotId, NULL);
    HashRedoForSetStashPrevPageId(pageId, pageId, NULL);
    HashRedoForSetStashBitMap(pageId, slotId, set, NULL);
    HashRedoForSetStashProbeLen(pageId, segIndex, NULL);
    HashRedoForCopyPageData(pageId, segIndex, NULL, NULL);
}

Status UtHashDirConstructor(HashTableT *newHt, uint32_t dirDepth)
{
    return GMERR_OUT_OF_MEMORY;
}

TEST_F(UtHashIndex, storage_list_localhash_hash_insert_truncate)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHash;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtxA;
    HashIndexInit(indexCfg, &htShmAddr, &idxCtxA);
    IndexCtxT *idxCtxB;
    HashIndexInit(indexCfg, &htShmAddr, &idxCtxB);

    Status ret = UtHashIndexInsert(idxCtxA, 1, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexTruncate(NULL, htShmAddr);

    ret = UtHashIndexInsert(idxCtxB, 1, 0xabe0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = UtHashIndexInsert(idxCtxA, 1, 0xab00);
    EXPECT_EQ(GMERR_OK, ret);

    HashIndexClose(idxCtxA);
    IdxRelease(idxCtxA);
    HashIndexClose(idxCtxB);
    IdxRelease(idxCtxB);
    HashIndexDrop(g_seRunCtxForHash, htShmAddr);
}
