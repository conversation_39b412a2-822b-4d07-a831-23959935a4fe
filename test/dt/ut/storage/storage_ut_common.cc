/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: storage_ut_common.cc
 * Description: common function for storage ut
 * Author: zhaoyongluo
 * Create: 2022/09/09
 */

#include "storage_ut_common.h"
#include "db_sysapp_context.h"
#include "db_mem_context.h"
#include "adpt_mem_segment_euler.h"
#include "db_dynmem_algo.h"
#include "db_table_space.h"

#ifdef __cplusplus
extern "C" {
#endif
extern Status UpdDynCtxAllocSizeThresholdTree(DbMemCtxT *ctx, uint32_t size);
extern void MemCtxUpdateAllocSizeOnTree(DbMemCtxT *ctx, uint64_t size, bool inc);
#ifdef __cplusplus
}
#endif

int g_stubDbDynMemCtxAllocCallCount = 0;
int g_stubDbDynMemCtxAllocCallWithErr = 0;
int g_stubDbDynMemCtxAllocCallIndex = 0;

// storage 内存态用例普遍使用，统一定义在这里
void *g_seTopDynCtx = NULL;
DbMemCtxT *g_topShmMemCtx = NULL;

void *DbDynMemCtxAllocActual(void *ctx, size_t size)
{
    DbMemCtxT *currCtx = (DbMemCtxT *)ctx;
    if (currCtx == NULL || currCtx->magicCode != DB_MAGIC_CODE) {
        return NULL;
    }
    /* If this is a shared context, make it thread safe by acquiring appropriate lock */
    Status ret = DbMemCtxLock(currCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return NULL;
    }
    ret = UpdDynCtxAllocSizeThresholdTree(currCtx, (uint32_t)size);
    if (ret != GMERR_OK) {
        DbMemCtxUnLock(currCtx);
        return NULL;
    }
    void *ptr = DynamicAlgoAlloc((DbMemCtxT *)ctx, size);
    if (ptr == NULL) {
        MemCtxUpdateAllocSizeOnTree(currCtx, (uint64_t)size, false);
    }
    DbMemCtxUnLock(currCtx);
    return ptr;
}

void *StubDbDynMemCtxAllocCount(void *ctx, size_t size)
{
    g_stubDbDynMemCtxAllocCallCount++;
    return DbDynMemCtxAllocActual(ctx, size);
}

void *StubDbDynMemCtxAllocWithErr(void *ctx, size_t size)
{
    void *ptr = NULL;
    if (g_stubDbDynMemCtxAllocCallIndex != g_stubDbDynMemCtxAllocCallWithErr) {
        ptr = DbDynMemCtxAllocActual(ctx, size);
    }
    g_stubDbDynMemCtxAllocCallIndex++;
    return ptr;
}

void StubUndoKeepThreadAlive(
    const TrxT *trx, uint64_t *splitStartTime, uint32_t undoTotalCnt, uint32_t *undoProcessedCnt)
{
    return;
}

TrxCfgT GetDefaultTrxCfg()
{
    return {
        .readOnly = false,
        .isLiteTrx = false,
        .isBackGround = false,
        .isInteractive = false,
        .isTrxForceCommit = false,
        .isRetryTrx = false,
        .isTrxAllowCloned = false,
        .isCloneTrx = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
        .reserve = 0,
#endif
        .connId = 8888,
        .trxType = PESSIMISTIC_TRX,
        .isolationLevel = READ_COMMITTED,
    };
}

TrxCfgT GetOptimisticTrxCfg()
{
    return {
        .readOnly = false,
        .isLiteTrx = false,
        .isBackGround = false,
        .isInteractive = false,
        .isTrxForceCommit = false,
        .isRetryTrx = false,
        .isTrxAllowCloned = false,
        .isCloneTrx = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
        .reserve = 0,
#endif
        .connId = 8888,
        .trxType = OPTIMISTIC_TRX,
        .isolationLevel = REPEATABLE_READ,
    };
}

Status ArtKeyCmpStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    return GMERR_OK;
}

void UtCreateHeap(SeRunCtxT *seRunCtx, ShmemPtrT *heapShmAddr, bool isPersistent, uint32_t labelId)
{
    ASSERT_EQ(GMERR_OK, SeTransBegin(seRunCtx, NULL));
    HeapAccessCfgT heapCfg = {.pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = isPersistent,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = labelId,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = DB_MAX_UINT64};
    ASSERT_EQ(GMERR_OK, HeapLabelCreate(seRunCtx, &heapCfg, heapShmAddr));
    ASSERT_EQ(GMERR_OK, SeTransCommit(seRunCtx));
}

void UtDropHeap(SeRunCtxT *seRunCtx, ShmemPtrT heapShmAddr)
{
    ASSERT_EQ(GMERR_OK, SeTransBegin(seRunCtx, NULL));
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ASSERT_EQ(GMERR_OK, HeapLabelDrop(seRunCtx, &heapCntrAcsInfo, NULL));
    ASSERT_EQ(GMERR_OK, SeTransCommit(seRunCtx));
}

void UtPessimisticTrxBegin(SeRunCtxHdT seRunCtx)
{
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = PESSIMISTIC_TRX;
    trxCfg.isolationLevel = READ_COMMITTED;
    trxCfg.isInteractive = true;
#ifdef FEATURE_GQL
    trxCfg.skipRowLockPessimisticRR = false;
#endif
    Status ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(GMERR_OK, ret);
}

void UtOptimisticTrxBegin(SeRunCtxHdT seRunCtx, bool isFetch)
{
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    trxCfg.isInteractive = true;
#ifdef FEATURE_GQL
    trxCfg.skipRowLockPessimisticRR = false;
#endif
    Status ret = SeTransBegin(seRunCtx, &trxCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = SeTransAssignReadView(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    if (!isFetch) {
        SeTransSetLabelModifiedActive(seRunCtx);
    }
}

void UtTrxCommit(SeRunCtxHdT seRunCtx)
{
    Status ret = SeTransCommit(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
}

void UtTrxRollback(SeRunCtxHdT seRunCtx)
{
    Status ret = SeTransRollback(seRunCtx, false);
    ASSERT_EQ(GMERR_OK, ret);
}

// 取消原函数对于labelId的约束判断,仅用例使用
StatusInter HeapCreateMock(SeRunCtxHdT seRunCtx, const HeapAccessCfgT *heapCfg, ShmemPtrT *heapShmAddr)
{
    DB_UNUSED(seRunCtx);
    DB_POINTER2(heapCfg, heapShmAddr);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapCfg->seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "NULL seInstance:%" PRIu16, heapCfg->seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapCfg->seInstanceId);
    if (heapMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv memCtxId:%" PRIu32, seInstance->heapShmMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    if (heapCfg->pageType >= HEAP_INVALID_PAGE_TYPE) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "page type %" PRId32, (int32_t)heapCfg->pageType);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    if (heapCfg->pageType == HEAP_FIX_LEN_ROW_PAGE) {
        PageTotalSizeT pageSize = (PageTotalSizeT)seInstance->seConfig.pageSize * DB_KIBI;
        if (heapCfg->fixRowSize > SeGetUndoPageMaxRecordSize(pageSize)) {
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "row size %" PRIu16, heapCfg->fixRowSize);
            return INVALID_PARAMETER_VALUE_INTER;
        }
    }

    // 申请创建heap容器中间体所需共享内存, 在删除heap的时候释放
    HeapJumpT *newHeapJump = (HeapJumpT *)SeShmAlloc(heapMemCtx, sizeof(HeapJumpT), heapShmAddr);
    if (newHeapJump == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "heap shmMalloc");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_sp((void *)newHeapJump, sizeof(HeapJumpT), 0, sizeof(HeapJumpT));
    newHeapJump->cfg = *heapCfg;
    newHeapJump->cfg.heapFileId = SeGetNewTrmId(seInstance);
    newHeapJump->cfg.heapFsmFileId = SeGetNewTrmId(seInstance);
    newHeapJump->heapShmAddr = DB_INVALID_SHMPTR;
    newHeapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    newHeapJump->isInit = false;
    DbSpinInit(&newHeapJump->lock);
    DB_LOG_INFO("create heap jump ok, labelId:%" PRIu32, heapCfg->labelId);
    return STATUS_OK_INTER;
}

// 取消原函数对于labelId的约束判断,仅用例使用
StatusInter MdCreateCachedPageListMock(MdMgrT *mgr, uint32_t spaceId, uint32_t labelId)
{
    DB_POINTER(mgr);
    DB_ASSERT(spaceId < DB_TABLE_SPACE_MAX_NUM);
    MdSpaceT *space = &mgr->spaceMgr->spaceArray[spaceId];
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    if (shmArray == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get md space list shm, (segid:%" PRIu32 " offset:%" PRIu32 ")",
            space->cachedListsShm.segId, space->cachedListsShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    SpaceMgrLock(mgr->spaceMgr);
    DbShmArrayIteratorT iter;
    LabelCachePageListT *cachePageList;
    for (DbShmArrayInitIterator(&iter, shmArray); DbShmArrayIteratorValid(&iter); DbShmArrayIteratorNext(&iter)) {
        cachePageList = (LabelCachePageListT *)DbShmArrayIteratorGetItem(&iter);
        if (cachePageList->labelId == labelId) {
            SpaceMgrUnlock(mgr->spaceMgr);
            return STATUS_OK_INTER;
        }
    }

    // 新建链表
    uint32_t itemId;
    DbArrayAddrT arrAddr;
    Status status = DbShmArrayGetItem(shmArray, &itemId, (void **)&cachePageList, &arrAddr);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status,
            "create new cache page list, spaceId:%" PRIu32 ", itemId:%" PRIu32 ", labelId:%" PRIu32 ")", spaceId,
            itemId, labelId);
        SpaceMgrUnlock(mgr->spaceMgr);
        return DbGetStatusInterErrno(status);
    }

    cachePageList->labelId = labelId;
    cachePageList->head = SE_INVALID_PAGE_ADDR;
    cachePageList->listCnt = 0;
    SpaceMgrUnlock(mgr->spaceMgr);
    return STATUS_OK_INTER;
}
