#include <stdlib.h>
#include <sys/types.h>
#include <unistd.h>
#include <vector>
#include <thread>
#include "gtest/gtest.h"
#include "stub.h"
#include "securec.h"
#include "adpt_types.h"
#include "gmc_errno.h"
#include "adpt_spinlock.h"
#include "se_lfsmgr.h"
#include "db_mem_context.h"
#include "adpt_mem_segment_euler.h"
#include "db_dynmem_algo.h"
#include "se_heap.h"
#include "se_heap_access.h"
#include "se_heap_inner.h"
#include "se_heap_utils.h"
#include "adpt_thread.h"
#include "common_init.h"
#include "dm_data_basic.h"
#include "dm_meta_prop_label.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "db_label_latch_mgr.h"
#include "dm_meta_vertex_label.h"
using namespace std;

#define UT_DEBUG
#ifdef UT_DEBUG
#define UT_PRINTF(format, ...) printf(format, ##__VA_ARGS__)
#else
#define UT_PRINTF(format, ...)
#endif

#define PAGE_SIZE_TYPE 5

extern ShmemPtrT g_VarHeapShmAddr;
extern SeRunCtxHdT g_seRunCtx;
static const uint32_t g_NUM = 1000;
extern ShmemPtrT g_FixHeapShmAddr;
extern DmVertexLabelT *stubVtxLabel;

extern void UtHeapAmBasicPrepareSeInstance(PageTotalSizeT pageSize);

static void UtHeapAMBasicCreateVarLabel(int slotExtend = 0)
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = static_cast<PageSizeT>(slotExtend),
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = UT_LABEL_ID,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 1000000,
    };
    ShmemPtrT heapShmAddr;
    int32_t ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_VarHeapShmAddr = heapShmAddr;
    stubVtxLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
}
extern void UtHeapAMBasicDrop();

class UtStorageDiffPage : public testing::Test {
protected:
    virtual void TearDown()
    {
        clearAllStub();
    }

    virtual int AllocObjects()
    {
        return 0;
    }

    virtual void FreeObjects()
    {}

    static void SetUpTestCase(){};

    static void TearDownTestCase(){};
};

void HeapScanTupleBuffer_Insert_Scan_Delete()
{
    void *usrMemCtx = g_seTopDynCtx;
    HpRunHdlT heapHdl;
    HpOpTypeE opType = HEAP_OPTYPE_INSERT;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_VarHeapShmAddr,
        .seRunCtx = g_seRunCtx,
        .dmInfo = stubVtxLabel,
        .isBackGround = false};
    int32_t ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, opType, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    /* 逐条插入, 然后scan, 然后批量查询, 删除 */
    vector<HpTupleAddr> heapItemList;
    char tupleBuf[50] = "";
    HpTupleAddr addr;
    for (uint32_t i = 0; i < g_NUM; ++i) {
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, strlen(tupleBuf) + 1, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
    }
    HeapLabelResetCtx(heapHdl);
    HeapLabelReleaseRunctx(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    uint32_t scanCnt = 0;
    HpFetchedAuxInfoT auxInfo = {.actualFetchRowsCnt = 0, .isEof = false, .reserve = {0}, .fetchRowTupleAddr = 0};
    HeapScanCursorT *heapCursor;
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)usrMemCtx);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false};
    beginScanCfg.beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginScanCfg.beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginScanCfg.maxFetchNum = 1;
    beginScanCfg.heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    ret = HeapLabelBeginScanHpTupleBuffer((HeapRunCtxT *)heapHdl, &beginScanCfg, &heapCursor);
    ASSERT_EQ(0, ret);
    while (true) {
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers((HeapRunCtxT *)heapHdl, heapCursor, &heapTupleBuf, &fetchedRowInfo, &auxInfo);
        if (auxInfo.isEof) {
            printf("Scan result: %d\n", ret);
            break;
        } else {
            ASSERT_EQ(0, ret);
            EXPECT_TRUE(heapCursor->isFetched);  // 不是EOF，应该设置成true
            scanCnt++;
        }
    }
    printf("scanCnt: %u\n", scanCnt);
    EXPECT_EQ(g_NUM, scanCnt);
    HeapLabelEndScan(heapHdl, heapCursor);
    HeapLabelResetCtx(heapHdl);
    HeapLabelReleaseRunctx(heapHdl);

    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    ASSERT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)usrMemCtx);
    ASSERT_EQ(0, ret);
    for (auto &heapAddr : heapItemList) {
        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
    }
    HeapLabelResetCtx(heapHdl);
    HeapLabelReleaseRunctx(heapHdl);
}

// 测试不同的heap是否支持不同的pagesize
TEST_F(UtStorageDiffPage, HeapDiffPageSize)
{
    // maxSeMem需要计算
    PERSISTENCE_NOT_SUPPORT;
    // 此处注意资源的申请释放顺序！

    uint64_t pageSize[PAGE_SIZE_TYPE] = {SE_CONF_MIN_PAGE_SIZE, 8, 16, 32, SE_CONF_MAX_PAGE_SIZE};
    ASSERT_EQ(SE_CONF_MIN_PAGE_SIZE, 4);
    ASSERT_EQ(SE_CONF_MAX_PAGE_SIZE, 64);

    for (int i = 0; i < PAGE_SIZE_TYPE; i++) {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        g_VarHeapShmAddr = DB_INVALID_SHMPTR;
        g_FixHeapShmAddr = DB_INVALID_SHMPTR;
        UtHeapAmBasicPrepareSeInstance(pageSize[i]);
        UtHeapAMBasicCreateVarLabel();
        HeapCntrAcsInfoT info = {
            .heapShmAddr = g_VarHeapShmAddr,
            .isPersistent = false,
            .isUseRsm = false,
            .instanceId = GET_INSTANCE_ID,
        };
        ret = HeapLabelTruncate(NULL, &info, NULL);
        ASSERT_EQ(0, ret);

        TrxT *trx = (TrxT *)(g_seRunCtx)->trx;
        TrxCfgT trxCfg = GetDefaultTrxCfg();
        ret = TrxBegin(trx, &trxCfg);
        ReadViewPrepare(trx);
        EXPECT_EQ(GMERR_OK, ret);

        HeapScanTupleBuffer_Insert_Scan_Delete();

        ret = TrxCommit((TrxT *)(g_seRunCtx)->trx);
        EXPECT_EQ(GMERR_OK, ret);

        UtHeapAMBasicDrop();
        SeReleasePageMgr((SeInstanceT *)g_seRunCtx->seIns);
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    }
}

void CreatStorageWithDiffDevice(uint32_t deviceSize)
{
    g_topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    if (g_topShmMemCtx == NULL) {
        ASSERT_EQ(0, 1);
    }
    DbMemCtxT *shmCtx = (DbMemCtxT *)g_topShmMemCtx;

    SeConfigT config = {0};
    config.deviceSize = deviceSize * DB_KIBI;
    config.pageSize = SE_DEFAULT_PAGE_SIZE;
    config.maxSeMem = 1024 * DB_KIBI;  // 1G最大存储
    config.instanceId = GET_INSTANCE_ID;
    config.deadlockCheckPeriod = 4000;
    config.lockJumpQueuePeriod = 5000;
    config.lockWakeupPeriod = 1000;
    config.lockTimeOut = 10000;
    config.maxTrxNum = MAX_TRX_NUM;

    SeInstanceHdT sePtr = NULL;
    int32_t ret = SeCreateInstance(NULL, (DbMemCtxT *)g_seTopDynCtx, &config, &sePtr);
    ASSERT_EQ(ret, GMERR_OK);
    DbInitLabelLatchMgr(NULL);

    DbMemCtxArgsT topDyncCtxCfg = {0};
    topDyncCtxCfg.ctxSize = sizeof(DbDynamicMemCtxT);
    topDyncCtxCfg.memType = DB_DYNAMIC_MEMORY;
    topDyncCtxCfg.init = DynamicAlgoInit;
    g_seTopDynCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "se dynamic", &topDyncCtxCfg);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    ASSERT_EQ(ret, 0);
    ShmemPtrT shmPtr;
    MetaShmAlloc((DbMemCtxT *)shmCtx, (void **)&stubVtxLabel, &shmPtr, sizeof(DmVertexLabelT));
    memset_s(stubVtxLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    stubVtxLabel->metaCommon.metaShmPtr = shmPtr;
    size_t size = sizeof(MetaVertexLabelT) + sizeof(DmVlIndexLabelT) + sizeof(DmSchemaT) + sizeof(DmPropertySchemaT);
    MetaShmAlloc((DbMemCtxT *)shmCtx, (void **)&stubVtxLabel->metaVertexLabel, &shmPtr, size);
    memset_s(stubVtxLabel->metaVertexLabel, size, 0, size);
    stubVtxLabel->metaCommon.metaShmPtr = shmPtr;
    MetaShmAlloc((DbMemCtxT *)shmCtx, (void **)&stubVtxLabel->commonInfo, &stubVtxLabel->commonInfoShmPtr,
        sizeof(VertexLabelCommonInfoT));
    srand(time(NULL));
}

TEST_F(UtStorageDiffPage, DiffDeviceSize)
{
    // maxSeMem需要计算
    PERSISTENCE_NOT_SUPPORT;
#if (defined HPE)
    uint32_t deviceSize[] = {1, 4, 8, 128};
#else
    uint32_t deviceSize[] = {1, 4, 8, 128, 256, 512};
#endif
    for (int i = 0; i < (int32_t)(sizeof(deviceSize) / sizeof(deviceSize[0])); i++) {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        g_VarHeapShmAddr = DB_INVALID_SHMPTR;
        g_FixHeapShmAddr = DB_INVALID_SHMPTR;
        CreatStorageWithDiffDevice(deviceSize[i]);
        UtHeapAMBasicCreateVarLabel();
        HeapCntrAcsInfoT info = {
            .heapShmAddr = g_VarHeapShmAddr,
            .isPersistent = false,
            .isUseRsm = false,
            .instanceId = GET_INSTANCE_ID,
        };
        ret = HeapLabelTruncate(NULL, &info, NULL);
        ASSERT_EQ(0, ret);

        TrxT *trx = (TrxT *)(g_seRunCtx)->trx;
        TrxCfgT trxCfg = GetDefaultTrxCfg();
        ret = TrxBegin(trx, &trxCfg);
        ReadViewPrepare(trx);
        EXPECT_EQ(GMERR_OK, ret);

        HeapScanTupleBuffer_Insert_Scan_Delete();

        ret = TrxCommit((TrxT *)(g_seRunCtx)->trx);
        EXPECT_EQ(GMERR_OK, ret);

        UtHeapAMBasicDrop();
        SeReleasePageMgr((SeInstanceT *)g_seRunCtx->seIns);
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    }
}
