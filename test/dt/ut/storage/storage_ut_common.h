#include <stdlib.h>
#include <sys/types.h>
#include <unistd.h>
#include <vector>
#include <thread>
#include <stdio.h>
#include "gtest/gtest.h"
#include "stub.h"
#include "securec.h"
#include "adpt_types.h"
#include "gmc_errno.h"
#include "db_log.h"
#include "db_internal_error.h"
#include "se_define.h"
#include "se_index.h"
#include "se_trx.h"
#include "se_heap_inner.h"
#include "se_spacemgr.h"

using namespace std;

#define UT_LABEL_ID 0xFACEABEE

#if (defined HPE)
#define MAX_TRX_NUM 64 + MAX_BG_WORKER_NUM
#else
#define MAX_TRX_NUM MAX_CONN_NUM + MAX_BG_WORKER_NUM
#endif

#ifndef GET_INSTANCE_ID
#define GET_INSTANCE_ID 1
#endif

#ifdef FEATURE_PERSISTENCE
#define PERSISTENCE_NOT_SUPPORT return
#else
#define PERSISTENCE_NOT_SUPPORT
#endif

extern int g_stubDbDynMemCtxAllocCallCount;
extern int g_stubDbDynMemCtxAllocCallWithErr;
extern int g_stubDbDynMemCtxAllocCallIndex;

extern void *g_seTopDynCtx;
extern DbMemCtxT *g_topShmMemCtx;

void *StubDbDynMemCtxAllocCount(void *ctx, size_t size);

void *StubDbDynMemCtxAllocWithErr(void *ctx, size_t size);

void StubUndoKeepThreadAlive(
    const TrxT *trx, uint64_t *splitStartTime, uint32_t undoTotalCnt, uint32_t *undoProcessedCnt);

inline int32_t SeUtCheckLastErrorStr(const char *error)
{
    TextT *lastError = DbGetLastErrorInfo();

    if (lastError == NULL || lastError->len == 0) {
        return -1;
    }

    if (strcmp(lastError->str, error) != 0) {
        printf("last error: %s\n", lastError->str);
        printf("expected last error: %s\n", error);
        return -1;
    }
    return GMERR_OK;
}

inline int32_t SeUtCheckLastError(int32_t exError, int32_t inError)
{
    TextT *lastError = DbGetLastErrorInfo();

    if (lastError == NULL || lastError->len == 0) {
        return -1;
    }

    if (lastError == NULL || lastError->len == 0 ||
        (strstr(lastError->str, "LASTERR") == NULL)) {  // 可能是last error格式发生了变化
        return -1;
    }

    char interError[32] = "";
    char errInfo[512] = "";
    int32_t ret =
        sscanf_s(lastError->str, "%*[^L]LASTERR %s %s", interError, sizeof(interError), errInfo, sizeof(errInfo));
    if (ret == -1) {
        printf("last error: %s\n", lastError->str);
        return -1;
    }
    interError[strlen(interError) - 1] = '\0';
    interError[0] = '0';
    int32_t errTmp = exError * MAX_INTERNAL_ERR_CODE + atoi(interError);
    if (errTmp != inError) {
        printf("last error: %s\n", lastError->str);
        EXPECT_EQ(errTmp, inError);
        return -1;
    }
    return GMERR_OK;
}

TrxCfgT GetDefaultTrxCfg();

TrxCfgT GetOptimisticTrxCfg();

Status ArtKeyCmpStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch);

void UtCreateHeap(SeRunCtxT *seRunCtx, ShmemPtrT *heapShmAddr, bool isPersistent = false, uint32_t labelId = 0);

void UtDropHeap(SeRunCtxT *seRunCtx, ShmemPtrT heapShmAddr);

void UtPessimisticTrxBegin(SeRunCtxHdT seRunCtx);

void UtOptimisticTrxBegin(SeRunCtxHdT seRunCtx, bool isFetch = false);

void UtTrxCommit(SeRunCtxHdT seRunCtx);

void UtTrxRollback(SeRunCtxHdT seRunCtx);

StatusInter HeapCreateMock(SeRunCtxHdT seRunCtx, const HeapAccessCfgT *heapCfg, ShmemPtrT *heapShmAddr);

StatusInter MdCreateCachedPageListMock(MdMgrT *mgr, uint32_t spaceId, uint32_t labelId);
