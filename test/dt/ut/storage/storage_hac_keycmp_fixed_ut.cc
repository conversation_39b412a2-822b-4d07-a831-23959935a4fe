/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: hardware offloading ut for keep logic of heap & device/page mgr unchanged
 * Author: lijianchuan
 * Create: 2024/5/20
 */

#include <random>
#include "common_init.h"
#include "se_capacity_def_inner.h"
#include "storage_ut_common.h"
#include "db_table_space.h"
#include "se_instance.h"
#include "se_page_mgr.h"
#include "se_device.h"
#include "se_memdata.h"
#include "se_spacemgr.h"
#include "db_dynmem_algo.h"
#include "dm_meta_basic_in.h"
#include "storage_session.h"
#include "se_heap.h"
#include "se_heap_page.h"
#include "se_heap_fetch.h"

class UtHacFixedKeyCmpPage : public testing::Test {
public:
    static PageIdT *gPageAddrs;
    static int32_t gAllocCount;
    static uint32_t gDeviceCnt;

protected:
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        DbMemCtxT *g_topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
        if (g_topShmMemCtx == NULL) {
            ASSERT_EQ(0, 1);
        }

        // 配置页大小16k，一个device 4M。最大内存500M
        SeConfigT config = {0};
        config.deviceSize = DbCfgGetInt32Lite(DB_CFG_SE_DEV_SIZE, NULL) * DB_KIBI;
        config.pageSize = DbCfgGetInt32Lite(DB_CFG_SE_PAGE_SIZE, NULL);
        config.instanceId = GET_INSTANCE_ID;
        config.maxSeMem = DbCfgGetInt32Lite(DB_CFG_SE_MAX_MEM, NULL) * DB_KIBI;
        config.maxTrxNum = MAX_TRX_NUM;

        SeInstanceT *seInstanceFromCreate = NULL;
        ret = SeCreateInstance(NULL, (DbMemCtxT *)g_topShmMemCtx, &config, (SeInstanceHdT *)&seInstanceFromCreate);
        ASSERT_EQ(ret, GMERR_OK);
        SeInstanceT *seInstanceFromGet = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        ASSERT_TRUE(seInstanceFromCreate == seInstanceFromGet);
        gAllocCount = config.maxSeMem / (2 * config.pageSize);
        gDeviceCnt = config.maxSeMem / config.deviceSize;
        if (gPageAddrs == NULL) {
            gPageAddrs = new PageIdT[gAllocCount];
            ASSERT_TRUE(gPageAddrs != NULL);
        }
    };

    static void TearDownTestCase()
    {
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
        if (gPageAddrs) {
            delete[] gPageAddrs;
            gPageAddrs = NULL;
        }
    };
};

int32_t UtHacFixedKeyCmpPage::gAllocCount = 0;
PageIdT *UtHacFixedKeyCmpPage::gPageAddrs = NULL;
uint32_t UtHacFixedKeyCmpPage::gDeviceCnt = 0;

// 看护HeapUncompressTupleAddr函数，tupleAddr->pageId/slotId的解析语义不可变
// 更新：放松对tupleAddr的看护，使用shiftBit和Mask的方式
// 更新：调整tupleAddr32的pageId和slotId顺序，保持和tupleAddr分布一致（pageId在低位，slotId在高位）
// 更新：复原对tupleAddr32的pageId和slotId顺序的修改
TEST_F(UtHacFixedKeyCmpPage, testHeapUncompressTupleAddr)
{
    std::default_random_engine e(time(NULL));
    uniform_int_distribution<uint32_t> u32;
    uint32_t pageAddrShiftBits32 = SE_TUPLE_SLOT_ID_BIT_FIELD;  // 支持SE_TUPLE_SLOT_ID_BIT_FIELD修改
    uint32_t pageAddrMask32 = ~(DB_MAX_UINT32 << pageAddrShiftBits32);
    for (uint32_t i = 0; i < 100; i++) {
        uint32_t addr32 = u32(e);
        TupleAddr addr = HeapUncompressTupleAddr((const void *)&addr32, SE_HEAP_TUPLE_ADDR_32);
        TuplePointerOrTupleAddr tp = (TuplePointerOrTupleAddr){.addr = addr};
        ASSERT_TRUE(tp.ptr.pageId == (addr32 >> pageAddrShiftBits32));
        ASSERT_TRUE(tp.ptr.slotId == (addr32 & pageAddrMask32));
    }

    uniform_int_distribution<uint64_t> u64;
    uint64_t pageAddrShiftBits64 = 32;  // 支持TuplePointerT 位域修改，但不支持调换顺序
    uint64_t pageAddrMask64 = ~(DB_MAX_UINT64 << pageAddrShiftBits64);
    for (uint32_t i = 0; i < 100; i++) {
        uint64_t addr64 = u64(e);
        TupleAddr addr = HeapUncompressTupleAddr((const void *)&addr64, SE_HEAP_TUPLE_ADDR_64);
        TuplePointerOrTupleAddr tp = (TuplePointerOrTupleAddr){.addr = addr};
        ASSERT_TRUE(tp.ptr.pageId == (addr64 & pageAddrMask64));
        ASSERT_TRUE(tp.ptr.slotId == (addr64 >> pageAddrShiftBits64));
    }
}

// 看护DeserializePageId函数，pageId->blockId/deviceId的解析语义不可变
TEST_F(UtHacFixedKeyCmpPage, testDeserializePageId)
{
    PageMgrT *pageMgr = (PageMgrT *)SeGetPageMgr(GET_INSTANCE_ID);
    int32_t deviceSize = DbCfgGetInt32Lite(DB_CFG_SE_DEV_SIZE, NULL);
    int32_t pageSize = DbCfgGetInt32Lite(DB_CFG_SE_PAGE_SIZE, NULL);
    uint32_t chunkCntPerDev = (uint32_t)deviceSize * DB_KIBI / (uint32_t)pageSize;
    uint32_t pageAddrShiftBit = log2(chunkCntPerDev);
    uint32_t bitMask = ~(DB_MAX_UINT32 << pageAddrShiftBit);
    ASSERT_EQ(pageAddrShiftBit, pageMgr->pageAddrShiftBit);

    std::default_random_engine e(time(NULL));
    uniform_int_distribution<uint32_t> u;
    for (uint32_t i = 0; i < 100; i++) {
        uint32_t id = u(e);
        PageIdT pageId = DeserializePageId(pageMgr, id);
        ASSERT_EQ(pageId.blockId, id & bitMask);
        ASSERT_EQ(pageId.deviceId, id >> pageAddrShiftBit);
    }
}

// 看护DevGetEntryHac函数，获取device的全量缓存，相关功能需要看护
TEST_F(UtHacFixedKeyCmpPage, testDevGetEntryHac)
{
    PageMgrT *pageMgr = (PageMgrT *)SeGetPageMgr(GET_INSTANCE_ID);

    int32_t ret = STATUS_OK_INTER;
    // index不使用rsmUndo
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 1, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (int32_t i = 0; i < gAllocCount; i++) {
        gPageAddrs[i] = SE_INVALID_PAGE_ADDR;
        ret = SeAllocPage(pageMgr, &allocPageParam, &gPageAddrs[i]);
        ASSERT_EQ(ret, STATUS_OK_INTER);
        ASSERT_TRUE(DbIsPageIdValid(gPageAddrs[i]));
    }
    MdMgrT *memdataMgr = (MdMgrT *)pageMgr;
    DeviceMgrT *devMgr = memdataMgr->devMgr;
    uint8_t **addr = DevGetEntryHac();

    for (int32_t i = gDeviceCnt - 1; i >= 0; i--) {
        uint8_t *virAddr = (uint8_t *)DbShmPtrToAddrAlign(devMgr->devDesc[i].devPtr, devMgr->sysPageSize);
        ASSERT_EQ(virAddr, addr[i]);
    }

    for (int32_t i = 0; i < gAllocCount; i++) {
        FreePageParamT freePageParam =
            SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, gPageAddrs[i], NULL, NULL, SE_INVALID_LABEL_ID, false);
        ret = SeFreePage(pageMgr, &freePageParam);
        ASSERT_EQ(ret, STATUS_OK_INTER);
    }
    for (int32_t i = gDeviceCnt - 1; i >= 0; i--) {
        uint8_t *virAddr = (uint8_t *)DbShmPtrToAddrAlign(devMgr->devDesc[i].devPtr, devMgr->sysPageSize);
        ASSERT_EQ(virAddr, addr[i]);
    }

    for (int32_t i = gDeviceCnt - 1; i > 0; i--) {
        if (addr[i] == NULL) {
            uint8_t *virAddr = (uint8_t *)DbShmPtrToAddrAlign(devMgr->devDesc[i].devPtr, devMgr->sysPageSize);
            ASSERT_EQ(virAddr, addr[i]);
            continue;
        }
        DevReturnDevice(devMgr, i, true);
        if (i >= DEV_CACHE_CAPACITY) {
            ASSERT_TRUE(addr[i] == NULL);
        } else {
            ASSERT_FALSE(addr[i] == NULL);
        }
        uint8_t *virAddr = (uint8_t *)DbShmPtrToAddrAlign(devMgr->devDesc[i].devPtr, devMgr->sysPageSize);
        ASSERT_EQ(virAddr, addr[i]);
    }
}

// 看护MdGetPage函数，deviceAddr[deviceId] + blockId << shiftBit语义不可变
TEST_F(UtHacFixedKeyCmpPage, testMdGetPage)
{
    PageMgrT *pageMgr = (PageMgrT *)SeGetPageMgr(GET_INSTANCE_ID);
    MdMgrT *memdataMgr = (MdMgrT *)pageMgr;
    uint8_t **addr = DevGetEntryHac();

    int32_t ret = STATUS_OK_INTER;
    // index不使用rsmUndo
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 1, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (int32_t i = 0; i < gAllocCount; i++) {
        gPageAddrs[i] = SE_INVALID_PAGE_ADDR;
        ret = SeAllocPage(pageMgr, &allocPageParam, &gPageAddrs[i]);
        ASSERT_EQ(ret, STATUS_OK_INTER);
        ASSERT_TRUE(DbIsPageIdValid(gPageAddrs[i]));
    }
    int32_t pageSize = DbCfgGetInt32Lite(DB_CFG_SE_PAGE_SIZE, NULL) * DB_KIBI;
    uint32_t pageSizeShiftBit = log2(pageSize);
    for (int32_t i = 0; i < gAllocCount; i++) {
        uint8_t *pageHead = NULL;
        ret = MdGetPage(memdataMgr, gPageAddrs[i], &pageHead, ENTER_PAGE_NORMAL, true);
        ASSERT_EQ(ret, STATUS_OK_INTER);
        ASSERT_FALSE(addr[gPageAddrs[i].deviceId] == NULL);
        ASSERT_EQ(pageHead, addr[gPageAddrs[i].deviceId] + (gPageAddrs[i].blockId << pageSizeShiftBit));
    }
}

void UtHeapAutoCommitAndBegin2(SeRunCtxHdT seRunCtx, bool isRollBack = false)
{
    int32_t ret;
    if (isRollBack) {
        ret = SeTransRollback(seRunCtx, false);
        ASSERT_EQ(ret, 0);
    } else {
        ret = SeTransCommit(seRunCtx);
        ASSERT_EQ(ret, 0);
    }

    ret = SeTransBegin(seRunCtx, nullptr);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(seRunCtx);
    ASSERT_EQ(ret, 0);
}
ShmemPtrT g_varHeapShmAddr;
ShmemPtrT g_fixHeapShmAddr;

SeRunCtxHdT g_seRunCtx10;
DmVertexLabelT *stubVtxLabel2 = NULL;

void UtHeapAmBasicPrepareSeInstance2(PageTotalSizeT pageSize)
{
    g_topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    if (g_topShmMemCtx == nullptr) {
        ASSERT_EQ(0, 1);
    }

    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = pageSize;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
    config.deadlockCheckPeriod = 4000;
    config.lockJumpQueuePeriod = 5000;
    config.lockWakeupPeriod = 1000;
    config.lockTimeOut = 10000;
    config.maxTrxNum = MAX_TRX_NUM;
    config.maxLockShareCnt = MAX_CONN_NUM / 2;

    SeInstanceHdT sePtr = nullptr;
    int32_t ret = SeCreateInstance(NULL, (DbMemCtxT *)g_topShmMemCtx, &config, &sePtr);
    ASSERT_EQ(ret, GMERR_OK);

    DbMemCtxArgsT topDyncCtxCfg = {0};
    topDyncCtxCfg.ctxSize = sizeof(DbDynamicMemCtxT);
    topDyncCtxCfg.memType = DB_DYNAMIC_MEMORY;
    topDyncCtxCfg.init = DynamicAlgoInit;
    g_seTopDynCtx = (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "se dynamic", &topDyncCtxCfg);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx10);
    ASSERT_EQ(ret, 0);
    ShmemPtrT shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabel2, &shmPtr, sizeof(DmVertexLabelT));
    memset_s(stubVtxLabel2, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    stubVtxLabel2->metaCommon.metaShmPtr = shmPtr;
    size_t size = sizeof(MetaVertexLabelT) + sizeof(DmVlIndexLabelT) + sizeof(DmSchemaT) + sizeof(DmPropertySchemaT);
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabel2->metaVertexLabel, &shmPtr, size);
    memset_s(stubVtxLabel2->metaVertexLabel, size, 0, size);
    stubVtxLabel2->metaCommon.metaShmPtr = shmPtr;
    MetaShmAlloc((DbMemCtxT *)g_topShmMemCtx, (void **)&stubVtxLabel2->commonInfo, &stubVtxLabel2->commonInfoShmPtr,
        sizeof(VertexLabelCommonInfoT));
}

static uint64_t g_namespaceTrxIdArray[100] = {0};

static Status UtGetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance)
{
    if (trxId == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *trxId = g_namespaceTrxIdArray[labelId];
    *trxIdLastModify = g_namespaceTrxIdArray[labelId];
    return GMERR_OK;
}

static Status UtSetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance)
{
    g_namespaceTrxIdArray[labelId] = trxId;
    return GMERR_OK;
}

static Status UtGetLabelNameByEdgeLabelId(uint32_t elId, char *labelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelName);
    return GMERR_DATA_EXCEPTION;
}

class UtHacFixedKeyCmpHeap : public testing::Test {
protected:
    void SetUp()
    {
        UtHeapAutoCommitAndBegin2(g_seRunCtx10);
    }

    virtual void TearDown()
    {
        int32_t ret = TrxCommit((TrxT *)(g_seRunCtx10)->trx);
        EXPECT_EQ(GMERR_OK, ret);
        clearAllStub();
    }

    virtual int AllocObjects()
    {
        return 0;
    }

    virtual void FreeObjects()
    {}

    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != STATUS_OK_INTER) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        g_varHeapShmAddr = DB_INVALID_SHMPTR;
        g_fixHeapShmAddr = DB_INVALID_SHMPTR;
        UtHeapAmBasicPrepareSeInstance2(SE_DEFAULT_PAGE_SIZE);
        UtHeapAutoCommitAndBegin2(g_seRunCtx10);
        OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelLastTrxIdAndTrxCommitTimeById, UtGetLabelLastTrxIdAndTrxCommitTimeById,
            UtGetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[TRX_CHECK_READVIEW_NUM] = {
            UtSetLabelLastTrxIdAndTrxCommitTimeById, UtSetLabelLastTrxIdAndTrxCommitTimeById,
            UtSetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId};
        SeInitTrxMgrCheckFunc(GET_INSTANCE_ID, setFunc, getFunc, getLabelName);
    };

    static void TearDownTestCase()
    {
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

static inline void HeapHandleClose(HpRunHdlT heapHdl)
{
    HeapLabelResetCtx(heapHdl);
    HeapLabelReleaseRunctx(heapHdl);
}

static const uint32_t FIX_ROW_UINT32_T_NUM = 20;
static const uint32_t FIX_ROW_LEN = sizeof(uint32_t) * FIX_ROW_UINT32_T_NUM;
static const uint32_t MAGIC_HEAD = 0xababefef;
static const uint32_t MAGIC_TAIL = 0xdedebcbc;

static StatusInter UtHeapFetchFixLenRowProc(HpReadRowInfoT *readRowInfo, void *userData)
{
    uint32_t *tupleBuf = (uint32_t *)readRowInfo->buf;
    EXPECT_EQ(FIX_ROW_LEN, readRowInfo->bufSize);
    EXPECT_EQ(MAGIC_HEAD, tupleBuf[0]);
    EXPECT_EQ(MAGIC_TAIL, tupleBuf[FIX_ROW_UINT32_T_NUM - 1]);

    if (FIX_ROW_LEN == readRowInfo->bufSize && MAGIC_HEAD == tupleBuf[0] &&
        MAGIC_TAIL == tupleBuf[FIX_ROW_UINT32_T_NUM - 1] &&
        memcmp(readRowInfo->buf, userData, readRowInfo->bufSize) == 0) {
        return STATUS_OK_INTER;
    }
    return INTERNAL_ERROR_INTER;
}

// 看护HeapFetch函数，看护轻量化事务&&heap fix场景下（不带升级后的跨页访问），获取tuple的语义
// 更新：放松对HpRowStateT isExist的看护，支持不在首字节
TEST_F(UtHacFixedKeyCmpHeap, testHeapFetch)
{
    int32_t ret;
    ShmemPtrT heapShmAddr;
    uint32_t fixRow = FIX_ROW_UINT32_T_NUM;
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = static_cast<PageSizeT>(FIX_ROW_LEN),
        .slotExtendSize = 10,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = UT_LABEL_ID,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 1000000,
    };
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);
    g_fixHeapShmAddr = heapShmAddr;
    stubVtxLabel2->commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    SeRunCtxHdT seRunCtx = nullptr;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.isolationLevel = READ_UNCOMMITTED;
    trxCfg.isLiteTrx = true;

    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = g_fixHeapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = stubVtxLabel2,
        .isBackGround = false};

    ASSERT_EQ(0, ret);
    uint32_t tupleBuf[fixRow] = {0};
    tupleBuf[0] = MAGIC_HEAD;
    tupleBuf[fixRow - 1] = MAGIC_TAIL;

    HpRunHdlT heapHdl;
    HpTupleAddr addr;
    vector<HpTupleAddr> heapItemList;
    for (uint32_t i = 0; i < 100000; ++i) {
        tupleBuf[1] = i;
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, heapCfg.fixRowSize, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }
    for (uint32_t i = 100000; i < 110000; ++i) {
        tupleBuf[1] = i;
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapInsert((HeapRunCtxT *)heapHdl, (uint8_t *)tupleBuf, heapCfg.fixRowSize, (HpItemPointerT *)&addr);
        ASSERT_EQ(0, ret);
        heapItemList.push_back(addr);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }

    HeapHacInfoT hacInfo = HeapLabelGetHacInfo(heapHdl);

    PageHeadT *pageHead = NULL;
    PageIdT pageId;
    for (auto &heapAddr : heapItemList) {
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapMdGetMemPage((HeapRunCtxT *)heapHdl, (HpItemPointerT *)&heapAddr, &pageHead, NULL, &pageId);
        ASSERT_EQ(ret, STATUS_OK_INTER);
        ASSERT_EQ(*(uint32_t *)((uint8_t *)pageHead + hacInfo.trmOffset), hacInfo.heapTrmId);
        ASSERT_LT(((HpItemPointerT *)&heapAddr)->slotId, hacInfo.rowCnt);
        uint32_t offset = hacInfo.rowBegin +
                          ((HpItemPointerT *)&heapAddr)->slotId * (hacInfo.oneRowSize + hacInfo.slotExtendSize) +
                          hacInfo.slotExtendSize;
        ASSERT_LE(offset, 0xFFFF);
        uint8_t *rowTuple = (uint8_t *)pageHead + offset;
        ASSERT_TRUE((hacInfo.existBitMask & *(rowTuple + hacInfo.existOffset)) != 0);

        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapFetchFixLenRowProc,
            (void *)(rowTuple + hacInfo.fixRowHead));
        EXPECT_EQ(0, ret);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }

    for (auto &heapAddr : heapItemList) {
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);

        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, true);
        ASSERT_EQ(0, ret);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }

    for (auto &heapAddr : heapItemList) {
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapMdGetMemPage((HeapRunCtxT *)heapHdl, (HpItemPointerT *)&heapAddr, &pageHead, NULL, &pageId);
        ASSERT_EQ(ret, STATUS_OK_INTER);
        ASSERT_EQ(*(uint32_t *)((uint8_t *)pageHead + hacInfo.trmOffset), hacInfo.heapTrmId);
        ASSERT_LT(((HpItemPointerT *)&heapAddr)->slotId, hacInfo.rowCnt);
        uint32_t offset = hacInfo.rowBegin +
                          ((HpItemPointerT *)&heapAddr)->slotId * (hacInfo.oneRowSize + hacInfo.slotExtendSize) +
                          hacInfo.slotExtendSize;
        ASSERT_LE(offset, 0xFFFF);
        uint8_t *rowTuple = (uint8_t *)pageHead + offset;
        ASSERT_TRUE((hacInfo.existBitMask & *(rowTuple + hacInfo.existOffset)) != 0);

        ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, UtHeapFetchFixLenRowProc,
            (void *)(rowTuple + hacInfo.fixRowHead));
        EXPECT_EQ(0, ret);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }

    for (auto &heapAddr : heapItemList) {
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);

        ret = HeapDelete((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, false);
        ASSERT_EQ(0, ret);
        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }
    for (auto &heapAddr : heapItemList) {
        ret = SeTransBegin(seRunCtx, &trxCfg);
        EXPECT_EQ(ret, 0);
        ret = SeTransAssignReadView(seRunCtx);
        EXPECT_EQ(ret, 0);
        ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
        EXPECT_EQ(0, ret);
        ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)g_seTopDynCtx);
        EXPECT_EQ(0, ret);
        ret = HeapMdGetMemPage((HeapRunCtxT *)heapHdl, (HpItemPointerT *)&heapAddr, &pageHead, NULL, &pageId);
        ASSERT_EQ(ret, STATUS_OK_INTER);
        if (*(uint32_t *)((uint8_t *)pageHead + hacInfo.trmOffset) != hacInfo.heapTrmId) {
            ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, NULL, NULL);
            EXPECT_EQ(NO_DATA_HEAP_PAGE_NOT_EXIST, ret);
        } else {
            ASSERT_LT(((HpItemPointerT *)&heapAddr)->slotId, hacInfo.rowCnt);
            uint32_t offset = hacInfo.rowBegin +
                              ((HpItemPointerT *)&heapAddr)->slotId * (hacInfo.oneRowSize + hacInfo.slotExtendSize) +
                              hacInfo.slotExtendSize;
            ASSERT_LE(offset, 0xFFFF);
            uint8_t *rowTuple = (uint8_t *)pageHead + offset;
            ASSERT_TRUE((hacInfo.existBitMask & *(rowTuple + hacInfo.existOffset)) == 0);
            ret = HeapFetch((HeapRunCtxT *)heapHdl, *(HpItemPointerT *)&heapAddr, NULL, NULL);
            EXPECT_EQ(NO_DATA_HEAP_ITEM_NOT_EXIST, ret);
        }

        ret = SeTransCommit(seRunCtx);
        EXPECT_EQ(ret, 0);
        HeapHandleClose(heapHdl);
    }

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = g_fixHeapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelTruncate(g_seRunCtx10, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);

    ret = HeapLabelDrop(g_seRunCtx10, &heapCntrAcsInfo, NULL);
    EXPECT_EQ(0, ret);
    g_fixHeapShmAddr = DB_INVALID_SHMPTR;
}
