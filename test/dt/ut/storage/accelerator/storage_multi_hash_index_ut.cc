/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: storage_multi_hash_index_ut.cc
 * Description: Implementation of multi hash index ut
 * Author: lijianchuan
 * Create: 2022/11/16
 */

#include <thread>
#include <vector>
#include <atomic>
#include <algorithm>

#include "db_common_init.h"
#include "common_init.h"
#include "se_index.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "se_multi_hash_index.h"
#include "se_hac_common.h"
#include "db_dynmem_algo.h"
#include "se_index_common.h"
#include "storage_hac_common.h"
#include "se_page_mgr.h"
#include "stub.h"
#include "db_hash.h"
#include "db_hashmap.h"
#include "db_config.h"

static const uint32_t MH_KEY_DATA_LEN = 8;

DbMemCtxT *g_topShmMemCtxForMH = NULL;
SeRunCtxHdT g_seRunCtxForMH;
DbOamapT *check_key;

static IndexMetaCfgT g_idxMetaCfgForMH = {
    .indexId = 0,
    .idxType = MULTI_HASH_INDEX,
    .realIdxType = MULTI_HASH_INDEX,
    .idxConstraint = NON_UNIQUE,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

int32_t MHCreateAndOpenStorageEngine(
    DbMemCtxT **topShmMemCtx, SeRunCtxHdT *seRunCtx, uint16_t deviceSize, uint16_t memSize)
{
    *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(topShmMemCtx);

    SeConfigT hashConfig = {0};
    hashConfig.deviceSize = deviceSize * DB_KIBI;
    hashConfig.pageSize = SE_DEFAULT_PAGE_SIZE;
    hashConfig.instanceId = 1;
    hashConfig.maxSeMem = memSize * DB_KIBI;  // 单位 K
    hashConfig.maxTrxNum = MAX_TRX_NUM;

    SeInstanceT *hashPtr = nullptr;
    Status ret = SeCreateInstance(NULL, (DbMemCtxT *)*topShmMemCtx, &hashConfig, (SeInstanceHdT *)&hashPtr);
    EXPECT_EQ(GMERR_OK, ret);
    SeInstanceT *cmpPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_EQ(hashPtr, cmpPtr);

    DbMemCtxArgsT hashArgs = {0};
    hashArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    hashArgs.memType = DB_DYNAMIC_MEMORY;
    hashArgs.init = DynamicAlgoInit;

    void *seTopDynamicCtxTop =
        (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "se dynamic", &hashArgs);
    return SeOpenWithNewSession(1, (DbMemCtxT *)seTopDynamicCtxTop, seRunCtx);
}

IndexKeyT MHUtConstructKey(HpTupleAddr addr, uint8_t *keydataPtr, uint32_t keyDataLen = MH_KEY_DATA_LEN)
{
    uint8_t *addrPtr = (uint8_t *)&addr;
    errno_t ret = memcpy_s(keydataPtr, keyDataLen, addrPtr, keyDataLen);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT key = {.keyData = keydataPtr, .keyLen = keyDataLen};
    return key;
}

Status MHCompareStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT existingKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
    const char *str1 = (const char *)existingKey.keyData;
    const char *str2 = (const char *)hashKey.keyData;
    *isMatch = (memcmp(str1, str2, hashKey.keyLen) == 0);
    *cmpRet = 0;
    return GMERR_OK;
}

// 查询的打桩函数
Status MHCompareStub2(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    *isMatch = true;
    *cmpRet = 0;
    return GMERR_OK;
}

Status MHCompareStub3(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    void *result = DbOamapLookup(check_key, addr, &addr, NULL);
    if (result != hashKey.keyData) {
        *isMatch = false;
        return GMERR_NO_DATA;
    }
    *isMatch = true;
    *cmpRet = 0;
    return GMERR_OK;
}

Status HashCheckAddrStub(const Handle heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    *isExist = true;
    return GMERR_OK;
}

class UtMultiHashIndex : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        init();
        int id = setStubC((void *)DbCfgGetInt32, (void *)DbCfgGetInt32Stub);
        EXPECT_GT(id, 0);
        int32_t ret = CommonInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = MHCreateAndOpenStorageEngine(&g_topShmMemCtxForMH, &g_seRunCtxForMH, 4, 1024);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(IsHacInitializedImpl(), true);
        SetTupleAddrMode(false);  // 默认32位运行
        GetHacMgr()->ccType = CONCURRENCY_CONTROL_NORMAL;
    };

    static void TearDownTestCase()
    {
        clearAllStub();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

TEST_F(UtMultiHashIndex, storage_hac_hash_index_create_and_drop_001)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    Status ret = MultiHashIdxCreate(g_seRunCtxForMH, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

inline void *SeShmAllocStub(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    DB_UNUSED(shmCtx);
    DB_UNUSED(size);
    DB_UNUSED(shmPtr);
    return nullptr;
}

TEST_F(UtMultiHashIndex, storage_hac_hash_index_drop_empty_002)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    int32_t stubId = setStubC((void *)SeShmAlloc, (void *)SeShmAllocStub);
    ShmemPtrT htShmAddr;
    Status ret = MultiHashIdxCreate(g_seRunCtxForMH, indexCfg, &htShmAddr);
    EXPECT_NE(GMERR_OK, ret);
    EXPECT_EQ(htShmAddr.offset, DB_INVALID_SHMPTR.offset);
    EXPECT_EQ(htShmAddr.segId, DB_INVALID_SHMPTR.segId);

    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_NE(GMERR_OK, ret);
    clearStub(stubId);
}

static Status UtMultiHashIndexOpen(IndexOpenCfgT openCfg, ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = openCfg;
    SeRunCtxT *hashRunCtxPtr = openCfg.seRunCtx;
    idxCtx->idxHandle = (IdxBaseT *)HashGetShmAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(hashRunCtxPtr == nullptr || idxCtx->idxHandle == nullptr)) {
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionCtxT *ctx = &hashRunCtxPtr->resSessionCtx;
    if (ctx->isDirectRead && !IdxIsConstructed(idxCtx->idxHandle)) {
        DB_LOG_DBG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "[SE-HASH] open unconstructed when DirectRead");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    idxCtx->idxMetaCfg = idxCtx->idxHandle->indexCfg;
    return MultiHashIdxOpen(idxCtx);
}

inline static IndexOpenCfgT GenerateHashIndexOpenCfg(bool hasScan = false)
{
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = g_seRunCtxForMH,
        .vertex = nullptr,
        .heapHandle = nullptr,
    };
    indexOpenCfg.callbackFunc.keyCmp = hasScan ? MHCompareStub2 : MHCompareStub;
    indexOpenCfg.callbackFunc.addrCheck = HashCheckAddrStub;
    return indexOpenCfg;
}

static void HashIndexInit(IndexMetaCfgT indexCfg, ShmemPtrT *htShmAddr, IndexCtxT **idxCtx, bool hasScan = false)
{
    DB_POINTER2(htShmAddr, idxCtx);
    Status ret = MultiHashIdxCreate(g_seRunCtxForMH, indexCfg, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IdxAlloc(g_seRunCtxForMH, MULTI_HASH_INDEX, idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg(hasScan);
    ret = UtMultiHashIndexOpen(indexOpenCfg, *htShmAddr, *idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
}

static const uint32_t HMH_KEY_DATA_LEN = 8;

static Status UtHacHashIndexOpen(IndexOpenCfgT openCfg, ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = openCfg;
    SeRunCtxT *hashRunCtxPtr = openCfg.seRunCtx;
    idxCtx->idxHandle = (IdxBaseT *)HashGetShmAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(hashRunCtxPtr == nullptr || idxCtx->idxHandle == nullptr)) {
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionCtxT *ctx = &hashRunCtxPtr->resSessionCtx;
    if (ctx->isDirectRead && !IdxIsConstructed(idxCtx->idxHandle)) {
        DB_LOG_DBG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "[SE-HASH] open unconstructed when DirectRead");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    idxCtx->idxMetaCfg = idxCtx->idxHandle->indexCfg;
    return HacHashIdxOpen(idxCtx);
}

IndexKeyT HMHUtConstructKey(HpTupleAddr addr, uint8_t *keydataPtr, uint32_t keyDataLen = HMH_KEY_DATA_LEN)
{
    uint8_t *addrPtr = (uint8_t *)&addr;
    errno_t ret = memcpy_s(keydataPtr, keyDataLen, addrPtr, keyDataLen);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT key = {.keyData = keydataPtr, .keyLen = keyDataLen};
    return key;
}

Status HMHCompareStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    uint8_t keydata[HMH_KEY_DATA_LEN] = "";
    IndexKeyT existingKey = HMHUtConstructKey(addr, keydata, HMH_KEY_DATA_LEN);
    const char *str1 = (const char *)existingKey.keyData;
    const char *str2 = (const char *)hashKey.keyData;
    *isMatch = (memcmp(str1, str2, hashKey.keyLen) == 0);
    *cmpRet = 0;
    return GMERR_OK;
}

inline static IndexOpenCfgT GenerateHacHashIndexOpenCfg()
{
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = g_seRunCtxForMH,
        .vertex = nullptr,
        .heapHandle = nullptr,
    };
    indexOpenCfg.callbackFunc.keyCmp = HMHCompareStub;
    return indexOpenCfg;
}

static void HacHashIndexInit(IndexMetaCfgT indexCfg, ShmemPtrT *htShmAddr, IndexCtxT **idxCtx)
{
    DB_POINTER2(htShmAddr, idxCtx);
    Status ret = HacHashIdxCreate(g_seRunCtxForMH, indexCfg, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IdxAlloc(g_seRunCtxForMH, HAC_HASH_INDEX, idxCtx);
    ASSERT_EQ(GMERR_OK, ret);

    IndexOpenCfgT indexOpenCfg = GenerateHacHashIndexOpenCfg();
    ret = UtHacHashIndexOpen(indexOpenCfg, *htShmAddr, *idxCtx);
    EXPECT_EQ(GMERR_OK, ret);
}
static IndexMetaCfgT g_idxMetaCfgForHMH = {
    .indexId = 0,
    .idxType = MULTI_HASH_INDEX,
    .realIdxType = MULTI_HASH_INDEX,
    .idxConstraint = PRIMARY,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = (uint32_t)SE_INDEX_DATATYPE_BLOB,
    .extendParam = NULL,
};

static uint32_t g_startNum = 100000;
static uint32_t g_threadsNum = 8;
static uint32_t g_insertSum = 8000;
static uint32_t g_perThreadsKeys = g_insertSum / g_threadsNum;  // 偶数

std::atomic<uint32_t> g_mHashAtomicCount{0};

void MHInsertData(IndexCtxT *idxCtx, int start, int end, int key = 0, bool isSameKey = false, bool isRand = false)
{
    HpTupleAddr addrWrite = start;
    Status ret;
    uint32_t count = 0u;
    uint64_t num = end - start;
    std::vector<HpTupleAddr> tempWrite(num);

    for (uint64_t i = 0; i < num; i++) {
        tempWrite[i] = start + i;
    }
    if (isRand) {
        random_shuffle(tempWrite.begin(), tempWrite.end());
    }

    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT HHKey = MHUtConstructKey(addrWrite + key, keydata);

    for (uint64_t i = 0; i < num; i++) {
        addrWrite = tempWrite[i];
        if (!isSameKey) {
            HHKey = MHUtConstructKey(addrWrite, keydata);
        }
        ret = MultiHashIdxInsert(idxCtx, HHKey, addrWrite);
        if (ret != GMERR_OK) {
            count++;
        }
    }
    if (count > 0) {
        g_mHashAtomicCount.fetch_add(count);
    }
}

void MHLookupData(IndexCtxT *idxCtx, int start, int end, bool isSameKey = false, bool isRand = false)
{
    HpTupleAddr addrWrite = start;
    Status ret;
    int count = 0;
    uint64_t num = end - start;
    std::vector<HpTupleAddr> tempWrite(num);

    for (uint64_t i = 0; i < num; i++) {
        tempWrite[i] = start + i;
    }
    if (isRand) {
        random_shuffle(tempWrite.begin(), tempWrite.end());
    }

    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT HHKey = MHUtConstructKey(addrWrite, keydata);

    bool isFound;
    for (uint64_t i = 0; i < num; i++) {
        addrWrite = tempWrite[i];
        if (!isSameKey) {
            HHKey = MHUtConstructKey(addrWrite, keydata);
        }
        ret = MultiHashIdxLookupWithAddr(idxCtx, HHKey, addrWrite, &isFound);
        if (ret != GMERR_OK || !isFound) {
            count++;
        }
    }
    if (count > 0) {
        g_mHashAtomicCount.fetch_add(count);
    }
}

void MHDeleteData(IndexCtxT *idxCtx, int start, int end, int key = 0, bool isSameKey = false, bool isRand = false)
{
    HpTupleAddr addrWrite = start;
    Status ret;
    int count = 0;
    uint64_t num = end - start;
    std::vector<HpTupleAddr> tempWrite(num);

    for (uint64_t i = 0; i < num; i++) {
        tempWrite[i] = start + i;
    }
    if (isRand) {
        random_shuffle(tempWrite.begin(), tempWrite.end());
    }

    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT HHKey = MHUtConstructKey(addrWrite + key, keydata);

    IndexRemoveParaT para = {.isErase = true, .isGc = true};
    for (uint64_t i = 0; i < num; i++) {
        addrWrite = tempWrite[i];
        if (!isSameKey) {
            HHKey = MHUtConstructKey(addrWrite, keydata);
        }
        ret = MultiHashIdxDelete(idxCtx, HHKey, addrWrite, para);
        if (ret != GMERR_OK) {
            count++;
        }
    }
    if (count > 0) {
        g_mHashAtomicCount.fetch_add(count);
    }
}

// 将本用例提前降低连跑时间
// 单点操作、并发的插入/删除相同Key不同addr，并发线程操作相同key
TEST_F(UtMultiHashIndex, storage_hash_index_unconflict_para_dml_011)
{
    g_mHashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    std::vector<std::thread> threads;

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads.emplace_back(std::thread(MHInsertData, idxCtx, g_startNum, g_startNum + g_insertSum, 0, true, false));
    }
    for (uint64_t i = 0; i < 1 * g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);  // 允许插入相同的key&addr对
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum * g_threadsNum);
    g_mHashAtomicCount.store(0);
    threads.clear();

    MHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum, true);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads.emplace_back(std::thread(MHDeleteData, idxCtx, g_startNum, g_startNum + g_insertSum, 0, true, false));
    }
    for (uint64_t i = 0; i < 1 * g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(ht->hashHac.entryNum, 0u);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    g_mHashAtomicCount.store(0);
    threads.clear();

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads.emplace_back(std::thread(MHInsertData, idxCtx, g_startNum, g_startNum + g_insertSum, 0, true, true));
    }
    for (uint64_t i = 0; i < 1 * g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum * g_threadsNum);
    g_mHashAtomicCount.store(0);
    threads.clear();

    MHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum, true);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads.emplace_back(std::thread(MHDeleteData, idxCtx, g_startNum, g_startNum + g_insertSum, 0, true, true));
        threads.emplace_back(std::thread(
            MHInsertData, idxCtx, g_startNum + g_insertSum, g_startNum + 2 * g_insertSum, -g_insertSum, true, true));
    }
    for (uint64_t i = 0; i < 2 * g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum * g_threadsNum);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    g_mHashAtomicCount.store(0);
    threads.clear();

    MHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum, true);
    ASSERT_EQ(g_mHashAtomicCount.load(), g_insertSum);
    g_mHashAtomicCount.store(0);
    MHLookupData(idxCtx, g_startNum, g_startNum + 2 * g_insertSum, true);
    ASSERT_EQ(g_mHashAtomicCount.load(), g_insertSum);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    Status ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtMultiHashIndex, storage_hash_index_alloc_and_release_and_truncate_003)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);

    IndexStatisticsT idxStatUntrunc;
    Status ret = MultiHashIdxStatView(htShmAddr, DbGetProcGlobalId(), &idxStatUntrunc);
    EXPECT_EQ(GMERR_OK, ret);

    ret = MultiHashIdxTruncate(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    IndexStatisticsT idxStatTrunc;
    ret = MultiHashIdxStatView(htShmAddr, DbGetProcGlobalId(), &idxStatTrunc);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.bucketCnt, idxStatTrunc.multiHashIndex.bucketCnt);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.bucketUsed, idxStatTrunc.multiHashIndex.bucketUsed);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.minTableSize, idxStatTrunc.multiHashIndex.minTableSize);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.maxTableSize, idxStatTrunc.multiHashIndex.maxTableSize);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.avgTableSize, idxStatTrunc.multiHashIndex.avgTableSize);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.cacheLineLen, idxStatTrunc.multiHashIndex.cacheLineLen);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.indexHashPerfStat.perPageSize,
        idxStatTrunc.multiHashIndex.indexHashPerfStat.perPageSize);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.indexHashPerfStat.pageCount,
        idxStatTrunc.multiHashIndex.indexHashPerfStat.pageCount);
    EXPECT_EQ(idxStatUntrunc.multiHashIndex.indexHashPerfStat.pageSize,
        idxStatTrunc.multiHashIndex.indexHashPerfStat.pageSize);

    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点增删查
TEST_F(UtMultiHashIndex, storage_hash_index_dml_004)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx, true);

    HpTupleAddr addr = 0xabcd;
    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
    HpTupleAddr addr2 = 0xabcd + 10;
    uint8_t keydata2[MH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey2 = MHUtConstructKey(addr2, keydata2, MH_KEY_DATA_LEN);

    Status ret = MultiHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFound;
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);

    IndexScanCfgT scanCfg{};
    scanCfg.leftKey = &idxKey;
    IndexScanItrT iter;
    ret = MultiHashIdxBeginScan(idxCtx, scanCfg, &iter);
    EXPECT_EQ(GMERR_OK, ret);

    TupleAddr addrReturned;
    ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);
    ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    IdxEndScan(idxCtx, iter);

    ret = MultiHashIdxInsert(idxCtx, idxKey, addr + 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr + 1, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);

    scanCfg.leftKey = &idxKey;
    ret = MultiHashIdxBeginScan(idxCtx, scanCfg, &iter);
    EXPECT_EQ(GMERR_OK, ret);

    ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(addrReturned == addr || addrReturned == addr + 1);
    ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(addrReturned == addr || addrReturned == addr + 1);
    ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    IdxEndScan(idxCtx, iter);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    IndexUpdateInfoT updateInfo = {.oldIdxKey = idxKey, .newIdxKey = idxKey2, .oldAddr = addr, .newAddr = addr2};
    ret = MultiHashIdxUpdate(idxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    removePara.isErase = true;
    ret = MultiHashIdxUpdate(idxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr + 1, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey2, addr2, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);

    removePara.isErase = false;
    ret = MultiHashIdxDelete(idxCtx, idxKey, addr + 1, removePara);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    removePara.isErase = true;
    ret = MultiHashIdxDelete(idxCtx, idxKey, addr + 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr + 1, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = MultiHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxTruncate(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点增删查，验证一层hash功能，不同Key
TEST_F(UtMultiHashIndex, storage_hash_index_dml_005)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    // 新的hash函数导致需要更大的值才能挂链，保证extendListNum不为0
    uint32_t insertNum = 200000;
    Status ret;

    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxInsert(idxCtx, idxKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, insertNum);
    EXPECT_NE(ht->extendListNum, 0u);

    bool isFound;
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxDelete(idxCtx, idxKey, addr, removePara);
        EXPECT_EQ(GMERR_OK, ret);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, 0u);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点增删查，验证两层hash功能，相同Key
TEST_F(UtMultiHashIndex, storage_hash_index_dml_006)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    uint32_t insertNum = 10000;
    Status ret;

    HpTupleAddr addr2 = 0xabcd;
    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey = MHUtConstructKey(addr2, keydata, MH_KEY_DATA_LEN);
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        ret = MultiHashIdxInsert(idxCtx, idxKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, insertNum);

    bool isFound;
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        ret = MultiHashIdxDelete(idxCtx, idxKey, addr, removePara);
        EXPECT_EQ(GMERR_OK, ret);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, 0u);
    EXPECT_NE(ht->extendListNum, 0u);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点增删查，验证混合hash功能，同Key、不同key均有
TEST_F(UtMultiHashIndex, storage_hash_index_dml_007)
{
    SetTupleAddrMode(false);  // 32位违反HeapCompressTupleAddr32断言，暂用回64位
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    uint32_t insertNum = 500;
    uint32_t sameKey = 100;
    Status ret;

    for (uint32_t i = 0; i < insertNum * sameKey; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxInsert(idxCtx, idxKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcdabcd + i * sameKey;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        for (uint32_t j = 0; j < sameKey; j++) {
            ret = MultiHashIdxInsert(idxCtx, idxKey, addr + j);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(ht->hashHac.entryNum, 2 * insertNum * sameKey);
    EXPECT_NE(ht->extendListNum, 0u);

    IndexStatisticsT idxStats;
    MultiHashIdxStatView(htShmAddr, DbGetProcGlobalId(), &idxStats);
    EXPECT_GE(idxStats.multiHashIndex.indexHashPerfStat.hashCollisionCnt, insertNum * sameKey);
    EXPECT_EQ(idxStats.multiHashIndex.indexHashPerfStat.hashInsertCnt, 2 * insertNum * sameKey);

    bool isFound;
    for (uint32_t i = 0; i < insertNum * sameKey; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcdabcd + i * sameKey;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        for (uint32_t j = 0; j < sameKey; j++) {
            ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr + j, &isFound);
            EXPECT_EQ(isFound, true);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    for (uint32_t i = 0; i < insertNum * sameKey; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxDelete(idxCtx, idxKey, addr, removePara);
        EXPECT_EQ(GMERR_OK, ret);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, insertNum * sameKey);

    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcdabcd + i * sameKey;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        for (uint32_t j = 0; j < sameKey; j++) {
            ret = MultiHashIdxDelete(idxCtx, idxKey, addr + j, removePara);
            EXPECT_EQ(GMERR_OK, ret);
            ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr + j, &isFound);
            EXPECT_EQ(isFound, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(ht->hashHac.entryNum, 0u);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    SetTupleAddrMode(true);
}

// 单点操作、并发插入/删除有冲突的值(同key同addr)，不存在同key不同addr
TEST_F(UtMultiHashIndex, storage_hash_index_conflict_para_dml_008)
{
    g_mHashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    std::vector<std::thread> threads;

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = i == 0 ? g_startNum : g_startNum + i * g_perThreadsKeys / 2;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(MHInsertData, idxCtx, start, end, 0, false, true));
    }
    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(g_mHashAtomicCount.load(), 0);  // 允许插入相同key&addr对
    ASSERT_EQ((uint32_t)ht->hashHac.entryNum, g_insertSum);
    g_mHashAtomicCount.store(0);
    MHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum);
    ASSERT_EQ(g_mHashAtomicCount.load(), g_perThreadsKeys * (g_threadsNum - 1) / 2);
    g_mHashAtomicCount.store(0);
    threads.clear();

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = i == 0 ? g_startNum : g_startNum + i * g_perThreadsKeys / 2;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(MHDeleteData, idxCtx, start, end, 0, false, true));
    }
    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(g_mHashAtomicCount.load(), 0);
    g_mHashAtomicCount.store(0);
    MHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum);
    ASSERT_EQ(g_mHashAtomicCount.load(), g_insertSum);
    ASSERT_EQ(ht->hashHac.entryNum, 0u);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    Status ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点操作、并发的插入与删除，没有冲突，不存在同key不同addr
TEST_F(UtMultiHashIndex, storage_hash_index_unconflict_para_dml_009)
{
    g_mHashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    std::vector<std::thread> threads;

    MHInsertData(idxCtx, g_startNum, g_startNum + g_insertSum);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum);

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(MHDeleteData, idxCtx, start, end, 0, false, true));
        threads.emplace_back(std::thread(MHInsertData, idxCtx, start + g_insertSum, end + g_insertSum, 0, false, true));
    }
    for (uint64_t i = 0; i < 2 * g_threadsNum; i++) {
        threads[i].join();
    }

    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum);
    threads.clear();

    MHLookupData(idxCtx, g_startNum, g_startNum + 2 * g_insertSum);
    ASSERT_EQ(g_mHashAtomicCount.load(), g_insertSum);
    g_mHashAtomicCount.store(0);

    MHDeleteData(idxCtx, g_startNum + g_insertSum, g_startNum + 2 * g_insertSum);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, 0u);

    MHLookupData(idxCtx, g_startNum, g_startNum + 2 * g_insertSum);
    ASSERT_EQ(g_mHashAtomicCount.load(), 2 * g_insertSum);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    Status ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点操作、并发的插入与删除相同Key不同addr，并发线程间不同key
TEST_F(UtMultiHashIndex, storage_hash_index_unconflict_para_dml_010)
{
    g_mHashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    std::vector<std::thread> threads;

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(MHInsertData, idxCtx, start, end, 0, true, false));
    }
    for (uint64_t i = 0; i < 1 * g_threadsNum; i++) {
        threads[i].join();
    }

    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum);
    threads.clear();

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(MHDeleteData, idxCtx, start, end, 0, true, false));
        threads.emplace_back(std::thread(MHInsertData, idxCtx, start + g_insertSum, end + g_insertSum, 0, true, false));
    }
    for (uint64_t i = 0; i < 2 * g_threadsNum; i++) {
        threads[i].join();
    }

    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum);
    threads.clear();
    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        MHLookupData(idxCtx, start, end, true);
        MHLookupData(idxCtx, start + g_insertSum, end + g_insertSum, true);
    }
    ASSERT_EQ(g_mHashAtomicCount.load(), g_insertSum);
    g_mHashAtomicCount.store(0);

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        MHDeleteData(idxCtx, start + g_insertSum, end + g_insertSum, 0, true);
    }
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, 0u);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    Status ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点查询验证
TEST_F(UtMultiHashIndex, storage_hash_index_scan_012)
{
    g_mHashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx, true);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    static uint32_t beginNum = 1000;

    MHInsertData(idxCtx, beginNum, beginNum + g_insertSum, 0, true);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum);
    MHLookupData(idxCtx, beginNum, beginNum + g_insertSum, true);
    ASSERT_EQ(g_mHashAtomicCount.load(), 0u);

    IndexScanCfgT scanCfg{};
    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT HHKey = MHUtConstructKey(beginNum, keydata);
    scanCfg.leftKey = &HHKey;
    IndexScanItrT iter;
    Status ret = MultiHashIdxBeginScan(idxCtx, scanCfg, &iter);
    EXPECT_EQ(GMERR_OK, ret);

    TupleAddr addrReturned;
    bool isFound;
    uint32_t count = 0;
    uint64_t addrCount = 0;
    while (!((MultiHashIteratorT *)iter)->isEOF) {
        ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
        if (!isFound) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        addrCount += addrReturned;
        count++;
    }
    EXPECT_EQ(count, g_insertSum);

    // 不能保序，但是保证插入addr的总和不变
    EXPECT_EQ(addrCount, (((uint64_t)beginNum << 1) + g_insertSum - 1) * g_insertSum >> 1);

    IdxEndScan(idxCtx, iter);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 批量增删，验证一层hash功能，不同Key
TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_013)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    uint32_t keyLen = MH_KEY_DATA_LEN;
    uint32_t batchNum = 1000;
    uint32_t realNum = 2 * batchNum;
    IndexKeyT indexkey[realNum];
    HpBatchOutT batchOut[realNum];
    IndexUpdateInfoT updateInfo[batchNum];

    uint8_t **keyData = (uint8_t **)malloc(realNum * sizeof(uint8_t *));
    (void)memset_s(keyData, realNum * sizeof(uint8_t *), 0, realNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < realNum; i++) {
        batchOut[i].addrOut = i + 0xabcd;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexkey[i] = MHUtConstructKey(batchOut[i].addrOut, keyData[i], keyLen);
    }
    for (uint32_t i = 0; i < batchNum; i++) {
        updateInfo[i].oldIdxKey = indexkey[i];
        updateInfo[i].oldAddr = batchOut[i].addrOut;
        updateInfo[i].newIdxKey = indexkey[i + batchNum];
        updateInfo[i].newAddr = batchOut[i + batchNum].addrOut;
    }

    Status ret = MultiHashIdxBatchInsert(idxCtx, indexkey, batchOut, batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(ht->hashHac.entryNum, batchNum);

    bool isFound;
    for (uint32_t i = 0; i < batchNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = MultiHashIdxBatchUpdate(idxCtx, updateInfo, batchNum, removePara);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    removePara.isErase = true;
    ret = MultiHashIdxBatchUpdate(idxCtx, updateInfo, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    //  新版设计查询功能只能用st验证
    for (uint32_t i = 0; i < batchNum; i++) {
        HpTupleAddr addr = 0xabcd + i + batchNum;
        uint8_t keydata[MH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = MHUtConstructKey(addr, keydata, MH_KEY_DATA_LEN);
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    removePara.isErase = false;
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey + batchNum, batchOut + batchNum, batchNum, removePara);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    removePara.isErase = true;
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey + batchNum, batchOut + batchNum, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.entryNum, 0u);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < realNum; i++) {
        free(keyData[i]);
    }
    free(keyData);
}

// 批量增删，验证二层hash功能，相同Key
TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_014)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    uint32_t keyLen = MH_KEY_DATA_LEN;
    uint32_t batchNum = 1000;
    IndexKeyT indexkey[batchNum];
    HpBatchOutT batchOut[batchNum];
    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    (void)memset_s(keyData, batchNum * sizeof(uint8_t *), 0, batchNum * sizeof(uint8_t *));
    TupleAddr fixedAddr = 0xabcd;
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 0xabcd;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexkey[i] = MHUtConstructKey(fixedAddr, keyData[i], keyLen);  // 同key
    }

    Status ret = MultiHashIdxBatchInsert(idxCtx, indexkey, batchOut, batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(ht->hashHac.entryNum, batchNum);

    bool isFound;
    uint8_t keydata[MH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey = MHUtConstructKey(fixedAddr, keydata, MH_KEY_DATA_LEN);
    for (uint32_t i = 0; i < batchNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        ASSERT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey, batchOut, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.entryNum, 0u);

    for (uint32_t i = 0; i < batchNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        ret = MultiHashIdxLookupWithAddr(idxCtx, idxKey, addr, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < batchNum; i++) {
        free(keyData[i]);
    }
    free(keyData);
}

typedef struct IdxBatchLookupIterStub {
    uint32_t batchNum;
    uint32_t index;
    IdxTupleOrIterT *tupleOrIter;
    IdxValueTypeE *valueType;
} IdxBatchLookupIterStubT;

Status IdxCategorizeFuncStub(
    IndexCtxT *idxCtx, IdxBatchLookupIterT *iter, IdxTupleOrIterT addrOrIter, uint32_t pos, IdxValueTypeE valueType)
{
    IdxBatchLookupIterStubT *iterStub = (IdxBatchLookupIterStubT *)(void *)iter;
    if (iterStub->index >= iterStub->batchNum) {
        return GMERR_DATA_EXCEPTION;
    }
    iterStub->tupleOrIter[iterStub->index] = addrOrIter;
    iterStub->valueType[iterStub->index++] = valueType;
    return GMERR_OK;
}

// 批量增删，验证混合hash功能，同Key不同key均有
TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_015)
{
    int s1 = setStubC((void *)MHCompareStub2, (void *)MHCompareStub3);
    EXPECT_GE(s1, 0);
    SetTupleAddrMode(false);  // 32位违反HeapCompressTupleAddr32断言，暂用回64位
    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx, true);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    uint32_t keyLen = MH_KEY_DATA_LEN;
    uint32_t batchNum = 500;
    uint32_t sameKey = 50;
    uint32_t insertNum = (sameKey + 1) * batchNum;
    IndexKeyT indexkey[insertNum];
    HpBatchOutT batchOut[insertNum];
    uint32_t diffNum = 2 * batchNum;
    IndexKeyT lookupKey[diffNum];

    uint8_t **keyData = (uint8_t **)malloc(2 * batchNum * sizeof(uint8_t *));
    (void)memset_s(keyData, 2 * batchNum * sizeof(uint8_t *), 0, 2 * batchNum * sizeof(uint8_t *));

    for (uint32_t i = 0; i < batchNum; i++) {
        HpTupleAddr addr = 0xabcdabcd + i * sameKey;
        keyData[i] = (uint8_t *)malloc(keyLen);
        lookupKey[i] = MHUtConstructKey(addr, keyData[i], MH_KEY_DATA_LEN);
        for (uint32_t j = 0; j < sameKey; j++) {  // 同key
            batchOut[i * sameKey + j].addrOut = addr + j;
            indexkey[i * sameKey + j] = lookupKey[i];
        }
    }
    for (uint32_t i = sameKey * batchNum; i < insertNum; i++) {
        batchOut[i].addrOut = i + 0xabcd;
        keyData[i - (sameKey - 1) * batchNum] = (uint8_t *)malloc(keyLen);
        indexkey[i] = MHUtConstructKey(batchOut[i].addrOut, keyData[i - (sameKey - 1) * batchNum], keyLen);  // 不同key
        lookupKey[i - (sameKey - 1) * batchNum] = indexkey[i];
    }

    Status ret = MultiHashIdxBatchInsert(idxCtx, indexkey, batchOut, insertNum);
    EXPECT_EQ(GMERR_OK, ret);
    DbMemCtxArgsT args = {0};
    DbMemCtxT *memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "tmp mem ctx", &args);
    check_key = (DbOamapT *)DbDynMemCtxAlloc(memCtx, sizeof(DbOamapT));
    ret = DbOamapInit(check_key, 0, DbOamapUint64Compare, memCtx, true);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < insertNum; i++) {
        DbOamapInsert(check_key, batchOut[i].addrOut, &batchOut[i].addrOut, (void *)indexkey[i].keyData, NULL);
    }

    EXPECT_EQ(ht->hashHac.entryNum, insertNum);

    bool isFound;
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = MultiHashIdxLookupWithAddr(idxCtx, indexkey[i], batchOut[i].addrOut, &isFound);
        ASSERT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    IdxTupleOrIterT tupleOrIter[insertNum];
    IdxValueTypeE valueType[insertNum];
    IdxBatchLookupIterStubT iter = {
        .batchNum = insertNum, .index = 0u, .tupleOrIter = tupleOrIter, .valueType = valueType};
    IdxBatchLookupParaT para = {.categorizeFunc = IdxCategorizeFuncStub, .iter = (IdxBatchLookupIterT *)(void *)&iter};
    ret = MultiHashIdxBatchLookup(idxCtx, lookupKey, diffNum, &para);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t i = 0;
    for (; i < batchNum; i++) {
        EXPECT_EQ(valueType[i], IDX_IS_ITERATOR);
        IndexScanItrT iter = tupleOrIter[i].iter;
        MultiHashIteratorT *mhIter = (MultiHashIteratorT *)iter;
        TupleAddr addrReturned;
        bool isFound1;
        uint32_t count = 0;
        while (!mhIter->isEOF) {
            ret = MultiHashIdxScan(idxCtx, iter, &addrReturned, &isFound1);
            if (!isFound1) {
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_GE(addrReturned, 0xabcdabcd + i * sameKey);
            EXPECT_LT(addrReturned, 0xabcdabcd + (i + 1) * sameKey);
            count++;
        }
        EXPECT_EQ(count, sameKey);
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey, batchOut, insertNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.entryNum, 0u);

    for (i = 0; i < insertNum; i++) {
        ret = MultiHashIdxLookupWithAddr(idxCtx, indexkey[i], batchOut[i].addrOut, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 2 * batchNum; i++) {
        free(keyData[i]);
    }
    free(keyData);
    SetTupleAddrMode(true);
    DbOamapDestroy(check_key);
    DbDeleteDynMemCtx(memCtx);
    clearStub(s1);
}

// 通过打桩模拟软硬通信，但是硬件模拟仅限于报文解析与返回报文，不涉及具体索引实现
// TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_016) // 暂时移除

TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_017)
{
    SetTupleAddrMode(false);
    SetHacMode(true);
    int s1 = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgStub);
    EXPECT_GE(s1, 0);
    int s2 = setStubC((void *)HacReceiveMsg, (void *)HacReceiveMsgStub);
    EXPECT_GT(s2, 0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx, true);

    uint32_t keyLen = MH_KEY_DATA_LEN;
    uint32_t batchNum = 1000;
    IndexKeyT indexkey[batchNum];
    HpBatchOutT batchOut[batchNum];
    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexkey[i] = MHUtConstructKey(batchOut[i].addrOut, keyData[i], keyLen);  // 不同key
    }

    Status ret = MultiHashIdxBatchInsert(idxCtx, indexkey, batchOut, batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey, batchOut, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < batchNum; i++) {
        free(keyData[i]);
    }
    free(keyData);
    clearStub(s1);
    clearStub(s2);

    SetHacMode(false);
    SetTupleAddrMode(true);
}

TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_018)
{
    SetTupleAddrMode(false);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx, true);

    IndexCtxT *idxCtxHac;
    indexCfg.indexCap = 10000;
    IndexMetaCfgT indexHacCfg = g_idxMetaCfgForHMH;
    ShmemPtrT htHacShmAddr;
    HacHashIndexInit(indexHacCfg, &htHacShmAddr, &idxCtxHac);

    uint32_t keyLen = MH_KEY_DATA_LEN;
    uint32_t batchNum = 2;
    IndexKeyT indexkey[batchNum];
    HpBatchOutT batchOut[batchNum];
    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = 1;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexkey[i] = MHUtConstructKey(batchOut[i].addrOut, keyData[i], keyLen);  // 同key
    }

    Status ret = MultiHashIdxInsert(idxCtx, indexkey[0], batchOut[0].addrOut);
    EXPECT_EQ(GMERR_OK, ret);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    ShmemPtrT *bucketShm = HashGetBucketShmPtrByHashCode(&ht->hashHac, MHGetHashCode(indexkey[0]));

    ShmemPtrT shmPtr = *bucketShm;
    SecHashTableT *secHt = (SecHashTableT *)HashGetShmAddrAlign(shmPtr);  // 流程已保证不为空
    EXPECT_EQ(1u, secHt->hashHac.entryNum);

    batchOut[1].addrOut = batchOut[0].addrOut;

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey, batchOut, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, secHt->hashHac.entryNum);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < batchNum; i++) {
        free(keyData[i]);
    }
    free(keyData);

    SetTupleAddrMode(true);
}

// 相同key场景走hachash mode
TEST_F(UtMultiHashIndex, storage_hash_index_batch_dml_019)
{
    SetTupleAddrMode(false);
    SetHacMode(true);
    int s1 = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgStub);
    EXPECT_GE(s1, 0);
    int s2 = setStubC((void *)HacReceiveMsg, (void *)HacReceiveMsgStub);
    EXPECT_GT(s2, 0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForMH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx, true);

    uint32_t keyLen = MH_KEY_DATA_LEN;
    uint32_t batchNum = 1000;
    IndexKeyT indexkey[batchNum];
    HpBatchOutT batchOut[batchNum];
    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexkey[i] = MHUtConstructKey(1, keyData[i], keyLen);  // 同key
    }

    Status ret = MultiHashIdxBatchInsert(idxCtx, indexkey, batchOut, batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = MultiHashIdxBatchDelete(idxCtx, indexkey, batchOut, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    MultiHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = MultiHashIdxDrop(g_seRunCtxForMH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < batchNum; i++) {
        free(keyData[i]);
    }
    free(keyData);
    clearStub(s1);
    clearStub(s2);
    SetHacMode(false);
    SetTupleAddrMode(true);
}
