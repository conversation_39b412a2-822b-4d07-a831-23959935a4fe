/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: storage_hac_hash_index_ut.cc
 * Description: Implementation of hac hash index ut
 * Author: lijianchuan
 * Create: 2022/9/1
 */

#include <thread>
#include <vector>
#include <atomic>
#include <algorithm>

#include "db_common_init.h"
#include "common_init.h"
#include "se_index.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "db_dynmem_algo.h"
#include "db_mem_context.h"
#include "se_hac_hash_index.h"
#include "se_hac_common.h"
#include "se_index_common.h"
#include "se_page_mgr.h"
#include "stub.h"
#include "storage_hac_common.h"
#include "db_config.h"

#ifdef __cplusplus
extern "C" {
#endif

bool IdxExceedSplitTimeStub(const IndexScaleInCfgT *idxScaleCfg)
{
    return true;
}

static const uint32_t HH_KEY_DATA_LEN = 8;

#ifdef __cplusplus
}
#endif

DbMemCtxT *g_topShmMemCtxForHH = NULL;
SeRunCtxHdT g_seRunCtxForHH;

static IndexMetaCfgT g_idxMetaCfgForHH = {
    .indexId = 0,
    .idxType = HAC_HASH_INDEX,
    .realIdxType = HAC_HASH_INDEX,
    .idxConstraint = PRIMARY,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

int32_t HHCreateAndOpenStorageEngine(
    DbMemCtxT **topShmMemCtx, SeRunCtxHdT *seRunCtx, uint16_t deviceSize, uint16_t memSize)
{
    *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(topShmMemCtx);

    SeConfigT hashConfig = {0};
    hashConfig.deviceSize = deviceSize * DB_KIBI;
    hashConfig.pageSize = SE_DEFAULT_PAGE_SIZE;
    hashConfig.instanceId = 1;
    hashConfig.maxSeMem = memSize * DB_KIBI;  // 单位 K
    hashConfig.maxTrxNum = MAX_TRX_NUM;

    SeInstanceT *hashPtr = nullptr;
    Status ret = SeCreateInstance(NULL, (DbMemCtxT *)*topShmMemCtx, &hashConfig, (SeInstanceHdT *)&hashPtr);
    EXPECT_EQ(GMERR_OK, ret);
    SeInstanceT *cmpPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_EQ(hashPtr, cmpPtr);

    DbMemCtxArgsT hashArgs = {0};
    hashArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    hashArgs.memType = DB_DYNAMIC_MEMORY;
    hashArgs.init = DynamicAlgoInit;

    void *seTopDynamicCtxTop =
        (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "se dynamic", &hashArgs);
    return SeOpenWithNewSession(1, (DbMemCtxT *)seTopDynamicCtxTop, seRunCtx);
}

IndexKeyT HHUtConstructKey(HpTupleAddr addr, uint8_t *keydataPtr, uint32_t keyDataLen = HH_KEY_DATA_LEN)
{
    uint8_t *addrPtr = (uint8_t *)&addr;
    errno_t ret = memcpy_s(keydataPtr, keyDataLen, addrPtr, keyDataLen);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT key = {.keyData = keydataPtr, .keyLen = keyDataLen};
    return key;
}

Status HHCompareStub(IndexCtxT *idxCtx, IndexKeyT hashKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER3(idxCtx, hashKey.keyData, isMatch);
    uint8_t keydata[HH_KEY_DATA_LEN] = "";
    IndexKeyT existingKey = HHUtConstructKey(addr, keydata, HH_KEY_DATA_LEN);
    const char *str1 = (const char *)existingKey.keyData;
    const char *str2 = (const char *)hashKey.keyData;
    *isMatch = (memcmp(str1, str2, hashKey.keyLen) == 0);
    *cmpRet = 0;
    return GMERR_OK;
}

class UtHacHashIndex : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        init();
        int id = setStubC((void *)DbCfgGetInt32, (void *)DbCfgGetInt32Stub);
        EXPECT_GT(id, 0);
        int32_t ret = CommonInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = HHCreateAndOpenStorageEngine(&g_topShmMemCtxForHH, &g_seRunCtxForHH, 4, 1024);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(IsHacInitializedImpl(), true);
        SetTupleAddrMode(false);  // 默认32位运行
        GetHacMgr()->ccType = CONCURRENCY_CONTROL_NORMAL;
    };

    static void TearDownTestCase()
    {
        clearAllStub();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

TEST_F(UtHacHashIndex, storage_hac_hash_index_create_and_drop_001)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    Status ret = HacHashIdxCreate(g_seRunCtxForHH, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

inline void *SeShmAllocStub(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    DB_UNUSED(shmCtx);
    DB_UNUSED(size);
    DB_UNUSED(shmPtr);
    return nullptr;
}

TEST_F(UtHacHashIndex, storage_hac_hash_index_drop_empty_002)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    int32_t stubId = setStubC((void *)SeShmAlloc, (void *)SeShmAllocStub);
    ShmemPtrT htShmAddr;
    Status ret = HacHashIdxCreate(g_seRunCtxForHH, indexCfg, &htShmAddr);
    EXPECT_NE(GMERR_OK, ret);
    EXPECT_EQ(htShmAddr.offset, DB_INVALID_SHMPTR.offset);
    EXPECT_EQ(htShmAddr.segId, DB_INVALID_SHMPTR.segId);

    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_NE(GMERR_OK, ret);
    clearStub(stubId);
}

static Status UtHacHashIndexOpen(IndexOpenCfgT openCfg, ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = openCfg;
    SeRunCtxT *hashRunCtxPtr = openCfg.seRunCtx;
    idxCtx->idxHandle = (IdxBaseT *)HashGetShmAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(hashRunCtxPtr == nullptr || idxCtx->idxHandle == nullptr)) {
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionCtxT *ctx = &hashRunCtxPtr->resSessionCtx;
    if (ctx->isDirectRead && !IdxIsConstructed(idxCtx->idxHandle)) {
        DB_LOG_DBG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "[SE-HASH] open unconstructed when DirectRead");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    idxCtx->idxMetaCfg = idxCtx->idxHandle->indexCfg;
    return HacHashIdxOpen(idxCtx);
}

inline static IndexOpenCfgT GenerateHashIndexOpenCfg()
{
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = g_seRunCtxForHH,
        .vertex = nullptr,
        .heapHandle = nullptr,
    };
    indexOpenCfg.callbackFunc.keyCmp = HHCompareStub;
    return indexOpenCfg;
}

static void HashIndexInit(IndexMetaCfgT indexCfg, ShmemPtrT *htShmAddr, IndexCtxT **idxCtx)
{
    DB_POINTER2(htShmAddr, idxCtx);
    Status ret = HacHashIdxCreate(g_seRunCtxForHH, indexCfg, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IdxAlloc(g_seRunCtxForHH, HAC_HASH_INDEX, idxCtx);
    ASSERT_EQ(GMERR_OK, ret);

    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    ret = UtHacHashIndexOpen(indexOpenCfg, *htShmAddr, *idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    TupleBufInit(&(*idxCtx)->tupleBuf, (DbMemCtxT *)g_seRunCtxForHH->sessionMemCtx);
}

TEST_F(UtHacHashIndex, storage_hash_index_alloc_and_release_and_truncate_003)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx = NULL;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);

    ASSERT_NE(idxCtx, nullptr);
    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);

    IndexStatisticsT idxStatUntrunc;
    Status ret = HacHashIdxStatView(htShmAddr, DbGetProcGlobalId(), &idxStatUntrunc);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxTruncate(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    IndexStatisticsT idxStatTrunc;
    ret = HacHashIdxStatView(htShmAddr, DbGetProcGlobalId(), &idxStatTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(idxStatUntrunc.hashIndex.entryCapacity, idxStatTrunc.hashIndex.entryCapacity);
    EXPECT_EQ(idxStatUntrunc.hashIndex.entryUsed, idxStatTrunc.hashIndex.entryUsed);
    EXPECT_EQ(idxStatUntrunc.hashIndex.segmentCnt, idxStatTrunc.hashIndex.segmentCnt);
    EXPECT_EQ(
        idxStatUntrunc.hashIndex.indexHashPerfStat.perPageSize, idxStatTrunc.hashIndex.indexHashPerfStat.perPageSize);
    EXPECT_EQ(idxStatUntrunc.hashIndex.indexHashPerfStat.pageCount, idxStatTrunc.hashIndex.indexHashPerfStat.pageCount);
    EXPECT_EQ(idxStatUntrunc.hashIndex.indexHashPerfStat.pageSize, idxStatTrunc.hashIndex.indexHashPerfStat.pageSize);
    EXPECT_EQ(
        idxStatUntrunc.hashIndex.indexHashPerfStat.usedMemSize, idxStatTrunc.hashIndex.indexHashPerfStat.usedMemSize);

    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 单点增删查
TEST_F(UtHacHashIndex, storage_hash_index_dml_004)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    EXPECT_EQ(idxCtx->hasLookup, false);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    EXPECT_EQ(ht->hashHac.hashSize, 1024u);

    HpTupleAddr addr = 0xabcd;
    uint8_t keydata[HH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey = HHUtConstructKey(addr, keydata, HH_KEY_DATA_LEN);
    HpTupleAddr addr2 = 0xabcd + 1;
    uint8_t keydata2[HH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey2 = HHUtConstructKey(addr2, keydata2, HH_KEY_DATA_LEN);

    Status ret = HacHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    HpTupleAddr addrReturned;
    bool isFound;
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);

    IndexScanCfgT scanCfg{};
    scanCfg.leftKey = &idxKey;
    IndexScanItrT iter;
    ret = HacHashIdxBeginScan(idxCtx, scanCfg, &iter);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);
    ret = HacHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);
    IdxEndScan(idxCtx, iter);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = HacHashIdxDelete(idxCtx, idxKey, addr, removePara);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    removePara.isErase = true;
    ret = HacHashIdxDelete(idxCtx, idxKey, addr, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);

    IndexUpdateInfoT updateInfo = {.oldIdxKey = idxKey, .newIdxKey = idxKey2, .oldAddr = addr, .newAddr = addr2};
    removePara.isErase = false;
    ret = HacHashIdxUpdate(idxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    removePara.isErase = true;
    ret = HacHashIdxUpdate(idxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey2, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr2, addrReturned);

    ret = HacHashIdxTruncate(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 重复多次单点增删查，是否扩容均不影响结果
TEST_F(UtHacHashIndex, storage_hash_index_dml_005)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    EXPECT_EQ(ht->hashHac.hashSize, 1024u);

    uint32_t insertNum = 100000;
    Status ret;

    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[HH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = HHUtConstructKey(addr, keydata, HH_KEY_DATA_LEN);
        ret = HacHashIdxInsert(idxCtx, idxKey, addr);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, insertNum);
    EXPECT_NE(ht->extendListNum, 0u);
    EXPECT_EQ(ht->hashHac.hashSize, 8 * 2048u);  // 8*2048*7>100000

    IndexStatisticsT idxStats;
    HacHashIdxStatView(htShmAddr, DbGetProcGlobalId(), &idxStats);
    EXPECT_NE(idxStats.hashIndex.indexHashPerfStat.hashCollisionCnt, 0u);
    EXPECT_EQ(idxStats.hashIndex.indexHashPerfStat.hashInsertCnt, insertNum);

    HpTupleAddr addrReturned;
    bool isFound;
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[HH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = HHUtConstructKey(addr, keydata, HH_KEY_DATA_LEN);
        ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(addr, addrReturned);
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    for (uint32_t i = 0; i < insertNum; i++) {
        HpTupleAddr addr = 0xabcd + i;
        uint8_t keydata[HH_KEY_DATA_LEN] = "";
        IndexKeyT idxKey = HHUtConstructKey(addr, keydata, HH_KEY_DATA_LEN);
        EXPECT_EQ(ht->hashHac.entryNum, insertNum - i);
        ret = HacHashIdxDelete(idxCtx, idxKey, addr, removePara);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(ht->hashHac.entryNum, insertNum - 1 - i);
        ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(ht->hashHac.entryNum, 0u);
    EXPECT_NE(ht->extendListNum, 0u);

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 批量增删，单点查(批查需在st中验证、或打桩验证)
// TEST_F(UtHacHashIndex, storage_hash_index_batch_dml_006) HacHashIdxBatchUpdate暂时删掉

// 通过打桩模拟软硬通信，但是硬件模拟仅限于报文解析与返回报文，不涉及具体索引实现
TEST_F(UtHacHashIndex, storage_hash_index_hac_batch_dml_007)
{
    SetTupleAddrMode(false);
    SetHacMode(true);
    int s1 = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgStub);
    EXPECT_GE(s1, 0);
    int s2 = setStubC((void *)HacReceiveMsg, (void *)HacReceiveMsgStub);
    EXPECT_GT(s2, 0);
    int s3 = setStubC((void *)IsHacHashSupportOffloading, (void *)IsHacHashSupportOffloadingStub);
    EXPECT_GE(s3, 0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    EXPECT_EQ(ht->hashHac.hashSize, 1024u);
    idxCtx->isHacBatchAsync = false;

    uint32_t keyLen = HH_KEY_DATA_LEN;
    uint32_t batchNum = 50;
    IndexKeyT indexkey[batchNum];
    HpBatchOutT batchOut[batchNum];
    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    (void)memset_s(keyData, batchNum * sizeof(uint8_t *), 0, batchNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexkey[i] = HHUtConstructKey(batchOut[i].addrOut, keyData[i], keyLen);
    }

    idxCtx->isKeyCmpByHac = true;
    Status ret = HacHashIdxBatchLookup(idxCtx, indexkey, batchNum, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxBatchInsert(idxCtx, indexkey, batchOut, batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxBatchLookup(idxCtx, indexkey, batchNum, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = HacHashIdxBatchDelete(idxCtx, indexkey, batchOut, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < batchNum; i++) {
        free(keyData[i]);
    }
    free(keyData);
    clearStub(s1);
    clearStub(s2);
    clearStub(s3);
    SetHacMode(false);
    SetTupleAddrMode(true);
}

static uint32_t g_startNum = 100000;
static uint32_t g_threadsNum = 16;
static uint32_t g_insertSum = 16 * 5000;
static uint32_t g_perThreadsKeys = g_insertSum / g_threadsNum;  // 偶数

std::atomic<uint32_t> g_hashAtomicCount{0};

void HHInsertData(IndexCtxT *idxCtx, int start, int end, bool isRand = false)
{
    HpTupleAddr addrWrite = start;
    Status ret;
    int count = 0;
    uint64_t num = end - start;
    std::vector<HpTupleAddr> tempWrite(num);
    for (uint64_t i = 0; i < num; i++) {
        tempWrite[i] = start + i;
    }
    random_shuffle(tempWrite.begin(), tempWrite.end());
    for (uint64_t i = 0; i < num; i++) {
        uint8_t keydata[HH_KEY_DATA_LEN] = "";
        if (isRand) {
            addrWrite = tempWrite[i];
            IndexKeyT HHKey = HHUtConstructKey(addrWrite, keydata);
            ret = HacHashIdxInsert(idxCtx, HHKey, addrWrite);
        } else {
            IndexKeyT HHKey = HHUtConstructKey(addrWrite, keydata);
            ret = HacHashIdxInsert(idxCtx, HHKey, addrWrite);
            addrWrite++;
        }
        if (ret != GMERR_OK) {
            count++;
        }
    }
    if (count > 0) {
        g_hashAtomicCount.fetch_add(count);
    }
}

void HHLookupData(IndexCtxT *idxCtx, int start, int end, bool isRand = false)
{
    HpTupleAddr addrWrite = start;
    HpTupleAddr addrReturned;
    Status ret;
    int count = 0;
    uint64_t num = end - start;
    std::vector<HpTupleAddr> tempWrite(num);
    for (uint64_t i = 0; i < num; i++) {
        tempWrite[i] = start + i;
    }
    if (isRand) {
        random_shuffle(tempWrite.begin(), tempWrite.end());
    }
    bool isFound;
    for (uint64_t i = 0; i < num; i++) {
        addrWrite = tempWrite[i];
        uint8_t keydata[HH_KEY_DATA_LEN] = "";
        IndexKeyT HHKey = HHUtConstructKey(addrWrite, keydata);
        ret = HacHashIdxLookup(idxCtx, HHKey, &addrReturned, &isFound);
        ASSERT_EQ(ret, 0);
        if (isFound) {
            ASSERT_TRUE(addrWrite == addrReturned);
        } else {
            count++;
        }
    }
    if (count > 0) {
        g_hashAtomicCount.fetch_add(count);
    }
}

void HHDeleteData(IndexCtxT *idxCtx, int start, int end, bool isRand = false)
{
    HpTupleAddr addrWrite = start;
    Status ret;
    int count = 0;
    uint64_t num = end - start;
    std::vector<HpTupleAddr> tempWrite(num);
    for (uint64_t i = 0; i < num; i++) {
        tempWrite[i] = start + i;
    }
    if (isRand) {
        random_shuffle(tempWrite.begin(), tempWrite.end());
    }
    IndexRemoveParaT para = {.isErase = true, .isGc = true};
    for (uint64_t i = 0; i < num; i++) {
        addrWrite = tempWrite[i];
        uint8_t keydata[HH_KEY_DATA_LEN] = "";
        IndexKeyT HHKey = HHUtConstructKey(addrWrite, keydata);
        ret = HacHashIdxDelete(idxCtx, HHKey, addrWrite, para);
        if (ret != GMERR_OK) {
            count++;
        }
    }
    if (count > 0) {
        g_hashAtomicCount.fetch_add(count);
    }
}

// 并发增删改
TEST_F(UtHacHashIndex, HacHashParaHybridInsertAndLookupAndDelete_008)
{
    g_hashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    std::vector<std::thread> threads;

    HHInsertData(idxCtx, g_startNum, g_startNum + g_insertSum);
    ASSERT_EQ(g_hashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, g_insertSum);

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(HHLookupData, idxCtx, start, end, false));
        threads.emplace_back(std::thread(HHInsertData, idxCtx, start + g_insertSum, end + g_insertSum, false));
    }
    for (uint64_t i = 0; i < 2 * g_threadsNum; i++) {
        threads[i].join();
    }

    ASSERT_EQ(g_hashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, 2 * g_insertSum);
    threads.clear();

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = g_startNum + i * g_perThreadsKeys;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(HHDeleteData, idxCtx, start, end, false));
        threads.emplace_back(std::thread(HHLookupData, idxCtx, start + g_insertSum, end + g_insertSum, false));
        threads.emplace_back(std::thread(HHInsertData, idxCtx, start + 2 * g_insertSum, end + 2 * g_insertSum, false));
    }
    for (uint64_t i = 0; i < 3 * g_threadsNum; i++) {
        threads[i].join();
    }

    ASSERT_EQ(g_hashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, 2 * g_insertSum);
    threads.clear();

    HHLookupData(idxCtx, g_startNum, g_startNum + 3 * g_insertSum);
    ASSERT_EQ(g_hashAtomicCount.load(), g_insertSum);
    g_hashAtomicCount.store(0);

    HHDeleteData(idxCtx, g_startNum + g_insertSum, g_startNum + 3 * g_insertSum);
    ASSERT_EQ(g_hashAtomicCount.load(), 0u);
    ASSERT_EQ(ht->hashHac.entryNum, 0u);

    HHLookupData(idxCtx, g_startNum, g_startNum + 3 * g_insertSum);
    ASSERT_EQ(g_hashAtomicCount.load(), 3 * g_insertSum);

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    Status ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtHacHashIndex, HacHashParaConflictInsertAndLookupAndDelete_009)
{
    g_hashAtomicCount.store(0);

    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;

    std::vector<std::thread> threads;

    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = i == 0 ? g_startNum : g_startNum + i * g_perThreadsKeys / 2;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(HHInsertData, idxCtx, start, end, true));
    }
    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads[i].join();
    }
    ASSERT_EQ(g_hashAtomicCount.load(), (g_threadsNum - 1) * g_perThreadsKeys / 2);
    ASSERT_EQ((uint32_t)ht->hashHac.entryNum, (g_threadsNum + 1) * g_perThreadsKeys / 2);
    g_hashAtomicCount.store(0);
    HHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum);
    ASSERT_EQ(g_hashAtomicCount.load(), (g_threadsNum - 1) * g_perThreadsKeys / 2);
    g_hashAtomicCount.store(0);
    threads.clear();
    for (uint64_t i = 0; i < g_threadsNum; i++) {
        int start = i == 0 ? g_startNum : g_startNum + i * g_perThreadsKeys / 2;
        int end = start + g_perThreadsKeys;
        threads.emplace_back(std::thread(HHDeleteData, idxCtx, start, end, true));
    }
    for (uint64_t i = 0; i < g_threadsNum; i++) {
        threads[i].join();
    }
    g_hashAtomicCount.store(0);
    HHLookupData(idxCtx, g_startNum, g_startNum + g_insertSum);
    ASSERT_EQ(g_hashAtomicCount.load(), g_insertSum);
    ASSERT_EQ(ht->hashHac.entryNum, 0u);

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    Status ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtHacHashIndex, storage_hash_index_ddl_010)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 160000000;

    Status ret = HacHashIdxCreate(g_seRunCtxForHH, indexCfg, &htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IdxAlloc(g_seRunCtxForHH, HAC_HASH_INDEX, &idxCtx);
    EXPECT_EQ(GMERR_OK, ret);

    IndexOpenCfgT indexOpenCfg = GenerateHashIndexOpenCfg();
    ret = UtHacHashIndexOpen(indexOpenCfg, htShmAddr, idxCtx);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);  // 超内存

    IdxRelease(idxCtx);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 扩缩容用例看护
TEST_F(UtHacHashIndex, storage_hash_index_scale_out_and_in_011)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 1;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    EXPECT_EQ(ht->hashHac.hashSize, 2u);  // 当前HacHash的Hashsize最小为2
    // 能存2*14=28个，扩容阈值28*0.7=19

    uint32_t keyLen = HH_KEY_DATA_LEN;
    uint32_t batchNum = 100;
    IndexKeyT indexKey[batchNum];
    HpBatchOutT batchOut[batchNum];

    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    (void)memset_s(keyData, batchNum * sizeof(uint8_t *), 0, batchNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexKey[i] = HHUtConstructKey(batchOut[i].addrOut, keyData[i], keyLen);
    }
    uint32_t threshold = 19;

    idxCtx->isKeyCmpByHac = false;
    Status ret = HacHashIdxBatchInsert(idxCtx, indexKey, batchOut, threshold - 1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 2u);
    ret = HacHashIdxInsert(idxCtx, indexKey[threshold - 1], batchOut[threshold - 1].addrOut);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 4u);  // 扩容一次
    ret = HacHashIdxBatchInsert(idxCtx, indexKey + threshold, batchOut + threshold, batchNum - threshold);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 16u);  // 8*14*0.7<100<16*14*0.7

    HpTupleAddr addrReturned;
    bool isFound;
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = HacHashIdxLookup(idxCtx, indexKey[i], &addrReturned, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i + 1, addrReturned);
    }

    threshold = 44;  // 16*14*0.25=56，需要删除44个才达到阈值
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = HacHashIdxBatchDelete(idxCtx, indexKey, batchOut, threshold - 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    IndexScaleInCfgT idxScaleCfg = {
        .splitTime = 1000000,    // ut简单验证保证缩容完成
        .startTime = DbRdtsc(),  // 入参，分片开始的时间
        .isOverTime = false      // 出参，运行是否超过时间片
    };
    ret = HacHashIdxScaleIn(idxCtx, &idxScaleCfg);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 16u);
    ret = HacHashIdxDelete(idxCtx, indexKey[threshold - 1], batchOut[threshold - 1].addrOut, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxScaleIn(idxCtx, &idxScaleCfg);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 8u);
    for (uint32_t i = 0; i < threshold; i++) {
        ret = HacHashIdxLookup(idxCtx, indexKey[i], &addrReturned, &isFound);
        EXPECT_EQ(isFound, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = threshold; i < batchNum; i++) {
        ret = HacHashIdxLookup(idxCtx, indexKey[i], &addrReturned, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i + 1, addrReturned);
    }
    // 4*14*0.25=14,需要删除86个，已删44个还需42个
    uint32_t threshold2 = 42;
    ret = HacHashIdxBatchDelete(
        idxCtx, indexKey + batchNum - threshold2, batchOut + batchNum - threshold2, threshold2, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxScaleIn(idxCtx, &idxScaleCfg);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 2u);  // 缩容两次
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = HacHashIdxLookup(idxCtx, indexKey[i], &addrReturned, &isFound);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= threshold && i < batchNum - threshold2) {
            EXPECT_EQ(isFound, true);
            EXPECT_EQ(i + 1, addrReturned);
        } else {
            EXPECT_EQ(isFound, false);
        }
    }

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
}

// 缩容用例看护
TEST_F(UtHacHashIndex, storage_hash_index_scale_out_and_in_012)
{
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 1;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    EXPECT_EQ(ht->hashHac.hashSize, 2u);  // 当前HacHash的Hashsize最小为2
    // 能存2*14=28个，扩容阈值28*0.7=19

    uint32_t keyLen = HH_KEY_DATA_LEN;
    uint32_t batchNum = 100;
    IndexKeyT indexKey[batchNum];
    HpBatchOutT batchOut[batchNum];

    uint8_t **keyData = (uint8_t **)malloc(batchNum * sizeof(uint8_t *));
    (void)memset_s(keyData, batchNum * sizeof(uint8_t *), 0, batchNum * sizeof(uint8_t *));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
        keyData[i] = (uint8_t *)malloc(keyLen);
        indexKey[i] = HHUtConstructKey(batchOut[i].addrOut, keyData[i], keyLen);
    }
    uint32_t threshold = 19;

    idxCtx->isKeyCmpByHac = false;
    Status ret = HacHashIdxBatchInsert(idxCtx, indexKey, batchOut, threshold - 1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 2u);
    ret = HacHashIdxInsert(idxCtx, indexKey[threshold - 1], batchOut[threshold - 1].addrOut);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 4u);  // 扩容一次
    ret = HacHashIdxBatchInsert(idxCtx, indexKey + threshold, batchOut + threshold, batchNum - threshold);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 16u);  // 8*14*0.7<100<16*14*0.7

    HpTupleAddr addrReturned;
    bool isFound;
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = HacHashIdxLookup(idxCtx, indexKey[i], &addrReturned, &isFound);
        EXPECT_EQ(isFound, true);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i + 1, addrReturned);
    }

    threshold = 44;  // 16*14*0.25=56，需要删除44个才达到阈值
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = HacHashIdxBatchDelete(idxCtx, indexKey, batchOut, threshold - 1, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    IndexScaleInCfgT idxScaleCfg = {
        .splitTime = 1000000,    // ut简单验证保证缩容完成
        .startTime = DbRdtsc(),  // 入参，分片开始的时间
        .isOverTime = false      // 出参，运行是否超过时间片
    };
    uint32_t stubIdx = setStubC((void *)IdxExceedSplitTime, (void *)IdxExceedSplitTimeStub);
    ret = HacHashIdxScaleIn(idxCtx, &idxScaleCfg);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 16u);
    ret = HacHashIdxDelete(idxCtx, indexKey[threshold - 1], batchOut[threshold - 1].addrOut, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxScaleIn(idxCtx, &idxScaleCfg);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(ht->hashHac.hashSize, 8u);
    clearStub(stubIdx);
}

TEST_F(UtHacHashIndex, storage_hash_index_dml_013)
{
    SetTupleAddrMode(false);  // 补充64位一般用例看护
    IndexMetaCfgT indexCfg = g_idxMetaCfgForHH;
    ShmemPtrT htShmAddr;
    IndexCtxT *idxCtx;
    indexCfg.indexCap = 10000;

    HashIndexInit(indexCfg, &htShmAddr, &idxCtx);
    HacHashTableT *ht = (HacHashTableT *)(void *)idxCtx->idxHandle;
    EXPECT_EQ(ht->hashHac.hashSize, 1024u);

    HpTupleAddr addr = 0xabcd;
    uint8_t keydata[HH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey = HHUtConstructKey(addr, keydata, HH_KEY_DATA_LEN);
    HpTupleAddr addr2 = 0xabcd + 1;
    uint8_t keydata2[HH_KEY_DATA_LEN] = "";
    IndexKeyT idxKey2 = HHUtConstructKey(addr2, keydata2, HH_KEY_DATA_LEN);

    Status ret = HacHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    HpTupleAddr addrReturned;
    bool isFound;
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);

    IndexScanCfgT scanCfg{};
    scanCfg.leftKey = &idxKey;
    IndexScanItrT iter;
    ret = HacHashIdxBeginScan(idxCtx, scanCfg, &iter);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);
    ret = HacHashIdxScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);
    IdxEndScan(idxCtx, iter);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    ret = HacHashIdxDelete(idxCtx, idxKey, addr, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = HacHashIdxInsert(idxCtx, idxKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr, addrReturned);

    IndexUpdateInfoT updateInfo = {.oldIdxKey = idxKey, .newIdxKey = idxKey2, .oldAddr = addr, .newAddr = addr2};
    ret = HacHashIdxUpdate(idxCtx, updateInfo, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey2, &addrReturned, &isFound);
    EXPECT_EQ(isFound, true);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(addr2, addrReturned);

    ret = HacHashIdxTruncate(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HacHashIdxLookup(idxCtx, idxKey, &addrReturned, &isFound);
    EXPECT_EQ(isFound, false);
    EXPECT_EQ(GMERR_OK, ret);

    HacHashIdxClose(idxCtx);
    IdxRelease(idxCtx);
    ret = HacHashIdxDrop(g_seRunCtxForHH, htShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    SetTupleAddrMode(true);
}

// 新建1个挂链桶，获取桶，验证桶数据，释放桶
TEST_F(UtHacHashIndex, hac_mem_mgr_test_001)
{
    HacMemMgrT *hacMemMgr = (HacMemMgrT *)HashGetShmAddr(GetHacMgr()->hacMemMgr);
    EXPECT_NE(hacMemMgr, nullptr);
    HacBucketT *newBucket = NULL;
    HacLogicAddressT logicAddr = HAC_INVALID_LOGICADDR;
    Status ret = HashNewExtendBucketInBlock(hacMemMgr, &newBucket, &logicAddr);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_NE(newBucket, nullptr);
    newBucket->reserved[0] = 888;
    HacBucketT *getBucket = HashGetExtendBucket(logicAddr);
    EXPECT_EQ(getBucket->reserved[0], 888);
    HashFreeExtendBucket(logicAddr);

    // 清空所有挂链桶
    HashCleanAllExtendBucket();
}

// 反复新建100000个挂链桶，获取桶，验证桶数据，释放桶
TEST_F(UtHacHashIndex, hac_mem_mgr_test_002)
{
    HacMemMgrT *hacMemMgr = (HacMemMgrT *)HashGetShmAddr(GetHacMgr()->hacMemMgr);
    EXPECT_NE(hacMemMgr, nullptr);
    uint32_t num = 100000;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; i++) {
        HacBucketT *newBucket = NULL;
        HacLogicAddressT logicAddr = HAC_INVALID_LOGICADDR;
        // 一直是第一个桶
        ret = HashNewExtendBucketInBlock(hacMemMgr, &newBucket, &logicAddr);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(logicAddr.blockId, 0);
        EXPECT_EQ(logicAddr.bucketId, 0);
        EXPECT_EQ(GMERR_OK, ret);
        newBucket->reserved[0] = i;
        HacBucketT *getBucket = HashGetExtendBucket(logicAddr);
        EXPECT_EQ(getBucket->reserved[0], i);
        HashFreeExtendBucket(logicAddr);
    }

    // 清空所有挂链桶
    HashCleanAllExtendBucket();
}

// 反复新建（HAC_BLOCK_BUCKET_NUM * HAC_MAX_BLOCK_NUM）个挂链桶，获取桶，验证桶数据，不释放桶
TEST_F(UtHacHashIndex, hac_mem_mgr_test_003)
{
    HacMemMgrT *hacMemMgr = (HacMemMgrT *)HashGetShmAddr(GetHacMgr()->hacMemMgr);
    EXPECT_NE(hacMemMgr, nullptr);
    uint32_t num = HAC_BLOCK_BUCKET_NUM * HAC_MAX_BLOCK_NUM;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; i++) {
        HacBucketT *newBucket = NULL;
        HacLogicAddressT logicAddr = HAC_INVALID_LOGICADDR;
        ret = HashNewExtendBucketInBlock(hacMemMgr, &newBucket, &logicAddr);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_NE(newBucket, nullptr);
        newBucket->reserved[0] = i;
        HacBucketT *getBucket = HashGetExtendBucket(logicAddr);
        EXPECT_EQ(getBucket->reserved[0], i);
    }

    // 清空所有挂链桶
    HashCleanAllExtendBucket();
}

// 反复新建（HAC_BLOCK_BUCKET_NUM * HAC_MAX_BLOCK_NUM + 1000）个挂链桶，获取桶，验证桶数据，不释放桶
TEST_F(UtHacHashIndex, hac_mem_mgr_test_004)
{
    HacMemMgrT *hacMemMgr = (HacMemMgrT *)HashGetShmAddr(GetHacMgr()->hacMemMgr);
    EXPECT_NE(hacMemMgr, nullptr);
    uint32_t num = HAC_BLOCK_BUCKET_NUM * HAC_MAX_BLOCK_NUM;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; i++) {
        HacBucketT *newBucket = NULL;
        HacLogicAddressT logicAddr = HAC_INVALID_LOGICADDR;
        ret = HashNewExtendBucketInBlock(hacMemMgr, &newBucket, &logicAddr);
        if (i < HAC_BLOCK_BUCKET_NUM * HAC_MAX_BLOCK_NUM) {
            // 不超过最大容量
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_NE(newBucket, nullptr);
            newBucket->reserved[0] = i;
            HacBucketT *getBucket = HashGetExtendBucket(logicAddr);
            EXPECT_EQ(getBucket->reserved[0], i);
        } else {
            // 超过最大容量
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            EXPECT_EQ(newBucket, nullptr);
            HacBucketT *getBucket = HashGetExtendBucket(logicAddr);
            EXPECT_EQ(getBucket, nullptr);
        }
    }

    // 清空所有挂链桶
    HashCleanAllExtendBucket();
}
