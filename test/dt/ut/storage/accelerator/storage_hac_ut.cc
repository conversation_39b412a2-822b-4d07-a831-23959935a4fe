/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: storage_hac_hash_index_ut.cc
 * Description: Implementation of hac ut
 * Author: lijianchuan
 * Create: 2022/9/1
 */

#include "db_common_init.h"
#include "common_init.h"
#include "se_index.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "se_hac_common.h"
#include "storage_hac_common.h"
#include "db_dynmem_algo.h"
#include "stub.h"
#include "db_config.h"

#ifdef __cplusplus
extern "C" {
#endif

DbMemCtxT *g_topShmMemCtxForHac = NULL;
DbMemCtxT *g_topDynMemCtxForHac = NULL;
int g_stubId;

#ifdef __cplusplus
}
#endif

void HacPrepare(DbMemCtxT **topShmMemCtx, DbMemCtxT **dynMemCtx)
{
    *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(topShmMemCtx);

    DbMemCtxArgsT hashArgs = {0};
    hashArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    hashArgs.memType = DB_DYNAMIC_MEMORY;
    hashArgs.init = DynamicAlgoInit;

    *dynMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "se dynamic", &hashArgs);
    DB_POINTER(dynMemCtx);
}

class UtHac : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        init();
        int id = setStubC((void *)DbCfgGetInt32, (void *)DbCfgGetInt32Stub);
        EXPECT_GT(id, 0);
        int32_t ret = CommonInit();
        EXPECT_EQ(GMERR_OK, ret);

        HacPrepare(&g_topShmMemCtxForHac, &g_topDynMemCtxForHac);
        StatusInter status = HacMgrCtxInitImpl(NULL, (DbMemCtxT *)g_topShmMemCtxForHac, 2);
        EXPECT_EQ(STATUS_OK_INTER, status);
        g_stubId = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgStub);
        EXPECT_GE(g_stubId, 0);
        int stubId = setStubC((void *)HacReceiveMsg, (void *)HacReceiveMsgStub);
        EXPECT_GE(stubId, 0);
        stubId = setStubC((void *)HacInjectMemory, (void *)HacInjectMemoryStub);
        EXPECT_GE(stubId, 0);
        stubId = setStubC((void *)HashSecHtCheckExpand, (void *)HashSecHtCheckExpandStub);
        EXPECT_GE(stubId, 0);
    };

    static void TearDownTestCase()
    {
        clearAllStub();
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

TEST_F(UtHac, storage_hac_open_and_close)
{
    HacCtxT hacCtx;
    IndexCtx idxCtx = (IndexCtx){0};
    idxCtx.idxRunCtx = (IndexRunCtxT *)(void *)&hacCtx;
    Status ret = HacOpen(&idxCtx, g_topDynMemCtxForHac);
    EXPECT_EQ(ret, GMERR_OK);
    HacClose(&idxCtx);
}

// 只能模拟报文解析和写应答报文，没有批量操作的具体实现
TEST_F(UtHac, storage_hac_batch_op)
{
    HacCtxT hacCtx;
    IndexCtx idxCtx = (IndexCtx){0};
    HacHashTableT ht = (HacHashTableT){0};
    idxCtx.idxHandle = &ht.idxBase;
    idxCtx.idxRunCtx = (IndexRunCtxT *)(void *)&hacCtx;
    uint16_t keyLen = 1;
    Status ret = HacOpen(&idxCtx, g_topDynMemCtxForHac);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.idxType = HAC_HASH_INDEX;

    uint32_t batchNum = 10;
    IndexKeyT *indexkey = (IndexKeyT *)malloc(batchNum * sizeof(IndexKeyT));
    for (uint32_t i = 0; i < batchNum; i++) {
        indexkey[i].keyLen = keyLen;
        indexkey[i].keyData = (uint8_t *)malloc(keyLen);
    }
    HpBatchOutT *batchOut = (HpBatchOutT *)malloc(batchNum * sizeof(HpBatchOutT));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
    }
    // 验证三个对外接口的正常运行
    hacCtx.idxKey = indexkey;
    hacCtx.addr = batchOut;
    hacCtx.hacType = HAC_HASH_INSERT;
    ret = HacHashBatchExecute(&hacCtx, batchNum);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.hacType = HAC_HASH_LOOKUP;
    ret = HacHashBatchExecute(&hacCtx, batchNum);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.hacType = HAC_HASH_DELETE;
    ret = HacHashBatchExecute(&hacCtx, batchNum);
    EXPECT_EQ(ret, GMERR_OK);
    HacClose(&idxCtx);
    for (uint32_t i = 0; i < batchNum; i++) {
        free(indexkey[i].keyData);
    }
    free(indexkey);
    free(batchOut);
}

// 批量操作操作单个批量上限
TEST_F(UtHac, storage_hac_big_batch_op)
{
    HacCtxT hacCtx;
    IndexCtx idxCtx = (IndexCtx){0};
    HacHashTableT ht = (HacHashTableT){0};
    idxCtx.idxHandle = &ht.idxBase;
    idxCtx.idxRunCtx = (IndexRunCtxT *)(void *)&hacCtx;
    uint16_t keyLen = 1;
    Status ret = HacOpen(&idxCtx, g_topDynMemCtxForHac);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.idxType = HAC_HASH_INDEX;

    uint32_t batchNum = 1000;
    IndexKeyT *indexkey = (IndexKeyT *)malloc(batchNum * sizeof(IndexKeyT));
    for (uint32_t i = 0; i < batchNum; i++) {
        indexkey[i].keyLen = keyLen;
        indexkey[i].keyData = (uint8_t *)malloc(keyLen);
    }
    HpBatchOutT *batchOut = (HpBatchOutT *)malloc(batchNum * sizeof(HpBatchOutT));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
    }
    // 验证三个对外接口的正常运行
    hacCtx.idxKey = indexkey;
    hacCtx.addr = batchOut;
    hacCtx.hacType = HAC_HASH_INSERT;
    ret = HacHashBatchExecute(&hacCtx, batchNum);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.hacType = HAC_HASH_LOOKUP;
    ret = HacHashBatchExecute(&hacCtx, batchNum);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.hacType = HAC_HASH_DELETE;
    ret = HacHashBatchExecute(&hacCtx, batchNum);
    EXPECT_EQ(ret, GMERR_OK);
    HacClose(&idxCtx);
    for (uint32_t i = 0; i < batchNum; i++) {
        free(indexkey[i].keyData);
    }
    free(indexkey);
    free(batchOut);
}

Status UtCategorizeStub(
    IndexCtxT *idxCtx, IdxBatchLookupIterT *iter, IdxTupleOrIterT addrOrIter, uint32_t pos, IdxValueTypeE valueType)
{
    return GMERR_OK;
}

Status UtLookupStub(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound)
{
    return GMERR_OK;
}

Status UtInsertOrDeleteStub(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, bool isInsert)
{
    return GMERR_OK;
}

Status UtKeyCmpStub(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    *isMatch = false;
    return GMERR_NO_DATA;
}

enum class HacOpType {
    UT_HAC_HASH_INSERT = 0,
    UT_HAC_HASH_LOOKUP = 1,
    UT_HAC_HASH_DELETE = 2,
    UT_MULTI_HASH_INSERT = 3,
    UT_MULTI_HASH_DELETE = 4,
};

typedef struct HacUtInput {
    HacCtx *hacCtx;
    int32_t *ret;
} HacUtInputT;

void CheckOpReturn(HacOpType type, HacUtInput input, uint32_t cnt)
{
    ShmemPtrT ptr = DbShmemCtxAlloc(g_topShmMemCtxForHac, sizeof(SecHashTableT));
    EXPECT_TRUE(DbIsShmPtrValid(ptr));
    SecHashTableT *ht = (SecHashTableT *)DbShmPtrToAddr(ptr);
    EXPECT_TRUE(ht != NULL);
    DbListT *list = &input.hacCtx->list;
    DbCreateListWithExtendSize(list, sizeof(MultiHashDMLParaT), cnt, g_topDynMemCtxForHac);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < cnt; i++) {
        uint32_t batchNum = i + 1;
        if (type == HacOpType::UT_HAC_HASH_LOOKUP) {
            ret = HacHashBatchExecute(input.hacCtx, batchNum);
            EXPECT_EQ(ret, input.ret[i]);
        } else if (type <= HacOpType::UT_HAC_HASH_DELETE) {
            ret = HacHashBatchExecute(input.hacCtx, batchNum);
            EXPECT_EQ(ret, input.ret[i]);
        } else if (type == HacOpType::UT_MULTI_HASH_INSERT) {
            MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbNewListItem(list);
            dmlPara->isInsert = true;
            dmlPara->secHash = ht;
            ret = MultiHashBatchExecuteByHac(input.hacCtx, list);
            EXPECT_EQ(ret, input.ret[i]);
        } else if (type == HacOpType::UT_MULTI_HASH_DELETE) {
            MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbNewListItem(list);
            dmlPara->isInsert = false;
            dmlPara->secHash = ht;
            ret = MultiHashBatchExecuteByHac(input.hacCtx, list);
            EXPECT_EQ(ret, input.ret[i]);
        }
    }
}

// hachash错误码校验
TEST_F(UtHac, storage_hac_batch_op_error)
{
    clearStub(g_stubId);
    int s1 = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgErrorStub);
    EXPECT_GE(s1, 0);
    HacCtxT hacCtx;
    IndexCtx idxCtx = (IndexCtx){0};
    HacHashTableT ht = (HacHashTableT){0};
    idxCtx.idxMetaCfg.idxConstraint = PRIMARY;
    idxCtx.idxHandle = &ht.idxBase;
    idxCtx.idxRunCtx = (IndexRunCtxT *)(void *)&hacCtx;
    idxCtx.idxOpenCfg.callbackFunc.keyCmp = UtKeyCmpStub;
    uint16_t keyLen = 1;
    Status ret = HacOpen(&idxCtx, g_topDynMemCtxForHac);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.idxType = HAC_HASH_INDEX;
    hacCtx.idxInsertOrDeleteFunc = UtInsertOrDeleteStub;
    hacCtx.idxLookupFunc = UtLookupStub;

    uint32_t batchNum = 30;
    IndexKeyT *indexkey = (IndexKeyT *)malloc(batchNum * sizeof(IndexKeyT));
    for (uint32_t i = 0; i < batchNum; i++) {
        indexkey[i].keyLen = keyLen;
        indexkey[i].keyData = (uint8_t *)malloc(keyLen);
    }
    HpBatchOutT *batchOut = (HpBatchOutT *)malloc(batchNum * sizeof(HpBatchOutT));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
    }
    // 不同报错返回不同错误码
    int32_t batchRet[][9] = {
        {GMERR_OK, GMERR_OUT_OF_MEMORY, GMERR_OUT_OF_MEMORY, GMERR_INTERNAL_ERROR, GMERR_UNIQUE_VIOLATION,
            GMERR_UNIQUE_VIOLATION, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR},
        {GMERR_OK, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR,
            GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR},
        {GMERR_OK, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR,
            GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR},
        {GMERR_OK, GMERR_OUT_OF_MEMORY, GMERR_OUT_OF_MEMORY, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR,
            GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR},
    };
    hacCtx.addr = batchOut;
    hacCtx.idxKey = indexkey;
    hacCtx.isInsert = true;
    hacCtx.hacType = HAC_HASH_INSERT;
    IdxBatchLookupParaT para = {.categorizeFunc = UtCategorizeStub, .iter = NULL};
    HacUtInputT input = {.hacCtx = &hacCtx, .ret = NULL};
    input.ret = batchRet[0];
    CheckOpReturn(HacOpType::UT_HAC_HASH_INSERT, input, 9);
    input.ret = batchRet[1];
    hacCtx.isInsert = false;
    hacCtx.para = &para;
    hacCtx.hacType = HAC_HASH_LOOKUP;
    CheckOpReturn(HacOpType::UT_HAC_HASH_LOOKUP, input, 9);
    input.ret = batchRet[2];
    hacCtx.hacType = HAC_HASH_DELETE;
    CheckOpReturn(HacOpType::UT_HAC_HASH_DELETE, input, 9);

    idxCtx.idxMetaCfg.idxConstraint = UNIQUE;
    input.ret = batchRet[3];
    hacCtx.isInsert = true;
    hacCtx.hacType = HAC_HASH_INSERT;
    CheckOpReturn(HacOpType::UT_HAC_HASH_INSERT, input, 9);
    input.ret = batchRet[1];
    hacCtx.isInsert = false;
    hacCtx.hacType = HAC_HASH_LOOKUP;
    CheckOpReturn(HacOpType::UT_HAC_HASH_LOOKUP, input, 9);
    input.ret = batchRet[2];
    hacCtx.hacType = HAC_HASH_DELETE;
    CheckOpReturn(HacOpType::UT_HAC_HASH_DELETE, input, 9);

    HacClose(&idxCtx);
    for (uint32_t i = 0; i < batchNum; i++) {
        free(indexkey[i].keyData);
    }
    free(indexkey);
    free(batchOut);
    clearStub(s1);
    g_stubId = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgStub);
    EXPECT_GE(g_stubId, 0);
}

// multihash错误码校验
TEST_F(UtHac, storage_hac_batch_op_error_02)
{
    SetTupleAddrMode(false);
    clearStub(g_stubId);
    int s1 = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgErrorStub);
    EXPECT_GE(s1, 0);
    HacCtxT hacCtx;
    IndexCtx idxCtx = (IndexCtx){0};
    HacHashTableT ht = (HacHashTableT){0};
    idxCtx.idxMetaCfg.idxConstraint = NON_UNIQUE;
    idxCtx.idxHandle = &ht.idxBase;
    idxCtx.idxRunCtx = (IndexRunCtxT *)(void *)&hacCtx;
    uint16_t keyLen = 1;
    Status ret = HacOpen(&idxCtx, g_topDynMemCtxForHac);
    EXPECT_EQ(ret, GMERR_OK);
    hacCtx.idxType = MULTI_HASH_INDEX;

    uint32_t batchNum = 30;
    IndexKeyT *indexkey = (IndexKeyT *)malloc(batchNum * sizeof(IndexKeyT));
    for (uint32_t i = 0; i < batchNum; i++) {
        indexkey[i].keyLen = keyLen;
        indexkey[i].keyData = (uint8_t *)malloc(keyLen);
    }
    HpBatchOutT *batchOut = (HpBatchOutT *)malloc(batchNum * sizeof(HpBatchOutT));
    for (uint32_t i = 0; i < batchNum; i++) {
        batchOut[i].addrOut = i + 1;
    }
    // 不同报错返回不同错误码
    int32_t batchRet[][9] = {
        {GMERR_OK, GMERR_OUT_OF_MEMORY, GMERR_OUT_OF_MEMORY, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR,
            GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR},
        {GMERR_OK, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR,
            GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR, GMERR_INTERNAL_ERROR}};

    hacCtx.addr = batchOut;
    hacCtx.idxKey = indexkey;
    hacCtx.isInsert = true;
    hacCtx.hacType = MULTI_HASH_INSERT;
    HacUtInputT input = {.hacCtx = &hacCtx, .ret = NULL};

    input.ret = batchRet[0];
    CheckOpReturn(HacOpType::UT_MULTI_HASH_INSERT, input, 9);
    hacCtx.isInsert = false;
    input.ret = batchRet[1];
    hacCtx.hacType = MULTI_HASH_DELETE;
    CheckOpReturn(HacOpType::UT_MULTI_HASH_DELETE, input, 9);
    hacCtx.isSameSecEntry = false;
    CheckOpReturn(HacOpType::UT_MULTI_HASH_DELETE, input, 9);
    hacCtx.isInsert = true;
    input.ret = batchRet[0];
    hacCtx.hacType = MULTI_HASH_INSERT;
    CheckOpReturn(HacOpType::UT_MULTI_HASH_INSERT, input, 9);

    HacClose(&idxCtx);
    for (uint32_t i = 0; i < batchNum; i++) {
        free(indexkey[i].keyData);
    }
    free(indexkey);
    free(batchOut);
    clearStub(s1);
    g_stubId = setStubC((void *)HacSendMsg, (void *)HacSendAndReceiveMsgStub);
    EXPECT_GE(g_stubId, 0);
    SetTupleAddrMode(true);
}

// 校验hash算法, 参考smhasher开源实现
TEST_F(UtHac, testHacCrcHash32Verification)
{
    uint8_t key[256] = {0};
    uint32_t hashCode[256];
    for (uint32_t i = 0; i < 256; i++) {
        key[i] = (uint8_t)i;
        hashCode[i] = HacCrcHash32(key, i, 256 - i);
    }
    uint32_t verificationHashCode = HacCrcHash32((const uint8_t *)hashCode, 1024, 0);
    EXPECT_EQ(verificationHashCode, 0xf2a1da78);
}

TEST_F(UtHac, testHacCrcHash32ConflictCheck)
{
    // 连续数据尽量没有hash冲突
    std::set<uint32_t> hashSet;
    uint32_t num = 400000;
    for (uint32_t key = 0; key < num; key++) {
        hashSet.emplace(HacCrcHash32((const uint8_t *)&key, 4, 10));
    }
    EXPECT_EQ(hashSet.size(), num);

    // 随机增加一个字节尽量没有hash冲突
    hashSet.clear();
    // 按最大key长来测
    uint32_t sum = 512;
    uint8_t indexKey[sum];
    std::srand(time(NULL));
    for (uint32_t i = 0; i < sum; i++) {
        indexKey[i] = (uint8_t)rand();  // 截断
    }
    for (uint32_t i = 0; i < sum; i++) {
        hashSet.emplace(HacCrcHash32(indexKey, i, 10));
    }
    EXPECT_EQ(hashSet.size(), sum);

    // 在随机位置（本测试指定位置）随机替换一个字节尽量没有hash冲突
    hashSet.clear();
    for (uint32_t i = 0; i <= 255; i++) {
        indexKey[37] = (uint8_t)i;
        hashSet.emplace(HacCrcHash32((const uint8_t *)&indexKey, sum, 10));
    }
    EXPECT_EQ(hashSet.size(), 256);
}
