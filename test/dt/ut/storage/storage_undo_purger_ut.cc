/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: storage_undo_purger_ut.cc
 * Description:
 * Author: zhaoyongluo
 * Create: 2022/07/25
 */

#include "gtest/gtest.h"
#include "db_table_space.h"
#include "se_heap_inner.h"
#include "se_heap_utils.h"
#include "common_init.h"
#include "stub.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#include "storage_ut_common.h"
#include "drt_instance.h"
#include "se_instance.h"
#include "se_page_mgr.h"
#include "se_trx_inner.h"
#include "se_undo.h"
#include "dm_meta_prop_label.h"
#include "dm_data_prop.h"
#include "se_undo_addr.h"
#include "adpt_sleep.h"
#include "se_dfx.h"
#include "storage_session.h"
#include "se_daemon.h"
#include "se_heap_access_inner.h"
#include "se_undo_purger.h"
#include "dm_meta_vertex_label.h"
#include "se_trx_mgr.h"
#include "se_heap_inner.h"
#include "se_heap_fetch.h"
using namespace std;
#ifdef __cplusplus
extern "C" {
#endif
// 此处照搬原定义，用于测试

const double PURGER_SPILT_TIME_NO_LIMIT = 10000000;  // 10000 s

const double PURGER_SPILT_TIME_NO_TIME = -1;  // 设置成负数，肯定能够让分片退出

extern Status PurgerSetIdxKeyCmpCallback(SeRunCtxHdT seRunCtx, uint32_t labelId, IndexOpenCfgT *idxOpenCfg);
extern StatusInter OptimisticTrxCommitActive(TrxT *trx, bool *isTrxCommitSuccess);

#ifdef __cplusplus
}
#endif

Status StubPurgerReleaseLabelAndLatch(DmLabelTypeE tupleType, void *label)
{
    return GMERR_OK;
}

Status StubHeapFetchHpTuple(const HpRunHdlT heapRunHdl, HpTupleAddr heapTupleAddr, HeapTupleT *heapTuple)
{
    return GMERR_OK;
}

StatusInter StubHeapLabelDeSerialHpTuple(HpReadRowInfoT *readRowInfo, void *userData)
{
    return STATUS_OK_INTER;
}

static uint64_t g_namespaceTrxIdArray[100] = {0};

static Status UtGetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance)
{
    if (trxId == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *trxId = g_namespaceTrxIdArray[labelId];
    *trxIdLastModify = g_namespaceTrxIdArray[labelId];
    return GMERR_OK;
}

static Status UtSetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance)
{
    g_namespaceTrxIdArray[labelId] = trxId;
    return GMERR_OK;
}

static Status UtGetLabelNameByEdgeLabelId(uint32_t elId, char *labelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelName);
    return GMERR_DATA_EXCEPTION;
}

class UtStorageUndoPurger : public testing::Test {

public:
    static SeInstanceT *gSeIns;
    static DbMemCtxT *gTopShmMemCtx;
    static void *gTopDynamicMemCtx;
    static ShmemPtrT gUtPurgerReclaimHeapShmAddr;
    static int gUtBlockTrxCommit;
    static DbMemCtxT *gVertexLabelMemCtx;

protected:
    void SetUp()
    {
        init();
        int stub1 = setStubC((void *)HeapCreate, (void *)HeapCreateMock);
        EXPECT_GE(stub1, 0);
        int stub2 = setStubC((void *)MdCreateCachedPageList, (void *)MdCreateCachedPageListMock);
        EXPECT_GE(stub2, 0);
    }
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        gTopShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
        ASSERT_TRUE(gTopShmMemCtx != nullptr);

        DbMemCtxArgsT dyargs = {0};
        dyargs.ctxSize = sizeof(DbDynamicMemCtxT);
        dyargs.memType = DB_DYNAMIC_MEMORY;
        dyargs.init = DynamicAlgoInit;
        gTopDynamicMemCtx =
            (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "Se Dynamic MemCtx", &dyargs);
        ASSERT_TRUE(gTopDynamicMemCtx != nullptr);
        // set up default dynamic memctx
        DbMemCtxSwitchTo((DbMemCtxT *)gTopDynamicMemCtx);

        SeConfigT config = {0};
        config.deviceSize = SE_DEFAULT_DEV_SIZE;
        config.pageSize = SE_DEFAULT_PAGE_SIZE;
        config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
        config.instanceId = GET_INSTANCE_ID;
        config.maxTrxNum = MAX_TRX_NUM;

        gSeIns = NULL;
        ret = SeCreateInstance(NULL, gTopShmMemCtx, &config, (SeInstanceHdT *)&gSeIns);
        ASSERT_EQ(GMERR_OK, ret);
        OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelLastTrxIdAndTrxCommitTimeById, UtGetLabelLastTrxIdAndTrxCommitTimeById,
            UtGetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[TRX_CHECK_READVIEW_NUM] = {
            UtSetLabelLastTrxIdAndTrxCommitTimeById, UtSetLabelLastTrxIdAndTrxCommitTimeById,
            UtSetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId};
        SeInitTrxMgrCheckFunc(GET_INSTANCE_ID, setFunc, getFunc, getLabelName);
        DbMemCtxArgsT args = {0};
        gVertexLabelMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "ut_dm_print", &args);
    };
    static void TearDownTestCase()
    {
        clearAllStub();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

SeInstanceT *UtStorageUndoPurger::gSeIns{nullptr};
DbMemCtxT *UtStorageUndoPurger::gTopShmMemCtx{nullptr};
void *UtStorageUndoPurger::gTopDynamicMemCtx{nullptr};
ShmemPtrT UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = DB_INVALID_SHMPTR;
int UtStorageUndoPurger::gUtBlockTrxCommit = 0;
DbMemCtxT *UtStorageUndoPurger::gVertexLabelMemCtx{nullptr};

Status UtGetHeapRunCtx(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, HpRunHdlT *heapRunHdl)
{
    static DmVertexLabelT stubVtxLabel = {0};
    static MetaVertexLabelT metaVL = {0};
    stubVtxLabel.metaVertexLabel = &metaVL;
    stubVtxLabel.memCtx = UtStorageUndoPurger::gVertexLabelMemCtx;
    MetaShmAlloc(UtStorageUndoPurger::gTopShmMemCtx, (void **)&stubVtxLabel.commonInfo, &stubVtxLabel.commonInfoShmPtr,
        sizeof(VertexLabelCommonInfoT));
    stubVtxLabel.commonInfo->heapInfo.heapShmAddr =
        DbIsShmPtrValid(heapShmAddr) ? heapShmAddr : UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr;

    HeapRunCtxAllocCfgT cfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = stubVtxLabel.commonInfo->heapInfo.heapShmAddr,
        .seRunCtx = seRunCtx,
        .dmInfo = (void *)&stubVtxLabel,
        .isBackGround = false};
    Status ret = HeapLabelAllocAndInitRunctx(&cfg, heapRunHdl);
    return ret;
}

void UtOptimisticTrxInsertVarHeapData(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, int rowNum,
    vector<HpTupleAddr> *tupleAddrList, std::string prefix = "insert")
{
    HpRunHdlT heapRunHdl = nullptr;
    UtOptimisticTrxBegin(seRunCtx);
    Status ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < rowNum; i++) {
        ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
        ASSERT_EQ(GMERR_OK, ret);
        string data = prefix + std::to_string(i);
        HeapTupleBufT heapTupleBuf = {
            .bufSize = (uint32_t)data.size(),
            .buf = (uint8_t *)data.c_str(),
        };
        HpTupleAddr heapTupleAddr;
        ret = HeapLabelInsertHpTupleBuffer(heapRunHdl, &heapTupleBuf, &heapTupleAddr);
        ASSERT_EQ(GMERR_OK, ret);
        if (tupleAddrList != NULL) {
            tupleAddrList->push_back(heapTupleAddr);
        }
        HeapLabelResetCtx(heapRunHdl);
    }
    HeapLabelReleaseRunctx(heapRunHdl);
    UtTrxCommit(seRunCtx);
}

void UtUpdateVarHeapData(
    SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, vector<HpTupleAddr> &tupleAddrList, std::string prefix = "update")
{
    HpRunHdlT heapRunHdl = nullptr;
    Status ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < (int)tupleAddrList.size(); i++) {
        ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
        ASSERT_EQ(GMERR_OK, ret);
        string data = prefix + std::to_string(i);
        HeapTupleBufT heapTupleBuf = {
            .bufSize = (uint32_t)data.size(),
            .buf = (uint8_t *)data.c_str(),
        };
        HpTupleAddr heapTupleAddr = tupleAddrList[i];
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapLabelUpdateWithHpTupleBuf(heapRunHdl, &heapTupleBuf, heapTupleAddr, &out);
        ASSERT_EQ(GMERR_OK, ret);
        HeapLabelResetCtx(heapRunHdl);
    }
    HeapLabelReleaseRunctx(heapRunHdl);
}

void UtUpdateVarHeapDataWithoutBeginTrx(
    HpRunHdlT heapRunHdl, vector<HpTupleAddr> &tupleAddrList, std::string prefix = "update")
{
    // 用于不开启事务，首边更新更长bufSize的情况
    Status ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);
    HeapLabelSetIsSkipUndoIndex(heapRunHdl, true);
    for (int i = 0; i < (int)tupleAddrList.size(); i++) {
        string data = prefix + std::to_string(i);
        for (int32_t j = 0; j < i; j++) {
            data = data + std::to_string(j);
        }
        HeapTupleBufT heapTupleBuf = {
            .bufSize = (uint32_t)data.size(),
            .buf = (uint8_t *)data.c_str(),
        };
        HpTupleAddr heapTupleAddr = tupleAddrList[i];
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapLabelUpdateWithHpTupleBuf(heapRunHdl, &heapTupleBuf, heapTupleAddr, &out);
        ASSERT_EQ(GMERR_OK, ret);
    }
    HeapLabelSetIsSkipUndoIndex(heapRunHdl, false);
    HeapLabelResetCtx(heapRunHdl);
}

void UtDeleteVarHeapDataWithoutBeginTrx(HpRunHdlT heapRunHdl, vector<HpTupleAddr> &tupleAddrList)
{
    // 用于不开启事务，首边更新更长bufSize的情况
    Status ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < (int)tupleAddrList.size(); i++) {
        HpTupleAddr heapTupleAddr = tupleAddrList[i];
        ret = HeapLabelDeleteHpTuple(heapRunHdl, heapTupleAddr);
        ASSERT_EQ(GMERR_OK, ret);
    }
    HeapLabelResetCtx(heapRunHdl);
}

void UtOptimisticTrxUpdateVarHeapData(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, vector<HpTupleAddr> &tupleAddrList)
{
    UtOptimisticTrxBegin(seRunCtx);
    UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    UtTrxCommit(seRunCtx);
}

void UtDeleteVarHeapData(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, vector<HpTupleAddr> &tupleAddrList)
{
    HpRunHdlT heapRunHdl = nullptr;
    Status ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < (int)tupleAddrList.size(); i++) {
        ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
        ASSERT_EQ(GMERR_OK, ret);
        HpTupleAddr heapTupleAddr = tupleAddrList[i];
        ret = HeapLabelDeleteHpTuple(heapRunHdl, heapTupleAddr);
        ASSERT_EQ(GMERR_OK, ret);
        HeapLabelResetCtx(heapRunHdl);
    }
    HeapLabelReleaseRunctx(heapRunHdl);
}

void UtOptimisticTrxDeleteVarHeapData(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, vector<HpTupleAddr> &tupleAddrList)
{
    UtOptimisticTrxBegin(seRunCtx);
    UtDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    UtTrxCommit(seRunCtx);
}

Status StubPurgerGetHeapRunCtx(
    uint32_t labelId, DbMemCtxT *memCtx, SeRunCtxHdT seCtx, DmLabelTypeE labelType, HpRunHdlT *heapRunCtx)
{
    HpRunHdlT heapRunHdl = nullptr;
    Status ret = UtGetHeapRunCtx(seCtx, DB_INVALID_SHMPTR, &heapRunHdl);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_UNDO_PURGER, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    *heapRunCtx = heapRunHdl;
    return STATUS_OK_INTER;
}

void UtTrxClearOnCommitOrRollback(TrxT *trx)
{
    UtStorageUndoPurger::gUtBlockTrxCommit = 1;  // 阻塞事务提交
    while (UtStorageUndoPurger::gUtBlockTrxCommit != 0) {
        DbSleep(100);
    }
    TrxClearOnCommitOrRollback(trx);
}

void UtHeapFetchExpectFail(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, HpTupleAddr addr)
{
    HpRunHdlT heapRunHdl = NULL;
    Status ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ret = HeapFetchHpTupleBuffer(heapRunHdl, addr, &heapTupleBuf);
    EXPECT_NE(GMERR_OK, ret);
    HeapLabelReleaseRunctx(heapRunHdl);
}

void UtHeapFetchExpectNoData(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, HpTupleAddr addr)
{
    HpRunHdlT heapRunHdl = NULL;
    Status ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ret = HeapFetchHpTupleBuffer(heapRunHdl, addr, &heapTupleBuf);
    EXPECT_EQ(GMERR_NO_DATA, ret);
    HeapLabelReleaseRunctx(heapRunHdl);
}

void UtVerifyHeapFetch(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, HpTupleAddr addr, string expectData)
{
    HpRunHdlT heapRunHdl = NULL;
    Status ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);
    TupleBufT heapTupleBuf;
    TupleBufInit(&heapTupleBuf, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ret = HeapFetchHpTupleBuffer(heapRunHdl, addr, &heapTupleBuf);
    EXPECT_EQ(GMERR_OK, ret);
    string data((char *)heapTupleBuf.buf, heapTupleBuf.len);
    EXPECT_TRUE(data.compare(expectData) == 0);
    TupleBufRelease(&heapTupleBuf);
    HeapLabelReleaseRunctx(heapRunHdl);
}

void UtOptimisticTrxVerifyHeapFetch(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, HpTupleAddr addr, string expectData)
{
    UtOptimisticTrxBegin(seRunCtx, true);
    UtVerifyHeapFetch(seRunCtx, heapShmAddr, addr, expectData);
    UtTrxCommit(seRunCtx);
}

void UtOptimisticTrxHeapFetchExpectNoData(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr, HpTupleAddr addr)
{
    UtOptimisticTrxBegin(seRunCtx, true);
    UtHeapFetchExpectNoData(seRunCtx, heapShmAddr, addr);
    UtTrxCommit(seRunCtx);
}

// 校验只insert,也会额外多申请一个Undo页（给retainedUndoCache用）
TEST_F(UtStorageUndoPurger, UndoRetainedUndoCache)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 1;
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;

    // 准备heap，预置数据，插入1条，预期Insert占用2个Undo页 (还有一个页给retainedUndoCache)
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    // 插入数据前，检查undo占用页数
    int undoPageNumBeforeInsert = undoCtx->undoSpace->meta.usedPageCnt;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    int undoPageNumAfterInsert = undoCtx->undoSpace->meta.usedPageCnt;
    EXPECT_EQ(undoPageNumAfterInsert - undoPageNumBeforeInsert, 2);

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, purge_TrxBeginAgain)
{
    SeRunCtxHdT seRunCtx0 = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx0);
    ASSERT_EQ(ret, GMERR_OK);

    // 准备heap，预置数据，插入1条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx0, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;

    int dataNum = 1;
    UtOptimisticTrxBegin(seRunCtx0);
    HpRunHdlT heapRunHdl0 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx0, heapShmAddr, &heapRunHdl0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = HeapLabelOpen(heapRunHdl0, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        string data = "insert" + std::to_string(i);
        HeapTupleBufT heapTupleBuf = {
            .bufSize = (uint32_t)data.size(),
            .buf = (uint8_t *)data.c_str(),
        };
        HpTupleAddr heapTupleAddr;
        ret = HeapLabelInsertHpTupleBuffer(heapRunHdl0, &heapTupleBuf, &heapTupleAddr);
        ASSERT_EQ(GMERR_OK, ret);
        tupleAddrList.push_back(heapTupleAddr);
    }
    HeapLabelResetCtx(heapRunHdl0);
    UtTrxCommit(seRunCtx0);

    // 预置数据完成
    SeRunCtxHdT seRunCtx1 = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx1);
    ASSERT_EQ(ret, GMERR_OK);

    // trx0执行删除，trx1作为同期事务，执行更新操作
    // trx0提交，trx1也提交，预期冲突提交失败,重新begin事务，预期还是提交失败
    UtOptimisticTrxBegin(seRunCtx1);
    HpRunHdlT heapRunHdl1 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx1, heapShmAddr, &heapRunHdl1);
    ASSERT_EQ(GMERR_OK, ret);
    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl1, tupleAddrList);

    UtOptimisticTrxBegin(seRunCtx0);
    ret = HeapLabelOpen(heapRunHdl0, HEAP_OPTYPE_DELETE, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < dataNum; i++) {
        ret = HeapLabelDeleteHpTuple(heapRunHdl0, tupleAddrList[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    HeapLabelResetCtx(heapRunHdl0);
    UtTrxCommit(seRunCtx0);

    ret = SeTransCommit(seRunCtx1);
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    // 测试trx1不结束事务，重新开启事务
    UtOptimisticTrxBegin(seRunCtx1);
    ret = SeTransCommit(seRunCtx1);
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    int stubIdx = setStubC((void *)HeapFetchHpTuple, (void *)StubHeapFetchHpTuple);
    ASSERT_TRUE(stubIdx >= 0);
    ret = SeTransRollback(seRunCtx1, false);
    ASSERT_EQ(GMERR_OK, ret);
    int clearStubRet = clearStub(stubIdx);
    ASSERT_TRUE(clearStubRet >= 0);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_GT(purgerCtx->clearList.len, 0u);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    UtDropHeap(seRunCtx0, heapShmAddr);
    SeClose(seRunCtx0);
    SeClose(seRunCtx1);
}

TEST_F(UtStorageUndoPurger, purgeDfxCheck)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxBegin(seRunCtx);
    HpRunHdlT heapRunHdl = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx, heapShmAddr, &heapRunHdl);
    ASSERT_EQ(GMERR_OK, ret);

    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        string data = "insert" + std::to_string(i);
        HeapTupleBufT heapTupleBuf = {
            .bufSize = (uint32_t)data.size(),
            .buf = (uint8_t *)data.c_str(),
        };
        HpTupleAddr heapTupleAddr;
        ret = HeapLabelInsertHpTupleBuffer(heapRunHdl, &heapTupleBuf, &heapTupleAddr);
        ASSERT_EQ(GMERR_OK, ret);
        tupleAddrList.push_back(heapTupleAddr);
    }
    HeapLabelResetCtx(heapRunHdl);

    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl, tupleAddrList);
    UtTrxCommit(seRunCtx);

    UtOptimisticTrxBegin(seRunCtx);

    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_DELETE, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        ret = HeapLabelDeleteHpTuple(heapRunHdl, tupleAddrList[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    HeapLabelResetCtx(heapRunHdl);

    UtTrxCommit(seRunCtx);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_GT(purgerCtx->clearList.len, 0u);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, PurgerReclaimInvisibleUndoHeapUpdate1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 清理前检查数据
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[0], string("update0"));

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_GT(purgerCtx->clearList.len, 0u);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[5], string("update5"));
    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, PurgerReclaimInvisibleUndoHeapUpdate2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    // 单独申请上下文检查清理效果，维持较老事务
    SeRunCtxHdT seRunCtxForOldTrx = NULL;
    ret =
        SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtxForOldTrx);
    ASSERT_EQ(ret, GMERR_OK);

    // 清理前获取结果
    UtOptimisticTrxBegin(seRunCtxForOldTrx, true);
    UtVerifyHeapFetch(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4], string("insert4"));

    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId + 1);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    // 清理后获取结果，老事务应该读不到数据了，返回错误码才对
    UtHeapFetchExpectFail(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4]);
    UtTrxCommit(seRunCtxForOldTrx);
    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[5], string("update5"));
    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, PurgerReclaimInvisibleUndoHeapDelete1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 清理前检查数据，执行过删除，新事务找不到记录
    UtOptimisticTrxHeapFetchExpectNoData(seRunCtx, heapShmAddr, tupleAddrList[0]);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    // 没有影响，一样找不到数据，没有受到清理的影响
    UtOptimisticTrxHeapFetchExpectNoData(seRunCtx, heapShmAddr, tupleAddrList[0]);
    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, PurgerReclaimInvisibleUndoHeapDelete2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    // 单独申请上下文检查清理效果，维持较老事务
    SeRunCtxHdT seRunCtxForOldTrx = NULL;
    ret =
        SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtxForOldTrx);
    ASSERT_EQ(ret, GMERR_OK);

    // 清理前获取结果
    UtOptimisticTrxBegin(seRunCtxForOldTrx, true);
    UtVerifyHeapFetch(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4], string("insert4"));

    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    // ut强制清理所有undo log，再通过老事务配置读Heap，预期读不到
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId + 1);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    // 清理后获取结果，老事务应该读不到数据了，返回错误码才对
    UtHeapFetchExpectFail(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4]);
    UtTrxCommit(seRunCtxForOldTrx);
    // 清理后检查数据还是获取不到
    UtOptimisticTrxHeapFetchExpectNoData(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4]);
    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

void *UtBlockTrxCommit(void *arg)
{
    DbSetServerThreadFlag();
    SeRunCtxHdT blockSeRunCtx = *(SeRunCtxHdT *)arg;
    UtTrxCommit(blockSeRunCtx);
    return NULL;
}

TEST_F(UtStorageUndoPurger, HistoryListOrderTestPurgerBlock1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 新建一个用于验证undo log先入队再阻塞purger机制的事务
    SeRunCtxHdT blockSeRunCtx = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &blockSeRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    UtOptimisticTrxBegin(blockSeRunCtx);
    UtUpdateVarHeapData(blockSeRunCtx, heapShmAddr, tupleAddrList);
    // 打桩阻塞事务的提交
    int stubIdx = setStubC((void *)TrxClearOnCommitOrRollback, (void *)UtTrxClearOnCommitOrRollback);
    ASSERT_TRUE(stubIdx >= 0);
    pthread_t blockThread;
    pthread_create(&blockThread, NULL, UtBlockTrxCommit, &blockSeRunCtx);

    uint32_t tryTime = 0;  // 尝试50次
    while (UtStorageUndoPurger::gUtBlockTrxCommit == 0 && tryTime < 50) {
        tryTime++;
        DbSleep(100);
    }
    ASSERT_NE(tryTime, 50u);  // 如果5秒钟事务提交逻辑都还没到阻塞位置，就需要定位

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 1u);  // 虽然历史链表有两条undo log，但最新的一条还没有进入待清理状态

    // 清理undo log，避免影响其他用例
    int clearStubRet = clearStub(stubIdx);  // 清理打桩，避免purger的清理操作阻塞
    ASSERT_TRUE(clearStubRet >= 0);
    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    UtStorageUndoPurger::gUtBlockTrxCommit = 0;  // 不再阻塞
    pthread_join(blockThread, NULL);

    newList = {0};
    histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 2ull);  // 解除阻塞后，最新的undo log能够进入待清理状态，执行清理
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum + dataNum));

    SeClose(seRunCtx);
    SeClose(blockSeRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, HistoryListOrderTestPurgerBlock2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);  // 在这里先获取一次RsegHder中的历史链表
    ASSERT_EQ(histLen, 2u);

    // 获取到待清理链表后，往后添加两个事务
    vector<HpTupleAddr> tupleAddrList2;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList2);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);

    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 2u);  // 总共有四条，但是有两条在list复制时没有产生，这里应该只有两条待清理

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 2ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum * 2));

    newList = {0};
    histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 2u);  // 剩下的两条这次应该在清理链表上了

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 4ull);  // 清理掉剩下的两条
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum * 4));

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

static void PurgetListContinuityCheck(UndoPurgerRunCtxT *purgerCtx)
{
    HistListT purgeList = purgerCtx->clearList.list;
    uint64_t cursor = purgeList.headPtr;
    UndoPageHeaderT *undoPage = (UndoPageHeaderT *)purgeList.headPage;
    uint32_t len = 0;
    bool haveMetTailPtr = false;
    // 应该可以从headPtr一直遍历到tailPtr
    while (cursor != UNDO_INVALID_ROLLPTR) {
        len++;
        UndoRecPtrT *ptr = (UndoRecPtrT *)&cursor;
        UndoLogHeaderT *logHdr = UndoGetLogHdr(undoPage, ptr->offset);
        if (cursor == purgeList.tailPtr) {
            haveMetTailPtr = true;
        }
        cursor = logHdr->historyNode.next;
        undoPage = (UndoPageHeaderT *)logHdr->historyNode.nextPage;
    }
    ASSERT_EQ(purgerCtx->clearList.len, len);
    ASSERT_EQ(true, haveMetTailPtr);
}

TEST_F(UtStorageUndoPurger, PurgeListContinuityCheck1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);  // 在这里先获取一次RsegHder中的历史链表
    ASSERT_EQ(histLen, 2u);

    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, 0);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 0u);  // 最小活跃事务ID设置的很小，这里应该是什么都获取不到
    ASSERT_EQ(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.headPtr);
    ASSERT_EQ(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.tailPtr);

    // 获取到待清理链表后，往后添加两个事务
    vector<HpTupleAddr> tupleAddrList2;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList2);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);

    TrxIdT minActiveTrxId = DB_MAX_UINT32;
    newList = {0};
    histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    ASSERT_EQ(histLen, 4u);  // 上一次更新没有更新purgeList，所以这里应该有4条
    retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 4u);  // 四条都应该在清理链表上了

    PurgetListContinuityCheck(purgerCtx);

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 4ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum * 4));

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, PurgeListContinuityCheck2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);  // 在这里先获取一次RsegHder中的历史链表
    ASSERT_EQ(histLen, 2u);

    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 2u);  // 这里应该有两条待清理

    // 获取到待清理链表后，往后添加两个事务
    vector<HpTupleAddr> tupleAddrList2;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList2);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);

    newList = {0};
    histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    ASSERT_EQ(histLen, 2u);  // 前两条应该已经在PurgeList上了，所以这里应该只有两条
    retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 4u);  // 四条都应该在清理链表上了

    PurgetListContinuityCheck(purgerCtx);

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 4ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum * 4));

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, PurgeListContinuityCheck3)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);  // 在这里先获取一次RsegHder中的历史链表
    ASSERT_EQ(histLen, 2u);

    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 2u);  // 这里应该有两条待清理
    ASSERT_NE(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.headPtr);
    ASSERT_NE(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.tailPtr);
    ASSERT_NE(purgerCtx->clearList.list.headPtr, purgerCtx->clearList.list.tailPtr);  // 有两条，头尾应该不同

    PurgetListContinuityCheck(purgerCtx);

    PurgerReclaimInvisibleUndo(purgerCtx);  // 清理一次，此处应该清理完了
    EXPECT_GE(purgerCtx->gcLogCnt, 2ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum * 2));

    newList = {0};
    histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    ASSERT_EQ(histLen, 0u);                   // 前两条应该清理完毕，为空
    ASSERT_EQ(purgerCtx->clearList.len, 0u);  // 这里应该也是空的
    ASSERT_EQ(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.headPtr);
    ASSERT_EQ(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.tailPtr);

    // 获取到待清理链表后，往后添加两个事务
    vector<HpTupleAddr> tupleAddrList2;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList2);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);

    newList = {0};
    histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    ASSERT_EQ(histLen, 2u);  // 新加入两条
    retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_EQ(purgerCtx->clearList.len, 2u);
    ASSERT_NE(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.headPtr);
    ASSERT_NE(UNDO_INVALID_ROLLPTR, purgerCtx->clearList.list.tailPtr);
    ASSERT_NE(purgerCtx->clearList.list.headPtr, purgerCtx->clearList.list.tailPtr);  // 有两条，头尾应该不同

    PurgetListContinuityCheck(purgerCtx);

    PurgerReclaimInvisibleUndo(purgerCtx);  // 清理一次，此处应该清理完了
    EXPECT_GE(purgerCtx->gcLogCnt, 4ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)(dataNum * 4));

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

void UtTriggleUndoPurgerMain(UndoPurgerRunCtxT **purgerCtx, ShmemPtrT heapShmAddr, double splitTime)
{
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = splitTime,
        .instanceId = DbGetProcGlobalId(),
    };
    Status ret = PurgerCtxInit(purgerCtx, &cfg);
    EXPECT_EQ(GMERR_OK, ret);
    // 主逻辑，获取事务提交情况，清理undo log
    (void)UndoPurgerMain(*purgerCtx, DbRdtsc(), DB_INVALID_ID16);
}

TEST_F(UtStorageUndoPurger, UndoPurgerMain1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 清理前检查数据
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[0], string("update0"));

    // 初始化purger的上下文
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_LIMIT);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[5], string("update5"));

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

// 测试分片逻辑
TEST_F(UtStorageUndoPurger, UndoPurgerMain2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    // 单独申请上下文检查清理效果，维持较老事务
    SeRunCtxHdT seRunCtxForOldTrx = NULL;
    ret =
        SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtxForOldTrx);
    ASSERT_EQ(ret, GMERR_OK);

    // 清理前获取结果
    UtOptimisticTrxBegin(seRunCtxForOldTrx, true);
    UtVerifyHeapFetch(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4], string("insert4"));

    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_LIMIT);

    EXPECT_GE(purgerCtx->gcLogCnt, 0ull);  // 因为有事务活跃所以清理不到
    EXPECT_GE(purgerCtx->gcRecCnt, 0ull);  // 因为有事务活跃所以清理不到

    // 尝试清理不成功，还能读到老版本
    UtVerifyHeapFetch(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4], string("insert4"));
    UtTrxCommit(seRunCtxForOldTrx);
    // 提交后再清理一次，成功
    (void)UndoPurgerMain(purgerCtx, DbRdtsc(), DB_INVALID_ID16);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);               // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);  // 更新10行，总共10条undo rec

    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[5], string("update5"));
    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

// 测试分片逻辑
TEST_F(UtStorageUndoPurger, UndoPurgerMainSpilt1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 清理前检查数据
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[0], string("update0"));

    // 初始化purger的上下文
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_TIME);

    // 上面触发回收已经清理了一条，从1开始
    for (int i = 1; i <= dataNum; i++) {
        // 提交后再清理一次，成功清理一条record
        EXPECT_EQ(purgerCtx->gcRecCnt, (uint64_t)i);  // 每次多一条
        (void)UndoPurgerMain(purgerCtx, DbRdtsc(), DB_INVALID_ID16);
    }
    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);  // 只有1个retain undo log，清理了1个undo log

    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[5], string("update5"));

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, UndoPurgerMainSpilt2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    // 单独申请上下文检查清理效果，维持较老事务
    SeRunCtxHdT seRunCtxForOldTrx = NULL;
    ret =
        SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtxForOldTrx);
    ASSERT_EQ(ret, GMERR_OK);

    // 清理前获取结果
    UtOptimisticTrxBegin(seRunCtxForOldTrx, true);
    UtVerifyHeapFetch(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4], string("insert4"));

    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_TIME);

    EXPECT_GE(purgerCtx->gcLogCnt, 0ull);  // 因为有事务活跃所以清理不到
    EXPECT_GE(purgerCtx->gcRecCnt, 0ull);  // 因为有事务活跃所以清理不到

    // 尝试清理不成功，还能读到老版本
    UtVerifyHeapFetch(seRunCtxForOldTrx, heapShmAddr, tupleAddrList[4], string("insert4"));
    UtTrxCommit(seRunCtxForOldTrx);

    // 直到现在提交才能回收，之前一条都没有
    for (int i = 1; i <= dataNum; i++) {
        // 提交后再清理一次，成功清理一条record
        (void)UndoPurgerMain(purgerCtx, DbRdtsc(), DB_INVALID_ID16);
        EXPECT_EQ(purgerCtx->gcRecCnt, (uint64_t)i);  // 每次多一条
    }
    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);  // 只有1个retain undo log，清理了1个undo log

    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[5], string("update5"));
    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, UndoPurgerMainTriggerPageRecycle1)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxBegin(seRunCtx);
    UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    TrxT *trx = (TrxT *)seRunCtx->trx;
    ASSERT_TRUE(trx->trx.base.retainedUndo != NULL);
    // 一条数据起码都40字节，64K页也不会找过
    int maxCount = SE_DEFAULT_PAGE_MAX_SIZE * DB_KIBI / sizeof(UndoModifyRecHeader);
    maxCount += maxCount;
    int undoRecCnt = 0;
    while (trx->trx.base.retainedUndo->usedPageCnt <= 1) {
        // 不断更新使retained undo占用两页
        UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
        trx->trx.base.savePointId++;
        undoRecCnt += tupleAddrList.size();
        ASSERT_TRUE(maxCount >= undoRecCnt);  // 用来防止用例死循环
    }
    int retainedUndoPageNum = trx->trx.base.retainedUndo->usedPageCnt;
    UtTrxCommit(seRunCtx);

    // 清理前，检查undo占用页数
    int undoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_LIMIT);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);  // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)undoRecCnt);
    int afterUndoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    EXPECT_EQ(undoPageNum - retainedUndoPageNum, afterUndoPageNum);  // 释放掉UndoLog的两页

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, UndoPurgerMainTriggerPageRecycle2)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxBegin(seRunCtx);
    UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    TrxT *trx = (TrxT *)seRunCtx->trx;
    ASSERT_TRUE(trx->trx.base.retainedUndo != NULL);
    // 一条数据起码都40字节，64K页也不会找过
    int maxCount = SE_DEFAULT_PAGE_MAX_SIZE * DB_KIBI / sizeof(UndoModifyRecHeader);
    maxCount += maxCount;
    int undoRecCnt = 0;
    while (trx->trx.base.retainedUndo->usedPageCnt <= 1) {
        // 不断更新使retained undo占用两页
        UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
        trx->trx.base.savePointId++;
        undoRecCnt += tupleAddrList.size();
        ASSERT_TRUE(maxCount >= undoRecCnt);  // 用来防止用例死循环
    }
    // 最后删除掉
    UtDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    int retainedUndoPageNum = trx->trx.base.retainedUndo->usedPageCnt;
    UtTrxCommit(seRunCtx);

    // 清理前，检查undo占用页数
    int undoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_LIMIT);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);  // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)undoRecCnt);
    int afterUndoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    EXPECT_EQ(undoPageNum - retainedUndoPageNum, afterUndoPageNum);  // 释放掉UndoLog的页

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

void UtPreparePurgeLogByHeapUpdate(
    SeRunCtxT *seRunCtx, ShmemPtrT heapShmAddr, vector<HpTupleAddr> &tupleAddrList, int *undoRecCnt)
{
    UtOptimisticTrxBegin(seRunCtx);
    UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
    TrxT *trx = (TrxT *)seRunCtx->trx;
    ASSERT_TRUE(trx->trx.base.retainedUndo != NULL);
    // 一条数据起码都40字节，64K页也不会找过
    int maxCount = SE_DEFAULT_PAGE_MAX_SIZE * DB_KIBI / sizeof(UndoModifyRecHeader);
    maxCount += maxCount;
    while (trx->trx.base.retainedUndo->usedPageCnt <= 1) {
        // 不断更新使retained undo占用两页
        UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);
        trx->trx.base.savePointId++;
        *undoRecCnt += tupleAddrList.size();
        ASSERT_TRUE(maxCount >= *undoRecCnt);  // 用来防止用例死循环
    }
    UtTrxCommit(seRunCtx);
}

TEST_F(UtStorageUndoPurger, UndoPurgerMainTriggerPageRecycle3)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;
    int purgeUndoLogNum = 10;
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);

    int undoPageNumBeforUpdate = undoCtx->undoSpace->meta.usedPageCnt;
    int undoRecCnt = 0;
    for (int i = 0; i < purgeUndoLogNum; i++) {
        int tmpUndorecCnt = 0;
        UtPreparePurgeLogByHeapUpdate(seRunCtx, heapShmAddr, tupleAddrList, &tmpUndorecCnt);
        undoRecCnt += tmpUndorecCnt;
    }
    int retainedUndoPageNum = undoCtx->undoSpace->meta.usedPageCnt - undoPageNumBeforUpdate;

    // 清理前，检查undo占用页数
    int undoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_LIMIT);

    EXPECT_GE(purgerCtx->gcLogCnt, (uint64_t)purgeUndoLogNum);  // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)undoRecCnt);
    int afterUndoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    EXPECT_EQ(undoPageNum - retainedUndoPageNum, afterUndoPageNum);  // 释放掉UndoLog的两页

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

TEST_F(UtStorageUndoPurger, UndoPurgerMainTriggerPageRecycle4)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 10;
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;

    // 准备heap，预置数据，插入10条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList, "long long insert");

    // 申请用于回滚的事务
    SeRunCtxHdT rollbackSeRunCtx = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &rollbackSeRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    UtOptimisticTrxBegin(rollbackSeRunCtx);
    UtUpdateVarHeapData(rollbackSeRunCtx, heapShmAddr, tupleAddrList);
    UtOptimisticTrxBegin(seRunCtx);
    UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList, "long long update");
    TrxT *rollbackTrx = (TrxT *)rollbackSeRunCtx->trx;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    int maxCount = SE_DEFAULT_PAGE_MAX_SIZE * DB_KIBI / sizeof(UndoModifyRecHeader);
    maxCount += maxCount;
    int undoRecCnt = 0;
    SeTrxSavepointNameT name = {0};
    char *spname = (char *)"abc";
    name.length = strlen(spname) + 1;
    strcpy_s(name.name, name.length, spname);
    while (rollbackTrx->trx.base.retainedUndo->usedPageCnt <= 2 || trx->trx.base.retainedUndo->usedPageCnt <= 2) {
        ret = SeTrxCreateSavepoint(rollbackSeRunCtx, &name);
        ASSERT_EQ(ret, GMERR_OK);
        ret = SeTrxCreateSavepoint(seRunCtx, &name);
        ASSERT_EQ(ret, GMERR_OK);
        UtUpdateVarHeapData(rollbackSeRunCtx, heapShmAddr, tupleAddrList);
        UtUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList, "long long update");
        undoRecCnt += tupleAddrList.size();
        ASSERT_TRUE(maxCount >= undoRecCnt);  // 用来防止用例死循环
    }

    EXPECT_EQ(rollbackTrx->trx.base.retainedUndo->usedPageCnt, trx->trx.base.retainedUndo->usedPageCnt);
    // 记录数相等，因为对于rollbackTrx和trx事务来说，每次更新申请的undo空间都能容纳
    // "long long insert"和"long long update"，所以占用的空间相同，条数相同
    EXPECT_EQ(rollbackTrx->trx.base.retainedUndo->recCnt, trx->trx.base.retainedUndo->recCnt);

    int retainedUndoPageNum = trx->trx.base.retainedUndo->usedPageCnt;
    UtTrxCommit(seRunCtx);

    // 回滚事务
    int stubIdx = setStubC((void *)HeapLabelFetchHpTupleInner, (void *)StubHeapFetchHpTuple);
    ASSERT_TRUE(stubIdx >= 0);
    stubIdx = setStubC((void *)HeapFetchHpTuple, (void *)StubHeapFetchHpTuple);
    ASSERT_TRUE(stubIdx >= 0);
    UtTrxRollback(rollbackSeRunCtx);
    int clearStubRet = clearStub(stubIdx);
    ASSERT_TRUE(clearStubRet >= 0);

    // 清理前，检查undo占用页数
    int undoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UtTriggleUndoPurgerMain(&purgerCtx, heapShmAddr, PURGER_SPILT_TIME_NO_LIMIT);

    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);  // 只有1个retain undo log，清理了1个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)undoRecCnt);
    int afterUndoPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    EXPECT_EQ(undoPageNum - retainedUndoPageNum, afterUndoPageNum);

    SeClose(seRunCtx);
    UtDropHeap(seRunCtx, heapShmAddr);
}

int g_commitStage = 1;

StatusInter StubOptimisticTrxCommit(TrxT *trx)
{
    StatusInter ret = STATUS_OK_INTER;
    if (g_commitStage == 1) {
        bool isTrxCommitSuccess = false;
        TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
        DbRWLatchW(&trxMgr->latch);
        if (trx->base.state == TRX_STATE_ACTIVE) {
            ret = OptimisticTrxCommitActive(trx, &isTrxCommitSuccess);
            if (ret != STATUS_OK_INTER && trx->base.isInteractive) {
                // 写写冲突，交互式事务需要用户手动回滚
                DbRWUnlatchW(&trxMgr->latch);
                return ret;
            }
        }
        TrxGetCommitTs(trx);
        DbRWUnlatchW(&trxMgr->latch);
        g_commitStage++;
        return STATUS_OK_INTER;
    }

    TrxClearOnCommitOrRollback(trx);
    // 释放namespace readview
    OptiTrxCloseLabelReadview(trx);
    TrxCloseSavepoint(trx);
    g_commitStage = 1;
    return ret;
}

TEST_F(UtStorageUndoPurger, TestPurgeAndCommitConcurrency)
{
    SeRunCtxHdT seRunCtx1 = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx1);
    ASSERT_EQ(ret, GMERR_OK);

    SeRunCtxHdT seRunCtx2 = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx2);
    ASSERT_EQ(ret, GMERR_OK);

    // 0. 准备heap，预置数据，插入1条
    ShmemPtrT heapAddr;
    UtCreateHeap(seRunCtx1, &heapAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxBegin(seRunCtx1);
    HpRunHdlT heapRunHdl1 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx1, heapAddr, &heapRunHdl1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = HeapLabelOpen(heapRunHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);

    string insertData = "insert buffer";
    HeapTupleBufT heapTupleBuf = {
        .bufSize = (uint32_t)insertData.size(),
        .buf = (uint8_t *)insertData.c_str(),
    };
    HpTupleAddr heapTupleAddr;
    ret = HeapLabelInsertHpTupleBuffer(heapRunHdl1, &heapTupleBuf, &heapTupleAddr);
    EXPECT_EQ(GMERR_OK, ret);
    tupleAddrList.push_back(heapTupleAddr);
    HeapLabelResetCtx(heapRunHdl1);
    UtTrxCommit(seRunCtx1);

    // 1. trx1 开启
    UtOptimisticTrxBegin(seRunCtx1);

    // 2. trx1 标记删除记录
    UtDeleteVarHeapDataWithoutBeginTrx(heapRunHdl1, tupleAddrList);

    // 3. trx1 提交至第一阶段
    int stubIdx = setStubC((void *)OptimisticTrxCommit, (void *)StubOptimisticTrxCommit);
    ASSERT_TRUE(stubIdx >= 0);
    g_commitStage = 1;
    HeapLabelResetCtx(heapRunHdl1);
    UtTrxCommit(seRunCtx1);

    // 4. trx2 开启
    UtOptimisticTrxBegin(seRunCtx2);
    HpRunHdlT heapRunHdl2 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx2, heapAddr, &heapRunHdl2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HeapLabelOpen(heapRunHdl2, HEAP_OPTYPE_NORMALREAD, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    EXPECT_EQ(GMERR_OK, ret);

    // 5. trx2 读取记录，此处trx1的标记删除修改应该可见，报错 GMERR_NO_DATA
    TupleBufT buf;
    TupleBufInit(&buf, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ret = HeapFetchHpTupleBuffer(heapRunHdl2, heapTupleAddr, &buf);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    // 6. trx1 提交至第二阶段
    UtTrxCommit(seRunCtx1);
    clearStub(stubIdx);

    // 7. trx1 事务提交成功后，启动purger清理数据
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    // 获取当前最小活跃事务，用于判断不可见版本
    TrxIdT minActiveTrxId = TrxMgrGetMinTrxId((TrxMgrT *)((TrxT *)seRunCtx2->trx)->trxMgr);
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    EXPECT_GT(purgerCtx->clearList.len, 0u);
    PurgerReclaimInvisibleUndo(purgerCtx);
    // 至少清理过1个undo log以及undo记录
    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);
    EXPECT_GE(purgerCtx->gcRecCnt, 1ull);

    // 8. trx2 回滚
    HeapLabelResetCtx(heapRunHdl2);
    UtTrxRollback(seRunCtx2);

    UtDropHeap(seRunCtx1, heapAddr);
    SeClose(seRunCtx1);
    SeClose(seRunCtx2);
    clearAllStub();
}

TEST_F(UtStorageUndoPurger, TestPurgeAndCommitConcurrency2)
{
    SeRunCtxHdT seRunCtx1 = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx1);
    ASSERT_EQ(ret, GMERR_OK);

    SeRunCtxHdT seRunCtx2 = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx2);
    ASSERT_EQ(ret, GMERR_OK);

    // 0. 准备heap，预置数据，插入1条
    ShmemPtrT heapAddr;
    UtCreateHeap(seRunCtx1, &heapAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxBegin(seRunCtx1);
    HpRunHdlT heapRunHdl1 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx1, heapAddr, &heapRunHdl1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = HeapLabelOpen(heapRunHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);

    string insertData = "insert buffer";
    HeapTupleBufT heapTupleBuf = {
        .bufSize = (uint32_t)insertData.size(),
        .buf = (uint8_t *)insertData.c_str(),
    };
    HpTupleAddr heapTupleAddr;
    ret = HeapLabelInsertHpTupleBuffer(heapRunHdl1, &heapTupleBuf, &heapTupleAddr);
    EXPECT_EQ(GMERR_OK, ret);
    tupleAddrList.push_back(heapTupleAddr);
    HeapLabelResetCtx(heapRunHdl1);
    UtTrxCommit(seRunCtx1);

    // 1. trx1 开启
    UtOptimisticTrxBegin(seRunCtx1);

    // 2. trx1 标记删除记录
    string updateStr = "update_str_xx";
    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl1, tupleAddrList, updateStr);

    // 3. trx1 提交至第一阶段
    int stubIdx = setStubC((void *)OptimisticTrxCommit, (void *)StubOptimisticTrxCommit);
    ASSERT_TRUE(stubIdx >= 0);
    g_commitStage = 1;
    HeapLabelResetCtx(heapRunHdl1);
    UtTrxCommit(seRunCtx1);

    // 4. trx2 开启
    UtOptimisticTrxBegin(seRunCtx2);
    HpRunHdlT heapRunHdl2 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx2, heapAddr, &heapRunHdl2);
    EXPECT_EQ(GMERR_OK, ret);
    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl2, tupleAddrList, updateStr);

    // 5. trx2 提交
    g_commitStage = 1;
    UtTrxCommit(seRunCtx2);
    UtTrxCommit(seRunCtx2);

    // 6. trx1 提交至第二阶段
    g_commitStage = 2;
    UtTrxCommit(seRunCtx1);
    clearStub(stubIdx);

    // 6. trx1、trx2事务都提交成功，启动purger清理数据
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    // 获取当前最小活跃事务，用于判断不可见版本
    TrxIdT minActiveTrxId = TrxMgrGetMinTrxId((TrxMgrT *)((TrxT *)seRunCtx2->trx)->trxMgr);
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    EXPECT_GT(purgerCtx->clearList.len, 0u);
    PurgerReclaimInvisibleUndo(purgerCtx);
    // 至少清理过1个undo log以及undo记录
    EXPECT_GE(purgerCtx->gcLogCnt, 2ull);
    EXPECT_GE(purgerCtx->gcRecCnt, 2ull);

    UtDropHeap(seRunCtx1, heapAddr);
    SeClose(seRunCtx1);
    SeClose(seRunCtx2);
    clearAllStub();
}

/*
测试版本链的维护是否正确：
出问题的情况是unlink时不会去刷新undo跳转行上的rollPtr，导致再次unlink时会将过期undo地址刷新到主版本
用例步骤：
trx0预置一条记录，比如长度为80
trx1、trx2、依次更新长度为90,70
trx1提交，此时purge还不能回收（因为trx2还要看到老版本）
trx3开始事务，更新长度为任意值
trx2回滚，将trx2的undoLog的内容（trx1的修改）刷新到trx3的undoLog中（覆盖掉trx2的修改）
因为trx1的修改长度更长，导致trx3的undo出现跳转行
然后purge开始处理，从版本链上回收trx0的版本（出问题的点，未刷新trx3的undo跳转行）
再接着回收trx1的版本（错误累计，将过期地址刷新到masterVersion上）
*/
TEST_F(UtStorageUndoPurger, purgeUnlinkRollPtrCheck)
{
    SeRunCtxHdT seRunCtx1 = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx1);
    ASSERT_EQ(ret, GMERR_OK);

    SeRunCtxHdT seRunCtx2 = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx2);
    ASSERT_EQ(ret, GMERR_OK);

    SeRunCtxHdT seRunCtx3 = NULL;
    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx3);
    ASSERT_EQ(ret, GMERR_OK);

    int dataNum = 1;
    // 准备heap，预置数据，插入1条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx1, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxBegin(seRunCtx1);
    HpRunHdlT heapRunHdl1 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx1, heapShmAddr, &heapRunHdl1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = HeapLabelOpen(heapRunHdl1, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        string data = "insert" + std::to_string(i);
        HeapTupleBufT heapTupleBuf = {
            .bufSize = (uint32_t)data.size(),
            .buf = (uint8_t *)data.c_str(),
        };
        HpTupleAddr heapTupleAddr;
        ret = HeapLabelInsertHpTupleBuffer(heapRunHdl1, &heapTupleBuf, &heapTupleAddr);
        ASSERT_EQ(GMERR_OK, ret);
        tupleAddrList.push_back(heapTupleAddr);
    }
    HeapLabelResetCtx(heapRunHdl1);
    UtTrxCommit(seRunCtx1);

    // 开启trx1、trx2进行更新
    UtOptimisticTrxBegin(seRunCtx1);
    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl1, tupleAddrList, "update----------longbufSize");

    UtOptimisticTrxBegin(seRunCtx2);
    HpRunHdlT heapRunHdl2 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx2, heapShmAddr, &heapRunHdl2);
    ASSERT_EQ(GMERR_OK, ret);
    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl2, tupleAddrList, "update--shortfSize");

    // trx1提交
    HeapLabelResetCtx(heapRunHdl1);
    UtTrxCommit(seRunCtx1);

    // trx3开启事务
    UtOptimisticTrxBegin(seRunCtx3);
    HpRunHdlT heapRunHdl3 = nullptr;
    ret = UtGetHeapRunCtx(seRunCtx3, heapShmAddr, &heapRunHdl3);
    ASSERT_EQ(GMERR_OK, ret);
    UtUpdateVarHeapDataWithoutBeginTrx(heapRunHdl3, tupleAddrList);

    int stubIdx = setStubC((void *)HeapLabelDeSerialHpTuple, (void *)StubHeapLabelDeSerialHpTuple);
    ASSERT_TRUE(stubIdx >= 0);

    // trx2回滚,构造undo的跳转行
    UtTrxRollback(seRunCtx2);

    // trx3提交
    HeapLabelResetCtx(heapRunHdl3);
    UtTrxCommit(seRunCtx3);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_GT(purgerCtx->clearList.len, 0u);
    PurgerReclaimInvisibleUndo(purgerCtx);

    EXPECT_GE(purgerCtx->gcLogCnt, 2ull);                   // 有2个retain undo log，清理了2个undo log
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum * 2);  // 更新了2次，总共2条undo rec

    UtDropHeap(seRunCtx1, heapShmAddr);
    SeClose(seRunCtx1);
    SeClose(seRunCtx2);
    SeClose(seRunCtx3);
    // 清理undo log，避免影响其他用例
    int clearStubRet = clearStub(stubIdx);  // 清理打桩，避免purger的清理操作阻塞
    ASSERT_TRUE(clearStubRet >= 0);
}

// 看护undo purge线程有内存不足，使用逃生内存的场景，逃生内存最新方案已经下沉到common层，用例需要重新设计
TEST_F(UtStorageUndoPurger, PurgerReclaimInvisibleUndoHeapUpdateWithMemAllocErr)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 1;

    // 准备heap，预置数据，插入1条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 清理前检查数据
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[0], string("update0"));

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_GT(purgerCtx->clearList.len, 0u);

    // 打桩内存申请
    g_stubDbDynMemCtxAllocCallCount = 0;
    int stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocCount);
    ASSERT_TRUE(stubIdx >= 0);

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);

    // 清理后检查数据不变
    UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[0], string("update0"));
    clearStub(stubIdx);

    for (int i = 0; i < g_stubDbDynMemCtxAllocCallCount; i++) {
        g_stubDbDynMemCtxAllocCallWithErr = i;
        g_stubDbDynMemCtxAllocCallIndex = 0;

        vector<HpTupleAddr> tupleAddrList2;
        UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList2);
        UtOptimisticTrxUpdateVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);

        newList = {0};
        histLen = 0;
        UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
        TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
        StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
        EXPECT_EQ(retInter, STATUS_OK_INTER);
        ASSERT_GT(purgerCtx->clearList.len, 0u);

        // 打桩内存申请模拟不同位置的内存申请报错
        stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocWithErr);
        ASSERT_TRUE(stubIdx >= 0);

        PurgerReclaimInvisibleUndo(purgerCtx);
        EXPECT_GE(purgerCtx->gcLogCnt, 1ull);
        EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);
        clearStub(stubIdx);

        // 清理后检查数据不变
        UtOptimisticTrxVerifyHeapFetch(seRunCtx, heapShmAddr, tupleAddrList[0], string("update0"));
    }

    UtDropHeap(seRunCtx, heapShmAddr);
    SeClose(seRunCtx);
}

TEST_F(UtStorageUndoPurger, PurgerReclaimInvisibleUndoHeapDeleteWithMemAllocErr)
{
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)UtStorageUndoPurger::gTopDynamicMemCtx, &seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    int dataNum = 1;

    // 准备heap，预置数据，插入1条
    ShmemPtrT heapShmAddr;
    UtCreateHeap(seRunCtx, &heapShmAddr, false);
    vector<HpTupleAddr> tupleAddrList;
    UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList);
    UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList);

    // 清理前检查数据，执行过删除，新事务找不到记录
    UtOptimisticTrxHeapFetchExpectNoData(seRunCtx, heapShmAddr, tupleAddrList[0]);

    // 初始化purger的上下文
    UtStorageUndoPurger::gUtPurgerReclaimHeapShmAddr = heapShmAddr;  // 提供给StubPurgerGetHeapRunCtx获取共享内存用
    UndoPurgerRunCtxT *purgerCtx = NULL;
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = PURGER_SPILT_TIME_NO_LIMIT,
        .instanceId = DbGetProcGlobalId(),
    };
    PurgerCtxInit(&purgerCtx, &cfg);
    PurgeHistListT newList = {0};
    uint32_t histLen = 0;
    UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
    TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
    StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
    EXPECT_EQ(retInter, STATUS_OK_INTER);
    ASSERT_GT(purgerCtx->clearList.len, 0u);

    // 打桩内存申请
    g_stubDbDynMemCtxAllocCallCount = 0;
    int stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocCount);
    ASSERT_TRUE(stubIdx >= 0);

    PurgerReclaimInvisibleUndo(purgerCtx);
    EXPECT_GE(purgerCtx->gcLogCnt, 1ull);
    EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);

    UtOptimisticTrxHeapFetchExpectNoData(seRunCtx, heapShmAddr, tupleAddrList[0]);
    clearStub(stubIdx);
    for (int i = 0; i < g_stubDbDynMemCtxAllocCallCount; i++) {
        g_stubDbDynMemCtxAllocCallWithErr = i;
        g_stubDbDynMemCtxAllocCallIndex = 0;

        vector<HpTupleAddr> tupleAddrList2;
        UtOptimisticTrxInsertVarHeapData(seRunCtx, heapShmAddr, dataNum, &tupleAddrList2);
        UtOptimisticTrxDeleteVarHeapData(seRunCtx, heapShmAddr, tupleAddrList2);

        PurgerCtxInit(&purgerCtx, &cfg);
        newList = {0};
        histLen = 0;
        UndoPurgerInitHistList(purgerCtx, &newList, &histLen);
        TrxIdT minActiveTrxId = DB_MAX_UINT32;  // 设置得特别大，获取全部undo log
        StatusInter retInter = PurgerUpdatePurgeList(purgerCtx, &newList, minActiveTrxId);
        EXPECT_EQ(retInter, STATUS_OK_INTER);
        ASSERT_GT(purgerCtx->clearList.len, 0u);

        // 打桩内存申请模拟不同位置的内存申请报错
        stubIdx = setStubC((void *)DbDynMemCtxAlloc, (void *)StubDbDynMemCtxAllocWithErr);
        ASSERT_TRUE(stubIdx >= 0);

        PurgerReclaimInvisibleUndo(purgerCtx);
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
        // TrxBegin时申请daf链表跳过后续检查
        if (i == 0) {
            clearStub(stubIdx);
            continue;
        }
#endif
        EXPECT_GE(purgerCtx->gcLogCnt, 1ull);
        EXPECT_GE(purgerCtx->gcRecCnt, (uint64_t)dataNum);
        clearStub(stubIdx);

        UtOptimisticTrxHeapFetchExpectNoData(seRunCtx, heapShmAddr, tupleAddrList2[0]);
    }

    UtDropHeap(seRunCtx, heapShmAddr);
    SeClose(seRunCtx);
}
