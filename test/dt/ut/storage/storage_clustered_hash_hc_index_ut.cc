/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: storage_clustered_hash_hc_index_ut.cc
 * Description: Implementation of hash cluster index for clustered hash label
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>an
 * Create: 2022/08/15
 */
#include "db_table_space.h"
#include "se_instance.h"
#include "gmc_errno.h"
#include "stub.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#include "common_init.h"
#include "se_index.h"
#include "se_page_mgr.h"
#include "dm_meta_index_label.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "se_heap_base.h"
#include "dm_data_index.h"
#include "se_clustered_hash_hc_index.h"
#include "se_clustered_hash_label_dml.h"
#include "se_clustered_hash.h"
#include "se_clustered_hash_label_ddl.h"
#include "se_hash_cluster_index.h"
#include "dm_data_prop.h"
#include "se_clustered_hash_access_dm.h"
#include "dm_meta_vertex_label.h"

static DbMemCtxT *g_seTopDynaCtxTop = NULL;
static DbMemCtxT *g_topShmMemCtx2 = NULL;
static SeRunCtxHdT g_seRunCtx = NULL;
static DmVertexLabelT g_stubVtxLabel = {0};
static MetaVertexLabelT g_stubMetaVL = {0};
static DmSchemaT g_stubSchema = {0};
static DmPropertySchemaT g_stubProperty = {0};
static DmVlIndexLabelT g_hcIndexLabel = {0};
static VertexLabelCommonInfoT g_stubCommonInfo = {0};
static ShmemPtrT g_indexShmAddr = DB_INVALID_SHMPTR;
static ShmemPtrT g_hcLabelShmAddr = DB_INVALID_SHMPTR;
static const uint32_t MAX_HC_INDEX_NUM = 1;
static const uint32_t MAX_RECORD_COUNT = 10000;
static const uint32_t MAX_BATCH_NUM = 30;
static DbLatchT g_hcRWLatch = {0};
static LabelRWLatchT g_labelRWLatch = {0};
static DmVertexT g_vertexStub = {0};
static uint32_t g_hashCount = 0;

typedef uint32_t (*ClusteredHashGet26BitHashCodeFunc)(IndexKeyT hashKey);
extern uint32_t g_hashFuncNum;
extern ClusteredHashGet26BitHashCodeFunc g_hashFuncArray[];

typedef struct TageTupleData {
    uint32_t pkValue;
    int32_t hcIndexVal;
    uint32_t f0;
} TupleDataT;

void SetTupleData(TupleDataT &data, uint32_t val)
{
    data.pkValue = val;
    data.hcIndexVal = val - 33;
    data.f0 = val + 5;
}

void CmpTupleData(const TupleDataT *data, uint32_t val)
{
    ASSERT_EQ(val, data->pkValue);
    ASSERT_EQ(((int32_t)val - 33), data->hcIndexVal);
    ASSERT_EQ(val + 5, data->f0);
}

int32_t CreateAndOpenStorageEngine(
    DbMemCtxT **topShmMemCtx, DbMemCtxT **topDynMemCtx, SeRunCtxHdT *seRunCtx, uint16_t deviceSize, uint16_t memSize)
{
    *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(*topShmMemCtx);

    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = SE_DEFAULT_PAGE_SIZE;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
    config.deadlockCheckPeriod = 4000;
    config.lockJumpQueuePeriod = 5000;
    config.lockWakeupPeriod = 1000;
    config.lockTimeOut = 10000;
    config.maxTrxNum = MAX_TRX_NUM;
    config.maxLockShareCnt = MAX_CONN_NUM / 2;

    SeInstanceHdT seIns = NULL;
    Status ret = SeCreateInstance(NULL, *topShmMemCtx, &config, (SeInstanceHdT *)&seIns);
    EXPECT_EQ(GMERR_OK, ret);

    DbMemCtxArgsT topDyncCtxCfg = {0};
    topDyncCtxCfg.ctxSize = sizeof(DbDynamicMemCtxT);
    topDyncCtxCfg.memType = DB_DYNAMIC_MEMORY;
    topDyncCtxCfg.init = DynamicAlgoInit;
    *topDynMemCtx =
        (DbMemCtxT *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "se dynamic", &topDyncCtxCfg);

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynaCtxTop, &g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    memset_s(&g_stubVtxLabel, sizeof(g_stubVtxLabel), 0, sizeof(g_stubVtxLabel));
    memset_s(&g_hcIndexLabel, sizeof(g_hcIndexLabel), 0, sizeof(g_hcIndexLabel));
    return ret;
}

int32_t GetCmpRet(const uint8_t *keyBuf, uint32_t length, IndexKeyT hashKey)
{
    if (length > hashKey.keyLen) {
        return 1;
    } else if (length < hashKey.keyLen) {
        return -1;
    } else {
        return memcmp(keyBuf, hashKey.keyData, length);
    }
}

Status HashClusterIdxKeyCmpFuncStub(
    Handle hcRunCtx, IndexKeyT idxKey, const HeapTupleBufT *heapTupleBuf, int32_t *cmpRet, bool *isMatch)
{
    *isMatch = false;
    const TupleDataT *buf = (const TupleDataT *)heapTupleBuf->buf;
    // 将数据拷贝到 ctx的heapTuple中
    Status ret = ChLabelCopyTupleBufToRunCtx((ChLabelRunHdlT)hcRunCtx, heapTupleBuf->buf, heapTupleBuf->bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    const uint8_t *keyBuf = (const uint8_t *)&buf->pkValue;
    uint32_t length = sizeof(buf->pkValue);
    *cmpRet = GetCmpRet(keyBuf, length, idxKey);
    *isMatch = (*cmpRet == 0);
    return GMERR_OK;
}

Status HcIndexIdxKeyCmpBufFunc(
    IndexCtxT *idxCtx, IndexKeyT idxKey, const HeapTupleBufT *hpBuf, int32_t *cmpRet, bool *isMatch)
{
    const TupleDataT *buf = (const TupleDataT *)hpBuf->buf;

    const uint8_t *keyBuf = (const uint8_t *)&buf->hcIndexVal;
    uint32_t length = sizeof(buf->hcIndexVal);
    *cmpRet = GetCmpRet(keyBuf, length, idxKey);
    *isMatch = (*cmpRet == 0);
    return GMERR_OK;
}

Status HcIndexIdxKeyFetchAndCmpBufFunc(
    IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    HeapTupleBufT hpBuf = {0, NULL};
    Status ret = ChLabelFetchTupleBuf((ChLabelRunHdlT)idxCtx->idxOpenCfg.chHandle, addr, &hpBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HcIndexIdxKeyCmpBufFunc(idxCtx, idxKey, &hpBuf, cmpRet, isMatch);
    ChLabelFreeTupleBuf((ChLabelRunHdlT)idxCtx->idxOpenCfg.chHandle, &hpBuf);
    return ret;
}

StatusInter ClusteredHashGetIndexKeyStub(
    const DmVertexT *vertex, uint32_t indexId, DmIndexKeyBufInfoT **indexKeyInfo, uint8_t *vertexBuf, IndexKeyT *idxKey)
{
    TupleDataT *tupleData = (TupleDataT *)vertexBuf;
    idxKey->keyData = (uint8_t *)&tupleData->hcIndexVal;
    idxKey->keyLen = sizeof(tupleData->hcIndexVal);
    return STATUS_OK_INTER;
}

Status DmPartialIndexFilterSatisfiedStub(
    const uint8_t *keyBuf, uint32_t keyLen, DmVlIndexLabelT *indexLabel, uint8_t *cmpRes)
{
    return GMERR_OK;
}

uint32_t DmGetFixVertexLabelLengthStub(DmVertexLabelT *vertexLabel)
{
    return sizeof(TupleDataT);
}

extern "C" StatusInter ClusteredHashConstructDir(
    ClusteredHashLabelT *label, ClusteredHashLabelVarInfoT *labelVarInfo, RsmUndoRecordT *rsmUndoRec);

extern Status UtCheckTupleBufIsMarkDelete(const HeapTupleBufT *heapTupleBuf, void *data);
extern Status UtGenerateSubs4ChLabelDeleteCb(void *stmt, void *cursor, const HeapTupleBufT *heapTupleBuf, bool isAged);

static StatusInter ClusteredHashTableLockStub(ChLabelRunCtxT *runCtx, bool isRead)
{
    return STATUS_OK_INTER;
}

static void ClusteredHashTableUnLockStub(ChLabelRunCtxT *runCtx, bool isRead)
{
    return;
}

void UtCreateBasicLabel(ShmemPtrT *hcIndexShmAddr, ShmemPtrT *hcLabelShmAddr)
{
    ChLabelCfgT cfgForHct = {
        .seInstanceId = GET_INSTANCE_ID,
        .initIndexCap = 0,
        .indexId = 0,
        .labelId = UT_LABEL_ID,
        .maxItemNum = MAX_RECORD_COUNT,
        .fixRowDataSize = sizeof(TupleDataT),
        .slotExtendSize = MAX_HC_INDEX_NUM * sizeof(ChIndexHcPtrT),
        .ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
    };
    ShmemPtrT containerInfo = DB_INVALID_SHMPTR;
    ASSERT_EQ(STATUS_OK_INTER, ChLabelCreate(&cfgForHct, hcLabelShmAddr, &containerInfo));
    ClusteredHashLabelT *label = (ClusteredHashLabelT *)DbShmPtrToAddr(*hcLabelShmAddr);
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(label);
    if (labelVarInfo->isConstructed == 0) {  // 创建目录页
        ASSERT_EQ(STATUS_OK_INTER, ClusteredHashConstructDir(label, labelVarInfo, NULL));
        ASSERT_EQ((uint32_t)1, labelVarInfo->isConstructed);
    }

    IndexMetaCfgT indexCfg = {
        .indexId = 1,
        .idxType = HASHCLUSTER_INDEX,
        .realIdxType = HASHCLUSTER_INDEX,
        .idxConstraint = NON_UNIQUE,
        .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
        .indexCap = 0,
        .isLabelLatchMode = false,
        .tableSpaceId = 0,
        .tableSpaceIndex = 0,
        .nullInfoBytes = 1,
        .isUseClusteredHash = true,
        .hasVarchar = false,
        .isHcGlobalLatch = false,
        .isMemFirst = false,
        .isVertexUseRsm = false,
        .hcLatch = NULL,
        .keyDataType = 0,
        .extendParam = NULL,
    };
    ASSERT_EQ(GMERR_OK, IdxCreate(g_seRunCtx, indexCfg, hcIndexShmAddr));
    g_stubVtxLabel.metaVertexLabel = &g_stubMetaVL;
    g_stubVtxLabel.metaVertexLabel->vertexLabelType = VERTEX_TYPE_NORMAL;
    g_stubVtxLabel.metaVertexLabel->labelLevel = VERTEX_LEVEL_SIMPLE;
    g_stubVtxLabel.metaVertexLabel->schema = &g_stubSchema;
    g_stubVtxLabel.metaVertexLabel->schema->properties = &g_stubProperty;
    g_stubVtxLabel.metaVertexLabel->schema->propeNum = 1;
    g_stubVtxLabel.metaCommon.metaId = 1;
    g_stubVtxLabel.commonInfo = &g_stubCommonInfo;
    g_stubVtxLabel.metaVertexLabel->secIndexNum = 0;
    DmVertexLabelT *copyVertexLabel = NULL;
    Status ret = CopyVertexLabel(g_topShmMemCtx2, &g_stubVtxLabel, &copyVertexLabel);
    EXPECT_EQ(ret, GMERR_OK);
    g_stubVtxLabel = *copyVertexLabel;
    g_stubVtxLabel.metaVertexLabel->secIndexNum = 1;
}

void UtDropBasicLabel(ShmemPtrT hcIndexShmAddr, ShmemPtrT hcLabelShmAddr)
{
    DestroyShmVertexLabel(g_topShmMemCtx2, &g_stubVtxLabel);
    ChLabelDrop(hcLabelShmAddr, NULL);
    IdxDrop(g_seRunCtx, HASHCLUSTER_INDEX, hcIndexShmAddr);
}

Status UtOpenLabel(ChLabelRunHdlT *chHandle, IndexCtxT **hcIndexCtx)
{
    g_labelRWLatch = (LabelRWLatchT){0};
    g_hcRWLatch = (DbLatchT){0};
    g_labelRWLatch.isAddHcLatch = true;
    g_labelRWLatch.hcLatch = g_hcRWLatch;
    DbRWLatchInit(&g_hcRWLatch);
    HcWLatchAcquire(&g_labelRWLatch);

    ChLabelOpenCfgT openCfg = {};
    openCfg.labelShmAddr = g_hcLabelShmAddr;
    openCfg.seRunCtx = g_seRunCtx;
    openCfg.usrMemCtx = g_seTopDynaCtxTop;
    openCfg.vertexLabel = &g_stubVtxLabel;
    openCfg.vertex = NULL;
    openCfg.userData = NULL;
    openCfg.indexKeyInfo = NULL;
    openCfg.secIndexNum = MAX_HC_INDEX_NUM;
    openCfg.secIdxCtx = hcIndexCtx;
    openCfg.keyCmp = HashClusterIdxKeyCmpFuncStub;
    openCfg.labelLatchVersionId = 0;
    openCfg.labelRWLatch = &g_labelRWLatch;

    EXPECT_EQ(GMERR_OK, ChLabelAllocAndInitRunCtx(&openCfg, chHandle));

    EXPECT_EQ(GMERR_OK, IdxAlloc(g_seRunCtx, HASHCLUSTER_INDEX, hcIndexCtx));
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = g_seRunCtx,
        .vertex = NULL,
        .heapHandle = NULL,
        .chHandle = *chHandle,
    };
    indexOpenCfg.callbackFunc = {NULL};
    indexOpenCfg.userData = NULL;
    indexOpenCfg.keyInfo = NULL;
    indexOpenCfg.indexLabel = (DmIndexLabelBaseT *)&g_hcIndexLabel;
    indexOpenCfg.callbackFunc.hpBufCmp = HcIndexIdxKeyCmpBufFunc;
    indexOpenCfg.callbackFunc.keyCmp = HcIndexIdxKeyFetchAndCmpBufFunc;
    indexOpenCfg.indexType = HASHCLUSTER_INDEX;
    EXPECT_EQ(GMERR_OK, IdxOpen(g_indexShmAddr, &indexOpenCfg, *hcIndexCtx));
    return GMERR_OK;
}

void UtCloseLabel(ChLabelRunHdlT chHandle, IndexCtxT *hcIndexCtx)
{
    HcWLatchRelease(&g_labelRWLatch);
    ChLabelReleaseRunCtx(chHandle);
    IdxClose(hcIndexCtx);
    IdxRelease(hcIndexCtx);
}

void UtAutoBeginTransaction(ChLabelRunHdlT chRunHdl, HpOpTypeE opType, SeRunCtxHdT seRunCtx)
{
    TrxCfgT cfg = {0};
    cfg.isLiteTrx = true;
    cfg.connId = 8888;
    cfg.trxType = DEFAULT_TRX;
    cfg.isolationLevel = READ_UNCOMMITTED;
    ASSERT_EQ(GMERR_OK, SeTransBegin(seRunCtx, &cfg));
    // 打开事务性容器
    ASSERT_EQ(GMERR_OK, ChLabelOpen(chRunHdl, &g_vertexStub, opType, (DbMemCtxT *)seRunCtx->sessionMemCtx));
}

void UtAutoCommitOrRollBack(Status ret, SeRunCtxHdT seRunCtx)
{
    if (ret == GMERR_OK) {
        ASSERT_EQ(GMERR_OK, SeTransCommit(seRunCtx));
    } else {
        ASSERT_EQ(GMERR_OK, SeTransRollback(seRunCtx, false));
    }
}

class StorageClusteredHashHcIndex : public testing::Test {
protected:
    virtual void SetUp()
    {
        UtCreateBasicLabel(&g_indexShmAddr, &g_hcLabelShmAddr);
    }
    virtual void TearDown()
    {
        UtDropBasicLabel(g_indexShmAddr, g_hcLabelShmAddr);
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        EXPECT_EQ(GMERR_OK, CommonInit());
        EXPECT_EQ(GMERR_OK, CreateAndOpenStorageEngine(&g_topShmMemCtx2, &g_seTopDynaCtxTop, &g_seRunCtx, 4, 1024));
        setStubC((void *)DmPartialIndexFilterSatisfied, (void *)DmPartialIndexFilterSatisfiedStub);
        setStubC((void *)ClusteredHashGetIndexKey, (void *)ClusteredHashGetIndexKeyStub);
        setStubC((void *)ClusteredHashTableLock, (void *)ClusteredHashTableLockStub);
        setStubC((void *)ClusteredHashTableUnLock, (void *)ClusteredHashTableUnLockStub);
        setStubC((void *)DmGetFixVertexLabelLen, (void *)DmGetFixVertexLabelLengthStub);
        setStubC((void *)DmGetFixVertexLabelLen4QE, (void *)DmGetFixVertexLabelLengthStub);
    };

    static void TearDownTestCase()
    {
        clearAllStub();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

IndexKeyT UtGetPKIndexKeyFromTupleBuff(TupleDataT *tuple)
{
    IndexKeyT idxKey;
    idxKey.keyLen = sizeof(uint32_t);
    idxKey.keyData = (uint8_t *)&tuple->pkValue;
    return idxKey;
}

IndexKeyT UtGetHCIndexKeyFromTupleBuff(TupleDataT *tuple)
{
    IndexKeyT idxKey;
    idxKey.keyLen = sizeof(int32_t);
    idxKey.keyData = (uint8_t *)&tuple->hcIndexVal;
    return idxKey;
}

HeapTupleBufT UtGenerateTupleData(uint32_t pkValue)
{
    HeapTupleBufT heapTupleBuf = {0, NULL};
    TupleDataT *tuple = (TupleDataT *)malloc(sizeof(TupleDataT));
    (void)memset_s(tuple, sizeof(TupleDataT), 0, sizeof(TupleDataT));
    SetTupleData(*tuple, pkValue);
    heapTupleBuf.buf = (uint8_t *)tuple;
    heapTupleBuf.bufSize = sizeof(TupleDataT);
    return heapTupleBuf;
}

HeapTupleBufT UtGenerateSameHcKeyTupleData(uint32_t pkValue)
{
    HeapTupleBufT heapTupleBuf = {0, NULL};
    TupleDataT *tuple = (TupleDataT *)malloc(sizeof(TupleDataT));
    (void)memset_s(tuple, sizeof(TupleDataT), 0, sizeof(TupleDataT));
    tuple->pkValue = pkValue;
    tuple->hcIndexVal = 1;
    tuple->f0 = pkValue + 5;
    heapTupleBuf.buf = (uint8_t *)tuple;
    heapTupleBuf.bufSize = sizeof(TupleDataT);
    return heapTupleBuf;
}

void UtFreeTupleData(HeapTupleBufT *heapTupleBuf)
{
    if (heapTupleBuf->buf != NULL) {
        free(heapTupleBuf->buf);
        heapTupleBuf->buf = NULL;
        heapTupleBuf->bufSize = 0;
    }
}

void UtSetBatchDeleteUserData(ChLabelBatchDeleteOrReplaceParaT *bacthDelete, uint32_t batchNum, uint32_t bufSize)
{
    if (batchNum > 1024) {
        ASSERT_EQ(0, 1);
    }
    bacthDelete->retPara = new ChLabelRetParaT[batchNum];
    bacthDelete->oldBufArray = new HeapTupleBufT[batchNum];
    for (uint32_t i = 0; i < batchNum; ++i) {
        bacthDelete->retPara[i].isFind = false;
        bacthDelete->retPara[i].isAged = false;
        bacthDelete->retPara[i].isNeedUpdateViewNum = false;
        bacthDelete->retPara[i].deleteMark = 0;
        bacthDelete->retPara[i].tupleAddr = HEAP_INVALID_ADDR;
        bacthDelete->oldBufArray[i].bufSize = bufSize;
        bacthDelete->oldBufArray[i].buf = (uint8_t *)malloc(bufSize);
    }
    bacthDelete->markDeleteCheckFunc = UtCheckTupleBufIsMarkDelete;
    bacthDelete->checkBeforeUpdateFunc = NULL;
    bacthDelete->deletePreOper = (ChLabelDeletePreOperT){
        .func = UtGenerateSubs4ChLabelDeleteCb,
        .stmt = NULL,
        .labelCursor = NULL,
    };
    bacthDelete->existCount = 0;
}

void FreeBatchDeleteUserData(ChLabelBatchDeleteOrReplaceParaT *bacthDelete, uint32_t batchNum)
{
    delete[] bacthDelete->retPara;

    for (uint32_t i = 0; i < batchNum; ++i) {
        free(bacthDelete->oldBufArray[i].buf);
        bacthDelete->oldBufArray[i].buf = NULL;
    }
    delete[] bacthDelete->oldBufArray;
}

TEST_F(StorageClusteredHashHcIndex, TestHcIndexInsertAndDelete)
{
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);
    Status ret;
    HpTupleAddr tupleAddr;
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 插入主键和数据以及hashcluster索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
        ret = ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr);
        UtAutoCommitOrRollBack(ret, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ret);
        // 利用聚簇索引查询指定key的个数
        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }

    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 查找主键
        bool isFind = false;
        ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr, &isFind));
        EXPECT_EQ(true, isFind);

        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);

        // 删除聚簇索引
        IndexRemoveParaT removePara = {true, false};
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_DELETE, g_seRunCtx);
        ASSERT_EQ(GMERR_OK, IdxDelete(idxCtx, hcIndexKey, tupleAddr, removePara));
        ASSERT_EQ(GMERR_OK, ChLabelDelete(hctRunxCtx, tupleAddr));
        UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)0, count);

        UtFreeTupleData(&tupleBuf);
    }
    UtCloseLabel(hctRunxCtx, idxCtx);
}

TEST_F(StorageClusteredHashHcIndex, testChLabelBatchInsertAndDetete)
{
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);

    HeapTupleBufT tupleData[MAX_BATCH_NUM] = {0};
    IndexKeyT pkIndexKey[MAX_BATCH_NUM] = {0};
    IndexKeyT hcIndexKey[MAX_BATCH_NUM] = {0};
    HpBatchOutT batchOut[MAX_BATCH_NUM] = {0};
    // 测试批量插入
    uint32_t batchNum = 0;
    uint64_t itemNum = 0;
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        tupleData[batchNum] = UtGenerateTupleData(i);
        pkIndexKey[batchNum] = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleData[batchNum].buf);
        hcIndexKey[batchNum] = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleData[batchNum].buf);
        batchNum++;
        if ((i == MAX_RECORD_COUNT - 1) || (batchNum == MAX_BATCH_NUM)) {
            // 批量插入主键和数据以及hashcluster索引
            UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
            SeTransSetDmlHint4BatchNum(g_seRunCtx, batchNum);
            ASSERT_EQ(GMERR_OK, ChLabelBatchInsert(hctRunxCtx, pkIndexKey, tupleData, batchOut, batchNum));
            UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
            for (uint32_t j = 0; j < batchNum; ++j) {
                uint64_t count = 0;
                ASSERT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey[j], &count));
                ASSERT_EQ((uint64_t)1, count);
                UtFreeTupleData(&tupleData[j]);
            }
            (void)memset_s(tupleData, sizeof(tupleData), 0, sizeof(tupleData));
            (void)memset_s(pkIndexKey, sizeof(pkIndexKey), 0, sizeof(pkIndexKey));
            (void)memset_s(hcIndexKey, sizeof(hcIndexKey), 0, sizeof(hcIndexKey));
            (void)memset_s(batchOut, sizeof(batchOut), 0, sizeof(batchOut));
            batchNum = 0;
        }
    }
    // 测试批量删除
    batchNum = 0;
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        tupleData[batchNum] = UtGenerateTupleData(i);
        pkIndexKey[batchNum] = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleData[batchNum].buf);
        hcIndexKey[batchNum] = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleData[batchNum].buf);

        // 查找主键
        bool isFind = false;
        ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey[batchNum], &batchOut[batchNum].addrOut, &isFind));
        ASSERT_EQ(true, isFind);
        batchNum++;
        if ((i == MAX_RECORD_COUNT - 1) || (batchNum == MAX_BATCH_NUM)) {
            // 批量删除主键和数据以及hashcluster索引
            UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_DELETE, g_seRunCtx);
            ChLabelBatchDeleteOrReplaceParaT bacthDelete = {0};
            UtSetBatchDeleteUserData(&bacthDelete, batchNum, sizeof(TupleDataT));
            ASSERT_EQ(GMERR_OK, ChLabelBatchDeleteByKey(hctRunxCtx, pkIndexKey, &bacthDelete, batchNum));
            UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
            for (uint32_t j = 0; j < batchNum; ++j) {
                // 校验hashclusetr索引是否已经删除
                uint64_t count = 0;
                ASSERT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey[j], &count));
                ASSERT_EQ((uint64_t)0, count);

                // 校验删除的地址是否和插入地址一致
                ASSERT_EQ(batchOut[j].addrOut, bacthDelete.retPara[j].tupleAddr);
                ASSERT_EQ(true, bacthDelete.retPara[j].isFind);

                UtFreeTupleData(&tupleData[j]);
            }
            FreeBatchDeleteUserData(&bacthDelete, batchNum);
            (void)memset_s(tupleData, sizeof(tupleData), 0, sizeof(tupleData));
            (void)memset_s(pkIndexKey, sizeof(pkIndexKey), 0, sizeof(pkIndexKey));
            (void)memset_s(hcIndexKey, sizeof(hcIndexKey), 0, sizeof(hcIndexKey));
            (void)memset_s(batchOut, sizeof(batchOut), 0, sizeof(batchOut));
            batchNum = 0;
        }
    }
    ASSERT_EQ(GMERR_OK, ChLabelGetPhyItemNum(hctRunxCtx, &itemNum));
    EXPECT_EQ((uint64_t)0, itemNum);
    UtCloseLabel(hctRunxCtx, idxCtx);
}

TEST_F(StorageClusteredHashHcIndex, TestHcIndexInsertSameKey)
{
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);

    HpTupleAddr tupleAddr;
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateSameHcKeyTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 插入主键和数据以及聚簇二级索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr));
        UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
        UtFreeTupleData(&tupleBuf);
    }

    HeapTupleBufT tupleBuf = UtGenerateSameHcKeyTupleData(0);
    IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    // 利用聚簇索引查询指定key的个数
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)MAX_RECORD_COUNT, count);
    UtFreeTupleData(&tupleBuf);

    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateSameHcKeyTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 查找主键
        bool isFind = false;
        ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr, &isFind));
        EXPECT_EQ(true, isFind);

        // 删除聚簇索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_DELETE, g_seRunCtx);
        IndexRemoveParaT removePara = {true, false};
        ASSERT_EQ(GMERR_OK, IdxDelete(idxCtx, hcIndexKey, tupleAddr, removePara));
        ASSERT_EQ(GMERR_OK, ChLabelDelete(hctRunxCtx, tupleAddr));
        UtFreeTupleData(&tupleBuf);
        UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
    }

    tupleBuf = UtGenerateSameHcKeyTupleData(0);
    hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    // 利用聚簇索引查询指定key的个数
    count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)0, count);
    UtFreeTupleData(&tupleBuf);
    UtCloseLabel(hctRunxCtx, idxCtx);
}

TEST_F(StorageClusteredHashHcIndex, TestHcIndexInsertAndDeleteWithDuplicateHashCode)
{
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    uint32_t dataNum = 10000;
    uint32_t hashFuncNum = g_hashFuncNum;
    for (uint32_t i = 0; i < hashFuncNum; ++i) {
        // 打桩计算hashCode函数
        int stubC = setStubC((void *)ClusteredHashGet26BitHashCode, (void *)g_hashFuncArray[i]);
        DB_ASSERT(stubC > 0);
        UtOpenLabel(&hctRunxCtx, &idxCtx);

        HpTupleAddr tupleAddr;
        for (uint32_t j = 0; j < dataNum; ++j) {
            HeapTupleBufT tupleBuf = UtGenerateTupleData(j);
            IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
            IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
            // 插入主键和数据以及hashcluster索引
            UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
            EXPECT_EQ(GMERR_OK, ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr));
            UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
            // 利用聚簇索引查询指定key的个数
            uint64_t count = 0;
            EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
            EXPECT_EQ((uint64_t)1, count);
            UtFreeTupleData(&tupleBuf);
        }

        for (uint32_t k = 0; k < dataNum; ++k) {
            HeapTupleBufT tupleBuf = UtGenerateTupleData(k);
            IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
            IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
            // 查找主键
            bool isFind = false;
            ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr, &isFind));
            EXPECT_EQ(true, isFind);

            uint64_t count = 0;
            EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
            EXPECT_EQ((uint64_t)1, count);

            // 删除聚簇索引
            UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
            IndexRemoveParaT removePara = {true, false};
            ASSERT_EQ(GMERR_OK, IdxDelete(idxCtx, hcIndexKey, tupleAddr, removePara));
            ASSERT_EQ(GMERR_OK, ChLabelDelete(hctRunxCtx, tupleAddr));
            UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
            EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
            EXPECT_EQ((uint64_t)0, count);

            UtFreeTupleData(&tupleBuf);
        }
        UtCloseLabel(hctRunxCtx, idxCtx);
        HcIndexTruncate(NULL, g_indexShmAddr);
        ChLabelTruncate(g_hcLabelShmAddr, NULL);
        clearStub(stubC);
    }
}

uint32_t HcDbHash32ChHcStub(const uint8_t *key, uint32_t len)
{
    if (*key == 10 || *key == 233) {
        g_hashCount++;
        if (g_hashCount == 6 || g_hashCount == 7) {
            // 模拟更新失败回滚时idxKey不一致的情况
            return 1;
        } else {
            return 0;
        }
    }
    if (*key > 10) {
        return (*key + 1) % 2;
    } else {
        return *key % 2;
    }
}

// 测试 hc undo 场景，链表中只有一条数据
TEST_F(StorageClusteredHashHcIndex, storage_clustered_hash_hc_index_undo_insert_001)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HcDbHash32ChHcStub);
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);

    g_hashCount = 0;
    // 插入数据
    Status ret;
    HpTupleAddr tupleAddr[2];
    for (uint32_t i = 9; i <= 10; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 插入主键和数据以及hashcluster索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
        ret = ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr[i - 9]);
        UtAutoCommitOrRollBack(ret, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ret);
        // 利用聚簇索引查询指定key的个数
        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }

    for (uint32_t i = 9; i <= 10; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 查找主键
        bool isFind = false;
        ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr[i - 9], &isFind));
        EXPECT_EQ(true, isFind);
        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }

    // 删除聚簇索引
    HeapTupleBufT tupleBuf = UtGenerateTupleData(10);
    IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    IndexRemoveParaT removePara = {true, false};
    UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_DELETE, g_seRunCtx);
    ASSERT_EQ(GMERR_OK, IdxDelete(idxCtx, hcIndexKey, tupleAddr[1], removePara));
    ASSERT_EQ(GMERR_OK, ChLabelDelete(hctRunxCtx, tupleAddr[1]));
    UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)0, count);
    UtFreeTupleData(&tupleBuf);

    // 查找主键
    tupleBuf = UtGenerateTupleData(9);
    IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    bool isFind = false;
    ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr[0], &isFind));
    EXPECT_EQ(true, isFind);
    count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)1, count);
    UtFreeTupleData(&tupleBuf);

    // 检验数据是否删除成功
    tupleBuf = UtGenerateTupleData(10);
    pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    isFind = false;
    ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr[1], &isFind));
    EXPECT_EQ(false, isFind);
    count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)0, count);
    UtFreeTupleData(&tupleBuf);

    UtCloseLabel(hctRunxCtx, idxCtx);
    clearStub(stubId);
}

// 测试 hc undo 场景，链表中有多条数据，删除链表头
TEST_F(StorageClusteredHashHcIndex, storage_clustered_hash_hc_index_undo_insert_002)
{
    int32_t stubId = setStubC((void *)DbHash32, (void *)HcDbHash32ChHcStub);
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);

    g_hashCount = 0;
    // 插入数据
    Status ret;
    HpTupleAddr tupleAddr[10];
    for (uint32_t i = 1; i <= 10; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 插入主键和数据以及hashcluster索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
        ret = ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr[i - 1]);
        UtAutoCommitOrRollBack(ret, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ret);
        // 利用聚簇索引查询指定key的个数
        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }
    for (uint32_t i = 1; i <= 10; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 查找主键
        bool isFind = false;
        ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr[i - 1], &isFind));
        EXPECT_EQ(true, isFind);

        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }

    // 删除聚簇索引
    HeapTupleBufT tupleBuf = UtGenerateTupleData(10);
    IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    IndexRemoveParaT removePara = {true, false};
    UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_DELETE, g_seRunCtx);
    ASSERT_EQ(GMERR_OK, IdxDelete(idxCtx, hcIndexKey, tupleAddr[9], removePara));
    ASSERT_EQ(GMERR_OK, ChLabelDelete(hctRunxCtx, tupleAddr[9]));
    UtAutoCommitOrRollBack(GMERR_OK, g_seRunCtx);
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)0, count);
    UtFreeTupleData(&tupleBuf);

    for (uint32_t i = 1; i < 10; ++i) {
        tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 查找主键
        bool isFind = false;
        ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr[i - 1], &isFind));
        EXPECT_EQ(true, isFind);
        count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }

    // 检验数据是否删除成功
    tupleBuf = UtGenerateTupleData(10);
    IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
    bool isFind = false;
    ASSERT_EQ(GMERR_OK, ChLabelLookUp(hctRunxCtx, pkIndexKey, &tupleAddr[9], &isFind));
    EXPECT_EQ(false, isFind);
    count = 0;
    EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
    EXPECT_EQ((uint64_t)0, count);
    UtFreeTupleData(&tupleBuf);

    UtCloseLabel(hctRunxCtx, idxCtx);
    clearStub(stubId);
}

void UtHcIndexInsertOneWithPrefetch(IndexCtxT *idxCtx, HpTupleAddr addr, IndexKeyT hashKey)
{
    HcInitHcPrefetchCtx(&idxCtx->hcPrefetchCtx);
    HcInitHcDeletePrefetchCtx(&idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx);

    Status ret = HcIndexPrefetch4Insert(idxCtx, hashKey, addr);
    EXPECT_EQ(GMERR_OK, ret);
    HcIndexInsertProcWithPrefetch(idxCtx, hashKey, addr, &idxCtx->hcPrefetchCtx.hcInsertPrefetchCtx);
}

/* 插入一个tuple，hashKey由addr构造得到 */
void UtHcIndexDeleteOneWithPrefetch(IndexCtxT *idxCtx, HpTupleAddr addr, IndexRemoveParaT removePara, IndexKeyT hashKey)
{
    HcInitHcPrefetchCtx(&idxCtx->hcPrefetchCtx);
    HcInitHcDeletePrefetchCtx(&idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx);

    Status ret = HcIndexPrefetch4Delete(idxCtx, hashKey, addr);
    EXPECT_EQ(GMERR_OK, ret);

    HcIndexDeleteProcWithPrefetch(idxCtx, hashKey, addr);
}

static uint64_t g_keyData;
static const uint32_t KEY_DATA_LEN = 8;

static inline IndexKeyT GenerateIndexKey(HpTupleAddr addr)
{
    IndexKeyT hashKey = {.keyData = (uint8_t *)&g_keyData, .keyLen = KEY_DATA_LEN};
    errno_t res = memcpy_s(hashKey.keyData, KEY_DATA_LEN, &addr, KEY_DATA_LEN);
    EXPECT_EQ(GMERR_OK, res);
    return hashKey;
}

void UtHcIndexLookupOne(IndexCtxT *idxCtx, HpTupleAddr expectedAddr, bool expectedIsFound)
{
    IndexKeyT hashKey = GenerateIndexKey(expectedAddr);
    HpTupleAddr addrReturned = HEAP_INVALID_ADDR;
    bool isFound;
    IndexScanItrT iter;
    IndexScanCfgT scanCfg{};
    scanCfg.scanType = INDEX_RANGE_OPENRIGHT;
    scanCfg.scanDirect = INDEX_SCAN_ASCEND;
    scanCfg.leftKey = &hashKey;
    Status ret = HcIndexBeginScan(idxCtx, scanCfg, &iter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = HcIndexScan(idxCtx, iter, &addrReturned, &isFound);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expectedIsFound, isFound);
    if (expectedIsFound) {
        EXPECT_EQ(expectedAddr, addrReturned);
    }
}

TEST_F(StorageClusteredHashHcIndex, TestHcIndexDeleteWithPrefetch)
{
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);
    HpTupleAddr tupleAddr;
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 插入主键和数据以及hashcluster索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
        Status ret = ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr);

        IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
        UtHcIndexDeleteOneWithPrefetch(idxCtx, tupleAddr, removePara, hcIndexKey);

        UtAutoCommitOrRollBack(ret, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ret);
        // 利用聚簇索引查询指定key的个数
        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)0, count);
        UtFreeTupleData(&tupleBuf);
    }

    UtCloseLabel(hctRunxCtx, idxCtx);
}

TEST_F(StorageClusteredHashHcIndex, TestHcIndexInsertWithPrefetch)
{
    ChLabelRunCtxT *hctRunxCtx = NULL;
    IndexCtxT *idxCtx = NULL;
    UtOpenLabel(&hctRunxCtx, &idxCtx);
    HpTupleAddr tupleAddr;
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        HeapTupleBufT tupleBuf = UtGenerateTupleData(i);
        IndexKeyT pkIndexKey = UtGetPKIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        IndexKeyT hcIndexKey = UtGetHCIndexKeyFromTupleBuff((TupleDataT *)tupleBuf.buf);
        // 插入主键和数据以及hashcluster索引
        UtAutoBeginTransaction(hctRunxCtx, HEAP_OPTYPE_INSERT, g_seRunCtx);
        Status ret = ChLabelInsert(hctRunxCtx, pkIndexKey, &tupleBuf, &tupleAddr);
        EXPECT_EQ(GMERR_OK, ret);
        IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
        UtHcIndexDeleteOneWithPrefetch(idxCtx, tupleAddr, removePara, hcIndexKey);
        UtHcIndexInsertOneWithPrefetch(idxCtx, tupleAddr, hcIndexKey);

        UtAutoCommitOrRollBack(ret, g_seRunCtx);
        EXPECT_EQ(GMERR_OK, ret);
        // 利用聚簇索引查询指定key的个数
        uint64_t count = 0;
        EXPECT_EQ(GMERR_OK, IdxGetKeyCount(idxCtx, hcIndexKey, &count));
        EXPECT_EQ((uint64_t)1, count);
        UtFreeTupleData(&tupleBuf);
    }

    UtCloseLabel(hctRunxCtx, idxCtx);
}
