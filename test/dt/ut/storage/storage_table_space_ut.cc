/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: ut for memdata table space
 * Author: wenming
 * Create: 2023-07-19
 */

#include "gtest/gtest.h"
#include "common_init.h"
#include "se_capacity_def_inner.h"
#include "se_page_mgr.h"
#include "se_table_space_pub.h"
#include "storage_ut_common.h"
#include "se_spacemgr.h"

#define UT_LABEL_ID_1 0xABadBabe
#define UT_LABEL_ID_2 0xC0ffee

class UtStorageTableSpace : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        DbSetServerThreadFlag();
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        DbMemCtxT *g_topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
        if (g_topShmMemCtx == NULL) {
            ASSERT_EQ(0, 1);
        }
        SeConfigT config = {0};
        config.deviceSize = 4 * DB_KIBI;
        config.pageSize = 16;
        config.instanceId = 1;
        config.maxSeMem = 500 * DB_KIBI;
        config.maxTrxNum = MAX_TRX_NUM;

        SeInstanceT *sePtr = NULL;
        ret = SeCreateInstance(NULL, (DbMemCtxT *)g_topShmMemCtx, &config, (SeInstanceHdT *)&sePtr);
        ASSERT_EQ(ret, 0);
        SeInstanceT *cmpPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        ASSERT_TRUE(sePtr == cmpPtr);
    };

    static void TearDownTestCase()
    {
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
        DbClearServerThreadFlag();
    };
};

TEST_F(UtStorageTableSpace, CreateAndDropSpace)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = ((MdMgrT *)pageMgr)->spaceMgr;
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(seIns->devMgrShm);
    for (uint32_t i = 0; i < DevGetMaxCount(devMgr); ++i) {
        TableSpaceCfgT cfg = {
            .initSize = (uint16_t)(i * seIns->seConfig.deviceSize / DB_KIBI),
            .stepSize = 0,
            .maxMemSize = (uint16_t)(i * seIns->seConfig.deviceSize / DB_KIBI),
            .reserve = 0,
            .isUseRsm = false,
            .tableSpaceId = CATA_AUTO_GENE_INIT_ID + CATA_RESERVED_RES_COL_POOL_ID + i,
        };
        uint32_t tableSpaceIndex = DB_INVALID_TABLE_SPACE_INDEX;
        Status ret = SeCreateUserTableSpace(GET_INSTANCE_ID, &cfg, &tableSpaceIndex);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_NE(DB_INVALID_TABLE_SPACE_INDEX, tableSpaceIndex);
        MdSpaceT *space = &spaceMgr->spaceArray[tableSpaceIndex];
        ASSERT_TRUE(space->inUse);
        ASSERT_EQ(CATA_AUTO_GENE_INIT_ID + CATA_RESERVED_RES_COL_POOL_ID + i, space->tableSpaceId);
        ASSERT_EQ(i, space->curDevSize);

        ret = SeDropUserTableSpace(GET_INSTANCE_ID, CATA_AUTO_GENE_INIT_ID + CATA_RESERVED_RES_COL_POOL_ID + i);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_FALSE(space->inUse);
        ASSERT_EQ(DevGetMaxCount(devMgr) - 1, devMgr->freeDevCount);
    }
}

/*
用例看护场景：
当devMgr中freeDevCount = 0时，归还一个maxDevSize = curDevSize 的 space
*/
TEST_F(UtStorageTableSpace, DropSpaceWhenNoFreeDeviceCount)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = ((MdMgrT *)pageMgr)->spaceMgr;

    uint32_t tableSpaceIndex;
    uint32_t tableSpaceId1 = CATA_AUTO_GENE_INIT_ID + CATA_RESERVED_RES_COL_POOL_ID;
    TableSpaceCfgT cfg = {
        .initSize = (uint16_t)(seIns->seConfig.deviceSize / DB_KIBI),
        .stepSize = (uint16_t)(seIns->seConfig.deviceSize / DB_KIBI),
        .maxMemSize = (uint16_t)(seIns->seConfig.deviceSize / DB_KIBI),
        .reserve = 0,
        .isUseRsm = false,
        .tableSpaceId = tableSpaceId1,
    };
    Status ret = SeCreateUserTableSpace(GET_INSTANCE_ID, &cfg, &tableSpaceIndex);
    ASSERT_EQ(GMERR_OK, ret);

    // 确保预留所有可被预留的device
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    cfg.maxMemSize = seIns->seConfig.deviceSize / DB_KIBI * DevGetFreeDeviceCount(devMgr);
    uint32_t tableSpaceId2 = CATA_AUTO_GENE_INIT_ID + CATA_RESERVED_RES_COL_POOL_ID + 1;
    cfg.tableSpaceId = tableSpaceId2;
    ret = SeCreateUserTableSpace(GET_INSTANCE_ID, &cfg, &tableSpaceIndex);
    ASSERT_EQ(GMERR_OK, ret);

    // 先归还maxMemSize = initSize 的space
    ret = SeDropUserTableSpace(GET_INSTANCE_ID, tableSpaceId1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = SeDropUserTableSpace(GET_INSTANCE_ID, tableSpaceId2);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtStorageTableSpace, CheckSpaceRecycle)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = ((MdMgrT *)pageMgr)->spaceMgr;
    uint32_t allocDeviceCount = 10;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);
    ASSERT_EQ(space->lastDevId, space->firstDevId);

    // 先把三个device都申请满
    uint32_t allocChunkCount =
        allocDeviceCount * spaceMgr->chunkCntPerDev - 1;  // Undo会占用一个页，所以这里少申请一个，保证是三个device
    for (uint32_t i = 0; i < allocChunkCount; ++i) {
        PageIdT addr = SE_INVALID_PAGE_ADDR;
        AllocPageParamT allocPageParam =
            SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
        StatusInter interRet = MdAllocPage((MdMgrT *)pageMgr, &allocPageParam, &addr);
        ASSERT_EQ(STATUS_OK_INTER, interRet);
    }

    uint32_t devAddr[allocDeviceCount] = {0};
    devAddr[0] = space->firstDevId;
    DevNodeT *devDesc = DevNodeGetById(((MdMgrT *)pageMgr)->devMgr, space->firstDevId);
    ASSERT_EQ(SE_INVALID_DEVICE_ID, devDesc->prevDevId);
    for (uint32_t i = 1; i < allocDeviceCount; ++i) {
        devAddr[i] = devDesc->nextDevId;
        devDesc = DevNodeGetById(((MdMgrT *)pageMgr)->devMgr, devDesc->nextDevId);
        ASSERT_EQ(devAddr[i - 1], devDesc->prevDevId);
    }
    ASSERT_EQ(devAddr[0], space->firstDevId);
    ASSERT_EQ(devAddr[allocDeviceCount - 1], space->lastDevId);
    ASSERT_EQ(allocDeviceCount, space->curDevSize);

    // 再把中间的干掉
    for (uint32_t i = 0; i < spaceMgr->chunkCntPerDev; ++i) {
        PageIdT addr = (PageIdT){.deviceId = devAddr[1], .blockId = i};
        FreePageParamT freePageParam =
            SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr, NULL, NULL, SE_INVALID_LABEL_ID, false);
        StatusInter interRet = MdFreePage((MdMgrT *)pageMgr, &freePageParam);
        ASSERT_EQ(STATUS_OK_INTER, interRet);
    }
    // 头尾保持不变
    devDesc = DevNodeGetById(((MdMgrT *)pageMgr)->devMgr, space->firstDevId);
    ASSERT_EQ(devAddr[0], space->firstDevId);
    ASSERT_EQ(devAddr[2], devDesc->nextDevId);
    devDesc = DevNodeGetById(((MdMgrT *)pageMgr)->devMgr, devAddr[2]);
    ASSERT_EQ(space->firstDevId, devDesc->prevDevId);
    ASSERT_EQ(allocDeviceCount - 1, space->curDevSize);
}

// free page时会根据入参need cached 挂在tableSpace的空闲链表上
TEST_F(UtStorageTableSpace, spaceCachedListAdd_001)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 1, listCntAfter);

    // 先申请两个页
    PageIdT addr1 = SE_INVALID_PAGE_ADDR;  // 正常释放
    PageIdT addr2 = SE_INVALID_PAGE_ADDR;  // 缓释放
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID, NULL, NULL);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 用不同的方式释放这两个页，观察tableSpace的空闲链表长度
    DbArrayAddrT arrAddr;
    LabelCachePageListT *pageList = (LabelCachePageListT *)DbShmArrayGetItemById(shmArray, 0, &arrAddr);
    uint32_t pageListLenBefore = pageList->listCnt;
    // 正常释放不会放到空闲链表上
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr1, NULL, NULL, UT_LABEL_ID, false);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore, pageList->listCnt);

    freePageParam.addr = addr2;
    freePageParam.cachePagePara.needCache = true;  // 缓释放的页放到tableSpace的空闲链表
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore + 1, pageList->listCnt);

    // 删除缓存页链表
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}

// tableSpace的空闲链表数量和表数量相关，不同表id缓释放页对应不同的链表
TEST_F(UtStorageTableSpace, spaceCachedListAdd_002)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 2, listCntAfter);

    // 先申请两个页
    PageIdT addr1 = SE_INVALID_PAGE_ADDR;  // 表1缓释放
    PageIdT addr2 = SE_INVALID_PAGE_ADDR;  // 表2缓释放
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    allocPageParam.labelId = UT_LABEL_ID_2;
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 不同的表缓释放这两个页，观察tableSpace的不同空闲链表的长度
    // 表1缓释放
    DbArrayAddrT arrAddr;
    LabelCachePageListT *pageList = (LabelCachePageListT *)DbShmArrayGetItemById(shmArray, 0, &arrAddr);
    uint32_t pageListLenBefore = pageList->listCnt;
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr1, NULL, NULL, UT_LABEL_ID_1, true);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore + 1, pageList->listCnt);

    // 表2缓释放
    pageList = (LabelCachePageListT *)DbShmArrayGetItemById(shmArray, 1, &arrAddr);
    pageListLenBefore = pageList->listCnt;
    freePageParam.addr = addr2;
    freePageParam.cachePagePara.labelId = UT_LABEL_ID_2;
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore + 1, pageList->listCnt);

    // 删除缓存页链表
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    para.labelId = UT_LABEL_ID_2;
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}

// 表缓释放的页会增加对应链表的长度，正常释放则不会改变链表的长度
TEST_F(UtStorageTableSpace, spaceCachedListAdd_003)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 1, listCntAfter);

    // 先申请两个页
    PageIdT addr1 = SE_INVALID_PAGE_ADDR;  // 正常释放
    PageIdT addr2 = SE_INVALID_PAGE_ADDR;  // 缓释放
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 不同的表缓释放这两个页，观察tableSpace的表级别空闲链表长度
    // 表1缓释放
    DbArrayAddrT arrAddr;
    LabelCachePageListT *pageList = (LabelCachePageListT *)DbShmArrayGetItemById(shmArray, 0, &arrAddr);
    uint32_t pageListLenBefore = pageList->listCnt;
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr1, NULL, NULL, UT_LABEL_ID_1, false);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore, pageList->listCnt);  // 正常释放不增加表级别list长度

    freePageParam.addr = addr2;
    freePageParam.cachePagePara.needCache = true;
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore + 1, pageList->listCnt);  // 缓释放表级别list长度增加1

    // 删除缓存页链表
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}

// 测试两种方式释放缓存页 1.仅释放页保留链表 2.释放页同时删除链表
TEST_F(UtStorageTableSpace, spaceCachedListAdd_004)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 1, listCntAfter);

    PageIdT addr = SE_INVALID_PAGE_ADDR;  // 缓释放
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    DbArrayAddrT arrAddr;
    LabelCachePageListT *pageList = (LabelCachePageListT *)DbShmArrayGetItemById(shmArray, 0, &arrAddr);
    uint32_t pageListLenBefore = pageList->listCnt;
    // 表1缓释放
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr, NULL, NULL, UT_LABEL_ID_1, true);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(pageListLenBefore + 1, pageList->listCnt);  // 表级别链表长度增加1

    // 仅释放页, 不删除链表, 释放后space空闲链表的数量不变
    bool isOverTime = false;
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, &isOverTime, false);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 1, listCntAfter);

    // 释放页同时删除链表, 释放后space空闲链表的数量减少
    interRet = MdClearCachedPageList(mdMgr, &para, &isOverTime, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}

// 缓释放的页可以在其他表申请时复用
TEST_F(UtStorageTableSpace, spaceCachedListRemove_001)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 1, listCntAfter);

    PageIdT addr = SE_INVALID_PAGE_ADDR;
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID, NULL, NULL);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    DbArrayAddrT arrAddr;
    LabelCachePageListT *pageList = (LabelCachePageListT *)DbShmArrayGetItemById(shmArray, 0, &arrAddr);
    uint32_t pageListLenBefore = pageList->listCnt;
    // 缓释放
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr, NULL, NULL, UT_LABEL_ID, true);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    // 表级别list长度增加1
    ASSERT_EQ(pageListLenBefore + 1, pageList->listCnt);

    allocPageParam.labelId = UT_LABEL_ID_1;  // 表1申请新的页是之前缓释放的页
    PageIdT newAddr;
    interRet = MdAllocPage(mdMgr, &allocPageParam, &newAddr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    ASSERT_EQ(DbIsPageIdEqual(addr, newAddr), true);

    // 删除缓存页链表
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}

// 缓释放的页会优先被再次分配
TEST_F(UtStorageTableSpace, spaceCachedListRemove_002)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 1, listCntAfter);

    PageIdT addr1 = SE_INVALID_PAGE_ADDR;  // 正常释放
    PageIdT addr2 = SE_INVALID_PAGE_ADDR;  // 缓释放
    PageIdT addr3 = SE_INVALID_PAGE_ADDR;  // 正常释放
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr3);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 归还顺序 addr1(正常释放) --> addr2(缓释放) --> addr3(正常释放)
    // 正常释放
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr1, NULL, NULL, UT_LABEL_ID_1, false);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    freePageParam.addr = addr2;
    freePageParam.cachePagePara.needCache = true;  // 缓释放
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    freePageParam.addr = addr3;
    freePageParam.cachePagePara.needCache = false;  // 正常释放
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 优先分配缓释放的页
    PageIdT newAddr;
    interRet = MdAllocPage(mdMgr, &allocPageParam, &newAddr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(DbIsPageIdEqual(newAddr, addr2), true);

    // 删除缓存页链表
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}

// 缓释放的页以表粒度管理，并且分配时优先将首个空闲链表分配完之后才会分配后面空闲链表上的页
TEST_F(UtStorageTableSpace, spaceCachedListRemove_003)
{
    // 持久化目前不支持tableSpace
    PERSISTENCE_NOT_SUPPORT;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    SpaceMgrT *spaceMgr = mdMgr->spaceMgr;
    MdSpaceT *space = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    ASSERT_TRUE(space->inUse);
    ASSERT_EQ(DB_DEFAULT_TABLE_SPACE_ID, (int32_t)space->tableSpaceId);

    // 创建缓存页链表
    DbShmArrayT *shmArray = (DbShmArrayT *)DbShmPtrToAddr(space->cachedListsShm);
    DB_ASSERT(shmArray != NULL);
    uint32_t listCntBefore = DbShmArrayGetUsedItemCnt(shmArray);
    StatusInter interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdCreateCachedPageList(mdMgr, DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    uint32_t listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore + 2, listCntAfter);

    PageIdT addr1 = SE_INVALID_PAGE_ADDR;  // 表1缓释放
    PageIdT addr2 = SE_INVALID_PAGE_ADDR;  // 表1缓释放
    PageIdT addr3 = SE_INVALID_PAGE_ADDR;  // 表2缓释放
    AllocPageParamT allocPageParam = SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, UT_LABEL_ID_1, NULL, NULL);
    // 表1申请2个页
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr1);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr2);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 表2申请1个页
    allocPageParam.labelId = UT_LABEL_ID_2;
    interRet = MdAllocPage(mdMgr, &allocPageParam, &addr3);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    // 归还顺序 addr1 --> addr3 --> addr2
    // 表1缓释放addr1
    FreePageParamT freePageParam =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr1, NULL, NULL, UT_LABEL_ID_1, true);
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    freePageParam.addr = addr3;
    freePageParam.cachePagePara.labelId = UT_LABEL_ID_2;  // 表2缓释放addr3
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    freePageParam.addr = addr2;
    freePageParam.cachePagePara.labelId = UT_LABEL_ID_1;  // 表1缓释放add2
    interRet = MdFreePage(mdMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, interRet);

    /*
    再次申请得到的addr和缓释放addr顺序无关，空闲链表以表粒度管理，即首个空闲链表分配完之后才会分配后面空闲链表上的页
    且同一个空闲链表归还采用头插法
    因此申请得到的新页顺序为addr2-->addr1-->addr3
    */
    PageIdT newAddr;
    allocPageParam.labelId = UT_LABEL_ID;
    interRet = MdAllocPage(mdMgr, &allocPageParam, &newAddr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(DbIsPageIdEqual(newAddr, addr2), true);

    interRet = MdAllocPage(mdMgr, &allocPageParam, &newAddr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(DbIsPageIdEqual(newAddr, addr1), true);

    interRet = MdAllocPage(mdMgr, &allocPageParam, &newAddr);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    ASSERT_EQ(DbIsPageIdEqual(newAddr, addr3), true);

    // 删除缓存页链表
    FreeCachedPageParamT para = SeInitCachedPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, UT_LABEL_ID_1, NULL, NULL);
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    para.labelId = UT_LABEL_ID_2;
    interRet = MdClearCachedPageList(mdMgr, &para, NULL, true);
    ASSERT_EQ(STATUS_OK_INTER, interRet);
    listCntAfter = DbShmArrayGetUsedItemCnt(shmArray);
    ASSERT_EQ(listCntBefore, listCntAfter);
}
