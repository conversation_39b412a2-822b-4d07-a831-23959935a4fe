#include "storage_session.h"
#include "storage_ut_common.h"
#include <stdlib.h>
#include <sys/types.h>
#include <unistd.h>
#include <vector>
#include <thread>
#include "gtest/gtest.h"
#include "stub.h"
#include "securec.h"
#include "adpt_types.h"
#include "db_mem_context.h"
#include "common_init.h"
#include <vector>
#include "se_define.h"
#include "db_resource_session_pub.h"
#include "db_resource_session.h"
#include <algorithm>
#include "se_instance.h"
#include "db_table_space.h"
#include "se_memdata.h"
#include "se_page_mgr.h"
#include "se_heap_utils.h"
#include "se_heap_inner.h"

using namespace std;

extern SeRunCtxHdT g_seRunCtx;

int32_t SeOpenWithNewSession(uint16_t instanceId, DbMemCtxT *seTopDynamicCtxTop, SeRunCtxHdT *seRunCtxHd)
{
    int32_t ret = SeOpen(instanceId, seTopDynamicCtxTop, NULL, seRunCtxHd);
    if (ret != 0) {
        return ret;
    }
    return SeOpenResSession(*seRunCtxHd);
}

int32_t SeOpenAttachSessionUt(uint16_t instanceId, DbMemCtxT *seTopDynamicCtxTop, SeRunCtxT **seRunCtxHd)
{
    DbSetServerThreadFlag();
    int32_t ret = SeOpen(instanceId, seTopDynamicCtxTop, NULL, seRunCtxHd);
    if (ret != 0) {
        return ret;
    }
    uint32_t sessionId;
    SeGetResSessionID(g_seRunCtx, &sessionId);
    return SeAttachResSessionById(*seRunCtxHd, sessionId);
}

bool DbSessionCheckAndUpdateStack_STUB1(DbSessionCtxT *sessionCtx, uint8_t *latchAddr)
{
    LatchStackT *latchStack = sessionCtx->latchStack;
    if (SECUREC_LIKELY(latchStack->stack[latchStack->stackTop - 1].virtAddr == latchAddr)) {
        return true;
    }

    // 栈顶记录的锁地址和当前要解锁的锁地址不一致，说明出现了逆序解锁场景, 需要整理锁栈
    // 发生逆序解锁的场景应该是非常少的
    bool isFound = false;
    int32_t idx = (int32_t)latchStack->stackTop - 1;
    for (; idx >= LATCH_STACK_BOTTOM; --idx) {
        if (latchStack->stack[idx].virtAddr == latchAddr) {
            isFound = true;
            break;
        }
    }
    // 一定可以找到
    DB_ASSERT(isFound);

    // 更新锁栈状态，在该状态中，锁栈可能记录了两把相同的锁或者中间记录了一把latchType为LATCH_ADDR_INVALID的锁
    // 在最后清理锁栈的时候需要根据该状态标识进行相应处理
    latchStack->stackStatus = STATUS_ABNORMAL;
    // 将当前需要解的锁重新压入栈顶, 再将原来锁的信息覆盖删除

    DbSessionLatchPush(sessionCtx, latchStack->stack[idx].virtAddr, &latchStack->stack[idx].shmAddr,
        latchStack->stack[idx].latchAcqMode, latchStack->stack[idx].latchType);

    // 模拟栈中存在两把相同锁异常退出场景
    printf("crash point [1] exit\n");
    pthread_exit(NULL);

    for (uint32_t i = (uint32_t)idx; i < latchStack->stackTop; ++i) {
        latchStack->stack[i].latchType = LATCH_ADDR_INVALID;
        COMPILER_BARRIER;
        latchStack->stack[i].latchAcqMode = latchStack->stack[i + 1].latchAcqMode;
        latchStack->stack[i].shmAddr = latchStack->stack[i + 1].shmAddr;
        latchStack->stack[i].virtAddr = latchStack->stack[i + 1].virtAddr;
        COMPILER_BARRIER;
        latchStack->stack[i].latchType = latchStack->stack[i + 1].latchType;
    }
    latchStack->stack[latchStack->stackTop].latchType = LATCH_ADDR_INVALID;
    COMPILER_BARRIER;
    latchStack->stackStatus = STATUS_NORMAL;
    return true;
}

bool DbSessionCheckAndUpdateStack_STUB2(DbSessionCtxT *sessionCtx, uint8_t *latchAddr)
{
    LatchStackT *latchStack = sessionCtx->latchStack;
    if (SECUREC_LIKELY(latchStack->stack[latchStack->stackTop - 1].virtAddr == latchAddr)) {
        return true;
    }

    // 栈顶记录的锁地址和当前要解锁的锁地址不一致，说明出现了逆序解锁场景, 需要整理锁栈
    // 发生逆序解锁的场景应该是非常少的
    bool isFound = false;
    int32_t idx = (int32_t)latchStack->stackTop - 1;
    for (; idx >= LATCH_STACK_BOTTOM; --idx) {
        if (latchStack->stack[idx].virtAddr == latchAddr) {
            isFound = true;
            break;
        }
    }
    // 一定可以找到
    DB_ASSERT(isFound);

    // 更新锁栈状态，在该状态中，锁栈可能记录了两把相同的锁或者中间记录了一把latchType为LATCH_ADDR_INVALID的锁
    // 在最后清理锁栈的时候需要根据该状态标识进行相应处理
    latchStack->stackStatus = STATUS_ABNORMAL;
    // 将当前需要解的锁重新压入栈顶, 再将原来锁的信息覆盖删除

    DbSessionLatchPush(sessionCtx, latchStack->stack[idx].virtAddr, &latchStack->stack[idx].shmAddr,
        latchStack->stack[idx].latchAcqMode, latchStack->stack[idx].latchType);

    for (uint32_t i = (uint32_t)idx; i < latchStack->stackTop; ++i) {
        latchStack->stack[i].latchType = LATCH_ADDR_INVALID;
        COMPILER_BARRIER;
        latchStack->stack[i].latchAcqMode = latchStack->stack[i + 1].latchAcqMode;
        latchStack->stack[i].shmAddr = latchStack->stack[i + 1].shmAddr;
        latchStack->stack[i].virtAddr = latchStack->stack[i + 1].virtAddr;
        // 模拟栈中记录一把无效锁异常退出场景
        printf("crash point [2] exit\n");
        pthread_exit(NULL);
        COMPILER_BARRIER;
        latchStack->stack[i].latchType = latchStack->stack[i + 1].latchType;
    }
    latchStack->stack[latchStack->stackTop].latchType = LATCH_ADDR_INVALID;
    COMPILER_BARRIER;
    latchStack->stackStatus = STATUS_NORMAL;
    return true;
}

extern void UtHeapAmBasicPrepareSeInstance(PageTotalSizeT pageSize);

class UtStorageSession : public testing::Test {
protected:
    virtual void SetUp()
    {}

    virtual void TearDown()
    {}

    virtual int AllocObjects()
    {
        return 0;
    }

    virtual void FreeObjects()
    {}

    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }
        g_seRunCtx = NULL;
        UtHeapAmBasicPrepareSeInstance(SE_DEFAULT_PAGE_SIZE);
        g_seRunCtx->resSessionCtx.session->shmRole = DbShmemCtxAlloc((DbMemCtxT *)g_topShmMemCtx, sizeof(CataRoleT));
    };

    static void TearDownTestCase()
    {
        if (g_seRunCtx != NULL) {
            SeClose(g_seRunCtx);
            g_seRunCtx = NULL;
        }
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

TEST_F(UtStorageSession, TestSessionSaveRefCount)
{
    SeRunCtxT *seRunCtx = NULL;
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    ShmRefItem item = {
        .shmType = REF_SHM_HEAP_CURSOR,
        .latchOffset = 0,
        .openCntOffset = 0,
        .refCount = 1,
        .shmAddr = {0, 0},
    };
    ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &item, true));

    // 第一次增加会缓存
    DB_POINTER(resSessionCtx->lastRefDesc);
    ASSERT_EQ(0, (int32_t)resSessionCtx->lastDescId);
    EXPECT_EQ(1, (int32_t)resSessionCtx->refArray->curPos);
    EXPECT_EQ(1, (int32_t)resSessionCtx->refArray->usedCnt);
    EXPECT_EQ(0, (int32_t)resSessionCtx->refArray->extendTimes);

    ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &item, false));
    // 删除之后移除缓存
    ASSERT_EQ(INVALID_SHMREF_DESC_ID, resSessionCtx->lastDescId);
    EXPECT_EQ(1, (int32_t)resSessionCtx->refArray->curPos);
    EXPECT_EQ(0, (int32_t)resSessionCtx->refArray->usedCnt);
    EXPECT_EQ(0, (int32_t)resSessionCtx->refArray->extendTimes);
    (void)SeClose(seRunCtx);
}

TEST_F(UtStorageSession, TestSessionSaveRefCountAndExit)
{
    // 持久化当前不支持kv
    PERSISTENCE_NOT_SUPPORT;
    SeRunCtxT *seRunCtxServer = NULL;
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtxServer);
    ASSERT_EQ(0, ret);
    uint32_t sessionId;
    SeGetResSessionID(seRunCtxServer, &sessionId);

    SeRunCtxT *seRunCtxClient = NULL;
    ret = SeOpen(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, NULL, &seRunCtxClient);
    ASSERT_EQ(0, ret);
    ret = SeAttachResSessionById(seRunCtxClient, sessionId);
    // 基于存储层的要求初始化heapCfg
    HeapAccessCfgT heapCfg;
    heapCfg.pageType = HEAP_VAR_LEN_ROW_PAGE;
    heapCfg.tupleType = HEAP_TUPLE_TYPE_KV;
    heapCfg.fixRowSize = 0;
    heapCfg.slotExtendSize = 0;
    heapCfg.seInstanceId = GET_INSTANCE_ID;
    heapCfg.isYangBigStore = false;
    heapCfg.isStatusMergeSubs = false;
    heapCfg.isPersistent = false;
    heapCfg.isUseRsm = false;
    heapCfg.ccType = CONCURRENCY_CONTROL_NORMAL;
    heapCfg.maxItemNum = 1000;
    heapCfg.labelId = UT_LABEL_ID;
    heapCfg.heapFileId = 0;
    heapCfg.heapFsmFileId = 0;
    heapCfg.tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID;
    heapCfg.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;

    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(0, ret);

#ifndef FEATURE_PERSISTENCE
    HeapJumpT *heapJump = (HeapJumpT *)DbShmPtrToAddr(heapShmAddr);
    if (heapJump == NULL) {
        EXPECT_EQ(0, 1);
    }
    HeapLazyInit(heapJump);
    HeapT *heap = (HeapT *)DbShmPtrToAddr(heapJump->heapShmAddr);
    if (heap == NULL) {
        EXPECT_EQ(0, 1);
    }
#else
    HeapT *heap = NULL;
    ret = SeGetHeap((PageMgrT *)seRunCtxClient->pageMgr, *(PageIdT *)&heapShmAddr, false, &heap);
    ASSERT_EQ(STATUS_OK_INTER, ret);
#endif

    ShmemPtrT labelLatchAddr = DbShmemCtxAlloc((DbMemCtxT *)g_topShmMemCtx, sizeof(LabelRWLatchT));
    LabelRWLatchT *labelRwLatch = (LabelRWLatchT *)DbShmPtrToAddr(labelLatchAddr);
    if (labelRwLatch == NULL) {
        EXPECT_EQ(0, 1);
    }
    DbRWLatchInit(&labelRwLatch->rwlatch);
    labelRwLatch->rwlatchShmptr = labelLatchAddr;
    GET_MEMBER_SHMPTR(labelRwLatch->rwlatchShmptr, offsetof(LabelRWLatchT, rwlatch));
    labelRwLatch->versionId = 0;
    // 更新引用计数后未复原就异常退出
    ASSERT_EQ(0, HeapCursorCountUpdate(seRunCtxClient, heap->runtimeInfoShmPtr, labelLatchAddr, 0, true));
    HeapRuntimeInfoT *runTimeInfo = (HeapRuntimeInfoT *)DbShmPtrToAddr(heap->runtimeInfoShmPtr);
    runTimeInfo->totalCursorNum++;
    EXPECT_EQ(1, (int32_t)seRunCtxServer->resSessionCtx.refArray->usedCnt);

    (void)SeClose(seRunCtxClient);

    // 服务器进行恢复
    (void)SeClose(seRunCtxServer);
    // heap计数已经复原
    EXPECT_EQ((uint32_t)0, runTimeInfo->totalCursorNum);
    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    HeapLabelDrop(NULL, &heapCntrAcsInfo, NULL);
    DbShmemCtxFree((DbMemCtxT *)g_topShmMemCtx, labelLatchAddr);
}

TEST_F(UtStorageSession, TestSessionRefArrayExtend)
{
    SeRunCtxT *seRunCtx = NULL;
    DbSetServerThreadFlag();
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    ShmRefItem item = {
        .shmType = REF_SHM_HEAP_CURSOR,
        .latchOffset = 0,
        .openCntOffset = 0,
        .refCount = 1,
        .shmAddr = {0, 0},
    };

    // 预期触发三次扩容
    uint32_t maxItem = (DEFAULT_SHMREF_DESC_COUNT + 4 * DEFAULT_SHMREF_DESC_COUNT + 1);
    for (uint32_t i = 0; i < maxItem; ++i) {
        item.shmAddr = {i, i};
        item.refCount = i + 2;
        ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &item, true));
    }
    EXPECT_EQ(maxItem, resSessionCtx->refArray->curPos);
    EXPECT_EQ(maxItem, resSessionCtx->refArray->usedCnt);
    EXPECT_EQ(3, (int32_t)resSessionCtx->refArray->extendTimes);
    EXPECT_EQ((DEFAULT_SHMREF_DESC_COUNT + 8 * DEFAULT_SHMREF_DESC_COUNT), (int32_t)resSessionCtx->refArray->capacity);

    for (uint32_t i = 0; i < maxItem; ++i) {
        item.shmAddr = {i, i};
        item.refCount = i + 2;
        ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &item, false));
    }
    EXPECT_EQ(maxItem, resSessionCtx->refArray->curPos);
    EXPECT_EQ(0, (int32_t)resSessionCtx->refArray->usedCnt);
    EXPECT_EQ(3, (int32_t)resSessionCtx->refArray->extendTimes);
    EXPECT_EQ((DEFAULT_SHMREF_DESC_COUNT + 8 * DEFAULT_SHMREF_DESC_COUNT), (int32_t)resSessionCtx->refArray->capacity);
    (void)SeClose(seRunCtx);
    DbClearServerThreadFlag();
}

ShmRefItem PushItem(vector<ShmRefItem> &data, uint index)
{
    ShmRefItem item = {.shmType = REF_SHM_HEAP_CURSOR,
        .latchOffset = index,
        .openCntOffset = 0,
        .refCount = (uint32_t)(rand() % 16) + 1,
        .shmAddr = {index + 1, 0}};
    data.push_back(item);
    return item;
}

TEST_F(UtStorageSession, TestSessionRandIncAndDec)
{
    SeRunCtxT *seRunCtx = NULL;
    DbSetServerThreadFlag();
    int32_t ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    vector<ShmRefItem> data;
    srand(unsigned(time(NULL)));
    ShmRefItem tempItem;
    uint32_t maxItem = (DEFAULT_SHMREF_DESC_COUNT + 16 * DEFAULT_SHMREF_DESC_COUNT + 3);
    for (uint32_t i = 0; i < maxItem; ++i) {
        int randData = rand() % 100;
        tempItem = PushItem(data, i);
        ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &tempItem, true));

        if (randData > 50) {
            ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &tempItem, false));
            data.pop_back();
        }
    }

    while (data.size() > 0) {
        tempItem = data.back();
        ASSERT_EQ(0, DbSessionUpdateShmRefCount(resSessionCtx, &tempItem, false));
        data.pop_back();
    }

    EXPECT_EQ(0, (int32_t)resSessionCtx->refArray->usedCnt);
    for (uint32_t i = 0; i < resSessionCtx->refArray->curPos; ++i) {
        if (i < DEFAULT_SHMREF_DESC_COUNT) {
            EXPECT_EQ(REF_SHM_INVALID, resSessionCtx->refArray->cacheDescs[i].item.shmType);
            EXPECT_EQ(0, (int32_t)resSessionCtx->refArray->cacheDescs[i].item.refCount);
        } else {
            ShmRefDescT *dynShmArray = (ShmRefDescT *)DbShmPtrToAddr(resSessionCtx->refArray->extShmPtr);
            EXPECT_EQ(REF_SHM_INVALID, dynShmArray[i - DEFAULT_SHMREF_DESC_COUNT].item.shmType);
            EXPECT_EQ(0, (int32_t)dynShmArray[i - DEFAULT_SHMREF_DESC_COUNT].item.refCount);
        }
    }
    printf("capacity: %u\n", resSessionCtx->refArray->capacity);
    printf("extendTimes: %u\n", resSessionCtx->refArray->extendTimes);
    (void)SeClose(seRunCtx);
    DbClearServerThreadFlag();
}

inline void HeapPageLock(
    SeInstanceT *seIns, DbSessionCtxT *resSessionCtx, PageHeadT *page, PageIdT pageAddr, bool isRead)
{
    ShmemPtrT shmAddr = *(ShmemPtrT *)&pageAddr;
    if (isRead) {
        DbRWSpinRLockWithSession(resSessionCtx, &page->lock, &shmAddr, LATCH_ADDR_PAGEID);
    } else {
        DbRWSpinWLockWithSession(resSessionCtx, &page->lock, &shmAddr, LATCH_ADDR_PAGEID);
    }
}

inline void HeapPageUnlock(DbSessionCtxT *resSessionCtx, PageHeadT *pageAddr, bool isRead)
{
    if (isRead) {
        DbRWSpinRUnlockWithSession(resSessionCtx, &pageAddr->lock);
    } else {
        DbRWSpinWUnlockWithSession(resSessionCtx, &pageAddr->lock);
    }
}

TEST_F(UtStorageSession, testPushLatchSequence)
{
    DbSetServerThreadFlag();
    SeRunCtxT *seRunCtx = NULL;
    int32_t ret = SeOpenAttachSessionUt(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr[MAX_LATCH_NUMS];
    PageHeadT *page[MAX_LATCH_NUMS];
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        addr[i] = SE_INVALID_PAGE_ADDR;

        // 获取新页
        ret = SeAllocPage(pageMgr, &allocPageParam, &addr[i]);
        ASSERT_EQ(ret, GMERR_OK);
        // 检验是否能够拿到非法的pageId
        ASSERT_EQ(true, DbIsPageIdValid(addr[i]));

        ret = SeGetPage(pageMgr, addr[i], (uint8_t **)&page[i], ENTER_PAGE_NORMAL, false);
        ASSERT_EQ(ret, GMERR_OK);

        // 对页加锁
        HeapPageLock((SeInstanceT *)(seRunCtx->seIns), resSessionCtx, page[i], addr[i], (i % 2));

        // 对页解锁
        HeapPageUnlock(resSessionCtx, page[i], (i % 2));

        // 释放页
        FreePageParamT freePageParam =
            SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr[i], NULL, NULL, SE_INVALID_LABEL_ID, false);
        ret = SeFreePage(pageMgr, &freePageParam);
        ASSERT_EQ(GMERR_OK, ret);
    }
    (void)SeClose(seRunCtx);
}

// 测试一次加多个页锁
TEST_F(UtStorageSession, testPushLatch2)
{
    SeRunCtxT *seRunCtx = NULL;
    int32_t ret = SeOpenAttachSessionUt(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr[MAX_LATCH_NUMS];
    PageHeadT *page[MAX_LATCH_NUMS];
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        addr[i] = SE_INVALID_PAGE_ADDR;

        // 获取新页
        ret = SeAllocPage(pageMgr, &allocPageParam, &addr[i]);
        ASSERT_EQ(ret, GMERR_OK);
        // 检验是否能够拿到非法的pageId
        ASSERT_EQ(true, DbIsPageIdValid(addr[i]));

        ret = SeGetPage(pageMgr, addr[i], (uint8_t **)&page[i], ENTER_PAGE_NORMAL, false);
        ASSERT_EQ(ret, GMERR_OK);

        // 对页加锁
        HeapPageLock((SeInstanceT *)(seRunCtx->seIns), resSessionCtx, page[i], addr[i], (i % 2));
    }

    for (int32_t i = MAX_LATCH_NUMS - 1; i >= 0; --i) {
        // 对页解锁
        HeapPageUnlock(resSessionCtx, page[i], (i % 2));
        FreePageParamT freePageParam =
            SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr[i], NULL, NULL, SE_INVALID_LABEL_ID, false);
        // 释放页
        ret = SeFreePage(pageMgr, &freePageParam);
        ASSERT_EQ(GMERR_OK, ret);
    }

    (void)SeClose(seRunCtx);
}

typedef struct TagThreadArgsT {
    PageHeadT **page;
    PageIdT *pageAddr;
    uint32_t count;
} ThreadArgsT;

void *ThreadUnlock(void *args)
{
    DbSetServerThreadFlag();
    ThreadArgsT *threadArgs = (ThreadArgsT *)args;
    PageHeadT **page = threadArgs->page;
    PageIdT *pageAddr = threadArgs->pageAddr;

    SeRunCtxT *seRunCtx = NULL;
    int32_t ret = SeOpenAttachSessionUt(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    EXPECT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    // 模拟线程对页加锁
    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        // 对页加锁
        HeapPageLock((SeInstanceT *)(seRunCtx->seIns), resSessionCtx, page[i], pageAddr[i], (i % 2));
    }

    // 测试随机解锁, 对于未被解锁的页将由服务器侧恢复
    srand(time(NULL));
    int32_t index = rand() % MAX_LATCH_NUMS;
    HeapPageUnlock(resSessionCtx, page[index], (index % 2));
    (void)SeClose(seRunCtx);
    return NULL;
}

bool DbCommonIsServerStub(void)
{
    return false;
}

extern "C" {
bool DbSessionCheckAndUpdateStack(DbSessionCtxT *sessionCtx, uint8_t *latchAddr);
}

TEST_F(UtStorageSession, testAbnormalExist)
{
    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr[MAX_LATCH_NUMS];
    PageHeadT *page[MAX_LATCH_NUMS];
    int32_t ret;
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        addr[i] = SE_INVALID_PAGE_ADDR;

        // 获取新页
        ret = SeAllocPage(pageMgr, &allocPageParam, &addr[i]);
        ASSERT_EQ(ret, GMERR_OK);
        // 检验是否能够拿到非法的pageId
        ASSERT_EQ(true, DbIsPageIdValid(addr[i]));

        ret = SeGetPage(pageMgr, addr[i], (uint8_t **)&page[i], ENTER_PAGE_NORMAL, false);
        ASSERT_EQ(ret, GMERR_OK);
    }

    ThreadArgsT args = {.page = page, .pageAddr = addr, .count = MAX_LATCH_NUMS};
    int32_t stub1 = -1;
    if (rand() % 100 < 50) {
        stub1 = setStubC((void *)DbSessionCheckAndUpdateStack, (void *)DbSessionCheckAndUpdateStack_STUB1);
    } else {
        stub1 = setStubC((void *)DbSessionCheckAndUpdateStack, (void *)DbSessionCheckAndUpdateStack_STUB2);
    }
    // 模拟客户端异常退出
    pthread_t subThread;
    // 子线程发生了线程退出
    ret = pthread_create(&subThread, NULL, ThreadUnlock, &args);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(subThread, NULL);
    clearStub(stub1);
    // 服务器侧进行回收
    (void)SeClose(g_seRunCtx);
    g_seRunCtx = NULL;

    ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    ASSERT_EQ(ret, 0);
    DbSessionCtxT *dbSessionCtx = &g_seRunCtx->resSessionCtx;
    dbSessionCtx->latchStack = (LatchStackT *)DbShmPtrToAddr(g_seRunCtx->resSessionCtx.session->shmLatchStack);
    DB_ASSERT(dbSessionCtx->latchStack != NULL);
    dbSessionCtx->latchStack->pid = DbRWlatchGetPid();
    g_seRunCtx->resSessionCtx.session->shmRole = DbShmemCtxAlloc((DbMemCtxT *)g_topShmMemCtx, sizeof(CataRoleT));

    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        HeapPageLock(sePtr, &g_seRunCtx->resSessionCtx, page[i], addr[i], false);

        HeapPageUnlock(&g_seRunCtx->resSessionCtx, page[i], false);

        // 释放页
        FreePageParamT freePageParam =
            SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, addr[i], NULL, NULL, SE_INVALID_LABEL_ID, false);
        ret = SeFreePage(pageMgr, &freePageParam);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

// 测试随机解锁
TEST_F(UtStorageSession, testPushLatch3)
{
    SeRunCtxT *seRunCtx = NULL;
    int32_t ret = SeOpenAttachSessionUt(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &seRunCtx);
    ASSERT_EQ(0, ret);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;

    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr[MAX_LATCH_NUMS];
    PageHeadT *page[MAX_LATCH_NUMS];
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        addr[i] = SE_INVALID_PAGE_ADDR;

        // 获取新页
        ret = SeAllocPage(pageMgr, &allocPageParam, &addr[i]);
        ASSERT_EQ(ret, GMERR_OK);
        // 检验是否能够拿到非法的pageId
        ASSERT_EQ(true, DbIsPageIdValid(addr[i]));

        ret = SeGetPage(pageMgr, addr[i], (uint8_t **)&page[i], ENTER_PAGE_NORMAL, false);
        ASSERT_EQ(ret, GMERR_OK);

        // 对页加锁
        HeapPageLock((SeInstanceT *)(seRunCtx->seIns), resSessionCtx, page[i], addr[i], (i % 2));
    }

    // 测试随机解锁, 对于未被解锁的页将由服务器侧恢复
    srand(time(NULL));
    for (int32_t i = 0; i < MAX_LATCH_NUMS; ++i) {
        int32_t index = rand() % MAX_LATCH_NUMS;
        if (page[index] == NULL) {
            continue;
        }
        HeapPageUnlock(resSessionCtx, page[index], (index % 2));
        page[index] = NULL;
    }

    (void)SeClose(seRunCtx);
}

void DbRWSpinRLockWithSessionStub(
    DbSessionCtxT *sessionCtx, DbLatchT *latch, const ShmemPtrT *latchShmAddr, LatchAddrTypeE latchType)
{
    // 打桩模拟入锁栈后，未真正加锁
    DbSessionLatchPush(sessionCtx, (uint8_t *)latch, latchShmAddr, LATCH_ACQ_READ, latchType);
}

TEST_F(UtStorageSession, testAbnormalExistPushLatchStackWithoutLatch)
{
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    EXPECT_EQ(0, ret);
    DbSessionCtxT *dbSessionCtx = &g_seRunCtx->resSessionCtx;
    dbSessionCtx->latchStack = (LatchStackT *)DbShmPtrToAddr(g_seRunCtx->resSessionCtx.session->shmLatchStack);
    DB_ASSERT(dbSessionCtx->latchStack != NULL);
    dbSessionCtx->latchStack->pid = DbRWlatchGetPid();
    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr = SE_INVALID_PAGE_ADDR;
    PageHeadT *page;
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    // 获取新页
    ret = SeAllocPage(pageMgr, &allocPageParam, &addr);
    ASSERT_EQ(ret, GMERR_OK);
    // 检验是否能够拿到非法的pageId
    ASSERT_EQ(true, DbIsPageIdValid(addr));

    ret = SeGetPage(pageMgr, addr, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(ret, GMERR_OK);

    // 设置为客户端环境，模拟客户端异常退出
    int32_t stubEnvClient = setStubC((void *)DbCommonIsServer, (void *)DbCommonIsServerStub);
    // 入锁栈后未真正加锁
    int32_t stubLatch = setStubC((void *)DbRWSpinRLockWithSession, (void *)DbRWSpinRLockWithSessionStub);
    HeapPageLock(sePtr, &g_seRunCtx->resSessionCtx, page, addr, true);

    clearStub(stubLatch);
    clearStub(stubEnvClient);
    // 服务端加读锁后解锁前，正好在清理客户端锁资源
    DbRWLatchR(&page->lock);
    // 资源回收
    (void)SeClose(g_seRunCtx);
    g_seRunCtx = NULL;
    // v3锁会发生多解锁，打桩函数去掉加锁操作后复现
    DbRWUnlatchR(&page->lock);
}

void DbRWSpinRUnlockWithSessionStub(DbSessionCtxT *sessionCtx, DbLatchT *latch)
{
    // 打桩模拟解锁后未入锁栈
    DB_POINTER3(sessionCtx, sessionCtx->session, latch);
    DbRWUnlatchREnterForClient(latch, sessionCtx->session->id);
    DbRWLatchLeave(latch);
}

TEST_F(UtStorageSession, testAbnormalExistLatchWithoutPopLatchStack)
{
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    EXPECT_EQ(0, ret);
    DbSessionCtxT *dbSessionCtx = &g_seRunCtx->resSessionCtx;
    dbSessionCtx->latchStack = (LatchStackT *)DbShmPtrToAddr(g_seRunCtx->resSessionCtx.session->shmLatchStack);
    DB_ASSERT(dbSessionCtx->latchStack != NULL);
    dbSessionCtx->latchStack->pid = DbRWlatchGetPid();

    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr = SE_INVALID_PAGE_ADDR;
    PageHeadT *page;
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    // 获取新页
    ret = SeAllocPage(pageMgr, &allocPageParam, &addr);
    ASSERT_EQ(ret, GMERR_OK);
    // 检验是否能够拿到非法的pageId
    ASSERT_EQ(true, DbIsPageIdValid(addr));

    ret = SeGetPage(pageMgr, addr, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(ret, GMERR_OK);

    // 设置为客户端环境，模拟客户端异常退出
    int32_t stubEnvClient = setStubC((void *)DbCommonIsServer, (void *)DbCommonIsServerStub);

    HeapPageLock(sePtr, &g_seRunCtx->resSessionCtx, page, addr, true);

    int32_t stubLatch = setStubC((void *)DbRWSpinRUnlockWithSession, (void *)DbRWSpinRUnlockWithSessionStub);
    HeapPageUnlock(&g_seRunCtx->resSessionCtx, page, true);
    clearStub(stubLatch);
    clearStub(stubEnvClient);
    DbRWLatchR(&page->lock);
    // 资源回收
    (void)SeClose(g_seRunCtx);
    // v3锁会发生多解锁，打桩函数去掉出栈操作后复现
    DbRWUnlatchR(&page->lock);
    g_seRunCtx = NULL;
}

bool DbAdptProcessIsExistStub(uint32_t pid)
{
    return false;
}

TEST_F(UtStorageSession, testServerWLatchAndShutdown)
{
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    EXPECT_EQ(0, ret);
    DbSessionCtxT *dbSessionCtx = &g_seRunCtx->resSessionCtx;
    dbSessionCtx->latchStack = (LatchStackT *)DbShmPtrToAddr(g_seRunCtx->resSessionCtx.session->shmLatchStack);
    DB_ASSERT(dbSessionCtx->latchStack != NULL);
    dbSessionCtx->latchStack->pid = DbRWlatchGetPid();
    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr = SE_INVALID_PAGE_ADDR;
    PageHeadT *page;
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    // 获取新页
    ret = SeAllocPage(pageMgr, &allocPageParam, &addr);
    ASSERT_EQ(ret, GMERR_OK);
    // 检验是否能够拿到非法的pageId
    ASSERT_EQ(true, DbIsPageIdValid(addr));

    ret = SeGetPage(pageMgr, addr, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(ret, GMERR_OK);

    // 模拟服务端加写锁后异常退出，此时客户端不能挂死
    DbRWLatchW(&page->lock);
    int32_t stubPidNotExist = setStubC((void *)DbAdptProcessIsExist, (void *)DbAdptProcessIsExistStub);

    // 客户端加读锁
    HeapPageLock(sePtr, &g_seRunCtx->resSessionCtx, page, addr, true);
    HeapPageUnlock(&g_seRunCtx->resSessionCtx, page, true);
    clearStub(stubPidNotExist);
    // 资源回收
    (void)SeClose(g_seRunCtx);
    g_seRunCtx = NULL;
}

TEST_F(UtStorageSession, testServerRLatchAndShutdown)
{
    Status ret = SeOpenWithNewSession(GET_INSTANCE_ID, (DbMemCtxT *)g_seTopDynCtx, &g_seRunCtx);
    EXPECT_EQ(0, ret);
    DbSessionCtxT *dbSessionCtx = &g_seRunCtx->resSessionCtx;
    dbSessionCtx->latchStack = (LatchStackT *)DbShmPtrToAddr(g_seRunCtx->resSessionCtx.session->shmLatchStack);
    DB_ASSERT(dbSessionCtx->latchStack != NULL);
    dbSessionCtx->latchStack->pid = DbRWlatchGetPid();
    SeInstanceT *sePtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageMgrT *pageMgr = (PageMgrT *)sePtr->mdMgr;

    PageIdT addr = SE_INVALID_PAGE_ADDR;
    PageHeadT *page;
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    // 获取新页
    ret = SeAllocPage(pageMgr, &allocPageParam, &addr);
    EXPECT_EQ(true, DbIsPageIdValid(addr));
    ret = SeGetPage(pageMgr, addr, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(ret, GMERR_OK);

    // 模拟服务端加读锁后异常退出
    DbRWLatchR(&page->lock);
    int32_t stubPidNotExist = setStubC((void *)DbAdptProcessIsExist, (void *)DbAdptProcessIsExistStub);

    int32_t stubEnvClient = setStubC((void *)DbCommonIsServer, (void *)DbCommonIsServerStub);

    // 客户端加读锁
    HeapPageLock(sePtr, &g_seRunCtx->resSessionCtx, page, addr, true);
    HeapPageUnlock(&g_seRunCtx->resSessionCtx, page, true);
    clearStub(stubEnvClient);
    clearStub(stubPidNotExist);
    // 资源回收
    (void)SeClose(g_seRunCtx);
    g_seRunCtx = NULL;
}
