#ifndef STARTDBSERVER_H
#define STARTDBSERVER_H
#include "stub.h"
#include "adpt_sleep.h"
#include "db_common_init.h"

#ifdef FEATURE_PERSISTENCE
#define PERSISTENCE_NOT_SUPPORT return
#else
#define PERSISTENCE_NOT_SUPPORT
#endif

#ifndef GET_INSTANCE_ID
#define GET_INSTANCE_ID 1
#endif

extern int32_t CommonInit(const char *configFileName = NULL);
extern void CommonRelease();
extern Status CommonInitDrtInstanceInEuler(bool recovery);
extern AdptEnvironmentE getEnv();
#endif
