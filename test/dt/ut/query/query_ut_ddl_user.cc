#include "query_ut_base.h"
#include "db_text.h"

class UtQueryDDLUser : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        int32_t ret = QrySetCfg("userPolicyMode", "2");
        EXPECT_EQ(ret, GMERR_OK);
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo(dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchBack(old, dyAlgoCtxVertex);
        }
        DbDeleteDynMemCtx(dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

Status QryTestCreateOrDropUser(DrtConnectionT *conn, TextT *userName, TextT *processName, uint32_t opCode)
{
    Status ret;
    MsgHeaderT *msgHeader = NULL;
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    DbMemCtxArgsT args = {0};
    dyAlgoCtxVertex = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    (void)old;
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    if (opCode == MSG_OP_RPC_CREATE_USER) {
        uint16_t userNum = 1;
        EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, userNum));
    }
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, userName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, processName));
    if (opCode == MSG_OP_RPC_CREATE_USER) {
        uint16_t reservedConnNum = 1;
        EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, reservedConnNum));
    }
    uint16_t limit[1][9];
    uint32_t userMetaLimitLen = sizeof(uint16_t) * 9;
    (void)memset_s(limit[0], userMetaLimitLen, 0, userMetaLimitLen);
    ret = FixBufPutData(&req, limit, 9 * sizeof(uint16_t));
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(&req), opCode, req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
    return ret;
}

Status QryTestGrantOrRevokeSysPrivs(
    DrtConnectionT *conn, TextT *userName, TextT *processName, uint32_t opType, uint32_t objType, uint32_t opCode)
{
    Status ret;
    MsgHeaderT *msgHeader = NULL;
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    DbMemCtxArgsT args = {0};
    dyAlgoCtxVertex = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    DB_UNUSED(old);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, userName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, opType));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, objType));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) true));   // touser
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(&req), opCode, req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
    return ret;
}

Status QryTestGrantOrRevokeObjPrivs(DrtConnectionT *conn, TextT *userName, TextT *processName, TextT *objName,
    TextT *namespaceName, uint32_t opType, uint32_t objType, uint32_t opCode)
{
    Status ret;
    MsgHeaderT *msgHeader = NULL;
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    DbMemCtxArgsT args = {0};
    dyAlgoCtxVertex = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    (void)old;
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, userName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, processName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, objName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, namespaceName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, opType));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, objType));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(&req), opCode, req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
    return ret;
}

TEST_F(UtQueryDDLUser, grantOrRevokePrivs)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName = {.len = strlen("user1") + 1, .str = (char *)"user1"};
    TextT processName = {.len = strlen("process1") + 1, .str = (char *)"process1"};
    TextT objName = {.len = strlen("obj1") + 1, .str = (char *)"obj1"};
    TextT namespaceName = {.len = strlen("nsp1") + 1, .str = (char *)"nsp1"};

    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_CREATE_USER);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestGrantOrRevokeSysPrivs(conn, &userName, &processName, 1, 1, MSG_OP_RPC_GRANT_USER_SYSTEM_PRIVS);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestGrantOrRevokeSysPrivs(conn, &userName, &processName, 1, 1, MSG_OP_RPC_REVOKE_USER_SYSTEM_PRIVS);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestGrantOrRevokeObjPrivs(
        conn, &userName, &processName, &objName, &namespaceName, 1, 1, MSG_OP_RPC_GRANT_OBJECT_PRIVS);
    EXPECT_NE(GMERR_OK, ret);
    ret = QryTestGrantOrRevokeObjPrivs(
        conn, &userName, &processName, &objName, &namespaceName, 1, 1, MSG_OP_RPC_REVOKE_OBJECT_PRIVS);
    EXPECT_NE(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLUser, QRY_DDL_CreateUserNameTooLong)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName = {.len = strlen("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa") +
                             1,
        .str = (char *)"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                       "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"};
    TextT processName = {.len = strlen("process1") + 1, .str = (char *)"process1"};

    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_CREATE_USER);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLUser, QRY_DDL_CreateUserProcessNameTooLong)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName = {.len = strlen("user2") + 1, .str = (char *)"user2"};
    TextT processName = {.len = strlen("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                       "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa") +
                                1,
        .str = (char *)"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                       "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"};

    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_CREATE_USER);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);

    QryTestReleaseSession(conn);
}

Status QryInitUserAndGroupWithoutPrivsStub(SessionT *session, SessionParamT *param)
{
    (void)param;
    errno_t rc = strcpy_s(session->externalUser.dbUserName, sizeof(session->externalUser.dbUserName), "testUser");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    rc = strcpy_s(session->externalUser.dbProcessName, sizeof(session->externalUser.dbProcessName), "testProc");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

TEST_F(UtQueryDDLUser, QRY_DDL_CreateUserWithoutPrivs)
{
    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupWithoutPrivsStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)QryTestDrtConnWritePackSuccessStub);

    uint32_t ret;
    DrtConnectionT *conn = NULL;

    ret = (Status)QrySetCfg("userPolicyMode", "1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestAllocSessionWithoutStub(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = (Status)QrySetCfg("userPolicyMode", "2");
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName = {.len = strlen("user2") + 1, .str = (char *)"user2"};
    TextT processName = {.len = strlen("process2") + 1, .str = (char *)"process2"};

    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_CREATE_USER);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    QryTestReleaseSessionWithoutStub(conn);
}

TEST_F(UtQueryDDLUser, QRY_DDL_DropUserWithoutPrivs)
{
    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupWithoutPrivsStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)QryTestDrtConnWritePackSuccessStub);

    Status ret;
    DrtConnectionT *conn = NULL;

    ret = (Status)QrySetCfg("userPolicyMode", "1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestAllocSessionWithoutStub(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = (Status)QrySetCfg("userPolicyMode", "2");
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName = {.len = strlen("user2") + 1, .str = (char *)"user2"};
    TextT processName = {.len = strlen("process2") + 1, .str = (char *)"process2"};

    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_DROP_USER);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    QryTestReleaseSessionWithoutStub(conn);
}

TEST_F(UtQueryDDLUser, QRY_DDL_DropUser)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName = {.len = strlen("user1") + 1, .str = (char *)"user1"};
    TextT processName = {.len = strlen("process1") + 1, .str = (char *)"process1"};
    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_CREATE_USER);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCreateOrDropUser(conn, &userName, &processName, MSG_OP_RPC_DROP_USER);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

Status QryTestCreateUserBatch(
    DrtConnectionT *conn, TextT *userName, TextT *processName, uint16_t *connNum, uint16_t userNum, uint32_t opCode)
{
    Status ret;
    MsgHeaderT *msgHeader = NULL;
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    DbMemCtxArgsT args = {0};
    dyAlgoCtxVertex = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    DB_UNUSED(old);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    uint16_t limit[1][9];
    (void)memset_s(limit[0], 9 * sizeof(uint16_t), 0, 9 * sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, userNum));
    for (uint16_t i = 0; i < userNum; i++) {
        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &(userName[i])));
        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &(processName[i])));
        EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, connNum[i]));
        ret = FixBufPutData(&req, limit, 9 * sizeof(uint16_t));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(&req), opCode, req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
    return ret;
}

TEST_F(UtQueryDDLUser, QRY_DDL_Create2User)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    TextT userName[2] = {
        {.len = strlen("user1") + 1, .str = (char *)"user1"}, {.len = strlen("user2") + 1, .str = (char *)"user2"}};
    TextT processName[2] = {{.len = strlen("process1") + 1, .str = (char *)"process1"},
        {.len = strlen("process2") + 1, .str = (char *)"process2"}};
    uint16_t connNum[2] = {1, 2};
    ret = QryTestCreateUserBatch(conn, userName, processName, connNum, 2, MSG_OP_RPC_CREATE_USER);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}
