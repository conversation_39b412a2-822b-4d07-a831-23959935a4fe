/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: datalog analyzer ut
 * Author: wangxu
 * Create: 2022-07-18
 */

#include "gtest/gtest.h"
#include "adpt_atomic.h"
#include "db_list.h"
#include "db_mem_context.h"
#include "dm_cache_label_map.h"
#include "dm_meta_basic.h"
#include "query_ut_base.h"
#include "ut_catalog_base.h"
#include "cpl_dtl_offline_compiler.h"
#include "cpl_optimizer.h"
#include "cpl_ir_explain.h"
#include "cpl_ir_func_op.h"
#include "cpl_ir_item_op.h"
#include "cpl_ir_logical_op.h"
#include "cpl_ir_op.h"
#include "cpl_ir_plan.h"
#include "query_ut_base.h"
#include "ut_catalog_base.h"

using namespace std;

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

DbMemCtxT *analyzerMemCtx = NULL;
static DbMemCtxT *oldMemCtx = NULL;

class DtlAnalyzerUt : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        analyzerMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        oldMemCtx = DbMemCtxSwitchTo((DbMemCtxT *)analyzerMemCtx);

        // 因为所有的元数据创建和删除都需要维护所属namespace的labelCount,同时ut中元数据的namespaceId设置都为0,
        // 因此在测试环境的初始化中创建了id为0的namespace
        DmNamespaceT *namespaceInfo = CreateNspWithId(0, (DbMemCtxT *)analyzerMemCtx);
        EXPECT_EQ(GMERR_OK, CataCreateNamespace(namespaceInfo, NULL));
    }
    static void TearDownTestCase()
    {
        DbMemCtxSwitchBack(oldMemCtx, analyzerMemCtx);
        DbDeleteDynMemCtx((DbMemCtxT *)analyzerMemCtx);
        BaseUninit();
    };

    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

void DtlAnalyzerOutputValidTest(const char *input, Status expectedStat1, bool isValid)
{
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, input, &program);
    ASSERT_EQ(GMERR_OK, ret);

    ret = DtlVerify((DbMemCtxT *)analyzerMemCtx, program);
    ASSERT_EQ(expectedStat1, ret);

    DbListT *plans = NULL;
    ret = DtlTransAst2IR(program, &plans);
    ASSERT_EQ(expectedStat1, ret);

    for (uint32_t i = 0; i < plans->count; i++) {
        IRPlanT *curPlan = (IRPlanT *)DbListItem(plans, i);
        if (curPlan->root->op->type == IR_FUNCOP_AGG_UDF) {
            continue;
        }

        if (curPlan->root->children[0]->op->type == IR_LOGOP_EXTEND_EXTRACT) {
            OpLogicalAAExtendExtractT *extractOp = (OpLogicalAAExtendExtractT *)curPlan->root->children[0]->op;
            AAVertexDescT *propAA = &extractOp->schema.propAA;
            for (uint32_t j = 0; j < propAA->propNum; j++) {
                DmPropertySchemaT *prop = propAA->properties[j];
                ASSERT_EQ(prop->isValid, isValid);
            }
        }
    }
}

void DtlCompilerTest(const char *input, Status expectedStat1, Status expectedStat2)
{
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, input, &program);
    ASSERT_EQ(GMERR_OK, ret);

    ret = DtlVerify((DbMemCtxT *)analyzerMemCtx, program);
    ASSERT_EQ(expectedStat1, ret);

    DbListT *plans = NULL;
    ret = DtlTransAst2IR(program, &plans);
    ASSERT_EQ(expectedStat2, ret);

    DbListT deltaPlans;
    ret = DtlQryRewrite(analyzerMemCtx, program, plans, &deltaPlans);
    ASSERT_EQ(expectedStat2, ret);
}

void DtlRewriterTest(const char *input, Status expectedStat1, Status expectedStat2)
{
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, input, &program);
    ASSERT_EQ(GMERR_OK, ret);

    ret = DtlVerify((DbMemCtxT *)analyzerMemCtx, program);
    ASSERT_EQ(expectedStat1, ret);

    DbListT *plans = NULL;
    ret = DtlTransAst2IR(program, &plans);
    ASSERT_EQ(expectedStat1, ret);

    DbListT deltaPlans;
    ret = DtlQryRewrite(analyzerMemCtx, program, plans, &deltaPlans);
    ASSERT_EQ(expectedStat2, ret);
}

void DtlVerifyTest(const char *input, Status expectedStat1, Status expectedStat2)
{
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, input, &program);
    ASSERT_EQ(expectedStat1, ret);

    ret = DtlVerify((DbMemCtxT *)analyzerMemCtx, program);
    ASSERT_EQ(expectedStat2, ret);
}

void DtlParserTest(const char *input, Status expectedStat)
{
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, input, &program);
    ASSERT_EQ(expectedStat, ret);
}

// 定位使用
TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithTmp)
{
    constexpr auto valid0 = R"(
        //中文
        %table A(a: int4, b: int1, c: int4)
        %table B(a: int4, b: int1, c: int4)
        %function func1(哈a: int1, b: int1)
        %function func4(@a: int4, b: int4)
        B(a, b, c) :- A(a, b, c), func1(b, 0), func4(c, 0).
    )";
    DtlParserTest(valid0, GMERR_SYNTAX_ERROR);

    constexpr auto validProgram = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }
        %aggregate funcA(a: int4 -> b: int4)

        rsc0(a, c, -) :- A(a, b) GROUP-BY(a) funcA(b, c).
        B(a, c, d) :- rsc0(a, b, c) GROUP-BY(a) funcA(b, d).
    )";
    DtlParserTest(validProgram, GMERR_OK);

    // 异常，状态机规则左表为null(0)
    constexpr auto valid55 = R"(
        %table trigger(k: int4, p: int4) {index(0(k))}
        %table state1(k: int4, s: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        null(0) :- trigger(k, p), transfer(p, s).
        state1(k, s) :- trigger(k, p), transfer(p, s).
        state2(k, s) :- state1(k, p), transfer(p, s).
        null(0) :- state1(k, p), transfer(p, s).
        null(0) :- state2(k, s).
    )";
    DtlVerifyTest(valid55, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态机规则左表出现重复字段
    constexpr auto valid56 = R"(
        %table state(a:int4, b:int4, p:int4, q:int4) { index(0(a, b)), state }
        %table trigger(a:int4, b:int4, c:int4, d:int4) { index(0(a, b)), transient(tuple)}
        %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
        %table out(a:int4, b:int4, c:int4, d:int4) { index(0(a)) }

        state(a, b, e, b) :- trigger(a, b, c, d), transfer(c, d, e, b).
        out(a, b, c, d) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid56, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，主键包含全部字段
    constexpr auto valid57 = R"(
        %table trigger(k: int4, s: int4) { index(0(k)) }
        %function transfer(p: int4) { state_transfer }
        %table state(k: int4) { index(0(k)), state }
        state(k) :- trigger(k, p), transfer(p).
        null(0) :- state(k).
    )";
    DtlVerifyTest(valid57, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithState)
{
    constexpr auto valid01 = R"(
        %table state(a:str, b:byte4, c:int4, d:int4) { index(0(a, b)), state }
        %table trigger(a:str, b:byte4, c:int4) { index(0(a, b)) }
        %function transfer(c:int4 -> d:int4, e:int4) { state_transfer, access_delta(trigger), access_current(state) }
        %table out(a:str, b:byte4, c:int4, d:int4) {index(0(a))}

        state(a, b, d, e) :- trigger(a, b, c), transfer(c, d, e).
        out(a, b, c, d) :- state(a, b, c, d).
    )";
    DtlRewriterTest(valid01, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // state join
    constexpr auto valid00 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4)
        %table out(a: int4, b: int4, c: int4, d: int4) {index(0(a))}
        out(a, b, e, f) :- state(a, b, c, d), transfer(c, d, e, f).
    )";
    DtlVerifyTest(valid00, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 正常
    constexpr auto valid0 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
        null(0) :- trigger(k, s).
    )";
    DtlCompilerTest(valid0, GMERR_OK, GMERR_OK);

    // 状态表为非中间表
    constexpr auto valid2 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, p), transfer(p, s).
    )";
    DtlVerifyTest(valid2, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态表没有指定主键
    constexpr auto valid3 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { state }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid3, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态表主键包含全部字段
    constexpr auto valid4 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k, s)), state }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid4, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态表不支持update
    constexpr auto valid5 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state, update }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid5, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态表和触发表的主键字段不一致
    constexpr auto valid6 = R"(
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        %table trigger(k: int4, p: int4) { transient(tuple), index(0(k, p)) }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid6, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态转移函数没有state_transfer
    constexpr auto valid7 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) {}
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid7, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态转移函数不包含状态表和触发表除主键字段外的所有字段
    constexpr auto valid8 = R"(
        %table trigger(k: int4, p: int4, t: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4, t: int4) { index(0(k)), state }
        state(k, s, t) :- trigger(k, p, t), transfer(p, s).
        null(0) :- state(k, s, t).
    )";
    DtlVerifyTest(valid8, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态机规则左表为非状态表
    constexpr auto valid9 = R"(
        %table trigger(k: int4, p: int4) { transient(tuple), index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k))}
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid9, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态机规则右表有多个触发表
    constexpr auto valid10 = R"(
        %table trigger1(k: int4, p: int4) { transient(tuple), index(0(k)) }
        %table trigger2(k: int4, p: int4) { transient(tuple), index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger1(k, p), trigger2(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid10, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态表不能为输入表
    constexpr auto valid11 = R"(
        %table state1(k: int4, p: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        state2(k, s) :- state1(k, p), transfer(p, s).
        null(0) :- state2(k, s).
    )";
    DtlVerifyTest(valid11, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态机规则右侧不包含状态转移函数
    constexpr auto valid12 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { }
        %table state(k: int4, s: int4) { index(0(k)) }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid12, GMERR_OK, GMERR_OK);

    // 状态机规则中出现常量或忽略字段
    constexpr auto valid13 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, 1), transfer(1, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid13, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态机规则左表出现重复字段
    constexpr auto valid14 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4, t: int4) { index(0(k)), state }
        state(k, s, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s, t).
    )";
    DtlVerifyTest(valid14, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 状态转移函数内支持读写其它表
    constexpr auto valid15 = R"(
        %table input(k: int4, p: int4)
        %table output(k: int4, p: int4)
        %table trigger(k: int4, p: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer, access_delta(input), access_current(output) }
        %table state(k: int4, s: int4) { index(0(k)), state }

        output(k, p) :- input(k, p).
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid15, GMERR_OK, GMERR_OK);

    // 其它udf读状态表增量表
    constexpr auto valid16 = R"(
        %table inpA(a:int4, b:int4)
        %table trigger(a:int4, b:int4){index(0(a))}
        %table state(k: int4, s: int4) { index(0(k)), state }
        %function tupleFilter(a:int4 -> b:int4) { access_delta(state)}
        %function transfer(p: int4 -> s: int4) { state_transfer }

        trigger(a, b) :- inpA(a, b), tupleFilter(a, b).
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid16, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 其它udf读状态表全量表
    constexpr auto valid17 = R"(
        %table inpA(a:int4, b:int4)
        %table trigger(a:int4, b:int4){index(0(a))}
        %table state(k: int4, s: int4) { index(0(k)), state }
        %function tupleFilter(a:int4 -> b:int4) { access_current(state)}
        %function transfer(p: int4 -> s: int4) { state_transfer }

        trigger(a, b) :- inpA(a, b), tupleFilter(a, b).
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid17, GMERR_OK, GMERR_OK);

    // 使用%precedence指定状态表顺序
    constexpr auto valid18 = R"(
        %table inpA(a:int4, b:int4)
        %table trigger(a:int4, b:int4){index(0(a))}
        %table state(k: int4, s: int4) { index(0(k)), state }
        %function tupleFilter(a:int4 -> b:int4) { access_current(state)}
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %precedence state, trigger
        trigger(a, b) :- inpA(a, b), tupleFilter(a, b).
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid18, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // state不支持聚合
    constexpr auto valid19 = R"(
        %table A(k: int4, p: int4, v: int4){index(0(k, p))}
        %table state(k: int4, p: int4, max: int4, min: int4){index(0(k, p)), state}
        %aggregate min_max(v: int4 -> min: int4, max: int4){ ordered }
        state(k, p, max, min) :- A(k, p, v) GROUP-BY (k, p) min_max(v, min, max).
        null(0) :- state(k, p, max, min).
    )";
    DtlVerifyTest(valid19, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // state必须为中间表
    constexpr auto valid20 = R"(
        %table state1(k: int4, p: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        state2(k, s) :- state1(k, p), transfer(p, s).
        null(0) :- state2(k, s).
    )";
    DtlVerifyTest(valid20, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 语法格式错误
    constexpr auto valid21 = R"(
        %table trigger(k: int4, p: int4) { index(0(k))}
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid21, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 正常，表来自不同的命名空间
    constexpr auto valid23 = R"(
        namespace nsA {
            %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
            %readwrite state
        }
        namespace nsB {
            %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
            %function transfer(c: int4, d: int4 -> e: int4, f: int4) { state_transfer }
            nsA.state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
        }
        namespace nsC {
            %table out(a: int4, b: int4, c: int4, d: int4)
            out(a, b, c, d) :- nsA.state(a, b, c, d).
        }
    )";
    DtlVerifyTest(valid23, GMERR_OK, GMERR_OK);

    // 正常，状态表为触发表
    constexpr auto valid24 = R"(
        %table trigger(k: int4, p: int4) {index(0(k))}
        %table state1(k: int4, s: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        state1(k, s) :- trigger(k, p), transfer(p, s).
        state2(k, s) :- state1(k, p), transfer(p, s).
        null(0) :- state2(k, s).
    )";
    DtlVerifyTest(valid24, GMERR_OK, GMERR_OK);

    // 异常，状态表为输入表（直接投影）
    constexpr auto valid25 = R"(
        %table trigger(k: int4, p: int4) {index(0(k))}
        %table state(k: int4, s: int4) { state, index(0(k)) }
        trigger(k, p) :- state(k, p).
    )";
    DtlVerifyTest(valid25, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态表为输入表（直接作为触发表）
    constexpr auto valid26 = R"(
        %table state1(k: int4, p: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        %table out(a: int4, b: int4)
        state2(k, s) :- state1(k, p), transfer(p, s).
        out(a, b) :- state2(a, b).
    )";
    DtlVerifyTest(valid26, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态表为输入表（参与join）
    constexpr auto valid27 = R"(
        %table state(k: int4, p: int4) { state, index(0(k)) }
        %table inp(p: int4, s: int4)
        %table out(k: int4, s: int4) { index(0(k))}
        out(k, s) :- state(k, p), inp(p, s).
    )";
    DtlVerifyTest(valid27, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，触发表为resource
    constexpr auto valid28 = R"(
        %table inpA(a: int4)
        %resource trigger(k: int4 -> p: int4) { sequential(max_size(3)) }
        %table outB(a: int4, b: int4)
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { state, index(0(k)) }
        trigger(a, -) :- inpA(a).
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
        outB(a, b) :- trigger(a, b).
    )";
    DtlVerifyTest(valid28, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态机规则有忽略字段
    constexpr auto valid29 = R"(
        %table trigger(k: int4, p: int4, s: int4) { index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, p, -), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid29, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 正常，触发表为update_partial
    constexpr auto valid30 = R"(
        %table trigger(k: int4, p: int4) { index(0(k)), update_partial }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        state(k, s) :- trigger(k, p), transfer(p, s).
        null(0) :- state(k, s).
    )";
    DtlVerifyTest(valid30, GMERR_OK, GMERR_OK);

    // 异常，触发表显式定义包含全部字段主键索引
    constexpr auto valid41 = R"(
        %table trigger(k: int4, s: int4, p:int4) { index(0(k, s)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k, s)), state }
        %table out(a:int4, b:int4)
        state(k, s) :- trigger(k, s, p), transfer(p, s).
        out(k, s) :- state(k, s).
    )";
    DtlVerifyTest(valid41, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，规则右侧包含多个状态转移函数
    constexpr auto valid42 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), transient(tuple) }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
        %table out(a: int4, b: int4, c: int4, d: int4)
        state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, g, h), transfer(g, h, e, f).
        out(a, b, c, d) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid42, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，其他表直接投影给状态表
    constexpr auto valid43 = R"(
        %table trigger(k: int4, p: int4) { index(0(k))}
        %table state(k: int4, s: int4) { index(0(k)), state }
        %table out(a: int4, b: int4)
        state(k, s) :- trigger(k, s).
        out(k, s) :- state(k, s).
    )";
    DtlVerifyTest(valid43, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，主键顺序不对应
    constexpr auto valid44 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
        state(a, b, e, f) :- trigger(b, a, c, d), transfer(c, d, e, f).
        null(0) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid44, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，触发字段顺序不对应
    constexpr auto valid45 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
        state(a, b, e, f) :- trigger(a, b, d, c), transfer(c, d, e, f).
        null(0) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid45, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态字段顺序不对应
    constexpr auto valid46 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
        state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, f, e).
        null(0) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid46, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，入参包含主键字段
    constexpr auto valid47 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4, g:int4) { state_transfer }
        state(a, b, e, f) :- trigger(a, b, c, d), transfer(b, d, c, e, f).
        null(0) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid47, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态表与触发表来自不同的命名空间，且规则错误
    constexpr auto valid48 = R"(
        namespace nsA {
            %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
            %readwrite state
        }
        namespace nsB {
            %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
            %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
            nsA.state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
            // null(0) :- nsA.state(a, b, c, d).
        }
    )";
    DtlVerifyTest(valid48, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态转移函数内有其他关键字
    constexpr auto valid49 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), transient(tuple) }
        %function transfer(c:int4, d:int4 -> e:int4, f:int4){ state_transfer, ordered }
        state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
        null(0) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid49, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 正常，字段名为关键字名
    constexpr auto valid50 = R"(
        %table trigger(state: int4, s: int4) { index(0(state)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        %table out(a:int4, b:int4)
        state(state, s) :- trigger(state, p), transfer(p, s).
        out(k, s) :- state(k, s).
    )";
    DtlVerifyTest(valid50, GMERR_OK, GMERR_OK);

    // 正常，字段名为关键字名
    constexpr auto valid51 = R"(
        %table trigger(state: int4, s: int4) { index(0(state)) }
        %function init(p: int4 -> s: int4) { state_transfer }
        %table state(k: int4, s: int4) { index(0(k)), state }
        %table out(a:int4, b:int4)
        state(state, s) :- trigger(state, p), init(p, s).
        out(k, s) :- state(k, s).
    )";
    DtlVerifyTest(valid51, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，指定为readonly的状态表在另一个命名空间里作为左表
    constexpr auto valid52 = R"(
        namespace nsA {
            %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
            %readonly state
        }
        namespace nsB {
            %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
            %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
            nsA.state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
        }
        namespace nsC {
            %table out(a: int4, b: int4, c: int4, d: int4)
            out(a, b, c, d) :- nsA.state(a, b, c, d).
        }
    )";
    DtlVerifyTest(valid52, GMERR_OK, GMERR_NO_DATA);

    // 异常，指定为readonly的状态表在另一个命名空间里作为右表
    constexpr auto valid53 = R"(
        namespace nsA {
            %table inp(a: int4, b: int4)
            %table out(a: int4, b: int4)
            out(a, b) :- inp(a, b), nsB.state(a, b, -, -).
        }
        namespace nsB {
            %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)), state }
            %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b)) }
            %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
            %table out(a: int4, b: int4, c: int4, d: int4)
            %readonly state
            state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
            out(a, b, c, d) :- state(a, b, c, d).
        }
    )";
    DtlVerifyTest(valid53, GMERR_OK, GMERR_OK);

    // 异常，函数与表同名
    constexpr auto valid54 = R"(
        %table state(a: int4, b: int4, c: int4, d: int4) { index(0(a, b, c)), state }
        %table trigger(a: int4, b: int4, c: int4, d: int4) { index(0(a, b, c)), transient(tuple) }
        %function state(d:int4 -> e:int4){ state_transfer, ordered }
        state(a, b, c, e) :- trigger(a, b, c, d), state(d, e).
        null(0) :- state(a, b, c, d).
    )";
    DtlVerifyTest(valid54, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态机规则左表为null(0)
    constexpr auto valid55 = R"(
        %table trigger(k: int4, p: int4) {index(0(k))}
        %table state1(k: int4, s: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        null(0) :- trigger(k, p), transfer(p, s).
        state1(k, s) :- trigger(k, p), transfer(p, s).
        state2(k, s) :- state1(k, p), transfer(p, s).
        null(0) :- state1(k, p), transfer(p, s).
        null(0) :- state2(k, s).
    )";
    DtlVerifyTest(valid55, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，状态机规则左表为null(0)
    constexpr auto valid56 = R"(
        %table trigger(k: int4, p: int4) {index(0(k))}
        %table state1(k: int4, s: int4) { state, index(0(k)) }
        %function transfer(p: int4 -> s: int4) { state_transfer }
        %table state2(k: int4, s: int4) { index(0(k)), state }
        state1(k, s) :- trigger(k, p), transfer(p, s).
        state2(k, s) :- state1(k, p), transfer(p, s).
        null(0) :- state1(k, p), transfer(p, s).
        null(0) :- state2(k, s).
    )";
    DtlVerifyTest(valid56, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 正常，其它规则左表为null(0)
    constexpr auto valid57 = R"(
        %table trigger(k: int4, p: int4) {index(0(k))}
        %function transfer(p: int4 -> s: int4)
        null(0) :- trigger(k, p), transfer(p, s).
    )";
    DtlVerifyTest(valid57, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithAggConst)
{
    // groupBy出现与input中相同值常量
    constexpr auto valid00 = R"(
        %table A(k: int4, p: str, v: int4)
        %table B(max: int4, min: int4, k: int4, p: str)
        %aggregate min_max(v: int4 -> min: int4, max: int4) {
            ordered
        }

        B(max, min, k, p) :- A(k, "p", v) GROUP-BY (k, "p") min_max(v, min, max).
        null(0) :- B(max, min, k, p).
    )";
    DtlVerifyTest(valid00, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 输出表有多个相同变量
    constexpr auto valid01 = R"(
        %table A1(a: int4, b: int4, c: int4, d: int4, e: int4)
        %table B1(a: int4, b: int4, c: int4, d: int4, e: int4)
        %aggregate expand(k: int4, v: int4 -> min: int4, max: int4) {
            ordered
        }
        B1(a, d, e, 10, a) :- A1(a, b, c, 1, -) GROUP-BY (a) expand(b, c, d, e).
        null(0) :- B1(a, b, c, d, e).
    )";
    DtlVerifyTest(valid01, GMERR_OK, GMERR_SEMANTIC_ERROR);

    constexpr auto valid0 = R"(
        %table I(a: str, b: byte4, c: int4, d: int4, e: int4)
        %table J(max: int4, min: int4, c: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4) {
            ordered
        }
        J(max, min, c) :- I("string1", "0x01010101", c, d, 1) GROUP-BY (c) min_max(d, min, max).
        null(0) :- J(max, min, c).
    )";
    DtlCompilerTest(valid0, GMERR_OK, GMERR_OK);

    // 聚合规则输出表和输入表都包含常量
    constexpr auto valid1 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4, v: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1, 1).
        out(min, max, k, 1) :- inp(k, p, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlCompilerTest(valid1, GMERR_OK, GMERR_OK);

    // 输出表v字段必须在groupby和聚合函数中出现
    constexpr auto valid2 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4, v: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1, 1).
        out(max, min, k, v) :- inp(k, p, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid2, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 输出表v字段必须在groupby和聚合函数中出现
    constexpr auto valid3 = R"(
        %table inp(k: int4, p: int4, v: int4, m: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4, v: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1, 1).
        out(min, max, k, v) :- inp(k, p, v, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid3, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 输入表中对应聚合函数入参不支持常量
    constexpr auto valid4 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(max, min, k) :- inp(k, 1, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid4, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 输入表中对应聚合函数入参不支持常量
    constexpr auto valid5 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(min, max, k) :- inp(k, 1, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid5, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 输入表中对应groupby字段不支持常量
    constexpr auto valid6 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4, v: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1, 1).
        out(min, max, k, 1) :- inp(1, p, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid6, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // k和min、max必须出现在输出表
    constexpr auto valid7 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(1, 1, 1):-inp(k, p, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid7, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // k和min、max必须出现在输出表
    constexpr auto valid8 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(k, min, 1):-inp(k, p, 1) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid8, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 输出表部分为常量
    constexpr auto valid9 = R"(
        %table inp(k:int4, p:int4, v:int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4, m: int4, n: int4)
        %aggregate min_max(v:int4 -> min:int4, max:int4)
        inp1(k, v) :- out(k, v, 1, 1, 1).
        out(k, min, max, 1, 1):-inp(k, p, -) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlCompilerTest(valid9, GMERR_OK, GMERR_OK);

    // 聚合函数不支持常量
    constexpr auto valid10 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(max, min, k):-inp(k, p, v) GROUP-BY(k) min_max(1, min, max).
    )";
    DtlVerifyTest(valid10, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // groupby字段不支持常量
    constexpr auto valid11 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(max, min, k):-inp(k, p, v) GROUP-BY(1) min_max(p, min, max).
    )";
    DtlVerifyTest(valid11, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 不包含变量
    constexpr auto valid12 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1).
        out(max, min, k):-inp(k, p, -) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlCompilerTest(valid12, GMERR_OK, GMERR_OK);

    // 输出表有忽略字段
    constexpr auto valid13 = R"(
        %table inp(k: int4, p: int4, v: int4)
        %table inp1(k: int4, v: int4)
        %table out(max: int4, min: int4, k: int4, v: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4)
        inp1(k, v) :- out(k, v, 1, 1).
        out(max, min, k, -):-inp(k, p, -) GROUP-BY(k) min_max(p, min, max).
    )";
    DtlVerifyTest(valid13, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// 综合测试
TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithComplex)
{
    constexpr auto valid1 = R"(
        %table inp00(a: int4, b: int4, c: int4, d: int4)
        %table inp3(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        inp00(1, 1, a, b):- inp3(a, b), NOT inp1(1, b).
    )";
    DtlCompilerTest(valid1, GMERR_OK, GMERR_OK);

    constexpr auto valid2 = R"(
        namespace ns3 {
            %table B(a: int4, b: int4, c: int4)
            %table C(a: int4, b: int4, c: int4)
            %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }

            rsc0(a, 1, -) :- C(a, 1, -).
            B(a, c, d) :- rsc0(a, d, c).
        }
    )";
    DtlCompilerTest(valid2, GMERR_OK, GMERR_OK);

    constexpr auto valid3 = R"(
        %table out1(a:int4, b:int4, c:int4, d:str)
        %table out2(a: int4, b: int4, c: int4, d: int4)
        %table A(a:int4)
        %table B(int1:int1, int2:int2, byte2:byte2, str:str, int4:int4, int8:int8)
        %table C(f1:int4, f2:int4, f3:int4, f4:int4, f5:int4, f6:int4, f7:int4, f8:int4, f9:int4, f10:int4,
        f11:int4, f12:int4, f13:int4, f14:int4, f15:int4, f16:int4, f17:int4, f18:int4, f19:int4, f20:int4,
        f21:int4, f22:int4, f23:int4, f24:int4, f25:int4, f26:int4, f27:int4, f28:int4, f29:int4, f30:int4,
        f31:int4)

        %table D0(a:int4, b:int4)
        %table D1(a:int4, b:int4)
        %table D2(a:int4, b:int4)
        %table D3(a:int4, b:int4)
        %table D4(a:int4, b:int4)
        %table D5(a:int4, b:int4)
        %table D6(a:int4, b:int4)
        %table D7(a:int4, b:int4)
        %table D8(a:int4, b:int4)
        {
            index(0(a, b)),
            index(1(a))
        }
        %table D9(a:int4, b:int4)
        %table D10(a:int4, b:int4)
        %table D11(a:int4, b:int4)
        %table D12(a:int4, b:int4)
        {
            index(0(a, b)),
            index(1(a))
        }
        %table D13(a:int4, b:int4)
        %table D14(a:int4, b:int4)
        %table D15(a:int4, b:int4)
        %table D16(a:int4, b:int4)

        B(-128, -32768, "EEEE", "Negative", -2147483648, -9223372036854775808):-A(-).
        B(127, 32767, "EEEE", "Positive", 2147483647, 9223372036854775807):-A(-).
        B(0, 0, "EEEE", "", 0, 0):-A(-).
        C(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
        26, 27, 28, 29, 30, 31):-A(-).

        D1(1, 2):-A(-).
        D2(2, 3):-A(-).
        D3(3, 4):-A(-).
        D4(4, 5):-A(-).
        D5(5, 6):-A(-).
        D6(6, 7):-A(-).
        D7(7, 8):-A(-).
        D8(8, 9):-A(-).
        D9(9, 10):-A(-).
        D10(10, 11):-A(-).
        D11(11, 12):-A(-).
        D12(12, 13):-A(-).
        D13(13, 14):-A(-).
        D14(14, 15):-A(-).
        D15(15, 16):-A(-).
        D16(16, 17):-A(-).

        out1(0, 0, 0, d):-B(-128, -32768, "EEEE", d, -2147483648, -9223372036854775808).
        out1(0, 0, 0, d):-B(127, 32767, "EEEE", d, 2147483647, 9223372036854775807).
        out1(0, 0, 0, "empty string filter"):-B(0, 0, "EEEE", "", 0, 0).
        out1(0, 0, 0, "D0"):-D1(1, 2).
        out1(0, 0, a, "D1"):-D1(1, a).
        out1(f1, f2, f8, "JOIN"):-D0(f1, f2), D1(f2, f3), D2(f3, f4), D3(f4, f5), D4(f5, f6), D5(f6, f7), D6(f7, f8).
        // 最多16
        out2(f1, f2, f11, f17):-D1(f1, f2), D2(f2, f3), D3(f3, f4), D4(f4, f5), D5(f5, f6), D6(f6, f7), D7(f7, f8),
        D8(f8, f9), D9(f9, f10), D10(f10, f11), D11(f11, f12), D12(f12, f13), D13(f13, f14), D14(f14, f15),
        D15(f15, f16), D16(f16, f17).
    )";
    DtlCompilerTest(valid3, GMERR_OK, GMERR_OK);

    constexpr auto valid4 = R"(
        // 1.project
        namespace ns1 {
            %table A(a: int4)
            %table B(a: int4, b: int4)
            %resource rsc0(a: int4 -> b: int4) { sequential(max_size(10)) }

            rsc0(a, -) :- A(a).
            B(a, b) :- rsc0(a, b).
        }

        // 2.not join
        namespace ns2 {
            %table A(a: int4, b: int4)
            %table B(a: int4, b: int4)
            %table C(a: int4, b: int4)
            %resource rsc0(a: int4 -> b: int4) { sequential(max_size(10)) }

            rsc0(a, -) :- A(a, b), NOT B(a, b).
            C(a, b) :- A(a, b), NOT rsc0(a, b).
        }

        // 4.join table
        namespace ns4 {
            %table A(a: int4, b: int4)
            %table B(a: int4, b: int4)
            %table C(a: int4, b: int4)
            %table D(a: int4, b: int4, c: int4)
            %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }

            rsc0(a, b, -) :- A(a, b), B(a, b), C(a, b).
            D(a, b, c) :- A(a, b), B(a, b), rsc0(a, b, c).
        }

        // 5.join transient
        namespace ns5 {
            %table A(a: int4, b: int4){ transient(tuple) }
            %table B(a: int4, b: int4)
            %table C(a: int4, b: int4)
            %table D(a: int4, b: int4, c: int4)
            %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }

            rsc0(a, b, -) :- A(a, b), B(a, b), C(a, b).
            D(a, b, c) :- A(a, b), B(a, b), rsc0(a, b, c).
        }

        // 6.join function
        namespace ns6 {
            %table A(a: int4, b: int4)
            %table B(a: int4, b: int4, c: int4)
            %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }
            %function func(a: int4 -> b: int4)

            rsc0(a, c, -) :- A(a, b), func(b, c).
            B(a, b, d) :- rsc0(a, b, c), func(c, d).
        }
    )";
    DtlCompilerTest(valid4, GMERR_OK, GMERR_OK);

    constexpr auto valid5 = R"(
        namespace ns1 {
            %table A000(a:int4) 
            %table A001(a:int4) 
            %table A002(a:int4) 
            %table A003(a:int4) 
            %table A004(a:int4)
           
            %table A005(a:int4)
            %table A006(a:int4)
            %table A007(a:int4)
            %table A008(a:int4)
            %table A009(a:int4)
            A000(a) :- A001(a), A002(-), A003(-), A004(-), A005(-), A006(-), A007(-), A008(-), A009(-).

            %table A010(a:int4)
            %table A011(a:int4)
            %table A012(a:int4)
            %table A013(a:int4)
            %table A014(a:int4)
                       
            %table A015(a:int4)
            %table A016(a:int4)
            %table A017(a:int4)
            %table A018(a:int4)
            %table A019(a:int4)
            A010(a) :- A011(a), A012(-), A013(-), A014(-), A015(-), A016(-), A017(-), A018(-), A019(-).
            
            %table A020(a:int4)
            %table A021(a:int4)
            %table A022(a:int4)
            %table A023(a:int4)
            %table A024(a:int4)
                       
            %table A025(a:int4)
            %table A026(a:int4)
            %table A027(a:int4)
            %table A028(a:int4)
            %table A029(a:int4)
            A020(a) :- A021(a), A022(-), A023(-), A024(-), A025(-), A026(-), A027(-), A028(-), A029(-).
                       
            %table A030(a:int4)
            %table A031(a:int4)
            %table A032(a:int4)
            %table A033(a:int4)
            %table A034(a:int4)
                       
            %table A035(a:int4)
            %table A036(a:int4)
            %table A037(a:int4)
            %table A038(a:int4)
            %table A039(a:int4)
            A030(a) :- A031(a), A032(-), A033(-), A034(-), A035(-), A036(-), A037(-), A038(-), A039(-).
                       
            %table A040(a:int4)
            %table A041(a:int4)
            %table A042(a:int4)
            %table A043(a:int4)
            %table A044(a:int4)
                       
            %table A045(a:int4)
            %table A046(a:int4)
            %table A047(a:int4)
            %table A048(a:int4)
            %table A049(a:int4)
            A040(a) :- A041(a), A042(-), A043(-), A044(-), A045(-), A046(-), A047(-), A048(-), A049(-).
              
            %table A050(a:int4)
            %table A051(a:int4)
            %table A052(a:int4)
            %table A053(a:int4)
            %table A054(a:int4)
                       
            %table A055(a:int4)
            %table A056(a:int4)
            %table A057(a:int4)
            %table A058(a:int4)
            %table A059(a:int4)
            A050(a) :- A051(a), A052(-), A053(-), A054(-), A055(-), A056(-), A057(-), A058(-), A059(-).
               
            %table A060(a:int4)
            %table A061(a:int4)
            %table A062(a:int4)
            %table A063(a:int4)
            A060(a) :- A061(a), A062(-), A063(-).
        }
    )";
    DtlCompilerTest(valid5, GMERR_OK, GMERR_OK);

    constexpr auto valid6 = R"(
        namespace ns1 {
            %table input(a:int4)
            %table A000(a:int4) 
            %table A001(a:int4) 
            %table A002(a:int4) 
            %table A003(a:int4) 
            %table A004(a:int4)
            %table A005(a:int4)
            %table A006(a:int4)
            %table A007(a:int4)
            %table A008(a:int4)
            %table A009(a:int4)
            %table A010(a:int4)
            %table A011(a:int4)
            %table A012(a:int4)
            %table A013(a:int4)
            %table A014(a:int4)
            %table A015(a:int4)
            %table A016(a:int4)
            %table A017(a:int4)
            %table A018(a:int4)
            %table A019(a:int4)
            %table A020(a:int4)
            %table A021(a:int4)
            %table A022(a:int4)
            %table A023(a:int4)
            %table A024(a:int4)
            %table A025(a:int4)
            %table A026(a:int4)
            %table A027(a:int4)
            %table A028(a:int4)
            %table A029(a:int4)
            %table A030(a:int4)
            %table A031(a:int4)
            %table A032(a:int4)
            %table A033(a:int4)
            %table A034(a:int4)
            %table A035(a:int4)
            %table A036(a:int4)
            %table A037(a:int4)
            %table A038(a:int4)
            %table A039(a:int4)
            %table A040(a:int4)
            %table A041(a:int4)
            %table A042(a:int4)
            %table A043(a:int4)
            %table A044(a:int4)
            %table A045(a:int4)
            %table A046(a:int4)
            %table A047(a:int4)
            %table A048(a:int4)
            %table A049(a:int4)
            %table A050(a:int4)
            %table A051(a:int4)
            %table A052(a:int4)
            %table A053(a:int4)
            %table A054(a:int4)
            %table A055(a:int4)
            %table A056(a:int4)
            %table A057(a:int4)
            %table A058(a:int4)
            %table A059(a:int4)
            %table A060(a:int4)
            %table A061(a:int4)
            %table A062(a:int4)
            %table A063(a:int4)
            %table A064(a:int4)
            %table A065(a:int4)
            %table A066(a:int4)
            %table A067(a:int4)
            %table A068(a:int4)
            %table A069(a:int4)
            %table A070(a:int4)
            %table A071(a:int4)
            %table A072(a:int4)
            %table A073(a:int4)
            %table A074(a:int4)
            %table A075(a:int4)
            %table A076(a:int4)
            %table A077(a:int4)
            %table A078(a:int4)
            %table A079(a:int4)
            %table A080(a:int4)
            %table A081(a:int4)
            %table A082(a:int4)
            %table A083(a:int4)
            %table A084(a:int4)
            %table A085(a:int4)
            %table A086(a:int4)
            %table A087(a:int4)
            %table A088(a:int4)
            %table A089(a:int4)
            %table A090(a:int4)
            %table A091(a:int4)
            %table A092(a:int4)
            %table A093(a:int4)
            %table A094(a:int4)
            %table A095(a:int4)
            %table A096(a:int4)
            %table A097(a:int4)
            %table A098(a:int4)
            %table A099(a:int4)
 
            A000(a) :- input(a).
            A001(a) :- input(a).
            A002(a) :- input(a).
            A003(a) :- input(a).
            A004(a) :- input(a).
            A005(a) :- input(a).
            A006(a) :- input(a).
            A007(a) :- input(a).
            A008(a) :- input(a).
            A009(a) :- input(a).
            A010(a) :- input(a).
            A011(a) :- input(a).
            A012(a) :- input(a).
            A013(a) :- input(a).
            A014(a) :- input(a).
            A015(a) :- input(a).
            A016(a) :- input(a).
            A017(a) :- input(a).
            A018(a) :- input(a).
            A019(a) :- input(a).
            A020(a) :- input(a).
            A021(a) :- input(a).
            A022(a) :- input(a).
            A023(a) :- input(a).
            A024(a) :- input(a).
            A025(a) :- input(a).
            A026(a) :- input(a).
            A027(a) :- input(a).
            A028(a) :- input(a).
            A029(a) :- input(a).
            A030(a) :- input(a).
            A031(a) :- input(a).
            A032(a) :- input(a).
            A033(a) :- input(a).
            A034(a) :- input(a).
            A035(a) :- input(a).
            A036(a) :- input(a).
            A037(a) :- input(a).
            A038(a) :- input(a).
            A039(a) :- input(a).
            A040(a) :- input(a).
            A041(a) :- input(a).
            A042(a) :- input(a).
            A043(a) :- input(a).
            A044(a) :- input(a).
            A045(a) :- input(a).
            A046(a) :- input(a).
            A047(a) :- input(a).
            A048(a) :- input(a).
            A049(a) :- input(a).
            A050(a) :- input(a).
            A051(a) :- input(a).
            A052(a) :- input(a).
            A053(a) :- input(a).
            A054(a) :- input(a).
            A055(a) :- input(a).
            A056(a) :- input(a).
            A057(a) :- input(a).
            A058(a) :- input(a).
            A059(a) :- input(a).
            A060(a) :- input(a).
            A061(a) :- input(a).
            A062(a) :- input(a).
            A063(a) :- input(a).
            A064(a) :- input(a).
            A065(a) :- input(a).
            A066(a) :- input(a).
            A067(a) :- input(a).
            A068(a) :- input(a).
            A069(a) :- input(a).
            A070(a) :- input(a).
            A071(a) :- input(a).
            A072(a) :- input(a).
            A073(a) :- input(a).
            A074(a) :- input(a).
            A075(a) :- input(a).
            A076(a) :- input(a).
            A077(a) :- input(a).
            A078(a) :- input(a).
            A079(a) :- input(a).
            A080(a) :- input(a).
            A081(a) :- input(a).
            A082(a) :- input(a).
            A083(a) :- input(a).
            A084(a) :- input(a).
            A085(a) :- input(a).
            A086(a) :- input(a).
            A087(a) :- input(a).
            A088(a) :- input(a).
            A089(a) :- input(a).
            A090(a) :- input(a).
            A091(a) :- input(a).
            A092(a) :- input(a).
            A093(a) :- input(a).
            A094(a) :- input(a).
            A095(a) :- input(a).
            A096(a) :- input(a).
            A097(a) :- input(a).
            A098(a) :- input(a).
            A099(a) :- input(a).
        }
    )";
    DtlCompilerTest(valid6, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithFastInsert)
{
    // fast_insert正常使用
    constexpr auto valid0 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, fast_insert, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlCompilerTest(valid0, GMERR_OK, GMERR_OK);

    // 只有fast_insert
    constexpr auto valid1 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {fast_insert, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid1, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 多个fast_insert
    constexpr auto valid2 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {fast_insert, fast_insert, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid2, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // fast_insert与其他表
    constexpr auto valid3 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update_partial, fast_insert, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid3, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // fast_insert与其他表
    constexpr auto valid4 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {external, fast_insert, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid4, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // fast_insert语法错误
    constexpr auto valid5 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, fast_insert0, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid5, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // fast_insert语法错误
    constexpr auto valid6 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, FAST_INSERT, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid6, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // fast_insert正常使用
    constexpr auto valid7 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, fast_insert, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlCompilerTest(valid7, GMERR_OK, GMERR_OK);

    // fast_insert只能是输入表
    constexpr auto valid8 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b)), fast_insert}
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid8, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 多个fast_insert
    constexpr auto valid9 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {fast_insert, fast_insert, update, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8)
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid9, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // union delete 不能和 fast_insert同时使用
    constexpr auto valid10 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b)), fast_insert, union_delete(inpB)}
        %table inpB(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b))}
        out(a, b, 1, 1) :- inp(a, b, -, -), inpB(a, b, -, -).
    )";
    DtlVerifyTest(valid10, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // notify 不能和 fast_insert同时使用
    constexpr auto valid11 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b))}
        %table out(a:int8, b:int8, c:int8, d:int8) {update, index(0(a, b)), fast_insert, notify}
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid11, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // update必须和fast_insert同时使用
    constexpr auto valid12 = R"(
        %table inp(a:int8, b:int8, c:int8, d:int8) {fast_insert, index(0(a, b)), update}
        %table out(a:int8, b:int8, c:int8, d:int8) {index(0(a, b)), update}
        out(a, b, 1, 1) :- inp(a, b, -, -).
    )";
    DtlVerifyTest(valid12, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithTable)
{
    // 多字段过滤，多字段投影，多表连接，笛卡尔积综合测试
    constexpr auto validWithMultiJoin = R"(
        %table inp00(a:int8, b:int8, c:int8, d:int8)
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table inp02(a:int8, b:int8, c:int8, d:int8)
        %table inp03(a:int8, b:int8, c:int8, d:int8)
        %table inp04(a:int8, b:int8, c:int8, d:int8)
        %table inp05(a:int8, b:int8, c:int8, d:int8)
        %table out01(a:int8, b:int8)
        out01(a, b) :- inp00(a, -, -, -), inp01(-, -, b, -), inp02(1, -, b, -),
        inp03(a, -, b, -), inp04(1, 2, b, 3), inp05(-, a, -, -).
    )";
    DtlCompilerTest(validWithMultiJoin, GMERR_OK, GMERR_OK);

    // 字符串与byte过滤
    constexpr auto validWithStrAndByte = R"(
        %table inps1(a:int8, b:str, c:int8, d:byte128)
        %table inps2(a:int8, b:str, c:int8, d:byte128)
        %table inps3(a:int8, b:str, c:int8, d:byte128)
        %table inps4(a:int8, b:str, c:int8, d:byte128)
        %table outs1(a:int8, b:int8)
        outs1(a, c) :- inps1(a, "a", c, "0xabcde").
        outs1(a, c) :- inps2(a, "hhhhhhhhhh", c, "0xfff").
        outs1(a, c) :- inps3(a, "hhhhhhhhhh", c, "0xfff").
        outs1(a, c) :- inps4(a, "12334", -, "0xfff"), inps3(-, "hhhhhhhhhh", c, "0xfff"),
        inps2(a, 1, c, 1), inps1(a, "a", c, "0xabcde").
    )";
    DtlCompilerTest(validWithStrAndByte, GMERR_OK, GMERR_OK);

    // 投影过滤测试
    constexpr auto validWithProject = R"(
        %table inp1(a:int8, b:int8, c:int8, d:int8)
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table out01(a:int8, b:int8)
        out01(a, b) :- inp1(a, b, 3, 2).
        out01(a, b) :- inp1(a, b, 3, 2), inp01(a, b, -, -).
        out01(a, b) :- inp1(a, -, 3, 2), inp01(a, b, -, -).
        out01(a, b) :- inp1(a, -, -, b), inp01(a, b, 2, 3).
        out01(a, b) :- inp1(a, b, 3, 2), inp01(2, 3, a, b).
        out01(a, b) :- inp1(a, b, 3, 2), inp01(2, 3, -, b).
        out01(a, b) :- inp1(a, b, -, -), inp01(2, 3, a, -).
        out01(a, b) :- inp1(a, -, -, -), inp01(2, 3, b, -).
        out01(a, b) :- inp1(a, 1, 2, 3), inp01(2, 3, b, -).
        out01(a, b) :- inp1(a, -, -, -), inp01(-, -, b, -).
        out01(a, b) :- inp1(a, -, -, -), inp01(-, -, b, -).
    )";
    DtlCompilerTest(validWithProject, GMERR_OK, GMERR_OK);

    // 测试byte过滤规则
    constexpr auto validWithByte = R"(
        %table inpA2(a:int8, b:int4, c:byte2)
        %table inpB2(a:int2, b:int4, c:int8)
        %table out(a:int4, b:int4)
        out(c, b) :- inpA2(a, b, "ABCD"), inpB2(-, c, a).
    )";
    DtlCompilerTest(validWithByte, GMERR_OK, GMERR_OK);

    // 测试table包含index
    constexpr auto validWithTableAndIndex = R"(
        %table inp61(a:int4, b:int4) {
            index(0(a)),
            index(1(b)),
            index(2(a, b)),
            update
        }
        %table inp64(a:int4, b:int4, c:int8) {
            index(0(a)),
            index(1(b)),
            index(2(a, b)),
            update_partial
        }
        %table out61(a:int4, b:int4)
        %table inp71(a: int4) { max_size(3) }
        %table out71(a: int4)
        out61(a, b) :- inp61(a, b).
        out61(a, b) :- inp64(a, b, -).
        out71(a) :- inp71(a).
    )";
    DtlCompilerTest(validWithTableAndIndex, GMERR_OK, GMERR_OK);

    // 测试多条规则
    constexpr auto validWithMultiRule = R"(
        %table inp(a:int4, b:int8, c:int4)
        %table out(a:int4, b:int8, c:int4)
        %table inpA(a:int4, b:int8)
        %table inpB(a:int8, b:int1, c:int2)
        %table outTbl(a:int4, b:int1)
        out(a, b, c) :- inp(a, b, c).
        outTbl(a, c) :- inpA(a, b), inpB(b, c, -).
    )";
    DtlCompilerTest(validWithMultiRule, GMERR_OK, GMERR_OK);

    // 测试右表中过滤
    constexpr auto validWithFilter = R"(
        %table inp1(a:int4, b:int8, c:int4)
        %table inp2(a:int4, b:int8, c:int4)
        %table out(a:int4, b:int8, c:int4)
        out(a, b, c) :- inp1(2, b, c), inp2(a, b, c).
    )";
    DtlCompilerTest(validWithFilter, GMERR_OK, GMERR_OK);

    // 测试多个右表中多个字段过滤
    constexpr auto validWithMultiFilter = R"(
        %table table1(a:int1, b:int2, c:int4, d:int8, e:byte4, f:byte6, g:byte16, h:byte64)
        %table table2(a:int4) {}
        %table table3(a:int4, b:int4) {}
        table2(a) :- table3(a, 9), table1(-123, -, a, -, "0xFFF", -, -, 655367).
    )";
    DtlCompilerTest(validWithMultiFilter, GMERR_OK, GMERR_OK);

    // 测试笛卡尔积
    constexpr auto validWithCartesian = R"(
        %table table4(a:int4) {}
        %table table5(c:int4, d:int4) {}
        %table table6(a:int4, c:int4, d:int4) {}
        table6(a, c, d) :- table5(c, d), table4(a).
    )";
    DtlCompilerTest(validWithCartesian, GMERR_OK, GMERR_OK);

    // 测试先过滤， 再投影
    constexpr auto validWithFilterAndProject = R"(
        %table table13(c:int4, a:int4) {}
        %table table14(c:int4) {
            index(0(c))
        }
        %table table15(a:int4, c:int4, d:int4) {}
        table14(c) :- table15(-, c, -12), table13(c, -).
    )";
    DtlCompilerTest(validWithFilterAndProject, GMERR_OK, GMERR_OK);

    // 索引元数据测试
    constexpr auto indexVertex = R"(
        %table inp1(c:int4, d:int4) {
            index(1(c, d)),
            index(2(c)),
            index(3(c))
        }
        %table inp2(a:int4, b:int4) {
            index(0(a)),
            index(1(b)),
            index(2(a, b)),
            update
        }
        %table out(a:int4, b:int4)
        out(a, b) :- inp1(a, b), inp2(a, b).
    )";
    DtlCompilerTest(indexVertex, GMERR_OK, GMERR_OK);
}

// 测试输出表字段是否valid
TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithExtend)
{
    constexpr auto valid0 = R"(
        namespace ns6 {
            %table A(a: int4, b: int4)
            %table B(a: int4, b: int4, c: int4)
            %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }
            %function func(a: int4 -> b: int4)

            rsc0(a, c, -) :- A(a, b), func(b, c).
            B(a, b, d) :- rsc0(a, b, c), func(c, d).
        }
    )";
    DtlAnalyzerOutputValidTest(valid0, GMERR_OK, true);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithIgnore)
{
    // 测试右表全为 -
    constexpr auto validWithRightIgnore0 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        inp1(1, 1):-inp0(-, -).
    )";
    DtlCompilerTest(validWithRightIgnore0, GMERR_OK, GMERR_OK);

    // 测试右表全为 - 变量
    constexpr auto validWithRightIgnore1 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4)
        inp2(a, 1):-inp0(-, -), inp1(a, -).
    )";
    DtlCompilerTest(validWithRightIgnore1, GMERR_OK, GMERR_OK);

    // 测试右表全为 -
    constexpr auto validWithRightIgnore2 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4)
        inp2(1, 1):-inp0(-, -), inp1(-, -).
    )";
    DtlCompilerTest(validWithRightIgnore2, GMERR_OK, GMERR_OK);

    // 测试右表为 - 常量
    constexpr auto validWithRightIgnore3 = R"(
        %table inp0(a:int4, b:int4, c:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4)
        inp2(a, 1):-inp1(-, -), inp0(1, -, a).
    )";
    DtlCompilerTest(validWithRightIgnore3, GMERR_OK, GMERR_OK);

    // 测试右表全为 - 常量， 左表全为常量
    constexpr auto validWithRightIgnore4 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4)
        inp2(1, 1):-inp0(-, -), inp1(1, 1).
    )";
    DtlCompilerTest(validWithRightIgnore4, GMERR_OK, GMERR_OK);

    // 测试右表全为- 左表常量 -
    constexpr auto validWithRightIgnore5 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4)
        inp2(a, 1):-inp0(-, -), inp1(a, -).
    )";
    DtlCompilerTest(validWithRightIgnore5, GMERR_OK, GMERR_OK);

    // 测试右表全为-
    constexpr auto validWithRightIgnore6 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4)
        inp2(1, a):-inp0(-, -), inp1(1, a).
    )";
    DtlCompilerTest(validWithRightIgnore6, GMERR_OK, GMERR_OK);

    // 测试右表全为- 左表常量
    constexpr auto validWithRightIgnore7 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        inp1(1, 2):-inp0(-, -).
    )";
    DtlCompilerTest(validWithRightIgnore7, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithNull)
{
    // 测试左表为null
    constexpr auto validWithNull = R"(
        %table inp0(a:int4, b:int4)
        null(0):-inp0(-, -).
    )";
    DtlCompilerTest(validWithNull, GMERR_OK, GMERR_OK);

    // 测试左表为null， 右表连接
    constexpr auto validWithNullAndJoin = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        null(0):-inp0(-, -), inp1(a, b).
    )";
    DtlCompilerTest(validWithNullAndJoin, GMERR_OK, GMERR_OK);

    // 测试左表为null， 右表聚合
    constexpr auto validWithNullAndAgg = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp2(a:int4, b:int4, c:int4)
        %aggregate expand(a:int4, b:int4 -> i:int4){ordered, many_to_one}
        null(0):-inp2(k, a, b) GROUP-BY (k) expand(a, b, v).
        null(0):-inp0(-, -), inp1(a, b).
    )";
    DtlCompilerTest(validWithNullAndAgg, GMERR_OK, GMERR_OK);

    // 测试左表为null， 右表变量
    constexpr auto validWithNullAndVar = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        null(0):-inp0(-, -), inp1(a, b).
    )";
    DtlCompilerTest(validWithNullAndVar, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithOutputCase)
{
    // 测试左表部分常量
    constexpr auto validWithOutput1 = R"(
        %table table0(a:int4, b:int4)
        %table table1(a:int4, b:int4)
        %table table2(a:int4, b:int4)
        table2(a, 1):- table0(1, 1), table1(a, 1).
    )";
    DtlCompilerTest(validWithOutput1, GMERR_OK, GMERR_OK);

    // 测试左表全为常量
    constexpr auto validWithOutput2 = R"(
        %table table0(a:int4, b:int4)
        %table table1(a:int4, b:int4)
        %table table2(a:int4, b:int4)
        table2(1, 1):- table0(1, 1), table1(1, 1).
    )";
    DtlCompilerTest(validWithOutput2, GMERR_OK, GMERR_OK);

    constexpr auto validWithOutput3 = R"(
        %table table0(a:int4, b:int4)
        %table table1(a:int4, b:int4)
        %table table2(a:int4, b:int4)
        table2(a, b):- table0(-, -), table1(a, b).
    )";
    DtlCompilerTest(validWithOutput3, GMERR_OK, GMERR_OK);

    // 测试左表部分重复
    constexpr auto validWithOutput4 = R"(
        %table table0(a:int4, b:int4)
        %table table1(a:int4, b:int4)
        %table table2(a: int4, b: int4, c: int4, d: int4)
        table2(a, 1, 1, a):- table0(-, -), table1(a, 1).
    )";
    DtlCompilerTest(validWithOutput4, GMERR_OK, GMERR_OK);

    // 测试左表全部重复
    constexpr auto validWithOutput5 = R"(
        %table table0(a:int4, b:int4)
        %table table1(a:int4, b:int4)
        %table table2(a: int4, b: int4, c: int4, d: int4)
        table2(a, a, a, a):- table0(-, -), table1(a, 1).
    )";
    DtlCompilerTest(validWithOutput5, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithNotJoin)
{
    // 测试not join左表常量
    constexpr auto validWithNot0 = R"(
        %table inp0(a: int4, b: int4, c: int4, d: int4)
        %table inp1(a:int4, b:int4)
        %table inp3(a:int4, b:int4)
        inp0(1, 1, a, b):- inp3(a, b), NOT inp1(a, b).
    )";
    DtlCompilerTest(validWithNot0, GMERR_OK, GMERR_OK);

    // 测试not join
    constexpr auto validWithNot1 = R"(
        %table inp0(a:int4, b:int4)
        %table inp1(a:int4, b:int4)
        %table inp3(a:int4, b:int4)
        inp0(a, b):- inp3(a, b), NOT inp1(a, b).
    )";
    DtlCompilerTest(validWithNot1, GMERR_OK, GMERR_OK);

    // 测试not join部分常量
    constexpr auto validWithNot2 = R"(
        %table inp0(a: int4, b: int4, c: int4, d: int4)
        %table inp1(a:int4, b:int4)
        %table inp3(a:int4, b:int4)
        inp0(1, 1, a, b):- inp3(a, b), NOT inp1(1, b).
    )";
    DtlCompilerTest(validWithNot2, GMERR_OK, GMERR_OK);

    // 测试-和常量
    constexpr auto validWithNot5 = R"(
        %table inp0(a: int4, b: int4, c: int4, d: int4)
        %table inp1(a:int4, b:int4)
        %table inp3(a:int4, b:int4)
        inp0(1, 1, 1, 1):- inp3(1, 1), NOT inp1(1, 1).
    )";
    DtlCompilerTest(validWithNot5, GMERR_OK, GMERR_OK);

    // 测试常量和-
    constexpr auto validWithNot6 = R"(
        %table inp0(a: int4, b: int4, c: int4, d: int4)
        %table inp1(a:int4, b:int4)
        %table inp3(a:int4, b:int4)
        inp0(1, 1, a, a):- inp3(a, 1), NOT inp1(1, 1).
    )";
    DtlCompilerTest(validWithNot6, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithParser)
{
    // 合法字符校验
    constexpr auto invalidProgram1 = R"(
        %table abc(&a:int1)
    )";
    DtlParserTest(invalidProgram1, GMERR_SYNTAX_ERROR);

    // 非法字符校验
    constexpr auto invalidProgram2 = R"(
        %table abc($a:int1)
    )";
    DtlParserTest(invalidProgram2, GMERR_SYNTAX_ERROR);

    // 多个非法字符校验
    constexpr auto invalidProgram3 = R"(
        %table inp(a:int4, b:int8, c:int4)
        %table out(@a:int4, b:int8, c:int4)
        %table inpA(a^:int4, b:int8)
        %table inpB(a:int8, b:int1, c:int2)
        %table outTbl(a:int4, b:int1)
        out(a, b, c) : inp(a, b, c).
        outTbl(a, c) :- inpA(a, b), inpB(b, c, -).
    )";
    DtlParserTest(invalidProgram3, GMERR_SYNTAX_ERROR);

    // 多个非法字符校验
    constexpr auto invalidProgram4 = R"(
        %table inp(a:int4, b:int8, c:int4)
        %table out(@a:int4, b:int8, c:int4)
        %table inpA(a^:int4, b:int8)
        %table inpB(a:int8, b:int1, c:int2)
        %table outTbl(a:int4, b:int1)
        out(a, b, c) : inp(a, b, c).
        outTbl(a, c) :- inpA(a, b), inpB(b, c, -).
        outTbl(a, c) :- inpB(a, b, $), inpA(b, c).
    )";
    DtlParserTest(invalidProgram4, GMERR_SYNTAX_ERROR);

    // 未识别字符测试
    constexpr auto invalidProgram5 = R"(
    namespace ns/&ns {
        %table A(a:int4, b:int4) {
        index(0(a, b)),
        index(1(a))
        }

        %table B(a:int4, b:int4) {
        max_size(100)
        }

        %table C(a:int4, b:int4)

        B(a, b) :- A(a, b).
        C(a, c) :- A(a, b), B(b, c).
    })";
    DtlParserTest(invalidProgram5, GMERR_SYNTAX_ERROR);

    // 不合法注释测试
    constexpr auto invalidProgram6 = R"(
        /*测试注释
        %table a1(a:int1) {}
        %table a2(a:int1) {}
    )";
    DtlParserTest(invalidProgram6, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram7 = R"(
        /*测试注释*/
        %table a3(a:int1)
    )";
    DtlParserTest(invalidProgram7, GMERR_OK);

    // 特殊字符测试
    constexpr auto invalidProgram8 = R"(
        %table a0(a+:int1) {}
    )";
    DtlParserTest(invalidProgram8, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram9 = R"(
        %table a1(a!:int1)
    )";
    DtlParserTest(invalidProgram9, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram10 = R"(
        %table a2(a/:int1)
    )";
    DtlParserTest(invalidProgram10, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram11 = R"(
        %table a3(a\:int1)
    )";
    DtlParserTest(invalidProgram11, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram12 = R"(
        %table a4("a":int1)
    )";
    DtlParserTest(invalidProgram12, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram13 = R"(
        %table a5('a':int1)
    )";
    DtlParserTest(invalidProgram13, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram14 = R"(
        %table a6(a*:int1)
    )";
    DtlParserTest(invalidProgram14, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram15 = R"(
        %table a7(a?:int1)
    )";
    DtlParserTest(invalidProgram15, GMERR_SYNTAX_ERROR);

    constexpr auto invalidProgram16 = R"(
        %table a8(a|:int1)
    )";
    DtlParserTest(invalidProgram16, GMERR_SYNTAX_ERROR);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithAgg)
{
    // 聚合order测试
    constexpr auto validWithOrder = R"(
        %table inp_m(a:int4, b:int4, c:int4)
        %table agg_m(a: int4, b: int4, c: int4, d: int4)
        %aggregate min_max(v:int4 -> min:int4, max:int4){ordered}
        agg_m(max, min, p, k) :- inp_m(k, p, v) GROUP-BY (k, p) min_max(v, min, max).
        null(0):- agg_m(max, min, p, k).
    )";
    DtlCompilerTest(validWithOrder, GMERR_OK, GMERR_OK);

    // 聚合many_to_many测试
    constexpr auto validWithMany2Many = R"(
        %table inp_e(a:int4, b:int4, c:int4)
        %table agg_e(a:int4, b:int4, c:int4)
        %aggregate expand(a:int4, b:int4 -> i:int4, c:int4){ordered, many_to_many}
        agg_e(k, i, v) :- inp_e(k, a, b) GROUP-BY (k) expand(a, b, i, v).
        null(0):- agg_e(k, i, v).
    )";
    DtlCompilerTest(validWithMany2Many, GMERR_OK, GMERR_OK);

    // 聚合many_to_one测试
    constexpr auto validWithMany2One = R"(
        %table inp_e(a:int4, b:int4, c:int4)
        %table agg_e(a:int4, b:int4)
        %aggregate expand(a:int4, b:int4 -> i:int4){ordered, many_to_one}
        agg_e(k, i) :- inp_e(k, a, b) GROUP-BY (k) expand(a, b, i).
        null(0):- agg_e(k, i).
    )";
    DtlCompilerTest(validWithMany2One, GMERR_OK, GMERR_OK);

    // 聚合min_max测试
    constexpr auto validWithMinMax = R"(
        %table A(k: int4, p: int4, v: int4)
        %table B(max: int4, min: int4, k: int4, p: int4)
        %aggregate min_max(v: int4 -> min: int4, max: int4) {
            ordered
        }

        B(max, min, k, p) :- A(k, p, v) GROUP-BY (k, p) min_max(v, min, max).
        null(0):- B(max, min, k, p).
    )";
    DtlCompilerTest(validWithMinMax, GMERR_OK, GMERR_OK);

    // 多个聚合规则测试
    constexpr auto validWithMultiAgg = R"(
        %table inp_0(a:int4, b:int4, c:int4)
        %table inp_1(a: int4, b: int4, c: int4, d: int4)
        %table agg_0(a:int4, b:int4)
        %table agg_1(a:int4, b:int4)
        %aggregate expand0(a:int4, b:int4 -> i:int4){ordered, many_to_one}
        %aggregate expand1(a:int4, b:int4, c:int4-> i:int4){ordered, many_to_one}
        agg_0(k, i) :- inp_0(k, a, b) GROUP-BY (k) expand0(a, b, i).
        agg_1(k, i) :- inp_1(k, a, b, c) GROUP-BY (k) expand1(a, b, c, i).
        null(0):- agg_0(k, i).
        null(0):- agg_1(k, i).
    )";
    DtlCompilerTest(validWithMultiAgg, GMERR_OK, GMERR_OK);

    // 多个聚合和table规则测试
    constexpr auto validWithMultiAgg1 = R"(
        %table inp_0(a:int4, b:int4, c:int4)
        %table inp_1(a: int4, b: int4, c: int4, d: int4)
        %table agg_0(a:int4, b:int4)
        %table agg_1(a:int4, b:int4)
        %aggregate expand0(a:int4, b:int4 -> i:int4){ordered, many_to_one}
        %aggregate expand1(a:int4, b:int4, c:int4-> i:int4){ordered, many_to_one}
        agg_0(k, i) :- inp_0(k, a, b) GROUP-BY (k) expand0(a, b, i).
        agg_1(k, i) :- inp_1(k, a, b, c) GROUP-BY (k) expand1(a, b, c, i).
        inp_1(a, b, c, d) :- inp_0(a, b, c), agg_0(a, d).
        null(0):- agg_1(k, i).
    )";
    DtlCompilerTest(validWithMultiAgg1, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithRecourse)
{
    constexpr auto validResource = R"(
        %resource rsc0(a:int8, b:int4 -> c:int4) {
            sequential(max_size(5))
        }
        %table table1(a:int8, b:int4)
        %table table2(a:int8, b:int4, c:int4)

        rsc0(a, b, -) :- table1(a, b).
        table2(a, b, c) :- rsc0(a, b, c).

        // 多个resource连接
        %resource rsc1(a:int4 -> b:int4) {
            sequential(max_size(5))
        }
        %resource rsc2(a:int4 -> b:int4) {
            sequential(max_size(5))
        }
        %resource rsc3(a:int4 -> b:int4) {
            sequential(max_size(5))
        }
        %resource rsc4(a:int4, b:int4, c:int4 -> d:int4) {
            sequential(max_size(5))
        }
        %table table3(a:int4, b:int4)
        %table table4(a:int4, b:int4)

        rsc1(a, -) :- table4(a, -).
        rsc2(a, -) :- table4(a, -).
        table3(a, b) :- rsc3(a, b).
        rsc3(a, -) :- rsc1(a, -), rsc2(a, -).
        rsc3(a, -) :- table4(a, -), rsc4(a, 1, 1, 1).
        rsc4(a, b, c, -) :- rsc1(a, b), rsc2(c, 1).

        // 支持函数建表
        %resource rsc5(a:int8, b:int4 -> c:int4) {
            pending_id(1)
        }
        %table table5(a:int8, b:int4, c:int4)

        rsc5(a, b, -) :- table1(a, b).           // resource作为左表支持忽略字段, 忽略字段只能是资源字段
        table5(a, b, c) :- rsc5(a, b, c).

        // resource + GROUP-BY + max_size
        %resource rsc6(a:int4 -> b:int4) {
            sequential(max_size(5))
        }
        %table table6(a:int4)
        %table table7(a:int4, b:int4)
        %aggregate agg(a:int4 -> b:int4)

        rsc6(a, -) :- table6(a).
        table7(a, d) :- rsc6(a, b) GROUP-BY(a) agg(b, d).
        null(0):- table7(a, d).
        
        // resource + function
        %function func0(a:int4 -> b:int4)
        %table table8(a:int4, b:int4)

        table8(a, d) :- rsc6(a, c), func0(c, d).
    )";
    DtlCompilerTest(validResource, GMERR_OK, GMERR_OK);

    constexpr auto validResource1 = R"(
        %table table1(a:int4, b:int4)
        %table out3(a:int4, b:int4, c:int4)
        %resource rsc5(a:int4, b:int4 -> c:int4) {
            pending_id(1)
        }
        %function funcWriteA(a:int4, b:int4 -> c:int4) {
            access_delta(table1),
            access_current(rsc5)
        }
        rsc5(a, b, -) :- table1(a, b).
        null(0) :- rsc5(1, 1, 1).
        out3(a, b, c) :- table1(a, b), funcWriteA(a, b, c).
    )";
    DtlVerifyTest(validResource1, GMERR_OK, GMERR_OK);

    constexpr auto validResource2 = R"(
        %table table1(a:int4, b:int4)
        %table out3(a:int4, b:int4, c:int4)
        %resource rsc5(a:int4, b:int4 -> c:int4) {
            pending_id(1)
        }
        %function funcWriteA(a:int4, b:int4 -> c:int4) {
            access_delta(rsc5)
        }
        rsc5(a, b, -) :- table1(a, b).
        null(0) :- rsc5(1, 1, 1).
        out3(a, b, c) :- table1(a, b), funcWriteA(a, b, c).

    )";
    DtlVerifyTest(validResource2, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithFunc)
{
    // 多个func
    constexpr auto validFunc0 = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %function funcA(a: int4 -> b: int4)
        %function funcB(a: int4 -> b: int4)
        B(a, b, c) :- A(a, b), funcA(1, c), funcB(1, c).
    )";
    DtlCompilerTest(validFunc0, GMERR_OK, GMERR_OK);

    // 多个func table交叉且入参为常量
    constexpr auto validFunc1 = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %table C(a: int4, b: int4)
        %function funcA(a: int4 -> b: int4)
        %function funcB(a: int4 -> b: int4)
        B(a, b, c) :- A(a, b), funcA(1, c), C(a, 1), funcB(1, c).
    )";
    DtlCompilerTest(validFunc1, GMERR_OK, GMERR_OK);

    // 入参为常量
    constexpr auto validFunc2 = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %function func(a: int4 -> b: int4)
        B(a, b, c) :- A(a, b), func(1, c).
    )";
    DtlCompilerTest(validFunc2, GMERR_OK, GMERR_OK);

    // 出现重名
    constexpr auto validFunc4 = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %function func(a: int4 -> b: int4)
        B(a, b, c) :- A(a, b), func(a, c), func(b, c).
    )";
    DtlCompilerTest(validFunc4, GMERR_OK, GMERR_OK);

    // func入参是否在左侧table中出现
    constexpr auto validFunc5 = R"(
        %table A(a: int4)
        %table B(a: int4, b: int4, c: int4)
        %table C(a: int4)
        %function funcA(a: int4 -> b: int4)
        %function funcB(a: int4 -> b: int4)
        B(a, b, c) :- A(a), funcA(a, b), C(a), funcB(b, c).
    )";
    DtlCompilerTest(validFunc5, GMERR_OK, GMERR_OK);

    // 测试udf不能修改当前delta表
    constexpr auto validFunc6 = R"(
        %table inpA(a:int4, b:int4)
        %table out3(a:int4, b:int4, c:int4)
        %function funcWriteA(a:int4, b:int4 -> c:int4) {
            access_delta(inpA)
        }
        out3(a, b, c) :- inpA(a, b), funcWriteA(a, b, c).
    )";
    DtlRewriterTest(validFunc6, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // func 综合测试
    constexpr auto validFunc7 = R"(
        %table A(a: int4, b: int4)
        %table A1(a: int4, b: int4)
        %table A2(a: int4, b: int4)
        %table A3(a: int4, b: int4)
        %table A4(a: int4, b: int4)
        %table A5(a: int4, b: int4)
        %table A6(a: int4, b: int4)
        %table A7(a: int4, b: int4)
        %table A8(a: int4, b: int4)
        %table A9(a: int4, b: int4)
        %table A10(a: int4, b: int4)
        %function func1(a: int4 -> b: int4)
        %function func2(a: int4 -> b: int4)
        %function func3(a: int4 -> b: int4)
        %function func4(a: int4 -> b: int4)
        %function func5(a: int4 -> b: int4)
        %function func6(a: int4 -> b: int4)
        A(a, b) :- A1(a, 1), func1(1, b), A2(a, 1), func2(a, b), func3(a, b), func4(a, b), func5(a, b), func6(a, b), A3(a, 1), A4(b, 1), A5(a, 1), A6(c, -), A7(c, -), A8(-, -), A9(-, -), A10(-, -).
    )";
    DtlCompilerTest(validFunc7, GMERR_OK, GMERR_OK);

    // func入参为变量和常量
    constexpr auto validFunc8 = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %function func(a: int4, b: int4 -> c: int4, d: int4)
        B(a, b, c) :- A(a, 1), func(a, 1, b, c).
    )";
    DtlCompilerTest(validFunc8, GMERR_OK, GMERR_OK);

    // func入参为变量和常量
    constexpr auto validFunc9 = R"(
        %table A(a: int4, b: int4)
        %table B(a: int4, b: int4, c: int4)
        %table C(a: int4, b: int4)
        %function funcA(a: int4 -> b: int4, c: int4)
        %function funcB(a: int4 -> b: int4)
        B(a, b, c) :- A(a, 1), funcA(1, b, c), C(a, 1), funcB(a, b).
    )";
    DtlCompilerTest(validFunc9, GMERR_OK, GMERR_OK);

    // 测试类型不同，值相同
    constexpr auto valid6 = R"(
        %table A(a: int4, b: int1, c: int4)
        %table B(a: int4, b: int1, c: int4)
        %function func1(a: int1, b: int1)
        %function func4(a: int4, b: int4)
        B(a, b, c) :- A(a, b, c), func1(b, 0), func4(c, 0).
    )";
    DtlCompilerTest(valid6, GMERR_OK, GMERR_OK);

    constexpr auto valid7 = R"(
        %table A(a: int4, b: int1, c: int4)
        %table B(a: int4, b: int1, c: int4)
        %function func1(a: int1, b: int1)
        %function func4(a: int4, b: int4)
        B(a, b, c) :- A(a, b, c), func1(b, 0), func4(0, 0).
    )";
    DtlCompilerTest(valid7, GMERR_OK, GMERR_OK);

    constexpr auto valid8 = R"(
        %table A(a: int4, b: int1, c: int4)
        %table B(a: int4, b: int1, c: int4)
        %function func1(a: int1, b: int1)
        %function func4(a: int4, b: int4)
        B(a, b, c) :- A(a, b, c), func1(0, 0), func4(0, 0).
    )";
    DtlCompilerTest(valid8, GMERR_OK, GMERR_OK);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithRecursion)
{
    // 递归测试 部分表递归
    constexpr auto validWithMultiJoin0 = R"(
        %table inp00(a:int8, b:int8, c:int8, d:int8)
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table inp02(a:int8, b:int8, c:int8, d:int8)
        %table inp03(a:int8, b:int8, c:int8, d:int8)
        %table inp04(a:int8, b:int8, c:int8, d:int8)
        %table inp05(a:int8, b:int8, c:int8, d:int8)
        inp00(a, b, c, d) :- inp01(a, b, c, d), inp02(a, b, c, d).
        inp03(a, b, c, d) :- inp00(a, b, c, d).
        inp04(a, b, c, d) :- inp00(a, b, c, d).
        inp02(a, b, c, d) :- inp03(a, b, c, d).
        inp01(a, b, c, d) :- inp04(a, b, c, d).
        inp01(a, b, c, d) :- inp05(a, b, c, d).
        inp00(a, b, c, d) :- inp05(a, b, c, d).
    )";
    // 递归校验由rewriter模块完成
    DtlVerifyTest(validWithMultiJoin0, GMERR_OK, GMERR_OK);

    // 递归测试 单个环
    constexpr auto validWithMultiJoin1 = R"(
        %table inp00(a:int8, b:int8, c:int8, d:int8)
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table inp02(a:int8, b:int8, c:int8, d:int8)
        %table inp03(a:int8, b:int8, c:int8, d:int8)
        inp01(a, b, c, d) :- inp00(a, b, c, d).
        inp02(a, b, c, d) :- inp01(a, b, c, d).
        inp03(a, b, c, d) :- inp02(a, b, c, d).
        inp00(a, b, c, d) :- inp03(a, b, c, d).
    )";
    // 递归校验由rewriter模块完成
    DtlVerifyTest(validWithMultiJoin1, GMERR_OK, GMERR_OK);

    // 递归测试 两个环
    constexpr auto validWithMultiJoin2 = R"(
        %table inp00(a:int8, b:int8, c:int8, d:int8)
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table inp02(a:int8, b:int8, c:int8, d:int8)
        %table inp03(a:int8, b:int8, c:int8, d:int8)
        %table inp04(a:int8, b:int8, c:int8, d:int8)
        inp00(a, b, c, d) :- inp01(a, b, c, d), inp02(a, b, c, d).
        inp03(a, b, c, d) :- inp00(a, b, c, d).
        inp04(a, b, c, d) :- inp00(a, b, c, d).
        inp02(a, b, c, d) :- inp03(a, b, c, d).
        inp01(a, b, c, d) :- inp04(a, b, c, d).
    )";
    // 递归校验由rewriter模块完成
    DtlVerifyTest(validWithMultiJoin2, GMERR_OK, GMERR_OK);

    // 递归测试 三个环
    constexpr auto validWithMultiJoin3 = R"(
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table inp02(a:int8, b:int8, c:int8, d:int8)
        %table inp03(a:int8, b:int8, c:int8, d:int8)
        %table inp04(a:int8, b:int8, c:int8, d:int8)
        %table inp05(a:int8, b:int8, c:int8, d:int8)
        %table inp06(a:int8, b:int8, c:int8, d:int8)
        inp02(a, b, c, d) :- inp01(a, b, c, d).
        inp03(a, b, c, d) :- inp02(a, b, c, d).
        inp01(a, b, c, d) :- inp03(a, b, c, d).
        inp04(a, b, c, d) :- inp02(a, b, c, d).
        inp05(a, b, c, d) :- inp04(a, b, c, d).
        inp02(a, b, c, d) :- inp05(a, b, c, d).
        inp05(a, b, c, d) :- inp03(a, b, c, d).
        inp06(a, b, c, d) :- inp05(a, b, c, d).
        inp03(a, b, c, d) :- inp06(a, b, c, d).
    )";
    // 递归校验由rewriter模块完成
    DtlVerifyTest(validWithMultiJoin3, GMERR_OK, GMERR_OK);

    // 递归测试 构成环表数超过10
    constexpr auto validWithMultiJoin4 = R"(
        %table inp01(a:int8, b:int8, c:int8, d:int8)
        %table inp02(a:int8, b:int8, c:int8, d:int8)
        %table inp03(a:int8, b:int8, c:int8, d:int8)
        %table inp04(a:int8, b:int8, c:int8, d:int8)
        %table inp05(a:int8, b:int8, c:int8, d:int8)
        %table inp06(a:int8, b:int8, c:int8, d:int8)
        %table inp07(a:int8, b:int8, c:int8, d:int8)
        %table inp08(a:int8, b:int8, c:int8, d:int8)
        %table inp09(a:int8, b:int8, c:int8, d:int8)
        %table inp10(a:int8, b:int8, c:int8, d:int8)
        %table inp11(a:int8, b:int8, c:int8, d:int8)
        %table inp12(a:int8, b:int8, c:int8, d:int8)
        %table inp13(a:int8, b:int8, c:int8, d:int8)
        %table inp14(a:int8, b:int8, c:int8, d:int8)
        inp02(a, b, c, d) :- inp01(a, b, c, d).
        inp03(a, b, c, d) :- inp02(a, b, c, d).
        inp04(a, b, c, d) :- inp03(a, b, c, d).
        inp05(a, b, c, d) :- inp04(a, b, c, d).
        inp06(a, b, c, d) :- inp05(a, b, c, d).
        inp07(a, b, c, d) :- inp06(a, b, c, d).
        inp08(a, b, c, d) :- inp07(a, b, c, d).
        inp09(a, b, c, d) :- inp08(a, b, c, d).
        inp10(a, b, c, d) :- inp09(a, b, c, d).
        inp11(a, b, c, d) :- inp10(a, b, c, d).
        inp12(a, b, c, d) :- inp11(a, b, c, d).
        inp13(a, b, c, d) :- inp12(a, b, c, d).
        inp14(a, b, c, d) :- inp13(a, b, c, d).
        inp01(a, b, c, d) :- inp14(a, b, c, d).
    )";
    // 递归校验由rewriter模块完成
    DtlVerifyTest(validWithMultiJoin4, GMERR_OK, GMERR_OK);

    // 聚合规则中左表为resource表
    constexpr auto invalid1 = R"(
        // 1.aggregate
        namespace ns3 {
            %table A(a: int4, b: int4)
            %table B(a: int4, b: int4, c: int4)
            %resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }
            %aggregate funcA(a: int4 -> b: int4)

            rsc0(a, c, -) :- A(a, b) GROUP-BY(a) funcA(b, c).
            B(a, c, d) :- rsc0(a, b, c) GROUP-BY(a) funcA(b, d).
        }
    )";
    DtlVerifyTest(invalid1, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// 校验输出表是否产生plan
TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithOutputPlan)
{
    constexpr auto valid = R"(
        namespace ns {
            %table A(a: int4, b: int4) {index(0(a)), update, local}
            %table B(a: int4, b: int4)
            A(a,b):-B(a,b).
        }
    )";
    uint32_t planNum = 3;
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, valid, &program);
    ASSERT_EQ(GMERR_OK, ret);

    ret = DtlVerify((DbMemCtxT *)analyzerMemCtx, program);
    ASSERT_EQ(GMERR_OK, ret);

    DbListT *plans = NULL;
    ret = DtlTransAst2IR(program, &plans);
    ASSERT_EQ(GMERR_OK, ret);

    DbListT deltaPlans;
    ret = DtlQryRewrite(analyzerMemCtx, program, plans, &deltaPlans);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(true, deltaPlans.count == planNum);

    for (uint32_t i = 0; i < deltaPlans.count; i++) {
        void *plan = (void *)DbListItem(&deltaPlans, i);
        IRPrintPlan((DbMemCtxT *)analyzerMemCtx, (IRPlanT *)plan);
    }
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithVariant)
{
    // 正常
    constexpr auto valid1 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int4){ transient(tuple), variant, index(0(a, b)), index(1(a, c)), max_size(5) }
        %table out(a: int4, b: int4, c: int4)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid1, GMERR_OK, GMERR_OK);

    // 正常, variant与transient(tuple)交换位置
    constexpr auto valid2 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int4){ variant, transient(tuple), index(0(a, b)), index(1(a, c)), max_size(5) }
        %table out(a: int4, b: int4, c: int4)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid2, GMERR_OK, GMERR_OK);

    // 异常，variant单独使用
    constexpr auto valid3 = R"(
        %table inp(a: int4, b: int4)
        %table var(a: int4){ variant }
        %table out(a: int4)
        var(a) :- inp(a, -).
        out(a) :- var(a).
    )";
    DtlVerifyTest(valid3, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，variant与update连用单独使用
    constexpr auto valid4 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int4){ update, variant, index(0(a, b)), max_size(5) }
        %table out(a: int4, b: int4, c: int4)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid4, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，variant出现多次
    constexpr auto valid5 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int4){ variant, transient(tuple), variant }
        %table out(a: int4, b: int4, c: int4)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid5, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，variant后接参数
    constexpr auto valid6 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int4){ transient(tuple), variant(0) }
        %table out(a: int4, b: int4, c: int4)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid6, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，variant与timeout连用
    constexpr auto valid7 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int8){ timeout(field(c), state_function), variant }
        %table out(a: int4, b: int4, c: int8)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid7, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 异常，variant与transient(field)连用
    constexpr auto valid8 = R"(
        %table inp(a: int4, b: int4, c: int4, d: int4)
        %table var(a: int4, b: int4, c: int4){ transient(field(c)), variant }
        %table out(a: int4, b: int4, c: int4)
        var(a, b, c) :- inp(a, b, c, -).
        out(a, b, c) :- var(a, b, c).
    )";
    DtlVerifyTest(valid8, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerWithRuleName)
{
    // 正常
    constexpr auto valid0 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b).
        %rule r00 out(a, b) :- inp2(a, b).
    )";
    DtlCompilerTest(valid0, GMERR_OK, GMERR_OK);

    // 正常
    constexpr auto valid1 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlCompilerTest(valid1, GMERR_OK, GMERR_OK);

    // 换行
    constexpr auto valid2 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule
        r0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlCompilerTest(valid2, GMERR_OK, GMERR_OK);

    // 下划线+换行
    constexpr auto valid3 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %
        rule _999 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlCompilerTest(valid3, GMERR_OK, GMERR_OK);

    // r999 + 换行
    constexpr auto valid4 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %
        rule
        r999
        out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlCompilerTest(valid4, GMERR_OK, GMERR_OK);
    printf("------ compile check end ------\n");

    // ****************词法校验****************
    // %rule非法
    constexpr auto parse0 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b).

        %ruler out(a, b) :- inp2(a, b).

    )";
    DtlParserTest(parse0, GMERR_SYNTAX_ERROR);

    // %rule必须用于规则
    constexpr auto parse1 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)

        %rule out(a: int4, b: int4)
        
        %rule r0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse1, GMERR_SYNTAX_ERROR);

    // 规则名非法
    constexpr auto parse2 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse2, GMERR_SYNTAX_ERROR);

    // 规则名以数字开头
    constexpr auto parse3 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule 999 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse3, GMERR_SYNTAX_ERROR);

    // 规则名有其他字符
    constexpr auto parse4 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule 'r9' out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse4, GMERR_SYNTAX_ERROR);

    // 规则名有其他字符
    constexpr auto parse5 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r-9 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse5, GMERR_SYNTAX_ERROR);

    // 规则名为rule关键字
    constexpr auto parse6 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule rule out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse6, GMERR_SYNTAX_ERROR);

    // 规则名为RULE
    constexpr auto parse6_0 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule RULE out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse6_0, GMERR_OK);

    // 规则名为其他关键字
    constexpr auto parse6_1 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule function out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse6_1, GMERR_SYNTAX_ERROR);

    // 规则名包含rule关键字
    constexpr auto parse7 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule rule1 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse7, GMERR_OK);
    // 长度超限
    DtlVerifyTest(parse7, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 缺少%
    constexpr auto parse8 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        rule out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse8, GMERR_SYNTAX_ERROR);

    // 只有_
    constexpr auto parse9 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule _ out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse9, GMERR_OK);

    // 只有字母
    constexpr auto parse9_0 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule a out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse9_0, GMERR_OK);

    // 下划线开头
    constexpr auto parse9_1 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule _a out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlVerifyTest(parse9_1, GMERR_OK, GMERR_OK);

    // %和rule间空格
    constexpr auto parse10_0 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        % rule r0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse10_0, GMERR_OK);

    constexpr auto parse10_1 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %ru le r0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse10_1, GMERR_SYNTAX_ERROR);

    constexpr auto parse10_2 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r 0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse10_2, GMERR_SYNTAX_ERROR);

    constexpr auto parse11 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule > out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlParserTest(parse11, GMERR_SYNTAX_ERROR);
    printf("------ parser check end ------\n");

    // ****************语法校验****************
    // 长度超过4B
    constexpr auto verify0 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r9999 out(a, b) :- inp1(a, b).
        %rule r0 out(a, b) :- inp2(a, b).
    )";
    DtlVerifyTest(verify0, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 重复定义
    constexpr auto verify1 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b).
        %rule r0 out(a, b) :- inp2(a, b).
    )";
    DtlVerifyTest(verify1, GMERR_OK, GMERR_SEMANTIC_ERROR);

    // 区分大小写
    constexpr auto verify2 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule R0 out(a, b) :- inp1(a, b).
        %rule r0 out(a, b) :- inp2(a, b).
    )";
    DtlVerifyTest(verify2, GMERR_OK, GMERR_OK);

    // 规则重复
    constexpr auto verify3 = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b).
        out(a, b) :- inp1(a, b).
    )";
    DtlVerifyTest(verify3, GMERR_OK, GMERR_OK);

    // 输出表重复
    constexpr auto verify4 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        out(a, b) :- inp1(a, b).
        out(a, b) :- inp2(a, b).
    )";
    DtlVerifyTest(verify4, GMERR_OK, GMERR_OK);

    // 不同nsp下出现相同ruleName
    constexpr auto verify5 = R"(
        namespace nsp0 {
            %table inp1(a: int4, b: int4)
            %table inp2(a: int4, b: int4)
            %table out(a: int4, b: int4)
            out(a, b) :- inp1(a, b).
            %rule r0 out(a, b) :- inp2(a, b).
        }

        namespace nsp1 {
            %table inp1(a: int4, b: int4)
            %table inp2(a: int4, b: int4)
            %table out(a: int4, b: int4)
            out(a, b) :- inp1(a, b).
            %rule r0 out(a, b) :- inp2(a, b).
        }
    )";
    DtlVerifyTest(verify5, GMERR_OK, GMERR_SEMANTIC_ERROR);

    constexpr auto verify6 = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        out(a, b) :- inp1(a, b). %rule
        r0 out(a, b) :- inp2(a, b).
    )";
    DtlVerifyTest(verify6, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// define rule option no_reorder
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder1)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_OK);
}

// invalid rule option
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder2)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b) {invalid_option}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// no rule option
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder3)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b) {}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_OK);
}

// define same rule option multi times
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder4)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b) {no_reorder, no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// rule option包含no_reorder属性时，rule不能存在关系not join
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder5)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), NOT inp2(a, b) {no_reorder, no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// rule option包含no_reorder属性时，rule不能存在关系not join
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder6)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), NOT funcA(a), inp2(a, b) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// 若rule option包含no_reorder，function的入参必须在前序右表的字段中出现
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder7)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, 1) :- inp1(a, 1), funcA(b) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// 若rule option包含no_reorder，function的入参必须在前序右表的字段中出现
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder8)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, -), funcA(b), inp2(a, b) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_SEMANTIC_ERROR);
}

// rule option包含no_reorder，rule join function
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder9)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), funcA(a) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_OK);
}

// rule option包含no_reorder，rule join function
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder10)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), funcA(a), inp2(a, b) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_OK);
}

// rule option包含no_reorder，rule join function
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder11)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), inp2(-, b), funcA(a) {no_reorder}.
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_OK);
}

// rule option包含no_reorder，rule join function
TEST_F(DtlAnalyzerUt, DatalogVerifyRulesPlanNoReorder12)
{
    constexpr auto input = R"(
        %table A(a: int4)
        %table B(a: int4, b: int4, c: int4)
        %table C(a: int4, b: int4)
        %function funcA(a: int4 -> b: int4)
        %function funcB(a: int4)
        %rule r0 B(a, b, 1) :- A(a), funcA(a, b), funcB(b), C(a, b).
    )";
    DtlVerifyTest(input, GMERR_OK, GMERR_OK);

    constexpr auto input2 = R"(
        %table A(a: int4)
        %table B(a: int4, b: int4, c: int4)
        %table C(a: int4, b: int4)
        %function funcA(a: int4 -> b: int4)
        %function funcB(a: int4)
        %rule r0 B(a, b, 1) :- A(a), funcA(a, b), funcB(b), C(a, b) {no_reorder}.
    )";
    DtlVerifyTest(input2, GMERR_OK, GMERR_OK);
}

static void DtlGetRewriterPlans(const char *input, DbListT *deltaPlans)
{
    DtlRawAstT *program = NULL;
    Status ret = DtlParserParse((DbMemCtxT *)analyzerMemCtx, input, &program);
    ASSERT_EQ(GMERR_OK, ret);

    ret = DtlVerify((DbMemCtxT *)analyzerMemCtx, program);
    ASSERT_EQ(GMERR_OK, ret);

    DbListT *plans = NULL;
    ret = DtlTransAst2IR(program, &plans);
    ASSERT_EQ(GMERR_OK, ret);

    ret = DtlQryRewrite(analyzerMemCtx, program, plans, deltaPlans);
    ASSERT_EQ(GMERR_OK, ret);
}

// rule: no_reorder && scan table <= 1
TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder1)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
    uint32_t planCnt = DbListGetItemCnt(&deltaPlans);
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < planCnt; i++) {
        IRPlanT *plan = (IRPlanT *)DbListItem(&deltaPlans, i);
        IRExprT *root = plan->root;
        if (root->op->type == IR_LOGOP_MERGE) {
            continue;
        }
        cnt++;
    }
    EXPECT_EQ(cnt, 1u);
}

// rule: no_reorder && scan table <= 1
TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder2)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %function funcA(in: int4)
        %rule r0 out(a, b) :- inp1(a, b), funcA(a) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
    uint32_t planCnt = DbListGetItemCnt(&deltaPlans);
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < planCnt; i++) {
        IRPlanT *plan = (IRPlanT *)DbListItem(&deltaPlans, i);
        IRExprT *root = plan->root;
        if (root->op->type == IR_LOGOP_MERGE) {
            continue;
        }
        cnt++;
    }
    EXPECT_EQ(cnt, 1u);
}

// rule: no_reorder && scan table > 1 && having scan_expr on table locates at leaf node
TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder3)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %function funcA(in: int4)
        %rule r0 out(a, b) :- inp1(a, b), inp2(a, b), funcA(a) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
    uint32_t planCnt = DbListGetItemCnt(&deltaPlans);
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < planCnt; i++) {
        IRPlanT *plan = (IRPlanT *)DbListItem(&deltaPlans, i);
        IRExprT *root = plan->root;
        if (root->op->type == IR_LOGOP_MERGE) {
            continue;
        }
        IRExprT *topJoin = root->children[0]->children[0];
        IRExprT *topJoinLeft = topJoin->children[0];
        EXPECT_EQ(topJoinLeft->op->type, IR_LOGOP_JOIN);
        EXPECT_EQ(topJoinLeft->children[0]->op->type, IR_LOGOP_SCANWORKLABEL);
        EXPECT_EQ(topJoinLeft->children[1]->op->type, IR_LOGOP_SCAN);
        IRExprT *topJoinRight = topJoin->children[1];
        EXPECT_EQ(topJoinRight->op->type, IR_FUNCOP_UDF);
        cnt++;
    }
    EXPECT_EQ(cnt, 2u);
}

// rule: no_reorder && scan table > 1 && having scan_expr on table locates at leaf node
TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder4)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), inp2(a, b) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
    uint32_t planCnt = DbListGetItemCnt(&deltaPlans);
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < planCnt; i++) {
        IRPlanT *plan = (IRPlanT *)DbListItem(&deltaPlans, i);
        IRExprT *root = plan->root;
        if (root->op->type == IR_LOGOP_MERGE) {
            continue;
        }
        cnt++;
    }
    EXPECT_EQ(cnt, 2u);
}

// rule: no_reorder && scan table > 1 && having scan_expr on table locates at topest join node
TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder5)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), funcA(a), inp2(a, b) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
    uint32_t planCnt = DbListGetItemCnt(&deltaPlans);
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < planCnt; i++) {
        IRPlanT *plan = (IRPlanT *)DbListItem(&deltaPlans, i);
        IRExprT *root = plan->root;
        if (root->op->type == IR_LOGOP_MERGE) {
            continue;
        }
        IRPrintPlan(analyzerMemCtx, plan);
        cnt++;
        IRExprT *topJoin = root->children[0]->children[0];
        IRExprT *topJoinLeft = topJoin->children[0];
        IRExprT *topJoinRight = topJoin->children[1];
        EXPECT_EQ(topJoinLeft->op->type, IR_LOGOP_JOIN);
        if (cnt == 1) {
            EXPECT_EQ(topJoinRight->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)topJoinRight->op;
            EXPECT_STREQ(scan->vertexLabel->metaCommon.metaName, "inp2");
            EXPECT_EQ(topJoinLeft->children[0]->op->type, IR_LOGOP_SCANWORKLABEL);
            OpLogicalAAScanT *workLabelScan = (OpLogicalAAScanT *)(void *)topJoinLeft->children[0]->op;
            EXPECT_STREQ(workLabelScan->vertexLabel->metaCommon.metaName, "inp1");
            EXPECT_EQ(topJoinLeft->children[1]->op->type, IR_FUNCOP_UDF);
        } else {
            EXPECT_EQ(topJoinRight->op->type, IR_FUNCOP_UDF);
            EXPECT_EQ(topJoinLeft->children[0]->op->type, IR_LOGOP_SCANWORKLABEL);
            OpLogicalAAScanT *workLabelScan = (OpLogicalAAScanT *)(void *)topJoinLeft->children[0]->op;
            EXPECT_STREQ(workLabelScan->vertexLabel->metaCommon.metaName, "inp2");
            EXPECT_EQ(topJoinLeft->children[1]->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)topJoinLeft->children[1]->op;
            EXPECT_STREQ(scan->vertexLabel->metaCommon.metaName, "inp1");
        }
    }
    EXPECT_EQ(cnt, 2u);
}

// rule: no_reorder && scan table > 1 && having scan_expr on table locates at middle join node
TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder6)
{
    constexpr auto input = R"(
        %table inp1(a: int4, b: int4)
        %table inp2(a: int4, b: int4)
        %table inp3(a: int4, b: int4)
        %function funcA(in: int4)
        %table out(a: int4, b: int4)
        %rule r0 out(a, b) :- inp1(a, b), funcA(a), inp2(a, b), inp3(a, b) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
    uint32_t planCnt = DbListGetItemCnt(&deltaPlans);
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < planCnt; i++) {
        IRPlanT *plan = (IRPlanT *)DbListItem(&deltaPlans, i);
        IRExprT *root = plan->root;
        if (root->op->type == IR_LOGOP_MERGE) {
            continue;
        }
        IRPrintPlan(analyzerMemCtx, plan);
        cnt++;
        IRExprT *topJoin = root->children[0]->children[0];
        IRExprT *topJoinLeft = topJoin->children[0];
        IRExprT *topJoinRight = topJoin->children[1];
        EXPECT_EQ(topJoinLeft->op->type, IR_LOGOP_JOIN);
        IRExprT *topJoinLeftLeft = topJoin->children[0]->children[0];
        EXPECT_EQ(topJoinLeftLeft->op->type, IR_LOGOP_JOIN);
        IRExprT *topJoinLeftRight = topJoin->children[0]->children[1];
        if (cnt == 1) {
            EXPECT_EQ(topJoinRight->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)topJoinRight->op;
            EXPECT_STREQ(scan->vertexLabel->metaCommon.metaName, "inp3");
            EXPECT_EQ(topJoinLeftRight->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan2 = (OpLogicalAAScanT *)(void *)topJoinLeftRight->op;
            EXPECT_STREQ(scan2->vertexLabel->metaCommon.metaName, "inp2");
            EXPECT_EQ(topJoinLeftLeft->children[0]->op->type, IR_LOGOP_SCANWORKLABEL);
            OpLogicalAAScanT *scan3 = (OpLogicalAAScanT *)(void *)topJoinLeftLeft->children[0]->op;
            EXPECT_STREQ(scan3->vertexLabel->metaCommon.metaName, "inp1");
            EXPECT_EQ(topJoinLeftLeft->children[1]->op->type, IR_FUNCOP_UDF);
        } else if (cnt == 2) {
            EXPECT_EQ(topJoinRight->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)topJoinRight->op;
            EXPECT_STREQ(scan->vertexLabel->metaCommon.metaName, "inp3");
            EXPECT_EQ(topJoinLeftRight->op->type, IR_FUNCOP_UDF);
            EXPECT_EQ(topJoinLeftLeft->children[0]->op->type, IR_LOGOP_SCANWORKLABEL);
            OpLogicalAAScanT *scan2 = (OpLogicalAAScanT *)(void *)topJoinLeftLeft->children[0]->op;
            EXPECT_STREQ(scan2->vertexLabel->metaCommon.metaName, "inp2");
            EXPECT_EQ(topJoinLeftLeft->children[1]->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan3 = (OpLogicalAAScanT *)(void *)topJoinLeftLeft->children[1]->op;
            EXPECT_STREQ(scan3->vertexLabel->metaCommon.metaName, "inp1");
        } else {
            EXPECT_EQ(topJoinRight->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)topJoinRight->op;
            EXPECT_STREQ(scan->vertexLabel->metaCommon.metaName, "inp2");
            EXPECT_EQ(topJoinLeftRight->op->type, IR_FUNCOP_UDF);
            EXPECT_EQ(topJoinLeftLeft->children[0]->op->type, IR_LOGOP_SCANWORKLABEL);
            OpLogicalAAScanT *scan2 = (OpLogicalAAScanT *)(void *)topJoinLeftLeft->children[0]->op;
            EXPECT_STREQ(scan2->vertexLabel->metaCommon.metaName, "inp3");
            EXPECT_EQ(topJoinLeftLeft->children[1]->op->type, IR_LOGOP_SCAN);
            OpLogicalAAScanT *scan3 = (OpLogicalAAScanT *)(void *)topJoinLeftLeft->children[1]->op;
            EXPECT_STREQ(scan3->vertexLabel->metaCommon.metaName, "inp1");
        }
    }
    EXPECT_EQ(cnt, 3u);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder7)
{
    constexpr auto input = R"(
        %table inp1(a:int8, b:int8, c:int8) {index(0(a, b))}
        %table inp2(a:int8, b:int8, c:int8) {index(0(a, b))}
        %table inp3(a:int8, b:int8, c:int8) {index(0(a, b))}
        %table out1(a:int8, b:int8, c:int8) {
            index(0(a, b))
        }
        %table out2(a:int8, b:int8, c:int8) {
            index(0(a, b))
        }
        out1(a, b, c) :- inp1(a, b, -), inp2(a, b, c) {no_reorder}.
        out2(a, b, 1000) :- inp3(a, b, 10), inp1(a, b, -) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
}

TEST_F(DtlAnalyzerUt, DatalogAnalyzerRulesPlanNoReorder8)
{
    constexpr auto input = R"(
        %table inp1(a:int8, b:int8, c:int8) {index(0(a, b))}
        %table inp2(a:int8, b:int8, c:int8) {index(0(a, b))}
        %table inp3(a:int8, b:int8, c:int8) {index(0(a, b))}
        %table out1(a:int8, b:int8, c:int8) {
            index(0(a, b))
        }
        %table out2(a:int8, b:int8, c:int8) {
            index(0(a, b))
        }
        out1(a, b, c) :- inp1(a, b, -), inp2(a, b, c), inp3(a, b, -) {no_reorder}.
        out2(a, b, 1000) :- inp3(a, b, 10), inp1(a, b, -), inp2(a, 1, b) {no_reorder}.
    )";
    DbListT deltaPlans = {0};
    DtlGetRewriterPlans(input, &deltaPlans);
}
