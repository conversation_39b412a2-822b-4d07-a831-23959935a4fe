%version v0.0.0 -> v2.0.0

%block 1
namespace rule_topo2 {

    // 新增表
    %table inpA(a:int4, b:int8, c:int4) {max_size(100), update, index(0(a))}
    %table inpB(a:int4, b:int8, c:int4) {
        update,
        update_by_rank,
        index(0(a)),
        index(1(a, b)),
        index(2(b, c))
    }
    
    // 新增udf
    %function funA(a:int4 -> b:int4)
    
    // 新增规则
    %rule rN0 null(0) :- inpA(-, -, -).
    %rule rN1 null(0) :- inpB(1, 2, -).
    
    // 修改规则
    // 1.新增表
    // 含update表规则升级
    %alter rule r1 inpC0(a, b) :- inpD0(a, b).
    
    // 含resource表规则升级
    %alter rule Res1 resMid(a, -) :- resInp(a, -).
    
    
    // 含transient表规则升级
    %alter rule r3 JC(a, b, c, t) :-  JB(a, b, c, t).

    // 含fast_insert表规则升级
    %alter rule r7 updateE(a, b) :- updateA(a, b).

    // 2.修改表
    // 修改投影顺序
    %alter rule rulA normalOutA(c, b, a) :- 
            normalA(c, b, a, 1).

    // 修改join字段
    %alter rule rulB normalOutB(a, b, c) :- normalB(a, b), normalC(a, c, b).

    // not join
    %alter rule rulN outU(a, b) :- updateF(a, b), NOT updateG(a, b).

    // 3.新增udf
    // 普通udf
    %alter rule r0 inpB0(a, b) :- inpA0(a, b), funA(a, b).

    // 4.修改udf（只能修改udf实现）
    %alter rule rAgg aggOut(a, c) :- aggInp(a, b) GROUP-BY(a) aggA(b, c).
    %alter rule rulU outU1(a, b) :- inpU1(a, b), tupleFilter(a, b).

}

// 新增readonly
namespace upgrade4Nsp {
    %readonly B4RuleTopoA
}

namespace complex1{
    // 含tbm表规则升级
    %alter rule r6p tbmOut(a, b) :- transientJoin(a, b).

    // 含外部表规则升级
    %alter rule r3C External(1,2):-C(3,1,2,4).
}

namespace complex2{
    // 含pubsub表规则升级
    %alter rule r6y outSimple(a, b) :- inpSimple(a, b).
}
 
// 综合
namespace rule_topo {
    %alter rule rt0u B4RuleTopo(a,b,d) :- A4RuleTopo(a,b,1), FA4RuleTopo(a,b,d).
}
namespace upgradeFunc{
    using namespace rule_topo2
    %alter rule oldX outNsp(c, b) :- 
            inpANsp(a, 1), inpBNsp(a, b), filterNsp(a, b, c), readCapsetKVNsp(a,b).
}



namespace P1 {
    %alter rule P1r1 out(a, b, c, d, e) :- inpA(a, b, c, d, e).
}

namespace P2 {
    
    %alter rule P2r1 midA(a, b, c) :- inpA(c, b, a), inpB(a, b, c), inpC(a, b, c).
}
        
