#include "query_ut_base.h"
#include "qry_sysview.h"
#include "sysview_instance.h"
#include "srv_data_service.h"
#include "srv_data_public.h"

class UtView : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtView, ViewGetHeapStat)
{
    uint32_t ret;
    /* RpcMsgHeader | stmtId | fetchNum | labelNameLen | labelName | filterLen | filterBuf */
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"V$STORAGE_HEAP_STAT";
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    uint32_t headerOffset = 0;
    ret = FixBufReserveDataOffset(&req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffset);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // labelId
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, 1));  // isView
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, 0));  // isCsMode
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // scan flag
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // left indexKey
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // right indexKey
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // condition
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // result set
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // sort count
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, 0));  // limit count

    DbFillMsgHeader(&req, MSG_OP_RPC_GET_VERTEX_LABEL, headerOffset, CS_NEVER_TIMEOUT, 0);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    MsgHeaderT *msgHeader = RpcGetMsgHeader(&req);
    EXPECT_TRUE(msgHeader != NULL);
    msgHeader->stmtId = 0;
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtView, ViewGetFsmStat)
{
    uint32_t ret;
    /* RpcMsgHeader | stmtId | fetchNum | labelNameLen | labelName | filterLen | filterBuf */
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"V$STORAGE_FSM_STAT";
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    uint32_t headerOffset = 0;
    ret = FixBufReserveDataOffset(&req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffset);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // labelId
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, 1));  // isView
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, 0));  // isCsMode
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // scan flag
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // left indexKey
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // right indexKey
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // condition
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // result set
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // sort count
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, 0));  // limit count

    DbFillMsgHeader(&req, MSG_OP_RPC_GET_VERTEX_LABEL, headerOffset, CS_NEVER_TIMEOUT, 0);

    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    MsgHeaderT *msgHeader = RpcGetMsgHeader(&req);
    EXPECT_TRUE(msgHeader != NULL);
    msgHeader->stmtId = 0;
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtView, ViewGetMemDataStat)
{
    uint32_t ret;
    /* RpcMsgHeader | stmtId | fetchNum | labelNameLen | labelName | filterLen | filterBuf */
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"V$STORAGE_MEMDATA_STAT";
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    uint32_t headerOffset = 0;
    ret = FixBufReserveDataOffset(&req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffset);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // labelId
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, 1));  // isView
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, 0));  // isCsMode
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // scan flag
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // left indexKey
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // right indexKey
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // condition
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // result set
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // sort count
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, 0));  // limit count

    DbFillMsgHeader(&req, MSG_OP_RPC_GET_VERTEX_LABEL, headerOffset, CS_NEVER_TIMEOUT, 0);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    MsgHeaderT *msgHeader = RpcGetMsgHeader(&req);
    EXPECT_TRUE(msgHeader != NULL);
    msgHeader->stmtId = 0;
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}
