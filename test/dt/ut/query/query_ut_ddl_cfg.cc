#include "query_ut_base.h"
#include "srv_data_prepare.h"
#include "db_text.h"
#include "ee_dql_meta.h"
#include "ee_ddl_cfg.h"
#include "db_log_ctrl.h"

class UtQueryDDLCfg : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();

        dyAlgoCtxVertex = QryGetDyAlgoCtxVertexBase();
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

Status QryTestCfgSet(const char *cfgName, const DbValueT *dbValue)
{
    Status ret;
    FixBufferT req;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbMemCtxT *dyAlgoCtxVertex = QryGetDyAlgoCtxVertexBase();

    ret = (Status)FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgHeaderT *msgHeader;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
    msgHeader->opNum = 1;

    TextT t;
    t.len = strlen(cfgName) + 1;
    t.str = (char *)cfgName;
    ret = (Status)FixBufPutText(&req, &t);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecutePutDbValue(&req, dbValue);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_SET_CFG,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    QryTestReleaseSession(conn);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

Status QryTestCfgGet(const char *cfgName, FixBufferT **rsp)
{
    Status ret;
    FixBufferT req;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbMemCtxT *dyAlgoCtxVertex = QryGetDyAlgoCtxVertexBase();
    ret = (Status)FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgHeaderT *msgHeader;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
    msgHeader->opNum = 1;

    TextT t;
    t.len = strlen(cfgName) + 1;
    t.str = (char *)cfgName;
    ret = (Status)FixBufPutText(&req, &t);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_CFG,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);

    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
    QryTestReleaseSession(conn);

    clearAllStub();
    return ret;
}

Status QryCheckValueFromRsp(FixBufferT *rsp, const DbValueT *expectValue)
{
    DbValueT value;

    TextT t;
    Status ret = FixBufGetObject(rsp, &t);
    EXPECT_EQ(GMERR_OK, ret);

    if (expectValue != NULL) {
        ret = DmValueDeSeriNoCopy((uint8_t *)t.str, t.len, &value);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (value.type == DB_DATATYPE_INT32) {
            EXPECT_EQ(expectValue->value.intValue, value.value.intValue);
        } else {
            EXPECT_STREQ((char *)expectValue->value.strAddr, (char *)value.value.strAddr);
        }
    }

    return GMERR_OK;
}

#define GET_CFG_RSP_FIELD_CNT 7u
Status QryTestCfgCheckRsp(const void **rspFieldArray, uint32_t fieldCnt, FixBufferT *rsp)
{
    EXPECT_EQ(fieldCnt, GET_CFG_RSP_FIELD_CNT);
    if (fieldCnt != GET_CFG_RSP_FIELD_CNT) {
        return GMERR_DATA_EXCEPTION;
    }

    int32_t idx = 0;
    const char *cfgName;
    const char *cfgDesc;
    const DbValueT *curValue;
    const DbValueT *minValue;
    const DbValueT *maxValue;
    const DbValueT *defaultValue;
    const uint32_t *changeMode;

    cfgName = (char *)rspFieldArray[idx++];
    cfgDesc = (char *)rspFieldArray[idx++];
    curValue = (DbValueT *)rspFieldArray[idx++];
    minValue = (DbValueT *)rspFieldArray[idx++];
    maxValue = (DbValueT *)rspFieldArray[idx++];
    defaultValue = (DbValueT *)rspFieldArray[idx++];
    changeMode = (uint32_t *)rspFieldArray[idx++];

    TextT getText;
    FixBufSeek(rsp, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);

    // check cfgName
    int32_t ret = FixBufGetText(rsp, &getText);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return (Status)ret;
    }
    if (cfgName != NULL) {
        EXPECT_STREQ(cfgName, getText.str);
    }

    // get desc
    ret = FixBufGetText(rsp, &getText);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return (Status)ret;
    }
    if (cfgDesc != NULL) {
        EXPECT_STREQ(cfgDesc, getText.str);
    }

    // get cfg data type
    uint32_t cfgType;
    ret = FixBufGetUint32(rsp, &cfgType);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(cfgType, curValue->type);

    // get cfg cur value
    ret = QryCheckValueFromRsp(rsp, curValue);
    EXPECT_EQ(GMERR_OK, ret);

    // min value
    ret = QryCheckValueFromRsp(rsp, minValue);
    EXPECT_EQ(GMERR_OK, ret);

    // max value
    ret = QryCheckValueFromRsp(rsp, maxValue);
    EXPECT_EQ(GMERR_OK, ret);

    // default value
    ret = QryCheckValueFromRsp(rsp, defaultValue);
    EXPECT_EQ(GMERR_OK, ret);

    // change mode
    uint32_t value;
    ret = FixBufGetUint32(rsp, &value);
    EXPECT_EQ(GMERR_OK, ret);
    if (changeMode != NULL) {
        EXPECT_EQ(value, *changeMode);
    }
    return GMERR_OK;
}

TEST_F(UtQueryDDLCfg, QRY_DDL_CFG_NoneExist_logFileNumNoneExist)
{
    uint32_t ret;
    TextT *lastErr;
    const char *result;

    FixBufferT *rsp = NULL;

    const char *cfgName = "logFileNumNoneExist";
    DbValueT cfgValue;
    cfgValue.type = DB_DATATYPE_INT32;
    cfgValue.value.intValue = 16;

    // get fail because of logFileNumNoneExist
    ret = QryTestCfgGet(cfgName, &rsp);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    lastErr = DbGetLastErrorInfo();
    result = (char *)"Syntax unsucc. config name logFileNumNoneExist.";
    EXPECT_STREQ(result, lastErr->str);

    // set fail because of logFileNumNoneExist
    cfgValue.value.intValue = 64;
    ret = QryTestCfgSet(cfgName, &cfgValue);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    lastErr = DbGetLastErrorInfo();
    result = (char *)"Syntax unsucc. config name logFileNumNoneExist.";
    EXPECT_STREQ(result, lastErr->str);

    QryReleaseRsp(rsp);
}

TEST_F(UtQueryDDLCfg, QRY_DDL_CFG_TYPE_INT32Check_logFoldMode)
{
    uint32_t ret;

    FixBufferT *rsp = NULL;

    const char *cfgName = "enableLogFold";
    const char *cfgDesc = "log fold switch";

    DbValueT cfgValue;
    cfgValue.type = DB_DATATYPE_INT32;
    cfgValue.value.intValue = 1;

    DbValueT minValue;
    minValue.type = DB_DATATYPE_INT32;
    minValue.value.intValue = 0;

    DbValueT maxValue;
    maxValue.type = DB_DATATYPE_INT32;
    maxValue.value.intValue = 1;

    DbValueT defaultValue;
    defaultValue.type = DB_DATATYPE_STRING;
    defaultValue.value.strAddr = (void *)"1";
    defaultValue.value.length = strlen((char *)defaultValue.value.strAddr) + 1;

    uint32_t changeMode = 3;

    const void *rspField[GET_CFG_RSP_FIELD_CNT] = {
        cfgName, cfgDesc, &cfgValue, &minValue, &maxValue, &defaultValue, &changeMode};

    ret = QryTestCfgGet(cfgName, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgCheckRsp(rspField, GET_CFG_RSP_FIELD_CNT, rsp);
    EXPECT_EQ(GMERR_OK, ret);
    QryReleaseRsp(rsp);

    // set ok
    cfgValue.value.intValue = 0;
    ret = QryTestCfgSet(cfgName, &cfgValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgGet(cfgName, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgCheckRsp(rspField, GET_CFG_RSP_FIELD_CNT, rsp);
    EXPECT_EQ(GMERR_OK, ret);
    QryReleaseRsp(rsp);

    // set to default
    cfgValue.value.intValue = 1;
    ret = QryTestCfgSet(cfgName, &cfgValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgGet(cfgName, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgCheckRsp(rspField, GET_CFG_RSP_FIELD_CNT, rsp);
    EXPECT_EQ(GMERR_OK, ret);

    QryReleaseRsp(rsp);
}

void TestCheckAuditFromShm(const char *cfgName, int32_t setVal)
{
    DbAuditCtrlItemT *ctrlItem = DbLogGetAuditCtrlItem();
    if (strcmp(cfgName, "auditLogEnableDCL") == 0) {
        EXPECT_EQ(ctrlItem->auditTypeSwitch[DB_AUDIT_DCL], setVal);
    } else if (strcmp(cfgName, "auditLogEnableDDL") == 0) {
        EXPECT_EQ(ctrlItem->auditTypeSwitch[DB_AUDIT_DDL], setVal);
    } else if (strcmp(cfgName, "auditLogEnableDML") == 0) {
        EXPECT_EQ(ctrlItem->auditTypeSwitch[DB_AUDIT_DML], setVal);
    }
}

void TestCheckSetAudit(const char *cfgName, int32_t minVal, int32_t maxVal, const char *defaultVal, int32_t setVal)
{
    uint32_t ret;

    FixBufferT *rsp = NULL;

    DbValueT cfgValue;
    cfgValue.type = DB_DATATYPE_INT32;
    cfgValue.value.intValue = atoi(defaultVal);

    DbValueT minValue;
    minValue.type = DB_DATATYPE_INT32;
    minValue.value.intValue = minVal;

    DbValueT maxValue;
    maxValue.type = DB_DATATYPE_INT32;
    maxValue.value.intValue = maxVal;

    DbValueT defaultValue;
    defaultValue.type = DB_DATATYPE_STRING;
    defaultValue.value.strAddr = (void *)defaultVal;
    defaultValue.value.length = strlen((char *)defaultValue.value.strAddr) + 1;

    uint32_t changeMode = 3;  // No limit

    const void *rspField[GET_CFG_RSP_FIELD_CNT] = {
        cfgName, NULL, &cfgValue, &minValue, &maxValue, &defaultValue, &changeMode};

    // set ok
    cfgValue.value.intValue = setVal;
    ret = QryTestCfgSet(cfgName, &cfgValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgGet(cfgName, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgCheckRsp(rspField, GET_CFG_RSP_FIELD_CNT, rsp);
    EXPECT_EQ(GMERR_OK, ret);
    QryReleaseRsp(rsp);
    TestCheckAuditFromShm(cfgName, cfgValue.value.intValue);

    // set to default
    cfgValue.value.intValue = atoi(defaultVal);
    ret = QryTestCfgSet(cfgName, &cfgValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgGet(cfgName, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCfgCheckRsp(rspField, GET_CFG_RSP_FIELD_CNT, rsp);
    EXPECT_EQ(GMERR_OK, ret);
    QryReleaseRsp(rsp);
    TestCheckAuditFromShm(cfgName, cfgValue.value.intValue);
}

TEST_F(UtQueryDDLCfg, QRY_DDL_CFG_AuditLog)
{
    TestCheckSetAudit("auditLogEnableDCL", 0, 1, "1", 0);
    TestCheckSetAudit("auditLogEnableDDL", 0, 1, "1", 0);
    TestCheckSetAudit("auditLogEnableDML", 0, 1, "1", 0);
    TestCheckSetAudit("auditLogEnableDQL", 0, 1, "1", 0);
}
