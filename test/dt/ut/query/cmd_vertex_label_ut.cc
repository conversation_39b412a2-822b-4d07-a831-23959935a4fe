#include <limits.h>
#include "gtest/gtest.h"
#include <vector>
#include <time.h>
#include <stdlib.h>
#include <string>
#include "adpt_atomic.h"
#include <securec.h>
#include "db_common_init.h"
#include "adpt_memory.h"
#include "db_mem_context.h"
#include "dm_meta_basic.h"
#include "ee_cmd.h"
#include "ee_ddl_vertex_label.h"
#include "ee_ddl_edge_label.h"
#include "stub.h"
#include "query_ut_base.h"
#include "ut_dm_fuzz_test.h"
#include "ut_catalog_base.h"
#include "ee_cmd_vertex_label_template.h"

#ifdef FEATURE_DATALOG
#include "ptl_data_service.h"
#include "ee_timeout_subject.h"
#include "ptl_handler.h"
#endif

SessionT *g_session = NULL;

using namespace std;

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

DbMemCtxT *memCtxTempVertex = NULL;
class UtCmdVertexLabel : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        memCtxTempVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo((DbMemCtxT *)memCtxTempVertex);

        // 因为所有的元数据创建和删除都需要维护所属namespace的labelCount,同时ut中元数据的namespaceId设置都为0，
        // 因此在测试环境的初始化中创建了id为0的namespace
        DmNamespaceT *namespaceInfo = CreateNspWithId(0, (DbMemCtxT *)memCtxTempVertex);
        EXPECT_EQ(GMERR_OK, CataCreateNamespace(namespaceInfo, NULL));
        Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &g_session);
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        QrySessionRelease(g_session);
        DbDeleteDynMemCtx((DbMemCtxT *)memCtxTempVertex);
        BaseUninit();
    };

    static Status CmdGetVertexLabelBufferByNameAndVersion(const CataKeyT *cataKey, uint32_t versionId, DmBuffer *buffer)
    {
        DB_POINTER2(cataKey, buffer);
        Status ret;
        // 从Catalog中查询相应点标签的元数据
        DmVertexLabelT *vertexLabel = NULL;
        if (versionId != DM_SCHEMA_INVALID_VERSION && versionId != DM_SCHEMA_MIN_VERSION) {
            ret = CataGetVertexLabelByNameAndVersion(NULL, cataKey, versionId, &vertexLabel);
        } else {
            if (versionId == DM_SCHEMA_MIN_VERSION) {
                ret = CataGetOldestVertexLabelByName(cataKey, &vertexLabel, NULL);
            } else {
                ret = CataGetVertexLabelByName(NULL, cataKey, &vertexLabel);
            }
        }
        if (ret != GMERR_OK) {
            return ret;
        }
        // 序列化vertexLabel得到buffer
        ret = DmSerializeVertexLabel(vertexLabel, buffer);
        // 释放该元数据
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtCmdVertexLabel, SaveAndDropWithRelatedEdge)
{
    // 创建两个vertexLabel并创建一个边与它们关联
    DmVertexLabel *vertexLabel1 = NULL;
    DmVertexLabel *vertexLabel2 = NULL;
    DmEdgeLabel *edgeLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(1, &vertexLabel1));
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(2, &vertexLabel2));
    EXPECT_EQ(GMERR_OK, DmGetFuzzEdgeLabel(3, vertexLabel1, vertexLabel2, &edgeLabel));
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel1, &tempLabel));
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel2, &tempLabel));
    EXPECT_EQ(GMERR_OK, QryCreateSingleEdgeLabel(NULL, edgeLabel));

    // 尝试去truncate或drop，预期报错
    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel1->metaCommon.namespaceId, vertexLabel1->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel1));
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, CmdDropOrTruncateVertexLabelCheck(vertexLabel1));
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char *)"Violated the constraint restrictions. drop|truncate vertex label because the vertex "
                                 "label's related edge label still exists. Exists and edge label num is 1.";
    EXPECT_STREQ(result, lastError->str);

    // 先get edge label
    DmEdgeLabelT *retEdgeLabel = NULL;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, edgeLabel->metaCommon.namespaceId, edgeLabel->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetEdgeLabelByName(&cataKey, &retEdgeLabel, NULL));
    // 再remove edge label, 此时edge label refCount为1，isDeleted为true
    EXPECT_EQ(GMERR_OK, CataRemoveEdgeLabelByName(NULL, &cataKey));
    // 此时drop vertex label，此时关联的edge label refCount不为0，isDeleted为true，可以删除
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel1->metaCommon.namespaceId, vertexLabel1->metaCommon.metaName);
    (void)CataReleaseVertexLabel(vertexLabel1);
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel1));
    (void)CmdDropVertexLabel(g_session->seInstance, vertexLabel1);
    EXPECT_EQ(GMERR_OK, CataReleaseEdgeLabel(retEdgeLabel));

    // 删除vertexLabel2
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel2->metaCommon.namespaceId, vertexLabel2->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel2));
    (void)CataReleaseVertexLabel(vertexLabel2);
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel2));
    (void)CmdDropVertexLabel(g_session->seInstance, vertexLabel2);
}

TEST_F(UtCmdVertexLabel, SaveAndDrop)
{
    // 创建vertexLabel
    DmVertexLabel *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(1, &vertexLabel));
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxTempVertex;

    // 通过name获取vertexLabel
    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CmdGetVertexLabelBufferByNameAndVersion(&cataKey, DM_SCHEMA_INVALID_VERSION, &buffer));

    // 反序列化之后判断是否相等
    DmVertexLabelT *retVertexLabel;
    EXPECT_EQ(GMERR_OK, DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel));
    EXPECT_EQ(true, CmpVertexLabel(vertexLabel, retVertexLabel));

    // 删除VertexLabel
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel));
    (void)CmdDropVertexLabel(g_session->seInstance, vertexLabel);
    EXPECT_EQ(
        GMERR_UNDEFINED_TABLE, CmdGetVertexLabelBufferByNameAndVersion(&cataKey, DM_SCHEMA_INVALID_VERSION, &buffer));
}

TEST_F(UtCmdVertexLabel, SaveAndDropWithResource)
{
    // 创建vertexLabel并绑定rescol
    DmVertexLabel *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabelWithCheckInfo(1, &vertexLabel, false));
    ShmemPtrT resPool = {1, 1};
    vertexLabel->commonInfo->resColInfo = (DmResColInfoT *)DbDynMemAlloc(sizeof(DmResColInfoT));
    (void)memset_s(vertexLabel->commonInfo->resColInfo, sizeof(DmResColInfoT), 0x00, sizeof(DmResColInfoT));
    vertexLabel->commonInfo->resColInfo->resColPool = resPool;
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxTempVertex;
    (void)buffer;
    // 删除VertexLabel，此时资源池未解绑，删除失败
    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
    DmVertexLabelT *vertexLabel1 = NULL;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel1));
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, CmdDropOrTruncateVertexLabelCheck(vertexLabel1));
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char *)"Violated the constraint restrictions. drop|truncate vertex label because the vertex "
                                 "label's related resource pool still exists.";
    EXPECT_STREQ(result, lastError->str);
    (void)CataReleaseVertexLabel(vertexLabel1);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, QryTruncateVertexLabelByName(NULL, g_session->seInstance, &cataKey));

    // 删除vertexLabel，此时refcount=0，直接物理删除
    EXPECT_EQ(GMERR_OK, CataRemoveVertexLabelById(NULL, vertexLabel->metaCommon.metaId));

    // 再次创建不绑定资源池的label
    resPool = {DB_INVALID_UINT32, DB_INVALID_UINT32};
    vertexLabel->commonInfo->resColInfo->resColPool = resPool;
    EXPECT_EQ(GMERR_OK, CataSaveVertexLabel(vertexLabel, &tempLabel));
    tempLabel->metaCommon.isCreated = true;

    // 删除VertexLabel，此时无绑定的资源池，删除成功
    EXPECT_EQ(GMERR_OK, QryTruncateVertexLabelByName(NULL, g_session->seInstance, &cataKey));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel));
    (void)CmdDropVertexLabel(g_session->seInstance, vertexLabel);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, QryTruncateVertexLabelByName(NULL, g_session->seInstance, &cataKey));
}

TEST_F(UtCmdVertexLabel, SaveAndGetByName)
{
    // 创建vertexLabel
    DmVertexLabel *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(1, &vertexLabel));
    vertexLabel->metaVertexLabel->pkIndex = NULL;

    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxTempVertex;
    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CmdGetVertexLabelBufferByNameAndVersion(&cataKey, DM_SCHEMA_INVALID_VERSION, &buffer));

    // 反序列化之后判断是否相等
    DmVertexLabelT *retVertexLabel;
    EXPECT_EQ(GMERR_OK, DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel));
    EXPECT_EQ(true, CmpVertexLabel(vertexLabel, retVertexLabel));

    // 删除VertexLabel
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel));
    (void)CmdDropVertexLabel(g_session->seInstance, vertexLabel);
    EXPECT_EQ(
        GMERR_UNDEFINED_TABLE, CmdGetVertexLabelBufferByNameAndVersion(&cataKey, DM_SCHEMA_INVALID_VERSION, &buffer));
}

Status AllocMetaInfoStub(DmReusableMetaT **metaInfoAddr, uint32_t labelId)
{
    return GMERR_INTERNAL_ERROR;
}

#ifdef FEATURE_DATALOG
// 提升覆盖率不要删除
TEST_F(UtCmdVertexLabel, RollBack)
{
    EXPECT_EQ(GMERR_OK, InitTimeoutSubject(NULL, ExecuteDatalogDmlWithLock, HandleCommit, HandleRollBack));

    DmVertexLabel *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(1, &vertexLabel));

    int stubIndex = setStubC((void *)AllocMetaInfo, (void *)AllocMetaInfoStub);

    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_NORMAL;
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->commonInfo->datalogLabelInfo->withTimeout = true;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));
    vertexLabel->commonInfo->datalogLabelInfo->withTimeout = false;

    vertexLabel->commonInfo->datalogLabelInfo->labelType = DM_DTL_TBM;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->commonInfo->datalogLabelInfo->labelType = DM_DTL_MSG_NOTIFY;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->commonInfo->datalogLabelInfo->labelType = DM_DTL_EXTERN;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->commonInfo->datalogLabelInfo = NULL;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_YANG;
    DmYangInfoT yangInfo = {};
    vertexLabel->metaVertexLabel->schema->yangInfo = &yangInfo;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->metaVertexLabel->schema->yangInfo = NULL;
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_SYSVIEW;
    EXPECT_EQ(GMERR_INTERNAL_ERROR, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    clearStub(stubIndex);
}
#endif

#ifdef EXPERIMENTAL_TSDB
TEST_F(UtCmdVertexLabel, CreateAndRemoveTsLogicLabel)
{
    DmVertexLabel *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(1, &vertexLabel));
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_TS_LOGICAL;
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->metaVertexLabel->pkIndex = NULL;
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    vertexLabel->metaVertexLabel->secIndexes = NULL;
    vertexLabel->metaVertexLabel->secIndexNum = 0;
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
    DmVertexLabelT *retVertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &retVertexLabel));

    EXPECT_EQ(false, DbIsShmPtrValid(vertexLabel->commonInfo->heapInfo.heapShmAddr));

    (void)CataReleaseVertexLabel(retVertexLabel);

    // 删除vertexLabel，此时refcount=0，直接物理删除
    EXPECT_EQ(GMERR_OK, CataRemoveVertexLabelById(NULL, vertexLabel->metaCommon.metaId));
}

TEST_F(UtCmdVertexLabel, SaveAndDropTsLogicLabelRepeatedly)
{
    // 创建VertexLabel
    DmVertexLabel *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(1, &vertexLabel));
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_TS_LOGICAL;
    vertexLabel->metaVertexLabel->pkIndex = NULL;
    vertexLabel->metaVertexLabel->secIndexes = NULL;
    vertexLabel->metaVertexLabel->secIndexNum = 0;
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));

    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, DEFAULT_DATABASE_ID, vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
    DmVertexLabelT *retVertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &retVertexLabel));

    EXPECT_EQ(false, DbIsShmPtrValid(vertexLabel->commonInfo->heapInfo.heapShmAddr));

    // 删除VertexLabel
    (void)CataReleaseVertexLabel(retVertexLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_session->seInstance, vertexLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel));
    EXPECT_EQ(GMERR_OK, SeTransCommit(g_session->seInstance));

    EXPECT_NE(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &retVertexLabel));
    EXPECT_EQ(GMERR_OK, CmdCreateVertexLabel(g_session->seInstance, vertexLabel, &tempLabel));
    EXPECT_EQ(false, DbIsShmPtrValid(vertexLabel->commonInfo->heapInfo.heapShmAddr));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(vertexLabel));
    EXPECT_NE(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &retVertexLabel));
}
#endif
