/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: query ut for dcl
 * Create: 2022-09-21
 */

#include <iostream>
#include "ee_schedule.h"
#include "query_ut_base.h"
#include "se_trx.h"
#include "se_define.h"
#include "se_trx_inner.h"

#ifdef __cplusplus
extern "C" {
#endif
Status QryCheckTrxTypeAndIsolationLevel(QryStmtT *stmt, TrxCfgT *cfg);
#ifdef __cplusplus
}
#endif

using namespace std;
class UtQueryDCL : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

void ReserveMsgHeaderAndInit(FixBufferT *req, MsgHeaderT **msgHeader)
{
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, NULL, NULL));
    (*msgHeader)->reqTimeOut = CS_NEVER_TIMEOUT;
    (*msgHeader)->stmtId = 0;
}

void HelpFillOpHeader(FixBufferT *req, MsgOpcodeRpcE opCode)
{
    uint32_t dataLen = req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), opCode, dataLen);
}

static Status HelpFillString(FixBufferT *buf, bool handleNull, const char *str)
{
    Status ret = GMERR_OK;
    if (str != NULL) {
        uint32_t size = (uint32_t)strlen(str) + 1;
        ret = FixBufPutRawText(buf, size, (const uint8_t *)str);
    } else if (handleNull) {
        ret = FixBufPutUint32(buf, 0);
    }
    return ret;
}

Status QryCheckTrxTypeSameStub(QryStmtT *stmt, TrxCfgT *cfg)
{
    return GMERR_OK;
}

static Status QryServiceProccessRequestTest(DrtConnectionT *conn, FixBufferT *req, MsgHeaderT *msgHeader)
{
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)QryCheckTrxTypeAndIsolationLevel, (void *)QryCheckTrxTypeSameStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}
static Status PublicServiceProccessRequestTest(DrtConnectionT *conn, FixBufferT *req, MsgHeaderT *msgHeader)
{
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx;
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)QryCheckTrxTypeAndIsolationLevel, (void *)QryCheckTrxTypeSameStub);
    return PublicServiceEntry(&serviceCtx, &procCtx);
}

static void GenerateLegalStringExceededLimit(char *str, uint32_t limit)
{
    int32_t z = (int32_t)'z';
    int32_t a = (int32_t)'a';
    int32_t wordInterval = z - a;
    for (uint32_t i = 0; i < limit - 1; i++) {
        str[i] = (char)(a + i % wordInterval);
    }
    str[limit - 1] = '\0';
}

#ifndef FEATURE_PERSISTENCE
TEST_F(UtQueryDCL, QRY_DCL_CreateSavepointIllegalChar)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, ".S><?!@$#"));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_INVALID_NAME, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_CreateSavepointExceededLength)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    char maxSavepointName[MAX_SAVEPOINT_LENGTH + 1];
    GenerateLegalStringExceededLimit(maxSavepointName, MAX_SAVEPOINT_LENGTH + 1);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, maxSavepointName));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_INVALID_NAME, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_CreateSavepointNull)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_TX_ISOLATION_REPEATABLE));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_OPTIMISTIC_TRX));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // isGetCloneId
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_START);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, NULL));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_CreateDuplicateSavepoint)
{
    // 1. 开启事务
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_TX_ISOLATION_REPEATABLE));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_OPTIMISTIC_TRX));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // isGetCloneId
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_START);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 2. 第一次创建DuplicateSavepoint
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, "DuplicateSavepoint"));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 3. 第二次创建DuplicateSavepoint
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, "DuplicateSavepoint"));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_ReleaseSavepointIllegalChar)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, ".S><?!@$#"));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_INVALID_NAME, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_ReleaseSavepointExceededLength)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    char maxSavepointName[MAX_SAVEPOINT_LENGTH + 1];
    GenerateLegalStringExceededLimit(maxSavepointName, MAX_SAVEPOINT_LENGTH + 1);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, maxSavepointName));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_INVALID_NAME, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_ReleaseSavepointNull)
{
    // 1. 开启事务
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_TX_ISOLATION_REPEATABLE));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_OPTIMISTIC_TRX));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // isGetCloneId
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_START);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 2. 创建匿名savepoint
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, NULL));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 3. 释放匿名savepoint
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, NULL));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_RELEASE_SAVEPOINT);

    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_RollbackSavepointIllegalChar)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, ".S><?!@$#"));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_INVALID_NAME, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_RollbackSavepointExceededLength)
{
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    char maxSavepointName[MAX_SAVEPOINT_LENGTH + 1];
    GenerateLegalStringExceededLimit(maxSavepointName, MAX_SAVEPOINT_LENGTH + 1);

    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, maxSavepointName));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);

    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_INVALID_NAME, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_RollbackSavepointNull)
{
    // 1. 开启事务
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_TX_ISOLATION_REPEATABLE));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_OPTIMISTIC_TRX));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // isGetCloneId
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_START);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 2. 创建匿名savepoint
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, NULL));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 3. 回滚匿名savepoint
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillString(&req, true, NULL));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT);
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}
#endif

TEST_F(UtQueryDCL, QRY_DCL_AbortTransWithoutForceAuth)
{
    // 1. 开启事务
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, 1024));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_ABORT);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_AbortTransNoActive)
{
    EXPECT_EQ(GMERR_OK, QrySetCfg("userPolicyMode", "2"));
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, 1024));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_ABORT);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_NO_ACTIVE_TRANSACTION, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

Status QryAddLongTrxRollBackTaskStub()
{
    return GMERR_OK;
}

TEST_F(UtQueryDCL, QRY_DCL_AbortTransSucc)
{
    // 1. 开启事务
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);

    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_TX_ISOLATION_REPEATABLE));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, GMC_OPTIMISTIC_TRX));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));  // isGetCloneId
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_START);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 2. 中断事务
    EXPECT_EQ(GMERR_OK, QrySetCfg("userPolicyMode", "2"));
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    Session *session = (Session *)conn->session;
    SeRunCtxT *seRunCtx = (SeRunCtxT *)session->seInstance;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, trx->base.trxId));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_TX_ABORT);
    (void)setStubC((void *)QryAddLongTrxRollBackTask, (void *)QryAddLongTrxRollBackTaskStub);
    EXPECT_EQ(GMERR_OK, PublicServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

static Status HelpFillTableSpaceMsg(FixBufferT *buf, const char *tablespace, uint16_t initSize, uint16_t stepSize,
    uint16_t maxSize, uint16_t isUseRsm = 0, uint16_t isPersistent = 0)
{
    Status ret = HelpFillString(buf, true, tablespace);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(buf, initSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(buf, stepSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(buf, maxSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(buf, isUseRsm);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint16(buf, isPersistent);
}

#ifndef FEATURE_PERSISTENCE
TEST_F(UtQueryDCL, QRY_DCL_CreateTablespaceIllegal)
{
    // 1. 非法字符
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, ".S><?!@$#", 4, 4, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_INVALID_NAME, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 2.非法长度
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    char maxTableSpaceName[MAX_TABLE_SPACE_LENGTH + 1];
    GenerateLegalStringExceededLimit(maxTableSpaceName, MAX_TABLE_SPACE_LENGTH + 1);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, maxTableSpaceName, 4, 4, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_INVALID_NAME, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 3. 空tableSpaceName
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, NULL, 4, 4, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 4. initSize非deviceSize的整数倍
    EXPECT_EQ(GMERR_OK, QrySetCfg("deviceSize", "4"));
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablspace", 5, 4, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 5. stepSize非deviceSize的整数倍
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablspace", 4, 5, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 6. maxSize非deviceSize的整数倍
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablspace", 4, 4, 9));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 7. initSize > maxSize
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablspace", 8, 4, 4));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 7. initSize > maxSize
    EXPECT_EQ(GMERR_OK, QrySetCfg("deviceSize", "3"));
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablspace", 9, 6, 12));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, QryServiceProccessRequestTest(conn, &req, msgHeader));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_CreateTablespaceSucc)
{
    EXPECT_EQ(GMERR_OK, QrySetCfg("deviceSize", "4"));
    // 1. 用户输入有效值创建成功
    MsgHeaderT *msgHeader = NULL;
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablespace", 4, 4, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 2.step, init, max全为0值，自动填充，创建成功
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablespace1", 0, 0, 0));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_OK, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 3.step为0值，自动填充，创建成功
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablespace2", 0, 4, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_OK, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 4.init为0值，自动填充，创建成功
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablespace3", 4, 0, 8));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_OK, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);

    // 5.max为0值，自动填充，创建成功
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    ReserveMsgHeaderAndInit(&req, &msgHeader);
    EXPECT_EQ(GMERR_OK, HelpFillTableSpaceMsg(&req, "tablespace4", 4, 4, 0));
    msgHeader->size = FixBufGetPos(&req);
    HelpFillOpHeader(&req, MSG_OP_RPC_CREATE_TABLESPACE);
    EXPECT_EQ(GMERR_OK, QryServiceProccessRequestTest(conn, &req, msgHeader));
    FixBufRelease(&req);
    QryTestReleaseSession(conn);
}
#endif

TEST_F(UtQueryDCL, QRY_DCL_VerifySetLogLevel)
{
    DrtConnectionT *conn = NULL;
    Status ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    EXPECT_EQ(GMERR_OK, ret);

    QrySetLogCtrlDescT *desc =
        (QrySetLogCtrlDescT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(QrySetLogCtrlDescT));
    EXPECT_TRUE(desc != NULL);
    session->currentStmt->context->type = QRY_TYPE_SET_LOG_LEVEL;
    session->currentStmt->context->entry = (void *)desc;

    char *processName = (char *)"ut_query";
    desc->processName.str = processName;
    // 进程名长度非法
    desc->processName.len = DB_MAX_PROC_NAME_LEN + 1;
    ret = QryVerifySetLogLevel(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    // 进程名字符串与字符串长度不匹配
    desc->processName.len = strlen(processName) - 1;
    ret = QryVerifySetLogLevel(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    desc->processName.len = strlen(processName) + 1;

    // 日志级别非法
    desc->logCtrlVal = DB_LOG_LVL_BUTT;
    ret = QryVerifySetLogLevel(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCL, QRY_DCL_VerifySetLogSwitch)
{
    DrtConnectionT *conn = NULL;
    Status ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    EXPECT_EQ(GMERR_OK, ret);

    QrySetLogCtrlDescT *desc =
        (QrySetLogCtrlDescT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(QrySetLogCtrlDescT));
    EXPECT_TRUE(desc != NULL);
    session->currentStmt->context->type = QRY_TYPE_SET_LOG_SWITCH;
    session->currentStmt->context->entry = (void *)desc;

    char *processName = (char *)"ut_query";
    desc->processName.str = processName;
    // 进程名长度非法
    desc->processName.len = DB_MAX_PROC_NAME_LEN + 1;
    ret = QryVerifySetLogSwitch(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    // 进程名字符串与字符串长度不匹配
    desc->processName.len = strlen(processName) - 1;
    ret = QryVerifySetLogSwitch(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    desc->processName.len = strlen(processName) + 1;

    // 日志开关非法
    desc->logCtrlVal = 2;
    ret = QryVerifySetLogSwitch(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 关闭日志时候，startCycle或duration非法
    desc->logCtrlVal = 0;
    desc->startCycle = 1;
    desc->duration = 0;
    ret = QryVerifySetLogSwitch(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    desc->startCycle = 0;
    desc->duration = 1;
    ret = QryVerifySetLogSwitch(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    QryTestReleaseSession(conn);
}
