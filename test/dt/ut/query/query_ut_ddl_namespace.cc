#include "query_ut_base.h"
#include "db_text.h"

class UtQueryDDLNamespace : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};
int32_t UtQueryDDLNamespaceNotExisted(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = (MsgHeaderT *)msg->buf;
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, header->opStatus);

    return GMERR_OK;
}

int32_t UtQueryDDLNamespaceNormal(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = (MsgHeaderT *)msg->buf;
    EXPECT_EQ(GMERR_OK, header->opStatus);

    return GMERR_OK;
}

int32_t UtQueryDDLNamespaceInvalidLen(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = (MsgHeaderT *)msg->buf;
    EXPECT_EQ(GMERR_DATA_EXCEPTION, header->opStatus);

    return GMERR_OK;
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_CreateNamespaceError)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen(".S><?!@$#") + 1, .str = (char *)".S><?!@$#"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    TextT userName = {.len = strlen("user_create") + 1, .str = (char *)"user_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &userName));
    TextT tspName = {.len = strlen("public") + 1, .str = (char *)"public"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &tspName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_TX_ISOLATION_DEFAULT));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_DEFAULT_TRX));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_CreateNamespaceSucc)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("azAZ09.:-_'\"") + 1, .str = (char *)"azAZ09.:-_'\""};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    TextT userName = {.len = strlen("user_create") + 1, .str = (char *)"user_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &userName));
    TextT tspName = {.len = strlen("public") + 1, .str = (char *)"public"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &tspName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_TX_ISOLATION_DEFAULT));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_DEFAULT_TRX));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

static void CreateNamespace(FixBufferT *req)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("namespace_create") + 1, .str = (char *)"namespace_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &namespaceName));

    TextT userName = {.len = strlen("user_create") + 1, .str = (char *)"user_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &userName));
    TextT tspName = {.len = strlen("public") + 1, .str = (char *)"public"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &tspName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, (uint32_t)GMC_TX_ISOLATION_DEFAULT));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, (uint32_t)GMC_DEFAULT_TRX));

    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_CREATE_NAMESPACE,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceNormal);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    FixBufRelease(req);
}

static void DropNamespace(FixBufferT *req, DbMemCtxT *memCtx)
{
    FixBufCreate(req, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("namespace_create") + 1, .str = (char *)"namespace_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &namespaceName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, (uint32_t)GMC_TX_ISOLATION_DEFAULT));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, (uint32_t)GMC_DEFAULT_TRX));

    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_DROP_NAMESPACE,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceNormal);

    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_CreateAndDropNamespace)
{
    CreateNamespace(&req);
    DropNamespace(&req, dyAlgoCtxVertex);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_CreateNamespaceInvalidLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = 0, .str = (char *)"namespace_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    TextT userName = {.len = strlen("user_create") + 1, .str = (char *)"user_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &userName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceInvalidLen);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Data exception occurs. get nsp name when parse create nsp.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_CreateNamespaceVerifyName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("user_create"), .str = (char *)"namespace_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    TextT userName = {.len = strlen("user_create") + 1, .str = (char *)"user_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &userName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_DropNamespaceInvalidLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = 0, .str = (char *)"namespace_drop"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceInvalidLen);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Data exception occurs. get nsp name when parse drop nsp.", lastError->str);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_DropNamespaceVerifyName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("namespace_drop"), .str = (char *)"namespace_drop"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_UseNamespace)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("namespace_use") + 1, .str = (char *)"namespace_use"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceNotExisted);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_UseNamespaceInvalidLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = 0, .str = (char *)"namespace_use"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceInvalidLen);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Data exception occurs. get nsp name when parse drop nsp.", lastError->str);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_UseNamespaceVerifyName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("namespace_use"), .str = (char *)"namespace_use"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_UseNamespaceExceedLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = 129, .str = (char *)"namespace_use"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_CreateNamespaceExceedLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = 129, .str = (char *)"namespace_use"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLNamespace, QRY_DDL_DropNamespaceExceedLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = 129, .str = (char *)"namespace_use"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    QryTestReleaseSession(conn);
}
