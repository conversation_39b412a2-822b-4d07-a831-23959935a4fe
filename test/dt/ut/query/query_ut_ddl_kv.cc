#include "query_ut_base.h"
#include "cpl_base_def.h"
#include "db_text.h"
#include "ee_ddl_kv.h"

namespace query_ut {

DbMemCtxT *dyAlgoCtxKv;
class UtQueryDDLKvTable : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        DbMemCtxArgsT args = {0};
        dyAlgoCtxKv = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        FixBufCreate(&req, dyAlgoCtxKv, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxKv);
        clearAllStub();
    }
};

char *kvTableFirst = (char *)"KV_TABLE_1";
char *kvInvalidTable = (char *)"KV_INVALID_TABLE";

void CreateTableSuccess()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *tableName = kvTableFirst;
    ret = QryTestCreateKvTable(tableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
}

void CreateKvSubsWithInsertType()
{
    char *subsName = (char *)"sub_kv1";
    char *subsJson = (char *)R"({
            "label_name":"KV_TABLE_1",
            "comment":"the KV_TABLE_1 sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ]
        })";

    // err events for kv label
    uint32_t ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. inv Event type of the kv table";
    EXPECT_STREQ(result, lastError1->str);
}

void CreateKvSubsWithUpdateType()
{
    char *subsName = (char *)"sub_kv1";
    char *subsJson = (char *)R"({
            "label_name":"KV_TABLE_1",
            "comment":"the KV_TABLE_1 sub",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]}
                ]
        })";

    // err events for kv label
    uint32_t ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. inv Event type of the kv table";
    EXPECT_STREQ(result, lastError1->str);
}

void CreateKvSubsWithReplaceType()
{
    char *subsName = (char *)"sub_kv1";
    char *subsJson = (char *)R"({
            "label_name":"KV_TABLE_1",
            "comment":"the KV_TABLE_1 sub",
            "events":
                [
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ]
        })";

    // err events for kv label
    uint32_t ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. inv Event type of the kv table";
    EXPECT_STREQ(result, lastError1->str);
}

void CreateKvSubsWithAgeType()
{
    char *subsName = (char *)"sub_kv1";
    char *subsJson = (char *)R"({
            "label_name":"KV_TABLE_1",
            "comment":"the KV_TABLE_1 sub",
            "events":
                [
                    {"type":"age", "msgTypes":["new object", "old object"]}
                ]
        })";

    // err events for kv label
    uint32_t ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. inv Event type of the kv table";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLKvTable, CreateTableFail)
{
    CreateTableSuccess();
    CreateKvSubsWithInsertType();
    CreateKvSubsWithUpdateType();
    CreateKvSubsWithReplaceType();
    CreateKvSubsWithAgeType();
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *tableName1 = (char *)"<>?#@!";
    EXPECT_EQ(GMERR_INVALID_NAME, QryTestCreateKvTable(tableName1, cfgJson));
    char *tableName2 = kvTableFirst;
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, QryTestCreateKvTable(tableName2, cfgJson));
    char *tableName = kvTableFirst;
    EXPECT_EQ(GMERR_OK, QryTestDropKvTable(tableName));
}

void FillMsgHeader(MsgHeaderT *msgHeader)
{
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
}

void FillTableId(FixBufferT *req, uint32_t tableId)
{
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, tableId));
}

void FillTableName(FixBufferT *req, char *tableName)
{
    TextT putText;
    putText.str = tableName;
    putText.len = strlen(tableName) + 1;
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &putText));
}

void FillOpHeader(FixBufferT *req, uint32_t opCode)
{
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(req), opCode, FixBufGetPos(req) - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

Status ServiceProcReq(DrtConnectionT *conn, MsgHeaderT *msgHeader, FixBufferT *req)
{
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

TEST_F(UtQueryDDLKvTable, GetKvTable)
{
    CreateTableSuccess();
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    FillTableId(&req, 0);
    FillTableName(&req, kvTableFirst);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_GET_KV_TABLE);
    DrtConnectionT *conn = NULL;
    FixBufferT *rsp = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, &rsp));
    EXPECT_EQ(GMERR_OK, ServiceProcReq(conn, msgHeader, &req));

    RpcSeekFirstOpMsg(rsp);
    RpcGetLabelRspT *getLabelRsp = (RpcGetLabelRspT *)FixBufGetData(rsp, 0);
    size_t bufferLen = sizeof(ShmemPtrT) + sizeof(ShmemPtrT) + sizeof(uint32_t);
    EXPECT_EQ((uint32_t)bufferLen, getLabelRsp->labelBufLen);
    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
    char *tableName = kvTableFirst;
    EXPECT_EQ(GMERR_OK, QryTestDropKvTable(tableName));
}

static inline uint32_t ExtractLabelType(uint32_t type)
{
    return type & 0xFF;
}

TEST_F(UtQueryDDLKvTable, GetTableByName)
{
    CreateTableSuccess();
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    uint32_t labelId = 0;
    FillTableId(&req, labelId);
    FillTableName(&req, kvTableFirst);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0u));
#endif
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_GET_LABEL);
    DrtConnectionT *conn = NULL;
    FixBufferT *rsp = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, &rsp));
    EXPECT_EQ(GMERR_OK, ServiceProcReq(conn, msgHeader, &req));

    RpcSeekFirstOpMsg(rsp);
    uint32_t labelType;
    TextT rspLabel;
    FixBufGetUint32(rsp, &labelType);
    FixBufGetObject(rsp, &rspLabel);
    labelType = ExtractLabelType(labelType);
    EXPECT_EQ((uint32_t)KV_TABLE, labelType);
    size_t bufferLen = sizeof(ShmemPtrT) + sizeof(ShmemPtrT) + sizeof(uint32_t);
    EXPECT_EQ((uint32_t)bufferLen, rspLabel.len);
    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
    char *tableName = kvTableFirst;
    EXPECT_EQ(GMERR_OK, QryTestDropKvTable(tableName));
}

TEST_F(UtQueryDDLKvTable, GetNotExistTable)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    FillTableId(&req, 0);
    FillTableName(&req, kvInvalidTable);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_GET_KV_TABLE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ServiceProcReq(conn, msgHeader, &req));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLKvTable, DropTable)
{
    CreateTableSuccess();
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    FillTableName(&req, kvTableFirst);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_DROP_KV_TABLE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    EXPECT_EQ(GMERR_OK, ServiceProcReq(conn, msgHeader, &req));
    QryTestReleaseSession(conn);
}

#define TABLE_NAME_MAX_LEN 64
#define MAX_TABLE_NUM 1024

TEST_F(UtQueryDDLKvTable, Create1024Table)
{
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char tableName[TABLE_NAME_MAX_LEN];
    for (int i = 0; i < 1100; i++) {
        (void)memset_s(tableName, TABLE_NAME_MAX_LEN, 0x0, TABLE_NAME_MAX_LEN);
        (void)sprintf_s(tableName, TABLE_NAME_MAX_LEN, "%s%d", kvTableFirst, i);
        uint32_t ret = QryTestCreateKvTable(tableName, cfgJson);
        if (i < MAX_TABLE_NUM) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        }
    }
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackDropKvTableStub);
    for (int i = 0; i < MAX_TABLE_NUM; i++) {
        (void)memset_s(tableName, TABLE_NAME_MAX_LEN, 0x0, TABLE_NAME_MAX_LEN);
        (void)sprintf_s(tableName, TABLE_NAME_MAX_LEN, "%s%d", kvTableFirst, i);
        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        FillMsgHeader(msgHeader);
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        FillTableName(&req, tableName);
        msgHeader->size = FixBufGetPos(&req);
        FillOpHeader(&req, MSG_OP_RPC_DROP_KV_TABLE);
        EXPECT_EQ(GMERR_OK, ServiceProcReq(conn, msgHeader, &req));
        FixBufResetMem(&req);
        EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxKv, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));
    }
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLKvTable, CreateGlobalKvTable)
{
    uint32_t ret;
    ret = QryTestCreateKvTable((char *)GLOBAL_KV_TABLE_NAME, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
}

TEST_F(UtQueryDDLKvTable, DropGlobalKvTable)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    FillTableName(&req, (char *)GLOBAL_KV_TABLE_NAME);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_DROP_KV_TABLE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_INVALID_NAME, ServiceProcReq(conn, msgHeader, &req));
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. kv table T_GMDB is global.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLKvTable, TruncateTable)
{
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *tableName = (char *)"truncateKvTable";
    uint32_t ret = QryTestCreateKvTable(tableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    FillTableName(&req, tableName);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_TRUNCATE_KV_TABLE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_OK, ServiceProcReq(conn, msgHeader, &req));
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLKvTable, TruncateNotExistTable)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    // name 长度为0
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    FillTableName(&req, kvInvalidTable);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_TRUNCATE_KV_TABLE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_INVALID_NAME, ServiceProcReq(conn, msgHeader, &req));
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal name. table name len is 0 when parse truncate", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLKvTable, TruncateTableNameTooLong)
{
    char *tableName = (char *)"kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh"
                              "kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh"
                              "kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh"
                              "kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh"
                              "kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh"
                              "kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh"
                              "kvInvalidTablegfhjjjjjjjjjjddddddddddddddddddddsmfhffffffvfhjdjghhhhhhdgjmhdgkjh";
    ;
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    FillMsgHeader(msgHeader);
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    FillTableName(&req, tableName);
    msgHeader->size = FixBufGetPos(&req);
    FillOpHeader(&req, MSG_OP_RPC_TRUNCATE_KV_TABLE);
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ServiceProcReq(conn, msgHeader, &req));
    TextT *lastError = DbGetLastErrorInfo();
    uint32_t resultLen = 1024;
    char result[resultLen] = {0};
    sprintf_s((char *)result, sizeof(result), "Name is too long. kv table: %s len exceeds, limit: 512.", tableName);
    EXPECT_STREQ(result, lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLKvTable, CreateTableWithDisableSubBackPressure)
{
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    char *cfgJson = (char *)R"({"max_record_count":1000, "disable_sub_back_pressure" : 2})";
    char *tableName = (char *)"kvTable";
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, QryTestCreateKvTable(tableName, cfgJson));
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch. The bool value should be true, false, 0 or 1.Type: disable_sub_back_pressure "
                 "should be bool.",
        lastError->str);
    QryTestReleaseSession(conn);
}

Status CataGetLabelByIdStub(uint32_t labelId, DmKvLabelT **label, void *dbInstance)
{
    return GMERR_OK;
}

Status CataCreateClientRefOnListSuc(
    CataCreateClientRefCtxT *ctx, TagLinkedListT *pidRefList, DmMetaCommonT *label, CataClientRefT **pidRef)
{
    label = (DmMetaCommonT *)malloc(sizeof(DmKvLabelT));
    return GMERR_OK;
}

Status CataCreateClientRefOnListFail(
    CataCreateClientRefCtxT *ctx, TagLinkedListT *pidRefList, DmMetaCommonT *label, CataClientRefT **pidRef)
{
    return GMERR_INTERNAL_ERROR;
}

Status CataReleaseLabelStub(DmKvLabelT *label)
{
    return GMERR_OK;
}

TEST_F(UtQueryDDLKvTable, QryGetKvTableByIdTest)
{
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));

    int id1 = setStubC((void *)CataGetLabelById, (void *)CataGetLabelByIdStub);
    int id2 = setStubC((void *)CataCreateClientRefOnList, (void *)CataCreateClientRefOnListFail);
    int id3 = setStubC((void *)CataReleaseLabel, (void *)CataReleaseLabelStub);

    QryStmt stmt = {0};

    QryContext context = {0};

    QryGetKvTableDescT kvTableDesc = {0};
    kvTableDesc.labelId = 1;

    stmt.context = &context;
    context.entry = &kvTableDesc;
    stmt.session = (Session *)conn->session;

    (void)QryExecuteGetKvTable(&stmt);

    clearStub(id1);
    clearStub(id2);
    clearStub(id3);
}

}  // namespace query_ut
