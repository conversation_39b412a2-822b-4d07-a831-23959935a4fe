#include "query_ut_base.h"
#include "adpt_init.h"
#include "adpt_atomic.h"
#include "db_text.h"
#include "db_sysapp_context.h"
#include "common_init.h"
#include "cpl_base_def.h"
#include "ee_check.h"
#include "ee_feature_import.h"
#include "ee_init.h"
#include "ee_ddl_user.h"
#include "ee_dcl_priv.h"
#include "ee_dcl_ctrl.h"
#include "ee_concurrency_control.h"
#include "se_define.h"
#include "ee_session.h"
#include "se_resource_session_pub.h"
#include "db_log_ctrl.h"
#include "ee_fastpath.h"
#include "db_label_latch_mgr.h"
#include "srv_data_alarm.h"
#include "ut_dm_common.h"
#include "srv_data_prepare.h"
#include "srv_data_fastpath_prepare.h"
#include "srv_data_service.h"
#include "db_sysapp_context.h"
#include "srv_data_service.h"
#include "srv_data_public.h"
#include "srv_data_yang.h"
#include "se_persist_inner.h"
#include "ee_systbl.h"
#include "db_mem_context_pool.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef FEATURE_PERSISTENCE
extern Status SysTableInitCompilerHelperImpl(QryStmtT *stmt);
extern Status SysTableInitExecutorHelperImpl(QryStmtT *stmt);
#endif

static uint32_t curStmtID = -1;
void QryTestSetCurStmtID(uint32_t stmtID)
{
    curStmtID = stmtID;
}

uint32_t QryTestGetCurStmtID()
{
    return curStmtID;
}

static void UtDrtDetachConnection(DrtConnectionT *conn)
{
    DB_POINTER2(conn, conn->connMgr);
    conn->nodeId.nodeId = DB_INVALID_ID16;
    (void)DbAtomicDec(&conn->ref);
    DrtSetConnStatus(conn, CONN_STATUS_CLOSED);
    DrtConnProcessClosedList(conn->connMgr);
}

void DbFillMsgHeader(const FixBufferT *fixBuffer, int opCode, uint32_t headerOff, int32_t reqTimeOut, uint32_t stmtId)
{
    memset(fixBuffer->buf + headerOff, 0, sizeof(MsgHeaderT));
    // fill head
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, serviceId, DRT_SERVICE_STMT);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, size, fixBuffer->pos);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, reqTimeOut, reqTimeOut);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, reqStartTime, (uint64_t)DbToUseconds(DbRdtsc()));
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, serialNumber, 1);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, stmtId, stmtId);
    uint8_t hdrFlag = ((MsgHeaderT *)(fixBuffer->buf + headerOff))->flags;
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, flags, hdrFlag & (~CS_FLAG_SELF_SCHEDULE));
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(fixBuffer), opCode,
        fixBuffer->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

// init storage module
int32_t DbInitStorage(DbMemCtxT *topShmMemCtx)
{
    // use defalut config without config file
    SeConfigT config = {0};
    Status ret = StorageConfigGet(NULL, &config, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    printf("config.maxTrxNum:%d----------------------\n", config.maxTrxNum);
    SeInstanceHdT se = NULL;
    return SeCreateInstance(NULL, (DbMemCtxT *)topShmMemCtx, &config, &se);
}

void DbUninitStorage(void)
{
    SeDestroyInstance(GET_INSTANCE_ID);
}

static DbMemCtxT *dyAlgoCtxVertexBase = NULL;
static const char *baseSubsChanneName = "baseSubsChanneName";
static DrtConnectionT *baseSubsConn = NULL;

DbMemCtxT *QryGetDyAlgoCtxVertexBase()
{
    return dyAlgoCtxVertexBase;
}

int32_t QrySetCfg(const char *configName, const char *configValue)
{
    DbCfgMgrHandleT handle = DbGetCfgHandle(NULL);
    return DbCfgSetByNameInner(handle, configName, configValue, false, true);
}

extern void SvServiceInit(void);
extern Status PublicRegisterAlarmTask(void);

int32_t BaseInitWithoutSystbl(bool isFastReadUncommitted, bool persistentMode)
{
    int32_t ret;
    CommonInit();
    ret = CommonInitDrtInstanceInEuler(false);
    if (ret != GMERR_OK) {
        printf("Drt instance init failed!\n");
        return ret;
    }
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    DrtDataPlaneT *plane = SaAllocDataPlane(&drtIns->sendAgent, {0, 1023});
    if (plane == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    // 初始化过程获取pageSize
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    if (cfgHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(NO_DATA_NULL_POINTER, "Config handle is null.");
        return NO_DATA_NULL_POINTER;
    }
    int32_t cfgValue;
    DbCfgMgrT *mgr = DbGetCfgHandle(NULL);
    DbCfgGetInt32(mgr, DB_CFG_SE_PAGE_SIZE, true, &cfgValue);
    ret = QrySetCfg("DBA", "root:gmrule;gmips");
    if (ret != GMERR_OK) {
        printf("set DBA from admin:gmrule;gmips to root:gmrule;gmips failed\n");
        return ret;
    }
    printf("[WARN] set DBA from admin:gmrule;gmips to root:gmrule;gmips\n");

    if (persistentMode) {
        ret = QrySetCfg("persistentMode", "1");
        if (ret != GMERR_OK) {
            printf("set persistentMode from 0 to 1 failed\n");
            return ret;
        }
        system("rm -rf ./data/gmdb/");
        system("mkdir -p ./data/gmdb/");
        ret = QrySetCfg("dataFileDirPath", "./data/gmdb/");
        if (ret != GMERR_OK) {
            printf("set dataFileDirPath to ./data/gmdb/ failed\n");
            return ret;
        }
        SeSetPersistMode(PERSIST_INCREMENT);
    }

    ret = CataLabelCacheInitWithOutTimer(NULL);
    if (ret != GMERR_OK) {
        // logging
        printf("catalog cache init failed!\n");
        return ret;
    }

    DbMemCtxT *topShmemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    EXPECT_TRUE(topShmemCtx != NULL);
    ret = DbInitStorage(topShmemCtx);
    if (ret != GMERR_OK) {
        // logging
        printf("storage init failed!\n");
        return ret;
    }

    ret = DataServiceInit(NULL);
    if (ret != GMERR_OK) {
        printf("service init failed!\n");
        return ret;
    }

    ret = CompilerInit();
    if (ret != GMERR_OK) {
        printf("compiler init failed!\n");
        return ret;
    }

    ret = ExecutorInit(NULL);
    if (ret != GMERR_OK) {
        printf("executor init failed!\n");
        return ret;
    }

    ret = DbServerInitCltStat();
    if (ret != GMERR_OK) {
        // logging
        printf("qry clt cache init failed!\n");
        return ret;
    }
    QrySetMemCtxPool(NULL, DbCreateDynMemCtxPool(true, (DbMemCtxT *)DbGetTopDynMemCtx(NULL)));
    if (QryGetMemCtxPool(NULL) == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = DbAuditSetEvtSwitch(DB_AUDIT_DML, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    SvServiceInit();

    ret = QrySetCfg("userPolicyMode", "1");
    QrySetCfg("compatibleV3", "0");
    if (isFastReadUncommitted) {
        QrySetCfg("isFastReadUncommitted", "1");
    } else {
        QrySetCfg("isFastReadUncommitted", "0");
    }
    DbMemCtxArgsT args = {0};
    dyAlgoCtxVertexBase =
        DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    (void)DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertexBase);

    ret = PublicRegisterAlarmTask();
    if (ret != GMERR_OK) {
        printf("Alarm timer fail to register. ret = %d\n", ret);
        return ret;
    }

    return QryTestAllocSubConn(&baseSubsConn, baseSubsChanneName);
}

#ifdef FEATURE_PERSISTENCE
void InitSysTableUt()
{
    DbSetServerThreadFlag();
    // on-demand persistence also use this function, persist mode set in create instance step.
    system("rm -rf /data/gmdb");
    SePersistAmInit();
    SysTableInit(NULL);

    SessionT *session = NULL;
    QryStmtT *sysTableInitStmt = NULL;
    EXPECT_EQ(GMERR_OK, QrySessionAlloc(SESSION_TYPE_NO_CONN, 1024, NULL, &session));
    EXPECT_EQ(GMERR_OK, ExecutorInitStmt(session, &sysTableInitStmt));
    session->currentStmt = sysTableInitStmt;
    EXPECT_EQ(GMERR_OK, SysTableInitCompilerHelperImpl(sysTableInitStmt));
    EXPECT_EQ(GMERR_OK, SysTableInitExecutorHelperImpl(sysTableInitStmt));
    QrySessionRelease(session);
}
#endif

int32_t BaseInit(bool isFastReadUncommitted, bool persistentMode)
{
    Status ret = BaseInitWithoutSystbl(isFastReadUncommitted, persistentMode);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef FEATURE_PERSISTENCE
    InitSysTableUt();
#endif
    return GMERR_OK;
}

int32_t BaseInitWithConfigFile(const char *configFileName, bool isFastReadUncommitted)
{
    int32_t ret;
    CommonInit(configFileName);
    ret = CommonInitDrtInstanceInEuler(false);
    if (ret != GMERR_OK) {
        printf("Drt instance init failed!\n");
        return ret;
    }
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    DrtDataPlaneT *plane = SaAllocDataPlane(&drtIns->sendAgent, {0, 1023});
    if (plane == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    // 初始化过程获取pageSize
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    if (cfgHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(NO_DATA_NULL_POINTER, "Config handle is null.");
        return NO_DATA_NULL_POINTER;
    }
    int32_t cfgValue;
    DbCfgMgrT *mgr = DbGetCfgHandle(NULL);
    DbCfgGetInt32(mgr, DB_CFG_SE_PAGE_SIZE, true, &cfgValue);
    ret = QrySetCfg("DBA", "root:gmrule;gmips");
    if (ret != GMERR_OK) {
        printf("set DBA from admin:gmrule;gmips to root:gmrule;gmips failed\n");
        return ret;
    }
    printf("[WARN] set DBA from admin:gmrule;gmips to root:gmrule;gmips\n");
    ret = CataLabelCacheInitWithOutTimer(NULL);
    if (ret != GMERR_OK) {
        // logging
        printf("catalog cache init failed!\n");
        return ret;
    }

    DbMemCtxT *topShmemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    EXPECT_TRUE(topShmemCtx != NULL);
    ret = DbInitStorage(topShmemCtx);
    if (ret != GMERR_OK) {
        // logging
        printf("storage init failed!\n");
        return ret;
    }

    ret = DataServiceInit(NULL);
    if (ret != GMERR_OK) {
        printf("service init failed!\n");
        return ret;
    }

    ret = CompilerInit();
    if (ret != GMERR_OK) {
        printf("compiler init failed!\n");
        return ret;
    }

    ret = ExecutorInit(NULL);
    if (ret != GMERR_OK) {
        printf("executor init failed!\n");
        return ret;
    }

    ret = DbServerInitCltStat();
    if (ret != GMERR_OK) {
        // logging
        printf("qry clt cache init failed!\n");
        return ret;
    }
    QrySetMemCtxPool(NULL, DbCreateDynMemCtxPool(true, (DbMemCtxT *)DbGetTopDynMemCtx(NULL)));
    if (QryGetMemCtxPool(NULL) == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = DbAuditSetEvtSwitch(DB_AUDIT_DML, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    SvServiceInit();

    ret = QrySetCfg("userPolicyMode", "1");
    QrySetCfg("compatibleV3", "0");
    if (isFastReadUncommitted) {
        QrySetCfg("isFastReadUncommitted", "1");
    } else {
        QrySetCfg("isFastReadUncommitted", "0");
    }
    DbMemCtxArgsT args = {0};
    dyAlgoCtxVertexBase =
        DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    (void)DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertexBase);

    ret = QryTestAllocSubConn(&baseSubsConn, baseSubsChanneName);
    if (ret != GMERR_OK) {
        printf("Alarm timer fail to register. ret = %d\n", ret);
        return ret;
    }
#ifdef FEATURE_PERSISTENCE
    InitSysTableUt();
#endif
    return GMERR_OK;
}

void BaseUninit4SysTableUt()
{
    QryTestReleaseSubConn(baseSubsConn);
    baseSubsConn = NULL;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    SeRunCtxHdT seRunCtx = NULL;
    Status ret = SeOpen((uint16_t)seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, &seRunCtx);
    if (ret == GMERR_OK) {
        SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
        DataServiceUnInit(NULL);
        (void)SeClose(seRunCtx);
    }
    if (dyAlgoCtxVertexBase != NULL) {
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase);
    }

    DrtInstanceDestroy(NULL);
    DbUninitStorage();
    DbDestroyTopShmemCtx(GET_INSTANCE_ID);
    CommonRelease();
    clearAllStub();
}

void BaseUninit()
{
#ifdef FEATURE_PERSISTENCE
    BaseUninit4SysTableUt();
    system("rm -rf /data/gmdb");
#else
    QryTestReleaseSubConn(baseSubsConn);
    baseSubsConn = NULL;
    DataServiceUnInit(NULL);
    if (dyAlgoCtxVertexBase != NULL) {
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase);
    }

    DrtInstanceDestroy(NULL);
    DbUninitStorage();
    DbDestroyTopShmemCtx(GET_INSTANCE_ID);
    CommonRelease();
    clearAllStub();
#endif
}

uint8_t DrtGetConnFlowCtrlLevelStub(DrtConnectionT *conn)
{
    return 0;
}

void DrtFreeMsgStub(FixBufferT *msg)
{
    return;
}

Status HeapLabelGetPerfStatStub(
    HeapCntrAcsInfoT *heapCntrAcsInfo, HeapPerfStatT *heapPerfStat, HeapCfgStatT *heapCfgStat)
{
    *heapPerfStat = (HeapPerfStatT){};
    *heapCfgStat = (HeapCfgStatT){};
    return GMERR_OK;
}

MsgHeaderT *DrtConnWritePackBegin(const DrtConnectionT *conn, bool isRpcGetOpHeader)
{
    SessionT *session = (SessionT *)conn->session;
    FixBufferT *rsp = QrySessionGetRsp(session);
    FixBufSeek(rsp, 0);
    MsgHeaderT *header = RpcGetMsgHeader(rsp);
    if (isRpcGetOpHeader) {
        (void)RpcGetOpHeader(rsp);
    }
    return header;
}

int32_t DrtConnWritePackStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = DrtConnWritePackBegin(conn, true);
    if (header->opStatus == GMERR_OK) {
        QryTestSetCurStmtID(header->stmtId);
    }
    return GMERR_OK;
}

Status ExpandChannelCapacityStub(DmSubscriptionT *sub)
{
    return GMERR_OK;
}

void DrtFreeProcCtxStub(DrtProcCtxT *procCtx)
{
    return;
}

void DrtLongOpLogStub(DrtProcCtxT *procCtx, uint64_t sendTime)
{
    return;
}

int32_t DrtConnWritePackDropKvTableStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = DrtConnWritePackBegin(conn, false);
    EXPECT_EQ(GMERR_OK, header->opStatus);
    if (header->opStatus == GMERR_OK) {
        QryTestSetCurStmtID(header->stmtId);
    }
    uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
    EXPECT_EQ((uint32_t)0, datalen);
    return GMERR_OK;
}

int32_t DrtConnWritePackTruncateKvTableStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = DrtConnWritePackBegin(conn, false);
    EXPECT_EQ(GMERR_OK, header->opStatus);
    if (header->opStatus == GMERR_OK) {
        QryTestSetCurStmtID(header->stmtId);
    }
    return GMERR_OK;
}

int32_t DrtConnWritePackFailStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = DrtConnWritePackBegin(conn, false);
    EXPECT_NE(GMERR_OK, header->opStatus);
    return GMERR_OK;
}

extern "C" {
Status AddSubsUserCbFuncName2Conn(DmSubscriptionT *subscription);
void DelSubsUserCbFuncNameFromConn(DmSubscriptionT *subs);
Status BuildStatusMergeRsp(QryStmtT *stmt, DmSubscriptionT *sub);
Status ExpandChannelCapacity(DmSubscriptionT *subscription);
}

Status AddSubsUserCbFuncName2ConnStub(DmSubscriptionT *subscription)
{
    return GMERR_OK;
}

void AddSubsUserCbFuncNameStub(void)
{
    (void)setStubC((void *)AddSubsUserCbFuncName2Conn, (void *)AddSubsUserCbFuncName2ConnStub);
}

void DelSubsUserCbFuncNameFromConnStub(DmSubscriptionT *subs)
{
    return;
}

void DelSubsUserCbFuncNameStub(void)
{
    (void)setStubC((void *)DelSubsUserCbFuncNameFromConn, (void *)DelSubsUserCbFuncNameFromConnStub);
}

Status BuildStatusMergeRspStub(QryStmtT *stmt, DmSubscriptionT *sub)
{
    return GMERR_OUT_OF_MEMORY;
}

int32_t UtSendDeleteVertexMsg(DrtConnectionT *conn, FixBufferT *req, DeleteVertexMsgT *delMsg)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags |= CS_FLAG_SPLIT_DISABLED;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, delMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, delMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, delMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, delMsg->indexId));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &delMsg->condBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, delMsg->scanFlag));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &delMsg->leftFilterBuf));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &delMsg->rightFilterBuf));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, delMsg->autoOperateFlag));
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_DELETE_VERTEX,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();

    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

int32_t UtSendReplaceOrMergeVertexMsg(DrtConnectionT *conn, FixBufferT *req, ReplaceVertexMsgT *repMsg, uint32_t opCode)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, repMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, repMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, repMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &repMsg->vertexBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, repMsg->flag));
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(req), opCode, req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

int32_t UtSendUpdateVertexMsg(DrtConnectionT *conn, FixBufferT *req, UpdateVertexMsgT *updMsg)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags |= CS_FLAG_SPLIT_DISABLED;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, updMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, updMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, updMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, updMsg->indexId));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &updMsg->condBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &updMsg->filterBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &updMsg->vertexBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, updMsg->autoOperateFlag));
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_UPDATE_VERTEX,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

int32_t UtSendInsertVertexMsg(DrtConnectionT *conn, FixBufferT *req, InsertVertexMsgT *insMsg)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, insMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, insMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, insMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &insMsg->vertexBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, insMsg->flag));
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_INSERT_VERTEX,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

int32_t UtSendKvMsg(DrtConnectionT *conn, FixBufferT *req, SetKvMsgT *setKvMsg, MsgOpcodeRpcE opcode)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, setKvMsg->labelId));
    if (setKvMsg->key != NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutText(req, setKvMsg->key));
    }
    if (setKvMsg->value != NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutText(req, setKvMsg->value));
    }
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(req), opcode, req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

Status CataLoginVerify(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isDBA, bool *isGroupLogin, CataRoleT *role)
{
    Status ret;
    // step 1 : 根据userName和processName判断能否登录
    if ((ret = CataLoginVerifyByUser(userNameInfo, login, isDBA, isGroupLogin, role) == GMERR_OK)) {
        return GMERR_OK;
    }
    // step 2 : 根据groupName和processName判断能否登录
    return CataLoginVerifyByGroup(userNameInfo, login, isDBA, isGroupLogin, role);
}

Status QryCheckLoginPrivByGroupListStub(SessionT *session, bool *login, CataRoleT *role)
{
    const SessionParamT param = {0};
    Status ret = QryInitUserAndGroup(session, &param);
    if (ret != GMERR_OK) {
        return ret;
    }
    CataUserNameInfoT user = {
        session->externalUser.dbUserName,
        session->externalUser.dbGroupName,
        session->externalUser.dbProcessName,
    };
    bool isGroupLogin;
    ret = (Status)CataLoginVerify(&user, login, &session->isDBA, &isGroupLogin, role);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Internal error occurs when check login.");
        return ret;
    }
    return GMERR_OK;
}

Status QryInitUserAndGroupStub(SessionT *session, SessionParamT *param)
{
    (void)param;
    errno_t rc = strcpy_s(session->externalUser.dbUserName, sizeof(session->externalUser.dbUserName), "root");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    rc = strcpy_s(session->externalUser.dbGroupName, sizeof(session->externalUser.dbGroupName), "root");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    rc = strcpy_s(session->externalUser.dbProcessName, sizeof(session->externalUser.dbProcessName), "gmrule");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

void QryDbDeleteDynMemCtxStub(void *ctx)
{
    return;
}

const char *QryGetBaseSubsChanneName()
{
    return baseSubsChanneName;
}

Status QryTestAllocConnWithPara(DrtConnectionT **conn, const char *connName, uint8_t flag)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    const char *auditUserInfo = "0-ut_query";
    DbCredT cred = {0};
    *conn = DrtAllocConnection(&drtInstance->connMgr, auditUserInfo, &cred);
    DrtNodeT *drtNode = DrtAllocNodeWithName(&drtInstance->nodeMgr, NODE_TYPE_CLIENT, connName);
    if (drtNode == NULL) {
        UtDrtDetachConnection(*conn);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    } else {
        drtNode->pid = 0;
        drtNode->connId = (*conn)->id;
        drtNode->nodeId.flag = flag;
    }
    DrtSetConnStatus((*conn), CONN_STATUS_NORMAL);
    (*conn)->nodeId = drtNode->nodeId;

    return GMERR_OK;
}

void QryFreeBuffUt(const FixBufferT *fixBuff)
{
    return;
}

Status QryTestAllocSubConn(DrtConnectionT **conn, const char *channeName)
{
    QryTestAllocConnWithPara(conn, channeName, 2);

    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    SmScheProcCtxT *scheProcCtx;
    return (Status)SmPrepareSchedule(&drtInstance->scheMgr, &scheProcCtx, *conn);
}

Status QryTestAllocConn(DrtConnectionT **conn)
{
    static uint32_t connId = 0;
    char connName[DM_MAX_NAME_LENGTH] = {0};
    sprintf_s(connName, DM_MAX_NAME_LENGTH, "testAllocConn%d", DbAtomicInc(&connId));

    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    const char *auditUserInfo = "0-ut_query";
    DbCredT cred = {0};
    *conn = DrtAllocConnection(&drtInstance->connMgr, auditUserInfo, &cred);
    DrtNodeT *drtNode = DrtAllocNodeWithName(&drtInstance->nodeMgr, NODE_TYPE_CLIENT, connName);
    if (drtNode == NULL) {
        UtDrtDetachConnection(*conn);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    } else {
        drtNode->pid = 0;
        drtNode->connId = (*conn)->id;
        drtNode->nodeId.flag = 0;
    }
    DrtSetConnStatus((*conn), CONN_STATUS_NORMAL);
    (*conn)->nodeId = drtNode->nodeId;
    (*conn)->cltTimeoutMs = DEFAULT_TIMEOUT * DB_RDTSC_SECOND_TO_MSECOND_TO_USECOND;

    return GMERR_OK;
}

void QryTestReleaseConn(DrtConnectionT *conn)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);

    DrtFreeNode(&drtInstance->nodeMgr, conn->nodeId.nodeId);
    UtDrtDetachConnection(conn);  // 模拟ra线程回收
}

void DrtScheduleListDestroyStub(DrtScheduleListT *scheList)
{
    return;
}

void QryTestReleaseSubConn(DrtConnectionT *conn)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);

    (void)setStubC((void *)DrtScheduleListDestroy, (void *)DrtScheduleListDestroyStub);
    SmStopSchedule(&drtInstance->scheMgr, conn->id);
    (void)setStubC((void *)DrtScheduleListDestroy, (void *)DrtScheduleListDestroy);

    QryTestReleaseConn(conn);
}

Status QryTestAllocSessionWithoutStub(DrtConnectionT **conn)
{
    Status ret = QryTestAllocConn(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    CliConnectResponseT connResp = {0};
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    SessionParamT param = {.logThreshold = 0, .rollBackThreshold = 0, .userName = "", .pwd = ""};
    return QryAllocSessionForDrt(*conn, &connResp, drtInstance, &param);
}

void QryTestReleaseSessionWithoutStub(DrtConnectionT *conn)
{
    QryReleaseSessionForDrt(conn);
    QryTestReleaseConn(conn);
}

Status QryTestDrtConnWritePackSuccessStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    return GMERR_OK;
}

Status QryTestAllocSession(DrtConnectionT **conn, FixBufferT **rsp)
{
    Status ret = QryTestAllocConn(conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupStub);
    (void)setStubC((void *)QryCheckLoginPrivByGroupList, (void *)QryCheckLoginPrivByGroupListStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)QryTestDrtConnWritePackSuccessStub);
    CliConnectResponseT connResp = {0};
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    SessionParamT param = {.logThreshold = 0, .rollBackThreshold = 0, .userName = "", .pwd = ""};
    ret = QryAllocSessionForDrt(*conn, &connResp, drtInstance, &param);
    if (ret != GMERR_OK) {
        return ret;
    }
    return QryCreateRsp(*conn, rsp);
}

void QryTestReleaseSession(DrtConnectionT *conn)
{
    (void)setStubC((void *)DbDeleteDynMemCtx, (void *)QryDbDeleteDynMemCtxStub);
    QryReleaseSessionForDrt(conn);
    QryTestReleaseConn(conn);
    clearAllStub();
}

int32_t QryCreateRsp(DrtConnectionT *conn, FixBufferT **rsp)
{
    FixBufferT *tmpRsp = (FixBufferT *)DbDynMemCtxAlloc(dyAlgoCtxVertexBase, sizeof(FixBufferT));
    if (tmpRsp == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t rc = memset_s(tmpRsp, sizeof(FixBufferT), 0x00, sizeof(FixBufferT));
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 重新申请一个fixbuffer的rsp的空壳子挂到session上
    SessionT *session = (SessionT *)conn->session;
    session->rsp = tmpRsp;

    if (rsp != NULL) {
        // 在释放rsp的时候，无动作执行
        (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);

        // 将rsp的壳子带出去
        *rsp = tmpRsp;
    }
    return GMERR_OK;
}

void QryReleaseRsp(FixBufferT *rsp)
{
    if (rsp != NULL) {
        DbDynMemCtxFree(dyAlgoCtxVertexBase, rsp);
    }
}

int32_t UtQueryDDLNamespaceNormal(const DrtConnectionT *conn, FixBufferT *msg)
{
    MsgHeaderT *header = (MsgHeaderT *)msg->buf;
    EXPECT_EQ(GMERR_OK, header->opStatus);

    return GMERR_OK;
}

int32_t QryTestCreateNamespace(DmNamespaceT *nsp, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = (uint32_t)(strlen(nsp->metaCommon.metaName) + 1), .str = nsp->metaCommon.metaName};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    TextT userText = {.len = (uint32_t)(strlen(nsp->owner.str) + 1), .str = nsp->owner.str};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &userText));
    TextT tspText = {.len = (uint32_t)(strlen("public") + 1), .str = (char *)"public"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &tspText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)nsp->trxInfo.isolationLevel));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)nsp->trxInfo.trxType));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceNormal);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);

    clearAllStub();
    return ret;
}

int32_t QryTestDropNamespace(char *nspName, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = (uint32_t)(strlen(nspName) + 1), .str = nspName};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_TX_ISOLATION_DEFAULT));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_DEFAULT_TRX));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLNamespaceNormal);

    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);

    clearAllStub();
    return ret;
}

Status DrtCreateReservedConnTokensStub(const char *userName, const char *processName, uint16_t reservedConnNum)
{
    return 0;
}

Status QryAcqLatchForPrivStub(QryStmtT *stmt)
{
    return 0;
}

void QryTestExecuteCreateUser(QryCreateOrDropUserBatchDescT *desc)
{
    SessionT *session = NULL;
    QryStmtT *testStmt = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ExecutorInitStmt(session, &testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    session->currentStmt = testStmt;
    testStmt->context->entry = desc;
    (void)setStubC((void *)DrtCreateReservedConnTokens, (void *)DrtCreateReservedConnTokensStub);
    (void)setStubC((void *)QryAcqLatchForPriv, (void *)QryAcqLatchForPrivStub);

    FixBufferT rsp = {0};
    ret = FixBufCreate(&rsp, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    session->rsp = &rsp;

    ret = QryExecuteBeginTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryExecuteCreateUser(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryExecuteCommitTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufRelease(&rsp);
    clearAllStub();
}

void QryTestExecuteGrantOrRevokeSysPrivs(QryGrantOrRevokeSysPrivsDescT *desc, bool isGrant)
{
    SessionT *session = NULL;
    QryStmtT *testStmt = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ExecutorInitStmt(session, &testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    session->currentStmt = testStmt;
    testStmt->context->entry = desc;
    (void)setStubC((void *)QryAcqLatchForPriv, (void *)QryAcqLatchForPrivStub);

    FixBufferT rsp = {0};
    ret = FixBufCreate(&rsp, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    session->rsp = &rsp;

    ret = QryExecuteBeginTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = isGrant ? QryExecuteGrantSysPrivs(testStmt) : QryExecuteRevokeSysPrivs(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryExecuteCommitTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufRelease(&rsp);
    clearAllStub();
}

void QryTestExecuteGrantOrRevokeObjPrivs(QryGrantOrRevokeObjPrivsDescT *desc, bool isGrant, CataRoleT *role)
{
    SessionT *session = NULL;
    QryStmtT *testStmt = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ExecutorInitStmt(session, &testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    session->currentStmt = testStmt;
    testStmt->context->entry = desc;
    (((SeRunCtxT *)session->seInstance)->resSessionCtx).session->role = role;
    (void)setStubC((void *)QryAcqLatchForPriv, (void *)QryAcqLatchForPrivStub);

    FixBufferT rsp = {0};
    ret = FixBufCreate(&rsp, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    session->rsp = &rsp;

    ret = QryExecuteBeginTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = isGrant ? QryExecuteGrantObjPrivs(testStmt) : QryExecuteRevokeObjPrivs(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryExecuteCommitTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufRelease(&rsp);
    clearAllStub();
}

Status DrtDestroyReservedConnTokensStub(const char *userName, const char *processName)
{
    return 0;
}

void QryTestExecuteDropUser(char *userName, char *processName)
{
    QryCreateOrDropUserDescT dropUserDesc = {0};
    dropUserDesc.userName.str = userName;
    dropUserDesc.userName.len = strlen(userName) + 1;
    dropUserDesc.processName.str = processName;
    dropUserDesc.processName.len = strlen(processName) + 1;

    SessionT *session = NULL;
    QryStmtT *testStmt = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ExecutorInitStmt(session, &testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    session->currentStmt = testStmt;
    testStmt->context->entry = &dropUserDesc;
    (void)setStubC((void *)DrtDestroyReservedConnTokens, (void *)DrtDestroyReservedConnTokensStub);
    (void)setStubC((void *)QryAcqLatchForPriv, (void *)QryAcqLatchForPrivStub);

    FixBufferT rsp = {0};
    ret = FixBufCreate(&rsp, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    session->rsp = &rsp;

    ret = QryExecuteBeginTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryExecuteDropUser(testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryExecuteCommitTrans(testStmt);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufRelease(&rsp);
    clearAllStub();
}

int32_t QryTestCreateVertexLabelWithAppointLabelName(
    char *cfgJson, char *labelJson, char *appointLabelName, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    if (ret != GMERR_OK) {
        // logging
        printf("alloc session failed!\n");
        return ret;
    }

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t labelJsonLen = 0;
    uint32_t cfgJsonLen = 0;
    if (labelJson != NULL) {
        labelJsonLen = strlen(labelJson) + 1;
    }
    if (cfgJson != NULL) {
        cfgJsonLen = strlen(cfgJson) + 1;
    }

    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    if (labelJson != NULL) {
        putText.str = labelJson;
        putText.len = labelJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, labelJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (cfgJson != NULL) {
        putText.str = cfgJson;
        putText.len = cfgJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, cfgJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (appointLabelName != NULL) {
        putText.str = appointLabelName;
        putText.len = strlen(appointLabelName) + 1;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, 0);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);

    clearAllStub();
    return ret;
}

int32_t QryTestCreateVertexLabel(char *cfgJson, char *labelJson, FixBufferT **rsp)
{
    return QryTestCreateVertexLabelWithAppointLabelName(cfgJson, labelJson, NULL, rsp);
}

int32_t QryTestAlterVertexLabelWithAppointLabelName(
    char *labelJson, char *appointLabelName, bool isCompatible, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    if (ret != GMERR_OK) {
        // logging
        printf("alloc session failed!\n");
        return ret;
    }

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t labelJsonLen = 0;
    if (labelJson != NULL) {
        labelJsonLen = strlen(labelJson) + 1;
    }

    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    if (labelJson != NULL) {
        putText.str = labelJson;
        putText.len = labelJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, labelJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (appointLabelName != NULL) {
        putText.str = appointLabelName;
        putText.len = strlen(appointLabelName) + 1;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, 0);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = FixBufPutUint32(&req, isCompatible);
    if (ret != GMERR_OK) {
        return ret;
    }

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_ALTER_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);

    clearAllStub();
    return ret;
}

int32_t QryTestAlterVertexLabel(char *labelJson, bool isCompatible, FixBufferT **rsp)
{
    return QryTestAlterVertexLabelWithAppointLabelName(labelJson, NULL, isCompatible, rsp);
}

int32_t QryTestCreateEdgeLabel(char *cfgJson, char *labelJson, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    TextT putText;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    if (ret != GMERR_OK) {
        // logging
        printf("alloc session failed!\n");
        return ret;
    }

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t labelJsonLen = 0;
    uint32_t cfgJsonLen = 0;

    if (labelJson != NULL) {
        labelJsonLen = strlen(labelJson) + 1;
    }
    if (cfgJson != NULL) {
        cfgJsonLen = strlen(cfgJson) + 1;
    }

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    if (labelJson != NULL) {
        putText.str = labelJson;
        putText.len = labelJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, labelJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (cfgJson != NULL) {
        putText.str = cfgJson;
        putText.len = cfgJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, cfgJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_EDGE_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);

    QryTestReleaseSession(conn);
    FixBufRelease(&req);

    clearAllStub();
    return ret;
}

int32_t QryTestInsertVertex(
    char *labelName, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            break;
        }

        DmVertexLabelT *vertexLabel = NULL;
        uint32_t labelNameLen = 0;
        uint32_t vertexBufLen = 0;
        uint8_t *buf = NULL;
        if (labelName != NULL) {
            labelNameLen = strlen(labelName) + 1;
            // 从Catalog中查询得到相应点标签的元数据
            DmVertexT *vertex = NULL;
            CataKeyT cataKey;
            CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
            if (labelName != NULL) {
                CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
                DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, &vertex);
            }

            // set the porperty of vertex
            for (uint32_t i = 0; i < propertyNum; i++) {
                DmVertexSetPropeByName((char *)propertyName[i], propertyValue[i], vertex);
            }
            if (propertyNum != 0) {
                DmSerializeVertex(vertex, &buf, &vertexBufLen);
                DmDestroyVertex(vertex);
            }
        }

        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);

        InsertVertexMsgT insMsg;
        insMsg.labelId = vertexLabel != NULL ? vertexLabel->metaCommon.metaId : 0;
        insMsg.version = vertexLabel != NULL ? vertexLabel->metaCommon.version : 0;
        insMsg.uuid = vertexLabel != NULL ? vertexLabel->metaVertexLabel->uuid : 0;
        insMsg.vertexBufText.str = (char *)buf;
        insMsg.vertexBufText.len = vertexBufLen;
        insMsg.labelName.str = labelName;
        insMsg.labelName.len = labelNameLen;
        insMsg.flag = 0x0007;
        ret = UtSendInsertVertexMsg(conn, &req, &insMsg);
        if (vertexLabel != NULL) {
            CataReleaseVertexLabel(vertexLabel);
        }

        QryTestReleaseSession(conn);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestReplaceVertexBatch(char *labelName, uint32_t replaceNum, uint32_t propertyNum, char **propertyName,
    DmValueT *propertyValue, FixBufferT **rsp, bool isReplace)
{
    uint32_t ret;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            break;
        }

        // 从Catalog中查询得到相应点标签的元数据
        DmVertexLabelT *vertexLabel = NULL;
        DmVertexT *vertex = NULL;
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
        CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);

        DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, &vertex);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaCommon.metaId));
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaCommon.version));
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaVertexLabel->uuid));
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, replaceNum));

        uint32_t vertexBufLen = 0;
        uint8_t *buf = NULL;
        TextT vertexBufText;

        for (uint32_t i = 0; i < replaceNum; i++) {
            // set the porperty of vertex
            for (uint32_t j = 0; j < propertyNum; j++) {
                DmVertexSetPropeByName((char *)propertyName[j], propertyValue[i * propertyNum + j], vertex);
            }

            DmSerializeVertex(vertex, &buf, &vertexBufLen);
            vertexBufText.str = (char *)buf;
            vertexBufText.len = vertexBufLen;
            EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &vertexBufText));
            EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0x0007));
        }

        uint32_t opCode = isReplace ? MSG_OP_RPC_REPLACE_VERTEX : MSG_OP_RPC_INSERT_VERTEX;
        RpcFillOpHeader(
            ProtocolPeekFirstOpHeader(&req), opCode, req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DmDestroyVertex(vertex);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);

        CataReleaseVertexLabel(vertexLabel);
        QryTestReleaseSession(conn);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestUpdateVertex(char *labelName, const char *indexName, uint32_t indexPropertyNum,
    DmValueT *indexPropertyValue, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue, uint32_t flag,
    FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            break;
        }

        uint32_t labelNameLen = 0;
        DmVertexLabelT *vertexLabel = NULL;
        uint32_t filterBufLen = 0;
        uint8_t *filterBuf = NULL;
        uint32_t vertexBufLen = 0;
        uint8_t *buf = NULL;
        DmIndexKeyT *filter = NULL;
        DmVertexT *vertex = NULL;
        DmVlIndexLabelT *indexLabel = NULL;
        uint32_t indexId = DB_MAX_UINT32;
        if (labelName != NULL) {
            labelNameLen = strlen(labelName) + 1;

            // 从Catalog中查询得到相应点标签的元数据
            CataKeyT cataKey;
            CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
            CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);

            DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, &vertex);

            // set the porperty of vertex
            for (uint32_t i = 0; i < propertyNum; i++) {
                DmVertexSetPropeByName((char *)propertyName[i], propertyValue[i], vertex);
            }
            DmSerializeVertex(vertex, &buf, &vertexBufLen);
            if (indexName != NULL && indexPropertyNum != 0) {
                EXPECT_EQ(GMERR_OK, DmCreateIndexKeyByNameWithMemCtx((DbMemCtxT *)((Session *)conn->session)->memCtx,
                                        vertexLabel, indexName, indexPropertyValue, indexPropertyNum, &filter));
                filterBufLen = DmIndexKeyGetSeriBufLength(filter);
                filterBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, filterBufLen);
                EXPECT_EQ(GMERR_OK, DmSerializeIndexKey2InvokerBuf(filter, filterBufLen, filterBuf));
                EXPECT_EQ(GMERR_OK, DmGetIndexLabelByName(vertexLabel, indexName, &indexLabel));
                indexId = indexLabel->idxLabelBase.indexId;
            }
        }
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

        UpdateVertexMsgT updMsg;
        updMsg.labelId = vertexLabel != NULL ? vertexLabel->metaCommon.metaId : 0;
        updMsg.vertexBufText = {.len = vertexBufLen, .str = (char *)buf};
        updMsg.filterBufText = {.len = filterBufLen, .str = (char *)filterBuf};
        updMsg.condBufText = {.len = 0, .str = NULL};
        updMsg.labelName.str = labelName;
        updMsg.labelName.len = labelNameLen;
        updMsg.autoOperateFlag = flag;
        updMsg.indexId = indexId;
        updMsg.version = vertexLabel != NULL ? vertexLabel->metaCommon.version : DB_MAX_UINT32;
        updMsg.uuid = vertexLabel != NULL ? vertexLabel->metaVertexLabel->uuid : DB_MAX_UINT32;
        ret = UtSendUpdateVertexMsg(conn, &req, &updMsg);
        if (labelName != NULL) {
            DmDestroyIndexKey(filter);
            CataReleaseVertexLabel(vertexLabel);
            DmDestroyVertex(vertex);
        }
        DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, filterBuf);
        QryTestReleaseSession(conn);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestReplaceOrMergeVertex(char *labelName, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue,
    uint32_t opCode, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            break;
        }

        uint32_t labelNameLen = 0;
        DmVertexLabelT *vertexLabel = NULL;
        DmVertexT *vertex = NULL;
        uint32_t vertexBufLen = 0;
        uint8_t *buf = NULL;
        if (labelName != NULL) {
            labelNameLen = strlen(labelName) + 1;
            // 从Catalog中查询得到相应点标签的元数据
            CataKeyT cataKey;
            CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
            CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
            DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, &vertex);
            // set the porperty of vertex
            for (uint32_t i = 0; i < propertyNum; i++) {
                DmVertexSetPropeByName((char *)propertyName[i], propertyValue[i], vertex);
            }
            DmSerializeVertex(vertex, &buf, &vertexBufLen);
        }

        ReplaceVertexMsgT repMsg;
        repMsg.labelId = vertexLabel != NULL ? vertexLabel->metaCommon.metaId : 0;
        repMsg.version = vertexLabel != NULL ? vertexLabel->metaCommon.version : 0;
        repMsg.uuid = vertexLabel != NULL ? vertexLabel->metaVertexLabel->uuid : 0;
        repMsg.vertexBufText = {.len = vertexBufLen, .str = (char *)buf};
        repMsg.labelName.str = labelName;
        repMsg.labelName.len = labelNameLen;
        repMsg.flag = 0;
        ret = UtSendReplaceOrMergeVertexMsg(conn, &req, &repMsg, opCode);
        if (labelName != NULL) {
            CataReleaseVertexLabel(vertexLabel);
            DmDestroyVertex(vertex);
        }
        QryTestReleaseSession(conn);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestDeleteVertex(char *labelName, const char *indexName, uint32_t indexPropertyNum,
    DmValueT *leftPropertyValues, DmValueT *rightPropertyValues, uint32_t autoFlag, uint32_t scanFlag, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            break;
        }

        uint32_t labelNameLen = strlen(labelName) + 1;

        // 从Catalog中查询得到相应点标签的元数据
        DmVertexLabelT *vertexLabel = NULL;
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
        CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);

        // create left filter
        uint32_t leftFilterBufLen = 0;
        uint8_t *leftFilterBuf = NULL;
        DmIndexKeyT *leftFilter = NULL;
        uint32_t rightFilterBufLen = 0;
        uint8_t *rightFilterBuf = NULL;
        DmIndexKeyT *rightFilter = NULL;
        DmVlIndexLabelT *indexLabel = NULL;
        uint32_t indexId = DB_MAX_UINT32;
        if (indexName != NULL && indexPropertyNum != 0) {
            EXPECT_EQ(GMERR_OK, DmCreateIndexKeyByNameWithMemCtx((DbMemCtxT *)((Session *)conn->session)->memCtx,
                                    vertexLabel, indexName, leftPropertyValues, indexPropertyNum, &leftFilter));
            leftFilterBufLen = DmIndexKeyGetSeriBufLength(leftFilter);
            leftFilterBuf =
                (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, leftFilterBufLen);
            DmSerializeIndexKey2InvokerBuf(leftFilter, leftFilterBufLen, leftFilterBuf);
            if (scanFlag & CS_RANGE_SCAN_ENABLED) {
                EXPECT_EQ(GMERR_OK, DmCreateIndexKeyByNameWithMemCtx((DbMemCtxT *)((Session *)conn->session)->memCtx,
                                        vertexLabel, indexName, rightPropertyValues, indexPropertyNum, &rightFilter));
                rightFilterBufLen = DmIndexKeyGetSeriBufLength(rightFilter);
                rightFilterBuf =
                    (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, rightFilterBufLen);
                DmSerializeIndexKey2InvokerBuf(rightFilter, rightFilterBufLen, rightFilterBuf);
            }
            EXPECT_EQ(GMERR_OK, DmGetIndexLabelByName(vertexLabel, indexName, &indexLabel));
            indexId = indexLabel->idxLabelBase.indexId;
        }
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

        DeleteVertexMsgT dedMsg;
        dedMsg.labelId = vertexLabel->metaCommon.metaId;
        dedMsg.version = vertexLabel->metaCommon.version;
        dedMsg.uuid = vertexLabel->metaVertexLabel->uuid;
        dedMsg.leftFilterBuf = {.len = leftFilterBufLen, .str = (char *)leftFilterBuf};
        dedMsg.rightFilterBuf = {.len = rightFilterBufLen, .str = (char *)rightFilterBuf};
        dedMsg.condBufText = {.len = 0, .str = NULL};
        dedMsg.labelName.str = labelName;
        dedMsg.labelName.len = labelNameLen;
        dedMsg.autoOperateFlag = autoFlag;
        dedMsg.scanFlag = scanFlag;
        dedMsg.indexId = indexId;
        ret = UtSendDeleteVertexMsg(conn, &req, &dedMsg);

        DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, leftFilterBuf);
        DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, rightFilterBuf);
        DmDestroyIndexKey(leftFilter);
        DmDestroyIndexKey(rightFilter);
        CataReleaseVertexLabel(vertexLabel);
        QryTestReleaseSession(conn);
        clearAllStub();
    } while (0);

    FixBufRelease(&req);
    return ret;
}

int32_t QryTestBeginScanVertex(DrtConnectionT *conn, const char *labelName, const char *keyName, uint32_t propertyNum,
    DmValueT *leftPropertyValues, DmValueT *rightPropertyValues, uint32_t scanFlag, char *cond, char *resultSetStr,
    uint64_t limitCount, uint16_t preFetchRows)
{
    uint32_t ret;
    FixBufferT req = {0};
    MsgHeaderT *msgHeader = NULL;
    DmIndexKeyT *leftFilter = NULL;
    DmIndexKeyT *rightFilter = NULL;
    uint8_t *leftSeriBuf = NULL;
    uint8_t *rightSeriBuf = NULL;
    uint32_t leftLength = 0;
    uint32_t rightLength = 0;

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);
    // get vertexLabel
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t labelId = vertexLabel->metaCommon.metaId;

    if (keyName != NULL) {
        // create left filter
        if (leftPropertyValues != NULL) {
            ret = DmCreateIndexKeyByNameWithMemCtx(
                (DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, keyName, leftPropertyValues, propertyNum, &leftFilter);
            EXPECT_EQ(GMERR_OK, ret);
            leftLength = DmIndexKeyGetSeriBufLength(leftFilter);
            leftSeriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)dyAlgoCtxVertexBase, leftLength);
            ret = DmSerializeIndexKey2InvokerBuf(leftFilter, leftLength, leftSeriBuf);
            EXPECT_EQ(GMERR_OK, ret);
            DmDestroyIndexKey(leftFilter);
        }

        // create righr filter
        if (scanFlag & CS_RANGE_SCAN_ENABLED) {
            DmVlIndexLabelT *indexLabel = NULL;
            if (leftPropertyValues == NULL) {
                Status ret = DmGetIndexLabelByName(vertexLabel, keyName, &indexLabel);
                EXPECT_EQ(GMERR_OK, ret);
                ret = DmCreateEmptyIndexKeyByIdxLabelWithMemCtx(
                    (DbMemCtxT *)dyAlgoCtxVertexBase, indexLabel, &leftFilter);
                EXPECT_EQ(GMERR_OK, ret);
                leftLength = DmIndexKeyGetSeriBufLength(leftFilter);
                leftSeriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)dyAlgoCtxVertexBase, leftLength);
                ret = DmSerializeIndexKey2InvokerBuf(leftFilter, leftLength, leftSeriBuf);
                EXPECT_EQ(GMERR_OK, ret);
                DmDestroyIndexKey(leftFilter);
            }
            if (rightPropertyValues == NULL) {
                Status ret = DmGetIndexLabelByName(vertexLabel, keyName, &indexLabel);
                EXPECT_EQ(GMERR_OK, ret);
                ret = DmCreateEmptyIndexKeyByIdxLabelWithMemCtx(
                    (DbMemCtxT *)dyAlgoCtxVertexBase, indexLabel, &rightFilter);
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                ret = DmCreateIndexKeyByNameWithMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, keyName,
                    rightPropertyValues, propertyNum, &rightFilter);
            }
            EXPECT_EQ(GMERR_OK, ret);
            rightLength = DmIndexKeyGetSeriBufLength(rightFilter);
            rightSeriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)dyAlgoCtxVertexBase, rightLength);
            ret = DmSerializeIndexKey2InvokerBuf(rightFilter, rightLength, rightSeriBuf);
            EXPECT_EQ(GMERR_OK, ret);
            DmDestroyIndexKey(rightFilter);
        }
    }
    CataReleaseVertexLabel(vertexLabel);

    TextT leftFilterBuf, rightFilterBuf;
    leftFilterBuf.len = leftLength;
    leftFilterBuf.str = (char *)leftSeriBuf;
    rightFilterBuf.len = rightLength;
    rightFilterBuf.str = (char *)rightSeriBuf;

    // scan begin
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    DbPrepExecReqT *reqHead = (DbPrepExecReqT *)FixBufReserveData(&req, sizeof(DbPrepExecReqT));
    reqHead->exec.preFetchRows = preFetchRows;
    // labelId
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaCommon.version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaVertexLabel->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, scanFlag));
    // leftFilter
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &leftFilterBuf));
    // rightFilter
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &rightFilterBuf));

    // isSysviewRecordCmd
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    // condition
    if (cond == NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    } else {
        TextT condT;
        condT.str = cond;
        condT.len = strlen(cond) + 1;
        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &condT));
    }
    if (resultSetStr == NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    } else {
        TextT resultSetStrT;
        resultSetStrT.str = resultSetStr;
        resultSetStrT.len = strlen(resultSetStr) + 1;
        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &resultSetStrT));
    }
    // sortCount
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    // limitCount
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, limitCount));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_SCAN_VERTEX_BEGIN,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    DbDynMemCtxFree((DbMemCtxT *)dyAlgoCtxVertexBase, leftSeriBuf);
    DbDynMemCtxFree((DbMemCtxT *)dyAlgoCtxVertexBase, rightSeriBuf);
    FixBufRelease(&req);
    return ret;
}

int32_t QryTestBeginSortScanVertex(DrtConnectionT *conn, char *labelName, char *propName, uint32_t scanFlag,
    char *resultSetStr, uint64_t limitCount, FixBufferT **rsp, uint16_t preFetchRows, OrderDirectionE direction)
{
    uint32_t ret;
    FixBufferT req = {0};
    MsgHeaderT *msgHeader = NULL;
    uint8_t *leftSeriBuf = NULL;
    uint8_t *rightSeriBuf = NULL;
    uint32_t leftLength = 0;
    uint32_t rightLength = 0;

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);
    // get vertexLabel
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t labelId = vertexLabel->metaCommon.metaId;
    TextT leftFilterBuf, rightFilterBuf;
    leftFilterBuf.len = leftLength;
    leftFilterBuf.str = (char *)leftSeriBuf;
    rightFilterBuf.len = rightLength;
    rightFilterBuf.str = (char *)rightSeriBuf;
    CataReleaseVertexLabel(vertexLabel);

    // scan begin
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    DbPrepExecReqT *reqHead = (DbPrepExecReqT *)FixBufReserveData(&req, sizeof(DbPrepExecReqT));
    reqHead->exec.preFetchRows = preFetchRows;
    // labelId
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaCommon.version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaVertexLabel->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, scanFlag));
    // leftFilter
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &leftFilterBuf));
    // rightFilter
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &rightFilterBuf));
    // isSysviewRecordCmd
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    // condition
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

    if (resultSetStr == NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    } else {
        TextT resultSetStrT;
        resultSetStrT.str = resultSetStr;
        resultSetStrT.len = strlen(resultSetStr) + 1;
        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &resultSetStrT));
    }

    // sort
    TextT property = {};
    if (propName != NULL) {
        property.str = propName;
        property.len = strlen(propName) + 1;
    }
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutUint16(&req, direction));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &property));

    // limitCount
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, limitCount));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_SCAN_VERTEX_BEGIN,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    QryCreateRsp(conn, rsp);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestGetCount(
    DrtConnectionT *conn, char *labelName, const char *keyName, uint32_t propertyNum, DmValueT *propertyValues)
{
    uint32_t ret;
    FixBufferT req = {0};
    MsgHeaderT *msgHeader = NULL;
    DmIndexKeyT *filter = NULL;
    uint8_t *seriBuf = NULL;
    uint32_t length = 0;

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    if (keyName != NULL) {
        // create filter
        ret = DmCreateIndexKeyByNameWithMemCtx(
            (DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, keyName, propertyValues, propertyNum, &filter);
        EXPECT_EQ(GMERR_OK, ret);
        length = DmIndexKeyGetSeriBufLength(filter);
        seriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)dyAlgoCtxVertexBase, length);
        ret = DmSerializeIndexKey2InvokerBuf(filter, length, seriBuf);
        EXPECT_EQ(GMERR_OK, ret);
        DmDestroyIndexKey(filter);
    }
    CataReleaseVertexLabel(vertexLabel);
    TextT filterBuf;
    filterBuf.len = length;
    filterBuf.str = (char *)seriBuf;

    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    // labelId
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaCommon.metaId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaCommon.version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaVertexLabel->uuid));
    // filter
    if (filterBuf.str == NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    } else {
        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &filterBuf));
    }
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_COUNT,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);

    DbDynMemCtxFree((DbMemCtxT *)dyAlgoCtxVertexBase, seriBuf);
    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestReleaseStmt(DrtConnectionT *conn)
{
    uint32_t ret;
    FixBufferT req = {0};
    MsgHeaderT *msgHeader = NULL;

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = QryTestGetCurStmtID();
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_RELEASE_STMT,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestDropVertexLabelWithVersion(
    char *labelNameStr, uint32_t isDropAssoc, uint32_t versionId, FixBufferT **rsp, bool dropVersion)
{
    uint32_t ret = GMERR_OK;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            break;
        }

        TextT labelName = {.len = (uint32_t)strlen(labelNameStr) + 1, .str = labelNameStr};

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;
        ret = FixBufPutText(&req, &labelName);
        if (ret != GMERR_OK) {
            break;
        }
        if (dropVersion) {
            ret = FixBufPutUint32(&req, versionId);
        } else {
            ret = FixBufPutUint32(&req, isDropAssoc);
        }
        if (ret != GMERR_OK) {
            return ret;
        }

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req),
            dropVersion ? MSG_OP_RPC_DEGRADE_VERTEX_LABEL : MSG_OP_RPC_DROP_VERTEX_LABEL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        // May Not ok
        // EXPECT_EQ(GMERR_OK, ret);

        QryTestReleaseSession(conn);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestDropVertexLabel(char *labelNameStr, uint32_t isDropAssoc, FixBufferT **rsp)
{
    return QryTestDropVertexLabelWithVersion(labelNameStr, isDropAssoc, DM_SCHEMA_INVALID_VERSION, rsp);
}

int32_t QryTestDropEdgeLabel(char *labelName, FixBufferT **rsp)
{
    int32_t ret = GMERR_OK;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t labelNameLen = strlen(labelName) + 1;
        TextT putText;

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        putText.str = labelName;
        putText.len = labelNameLen;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);
        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_EDGE_LABEL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        (void)setStubC((void *)HeapLabelGetPerfStat, (void *)HeapLabelGetPerfStatStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        // May NOT OK
        // EXPECT_EQ(GMERR_OK, ret);

        QryTestReleaseSession(conn);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestQueryMultiVertexFetchNext(DrtConnectionT *conn, DrtConnWritePackFunc drtFunc)
{
    uint32_t ret = GMERR_OK;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }
        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = QryTestGetCurStmtID();
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_FETCH_CYPHER,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        (void)setStubC((void *)DrtConnWritePack, (void *)drtFunc);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestQueryMultiVertexEnd(DrtConnectionT *conn, FixBufferT **rsp)
{
    uint32_t ret = GMERR_OK;
    FixBufferT req = {0};

    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }
        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = QryTestGetCurStmtID();
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_RELEASE_STMT,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        QryCreateRsp(conn, rsp);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
    } while (0);

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestDropBaseVertexLabelFirst()
{
    char *labelName = (char *)"labelvertex1";
    return QryTestDropVertexLabel(labelName, true, NULL);
}

int32_t QryTestCreateBaseVertexLabelFirst(char *cfgJson)
{
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex1",
        "id":123,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"},
            {"name":"F4", "type":"time", "nullable":true},
            {"name":"F5", "type":"string", "size":20, "nullable":true},
            {"name":"F6", "type":"fixed", "default":"fff", "size":3},
            {"name":"F77", "type":"uint32"}
        ],
        "keys":
        [
            {"node":"labelvertex1", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertex1", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertex1", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    return QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
}

int32_t QryTestCreateBaseVertexLabelChLabel(char *cfgJson)
{
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexChLabel",
        "id":9999,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"},
            {"name":"F3", "type":"int64"}
        ],
        "keys":
        [
            {"node":"labelvertexChLabel",
             "name":"T39_K0",
             "fields":["F0"],
             "index":{"type":"primary"},
             "constraints":{"unique":true}}
        ]
    }])";

    return QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
}

int32_t QryTestDropBaseVertexLabelSecond()
{
    char *labelName = (char *)"labelvertex2";
    return QryTestDropVertexLabel(labelName, true, NULL);
}

int32_t QryTestCreateBaseVertexLabelSecond(char *cfgJson)
{
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex2",
        "id":456,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"},
            {"name":"F4", "type":"time", "nullable":true},
            {"name":"F5", "type":"string", "size":20, "nullable":true},
            {"name":"F6", "type":"bytes", "size":20, "nullable":true},
            {"name":"F88", "type":"uint32"}
        ],
        "keys":
        [
            {"node":"labelvertex2", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertex2", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}},
            {"node":"labelvertex2", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}}
        ]
    }])";

    return QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
}

int32_t QryTestDropBaseEdgeLabel()
{
    char *labelEdgeName = (char *)"edgeLabel1";
    return QryTestDropEdgeLabel(labelEdgeName, NULL);
}

int32_t QryTestCreateBaseEdgeLabel()
{
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *labelJson = (char *)R"([{
        "name":"edgeLabel1",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";

    return QryTestCreateEdgeLabel(cfgJson, labelJson);
}

int32_t QryTestCreateSubsBase(QryTestCreateSubsParamT *createParam)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    FixBufferT *response = NULL;
    ret = QryTestAllocSession(&conn, &response);
    if (ret != GMERR_OK) {
        // logging
        printf("alloc session failed!\n");
        return ret;
    }
    createParam->nspId = ((SessionT *)(conn->session))->namespaceId;

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    char *chanName = (char *)QryGetBaseSubsChanneName();

    uint32_t subsJsonLen = strlen(createParam->subsJson) + 1;
    uint32_t chanNameLen = strlen(chanName) + 1;

    TextT putText;
    (void)setStubC((void *)DrtConnWritePack, (void *)createParam->drtConnWritePackStub);
    (void)setStubC((void *)ExpandChannelCapacity, (void *)createParam->expandChannelFunc);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    putText.str = createParam->subsName;
    putText.len = strlen(createParam->subsName) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = (char *)createParam->subsJson;
    putText.len = subsJsonLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = chanName;
    putText.len = chanNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = (char *)"TEST";
    putText.len = strlen(putText.str) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_SUBSCRIPTION,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    (void)setStubC((void *)AddSubsUserCbFuncName2Conn, (void *)createParam->addSubCbFunc);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(response);
    EXPECT_EQ(op->opCode, MSG_OP_RPC_CREATE_SUBSCRIPTION);
    DbCreateSubAckT *respAck = (DbCreateSubAckT *)FixBufGetData(response, sizeof(DbCreateSubAckT));
    EXPECT_TRUE(respAck != NULL);
    if (createParam->createSubAck != NULL) {
        *createParam->createSubAck = *respAck;
    }

    QryTestReleaseSession(conn);
    FixBufRelease(&req);
    return ret;
}

QryTestCreateSubsParamT CreateDefaultCreatSubParam(void)
{
    QryTestCreateSubsParamT createParam = {0};
    createParam.createSubAck = NULL;
    createParam.drtConnWritePackStub = DrtConnWritePackStub;
    createParam.addSubCbFunc = AddSubsUserCbFuncName2ConnStub;
    createParam.expandChannelFunc = ExpandChannelCapacityStub;
    return createParam;
}

int32_t QryTestCreateSubs(
    char *subsName, const char *subsJson, DrtConnWritePackFunc drtConnWritePackStub, DbCreateSubAckT *createSubAck)
{
    QryTestCreateSubsParamT createParam = {0};
    createParam.subsName = subsName;
    createParam.subsJson = subsJson;
    createParam.drtConnWritePackStub = drtConnWritePackStub;
    createParam.createSubAck = createSubAck;
    createParam.addSubCbFunc = AddSubsUserCbFuncName2ConnStub;
    return QryTestCreateSubsBase(&createParam);
}

int32_t QryTestCreateLabelTree()
{
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

    char *labelJson = (char *)R"([{
      "version": "2.0", "type": "record", "name": "labelTree",
      "fields": [
        { "name": "a0", "type": "uint32" },
        { "name": "a1", "type": "string", "size":20, "nullable":true },
        { "name": "a2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "uint32" },
            { "name": "b2", "type": "uint32" }
          ]
        },
        { "name": "a3", "type": "bytes", "size": 128, "nullable":true },
        { "name": "a4", "type": "record",
          "fixed_array": true, "size": 512,
          "fields": [
            { "name": "b3", "type": "uint32"},
            { "name": "b4", "type": "string","size":8, "nullable":true}
          ]
        },
        { "name": "a5", "type": "record",
          "fields": [
            { "name": "b5", "type": "uint32"},
            { "name": "b6", "type": "record",
              "fields": [
                { "name": "c0", "type": "uint32"},
                { "name": "c1", "type": "record",
                    "fields": [
                    { "name": "d1", "type": "uint64"},
                    { "name": "d2", "type": "string","size":8, "nullable":false}
                    ]
                }
              ]
            }
          ]
        },
        { "name": "a6", "type": "int64", "nullable":true},
        { "name": "a7", "type": "double", "nullable":true}
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "labelTree",
          "fields": [ "a0" ],
          "constraints": { "unique": true }
        }
      ]
    }])";

    return QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
}

int32_t QryTestCreateKvTable(char *tableName, char *cfgJson, FixBufferT **rsp)
{
    DrtConnectionT *conn = NULL;
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    FixBufferT req = {0};
    EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    uint32_t ret;
    TextT putText;
    if (tableName != NULL) {
        putText.str = tableName;
        putText.len = strlen(tableName) + 1;
        ret = FixBufPutText(&req, &putText);
    } else {
        ret = FixBufPutUint32(&req, 0);
    }
    EXPECT_EQ(GMERR_OK, ret);
    if (cfgJson != NULL) {
        putText.str = cfgJson;
        putText.len = strlen(cfgJson) + 1;
        ret = FixBufPutText(&req, &putText);
    } else {
        ret = FixBufPutUint32(&req, 0);
    }
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_KV_TABLE,
        FixBufGetPos(&req) - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
    QryTestReleaseSession(conn);
    return ret;
}

int32_t QryTestDropKvTable(char *tableName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    TextT labelName = {0};
    labelName.str = tableName;
    labelName.len = strlen(tableName) + 1;
    FixBufferT req = {0};
    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_KV_TABLE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackDropKvTableStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    return ret;
}

int32_t QryTestKvDmlInner(uint32_t labelId, TextT *labelName, TextT *key, TextT *value, FixBufferT **rsp,
    MsgOpcodeRpcE opCode, FixBufferT *req, DrtConnectionT *conn, SetKvMsg *setKvMsg, DmKvLabelT *kvTable)
{
    int32_t ret;
    if (labelId == 0) {
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName->str);
        ret = CataGetLabelByName(&cataKey, &kvTable, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
        labelId = kvTable->metaCommon.metaId;
        CataReleaseLabel(kvTable);
    }
    setKvMsg->value = NULL;
    setKvMsg->key = NULL;
    setKvMsg->labelId = labelId;
    if (opCode != MSG_OP_RPC_GET_KV_RECORD_COUNT) {
        setKvMsg->key = key;
    }
    if (opCode == MSG_OP_RPC_SET_KV) {
        setKvMsg->value = value;
    }
    ret = UtSendKvMsg(conn, req, setKvMsg, opCode);
    return ret;
}

int32_t UtSendWrongKvMsg(DrtConnectionT *conn, FixBufferT *req, SetKvMsgT *setKvMsg, MsgOpcodeRpcE opcode)
{
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    MsgHeaderT *reqHeader = RpcPeekMsgHeader(req);
    reqHeader->flags |= CS_FLAG_NOT_FIRST_SPLIT;
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, setKvMsg->labelId));
    if (setKvMsg->key != NULL) {
        EXPECT_EQ(GMERR_OK, FixBufPutText(req, setKvMsg->key));
    }
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(
        ProtocolPeekFirstOpHeader(req), opcode, req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    return FastpathEntry(&serviceCtx, &procCtx);
}

int32_t QryTestKvDmlFuzz(
    uint32_t labelId, TextT *labelName, TextT *key, TextT *value, FixBufferT **rsp, MsgOpcodeRpcE opCode)
{
    uint32_t ret;
    FixBufferT req = {0};
    DmKvLabelT *kvTable = NULL;
    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            return ret;
        }
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            return ret;
        }
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        SetKvMsg setKvMsg;
        ret = QryTestKvDmlInner(labelId, labelName, key, value, rsp, opCode, &req, conn, &setKvMsg, kvTable);
        if (ret != GMERR_OK) {
            break;
        }
        FixBufRelease(&req);
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            break;
        }
        ret = UtSendWrongKvMsg(conn, &req, &setKvMsg, opCode);
        QryTestReleaseSession(conn);
    } while (0);
    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

int32_t QryTestKvDml(
    uint32_t labelId, TextT *labelName, TextT *key, TextT *value, FixBufferT **rsp, MsgOpcodeRpcE opCode)
{
    uint32_t ret;
    FixBufferT req = {0};
    DmKvLabelT *kvTable = NULL;
    do {
        ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        if (ret != GMERR_OK) {
            return ret;
        }
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, rsp);
        if (ret != GMERR_OK) {
            return ret;
        }
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
        SetKvMsg setKvMsg;
        ret = QryTestKvDmlInner(labelId, labelName, key, value, rsp, opCode, &req, conn, &setKvMsg, kvTable);
        QryTestReleaseSession(conn);
    } while (0);
    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

void QryTestBeginScanKv(
    DrtConnectionT *conn, char *labelName, uint64_t limitCount, DrtConnWritePackFunc drtFunc, uint16_t preFetchRows)
{
    uint32_t ret;
    FixBufferT req = {0};
    MsgHeaderT *msgHeader = NULL;

    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);

    // scan begin
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    DbPrepExecReqT *reqHead = (DbPrepExecReqT *)FixBufReserveData(&req, sizeof(DbPrepExecReqT));
    reqHead->exec.preFetchRows = preFetchRows;
    // labelId
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    DmKvLabelT *kvTable = NULL;
    EXPECT_EQ(GMERR_OK, CataGetLabelByName(&cataKey, &kvTable, NULL));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, kvTable->metaCommon.metaId));
    CataReleaseLabel(kvTable);

    // limitCount
    EXPECT_EQ(GMERR_OK, FixBufPutUint64(&req, limitCount));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_SCAN_KV_BEGIN,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)drtFunc);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufRelease(&req);
    return;
}

int32_t QryTestServiceProcReqEntryGetRsp(
    DrtConnectionT *conn, MsgHeaderT *msgHeader, FixBufferT *request, void *ctx, FixBufferT **rsp)
{
    uint32_t ret;
    SessionT *session = (SessionT *)conn->session;
    *rsp = QrySessionGetRsp(session);
    uint32_t len = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    (*rsp)->pos = len;
    (*rsp)->seekPos = len;
    (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    serviceCtx.ctx = ctx;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(request, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    clearAllStub();
    return ret;
}

Status QryTestBeginCheck(uint32_t labelId, uint32_t partitionId)
{
    Status ret;
    FixBufferT req;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = (Status)FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
    msgHeader->opNum = 1;
    ret = (Status)FixBufPutUint32(&req, labelId);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = (Status)FixBufPutUint32(&req, partitionId);
    if (ret != GMERR_OK) {
        return ret;
    }
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_BEGIN_CHECK,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

Status QryAddAgedTaskStub(const QryAgedTaskArgsT *agedTaskArgs)
{
    return GMERR_OK;
}

Status QryAddAgedTaskFailedStub(const QryAgedTaskArgsT *agedTaskArgs)
{
    return GMERR_INTERNAL_ERROR;
}

Status QryHeapTupleBufIsTruncatedStub(const QryLabelCursorT *cursor, bool *isTruncate)
{
    return GMERR_OK;
}

Status QryTestEndCheck(
    uint32_t labelId, uint32_t partitionId, bool isAbnormal, bool isAddAgeTask, bool isAddAgeTaskFailed)
{
    Status ret;
    FixBufferT req;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = (Status)FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (isAddAgeTaskFailed) {
        (void)setStubC((void *)QryAddAgedTask, (void *)QryAddAgedTaskFailedStub);
    } else if (!isAddAgeTask) {
        (void)setStubC((void *)QryAddAgedTask, (void *)QryAddAgedTaskStub);
    }

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
    msgHeader->opNum = 1;
    ret = (Status)FixBufPutUint32(&req, labelId);
    ret = (Status)FixBufPutUint32(&req, partitionId);
    ret = (Status)FixBufPutUint32(&req, isAbnormal);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_END_CHECK,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    if (isAddAgeTaskFailed) {
        EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
    QryTestReleaseSession(conn);
    FixBufRelease(&req);
    clearAllStub();
    (void)setStubC((void *)QryHeapTupleBufIsTruncated, (void *)QryHeapTupleBufIsTruncatedStub);
    return ret;
}

Status QryTestEndCheckNoClearStub(
    DrtConnectionT *conn, FixBufferT *req, uint32_t labelId, uint32_t partitionId, bool isAbnormal)
{
    Status ret;
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
    msgHeader->opNum = 1;
    ret = (Status)FixBufPutUint32(req, labelId);
    ret = (Status)FixBufPutUint32(req, partitionId);
    ret = (Status)FixBufPutUint32(req, isAbnormal);
    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_END_CHECK,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

CataRoleT *UtQueryGetRoleHaveAllPrivs(SeRunCtxHdT seInstance)
{
    DbSessionCtxT *ctx = NULL;
    SeGetResSessionCtx(seInstance, &ctx);
    CataRoleT *role = (CataRoleT *)ctx->session->role;
    for (int i = 0; i < CATA_OBJ_TYPE_NUM; i++) {
        role->sysPrivileges[i].privilege = 0xffffffff;
    }
    return role;
}

CataRoleT *UtQueryGetRoleHaveNoPrivs(SeRunCtxHdT seInstance)
{
    DbSessionCtxT *ctx = NULL;
    SeGetResSessionCtx(seInstance, &ctx);
    CataRoleT *role = (CataRoleT *)ctx->session->role;
    for (int i = 0; i < CATA_OBJ_TYPE_NUM; i++) {
        role->sysPrivileges[i].privilege = 0x00000000;
    }
    return role;
}

uint64_t g_enterDeleteTime = 0;
int32_t g_stubId = 0;

Status QryExecuteDeleteVertexInfoStub(QryStmtT *stmt, QryLabelCursorT *cursor)
{
    g_enterDeleteTime++;
    if (g_enterDeleteTime == 2) {
        return GMERR_SUB_PUSH_QUEUE_FULL;
    }

    clearStub(g_stubId);
    Status ret = QryExecuteDeleteVertexInfo(stmt, cursor);
    g_stubId = setStubC((void *)QryExecuteDeleteVertexInfo, (void *)QryExecuteDeleteVertexInfoStub);
    return ret;
}

int32_t QryTestPublicProcReqEntryGetRsp(
    DrtConnectionT *conn, MsgHeaderT *msgHeader, FixBufferT *request, void *ctx, FixBufferT **rsp)
{
    uint32_t ret;
    SessionT *session = (SessionT *)conn->session;
    *rsp = QrySessionGetRsp(session);
    uint32_t len = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    (*rsp)->pos = len;
    (*rsp)->seekPos = len;
    (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    serviceCtx.ctx = ctx;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(request, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    clearAllStub();
    return ret;
}

#ifdef __cplusplus
}
#endif
