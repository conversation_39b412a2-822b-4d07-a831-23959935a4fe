#ifndef QUERY_UT_BASE_H
#define QUERY_UT_BASE_H
#include "gtest/gtest.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "adpt_types.h"
#include "dm_data_basic.h"
#include "dm_data_prop.h"
#include "stub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "db_rpc_msg_op.h"
#include "ee_context.h"
#include "db_mem_context.h"
#include "adpt_memory.h"
#include "se_instance.h"
#include "drt_instance_inner.h"
#include "drt_schedule_manager.h"
#include "db_list.h"
#include "db_common_init.h"
#include "ee_fastpath.h"
#include "se_heap.h"
#include "ee_session_interface.h"
#include "db_dynmem_algo.h"
#include "dm_data_topo.h"
#include "dm_meta_subscription.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_priv.h"
#include "ee_topo_label.h"
#include "se_dfx.h"
#include "adpt_rdtsc.h"
#include "ee_dml_common.h"
#include "ee_dml_vertex.h"
#include "ee_tuplebuf_array.h"
#include "se_resource_column.h"
#include "srv_data_fastpath.h"
#include "srv_data_public.h"
#include "ee_cltcache.h"
#include "db_rpc_msg.h"
#include "ee_dml_sort.h"
#include "cpl_public_verifier_dcl.h"
#include "ee_dcl_desc.h"
#include "cpl_fp_parser_dml.h"
#include "cpl_public_parser_ddl.h"
#include "ut_dm_common.h"
#include "ut_dm_meta_topo_label_prop.h"
#include "ut_dm_meta_kv_base_label.h"
#include "ptl_datalog_ctx.h"

#ifndef GET_INSTANCE_ID
#define GET_INSTANCE_ID 1
#endif

#define QRY_FREE(p)        \
    do {                   \
        if ((p) != NULL) { \
            free(p);       \
        }                  \
    } while (0);

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint32_t batchCntMax;
    uint32_t direction;
} QryMultiVertexAttr;

typedef struct DeleteVertexMsg {
    uint32_t labelId;
    uint32_t version;
    uint32_t uuid;
    uint32_t autoOperateFlag;
    uint32_t scanFlag;
    uint32_t indexId;
    TextT labelName;
    TextT leftFilterBuf;
    TextT rightFilterBuf;
    TextT condBufText;
} DeleteVertexMsgT;

typedef struct UpdateVertexMsg {
    uint32_t labelId;
    uint32_t autoOperateFlag;
    uint32_t indexId;
    uint32_t version;
    uint32_t uuid;
    TextT labelName;
    TextT vertexBufText;
    TextT filterBufText;
    TextT condBufText;
} UpdateVertexMsgT;

typedef struct ReplaceVertexMsg {
    uint32_t labelId;
    uint32_t version;
    uint32_t uuid;
    uint32_t flag;
    TextT labelName;
    TextT vertexBufText;
} ReplaceVertexMsgT;

typedef struct InsertVertexMsg {
    uint32_t labelId;
    uint32_t flag;
    uint32_t version;
    uint32_t uuid;
    TextT labelName;
    TextT vertexBufText;
} InsertVertexMsgT;

typedef struct SetKvMsg {
    uint32_t labelId;
    TextT *labelName;
    TextT *key;
    TextT *value;
} SetKvMsgT;

typedef struct InsertEdgeMsg {
    TextT labelName;
    TextT filterBufText;
} InsertEdgeMsgT;

typedef struct DeleteEdgeMsg {
    TextT labelName;
    TextT filterBufText;
} DeleteEdgeMsgT;

typedef int32_t (*DrtConnWritePackFunc)(const DrtConnectionT *conn, FixBufferT *msg);
typedef Status (*AddSubUserCbFuncNameFunc)(DmSubscriptionT *subs);
typedef Status (*ExpandChannelCapacityFunc)(DmSubscriptionT *sub);

typedef struct QryTestCreateSubsParam {
    char *subsName;
    const char *subsJson;
    DbCreateSubAckT *createSubAck;
    DrtConnWritePackFunc drtConnWritePackStub;
    AddSubUserCbFuncNameFunc addSubCbFunc;
    ExpandChannelCapacityFunc expandChannelFunc;
    uint32_t nspId;  // outparam
} QryTestCreateSubsParamT;

QryTestCreateSubsParamT CreateDefaultCreatSubParam(void);

void QryTestSetCurStmtID(uint32_t stmtID);
uint32_t QryTestGetCurStmtID();
void DbFillMsgHeader(const FixBufferT *fixBuffer, int opCode, uint32_t headerOff, int32_t reqTimeOut, uint32_t stmtId);

int32_t QrySetCfg(const char *configName, const char *configValue);
// init storage module
int32_t DbInitStorage(DbMemCtxT *topShmMemCtx);
int32_t BaseInit(bool isFastReadUncommitted = true, bool persistentMode = false);
int32_t BaseInitWithoutSystbl(bool isFastReadUncommitted = true, bool persistentMode = false);
int32_t BaseInitWithConfigFile(const char *configFileName, bool isFastReadUncommitted = true);
void BaseUninit();
void BaseUninit4SysTableUt();
DbMemCtxT *QryGetDyAlgoCtxVertexBase();
extern "C" uint8_t DrtGetConnFlowCtrlLevel(DrtConnectionT *conn);
extern "C" void DrtLongOpLog(DrtProcCtxT *procCtx, uint64_t sendTime);
extern "C" Status FastpathEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx);
extern "C" Status PublicServiceEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx);
extern "C" void QryParseYangUniqueNodeId(DmVertexLabelT *vertexLabel);
uint8_t DrtGetConnFlowCtrlLevelStub(DrtConnectionT *conn);
void DrtFreeMsgStub(FixBufferT *msg);
MsgHeaderT *DrtConnWritePackBegin(const DrtConnectionT *conn, bool isRpcGetOpHeader);
int32_t DrtConnWritePackStub(const DrtConnectionT *conn, FixBufferT *msg);
int32_t DrtConnWritePackFailStub(const DrtConnectionT *conn, FixBufferT *msg);
Status HeapLabelGetPerfStatStub(
    HeapCntrAcsInfoT *heapCntrAcsInfo, HeapPerfStatT *heapPerfStat, HeapCfgStatT *heapCfgStat);
int32_t QryCreateRsp(DrtConnectionT *conn, FixBufferT **rsp);
void QryReleaseRsp(FixBufferT *rsp);

int32_t QryTestCreateNamespace(DmNamespaceT *nsp, FixBufferT **rsp = nullptr);
int32_t QryTestDropNamespace(char *nspName, FixBufferT **rsp = nullptr);

void QryTestExecuteCreateUser(QryCreateOrDropUserBatchDescT *desc);
void QryTestExecuteDropUser(char *userName, char *processName);

void QryTestExecuteGrantOrRevokeSysPrivs(QryGrantOrRevokeSysPrivsDescT *desc, bool isGrant);
void QryTestExecuteGrantOrRevokeObjPrivs(QryGrantOrRevokeObjPrivsDescT *desc, bool isGrant, CataRoleT *role);

int32_t QryTestCreateVertexLabelWithAppointLabelName(
    char *cfgJson, char *labelJson, char *appointLabelName, FixBufferT **rsp = nullptr);
int32_t QryTestCreateVertexLabel(char *cfgJson, char *labelJson, FixBufferT **rsp = nullptr);
int32_t QryTestAlterVertexLabel(char *labelJson, bool isCompatible, FixBufferT **rsp = nullptr);

int32_t QryTestCreateEdgeLabel(char *cfgJson, char *labelJson, FixBufferT **rsp = nullptr);
int32_t QryTestInsertVertex(
    char *labelName, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue, FixBufferT **rsp = nullptr);
int32_t QryTestInsertVertex2(const char *labelName, DmVertexT *vertex, FixBufferT **rsp = nullptr);
int32_t QryTestReplaceVertexBatch(char *labelName, uint32_t replaceNum, uint32_t propertyNum, char **propertyName,
    DmValueT *propertyValue, FixBufferT **rsp, bool isReplace);
int32_t QryTestBeginScanVertex(DrtConnectionT *conn, const char *labelName, const char *keyName, uint32_t propertyNum,
    DmValueT *leftPropertyValues, DmValueT *rightPropertyValues, uint32_t scanFlag, char *cond, char *resultSetStr,
    uint64_t limitCount, uint16_t preFetchRows);

int32_t QryTestBeginSortScanVertex(DrtConnectionT *conn, char *labelName, char *propName, uint32_t scanFlag,
    char *resultSetStr, uint64_t limitCount, FixBufferT **rsp, uint16_t preFetchRows, OrderDirectionE direction);

int32_t QryTestReleaseStmt(DrtConnectionT *conn);
int32_t QryTestDropVertexLabel(char *labelNameStr, uint32_t isDropAssoc, FixBufferT **rsp = nullptr);
int32_t QryTestDropVertexLabelWithVersion(
    char *labelNameStr, uint32_t isDropAssoc, uint32_t versionId, FixBufferT **rsp, bool dropVersion = false);
int32_t QryTestDropEdgeLabel(char *labelName, FixBufferT **rsp = nullptr);

int32_t QryTestQueryMultiVertexFetchNext(DrtConnectionT *conn, DrtConnWritePackFunc drtFunc);
int32_t QryTestQueryMultiVertexEnd(DrtConnectionT *conn, FixBufferT **rsp = nullptr);
int32_t QryTestCreateBaseVertexLabelFirst(char *cfgJson = (char *)"{\"max_record_count\":1000}");
int32_t QryTestDropBaseVertexLabelFirst();
int32_t QryTestCreateBaseVertexLabelSecond(char *cfgJson = (char *)"{\"max_record_count\":1000}");
int32_t QryTestDropBaseVertexLabelSecond();
int32_t QryTestCreateBaseVertexLabelChLabel(char *cfgJson = (char *)"{\"max_record_count\":1000}");
int32_t QryTestCreateBaseEdgeLabel();
int32_t QryTestDropBaseEdgeLabel();
int32_t QryTestCreateSubs(char *subsName, const char *subsJson, DrtConnWritePackFunc drtConnWritePackStub,
    DbCreateSubAckT *createSubAck = NULL);
int32_t QryTestCreateSubsBase(QryTestCreateSubsParamT *createParam);
int32_t UtSendInsertVertexMsg(DrtConnectionT *conn, FixBufferT *req, InsertVertexMsgT *insMsg);
int32_t QryTestUpdateVertex(char *labelName, const char *indexName, uint32_t indexPropertyNum,
    DmValueT *indexPropertyValue, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue, uint32_t flag,
    FixBufferT **rsp = nullptr);
int32_t QryTestReplaceOrMergeVertex(char *labelName, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue,
    uint32_t opCode, FixBufferT **rsp = nullptr);
int32_t QryTestDeleteVertex(char *labelName, const char *indexName, uint32_t indexPropertyNum,
    DmValueT *leftPropertyValues, DmValueT *rightPropertyValues, uint32_t autoFlag, uint32_t scanFlag,
    FixBufferT **rsp = nullptr);
int32_t QryTestGetCount(
    DrtConnectionT *conn, char *labelName, const char *keyName, uint32_t propertyNum, DmValueT *propertyValues);
int32_t QryTestCreateLabelTree();
int32_t UtSendUpdateVertexMsg(DrtConnectionT *conn, FixBufferT *req, UpdateVertexMsgT *updMsg);
int32_t UtSendDeleteVertexMsg(DrtConnectionT *conn, FixBufferT *req, DeleteVertexMsgT *delMsg);

int32_t QryTestCreateKvTable(char *tableName, char *configJson, FixBufferT **rsp = nullptr);
int32_t QryTestDropKvTable(char *tableName);
int32_t QryTestKvDml(
    uint32_t labelId, TextT *labelName, TextT *key, TextT *value, FixBufferT **rsp, MsgOpcodeRpcE opCode);
int32_t QryTestKvDmlFuzz(
    uint32_t labelId, TextT *labelName, TextT *key, TextT *value, FixBufferT **rsp, MsgOpcodeRpcE opCode);
void QryTestBeginScanKv(
    DrtConnectionT *conn, char *labelName, uint64_t limitCount, DrtConnWritePackFunc drtFunc, uint16_t preFetchRows);

int32_t DrtConnWritePackDropKvTableStub(const DrtConnectionT *conn, FixBufferT *msg);
Status QryTestDrtConnWritePackSuccessStub(const DrtConnectionT *conn, FixBufferT *msg);
int32_t DrtConnWritePackTruncateKvTableStub(const DrtConnectionT *conn, FixBufferT *msg);
int32_t QryTestServiceProcReqEntryGetRsp(
    DrtConnectionT *conn, MsgHeaderT *msgHeader, FixBufferT *request, void *ctx, FixBufferT **rsp = nullptr);
int32_t QryTestPublicProcReqEntryGetRsp(
    DrtConnectionT *conn, MsgHeaderT *msgHeader, FixBufferT *request, void *ctx, FixBufferT **rsp = nullptr);

Status QryTestAllocConnWithPara(DrtConnectionT **conn, const char *connName, uint8_t flag);
const char *QryGetBaseSubsChanneName();
Status QryTestAllocSubConn(DrtConnectionT **conn, const char *channeName);
Status QryTestAllocConn(DrtConnectionT **conn);
void QryTestReleaseConn(DrtConnectionT *conn);
void QryTestReleaseSubConn(DrtConnectionT *conn);
Status QryTestAllocSessionWithoutStub(DrtConnectionT **conn);
void QryTestReleaseSessionWithoutStub(DrtConnectionT *conn);
Status QryTestAllocSession(DrtConnectionT **conn, FixBufferT **rsp = NULL);
void QryTestReleaseSession(DrtConnectionT *conn);
Status QryInitUserAndGroupStub(SessionT *session, SessionParamT *param);
Status QryCheckLoginPrivByGroupListStub(SessionT *session, bool *login, CataRoleT *role);
Status QryHeapTupleBufIsTruncatedStub(const QryLabelCursorT *cursor, bool *isTruncate);

void DrtLongOpLogStub(DrtProcCtxT *procCtx, uint64_t sendTime);

Status QryTestEndCheck(
    uint32_t labelId, uint32_t partitionId, bool isAbnormal, bool isAddAgeTask, bool isAddAgeTaskFailed);
Status QryTestBeginCheck(uint32_t labelId, uint32_t partitionId);
Status QryTestEndCheckNoClearStub(
    DrtConnectionT *conn, FixBufferT *req, uint32_t labelId, uint32_t partitionId, bool isAbnormal);

extern int32_t g_stubId;
extern uint64_t g_enterDeleteTime;
Status QryExecuteDeleteVertexInfoStub(QryStmtT *stmt, QryLabelCursorT *cursor);

CataRoleT *UtQueryGetRoleHaveAllPrivs(SeRunCtxHdT seInstance);
CataRoleT *UtQueryGetRoleHaveNoPrivs(SeRunCtxHdT seInstance);
void DrtFreeProcCtxStub(DrtProcCtxT *procCtx);

extern Status DeserializeAndVerifyUnloadReq(SessionT *session, UnloadCtxT *unloadCtx);

void AddSubsUserCbFuncNameStub(void);
void DelSubsUserCbFuncNameStub(void);

Status CataLoginVerify(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isDBA, bool *isGroupLogin, CataRoleT *role);

inline Status UtCataRemoveVertexLabelById(DbInstanceHdT dbInstance, uint32_t vlId)
{
    return CataRemoveVertexLabelById(NULL, vlId, false);
}

#ifdef __cplusplus
}
#endif

#endif
