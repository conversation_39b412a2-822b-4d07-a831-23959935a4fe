#include "query_ut_base.h"
#include "db_text.h"
#include "ee_cmd.h"
#include "ee_ddl_edge_label.h"
#include "ut_dm_common.h"

class UtQueryDDLLabel : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};
#ifndef FEATURE_PERSISTENCE
// 不支持资源字段
TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithError)
{
    // config中delta_store_name不是string
    char *cfgJson = (char *)R"({"max_record_count":1000, "delta_store_name":11, "writers":"XXuser"})";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErr2",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F11", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexErr2", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";
    int32_t ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // config中delta_store_name是空字符
    char *cfgJson2 = (char *)R"({"max_record_count":1000, "delta_store_name":"", "writers":"XXuser"})";
    ret = QryTestCreateVertexLabel(cfgJson2, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    // config中delta_store_name长度超过128
    char *cfgJson3 = (char
            *)R"({"max_record_count":1000, "delta_store_name":"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "writers":"XXuser"})";
    ret = QryTestCreateVertexLabel(cfgJson3, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    // 不同时支持deltaStore和resource
    char *cfgJson4 = (char *)R"({"max_record_count":1000, "delta_store_name":"qwe", "writers":"XXuser"})";
    char *labelJson2 = (char *)R"([{
        "type":"record",
        "name":"labelvertexErr3",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F00", "type":"resource", "nullable": false},
            {"name":"F11", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexErr3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson4, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // json不合法
    char *cfgJson5 = (char *)R"()";
    ret = QryTestCreateVertexLabel(cfgJson5, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    // resource字段最多4个
    char *labelJson3 = (char *)R"([{
        "type":"record",
        "name":"labelvertexErr3",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F00", "type":"resource", "nullable": false},
            {"name":"F01", "type":"resource", "nullable": false},
            {"name":"F02", "type":"resource", "nullable": false},
            {"name":"F03", "type":"resource", "nullable": false},
            {"name":"F04", "type":"resource", "nullable": false},
            {"name":"F11", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexErr3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson4, labelJson3, NULL);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);

    // comment不是字符串
    char *labelJson4 = (char *)R"([{
        "type":"record",
        "name":"labelvertexErr3",
        "comment":11,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F11", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexErr3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson4, labelJson4, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // json不是数组,转Lite解析流程,发现没有name
    char *labelJson5 = (char *)R"({})";
    ret = QryTestCreateVertexLabel(cfgJson4, labelJson5, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // json数组个数为0
    char *labelJson6 = (char *)R"([])";
    ret = QryTestCreateVertexLabel(cfgJson4, labelJson6, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    // 边json中缺少constraint
    char *labelJsonEdge = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2"
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 边json的constraint中缺少operator_type
    char *labelJsonEdge2 = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge2);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 边json的constraint中缺少conditions
    char *labelJsonEdge3 = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and"
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge3);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 边json的constraint中出表不合法
    char *labelJsonEdge4 = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"sou":"F0", "dest_property":"F0"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge4);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 边json的constraint中入表不合法
    char *labelJsonEdge5 = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "des":"F0"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge5);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 边json的comment不是字符串, 不进行校验
    char *labelJsonEdge6 = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":222,
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge6);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // 边json的name不是字符串
    char *labelJsonEdge7 = (char *)R"([{
        "name":88,
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge7);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 边json的dest_vertex_label不是字符串
    char *labelJsonEdge8 = (char *)R"([{
        "name":"asd",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":88,
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge8);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 边json的不是数组
    char *labelJsonEdge9 = (char *)R"({})";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge9);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 边json数组个数为0
    char *labelJsonEdge10 = (char *)R"([])";
    ret = QryTestCreateEdgeLabel(NULL, labelJsonEdge10);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
}
#endif

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabel1)
{
    char *cfgJson = (char *)"{\"isFastReadUncommitted\":false}";
    uint32_t ret = QryTestCreateBaseVertexLabelFirst(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropBaseVertexLabelFirst();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithSamePropertyName)
{
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErr",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F1", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexErr", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    uint32_t ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Duplicate column. Duplicate property name: F1.vertexLabel name:labelvertexErr.", lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetVertexLabel)
{
    char *cfgJson = (char *)"{\"isFastReadUncommitted\":false}";
    uint32_t ret;
    ret = QryTestCreateBaseVertexLabelFirst(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"labelvertex1";
    uint32_t labelNameLen = strlen(labelName) + 1;

    uint32_t labelId = 123;
    uint16_t isView = 0;
    uint16_t isCsMode = 0;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    ret = FixBufPutUint32(&req, labelId);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    ret = FixBufPutUint16(&req, isView);
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(&req, isCsMode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(&req, DB_INVALID_UINT32);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    FixBufferT *rsp = NULL;
    ret = QryTestPublicProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
    EXPECT_EQ(GMERR_OK, ret);

    RpcSeekFirstOpMsg(rsp);
    RpcGetLabelRspT *getLabelRsp = (RpcGetLabelRspT *)FixBufGetData(rsp, 0);

    size_t bufferLen = sizeof(ShmemPtrT) + sizeof(ShmemPtrT) + sizeof(uint32_t);
    EXPECT_EQ((uint32_t)bufferLen, getLabelRsp->labelBufLen);
    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
    ret = QryTestDropBaseVertexLabelFirst();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_DropVertexLabel)
{
    char *cfgJson = (char *)"{\"isFastReadUncommitted\":false}";
    uint32_t ret;
    ret = QryTestCreateBaseVertexLabelFirst(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    TextT labelName = {.len = strlen("labelvertex1") + 1, .str = (char *)"labelvertex1"};

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, DM_SCHEMA_INVALID_VERSION));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

void QryDDLCreateEdgeLabel()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    ret = QryTestCreateBaseVertexLabelFirst(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCreateBaseVertexLabelSecond(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestCreateBaseEdgeLabel();
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_CreateEdgeLabel success\n");

    char *labelJson = (char *)R"([{
        "name":"edgeLabel2",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_CreateEdgeLabelWithoutCfgJson success\n");
}

void QryDDLDropEdgeLabel()
{
    EXPECT_EQ(GMERR_OK, QryTestDropBaseEdgeLabel());
    char *labelEdgeName = (char *)"edgeLabel2";
    EXPECT_EQ(GMERR_OK, QryTestDropEdgeLabel(labelEdgeName, NULL));
}

void QryDDLDropVertexLabel()
{
    char *labelName1 = (char *)"labelvertex1";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName1, true, NULL));
    char *labelName2 = (char *)"labelvertex2";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName2, true, NULL));
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabelFail)
{
    QryDDLCreateEdgeLabel();
    char *cfgJson1 = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    uint32_t ret = QryTestCreateEdgeLabel(cfgJson1, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Data exception occurs. get label json of edge label.", lastError->str);
    printf("QRY_DDL_CreateEdgeLabelWithoutlabelJson success\n");

    char *labelJson1 = (char *)R"([{
        "name":"edgeLabel3",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";

    ret = QryTestCreateEdgeLabel(cfgJson1, labelJson1);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    printf("QRY_DDL_CreateEdgeLabelWithErrLabelJson success\n");
    QryDDLDropEdgeLabel();
    QryDDLDropVertexLabel();
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabelErr)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufResetMem(&req);

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "name":"edgeLabel4",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";

    uint32_t labelJsonLen = strlen(labelJson) + 1;
    uint32_t cfgJsonLen = strlen(cfgJson) + 1;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = labelJson;
    putText.len = labelJsonLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    char *cfj = (char *)"";
    putText.str = cfj;
    putText.len = 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_EDGE_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Incorrect json content. get config json when get tablespace info.", lastError->str);

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateEdgeLabelWithErrCfgJson success\n");

    conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    char *label = (char *)"";
    putText.str = label;
    putText.len = 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = cfgJson;
    putText.len = cfgJsonLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_EDGE_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    // update serviceCtx;

    serviceCtx.ctx = NULL;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Incorrect json content. Incorrect edge label json.", lastError->str);
    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateEdgeLabelWithErrLabelJson1 success\n");
}

int32_t QryGetEdgeLabelBufferByNameStub(const CataKeyT *cataKey, DmBuffer *buffer)
{
    const char *labelNameBuf = "fighting";
    buffer->len = strlen(labelNameBuf) + 1;
    buffer->buf = (uint8_t *)malloc(buffer->len);
    (void)memcpy_s(buffer->buf, buffer->len, labelNameBuf, buffer->len);
    return GMERR_OK;
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetEdgeLabel)
{
    int32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"edgeLabel1";
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    ret = FixBufPutUint32(&req, 0);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_EDGE_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)QryGetEdgeLabelBufferByName, (void *)QryGetEdgeLabelBufferByNameStub);

    FixBufferT *rsp = NULL;
    ret = QryTestServiceProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    TextT getText;
    ret = FixBufGetText(rsp, &getText);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)9, getText.len);
    if (strcmp("fighting", getText.str) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
    printf("QRY_DDL_GetEdgeLabel success\n");
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetEdgeLabelALL)
{
    int32_t ret;
    QryDDLCreateEdgeLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"edgeLabel1";
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    ret = FixBufPutUint32(&req, 0);
    EXPECT_EQ(GMERR_OK, ret);
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_EDGE_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    FixBufferT *rsp = NULL;
    ret = QryTestServiceProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
    EXPECT_EQ(GMERR_OK, ret);

    TextT getText;
    RpcSeekFirstOpMsg(rsp);
    ret = FixBufGetObject(rsp, &getText);
    EXPECT_EQ(GMERR_OK, ret);

    size_t bufferLen = sizeof(ShmemPtrT) + sizeof(ShmemPtrT) + sizeof(uint32_t);
    EXPECT_EQ((uint32_t)bufferLen, getText.len);

    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
    printf("QRY_DDL_GetEdgeLabel success\n");
    QryDDLDropEdgeLabel();
    QryDDLDropVertexLabel();
}

TEST_F(UtQueryDDLLabel, QRY_DDL_DropEdgeLabel)
{
    QryDDLCreateEdgeLabel();
    DrtConnectionT *conn = NULL;
    uint32_t ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"edgeLabel1";
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_EDGE_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    (void)setStubC((void *)HeapLabelGetPerfStat, (void *)HeapLabelGetPerfStatStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
    printf("QRY_DDL_DropEdgeLabel success\n");
    char *labelEdgeName = (char *)"edgeLabel2";
    EXPECT_EQ(GMERR_OK, QryTestDropEdgeLabel(labelEdgeName, NULL));
    QryDDLDropVertexLabel();
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabelWithSchema)
{
    uint32_t ret;
    char *cfgJson1 = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    ret = QryTestCreateBaseVertexLabelFirst(cfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCreateBaseVertexLabelSecond(cfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    char *cfgJson2 = (char *)"{\"max_record_count\":1000}";
    char *labelJson = (char *)R"([{
        "name":"edgeLabel5",
        "source_vertex_label":"labelvertex1",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"labelvertex2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1", "dest_property":"F1"}
            ]
        },
        "fields":[
            {"name":"F0", "type":"char","default":"a"},
            {"name":"F1", "type":"uchar","default":"f"},
            {"name":"F2", "type":"uchar","default":"f"},
            {"name":"F3", "type":"int16","default":2}
        ],
        "super_fields":[
            {"name":"superfield0", "comment":"test", "fields":{"begin":"F0","end":"F2"}},
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ]
    }])";

    ret = QryTestCreateEdgeLabel(cfgJson2, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_CreateEdgeLabelWithSchema success\n");
    char *labelEdgeName = (char *)"edgeLabel5";
    EXPECT_EQ(GMERR_OK, QryTestDropEdgeLabel(labelEdgeName, NULL));
    QryDDLDropVertexLabel();
}

#ifndef FEATURE_PERSISTENCE
// 不支持super_field
TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_MAX_UINT32)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":4294967295}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto1",
        "fields":[
            {"name":"F0", "type":"uint32", "auto_increment":true},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto1", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_MAX_UINT32_ERR)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":4294967296}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto2",
        "fields":[
            {"name":"F0", "type":"uint32", "auto_increment":true},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto2", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Not normal property. autoinc F0 value more than 4294967295.vertexLabel name:labelvertexAuto2.";
    EXPECT_STREQ(result, lastError1->str);
}

#ifndef FEATURE_PERSISTENCE
// 不支持super_field
TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_WITH_SUPER)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":4294967295}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto3",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32", "auto_increment":true},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F0","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto3", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Inv table definition. inv type of super_fields property F1.vertexLabel name:labelvertexAuto3.";
    EXPECT_STREQ(result, lastError1->str);
}
#endif

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_TYPE_INT32_ERR)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":10000}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto4",
        "fields":[
            {"name":"F0", "type":"int32", "auto_increment":true},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto4", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Datatype mismatch. parse autoinc, property:F0 type:6.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_BE_ZERO)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":0, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAutoBeZero",
        "fields":[
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAutoBeZero", "name":"T39_K2", "fields":["F1"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"labelvertexAutoBeZero", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_MULTIPLE_ERR)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":10000}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto5",
        "fields":[
            {"name":"F0", "type":"uint32", "auto_increment":true},
            {"name":"F1", "type":"uint32", "auto_increment":true},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto5", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Inv table definition. Too much auto increment property in vertexLabel "
                                 "labelvertexAuto5, curr num:2, max num:1.vertexLabel name:labelvertexAuto5.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_TreeModel_ERR)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":10000}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "labelvertexAuto6",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        },
        { "name": "c3", "type": "string", "size":20, "nullable":true },
        { "name": "c4", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "float" },
            { "name": "b2", "type": "uint8" }
          ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
          "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true},
            { "name": "t2", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "h1", "type": "uint16" },
                { "name": "h2", "type": "uint32", "auto_increment":true}
              ]
            },
            { "name": "t3", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "labelvertexAuto6",
          "fields": [ "c0" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Inv table definition. Property:h2,datatype:7,autoinc:1";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_FALSE)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":4294967295}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto7",
        "fields":[
            {"name":"F0", "type":"uint32", "auto_increment":false},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto7", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Datatype mismatch. parse autoinc, property:F0 type:7.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_WITH_NO_VALUE)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto8",
        "fields":[
            {"name":"F0", "type":"uint32", "auto_increment":true},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto8", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    const char *result =
        (char *)"Data exception occurs. Auto increment is not set or inited be zero.vertexLabel name:labelvertexAuto8.";
    TextT *lastErr = DbGetLastErrorInfo();
    EXPECT_STREQ(lastErr->str, result);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_WITH_NO_PROPERTY_OK)
{
    uint32_t ret;
    char *cfgJson =
        (char *)"{\"max_record_count\":1000, \"auto_increment\":4294967295, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto9",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto9", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_AUTOINC_WITH_SCHEMA_VALUE)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"auto_increment\":4294967295}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAuto10",
        "fields":[
            {"name":"F0", "type":"uint32", "auto_increment":1},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertexAuto10", "name":"T39_K2", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Datatype mismatch. parse autoinc, property:F0 type:7.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SUPER_FIELD_WITH_SAME_NAME)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"int32"}
        ],
        "super_fields":[
            {"name":"superfield0", "comment":"test", "fields":{"begin":"F0","end":"F1"}},
            {"name":"superfield0", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertex3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Duplicate column. super_fields superfield0 have the same name.vertexLabel name:labelvertex3.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SUPER_FIELD_WITH_INVALID_PROPER_NUM)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"int32"}
        ],
        "super_fields":[
            {"name":"superfield0", "comment":"test", "fields":{"begin":"F3","end":"F1"}},
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertex3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. In super_fields superfield0, begin should before end, and "
                                 "the spacing should not exceed:1024.vertexLabel name:labelvertex3.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SUPER_FIELD_WITH_INVALID_PROPER_TYPE)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"string", "default":"fff", "size":4},
            {"name":"F3", "type":"int32"}
        ],
        "super_fields":[
            {"name":"superfield0", "comment":"test", "fields":{"begin":"F0","end":"F1"}},
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertex3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Inv table definition. inv type of super_fields property F2.vertexLabel name:labelvertex3.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SUPER_FIELD_WITH_INVALID_PROPER_NAME)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"int32"}
        ],
        "super_fields":[
            {"name":"superfield0", "comment":"test", "fields":{"begin":"F0","end":"F4"}},
            {"name":"superfield1", "comment":"test", "fields":{"begin":"F2","end":"F3"}}
        ],
        "keys":[
            {"node":"labelvertex3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_INDEX_WITHOUT_FIELDS)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"string", "default":"fff", "size":4},
            {"name":"F3", "type":"int32"}
        ],
        "keys":[
            {"node":"labelvertex3", "name":"T39_K0", "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Undefine column. colum:fieldsThe vertexLabel is labelvertex3.The indexLabel is T39_K0.", lastError->str);
}

void QryDDLCreateSrcVertexLabelNormalName()
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"type\":\"record\", \"name\":\"srcVextexLabelName175Normal\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"fixed\", \"default\":\"fff\", \"size\":3}],"
                "\"keys\":[{\"node\":\"srcVextexLabelName175Normal\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateDstVertexLabelNormalName()
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"type\":\"record\", \"name\":\"dstVextexLabelName175Normal\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"fixed\", \"default\":\"fff\", \"size\":3}],"
                "\"keys\":[{\"node\":\"dstVextexLabelName175Normal\", \"name\":\"T59_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLDropVertexLabel1NormalName()
{
    char *labelName1 = (char *)"srcVextexLabelName175Normal";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName1, true, NULL));
    char *labelName2 = (char *)"dstVextexLabelName175Normal";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName2, true, NULL));
}

void QryDDLDropEdgeLabel1NormalName()
{
    char *labelEdgeName = (char *)"edgeLabelName175Normal";
    EXPECT_EQ(GMERR_OK, QryTestDropEdgeLabel(labelEdgeName, NULL));
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabel1NormalName)
{
    uint32_t ret;
    QryDDLCreateSrcVertexLabelNormalName();
    QryDDLCreateDstVertexLabelNormalName();
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"name\":\"edgeLabelName175Normal\",\"source_vertex_label\":\"srcVextexLabelName175Normal\","
                "\"dest_vertex_label\":\"dstVextexLabelName175Normal\",\"constraint\":{\"operator_type\":\"and\","
                "\"conditions\":"
                "[{\"source_property\": \"F0\",\"dest_property\": \"F0\"},{\"source_property\": "
                "\"F1\",\"dest_property\": \"F1\"}]}}]";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);

    printf("QRY_DDL_CreateEdgeLabel success\n");
    QryDDLDropEdgeLabel1NormalName();
    QryDDLDropVertexLabel1NormalName();
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateSrcVertexLabelWithSpecialCharAtSignInName)
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"type\":\"record\", \"name\":\"srcVextexLabel@Name175\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"fixed\", \"default\":\"fff\", \"size\":3}],"
                "\"keys\":[{\"node\":\"srcVextexLabel@Name175\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. The object name srcVextexLabel@Name175 contains special character "
                                 "@.vertexLabel name:srcVextexLabel@Name175.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateSrcVertexLabelWithSpecialCharGreaterThanInName)
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"type\":\"record\", \"name\":\"match-:\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"fixed\", \"default\":\"fff\", \"size\":3}],"
                "\"keys\":[{\"node\":\"match-:\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabelWithSpecialCharAtSignInName)
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"name\":\"edgeLabel@Name175\",\"source_vertex_label\":\"srcVextexLabelName175Normal\","
                "\"dest_vertex_label\":\"dstVextexLabelName175Normal\",\"constraint\":{\"operator_type\":\"and\","
                "\"conditions\":"
                "[{\"source_property\": \"F0\",\"dest_property\": \"F0\"},{\"source_property\": "
                "\"F1\",\"dest_property\": \"F1\"}]}}]";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. The object name edgeLabel@Name175 contains special character @.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabelWithSpecialChar)
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson =
        (char *)"[{\"name\":\"edgeLabel/Name175\",\"source_vertex_label\":\"srcVextexLabelName175Normal\","
                "\"dest_vertex_label\":\"dstVextexLabelName175Normal\",\"constraint\":{\"operator_type\":\"and\","
                "\"conditions\":"
                "[{\"source_property\": \"F0\",\"dest_property\": \"F0\"},{\"source_property\": "
                "\"F1\",\"dest_property\": \"F1\"}]}}]";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. The object name edgeLabel/Name175 contains special character /.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithInvaldId)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    // 983040 对应十六进制 0xF0000, 用户指定tableid时必须小于0xF0000，大于或等于均会报错
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983040,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. vertexLabel labelvertexErrId id 983040 exceeds maximum "
                                 "983040.vertexLabel name:labelvertexErrId.";
    EXPECT_STREQ(result, lastError1->str);
}

uint32_t QryScan(
    FixBufferT req, DbMemCtxT *dyAlgoCtxVertex, DrtConnectionT *conn, uint32_t labelId, TextT filterBuf, uint32_t num)
{
    uint32_t ret;
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    DbPrepExecReqT *reqHead = (DbPrepExecReqT *)FixBufReserveData(&req, sizeof(DbPrepExecReqT));
    reqHead->exec.preFetchRows = num;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, labelId));
    uint32_t schemaVersion = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, schemaVersion));
    uint32_t uuid = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, uuid));
    FixBufPutUint32(&req, 0);                              // scan flag
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &filterBuf));  // left indexKey
    FixBufPutUint32(&req, 0);                              // right indexKey
    FixBufPutUint32(&req, 0);                              // isSysviewRecordCmd
    FixBufPutUint32(&req, 0);                              // condition
    FixBufPutUint32(&req, 0);                              // resultSet
    FixBufPutUint32(&req, 0);                              // sortCount
    FixBufPutUint64(&req, 0);                              // limitCount
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_SCAN_VERTEX_BEGIN,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    // scan next
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = QryTestGetCurStmtID();

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_FETCH_CYPHER,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    // update serviceCtx

    serviceCtx.ctx = NULL;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    // scan close
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = QryTestGetCurStmtID();

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_RELEASE_STMT,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    // updata serviceCtx

    serviceCtx.ctx = NULL;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithSecKeys)
{
    uint32_t ret;
    char *labelName = (char *)"T36";
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)
        R"([{
            "type":"record",
            "name":"T36",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T36",
                     "name":"T36_K0",
                     "fields":["F0"],
                     "index":{"type":"localhash"},
                     "constraints":{"unique":true}
                    }
                ]
            }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F2"};
    DmValueT propertyValue[3];
    propertyValue[0].type = DB_DATATYPE_INT32;
    propertyValue[0].value.intValue = 600;
    propertyValue[1].type = DB_DATATYPE_INT32;
    propertyValue[1].value.intValue = 400;
    propertyValue[2].type = DB_DATATYPE_INT32;
    propertyValue[2].value.intValue = 200;

    ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    const char *keyName = "T36_K0";
    DmIndexKeyT *filter = NULL;
    uint8_t *seriBuf = NULL;
    uint32_t length;
    TextT labelNameT = {.len = strlen("T36") + 1, .str = (char *)"T36"};
    DmVertexLabelT *vertexLabel2 = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelNameT.str);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel2);
    uint32_t labelId = vertexLabel2->metaCommon.metaId;
    DmValueT propertyValues;
    propertyValues.type = DB_DATATYPE_INT32;
    propertyValues.value.intValue = 600;
    ret = DmCreateIndexKeyByNameWithMemCtx(
        (DbMemCtxT *)((Session *)conn->session)->memCtx, vertexLabel2, keyName, &propertyValues, 1, &filter);
    EXPECT_EQ(GMERR_OK, ret);
    length = DmIndexKeyGetSeriBufLength(filter);
    seriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, length);
    ret = DmSerializeIndexKey2InvokerBuf(filter, length, seriBuf);
    EXPECT_EQ(GMERR_OK, ret);
    DmDestroyIndexKey(filter);
    CataReleaseVertexLabel(vertexLabel2);
    TextT filterBuf = {.len = length, .str = (char *)seriBuf};
    ret = QryScan(req, dyAlgoCtxVertex, conn, labelId, filterBuf, 1);
    EXPECT_EQ(GMERR_OK, ret);
    DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, seriBuf);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelKeyWithoutProp)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)
        R"([{
            "type":"record",
            "name":"T37",
            "fields":
                [
                    {"name":"F0", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":[],
                     "index":{"type":"localhash"}
                    }
                ]
            }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char *)"Array subscript unsucc. array fields size should between: 1 and 32.The vertexLabel "
                                 "is T37.The indexLabel is T37_K0.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelKeyExceedMaxProp)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)
        R"([{
            "type":"record",
            "name":"T37",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"},
                    {"name":"F3", "type":"int32"},
                    {"name":"F4", "type":"int32"},
                    {"name":"F5", "type":"int32"},
                    {"name":"F6", "type":"int32"},
                    {"name":"F7", "type":"int32"},
                    {"name":"F8", "type":"int32"},
                    {"name":"F9", "type":"int32"},
                    {"name":"F10", "type":"int32"},
                    {"name":"F11", "type":"int32"},
                    {"name":"F12", "type":"int32"},
                    {"name":"F13", "type":"int32"},
                    {"name":"F14", "type":"int32"},
                    {"name":"F15", "type":"int32"},
                    {"name":"F16", "type":"int32"},
                    {"name":"F17", "type":"int32"},
                    {"name":"F18", "type":"int32"},
                    {"name":"F19", "type":"int32"},
                    {"name":"F20", "type":"int32"},
                    {"name":"F21", "type":"int32"},
                    {"name":"F22", "type":"int32"},
                    {"name":"F23", "type":"int32"},
                    {"name":"F24", "type":"int32"},
                    {"name":"F25", "type":"int32"},
                    {"name":"F26", "type":"int32"},
                    {"name":"F27", "type":"int32"},
                    {"name":"F28", "type":"int32"},
                    {"name":"F29", "type":"int32"},
                    {"name":"F30", "type":"int32"},
                    {"name":"F31", "type":"int32"},
                    {"name":"F32", "type":"int32"},
                    {"name":"F33", "type":"int32"},
                    {"name":"F34", "type":"int32"},
                    {"name":"F35", "type":"int32"},
                    {"name":"F36", "type":"int32"},
                    {"name":"F37", "type":"int32"},
                    {"name":"F38", "type":"int32"},
                    {"name":"F39", "type":"int32"},
                    {"name":"F40", "type":"int32"},
                    {"name":"F41", "type":"int32"},
                    {"name":"F42", "type":"int32"},
                    {"name":"F43", "type":"int32"},
                    {"name":"F44", "type":"int32"},
                    {"name":"F45", "type":"int32"},
                    {"name":"F46", "type":"int32"},
                    {"name":"F47", "type":"int32"},
                    {"name":"F48", "type":"int32"},
                    {"name":"F49", "type":"int32"},
                    {"name":"F50", "type":"int32"},
                    {"name":"F51", "type":"int32"},
                    {"name":"F52", "type":"int32"},
                    {"name":"F53", "type":"int32"},
                    {"name":"F54", "type":"int32"},
                    {"name":"F55", "type":"int32"},
                    {"name":"F56", "type":"int32"},
                    {"name":"F57", "type":"int32"},
                    {"name":"F58", "type":"int32"},
                    {"name":"F59", "type":"int32"},
                    {"name":"F60", "type":"int32"},
                    {"name":"F61", "type":"int32"},
                    {"name":"F62", "type":"int32"},
                    {"name":"F63", "type":"int32"},
                    {"name":"F64", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":[
                          "F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7",
                          "F8", "F9", "F10", "F11", "F12", "F13", "F14", "F15",
                          "F16", "F17", "F18", "F19", "F20", "F21", "F22", "F23",
                          "F24", "F25", "F26", "F27", "F28", "F29", "F30", "F31", "F32"],
                     "index":{"type":"localhash"}
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char *)"Array subscript unsucc. array fields size should between: 1 and 32.The vertexLabel "
                                 "is T37.The indexLabel is T37_K0.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelKeyWithMaxProp)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)
        R"([{
            "type":"record",
            "name":"T37",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"},
                    {"name":"F3", "type":"int32"},
                    {"name":"F4", "type":"int32"},
                    {"name":"F5", "type":"int32"},
                    {"name":"F6", "type":"int32"},
                    {"name":"F7", "type":"int32"},
                    {"name":"F8", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":["F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7"],
                     "index":{"type":"localhash"}
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"T37", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithSecKeysNoUnique)
{
    uint32_t ret;
    char *labelName = (char *)"T37";
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)
        R"([{
            "type":"record",
            "name":"T37",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":["F0"],
                     "index":{"type":"localhash"}
                    }
                ]
            }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F2"};
    DmValueT propertyValue[3];
    propertyValue[0].type = DB_DATATYPE_INT32;
    propertyValue[0].value.intValue = 600;
    propertyValue[1].type = DB_DATATYPE_INT32;
    propertyValue[1].value.intValue = 400;
    propertyValue[2].type = DB_DATATYPE_INT32;
    propertyValue[2].value.intValue = 200;

    ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);

    const char *keyName = "T37_K0";
    DmIndexKeyT *filter = NULL;
    uint8_t *seriBuf = NULL;
    uint32_t length;
    TextT labelNameT = {.len = strlen("T37") + 1, .str = (char *)"T37"};
    DmVertexLabelT *vertexLabel2 = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelNameT.str);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel2);
    DmValueT propertyValues;
    propertyValues.type = DB_DATATYPE_INT32;
    propertyValues.value.intValue = 600;
    ret = DmCreateIndexKeyByNameWithMemCtx(
        (DbMemCtxT *)dyAlgoCtxVertex, vertexLabel2, keyName, &propertyValues, 1, &filter);
    EXPECT_EQ(GMERR_OK, ret);
    length = DmIndexKeyGetSeriBufLength(filter);
    seriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, length);
    ret = DmSerializeIndexKey2InvokerBuf(filter, length, seriBuf);
    EXPECT_EQ(GMERR_OK, ret);
    DmDestroyIndexKey(filter);
    uint32_t labelId = vertexLabel2->metaCommon.metaId;
    CataReleaseVertexLabel(vertexLabel2);
    TextT filterBuf = {.len = length, .str = (char *)seriBuf};
    ret = QryScan(req, dyAlgoCtxVertex, conn, labelId, filterBuf, 1);
    EXPECT_EQ(GMERR_OK, ret);
    DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, seriBuf);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithoutKeys)
{
    uint32_t ret;
    char *labelName = (char *)"T38";
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)
        R"([{
            "type":"record",
            "name":"T38",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"}
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F2"};
    DmValueT propertyValue[3];
    propertyValue[0].type = DB_DATATYPE_INT32;
    propertyValue[0].value.intValue = 600;
    propertyValue[1].type = DB_DATATYPE_INT32;
    propertyValue[1].value.intValue = 400;
    propertyValue[2].type = DB_DATATYPE_INT32;
    propertyValue[2].value.intValue = 200;

    for (int i = 0; i < 6; i++) {
        ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
        EXPECT_EQ(GMERR_OK, ret);
    }

    TextT labelNameText = {.len = strlen("T38") + 1, .str = (char *)"T38"};
    uint8_t *seriBuf = NULL;
    TextT filterBuf = {.len = 0, .str = (char *)seriBuf};
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelNameText.str);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t labelId = vertexLabel->metaCommon.metaId;
    ret = QryScan(req, dyAlgoCtxVertex, conn, labelId, filterBuf, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName1[2] = {(char *)"F0", (char *)"F1"};
    DmValueT propertyValue1[2];
    propertyValue1[0].type = DB_DATATYPE_INT32;
    propertyValue1[0].value.intValue = 111;
    propertyValue1[1].type = DB_DATATYPE_INT32;
    propertyValue1[1].value.intValue = 555;

    FixBufferT *rsp = NULL;
    ret = QryTestUpdateVertex(
        labelName, NULL, 0, NULL, sizeof(propertyName1) / sizeof(char *), propertyName1, propertyValue1, 0, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t affectRows;
    RpcSeekFirstOpMsg(rsp);
    EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
    EXPECT_EQ((uint32_t)6, affectRows);

    QryReleaseRsp(rsp);
    ret = QryTestDeleteVertex(labelName, NULL, 0, NULL, NULL, 0, 0, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    RpcSeekFirstOpMsg(rsp);
    EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
    EXPECT_EQ((uint32_t)6, affectRows);

    QryReleaseRsp(rsp);
    ret = QryTestDeleteVertex(labelName, NULL, 0, NULL, NULL, 0, 0, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    RpcSeekFirstOpMsg(rsp);
    EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
    EXPECT_EQ((uint32_t)0, affectRows);
    QryReleaseRsp(rsp);

    ret = QryScan(req, dyAlgoCtxVertex, conn, labelId, filterBuf, 1);
    EXPECT_EQ(GMERR_OK, ret);
    CataReleaseVertexLabel(vertexLabel);
    QryTestReleaseSession(conn);
}

void QryDDLCreateVertexLabelBatch()
{
    uint32_t ret;
    char *labelJson = (char *)R"([
        {
            "type":"record",
            "name":"vsys",
            "fields":[
                {"name":"id", "type":"int32", "nullable":false},
                {"name":"name", "type":"string","size":8, "nullable":false}
            ],
            "keys":[
                {
                    "node":"vsys",
                    "name":"id",
                    "fields":["id"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"vsys",
                    "name":"name",
                    "fields":["name"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":true}
                }
            ]
        },

        {
            "type":"record",
            "name":"vsys_rule",
            "fields":[
                {"name":"vsys_id", "type":"int32", "nullable":false},
                {"name":"id", "type":"int32", "nullable":false},
                {"name":"name", "type":"string","size":8, "nullable":false}
            ],
            "keys":[
                {
                    "node":"vsys_rule",
                    "name":"vsys_id_id",
                    "fields":["vsys_id","id"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        },
        {
            "type":"record",
            "name":"vsys_rule_source",
            "fields":[
                {"name":"rule_vsys_id", "type":"int32", "nullable":false},
                {"name":"rule_id", "type":"int32", "nullable":false},
                {"name":"ipLen", "type":"int32", "nullable":false},
                {"name":"maskLen", "type":"int32", "nullable":false}
            ],
            "keys":[
                {
                    "node":"vsys_rule_source",
                    "name":"vsys_rule_source_K0",
                    "fields":["rule_vsys_id","rule_id","ipLen","maskLen"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }
    ])";
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLReleaseVertexLabelBatch(DbMemCtxT *memCtx)
{
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    uint32_t nspId = 0;
    EXPECT_EQ(GMERR_OK, CataGetNamespaceIdByName(NULL, "public", &nspId));
    char *nspName = NULL;
    EXPECT_EQ(GMERR_OK, CataGetNamespaceNameById(memCtx, &nspName, nspId));
    EXPECT_STREQ("public", nspName);
    DbDynMemCtxFree(memCtx, nspName);
    char *labelName = (char *)"vsys";
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, nspId, labelName);
    uint32_t ret = CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(vertexLabel));
    labelName = (char *)"vsys_rule";
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, nspId, labelName);
    ret = CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(vertexLabel));
    labelName = (char *)"vsys_rule_source";
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, nspId, labelName);
    ret = CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(vertexLabel));
}

void QryDDLDropVertexLabelBatch()
{
    char *labelName1 = (char *)"vsys";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName1, true, NULL));
    char *labelName2 = (char *)"vsys_rule";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName2, true, NULL));
    char *labelName3 = (char *)"vsys_rule_source";
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(labelName3, true, NULL));
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelBatch)
{
    QryDDLCreateVertexLabelBatch();
    QryDDLReleaseVertexLabelBatch(dyAlgoCtxVertex);
    QryDDLDropVertexLabelBatch();
}

void QryDDLCreateEdgeLabelBatch()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
        {
            "name":"vsys_rule1",
            "source_vertex_label":"vsys",
            "dest_vertex_label":"vsys_rule",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {
                        "source_property": "id",
                        "dest_property": "vsys_id"
                    }
                ]
            }
        },

        {
            "name":"vsys_rule2",
            "source_vertex_label":"vsys_rule",
            "dest_vertex_label":"vsys_rule_source",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {
                        "source_property": "vsys_id",
                        "dest_property": "rule_vsys_id"
                    },
                    {
                        "source_property": "id",
                        "dest_property": "rule_id"
                    }
                ]
            }
        }
    ])";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLDropEdgeLabelBatch()
{
    char *labelEdgeName1 = (char *)"vsys_rule1";
    EXPECT_EQ(GMERR_OK, QryTestDropEdgeLabel(labelEdgeName1, NULL));
    char *labelEdgeName2 = (char *)"vsys_rule2";
    EXPECT_EQ(GMERR_OK, QryTestDropEdgeLabel(labelEdgeName2, NULL));
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateEdgeLabelBatch)
{
    QryDDLCreateVertexLabelBatch();
    QryDDLCreateEdgeLabelBatch();
    QryDDLDropEdgeLabelBatch();
    QryDDLReleaseVertexLabelBatch(dyAlgoCtxVertex);
    QryDDLDropVertexLabelBatch();
}

TEST_F(UtQueryDDLLabel, QRY_DDL_InsertVertex)
{
    uint32_t ret;
    QryDDLCreateVertexLabelBatch();
    char *labelName1 = (char *)"vsys";
    char *propertyName1[2] = {(char *)"id", (char *)"name"};
    DmValueT propertyValue1[2];
    propertyValue1[0].type = DB_DATATYPE_INT32;
    propertyValue1[0].value.intValue = 400;
    propertyValue1[1].type = DB_DATATYPE_STRING;
    propertyValue1[1].value.strAddr = (void *)"zhang";
    propertyValue1[1].value.length = 6;

    ret = QryTestInsertVertex(labelName1, sizeof(propertyName1) / sizeof(char *), propertyName1, propertyValue1);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName2 = (char *)"vsys_rule";
    char *propertyName2[3] = {(char *)"vsys_id", (char *)"id", (char *)"name"};
    DmValueT propertyValue2[3];
    propertyValue2[0].type = DB_DATATYPE_INT32;
    propertyValue2[0].value.intValue = 400;
    propertyValue2[1].type = DB_DATATYPE_INT32;
    propertyValue2[1].value.intValue = 400;
    propertyValue2[2].type = DB_DATATYPE_STRING;
    propertyValue2[2].value.strAddr = (void *)"zhang";
    propertyValue2[2].value.length = 6;

    ret = QryTestInsertVertex(labelName2, sizeof(propertyName2) / sizeof(char *), propertyName2, propertyValue2);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName3 = (char *)"vsys_rule_source";
    char *propertyName3[4] = {(char *)"rule_vsys_id", (char *)"rule_id", (char *)"ipLen", (char *)"maskLen"};
    DmValueT propertyValue3[4];
    propertyValue3[0].type = DB_DATATYPE_INT32;
    propertyValue3[0].value.intValue = 400;
    propertyValue3[1].type = DB_DATATYPE_INT32;
    propertyValue3[1].value.intValue = 400;
    propertyValue3[2].type = DB_DATATYPE_INT32;
    propertyValue3[2].value.intValue = 300;
    propertyValue3[3].type = DB_DATATYPE_INT32;
    propertyValue3[3].value.intValue = 300;

    ret = QryTestInsertVertex(labelName3, sizeof(propertyName3) / sizeof(char *), propertyName3, propertyValue3);
    EXPECT_EQ(GMERR_OK, ret);
    QryDDLReleaseVertexLabelBatch(dyAlgoCtxVertex);
    QryDDLDropVertexLabelBatch();
}

void CreateVertexLabelBatch()
{
    uint32_t ret;
    char *batchLabelJson = (char *)R"([
        {
            "type":"record",
            "name":"vertexLabel1",
            "fields":[
                {"name":"F0", "type":"int32", "nullable":false}
            ]
        },

        {
            "type":"record",
            "name":"vertexLabel2",
            "fields":[
                {"name":"F0", "type":"int32", "nullable":false}
            ]
        },

        {
            "type":"record",
            "name":"vertexLabel3",
            "fields":[
                {"name":"F0", "type":"int32", "nullable":false}
            ]
        }
    ])";
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    ret = QryTestCreateVertexLabel(cfgJson, batchLabelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void CreateEdgeLabelBatch()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
        {
            "name":"vertexLabel1_vertexLabel2",
            "source_vertex_label":"vertexLabel1",
            "dest_vertex_label":"vertexLabel2",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {
                        "source_property": "F0",
                        "dest_property": "F0"
                    }
                ]
            }
        },

        {
            "name":"vertexLabel2_vertexLabel3",
            "source_vertex_label":"vertexLabel2",
            "dest_vertex_label":"vertexLabel3",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {
                        "source_property": "F0",
                        "dest_property": "F0"
                    }
                ]
            }
        },

        {
            "name":"vertexLabel1_vertexLabel2_same",
            "source_vertex_label":"vertexLabel1",
            "dest_vertex_label":"vertexLabel2",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {
                        "source_property": "F0",
                        "dest_property": "F0"
                    }
                ]
            }
        }
    ])";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_DropAssocVertexLabelWithSameEdgeLabel)
{
    CreateVertexLabelBatch();
    CreateEdgeLabelBatch();

    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"vertexLabel1";
    uint32_t labelNameLen = strlen(labelName) + 1;

    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(&req, 1);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithSameName)
{
    uint32_t ret;
    char *labelJson = (char *)R"([
        {
            "type":"record",
            "name":"vsys1",
            "fields":[
                {"name":"id", "type":"int32", "nullable":false},
                {"name":"name", "type":"string","size":8, "nullable":false}
            ],
            "keys":[
                {
                    "node":"vsys1",
                    "name":"id",
                    "fields":["id"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        },

        {
            "type":"record",
            "name":"vsys1",
            "fields":[
                {"name":"vsys_id", "type":"int32", "nullable":false},
                {"name":"id", "type":"int32", "nullable":false},
                {"name":"name", "type":"string","size":8, "nullable":false}
            ],
            "keys":[
                {
                    "node":"vsys1",
                    "name":"vsys_id_id",
                    "fields":["vsys_id","id"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }
    ])";
    ret = QryTestCreateVertexLabel(NULL, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. VertexLabel:vsys1 is repeated.";
    EXPECT_STREQ(result, lastError1->str);
}

void QryDDLCreateVertexLabelTreeModel()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        },
        { "name": "c3", "type": "string", "size":20, "nullable":true },
        { "name": "c4", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "float" },
            { "name": "b2", "type": "uint8" }
          ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
          "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true},
            { "name": "t2", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "h1", "type": "uint16" },
                { "name": "h2", "type": "uint32"}
              ]
            },
            { "name": "t3", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "sys",
          "fields": [ "c0" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_InsertDeleteDropVertexTreeModel)
{
    QryDDLCreateVertexLabelTreeModel();
    char *labelName1 = (char *)"sys";
    uint32_t ret;
    char *propertyName[7] = {(char *)"c0", (char *)"c1.f1", (char *)"c4.b1", (char *)"c4.b2", (char *)"c6.t2.h1",
        (char *)"c6.t2.h2", (char *)"c6.t3"};
    DmValueT propertyValue[7];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'a';
    propertyValue[2].type = DB_DATATYPE_FLOAT;
    propertyValue[2].value.floatValue = 3.00;
    propertyValue[3].type = DB_DATATYPE_UINT8;
    propertyValue[3].value.uintValue = 3;
    propertyValue[4].type = DB_DATATYPE_UINT16;
    propertyValue[4].value.uintValue = 13;
    propertyValue[5].type = DB_DATATYPE_UINT32;
    propertyValue[5].value.uintValue = 23;
    propertyValue[6].type = DB_DATATYPE_UINT32;
    propertyValue[6].value.uintValue = 23;

    ret = QryTestInsertVertex(labelName1, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName5 = (char *)"sys";
    const char *indexName = "table_pk";
    DmValueT indexPropertyValue[1];
    indexPropertyValue[0].type = DB_DATATYPE_UINT32;
    indexPropertyValue[0].value.uintValue = 300;
    ret = QryTestDeleteVertex(labelName5, indexName, 1, indexPropertyValue, NULL, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);

    TextT labelName = {.len = strlen("sys") + 1, .str = (char *)"sys"};

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, DM_SCHEMA_INVALID_VERSION));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelErr)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f1.a", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_TruncateVertexLabel)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"truncateVertexLabel",
        "id":1237,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"},
            {"name":"F4", "type":"time", "nullable":true},
            {"name":"F5", "type":"string", "size":20, "nullable":true},
            {"name":"F6", "type":"fixed", "default":"fff", "size":3},
            {"name":"F77", "type":"uint32"}
        ],
        "keys":
        [
            {"node":"truncateVertexLabel", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"truncateVertexLabel", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"truncateVertexLabel", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    // create label
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert data1
    char *labelName = (char *)"truncateVertexLabel";
    char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
    DmValueT propertyValue[3];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 200;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'b';
    propertyValue[2].type = DB_DATATYPE_FLOAT;
    propertyValue[2].value.floatValue = 2.00;
    ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);
    // insert data2
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'c';
    propertyValue[2].type = DB_DATATYPE_FLOAT;
    propertyValue[2].value.floatValue = 3.00;
    ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate label
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    TextT labelNameText = {.len = strlen("truncateVertexLabel") + 1, .str = (char *)"truncateVertexLabel"};

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelNameText));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_TRUNCATE_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelWithMemberKeyErrRecord)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "float" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "c1",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Inv table definition. node c1 type is record, expected array or vector.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelWithMemberKeyErrPath)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "float" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "T0",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. Node name is illegal, name: T0.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelWithMemberKeyErrPath2)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "float" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "c3/f1/f2",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal name. Node name is illegal, name: f2.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelWithMemberKeyErrSameName)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "float" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "c3/f1",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        },
        { "name": "k0", "index": { "type": "none" },
          "node": "c3/f1",
          "fields": [ "f1" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Duplicate object. index: k0 is repeated in node f1.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, CreateVertexLabelTreeModelWithMemberKeyErrInvalidPropNum)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "int64" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"},
                { "name": "f2", "type": "uint16" },
                { "name": "f3", "type": "uint32"},
                { "name": "f4", "type": "uint16" },
                { "name": "f5", "type": "uint32"},
                { "name": "f6", "type": "uint16" },
                { "name": "f7", "type": "uint32"},
                { "name": "f8", "type": "uint32"},
                {"name":"F9", "type":"int32"},
                {"name":"F10", "type":"int32"},
                {"name":"F11", "type":"int32"},
                {"name":"F12", "type":"int32"},
                {"name":"F13", "type":"int32"},
                {"name":"F14", "type":"int32"},
                {"name":"F15", "type":"int32"},
                {"name":"F16", "type":"int32"},
                {"name":"F17", "type":"int32"},
                {"name":"F18", "type":"int32"},
                {"name":"F19", "type":"int32"},
                {"name":"F20", "type":"int32"},
                {"name":"F21", "type":"int32"},
                {"name":"F22", "type":"int32"},
                {"name":"F23", "type":"int32"},
                {"name":"F24", "type":"int32"},
                {"name":"F25", "type":"int32"},
                {"name":"F26", "type":"int32"},
                {"name":"F27", "type":"int32"},
                {"name":"F28", "type":"int32"},
                {"name":"F29", "type":"int32"},
                {"name":"F30", "type":"int32"},
                {"name":"F31", "type":"int32"},
                {"name":"F32", "type":"int32"},
                {"name":"F33", "type":"int32"},
                {"name":"F34", "type":"int32"},
                {"name":"F35", "type":"int32"},
                {"name":"F36", "type":"int32"},
                {"name":"F37", "type":"int32"},
                {"name":"F38", "type":"int32"},
                {"name":"F39", "type":"int32"},
                {"name":"F40", "type":"int32"},
                {"name":"F41", "type":"int32"},
                {"name":"F42", "type":"int32"},
                {"name":"F43", "type":"int32"},
                {"name":"F44", "type":"int32"},
                {"name":"F45", "type":"int32"},
                {"name":"F46", "type":"int32"},
                {"name":"F47", "type":"int32"},
                {"name":"F48", "type":"int32"},
                {"name":"F49", "type":"int32"},
                {"name":"F50", "type":"int32"},
                {"name":"F51", "type":"int32"},
                {"name":"F52", "type":"int32"},
                {"name":"F53", "type":"int32"},
                {"name":"F54", "type":"int32"},
                {"name":"F55", "type":"int32"},
                {"name":"F56", "type":"int32"},
                {"name":"F57", "type":"int32"},
                {"name":"F58", "type":"int32"},
                {"name":"F59", "type":"int32"},
                {"name":"F60", "type":"int32"},
                {"name":"F61", "type":"int32"},
                {"name":"F62", "type":"int32"},
                {"name":"F63", "type":"int32"},
                {"name":"F64", "type":"int32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "c2",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        },
        { "name": "k1",
          "node": "c3/f1",
          "fields":[
                 "F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7",
                 "F8", "F9", "F10", "F11", "F12", "F13", "F14", "F15",
                 "F16", "F17", "F18", "F19", "F20", "F21", "F22", "F23",
                 "F24", "F25", "F26", "F27", "F28", "F29", "F30", "F31", "F32"],
          "constraints": { "unique": false }
        },
        { "name": "k2",  "index": { "type": "primary" },
          "node": "T0",
          "fields": [ "c0"],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Array subscript unsucc. array fields size should between: 1 and 32.The vertexLabel "
                                 "is T0.The indexLabel is k1.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelWithMemberKey)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    // 持久化不支持member key
#ifdef FEATURE_PERSISTENCE
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "int64" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k2",  "index": { "type": "primary" },
          "node": "T0",
          "fields": [ "c0"],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
#else
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f0", "type": "char"}
          ]
        },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type": "int64" },
            { "name": "f1", "type": "uint32"}
          ]
        },
        { "name": "c3", "type": "record",
          "fields": [
            { "name": "f0", "type": "fixed", "size": 6, "nullable":true},
            { "name": "f1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "f0", "type": "uint16" },
                { "name": "f1", "type": "uint32"}
              ]
            },
            { "name": "f2", "type": "uint32"}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "c2",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        },
        { "name": "k1",
          "node": "c3/f1",
          "fields": [ "f0", "f1" ],
          "constraints": { "unique": false }
        },
        { "name": "k2",  "index": { "type": "primary" },
          "node": "T0",
          "fields": [ "c0"],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
#endif
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelNest)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    // 持久化不支持member key
#ifdef FEATURE_PERSISTENCE
    char *labelJson = (char *)R"([{
          "version": "2.0", "type": "record", "name": "treeModel",
          "fields": [
            { "name": "c0", "type": "uint32" },
            { "name": "c1", "type": "string", "size":20, "nullable":true },
            { "name": "c2", "type": "record",
              "fields": [
                { "name": "b1", "type": "record",
                  "fixed_array": true, "size": 512,
                  "fields": [
                    { "name": "t1", "type": "uint32"},
                    { "name": "t2", "type": "record",
                      "fields": [
                        { "name": "h1", "type": "uint32"},
                        { "name": "h2", "type": "record",
                          "fixed_array": true, "size": 512,
                          "fields": [
                           { "name": "r1", "type": "uint32"},
                           { "name": "r2", "type": "record",
                             "fixed_array": true, "size": 512,
                             "fields": [
                               { "name": "y1", "type": "uint32"},
                               { "name": "y2", "type": "uint32"}
                             ]
                           }
                         ]
                        }
                      ]
                    }
                  ]
                },
                { "name": "b2", "type": "record",
                  "vector": true, "size": 512,
                  "fields": [
                    { "name": "t1", "type": "uint32"},
                    { "name": "t2", "type": "uint32"}
                  ]
                }
              ]
            },
            { "name": "c3", "type": "bytes", "size": 128, "nullable":true }
          ],
          "keys": [
            { "name": "table_pk", "index": { "type": "primary" },
              "node": "treeModel",
              "fields": [ "c0" ],
              "constraints": { "unique": true }
            }
          ]
        }])";
#else
    char *labelJson = (char *)R"([{
      "version": "2.0", "type": "record", "name": "treeModel",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "string", "size":20, "nullable":true },
        { "name": "c2", "type": "record",
          "fields": [
            { "name": "b1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "t1", "type": "uint32"},
                { "name": "t2", "type": "record",
                  "fields": [
                    { "name": "h1", "type": "uint32"},
                    { "name": "h2", "type": "record",
                      "fixed_array": true, "size": 512,
                      "fields": [
                       { "name": "r1", "type": "uint32"},
                       { "name": "r2", "type": "record",
                         "fixed_array": true, "size": 512,
                         "fields": [
                           { "name": "y1", "type": "uint32"},
                           { "name": "y2", "type": "uint32"}
                         ]
                       }
                     ]
                    }
                  ]
                }
              ]
            },
            { "name": "b2", "type": "record",
              "vector": true, "size": 512,
              "fields": [
                { "name": "t1", "type": "uint32"},
                { "name": "t2", "type": "uint32"}
              ]
            }
          ]
        },
        { "name": "c3", "type": "bytes", "size": 128, "nullable":true }
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "treeModel",
          "fields": [ "c0" ],
          "constraints": { "unique": true }
        },
        { "name": "k0", "index": { "type": "none" },
          "node": "c2/b1/t2/h2",
          "fields": [ "r1" ],
          "constraints": { "unique": true }
        }
      ]
    }])";
#endif
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelNestErr)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
      "version": "2.0", "type": "record", "name": "treeModel",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "string", "size":20, "nullable":true },
        { "name": "c2", "type": "record",
          "fields": [
            { "name": "b1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "t1", "type": "uint32"},
                { "name": "t2", "type": "record",
                  "vector": true, "size": 1024,
                  "fields": [
                    { "name": "f1", "type": "uint32"},
                    { "name": "f2", "type": "uint32"}
                  ]
                }
              ]
            },
            { "name": "b2", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "t1", "type": "uint32"},
                { "name": "t2", "type": "record",
                  "vector": true, "size": 1024,
                  "fields": [
                    { "name": "f1", "type": "uint32"},
                    { "name": "f2", "type": "uint32"}
                  ]
                }
              ]
            }
          ]
        },
        { "name": "c3", "type": "bytes", "size": 128, "nullable":true }
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "treeModel",
          "fields": [ "c0" ],
          "constraints": { "unique": true }
        }
      ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Program limit exceeded. the parent node is an array and the child node is a vector, or the parent "
                 "node is a vector and the child node is an array.vertexLabel name:treeModel.",
        lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelTreeModelNestWithoutMemberKey)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
      "version": "2.0", "type": "record", "name": "treeModelNest",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "string", "size":20, "nullable":true },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "uint32" },
            { "name": "b2", "type": "record",
              "vector": true, "size": 512,
              "fields": [
                { "name": "t1", "type": "uint32"},
                { "name": "t2", "type": "string","size":8, "nullable":false}
              ]
            }
          ]
        },
        { "name": "c3", "type": "bytes", "size": 128, "nullable":true }
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "treeModelNest",
          "fields": [ "c0" ],
          "constraints": { "unique": true }
        }
      ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithLPMIndex)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
            "type":"record",
            "name":"LPM001",
            "fields":
                [
                    {"name":"vr_id", "type":"uint32", "size":300},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}
                ],
            "keys":
                [
                    {
                        "node":"LPM001",
                        "name":"ip4forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithLPMIndexError)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
            "type":"record",
            "name":"LPM001",
            "fields":
                [
                    {"name":"vr_id", "type":"uint32", "size":300},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}
                ],
            "keys":
                [
                    {
                        "node":"LPM001",
                        "name":"ip4forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char
            *)"Array subscript unsucc. array fields size should be 4. vertexLabel: LPM001.indexLabel: ip4forward_lpm.";
    EXPECT_STREQ(result, lastError1->str);
}

int32_t QRY_DDL_CreateVertexLabelWithMULILPMIndexError(const DrtConnectionT *conn, FixBufferT *msg)
{
    SessionT *session = (SessionT *)conn->session;

    FixBufferT *rsp = QrySessionGetRsp(session);
    FixBufSeek(rsp, 0);
    MsgHeaderT *header = (MsgHeaderT *)FixBufGetData(rsp, 0);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, header->opStatus);
    TextT *lastError1 = DbGetLastErrorInfo();
    EXPECT_STREQ("", lastError1->str);
    printf("%s\n", lastError1->str);
    return GMERR_OK;
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithMULILPMIndexError)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
            "type":"record",
            "name":"LPM001",
            "fields":
                [
                    {"name":"vr_id", "type":"uint32", "size":300},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"vr_id2", "type":"uint32", "size":300},
                    {"name":"vrf_index2", "type":"uint32"},
                    {"name":"dest_ip_addr2", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}
                ],
            "keys":
                [
                    {
                        "node":"LPM001",
                        "name":"ip4forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"LPM001",
                        "name":"ip4forward_lpm2",
                        "fields":["vr_id2", "vrf_index2", "dest_ip_addr2", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Duplicate object. The label LPM001 has multiple LPM index.vertexLabel name:LPM001.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithErrName)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"3Sv_123",
        "id":1111,
        "fields":[
            {"name":"F0", "type":"uint32"}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Not normal name. inv object name:3Sv_123, the first character is:3.vertexLabel name:3Sv_123.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithProNameMaxLen)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"(
        [{
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
                        "type":"int32"
                    }
                ]
             }
            ]
    )";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. name len exceeds limit 128, which is "
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithNodeNameMaxLen)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. name len exceeds limit 128, which is "
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, CreateVertexLabelWithBitFieldError)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        {"name": "c0", "type": "uint32" },
        {"name":"F15", "type":"uint8: 2",   "nullable":false, "default":4}
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property F15 value size exceeds limit 3.";
    EXPECT_STREQ(result, lastError1->str);

    char *labelJson2 = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        {"name": "c0", "type": "uint32" },
        {"name":"F15", "type":"uint16: 2",   "nullable":false, "default":4}
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson2, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    lastError1 = DbGetLastErrorInfo();
    result = (char *)"Not normal property. property F15 value size exceeds limit 3.";
    EXPECT_STREQ(result, lastError1->str);

    char *labelJson3 = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        {"name": "c0", "type": "uint32" },
        {"name":"F15", "type":"uint32: 2",   "nullable":false, "default":4}
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson3, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    lastError1 = DbGetLastErrorInfo();
    result = (char *)"Not normal property. property F15 value size exceeds limit 3.";
    EXPECT_STREQ(result, lastError1->str);

    char *labelJson4 = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "sys",
      "fields": [
        {"name": "c0", "type": "uint32" },
        {"name":"F15", "type":"uint64: 2",   "nullable":false, "default":4}
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson4, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    lastError1 = DbGetLastErrorInfo();
    result = (char *)"Not normal property. property F15 value size exceeds limit 3.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithNodeCountLimitMax)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"SecondNode", "type":"record", "fields": [
                    {"name":"ThirdNode", "type":"record", "fields": [
                        {"name":"H0", "type":"int32", "nullable":false}
                    ]}
                ]},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 154922 字节
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);

    // 组装指定节点数量的schema
    int offsetLen = 0;
    offsetLen += sprintf(schemaJson + offsetLen, "%s", schemaJsonHead);
    // 已经预置了3个节点 因此再组装1022个节点即可
    int32_t nodeCnt = 1022;
    for (int32_t i = 0; i < nodeCnt - 1; i++) {
        offsetLen +=
            sprintf(schemaJson + offsetLen, "\n%*s{\"name\":\"N%d\", \"type\":\"record\", \"fields\": [", 16, " ", i);
        offsetLen += sprintf(
            schemaJson + offsetLen, "\n%*s{\"name\":\"G%d\", \"type\":\"int32\", \"nullable\":false}", 20, " ", i);
        offsetLen += sprintf(schemaJson + offsetLen, "\n%*s]},", 16, " ");
    }
    offsetLen += sprintf(
        schemaJson + offsetLen, "\n%*s{\"name\":\"N%d\", \"type\":\"record\", \"fields\": [", 16, " ", nodeCnt - 1);
    offsetLen += sprintf(schemaJson + offsetLen, "\n%*s{\"name\":\"G%d\", \"type\":\"int32\", \"nullable\":false}", 20,
        " ", nodeCnt - 1);
    offsetLen += sprintf(schemaJson + offsetLen, "\n%*s]}", 16, " ");
    offsetLen += sprintf(schemaJson + offsetLen, schemaJsonTail);

    ret = QryTestCreateVertexLabel(cfgJson, schemaJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    char *lastErr = (char
            *)"Program limit exceeded. The nodes count (1025) should between: 1 and 1024.vertexLabel name:VertexLabel.";
    TextT *rspLastErr = DbGetLastErrorInfo();
    printf("rspLastErr is [%s]\n", rspLastErr->str);
    EXPECT_EQ(0, strcmp(rspLastErr->str, lastErr));

    free(schemaJson);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithInvalidLabeName)
{
    // 首字母是数字
    uint32_t ret;
    char *cfgJson = (char *)R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    char *labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "1sys",
      "fields": [
        { "name": "c0", "type": "uint32" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    // 首字母是特殊字符
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "_sys",
      "fields": [
        { "name": "c0", "type": "uint32" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    // 首字母是特殊字符
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": ".sys",
      "fields": [
        { "name": "c0", "type": "uint32" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "''_-.:10aZ>?sys",
      "fields": [
        { "name": "c0", "type": "uint32" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "'\"'_-.:09azAZ",
      "fields": [
        { "name": "c0", "type": "uint32" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 索引name最大长度 128*33
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",)";

    int32_t schemaJsonMiddleLen = 5000;
    char *schemaJsonMiddle = (char *)malloc(schemaJsonMiddleLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJsonMiddle);
    memset(schemaJsonMiddle, '\0', schemaJsonMiddleLen);

    int32_t indexNameLen = 33 * 129 + 1;
    char *indexName = (char *)malloc(indexNameLen * sizeof(char));
    EXPECT_NE((char *)NULL, indexName);
    memset(indexName, 'a', indexNameLen - 1);
    indexName[4257] = '\0';
    sprintf(schemaJsonMiddle, "\n%*s\"name\":\"%s\",", 20, " ", indexName);

    const char *schemaJsonTail =
        R"(
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int32_t jsonLen = 1024 * 70;
    labelJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, labelJson);
    memset(labelJson, '\0', jsonLen);

    strcat(labelJson, schemaJsonHead);
    strcat(labelJson, schemaJsonMiddle);
    strcat(labelJson, schemaJsonTail);

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    indexName[4256] = '\0';
    sprintf(schemaJsonMiddle, "\n%*s\"name\":\"%s\",", 20, " ", indexName);
    memset(labelJson, '\0', jsonLen);

    strcat(labelJson, schemaJsonHead);
    strcat(labelJson, schemaJsonMiddle);
    strcat(labelJson, schemaJsonTail);

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    free(indexName);
    free(schemaJsonMiddle);

    // 索引名不合法
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "aA_001",
      "fields": [
        { "name": "c0", "type": "uint32" }
        ],
        "keys":
        [
            {"node":"aA_001", "name":"<>?@!%", "fields":["c0"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "aA_001",
      "fields": [
        { "name": "azAZ09.:-_'\"'", "type": "uint32" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    labelJson = (char *)R"([{
        "name":"$1ew",
        "source_vertex_label":"'\"'_-.:09azAZ",
        "comment":"testInvalidName",
        "dest_vertex_label":"aA_001",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"c0","dest_property":"azAZ09.:-_'\"'"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJson);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    labelJson = (char *)R"([{
        "name":"a..",
        "source_vertex_label":"'\"'_-.:09azAZ",
        "comment":"testInvalidName",
        "dest_vertex_label":"aA_001",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"c0","dest_property":"azAZ09.:-_'\"'"}
            ]
        }
    }])";
    ret = QryTestCreateEdgeLabel(NULL, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithAppointLabelName)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexAppointedNameOld",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexAppointedNameOld", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabelWithAppointLabelName(cfgJson, labelJson, (char *)"labelvertexAppointedNameNew", NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestDropVertexLabel((char *)"labelvertexAppointedNameOld", 1, NULL);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = QryTestDropVertexLabel((char *)"labelvertexAppointedNameNew", 1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelJson2 = (char *)R"([{
        "type":"record",
        "name":"labelvertexAppointedNameOld2",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexAppointedNameOld2", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    },
    {
        "type":"record",
        "name":"labelvertexAppointedNameOld3",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexAppointedNameOld3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret =
        QryTestCreateVertexLabelWithAppointLabelName(cfgJson, labelJson2, (char *)"labelvertexAppointedNameNew2", NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Incorrect json content. Vertex label array num should be 1 when appointed the labelName.", lastError->str);
}

#ifndef FEATURE_PERSISTENCE
// 不支持resource
TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithResourcePrimaryKey)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex",
        "fields":[
            {"name":"F0", "type":"resource"},
            {"name":"F1", "type":"char"}
        ],
        "keys":[
            {"node":"labelvertex", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithVariabelDataType)
{
    // 类型fixed，不带size 报错
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType",
      "fields": [
        { "name": "f1", "type": "fixed" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 类型bitmap，不带size 报错
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType",
      "fields": [
        { "name": "f1", "type": "bitmap" }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 类型bitmap，size超过32k 报错
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType",
      "fields": [
        { "name": "f1", "type": "bitmap", "size": 32769 }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 类型bitmap，size非8的倍数 报错
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType",
      "fields": [
        { "name": "f1", "type": "bitmap", "size": 7 }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 类型bitmap，size=32k 成功
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType1",
      "fields": [
        { "name": "f1", "type": "bitmap", "size": 32768 }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 类型string，size超过64k 报错
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType",
      "fields": [
        { "name": "f1", "type": "string", "size": 65537 }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 类型bytes，size超过64k 报错
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType",
      "fields": [
        { "name": "f1", "type": "bytes", "size": 65537 }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 类型string/bytes，size=64k 成功
    labelJson = (char *)R"(
    [
     {
      "version": "2.0", "type": "record", "name": "VariabelDataType2",
      "fields": [
        { "name": "f1", "type": "string", "size": 65536 },
        { "name": "f2", "type": "bytes", "size": 65536 }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SUPER_FIELD_WITH_SAME_BEGIN_AND_END)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "comment":"",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"fixed", "default":"fff", "size":3},
            {"name":"F3", "type":"int32"}
        ],
        "super_fields":[
            {"name":"superfield0", "comment":"", "fields":{"begin":"F0","end":"F0"}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithMaxMemberKeyPropertySize)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T0",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "f0", "type":"fixed", "size":555}
          ]
        }
      ],
      "keys": [
        { "name": "k0", "index": { "type": "none" },
          "node": "c1",
          "fields": [ "f0" ],
          "constraints": { "unique": true }
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    auto result =
        "Program limit exceeded. properties total size 555 of index k0 exceeds limit 532.The vertexLabel name: T0.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithCheckValidity)
{
    uint32_t ret;
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"XT60",
        "config":{"check_validity":false, "isFastReadUncommitted":false},
        "fields":[
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false}],
        "keys":[{
                "node":"XT60",
                "name":"XT60_K0",
                "fields":["F0"],
                "index":{"type":"local"},
                "constraints": { "unique": true }}
       ]
    }])";
    ret = QryTestCreateVertexLabel(NULL, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Inv table definition. The configuration item check_validity should be true, because label XT60 has "
                 "local index.vertexLabel name:XT60.",
        lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_INDEX_EXCEED_MAX_NUM)
{
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    int proNum = 64;
    int size = 10240;
    int tmpSize = 128;
    char buffer[10240] = {0};
    char tmp[128] = {0};
    char *schemaHead = (char *)R"([{
        "type":"record",
        "name":"T1",
        "fields":[)";
    strcat_s(buffer, size, schemaHead);
    for (int i = 0; i < proNum; i++) {
        sprintf_s(tmp, tmpSize, "{ \"name\": \"F%d\", \"type\": \"uint32\"},", i);
        strcat_s(buffer, size, tmp);
    }
    sprintf_s(tmp, tmpSize, "{\"name\": \"F%d\", \"type\": \"uint32\"}],\"keys\":[", proNum);
    strcat_s(buffer, size, tmp);
    for (int i = 0; i < proNum; i++) {
        sprintf_s(tmp, tmpSize,
            "{\"node\":\"T1\", \"name\":\"T1_local%d\", \"fields\":[\"F%d\"], \"index\": {\"type\":\"local\"}, "
            "\"constraints\": {\"unique\":true}},",
            i, i);
        strcat_s(buffer, size, tmp);
    }
    sprintf_s(tmp, tmpSize,
        "{\"node\":\"T1\", \"name\":\"T1_local%d\", \"fields\":[\"F%d\"], \"index\": {\"type\":\"local\"}, "
        "\"constraints\": {\"unique\":true}}]}]",
        proNum, proNum);
    strcat_s(buffer, size, tmp);
    uint32_t ret = QryTestCreateVertexLabel(cfgJson, buffer, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char *)"Array subscript unsucc. array keys size should between: 1 and 64.";
    EXPECT_STREQ(result, lastError->str);
}
// 最外层record名和其它节点名不可以重复，最外层record名不一定是表名
TEST_F(UtQueryDDLLabel, QRY_DDL_DUPLICATE_NODENAME_TreeModel1)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "t1",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "t1", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Duplicate column. Duplicate node name : t1.vertexLabel name:t1.";
    EXPECT_STREQ(result, lastError1->str);
}
// 同层的nodeName同名
TEST_F(UtQueryDDLLabel, QRY_DDL_DUPLICATE_NODENAME_TreeModel2)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "t1",
      "fields": [
        { "name": "f0", "type": "uint32" },
        { "name": "c0", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        },
        { "name": "c0", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "float" },
            { "name": "b2", "type": "uint8" }
          ]
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Duplicate column. Duplicate node name : c0.vertexLabel name:t1.";
    EXPECT_STREQ(result, lastError1->str);
}
// 不同层的nodeName和nodeName同名
TEST_F(UtQueryDDLLabel, QRY_DDL_DUPLICATE_NODENAME_TreeModel3)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "T39",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "f2", "type": "uint32" }
          ]
        },
        { "name": "c3", "type": "string", "size":20, "nullable":true },
        { "name": "c4", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "float" },
            { "name": "b2", "type": "uint8" }
          ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
          "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true},
            { "name": "c1", "type": "record",
              "fixed_array": true, "size": 512,
              "fields": [
                { "name": "h1", "type": "uint16" },
                { "name": "h2", "type": "uint32"}
              ]
            },
            { "name": "t3", "type": "uint32"}
          ]
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Duplicate column. Duplicate node name : c1.vertexLabel name:T39.";
    EXPECT_STREQ(result, lastError1->str);
}
// 不同层的nodeName和propertyName相同
TEST_F(UtQueryDDLLabel, QRY_DDL_DUPLICATE_NODENAME_TreeModel4)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "t1",
      "fields": [
        { "name": "c1", "type": "uint32" },
        { "name": "c1", "type": "record",
          "fields": [
            { "name": "f1", "type": "char"},
            { "name": "c1", "type": "uint32" }
          ]
        }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"t1", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// bitmap没有写size
TEST_F(UtQueryDDLLabel, QRY_DDL_BITMAP_WITHOUT_SIZE)
{
    uint32_t ret;
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "t1",
      "fields": [
        { "name": "c1", "type": "uint32" },
        { "name": "c1", "type": "bitmap" }
      ]
    }])";
    ret = QryTestCreateVertexLabel(NULL, labelJson, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Undefine column. key:size\n parse size of property c1.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithValdId)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界，即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"labelvertexErrId", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithValdId1)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"testtableid0",
        "id":0,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"}
        ],
        "keys":
        [
            {"node":"testtableid0", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"testtableid0", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"testtableid0", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"testtableid0", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithInValdId1)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"testtableid1",
        "id":-1,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"float"}
        ],
        "keys":
        [
            {"node":"testtableid1", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"testtableid1", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"testtableid1", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
}

// 字段的类型写的不对，超出范围
TEST_F(UtQueryDDLLabel, QRY_DDL_WRONG_PROPERTY_TYPE)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([
    {
      "version": "2.0", "type": "record", "name": "t1",
      "fields": [
        { "name":"F0", "type":"auto_increment_type","auto_increment":true,"nullable":false },
        { "name":"F1", "type":"uint32","nullable":false }
      ]
    }
    ])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. type auto_increment_type size in property F0 exceed limit: 10.";
    EXPECT_STREQ(result, lastError1->str);
}

static inline uint32_t ExtractLabelType(uint32_t type)
{
    return type & 0xFF;
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetVertexLabelById)
{
    char *cfgJson = (char *)"{\"isFastReadUncommitted\":false}";
    uint32_t ret = QryTestCreateBaseVertexLabelFirst(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"labelvertex1";
    uint32_t labelNameLen = strlen(labelName) + 1;

    uint32_t labelId = 0;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    ret = FixBufPutUint32(&req, labelId);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0u));
#endif

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    FixBufferT *rsp = NULL;
    ret = QryTestServiceProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
    EXPECT_EQ(GMERR_OK, ret);

    RpcSeekFirstOpMsg(rsp);
    uint32_t labelType;
    TextT rspLabel;
    FixBufGetUint32(rsp, &labelType);
    FixBufGetObject(rsp, &rspLabel);
    labelType = ExtractLabelType(labelType);
    EXPECT_EQ(labelType, (uint32_t)VERTEX_LABEL);

    size_t bufferLen = sizeof(ShmemPtrT) + sizeof(ShmemPtrT) + sizeof(uint32_t);
    EXPECT_EQ((uint32_t)bufferLen, rspLabel.len);

    QryReleaseRsp(rsp);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetSysviewLabelByName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"V$DRT_CONN_STAT";
    uint32_t labelNameLen = strlen(labelName) + 1;

    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    ret = FixBufPutUint32(&req, 0);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0u));
#endif

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    FixBufferT *rsp = NULL;
    ret = QryTestServiceProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetSysviewLabelByID)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t labelId = 5048662;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    ret = FixBufPutUint32(&req, labelId);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"test";
    putText.str = labelName;
    putText.len = strlen(labelName) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0u));
#endif

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    FixBufferT *rsp = NULL;
    ret = QryTestServiceProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    QryReleaseRsp(rsp);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SIMPLE_LABEL_WITH_SPECIAL_COMPLEX)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
    "type":"record",
    "name":"T0",
    "special_complex": true,
    "fields":[
        {"name":"F0", "type":"int64", "nullable":true},
        {"name":"F1", "type":"uint64", "nullable":true}
    ]
  }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. T0 is simple label, can not define as special complex.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SPECIAL_COMPLEX_LABEL_WITH_BITFIELD)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
    "type":"record",
    "name":"T0Complex",
    "special_complex": true,
    "fields":[
        {"name":"F0", "type":"int64", "nullable":true},
        {"name":"F1", "type":"uint64:4", "nullable":true},
        {"name":"F2", "type":"bitmap", "size":1024, "nullable":true},
        {"name":"F3", "type":"string", "size":1024, "nullable":true}
    ]
  }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"T0Complex", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SUPERFIELD_LABEL_WITH_BITFIELD)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
    "type":"record",
    "name":"T0Complex",
    "special_complex": true,
    "fields":[
        {"name":"F0", "type":"int64", "nullable":true},
        {"name":"F1", "type":"uint64:4", "nullable":true},
        {"name":"F2", "type":"bitmap", "size":1024, "nullable":true},
        {"name":"F3", "type":"string", "size":1024, "nullable":true}
    ],
    "super_fields":[
        {
            "name":"superfield0",
            "comment":"test",
            "fields":{"begin":"F0","end":"F1"}
        }
    ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Inv table definition. inv type of super_fields property F1.vertexLabel name:T0Complex.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_SPECIAL_COMPLEX_LABEL_WITH_UNFIXED_KEY_FIELD)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJsonFormat = (char *)R"([{
    "type":"record",
    "name":"T0",
    "special_complex": true,
    "fields":[
        {"name":"F0", "type":"int64", "nullable":true},
        {"name":"F1", "type":"uint64", "nullable":true},
        {"name":"F2", "type":"bytes", "size":100}
    ],
    "keys":[
        { "node":"T0", "name":"K0", "fields":["F0", %s],"index":{"type":"primary"},"constraints":{"unique":true}},
        { "node":"T0", "name":"K1", "fields":["F0", %s],"index":{"type":"localhash"},"constraints":{"unique":true}}
    ]
  }])";
    int32_t maxSize = 1500;
    char invalidLabelJson1[maxSize], invalidLabelJson2[maxSize];
    int32_t len = sprintf_s(invalidLabelJson1, maxSize, labelJsonFormat, "\"F2\"", "\"F1\"");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson1, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    char *result = (char *)"Inv table definition. The property F2 of index K0 should be fixed in special complex "
                           "label.vertexLabel name:T0.";
    EXPECT_STREQ(result, lastError->str);
    len = sprintf_s(invalidLabelJson2, maxSize, labelJsonFormat, "\"F1\"", "\"F2\"");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson2, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    lastError = DbGetLastErrorInfo();
    result = (char *)"Inv table definition. The property F2 of index K1 should be fixed in special complex "
                     "label.vertexLabel name:T0.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_DISABLE_SUB_BACK_PRESSURE)
{
    uint32_t ret;
    char *cfgJson1 =
        (char *)R"({"max_record_count":1000, "disable_sub_back_pressure" : 2, "isFastReadUncommitted":false})";
    char *cfgJson2 =
        (char *)R"({"max_record_count":1000, "disable_sub_back_pressure" : 0, "isFastReadUncommitted":false})";
    char *cfgJson3 =
        (char *)R"({"max_record_count":1000, "disable_sub_back_pressure" : 1, "isFastReadUncommitted":false})";
    char *cfgJson4 =
        (char *)R"({"max_record_count":1000, "disable_sub_back_pressure" : true, "isFastReadUncommitted":false})";
    char *cfgJson5 =
        (char *)R"({"max_record_count":1000, "disable_sub_back_pressure" : false, "isFastReadUncommitted":false})";
    char *labelJson = (char *)R"([{
    "type":"record",
    "name":"T0",
    "fields":[
        {"name":"F0", "type":"int64", "nullable":true}
    ]
  }])";
    char *labelName = (char *)"T0";
    QryTestDropVertexLabel(labelName, false, NULL);
    ret = QryTestCreateVertexLabel(cfgJson1, labelJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch. The bool value should be true, false, 0 or 1.Type: disable_sub_back_pressure "
                 "should be bool.",
        lastError->str);
    ret = QryTestCreateVertexLabel(cfgJson2, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel(labelName, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCreateVertexLabel(cfgJson3, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel(labelName, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCreateVertexLabel(cfgJson4, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel(labelName, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestCreateVertexLabel(cfgJson5, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel(labelName, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

int32_t QryTestGetEstimateMemory(char *cfgJson, char *labelJson, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, rsp);
    if (ret != GMERR_OK) {
        // logging
        printf("alloc session failed!\n");
        return ret;
    }
    DbMemCtxArgsT args = {0};
    DbMemCtxT *dyAlgoCtxVertex =
        DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    ret = FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t labelJsonLen = 0;
    uint32_t cfgJsonLen = 0;
    if (labelJson != NULL) {
        labelJsonLen = strlen(labelJson) + 1;
    }
    if (cfgJson != NULL) {
        cfgJsonLen = strlen(cfgJson) + 1;
    }

    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    if (labelJson != NULL) {
        putText.str = labelJson;
        putText.len = labelJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, labelJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (cfgJson != NULL) {
        putText.str = cfgJson;
        putText.len = cfgJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, cfgJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_ESTIMATE_MEMORY,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);

    clearAllStub();
    return ret;
}

TEST_F(UtQueryDDLLabel, QRY_DDL_GetEstimateMemory)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErr",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true}
        ],
        "keys":[
            {"node":"labelvertexErr", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    FixBufferT *rsp = NULL;
    ret = QryTestGetEstimateMemory(cfgJson, labelJson, &rsp);
    EXPECT_EQ(GMERR_OK, ret);
    RpcSeekFirstOpMsg(rsp);
    CmdEstimateMemoryDataT *data = (CmdEstimateMemoryDataT *)FixBufGetData(rsp, sizeof(CmdEstimateMemoryDataT));
    EXPECT_EQ((int64_t)data->objectNum, 1000);
    EXPECT_EQ((int64_t)data->objectSize, 7000);
    // EXPECT_EQ(data->indexSize, 0);
    // 不固定 取决于是否开大表锁以及容量配置大小
    // EXPECT_EQ(data->uniqueHashSize, 0);
    EXPECT_EQ((int64_t)data->nonUniqueHashSize, 0);
    EXPECT_EQ((int64_t)data->hashclusterSize, 0);
    EXPECT_EQ((int64_t)data->lpmSize, 0);
    EXPECT_EQ((int64_t)data->localkeySize, 0);
    // EXPECT_EQ(data->totalSize, 7000);
    QryReleaseRsp(rsp);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateIndexError)
{
    uint32_t ret;

    QrySetCfg("compatibleV3", "1");

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"CreateIndexError",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"}
        ],
        "keys":
        [
            {"node":"CreateIndexError", "name":"T39_K0", "fields":["F1"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Inv table definition. The primary index (T39_K0) must be in field order.vertexLabel "
                                 "name:CreateIndexError.";
    EXPECT_STREQ(result, lastError1->str);

    char *labelJson2 = (char *)R"({
        "type":"record",
        "name":"CreateIndexError2",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"}
        ],
        "keys":
        [
            {"node":"CreateIndexError2", "name":"T39_K0", "fields":["F0", "F2"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    })";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson2);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError2 = DbGetLastErrorInfo();
    const char *result2 = (char *)"Inv table definition. The primary index (T39_K0) must be in field order.vertexLabel "
                                  "name:CreateIndexError2.";
    EXPECT_STREQ(result2, lastError2->str);

    char *labelJson3 = (char *)R"({
        "type":"record",
        "name":"CreateIndexError3",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"}
        ],
        "keys":
        [
            {"node":"CreateIndexError3", "name":"T39_K0", "fields":["F0", "F2", "F1"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    })";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson3);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError3 = DbGetLastErrorInfo();
    const char *result3 = (char *)"Inv table definition. The primary index (T39_K0) must be in field order.vertexLabel "
                                  "name:CreateIndexError3.";
    EXPECT_STREQ(result3, lastError3->str);

    // 主键有变长字段，则不校验索引的位置关系
    char *labelJson4 = (char *)R"({
        "type":"record",
        "name":"CreateIndexError4",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"string"}
        ],
        "keys":
        [
            {"node":"CreateIndexError4", "name":"T39_K0", "fields":["F0", "F2", "F1"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    })";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson4);
    EXPECT_EQ(GMERR_OK, ret);

    QrySetCfg("compatibleV3", "0");
}
TEST_F(UtQueryDDLLabel, QRY_DDL_CreateV3LabelWithLong)
{
    QrySetCfg("compatibleV3", "1");
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"({
        "type":"record",
        "name":"T0",
        "fields":[
            {"name":"F0", "type":"uint32", "comment":"test"},
            {"name":"F1", "type":"long"},
            {"name":"F2", "type":"uint32"},
            {"name":"F3", "type":"uint32"}
        ]
    })";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result1 = (char *)"Datatype mismatch. property F1 type is inv.";
    EXPECT_STREQ(result1, lastError1->str);
}

#ifndef FEATURE_PERSISTENCE
// 不支持super_field
TEST_F(UtQueryDDLLabel, QRY_DDL_CreateV3LabelSchemaJson)
{
    QrySetCfg("compatibleV3", "1");
    uint32_t ret;
    char *cfgJson = (char *)R"({"max_record_count":1000})";
    char *labelJsonFormat = (char *)R"({
        "type":"record",
        "name":"T0",
        "fields":[
            {"name":"F0", "type":"uint32", "comment":"test"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"},
            {"name":"F3", "type":"uint32"}
        ],
        "super_fields":[
            {"name":"superfield1", "fields":%s}
        ],
        "keys":[
            {"node":"T0", "name":"T0_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    })";
    char *labelName = (char *)"T0";
    int32_t maxSize = 1000;
    char normalLabelJson[maxSize], invalidLabelJson1[maxSize], invalidLabelJson2[maxSize], invalidLabelJson3[maxSize],
        invalidLabelJson4[maxSize], invalidLabelJson5[maxSize];
    int32_t len = sprintf_s(normalLabelJson, maxSize, labelJsonFormat, "[\"F0\",\"F1\",\"F2\"]");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, normalLabelJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel(labelName, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    len = sprintf_s(invalidLabelJson1, maxSize, labelJsonFormat, "1");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson1);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result1 = (char *)"Datatype mismatch. fields type should be array.The superField is superfield1.";
    EXPECT_STREQ(result1, lastError1->str);

    len = sprintf_s(invalidLabelJson2, maxSize, labelJsonFormat, "[1]");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson2);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError2 = DbGetLastErrorInfo();
    const char *result2 =
        (char *)"Datatype mismatch. super_field member type for lite should be string.The superField is superfield1.";
    EXPECT_STREQ(result2, lastError2->str);

    len = sprintf_s(invalidLabelJson3, maxSize, labelJsonFormat, "[\"F0\",\"F2\",\"F1\"]");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson3);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    TextT *lastError3 = DbGetLastErrorInfo();
    const char *result3 = (char *)"Program limit exceeded. super_field member F2 for lite should be continue.The "
                                  "superField is superfield1.";
    EXPECT_STREQ(result3, lastError3->str);

    len = sprintf_s(invalidLabelJson4, maxSize, labelJsonFormat, "[\"F0\", 1,\"F1\"]");
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson4);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError4 = DbGetLastErrorInfo();
    const char *result4 =
        (char *)"Datatype mismatch. super_field member type for lite should be string.The superField is superfield1.";
    EXPECT_STREQ(result4, lastError4->str);

    char name[133] = {0};
    memset_s(name, 132, 'a', 132);
    name[0] = '[';
    name[1] = '"';
    name[130] = '"';
    name[131] = ']';
    len = sprintf_s(invalidLabelJson5, maxSize, labelJsonFormat, name);
    printf("%s\n", invalidLabelJson5);
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(cfgJson, invalidLabelJson5);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError5 = DbGetLastErrorInfo();
    const char *result5 = (char *)"Not normal property. super_field member len:128. for lite "
                                  "exceeds:"
                                  "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                  "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaThe superField is superfield1.";
    EXPECT_STREQ(result5, lastError5->str);
    QrySetCfg("compatibleV3", "0");
}
#endif

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateV3LabelConfigJson)
{
    QrySetCfg("compatibleV3", "1");
    uint32_t ret;
    char *cfgJsonFormat =
        (char *)R"({"max_record_count":%s, "max_record_count_check":%s, "table_id":%s, "isFastReadUncommitted":false})";
    char *labelJson = (char *)R"({
        "type":"record",
        "name":"T0",
        "fields":[
            {"name":"F0", "type":"uint32", "comment":"test"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"},
            {"name":"F3", "type":"uint32"}
        ],
        "keys":[
            {"node":"T0", "name":"T0_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    })";
    int32_t maxSize = 200;
    char *labelName = (char *)"T0";
    char normalCfgJson[maxSize], invalidCfgJson1[maxSize], invalidCfgJson2[maxSize], invalidCfgJson3[maxSize];
    int32_t len = sprintf_s(normalCfgJson, maxSize, cfgJsonFormat, "1000", "true", "1");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(normalCfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel(labelName, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    len = sprintf_s(invalidCfgJson1, maxSize, cfgJsonFormat, "\"xxx\"", "true", "1");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(invalidCfgJson1, labelJson);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result1 = (char *)"Datatype mismatch.  max_record_count type should be uint.";
    EXPECT_STREQ(result1, lastError1->str);

    len = sprintf_s(invalidCfgJson2, maxSize, cfgJsonFormat, "1000", "\"xxx\"", "1");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(invalidCfgJson2, labelJson);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError2 = DbGetLastErrorInfo();
    const char *result2 = (char *)"Datatype mismatch. The bool value should be true, false, 0 or 1.Type: "
                                  "max_record_count_check should be bool.";
    EXPECT_STREQ(result2, lastError2->str);

    len = sprintf_s(invalidCfgJson3, maxSize, cfgJsonFormat, "1000", "true", "\"xxx\"");
    ASSERT_TRUE(len >= 0);
    ret = QryTestCreateVertexLabel(invalidCfgJson3, labelJson);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError3 = DbGetLastErrorInfo();
    const char *result3 = (char *)"Datatype mismatch.  table_id type should be uint.";
    EXPECT_STREQ(result3, lastError3->str);
    QrySetCfg("compatibleV3", "0");
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateVertexLabelWithLPMIndexFilter)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
            "type":"record",
            "name":"LPM001",
            "fields":
                [
                    {"name":"vr_id", "type":"uint32", "size":300},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}
                ],
            "keys":
                [
                    {
                        "node":"LPM001",
                        "name":"ip4forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true},
                        "filter":
                        {
                            "operator_type": "and",
                            "conditions": [
                                {"property": "vr_id", "compare_type": "=", "value": 1},
                                {"property": "vrf_index", "compare_type": "!=", "value": 1}
                            ]
                        }
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    char *result = (char *)"Inv table definition. The primary|lpm4|lpm6 index not support index filter."
                           "The vertexLabel is LPM001. The indexLabel is ip4forward_lpm.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateLabelWithlocalhashUnfixed)
{
    uint32_t ret;
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"testIndexFilter",
        "id":0,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"string"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"float"}
        ],
        "keys":
                [
                    {
                        "node":"testIndexFilter",
                        "name":"localIndex",
                        "fields":["F0", "F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true},
                        "filter":
                        {
                            "operator_type": "and",
                            "conditions": [
                                {"property": "F0", "compare_type": "equal", "value": 1},
                                {"property": "F1", "compare_type": "unequal", "value": "aaa"}
                            ]
                        }
                    }
                ]
    }])";
    char *labelCfgJson = (char *)R"({
        "isFastReadUncommitted":false
        })";
    ret = QryTestCreateVertexLabel(labelCfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    char *result = (char *)"Inv table definition. property F1 cannot be defined as index filter cond because of the "
                           "field type.The vertexLabel is testIndexFilter. The indexLabel is localIndex.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateLabelWithlocalNotExistField)
{
    uint32_t ret;
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"testIndexFilter",
        "id":0,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"string", "size":10},
            {"name":"F2", "type":"uint32"},
            {"name":"F3", "type":"float"}
        ],
        "keys":
                [
                    {
                        "node":"testIndexFilter",
                        "name":"localIndex",
                        "fields":["F0", "F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true},
                        "filter":
                        {
                            "operator_type": "and",
                            "conditions": [
                                {"property": "F0", "compare_type": "equal", "value": 1},
                                {"property": "F2", "compare_type": "unequal", "value": 2}
                            ]
                        }
                    }
                ]
    }])";
    char *labelCfgJson = (char *)R"({
        "isFastReadUncommitted":false
        })";
    ret = QryTestCreateVertexLabel(labelCfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    char *result = (char *)"Inv table definition. The cond property F2 of index filter localIndex does not "
                           "exist.vertexLabel name:testIndexFilter.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateTreeLabelWithlocalConditions)
{
    uint32_t ret;
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"testIndexFilter",
        "fields":[
            {"name":"F0", "type":"uint8", "nullable":true},
            {"name":"F1", "type":"uint8", "nullable":true},
            {"name":"T1", "type": "record",
            "fields": [
                    {"name":"P0", "type":"int64", "nullable":true},
                    {"name":"P1", "type":"uint64", "nullable":true}
                  ]
            }
        ],
        "keys":[
            {
                "node":"testIndexFilter",
                "name":"localIndex",
                "fields":["F0", "F1"],
                "index":{"type":"localhash"},
                "constraints":{"unique":true},
                "filter":
                {
                    "operator_type": "and",
                    "conditions": [
                        {"property": "F1", "compare_type": "equal", "value": 1},
                        {"property": "T1/P0", "compare_type": "unequal", "value": 1}
                    ]
                }
            }
        ]
    }])";
    ret = QryTestCreateVertexLabel(NULL, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError = DbGetLastErrorInfo();
    char *result = (char *)"Not normal property. property name is T1/P0.The filter condition property T1/P0 is "
                           "incorrect.The vertexLabel is testIndexFilter. The indexLabel is localIndex.";
    EXPECT_STREQ(result, lastError->str);
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CreateLabelWithLocalFilter)
{
    uint32_t ret;
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"testIndexFilter",
        "id":0,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"float"}
        ],
        "keys":
                [
                    {
                        "node":"testIndexFilter",
                        "name":"localIndex",
                        "fields":["F0", "F1"],
                        "index":{"type":"local"},
                        "constraints":{"unique":true},
                        "filter":
                        {
                            "operator_type": "and",
                            "conditions": [
                                {"property": "F0", "compare_type": "equal", "value": 1},
                                {"property": "F1", "compare_type": "unequal", "value": "a"}
                            ]
                        }
                    }
                ]
    }])";
    char *labelCfgJson = (char *)R"({
        "isFastReadUncommitted":false
        })";
    ret = QryTestCreateVertexLabel(labelCfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryTestDropVertexLabel((char *)"testIndexFilter", false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

#ifndef NDEBUG
TEST_F(UtQueryDDLLabel, testQryMutilSessionRecycle)
{
    SessionPoolT *sessionPool = QryGetSessionPool(NULL);
    uint32_t connNum = 50;
    DrtConnectionT *conn[connNum];
    for (uint32_t i = 0; i < connNum; i++) {
        QryTestAllocSession(&conn[i]);
    }
    EXPECT_EQ(sessionPool->hwm, connNum);
    EXPECT_EQ(sessionPool->validSessionsCnt, connNum);
    EXPECT_EQ((int32_t)sessionPool->idleSessionsCnt, 0);

    for (uint32_t i = 1; i < connNum; i++) {
        QryTestReleaseSession(conn[i]);
    }
    EXPECT_EQ(sessionPool->hwm, connNum);
    EXPECT_EQ(sessionPool->validSessionsCnt, connNum);
    EXPECT_EQ(sessionPool->idleSessionsCnt, connNum - 1);

    sleep(QRY_SESSION_RECYCLE_INTERVAL_FROM_IDLE * 2);
    QryTestReleaseSession(conn[0]);

    EXPECT_EQ(sessionPool->hwm, connNum);
    EXPECT_EQ((int32_t)sessionPool->validSessionsCnt, 4);  // < connNUm * %10
    EXPECT_EQ((int32_t)sessionPool->idleSessionsCnt, 4);
}
#endif

TEST_F(UtQueryDDLLabel, AlterLabelNormalForModifyWrong)
{
    uint32_t ret;
    char *originLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F2",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    char *labelCfgJson = (char *)R"({
        "isFastReadUncommitted":false
        })";
    ret = QryTestCreateVertexLabel(labelCfgJson, originLabelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    char *renameLabelNameLabelJson = (char *)R"({
        "type":"record",
        "name":"updatedLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"updatedLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"updatedLabel",
                "name":"Local_Unique_F1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"updatedLabel",
                "name":"Local_Unique_F2",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(renameLabelNameLabelJson, true, NULL);
#ifdef FEATURE_PERSISTENCE
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#else
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
#endif

    char *renameFieldNameLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2_update", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F2",
                "fields":["F2_update"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(renameFieldNameLabelJson, true, NULL);
#ifdef FEATURE_PERSISTENCE
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#else
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
#endif
    char *renameIndexNameLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0_update",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F2",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(renameIndexNameLabelJson, true, NULL);
#ifdef FEATURE_PERSISTENCE
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#else
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
#endif
    char *modifyIndexFieldsLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F2"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F2",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(modifyIndexFieldsLabelJson, true, NULL);
#ifdef FEATURE_PERSISTENCE
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#else
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
#endif
    char *modifyIndexOrderLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F2",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(modifyIndexOrderLabelJson, true, NULL);
#ifdef FEATURE_PERSISTENCE
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#else
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
#endif
    char *reduceIndexNumLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"originLabel",
                "name":"Local_Unique_F2",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(reduceIndexNumLabelJson, true, NULL);
#ifdef FEATURE_PERSISTENCE
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#else
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
#endif
    ret = QryTestDropVertexLabel((char *)"originLabel", false, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, DISABLED_AlterLabelAboutSuperFieldForModifyWrong)
{
    uint32_t ret;
    char *originLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "super_fields":[
            {
                "name":"superfield",
                "fields":{"begin":"F0", "end":"F2"}
            }
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestCreateVertexLabel(NULL, originLabelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    char *renameLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "super_fields":[
            {
                "name":"superfield_update",
                "fields":{"begin":"F0", "end":"F2"}
            }
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(renameLabelJson, true, NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    char *modifyBeginLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "super_fields":[
            {
                "name":"superfield",
                "fields":{"begin":"F1", "end":"F2"}
            }
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(modifyBeginLabelJson, true, NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    char *modifyEndLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "super_fields":[
            {
                "name":"superfield",
                "fields":{"begin":"F0", "end":"F1"}
            }
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(modifyEndLabelJson, true, NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    char *addSuperFieldLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "super_fields":[
            {
                "name":"superfield",
                "fields":{"begin":"F0", "end":"F2"}
            },
            {
                "name":"superfield1",
                "fields":{"begin":"F0", "end":"F2"}
            }
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(addSuperFieldLabelJson, true, NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    char *reduceSuperFieldLabelJson = (char *)R"({
        "type":"record",
        "name":"originLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"originLabel",
                "name":"PK_F0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";
    ret = QryTestAlterVertexLabel(reduceSuperFieldLabelJson, true, NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = QryTestDropVertexLabel((char *)"originLabel", false, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

static void getYangUniqueNodeId(DmSchemaT *schema, uint32_t *uniqueNodeId)
{
    uint32_t NodeId = schema->yangInfo->uniqueNodeId;
    ASSERT_EQ((*uniqueNodeId)++, NodeId);

    // 递归调用
    for (uint32_t i = 0; i < schema->nodeNum; i++) {
        getYangUniqueNodeId(schema->nodes[i].schema, uniqueNodeId);
    }
}

DmYangInfoT *AllocYangInfo(DbMemCtxT *memCtx)
{
    DmYangInfoT *YangInfo = (DmYangInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmYangInfoT));
    EXPECT_FALSE(YangInfo == NULL);
    (void)memset_s(YangInfo, sizeof(DmYangInfoT), 0, sizeof(DmYangInfoT));
    return YangInfo;
}

DmPropertySchemaT *getPropertyTreeSchema1Qry(DbMemCtxT *memCtx, uint32_t propertyNum)
{
    DmPropertySchemaT *property = AllocProperties(memCtx, 5);

    property[0].isValid = true;
    property[0].dataType = DB_DATATYPE_STRING;
    property[0].isNullable = true;
    property[0].nameLen = DM_STR_LEN("c1");
    property[0].name = (char *)DbDynMemCtxAlloc(memCtx, property[0].nameLen);
    strcpy_s(property[0].name, property[0].nameLen, "c1");
    property[0].propeId = 0;
    property[0].isFixed = false;
    property[0].size = 10;

    property[1].isValid = true;
    property[1].dataType = DB_DATATYPE_UINT64;
    property[1].isNullable = false;
    property[1].nameLen = DM_STR_LEN("c2");
    property[1].name = (char *)DbDynMemCtxAlloc(memCtx, property[1].nameLen);
    strcpy_s(property[1].name, property[1].nameLen, "c2");
    property[1].propeId = 1;
    property[1].isFixed = true;
    property[1].size = 8;

    property[2].isValid = true;
    property[2].dataType = DB_DATATYPE_UINT64;
    property[2].isNullable = false;
    property[2].nameLen = DM_STR_LEN("c3");
    property[2].name = (char *)DbDynMemCtxAlloc(memCtx, property[2].nameLen);
    strcpy_s(property[2].name, property[2].nameLen, "c3");
    property[2].propeId = 2;
    property[2].isFixed = true;
    property[2].size = 8;

    property[3].isValid = true;
    property[3].dataType = DB_DATATYPE_UINT64;
    property[3].isNullable = false;
    property[3].nameLen = DM_STR_LEN("c4");
    property[3].name = (char *)DbDynMemCtxAlloc(memCtx, property[3].nameLen);
    strcpy_s(property[3].name, property[3].nameLen, "c4");
    property[3].propeId = 3;
    property[3].isFixed = true;
    property[3].size = 8;

    property[4].isValid = true;
    property[4].dataType = DB_DATATYPE_UINT64;
    property[4].isNullable = false;
    property[4].nameLen = DM_STR_LEN("c5");
    property[4].name = (char *)DbDynMemCtxAlloc(memCtx, property[4].nameLen);
    strcpy_s(property[4].name, property[4].nameLen, "c5");
    property[4].propeId = 4;
    property[4].isFixed = true;
    property[4].size = 8;

    return property;
}

// 构建带有多层嵌套 node 的 vertex label，其结构示意图参见 ut_dm_constructor.cc
DmVertexLabelT *GetNestVertexLabelTreeSchema(DbMemCtxT *memctx, bool arrayOrVector)
{
    DmPropertySchemaT *property = AllocProperties(memctx, 5);
    property[0].isValid = true;
    property[0].dataType = DB_DATATYPE_INT32;
    property[0].nameLen = DM_STR_LEN("c1");
    property[0].name = (char *)DbDynMemCtxAlloc(memctx, property[0].nameLen);
    strcpy_s(property[0].name, property[0].nameLen, "c1");
    property[0].propeId = 0;
    property[0].isFixed = true;
    property[0].size = 4;
    property[0].commentLen = DM_STR_LEN("VertexC1");
    property[0].comment = (char *)DbDynMemCtxAlloc(memctx, property[0].commentLen);
    strcpy_s(property[0].comment, property[0].commentLen, "VertexC1");

    property[1].isValid = false;
    property[1].dataType = DB_DATATYPE_INT32;
    property[1].nameLen = DM_STR_LEN("c2");
    property[1].name = (char *)DbDynMemCtxAlloc(memctx, property[1].nameLen);
    strcpy_s(property[1].name, property[1].nameLen, "c2");
    property[1].propeId = 1;
    property[1].isFixed = true;
    property[1].size = 4;

    property[2].isValid = false;
    property[2].dataType = DB_DATATYPE_INT32;
    property[2].nameLen = DM_STR_LEN("c3");
    property[2].name = (char *)DbDynMemCtxAlloc(memctx, property[2].nameLen);
    strcpy_s(property[2].name, property[2].nameLen, "c3");
    property[2].propeId = 2;
    property[2].isFixed = true;
    property[2].size = 4;

    property[3].isValid = true;
    property[3].dataType = DB_DATATYPE_INT32;
    property[3].nameLen = DM_STR_LEN("c4");
    property[3].name = (char *)DbDynMemCtxAlloc(memctx, property[3].nameLen);
    strcpy_s(property[3].name, property[3].nameLen, "c4");
    property[3].propeId = 3;
    property[3].isFixed = true;
    property[3].size = 4;

    property[4].isValid = false;
    property[4].dataType = DB_DATATYPE_INT32;
    property[4].nameLen = DM_STR_LEN("c5");
    property[4].name = (char *)DbDynMemCtxAlloc(memctx, property[4].nameLen);
    strcpy_s(property[4].name, property[4].nameLen, "c5");
    property[4].propeId = 4;
    property[4].isFixed = true;
    property[4].size = 4;

    // 构建第一层 vertex 的 schema
    DmSchemaT *schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->isFlat = false;
    schema->propeNum = 5;
    schema->superFieldNum = 0;
    schema->properties = property;
    schema->nodeNum = 3;
    schema->nodes = (DmNodeSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmNodeSchemaT) * 3);
    memset_s(schema->nodes, sizeof(DmNodeSchemaT) * 3, 0, sizeof(DmNodeSchemaT) * 3);
    schema->yangInfo = AllocYangInfo(memctx);

    // 构建 vertex 的孩子 node1
    schema->nodes[0].nodeType = arrayOrVector ? DM_NODE_ARRAY : DM_NODE_VECTOR;
    schema->nodes[0].nameLen = DM_STR_LEN("node1");
    schema->nodes[0].name = (char *)DbDynMemCtxAlloc(memctx, schema->nodes[0].nameLen);
    strcpy_s(schema->nodes[0].name, schema->nodes[0].nameLen, "node1");
    schema->nodes[0].arraySize = 10;
    schema->nodes[0].vectorMaxSize = 10;
    schema->nodes[0].vectorInitSize = 6;
    schema->nodes[0].vectorExtendSize = 2;
    schema->nodes[0].id = 1;
    schema->nodes[0].memberKeyNum = 1;
    schema->nodes[0].commentLen = DM_STR_LEN("CommentNode1");
    schema->nodes[0].comment = (char *)DbDynMemCtxAlloc(memctx, schema->nodes[0].commentLen);
    strcpy_s(schema->nodes[0].comment, schema->nodes[0].commentLen, "CommentNode1");

    schema->nodes[0].schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(schema->nodes[0].schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->nodes[0].schema->isFlat = false;
    schema->nodes[0].schema->propeNum = 5;
    schema->nodes[0].schema->properties = getPropertyTreeSchema1Qry(memctx, 5);

    schema->nodes[0].schema->properties[0].commentLen = DM_STR_LEN("Node1C1");
    schema->nodes[0].schema->properties[0].comment =
        (char *)DbDynMemCtxAlloc(memctx, schema->nodes[0].schema->properties[0].commentLen);
    strcpy_s(
        schema->nodes[0].schema->properties[0].comment, schema->nodes[0].schema->properties[0].commentLen, "Node1C1");

    schema->nodes[0].schema->properties[2].isValid = false;
    schema->nodes[0].schema->nodeNum = 1;
    schema->nodes[0].schema->nodes = (DmNodeSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmNodeSchemaT));
    memset_s(schema->nodes[0].schema->nodes, sizeof(DmNodeSchemaT), 0, sizeof(DmNodeSchemaT));
    schema->nodes[0].schema->superFieldNum = 0;
    schema->nodes[0].schema->yangInfo = AllocYangInfo(memctx);

    // 构建 node1 的孩子 node4
    DmNodeSchemaT *node4Schema = &(schema->nodes[0].schema->nodes[0]);
    node4Schema->nodeType = arrayOrVector ? DM_NODE_ARRAY : DM_NODE_VECTOR;
    node4Schema->nameLen = DM_STR_LEN("node4");
    node4Schema->name = (char *)DbDynMemCtxAlloc(memctx, node4Schema->nameLen);
    strcpy_s(node4Schema->name, node4Schema->nameLen, "node4");
    node4Schema->arraySize = 10;
    node4Schema->vectorMaxSize = 10;
    node4Schema->vectorInitSize = 6;
    node4Schema->vectorExtendSize = 2;
    node4Schema->id = 2;
    node4Schema->memberKeyNum = 1;
    node4Schema->commentLen = DM_STR_LEN("CommentNode4");
    node4Schema->comment = (char *)DbDynMemCtxAlloc(memctx, node4Schema->commentLen);
    strcpy_s(node4Schema->comment, node4Schema->commentLen, "CommentNode4");

    node4Schema->schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(node4Schema->schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    node4Schema->schema->isFlat = false;
    node4Schema->schema->propeNum = 2;
    node4Schema->schema->properties = AllocProperties(memctx, 2);

    node4Schema->schema->properties[0].isValid = true;
    node4Schema->schema->properties[0].dataType = DB_DATATYPE_STRING;
    node4Schema->schema->properties[0].isNullable = true;
    node4Schema->schema->properties[0].nameLen = DM_STR_LEN("c1");
    node4Schema->schema->properties[0].name = (char *)DbDynMemCtxAlloc(memctx, DM_STR_LEN("c1"));
    strcpy_s(node4Schema->schema->properties[0].name, node4Schema->schema->properties[0].nameLen, "c1");
    node4Schema->schema->properties[0].propeId = 0;
    node4Schema->schema->properties[0].isFixed = false;
    node4Schema->schema->properties[0].size = 10;

    node4Schema->schema->properties[1].isValid = false;
    node4Schema->schema->nodeNum = 1;
    node4Schema->schema->nodes = (DmNodeSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmNodeSchemaT));
    memset_s(node4Schema->schema->nodes, sizeof(DmNodeSchemaT), 0, sizeof(DmNodeSchemaT));
    node4Schema->schema->superFieldNum = 0;
    node4Schema->schema->yangInfo = AllocYangInfo(memctx);

    // 构建 node4 的孩子 node6
    DmNodeSchemaT *node6Schema = &(node4Schema->schema->nodes[0]);
    node6Schema->nodeType = DM_NODE_RECORD;
    node6Schema->nameLen = DM_STR_LEN("node6");
    node6Schema->name = (char *)DbDynMemCtxAlloc(memctx, node6Schema->nameLen);
    strcpy_s(node6Schema->name, node6Schema->nameLen, "node6");
    node6Schema->arraySize = 0;
    node6Schema->vectorMaxSize = 0;
    node6Schema->vectorInitSize = 0;
    node6Schema->vectorExtendSize = 0;
    node6Schema->id = 1;
    node6Schema->commentLen = DM_STR_LEN("CommentNode6");
    node6Schema->comment = (char *)DbDynMemCtxAlloc(memctx, node6Schema->commentLen);
    strcpy_s(node6Schema->comment, node6Schema->commentLen, "CommentNode6");
    node6Schema->schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(node6Schema->schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    node6Schema->schema->isFlat = true;
    node6Schema->schema->propeNum = 2;
    node6Schema->schema->properties = AllocProperties(memctx, 2);

    node6Schema->schema->properties[0].isValid = true;
    node6Schema->schema->properties[0].dataType = DB_DATATYPE_STRING;
    node6Schema->schema->properties[0].isNullable = true;
    node6Schema->schema->properties[0].nameLen = DM_STR_LEN("c1");
    node6Schema->schema->properties[0].name = (char *)DbDynMemCtxAlloc(memctx, DM_STR_LEN("c1"));
    strcpy_s(node6Schema->schema->properties[0].name, node6Schema->schema->properties[0].nameLen, "c1");
    node6Schema->schema->properties[0].propeId = 0;
    node6Schema->schema->properties[0].isFixed = false;
    node6Schema->schema->properties[0].size = 10;

    node6Schema->schema->properties[1].isValid = true;
    node6Schema->schema->properties[1].dataType = DB_DATATYPE_UINT64;
    node6Schema->schema->properties[1].isNullable = false;
    node6Schema->schema->properties[1].nameLen = DM_STR_LEN("c2");
    node6Schema->schema->properties[1].name = (char *)DbDynMemCtxAlloc(memctx, DM_STR_LEN("c2"));
    strcpy_s(node6Schema->schema->properties[1].name, node6Schema->schema->properties[1].nameLen, "c2");
    node6Schema->schema->properties[1].propeId = 1;
    node6Schema->schema->properties[1].isFixed = true;
    node6Schema->schema->properties[1].size = 8;

    node6Schema->schema->properties[0].commentLen = DM_STR_LEN("Node6C1");
    node6Schema->schema->properties[0].comment =
        (char *)DbDynMemCtxAlloc(memctx, node6Schema->schema->properties[0].commentLen);
    strcpy_s(node6Schema->schema->properties[0].comment, node6Schema->schema->properties[0].commentLen, "Node6C1");
    node6Schema->schema->nodeNum = 0;
    node6Schema->schema->nodes = NULL;
    node6Schema->schema->superFieldNum = 0;
    node6Schema->schema->yangInfo = AllocYangInfo(memctx);

    // 构建 vertex 的孩子 node2
    schema->nodes[1].nodeType = DM_NODE_RECORD;
    schema->nodes[1].nameLen = DM_STR_LEN("node2");
    schema->nodes[1].name = (char *)DbDynMemCtxAlloc(memctx, schema->nodes[1].nameLen);
    strcpy_s(schema->nodes[1].name, schema->nodes[1].nameLen, "node2");
    schema->nodes[1].arraySize = 0;
    schema->nodes[1].vectorMaxSize = 0;
    schema->nodes[1].vectorInitSize = 0;
    schema->nodes[1].vectorExtendSize = 0;
    schema->nodes[1].id = 2;
    schema->nodes[1].schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(schema->nodes[1].schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->nodes[1].schema->isFlat = true;
    schema->nodes[1].schema->propeNum = 1;
    schema->nodes[1].schema->properties = AllocProperties(memctx, 1);

    schema->nodes[1].schema->properties[0].isValid = true;
    schema->nodes[1].schema->properties[0].dataType = DB_DATATYPE_STRING;
    schema->nodes[1].schema->properties[0].isNullable = true;
    schema->nodes[1].schema->properties[0].nameLen = DM_STR_LEN("c1");
    schema->nodes[1].schema->properties[0].name = (char *)DbDynMemCtxAlloc(memctx, DM_STR_LEN("c1"));
    strcpy_s(schema->nodes[1].schema->properties[0].name, schema->nodes[1].schema->properties[0].nameLen, "c1");
    schema->nodes[1].schema->properties[0].propeId = 0;
    schema->nodes[1].schema->properties[0].isFixed = false;
    schema->nodes[1].schema->properties[0].size = 10;

    schema->nodes[1].schema->nodeNum = 0;
    schema->nodes[1].schema->nodes = NULL;
    schema->nodes[1].schema->superFieldNum = 0;
    schema->nodes[1].schema->yangInfo = AllocYangInfo(memctx);

    // 构建 vertex 的孩子 node3
    schema->nodes[2].nodeType = DM_NODE_ARRAY;
    schema->nodes[2].nameLen = DM_STR_LEN("node3");
    schema->nodes[2].name = (char *)DbDynMemCtxAlloc(memctx, schema->nodes[2].nameLen);
    strcpy_s(schema->nodes[2].name, schema->nodes[2].nameLen, "node3");
    schema->nodes[2].arraySize = 10;
    schema->nodes[2].vectorMaxSize = 10;
    schema->nodes[2].vectorInitSize = 6;
    schema->nodes[2].vectorExtendSize = 2;
    schema->nodes[2].id = 4;
    schema->nodes[2].schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(schema->nodes[2].schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->nodes[2].schema->isFlat = false;
    schema->nodes[2].schema->propeNum = 2;
    schema->nodes[2].schema->properties = AllocProperties(memctx, 2);

    schema->nodes[2].schema->properties[0].isValid = true;
    schema->nodes[2].schema->properties[0].dataType = DB_DATATYPE_STRING;
    schema->nodes[2].schema->properties[0].isNullable = true;
    schema->nodes[2].schema->properties[0].nameLen = DM_STR_LEN("c1");
    schema->nodes[2].schema->properties[0].name = (char *)DbDynMemCtxAlloc(memctx, DM_STR_LEN("c1"));
    strcpy_s(schema->nodes[2].schema->properties[0].name, schema->nodes[2].schema->properties[0].nameLen, "c1");
    schema->nodes[2].schema->properties[0].propeId = 0;
    schema->nodes[2].schema->properties[0].isFixed = false;
    schema->nodes[2].schema->properties[0].size = 10;

    schema->nodes[2].schema->properties[1].isValid = false;
    schema->nodes[2].schema->nodeNum = 1;
    schema->nodes[2].schema->nodes = (DmNodeSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmNodeSchemaT));
    memset_s(schema->nodes[2].schema->nodes, sizeof(DmNodeSchemaT), 0, sizeof(DmNodeSchemaT));
    schema->nodes[2].schema->superFieldNum = 0;
    schema->nodes[2].schema->yangInfo = AllocYangInfo(memctx);

    // 构建 node3 的孩子 node5
    DmNodeSchemaT *node5Schema = &(schema->nodes[2].schema->nodes[0]);
    node5Schema->nodeType = DM_NODE_RECORD;
    node5Schema->nameLen = DM_STR_LEN("node5");
    node5Schema->name = (char *)DbDynMemCtxAlloc(memctx, node5Schema->nameLen);
    strcpy_s(node5Schema->name, node5Schema->nameLen, "node5");
    node5Schema->arraySize = 0;
    node5Schema->vectorMaxSize = 0;
    node5Schema->vectorInitSize = 0;
    node5Schema->vectorExtendSize = 0;
    node5Schema->id = 1;
    node5Schema->schema = (DmSchemaT *)DbDynMemCtxAlloc(memctx, sizeof(DmSchemaT));
    memset_s(node5Schema->schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    node5Schema->schema->isFlat = true;
    node5Schema->schema->propeNum = 1;
    node5Schema->schema->properties = AllocProperties(memctx, 1);

    node5Schema->schema->properties[0].isValid = true;
    node5Schema->schema->properties[0].dataType = DB_DATATYPE_STRING;
    node5Schema->schema->properties[0].isNullable = true;
    node5Schema->schema->properties[0].nameLen = DM_STR_LEN("c1");
    node5Schema->schema->properties[0].name = (char *)DbDynMemCtxAlloc(memctx, DM_STR_LEN("c1"));
    strcpy_s(node5Schema->schema->properties[0].name, node5Schema->schema->properties[0].nameLen, "c1");
    node5Schema->schema->properties[0].propeId = 0;
    node5Schema->schema->properties[0].isFixed = false;
    node5Schema->schema->properties[0].size = 10;

    node5Schema->schema->nodeNum = 0;
    node5Schema->schema->nodes = NULL;
    node5Schema->schema->superFieldNum = 0;
    node5Schema->schema->yangInfo = AllocYangInfo(memctx);

    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memctx, &vertexLabel);
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_YANG;
    vertexLabel->metaCommon.metaId = 1;
    uint32_t metaNameLen = strlen("zhang") + 1;
    vertexLabel->metaCommon.metaName = (char *)DbDynMemCtxAlloc(memctx, metaNameLen);
    strcpy_s(vertexLabel->metaCommon.metaName, metaNameLen, "zhang");
    uint32_t topRecordNameLen = strlen("topRecordName") + 1;
    vertexLabel->metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(memctx, topRecordNameLen);
    strcpy_s(vertexLabel->metaVertexLabel->topRecordName, topRecordNameLen, "topRecordName");
    vertexLabel->metaVertexLabel->secIndexNum = 0;
    vertexLabel->metaVertexLabel->secIndexes = NULL;
    vertexLabel->metaVertexLabel->schema = schema;
    VertexLabelCommonInfoT *commonInfo =
        (VertexLabelCommonInfoT *)DbDynMemCtxAlloc(memctx, sizeof(VertexLabelCommonInfoT));
    memset_s(commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    vertexLabel->commonInfo = commonInfo;
    vertexLabel->commonInfo->refCount = 1;
    vertexLabel->commonInfo->edgeLabelNum = 0;
    vertexLabel->commonInfo->relatedEdgeLabels = NULL;
    vertexLabel->commonInfo->labelLatchShmAddr = DB_INVALID_SHMPTR;
    vertexLabel->metaVertexLabel->checkValidity = true;
    vertexLabel->metaVertexLabel->labelLevel = VERTEX_LEVEL_GENERAL;

    return vertexLabel;
}

TEST_F(UtQueryDDLLabel, QRY_DDL_CheckUniqueNodeId)
{
    DmVertexLabel *vertexLabel = GetNestVertexLabelTreeSchema((DbMemCtxT *)dyAlgoCtxVertex, true);
    QryParseYangUniqueNodeId(vertexLabel);
    uint32_t uniqueNodeId = 1;  // 目前uniqueNodeId从1开始赋值
    getYangUniqueNodeId(vertexLabel->metaVertexLabel->schema, &uniqueNodeId);
}

#ifndef FEATURE_PERSISTENCE
Status QryPrepareUninstallDatalog(const char *fileName)
{
    Status ret;

    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = FixBufCreate(&req, QryGetDyAlgoCtxVertexBase(), CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }
    TextT datalogLibName = {.len = static_cast<uint32_t>(strlen(fileName) + 1), .str = (char *)fileName};
    TextT nspName = {.len = static_cast<uint32_t>(strlen("public") + 1), .str = (char *)"public"};

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &datalogLibName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &nspName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)0));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_UNINSTALL_DATALOG,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);

    SessionT *session = (SessionT *)procCtx.conn->session;
    session->procCtx = (void *)&procCtx;
    QrySessionSetReq(session, &procCtx.msg);
    RpcSeekFirstOpMsg(&procCtx.msg);
    ret = QryGetStmtById(session, msgHeader->stmtId, &session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    UnloadCtxT unloadCtx = {0};
    DtlUnImportParamT unloadParam = {0};
    unloadCtx.unloadParam = &unloadParam;
    unloadCtx.memCtx = session->memCtx;
    ret = DeserializeAndVerifyUnloadReq(session, &unloadCtx);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

TEST_F(UtQueryDDLLabel, TestUnsintallDatalogNormal)
{
    char *path = (char *)"datalogNormal.so";
    Status ret = QryPrepareUninstallDatalog(path);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLLabel, TestUnsintallDatalogFailed)
{
    char fileName[PATH_MAX] = {0};
    Status ret = QryPrepareUninstallDatalog(fileName);
    EXPECT_NE(GMERR_OK, ret);

    memset(fileName, 'a', 258);
    ret = QryPrepareUninstallDatalog(fileName);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
}
#endif

TEST_F(UtQueryDDLLabel, QRY_DDL_StatusMergeLabelGc)
{
    DrtConnectionT *conn = NULL;
    uint32_t ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    const char *subsLabelStMergeJson =
        R"([{
    "type":"record",
    "name":"subsLabelStMerge",
    "subs_type":"status_merge",
    "fields":
        [
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"uint32", "nullable":false},
            {"name":"F2", "type":"uint32", "nullable":false}
        ],
    "keys":
        [
            {
                "node":"subsLabelStMerge",
                "name":"subLabel1_K0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }])";
    const char *labelCfgJson = R"({
        "max_record_count":1000,
        "push_age_record_batch":20,
        "isFastReadUncommitted":false,
        "defragmentation":false
        })";

    ret = QryTestCreateVertexLabel((char *)labelCfgJson, (char *)subsLabelStMergeJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *label = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, "subsLabelStMerge");
    CataGetVertexLabelByName(NULL, &cataKey, &label);
    CataReleaseVertexLabel(label);
    ret = QryTestDropVertexLabel((char *)"subsLabelStMerge", true, NULL);
    QryTestReleaseSession(conn);
}
