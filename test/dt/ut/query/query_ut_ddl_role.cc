#include "db_text.h"
#include "dm_cache_basic.h"
#include "dm_meta_user.h"
#include "dm_meta_role.h"
#include "ee_cmd.h"
#include "query_ut_base.h"

class UtQueryDDLRole : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        int32_t ret = QrySetCfg("userPolicyMode", "2");
        EXPECT_EQ(ret, GMERR_OK);
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo(dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtQueryDDLRole, QRY_DDL_CreateRole)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);

    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    CataUserNameInfoT info;
    info.groupName = roleName.str;
    info.processName = processName.str;
    ret = CataDropGroup(&info, NULL);  // 创建完成之后进行删除
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}
TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleAlreadyExist)
{
    char role[] = "role";
    char process[] = "process";

    CataUserNameInfoT info;
    info.groupName = role;
    info.processName = process;
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, role, process);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    EXPECT_EQ(GMERR_OK, ret);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = role};
    TextT processName = {.len = strlen("process") + 1, .str = process};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);

    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    printf("ret = %d\n", ret);
    EXPECT_NE(GMERR_OK, ret);
    ret = CataDropGroup(&info, NULL);  // 创建完成之后进行删除
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleInvalidNameLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = 0, .str = (char *)"role"};  // 这里长度为0
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleExceedNameLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("1111222233334444555566667777888899990000111122223333444455556666777788889999000011"
                                    "11222233334444555566667777888899990000++++----*") +
                             1,
        .str = (char *)"11112222333344445555666677778888999900001111222233334444555566667777888899990000111122223333444"
                       "4555566667777888899990000++++----*"};  // 这里长度超128
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleVerifyName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("operator") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    QryTestReleaseSession(conn);
}
TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleWithoutPrivs2)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    QryTestReleaseSession(conn);
}
TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleWithoutPrivs1)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    ret = QrySetCfg("userPolicyMode", "1");
    EXPECT_EQ(ret, GMERR_OK);
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    CataUserNameInfoT info;
    info.groupName = roleName.str;
    info.processName = processName.str;
    ret = CataDropGroup(&info, NULL);  // 创建完成之后进行删除
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleWithoutPrivs0)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    ret = QrySetCfg("userPolicyMode", "0");
    EXPECT_EQ(ret, GMERR_OK);
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    CataUserNameInfoT info;
    info.groupName = roleName.str;
    info.processName = processName.str;
    ret = CataDropGroup(&info, NULL);  // 创建完成之后进行删除
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}
TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleAsSuperUser)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = true;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    CataUserNameInfoT info;
    info.groupName = roleName.str;
    info.processName = processName.str;
    ret = CataDropGroup(&info, NULL);  // 创建完成之后进行删除
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_CreateRoleItself)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("operator") + 1, .str = (char *)"operator"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    uint16_t groupMetadataMaxNumArray[USER_META_TYPE_NUM_N] = {0};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    EXPECT_EQ(GMERR_OK, FixBufPutData(&req, groupMetadataMaxNumArray, USER_META_TYPE_NUM_N * sizeof(uint16_t)));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRole)
{
    char role[] = "role";
    char process[] = "process";
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, role, process);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    EXPECT_EQ(GMERR_OK, ret);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = role};
    TextT processName = {.len = strlen("process") + 1, .str = process};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleInvalidLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = 0, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleExceedLen)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = 129, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleVerifyName)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("operator") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleWithoutPrivs2)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = (char *)"role"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleWithoutPrivs1)
{
    char role[] = "role";
    char process[] = "process";
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, role, process);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = role};
    TextT processName = {.len = strlen("process") + 1, .str = process};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    ret = QrySetCfg("userPolicyMode", "1");
    EXPECT_EQ(ret, GMERR_OK);
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleWithoutPrivs0)
{
    char role[] = "role";
    char process[] = "process";

    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, role, process);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = role};
    TextT processName = {.len = strlen("process") + 1, .str = process};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    ret = QrySetCfg("userPolicyMode", "0");
    EXPECT_EQ(ret, GMERR_OK);
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}
TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleAsSuperUser)
{
    char role[] = "role";
    char process[] = "process";
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, role, process);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("role") + 1, .str = role};
    TextT processName = {.len = strlen("process") + 1, .str = process};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = true;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveNoPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleItself)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("operator") + 1, .str = (char *)"operator"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t) false));  // 默认原子性是关闭
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLRole, QRY_DDL_DropRoleNotExist)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT roleName = {.len = strlen("lalala") + 1, .str = (char *)"lalala"};
    TextT processName = {.len = strlen("process") + 1, .str = (char *)"process"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &processName));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_GROUP,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    SessionT *session = (SessionT *)conn->session;
    strcpy(session->externalUser.dbGroupName, "operator");
    strcpy(session->externalUser.dbProcessName, "process");
    session->isDBA = false;
    (void)setStubC((void *)QryGetDbSessionRole, (void *)UtQueryGetRoleHaveAllPrivs);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    printf("ret = %d\n", ret);
    EXPECT_NE(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}
TEST_F(UtQueryDDLRole, QRY_DDL_CreateRole10001)
{
    char process[] = "process";
    status_t ret;
    char name[20];
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    int32_t roleNum = DbOamapUsedSize(cataCacheMgr->metaCache[CATA_RL]->idMap);
    int createNum = 10000 - roleNum;
    for (int i = 0; i < createNum + 1; i++) {
        sprintf(name, "role%d", i);
        CreatePrivEntityPara para = {
            .userOrGroupStr = NULL,
            .roleName = name,
            .processName = process,
            .metaType = CATA_USR,
            .roleId = (uint32_t)i,
            .isUser = false,
            .isDBA = false,
            .dbInstance = NULL,
        };
        ret = CreateRoleNoLock(&para);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // test point: 超过10000个role失败
    char testCreateRoleFail[] = "testCreateRoleFail";
    CreatePrivEntityPara para2 = {
        .userOrGroupStr = NULL,
        .roleName = testCreateRoleFail,
        .processName = process,
        .metaType = CATA_USR,
        .roleId = 20000,
        .isUser = false,
        .isDBA = false,
        .dbInstance = NULL,
    };
    ret = CreateRoleNoLock(&para2);
    EXPECT_NE(GMERR_OK, ret);

    for (int i = 0; i < createNum + 1; i++) {
        sprintf(name, "role%d", i);
        ret = DropRoleNoLock(i, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
