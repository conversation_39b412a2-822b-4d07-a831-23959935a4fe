#include "query_ut_base.h"
#include "db_text.h"
#include "adpt_define.h"
#include "se_resource_session_pub.h"

static char *kvTableName = (char *)"kvTabelDml1";
char *kvTableValid = (char *)"KV_TABLE_1";
char *kvTableInvalid = (char *)"KV_TABLE_2";
class UtQueryDMLKv : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}

    void SetToNotExistKvTable(const char *tableName, const char *key, const char *value)
    {
        uint32_t labelId = 0;
        TextT labelName = DbStr2Text((char *)tableName);
        uint32_t keyLen = strlen(key);
        uint32_t valueLen = strlen(value);
        TextT keyT;
        keyT.str = (char *)key;
        keyT.len = keyLen;
        TextT valueT;
        valueT.str = (char *)value;
        valueT.len = valueLen;
        int32_t ret = QryTestKvDml(labelId, &labelName, &keyT, &valueT, NULL, MSG_OP_RPC_SET_KV);
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    }

    void CreateKvTable(char *tableName)
    {
        uint32_t ret;
        char *cfgJson = (char *)"{\"max_record_count\":2, \"max_record_count_check\":true}";
        ret = QryTestCreateKvTable(tableName, cfgJson);
        EXPECT_EQ(GMERR_OK, ret);
    }

    Status SetKv(const char *tableName, const char *key, const char *value)
    {
        uint32_t labelId = 0;
        TextT labelName = DbStr2Text((char *)tableName);
        uint32_t keyLen = strlen(key);
        uint32_t valueLen = strlen(value);
        TextT keyT;
        keyT.str = (char *)key;
        keyT.len = keyLen;
        TextT valueT;
        valueT.str = (char *)value;
        valueT.len = valueLen;
        return QryTestKvDml(labelId, &labelName, &keyT, &valueT, NULL, MSG_OP_RPC_SET_KV);
    }

    void GetKv(const char *key, const char *value)
    {
        uint32_t labelId = 0;
        TextT labelName = DbStr2Text(kvTableName);
        uint32_t keyLen = strlen(key);
        TextT keyT;
        keyT.str = (char *)key;
        keyT.len = keyLen;
        TextT valueT;
        valueT.str = (char *)value;
        valueT.len = strlen(value);
        int32_t ret = QryTestKvDml(labelId, &labelName, &keyT, &valueT, NULL, MSG_OP_RPC_GET_KV);
        EXPECT_EQ(GMERR_OK, ret);
    }

    uint32_t GetKvCount(const char *key)
    {
        uint32_t labelId = 0;
        TextT labelName = DbStr2Text(kvTableName);
        uint32_t keyLen = strlen(key);
        TextT keyT;
        keyT.str = (char *)key;
        keyT.len = keyLen;
        FixBufferT *rsp = NULL;
        int32_t ret = QryTestKvDml(labelId, &labelName, &keyT, NULL, &rsp, MSG_OP_RPC_GET_KV_RECORD_COUNT);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        uint32_t count;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &count));
        QryReleaseRsp(rsp);
        return count;
    }

    uint32_t IsKvExist(const char *key)
    {
        uint32_t labelId = 0;
        TextT labelName = DbStr2Text(kvTableName);
        uint32_t keyLen = strlen(key);
        TextT keyT;
        keyT.str = (char *)key;
        keyT.len = keyLen;
        FixBufferT *rsp = NULL;
        int32_t ret = QryTestKvDml(labelId, &labelName, &keyT, NULL, &rsp, MSG_OP_RPC_IS_KV_EXIST);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        uint32_t exist;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &exist));
        QryReleaseRsp(rsp);
        return exist;
    }

    void DeleteKv(const char *key)
    {
        uint32_t labelId = 0;
        TextT labelName = DbStr2Text(kvTableName);
        uint32_t keyLen = strlen(key);
        TextT keyT;
        keyT.str = (char *)key;
        keyT.len = keyLen;
        FixBufferT *rsp = NULL;
        int32_t ret = QryTestKvDml(labelId, &labelName, &keyT, NULL, &rsp, MSG_OP_RPC_DELETE_KV);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        uint32_t exist;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &exist));
        EXPECT_EQ(true, exist);
        QryReleaseRsp(rsp);
    }

    void SetValidKvValue()
    {
        uint32_t ret;
        char *cfgJson = (char *)"{\"max_record_count\":1000}";
        char *tableName = kvTableValid;
        ret = QryTestCreateKvTable(tableName, cfgJson);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t labelId = 0;
        TextT labelName = {0};
        labelName.str = kvTableValid;
        labelName.len = strlen(kvTableValid) + 1;
        char *key = (char *)"hello";
        char value[7167] = {0};
        for (int i = 0; i < 7166; i++) {
            value[i] = '0';
        }
        uint32_t keyLen = strlen(key);
        uint32_t valueLen = strlen(value);
        EXPECT_EQ(7166U, valueLen);
        TextT keyT;
        keyT.str = key;
        keyT.len = keyLen;
        TextT valueT;
        valueT.str = value;
        valueT.len = valueLen;
        ret = QryTestKvDml(labelId, &labelName, &keyT, &valueT, NULL, MSG_OP_RPC_SET_KV);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void SetInvalidKvValue()
    {
        const uint32_t valueSize = MAX_VALUE_LEN;
        uint32_t ret;
        char *cfgJson = (char *)"{\"max_record_count\":1000}";
        char *tableName = kvTableInvalid;
        ret = QryTestCreateKvTable(tableName, cfgJson);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t labelId = 0;
        TextT labelName = {0};
        labelName.str = kvTableInvalid;
        labelName.len = strlen(kvTableInvalid) + 1;
        char *key = (char *)"hello";
        char *value = (char *)malloc(valueSize + 2);
        for (int i = 0; i < (int32_t)valueSize + 1; i++) {
            value[i] = '0';
        }
        value[valueSize + 1] = '\0';
        uint32_t keyLen = strlen(key);
        uint32_t valueLen = strlen(value);
        EXPECT_EQ(valueSize + 1, valueLen);
        TextT keyT;
        keyT.str = key;
        keyT.len = keyLen;
        TextT valueT;
        valueT.str = value;
        valueT.len = valueLen;
        ret = QryTestKvDml(labelId, &labelName, &keyT, &valueT, NULL, MSG_OP_RPC_SET_KV);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        char result[128];
        sprintf(result, "Program limit exceeded. key len: %" PRIu32 " limit:%" PRId32 ".", valueT.len, valueSize);
        EXPECT_STREQ(result, lastError1->str);
        free(value);
    }

    void TruncateKvTable(const char *tableName)
    {
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        TextT labelName = DbStr2Text((char *)tableName);
        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelName));
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_TRUNCATE_KV_TABLE,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackTruncateKvTableStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);

        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void DropKvTable(const char *tableName)
    {
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        TextT labelName = DbStr2Text((char *)tableName);
        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelName));
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_KV_TABLE,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackDropKvTableStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);

        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }
};

TEST_F(UtQueryDMLKv, QRY_DML_KvTable)
{
    SetToNotExistKvTable(kvTableName, "hello", "NotExist");

    CreateKvTable(kvTableName);

    int32_t ret;

    EXPECT_EQ(SetKv(kvTableName, "hello", "world"), GMERR_OK);

    EXPECT_EQ(SetKv(kvTableName, "hello", "world"), GMERR_OK);

    GetKv("hello", "world");

    EXPECT_EQ(GetKvCount("hello"), 1U);

    EXPECT_EQ(IsKvExist("hello"), true);

    DeleteKv("hello");

    EXPECT_EQ(SetKv(kvTableName, "hello2", "world2"), GMERR_OK);

    // fuzz测试异常报文
    EXPECT_EQ(SetKv(kvTableName, "hello11", "world11"), GMERR_OK);
    TextT labelName = DbStr2Text(kvTableName);
    FixBufferT *rsp = NULL;
    uint32_t labelId = 0;
    char *key = (char *)"hello11";
    uint32_t keyLen = strlen(key);
    TextT keyT;
    keyT.str = key;
    keyT.len = keyLen;
    ret = QryTestKvDmlFuzz(labelId, &labelName, &keyT, NULL, &rsp, MSG_OP_RPC_DELETE_KV);
    EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    TextT *lastError = DbGetLastErrorInfo();
    const char *result1 = (char *)"Object not in pre-required state. stmt status in exec.";
    EXPECT_STREQ(result1, lastError->str);
    QryReleaseRsp(rsp);

    EXPECT_EQ(SetKv(kvTableName, "hello3", "world3"), GMERR_OK);

    EXPECT_EQ(SetKv(kvTableName, "hello4", "world4"), GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result2 =
        (char *)"Record count limit exceeded. curPhyItemNum:2, curUserDefItemNum:2, insertNum:1, maxItemNum:2"
                "\nThe kv table kvTabelDml1 can't be set.";
    EXPECT_STREQ(result2, lastError1->str);

    SetToNotExistKvTable("kvTabelDml2", "hello5", "world5");

    SetValidKvValue();

    SetInvalidKvValue();

    TruncateKvTable(kvTableName);

    DropKvTable(kvTableName);
}
