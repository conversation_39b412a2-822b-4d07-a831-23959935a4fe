#include <limits.h>
#include <securec.h>
#include <stdlib.h>
#include <string>
#include "gtest/gtest.h"
#include "common_init.h"
#include "adpt_memory.h"
#include "adpt_atomic.h"
#include "db_common_init.h"
#include "db_mem_context.h"
#include "dm_meta_subs_in.h"
#include "ee_cmd.h"
#include "gmc.h"
#include "query_ut_base.h"
#include "ut_catalog_base.h"
#include "ut_dm_fuzz_test.h"
#include "ut_dm_common.h"

using namespace std;

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

DbMemCtxT *nspMemCtx = NULL;
class UtNamespaceCache : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        nspMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo((DbMemCtxT *)nspMemCtx);
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx((DbMemCtxT *)nspMemCtx);
        BaseUninit();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtNamespaceCache, CataCreateNspAndDrop)
{
    // 测试默认namespace是否初始化成功
    DmNamespaceT *defaultNsp = CreateNsp((DbMemCtxT *)nspMemCtx, 11, "public", "admin");
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, CataCreateNamespace(defaultNsp, NULL));

    // 构建namespace结构体
    DmNamespaceT *nsp = CreateNsp((DbMemCtxT *)nspMemCtx, 1, "nsp1", "user1");

    // 插入
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(nsp, NULL));
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, CataCreateNamespace(nsp, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName(nsp->metaCommon.metaName, NULL));
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, CataDropNamespaceByName("nsp1", NULL));

    // 使用
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, CataUseNamespace(11, NULL));
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, CataUnuseNamespace(11, NULL));
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(nsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(1, NULL));
    uint32_t useCount;
    EXPECT_EQ(GMERR_OK, CataGetNspUseCountById(1, &useCount));
    EXPECT_EQ((uint32_t)1, useCount);
    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(1, NULL));
    EXPECT_EQ(GMERR_OK, CataGetNspUseCountById(1, &useCount));
    EXPECT_EQ((uint32_t)0, useCount);
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nsp1", NULL));
}

#ifdef FEATURE_DATALOG
TEST_F(UtNamespaceCache, CreateAndDropByIdNspTest)
{
    // 构建namespace
    DmNamespaceT *nsp = CreateNsp((DbMemCtxT *)nspMemCtx, 23, "nsp123", "user123");

    // 插入namespace
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(nsp, NULL));

    // 构建src和dest vertexlabel，构建edgelabel关联
    DmVertexLabelT *srcVertexLabel;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(123, &srcVertexLabel));
    srcVertexLabel->metaCommon.namespaceId = 23;
    srcVertexLabel->metaCommon.version = 0;
    DmVertexLabelT *destVertexLabel;
    EXPECT_EQ(GMERR_OK, DmGetFuzzVertexLabel(134, &destVertexLabel));
    destVertexLabel->metaCommon.namespaceId = 23;
    destVertexLabel->metaCommon.version = 0;
    DmEdgeLabelT *edgeLabel;
    EXPECT_EQ(GMERR_OK, DmGetFuzzEdgeLabel(1234, srcVertexLabel, destVertexLabel, &edgeLabel));
    edgeLabel->metaCommon.namespaceId = 23;

    // 构建表订阅
    DmSubscriptionT *subs1 = (DmSubscriptionT *)DbDynMemAlloc(sizeof(DmSubscriptionT));
    DmSubscriptionT *subs2 = (DmSubscriptionT *)DbDynMemAlloc(sizeof(DmSubscriptionT));
    DmSubscriptionT *subs3 = (DmSubscriptionT *)DbDynMemAlloc(sizeof(DmSubscriptionT));
    (void)memset_s(subs1, sizeof(DmSubscriptionT), 0, sizeof(DmSubscriptionT));
    (void)memset_s(subs2, sizeof(DmSubscriptionT), 0, sizeof(DmSubscriptionT));
    (void)memset_s(subs3, sizeof(DmSubscriptionT), 0, sizeof(DmSubscriptionT));
    InitSubsWithName(nspMemCtx, subs1, 78, 123);
    InitSubsWithName(nspMemCtx, subs2, 89, 123);
    InitSubsWithName(nspMemCtx, subs3, 490, 134);
    subs1->metaCommon.namespaceId = 23;
    subs2->metaCommon.namespaceId = 23;
    subs3->metaCommon.namespaceId = 23;

    // 插入vertexlabel、edgelabel
    CataSetVertexLabelCanSub(srcVertexLabel, true);
    CataSetVertexLabelCanSub(destVertexLabel, true);
    DmVertexLabelT *tempLabel = NULL;
    EXPECT_EQ(GMERR_OK, CataSaveVertexLabel(srcVertexLabel, &tempLabel));
    tempLabel->metaCommon.isCreated = true;
    EXPECT_EQ(GMERR_OK, CataSaveVertexLabel(destVertexLabel, &tempLabel));
    tempLabel->metaCommon.isCreated = true;
    EXPECT_EQ(GMERR_OK, CataSaveEdgeLabel(edgeLabel));

    // 插入表订阅和path订阅
    if (DmVertexLabelIsDatalogLabel(destVertexLabel) &&
        destVertexLabel->metaVertexLabel->labelSubsType == LABEL_MESSAGE_QUEUE) {
        // Datalog表普通订阅只能有一个订阅者
        EXPECT_EQ(GMERR_OK, CataSaveSubscription(subs1));
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, CataSaveSubscription(subs2));
        EXPECT_EQ(GMERR_OK, CataSaveSubscription(subs3));
    } else {
        EXPECT_EQ(GMERR_OK, CataSaveSubscription(subs1));
        EXPECT_EQ(GMERR_OK, CataSaveSubscription(subs2));
        EXPECT_EQ(GMERR_OK, CataSaveSubscription(subs3));
    }

    // 尝试删除namespace,失败
    EXPECT_NE(GMERR_OK, CataDropNamespaceByName("nsp123", NULL));

    // 删除subs订阅, 分别根据labelId和labelName删除
    RemoveAllSubsByLabelId(NULL, 123, VERTEX_LABEL);
    RemoveAllSubsByLabelId(NULL, 134, VERTEX_LABEL);
    EXPECT_NE(GMERR_OK, CataDropNamespaceByName("nsp123", NULL));

    // 删除边标签
    EXPECT_EQ(GMERR_OK, CataRemoveEdgeLabelById(NULL, edgeLabel->metaCommon.metaId));
    EXPECT_NE(GMERR_OK, CataDropNamespaceByName("nsp123", NULL));

    // 删除点标签
    EXPECT_EQ(GMERR_OK, UtCataRemoveVertexLabelById(NULL, srcVertexLabel->metaCommon.metaId));
    EXPECT_EQ(GMERR_OK, UtCataRemoveVertexLabelById(NULL, destVertexLabel->metaCommon.metaId));

    // 删除namespace成功
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nsp123", NULL));
}
#endif

TEST_F(UtNamespaceCache, TestCreateNspExceedNum)
{
    DmNamespaceT *nsp = NULL;
    char nspName[3];
    char *nspNamePtr = nspName;
    for (uint32_t i = 10; i < 74; i++) {
        sprintf(nspNamePtr, "%d", i);
        nsp = CreateNsp((DbMemCtxT *)nspMemCtx, i, nspName, "user123");
        EXPECT_EQ(GMERR_OK, CataCreateNamespace(nsp, NULL));
    }
    nsp = CreateNsp((DbMemCtxT *)nspMemCtx, 75, "75", "user123");
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, CataCreateNamespace(nsp, NULL));
    for (uint32_t i = 10; i < 74; i++) {
        sprintf(nspNamePtr, "%d", i);
        EXPECT_EQ(GMERR_OK, CataDropNamespaceByName(nspName, NULL));
    }
}

TEST_F(UtNamespaceCache, TestBindNspToTsp)
{
    DmNamespaceT *nsp0 = CreateNsp((DbMemCtxT *)nspMemCtx, 0, "nsp1", "user123");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(nsp0, NULL));
    DmTablespaceT *tsp = CreateTsp((DbMemCtxT *)nspMemCtx, 22, "tsp1");
    EXPECT_EQ(GMERR_OK, CataCreateTsp(tsp));

    EXPECT_EQ(GMERR_OK, CataBindNsptoTsp((char *)"nsp1", (char *)"tsp1", NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nsp1", NULL));
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, CataBindNsptoTsp((char *)"nsp1", (char *)"tsp1", NULL));
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(nsp0, NULL));
    EXPECT_EQ(GMERR_OK, CataDropTspByName("tsp1", NULL));
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, CataBindNsptoTsp((char *)"nsp1", (char *)"tsp1", NULL));
    EXPECT_EQ(GMERR_OK, CataCreateTsp(tsp));
    EXPECT_EQ(GMERR_OK, CataBindNsptoTsp((char *)"nsp1", (char *)"tsp1", NULL));
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, CataBindNsptoTsp((char *)"nsp1", (char *)"tsp1", NULL));
}

TEST_F(UtNamespaceCache, loadSoAndDropNamespace)
{
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED,
        "Unsuccessful to find function's address, name=dtl_compare_tuple_A., ret = 1017001.");
    Status ret = CataDropNamespaceByName((char *)"public", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t nspId = 0;
    ret = CataGetNamespaceIdByName(NULL, (char *)"public", &nspId);
    EXPECT_NE(GMERR_OK, ret);
    char *expectError = (char *)"Undefined object. Get nsp:public without lock.";
    const char *lastError = GmcGetLastError();
    EXPECT_EQ(0, strcmp(expectError, lastError));
}
