#include "query_ut_base.h"
#include "cpl_public_verifier_dcl.h"
#include "cpl_public_verifier.h"
#include "db_text.h"
#include "dm_data_basic.h"
#include "dm_data_define.h"
#include "dm_meta_user.h"
#include "ee_dcl_desc.h"
#include "ee_ddl_role.h"
#include "query_ut_base.h"
#include "ut_cata_common.h"
#include "ee_cmd_namespace.h"
#include "ee_dcl_ctrl.h"

extern "C" Status GetPrivEntityByName(
    DbInstanceHdT dbInstance, const CataUserNameInfoT *userNameInfo, bool isUser, CataPrivEntityT **privEntity);
bool getMyRole = false;
class UtQueryDCLRole : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req;

    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        int32_t ret = QrySetCfg("userPolicyMode", "2");
        EXPECT_EQ(ret, GMERR_OK);
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

void setInfor(DrtConnectionT *conn, FixBufferT *req, TextT roleName, TextT processName)
{
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &roleName));
    EXPECT_EQ(GMERR_OK, FixBufPutText(req, &processName));

    uint32_t obj1 = CATA_NAMESPACE;
    uint32_t sys1 = CREATE_PRIV;
    sys1 |= DROP_PRIV;
    sys1 |= USE_PRIV;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, obj1));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(req, sys1));
}

int32_t CataLoginVerifyStub(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isSuper, bool *isGrouplog, CataRoleT *role)
{
    int32_t ret;
    if (getMyRole) {
        // 得到我自己的role
        ret = CataGetRoleSysPrivInfo("role", "process", true, role);
        if (ret != GMERR_OK) {
            *login = false;
        } else {
            *login = true;
        }
        *isSuper = false;
        getMyRole = false;
        return GMERR_OK;
    }
    // 正常流程
    DB_POINTER3(userNameInfo, login, role);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    // step 1 : 根据userName和processName判断能否登录
    CataUserT *user = NULL;
    ret = GetPrivEntityByName(NULL, userNameInfo, true, &user);
    if (ret == GMERR_OK) {
        *login = true;
        *isSuper = user->isDBA;
        ret = GetRoleSysPrivInfoNoLock(NULL, user->roleIds[0], role);
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        return ret;
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    // step 2 : 根据groupName和processName判断能否登录
    ret = CataGetRoleSysPrivInfo(userNameInfo->groupName, userNameInfo->processName, false, role);
    if (ret != GMERR_OK) {
        *login = false;
    } else {
        *login = true;
    }
    *isSuper = false;
    return GMERR_OK;
}
TEST_F(UtQueryDCLRole, QRY_DCL_OperateWithNoGrant)
{
    char role[] = "role";
    char process[] = "process";
    // 测试没有create namespace权限进行操作，预期失败
    CataUserNameInfoT info = {0};
    info.userName = role;
    info.processName = process;
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, role, NULL, process);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    userInfo.isReboot = false;
    int32_t ret = CataCreateUser(NULL, &userInfo);
    EXPECT_EQ(GMERR_OK, ret);
    DrtConnectionT *conn = NULL;
    getMyRole = true;  // 按照我的role来登录

    (void)setStubC((void *)CataLoginVerify, (void *)CataLoginVerifyStub);
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));

    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT namespaceName = {.len = strlen("namespace_create") + 1, .str = (char *)"namespace_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &namespaceName));

    TextT userName = {.len = strlen("user_create") + 1, .str = (char *)"user_create"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &userName));
    TextT tspName = {.len = strlen("public") + 1, .str = (char *)"public"};
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &tspName));
    // 直接进行操作不授予权限
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_TX_ISOLATION_DEFAULT));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, (uint32_t)GMC_DEFAULT_TRX));
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_NAMESPACE,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    QryTestReleaseSession(conn);

    ret = CataDropUser(&info, NULL);  // 创建完成之后进行删除
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDCLRole, QRY_DCL_Wildcard_GrantSysPrivsToUserWildcard)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    EXPECT_EQ(GMERR_OK, ret);
    QryGrantOrRevokeSysPrivsDescT *desc = NULL;
    desc = (QryGrantOrRevokeSysPrivsDescT *)DbDynMemCtxAlloc(
        session->currentStmt->memCtx, sizeof(QryGrantOrRevokeSysPrivsDescT));
    session->currentStmt->context->type = QRY_TYPE_GRANT_SYS_PRIVS;
    session->currentStmt->context->entry = (void *)desc;
    desc->userOrGrpName.str = (char *)"*";
    desc->processName.str = (char *)"*";
    desc->userOrGrpName.len = DM_STR_LEN(desc->userOrGrpName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objType = CATA_RESOURCE;
    desc->opType = 1;

    ret = QryVerifyGrantSysPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    desc->userOrGrpName.str = (char *)"*";
    desc->processName.str = (char *)"*";
    desc->userOrGrpName.len = DM_STR_LEN(desc->userOrGrpName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objType = CATA_VERTEX_LABEL;

    ret = QryVerifyGrantSysPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Feature is not supported. resource type only supports the user or group name:* and the process:*. objType:0.",
        lastError->str);

    desc->userOrGrpName.str = (char *)"*";
    desc->processName.str = (char *)"123";
    desc->userOrGrpName.len = DM_STR_LEN(desc->userOrGrpName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objType = CATA_RESOURCE;

    ret = QryVerifyGrantSysPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal name. The user, group or process name can not be *.", lastError->str);

    desc->userOrGrpName.str = (char *)"123";
    desc->processName.str = (char *)"*";
    desc->userOrGrpName.len = DM_STR_LEN(desc->userOrGrpName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objType = CATA_RESOURCE;

    ret = QryVerifyGrantSysPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal name. The user, group or process name can not be *.", lastError->str);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDCLRole, QRY_DCL_Wildcard_GrantObjPrivsToUserWildcard)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    EXPECT_EQ(GMERR_OK, ret);
    QryGrantOrRevokeObjPrivsDescT *desc = NULL;
    desc = (QryGrantOrRevokeObjPrivsDescT *)DbDynMemCtxAlloc(
        session->currentStmt->memCtx, sizeof(QryGrantOrRevokeObjPrivsDescT));
    desc->objPrivs = (CataObjPrivT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(CataObjPrivT));
    session->currentStmt->context->type = QRY_TYPE_GRANT_SYS_PRIVS;
    session->currentStmt->context->entry = (void *)desc;
    desc->userOrGroupName.str = (char *)"*";
    desc->userOrGroupName.len = DM_STR_LEN(desc->userOrGroupName.str);
    desc->processName.str = (char *)"*";
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objPrivs->objType = CATA_VERTEX_LABEL;
    desc->objPrivs->privileges = DM_OBJ_SELECT_PRIV;

    ret = QryVerifyGrantObjPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    desc->userOrGroupName.str = (char *)"*";
    desc->userOrGroupName.len = DM_STR_LEN(desc->userOrGroupName.str);
    desc->processName.str = (char *)"*";
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objPrivs->privileges = DM_OBJ_SELECT_PRIV | DM_OBJ_INSERT_PRIV;

    ret = QryVerifyGrantObjPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Not normal name. Only select priv supports the user or group name:* and process name:*.", lastError->str);

    desc->userOrGroupName.str = (char *)"*";
    desc->userOrGroupName.len = DM_STR_LEN(desc->userOrGroupName.str);
    desc->processName.str = (char *)"123";
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objPrivs->privileges = DM_OBJ_SELECT_PRIV | DM_OBJ_INSERT_PRIV;

    ret = QryVerifyGrantObjPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Not normal name. Select priv unsupport the user or group name:* and process name is not *.", lastError->str);

    desc->userOrGroupName.str = (char *)"*";
    desc->userOrGroupName.len = DM_STR_LEN(desc->userOrGroupName.str);
    desc->processName.str = (char *)"123";
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objPrivs->privileges = DM_OBJ_UPDATE_PRIV | DM_OBJ_INSERT_PRIV;

    ret = QryVerifyGrantObjPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);

    desc->userOrGroupName.str = (char *)"123";
    desc->processName.str = (char *)"*";
    desc->userOrGroupName.len = DM_STR_LEN(desc->userOrGroupName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objPrivs->privileges = DM_OBJ_UPDATE_PRIV | DM_OBJ_INSERT_PRIV;

    ret = QryVerifyGrantObjPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal name. inv user or group name: 123 and process name: *, * is wildcard.", lastError->str);

    desc->userOrGroupName.str = (char *)"123";
    desc->processName.str = (char *)"*";
    desc->userOrGroupName.len = DM_STR_LEN(desc->userOrGroupName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->objPrivs->privileges = DM_OBJ_SELECT_PRIV | DM_OBJ_INSERT_PRIV;

    ret = QryVerifyGrantObjPrivs(session->currentStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal name. inv user or group name: 123 and process name: *, * is wildcard.", lastError->str);

    QryTestReleaseSession(conn);
}

Status CataSetUserProtocolStub(
    const CataUserNameInfoT *userNameInfo, PrivProtocolTypeE protocolType, bool isEnable, DbInstanceHdT dbInstance)
{
    return GMERR_OK;
}

TEST_F(UtQueryDCLRole, QRY_DCL_SetUserProtocol)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    EXPECT_EQ(GMERR_OK, ret);

    QryIpsSetUserProtocolDescT *desc = NULL;
    desc = (QryIpsSetUserProtocolDescT *)DbDynMemCtxAlloc(
        session->currentStmt->memCtx, sizeof(QryIpsSetUserProtocolDescT));
    session->currentStmt->context->type = QRY_TYPE_IPS_SET_USER_PROTOCOL;
    session->currentStmt->context->entry = (void *)desc;
    desc->userName.str = (char *)"*";
    desc->processName.str = (char *)"*";
    desc->userName.len = DM_STR_LEN(desc->userName.str);
    desc->processName.len = DM_STR_LEN(desc->processName.str);
    desc->isEnable = true;
    desc->protocolType = PRIV_PROTOCOL_TYPE_DIRECT_READ;
    (void)setStubC((void *)CataSetUserProtocol, (void *)CataSetUserProtocolStub);
    ret = QryExecuteIpsSetUserProtocol(session->currentStmt);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}
