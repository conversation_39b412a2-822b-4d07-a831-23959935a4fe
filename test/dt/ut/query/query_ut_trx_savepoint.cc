/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: ir physical operator ut
 * Author: linhuabin
 * Create: 2022-09-13
 */
#include "query_ut_base.h"
#include "db_text.h"
#include "ee_dcl_desc.h"
#include "ee_dcl_ctrl.h"
#ifndef FEATURE_PERSISTENCE
static char *g_labelName = (char *)"labelvertex";
static DrtConnectionT *g_conn = NULL;
static FixBufferT *g_rsp = NULL;

class UtTrxSavepoint : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    static void SetUpTestCase()
    {
        BaseInit();
        (void)QryTestAllocSession(&g_conn, &g_rsp);
        SessionT *session = QryGetSessionById(g_conn->sessionId, false, NULL);
        (void)QryGetStmtById(session, 0, &session->currentStmt);
        (void)QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    }
    static void TearDownTestCase()
    {
        SessionT *session = QryGetSessionById(g_conn->sessionId, false, NULL);
        (void)QryGetStmtById(session, 0, &session->currentStmt);
        QryFreeCtx2CtxMemPool(session->currentStmt);
        QryReleaseStmtById(session, 0);
        QryReleaseRsp(g_rsp);
        QryTestReleaseSession(g_conn);
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    }
    virtual void TearDown()
    {
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

static void CreateVertexLabel()
{
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex",
        "id":123,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"int64"},
            {"name":"F3", "type":"string", "size":20, "nullable":true}
        ],
        "keys":
        [
            {"node":"labelvertex", "name":"T39_K0", "fields":["F0"],
             "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\": 0}";
    DbMemCtxT *dyAlgoCtxVertexBase = QryGetDyAlgoCtxVertexBase();
    FixBufferT req = {0};
    EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));

    uint32_t labelJsonLen = strlen(labelJson) + 1;
    uint32_t cfgJsonLen = strlen(cfgJson) + 1;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    TextT putText;
    putText.str = labelJson;
    putText.len = labelJsonLen;
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &putText));

    putText.str = cfgJson;
    putText.len = cfgJsonLen;
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &putText));

    // appointLabelName = NULL
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = g_conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    EXPECT_EQ(GMERR_OK, FastpathEntry(&serviceCtx, &procCtx));
    FixBufRelease(&req);
}

static void DropVertexLabel()
{
    g_gmdbLongTrxLogCount = 0;
    g_gmdbLongTrxRollbackCount = 0;
    DbMemCtxT *dyAlgoCtxVertexBase = QryGetDyAlgoCtxVertexBase();
    FixBufferT req = {0};
    EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));

    TextT labelName = {.len = (uint32_t)strlen(g_labelName) + 1, .str = g_labelName};

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &labelName));
    // isDropAssoc = 0
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_VERTEX_LABEL,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = g_conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    (void)FastpathEntry(&serviceCtx, &procCtx);
    FixBufRelease(&req);
}

TEST_F(UtTrxSavepoint, QRY_TRX_CreateVertexLabel)
{
    CreateVertexLabel();
    DropVertexLabel();
}

static Status InsertVertex(char *labelName, uint32_t propertyNum, char **propertyName, DmValueT *propertyValue)
{
    Status ret;
    DbMemCtxT *dyAlgoCtxVertexBase = QryGetDyAlgoCtxVertexBase();
    FixBufferT req = {0};
    ret = FixBufCreate(&req, dyAlgoCtxVertexBase, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmVertexLabelT *vertexLabel = NULL;
    uint32_t labelNameLen = 0;
    uint32_t vertexBufLen = 0;
    uint8_t *buf = NULL;
    if (labelName != NULL) {
        labelNameLen = strlen(labelName) + 1;
        // 从Catalog中查询得到相应点标签的元数据
        DmVertexT *vertex = NULL;
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)g_conn->session)->namespaceId, labelName);
        if (labelName != NULL) {
            CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
            DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)dyAlgoCtxVertexBase, vertexLabel, &vertex);
        }

        // set the porperty of vertex
        for (uint32_t i = 0; i < propertyNum; i++) {
            DmVertexSetPropeByName((char *)propertyName[i], propertyValue[i], vertex);
        }
        if (propertyNum != 0) {
            DmSerializeVertex(vertex, &buf, &vertexBufLen);
            DmDestroyVertex(vertex);
        }
    }

    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);

    InsertVertexMsgT insMsg;
    insMsg.labelId = vertexLabel != NULL ? vertexLabel->metaCommon.metaId : 0;
    insMsg.vertexBufText.str = (char *)buf;
    insMsg.vertexBufText.len = vertexBufLen;
    insMsg.labelName.str = labelName;
    insMsg.labelName.len = labelNameLen;
    insMsg.flag = 0x0007;
    insMsg.version = vertexLabel != NULL ? vertexLabel->metaCommon.version : DB_MAX_UINT32;
    insMsg.uuid = vertexLabel != NULL ? vertexLabel->metaVertexLabel->uuid : DB_MAX_UINT32;
    ret = UtSendInsertVertexMsg(g_conn, &req, &insMsg);
    if (vertexLabel != NULL) {
        CataReleaseVertexLabel(vertexLabel);
    }

    FixBufRelease(&req);
    clearAllStub();
    return ret;
}

static QryStmt *GetPessimisticTrxStmt(QryBeginTransDescT *desc)
{
    SessionT *session = QryGetSessionById(g_conn->sessionId, false, NULL);
    QryStmtT *stmt = session->currentStmt;
    desc->cfg.readOnly = false;
    desc->cfg.isLiteTrx = false;
    desc->cfg.isBackGround = false;
    desc->cfg.trxType = PESSIMISTIC_TRX;
    desc->cfg.isolationLevel = READ_COMMITTED;
    stmt->context->entry = (void *)desc;
    return stmt;
}

static QryStmt *GetOptimisticTrxStmt(QryBeginTransDescT *desc)
{
    SessionT *session = QryGetSessionById(g_conn->sessionId, false, NULL);
    QryStmtT *stmt = session->currentStmt;
    desc->cfg.readOnly = false;
    desc->cfg.isLiteTrx = false;
    desc->cfg.isBackGround = false;
    desc->cfg.trxType = OPTIMISTIC_TRX;
    desc->cfg.isolationLevel = REPEATABLE_READ;
    stmt->context->entry = (void *)desc;
    return stmt;
}

TEST_F(UtTrxSavepoint, QRY_TRX_InsertVertex1)
{
    CreateVertexLabel();

    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetPessimisticTrxStmt(&trxDesc);
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F3"};
    DmValueT propertyValue[4];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'a';
    propertyValue[2].type = DB_DATATYPE_INT64;
    propertyValue[2].value.longValue = 3;
    propertyValue[3].type = DB_DATATYPE_STRING;
    propertyValue[3].value.strAddr = (void *)"mongo";
    propertyValue[3].value.length = 6;

    ret = InsertVertex(g_labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();
}

DmNamespaceT *CreateNspTest(DbMemCtxT *memCtx, uint32_t nspId, const char *nspName, const char *ownerName)
{
    DmNamespaceT *nsp = NULL;
    EXPECT_EQ(GMERR_OK, DmCreateEmptyNamespace(memCtx, &nsp));
    nsp->metaCommon.metaId = nspId;
    nsp->metaCommon.metaName = (char *)nspName;
    nsp->owner = DbStr2Text((char *)ownerName);
    nsp->defaultTspId = PUBLIC_TABLESPACE_ID;
    nsp->trxInfo.isolationLevel = REPEATABLE_READ;
    nsp->trxInfo.trxType = OPTIMISTIC_TRX;
    return nsp;
}

TEST_F(UtTrxSavepoint, QRY_TRX_CreateSavepoint1)
{
    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetOptimisticTrxStmt(&trxDesc);
    DmNamespaceT *defaultNsp = CreateNspTest((DbMemCtxT *)stmt->memCtx, 88, "nspTest", "admin");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(defaultNsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(88, NULL));
    // 建表之前切换nsp
    stmt->session->namespaceId = 88;
    CreateVertexLabel();
    // 重刷事务类型
    stmt->context->entry = (void *)&trxDesc;
    // 开启事务
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F3"};
    DmValueT propertyValue[4];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'a';
    propertyValue[2].type = DB_DATATYPE_INT64;
    propertyValue[2].value.longValue = 3;
    propertyValue[3].type = DB_DATATYPE_STRING;
    propertyValue[3].value.strAddr = (void *)"mongo";
    propertyValue[3].value.length = 6;

    ret = InsertVertex(g_labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc = {.savepointName = {.len = 4, .str = (char *)"sp1"}};
    stmt->context->entry = (void *)&desc;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();

    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(88, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nspTest", NULL));
}

TEST_F(UtTrxSavepoint, QRY_TRX_InsertVertex2)
{
    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetOptimisticTrxStmt(&trxDesc);
    DmNamespaceT *defaultNsp = CreateNspTest((DbMemCtxT *)stmt->memCtx, 88, "nspTest", "admin");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(defaultNsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(88, NULL));
    // 建表之前切换nsp
    stmt->session->namespaceId = 88;
    CreateVertexLabel();
    // 重刷事务类型
    stmt->context->entry = (void *)&trxDesc;
    // 开启事务
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F3"};
    DmValueT propertyValue[4];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 500;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'v';
    propertyValue[2].type = DB_DATATYPE_INT64;
    propertyValue[2].value.longValue = 6;
    propertyValue[3].type = DB_DATATYPE_STRING;
    propertyValue[3].value.strAddr = (void *)"redis";
    propertyValue[3].value.length = 6;

    ret = InsertVertex(g_labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();
    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(88, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nspTest", NULL));
}

TEST_F(UtTrxSavepoint, QRY_TRX_CreateSavepoint2)
{
    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetOptimisticTrxStmt(&trxDesc);
    DmNamespaceT *defaultNsp = CreateNspTest((DbMemCtxT *)stmt->memCtx, 88, "nspTest", "admin");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(defaultNsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(88, NULL));
    // 建表之前切换nsp
    stmt->session->namespaceId = 88;
    CreateVertexLabel();
    // 重刷事务类型
    stmt->context->entry = (void *)&trxDesc;
    // 开启事务
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc = {.savepointName = {.len = 4, .str = (char *)"sp2"}};
    stmt->context->entry = (void *)&desc;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();
    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(88, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nspTest", NULL));
}

TEST_F(UtTrxSavepoint, QRY_TRX_ReleaseSavepoint)
{
    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetOptimisticTrxStmt(&trxDesc);
    DmNamespaceT *defaultNsp = CreateNspTest((DbMemCtxT *)stmt->memCtx, 88, "nspTest", "admin");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(defaultNsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(88, NULL));
    // 建表之前切换nsp
    stmt->session->namespaceId = 88;
    CreateVertexLabel();
    // 重刷事务类型
    stmt->context->entry = (void *)&trxDesc;
    // 开启事务
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc = {.savepointName = {.len = 4, .str = (char *)"sp2"}};
    stmt->context->entry = (void *)&desc;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteReleaseSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();
    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(88, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nspTest", NULL));
}

TEST_F(UtTrxSavepoint, QRY_TRX_RollbackSavepoint)
{
    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetOptimisticTrxStmt(&trxDesc);
    DmNamespaceT *defaultNsp = CreateNspTest((DbMemCtxT *)stmt->memCtx, 88, "nspTest", "admin");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(defaultNsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(88, NULL));
    // 建表之前切换nsp
    stmt->session->namespaceId = 88;
    CreateVertexLabel();
    // 重刷事务类型
    stmt->context->entry = (void *)&trxDesc;
    // 开启事务
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F3"};
    DmValueT propertyValue[4];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'a';
    propertyValue[2].type = DB_DATATYPE_INT64;
    propertyValue[2].value.longValue = 3;
    propertyValue[3].type = DB_DATATYPE_STRING;
    propertyValue[3].value.strAddr = (void *)"mongo";
    propertyValue[3].value.length = 6;

    ret = InsertVertex(g_labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc1 = {.savepointName = {.len = 4, .str = (char *)"sp1"}};
    stmt->context->entry = (void *)&desc1;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc2 = {.savepointName = {.len = 4, .str = (char *)"sp2"}};
    stmt->context->entry = (void *)&desc2;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    stmt->context->entry = (void *)&desc1;
    ret = QryExecuteRollbackToSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();
    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(88, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nspTest", NULL));
}

TEST_F(UtTrxSavepoint, QRY_TRX_ReleaseInexistSavepoint)
{
    QryBeginTransDescT trxDesc = {0};
    QryStmtT *stmt = GetOptimisticTrxStmt(&trxDesc);
    DmNamespaceT *defaultNsp = CreateNspTest((DbMemCtxT *)stmt->memCtx, 88, "nspTest", "admin");
    EXPECT_EQ(GMERR_OK, CataCreateNamespace(defaultNsp, NULL));
    EXPECT_EQ(GMERR_OK, CataUseNamespace(88, NULL));
    // 建表之前切换nsp
    stmt->session->namespaceId = 88;
    CreateVertexLabel();
    // 重刷事务类型
    stmt->context->entry = (void *)&trxDesc;
    // 开启事务
    Status ret = QryExecuteBeginTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F3"};
    DmValueT propertyValue[4];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'a';
    propertyValue[2].type = DB_DATATYPE_INT64;
    propertyValue[2].value.longValue = 3;
    propertyValue[3].type = DB_DATATYPE_STRING;
    propertyValue[3].value.strAddr = (void *)"mongo";
    propertyValue[3].value.length = 6;

    ret = InsertVertex(g_labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc1 = {.savepointName = {.len = 4, .str = (char *)"sp1"}};
    stmt->context->entry = (void *)&desc1;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    QrySavepointDescT desc2 = {.savepointName = {.len = 4, .str = (char *)"sp2"}};
    stmt->context->entry = (void *)&desc2;
    ret = QryExecuteCreateSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    stmt->context->entry = (void *)&desc1;
    ret = QryExecuteRollbackToSavepoint(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    stmt->context->entry = (void *)&desc2;
    ret = QryExecuteReleaseSavepoint(stmt);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    ret = QryExecuteCommitTrans(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    DropVertexLabel();
    EXPECT_EQ(GMERR_OK, CataUnuseNamespace(88, NULL));
    EXPECT_EQ(GMERR_OK, CataDropNamespaceByName("nspTest", NULL));
}
#endif
