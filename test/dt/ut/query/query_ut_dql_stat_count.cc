#include "query_ut_base.h"
#include "db_text.h"

static char *labelName = (char *)"labelvertex1";
static char *tableName = (char *)"student";
class UtQueryGetStatCount : public testing::Test {
protected:
    static DbMemCtxT *dyAlgoCtxVertex;
    static DbMemCtxT *old;
    static FixBufferT req;

    static void SetUpTestCase()
    {
        BaseInit();
        int32_t ret = QrySetCfg("enableDmlPerfStat", "1");
        EXPECT_EQ(GMERR_OK, ret);
        char *cfgJson1 = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
        ret = QryTestCreateBaseVertexLabelFirst(cfgJson1);
        EXPECT_EQ(GMERR_OK, ret);
        char *cfgJson2 = (char *)"{\"max_record_count\":1000}";
        ret = QryTestCreateKvTable(tableName, cfgJson2);
        EXPECT_EQ(GMERR_OK, ret);

        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        PrepareVertex();
        PrepareKV();
    }

    static void TearDownTestCase()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();

        int32_t ret = QrySetCfg("enableDmlPerfStat", "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = QryTestDropVertexLabel(labelName, false);
        EXPECT_EQ(GMERR_OK, ret);
        ret = QryTestDropKvTable(tableName);
        EXPECT_EQ(GMERR_OK, ret);
        BaseUninit();
    };

    virtual void SetUp()
    {}

    virtual void TearDown()
    {}

    virtual int AllocObjects()
    {
        return 0;
    }

    virtual void FreeObjects()
    {}

    static void PrepareVertex()
    {
        uint32_t ret;
        char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F3", (char *)"F5"};
        DmValueT propertyValue[4];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 300;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = 'a';
        propertyValue[2].type = DB_DATATYPE_FLOAT;
        propertyValue[2].value.floatValue = 3.00;
        propertyValue[3].type = DB_DATATYPE_STRING;
        propertyValue[3].value.strAddr = (void *)"xyz";
        propertyValue[3].value.length = 4;
        ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
        EXPECT_EQ(GMERR_OK, ret);
        char *propertyName2[2] = {(char *)"F1", (char *)"F3"};
        DmValueT propertyValue2[2];
        propertyValue2[0].type = DB_DATATYPE_CHAR;
        propertyValue2[0].value.charValue = 'b';
        propertyValue2[1].type = DB_DATATYPE_FLOAT;
        propertyValue2[1].value.floatValue = 4.00;
        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 300;
        ret = QryTestUpdateVertex(labelName, indexName, 1, indexPropertyValue, sizeof(propertyName2) / sizeof(char *),
            propertyName2, propertyValue2, 0);
        char *propertyName3[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
        DmValueT propertyValue3[3];
        propertyValue3[0].type = DB_DATATYPE_UINT32;
        propertyValue3[0].value.uintValue = 800;
        propertyValue3[1].type = DB_DATATYPE_CHAR;
        propertyValue3[1].value.charValue = 'i';
        propertyValue3[2].type = DB_DATATYPE_FLOAT;
        propertyValue3[2].value.floatValue = 33.00;
        ret = QryTestReplaceOrMergeVertex(labelName, sizeof(propertyName3) / sizeof(char *), propertyName3,
            propertyValue3, MSG_OP_RPC_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = QryTestReplaceOrMergeVertex(
            labelName, sizeof(propertyName3) / sizeof(char *), propertyName3, propertyValue3, MSG_OP_RPC_MERGE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        DmValueT indexPropertyValue4[1];
        indexPropertyValue4[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue4[0].value.uintValue = 300;
        ret = QryTestDeleteVertex(labelName, indexName, 1, indexPropertyValue4, NULL, 0, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void PrepareKV()
    {
        uint32_t labelId = 0;
        TextT labelName = {0};
        labelName.str = tableName;
        labelName.len = strlen(tableName) + 1;
        char *key = (char *)"hello";
        char *value = (char *)"world";
        uint32_t keyLen = strlen(key);
        uint32_t valueLen = strlen(value);
        TextT keyT;
        keyT.str = key;
        keyT.len = keyLen;
        TextT valueT;
        valueT.str = value;
        valueT.len = valueLen;
        int32_t ret = QryTestKvDml(labelId, &labelName, &keyT, &valueT, NULL, MSG_OP_RPC_SET_KV);
        EXPECT_EQ(GMERR_OK, ret);
        ret = QryTestKvDml(labelId, &labelName, &keyT, NULL, NULL, MSG_OP_RPC_DELETE_KV);
        EXPECT_EQ(GMERR_OK, ret);
    }
};

DbMemCtxT *UtQueryGetStatCount::dyAlgoCtxVertex = NULL;
DbMemCtxT *UtQueryGetStatCount::old = NULL;
FixBufferT UtQueryGetStatCount::req = {};

// 单个vertexLabel
int32_t DrtConnWritePackGetStatCountStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    SessionT *session = (SessionT *)conn->session;
    FixBufferT *rsp = QrySessionGetRsp(session);
    FixBufSeek(rsp, 0);
    MsgHeaderT *header = RpcGetMsgHeader(rsp);
    (void)RpcGetOpHeader(rsp);
    EXPECT_EQ(GMERR_OK, header->opStatus);
    if (header->opStatus == GMERR_OK) {
        QryTestSetCurStmtID(header->stmtId);
    }
    uint32_t num;
    FixBufGetUint32(rsp, &num);
    printf("num:%d\n", num);
    uint32_t validOpNum;
    uint16_t validOp;
    uint64_t successCnt;
    uint64_t failCnt;
    for (uint32_t i = 0; i < num; i++) {
        FixBufGetUint32(rsp, &validOpNum);
        EXPECT_EQ(5, (int32_t)validOpNum);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(INSERT_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(DELETE_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(UPDATE_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(REPLACE_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(MERGE_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
    }
    return GMERR_OK;
}

TEST_F(UtQueryGetStatCount, QRY_DQL_GetStatCount)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    FixBufPutUint32(&req, 1);
    putText.str = labelName;
    putText.len = labelNameLen;
    FixBufPutUint32(&req, 0);
    FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_STAT_COUNT,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackGetStatCountStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);
}

int32_t DrtConnWritePackGetKvStatCountStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    SessionT *session = (SessionT *)conn->session;
    FixBufferT *rsp = QrySessionGetRsp(session);
    FixBufSeek(rsp, 0);
    MsgHeaderT *header = RpcGetMsgHeader(rsp);
    (void)RpcGetOpHeader(rsp);
    EXPECT_EQ(GMERR_OK, header->opStatus);
    if (header->opStatus == GMERR_OK) {
        QryTestSetCurStmtID(header->stmtId);
    }
    uint32_t num;
    FixBufGetUint32(rsp, &num);
    printf("num:%d\n", num);
    uint32_t validOpNum;
    uint16_t validOp;
    uint64_t successCnt;
    uint64_t failCnt;
    for (uint32_t i = 0; i < num; i++) {
        FixBufGetUint32(rsp, &validOpNum);
        EXPECT_EQ(2, (int32_t)validOpNum);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(TIME_STATIS_END + KV_SET_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
        FixBufGetUint16(rsp, &validOp);
        EXPECT_EQ(TIME_STATIS_END + KV_REMOVE_TIME_STATIS, validOp);
        FixBufGetUint64(rsp, &successCnt);
        EXPECT_EQ(1u, successCnt);
        FixBufGetUint64(rsp, &failCnt);
        EXPECT_EQ(0u, failCnt);
    }
    return GMERR_OK;
}

TEST_F(UtQueryGetStatCount, QRY_DQL_GetStatKvCount)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t tabelNameLen = strlen(tableName) + 1;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    FixBufPutUint32(&req, 1);
    putText.str = tableName;
    putText.len = tabelNameLen;
    FixBufPutUint32(&req, 0);
    FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_STAT_COUNT,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackGetKvStatCountStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);
}
