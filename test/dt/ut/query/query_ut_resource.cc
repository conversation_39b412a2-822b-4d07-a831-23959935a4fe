#include "query_ut_base.h"
#include "db_text.h"
#include "adpt_sleep.h"
#include "se_resource_column.h"
#include "dm_meta_res_col_pool.h"
#include "ee_check.h"
#include "ee_feature_import.h"
#include "srv_data_service.h"

#ifndef FEATURE_PERSISTENCE
static char *resourcePoolName = (char *)"res1";
static char *resourcePoolName2 = (char *)"res2";
static char *resourceLabelName = (char *)"labelvertex_res";

class UtQueryResource : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    }
    virtual void TearDown()
    {
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}

    Status ResColSetResId(ResId *resId, uint32_t poolId, uint32_t count, uint32_t start)
    {
        if (poolId > SE_RES_POOL_MAX_POOL_ID || count > SE_RES_POOL_MAX_RES_COUNT) {
            DB_LOG_ERROR(GMERR_RESOURCE_POOL_ERROR,
                "Invalid res id [poolId: %" PRIu32 " count: %" PRIu32 " statrIndex:%" PRIu32 "]", poolId, count, start);
            return GMERR_RESOURCE_POOL_ERROR;
        }
        DmResColSetResId(resId, (uint16_t)poolId, (uint16_t)count, start);
        return GMERR_OK;
    }

    void CreateResourcePool1()
    {
        uint32_t ret;
        char *resourceJson = (char *)R"({
        "name": "res1",
        "pool_id" : 0,
        "start_id" : 1,
        "capacity" : 2000,
        "order": 0,
        "alloc_type" : 0
    })";
        uint32_t resourceJsonLen = strlen(resourceJson) + 1;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourceJson;
        putText.len = resourceJsonLen;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void GetResourcePool1()
    {
        uint32_t ret;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = (char *)"res1";
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_GET_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);

        FixBufferT *rsp = NULL;
        ret = QryTestServiceProcReqEntryGetRsp(conn, msgHeader, &req, NULL, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        TextT getText;
        FixBufSeek(rsp, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
        ret = FixBufGetObject(rsp, &getText);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)312, getText.len);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void CreateResourcePool2()
    {
        uint32_t ret;
        char *resourceJson2 = (char *)R"({
        "name": "res2",
        "pool_id" : 1,
        "start_id" : 1,
        "capacity" : 2000,
        "order": 0,
        "alloc_type" : 0
    })";
        uint32_t resourceJsonLen2 = strlen(resourceJson2) + 1;

        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourceJson2;
        putText.len = resourceJsonLen2;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void BindExtendedResPool()
    {
        uint32_t ret;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourcePoolName;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        putText.str = resourcePoolName2;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_BIND_EXTENDED_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void UnbindExtendedResPool()
    {
        uint32_t ret;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourcePoolName;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void CreateVertexLabel1()
    {
        uint32_t ret;
        char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":1}";
        char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex_res",
        "id":123,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F81", "type":"resource", "nullable": false},
            {"name":"F82", "type":"resource", "nullable": false},
            {"name":"F83", "type":"resource", "nullable": false}
        ],
        "keys":
        [
            {"node":"labelvertex_res", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

        ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void InsertNullResCol()
    {
        char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F2"};
        DmValueT propertyValue[3];

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 500;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'e';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = true;

        uint32_t ret =
            QryTestInsertVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
        EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        const char *result = (char *)"Resource pool unsucc. ResourcePool is not bound.";
        EXPECT_STREQ(result, lastError1->str);
    }

    void UpdateResCol()
    {
        char *propertyName[5] = {(char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[5];

        ResId resid;
        ResColSetResId(&resid, DM_RES_POOL_MAX_POOL_ID, 1, SE_RES_POOL_MAX_START_INDEX);

        propertyValue[0].type = DB_DATATYPE_CHAR;
        propertyValue[0].value.intValue = 'e';
        propertyValue[1].type = DB_DATATYPE_BOOL;
        propertyValue[1].value.boolValue = true;

        propertyValue[2].type = DB_DATATYPE_RESOURCE;
        propertyValue[2].value.ulongValue = (uint64_t)resid;
        propertyValue[3].type = DB_DATATYPE_RESOURCE;
        propertyValue[3].value.ulongValue = (uint64_t)resid;
        propertyValue[4].type = DB_DATATYPE_RESOURCE;
        propertyValue[4].value.ulongValue = (uint64_t)resid;

        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 300;

        uint32_t ret = QryTestUpdateVertex(resourceLabelName, indexName, 1, indexPropertyValue,
            sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, 0);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        const char *result = (char *)"Feature is not supported. Resource property can not modify.";
        EXPECT_STREQ(result, lastError1->str);
    }

    void BindVertexLabel()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourcePoolName;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        putText.str = resourceLabelName;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
    }

    void CreateResourcePool3()
    {
        uint32_t ret;
        char *resourceJson2 = (char *)R"({
        "name": "res3",
        "pool_id" : 2,
        "start_id" : 1,
        "capacity" : 2000,
        "order": 0,
        "alloc_type" : 0
    })";
        uint32_t resourceJsonLen2 = strlen(resourceJson2) + 1;

        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourceJson2;
        putText.len = resourceJsonLen2;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void InsertVertex()
    {
        char *propertyName[6] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[6];

        ResId resid;
        ResColSetResId(&resid, DM_RES_POOL_MAX_POOL_ID, 1, SE_RES_POOL_MAX_START_INDEX);

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 600;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'a';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = true;

        ResColSetResId(&resid, 0, 1, 2);
        propertyValue[3].type = DB_DATATYPE_RESOURCE;
        propertyValue[3].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 3);
        propertyValue[4].type = DB_DATATYPE_RESOURCE;
        propertyValue[4].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 4);
        propertyValue[5].type = DB_DATATYPE_RESOURCE;
        propertyValue[5].value.ulongValue = (uint64_t)resid;

        FixBufferT *rsp = NULL;
        uint32_t ret = QryTestInsertVertex(
            resourceLabelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        RpcSeekFirstOpMsg(rsp);
        uint32_t affectRows;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)1, affectRows);
        uint32_t resGroupCnt = 0;
        ret = FixBufGetUint32(rsp, &resGroupCnt);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)1, resGroupCnt);

        TextT vertexLabelName = {0};
        ret = FixBufGetText(rsp, &vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        if (strcmp(resourceLabelName, vertexLabelName.str) == 0) {
            EXPECT_EQ(0, 0);
        } else {
            EXPECT_EQ(1, 0);
        }

        uint32_t resIdCount = 0;
        ret = FixBufGetUint32(rsp, &resIdCount);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)3, resIdCount);
        QryReleaseRsp(rsp);
    }

    void Replace1()
    {
        char *propertyName[6] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[6];
        ResId resid;

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 600;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'c';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = true;

        ResColSetResId(&resid, 0, 1, 2);
        propertyValue[3].type = DB_DATATYPE_RESOURCE;
        propertyValue[3].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 3);
        propertyValue[4].type = DB_DATATYPE_RESOURCE;
        propertyValue[4].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 60);
        propertyValue[5].type = DB_DATATYPE_RESOURCE;
        propertyValue[5].value.ulongValue = (uint64_t)resid;

        uint32_t ret = QryTestReplaceOrMergeVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    void Replace2()
    {
        char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F2"};
        DmValueT propertyValue[3];

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 600;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'c';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = true;

        uint32_t ret = QryTestReplaceOrMergeVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    }

    void Replace3()
    {
        char *propertyName[6] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[6];
        ResId resid;

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 600;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'a';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = false;

        propertyValue[3].type = DB_DATATYPE_RESOURCE;
        propertyValue[3].value.ulongValue = DB_INVALID_UINT64;
        ResColSetResId(&resid, 0, 1, 3);
        propertyValue[4].type = DB_DATATYPE_RESOURCE;
        propertyValue[4].value.ulongValue = (uint64_t)resid;
        propertyValue[5].type = DB_DATATYPE_RESOURCE;
        propertyValue[5].value.ulongValue = DB_INVALID_UINT64;

        uint32_t ret = QryTestReplaceOrMergeVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    void Replace4()
    {
        char *propertyName[6] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[6];
        ResId resid;

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 600;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'd';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = false;

        ResColSetResId(&resid, 0, 1, 2);
        propertyValue[3].type = DB_DATATYPE_RESOURCE;
        propertyValue[3].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 3);
        propertyValue[4].type = DB_DATATYPE_RESOURCE;
        propertyValue[4].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 4);
        propertyValue[5].type = DB_DATATYPE_RESOURCE;
        propertyValue[5].value.ulongValue = (uint64_t)resid;

        FixBufferT *rsp = NULL;
        uint32_t ret = QryTestReplaceOrMergeVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        FixBufSeek(rsp, 0);
        MsgHeaderT *header = (MsgHeaderT *)RpcPeekMsgHeader(rsp);
        uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
        EXPECT_EQ((uint32_t)8, datalen);
        uint32_t affectRows, group;
        RpcSeekFirstOpMsg(rsp);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)2, affectRows);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &group));
        EXPECT_EQ((uint32_t)0, group);
        QryReleaseRsp(rsp);
    }

    void ReplaceOrMergeVertex()
    {
        char *propertyName[6] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[6];
        ResId resid;

        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.intValue = 700;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.intValue = 'b';
        propertyValue[2].type = DB_DATATYPE_BOOL;
        propertyValue[2].value.boolValue = true;

        ResColSetResId(&resid, 0, 1, 5);
        propertyValue[3].type = DB_DATATYPE_RESOURCE;
        propertyValue[3].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 6);
        propertyValue[4].type = DB_DATATYPE_RESOURCE;
        propertyValue[4].value.ulongValue = (uint64_t)resid;
        ResColSetResId(&resid, 0, 1, 7);
        propertyValue[5].type = DB_DATATYPE_RESOURCE;
        propertyValue[5].value.ulongValue = (uint64_t)resid;

        FixBufferT *rsp = NULL;
        uint32_t ret = QryTestReplaceOrMergeVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        FixBufSeek(rsp, 0);
        MsgHeaderT *header = (MsgHeaderT *)RpcPeekMsgHeader(rsp);
        uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
        EXPECT_EQ((uint32_t)56, datalen);

        uint32_t affectRows, group, resCount;
        uint64_t resId1, resId2, resId3;
        TextT rspLabelName;
        RpcSeekFirstOpMsg(rsp);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)1, affectRows);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &group));
        EXPECT_EQ((uint32_t)1, group);
        EXPECT_EQ(GMERR_OK, FixBufGetText(rsp, &rspLabelName));
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &resCount));
        EXPECT_EQ((uint32_t)3, resCount);
        EXPECT_EQ(GMERR_OK, FixBufGetUint64(rsp, &resId1));
        EXPECT_EQ((uint64_t)5, DmResColGetStartIndexFromResId(resId1));
        EXPECT_EQ(GMERR_OK, FixBufGetUint64(rsp, &resId2));
        EXPECT_EQ((uint64_t)6, DmResColGetStartIndexFromResId(resId2));
        EXPECT_EQ(GMERR_OK, FixBufGetUint64(rsp, &resId3));
        EXPECT_EQ((uint64_t)7, DmResColGetStartIndexFromResId(resId3));

        ret = QryTestReplaceOrMergeVertex(resourceLabelName, sizeof(propertyName) / sizeof(char *), propertyName,
            propertyValue, MSG_OP_RPC_MERGE_VERTEX);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        const char *result =
            (char *)"Feature is not supported. Label labelvertex_res with resource field can't be merged.";
        EXPECT_STREQ(result, lastError1->str);
    }

    // 此时表中有三条数据，测试delete，只删除一条
    void DeleteVertex()
    {
        uint32_t ret;
        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 600;

        ret = QryTestDeleteVertex(
            resourceLabelName, indexName, 1, indexPropertyValue, NULL, QRY_AUTO_DEL_RELATION_VERTEX, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void InsertVertex2()
    {
        char *propertyName[6] = {(char *)"F0", (char *)"F1", (char *)"F2", (char *)"F81", (char *)"F82", (char *)"F83"};
        DmValueT propertyValue[6];

        for (int32_t i = 1; i <= 100; i++) {
            ResId resid;
            ResColSetResId(&resid, DM_RES_POOL_MAX_POOL_ID, 1, SE_RES_POOL_MAX_START_INDEX);

            propertyValue[0].type = DB_DATATYPE_UINT32;
            propertyValue[0].value.intValue = i;
            propertyValue[1].type = DB_DATATYPE_CHAR;
            propertyValue[1].value.intValue = 'c';
            propertyValue[2].type = DB_DATATYPE_BOOL;
            propertyValue[2].value.boolValue = true;

            propertyValue[3].type = DB_DATATYPE_RESOURCE;
            propertyValue[3].value.ulongValue = (uint64_t)resid;
            propertyValue[4].type = DB_DATATYPE_RESOURCE;
            propertyValue[4].value.ulongValue = (uint64_t)resid;
            propertyValue[5].type = DB_DATATYPE_RESOURCE;
            propertyValue[5].value.ulongValue = (uint64_t)resid;

            uint32_t ret = QryTestInsertVertex(
                resourceLabelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    uint32_t QryTestTruncateVertexLabelBackGround(FixBufferT *req, TextT *putText)
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        ret = FixBufPutText(req, putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_TRUNCATE_VERTEX_LABEL_BACKGROUND,
            req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        QryTestReleaseSession(conn);
        return ret;
    }

    // 删除表中所有数据
    void TruncateVertexLabel()
    {
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        uint32_t ret;
        TextT putText;
        putText.str = (char *)"labelvertex_res";
        putText.len = strlen(putText.str) + 1;
        ret = QryTestTruncateVertexLabelBackGround(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);
        FixBufRelease(&req);
    }

    // 对账状态时，truncate失败
    void TruncateVertexLabelWithChecking()
    {
        uint32_t ret;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DmVertexLabelT *vertexLabel = NULL;
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, resourceLabelName);
        ret = (Status)CataGetNamespaceIdByName(NULL, "public", &cataKey.nspId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = (Status)CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
        EXPECT_EQ(GMERR_OK, ret);

        ret = QryTestBeginCheck(vertexLabel->metaCommon.metaId, DB_INVALID_UINT8);
        EXPECT_EQ(GMERR_OK, ret);

        TextT putText;
        putText.str = (char *)"labelvertex_res";
        putText.len = strlen(putText.str) + 1;
        ret = QryTestTruncateVertexLabelBackGround(&req, &putText);
        DbSleep(100);
        EXPECT_EQ(GMERR_OK, ret);
        FixBufRelease(&req);
    }

    void UnbindVertexLabel()
    {
        uint32_t ret;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourceLabelName;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }

    void DropResourcePool()
    {
        uint32_t ret;
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;

        TextT putText;
        putText.str = resourcePoolName;
        putText.len = strlen(putText.str) + 1;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_RES_POOL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        FixBufRelease(&req);
    }
};

TEST_F(UtQueryResource, WholeProcess)
{
    QrySetCfg("directWrite", "0");
    QryAgedMgrT agedMgrLoacal = {0};
    QryAgedMgrT *gmdbAgedMgr = GetQryAgedMgrImpl();
    *gmdbAgedMgr = agedMgrLoacal;
    uint32_t ret = QryCreateAgedService();
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_NE(gmdbAgedMgr->memCtx, (DbMemCtxT *)NULL);

    CreateResourcePool1();
    GetResourcePool1();
    CreateResourcePool2();
    BindExtendedResPool();
    UnbindExtendedResPool();
    CreateVertexLabel1();
    InsertNullResCol();
    UpdateResCol();
    BindVertexLabel();
    CreateResourcePool3();
    InsertVertex();
    Replace1();
    Replace2();
    Replace3();
    Replace4();
    ReplaceOrMergeVertex();
    DeleteVertex();
    InsertVertex2();
    TruncateVertexLabel();
    TruncateVertexLabelWithChecking();
    UnbindVertexLabel();
    DropResourcePool();
}
#endif
