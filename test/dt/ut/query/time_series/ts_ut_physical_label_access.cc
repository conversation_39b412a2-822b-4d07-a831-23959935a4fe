/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: test for physical label access method
 * Author: tangjunnong
 * Create: 2023-08-17
 */

// 目前的 cu data 数据内容构造为随机值
#include "gtest/gtest.h"
#include "stub.h"
#include "adpt_string.h"
#include "cpl_ts_compiler.h"
#include "query_ut_base.h"
#include "ut_ts_common.h"
#include "dm_cu_physical_label.h"
#include "cpl_public_parser.h"
#include "ee_physical_label_access_method.h"
#include "ee_time_partition_util.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

typedef struct DataArr {
    uint32_t batchNum;
    uint32_t colNum;
    DmCuDescT **cuDescArr;
    DmCuT **cuArr;
} DataArrT;

static DbMemCtxT *g_ts_memCtx = NULL;
static SessionT *g_ts_session = NULL;
static QryStmtT *g_ts_qryStmt = NULL;
static char *g_cstoreFileDir = NULL;

static Status CreatePhysicalLabel(uint32_t logicTblId, int64_t upperBound, DmVertexLabelT **pPhysicTbl)
{
    QryCreateVertexLabelDescT *desc = NULL;
    desc = (QryCreateVertexLabelDescT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(QryCreateVertexLabelDescT));
    memset_s(desc, sizeof(QryCreateVertexLabelDescT), 0, sizeof(QryCreateVertexLabelDescT));
    char physicalLabelJson[PHYSICAL_LABEL_JSON_MAX_LEN];
    int32_t length = snprintf_s(physicalLabelJson, PHYSICAL_LABEL_JSON_MAX_LEN, PHYSICAL_LABEL_JSON_MAX_LEN - 1,
        PHYSIC_TBL_JSON_PATTERN, logicTblId, upperBound, CU_MIN_MAX_LEN, CU_MIN_MAX_LEN, logicTblId, upperBound);
    if (length <= 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    char physicalConfigJson[PHYSICAL_CONFIG_JSON_MAX_LEN];
    length = snprintf_s(
        physicalConfigJson, PHYSICAL_CONFIG_JSON_MAX_LEN, PHYSICAL_CONFIG_JSON_MAX_LEN - 1, PHYSICAL_LABEL_CONFIG_JSON);
    if (length <= 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    TextT labelJson = {.len = DM_STR_LEN(physicalLabelJson), .str = physicalLabelJson};
    TextT configJson = {.len = DM_STR_LEN(physicalConfigJson), .str = physicalConfigJson};
    Status ret = QryParseVertexLabel(g_ts_qryStmt, &labelJson, &configJson, desc);
    if (ret != GMERR_OK) {
        return ret;
    }
    QryCreateSingleVertexLabelDescT *singleDesc = (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, 0);
    DmVertexLabelT *physicalLabel = singleDesc->vertexLabel;
    physicalLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_TS_PHYSICAL;
    physicalLabel->metaCommon.version = 0;
    ret = CataGetNspDefaultTspId(physicalLabel->metaCommon.namespaceId, &physicalLabel->metaCommon.tablespaceId,
        (DbInstanceHdT)DbGetInstanceByMemCtx(physicalLabel->memCtx));  // ut中不持久化，无法使用时序表空间
    if (ret != GMERR_OK) {
        return ret;
    }
    // 建表需要开启事务，DDL原子性新增
    ret = SeTransBegin(g_ts_session->seInstance, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexLabelT *tempLabel = NULL;
    ret = CmdCreateVertexLabel(g_ts_qryStmt->session->seInstance, physicalLabel, &tempLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeTransCommit(g_ts_session->seInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    *pPhysicTbl = physicalLabel;
    return ret;
}

// 通过查找cu文件校验注入成功
static bool CheckCuFileIsExist(uint32_t logicalId, uint32_t physicalId)
{
    char *cuFilePath = (char *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(char) * PATH_MAX);
    (void)memset_s(cuFilePath, PATH_MAX, 0, PATH_MAX);
    (void)snprintf_s(cuFilePath, PATH_MAX, PATH_MAX - 1, "%s/%" PRIu32 "/%" PRIu32 "/%s_%" PRIu32 ".%" PRId32,
        g_cstoreFileDir, logicalId, physicalId, "cuFile", physicalId, 0);

    return DbFileExist(cuFilePath);
}

static bool IsCuDescSame(DmCuDescT *cuDesc, DmCuDescT *otherCuDesc)
{
    if (cuDesc->dataType != otherCuDesc->dataType || cuDesc->colId != otherCuDesc->colId ||
        cuDesc->rowCount != otherCuDesc->rowCount || cuDesc->cuSize != otherCuDesc->cuSize ||
        cuDesc->cuPointer != otherCuDesc->cuPointer) {
        return false;
    }
    if (memcmp(cuDesc->cuMin, otherCuDesc->cuMin, CU_MIN_MAX_LEN) != 0) {
        return false;
    }
    if (memcmp(cuDesc->cuMax, otherCuDesc->cuMax, CU_MIN_MAX_LEN) != 0) {
        return false;
    }
    if (cuDesc->cuMode != otherCuDesc->cuMode) {
        return false;
    }
    if (cuDesc->magic != otherCuDesc->magic) {
        return false;
    }
    return true;
}

static void InsertData(PhysicalLabelWrapperT physicalLabelWrapper, DbMemCtxT *memCtx, DataArrT *dataArr,
    int64_t *srcData, uint32_t rowCount)
{
    PhysicalLabelModifyDescT modifyDesc;
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginModify(physicalLabelWrapper, g_ts_session, g_ts_memCtx, &modifyDesc));
    uint32_t j = 0;
    while (j < dataArr->batchNum) {
        // 构造cu、cuDesc并注入，注入batchNum次
        dataArr->cuArr[j] = (DmCuT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuT) * dataArr->colNum);
        dataArr->cuDescArr[j] = (DmCuDescT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuDescT) * dataArr->colNum);
        for (uint32_t i = 0; i < dataArr->colNum; i++) {
            dataArr->cuArr[j][i].memCtx = g_ts_memCtx;
            dataArr->cuArr[j][i].srcData = (void *)srcData;
            dataArr->cuArr[j][i].dataType = DB_DATATYPE_INT64;
            dataArr->cuArr[j][i].rowCount = rowCount;
            dataArr->cuArr[j][i].infoMode = 0;
            dataArr->cuArr[j][i].compressAlgoNum = 0;
            dataArr->cuArr[j][i].originalDataSize = sizeof(int64_t) * rowCount;
            dataArr->cuArr[j][i].srcDataSize = dataArr->cuArr[j][i].originalDataSize;
            dataArr->cuArr[j][i].cuBufSize =
                DmGetCuHeaderSize(&dataArr->cuArr[j][i]) + dataArr->cuArr[j][i].originalDataSize;
            dataArr->cuArr[j][i].cuHeaderBufSize = DmGetCuHeaderSize(&dataArr->cuArr[j][i]);
            dataArr->cuArr[j][i].cuBodyMagicCode =
                DbCRC32((char *)dataArr->cuArr[j][i].srcData, dataArr->cuArr[j][i].srcDataSize);
            DmBuffer buf;
            buf.memCtx = g_ts_memCtx;
            uint32_t crcCode;
            EXPECT_EQ(GMERR_OK, DmSerializeCu(&dataArr->cuArr[j][i], &buf, &crcCode));
            dataArr->cuDescArr[j][i].dataType = DB_DATATYPE_INT64;
            dataArr->cuDescArr[j][i].colId = i;
            dataArr->cuDescArr[j][i].rowCount = rowCount;
            dataArr->cuDescArr[j][i].cuSize = dataArr->cuArr[j][i].cuBufSize;
            *(int64_t *)(void *)dataArr->cuDescArr[j][i].cuMin = srcData[0];
            *(int64_t *)(void *)dataArr->cuDescArr[j][i].cuMax = srcData[rowCount - 1];
            dataArr->cuDescArr[j][i].cuMode = 0;
            dataArr->cuDescArr[j][i].magic = crcCode;
            dataArr->cuDescArr[j][i].ptr2MsgBuffer = buf.buf;
            EXPECT_NE(dataArr->cuDescArr[j][i].ptr2MsgBuffer, nullptr);
        }

        EXPECT_EQ(GMERR_OK, PhysicalLabelInsert(&modifyDesc, dataArr->cuDescArr[j], dataArr->colNum));
        j++;
    }

    PhysicalLabelEndModify(&modifyDesc);
    SeTransCommit(g_ts_session->seInstance);
}

class TsUtPhysicalLabelAccess : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {
        system("rm -rf /data/gmdb");
    }
    static void SetUpTestCase()
    {
        system("rm -rf /data/gmdb");
        BaseInit();
        SeCStoreInit();
        DbMemCtxArgsT args = {0};
        g_ts_memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memctx", &args);
        EXPECT_NE(nullptr, g_ts_memCtx);
        EXPECT_EQ(GMERR_OK, SeInitCuStorageHdlCache(g_ts_memCtx));
        EXPECT_EQ(GMERR_OK, SeInitCuFileSizeCache(g_ts_memCtx));
        EXPECT_EQ(GMERR_OK, SeSetCuOperateMemCtx(g_ts_memCtx));
        EXPECT_EQ(GMERR_OK, InitSessionAndStmt(&g_ts_session, &g_ts_qryStmt));
        EXPECT_EQ(GMERR_OK, InitCStoreFileDir(g_ts_memCtx, &g_cstoreFileDir));
    }
    static void TearDownTestCase()
    {
        SeUnInitCuStorageHdlCache();
        SeUnInitCuFileSizeCache();
        DbDeleteDynMemCtx(g_ts_memCtx);
        BaseUninit();
        (void)clearAllStub();
    };
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

// 正常注入一批数据，通过直接读取文件来校验正确性
TEST_F(TsUtPhysicalLabelAccess, InsertCuDesc)
{
    uint32_t logicalId = 50000;
    uint32_t colNum = 5;
    uint32_t upperBound = 10000;
    DmVertexLabelT *physicalLabel = NULL;
    EXPECT_EQ(GMERR_OK, CreatePhysicalLabel(logicalId, upperBound, &physicalLabel));
    uint32_t physicalId = physicalLabel->metaCommon.metaId;
    EXPECT_EQ(GMERR_OK, CataSetVertexLabelCreateStatusById(NULL, physicalId, true));
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelById(NULL, physicalId, &physicalLabel));
    // 构造简易logicalLabel
    DmVertexLabelT logicalLabel = {};
    logicalLabel.metaCommon.metaId = logicalId;
    MetaVertexLabelT metaVertexLabel = {};
    logicalLabel.metaVertexLabel = &metaVertexLabel;
    DmSchemaT schema = {};
    schema.propeNum = colNum;
    logicalLabel.metaVertexLabel->schema = &schema;
    logicalLabel.metaVertexLabel->extraInfo.data = (DmTsInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmTsInfoT));
    (void)memset_s(logicalLabel.metaVertexLabel->extraInfo.data, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));
    logicalLabel.metaVertexLabel->extraInfo.len = sizeof(DmTsInfoT);

    DmCuDescT *cuDescArr = (DmCuDescT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuDescT) * colNum);
    EXPECT_NE(cuDescArr, nullptr);

    for (uint32_t i = 0; i < colNum; i++) {
        cuDescArr[i].dataType = DB_DATATYPE_INT64;
        cuDescArr[i].colId = i;
        cuDescArr[i].rowCount = 10000;
        cuDescArr[i].cuSize = (i + 1) * 200;
        *(int64_t *)(void *)cuDescArr[i].cuMin = 0;
        *(int64_t *)(void *)cuDescArr[i].cuMax = 1000;
        cuDescArr[i].cuMode = 0;
        cuDescArr[i].magic = 0;
        cuDescArr[i].ptr2MsgBuffer = DbDynMemCtxAlloc(g_ts_memCtx, sizeof(uint8_t *) * cuDescArr[i].cuSize);
        EXPECT_NE(cuDescArr[i].ptr2MsgBuffer, nullptr);
    }
    // 插入之前，无对应cu文件
    EXPECT_EQ(false, CheckCuFileIsExist(logicalId, physicalId));
    PhysicalLabelWrapperT physicalLabelWrapper;
    physicalLabelWrapper.logicalLabel = &logicalLabel;
    physicalLabelWrapper.physicalLabel = physicalLabel;
    physicalLabelWrapper.modifiedColNum = colNum;
    PhysicalLabelModifyDescT modifyDesc;
    SeTransBegin(g_ts_session->seInstance, NULL);
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginModify(physicalLabelWrapper, g_ts_session, g_ts_memCtx, &modifyDesc));
    EXPECT_EQ(GMERR_OK, PhysicalLabelInsert(&modifyDesc, cuDescArr, colNum));
    PhysicalLabelEndModify(&modifyDesc);
    SeTransCommit(g_ts_session->seInstance);
    // 插入完后，应当有相应的cu文件
    EXPECT_EQ(true, CheckCuFileIsExist(logicalId, physicalId));
    (void)CataReleaseVertexLabel(physicalLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_ts_session->seInstance, physicalLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(physicalLabel));
    EXPECT_EQ(GMERR_OK, SeTransCommit(g_ts_session->seInstance));
}

// 正常注入一个cuDesc块，无cu数据
TEST_F(TsUtPhysicalLabelAccess, InsertSingleCuDescNoCuData)
{
    uint32_t logicalId = 50000;
    uint32_t colNum = 3;
    uint32_t upperBound = 10000;
    DmVertexLabelT *physicalLabel = NULL;
    EXPECT_EQ(GMERR_OK, CreatePhysicalLabel(logicalId, upperBound, &physicalLabel));
    uint32_t physicalId = physicalLabel->metaCommon.metaId;
    EXPECT_EQ(GMERR_OK, CataSetVertexLabelCreateStatusById(NULL, physicalId, true));
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelById(NULL, physicalId, &physicalLabel));
    // 构造简易logicalLabel
    DmVertexLabelT logicalLabel = {};
    logicalLabel.metaCommon.metaId = logicalId;
    MetaVertexLabelT metaVertexLabel = {};
    logicalLabel.metaVertexLabel = &metaVertexLabel;
    DmSchemaT schema = {};
    schema.propeNum = colNum;
    logicalLabel.metaVertexLabel->schema = &schema;
    logicalLabel.metaVertexLabel->extraInfo.data = (DmTsInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmTsInfoT));
    (void)memset_s(logicalLabel.metaVertexLabel->extraInfo.data, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));
    logicalLabel.metaVertexLabel->extraInfo.len = sizeof(DmTsInfoT);

    DmCuDescT cuDesc;
    cuDesc.dataType = DB_DATATYPE_INT64;
    cuDesc.colId = 0;
    cuDesc.rowCount = 10000;
    cuDesc.cuSize = 200;
    *(int64_t *)(void *)cuDesc.cuMin = 0;
    *(int64_t *)(void *)cuDesc.cuMax = 1000;
    cuDesc.cuMode = 0;
    cuDesc.magic = 0;
    cuDesc.ptr2MsgBuffer = DbDynMemCtxAlloc(g_ts_memCtx, sizeof(uint8_t *) * cuDesc.cuSize);
    EXPECT_NE(cuDesc.ptr2MsgBuffer, nullptr);

    PhysicalLabelWrapperT physicalLabelWrapper;
    physicalLabelWrapper.logicalLabel = &logicalLabel;
    physicalLabelWrapper.physicalLabel = physicalLabel;
    physicalLabelWrapper.modifiedColNum = colNum;
    PhysicalLabelModifyDescT modifyDesc;
    TrxCfgT cfg = {0};
    SeTransBegin(g_ts_session->seInstance, &cfg);
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginModify(physicalLabelWrapper, g_ts_session, g_ts_memCtx, &modifyDesc));
    EXPECT_EQ(GMERR_OK, PhysicalLabelInsertSingleCuDesc(&modifyDesc, &cuDesc));
    PhysicalLabelEndModify(&modifyDesc);
    SeTransCommit(g_ts_session->seInstance);

    // 插入后，无对应cu文件
    EXPECT_EQ(false, CheckCuFileIsExist(logicalId, physicalId));
    (void)CataReleaseVertexLabel(physicalLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_ts_session->seInstance, physicalLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(physicalLabel));
    EXPECT_EQ(GMERR_OK, SeTransCommit(g_ts_session->seInstance));
}

// 注入cuDesc {colId, cuId} 冲突，同一列出现重复cuId的cu块
TEST_F(TsUtPhysicalLabelAccess, InsertSingleCuDescDuplicateCuId)
{
    uint32_t logicalId = 50000;
    uint32_t colNum = 5;
    uint32_t upperBound = 10000;
    DmVertexLabelT *physicalLabel = NULL;
    EXPECT_EQ(GMERR_OK, CreatePhysicalLabel(logicalId, upperBound, &physicalLabel));
    uint32_t physicalId = physicalLabel->metaCommon.metaId;
    EXPECT_EQ(GMERR_OK, CataSetVertexLabelCreateStatusById(NULL, physicalId, true));
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelById(NULL, physicalId, &physicalLabel));
    // 构造简易logicalLabel
    DmVertexLabelT logicalLabel = {};
    logicalLabel.metaCommon.metaId = logicalId;
    MetaVertexLabelT metaVertexLabel = {};
    logicalLabel.metaVertexLabel = &metaVertexLabel;
    DmSchemaT schema = {};
    schema.propeNum = colNum;
    logicalLabel.metaVertexLabel->schema = &schema;
    logicalLabel.metaVertexLabel->extraInfo.data = (DmTsInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmTsInfoT));
    (void)memset_s(logicalLabel.metaVertexLabel->extraInfo.data, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));
    logicalLabel.metaVertexLabel->extraInfo.len = sizeof(DmTsInfoT);

    DmCuDescT cuDesc;
    cuDesc.dataType = DB_DATATYPE_INT64;
    cuDesc.colId = 0;
    cuDesc.cuId = 2000;
    cuDesc.rowCount = 10000;
    cuDesc.cuSize = 200;
    *(int64_t *)(void *)cuDesc.cuMin = 0;
    *(int64_t *)(void *)cuDesc.cuMax = 1000;
    cuDesc.cuMode = 0;
    cuDesc.magic = 0;
    cuDesc.cuPointer = DB_MAX_INT64;

    PhysicalLabelWrapperT physicalLabelWrapper;
    physicalLabelWrapper.logicalLabel = &logicalLabel;
    physicalLabelWrapper.physicalLabel = physicalLabel;
    physicalLabelWrapper.modifiedColNum = colNum;
    PhysicalLabelModifyDescT modifyDesc;
    SeTransBegin(g_ts_session->seInstance, NULL);
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginModify(physicalLabelWrapper, g_ts_session, g_ts_memCtx, &modifyDesc));
    EXPECT_EQ(GMERR_OK, PhysicalLabelInsertSingleCuDesc(&modifyDesc, &cuDesc));
    // 二次插入相同元数据，不成功
    EXPECT_NE(GMERR_OK, PhysicalLabelInsertSingleCuDesc(&modifyDesc, &cuDesc));
    PhysicalLabelEndModify(&modifyDesc);
    SeTransCommit(g_ts_session->seInstance);

    (void)CataReleaseVertexLabel(physicalLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_ts_session->seInstance, physicalLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(physicalLabel));
    EXPECT_EQ(GMERR_OK, SeTransCommit(g_ts_session->seInstance));
}

// 注入数据列与时序表的列数不一致
TEST_F(TsUtPhysicalLabelAccess, InsertCuDescWithInvalidColNum)
{
    uint32_t logicalId = 30000;
    uint32_t colNum = 5;
    uint32_t upperBound = 15000;
    DmVertexLabelT *physicalLabel = NULL;
    EXPECT_EQ(GMERR_OK, CreatePhysicalLabel(logicalId, upperBound, &physicalLabel));
    uint32_t physicalId = physicalLabel->metaCommon.metaId;
    EXPECT_EQ(GMERR_OK, CataSetVertexLabelCreateStatusById(NULL, physicalId, true));
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelById(NULL, physicalId, &physicalLabel));
    // 构造简易logicalLabel
    DmVertexLabelT logicalLabel = {};
    logicalLabel.metaCommon.metaId = logicalId;
    MetaVertexLabelT metaVertexLabel = {};
    logicalLabel.metaVertexLabel = &metaVertexLabel;
    DmSchemaT schema = {};
    schema.propeNum = colNum;
    logicalLabel.metaVertexLabel->schema = &schema;
    logicalLabel.metaVertexLabel->extraInfo.data = (DmTsInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmTsInfoT));
    (void)memset_s(logicalLabel.metaVertexLabel->extraInfo.data, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));
    logicalLabel.metaVertexLabel->extraInfo.len = sizeof(DmTsInfoT);
    uint32_t arrLen = 3;
    DmCuDescT *cuDescArr = (DmCuDescT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuDescT) * arrLen);
    EXPECT_NE(cuDescArr, nullptr);

    for (uint32_t i = 0; i < arrLen; i++) {
        cuDescArr[i].dataType = DB_DATATYPE_INT64;
        cuDescArr[i].colId = i;
        cuDescArr[i].rowCount = 10000;
        cuDescArr[i].cuSize = (i + 1) * 200;
        *(int64_t *)(void *)cuDescArr[i].cuMin = 0;
        *(int64_t *)(void *)cuDescArr[i].cuMax = 1000;
        cuDescArr[i].cuMode = 0;
        cuDescArr[i].magic = 0;
        cuDescArr[i].ptr2MsgBuffer = DbDynMemCtxAlloc(g_ts_memCtx, sizeof(uint8_t *) * cuDescArr[i].cuSize);
        EXPECT_NE(cuDescArr[i].ptr2MsgBuffer, nullptr);
    }
    // 插入之前，无对应cu文件
    EXPECT_EQ(false, CheckCuFileIsExist(logicalId, physicalId));
    PhysicalLabelWrapperT physicalLabelWrapper;
    physicalLabelWrapper.logicalLabel = &logicalLabel;
    physicalLabelWrapper.physicalLabel = physicalLabel;
    physicalLabelWrapper.modifiedColNum = colNum;
    PhysicalLabelModifyDescT modifyDesc;
    TrxCfgT cfg = {0};
    SeTransBegin(g_ts_session->seInstance, &cfg);
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginModify(physicalLabelWrapper, g_ts_session, g_ts_memCtx, &modifyDesc));
    PhysicalLabelEndModify(&modifyDesc);
    SeTransCommit(g_ts_session->seInstance);
    // 插入失败后，无相应的cu文件
    EXPECT_EQ(false, CheckCuFileIsExist(logicalId, physicalId));
    (void)CataReleaseVertexLabel(physicalLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_ts_session->seInstance, physicalLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(physicalLabel));
    EXPECT_EQ(GMERR_OK, SeTransCommit(g_ts_session->seInstance));
}

// 正常注入一批数据后，扫描物理表获取cuDesc与cu，包括顺序、逆序和单点获取
TEST_F(TsUtPhysicalLabelAccess, ScanCuDesc)
{
    uint32_t logicalId = 50000;
    uint32_t colNum = 5;
    uint32_t batchNum = 5;  // cu块的批数
    uint32_t upperBound = 10000;
    DmVertexLabelT *physicalLabel = NULL;
    EXPECT_EQ(GMERR_OK, CreatePhysicalLabel(logicalId, upperBound, &physicalLabel));
    uint32_t physicalId = physicalLabel->metaCommon.metaId;
    EXPECT_EQ(GMERR_OK, CataSetVertexLabelCreateStatusById(NULL, physicalId, true));
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelById(NULL, physicalId, &physicalLabel));
    // 构造简易logicalLabel
    DmVertexLabelT logicalLabel = {};
    logicalLabel.metaCommon.metaId = logicalId;
    logicalLabel.commonInfo = (VertexLabelCommonInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(VertexLabelCommonInfoT));
    EXPECT_NE(logicalLabel.commonInfo, nullptr);
    (void)memset_s(logicalLabel.commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    MetaVertexLabelT metaVertexLabel = {};
    logicalLabel.metaVertexLabel = &metaVertexLabel;
    DmSchemaT *schema = (DmSchemaT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmSchemaT));
    EXPECT_NE(schema, nullptr);
    (void)memset_s(schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->properties = (DmPropertySchemaT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmPropertySchemaT) * colNum);
    EXPECT_NE(schema->properties, nullptr);
    (void)memset_s(schema->properties, sizeof(DmPropertySchemaT) * colNum, 0, sizeof(DmPropertySchemaT) * colNum);
    schema->propertiesOffset = (uint32_t)((uintptr_t)schema->properties - (uintptr_t)schema);
    for (uint32_t i = 0; i < colNum; i++) {
        schema->properties[i].dataType = DB_DATATYPE_INT64;
        schema->properties[i].isValid = true;
        schema->properties[i].size = sizeof(int64_t);
    }
    schema->propeNum = colNum;
    logicalLabel.metaVertexLabel->schema = schema;
    logicalLabel.metaVertexLabel->extraInfo.data = (DmTsInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmTsInfoT));
    logicalLabel.metaVertexLabel->extraInfo.len = sizeof(DmTsInfoT);
    (void)memset_s(logicalLabel.metaVertexLabel->extraInfo.data, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));

    DmVertexLabelT *newLogicalLabel = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    ASSERT_EQ(GMERR_OK, CopyVertexLabel(cataCacheMgr->shmMemCtx, &logicalLabel, &newLogicalLabel));
    uint32_t rowCount = 5;  // 为了构造过程的简易，统一行数为5，srcData内容一致，并不影响测试效果
    // 构造实际cu数据
    int64_t *srcData = (int64_t *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(int64_t) * rowCount);
    EXPECT_NE(srcData, nullptr);
    for (uint32_t i = 0; i < rowCount; i++) {
        srcData[i] = i;
    }

    DmCuT **cuArr = (DmCuT **)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuT *) * batchNum);
    EXPECT_NE(cuArr, nullptr);
    DmCuDescT **cuDescArr = (DmCuDescT **)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuDescT *) * batchNum);
    EXPECT_NE(cuDescArr, nullptr);

    // 插入之前，无对应cu文件
    EXPECT_EQ(false, CheckCuFileIsExist(logicalId, physicalId));
    PhysicalLabelWrapperT physicalLabelWrapper;
    physicalLabelWrapper.logicalLabel = newLogicalLabel;
    physicalLabelWrapper.physicalLabel = physicalLabel;
    physicalLabelWrapper.modifiedColNum = colNum;

    DataArrT dataArr = {0};
    dataArr.colNum = colNum;
    dataArr.batchNum = batchNum;
    dataArr.cuArr = cuArr;
    dataArr.cuDescArr = cuDescArr;
    InsertData(physicalLabelWrapper, g_ts_memCtx, &dataArr, srcData, rowCount);

    PhysicalLabelScanDescT scanDesc = {};
    DmCuDescT resultCuDesc = {};
    DmCuT *resultCu = NULL;
    bool isFound = false;

    // 插入完后，顺序查询最后一列
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, PhysicalLabelOpen(physicalLabelWrapper, g_ts_session, g_ts_memCtx, HEAP_OPTYPE_NORMALREAD,
                            &scanDesc.labelDesc));
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginScanCuDesc(&scanDesc, colNum - 1, cuDescArr[0][colNum - 1].cuId, 2000, true));
    for (uint32_t i = 0; i < batchNum; i++) {
        EXPECT_EQ(GMERR_OK, PhysicalLabelScanNextCuDesc(&scanDesc, &resultCuDesc, &isFound));
        EXPECT_EQ(true, isFound);
        // 校验cuDesc的正确性
        EXPECT_EQ(true, IsCuDescSame(&resultCuDesc, &cuDescArr[i][colNum - 1]));
        // 获取cu块
        EXPECT_EQ(GMERR_OK, PhysicalLabelGetCuByDesc(&scanDesc, &resultCuDesc, &resultCu));
        int64_t *resultSrcData = (int64_t *)resultCu->srcData;
        // 校验cu内部数据正确性
        for (uint32_t k = 0; k < resultCuDesc.rowCount; k++) {
            EXPECT_EQ(resultSrcData[k], srcData[k]);
        }
    }
    EXPECT_EQ(GMERR_OK, PhysicalLabelScanNextCuDesc(&scanDesc, &resultCuDesc, &isFound));
    EXPECT_EQ(false, isFound);
    PhysicalLabelEndScanCuDesc(&scanDesc);

    // 逆序查询第一列
    EXPECT_EQ(GMERR_OK, PhysicalLabelBeginScanCuDesc(&scanDesc, 0, cuDescArr[batchNum - 2][0].cuId, 2000, false));
    for (int i = batchNum - 2; i >= 0; i--) {
        EXPECT_EQ(GMERR_OK, PhysicalLabelScanNextCuDesc(&scanDesc, &resultCuDesc, &isFound));
        EXPECT_EQ(true, isFound);
        // 校验cuDesc的正确性
        EXPECT_EQ(true, IsCuDescSame(&resultCuDesc, &cuDescArr[i][0]));
        // 获取cu块
        EXPECT_EQ(GMERR_OK, PhysicalLabelGetCuByDesc(&scanDesc, &resultCuDesc, &resultCu));
        int64_t *resultSrcData = (int64_t *)resultCu->srcData;
        // 校验cu内部数据正确性
        for (uint32_t k = 0; k < rowCount; k++) {
            EXPECT_EQ(resultSrcData[k], srcData[k]);
        }
    }
    EXPECT_EQ(GMERR_OK, PhysicalLabelScanNextCuDesc(&scanDesc, &resultCuDesc, &isFound));
    EXPECT_EQ(false, isFound);
    PhysicalLabelEndScanCuDesc(&scanDesc);

    // 单点获取指定colId、cuId的cuDesc
    EXPECT_EQ(GMERR_OK, PhysicalLabelGetSingleCuDesc(&scanDesc.labelDesc, 0, cuDescArr[0][0].cuId, &resultCuDesc));
    EXPECT_EQ(true, IsCuDescSame(&resultCuDesc, &cuDescArr[0][0]));

    SeTransCommit(g_ts_session->seInstance);
    PhysicalLabelClose(&scanDesc.labelDesc, false);
    (void)CataReleaseVertexLabel(physicalLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_ts_session->seInstance, physicalLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(physicalLabel));
    EXPECT_EQ(GMERR_OK, SeTransCommit(g_ts_session->seInstance));
}

// 注入部分列（前n列）
TEST_F(TsUtPhysicalLabelAccess, InsertPartialColumns)
{
    uint32_t logicalId = 50000;
    uint32_t colNum = 5;        // 逻辑表的列数
    uint32_t insertColNum = 3;  // 实际注入的列数
    uint32_t batchNum = 5;      // cu块的批数
    uint32_t upperBound = 10000;
    DmVertexLabelT *physicalLabel = NULL;
    EXPECT_EQ(GMERR_OK, CreatePhysicalLabel(logicalId, upperBound, &physicalLabel));
    uint32_t physicalId = physicalLabel->metaCommon.metaId;
    EXPECT_EQ(GMERR_OK, CataSetVertexLabelCreateStatusById(NULL, physicalId, true));
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelById(NULL, physicalId, &physicalLabel));
    // 构造简易logicalLabel
    DmVertexLabelT logicalLabel = {};
    logicalLabel.metaCommon.metaId = logicalId;
    logicalLabel.commonInfo = (VertexLabelCommonInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(VertexLabelCommonInfoT));
    EXPECT_NE(logicalLabel.commonInfo, nullptr);
    (void)memset_s(logicalLabel.commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    MetaVertexLabelT metaVertexLabel = {};
    logicalLabel.metaVertexLabel = &metaVertexLabel;
    DmSchemaT *schema = (DmSchemaT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmSchemaT));
    EXPECT_NE(schema, nullptr);
    (void)memset_s(schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->properties = (DmPropertySchemaT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmPropertySchemaT) * colNum);
    EXPECT_NE(schema->properties, nullptr);
    (void)memset_s(schema->properties, sizeof(DmPropertySchemaT) * colNum, 0, sizeof(DmPropertySchemaT) * colNum);
    schema->propertiesOffset = (uint32_t)((uintptr_t)schema->properties - (uintptr_t)schema);
    for (uint32_t i = 0; i < colNum; i++) {
        schema->properties[i].dataType = DB_DATATYPE_INT64;
        schema->properties[i].isValid = true;
        schema->properties[i].size = sizeof(int64_t);
    }
    schema->propeNum = colNum;
    logicalLabel.metaVertexLabel->schema = schema;
    logicalLabel.metaVertexLabel->extraInfo.data = (DmTsInfoT *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmTsInfoT));
    (void)memset_s(logicalLabel.metaVertexLabel->extraInfo.data, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));
    logicalLabel.metaVertexLabel->extraInfo.len = sizeof(DmTsInfoT);

    DmVertexLabelT *newLogicalLabel = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    ASSERT_EQ(GMERR_OK, CopyVertexLabel(cataCacheMgr->shmMemCtx, &logicalLabel, &newLogicalLabel));
    uint32_t rowCount = 5;  // 为了构造过程的简易，统一行数为5，srcData内容一致，并不影响测试效果
    // 构造实际cu数据
    int64_t *srcData = (int64_t *)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(int64_t) * rowCount);
    EXPECT_NE(srcData, nullptr);
    for (uint32_t i = 0; i < rowCount; i++) {
        srcData[i] = i;
    }
    DmCuT **cuArr = (DmCuT **)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuT *) * batchNum);
    EXPECT_NE(cuArr, nullptr);
    DmCuDescT **cuDescArr = (DmCuDescT **)DbDynMemCtxAlloc(g_ts_memCtx, sizeof(DmCuDescT *) * batchNum);
    EXPECT_NE(cuDescArr, nullptr);

    // 插入之前，无对应cu文件
    EXPECT_EQ(false, CheckCuFileIsExist(logicalId, physicalId));
    PhysicalLabelWrapperT physicalLabelWrapper;
    physicalLabelWrapper.logicalLabel = newLogicalLabel;
    physicalLabelWrapper.physicalLabel = physicalLabel;
    physicalLabelWrapper.modifiedColNum = insertColNum;  // 实际注入的列数由此指定

    DataArrT dataArr = {0};
    dataArr.colNum = insertColNum;
    dataArr.batchNum = batchNum;
    dataArr.cuArr = cuArr;
    dataArr.cuDescArr = cuDescArr;
    InsertData(physicalLabelWrapper, g_ts_memCtx, &dataArr, srcData, rowCount);

    (void)CataReleaseVertexLabel(physicalLabel);
    EXPECT_EQ(GMERR_OK, SeTransBegin(g_ts_session->seInstance, NULL));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabel(g_ts_session->seInstance, physicalLabel));
    EXPECT_EQ(GMERR_OK, CmdDropVertexLabelFromCatalog(physicalLabel));
}
