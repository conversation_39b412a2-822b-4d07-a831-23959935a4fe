/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: some common functions for ts ut
 * Author: lihainuo
 * Create: 2023-05-15
 */
#ifdef EXPERIMENTAL_TSDB
#include "db_config.h"
#include "cpl_ts_compiler.h"
#include "cpl_ts_analyzer_common.h"
#include "ut_ts_common.h"
#include "ee_context.h"
#include "common_init.h"
#include "db_label_latch_mgr.h"
#include "db_sysapp_context.h"
#include "query_ut_base.h"
#include "ee_operate_stat.h"

void TsGetLablesFromCreateStmt(
    DbMemCtxT *memCtx, SessionT *session, const TableRefTestCaseT *tableRefs, DbListT **retList, uint32_t tableCnt)
{
    DbListT *list = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    ASSERT_TRUE(list != NULL);
    DbCreateListWithExtendSize(list, sizeof(DmVertexLabelT *), SQL_LIST_EXTEND_SIZE, memCtx);

    for (uint32_t i = 0; i < tableCnt; i++) {
        ASSERT_TRUE(tableRefs[i].dbName == NULL);
        char *tableName = NULL;
        Status ret = DmStrAllocAndCopy(tableRefs[i].tableName, strlen(tableRefs[i].tableName) + 1, &tableName);
        ASSERT_EQ(GMERR_OK, ret);
        DbStrToLower(tableName);
        DmVertexLabelT *label = TsSqlGetVertexLabelByName(session->dbId, session->namespaceId, tableName);
        ASSERT_TRUE(label != NULL);
        ret = DbAppendListItem(list, &label);
        ASSERT_EQ(GMERR_OK, ret);
    }
    *retList = list;
}

static void GenerateTsSqlReq(FixBufferT *req, MsgHeaderT *msgHeader, const void *data, uint32_t len)
{
    FixBufResetMem(req);
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->modelType = MODEL_TS;
    msgHeader->serviceId = DRT_SERVICE_STMT;
    msgHeader->opNum = 1;
    OpHeaderT opHeader = {0};
    opHeader.opCode = MSG_OP_RPC_TS_EXEC;
    FixBufPutData(req, msgHeader, sizeof(MsgHeaderT));
    FixBufPutData(req, &opHeader, sizeof(OpHeaderT));
    FixBufPutRawText(req, len, data);
    FixBufPutUint16(req, 0);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_TS_EXEC,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

inline static uint32_t TsDataStrLen(const char *str)
{
    if (str == NULL) {
        return 0;
    }
    size_t len = strlen(str);
    if (len > 0) {
        ++len;
    }
    return (uint32_t)len;
}

void TsSqlCreateTable(DrtConnectionT *conn, MsgHeaderT *msgHeader, DbMemCtxT *memCtx, const char *ddl)
{
    FixBufferT req = {0};
    FixBufCreate(&req, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    GenerateTsSqlReq(&req, msgHeader, ddl, TsDataStrLen(ddl));
    FixBufferT *rsp = QrySessionGetRsp((SessionT *)conn->session);
    uint32_t len = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    rsp->pos = len;
    rsp->seekPos = len;
    (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    TsSetOperateStatMapNull();
    Status ret = TsServiceEntry(&serviceCtx, &procCtx);
    ASSERT_EQ(ret, GMERR_OK);
    rsp = QrySessionGetRsp((SessionT *)procCtx.conn->session);
    MsgHeaderT *msg = RpcPeekMsgHeader(rsp);
    ASSERT_EQ(msg->opStatus, GMERR_OK);

    FixBufRelease(&req);
}

void TsUtAnalyzeErr(Status ret)
{
    if (ret == GMERR_OUT_OF_MEMORY) {
        (void)DbPrintfDefault("Analyze ts with problems, unable to malloc analyzer.\n");
    }
    if (ret == GMERR_INTERNAL_ERROR) {
        (void)DbPrintfDefault("Unknown problem.\n");
    }
}

Status TsUtAnalyze(SessionT *session, DbMemCtxT *memCtx, SqlParsedListT *parsedList, DbListT *irStmtList)
{
    DB_POINTER4(session, memCtx, parsedList, irStmtList);
    DbCreateListWithExtendSize(irStmtList, (uint32_t)sizeof(SqlIrStmtT *), SQL_LIST_EXTEND_SIZE, memCtx);

    uint32_t cnt = DbListGetItemCnt(&parsedList->cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        NodeT *stmt = *(NodeT **)DbListItem(&parsedList->cmdList, i);
        if (stmt == NULL) {
            TsUtAnalyzeErr(GMERR_INTERNAL_ERROR);
            return GMERR_INTERNAL_ERROR;
        }
        SqlIrStmtT *irStmt = NULL;
        Status ret = TsSqlAnalyze(session, memCtx, stmt, &irStmt);
        if (ret != GMERR_OK) {
            // handle system error here
            TsUtAnalyzeErr(ret);
            return ret;
        }

        ret = DbAppendListItem(irStmtList, &irStmt);
        if (ret != GMERR_OK) {
            TsUtAnalyzeErr(GMERR_OUT_OF_MEMORY);
            return GMERR_OUT_OF_MEMORY;
        }
    }

    return GMERR_OK;
}

Status InitSessionAndStmt(SessionT **session, QryStmtT **qryStmt)
{
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, session);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryAllocStmt(*session, qryStmt);
    if (ret != GMERR_OK) {
        QrySessionRelease(*session);
        return ret;
    }
    ret = QryAllocCtxFromCtxMemPool(NULL, &(*qryStmt)->context);
    if (ret != GMERR_OK) {
        QryReleaseStmt(*qryStmt);
        QrySessionRelease(*session);
        return ret;
    }
    return GMERR_OK;
}

Status InitCStoreFileDir(DbMemCtxT *memCtx, char **filePath)
{
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_CSTORE_DIR, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "[CFG] Unable to get CStore dirPath.");
        return ret;
    }
    *filePath = (char *)DbDynMemCtxAlloc(memCtx, sizeof(char) * DB_CFG_PARAM_MAX_STRING);
    if (*filePath == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    errno_t err = memcpy_s(*filePath, DB_MAX_PATH, cfgValue.str, DB_CFG_PARAM_MAX_STRING);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "[CFG] Unable to get memcpy_s data(cu) dirPath.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return ret;
}

int32_t TsInit(const char *configFileName, bool isFastReadUncommitted)
{
    return BaseInitWithConfigFile(configFileName, isFastReadUncommitted);
}

void TsUnInit()
{
    BaseUninit();
}

#endif
