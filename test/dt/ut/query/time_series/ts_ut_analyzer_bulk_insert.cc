/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: test for bulk insert in TS
 * Author: lihainuo
 * Create: 2023-08-30
 */
#ifdef EXPERIMENTAL_TSDB
#include "gtest/gtest.h"
#include "query_ut_base.h"
#include "dm_meta_prop_strudefs.h"
#include "cpl_ts_analyzer_bulk_insert.h"
#include "cpl_ts_compiler.h"
#include "srv_data_ts.h"
#include "ee_operate_stat.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif
extern "C" Status TsServiceEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx);
extern "C" void TsSetOperateStatMapNull(void);

static DbMemCtxT *g_tsBulkMem = NULL;
static TsQryStmtT *g_tsStmt = NULL;
static FixBufferT g_req = {0};
static MsgHeaderT g_msgHeader = {0};
static int64_t g_data[] = {1, 2, 3};
static DrtConnectionT *conn = NULL;
static SessionT *session = NULL;
static DrtServiceCtxT serviceCtx;
static DrtProcCtxT procCtx = {0};

static void FillCreateBulkInsertTblBuf(FixBufferT *req, MsgHeaderT *msgHeader)
{
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->serviceId = DRT_SERVICE_STMT;
    msgHeader->modelType = MODEL_TS;
    msgHeader->opNum = 1;
    char *ddl = (char *)"create table t1(age integer, id integer, salary integer)"
                        "with (time_col = 'id', interval = '1 hour');";
    FixBufResetMem(req);
    OpHeaderT opHeader = {0};
    FixBufPutData(req, msgHeader, sizeof(MsgHeaderT));
    FixBufPutData(req, &opHeader, sizeof(OpHeaderT));
    FixBufPutRawText(req, DM_STR_LEN(ddl), ddl);
    FixBufPutUint16(req, 0);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_TS_EXEC,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

static void CreateTable4BulkInsert()
{
    EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn));
    FillCreateBulkInsertTblBuf(&g_req, &g_msgHeader);
    session = (SessionT *)conn->session;
    FixBufferT *rsp = QrySessionGetRsp(session);
    uint32_t len = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    rsp->pos = len;
    rsp->seekPos = len;
    (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    serviceCtx.ctx = NULL;
    procCtx.conn = conn;
    procCtx.msgHeader = &g_msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    FixBufDup(&g_req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    printf("----------->test map is null\n");
    TsSetOperateStatMapNull();
    Status ret = TsServiceEntry(&serviceCtx, &procCtx);
    ASSERT_EQ(ret, GMERR_OK);
    rsp = QrySessionGetRsp((SessionT *)procCtx.conn->session);
    MsgHeaderT *msg = RpcPeekMsgHeader(rsp);
    ASSERT_EQ(msg->opStatus, GMERR_OK);
    session->req = &g_req;
    g_tsStmt->base.session = session;
}

class TsUtAnalyzerBulkInsert : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        QrySetCfg("userPolicyMode", "0");  // ut关闭鉴权
        clearAllStub();
        DbMemCtxArgsT args = {0};
        g_tsBulkMem = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memCtx", &args);
        if (g_tsBulkMem == NULL) {
            return;
        }
        ASSERT_NE(nullptr, g_tsBulkMem);
        FixBufCreate(&g_req, g_tsBulkMem, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        g_tsStmt = (TsQryStmtT *)DbDynMemCtxAlloc(g_tsBulkMem, sizeof(TsQryStmtT));
        if (g_tsStmt == NULL) {
            return;
        }
        g_tsStmt->memCtx = g_tsBulkMem;
        CreateTable4BulkInsert();
    }
    static void TearDownTestCase()
    {
        QryTestReleaseSession(conn);
        DbDeleteDynMemCtx(g_tsBulkMem);
        clearAllStub();
        BaseUninit();
    };
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

static void FillCuDescAndCuData(DmCuDescT **cuDesArr, FixBufferT *req, uint32_t totalCols)
{
    for (uint32_t i = 0; i < totalCols; i++) {
        DmCu cuData = {0};
        cuData.srcData = g_data;
        (*cuDesArr)[i].colId = i;
        (*cuDesArr)[i].cuId = i + 1;
        (*cuDesArr)[i].dataType = DB_DATATYPE_INT64;
        (*cuDesArr)[i].dataSize = DmGetBasicDataTypeLength(DB_DATATYPE_INT64);
        (*cuDesArr)[i].rowCount = 3;
        (*cuDesArr)[i].ptr2MsgBuffer = NULL;
        (*cuDesArr)[i].cuSize = DmGetCuHeaderSize(&cuData) + sizeof(g_data);
        cuData.cuBufSize = DmGetCuHeaderSize(&cuData) + sizeof(g_data);
        cuData.cuHeaderBufSize = DmGetCuHeaderSize(&cuData);
        cuData.dataType = DB_DATATYPE_INT64;
        cuData.rowCount = 3;
        cuData.originalDataSize = sizeof(g_data);
        cuData.srcDataSize = sizeof(g_data);
        cuData.cuBodyMagicCode = DbCRC32((char *)&cuData.srcData, cuData.srcDataSize);
        DmBuffer cuDescBuffer = {0};
        DmBuffer cuBuffer = {0};
        cuDescBuffer.memCtx = g_tsStmt->memCtx;
        cuBuffer.memCtx = g_tsStmt->memCtx;
        uint32_t crcCode;
        EXPECT_EQ(DmSerializeCu(&cuData, &cuBuffer, &crcCode), 0);
        (*cuDesArr)[i].magic = crcCode;
        EXPECT_EQ(DmSerializeCuDesc(&(*cuDesArr)[i], &cuDescBuffer), 0);
        EXPECT_EQ(FixBufPutData(req, cuDescBuffer.buf, cuDescBuffer.len), 0);
        EXPECT_EQ(FixBufPutData(req, cuBuffer.buf, cuBuffer.len), 0);
    }
}

/* =========== normal scenarios begin =========== */
TEST_F(TsUtAnalyzerBulkInsert, BulkInsertNormal)
{
    FixBufferT *req = g_tsStmt->base.session->req;
    FixBufResetMem(req);
    FixBufSeek(req, FixBufGetSeekPos(req));
    // get created table
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, g_tsStmt->base.session->namespaceId, "t1");
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t tblId = vertexLabel->metaCommon.metaId;  // set table id
    CataReleaseVertexLabel(vertexLabel);
    uint32_t totalRows = 6;      // set num of insert rows
    uint32_t totalCols = 3;      // set num of insert cols, same num as cols in target table
    int64_t upperBound = 10000;  // set upper bound
    // fill bulk insert info
    EXPECT_EQ(FixBufPutUint32(req, tblId), 0);
    EXPECT_EQ(FixBufPutUint32(req, totalRows), 0);
    EXPECT_EQ(FixBufPutUint32(req, totalCols), 0);
    uint16_t isDeduplicate = 0;  // set whether need to de-duplicate
    EXPECT_EQ(FixBufPutUint16(req, isDeduplicate), 0);
    DmCuDescT *cuDesArr = (DmCuDescT *)DbDynMemCtxAlloc(g_tsStmt->memCtx, sizeof(DmCuDescT) * totalCols);
    ASSERT_NE(nullptr, cuDesArr);
    // fill first batch of CuDescs and CuData, 3 rows
    EXPECT_EQ(FixBufPutInt64(req, upperBound), 0);
    FillCuDescAndCuData(&cuDesArr, req, totalCols);
    // fill second batch of CuDescs and CuData, 3 rows
    EXPECT_EQ(FixBufPutInt64(req, upperBound), 0);
    FillCuDescAndCuData(&cuDesArr, req, totalCols);

    SqlIrStmtT *irStmt = NULL;
    DmVertexLabelT *logicTbl = NULL;
    Status ret = TsBulkInsertAnalyze(g_tsStmt, &irStmt, &logicTbl);
    EXPECT_EQ(ret, GMERR_OK);

    OpLogicalAABulkInsertT *logicBlkIst = (OpLogicalAABulkInsertT *)irStmt->irPlan->root->op;
    EXPECT_EQ(logicBlkIst->base.type, IR_LOGOP_BULK_INSERT);       // verify node type
    EXPECT_EQ(logicBlkIst->logicLabel->metaCommon.metaId, tblId);  // verify table id
    for (uint32_t batchId = 0; batchId < 2; batchId++) {
        BulkInsertArrT *istItem = (BulkInsertArrT *)DbListItem(logicBlkIst->insertDataList, batchId);
        EXPECT_EQ(istItem->upperBound, upperBound);
        DmCuDescT *cuDesc = NULL;
        // verify cuDesc
        for (uint32_t i = 0; i < totalCols; i++) {
            cuDesc = &istItem->cuDescArr[i];
            EXPECT_EQ(cuDesc->colId, i);
            EXPECT_EQ(cuDesc->cuId, i + 1);
            EXPECT_EQ(cuDesc->dataType, DB_DATATYPE_INT64);
            EXPECT_EQ(cuDesc->dataSize, DmGetBasicDataTypeLength(DB_DATATYPE_INT64));
            EXPECT_EQ(cuDesc->rowCount, (uint32_t)3);
            DmCu cuData = {0};
            EXPECT_EQ(cuDesc->cuSize, DmGetCuHeaderSize(&cuData) + sizeof(g_data));
        }
    }
}
/* =========== normal scenarios end =========== */

/* =========== unnormal scenarios begin =========== */
TEST_F(TsUtAnalyzerBulkInsert, TsUtCreateTableTotalRowsErr1)
{
    FixBufferT *req = g_tsStmt->base.session->req;
    FixBufResetMem(req);
    FixBufSeek(req, FixBufGetSeekPos(req));
    // get created table
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, g_tsStmt->base.session->namespaceId, "t1");
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t tblId = vertexLabel->metaCommon.metaId;  // set table id
    CataReleaseVertexLabel(vertexLabel);
    uint32_t totalRows = 7;      // set num of insert rows (set 7, but insert 6)
    uint32_t totalCols = 3;      // set num of insert cols, same num as cols in target table
    int64_t upperBound = 10000;  // set upper bound
    // fill bulk insert info
    EXPECT_EQ(FixBufPutUint32(req, tblId), 0);
    EXPECT_EQ(FixBufPutUint32(req, totalRows), 0);
    EXPECT_EQ(FixBufPutUint32(req, totalCols), 0);
    uint16_t isDeduplicate = 0;  // set whether need to de-duplicate
    EXPECT_EQ(FixBufPutUint16(req, isDeduplicate), 0);
    DmCuDescT *cuDesArr = (DmCuDescT *)DbDynMemCtxAlloc(g_tsStmt->memCtx, sizeof(DmCuDescT) * totalCols);
    ASSERT_NE(nullptr, cuDesArr);
    // fill first batch of CuDescs and CuData, 3 rows
    EXPECT_EQ(FixBufPutInt64(req, upperBound), 0);
    FillCuDescAndCuData(&cuDesArr, req, totalCols);
    // fill second batch of CuDescs and CuData, 3 rows
    EXPECT_EQ(FixBufPutInt64(req, upperBound), 0);
    FillCuDescAndCuData(&cuDesArr, req, totalCols);

    SqlIrStmtT *irStmt = NULL;
    DmVertexLabelT *logicTbl = NULL;
    Status ret = TsBulkInsertAnalyze(g_tsStmt, &irStmt, &logicTbl);
    EXPECT_EQ(ret, GMERR_NO_DATA);
}

TEST_F(TsUtAnalyzerBulkInsert, TsUtCreateTableTotalRowsErr2)
{
    FixBufferT *req = g_tsStmt->base.session->req;
    FixBufResetMem(req);
    FixBufSeek(req, FixBufGetSeekPos(req));
    // get created table
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, g_tsStmt->base.session->namespaceId, "t1");
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t tblId = vertexLabel->metaCommon.metaId;  // set table id
    CataReleaseVertexLabel(vertexLabel);
    uint32_t totalRows = 5;      // set num of insert rows (set 5, but insert 6)
    uint32_t totalCols = 3;      // set num of insert cols, same num as cols in target table
    int64_t upperBound = 10000;  // set upper bound
    // fill bulk insert info
    EXPECT_EQ(FixBufPutUint32(req, tblId), 0);
    EXPECT_EQ(FixBufPutUint32(req, totalRows), 0);
    EXPECT_EQ(FixBufPutUint32(req, totalCols), 0);
    uint16_t isDeduplicate = 0;  // set whether need to de-duplicate
    EXPECT_EQ(FixBufPutUint16(req, isDeduplicate), 0);
    DmCuDescT *cuDesArr = (DmCuDescT *)DbDynMemCtxAlloc(g_tsStmt->memCtx, sizeof(DmCuDescT) * totalCols);
    ASSERT_NE(nullptr, cuDesArr);
    // fill first batch of CuDescs and CuData, 3 rows
    EXPECT_EQ(FixBufPutInt64(req, upperBound), 0);
    FillCuDescAndCuData(&cuDesArr, req, totalCols);
    // fill second batch of CuDescs and CuData, 3 rows
    EXPECT_EQ(FixBufPutInt64(req, upperBound), 0);
    FillCuDescAndCuData(&cuDesArr, req, totalCols);

    SqlIrStmtT *irStmt = NULL;
    DmVertexLabelT *logicTbl = NULL;
    Status ret = TsBulkInsertAnalyze(g_tsStmt, &irStmt, &logicTbl);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
}

/* =========== unnormal scenarios end =========== */
#endif
