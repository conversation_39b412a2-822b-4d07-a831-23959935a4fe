/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Resource Pool and Resource Table UT.
 * Author: GMDBv5 EE Team
 * Create: 2022-08-26
 */

#include "query_ut_base.h"
#include "ee_cmd_router_fusion.h"
#include "ee_cmd_state_fusion.h"
#include "ee_plan_state.h"
#include "ee_reserved_names.h"
#include "ee_cmd.h"
#include "dm_data_pattern.h"
#include "se_define.h"
#include "ee_delta_table_info_access_method.h"
#include "ee_access_method.h"
#include "ee_experssion_dtl.h"

static char *g_vertexLabelName = (char *)"rsc0";
static char *g_labelJson = (char *)R"([{
                "type":"record",
                "name":"rsc0",
                "fields":[
                    {"name":"dtlReservedCount", "type":"int32"},
                    {"name":"c", "type":"int32"},
                    {"name":"d", "type":"int32"},
                    {"name":"e", "type":"int32"},
                    {"name":"f", "type":"int32"}
                ],
                "keys":[
                    {
                        "node":"rsc0",
                        "name":"K_D",
                        "fields":["d", "e", "f"],
                        "index":{"type":"primary"}
                    }
                ]
            }])";
static char *g_cfgJson =
    (char *)"{\"max_record_count\":10000, \"isFastReadUncommitted\": false, \"enable_clusterhash\":false}";

class ExecutorFusionUtResource : public testing::Test {
protected:
    DbMemCtxT *memCtx;
    DmVertexLabelT *rscLabel;
    Session *session;
    DrtConnectionT *conn;
    AASlotT *rscSlot;
    EStateT *estate;
    DeltaTableT *rscDeltaTable;

protected:
    static void SetUpTestCase()
    {
        BaseInit();
    }

    static void TearDownTestCase()
    {
        BaseUninit();
    }

    virtual void SetUp()
    {
        EXPECT_EQ(GMERR_OK, QryTestCreateVertexLabel(g_cfgJson, g_labelJson, NULL));

        DbMemCtxArgsT args = {0};
        memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memory context", &args);
        ASSERT_NE(nullptr, memCtx);
        EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));

        session = (Session *)conn->session;

        CataKeyT cataKey = {0};
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, session->namespaceId, g_vertexLabelName);
        DmVertexLabelT *vertexLabel = NULL;
        EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel));
        EXPECT_EQ(GMERR_OK, DmCreateVertexDesc(vertexLabel->memCtx, vertexLabel, &(vertexLabel->vertexDesc)));
        rscLabel = vertexLabel;
        EXPECT_EQ(GMERR_OK, NewAASlot(memCtx, rscLabel->vertexDesc, NULL, &rscSlot));
        EXPECT_EQ(GMERR_OK, NewDtlSess(memCtx, QrySessionGetServerTimeout(session), &session->datalogCtx));
    }

    virtual void TearDown()
    {
        if (conn != NULL) {
            QryTestReleaseSession(conn);
        }
        if (rscLabel != NULL) {
            if (rscLabel->commonInfo->datalogLabelInfo != NULL) {
                DbDynMemCtxFree(memCtx, rscLabel->commonInfo->datalogLabelInfo);
                rscLabel->commonInfo->datalogLabelInfo = NULL;
            }
            if (rscLabel->commonInfo->resColInfo != NULL) {
                DbDynMemCtxFree(memCtx, rscLabel->commonInfo->resColInfo);
                rscLabel->commonInfo->resColInfo = NULL;
            }
            EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(rscLabel));
            EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(g_vertexLabelName, false));
        }
        if (memCtx != NULL) {
            DbDeleteDynMemCtx(memCtx);
        }
    }

    void TestCreateAndBindResourcePool(DmResColPoolT *resColPool)
    {
        rscLabel->commonInfo->resColInfo = (DmResColInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmResColInfoT));
        ASSERT_NE(nullptr, rscLabel->commonInfo->resColInfo);
        memset_s(rscLabel->commonInfo->resColInfo, sizeof(DmResColInfoT), 0, sizeof(DmResColInfoT));
        rscLabel->commonInfo->resColInfo->resColPool = DB_INVALID_SHMPTR;
        rscLabel->commonInfo->resColInfo->resPropeId[0] = 1;
        rscLabel->commonInfo->resColInfo->resColCount = 1;
        DmDatalogLabelInfoT *datalogLabelInfo =
            (DmDatalogLabelInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDatalogLabelInfoT));
        ASSERT_NE(nullptr, datalogLabelInfo);
        memset_s(datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
        datalogLabelInfo->labelType = DM_DTL_RESOURCE_SEQUENTIAL;
        datalogLabelInfo->resourceInfo = (DmDtlResourceInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDtlResourceInfoT));
        (void)memset_s(datalogLabelInfo->resourceInfo, sizeof(DmDtlResourceInfoT), 0x00, sizeof(DmDtlResourceInfoT));
        datalogLabelInfo->resourceInfo->outputCount = 1;
        datalogLabelInfo->resourceInfo->outputPropIds = rscLabel->commonInfo->resColInfo->resPropeId;
        datalogLabelInfo->soId = 0;
        rscLabel->commonInfo->datalogLabelInfo = datalogLabelInfo;

        CStateT *cstate = NULL;
        EXPECT_EQ(GMERR_OK, CreateCommandState(memCtx, session, &cstate));

        CreateResourcePoolStmtT *createStmt = NULL;
        CreateResourcePoolResultStmtT *resultStmt = NULL;
        resColPool->metaCommon.metaId = DM_RES_POOL_MAX_POOL_ID;
        resColPool->metaCommon.namespaceId = session->namespaceId;
        resColPool->metaCommon.metaName = (char *)"rscut";
        resColPool->metaCommon.dbId = DEFAULT_DATABASE_ID;
        resColPool->startId = 0;
        resColPool->capacity = 3;
        resColPool->allocOrder = RES_COL_POOL_ALLOC_ORDER_SEQUENCE;
        resColPool->allocType = RES_COL_POOL_ALLOC_TYPE_ANY;
        resColPool->labelJsonLen = 0;
        resColPool->soId = 0;
        EXPECT_EQ(GMERR_OK, NewCreateResourcePoolStmt(memCtx, resColPool, &createStmt));
        EXPECT_EQ(GMERR_OK, CmdCreateResourcePoolFusion(cstate, (NodeT *)createStmt, (NodeT **)&resultStmt));
        EXPECT_EQ(T_CREATE_RESOURCE_POOL_STMT, resultStmt->node.tag);

        BindResourcePoolStmtT *bindStmt = NULL;
        BindResourcePoolResultStmtT *bindResultStmt = NULL;
        TextT resPoolName = DbStr2Text(resColPool->metaCommon.metaName);
        EXPECT_EQ(GMERR_OK,
            NewBindResourcePoolStmt(memCtx, resPoolName, session->namespaceId, rscLabel->metaCommon.metaId, &bindStmt));
        EXPECT_EQ(GMERR_OK, CmdBindResourcePoolFusion(cstate, (NodeT *)bindStmt, (NodeT **)&bindResultStmt));
        EXPECT_EQ(T_BIND_RESOURCE_POOL_STMT, bindResultStmt->node.tag);
        EXPECT_TRUE(DmIsBindResPool(rscLabel->commonInfo->resColInfo->resColPool));
    }

    void TestUnbindAndDropResourcePool(DmResColPoolT *resColPool)
    {
        CStateT *cstate = NULL;
        EXPECT_EQ(GMERR_OK, CreateCommandState(memCtx, session, &cstate));

        UnBindResourcePoolStmtT *unbindStmt = NULL;
        UnBindResourcePoolResultStmtT *unbindResultStmt = NULL;
        EXPECT_EQ(GMERR_OK, NewUnBindResourcePoolStmt(memCtx, rscLabel->metaCommon.metaId, &unbindStmt));
        EXPECT_EQ(GMERR_OK, CmdUnBindResourcePoolFusion(cstate, (NodeT *)unbindStmt, (NodeT **)&unbindResultStmt));

        DropResourcePoolStmtT *dropStmt = NULL;
        DropResourcePoolResultStmtT *dropResultStmt = NULL;
        TextT resPoolName = DbStr2Text(resColPool->metaCommon.metaName);
        EXPECT_EQ(GMERR_OK, NewDropResourcePoolStmt(memCtx, resPoolName, session->namespaceId, &dropStmt));
        EXPECT_EQ(GMERR_OK, CmdDropResourcePoolFusion(cstate, (NodeT *)dropStmt, (NodeT **)&dropResultStmt));
    }

    void TestInitPubsubResource()
    {
        DmDatalogLabelInfoT *datalogLabelInfo =
            (DmDatalogLabelInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDatalogLabelInfoT));
        ASSERT_NE(nullptr, datalogLabelInfo);
        memset_s(datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
        datalogLabelInfo->labelType = DM_DTL_RESOURCE_PUBSUB;
        datalogLabelInfo->resourceInfo = (DmDtlResourceInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDtlResourceInfoT));
        (void)memset_s(datalogLabelInfo->resourceInfo, sizeof(DmDtlResourceInfoT), 0, sizeof(DmDtlResourceInfoT));
        datalogLabelInfo->resourceInfo->outputCount = 1;
        datalogLabelInfo->resourceInfo->outputPropIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t));
        ASSERT_NE(nullptr, datalogLabelInfo->resourceInfo->outputPropIds);
        *(datalogLabelInfo->resourceInfo->outputPropIds) = 1;
        datalogLabelInfo->resourceInfo->defaultResVals = (DbValueT *)DbDynMemCtxAlloc(memCtx, sizeof(DbValueT));
        ASSERT_NE(nullptr, datalogLabelInfo->resourceInfo->defaultResVals);
        memset_s(datalogLabelInfo->resourceInfo->defaultResVals, sizeof(DbValueT), 0, sizeof(DbValueT));
        datalogLabelInfo->resourceInfo->defaultResVals[0].type = DB_DATATYPE_INT32;
        datalogLabelInfo->resourceInfo->defaultResVals[0].value.intValue = -1;
        datalogLabelInfo->soId = 0;
        rscLabel->commonInfo->datalogLabelInfo = datalogLabelInfo;
    }

    void TestGetEstate()
    {
        EXPECT_EQ(GMERR_OK, NewExecutorState(memCtx, &estate));
        estate->startTime = DbClockGetTsc();
        estate->seInstance = (session->seInstance);
        estate->trxMemCtx = session->memCtx;  // UT 里设置成一样
        estate->session = session;
        estate->paramArray.totalParamNum = 0;

        estate->dtlTableInfoMap = (DbOamapT *)DbDynMemCtxAlloc(memCtx, sizeof(DbOamapT));
        ASSERT_NE(nullptr, estate->dtlTableInfoMap);
        memset_s(estate->dtlTableInfoMap, sizeof(DbOamapT), 0, sizeof(DbOamapT));
        Status ret = DbOamapInit(estate->dtlTableInfoMap, 8, DbOamapUint32Compare, memCtx, true);
        TableInfoT *tableInfo = (TableInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(TableInfoT));
        ASSERT_NE(nullptr, tableInfo);
        memset_s(tableInfo, sizeof(TableInfoT), 0, sizeof(TableInfoT));

        DeltaTableT *deltaTable = NULL;
        CreateTableCmd tableCmd = {.vertexLabel = rscLabel};
        ret = NewTempTable(&tableCmd, memCtx, &deltaTable);
        EXPECT_EQ(GMERR_OK, ret);
        tableInfo->table = deltaTable;
        ret = DbOamapInsert(
            estate->dtlTableInfoMap, rscLabel->metaCommon.metaId, &rscLabel->metaCommon.metaId, tableInfo, NULL);
        rscDeltaTable = deltaTable;
    }

    void TestReleaseEstate()
    {
        if (estate != NULL) {
            DbOamapDestroy(estate->dtlTableInfoMap);
            DropTempTable(rscDeltaTable);
            DbDynMemCtxFree(memCtx, estate);
        }
    }

    void TestGetResourceModifyDescT(SeqResModifyDescT **modifyDesc)
    {
        EXPECT_EQ(GMERR_OK, SeTransBegin(session->seInstance, NULL));
        EXPECT_EQ(GMERR_OK, SequentialResourceBeginModify(rscLabel, session->seInstance, memCtx, modifyDesc));
    }

    void TestEndResourceModifyDescT(SeqResModifyDescT *modifyDesc)
    {
        SequentialResourceEndModify(modifyDesc);
        SeTransCommit(session->seInstance);
    }

    void TestAlloc(int resValue, SeqResModifyDescT *modifyDesc)
    {
        DmValueT value = {};
        EXPECT_EQ(GMERR_OK, SequentialResourceAllocate(modifyDesc, rscSlot));
        EXPECT_EQ(GMERR_OK, DmVertexGetPropeById(rscSlot->dmVertex, 1, &value));
        EXPECT_EQ(resValue, value.value.intValue);
    }

    void TestRelease(int resValue, SeqResModifyDescT *modifyDesc)
    {
        DmValueT value = {};
        value.type = DB_DATATYPE_INT32;
        value.value.intValue = resValue;
        EXPECT_EQ(GMERR_OK, DmVertexSetPropeById(1, value, rscSlot->dmVertex));
        EXPECT_EQ(GMERR_OK, SequentialResourceRelease(modifyDesc, rscSlot));
    }

    void TestNewResourcePoolMergePlanState(PlanStateT **planState)
    {
        TestGetEstate();
        DeltaResourcePoolMergeT *merge =
            (DeltaResourcePoolMergeT *)DbDynMemCtxAlloc(memCtx, sizeof(DeltaResourcePoolMergeT));
        ASSERT_NE(nullptr, merge);
        *merge = {};
        merge->deltaMerge.plan.tagType = T_DELTA_RESOURCE_POOL_MERGE;
        merge->deltaMerge.label = rscLabel;

        EXPECT_EQ(GMERR_OK, SeTransBegin(session->seInstance, NULL));
        EXPECT_EQ(GMERR_OK, ExecInitNode((PlanT *)merge, estate, planState));
    }

    void TestNewResourcePubsubMergePlanState(PlanStateT **planState)
    {
        TestGetEstate();
        DeltaResourcePubsubMergeT *merge =
            (DeltaResourcePubsubMergeT *)DbDynMemCtxAlloc(memCtx, sizeof(DeltaResourcePubsubMergeT));
        ASSERT_NE(nullptr, merge);
        *merge = {};
        merge->deltaMerge.plan.tagType = T_DELTA_RESOURCE_PUBSUB_MERGE;
        merge->deltaMerge.label = rscLabel;

        EXPECT_EQ(GMERR_OK, SeTransBegin(session->seInstance, NULL));
        EXPECT_EQ(GMERR_OK, ExecInitNode((PlanT *)merge, estate, planState));
    }

    void TestExecEndNode(PlanStateT *planState)
    {
        ExecEndNode(planState);
        SeTransCommit(session->seInstance);
        TestReleaseEstate();
    }

    void TestPrepareData(int resValue, int count, bool isOrg)
    {
        char *names[5] = {(char *)"c", (char *)"d", (char *)"e", (char *)"f", (char *)DTL_RESERVED_COUNT};
        for (uint32_t i = 0; i < 3; i++) {
            DmValueT values[5] = {};
            values[0].type = DB_DATATYPE_INT32;
            values[0].value.intValue = resValue;
            values[1].type = DB_DATATYPE_INT32;
            values[1].value.intValue = i;
            values[2].type = DB_DATATYPE_INT32;
            values[2].value.intValue = i;
            values[3].type = DB_DATATYPE_INT32;
            values[3].value.intValue = i;
            values[4].type = DB_DATATYPE_INT32;
            values[4].value.intValue = count;

            if (isOrg) {
                values[0].value.intValue = i;
                EXPECT_EQ(GMERR_OK, QryTestInsertVertex(g_vertexLabelName, 5, names, values));
            } else {
                DmVertexT *tmpVertex = NULL;
                EXPECT_EQ(GMERR_OK, DmCreateEmptyVertexWithMemCtx(QryGetDyAlgoCtxVertexBase(), rscLabel, &tmpVertex));
                for (int j = 0; j < 5; ++j) {
                    DmVertexSetPropeByName(names[j], values[j], tmpVertex);
                }
                EXPECT_EQ(GMERR_OK, TempTableInsert(rscDeltaTable, tmpVertex));
            }
        }
    }
};

static Status LabelInsertSub(LabelModifyDescT *modifyDesc, AASlotT *slot)
{
    DB_POINTER2(modifyDesc, slot);
    return GMERR_OK;
}

static Status LabelUpdateSub(LabelModifyDescT *modifyDesc, AASlotT *slot)
{
    DB_POINTER2(modifyDesc, slot);
    return GMERR_OK;
}

static Status LabelDeleteSub(LabelModifyDescT *modifyDesc, AASlotT *slot)
{
    DB_POINTER2(modifyDesc, slot);
    return GMERR_OK;
}

// [resource pool] create, bind, unbind and drop resource pool DDL
TEST_F(ExecutorFusionUtResource, CmdCreateAndBindResourcePool)
{
    DmResColPoolT resColPool = {0};
    TestCreateAndBindResourcePool(&resColPool);
    TestUnbindAndDropResourcePool(&resColPool);
}

// [resource pool] allocate and release resource
TEST_F(ExecutorFusionUtResource, AllocAndReleaseResource)
{
    DmResColPoolT resColPool = {0};
    TestCreateAndBindResourcePool(&resColPool);

    SeqResModifyDescT *modifyDesc = NULL;

    TestGetResourceModifyDescT(&modifyDesc);

    TestAlloc(0, modifyDesc);
    TestAlloc(1, modifyDesc);
    TestAlloc(2, modifyDesc);

    TestRelease(1, modifyDesc);
    TestEndResourceModifyDescT(modifyDesc);
    TestGetResourceModifyDescT(&modifyDesc);
    TestAlloc(1, modifyDesc);

    TestRelease(0, modifyDesc);
    TestRelease(1, modifyDesc);
    TestRelease(2, modifyDesc);

    TestEndResourceModifyDescT(modifyDesc);
    TestUnbindAndDropResourcePool(&resColPool);
}

// [sequential resource table] delta tuple unexist in org table
TEST_F(ExecutorFusionUtResource, SeqResourceMergeOrgTupleUnExistAlloc)
{
    DmResColPoolT resColPool = {0};
    TestCreateAndBindResourcePool(&resColPool);
    (void)setStubC((void *)LabelInsert, (void *)LabelInsertSub);

    DeltaResourcePoolMergeState *resourceMergeState = NULL;
    TestNewResourcePoolMergePlanState((PlanState **)&resourceMergeState);
    TestPrepareData(-1, 1, false);
    ExecProcNode((PlanState *)resourceMergeState, NULL);
    TestRelease(0, resourceMergeState->resourceModifyDesc);
    TestRelease(1, resourceMergeState->resourceModifyDesc);
    TestRelease(2, resourceMergeState->resourceModifyDesc);
    TestExecEndNode((PlanState *)resourceMergeState);

    TestUnbindAndDropResourcePool(&resColPool);
}

// [sequential resource table] delta tuple exist in org table and delta count is larger than 0
TEST_F(ExecutorFusionUtResource, SeqResourceMergeOrgTupleExistUpdateCount)
{
    TestPrepareData(-1, 1, true);
    DmResColPoolT resColPool = {0};
    TestCreateAndBindResourcePool(&resColPool);
    (void)setStubC((void *)LabelInsert, (void *)LabelInsertSub);
    (void)setStubC((void *)LabelUpdate, (void *)LabelUpdateSub);

    DeltaResourcePoolMergeState *resourceMergeState = NULL;

    TestNewResourcePoolMergePlanState((PlanState **)&resourceMergeState);
    TestPrepareData(-1, 1, false);
    ExecProcNode((PlanState *)resourceMergeState, NULL);
    TestExecEndNode((PlanState *)resourceMergeState);

    TestUnbindAndDropResourcePool(&resColPool);
}

// [sequential resource table] delta tuple exist in org table and delta count is less than 0
TEST_F(ExecutorFusionUtResource, SeqResourceMergeOrgTupleExistRelease)
{
    TestPrepareData(-1, 1, true);
    DmResColPoolT resColPool = {0};
    TestCreateAndBindResourcePool(&resColPool);
    (void)setStubC((void *)LabelInsert, (void *)LabelInsertSub);
    (void)setStubC((void *)LabelDelete, (void *)LabelDeleteSub);

    DeltaResourcePoolMergeState *resourceMergeState = NULL;

    TestNewResourcePoolMergePlanState((PlanState **)&resourceMergeState);
    TestPrepareData(-1, -1, false);
    TestAlloc(0, resourceMergeState->resourceModifyDesc);
    TestAlloc(1, resourceMergeState->resourceModifyDesc);
    TestAlloc(2, resourceMergeState->resourceModifyDesc);
    ExecProcNode((PlanState *)resourceMergeState, NULL);
    TestExecEndNode((PlanState *)resourceMergeState);

    TestUnbindAndDropResourcePool(&resColPool);
}

// [sequential resource table] delta tuple unexist in org table
TEST_F(ExecutorFusionUtResource, PubsubResourceMergeOrgTupleUnExistAlloc)
{
    TestInitPubsubResource();
    setStubC((void *)LabelInsert, (void *)LabelInsertSub);

    DeltaResourcePubsubMergeState *resourceMergeState = NULL;
    TestNewResourcePubsubMergePlanState((PlanState **)&resourceMergeState);
    TestPrepareData(-1, 1, false);
    ExecProcNode((PlanState *)resourceMergeState, NULL);
    TestExecEndNode((PlanState *)resourceMergeState);
}

// [sequential resource table] delta tuple exist in org table and delta count is larger than 0
TEST_F(ExecutorFusionUtResource, PubsubResourceMergeOrgTupleExistUpdateCount)
{
    TestPrepareData(-1, 1, true);
    TestInitPubsubResource();
    (void)setStubC((void *)LabelInsert, (void *)LabelInsertSub);
    (void)setStubC((void *)LabelUpdate, (void *)LabelUpdateSub);

    DeltaResourcePubsubMergeState *resourceMergeState = NULL;
    TestNewResourcePubsubMergePlanState((PlanState **)&resourceMergeState);
    TestPrepareData(-1, 1, false);
    ExecProcNode((PlanState *)resourceMergeState, NULL);
    TestExecEndNode((PlanState *)resourceMergeState);
}

// [sequential resource table] delta tuple exist in org table and delta count is less than 0
TEST_F(ExecutorFusionUtResource, PubsubResourceMergeOrgTupleExistRelease)
{
    TestPrepareData(-1, 1, true);
    TestInitPubsubResource();
    (void)setStubC((void *)LabelInsert, (void *)LabelInsertSub);
    (void)setStubC((void *)LabelDelete, (void *)LabelDeleteSub);

    DeltaResourcePubsubMergeState *resourceMergeState = NULL;
    TestNewResourcePubsubMergePlanState((PlanState **)&resourceMergeState);
    TestPrepareData(-1, -1, false);
    ExecProcNode((PlanState *)resourceMergeState, NULL);
    TestExecEndNode((PlanState *)resourceMergeState);
}

#ifdef FEATURE_DATALOG
// [resource pool] unbind and drop all res pool in so, after and create bind resource pool.
TEST_F(ExecutorFusionUtResource, DropAllResPoolInSoDDL)
{
    DmResColPoolT resColPool = {0};
    TestCreateAndBindResourcePool(&resColPool);

    SeqResModifyDescT *modifyDesc = NULL;
    TestGetResourceModifyDescT(&modifyDesc);
    TestAlloc(0, modifyDesc);
    TestAlloc(1, modifyDesc);
    TestEndResourceModifyDescT(modifyDesc);

    CStateT cstate = {0};
    cstate.memCtx = memCtx;
    cstate.session = session;
    UnbindAllResPoolInSoStmtT unbindCmd = {};
    unbindCmd.node.tag = T_UNBIND_ALL_RESOURCEPOOL_IN_SO_STMT;
    unbindCmd.soId = 0;
    UnbindAllResPoolInSoResultStmtT *unbindCmdResult;
    Status ret = CmdUnbindAllResPoolInSo(&cstate, (NodeT *)&unbindCmd, (NodeT **)&unbindCmdResult);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(T_UNBIND_ALL_RESOURCEPOOL_IN_SO_STMT, unbindCmdResult->node.tag);

    DropAllResPoolInSoStmtT dropCmd = {};
    dropCmd.node.tag = T_DROP_ALL_RESOURCEPOOL_IN_SO_STMT;
    dropCmd.soId = 0;
    DropAllResPoolInSoResultStmtT *dropCmdResult;
    ret = CmdDropAllResPoolInSo(&cstate, (NodeT *)&dropCmd, (NodeT **)&dropCmdResult);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(T_DROP_ALL_RESOURCEPOOL_IN_SO_STMT, dropCmdResult->node.tag);
}
#endif
