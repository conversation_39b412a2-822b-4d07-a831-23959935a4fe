#include "gtest/gtest.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "adpt_types.h"
#include "dm_data_basic.h"
#include "dm_data_prop.h"
#include "dm_meta_topo_label.h"
#include "stub.h"
#include "srv_data_fastpath.h"
#include "db_rpc_msg_op.h"
#include "ee_context.h"
#include "db_mem_context.h"
#include "adpt_memory.h"
#include "se_instance.h"
#include "drt_instance.h"
#include "db_list.h"
#include "db_common_init.h"
#include "ee_fastpath.h"
#include "se_heap.h"
#include "ee_session_interface.h"
#include "db_dynmem_algo.h"
#include "query_ut_base.h"
#include "cpl_public_parser_ddl_schema.h"
#include "cpl_public_parser_ddl_index.h"
#include "cpl_public_parser.h"
#include "cpl_fp_parser_dml.h"
#include "cpl_public_parser_ddl.h"
#include "jansson.h"
#if defined(FEATURE_SQL)
#include "db_json.h"
#else
#include "db_json_common.h"
#endif

#define QRY_FREE(p)        \
    do {                   \
        if ((p) != NULL) { \
            free(p);       \
        }                  \
    } while (0);

class UtQueryJsonParse : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();

        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
    }
    virtual void TearDown()
    {
        DbMemCtxSwitchTo(old);
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

/*
 * 测试不包含default_value的schema的解析
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_WITHOUT_DEFAULT_VALUE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
        "\"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含default_value的schema的解析
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_DEFAULT_VALUE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"(
        {
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"int32"
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32",
                        "default":2
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
             }
    )";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含auto_increment的schema的解析
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_AUTO_INCREMENT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"(
        {
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"uint32",
                        "auto_increment":true
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32",
                        "default":2
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
             }
    )";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    uint32_t topRecordNameLen = strlen("T39") + 1;
    vertexLabel.metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(session->memCtx, topRecordNameLen);
    strcpy_s(vertexLabel.metaVertexLabel->topRecordName, topRecordNameLen, "T39");
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 为多版本共用的表级唯一字段分配内存
    ret = QryCtxAllocMem(
        session->currentStmt->context, (uint32_t)sizeof(VertexLabelCommonInfoT), (char **)&(vertexLabel.commonInfo));
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryParseVertexIndexLabel(session->currentStmt, jsonRootLabel, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

/*
 * 测试包含auto_increment的schema的解析, 并且主键为自增列
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_AUTO_INCREMENT_ISPK)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"(
        {
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"uint32",
                        "auto_increment":true
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32",
                        "default":2
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
             }
    )";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    uint32_t topRecordNameLen = strlen("T39") + 1;
    vertexLabel.metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(session->memCtx, topRecordNameLen);
    strcpy_s(vertexLabel.metaVertexLabel->topRecordName, topRecordNameLen, "T39");
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 为多版本共用的表级唯一字段分配内存
    ret = QryCtxAllocMem(
        session->currentStmt->context, (uint32_t)sizeof(VertexLabelCommonInfoT), (char **)&(vertexLabel.commonInfo));
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryParseVertexIndexLabel(session->currentStmt, jsonRootLabel, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

/*
 * 测试包含auto_increment的schema的解析，并且自增列设置默认值，失败
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_AUTO_INCREMENT_WITH_DEFAULT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"(
        {
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"uint32",
                        "auto_increment":true,
                        "default":2
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32",
                        "default":2
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
             }
    )";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    uint32_t topRecordNameLen = strlen("T39") + 1;
    vertexLabel.metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(session->memCtx, topRecordNameLen);
    strcpy_s(vertexLabel.metaVertexLabel->topRecordName, topRecordNameLen, "T39");
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Inv table definition. Property F0 cannot set a default value because of the field type.";
    EXPECT_STREQ(result, lastError1->str);
    // 为多版本共用的表级唯一字段分配内存
    ret = QryCtxAllocMem(
        session->currentStmt->context, (uint32_t)sizeof(VertexLabelCommonInfoT), (char **)&(vertexLabel.commonInfo));
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryParseVertexIndexLabel(session->currentStmt, jsonRootLabel, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

/*
 * 测试包含auto_increment的schema的解析, 并且主键nullable设置为true的属性
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_AUTO_INCREMENT_WITH_NULLABLE_TRUE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"(
        {
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"uint32",
                        "auto_increment":true,
                        "nullable":true
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32",
                        "default":2
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
             }
    )";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    uint32_t topRecordNameLen = strlen("T39") + 1;
    vertexLabel.metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(session->memCtx, topRecordNameLen);
    strcpy_s(vertexLabel.metaVertexLabel->topRecordName, topRecordNameLen, "T39");
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 为多版本共用的表级唯一字段分配内存
    ret = QryCtxAllocMem(
        session->currentStmt->context, (uint32_t)sizeof(VertexLabelCommonInfoT), (char **)&(vertexLabel.commonInfo));
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryParseVertexIndexLabel(session->currentStmt, jsonRootLabel, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

/*
 * 测试包含auto_increment的schema的解析, 并且主键nullable设置为false的属性,成功
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_AUTO_INCREMENT_WITH_NULLABLE_FALSE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"(
        {
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"uint32",
                        "auto_increment":true,
                        "nullable":false
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32",
                        "default":2
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
             }
    )";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    uint32_t topRecordNameLen = strlen("T39") + 1;
    vertexLabel.metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(session->memCtx, topRecordNameLen);
    strcpy_s(vertexLabel.metaVertexLabel->topRecordName, topRecordNameLen, "T39");
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 为多版本共用的表级唯一字段分配内存
    ret = QryCtxAllocMem(
        session->currentStmt->context, (uint32_t)sizeof(VertexLabelCommonInfoT), (char **)&(vertexLabel.commonInfo));
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryParseVertexIndexLabel(session->currentStmt, jsonRootLabel, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

/*
 * 测试包含default_value的schema的解析,uint32类型不匹配,参数小于0
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_DEFAULT_VALUE_TYPE_NOT_TRUE_UINT32)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"uint32\",\"default\":-1},{\"name\":\"F3\", "
        "\"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property F2 value less than 0.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含default_value的schema的解析,int16类型不匹配，是个字符串
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_DEFAULT_VALUE_TYPE_NOT_TRUE_INT16)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", "
        "\"type\":\"int16\",\"default\":\"F2\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Data exception occurs. property F2 value (F2) is not in right format.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含default_value的schema的解析,string类型default值对不上
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_HAVE_DEFAULT_VALUE_TYPE_NOT_TRUE_STRING)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", "
        "\"type\":\"string\",\"size\":8,\"default\":1},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Datatype mismatch. property F2 value type is expected string|bytes|fixed|bitmap.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含fixed的schema的解析
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_FIXED)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"fixed\",\"size\":8},"
        "{\"name\":\"F1\", \"type\":\"int64\"},{\"name\":\"F2\", \"type\":\"int32\",\"default\":2},{\"name\":\"F3\", "
        "\"type\":\"string\",\"size\":8}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试size<0的边界条件
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_INVALID_SIZE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"fixed\",\"size\":8},"
        "{\"name\":\"F1\", \"type\":\"int64\"},{\"name\":\"F2\", \"type\":\"int32\",\"default\":2},{\"name\":\"F3\", "
        "\"type\":\"string\",\"size\":-1}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal property. The value of size should between 0 and 4294967295.", lastError->str);
    QryTestReleaseSession(conn);
}

/*
 * 测试fixed的default value size
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_FIXED_INVALID_DEFAULT_SIZE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", "
        "\"type\":\"fixed\",\"size\":4,\"default\":\"ffff\"},"
        "{\"name\":\"F1\", \"type\":\"int64\"},{\"name\":\"F2\", \"type\":\"int32\",\"default\":2},{\"name\":\"F3\", "
        "\"type\":\"string\",\"size\":5}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}
/*
 * index_label的解析
 */
TEST_F(UtQueryJsonParse, QRY_JSON_INDEX_PARSE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
        "\"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    uint32_t topRecordNameLen = strlen("T39") + 1;
    vertexLabel.metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc(session->memCtx, topRecordNameLen);
    strcpy_s(vertexLabel.metaVertexLabel->topRecordName, topRecordNameLen, "T39");
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);

    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = QryParseVertexIndexLabel(session->currentStmt, jsonRootLabel, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
}

/*
 * 主流程测试
 */
TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_AND_CONFIG_LABEL_PARSE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试maxvertenum<0的边界条件
 */
TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_AND_CONFIG_LABEL_MAXVERTEXNUM)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":-1, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal property. max_record_count value is negative number.", lastError->str);
    QryTestReleaseSession(conn);
}

/*
 * 测试maxvertenum未传入
 */
TEST_F(UtQueryJsonParse, QRY_CONFIG_MAX_VERTEX_NUM_NULL)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试config_json传入值为空
 */
TEST_F(UtQueryJsonParse, QRY_CONFIG_LABEL_NULL2)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT configJson = {.len = 0, .str = NULL};
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, &configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 序列化和反序列化测试
 */
TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_SERIALIZE_AND_DESERIALIZE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryCreateSingleVertexLabelDescT *singleDesc = (QryCreateSingleVertexLabelDescT *)DbListItem(&desc.vertexLabels, 0);
    DmVertexLabelT *vertexLabel = singleDesc->vertexLabel;
#if defined(EXPERIMENTAL_NERGC) || defined(FEATURE_REPLICATION)
    vertexLabel->structSize = sizeof(DmVertexLabelT);
#endif
    EXPECT_NE(vertexLabel, (void *)NULL);
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)(session->currentStmt->memCtx);
    int32_t res = DmSerializeVertexLabel(vertexLabel, &buffer);
    EXPECT_EQ(GMERR_OK, res);
    DmVertexLabelT *vertexLabelout = NULL;
    res = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &vertexLabelout);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(vertexLabel->commonInfo->heapInfo.maxVertexNum, vertexLabelout->commonInfo->heapInfo.maxVertexNum);
    QryTestReleaseSession(conn);
}

/*
 * char和uchar测试
 */
TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_CHAR_UCHAR)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"char\"},"
                "{\"name\":\"F1\", \"type\":\"uchar\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryCreateSingleVertexLabelDescT *singleDesc = (QryCreateSingleVertexLabelDescT *)DbListItem(&desc.vertexLabels, 0);
    DmVertexLabelT *vertexLabel = singleDesc->vertexLabel;
#if defined(EXPERIMENTAL_NERGC) || defined(FEATURE_REPLICATION)
    vertexLabel->structSize = sizeof(DmVertexLabelT);
#endif
    EXPECT_NE(vertexLabel, (void *)NULL);
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)(session->currentStmt->memCtx);
    int32_t res = DmSerializeVertexLabel(vertexLabel, &buffer);
    EXPECT_EQ(GMERR_OK, res);
    DmVertexLabelT *vertexLabelout = NULL;
    res = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &vertexLabelout);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(vertexLabel->commonInfo->heapInfo.maxVertexNum, vertexLabelout->commonInfo->heapInfo.maxVertexNum);
    QryTestReleaseSession(conn);
}

/*
 * 空vertexLabel名字测试
 */
TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_NULL_NAME)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"\", \"fields\":[{\"name\":\"F0\", \"type\":\"char\"},"
                "{\"name\":\"F1\", \"type\":\"uchar\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                "\"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. name len less than 0.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_UNIT8_DEFAULT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json2 =
        (char *)"[{\"type\":\"record\",\"name\":\"T40\",\"fields\":[{\"name\":\"F6\", "
                "\"type\":\"char\",\"default\":\"1\"},"
                "{\"name\":\"F7\", \"type\":\"uchar\",\"default\":\"2\"},{\"name\":\"F2\", \"type\":\"int8\", "
                "\"default\":8},{\"name\":\"F3\", \"type\":\"uint8\",\"default\":9},"
                "{\"name\":\"F4\", \"type\":\"int16\",   \"default\":20},{\"name\":\"F5\", \"type\":\"uint16\",  "
                "\"default\":30},{\"name\":\"F1\", \"type\":\"int32\",   \"default\":5},"
                "{\"name\":\"F0\", \"type\":\"uint32\"},{\"name\":\"F8\", \"type\":\"boolean\",   "
                "\"default\":true},{\"name\":\"F9\", \"type\":\"int64\",   \"default\":1000},"
                "{\"name\":\"F10\", \"type\":\"uint64\", \"default\":10000},"
                "{\"name\":\"F11\", \"type\":\"float\",  \"default\":1.58},"
                "{\"name\":\"F12\", \"type\":\"double\", \"default\":5.55},"
                "{\"name\":\"F13\", \"type\":\"time\",   \"default\":1000},"
                "{\"name\":\"F14\", \"type\":\"string\",  \"default\":\"testvertex\",\"size\":100 },"
                "{\"name\":\"F15\", \"type\":\"uint32\", \"default\":100}"
                "],"
                "\"keys\":["
                " {\"node\":\"T40\","
                "\"name\":\"T40_K0\","
                "\"fields\":[\"F0\"],"
                " \"index\":{\"type\":\"primary\"},"
                " \"constraints\":{\"unique\":true}"
                "}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json2) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json2;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_CHAR_DEFAULT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"char\",\"default\":\"a\"},"
                "{\"name\":\"F1\", \"type\":\"uchar\",\"default\":\"f\"},{\"name\":\"F2\", \"type\":\"int8\", "
                "\"default\":2},{\"name\":\"F3\", \"type\":\"int16\", \"default\":2}],"
                "\"keys\":[{\"node\":\"ffffff\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_PARSE_EDGE_LABEL)
{
    DrtConnectionT *conn = NULL;
    Status ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json =
        (char *)"[{\"name\":\"from_ip4_to_nhp_group\",\"source_vertex_label\":\"ip4forward\",\"comment\": \"the edge 7 "
                "to 8\","
                "\"dest_vertex_label\":\"nhp_group\",\"constraint\":{\"operator_type\":\"and\",\"conditions\":"
                "[{\"source_property\": \"nhp_group_id1\",\"dest_property\": \"nhp_group_id2\"},{\"source_property\": "
                "\"nhp_group_name1\",\"dest_property\": \"nhp_group_name2\"}]}}]";
    const char *edge_label = "from_ip4_to_nhp_group";
    const char *source_vertex_label = "ip4forward";
    const char *dest_vertex_label = "nhp_group";
    const char *source_property = "nhp_group_id1";
    const char *dest_property = "nhp_group_id2";
    const char *source_property2 = "nhp_group_name1";
    const char *dest_property2 = "nhp_group_name2";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    printf("labelJsonLen:%d\n", labelJson->len);
    printf("configJsonLen:%d\n", configJson->len);
    printf("labelJson:%s\n", labelJson->str);
    printf("configJson:%s\n", configJson->str);
    QryCreateEdgeLabelDescT desc;
    ret = QryParseEdgeLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbGaListGet(&desc.edgeLabels, 0);
    EXPECT_NE(edgeLabel, (void *)NULL);
    EXPECT_EQ((uint32_t)2, edgeLabel->edgeConstraint.conditionNum);
    EXPECT_EQ(DM_LOGICAL_OPERATOR_AND, edgeLabel->edgeConstraint.operatorType);
    if (strcmp(edgeLabel->sourceVertexName, source_vertex_label) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    if (strcmp(edgeLabel->destVertexName, dest_vertex_label) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    if (strcmp(edgeLabel->metaCommon.metaName, edge_label) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    if (strcmp(edgeLabel->edgeConstraint.edgeConditions[0].sourceVertexPropeName, source_property) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    if (strcmp(edgeLabel->edgeConstraint.edgeConditions[0].destVertexPropeName, dest_property) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    if (strcmp(edgeLabel->edgeConstraint.edgeConditions[1].sourceVertexPropeName, source_property2) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    if (strcmp(edgeLabel->edgeConstraint.edgeConditions[1].destVertexPropeName, dest_property2) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    QryTestReleaseSession(conn);
    printf("QRY_PARSE_EDGE_LABEL success\n");
}
TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_SUPER_FIELD)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"char\",\"default\":\"a\"},"
                "{\"name\":\"F1\", \"type\":\"uchar\",\"default\":\"f\"},{\"name\":\"F2\", \"type\":\"int8\", "
                "\"default\":2},{\"name\":\"F3\", \"type\":\"int16\", \"default\":2}],"
                "\"super_fields\":[{\"name\":\"superfield0\", \"comment\":\"test\", "
                "\"fields\":{\"begin\":\"F0\",\"end\":\"F1\"}},"
                "{\"name\":\"superfield1\", \"id\":1, \"comment\":\"test\", "
                "\"fields\":{\"begin\":\"F2\",\"end\":\"F3\"}}],"
                "\"keys\":[{\"node\":\"ffffff\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_SUPER_FIELD_NULL_NAME)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"char\",\"default\":\"a\"},"
                "{\"name\":\"F1\", \"type\":\"uchar\",\"default\":\"f\"},{\"name\":\"F2\", \"type\":\"int8\", "
                "\"default\":2},{\"name\":\"F3\", \"type\":\"int16\", \"default\":2}],"
                "\"super_fields\":[{\"name\":\"\", \"comment\":\"test\", \"fields\":{\"begin\":\"F0\",\"end\":\"F1\"}},"
                "{\"name\":\"superfield0\", \"id\":1, \"comment\":\"test\", "
                "\"fields\":{\"begin\":\"F2\",\"end\":\"F3\"}}],"
                "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Not normal property. name len less than 0.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_SUPER_FIELD_WITHOUT_COMMENT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json =
        (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[{\"name\":\"F0\", "
                "\"type\":\"char\",\"default\":\"a\"},"
                "{\"name\":\"F1\", \"type\":\"uchar\",\"default\":\"f\"},{\"name\":\"F2\", \"type\":\"int8\", "
                "\"default\":2},{\"name\":\"F3\", \"type\":\"int16\", \"default\":2}],"
                "\"super_fields\":[{\"name\":\"superfield0\", \"fields\":{\"begin\":\"F0\",\"end\":\"F1\"}},"
                "{\"name\":\"superfield1\", \"id\":1, \"comment\":\"test\", "
                "\"fields\":{\"begin\":\"F2\",\"end\":\"F3\"}}],"
                "\"keys\":[{\"node\":\"ffffff\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_FIELD_IS_NULL)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json = (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[],"
                                           "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                                           "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Array subscript unsucc. fields properties num should be (0, 1024).";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_FIELD_WITHOUT_TYPE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json = (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[{\"name\":\"F1\"}],"
                                           "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                                           "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Undefine column. type is undefined.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_FIELD_WITH_NULL_TYPE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000, \"delta_store_name\":\"cache\"}";
    char *test_normal_label_json = (char *)"[{\"type\":\"record\", \"name\":\"ffffff\", \"fields\":[{\"name\":\"F1\", "
                                           "\"type\":123,\"default\":\"f\"}],"
                                           "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                                           "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch. Type of \"type\" should be string.", lastError->str);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_WITHOUT_KEYS)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)R"([{
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {"name":"F0", "type":"char","default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar","default":"f", "nullable":false},
                    {"name":"F1", "type":"int8", "default":2, "nullable":false}
                ]
            }])";
    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_WITH_INDEXCAPACITY)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":"T37",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":["F0"],
                     "index":{"type":"primary"},
                     "config":{"init_hash_capacity":100}
                    }
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_WITH_PRIMARY_NON_UNIQUE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":"T37",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":["F0"],
                     "index":{"type":"primary"},
                     "constraints":{ "unique":false}
                    }
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Inv table definition. The primary key T37_K0 should be unique. label: T37 ", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_JSON_NAME_WITH_INVALID_VALUE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":12345,
            "fields":
                [
                    {"name":"F0", "type":"int32"}
                ],
            "keys":
                [
                    {"node":"T37",
                     "name":"T37_K0",
                     "fields":["F0"],
                     "index":{"type":"primary"},
                     "config":{"init_hash_capacity":100}
                    }
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch. name type should be string.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_JSON_WITH_INVALID_BOOL)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":"T7",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"boolean", "nullable":"xxx"}
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch. The bool value should be true, false, 0 or 1.Type: nullable should be bool.",
        lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_JSON_WITH_INVALID_UINT32)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":"T7",
            "fields":
                [
                    {"name":"F0", "type":"fixed","size":"xxx"}
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch.  size type should be uint.\n parse size of property F0.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_JSON_WITH_INVALID_UINT64)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":\"xxx\"}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":"T7",
            "fields":
                [
                    {"name":"F0", "type":"uint64"}
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ("Datatype mismatch.  max_record_count type should be uint.", lastError->str);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含int64default_value的最大值
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_INT64_HAVE_DEFAULT_VALUE_TYPE)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", "
                                         "\"type\":\"int64\", \"default\":9223372036854775807}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含property的comment和数组的comment
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_PROPERTY_COMMENT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", "
                                         "\"type\":\"int64\", \"comment\":\"F0 comment\"}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

/*
 * 测试包含property的comment和数组的comment
 */
TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_PROPERTY_COMMENT2)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int64\", \"comment\":\"\"}]}";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_NODE_COMMENT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"({
      "version": "2.0", "type": "record", "name": "vertexLabelTest1",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "string", "size":20, "nullable":true },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024, "comment":"yyy",
          "fields": [
            { "name": "b1", "type": "uint32" },
            { "name": "b2", "type": "uint32" }
          ]
        },
        { "name": "c3", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c4", "type": "record",
          "fixed_array": true, "size": 512, "comment":"",
          "fields": [
            { "name": "t1", "type": "uint32", "comment":""},
            { "name": "t2", "type": "string","size":8, "nullable":true}
          ]
        }
      ]
    })";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_PROPERTY_COMMENT_EXCEEDS_MAX)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char comment[513] = {0};
    for (int i = 0; i < 513; i++) {
        comment[i] = 'a';
    }
    comment[512] = '\0';
    char label_json[1024] =
        "{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int64\", \"comment\":\"";
    strcat(label_json, comment);
    strcat(label_json, "\"}]}");
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(label_json, JSON_REJECT_DUPLICATES);
    QrySetCfg("compatibleV3", "1");
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QrySetCfg("compatibleV3", "0");
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char
            *)"Not normal property. comment len exceeds limit 512, which is "
              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.";
    EXPECT_STREQ(result, lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_WTIH_STRING_DEFAULT)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"({
      "version": "2.0", "type": "record", "name": "vertexLabelTest1",
      "fields": [
        { "name": "int8", "type": "int8" },
        { "name": "int8_default_num", "type": "int8", "default" : -1 },
        { "name": "int8_default_string", "type": "int8", "default" : "-1" },
        { "name": "uint8", "type": "uint8" },
        { "name": "uint8_default_num", "type": "uint8", "default" : 1 },
        { "name": "uint8_default_string", "type": "uint8", "default" : "1" },
        { "name": "int16", "type": "int16" },
        { "name": "int16_default_num", "type": "int16", "default" : -1 },
        { "name": "int16_default_string", "type": "int16", "default" : "-1" },
        { "name": "uint16", "type": "uint16" },
        { "name": "uint16_default_num", "type": "uint16", "default" : 1 },
        { "name": "uint16_default_string", "type": "uint16", "default" : "1" },
        { "name": "int32", "type": "int32" },
        { "name": "int32_default_num", "type": "int32", "default" : -1 },
        { "name": "int32_default_string", "type": "int32", "default" : "-1" },
        { "name": "uint32", "type": "uint32" },
        { "name": "uint32_default_num", "type": "uint32", "default" : 1 },
        { "name": "uint32_default_string", "type": "uint32", "default" : "1" },
        { "name": "int64", "type": "int64" },
        { "name": "int64_default_num", "type": "int64", "default" : -1 },
        { "name": "int64_default_string", "type": "int64", "default" : "-1" },
        { "name": "uint64", "type": "uint64" },
        { "name": "uint64_default_num", "type": "uint64", "default" : 1 },
        { "name": "uint64_default_string", "type": "uint64", "default" : "1" },
        { "name": "float", "type": "float" },
        { "name": "float_default_num", "type": "float", "default" : 1.0 },
        { "name": "float_default_string", "type": "float", "default" : "1.0" },
        { "name": "double", "type": "double" },
        { "name": "double_default_num", "type": "double", "default" : 1.0 },
        { "name": "double_default_string", "type": "double", "default" : "1.0" }
      ]
    })";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 0
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[1].defaultValue->value.byteValue, -1);
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[2].defaultValue->value.byteValue, -1);

    // 3
    EXPECT_EQ((int8_t)vertexLabel.metaVertexLabel->schema->properties[4].defaultValue->value.ubyteValue, 1);
    EXPECT_EQ((int8_t)vertexLabel.metaVertexLabel->schema->properties[5].defaultValue->value.ubyteValue, 1);

    // 6
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[7].defaultValue->value.shortValue, -1);
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[8].defaultValue->value.shortValue, -1);

    // 9
    EXPECT_EQ((int16_t)vertexLabel.metaVertexLabel->schema->properties[10].defaultValue->value.ushortValue, 1);
    EXPECT_EQ((int16_t)vertexLabel.metaVertexLabel->schema->properties[11].defaultValue->value.ushortValue, 1);

    // 12
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[13].defaultValue->value.intValue, -1);
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[14].defaultValue->value.intValue, -1);

    // 15
    EXPECT_EQ((int32_t)vertexLabel.metaVertexLabel->schema->properties[16].defaultValue->value.uintValue, 1);
    EXPECT_EQ((int32_t)vertexLabel.metaVertexLabel->schema->properties[17].defaultValue->value.uintValue, 1);

    // 18
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[19].defaultValue->value.longValue, -1);
    EXPECT_EQ(vertexLabel.metaVertexLabel->schema->properties[20].defaultValue->value.longValue, -1);

    // 21
    EXPECT_EQ((int64_t)vertexLabel.metaVertexLabel->schema->properties[22].defaultValue->value.ulongValue, 1);
    EXPECT_EQ((int64_t)vertexLabel.metaVertexLabel->schema->properties[23].defaultValue->value.ulongValue, 1);

    // 24
    EXPECT_FLOAT_EQ(vertexLabel.metaVertexLabel->schema->properties[25].defaultValue->value.floatValue, 1.0);
    EXPECT_FLOAT_EQ(vertexLabel.metaVertexLabel->schema->properties[26].defaultValue->value.floatValue, 1.0);

    // 27
    EXPECT_DOUBLE_EQ(vertexLabel.metaVertexLabel->schema->properties[28].defaultValue->value.doubleValue, 1.0);
    EXPECT_DOUBLE_EQ(vertexLabel.metaVertexLabel->schema->properties[29].defaultValue->value.doubleValue, 1.0);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_WTIH_STRING_SIGNED_ERR)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"({
      "version": "2.0", "type": "record", "name": "vertexLabelTest1",
      "fields": [
        { "name": "int8", "type": "int8" },
        { "name": "int8_default_num", "type": "int8", "default" : 1 },
        { "name": "int8_default_string", "type": "int8", "default" : "1xx" }
      ]
    })";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Data exception occurs. property int8_default_string value (1xx) is not in right format.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_WTIH_STRING_UNSIGNED_ERR)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"({
      "version": "2.0", "type": "record", "name": "vertexLabelTest1",
      "fields": [
        { "name": "uint8", "type": "uint8" },
        { "name": "uint8_default_num", "type": "uint8", "default" : 1 },
        { "name": "uint8_default_string", "type": "int8", "default" : "" }
      ]
    })";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Data exception occurs. property uint8_default_string value () is not in right format.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_WTIH_STRING_FLOAT_ERR)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"({
      "version": "2.0", "type": "record", "name": "vertexLabelTest1",
      "fields": [
        { "name": "float", "type": "float" },
        { "name": "float_default_num", "type": "float", "default" : 1.0 },
        { "name": "float_default_string", "type": "float", "default" : "x1.0" }
      ]
    })";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Data exception occurs. property float_default_string value (x1.0) is not in right format.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_JSON_SCHEMA_PARSE_WTIH_STRING_DOUBLE_ERR)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    const char *test_normal_label_json = (char *)R"({
      "version": "2.0", "type": "record", "name": "vertexLabelTest1",
      "fields": [
        { "name": "double", "type": "double" },
        { "name": "double_default_num", "type": "double", "default" : 1.0 },
        { "name": "double_default_string", "type": "double", "default" : "1.0y" }
      ]
    })";
    DmVertexLabelT vertexLabel;
    (void)memset_s(&vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    MetaVertexLabelT metaVL;
    (void)memset_s(&metaVL, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel.metaVertexLabel = &metaVL;
    DbJsonT *jsonRootLabel = NULL;

    jsonRootLabel = DbJsonLoads(test_normal_label_json, JSON_REJECT_DUPLICATES);
    ret = QryParseSchema(session->currentStmt, jsonRootLabel, &(vertexLabel.metaVertexLabel->schema), false, NULL);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError = DbGetLastErrorInfo();
    EXPECT_STREQ(
        "Data exception occurs. property double_default_string value (1.0y) is not in right format.", lastError->str);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryJsonParse, QRY_VERTEX_LABEL_JSON_WITH_INT_BOOL)
{
    Status ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    SessionT *session = (SessionT *)conn->session;
    ret = QryAllocStmt(session, &session->currentStmt);
    ret = QryAllocCtxFromCtxMemPool(NULL, &session->currentStmt->context);
    char *test_normal_config_json = (char *)"{\"max_record_count\":1000}";
    char *test_normal_label_json = (char *)
        R"([{
            "type":"record",
            "name":"T7",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"boolean", "default":0}
                ]
            }])";

    int32_t labelJsonLen = strlen(test_normal_label_json) + 1;
    int32_t configJsonLen = strlen(test_normal_config_json) + 1;
    TextT *labelJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    labelJson->len = labelJsonLen;
    labelJson->str = test_normal_label_json;
    TextT *configJson = (TextT *)DbDynMemCtxAlloc(session->currentStmt->memCtx, sizeof(TextT));
    configJson->len = configJsonLen;
    configJson->str = test_normal_config_json;
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(session->currentStmt, labelJson, configJson, &desc);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}
