#include "gtest/gtest.h"
#include "common_init.h"
#include "db_mem_context.h"
#include "dm_cache_label_map.h"
#include "dm_meta_role.h"
#include "dm_cache_single_ver_mgr.h"
#include "ee_cmd.h"
#include "srv_data_fastpath.h"
#include "query_ut_base.h"
#include "ut_cata_common.h"
#include "ut_catalog_base.h"
#include "dm_meta_user.h"

DbMemCtxT *memCtxCataUtRole = NULL;
// clang-format off
const uint32_t g_sysPris[CATA_OBJ_TYPE_NUM] = {
    // vertexlabel 的权限，具体可以参考V5的用户手册中对象类型说明的章节。
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)TRUNCATE_PRIV  |
    (uint32_t)INSERTANY_PRIV    | (uint32_t)DELETEANY_PRIV  | (uint32_t)UPDATEANY_PRIV      | (uint32_t)SELECTANY_PRIV |
    (uint32_t)REPLACEANY_PRIV   | (uint32_t)MERGEANY_PRIV   | (uint32_t)ALTER_PRIV,

    // KV Table 的权限
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)TRUNCATE_PRIV  |
    (uint32_t)SELECTANY_PRIV    | (uint32_t)DELETEANY_PRIV  | (uint32_t)REPLACEANY_PRIV     | (uint32_t)ALTER_PRIV,

    // udf 的权限
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)ALTER_PRIV          | (uint32_t)INVOKEANY_PRIV,

    // namespace
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)ALTER_PRIV     |
    (uint32_t)USE_PRIV,

    // ROLE
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)ALTER_PRIV     |
    (uint32_t)GRANT_PRIV        | (uint32_t)REVOKE_PRIV,

    // user
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)ALTER_PRIV     |
    (uint32_t)GRANT_PRIV        | (uint32_t)REVOKE_PRIV,

    // tablespace
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV           | (uint32_t)BIND_PRIV,

    // EDGE
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)ALTER_PRIV     |
    (uint32_t)INSERTANY_PRIV | (uint32_t)DELETEANY_PRIV,

    // resource
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)ALTER_PRIV     |
    (uint32_t)BIND_PRIV         | (uint32_t)UNBIND_PRIV,

    // LABEL_SUBS 订阅不再具有特殊权限，转而判断是否拥有表的SELECT权限。
    0,

    // PATH SUBS
    (uint32_t)CREATE_PRIV       | (uint32_t)DROP_PRIV       | (uint32_t)GET_PRIV            | (uint32_t)ALTER_PRIV     |
    (uint32_t)SELECTANY_PRIV,

    // BINARY FILE
    (uint32_t)OPEN_PRIV       | (uint32_t)CLOSE_PRIV,

};
// clang-format on
class UtCataRole : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        memCtxCataUtRole =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo((DbMemCtxT *)memCtxCataUtRole);
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx((DbMemCtxT *)memCtxCataUtRole);
        BaseUninit();
    };

    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtCataRole, CataRoleCreateRoleTest)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    uint32_t roleId = 1;
    CreatePrivEntityPara para = {
        .userOrGroupStr = NULL,
        .roleName = roleName,
        .processName = processName,
        .metaType = CATA_USR,
        .roleId = roleId,
        .isUser = false,
        .isDBA = false,
        .dbInstance = NULL,
    };
    int32_t ret = CreateRoleNoLock(&para);
    EXPECT_EQ(ret, GMERR_OK);
    CataRoleT *role;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName, processName, &role), GMERR_OK);
    ret = GetRoleSysPrivInfoNoLock(NULL, roleId, role);
    EXPECT_EQ(ret, GMERR_OK);
    ret = DropRoleNoLock(roleId, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    CataDestroyRoleTest(memCtxCataUtRole, role);
}

TEST_F(UtCataRole, CataRoleGrantPrivTest)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    CataUserNameInfoT info;
    SetCataUserNameInfo(&info, NULL, roleName, processName);
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, roleName, processName);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    EXPECT_EQ(ret, GMERR_OK);
    PrivElemT *privElem = (PrivElemT *)DbDynMemCtxAlloc(memCtxCataUtRole, sizeof(PrivElemT));
    privElem->sysPrivs = CREATE_PRIV | DROP_PRIV | ALTER_PRIV;
    privElem->objType = CATA_VERTEX_LABEL;
    EXPECT_EQ(CataGrantSysPrivToGroup(false, &info, privElem, 1, NULL), GMERR_OK);
    CataRoleT *role;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName, processName, &role), GMERR_OK);
    ret = CataGetRoleSysPrivInfo(roleName, processName, false, role);
    EXPECT_EQ(ret, GMERR_OK);

    PrivToCheckT privToCheck = {.sysPrivToCheck = CREATE_PRIV, .objPrivToCheck = 0};
    bool hasPriv = CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL);
    EXPECT_EQ(hasPriv, true);
    ret = CataDropGroup(&info, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    DbDynMemFree(privElem);
    CataDestroyRoleTest(memCtxCataUtRole, role);
}

TEST_F(UtCataRole, CataRoleRevokePrivTest)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    CataUserNameInfoT info;
    SetCataUserNameInfo(&info, roleName, roleName, processName);
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, roleName, roleName, processName);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    EXPECT_EQ(ret, GMERR_OK);
    PrivElemT *privElem = (PrivElemT *)DbDynMemCtxAlloc(memCtxCataUtRole, sizeof(PrivElemT));
    privElem->sysPrivs = CREATE_PRIV | DROP_PRIV | ALTER_PRIV;
    privElem->objType = CATA_VERTEX_LABEL;
    EXPECT_EQ(CataGrantSysPrivToGroup(false, &info, privElem, 1, NULL), GMERR_OK);
    CataRoleT *role1 = NULL;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName, processName, &role1), GMERR_OK);
    ret = CataGetRoleSysPrivInfo(roleName, processName, false, role1);
    EXPECT_EQ(ret, GMERR_OK);

    PrivToCheckT privToCheck = {.sysPrivToCheck = CREATE_PRIV, .objPrivToCheck = 0};
    bool hasPriv = CataCheckPriv(role1, CATA_VERTEX_LABEL, privToCheck, NULL);
    EXPECT_EQ(hasPriv, true);
    CataDestroyRoleTest(memCtxCataUtRole, role1);
    EXPECT_EQ(CataRevokeSysPrivFromGroup(&info, privElem, 1, NULL), GMERR_OK);
    CataRoleT *role2 = NULL;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName, processName, &role2), GMERR_OK);
    ret = CataGetRoleSysPrivInfo(roleName, processName, false, role2);
    EXPECT_EQ(ret, GMERR_OK);

    hasPriv = CataCheckPriv(role2, CATA_VERTEX_LABEL, privToCheck, NULL);
    EXPECT_EQ(hasPriv, false);
    ret = CataDropGroup(&info, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    DbDynMemFree(privElem);
    CataDestroyRoleTest(memCtxCataUtRole, role2);
}

TEST_F(UtCataRole, CataRoleDropTest)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    uint32_t roleId = 1;
    CreatePrivEntityPara para = {
        .userOrGroupStr = NULL,
        .roleName = roleName,
        .processName = processName,
        .metaType = CATA_USR,
        .roleId = roleId,
        .isUser = false,
        .isDBA = false,
        .dbInstance = NULL,
    };
    int32_t ret = CreateRoleNoLock(&para);
    uint32_t metaNameLen = strlen(roleName) + strlen(processName) + 1 + 1;
    char metaName[metaNameLen];
    sprintf_s(metaName, metaNameLen, "%s:%s", roleName, processName);
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, metaName);

    CataRoleT *role = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_RL], roleId, (DmMetaCommonT **)&role);
    EXPECT_EQ(GMERR_OK, ret);
    DbAtomicInc(&(role->objPrivRefCount));
    ret = DropRoleNoLock(roleId, NULL);
    EXPECT_NE(ret, GMERR_OK);
    DbAtomicDec(&(role->objPrivRefCount));
    ret = DropRoleNoLock(roleId, NULL);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(UtCataRole, CataRoleException)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    uint32_t roleId = 1;
    CreatePrivEntityPara para = {
        .userOrGroupStr = NULL,
        .roleName = roleName,
        .processName = processName,
        .metaType = CATA_USR,
        .roleId = roleId,
        .isUser = false,
        .isDBA = false,
        .dbInstance = NULL,
    };
    int32_t ret = CreateRoleNoLock(&para);
    EXPECT_EQ(ret, GMERR_OK);
    ret = CreateRoleNoLock(&para);
    EXPECT_NE(ret, GMERR_OK);
    char roleName1[5] = "user";

    CataRoleT *role = NULL;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName1, processName, &role), GMERR_OK);
    ret = GetRoleSysPrivInfoNoLock(NULL, 2, role);
    EXPECT_NE(ret, GMERR_OK);
    ret = DropRoleNoLock(2, NULL);
    EXPECT_NE(ret, GMERR_OK);
    ret = DropRoleNoLock(roleId, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    CataDestroyRoleTest(memCtxCataUtRole, role);
}

TEST_F(UtCataRole, GetRoleById)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    uint32_t roleId = 1;
    CreatePrivEntityPara para = {
        .userOrGroupStr = NULL,
        .roleName = roleName,
        .processName = processName,
        .metaType = CATA_USR,
        .roleId = roleId,
        .isUser = false,
        .isDBA = false,
        .dbInstance = NULL,
    };
    int32_t ret = CreateRoleNoLock(&para);
    EXPECT_EQ(ret, GMERR_OK);

    // 通过id获取role，引用计数改变
    CataRoleT *role1 = NULL;
    EXPECT_EQ(GMERR_OK, CataGetRoleById(roleId, &role1, NULL));
    EXPECT_EQ((int32_t)role1->metaCommon.refCount, 1);
    EXPECT_EQ(role1->metaCommon.isDeleted, false);

    // 删除role，此时应该无法删除，因为refCount不为0
    EXPECT_EQ(GMERR_OK, DropRoleNoLock(roleId, NULL));
    EXPECT_EQ((int32_t)role1->metaCommon.refCount, 1);
    EXPECT_EQ(role1->metaCommon.isDeleted, true);

    // 从catalog中再次查询
    CataRoleT *role2 = NULL;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName, processName, &role2), GMERR_OK);

    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GetRoleSysPrivInfoNoLock(NULL, roleId, role2));

    // 释放占用的role，引用计数降低，然后内部被删除
    EXPECT_EQ(GMERR_OK, CataReleaseRole(role1));
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    // 再次查询失败
    EXPECT_EQ(
        GMERR_UNDEFINED_TABLE, SingleVersionGetById(cataCacheMgr->metaCache[CATA_RL], roleId, (DmMetaCommonT **)role1));

    CataDestroyRoleTest(memCtxCataUtRole, role2);
}

void GrantAndCheckSysPrivsToRole(CataUserNameInfoT *info, PrivElemT *privElem, const CataObjTypeE objType)
{
    for (uint32_t i = 0; i < SYS_PRIV_TYPE_NUM; i++) {
        privElem->sysPrivs |= (CataSysPrivTypeE)(1u << i);
    }
    privElem->objType = objType;
    EXPECT_EQ(CataGrantSysPrivToGroup(false, info, privElem, 1, NULL), GMERR_OK);

    CataRoleT *role;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, info->groupName, info->processName, &role), GMERR_OK);

    const uint32_t *sysPrivs = g_sysPris;
    EXPECT_EQ(CataGetRoleSysPrivInfo(info->groupName, info->processName, false, role), GMERR_OK);
    EXPECT_EQ((int32_t)(role->sysPrivileges[objType].privilege & (~sysPrivs[objType])), 0);
    EXPECT_EQ((role->sysPrivileges[objType].privilege & sysPrivs[objType]), role->sysPrivileges[objType].privilege);

    CataDestroyRoleTest(memCtxCataUtRole, role);
}

// 该用例给角色赋予所有的权限，然后check是否过滤了不该拥有的权限。
// 给对象赋予一些该对象不该拥有的权限，此时的策略是不报错也不赋予该权限。
TEST_F(UtCataRole, CataRoleGrantPrivMoreTest)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    CataUserNameInfoT info;
    SetCataUserNameInfo(&info, NULL, roleName, processName);
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, NULL, roleName, processName);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    EXPECT_EQ(ret, GMERR_OK);

    PrivElemT *privElem = (PrivElemT *)DbDynMemCtxAlloc(memCtxCataUtRole, sizeof(PrivElemT));
    for (uint32_t i = 0; i < CATA_OBJ_TYPE_NUM; i++) {
        GrantAndCheckSysPrivsToRole(&info, privElem, (const CataObjTypeE)i);
    }

    ret = CataDropGroup(&info, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    DbDynMemFree(privElem);
}

// 给role赋予一些权限，check可以有的和不该拥有的权限。
TEST_F(UtCataRole, CataRoleGrantPrivVertexLabelTest)
{
    char roleName[5] = "role";
    char processName[5] = "root";
    CataUserNameInfoT info;
    SetCataUserNameInfo(&info, roleName, roleName, processName);
    CataUserInfoT userInfo = {0};
    SetCataUserNameInfo(&userInfo.userNameInfo, roleName, roleName, processName);
    (void)CataGenerateUuid(NULL, &userInfo.userId);
    int32_t ret = CataCreateGroup(NULL, &userInfo);
    EXPECT_EQ(ret, GMERR_OK);
    PrivElemT *privElem = (PrivElemT *)DbDynMemCtxAlloc(memCtxCataUtRole, sizeof(PrivElemT));
    privElem->sysPrivs = CREATE_PRIV | DROP_PRIV | ALTER_PRIV | BIND_PRIV | USE_PRIV | INSERTANY_PRIV;
    privElem->objType = CATA_VERTEX_LABEL;
    EXPECT_EQ(CataGrantSysPrivToGroup(false, &info, privElem, 1, NULL), GMERR_OK);

    CataRoleT *role;
    EXPECT_EQ(GenerateRoleTest(memCtxCataUtRole, roleName, processName, &role), GMERR_OK);
    ret = CataGetRoleSysPrivInfo(roleName, processName, false, role);
    EXPECT_EQ(ret, GMERR_OK);

    PrivToCheckT privToCheck = {.sysPrivToCheck = CREATE_PRIV, .objPrivToCheck = 0};
    EXPECT_EQ((CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL)), true);
    privToCheck.sysPrivToCheck = DROP_PRIV;
    EXPECT_EQ((CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL)), true);
    privToCheck.sysPrivToCheck = ALTER_PRIV;
    EXPECT_EQ((CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL)), true);
    privToCheck.sysPrivToCheck = BIND_PRIV;
    EXPECT_EQ((CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL)), false);
    privToCheck.sysPrivToCheck = USE_PRIV;
    EXPECT_EQ((CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL)), false);
    privToCheck.sysPrivToCheck = INSERTANY_PRIV;
    EXPECT_EQ((CataCheckPriv(role, CATA_VERTEX_LABEL, privToCheck, NULL)), true);

    ret = CataDropGroup(&info, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    DbDynMemFree(privElem);
    CataDestroyRoleTest(memCtxCataUtRole, role);
}
