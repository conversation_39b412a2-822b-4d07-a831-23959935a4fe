/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: ut file for data model udf
 * Author: zhangjinglong
 * Create: 2022-9-6
 */
#ifdef FEATURE_DATALOG
#include <securec.h>
#include <string>
#include <thread>
#include <vector>
#include "gtest/gtest.h"
#include "common_init.h"
#include "adpt_memory.h"
#include "adpt_atomic.h"
#include "db_common_init.h"
#include "db_mem_context.h"
#include "dm_cache_label_map.h"
#include "dm_meta_basic.h"
#include "query_ut_base.h"
#include "ut_catalog_base.h"
#include "ut_dm_fuzz_test.h"

using namespace std;

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

namespace query_ut {

void FillProperty(DmPropertySchemaT *property)
{
    property->nameLen = DM_STR_LEN("c1");
    property->name = (char *)DbDynMemAlloc(property->nameLen);
    errno_t err = strcpy_s(property->name, property->nameLen, "c1");
    EXPECT_EQ(EOK, err);
}

static bool IsPropertySchemaEqual(const DmPropertySchemaT *prop1, const DmPropertySchemaT *prop2)
{
    // 当前暂不支持constraint
    DB_ASSERT(prop1->constraint == NULL && prop2->constraint == NULL);

    if (prop1 == prop2) {
        return true;
    }

    if (prop1->propeId != prop2->propeId) {
        return false;
    }
    if (prop1->dataType != prop2->dataType) {
        return false;
    }
    if (prop1->sysPropeType != prop2->sysPropeType) {
        return false;
    }
    if (prop1->size != prop2->size) {
        return false;
    }
    if (prop1->nameLen != prop2->nameLen) {
        return false;
    }
    if (prop1->commentLen != prop2->commentLen) {
        return false;
    }
    if (prop1->isValid != prop2->isValid) {
        return false;
    }
    if (prop1->isNullable != prop2->isNullable) {
        return false;
    }
    if (prop1->isFixed != prop2->isFixed) {
        return false;
    }
    if (prop1->isValid != prop2->isValid) {
        return false;
    }
    if (prop1->isResource != prop2->isResource) {
        return false;
    }
    if (prop1->isSysPrope != prop2->isSysPrope) {
        return false;
    }
    if (prop1->isAutoIncProp != prop2->isAutoIncProp) {
        return false;
    }
    if (prop1->isSensitive != prop2->isSensitive) {
        return false;
    }
    if (prop1->isInPrimKey != prop2->isInPrimKey) {
        return false;
    }
    if (prop1->isNullable != prop2->isNullable) {
        return false;
    }
    if (prop1->defaultValueNum != prop2->defaultValueNum) {
        return false;
    }
    if (prop1->parentId != prop2->parentId) {
        return false;
    }
    if (prop1->hasConstraint != prop2->hasConstraint) {
        return false;
    }
    if (prop1->isConfig != prop2->isConfig) {
        return false;
    }

    DB_ASSERT(prop1->name != NULL && prop2->name != NULL);
    if (strcmp(prop1->name, prop2->name) != 0) {
        return false;
    }

    if (prop1->comment != NULL && prop2->comment != NULL) {
        if (strcmp(prop1->comment, prop2->comment) != 0) {
            return false;
        }
    } else if ((prop1->comment != NULL && prop2->comment == NULL) ||
               (prop1->comment == NULL && prop2->comment != NULL)) {
        return false;
    }

    if (!DmValueIsEqual((const DmValueT *)&prop1->defaultValue, (const DmValueT *)&prop2->defaultValue)) {
        return false;
    }

    return true;
}

void InitialBaseUdfValueWithId(DmUdfBaseT *udf, uint32_t udfId)
{
    udf->metaCommon.dbId = 0;
    udf->metaCommon.metaId = udfId;
    udf->metaCommon.namespaceId = 0;
    char *name = (char *)DbDynMemAlloc(DM_MAX_NAME_LENGTH);
    sprintf_s(name, DM_MAX_NAME_LENGTH, "udf%" PRIu32 "", udfId);
    udf->metaCommon.metaName = name;
    udf->funcHandle = (void *)0X1;

    udf->accessDeltaNum = 2;
    udf->accessDeltaNames = (char **)DbDynMemAlloc(udf->accessDeltaNum * sizeof(char **));
    udf->accessDeltaNames[0] = (char *)DbDynMemAlloc(DM_STR_LEN("deltaname1"));
    errno_t err = strcpy_s(udf->accessDeltaNames[0], DM_STR_LEN("deltaname1"), "deltaname1");
    EXPECT_EQ(EOK, err);
    udf->accessDeltaNames[1] = (char *)DbDynMemAlloc(DM_STR_LEN("deltaname2"));
    err = strcpy_s(udf->accessDeltaNames[1], DM_STR_LEN("deltaname2"), "deltaname2");
    EXPECT_EQ(EOK, err);

    udf->accessOrgNum = 2;
    udf->accessOrgNames = (char **)DbDynMemAlloc(udf->accessOrgNum * sizeof(char **));
    udf->accessOrgNames[0] = (char *)DbDynMemAlloc(DM_STR_LEN("orgname1"));
    err = strcpy_s(udf->accessOrgNames[0], DM_STR_LEN("orgname1"), "orgname1");
    EXPECT_EQ(EOK, err);
    udf->accessOrgNames[1] = (char *)DbDynMemAlloc(DM_STR_LEN("orgname2"));
    err = strcpy_s(udf->accessOrgNames[1], DM_STR_LEN("orgname2"), "orgname2");
    EXPECT_EQ(EOK, err);
    udf->multiVersionShare->soId = 0;
}

void InitialCmpUdfValue(DmCmpUdfT *udf)
{
    InitialBaseUdfValueWithId(udf, 0);
}

void InitialCmpUdfValueWithId(DmCmpUdfT *udf, uint32_t udfId)
{
    InitialBaseUdfValueWithId(udf, udfId);
}

void InitialNormalUdfValue(DmNormalUdf *udf)
{
    InitialBaseUdfValueWithId(&udf->base, 0);
    udf->inputPropNum = 2;
    uint32_t inputPropsSize = udf->inputPropNum * sizeof(DmPropertySchemaT);
    udf->inputProps = (DmPropertySchemaT *)DbDynMemAlloc(inputPropsSize);
    (void)memset_s(udf->inputProps, inputPropsSize, 0, inputPropsSize);
    for (uint32_t i = 0; i < udf->inputPropNum; i++) {
        FillProperty(&udf->inputProps[i]);
    }
    udf->outputPropNum = 3;
    uint32_t outputPropsSize = udf->outputPropNum * sizeof(DmPropertySchemaT);
    udf->outputProps = (DmPropertySchemaT *)DbDynMemAlloc(outputPropsSize);
    (void)memset_s(udf->outputProps, outputPropsSize, 0, outputPropsSize);
    for (uint32_t i = 0; i < udf->outputPropNum; i++) {
        FillProperty(&udf->outputProps[i]);
    }
}

void InitialTimeoutUdfValue(DmTimeoutUdfT *udf)
{
    InitialBaseUdfValueWithId(udf, 0);
}

void InitialAggUdfValue(DmAggUdfT *udf)
{
    InitialNormalUdfValue(&udf->normal);
    udf->cmpUdfName = (char *)DbDynMemAlloc(DM_STR_LEN("aggcmpudfname"));
    errno_t err = strcpy_s(udf->cmpUdfName, DM_STR_LEN("aggcmpudfname"), "udf");
    EXPECT_EQ(EOK, err);
}

uint32_t IsBaseUdfEqual(DmUdfBaseT *udf, DmUdfBaseT *retUdf)
{
    if (udf->metaCommon.dbId != retUdf->metaCommon.dbId)
        return false;
    if (udf->metaCommon.namespaceId != retUdf->metaCommon.namespaceId)
        return false;
    if (udf->metaCommon.metaId != retUdf->metaCommon.metaId)
        return false;
    if (strcmp(udf->metaCommon.metaName, retUdf->metaCommon.metaName) != 0)
        return false;
    if (udf->udfType != retUdf->udfType)
        return false;
    if (udf->funcHandle != retUdf->funcHandle)
        return false;
    if (udf->accessDeltaNum != retUdf->accessDeltaNum)
        return false;
    if (udf->accessOrgNum != retUdf->accessOrgNum)
        return false;
    for (uint32_t i = 0; i < udf->accessDeltaNum; i++) {
        if (strcmp(udf->accessDeltaNames[i], retUdf->accessDeltaNames[i]) != 0)
            return false;
    }
    for (uint32_t i = 0; i < udf->accessOrgNum; i++) {
        if (strcmp(udf->accessOrgNames[i], retUdf->accessOrgNames[i]) != 0)
            return false;
    }
    return true;
}

uint32_t IsCmpUdfEqual(DmCmpUdfT *udf, DmCmpUdfT *retUdf)
{
    return IsBaseUdfEqual(udf, retUdf);
}

uint32_t IsNormalUdfEqual(DmNormalUdfT *udf, DmNormalUdfT *retUdf)
{
    if (!IsBaseUdfEqual(&udf->base, &retUdf->base))
        return false;
    if (udf->inputPropNum != retUdf->inputPropNum)
        return false;
    if (udf->outputPropNum != retUdf->outputPropNum)
        return false;
    for (uint32_t i = 0; i < udf->inputPropNum; i++) {
        if (!IsPropertySchemaEqual(&udf->inputProps[i], &retUdf->inputProps[i]))
            return false;
    }
    for (uint32_t i = 0; i < udf->outputPropNum; i++) {
        if (!IsPropertySchemaEqual(&udf->outputProps[i], &retUdf->outputProps[i]))
            return false;
    }
    return true;
}

uint32_t IsTimeoutUdfEqual(DmTimeoutUdfT *udf, DmTimeoutUdfT *retUdf)
{
    return IsBaseUdfEqual(udf, retUdf);
}

bool IsAggUdfEqual(DmAggUdfT *udf, DmAggUdfT *retUdf)
{
    if (!IsNormalUdfEqual(&udf->normal, &retUdf->normal))
        return false;
    if (strcmp(udf->cmpUdfName, retUdf->cmpUdfName) != 0)
        return false;
    if (udf->isOrdered != retUdf->isOrdered)
        return false;
    if (udf->isManyToMany != retUdf->isManyToMany)
        return false;
    return true;
}

DbMemCtxT *memCtxUdf = NULL;
class UtCataUdf : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        memCtxUdf = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo((DbMemCtxT *)memCtxUdf);

        // 因为所有的元数据创建和删除都需要维护所属namespace的labelCount,同时ut中元数据的namespaceId设置都为0，
        // 因此在测试环境的初始化中创建了id为0的namespace
        DmNamespaceT *namespaceInfo = CreateNspWithId(0, (DbMemCtxT *)memCtxUdf);
        EXPECT_EQ(GMERR_OK, CataCreateNamespace(namespaceInfo, NULL));
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx((DbMemCtxT *)memCtxUdf);
        BaseUninit();
    };

    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

void CreateUdf(uint32_t id, char *name, DmUdfTypeE udfType, DmUdfBaseT **udf)
{
    DmCreateEmptyUdfWithMemCtx(memCtxUdf, udfType, (DmUdfBaseT **)udf);
    (*udf)->metaCommon.metaId = id;
    (*udf)->metaCommon.metaName = (char *)DbDynMemAlloc(DM_STR_LEN(name));
    EXPECT_EQ(EOK, strcpy_s((*udf)->metaCommon.metaName, DM_STR_LEN(name), name));
    (*udf)->multiVersionShare = (UdfMultiVersionShareT *)DbDynMemCtxAlloc(memCtxUdf, sizeof(UdfMultiVersionShareT));
    errno_t err = memset_s((*udf)->multiVersionShare, sizeof(UdfMultiVersionShareT), 0, sizeof(UdfMultiVersionShareT));
    EXPECT_EQ(EOK, err);
}

TEST_F(UtCataUdf, CataSaveCmpUdfAndGetByIdAndName)
{
    DmCmpUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&udf);
    InitialCmpUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    uint32_t udfId = udf->metaCommon.metaId;
    DmCmpUdfT *retUdf;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, udf->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetUdfById(udfId, &retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataGetUdfByName(&cataKey, &retUdf, NULL));
    EXPECT_TRUE(IsCmpUdfEqual(udf, retUdf));

    EXPECT_EQ(GMERR_OK, CataReleaseUdf(retUdf));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf(retUdf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udfId));
}

TEST_F(UtCataUdf, CataSaveNormalUdfAndGetByIdAndName)
{
    DmNormalUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    InitialNormalUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    uint32_t udfId = udf->base.metaCommon.metaId;
    DmNormalUdfT *retUdf;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, udf->base.metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetUdfById(udfId, (DmUdfBaseT **)&retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataGetUdfByName(&cataKey, (DmUdfBaseT **)&retUdf, NULL));
    EXPECT_TRUE(IsNormalUdfEqual(udf, retUdf));

    EXPECT_EQ(GMERR_OK, CataReleaseUdf((DmUdfBaseT *)retUdf));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf((DmUdfBaseT *)retUdf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udfId));
}

TEST_F(UtCataUdf, CataSaveTimeOutUdfAndGetByIdAndName)
{
    DmTimeoutUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_TIMEOUT, (DmUdfBaseT **)&udf);
    InitialTimeoutUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    uint32_t udfId = udf->metaCommon.metaId;
    DmTimeoutUdfT *retUdf;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, udf->metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetUdfById(udfId, (DmUdfBaseT **)&retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataGetUdfByName(&cataKey, (DmUdfBaseT **)&retUdf, NULL));
    EXPECT_TRUE(IsTimeoutUdfEqual(udf, retUdf));

    EXPECT_EQ(GMERR_OK, CataReleaseUdf((DmUdfBaseT *)retUdf));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf((DmUdfBaseT *)retUdf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udfId));
}

TEST_F(UtCataUdf, CataSaveAggUdfAndGetByIdAndName)
{
    DmAggUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_AGGREGATION, (DmUdfBaseT **)&udf);
    InitialAggUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    uint32_t udfId = udf->normal.base.metaCommon.metaId;
    DmAggUdfT *retUdf;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, udf->normal.base.metaCommon.metaName);
    EXPECT_EQ(GMERR_OK, CataGetUdfById(udfId, (DmUdfBaseT **)&retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataGetUdfByName(&cataKey, (DmUdfBaseT **)&retUdf, NULL));
    EXPECT_TRUE(IsAggUdfEqual(udf, retUdf));

    EXPECT_EQ(GMERR_OK, CataReleaseUdf((DmUdfBaseT *)retUdf));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf((DmUdfBaseT *)retUdf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udfId));
}

TEST_F(UtCataUdf, CataSaveUdfAndGetByInvalidIdAndName)
{
    DmCmpUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&udf);
    InitialCmpUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    uint32_t invalidUdfId = 100;
    const char *invalidUdfName = "invalid udf name";
    DmCmpUdfT *retUdf;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, invalidUdfName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataGetUdfById(invalidUdfId, &retUdf, NULL));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataGetUdfByName(&cataKey, &retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udf->metaCommon.metaId));
}

TEST_F(UtCataUdf, CataRemoveUdfById)
{
    DmCmpUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&udf);
    InitialCmpUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    DmCmpUdfT *retUdf;
    EXPECT_EQ(GMERR_OK, CataGetUdfById(udf->metaCommon.metaId, &retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf(retUdf));

    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udf->metaCommon.metaId));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataGetUdfById(udf->metaCommon.metaId, &retUdf, NULL));
}

TEST_F(UtCataUdf, CataRemoveUdfByInvalidId)
{
    DmCmpUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&udf);
    InitialCmpUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    uint32_t invalidUdfId = 100;
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataRemoveUdfById(NULL, invalidUdfId));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udf->metaCommon.metaId));
}

TEST_F(UtCataUdf, CataSaveSameUdf)
{
    DmCmpUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&udf);
    InitialCmpUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    DmCmpUdfT *sameUdf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&sameUdf);
    InitialCmpUdfValue(sameUdf);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, CataSaveUdf((DmUdfBaseT *)sameUdf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udf->metaCommon.metaId));
}

TEST_F(UtCataUdf, CataRemoveAndReleaseUdf)
{
    DmCmpUdfT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_CMP, (DmUdfBaseT **)&udf);
    InitialCmpUdfValue(udf);
    EXPECT_EQ(GMERR_OK, CataSaveUdf((DmUdfBaseT *)udf));

    DmCmpUdfT *retUdf1;
    EXPECT_EQ(GMERR_OK, CataGetUdfById(udf->metaCommon.metaId, &retUdf1, NULL));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, udf->metaCommon.metaId));
    DmCmpUdfT *retUdf2;
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataGetUdfById(udf->metaCommon.metaId, &retUdf2, NULL));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf(retUdf1));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataGetUdfById(udf->metaCommon.metaId, &retUdf1, NULL));
}

TEST_F(UtCataUdf, CataCreateUdfAndAlterUdf)
{
    DmUdfBaseT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));
    udf->metaCommon.version = 2;
    EXPECT_EQ(GMERR_OK, CataAlterUdf(udf));

    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 0));
}

TEST_F(UtCataUdf, CataGetUdfByNameAndId)
{
    DmUdfBaseT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));
    udf->metaCommon.version = 2;
    EXPECT_EQ(GMERR_OK, CataAlterUdf(udf));
    udf->metaCommon.version = 3;
    EXPECT_EQ(GMERR_OK, CataAlterUdf(udf));
    CataKeyT cataKey = {0};
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, udf->metaCommon.namespaceId, udf->metaCommon.metaName);

    DmUdfBaseT *udf1 = NULL;
    DmUdfBaseT *udf2 = NULL;
    EXPECT_EQ(GMERR_OK, CataGetUdfByName(&cataKey, &udf1, NULL));
    EXPECT_EQ((uint32_t)3, udf1->metaCommon.version);
    EXPECT_EQ(GMERR_OK, CataGetUdfById(0, &udf2, NULL));
    EXPECT_EQ((uint32_t)3, udf2->metaCommon.version);

    EXPECT_EQ(udf1, udf2);

    EXPECT_EQ(GMERR_OK, CataReleaseUdf(udf1));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf(udf2));

    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 0));
}

TEST_F(UtCataUdf, CataAlterUdfCheckidExist)
{
    DmUdfBaseT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));

    CreateUdf(1, (char *)"udftest1", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));

    CreateUdf(0, (char *)"udftest1", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 2;
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataAlterUdf(udf));

    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 0));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 1));

    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));

    // 将id标记删除
    DmUdfBaseT *retUdf = NULL;
    EXPECT_EQ(GMERR_OK, CataGetUdfById(0, &retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 0));
    // 此时创建id为1的label或alter均失败
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, CataSaveUdf(udf));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, CataAlterUdf(udf));
    // release后label被物理删除
    EXPECT_EQ(GMERR_OK, CataReleaseUdf(retUdf));
    // 创建id为1的label成功
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 0));
}

TEST_F(UtCataUdf, CataAlterUdfCheckNameExist)
{
    DmUdfBaseT *udf = NULL;
    CreateUdf(0, (char *)"udftest", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));
    udf->metaCommon.version = 2;
    EXPECT_EQ(GMERR_OK, CataAlterUdf(udf));

    // 已存在相同名字的udf，创建失败
    CreateUdf(1, (char *)"udftest", DM_UDF_NORMAL, (DmUdfBaseT **)&udf);
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, CataSaveUdf(udf));

    // 将udf的version1删除，version2标记删除，此时可以创建相同名字的udf
    DmUdfBaseT *retUdf = NULL;
    EXPECT_EQ(GMERR_OK, CataGetUdfById(0, &retUdf, NULL));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 0));
    udf->metaCommon.version = 1;
    EXPECT_EQ(GMERR_OK, CataSaveUdf(udf));
    EXPECT_EQ(GMERR_OK, CataRemoveUdfById(NULL, 1));
    EXPECT_EQ(GMERR_OK, CataReleaseUdf(retUdf));
}

}  // namespace query_ut

#endif  // FEATURE_DATALOG
