#include "query_ut_base.h"
#include "db_text.h"
#include "ee_dml_subs.h"
#include "drt_send_queue.h"
#include "ut_catalog_base.h"

class UtQueryDDLSubs : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        dyAlgoCtxVertex = QryGetDyAlgoCtxVertexBase();
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

void QryDDLCreateVertexLabel(bool isStatusMerge = false)
{
    uint32_t ret;

    char *cfgJson1 = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    const char *cfgJson2 = R"({
        "max_record_count":1000,
        "isFastReadUncommitted":false,
        "status_merge_sub":true
        })";
    char *labelJson1 = (char *)R"([{
        "type":"record",
        "name":"ip4forward",
        "fields":[
            {"name":"nhp_group_id1", "type":"int32"},
            {"name":"nhp_group_name1", "type":"int32"},
            {"name":"subs_string", "type":"string", "size":8},
            {"name":"subs_char", "type":"char"},
            {"name":"subs_int32", "type":"int32"},
            {"name":"subs_int8", "type":"int8"},
            {"name":"subs_uint8", "type":"uint8"},
            {"name":"subs_int16", "type":"int16"},
            {"name":"subs_uint16", "type":"uint16"},
            {"name":"subs_uint32", "type":"uint32"},
            {"name":"subs_bytes", "type":"bytes", "size":20, "nullable":true},
            {"name":"subs_bytes2", "type":"bytes", "size":20, "default":"FFFF"},
            {"name":"subs_bytes3", "type":"bytes", "size":20, "default":"0xFFFF"},
            {"name":"subs_fixed", "type":"fixed", "default":"ffff", "size":4},
            {"name":"subs_fixed2", "type":"fixed", "default":"0xffffffff", "size":4},
            {"name":"subs_bit8", "type":"uint8:3", "default":1},
            {"name":"subs_bit8_2", "type":"uint8:5", "default":"0xA"},
            {"name":"subs_bit16", "type":"uint16:3", "default":1},
            {"name":"subs_bit16_2", "type":"uint16:13", "default":"0xa"},
            {"name":"subs_bit32", "type":"uint32:3", "default":1},
            {"name":"subs_bit32_2", "type":"uint32:29", "default":"0xa"},
            {"name":"subs_bit64", "type":"uint64:3", "default":1},
            {"name":"subs_bit64_2", "type":"uint64:61", "default":"0xa"},
            {"name":"subs_F7_partion", "type":"partition", "nullable":false}
        ],
        "keys":
        [
            {"node":"ip4forward", "name":"ip4forward_K0", "fields":["nhp_group_id1"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(isStatusMerge ? (char *)cfgJson2 : cfgJson1, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateSubsWithFixedAndBytes(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"subs_FixedAndBytes";
    char subsJson[] = R"({
            "label_name":"ip4forward",
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_fixed",
                                "value":"0x66666666"
                            },
                            {
                                "property":"subs_bytes",
                                "value":"0x66666666"
                            },
                            {
                                "property":"subs_F7_partion",
                                "value":1
                            }
                        ]
                }
        })";
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_OK, ret);
    DmSubscriptionT *subscription;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, subsName);
    EXPECT_EQ(GMERR_OK, CataGetSubsByName(&cataKey, &subscription, NULL));
    if (memcmp("subs_fixed", subscription->subsConstraint.conditions[0].pathInfo.namePath[0],
            strlen(subscription->subsConstraint.conditions[0].pathInfo.namePath[0]) + 1) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        printf("propeName:%s\n", subscription->subsConstraint.conditions[0].pathInfo.namePath[0]);
        EXPECT_EQ(1, 0);
    }
    DmValueT fixedValue;
    DmValueT bytesValue;
    fixedValue.type = DB_DATATYPE_FIXED;
    fixedValue.value.length = 4;
    fixedValue.value.strAddr = DB_MALLOC(fixedValue.value.length);
    memcpy_s((void *)fixedValue.value.strAddr, fixedValue.value.length, "ffff", fixedValue.value.length);

    bytesValue.type = DB_DATATYPE_BYTES;
    bytesValue.value.length = 4;
    bytesValue.value.strAddr = DB_MALLOC(bytesValue.value.length);
    memcpy_s((void *)bytesValue.value.strAddr, bytesValue.value.length, "ffff", bytesValue.value.length);

    EXPECT_EQ(true, DmValueIsEqual(&subscription->subsConstraint.conditions[0].subsValue, &fixedValue));
    EXPECT_EQ(true, DmValueIsEqual(&subscription->subsConstraint.conditions[1].subsValue, &bytesValue));

    EXPECT_EQ(GMERR_OK, CataReleaseSubscription(subscription));
    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs_FixedAndBytes success\n");
}

void QryDDLCreateSubsBase(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxx";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

void QryDDLCreateSubs1(FixBufferT *buffer, DbMemCtxT *memCtx, DbCreateSubAckT *createSubAck)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "persist":false,
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub, createSubAck);

    EXPECT_EQ(GMERR_OK, ret);

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

void QryDDLCreateSubs2(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx1";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"ip4forward";
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    DmVertexLabelT *label;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &label));
    DmEventSubsCacheT *subsCache = NULL;
    CataGetSubsCacheParamT getParam = {
        .labelId = label->metaCommon.metaId, .subsEvent = DM_SUBS_EVENT_INSERT, .subsCache = &subsCache};
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ret = CataGetSubsCacheByEvent(getParam, removePara, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)3, subsCache->subsNum);
    CataReleaseEventSubsCache(subsCache);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(label));

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

void QryDDLCreateSubsReplace(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx2";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"ip4forward";
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    DmVertexLabelT *label;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &label));
    DmEventSubsCacheT *subsCache = NULL;
    CataGetSubsCacheParamT getParam = {
        .labelId = label->metaCommon.metaId, .subsEvent = DM_SUBS_EVENT_REPLACE, .subsCache = &subsCache};
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ret = CataGetSubsCacheByEvent(getParam, removePara, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, subsCache->subsNum);
    CataReleaseEventSubsCache(subsCache);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(label));

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

void QryDDLCreateSubs3(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "persist":false,
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_int16",
                                "value":1234
                            },
                            {
                                "property":"subs_int8",
                                "value":122
                            },
                            {
                                "property":"subs_uint32",
                                "value":1234
                            },
                            {
                                "property":"subs_uint16",
                                "value":1234
                            },
                            {
                                "property":"subs_uint8",
                                "value":122
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"ip4forward";
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    DmVertexLabelT *label;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &label));
    DmEventSubsCacheT *subsCache = NULL;
    CataGetSubsCacheParamT getParam = {
        .labelId = label->metaCommon.metaId, .subsEvent = DM_SUBS_EVENT_INSERT, .subsCache = &subsCache};
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ret = CataGetSubsCacheByEvent(getParam, removePara, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    CataReleaseEventSubsCache(subsCache);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(label));

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs4)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_int16",
                                "value":32888
                            }
                        ]
                }
        })";
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);

    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property subs_int16 value size should be -32768 to 32767.";
    EXPECT_STREQ(result, lastError1->str);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs5)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_uint16",
                                "value":-1
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);

    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property subs_uint16 value less than 0.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs6)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_uint8",
                                "value":-1
                            }
                        ]
                }
        })";
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);

    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property subs_uint8 value less than 0.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs7)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_int8",
                                "value":32888
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property subs_int8 value size should be -128 to 127.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs8)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_uint32",
                                "value":-1
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property subs_uint32 value less than 0.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs9)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"xxx",
                    "conditions":
                        [
                            {
                                "property":"subs_uint32",
                                "value":5
                            }
                        ]
                }
        })";

    // err Datatype mismatch
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Datatype mismatch. inv optype: xxx.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs11)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":1234567,
                    "conditions":
                        [
                            {
                                "property":"subs_uint32",
                                "value":5
                            }
                        ]
                }
        })";

    // err type for condition value
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Datatype mismatch. op type should be string.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs12)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"set"}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_uint32",
                                "value":5
                            }
                        ]
                }
        })";

    // err events for vertex label
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Program limit exceeded. the label event type should be kv table.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateSubsBaseWithVersion(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxx";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "persist":false,
            "schema_version":0,
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"ip4forward";
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    DmVertexLabelT *label;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &label));
    DmEventSubsCacheT *subsCache = NULL;
    CataGetSubsCacheParamT getParam = {
        .labelId = label->metaCommon.metaId, .subsEvent = DM_SUBS_EVENT_INSERT, .subsCache = &subsCache};
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ret = CataGetSubsCacheByEvent(getParam, removePara, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, subsCache->subsNum);
    CataReleaseEventSubsCache(subsCache);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(label));

    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs13)
{
    QryDDLCreateVertexLabel();
    QryDDLCreateSubsBaseWithVersion(&req, dyAlgoCtxVertex);
    CataUnBindChannelByName(QryGetBaseSubsChanneName(), NULL);
    char *labelVertexName = (char *)"ip4forward";
    uint32_t ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs14)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "schema_version":1,
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";
    // err Undefine column
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Undefined table. Label name:ip4forward, version:1, cataCache name: vertexLabelCache.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

Status AddSubsUserCbFuncName2ConnFailStub(DmSubscriptionT *subscription)
{
    return GMERR_OUT_OF_MEMORY;
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubsWithAddSubsCbFail)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();

    char *subsName = (char *)"sub_cbFail";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events": [{"type":"insert", "msgTypes":["new object", "old object"]}],
            "retry":true
        })";
    QryTestCreateSubsParamT createParam = CreateDefaultCreatSubParam();
    createParam.subsName = subsName;
    createParam.subsJson = subsJson;
    createParam.addSubCbFunc = AddSubsUserCbFuncName2ConnFailStub;
    ret = QryTestCreateSubsBase(&createParam);
    EXPECT_NE(GMERR_OK, ret);

    DmSubscriptionT *subscription = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, createParam.nspId, subsName);
    (void)CataGetSubsByName(&cataKey, &subscription, NULL);
    EXPECT_EQ(NULL, subscription);

    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

Status ExpandChannelCapacityFailStub(DmSubscriptionT *subscription)
{
    return GMERR_OUT_OF_MEMORY;
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubsWithExpandChannelStub)
{
    uint32_t ret;
    QryDDLCreateVertexLabel(true);

    char *subsName = (char *)"sub_respFail";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "subs_type":"status_merge",
            "events": [{"type":"modify", "msgTypes":["new object"]}],
            "retry":true
        })";
    QryTestCreateSubsParamT createParam = CreateDefaultCreatSubParam();
    createParam.subsName = subsName;
    createParam.subsJson = subsJson;
    createParam.expandChannelFunc = ExpandChannelCapacityFailStub;
    ret = QryTestCreateSubsBase(&createParam);
    EXPECT_NE(GMERR_OK, ret);

    DmSubscriptionT *subscription = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, createParam.nspId, subsName);
    (void)CataGetSubsByName(&cataKey, &subscription, NULL);
    EXPECT_EQ(NULL, subscription);

    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs_WithMsgType)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    QryDDLCreateSubs3(&req, dyAlgoCtxVertex);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward sub",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "old object"]},
                    {"type":"update", "msgTypes": ["old object", "new object"]},
                    {"type":"delete", "msgTypes": ["new object","key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "retry":true
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    CataUnBindChannelByName(QryGetBaseSubsChanneName(), NULL);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs_WithMsgType_Err)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *cfgJson1 = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson1 = (char *)R"([{
        "type":"record",
        "name":"lableWithoutPK",
        "fields":[
            {"name":"nhp_group_id1", "type":"int32"},
            {"name":"nhp_group_name1", "type":"int32"}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson1, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"lableWithoutPK",
            "comment":"sub key data on lable without PK",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object"]},
                    {"type":"update", "msgTypes": ["old object", "new object"]},
                    {"type":"delete", "msgTypes": ["key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "retry":true
        })";

    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Feature is not supported. The subscribed label lableWithoutPK have no primary key.";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"lableWithoutPK";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_DropSubscription)
{
    int32_t ret;
    QryDDLCreateVertexLabel();
    QryDDLCreateSubsBase(&req, dyAlgoCtxVertex);
    DbCreateSubAckT subAck;
    QryDDLCreateSubs1(&req, dyAlgoCtxVertex, &subAck);
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *subsName = (char *)"sub_xxx";
    uint32_t subsNameLen = strlen(subsName) + 1;
    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = subsName;
    putText.len = subsNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(&req, subAck.subsMetaId);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DROP_SUBSCRIPTION,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    DelSubsUserCbFuncNameStub();
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"ip4forward";
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    DmVertexLabelT *label;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &label));
    DmEventSubsCacheT *subsCache = NULL;
    CataGetSubsCacheParamT getParam = {
        .labelId = label->metaCommon.metaId, .subsEvent = DM_SUBS_EVENT_INSERT, .subsCache = &subsCache};
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ret = CataGetSubsCacheByEvent(getParam, removePara, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, subsCache->subsNum);
    CataReleaseEventSubsCache(subsCache);
    CataUnBindChannelByName(QryGetBaseSubsChanneName(), NULL);
    EXPECT_EQ(GMERR_OK, CataReleaseVertexLabel(label));
    QryTestReleaseSession(conn);

    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_DropSubscription success\n");
}

void QryDDLCreateSubsBigThanInt32(FixBufferT *buffer, DbMemCtxT *memCtx)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(buffer, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxx1";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property":"subs_string",
                                "value":"xxxxxxx"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483648
                            }
                        ]
                }
        })";
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackStub);

    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result =
        (char *)"Not normal property. property subs_int32 value size should be -2147483648 to 2147483647.";
    EXPECT_STREQ(result, lastError1->str);

    QryTestReleaseSession(conn);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubsBigThanInt32)
{
    QryDDLCreateVertexLabel();
    QryDDLCreateSubsBigThanInt32(&req, dyAlgoCtxVertex);
    char *labelVertexName = (char *)"ip4forward";
    uint32_t ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

int32_t UtQueryDDLSubsCreateSubsFailBigNotChar(const DrtConnectionT *conn, FixBufferT *msg)
{
    SessionT *session = (SessionT *)conn->session;

    FixBufferT *rsp = QrySessionGetRsp(session);
    FixBufSeek(rsp, 0);
    MsgHeaderT *header = (MsgHeaderT *)FixBufGetData(rsp, 0);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, header->opStatus);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Not normal property. property subs_char is NULL, or len exceeds limit 1.";
    EXPECT_STREQ(result, lastError1->str);
    return GMERR_OK;
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateSubs_NotChar)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *chanName = (char *)QryGetBaseSubsChanneName();
    char *subsName = (char *)"sub_xxx2";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            },
                            {
                                "property":"subs_char",
                                "value":"xxxxxxx"
                            }
                        ]
                }
        })";

    uint32_t subsJsonLen = strlen(subsJson) + 1;
    uint32_t chanNameLen = strlen(chanName) + 1;

    TextT putText;
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    putText.str = subsName;
    putText.len = strlen(subsName) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = subsJson;
    putText.len = subsJsonLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = chanName;
    putText.len = chanNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = (char *)"TEST";
    putText.len = strlen(putText.str) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_SUBSCRIPTION,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)UtQueryDDLSubsCreateSubsFailBigNotChar);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_NO_EVENTS)
{
    uint32_t ret;
    QryDDLCreateVertexLabel();
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsName = (char *)"sub_xxxx3";
    char *subsJson = (char *)R"({
            "label_name":"ip4forward",
            "comment":"the ip4forward"
        })";
    ret = QryTestCreateSubs(subsName, subsJson, DrtConnWritePackFailStub);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Undefine column. colum:events";
    EXPECT_STREQ(result, lastError1->str);
    QryTestReleaseSession(conn);
    char *labelVertexName = (char *)"ip4forward";
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateFullSyncSubs)
{
    uint32_t ret;
    char *labelVertexName = (char *)"ip4forward_full_sync";
    char *cfgJson1 = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson1 = (char *)R"([{
        "type":"record",
        "name":"ip4forward_full_sync",
        "fields":[
            {"name":"nhp_group_id1", "type":"int32"},
            {"name":"nhp_group_name1", "type":"int32"},
            {"name":"subs_string", "type":"string", "size":8},
            {"name":"subs_char", "type":"char"},
            {"name":"subs_int32", "type":"int32"},
            {"name":"subs_fixed", "type":"fixed", "default":"ffff", "size":4},
            {"name":"subs_bytes", "type":"bytes", "size":20, "nullable":true},
            {"name":"subs_int8", "type":"int8"},
            {"name":"subs_uint8", "type":"uint8"},
            {"name":"subs_int16", "type":"int16"},
            {"name":"subs_uint16", "type":"uint16"},
            {"name":"subs_uint32", "type":"uint32"}
        ],
        "keys":
        [
            {"node":"ip4forward_full_sync", "name":"ip4forward_K0", "fields":["nhp_group_id1"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson1, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *chanName = (char *)QryGetBaseSubsChanneName();
    char *subsName = (char *)"sub_xxx2";
    char *subsJson = (char *)R"(
    {
        "label_name":"ip4forward_full_sync",
        "persist":false,
        "comment":"ip4forward full sync",
        "events":
        [
            {
                "type":"initial_load"
            },
            {
                "type":"insert",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"delete",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"update",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"replace",
                "msgTypes":["new object", "old object"]
            }
        ]
    })";

    uint32_t subsJsonLen = strlen(subsJson) + 1;
    uint32_t chanNameLen = strlen(chanName) + 1;

    TextT putText;
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    putText.str = subsName;
    putText.len = strlen(subsName) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = subsJson;
    putText.len = subsJsonLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = chanName;
    putText.len = chanNameLen;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_SUBSCRIPTION,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    FastpathEntry(&serviceCtx, &procCtx);
    CataUnBindChannelByName(QryGetBaseSubsChanneName(), NULL);
    ret = QryTestDropVertexLabel(labelVertexName, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

void QryDDLCreateSrcVertexLabel()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"srcVextexLabelName175",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"},
            {"name":"F3", "type":"fixed", "default":"fff", "size":3}
        ],
        "keys":[
            {"node":"srcVextexLabelName175", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateDstVertexLabel()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"dstVextexLabelName175",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"},
            {"name":"F3", "type":"fixed", "default":"fff", "size":3}
        ],
        "keys":[
            {"node":"dstVextexLabelName175", "name":"T59_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateDstVertexLabel2()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"dstVextexLabelName176",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"},
            {"name":"F3", "type":"fixed", "default":"fff", "size":3}
        ],
        "keys":[
            {"node":"dstVextexLabelName176", "name":"T59_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateEdgeLabel1()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *labelJson = (char *)R"([{
            "name":"edgeLabelName175",
            "source_vertex_label":"srcVextexLabelName175",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"dstVextexLabelName175",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {"source_property":"F0", "dest_property":"F0"},
                    {"source_property":"F1","dest_property":"F1"}
                ]
            }
        }])";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_CreateEdgeLabel success\n");
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateEdgeLabel1)
{
    QryDDLCreateSrcVertexLabel();
    QryDDLCreateDstVertexLabel();
    QryDDLCreateEdgeLabel1();
    char *labelName = (char *)"edgeLabelName175";
    QryTestDropEdgeLabel(labelName, NULL);
    char *labelVertexName1 = (char *)"srcVextexLabelName175";
    uint32_t ret = QryTestDropVertexLabel(labelVertexName1, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelVertexName2 = (char *)"dstVextexLabelName175";
    ret = QryTestDropVertexLabel(labelVertexName2, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateEdgeLabel12()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *labelJson = (char *)R"([{
            "name":"edgeLabelName176",
            "source_vertex_label":"dstVextexLabelName175",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"dstVextexLabelName176",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {"source_property":"F0", "dest_property":"F0"},
                    {"source_property":"F1","dest_property":"F1"}
                ]
            }
        }])";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_CreateEdgeLabel success\n");
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateEdgeLabel1_2)
{
    QryDDLCreateDstVertexLabel();
    QryDDLCreateDstVertexLabel2();
    QryDDLCreateEdgeLabel12();
    char *labelName = (char *)"edgeLabelName176";
    QryTestDropEdgeLabel(labelName, NULL);
    char *labelVertexName1 = (char *)"dstVextexLabelName175";
    uint32_t ret = QryTestDropVertexLabel(labelVertexName1, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelVertexName2 = (char *)"dstVextexLabelName176";
    ret = QryTestDropVertexLabel(labelVertexName2, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void QryDDLCreateEdgeLabel21()
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000}";
    char *labelJson = (char *)R"([{
            "name":"edgeLabelName177",
            "source_vertex_label":"dstVextexLabelName176",
            "comment":"the edge 8 to 7",
            "dest_vertex_label":"dstVextexLabelName175",
            "constraint":{
                "operator_type":"and",
                "conditions":[
                    {"source_property":"F0", "dest_property":"F0"},
                    {"source_property":"F1","dest_property":"F1"}
                ]
            }
        }])";

    ret = QryTestCreateEdgeLabel(cfgJson, labelJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("QRY_DDL_CreateEdgeLabel success\n");
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateEdgeLabel2_1)
{
    QryDDLCreateDstVertexLabel2();
    QryDDLCreateDstVertexLabel();
    QryDDLCreateEdgeLabel21();
    char *labelName = (char *)"edgeLabelName177";
    QryTestDropEdgeLabel(labelName, NULL);
    char *labelVertexName1 = (char *)"dstVextexLabelName176";
    uint32_t ret = QryTestDropVertexLabel(labelVertexName1, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelVertexName2 = (char *)"dstVextexLabelName175";
    ret = QryTestDropVertexLabel(labelVertexName2, 0, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtQueryDDLSubs, QRY_DDL_CreateEdgeLabel2)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    ret = QryTestCreateBaseVertexLabelFirst(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestCreateBaseVertexLabelSecond(cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestCreateBaseEdgeLabel();
    EXPECT_EQ(GMERR_OK, ret);

    QryTestDropBaseEdgeLabel();
    QryTestDropBaseVertexLabelFirst();
    QryTestDropBaseVertexLabelSecond();
    printf("QRY_DML_CreateEdgeLabel success\n");
}

static char *subLabelName = (char *)"testSubPushPathSubLabel1";
static char *subLabelName2 = (char *)"testSubPushPathSubLabel2";
static char *subLabelName3 = (char *)"testSubPushPathSubLabel3";
static char *subLabelName4 = (char *)"testSubPushPathSubLabel4";
static char *edgeLabelName = (char *)"testSubPushPathSubEdgeLabel1";
static char *edgeLabelName2 = (char *)"testSubPushPathSubEdgeLabel2";
static char *edgeLabelName3 = (char *)"testSubPushPathSubEdgeLabel3";

extern "C" void QryInitSinglePathMsg(QryStmtT *tempStmt, const DmSubscriptionT *subs, const QrySubsEventT *subsEvent);

static QrySubsMsgT localSubMsg;
void QryInitSinglePathMsgSub(QryStmtT *tempStmt, const DmSubscriptionT *subs, QrySubsEventT *subsEvent)
{
    tempStmt->subsCtx.subsMsg = &localSubMsg;
    localSubMsg.subsEvent = subsEvent;
}

void MsgWriterFinishPutOpStub(MsgWriterT *writer)
{
    uint32_t hopCnt2 = 0;

    TextT info;
    FixBufferT *rsp = &writer->rawBuf;

    FixBufSeek(rsp, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    DbSubsEventAckT *subsHeader = (DbSubsEventAckT *)FixBufGetData(rsp, sizeof(DbSubsEventAckT));
    EXPECT_EQ((uint32_t)3, subsHeader->pathLen);
    FixBufGetText(rsp, &info);  // subsName

    hopCnt2 = 0;
    FixBufGetText(rsp, &info);
    EXPECT_STREQ(subLabelName, info.str);

    FixBufGetText(rsp, &info);
    EXPECT_STREQ(edgeLabelName, info.str);
    hopCnt2++;

    FixBufGetText(rsp, &info);
    EXPECT_STREQ(subLabelName2, info.str);

    FixBufGetText(rsp, &info);
    EXPECT_STREQ(edgeLabelName2, info.str);
    hopCnt2++;

    FixBufGetText(rsp, &info);
    EXPECT_STREQ(subLabelName3, info.str);

    FixBufGetText(rsp, &info);
    EXPECT_STREQ(edgeLabelName3, info.str);
    hopCnt2++;

    FixBufGetText(rsp, &info);
    EXPECT_STREQ(subLabelName4, info.str);

    EXPECT_EQ(subsHeader->pathLen, hopCnt2);

    uint32_t opLen = FixBufGetPos(&writer->rawBuf) - writer->opHeaderOffset;
    FIX_BUF_SET_STRUCT_VALUE(&writer->rawBuf, writer->opHeaderOffset, OpHeaderT, opCode, writer->opCode);
    FIX_BUF_SET_STRUCT_VALUE(&writer->rawBuf, writer->opHeaderOffset, OpHeaderT, len, opLen);
    writer->opHeaderOffset = DB_MAX_UINT32;
    writer->putOpCnt++;
}

TEST_F(UtQueryDDLSubs, QRY_DDL_TriggerScan_Fuzz_Err)
{
    int32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    TextT putText;
    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = (char *)"";
    putText.len = 0;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);
    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_TRIGGER_SCAN,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    QryTestReleaseSession(conn);
    printf("QRY_DDL_TriggerScan_Fuzz_Err success\n");
}
