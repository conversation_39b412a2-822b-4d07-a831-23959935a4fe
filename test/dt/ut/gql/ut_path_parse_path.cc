/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: test for DDL about path in Path
 * Author: GQL
 * Create: 2022-09-20
 */

#include "gtest/gtest.h"
#include "query_ut_base.h"
#include "ut_catalog_base.h"
#include "cpl_gql_compiler.h"
#include "cpl_gql_ast.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif
static DbMemCtxT *g_basicMem = NULL;
static SessionT session;

class UtPathParsePath : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        g_basicMem = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memory context", &args);
        ASSERT_NE(nullptr, g_basicMem);
        DmNamespaceT *namespaceInfo = CreateNspWithId(0, g_basicMem);
        EXPECT_EQ(GMERR_OK, CataCreateNamespace(namespaceInfo, NULL));
        session.dbId = 1;
        session.namespaceId = namespaceInfo->metaCommon.namespaceId;
        session.memCtx = g_basicMem;
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(g_basicMem);
        BaseUninit();
    };
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtPathParsePath, PathPath1)
{
    const char *input = R"(
        CREATE PATH path63215 (
            MATCH (:T2193)-[:R63215]->(:T2662)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t stmtCnt = parsedList->statements.count;
    uint32_t value = 1;
    ASSERT_EQ(stmtCnt, value);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    const char *pathName = "path63215";
    ASSERT_STREQ(stmtOne->pathName, pathName);

    /* check ParsePathPatternT */
    ParsePathPatternT *pattern = stmtOne->pattern;
    uint32_t vertexCnt = 2;
    uint32_t edgeCnt = 1;
    ASSERT_EQ(stmtOne->base.vertexCount, vertexCnt);
    ASSERT_EQ(stmtOne->base.edgeCount, edgeCnt);

    const char *labelName = "T2193";
    const char *variableName = "#anon_0";
    ParsePathInfoVertexT *root = *(ParsePathInfoVertexT **)DbListItem(pattern->roots, 0);
    ASSERT_STREQ(root->labelName, labelName);
    ASSERT_STREQ(root->variableName, variableName);
    uint32_t inDegree = 0;
    uint32_t outDegree = 1;
    ASSERT_EQ(root->inDegree, inDegree);
    ASSERT_EQ(root->outDegree, outDegree);
    ParsePathInfoEdgeT *outEdge = *(ParsePathInfoEdgeT **)DbListItem(&root->outEdgeList, 0);
    const char *edgeName = "R63215";
    ASSERT_STREQ(outEdge->labelName, edgeName);
    ASSERT_STREQ(outEdge->srcPathInfoVertex->labelName, labelName);
    labelName = "T2662";
    variableName = "#anon_1";
    ASSERT_STREQ(outEdge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(outEdge->dstPathInfoVertex->variableName, variableName);

    /* check variable2PathInfoVertex in ParsePathPatternT */
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.variable2PathInfoVertex), 2u);
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.labels), 2u);
}

TEST_F(UtPathParsePath, PathPath2)
{
    const char *input = R"(
        CREATE PATH NonLiner (
            MATCH
            (:a)-[:AB]->(:b)-[:BC]->(:c),
            (:b)-[:BD]->(:d)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t stmtCnt = parsedList->statements.count;
    uint32_t value = 1;
    ASSERT_EQ(stmtCnt, value);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    const char *pathName = "NonLiner";
    ASSERT_STREQ(stmtOne->pathName, pathName);

    /* check ParsePathPatternT */
    ParsePathPatternT *pattern = stmtOne->pattern;
    uint32_t vertexCnt = 5;
    uint32_t edgeCnt = 3;
    ASSERT_EQ(stmtOne->base.vertexCount, vertexCnt);
    ASSERT_EQ(stmtOne->base.edgeCount, edgeCnt);

    const char *labelName = "a";
    const char *variableName = "#anon_0";
    ParsePathInfoVertexT *root = *(ParsePathInfoVertexT **)DbListItem(pattern->roots, 0);
    ASSERT_STREQ(root->labelName, labelName);
    ASSERT_STREQ(root->variableName, variableName);
    uint32_t inDegree = 0;
    uint32_t outDegree = 1;
    ASSERT_EQ(root->inDegree, inDegree);
    ASSERT_EQ(root->outDegree, outDegree);
    ParsePathInfoEdgeT *outEdge = *(ParsePathInfoEdgeT **)DbListItem(&root->outEdgeList, 0);
    const char *edgeName = "AB";
    ASSERT_STREQ(outEdge->labelName, edgeName);
    ASSERT_STREQ(outEdge->srcPathInfoVertex->labelName, labelName);
    labelName = "b";
    variableName = "#anon_1";
    ASSERT_STREQ(outEdge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(outEdge->dstPathInfoVertex->variableName, variableName);

    ParsePathInfoVertexT *b = outEdge->dstPathInfoVertex;
    ASSERT_EQ(DbListGetItemCnt(&b->outEdgeList), 1u);
    outEdge = *(ParsePathInfoEdgeT **)DbListItem(&b->outEdgeList, 0);
    edgeName = "BC";
    ASSERT_STREQ(outEdge->labelName, edgeName);
    ASSERT_STREQ(outEdge->srcPathInfoVertex->labelName, labelName);
    labelName = "c";
    variableName = "#anon_2";
    ASSERT_STREQ(outEdge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(outEdge->dstPathInfoVertex->variableName, variableName);

    /* check variable2PathInfoVertex in ParsePathPatternT */
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.variable2PathInfoVertex), 5u);
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.labels), 4u);
}

TEST_F(UtPathParsePath, PathPath4)
{
    const char *input = R"(
        CREATE PATH ORGroup (
            MATCH
            (:a)-[:AB|:AC]->(
                (:b)-[:BD|:BE]->(
                    (:d)|
                    (:e)-[:EF]->(:f)
                )|
                (:c)
            )
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t stmtCnt = parsedList->statements.count;
    uint32_t value = 1;
    ASSERT_EQ(stmtCnt, value);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    const char *pathName = "ORGroup";
    ASSERT_STREQ(stmtOne->pathName, pathName);

    /* check ParsePathPatternT */
    ParsePathPatternT *pattern = stmtOne->pattern;
    uint32_t vertexCnt = 6;
    uint32_t edgeCnt = 5;
    ASSERT_EQ(stmtOne->base.vertexCount, vertexCnt);
    ASSERT_EQ(stmtOne->base.edgeCount, edgeCnt);

    const char *labelName = "a";
    const char *variableName = "#anon_0";
    ParsePathInfoVertexT *root = *(ParsePathInfoVertexT **)DbListItem(pattern->roots, 0);
    ASSERT_STREQ(root->labelName, labelName);
    ASSERT_STREQ(root->variableName, variableName);
    uint32_t inDegree = 0;
    uint32_t outDegree = 2;
    ASSERT_EQ(root->inDegree, inDegree);
    ASSERT_EQ(root->outDegree, outDegree);

    ASSERT_EQ(DbListGetItemCnt(&root->outEdgeList), 0u);
    ASSERT_EQ(DbListGetItemCnt(&root->outOrGroupList), 1u);
    ParsePathInfoOrGroupT *outGroup = (ParsePathInfoOrGroupT *)DbListItem(&root->outOrGroupList, 0);
    ParsePathInfoEdgeT *edge = *(ParsePathInfoEdgeT **)DbListItem(&outGroup->edges, 0);
    const char *edgeName = "AB";
    ASSERT_STREQ(edge->labelName, edgeName);
    ASSERT_STREQ(edge->srcPathInfoVertex->labelName, labelName);
    labelName = "b";
    variableName = "#anon_1";
    ASSERT_STREQ(edge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(edge->dstPathInfoVertex->variableName, variableName);

    edge = *(ParsePathInfoEdgeT **)DbListItem(&outGroup->edges, 1);
    edgeName = "AC";
    ASSERT_STREQ(edge->labelName, edgeName);
    labelName = "a";
    ASSERT_STREQ(edge->srcPathInfoVertex->labelName, labelName);
    labelName = "c";
    variableName = "#anon_5";
    ASSERT_STREQ(edge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(edge->dstPathInfoVertex->variableName, variableName);

    /* check variable2PathInfoVertex in ParsePathPatternT */
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.variable2PathInfoVertex), 6u);
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.labels), 6u);
}

TEST_F(UtPathParsePath, PathPath5)
{
    const char *input = R"deli(CREATE PATH path89_simple (
        MATCH
        (:T7)-[:R84300|:R84|:R4|:R83016|:R83017|:R87275]->(
        (:T3452)-[:R203319]->(:T4171),
        (:T3452)-[:R4301]->(:T36)-[:R84110|:R82498|:R83557|:R83169|:R86614]->(
            (:T2153)|
            (:T2726)|
            (:T3378)|
            (:T2169)|
            (:T3860)
        ),
        (:T36)-[:R203423]->(:T359)-[:R83013]->(:T357)|
        (:T37)|
        (:T10)|
        (:T1)-[:R83030]->(:T31),
        (:T1)-[:R83031]->(:T279)|
        (:T813)|
        (:T2009)-[:R81151|:R81152|:R81132|:R81401|:R82449|:R83240]->(
            (:T2010)|
            (:T2011)|
            (:T2337)-[:R81133]->(:T2336)|
            (:T2563)|
            (:T2745)|
            (:T2896)
        )
    )
    );)deli";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    input = R"deli(CREATE PATH path89_simple2 (
        MATCH
        (:T7)-[:R84300|:R84|:R4|:R83016|:R83017|:R87275]->(
        (:T3452)-[:R203319]->(:T4171),
        (:T3452)-[:R4301]->(:T36)-[:R84110|:R82498|:R83557|:R83169|:R86614]->(
            (:T2153)|
            (:T2726)|
            (:T3378)|
            (:T2169)|
            (:T3860)
        ),
        (:T36)-[:R203423]->(:T359)-[:R83013]->(:T357)|
        (:T37)|
        (:T10)|
        (:T1)-[:R83030]->(:T31),
        (:T1)-[:R83031]->(:T279)|
        (:T813)|
        (:T2009)-[:R81151|:R81152|:R81132|:R81401|:R82449|:R83240]->(
            (:T2010)|
            (:T2011)|
            (:T2337)-[:R81133]->(:T2336)|
            (:T2563)|
            (:T2745)|
            (:T2896)
        )
    )
    );)deli";

    GqlParsedListT *parsedList2;
    ret = GqlParse(g_basicMem, input, &parsedList2);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(UtPathParsePath, PathPath6)
{
    const char *input = R"deli(CREATE PATH too_many_edge (
        MATCH
        (:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)
    );)deli";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    input = R"deli(CREATE PATH normal (
        MATCH
        (:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)-[:BA]->(:a)-[:AB]->(:b)
    );)deli";

    ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);

    input = R"deli(CREATE PATH too_many_edge_in_or_group (
        MATCH
        (:a)-[:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB]->(
            (:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)
        )
    );)deli";

    ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    input = R"deli(CREATE PATH normal_or_group (
        MATCH
        (:a)-[:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB|:AB]->(
            (:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)|(:b)
        )
    );)deli";

    ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtPathParsePath, PathPath7)
{
    const char *input = R"(
        CREATE PATH P_917021_5 (
        MATCH
            (:T_29)-[:E_63234]->(:T_31),
            (:T_31)-[:E_63249|:E_63243]->(
                (NULL)|
                (:T_893)
            )
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtPathParsePath, PathPath8)
{
    const char *input = R"(CREATE PATH P_917021 (
    MATCH
    (:T_93)-[:E_78021|:E_63251]->(
        (:T_29)-[:E_63234]->(:T_31),
        (:T_31)-[:E_63249|:E_63243]->(
            (NULL)|
            (:T_893)
        ),
        (:T_29)-[:E_63175]->(:T_1719),
        (:T_29)-[:E_63248]->(:T_2456),
        (:T_2456)-[:E_63250|:E_63247]->(
            (NULL)|
            (:T_893)
        ),
        (:T_29)-[:E_63174]->(:T_20)|
        (NULL)
    )
);)";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtPathParsePath, PathPath9)
{
    const char *input = R"(
CREATE PATH P_83018 (
    MATCH
    (:T_279)-[:E_55003|:E_55013|:E_88768|:E_55070|:E_55002|:E_81080|:E_55005|:E_55001|:E_90000|:E_55006|:E_88772|:E_81079|:E_81091|:E_88649]->(
        (:T_808)-[:E_55008|:E_55007]->(
            (:T_28)|
            (:T_14)
        )|
        (:T_359)|
        (:T_17)-[:E_88770|:E_90001]->(
            (:T_28)|
            (:T_889)
        )|
        (:T_890)-[:E_55071]->(:T_889)|
        (:T_14)|
        (NULL)|
        (:T_28)|
        (NULL)|
        (:T_17)|
        (:T_111)-[:E_55012|:E_81666|:E_55033]->(
            (:T_38)|
            (:T_1176)|
            (NULL)
        )|
        (:T_1412)-[:E_88773]->(:T_1410)|
        (NULL)|
        (:T_2080)|
        (:T_968)-[:E_88651|:E_88650]->(
            (:T_28)|
            (:T_14)
        )
    ),
    (:T_279)-[:E_201587|:E_80009|:E_80008]->(
        (:T_4089)|
        (:T_446)|
        (NULL)
    )
);
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtPathParsePath, PathPath10)
{
    const char *input = R"(
CREATE PATH P_55000 (
    MATCH
    (:T_279)-[:E_55050|:E_55024]->(
        (:T_309)|
        (NULL)
    ),
    (:T_279)-[:E_55025|:E_55060]->(
        (NULL)|
        (:T_334)
    ),
    (:T_279)-[:E_88786|:E_55002|:E_81080|:E_55013|:E_55003|:E_201623|:E_55001|:E_81079|:E_88768|:E_55006|:E_55080|:E_55005|:E_88649|:E_80000|:E_81091|:E_90000|:E_88761|:E_88772|:E_55070|:E_88643|:E_88490]->(
        (:T_1024)|
        (:T_14)|
        (NULL)|
        (:T_359)|
        (:T_808)-[:E_88762|:E_201624|:E_55007|:E_55009|:E_55008]->(
            (:T_1354)|
            (:T_2462)|
            (:T_14)|
            (:T_814)-[:E_201625|:E_55010]->(
                (:T_2462)|
                (:T_14)
            )|
            (:T_28)
        )|
        (:T_2462)|
        (NULL)|
        (NULL)|
        (:T_17)-[:E_88771|:E_88770|:E_88769|:E_201628|:E_90001]->(
            (:T_814)|
            (:T_28)|
            (:T_14)|
            (:T_2462)|
            (:T_889)-[:E_88669]->(:T_14),
            (:T_889)-[:E_80001]->(:T_14),
            (:T_889)-[:E_55072]->(:T_14),
            (:T_889)-[:E_90002]->(:T_14)
        )|
        (:T_111)-[:E_55012|:E_86698|:E_87047|:E_55033|:E_81666|:E_201608]->(
            (:T_38)|
            (:T_108)|
            (:T_838)|
            (NULL)|
            (:T_1176)|
            (:T_3699)
        )|
        (:T_814)|
        (:T_28)|
        (:T_968)-[:E_88650|:E_201626|:E_88651|:E_204253]->(
            (:T_14)|
            (:T_2462)|
            (:T_28)|
            (:T_1354)
        )|
        (:T_889)|
        (:T_2080)|
        (:T_17)|
        (:T_1354)|
        (:T_1412)-[:E_88773]->(:T_1410)|
        (:T_890)-[:E_55071]->(:T_889)|
        (:T_1112)-[:E_88666|:E_88644|:E_83388]->(
            (:T_14)|
            (:T_889)|
            (NULL)
        )|
        (:T_995)-[:E_88491|:E_201627]->(
            (:T_14)|
            (:T_2462)
        ),
        (:T_995)-[:E_88503|:E_88492]->(
            (NULL)|
            (:T_14)
        )
    ),
    (:T_279)-[:E_201587|:E_80009|:E_80008]->(
        (:T_4089)|
        (:T_446)|
        (NULL)
    )
);
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtPathParsePath, PathPath11)
{
    const char *input = R"(
CREATE PATH P_83018 (
    MATCH
    (:T_279)-[:E_55003|:E_55001|:E_55013|:E_81091|:E_55070|:E_88772|:E_55006|:E_88768|:E_88649|:E_55005|:E_55002|:E_81080|:E_90000|:E_81079]->(
        (:T_808)-[:E_55008|:E_55007]->(
            (:T_28)|
            (:T_14)
        )|
        (NULL)|
        (:T_359)|
        (:T_2080)|
        (:T_890)-[:E_55071]->(:T_889)|
        (:T_1412)-[:E_88773]->(:T_1410)|
        (:T_111)-[:E_55033|:E_55012|:E_81666]->(
            (NULL)|
            (:T_38)|
            (:T_1176)
        )|
        (:T_17)-[:E_90001|:E_88770]->(
            (:T_889)|
            (:T_28)
        )|
        (:T_968)-[:E_88650|:E_88651]->(
            (:T_14)|
            (:T_28)
        )|
        (:T_28)|
        (:T_14)|
        (NULL)|
        (:T_17)|
        (NULL)
    ),
    (:T_279)-[:E_80008|:E_201587|:E_80009]->(
        (NULL)|
        (:T_4089)|
        (:T_446)
    )
);
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(UtPathParsePath, PathPath12)
{
    const char *input = R"(
        CREATE PATH path88 (
            MATCH
            (:T7)-[:E51|:E84504]->(
                (:T37)-[:E160]->(:T42)-[:E122]->(:T227)|
                (:T42)
            )
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t stmtCnt = parsedList->statements.count;
    uint32_t value = 1;
    ASSERT_EQ(stmtCnt, value);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    const char *pathName = "path88";
    ASSERT_STREQ(stmtOne->pathName, pathName);

    /* check ParsePathPatternT */
    ParsePathPatternT *pattern = stmtOne->pattern;
    uint32_t vertexCnt = 5;
    uint32_t edgeCnt = 4;
    ASSERT_EQ(stmtOne->base.vertexCount, vertexCnt);
    ASSERT_EQ(stmtOne->base.edgeCount, edgeCnt);

    const char *labelName = "T7";
    const char *variableName = "#anon_0";
    ParsePathInfoVertexT *root = *(ParsePathInfoVertexT **)DbListItem(pattern->roots, 0);
    ASSERT_STREQ(root->labelName, labelName);
    ASSERT_STREQ(root->variableName, variableName);
    uint32_t inDegree = 0;
    uint32_t outDegree = 2;
    ASSERT_EQ(root->inDegree, inDegree);
    ASSERT_EQ(root->outDegree, outDegree);

    ASSERT_EQ(DbListGetItemCnt(&root->outEdgeList), 0u);
    ASSERT_EQ(DbListGetItemCnt(&root->outOrGroupList), 1u);
    ParsePathInfoOrGroupT *outGroup = (ParsePathInfoOrGroupT *)DbListItem(&root->outOrGroupList, 0);
    ParsePathInfoEdgeT *edge = *(ParsePathInfoEdgeT **)DbListItem(&outGroup->edges, 0);
    const char *edgeName = "E51";
    ASSERT_STREQ(edge->labelName, edgeName);
    ASSERT_STREQ(edge->srcPathInfoVertex->labelName, labelName);
    labelName = "T37";
    variableName = "#anon_1";
    ASSERT_STREQ(edge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(edge->dstPathInfoVertex->variableName, variableName);

    edge = *(ParsePathInfoEdgeT **)DbListItem(&outGroup->edges, 1);
    edgeName = "E84504";
    ASSERT_STREQ(edge->labelName, edgeName);
    labelName = "T7";
    ASSERT_STREQ(edge->srcPathInfoVertex->labelName, labelName);
    labelName = "T42";
    variableName = "#anon_4";
    ASSERT_STREQ(edge->dstPathInfoVertex->labelName, labelName);
    ASSERT_STREQ(edge->dstPathInfoVertex->variableName, variableName);
    ParsePathInfoVertexT *T42 = edge->dstPathInfoVertex;
    ASSERT_EQ(DbListGetItemCnt(&T42->outEdgeList), 0u);
    ASSERT_EQ(T42->outDegree, 0u);

    /* check variable2PathInfoVertex in ParsePathPatternT */
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.variable2PathInfoVertex), 5u);
    ASSERT_EQ(DbOamapUsedSize(stmtOne->base.labels), 4u);
}

TEST_F(UtPathParsePath, PathDropPath1)
{
    const char *input = R"(
        DROP PATH p_test123;
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    // 校验拓扑结构每条边的深度
    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseDropPathStmtT *dropPathStmt = (ParseDropPathStmtT *)stmt;
    ASSERT_EQ(dropPathStmt->base.tag, DROP_PATH);
    ASSERT_STREQ("p_test123", dropPathStmt->pathName);
}

TEST_F(UtPathParsePath, PathWithSub1)
{
    const char *input = R"deli(CREATE PATH NonLiner123 (
        MATCH
        (:a)-[:AB]->(:b),
        (:b)-[:BD]->(:d)
        RETURN (
            REPLACE a.c1 346, b.c1 2042, a.c2 346
        )
    );)deli";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    std::string label = "NonLiner123";
    EXPECT_EQ(stmtOne->pathName, label);

    /* check subscription */
    SubscribeFieldT *subFields = stmtOne->subFields;
    uint32_t replaceCnt = 3;
    ASSERT_EQ(replaceCnt, DbListGetItemCnt(subFields->replaceForward));

    ForwardPropertyT **forwardProperty = (ForwardPropertyT **)DbListItem(subFields->replaceForward, 0);
    const char *vertexLabelName = "a";
    const char *propertyName = "c1";
    EXPECT_STREQ(vertexLabelName, (*forwardProperty)->vertexlabelName);
    EXPECT_STREQ(propertyName, (*forwardProperty)->propertyName);

    forwardProperty = (ForwardPropertyT **)DbListItem(subFields->replaceForward, 1);
    vertexLabelName = "b";
    propertyName = "c1";
    EXPECT_STREQ(vertexLabelName, (*forwardProperty)->vertexlabelName);
    EXPECT_STREQ(propertyName, (*forwardProperty)->propertyName);

    forwardProperty = (ForwardPropertyT **)DbListItem(subFields->replaceForward, 2);
    vertexLabelName = "a";
    propertyName = "c2";
    EXPECT_STREQ(vertexLabelName, (*forwardProperty)->vertexlabelName);
    EXPECT_STREQ(propertyName, (*forwardProperty)->propertyName);
}

// create path success: with variable
TEST_F(UtPathParsePath, PathWithNodeVariable1)
{
    const char *input = R"(
        CREATE PATH NonLiner123 (
            MATCH
            (:a)-[:AB]->(vb:b),
            (vb:b)-[:BD]->(:d)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    std::string label = "NonLiner123";
    EXPECT_EQ(stmtOne->pathName, label);
}

// create path success: one node => two variable
TEST_F(UtPathParsePath, PathWithNodeVariable2)
{
    const char *input = R"(
        CREATE PATH NonLiner123 (
            MATCH
            (:a)-[:AB]->(vb:b),
            (vb2:b)-[:BD]->(:d)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);
}

// create path fail: one variable => two label
TEST_F(UtPathParsePath, PathWithNodeVariable3)
{
    const char *input = R"(
        CREATE PATH NonLiner123 (
            MATCH
            (:a)-[:AB]->(vb:b),
            (:b)-[:BD]->(vb:d)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_INVALID_OBJECT_DEFINITION);
}

// create path fail: variable can't project to label
TEST_F(UtPathParsePath, PathWithNodeVariable4)
{
    const char *input = R"(
        CREATE PATH NonLiner123 (
            MATCH
            (:a)-[:AB]->(vb),
            (vb:b)-[:BD]->(:d)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_INVALID_OBJECT_DEFINITION);
}

// create path success: variable can project to label
TEST_F(UtPathParsePath, PathWithNodeVariable5)
{
    const char *input = R"(
        CREATE PATH NonLiner123 (
            MATCH
            (:a)-[:AB]->(vb:vb),
            (vb)-[:BD]->(:d)
        );
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);
}

// create path success: with cycle
TEST_F(UtPathParsePath, PathPath13)
{
    const char *input = R"(CREATE PATH path13 (
        MATCH (v34:v34)-[:e3437]->(:v37)-[:e3739]->(:v39)-[:e3938]->(:v38)-[:e3835]->(:v35)-[:e3534]->(v34),
            (v34)-[:e3436]->(:v36)
    );)";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);
}

// create path success: with cycle
TEST_F(UtPathParsePath, PathPath14)
{
    const char *input = R"(CREATE PATH path13 (
        MATCH (:a)-[:ab]->(b:b)-[:bc]->(:c)-[:cb]->(b)-[:bd]->(:d)
    );)";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);
}

// create path success: with variable
TEST_F(UtPathParsePath, PathParseDeletionMode1)
{
    const char *input = R"(
        CREATE PATH NonLiner123 (
            MATCH
            (:a)-[:AB]->(vb:b),
            (vb:b)-[:BD]->(:d)
        WITH MODE 2);
    )";

    GqlParsedListT *parsedList;
    Status ret = GqlParse(g_basicMem, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    GqlStatementT **stmts = (GqlStatementT **)(parsedList->statements.items);
    GqlStatementT *stmt = *(stmts + 0 * sizeof(GqlStatementT *));
    ParseCreatePathStmtT *stmtOne = (ParseCreatePathStmtT *)stmt;
    ASSERT_EQ(stmtOne->base.tag, CREATE_PATH);
    std::string label = "NonLiner123";
    EXPECT_EQ(stmtOne->pathName, label);
    EXPECT_EQ(stmtOne->deletionMode, 2u);
}
