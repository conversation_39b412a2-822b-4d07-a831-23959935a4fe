/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: ut_label_latch.cc
 * Description:
 * Author: baiyang
 * Create: 2022-3-11
 */
#include <vector>
#include <future>
#include <iostream>
#include "gtest/gtest.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "dm_data_basic.h"
#include "dm_data_prop.h"
#include "stub.h"
#include "pthread.h"
#include "adpt_types.h"
#include "adpt_semaphore.h"
#include "adpt_spinlock.h"
#include "db_config.h"
#include "db_sysapp_context.h"
#include "db_common_init.h"
#include "adpt_sleep.h"
#include "common_init.h"
#include "db_label_latch_mgr.h"
#include "ut_mem_common.h"
using namespace std;

class UtLabelLatch : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        CommonInit();
    }
    static void TearDownTestCase()
    {
        CommonRelease();
    };
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtLabelLatch, baseFunc)
{
    uint32_t labelLatchId;
    uint32_t labelLatchVersionId;     // 0
    uint32_t labelLatchVersionIdNew;  // 1
    ShmemPtrT latchLatchShmAddr;
    InitLabelLatch(&labelLatchId, &labelLatchVersionId, &latchLatchShmAddr, NULL);
    LabelRWLatchT *labelLatch = GetLabelRWLatchPtrById(labelLatchId, NULL);
    LabelWLatchAcquire(labelLatch);
    bool isGetLatch = LabelWLatchTryAcquire(labelLatch);
    ASSERT_EQ(isGetLatch, false);  // 未解锁，try失败
    LabelWLatchRelease(labelLatch);
    isGetLatch = LabelWLatchTryAcquire(labelLatch);
    ASSERT_EQ(isGetLatch, true);  // 解锁，try成功
    RemoveLabelLatchAndRuMode(labelLatch, labelLatchId, labelLatchVersionId, NULL);
    InitLabelLatch(&labelLatchId, &labelLatchVersionIdNew, &latchLatchShmAddr, NULL);
    ASSERT_EQ(labelLatchVersionId + 1, labelLatchVersionIdNew);  // 版本号+1，说明已被drop
    labelLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(labelLatchId, NULL);
    LabelWLatchAcquire(labelLatch);
    RemoveLabelLatchAndRuMode(labelLatch, labelLatchId, labelLatchVersionIdNew, NULL);
}

TEST_F(UtLabelLatch, useAfterDrop)
{
    uint32_t testNum = 1000;
    uint32_t labelLatchId;
    uint32_t labelLatchIdNew;
    uint32_t labelLatchVersionId;
    uint32_t labelLatchVersionIdNew;
    ShmemPtrT latchLatchShmAddr;
    LabelRWLatchT *labelLatch;
    for (uint32_t i = 0; i < testNum; i++) {
        InitLabelLatch(&labelLatchId, &labelLatchVersionId, &latchLatchShmAddr, NULL);
        labelLatch = GetLabelRWLatchPtrById(labelLatchId, NULL);
        LabelWLatchAcquire(labelLatch);
        RemoveLabelLatchAndRuMode(labelLatch, labelLatchId, labelLatchVersionId, NULL);  // drop掉

        // 申请新的锁
        InitLabelLatch(&labelLatchIdNew, &labelLatchVersionIdNew, &latchLatchShmAddr, NULL);
        ASSERT_EQ(labelLatchId, labelLatchIdNew);  // 预期申请到刚刚释放的锁（只有单线程在申请的情况）
        ASSERT_EQ(labelLatchVersionId + 1, labelLatchVersionIdNew);  // 版本号+1，说明已被drop
        labelLatch = GetLabelRWLatchPtrById(labelLatchIdNew, NULL);
        LabelWLatchAcquire(labelLatch);  // 新申请出来的还能正常加上锁
        RemoveLabelLatchAndRuMode(labelLatch, labelLatchIdNew, labelLatchVersionIdNew, NULL);
    }
}

void RemoveLabelLatchStub(
    LabelRWLatchT *labelRWLatch, uint32_t labelLatchId, uint32_t labelLatchVersionId, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelRWLatch);
    if (labelRWLatch->versionId != labelLatchVersionId) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "try to remove old latch[labelLatchId:%" PRIu32 "] again.", labelLatchId);
        return;
    }
    labelRWLatch->versionId++;
    LabelLatchMgrT *llMgr = (LabelLatchMgrT *)DbGetShmemStructById(DB_LABEL_LATCH_STRUCT_ID, DbGetProcGlobalId());
    if (llMgr == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "llMgr is invalid.");
        return;
    }
    DbRWSpinWLock(&llMgr->rwlock);
    (void)DbShmArrayRemoveItem(&llMgr->labelLatchArray, labelLatchId);
    DbRWSpinWUnlock(&llMgr->rwlock);
}

TEST_F(UtLabelLatch, multiUse)
{
    (void)setStubC((void *)RemoveLabelLatchAndRuMode, (void *)RemoveLabelLatchStub);  // 模拟某个线程没有放锁的情况
    uint32_t labelLatchId;
    uint32_t labelLatchVersionId;
    ShmemPtrT latchLatchShmAddr;
    LabelRWLatchT *labelLatch;
    InitLabelLatch(&labelLatchId, &labelLatchVersionId, &latchLatchShmAddr, NULL);
    labelLatch = GetLabelRWLatchPtrById(labelLatchId, NULL);
    LabelWLatchAcquire(labelLatch);
    RemoveLabelLatchAndRuMode(labelLatch, labelLatchId, labelLatchVersionId, NULL);  // drop掉

    uint32_t labelLatchIdNew;
    uint32_t labelLatchVersionIdNew;
    // 申请新的锁
    InitLabelLatch(&labelLatchIdNew, &labelLatchVersionIdNew, &latchLatchShmAddr, NULL);
    ASSERT_EQ(labelLatchId, labelLatchIdNew);  // 预期申请到刚刚释放的锁（只有单线程在申请的情况）
    ASSERT_EQ(labelLatchVersionId + 1, labelLatchVersionIdNew);  // 版本号+1，说明已被drop
    labelLatch = GetLabelRWLatchPtrById(labelLatchIdNew, NULL);
    ASSERT_EQ(DB_LATCH_SERVER_X, labelLatch->rwlatch.latchMode);
    uint32_t timeoutMs = 100;
    bool isSuccess = LabelWLatchTimedAcquire(labelLatch, timeoutMs * (uint32_t)USECONDS_IN_MSECOND);  // 预期加锁失败
    ASSERT_EQ(false, isSuccess);
    clearAllStub();
    RemoveLabelLatchAndRuMode(labelLatch, labelLatchIdNew, labelLatchVersionIdNew, NULL);  // drop掉
}

DbMemCtxT *DbSrvGetSysShmCtxStub(uint16_t instanceId)
{
    return NULL;
}

extern "C" uint32_t LabelLatchCreateMemCtx(DbMemCtxT **llMgrTopShmMemCtx, DbInstanceHdT dbInstance);
uint32_t LabelLatchCreateMemCtxStub(DbMemCtxT **llMgrTopShmMemCtx, DbInstanceHdT dbInstance)
{
    return 0;
}

TEST_F(UtLabelLatch, labelLatchMgrInitFail)
{
    (void)setStubC((void *)DbSrvGetSysShmCtx, (void *)DbSrvGetSysShmCtxStub);
    uint32_t ret = DbInitLabelLatchMgr(NULL);
    EXPECT_EQ(ret, GMERR_UNEXPECTED_NULL_VALUE);
    (void)setStubC((void *)LabelLatchCreateMemCtx, (void *)LabelLatchCreateMemCtxStub);
    ret = DbInitLabelLatchMgr(NULL);
    EXPECT_EQ(ret, GMERR_UNEXPECTED_NULL_VALUE);
    clearAllStub();
}
