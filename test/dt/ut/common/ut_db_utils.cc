/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: db_utils相关的单元测试用例
 * Author:
 * Create: 2022-03-20
 */

#include <climits>
#include "gtest/gtest.h"
#include "stub.h"
#include "db_utils.h"
#include "gmc_errno.h"
#include "adpt_patch_c2h_type.h"
#include "adpt_patch_c2h_hpe.h"
#include "db_patch_gmserver_c2h.h"
#include "db_patch_common_c2h.h"
#include "adpt_init.h"

class UtDbUtils : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}

    static void SetUpTestCase()
    {
        init();
    };

    static void TearDownTestCase()
    {
        clearAllStub();
    };
};

TEST_F(UtDbUtils, LibGmdbPatchC2hHook)
{
    tagHpePatchInfo patchInfo;
    bool process = true;
    int32_t num = LibGmdbPatchC2hHook(true, NULL, &process);
    EXPECT_EQ(num, 1);
    char acFileName[HPE_PAT_FILENAME_LEN] = "libgmdb.so.5";
    strcpy_s(patchInfo.acFileName, strlen(acFileName) + 1, acFileName);
    process = true;
    num = LibGmdbPatchC2hHook(true, &patchInfo, &process);
    EXPECT_EQ(num, 0);
    num = LibGmdbPatchC2hHook(false, &patchInfo, &process);
    EXPECT_EQ(num, 0);
}
