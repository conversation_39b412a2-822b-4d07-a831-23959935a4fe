/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: ut_queue.cc
 * Description: unit test for the DbQueue.
 * Author: <PERSON>
 * Create: 2022/12/14
 */
#include "gtest/gtest.h"
#include "db_common_init.h"
#include "common_init.h"
#include "db_queue.h"

static DbMemCtxT *g_memCtx = NULL;

class UtDbQueue : public testing ::Test {
protected:
    static void SetUpTestCase()
    {
        CommonInit();
        DbMemCtxArgsT args = {0};
        g_memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo(g_memCtx);
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(g_memCtx);
        CommonRelease();
    }
};

TEST_F(UtDbQueue, DbQueueAddAndRemove)
{
    DbMemCtxArgsT args = {0};
    DbMemCtxT *memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "Test Db queue", &args);
    EXPECT_NE(nullptr, memCtx);

    DbQueueT queue = {0};
    Status ret = DbQueueCreate(memCtx, &queue, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);

    // 入队数据0-299，数据要大于队列初始容量，便于检测扩容正确性
    for (uint32_t i = 0; i < 300; i++) {
        ret = DbQueuePush(&queue, (void *)&i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 检查DbQueueFront和DbQueueBack接口正确性
    uint32_t *item = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t));
    ret = DbQueueFront(&queue, (void *)item);
    EXPECT_EQ(0u, *item);
    ret = DbQueueFront(&queue, (void *)item);
    EXPECT_EQ(0u, *item);
    ret = DbQueueBack(&queue, (void *)item);
    EXPECT_EQ(299u, *item);

    // 检查Queue是否为空以及大小
    EXPECT_FALSE(DbQueueIsEmpty(&queue));
    EXPECT_EQ(DbQueueSize(&queue), 300u);

    // 出队数据0-299
    for (uint32_t i = 0; i < 300; i++) {
        ret = DbQueuePop(&queue, (void *)item);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i, *item);
    }

    // 检查队列元素是否为空
    EXPECT_TRUE(DbQueueIsEmpty(&queue));

    DbQueueDestroy(&queue);
    DbDeleteDynMemCtx(memCtx);
}

TEST_F(UtDbQueue, DbQueueAddAndRemove2)
{
    DbMemCtxArgsT args = {0};
    DbMemCtxT *memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "Test Db queue", &args);
    EXPECT_NE(nullptr, memCtx);

    DbQueueT queue = {0};
    Status ret = DbQueueCreateWithSize(memCtx, &queue, sizeof(uint32_t), 10);
    EXPECT_EQ(ret, GMERR_OK);

    // 入队10个，出队10个，入队10个，期望不扩容
    for (uint32_t i = 0; i < 10; i++) {
        ret = DbQueuePush(&queue, (void *)&i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(10u, queue.capacity);
    uint32_t *num = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t));
    *num = 100;  // a wrong number
    for (uint32_t i = 0; i < 10; i++) {
        ret = DbQueuePop(&queue, (void *)num);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i, *num);
    }
    EXPECT_EQ(10u, queue.capacity);
    for (uint32_t i = 0; i < 10; i++) {
        ret = DbQueuePush(&queue, (void *)&i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(10u, queue.capacity);
    for (uint32_t i = 10; i < 20; i++) {
        ret = DbQueuePush(&queue, (void *)&i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(20u, queue.capacity);  // 根据EXTEND_FACTOR计算扩容后的长度
    *num = 100;                      // a wrong number
    for (uint32_t i = 0; i < 20; i++) {
        ret = DbQueuePop(&queue, (void *)num);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i, *num);
    }
    EXPECT_EQ(20u, queue.capacity);  // 暂无缩容逻辑

    // 检查队列元素是否为空
    EXPECT_TRUE(DbQueueIsEmpty(&queue));

    DbDynMemCtxFree(memCtx, num);
    DbQueueDestroy(&queue);
    DbDeleteDynMemCtx(memCtx);
}
