#include "gtest/gtest.h"
#include "string"
#include "db_config.h"
#include "db_config_file.h"
#include "adpt_function_loader.h"
#ifdef __cplusplus

extern "C" {
#endif
#include "db_dyn_load.h"
#ifdef __cplusplus
}
#endif

class UtDynLoad : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

#ifdef SINGLE_SO
TEST_F(UtDynLoad, DynloadEmptyFeature)
{
    DbSetServerThreadFlag();
    DbCfgMgrHandleT emCfgHandle;
    Status ret = DbInitConfigFile("./ut_common_config_test_files/so_dynload_empty_gmserver.ini", &emCfgHandle);
    DbSetCfgHandle(emCfgHandle);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(DbDynLoadFeatureSo(NULL), GMERR_OK);
    DbDynUnloadFeatureSoHandle();
}

TEST_F(UtDynLoad, DynloadSingleSoUnLoadSoHandle)
{
    system("cd ut_common_config_test_files;pwd;gcc -shared -fpic -lm -ldl -o libgmdatalog.so datalog.c");
    void *soHandle = NULL;
    Status ret = DbAdptLoadLibrary("./ut_common_config_test_files/libgmdatalog.so", &soHandle, RTLD_NOW);
    EXPECT_EQ(ret, GMERR_OK);
    ret = DbDynLoadSetSoHandleClt(COMPONENT_MEMDATA, soHandle);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(DbDynLoadSoIsLoadedClt(COMPONENT_MEMDATA), true);
    DbSetServerThreadFlag();
    DbDynUnloadFeatureSoHandle();
    EXPECT_EQ(DbDynLoadSoIsLoadedClt(COMPONENT_MEMDATA), false);
}

TEST_F(UtDynLoad, DynloadSingleSoGetFun)
{
    EXPECT_EQ(DbDynLoadFeatureSo(NULL), GMERR_OK);
    EXPECT_EQ(DbDynLoadHasFeature(COMPONENT_MEMDATA), true);
    void *fun = DbDynLoadGetFunc(COMPONENT_MEMDATA, "init");
    EXPECT_NE((long)(uintptr_t)fun, NULL);
    DbSetServerThreadFlag();
    DbDynUnloadFeatureSoHandle();
}

TEST_F(UtDynLoad, DynloadSingleSoFeatureIsLoad)
{
    Status ret = DbDynLoadFeatureSo(NULL);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(DbDynLoadSoIsLoadedClt(COMPONENT_MEMDATA), false);
    DbSetServerThreadFlag();
    DbDynUnloadFeatureSoHandle();
}

TEST_F(UtDynLoad, DynloadSingleSoSetSoHandle)
{
    Status ret = DbDynLoadFeatureSo(NULL);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(DbDynLoadSoIsLoadedClt(COMPONENT_MEMDATA), false);
    system("cd ut_common_config_test_files;gcc -shared -fpic -lm -ldl -o libgmdatalog.so datalog.c");
    void *soHandle = NULL;
    ret = DbAdptLoadLibrary("./ut_common_config_test_files/libgmdatalog.so", &soHandle, RTLD_NOW);
    EXPECT_EQ(ret, GMERR_OK);
    ret = DbDynLoadSetSoHandleClt(COMPONENT_MEMDATA, soHandle);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(DbDynLoadSoIsLoadedClt(COMPONENT_MEMDATA), true);
    DbSetServerThreadFlag();
    DbDynUnloadFeatureSoHandle();
}
#endif

TEST_F(UtDynLoad, DISABLED_DynloadMutilSoLoad)
{
    // how to run this testcase:
    // step 1. make sure featureLibPath config option in
    //         test/dt/ut/common/ut_common_config_test_files/so_dynload_ok_gmserver.ini is right
    // step 2. change code , set  g_funcCfg as follow
    //   DynLoadFuncCfgT g_funcCfg[] = {
    //    {{"kv" , "qe"}, {NULL, SET_DYNLOAD_FUNC(kv)}},
    //    {{"tree" , "qe"}, {NULL, SET_DYNLOAD_FUNC(tree)}},
    //    {{"datalog" , "qe"}, {NULL, SET_DYNLOAD_FUNC(datalog)}},
    //   };
    // step 3. make sure build version is mutil so, not single so
    // step 4. compile the c files to get some so files
    // cd test/dt/ut/common/ut_common_config_test_files/
    // gcc -shared -fpic -lm -ldl -o libgmkv.so kvso.c
    // gcc -shared -fpic -lm -ldl -o libgmtree.so treeso.c
    // gcc -shared -fpic -lm -ldl -o libgmdatalog.so datalog.c

    DbCfgMgrHandleT emCfgHandle;
    Status ret = DbInitConfigFile("./ut_common_config_test_files/so_dynload_ok_gmserver.ini", &emCfgHandle);
    EXPECT_EQ(ret, GMERR_OK);
    DbSetCfgHandle(emCfgHandle);
    char feature[] = "xxx";
    char subsystem[] = "xxxxx";
    EXPECT_EQ((long)DbDynLoadGetFunc(feature, subsystem), NULL);
    char feature1[] = "kv";
    char subsystem1[] = "qe";
    EXPECT_NE((long)DbDynLoadGetFunc(feature1, subsystem1), NULL);
    char feature2[] = "tree";
    char subsystem2[] = "qe";
    EXPECT_NE((long)DbDynLoadGetFunc(feature2, subsystem2), NULL);
    char feature3[] = "datalog";
    char subsystem3[] = "qe";
    EXPECT_NE((long)DbDynLoadGetFunc(feature3, subsystem3), NULL);

    EXPECT_EQ(DbDynLoadHasFeature(feature1), true);
    EXPECT_EQ(DbDynLoadHasFeature(feature2), true);
    EXPECT_EQ(DbDynLoadHasFeature(feature3), true);
    EXPECT_EQ(DbDynLoadHasFeature(feature), false);
}

TEST_F(UtDynLoad, DISABLED_DynloadMutilSoLoadDenpency)
{
    // how to run this testcase:
    // step.1  config file : featureNames = KV
    // step.2

    //   DynLoadFuncCfgT g_funcCfg[] = {
    //      {{"kv" , "qe"}, {NULL, SET_DYNLOAD_FUNC(kv)}},
    //      {{"tree" , "qe"}, {NULL, SET_DYNLOAD_FUNC(tree)}},
    //      {{"datalog" , "qe"}, {NULL, SET_DYNLOAD_FUNC(datalog)}},
    //   };
    //   case A
    //         DynLoadFeatureDepT g_featureDep[] = {
    //                  {"kv", "tree,datalog"}
    //         };
    //   case B
    //         DynLoadFeatureDepT g_featureDep[] = {
    //                  {"kv", "tree"}
    //                  {"tree", "datalog"}
    //         };
    //   case C
    //         DynLoadFeatureDepT g_featureDep[] = {
    //                  {"kv", "tree,datalog"}
    //                  {"tree", "datalog,kv"}
    //         };
    // step 3. make sure build version is mutil so, not single so
    // step 4. compile the c files to get some so files
    // cd test/dt/ut/common/ut_common_config_test_files/
    // gcc -shared -fpic -lm -ldl -o libgmkv.so kvso.c
    // gcc -shared -fpic -lm -ldl -o libgmtree.so treeso.c
    // gcc -shared -fpic -lm -ldl -o libgmdatalog.so datalog.c

    DbCfgMgrHandleT emCfgHandle;
    Status ret = DbInitConfigFile("./ut_common_config_test_files/so_dynload_ok_gmserver.ini", &emCfgHandle);
    EXPECT_EQ(ret, GMERR_OK);
    DbSetCfgHandle(emCfgHandle);
    char feature1[] = "kv";
    char subsystem1[] = "qe";
    EXPECT_NE((long)DbDynLoadGetFunc(feature1, subsystem1), NULL);
    char feature2[] = "tree";
    char subsystem2[] = "qe";
    EXPECT_NE((long)DbDynLoadGetFunc(feature2, subsystem2), NULL);
    char feature3[] = "datalog";
    char subsystem3[] = "qe";
    EXPECT_NE((long)DbDynLoadGetFunc(feature3, subsystem3), NULL);

    EXPECT_EQ(DbDynLoadHasFeature(feature1), true);
    EXPECT_EQ(DbDynLoadHasFeature(feature2), true);
    EXPECT_EQ(DbDynLoadHasFeature(feature3), true);
}

// normal case: getting feature lib path
TEST_F(UtDynLoad, DynloadGetFeatureLibPath)
{
    char libNameBuf[DB_MAX_PATH] = "";
    char featureDir[DB_MAX_PATH] = "testDir";
    const char *featureName = "testFeature";
    Status ret = DbDynGetFeatureLibPath(libNameBuf, featureDir, featureName);
    EXPECT_EQ(GMERR_OK, ret);
}

// error case: feature name contains more than 254 characters
TEST_F(UtDynLoad, DynloadGetFeatureLibPath2)
{
    uint32_t defaultCharacterLen = 8;
    char libNameBuf[DB_MAX_PATH] = "";
    char featureDir[DB_MAX_PATH] = "";

    std::string featureName = "";
    for (uint32_t i = 0; i < DB_MAX_PATH - defaultCharacterLen; i++) {
        featureName += "t";
    }
    Status ret = DbDynGetFeatureLibPath(libNameBuf, featureDir, featureName.c_str());
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
}
