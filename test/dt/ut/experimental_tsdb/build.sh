#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# used for build db entry
#
set -e

CUR_DIR=$(cd "$(dirname $0)";pwd)
GMDB_DIR="$CUR_DIR"/
TMP_DIR="$GMDB_DIR"/tmp
GMDB_HOME=`pwd`/../../../../
GMDB_LIB_PATH=$GMDB_HOME/output/euler/aarch64/lib/
GMDB_BUILD_LIB_PATH=$GMDB_HOME/build/lib/
GMDB_BIN_PATH=$GMDB_HOME/output/euler/aarch64/bin/

. ../../gmdb_dt_function.sh

function main() {
    if [ "$1" == "clean" ]; then
        clean_build_hisotry "ut_experimental_tsdb"
        exit 0
    fi
    if [ ! -d "$GMDB_DIR/tsdb_dt_tools" ];then
        echo "git clone ssh://***************************:2222/TSDB/tsdb_dt_tools.git"
        git clone ssh://***************************:2222/TSDB/tsdb_dt_tools.git
    fi
    if [ ! -f "$GMDB_BIN_PATH/gmserver" ];then
        ln -s $GMDB_BIN_PATH/gmserver_ts $GMDB_BIN_PATH/gmserver
        ln -s $GMDB_BIN_PATH/gmsysview_ts $GMDB_BIN_PATH/gmsysview
        ln -s $GMDB_BIN_PATH/gmimport_ts $GMDB_BIN_PATH/gmimport
        ln -s $GMDB_BIN_PATH/gmexport_ts $GMDB_BIN_PATH/gmexport
        ln -s $GMDB_BIN_PATH/gmrule_ts $GMDB_BIN_PATH/gmrule
        ln -s $GMDB_LIB_PATH/libgmtools_ts.so $GMDB_LIB_PATH/libgmtools.so
    fi
    if [ ! -f "$GMDB_BUILD_LIB_PATH/libgmtools.so" ];then
        ln -s $GMDB_BUILD_LIB_PATH/libgmtools_ts.so $GMDB_BUILD_LIB_PATH/libgmtools.so
    fi
    dt_build_main --test ut --module experimental_tsdb --experimental_tsdb "$@"
    cd $GMDB_DIR
    cp -rf ./ut_experimental_tsdb.sh ./ut_experimental_tsdb
    dos2unix ./ut_experimental_tsdb
    chmod 777 ./ut_experimental_tsdb
    cd -
}

main "$@"
