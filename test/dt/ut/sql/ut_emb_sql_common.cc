/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Index Commands UT
 * Author: SQL Team
 * Create: 2024-02-01
 */

#include "common_init.h"
#include "db_instance.h"
#include "db_internal_error.h"
#include "dm_meta_priv.h"
#include "dm_data_basic.h"
#include "cpl_base_def.h"
#include "cpl_ir_logical_op.h"
#include "drt_connection_inner.h"
#include "adpt_atomic.h"
#include "ee_command_fusion.h"
#include "ee_init.h"
#include "ee_systbl.h"
#include "srv_data_service.h"
#include "se_persist_inner.h"
#include "ut_emb_sql_common.h"

using namespace std;
const char *g_defaultSqlCfgFileName = "gmserver_sql.ini";

extern "C" Status SqlServiceEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx);

static void UtDrtDetachConnection(DrtConnectionT *conn)
{
    DB_POINTER2(conn, conn->connMgr);
    conn->nodeId.nodeId = DB_INVALID_ID16;
    (void)DbAtomicDec(&conn->ref);
    DrtSetConnStatus(conn, CONN_STATUS_CLOSED);
    DrtConnProcessClosedList(conn->connMgr);
}

Status QryInitUserAndGroupStub(SessionT *session, SessionParamT *param)
{
    (void)param;
    errno_t rc = strcpy_s(session->externalUser.dbUserName, sizeof(session->externalUser.dbUserName), "root");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    rc = strcpy_s(session->externalUser.dbProcessName, sizeof(session->externalUser.dbProcessName), "gmrule");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status QryTestDrtConnWritePackSuccessStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    return GMERR_OK;
}

Status CataLoginVerify(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isDBA, bool *isGroupLogin, CataRoleT *role)
{
    Status ret;
    // step 1 : 根据userName和processName判断能否登录
    if ((ret = CataLoginVerifyByUser(userNameInfo, login, isDBA, isGroupLogin, role) == GMERR_OK)) {
        return GMERR_OK;
    }
    // step 2 : 根据groupName和processName判断能否登录
    return CataLoginVerifyByGroup(userNameInfo, login, isDBA, isGroupLogin, role);
}

Status QryCheckLoginPrivByGroupListStub(SessionT *session, bool *login, CataRoleT *role)
{
    const SessionParamT param = {0};
    Status ret = QryInitUserAndGroup(session, &param);
    if (ret != GMERR_OK) {
        return ret;
    }
    CataUserNameInfoT user = {
        session->externalUser.dbUserName,
        session->externalUser.dbGroupName,
        session->externalUser.dbProcessName,
    };
    bool isGroupLogin;
    ret = (Status)CataLoginVerify(&user, login, &session->isDBA, &isGroupLogin, role);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Internal error occurs when check login.");
        return ret;
    }
    return GMERR_OK;
}

Status QryTestAllocConn(DrtConnectionT **conn)
{
    static uint32_t connId = 0;
    char connName[DM_MAX_NAME_LENGTH] = {0};
    sprintf_s(connName, DM_MAX_NAME_LENGTH, "testAllocConn%d", DbAtomicInc(&connId));

    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    const char *auditUserInfo = "0-ut_query";
    DbCredT cred = {0};
    *conn = DrtAllocConnection(&drtInstance->connMgr, auditUserInfo, &cred);
    DrtNodeT *drtNode = DrtAllocNodeWithName(&drtInstance->nodeMgr, NODE_TYPE_CLIENT, connName);
    if (drtNode == NULL) {
        UtDrtDetachConnection(*conn);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    } else {
        drtNode->pid = 0;
        drtNode->connId = (*conn)->id;
        drtNode->nodeId.flag = 0;
    }
    DrtSetConnStatus((*conn), CONN_STATUS_NORMAL);
    (*conn)->nodeId = drtNode->nodeId;
    (*conn)->cltTimeoutMs = DEFAULT_TIMEOUT * DB_RDTSC_SECOND_TO_MSECOND_TO_USECOND;

    return GMERR_OK;
}

Status QryTestAllocSession(DrtConnectionT **conn, FixBufferT **rsp)
{
    Status ret = QryTestAllocConn(conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupStub);
    (void)setStubC((void *)QryCheckLoginPrivByGroupList, (void *)QryCheckLoginPrivByGroupListStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)QryTestDrtConnWritePackSuccessStub);
    CliConnectResponseT connResp = {0};
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    SessionParamT param = {.logThreshold = 0, .rollBackThreshold = 0, .userName = "", .pwd = ""};
    ret = QryAllocSessionForDrt(*conn, &connResp, drtInstance, &param);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

void QryDbDeleteDynMemCtxStub(DbMemCtxT *ctx)
{
    return;
}

void QryTestReleaseConn(DrtConnectionT *conn)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);

    DrtFreeNode(&drtInstance->nodeMgr, conn->nodeId.nodeId);
    UtDrtDetachConnection(conn);  // 模拟ra线程回收
}

void QryTestReleaseSession(DrtConnectionT *conn)
{
    (void)setStubC((void *)DbDeleteDynMemCtx, (void *)QryDbDeleteDynMemCtxStub);
    QryReleaseSessionForDrt(conn);
    QryTestReleaseConn(conn);
    clearAllStub();
}

// init storage module
int32_t DbInitStorage(DbMemCtxT *topShmMemCtx)
{
    // use default config without config file
    SeConfigT config = {0};

    Status ret = StorageConfigGet(NULL, &config, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    printf("config.maxTrxNum:%d----------------------\n", config.maxTrxNum);
    SeInstanceHdT se = NULL;
    return SeCreateInstance(NULL, (DbMemCtxT *)topShmMemCtx, &config, &se);
}

int32_t QrySetCfg(const char *configName, const char *configValue)
{
    DbCfgMgrHandleT handle = DbGetCfgHandle(NULL);
    return DbCfgSetByNameInner(handle, configName, configValue, false, true);
}

int32_t BaseInit(bool isFastReadUncommitted, const char *cfgFileName)
{
    int32_t ret;
#ifdef IDS_HAOTIAN
    char *gmdbHome = getenv("GMDB_HOME");
    if (gmdbHome == NULL) {
        std::cout << "\033[31m"
                  << "Please exe: source ../../../../scripts/env_aarch64.sh "
                  << "\033[0m" << std::endl;
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    string fileName = string(gmdbHome) + string("/test/dt/ut/sql/") + "gmserver_sql.ini";
    CommonInit(cfgFileName != NULL ? cfgFileName : fileName.c_str());
#else
    CommonInit(cfgFileName);
#endif
    ret = CommonInitDrtInstanceInEuler(false);
    if (ret != GMERR_OK) {
        printf("Drt instance init failed!\n");
        return ret;
    }
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    DrtDataPlaneT *plane = SaAllocDataPlane(&drtIns->sendAgent, {0, 1023});
    if (plane == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    // 初始化过程获取pageSize
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    if (cfgHandle == NULL) {
        DB_LOG_AND_SET_LASERR(NO_DATA_NULL_POINTER, "Config handle is null.");
        return NO_DATA_NULL_POINTER;
    }
    int32_t cfgValue;
    DbCfgMgrT *mgr = DbGetCfgHandle(NULL);
    DbCfgGetInt32(mgr, DB_CFG_SE_PAGE_SIZE, true, &cfgValue);
    ret = QrySetCfg("DBA", "root:gmrule;gmips");
    if (ret != GMERR_OK) {
        printf("set DBA from admin:gmrule;gmips to root:gmrule;gmips failed\n");
        return ret;
    }
    printf("[WARN] set DBA from admin:gmrule;gmips to root:gmrule;gmips\n");

#ifdef FEATURE_PERSISTENCE
    ret = QrySetCfg("persistentMode", "1");
    if (ret != GMERR_OK) {
        printf("set persistentMode from 0 to 1 failed\n");
        return ret;
    }
    system("rm -rf ./data/gmdb/");
    system("mkdir -p ./data/gmdb/");
    ret = QrySetCfg("dataFileDirPath", "./data/gmdb/");
    if (ret != GMERR_OK) {
        printf("set dataFileDirPath to ./data/gmdb/ failed\n");
        return ret;
    }
    SeSetPersistMode(PERSIST_INCREMENT);
#endif
    ret = CataLabelCacheInitWithOutTimer(DbGetGlobalInstance());
    if (ret != GMERR_OK) {
        // logging
        printf("catalog cache init failed!\n");
        return ret;
    }

    DbMemCtxT *topShmemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    EXPECT_TRUE(topShmemCtx != NULL);
    ret = DbInitStorage(topShmemCtx);
    if (ret != GMERR_OK) {
        // logging
        printf("storage init failed!\n");
        return ret;
    }

    ret = DataServiceInit(DbGetGlobalInstance());
    if (ret != GMERR_OK) {
        printf("service init failed!\n");
        return ret;
    }

    ret = CompilerInit();
    if (ret != GMERR_OK) {
        printf("compiler init failed!\n");
        return ret;
    }

    ret = ExecutorInit(NULL);
    if (ret != GMERR_OK) {
        printf("executor init failed!\n");
        return ret;
    }

    ret = QrySetCfg("userPolicyMode", "1");
    if (isFastReadUncommitted) {
        QrySetCfg("isFastReadUncommitted", "1");
    } else {
        QrySetCfg("isFastReadUncommitted", "0");
    }
    return ret;
}

void DbUninitStorage(void)
{
    SeDestroyInstance(GET_INSTANCE_ID);
}

void BaseUninit()
{
    DataServiceUnInit(NULL);
    DrtInstanceDestroy(NULL);
    DbUninitStorage();
    DbDestroyTopShmemCtx(GET_INSTANCE_ID);
    CommonRelease();
    clearAllStub();
}

Status SqlInitLabelList4irStmt(DbMemCtxT *memCtx, SqlIrStmtT *irStmt)
{
    irStmt->labelList = static_cast<DbListT *>(DbDynMemCtxAlloc(memCtx, sizeof(DbListT)));
    if (irStmt->labelList == NULL) {
        DbDynMemCtxFree(memCtx, irStmt);
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "[SQL Service] Unable to malloc for create labelList in query stmt .");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(irStmt->labelList, sizeof(DbListT), 0, sizeof(DbListT));
    DbCreateList(irStmt->labelList, sizeof(RelatedTblT), memCtx);
    return GMERR_OK;
}

// 执行 DDL 语句，不包含解析层
Status SqlUtCmdExecutor(DbMemCtxT *memCtx, SessionT *session, NodeT *stmt)
{
    Status ret = SeTransBegin(session->seInstance, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    CStateT *state = NULL;
    EXPECT_EQ(GMERR_OK, CreateCommandState(memCtx, session, &state));
    state->memCtx = memCtx;
    state->seInstance = session->seInstance;
    state->session = session;
    NodeT *unused = NULL;
    ret = ExecFusionCmd(state, stmt, &unused);
    DestroyCommandState(state);
    SeTransCommit(session->seInstance);
    return ret;
}

// 简单执行已支持的 SQL 的 DDL 和 DML 语句, 包含解析层和执行
void SqlUtExecuteStmt(DbMemCtxT *memCtx, SessionT *session, const char *input)
{
    FixBufferT req = {0};
    FixBufCreate(&req, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    FixBufResetMem(&req);
    MsgHeaderT msgHeader = {0};
    msgHeader.modelType = MODEL_SQL;
    msgHeader.serviceId = DRT_SERVICE_EMBEDDED;
    msgHeader.opNum = 1;
    OpHeaderT opHeader = {0};
    FixBufPutData(&req, &msgHeader, sizeof(MsgHeaderT));
    FixBufPutData(&req, &opHeader, sizeof(OpHeaderT));
    FixBufPutData(&req, &input, sizeof(uint64_t));
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_SQL_EXEC,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    QrySessionSetReq(session, &req);
    uint64_t procStartTime = DbGlobalRdtsc();
    QrySessionSetStmtReqTimestamp(session, procStartTime);
    QueryResultListT *resultList = (QueryResultListT *)DbDynMemCtxAlloc(memCtx, sizeof(QueryResultListT));
    resultList->memCtx = memCtx;  // 简单语句，memCtx设置成一样，统一释放
    DbCreateList(&resultList->results, sizeof(QueryResultT), memCtx);
    session->resultList = resultList;
    DrtProcCtxT procCtx = {0};
    procCtx.specialCtx = session;
    procCtx.msgHeader = RpcGetMsgHeader(session->req);
    EXPECT_EQ(GMERR_OK, SqlServiceEntry(NULL, &procCtx));
    FixBufRelease(&req);
}

// 需要调用 CataReleaseVertexLabel
DmVertexLabelT *SqlUtGetVertexLabel(const char *name)
{
    uint32_t namespaceId;
    char *upperName = NULL;
    EXPECT_EQ(GMERR_OK, SqlAllocUpperStr(name, &upperName));
    EXPECT_EQ(GMERR_OK, CataGetNamespaceIdByName(NULL, PUBLIC_NAMESPACE_NAME, &namespaceId));
    CataKeyT cataKey = {.dbId = DEFAULT_DATABASE_ID, .nspId = namespaceId, .labelName = upperName};
    DmVertexLabelT *vertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel));
    SqlFreeUpperStr(upperName);
    return vertexLabel;
}

void SqlUtAnalyzeErr(Status ret)
{
    if (ret == GMERR_OUT_OF_MEMORY) {
        (void)DbPrintfDefault("Failed to analyze sql, malloc analyzer failure\n");
    }
    if (ret == GMERR_INTERNAL_ERROR) {
        (void)DbPrintfDefault("unknown error\n");
    }
}

Status SqlUtAnalyze(SessionT *session, DbMemCtxT *memCtx, SqlParsedListT *parsedList, DbListT *irStmtList)
{
    DB_POINTER4(session, memCtx, parsedList, irStmtList);
    DbCreateListWithExtendSize(irStmtList, (uint32_t)sizeof(SqlIrStmtT *), SQL_LIST_EXTEND_SIZE, memCtx);

    uint32_t cnt = DbListGetItemCnt(&parsedList->cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        NodeT *stmt = *(NodeT **)DbListItem(&parsedList->cmdList, i);
        if (stmt == NULL) {
            SqlUtAnalyzeErr(GMERR_INTERNAL_ERROR);
            return GMERR_INTERNAL_ERROR;
        }
        SqlIrStmtT *irStmt = (SqlIrStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(SqlIrStmtT));
        if (irStmt == NULL) {
            SqlUtAnalyzeErr(GMERR_OUT_OF_MEMORY);
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(irStmt, sizeof(SqlIrStmtT), 0, sizeof(SqlIrStmtT));
        Status ret = SqlAnalyze(session, memCtx, stmt, irStmt);
        if (ret != GMERR_OK) {
            // handle system error here
            SqlUtAnalyzeErr(ret);
            return ret;
        }

        ret = DbAppendListItem(irStmtList, &irStmt);
        if (ret != GMERR_OK) {
            SqlUtAnalyzeErr(GMERR_OUT_OF_MEMORY);
            return GMERR_OUT_OF_MEMORY;
        }
    }
    return GMERR_OK;
}

Status SqlUtRewrite(DbListT *irStmtList)
{
    DB_POINTER(irStmtList);
    uint32_t cnt = DbListGetItemCnt(irStmtList);
    for (uint32_t i = 0; i < cnt; i++) {
        SqlIrStmtT *stmt = *(SqlIrStmtT **)DbListItem(irStmtList, i);
        Status ret = SqlRewrite(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static inline bool SqlUtCheckVertexLabel(const DmVertexLabelT *label, const DmVertexLabelT *exptLabel)
{
    if (label->metaCommon.metaId != exptLabel->metaCommon.metaId) {
        return false;
    }
    if (strcmp(label->metaCommon.metaName, exptLabel->metaCommon.metaName) != 0) {
        return false;
    }
    return true;
}

static bool SqlUtCheckSchema(const AASchemaT *aaSchema, const DmSchemaT *dmSchema)
{
    if (aaSchema->propAA.propNum != dmSchema->propeNum) {
        return false;
    }
    for (uint32_t i = 0; i < aaSchema->propAA.propNum; ++i) {
        DmPropertySchemaT *aaProp = aaSchema->propAA.properties[i];
        DmPropertySchemaT *dmProp = &dmSchema->properties[i];
        if (aaProp->propeId != dmProp->propeId || aaProp->dataType != dmProp->dataType) {
            return false;
        }
        if (strcmp(aaProp->name, dmProp->name) != 0) {
            return false;
        }
    }
    return true;
}

bool SqlUtCheckDmlOp(IROpTypeE type, uint32_t arity, const IRExprT *irExpr, const DmVertexLabelT *label)
{
    if (irExpr->op->type != type || irExpr->op->arity != arity) {
        return false;
    }

    for (uint32_t i = 0; i < arity; ++i) {
        if (irExpr->children[i] == nullptr) {
            return false;
        }
    }

    OpLogicalAACudT *dmlOp = (OpLogicalAACudT *)(void *)irExpr->op;
    if (!SqlUtCheckVertexLabel(dmlOp->label, label)) {
        return false;
    }
    AASchemaT *dmlSchema = &dmlOp->schema;
    DmSchemaT *labelSchema = label->metaVertexLabel->schema;
    return SqlUtCheckSchema(dmlSchema, labelSchema);
}

bool SqlUtCheckScanOp(const IRExprT *irExpr, const DmVertexLabelT *label)
{
    if (irExpr->op->type != IR_LOGOP_SCAN || irExpr->op->arity != IR_OPARITY_ZERO || irExpr->children != nullptr) {
        return false;
    }
    OpLogicalAAScanT *scanOp = (OpLogicalAAScanT *)(void *)irExpr->op;
    if (!SqlUtCheckVertexLabel(scanOp->vertexLabel, label)) {
        return false;
    }
    AASchemaT *deleteSchema = &scanOp->schema;
    DmSchemaT *labelSchema = label->metaVertexLabel->schema;
    return SqlUtCheckSchema(deleteSchema, labelSchema);
}

bool SqlUtCheckSelectOp(const IRExprT *irExpr, const DmVertexLabelT *label)
{
    if (irExpr->op->type != IR_LOGOP_SELECT || irExpr->op->arity != IR_OPARITY_TWO) {
        return false;
    }
    for (uint32_t i = 0; i < irExpr->op->arity; ++i) {
        if (irExpr->children[i] == nullptr) {
            return false;
        }
    }

    OpLogicalAASelectT *selectOp = (OpLogicalAASelectT *)(void *)irExpr->op;
    AASchemaT *selectSchema = &selectOp->schema;
    DmSchemaT *labelSchema = label->metaVertexLabel->schema;
    return SqlUtCheckSchema(selectSchema, labelSchema);
}

static bool SqlUtExprTupleCmpFunc(ExprT *tuple1, ExprT *tuple2)
{
    if (tuple1->opType != EXPR_OP_TUPLE || tuple2->opType != EXPR_OP_TUPLE) {
        return false;
    }
    ExprArrayT *array1 = &CastToFunc(tuple1)->array;
    ExprArrayT *array2 = &CastToFunc(tuple2)->array;
    if (array1->num != array2->num) {
        return false;
    }

    for (uint32_t i = 0; i < array1->num; ++i) {
        ExprConstT *constVal1 = CastToConst(array1->expr[i]);
        ExprConstT *constVal2 = CastToConst(array2->expr[i]);
        if (!DmValueIsEqual(&constVal1->arg, &constVal2->arg)) {
            return false;
        }
    }
    return true;
}

bool SqlUtCheckBuildOp(const IRExprT *irExpr, const DmVertexLabelT *label, const DbListT *valueList)
{
    if (irExpr->op->type != IR_LOGOP_BUILD || irExpr->op->arity != IR_OPARITY_ZERO || irExpr->children != nullptr) {
        return false;
    }

    OpLogicalAABuildT *buildOp = (OpLogicalAABuildT *)(void *)irExpr->op;
    if (!SqlUtCheckVertexLabel(buildOp->label, label)) {
        return false;
    }
    AASchemaT *buildSchema = &buildOp->schema;
    DmSchemaT *labelSchema = label->metaVertexLabel->schema;
    if (!SqlUtCheckSchema(buildSchema, labelSchema)) {
        return false;
    }

    return DbIsListEqual(buildOp->exprList, valueList, (DbListItemCompareFunc)SqlUtExprTupleCmpFunc);
}

// 校验 prop binaryCmp constValue 场景, 形如: id = 1, age < 30 等
bool SqlUtCheckItemOpWithCmp(const IRExprT *irExpr, OpBinaryCodeE code, uint32_t propId, const DmValueT *value)
{
    if (irExpr->op->type != IR_ITEMOP_BINARY) {
        return false;
    }

    OpItemBinaryT *binaryOp = (OpItemBinaryT *)(void *)irExpr->op;
    if (binaryOp->code != code) {
        return false;
    }
    if (irExpr->children[0]->op->type != IR_ITEMOP_ATTR || irExpr->children[1]->op->type != IR_ITEMOP_CONSTVALUE) {
        return false;
    }

    OpItemAttrT *attrOp = (OpItemAttrT *)(void *)irExpr->children[0]->op;
    if (attrOp->propId != propId) {
        return false;
    }

    OpItemConstT *constOp = (OpItemConstT *)(void *)irExpr->children[1]->op;
    return DmValueIsEqual(&constOp->value, value);
}

static inline bool SqlUtCheckAttr(UtSqlItemAttrT attr, const OpItemAttrT *attrOp)
{
    if (attr.propId != attrOp->propId) {
        return false;
    }
    if (attr.type == UT_SQL_ATTR_OLD) {
        return attrOp->isTrigRelated && !attrOp->isNewSlot;
    } else if (attr.type == UT_SQL_ATTR_NEW) {
        return attrOp->isTrigRelated && attrOp->isNewSlot;
    }
    return true;
}

// 校验 prop binaryCmp prop 场景, 形如: id = age, age < scr 等
bool SqlUtCheckItemOpWithCmp(
    const IRExprT *irExpr, OpBinaryCodeE code, UtSqlItemAttrT leftAttr, UtSqlItemAttrT rightAttr)
{
    if (irExpr->op->type != IR_ITEMOP_BINARY) {
        return false;
    }

    OpItemBinaryT *binaryOp = (OpItemBinaryT *)(void *)irExpr->op;
    if (binaryOp->code != code) {
        return false;
    }
    if (irExpr->children[0]->op->type != IR_ITEMOP_ATTR || irExpr->children[1]->op->type != IR_ITEMOP_ATTR) {
        return false;
    }

    OpItemAttrT *attrOp1 = (OpItemAttrT *)(void *)irExpr->children[0]->op;
    if (!SqlUtCheckAttr(leftAttr, attrOp1)) {
        return false;
    }

    OpItemAttrT *attrOp2 = (OpItemAttrT *)(void *)irExpr->children[1]->op;
    if (!SqlUtCheckAttr(rightAttr, attrOp2)) {
        return false;
    }
    return true;
}

bool SqlUtCheckItemOpWithLogic(const IRExprT *irExpr, OpBinaryCodeE code, OpBinaryCodeE left, OpBinaryCodeE right)
{
    if (irExpr->op->type != IR_ITEMOP_BINARY) {
        return false;
    }
    if (irExpr->children[0]->op->type != IR_ITEMOP_BINARY || irExpr->children[1]->op->type != IR_ITEMOP_BINARY) {
        return false;
    }
    OpItemBinaryT *binaryOp = (OpItemBinaryT *)(void *)irExpr->op;
    OpItemBinaryT *leftOp = (OpItemBinaryT *)(void *)irExpr->children[0]->op;
    OpItemBinaryT *rightOp = (OpItemBinaryT *)(void *)irExpr->children[1]->op;
    return (binaryOp->code == code) && (leftOp->code == left) && (rightOp->code == right);
}

void InitBasicProperty(
    DbMemCtxT *memCtx, DmPropertySchemaT *property, uint32_t propeId, DbDataTypeE type, const char *name)
{
    char *str = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN(name));
    ASSERT_NE(nullptr, str);
    strcpy_s(str, DM_STR_LEN(name), name);

    *property = (DmPropertySchemaT){};
    property->isValid = true;
    property->dataType = type;
    property->isNullable = false;
    property->isFixed = true;
    property->isSysPrope = false;
    property->nameLen = DM_STR_LEN(str);
    property->name = (char *)str;
    property->propeId = propeId;
    property->size = type == DB_DATATYPE_STRING ? 65536u : DmGetBasicDataTypeLength(type);
    property->defaultValue = NULL;
}

void SqlSetGlobalSysTableAmNull(void)
{
    g_sysTableAm = {0};
}
