/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test for parser of explain in SQL
 * Author: QE Team
 * Create: 2024-03-12
 */

#include "gtest/gtest.h"
#include "ee_plan_node_ddl.h"
#include "cpl_sql_compiler.h"
#include "cpl_public_sql_parser_common.h"
#include "ut_emb_sql_common.h"

static DbMemCtxT *g_sqlBasicMem = NULL;

class UtEmbSqlParserExplain : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        g_sqlBasicMem = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memory context", &args);
        ASSERT_NE(nullptr, g_sqlBasicMem);
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(g_sqlBasicMem);
        BaseUninit();
    };
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

/*
 * TestCase for normal and abnormal explain create table stmt parser
 */
TEST_F(UtEmbSqlParserExplain, SqlExplainCreateTable)
{
    const char *normalInput[] = {
        "explain create table tableName(name text, age int);",
        "explain query plan create table tableName(name text, age int);",
        "explain create table if not exists tableName(name text, age int);",
        "explain create table tableName(name text primary key, age int);",
        "explain create table tableName(name text primary key asc, age int);",
        // 当前语法不支持
        /*
                "explain create table tableName(name text primary key asc on conflict abort, age int);",
                "explain create table tableName(name text not null on conflict rollback, age int);",
                "explain create table tableName(name text constraint consName unique on conflict ignore, age int);",
        */
        "explain create table tableName(name text primary key asc autoincrement, age int);",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_CREATE_TABLE_STMT);
    }

    const char *abnormalInput[] = {
        "explain explain create table tableName(name text, age int);",
        "explains create table tableName(name text, age int);",
        "explain query tree create table tableName(name text, age int);",
        "explain plan create table tableName(name text, age int);",
        "explain query create table tableName(name text, age int);",
        "query plan create table tableName(name text, age int);",
        "explain create table (name text, age int);",
        "explain create tables tableName(name, age int);",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain create index stmt parser
 */
TEST_F(UtEmbSqlParserExplain, SqlExplainCreateIndex)
{
    const char *normalInput[] = {
        "explain create index indexName on tableName(name);",
        "explain query plan create index indexName on tableName(name);",
        // 当前语法不支持
        /*
                "explain create index testDatabase.indexName on tableName(name) where someExpr;",
                "explain create unique index if not exists indexName on tableName(name collate RTRIM DESC, name collate
           RTRIM " "DESC);",
        */
        "explain create unique index if not exists indexName on tableName(name);",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_CREATE_INDEX_STMT);
    }

    const char *abnormalInput[] = {
        "explain create iBdex indexName on tableName(name);",
        // "explain create index indexName on tableName(name collate binary ASCCCC, name collate binary asc);",
        "explain create uniqueA index if not exists indexName on tableName(name);",
        "explain create index indexName on tableName();",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain create trigger stmt parser
 */
// 当前不支持trigger，后续回合特性后取消注释
TEST_F(UtEmbSqlParserExplain, DISABLED_SqlExplainCreateTrigger)
{
    const char *normalInput[] = {
        "explain create temp trigger if not exists sch_name.trigger_name after update of f1,f2 on table_name "
        " for each row when col==1 begin insert into t2(a,b,c) values(1,2,3); end;",
        "explain create trigger trigger_name update on table_name "
        " begin delete from t1 where id =1;update t1 set a=1 where id =1; end;",
        "explain create trigger trigger_name instead of delete on table_name "
        " begin insert into t2(a,b,c) values(1,2,3);update t1 set a=1 where id =1; end;",
        "explain create trigger trigger_name instead of delete on table_name "
        " begin insert into t2(a,b,c) values(1,2,3); end; create temp trigger trigger_name2 before insert on "
        "table_name2 begin update t1 set a=1 where id =1; end;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_CREATE_TRIGGER_STMT);
    }

    const char *abnormalInput[] = {
        "explain create trigger instead of delete on table_name "
        " begin insert into t2(a,b,c) values(1,2,3);update t1 set a=1 where id = 1; end;",
        "explain create trigger instead of select * from t1; end;",
        "explain create trigger trigger_name update on table_name begin ; end;",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain create view stmt parser
 */
// 当前不支持view，后续回合特性后取消注释
TEST_F(UtEmbSqlParserExplain, DISABLED_SqlExplainCreateView)
{
    const char *normalInput[] = {
        "explain create view testView as select * from test2;",
        "explain query plan create view if not exists testView as select * from test2;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_CREATE_VIEW_STMT);
    }

    const char *abnormalInput[] = {"explain create test.testView(age) as select * from test2;",
        "explain create view not exists test.testView(age) as select * from test2;",
        "explain create view test. as select * from test2;", "explain create view test.testView select * from test2;",
        "explain create view test.testView as * from test2;"};

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain alter table stmt parser
 */
// alter table 当前不支持
TEST_F(UtEmbSqlParserExplain, DISABLED_SqlExplainAlterTable)
{
    const char *normalInput[] = {
        "explain alter table test.testTable rename to testNew;",
        "explain query plan alter table test.testTable rename to testNew;",
        "explain alter table testTable rename column age to date;",
        "explain alter table testTable drop age;",
        "explain alter table testTable add age int;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_ALTER_TABLE_STMT);
    }

    const char *abnormalInput[] = {
        "explain alter table rename to testNew;",
        "explain alter table testTable drop;",
        "explain alter table testTable delete age;",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain drop stmt parser
 */
TEST_F(UtEmbSqlParserExplain, SqlExplainDrop)
{
    const char *normalInput[] = {
        "explain drop table t1;", "explain query plan drop table t1;", "explain drop table if exists database.t1;",
        "explain drop index database.t1;", "explain drop index if exists database.t1;",
        // 当前不支持trigger 和 view
        /*
                "explain drop trigger if exists sch_name.trigger_name;",
                "explain drop view test3;",
                "explain drop view if exists view_2;",
        */
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_DROP_STMT);
    }

    const char *abnormalInput[] = {
        "explain drop if exists database.t1;",
        "explain drop index exists database.t1;",
        "explain drop trigger;",
        "explain drop trigger if not exists sch_name.trigger_name;",
        "explain drop view;",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain select stmt parser
 */
TEST_F(UtEmbSqlParserExplain, SqlExplainSelect)
{
    const char *normalInput[] = {
        "explain select A as newA from db.t1 as X;",
        "explain select * from db.t1 as X;",
        "explain select * from t1 natural join t2;"
        "explain select * from t1, t2;"
        "explain select * from t1 cross join t2 as subQ using (A, B);",
        // 当前不支持该select用法和union
        /*
                "explain select 2 + 2 * 0.5;",
                "explain select -2 - 0.5;",
                "explain select a1,a2 from database.tb1 union select a1,a2 from database.tb2;",
        */
        "explain select 2 in (1,2);",
        "explain select 2 like 'abc';",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_SELECT_STMT);
    }

    const char *abnormalInput[] = {
        "explain select * newA from db.t1 as X;",
        "explain select * from;",
        "explain select * from t1 on 12345;",
        "explain select * from t1 using (id);",
        "explain select * from t1 inner outer join t2;",
        "explain select * from t1 inner left join t2;",
        "explain select * from t1 cross outer join t2;",
        "explain select * from (t1;",
        "explain select * from t1 join (select * as from t3) join t3",
        "explain select * from t1 union select * from t2 union ;",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain insert stmt parser
 */
TEST_F(UtEmbSqlParserExplain, SqlExplainInsert)
{
    const char *normalInput[] = {
        "explain insert into helloTable values (12345, 12345);",
        "explain query plan insert into helloTable values (12345, 12345);",
        "explain insert into database.helloTable as aliasTable(id, name) values (12345, 'Tommy'), (12345, 'Tommy');",
        "explain insert into helloTable default values;",
        // 当前语法不支持
        /*
                "explain insert into helloTable values(12345, 'JOY') on conflict do nothing;",
                "explain insert into helloTable values(12345, 'JOY') on conflict(id) do update set id = excluded.id +
           1;", "explain insert into helloTable values(12345, 'JOY') on conflict(id) where id = 12345 do nothing;",
        */
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_INSERT_STMT);
    }

    const char *abnormalInput[] = {
        "explain insert helloTable values (12345, 'Tommy');",
        "explain insert into values (12345, 'Tommy');",
        "explain insert into as aliasTable(id, name) values (12345, 'Tommy'), (12345, 'Tommy');",
        "explain insert into database.helloTable as aliasTable (id, name) values (12345, 'Tommy')(12345, 'Tommy');",
        "explain insert into helloTable default;",
        "explain insert into helloTable values (12345, 'Tommy') default values;",
        "explain insert into helloTable values(12345, 'JOY') conflict do nothing;",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain delete stmt parser
 */
TEST_F(UtEmbSqlParserExplain, SqlExplainDelete)
{
    const char *normalInput[] = {
        "explain delete from tbl;",
        "explain query plan delete from tbl;",
        "explain delete from tbl where col = 1 and col2 = 'constStr';",
        "explain delete from tbl indexed by idx;",
        "explain delete from tbl;delete from database.tbl as aliasTbl;",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_DELETE_STMT);
    }

    const char *abnormalInput[] = {
        "explain delete tbl;",
        "explain delete from tbl by idx;",
        "explain delete from tbl col = 1;",
        "explain delete from tbl where col = 1 col2 = 'constStr';",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}

/*
 * TestCase for normal and abnormal explain update stmt parser
 */
// 当前存在特性不支持，先DISABLED
TEST_F(UtEmbSqlParserExplain, DISABLED_SqlExplainUpdate)
{
    const char *normalInput[] = {
        "explain update or FAIL tbl set col = 'exprSet';",
        "explain query plan update or FAIL tbl set col = 'exprSet';",
        "explain update or abort tbl indexed by idx set col = 'exprSet';",
        "explain update tbl set col = 'exprSet', col2 = 'exprSet2';",
        "explain update tbl set (col, col2) = ('exprSet', 'exprSet2'), (col3, col4) = ('exprSet3', 'exprSet4');",
        "explain update tbl set col = 'exprSet' where col = 1 and col2 = 'constStr';",
    };
    for (uint32_t i = 0; i < sizeof(normalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, normalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_OK);
        SqlExplainStmtT *stmt = *(SqlExplainStmtT **)DbListItem(&parsedList.cmdList, 0);
        ASSERT_EQ(stmt->tag, T_SQL_EXPLAIN_STMT);
        ASSERT_EQ(stmt->parseTree->tag, T_SQL_UPDATE_STMT);
    }

    const char *abnormalInput[] = {
        // 当前错误码为GMERR_UNDEFINED_TABLE
        "explain update abort tbl set col = 'exprSet';",
        "explain update or abort tbl set col =;",
        "explain update tbl set col = 'exprSet' where col = 1 col2 = 'constStr';",
        "explain update tbl set (col, col2) = ('exprSet', 'exprSet2', (col3, col4) = ('exprSet3', 'exprSet4');",
    };

    for (uint32_t i = 0; i < sizeof(abnormalInput) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_sqlBasicMem, abnormalInput[i], &parsedList);
        ASSERT_EQ(ret, GMERR_SYNTAX_ERROR);
    }
}
