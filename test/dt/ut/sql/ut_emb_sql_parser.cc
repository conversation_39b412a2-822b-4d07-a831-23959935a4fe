/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test for parser of SQL
 * Author: QE Team
 * Create: 2024-01-30
 */

#include "gtest/gtest.h"
#include "common_init.h"
#include "ee_plan_node_ddl.h"
#include "cpl_sql_compiler.h"
#include "cpl_sql_parser.h"
#include "cpl_public_sql_parser_common.h"
#include "cpl_public_sql_keywords.h"
#include "ut_emb_sql_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

static DbMemCtxT *g_parserMemCtx = NULL;
static DbMemCtxT *g_oldMemCtx = NULL;

class UtEmbSqlParser : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        system("ipcrm -a");
        Status ret = BaseInit(false);
        ASSERT_EQ(ret, GMERR_OK);
        SqlSetGlobalSysTableAmNull();

        DbMemCtxArgsT args = {0};
        g_parserMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        g_oldMemCtx = DbMemCtxSwitchTo((DbMemCtxT *)g_parserMemCtx);
    }
    static void TearDownTestCase()
    {
        BaseUninit();
        system("ipcrm -a");
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

extern const SqlKeyWordInfoT g_sqlKeyWordInfo[];
extern const int32_t g_numSqlKeyWordInfo;

/* =========== Common Part for Update/Delete UT Begin =========== */
#define TEST_CASE_CMD_MAX 3

enum class TargetTableTypeE { TARGET_TABLE, TARGET_DB_DOT_TABLE, TARGET_TABLE_ALIAS, TARGET_DB_DOT_TABLE_ALIAS };

void CheckTableItems(TargetTableTypeE type, const SqlTableRefT *tblName)
{
    const char *tbl = "tbl";
    const char *db = "database";
    const char *alias = "aliasTbl";

    EXPECT_STRCASEEQ(tbl, tblName->tableName);
    switch (type) {
        case TargetTableTypeE::TARGET_TABLE:
            EXPECT_TRUE(tblName->database == NULL);
            EXPECT_TRUE(tblName->aliasName == NULL);
            break;
        case TargetTableTypeE::TARGET_DB_DOT_TABLE:
            EXPECT_STRCASEEQ(db, tblName->database);
            EXPECT_TRUE(tblName->aliasName == NULL);
            break;
        case TargetTableTypeE::TARGET_TABLE_ALIAS:
            EXPECT_TRUE(tblName->database == NULL);
            EXPECT_STRCASEEQ(alias, tblName->aliasName);
            break;
        case TargetTableTypeE::TARGET_DB_DOT_TABLE_ALIAS:
            EXPECT_STRCASEEQ(db, tblName->database);
            EXPECT_STRCASEEQ(alias, tblName->aliasName);
            break;
    }
}

enum class IndexedTypeE {
    INDEXED_BY,   // INDEXED BY
    INDEXED_NOT,  // NOT INDEXED
    INDEXED_BUTT  // without INDEXED token
};

void CheckIndexedItems(IndexedTypeE type, const SqlIndexByT *indexBy)
{
    const char *idx = "idx";
    switch (type) {
        case IndexedTypeE::INDEXED_BY:
            EXPECT_TRUE(indexBy->notIndexed == false);
            EXPECT_STRCASEEQ(idx, indexBy->name);
            break;
        case IndexedTypeE::INDEXED_NOT:
            EXPECT_TRUE(indexBy->notIndexed == true);
            EXPECT_TRUE(indexBy->name == NULL);
            break;
        case IndexedTypeE::INDEXED_BUTT:
            EXPECT_TRUE(indexBy == NULL);
            break;
    }
}

enum class ConExprTypeE { CONST_EXPR_INT, CONST_EXPR_STR, CONST_EXPR_BUTT };

void CheckConditionItemsDetail(const ConExprTypeE type, const SqlExprT *expr)
{
    const char *colList[] = {"col", "col2"};
    const int32_t val = 1;
    const char *strExpr = "constStr";

    ASSERT_EQ(SQL_EXPR_OP_EQ, expr->op->type);

    SqlExprColumnT *colExpr = (SqlExprColumnT *)expr->children[0]->op;
    ASSERT_EQ(SQL_EXPR_ITEM_COLUMN, colExpr->expr.type);
    SqlExprConstT *constExpr = (SqlExprConstT *)expr->children[1]->op;
    ASSERT_EQ(SQL_EXPR_ITEM_CONST, constExpr->expr.type);

    switch (type) {
        case ConExprTypeE::CONST_EXPR_INT: {
            EXPECT_STRCASEEQ(colList[0], colExpr->columnName);
            ASSERT_EQ(DB_DATATYPE_INT64, constExpr->arg.type);
            ASSERT_EQ(val, constExpr->arg.value.longValue);
            break;
        }
        case ConExprTypeE::CONST_EXPR_STR: {
            EXPECT_STRCASEEQ(colList[1], colExpr->columnName);
            ASSERT_EQ(DB_DATATYPE_STRING, constExpr->arg.type);
            EXPECT_STRCASEEQ(strExpr, (const char *)constExpr->arg.value.strAddr);
            break;
        }
        default: {
            printf("Invalid Param!\n");
            ASSERT_EQ(true, false);
        }
    }
}

void CheckConditionItems(uint32_t num, const SqlExprT *expr)
{
    if (num == 0) {
        return;
    }

    if (num == 1) {
        CheckConditionItemsDetail(ConExprTypeE::CONST_EXPR_INT, expr);
    }

    if (num == 2) {
        ASSERT_EQ(SQL_EXPR_OP_AND, expr->op->type);
        CheckConditionItemsDetail(ConExprTypeE::CONST_EXPR_INT, expr->children[0]);
        CheckConditionItemsDetail(ConExprTypeE::CONST_EXPR_STR, expr->children[1]);
    }
}

/* =========== Common Part for Update/Delete UT End =========== */

TEST_F(UtEmbSqlParser, SqlLookUpKeyWord)
{
    const SqlKeyWordInfoT *findResult = NULL;
    for (int i = 0; i < g_numSqlKeyWordInfo; i++) {
        findResult = GetSqlKeyWordInfo(g_sqlKeyWordInfo[i].name, g_sqlKeyWordInfo, g_numSqlKeyWordInfo);
        ASSERT_NE(nullptr, findResult);
        ASSERT_EQ(g_sqlKeyWordInfo[i].token, findResult->token);
    }

    findResult = GetSqlKeyWordInfo("CREATe", g_sqlKeyWordInfo, g_numSqlKeyWordInfo);
    ASSERT_NE(nullptr, findResult);

    findResult = GetSqlKeyWordInfo("ABC", g_sqlKeyWordInfo, g_numSqlKeyWordInfo);
    ASSERT_EQ(nullptr, findResult);
}

// for check one stmt
static NodeTagT g_expTag = T_SQL_STMT_BEGIN;
static const char *g_expTabName = NULL;
static const char *g_expDbName = NULL;
static bool g_expIfNotExists = 0;
static bool g_expIsTemp = 0;
// support check two column in one stmt
static uint32_t g_expColumnNum = 0;
static SqlColumnDefT g_expColumn1 = {0};
static SqlColumnDefT g_expColumn2 = {0};
// support check two cons in one column
static uint32_t g_expConsNum = 0;
static SqlConstraintT g_expCons1 = {CONSTR_NULL, NULL, {0}};
static SqlConstraintT g_expCons2 = {CONSTR_NULL, NULL, {0}};
static SqlConflictConstraintT g_expPkBaseCons1 = {NULL, 0, NULL};
static SqlConflictConstraintT g_expPkBaseCons2 = {NULL, 0, NULL};
static SqlConflictConstraintT g_expUniqueBaseCons1 = {NULL, 0, NULL};
static SqlConflictConstraintT g_expUniqueBaseCons2 = {NULL, 0, NULL};
static SqlConflictConstraintT g_expNotNullBaseCons1 = {NULL, 0, NULL};
static SqlConflictConstraintT g_expNotNullBaseCons2 = {NULL, 0, NULL};

void CreateTabCheckInit()
{
    g_expTag = T_SQL_STMT_BEGIN;
    g_expTabName = NULL;
    g_expDbName = NULL;
    g_expIfNotExists = 0;
    g_expIsTemp = 0;
    g_expColumnNum = 0;
    g_expConsNum = 0;
}

void CreateTabCheckCons(SqlColumnDefT *col)
{
    if (g_expConsNum == 0) {
        return;
    }
    uint32_t cnt = DbListGetItemCnt(col->constraints);
    ASSERT_EQ(cnt, g_expConsNum);
    for (uint32_t i = 0; i < g_expConsNum; i++) {
        SqlConstraintT *expCons = ((i == 0) ? &g_expCons1 : &g_expCons2);
        if (expCons->conType == CONSTR_NOTNULL) {
            SqlConflictConstraintT *baseCons = ((i == 0) ? &g_expNotNullBaseCons1 : &g_expNotNullBaseCons2);
            baseCons->onConflict = RESOLVE_NONE;
            expCons->notNullCons.base = baseCons;
        }
        if (expCons->conType == CONSTR_UNIQUE) {
            SqlConflictConstraintT *baseCons = ((i == 0) ? &g_expUniqueBaseCons1 : &g_expUniqueBaseCons2);
            expCons->uniCons.base = baseCons;
        }
        if (expCons->conType == CONSTR_PRIMARY) {
            SqlConflictConstraintT *baseCons = ((i == 0) ? &g_expPkBaseCons1 : &g_expPkBaseCons2);
            expCons->pkCons.base = baseCons;
        }
        SqlConstraintT *cons = *(SqlConstraintT **)DbListItem(col->constraints, i);
        ASSERT_NE(nullptr, cons);

        ASSERT_EQ(expCons->conType, cons->conType);
        if (expCons->conName) {
            ASSERT_EQ(strcmp(expCons->conName, cons->conName), 0);
        }
        switch (expCons->conType) {
            case CONSTR_PRIMARY: {
                ASSERT_EQ(expCons->pkCons.base->onConflict, cons->pkCons.base->onConflict);
                ASSERT_EQ(expCons->pkCons.sortOrder, cons->pkCons.sortOrder);
                ASSERT_EQ(expCons->pkCons.autoInc, cons->pkCons.autoInc);
                break;
            }
            case CONSTR_UNIQUE: {
                ASSERT_EQ(expCons->uniCons.base->onConflict, cons->uniCons.base->onConflict);
                break;
            }
            case CONSTR_NOTNULL: {
                ASSERT_EQ(expCons->notNullCons.base->onConflict, cons->notNullCons.base->onConflict);
                break;
            }
            case CONSTR_COLLATE: {
                ASSERT_EQ(strcmp(expCons->collateName, cons->collateName), 0);
                break;
            }
            default: {
                // to consider
                return;
            }
        }
    }
}

void CreateTableStmtCheck(SqlCreateTableStmtT *stmt)
{
    ASSERT_EQ(g_expTag, stmt->tag);
    ASSERT_EQ(g_expIsTemp, stmt->isTemp);
    ASSERT_EQ(g_expIfNotExists, stmt->ifNotExists);

    if (g_expDbName) {
        ASSERT_EQ(strcmp(g_expDbName, stmt->name->database), 0);
    }
    if (g_expTabName) {
        ASSERT_EQ(strcmp(g_expTabName, stmt->name->tableName), 0);
    }

    for (uint32_t i = 0; i < g_expColumnNum; i++) {
        SqlColumnDefT *expectCol = ((i == 0) ? &g_expColumn1 : &g_expColumn2);
        SqlColumnDefT *col = *(SqlColumnDefT **)DbListItem(stmt->columns, i);
        ASSERT_NE(nullptr, col);
        ASSERT_EQ(strcmp(expectCol->colName, col->colName), 0);
        ASSERT_EQ(strcmp(expectCol->typeName, col->typeName), 0);
        // only support check the first column's cons
        if (i == 0) {
            CreateTabCheckCons(col);
        }
    }
}

void CreateTabSetExists(bool exists)
{
    g_expIfNotExists = exists;
}

void CreateTabSetTemp(bool isTemp)
{
    g_expIsTemp = isTemp;
}

void CreateTabSetCheckName(const char *dbName, const char *tabName)
{
    g_expDbName = dbName;
    g_expTabName = tabName;
}

void CreateTabSetCheckConPK(char *conName, uint8_t sortOrder, uint8_t onConflict, bool autoInc, uint32_t index)
{
    SqlConstraintT *expectCol = ((index == 0) ? &g_expCons1 : &g_expCons2);
    SqlConflictConstraintT *base = ((index == 0) ? &g_expPkBaseCons1 : &g_expPkBaseCons2);
    expectCol->pkCons.base = base;
    expectCol->conType = CONSTR_PRIMARY;
    expectCol->conName = conName;
    expectCol->pkCons.sortOrder = sortOrder;
    expectCol->pkCons.base->onConflict = onConflict;

    expectCol->pkCons.autoInc = autoInc;
    // else means update
    if (g_expConsNum == index) {
        g_expConsNum++;
    }
}

void CreateTabSetCheckConUni(char *conName, uint8_t onConflict, uint32_t index)
{
    SqlConstraintT *expectCol = ((index == 0) ? &g_expCons1 : &g_expCons2);
    SqlConflictConstraintT *base = ((index == 0) ? &g_expUniqueBaseCons1 : &g_expUniqueBaseCons2);
    expectCol->uniCons.base = base;
    expectCol->conType = CONSTR_UNIQUE;
    expectCol->conName = conName;
    expectCol->uniCons.base->onConflict = onConflict;
    // else means update
    if (g_expConsNum == index) {
        g_expConsNum++;
    }
}

void CreateTabSetCheckConNull(char *conName, uint32_t index)
{
    SqlConstraintT *expectCol = ((index == 0) ? &g_expCons1 : &g_expCons2);
    expectCol->conType = CONSTR_NOTNULL;
    expectCol->conName = conName;
    // else means update
    if (g_expConsNum == index) {
        g_expConsNum++;
    }
}

void CreateTabSetCheckConColl(char *conName, char *collateName, uint32_t index)
{
    SqlConstraintT *expectCol = ((index == 0) ? &g_expCons1 : &g_expCons2);
    expectCol->conType = CONSTR_COLLATE;
    expectCol->conName = conName;
    expectCol->collateName = collateName;
    // else means update
    if (g_expConsNum == index) {
        g_expConsNum++;
    }
}

void CreateTabSetColumn(char *colName, char *typeName, uint32_t index)
{
    SqlColumnDefT *expectCol = ((index == 0) ? &g_expColumn1 : &g_expColumn2);
    expectCol->colName = colName;
    expectCol->typeName = typeName;
    g_expColumnNum++;
}

TEST_F(UtEmbSqlParser, SqlCreateTableBaseCase)
{
    const char *input = "create table tableName(name text, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();
    CreateTabSetCheckName(NULL, "TABLENAME");
    // can not assign a const string to char *
    char coLName1[10] = "name";
    char colType1[10] = "text";
    char coLName2[10] = "age";
    char colType2[10] = "int";
    CreateTabSetColumn(coLName1, colType1, 0);
    CreateTabSetColumn(coLName2, colType2, 1);

    SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, 0);
    CreateTableStmtCheck(stmt);
}

TEST_F(UtEmbSqlParser, SqlCreateTableTemp)
{
    const char *input = "create table tableName(name text, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();

    uint32_t cnt = DbListGetItemCnt(&parsedList.cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, i);
        if (i > 0) {
            CreateTabSetTemp(true);
        }
        CreateTableStmtCheck(stmt);
    }
}

TEST_F(UtEmbSqlParser, SqlCreateTableDbName)
{
    const char *input = "create table tableName(name text, age int);"
                        "create table dbname.tableName(name text, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();

    CreateTabSetCheckName(NULL, "TABLENAME");

    uint32_t cnt = DbListGetItemCnt(&parsedList.cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, i);
        if (i > 0) {
            CreateTabSetCheckName("dbname", "TABLENAME");
        }
        CreateTableStmtCheck(stmt);
    }
}

TEST_F(UtEmbSqlParser, SqlCreateTableIfNotExists)
{
    const char *input = "create table tableName(name text, age int);"
                        "create table if not exists tableName(name text, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();

    uint32_t cnt = DbListGetItemCnt(&parsedList.cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, i);
        if (i > 0) {
            CreateTabSetExists(true);
        }
        CreateTableStmtCheck(stmt);
    }
}

TEST_F(UtEmbSqlParser, SqlCreateTablePrimaryKey)
{
    const char *input = "create table tableName(name text primary key, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();
    char coLName1[10] = "name";
    char colType1[10] = "text";
    char coLName2[10] = "age";
    char colType2[10] = "int";
    CreateTabSetColumn(coLName1, colType1, 0);
    CreateTabSetColumn(coLName2, colType2, 1);

    CreateTabSetCheckConPK(NULL, SQL_SORT_ASC, RESOLVE_NONE, false, 0);

    uint32_t cnt = DbListGetItemCnt(&parsedList.cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, i);
        if (i == 1) {
            CreateTabSetCheckConPK(NULL, SQL_SORT_ASC, RESOLVE_NONE, false, 0);
        }
        if (i == 2) {
            CreateTabSetCheckConPK(NULL, SQL_SORT_ASC, RESOLVE_NONE, false, 0);
        }
        if (i == 3) {
            CreateTabSetCheckConPK(NULL, SQL_SORT_ASC, RESOLVE_NONE, true, 0);
        }
        CreateTableStmtCheck(stmt);
    }
}

TEST_F(UtEmbSqlParser, SqlCreateTableUnique)
{
    const char *input = "create table tableName(name text unique, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();
    char coLName1[10] = "name";
    char colType1[10] = "text";
    char coLName2[10] = "age";
    char colType2[10] = "int";
    CreateTabSetColumn(coLName1, colType1, 0);
    CreateTabSetColumn(coLName2, colType2, 1);

    CreateTabSetCheckConUni(NULL, RESOLVE_NONE, 0);

    uint32_t cnt = DbListGetItemCnt(&parsedList.cmdList);
    for (uint32_t i = 0; i < cnt; i++) {
        SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, i);
        if (i == 1) {
            char consName[10] = "consName";
            CreateTabSetCheckConUni(consName, RESOLVE_NONE, 0);
        }
        CreateTableStmtCheck(stmt);
    }
}

TEST_F(UtEmbSqlParser, SqlCreateTableNull)
{
    const char *input = "create table tableName(name text NOT NULL, age int);";
    SqlParsedListT parsedList;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedList);
    ASSERT_EQ(ret, GMERR_OK);

    CreateTabCheckInit();
    char coLName1[10] = "name";
    char colType1[10] = "text";
    char coLName2[10] = "age";
    char colType2[10] = "int";
    CreateTabSetColumn(coLName1, colType1, 0);
    CreateTabSetColumn(coLName2, colType2, 1);

    CreateTabSetCheckConNull(NULL, 0);
    SqlCreateTableStmtT *stmt = *(SqlCreateTableStmtT **)DbListItem(&parsedList.cmdList, 0);
    CreateTableStmtCheck(stmt);
}

/* TestCase for insert stmt Parser Begin */
/* TestCase design dimension :
 *   1. table : table / database.table / table as alias / database.table as alias
 *   2. column list : null column list / some columnRefs
 *   3. values : default values / select stmt / values-list
 *          for value-list:
 *           a.single row value-list
 *           b.multi-row value-list
 */
void CheckInsertTableSyntax(SqlTableRefT *table)
{
    ASSERT_NE(table, (void *)NULL);
    ASSERT_EQ(DbStrCmp(table->tableName, "helloTable", true), 0);
    if (table->aliasName != NULL) {
        ASSERT_EQ(strcmp(table->aliasName, "aliasTable"), 0);
    }
    if (table->database != NULL) {
        ASSERT_EQ(strcmp(table->database, "database"), 0);
    }
}

void CheckInsertSyntax(SqlParsedListT *parsedList, uint32_t numCmd, uint32_t numValues)
{
    ASSERT_EQ(DbListGetItemCnt(&parsedList->cmdList), numCmd);
    for (uint32_t i = 0; i < numCmd; i++) {
        SqlInsertStmtT *insertStmt = *(SqlInsertStmt **)DbListItem(&parsedList->cmdList, 0);
        ASSERT_EQ(insertStmt->tag, T_SQL_INSERT_STMT);
        ASSERT_EQ(insertStmt->upsertClause, (void *)NULL);
        ASSERT_EQ(insertStmt->returningClause, (void *)NULL);
        ASSERT_EQ(insertStmt->withClause, (void *)NULL);
        CheckInsertTableSyntax(insertStmt->table);
        ASSERT_EQ(insertStmt->conflictStrategy, RESOLVE_NONE);

        uint32_t columnCnt = 2;
        const char *columnNames[2] = {"id", "name"};
        DbListT *columnList = insertStmt->columnList;
        if (columnList != NULL) {
            ASSERT_EQ(DbListGetItemCnt(columnList), columnCnt);
            for (uint32_t i = 0; i < columnCnt; i++) {
                char *col = *(char **)DbListItem(columnList, i);
                ASSERT_EQ(strcmp(col, columnNames[i]), 0);
            }
        }

        if (insertStmt->value->type == DEFAULT_VALUES) {
            ASSERT_EQ(insertStmt->value->type, DEFAULT_VALUES);
            ASSERT_EQ(insertStmt->value->valuesList, (void *)NULL);
            ASSERT_EQ(insertStmt->value->selectSubquery, (void *)NULL);
        } else if (insertStmt->value->type == SELECT_SUBQUERY) {
            ASSERT_EQ(insertStmt->value->type, SELECT_SUBQUERY);
            ASSERT_EQ(insertStmt->value->selectSubquery->tag, T_SQL_SELECT_STMT);
        } else {
            ASSERT_EQ(insertStmt->value->type, VALUES_LIST);
            ASSERT_NE(insertStmt->value->valuesList, (void *)NULL);
            uint32_t listsCnt = DbListGetItemCnt(insertStmt->value->valuesList);
            ASSERT_EQ(listsCnt, numValues);
            for (uint32_t i = 0; i < listsCnt; ++i) {
                DbListT *valueList = *(DbListT **)DbListItem(insertStmt->value->valuesList, i);
                ASSERT_EQ(DbListGetItemCnt(valueList), columnCnt);
                for (uint32_t j = 0; j < columnCnt; ++j) {
                    SqlExprT *expr = *(SqlExprT **)DbListItem(valueList, j);
                    ASSERT_EQ(expr->op->type, SQL_EXPR_ITEM_CONST);
                }
            }
        }
    }
}

void CheckUpsertSyntax(SqlParsedListT *parsedList, uint32_t numConflictTarget, uint32_t numSetClause, bool isDoUpdate)
{
    SqlInsertStmtT *insertStmt = *(SqlInsertStmt **)DbListItem(&parsedList->cmdList, 0);
    ASSERT_EQ(insertStmt->tag, T_SQL_INSERT_STMT);
    SqlUpsertClauseT *upsertClause = insertStmt->upsertClause;

    /* check conflict target */
    const char *columnNames[2] = {"id", "name"};
    DbListT *columnList = insertStmt->upsertClause->columnList;
    if (columnList != NULL) {
        ASSERT_EQ(DbListGetItemCnt(columnList), numConflictTarget);
        for (uint32_t i = 0; i < numConflictTarget; i++) {
            char *col = *(char **)DbListItem(columnList, i);
            ASSERT_EQ(strcmp(col, columnNames[i]), 0);
        }
    }

    /* check upsert is do update */
    ASSERT_EQ(upsertClause->upsertOptInfo->isDoUpdate, isDoUpdate);

    /* check set clause */
    if (upsertClause->upsertOptInfo->setClause == NULL || !isDoUpdate) {
        return;
    }
    const char *colName = "id";
    const char *tableName = "excluded";
    SqlSetExprRefT *setItem = *(SqlSetExprRefT **)DbListItem(upsertClause->upsertOptInfo->setClause, 0);
    ASSERT_EQ(setItem->isSetList, false);
    EXPECT_STRCASEEQ(setItem->columnName, colName);

    SqlExprT *exprItem = setItem->expr;
    ASSERT_EQ(exprItem->op->type, SQL_EXPR_OP_ADD);
    SqlExprColumnT *left = (SqlExprColumnT *)exprItem->children[0]->op;
    SqlExprConstT *right = (SqlExprConstT *)exprItem->children[1]->op;
    ASSERT_EQ(left->expr.type, SQL_EXPR_ITEM_COLUMN);
    EXPECT_STRCASEEQ(left->columnName, colName);
    EXPECT_STRCASEEQ(left->tableName, tableName);

    ASSERT_EQ(right->expr.type, SQL_EXPR_ITEM_CONST);
    ASSERT_EQ(right->arg.value.uintValue, (uint32_t)1);
}

/* =========== normal scenarios begin =========== */
/* Testcase1: table + null column list +  single row value-list */
/* Testcase2: database.table + some columnRefs +  single row value-list */
/* Testcase3: table as alias + some columnRefs +  multi-row value-list */
/* Testcase4: database.table as alias + some columnRefs +  multi-row value-list */
/* Testcase5: table + null column list +  default values */
/* Testcase6: table + some columnRefs +   select stmt */
/* Testcase7: multi Statement */
/* Testcase8: Uppercase and lowercase Statement */
/* =========== normal scenarios end =========== */

typedef struct {
    const char *sql;
    uint32_t numCmd;
    uint32_t numValues;
} InsertTestCaseT;

typedef struct {
    const char *sql;
    uint32_t numConflictTarget;
    uint32_t numSetClause;
    bool isDoUpdate;
} UpsertTestCaseT;

TEST_F(UtEmbSqlParser, SqlInsertNormal)
{
    const InsertTestCaseT input[] = {{"INSERT INTO helloTable VALUES (12345, 12345);", 1, 1},
        {"INSERT INTO database.helloTable(id, name) VALUES (12345, 'Tommy');", 1, 1},
        {"INSERT INTO helloTable as aliasTable(id, name) VALUES (12345, 'Tommy'), (12345, 'Tommy');", 1, 2},
        {"INSERT INTO database.helloTable as aliasTable(id, name) VALUES (12345, 'Tommy'), (12345, 'Tommy');", 1, 2},
        {"INSERT INTO helloTable DEFAULT VALUES;", 1, 2},
        {"INSERT INTO helloTable as aliasTable(id, name) VALUES (12345, 'Tommy'), (12345, 'Tommy');"
         "INSERT INTO database.helloTable as aliasTable(id, name) VALUES (12345, 'Tommy'), (12345, 'Tommy');",
            2, 2},
        {"InSeRT into database.helloTable as aliasTable(id, name) VAlUES (12345, 'Tommy'), (12345, 'Tommy');", 1, 2}};

    for (uint32_t i = 0; i < sizeof(input) / sizeof(InsertTestCaseT); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        CheckInsertSyntax(&parsedList, input[i].numCmd, input[i].numValues);
    }
}

/* abnormal scenarios */
/* : INSERT not followed by INTO */
/* : no table  */
/* : table format error */
/* : multi columns format exception: no '(' ',' ')' */
/* : multi value list not separated with ',' */
/* : DEFAULT not followed by VALUES */
/* : '(' not together with ') */
/* : no insert */
/* : no ; for multi sql */
/* : not complete sql  */
/* : values together with default values */
TEST_F(UtEmbSqlParser, SqlInsertSyntaxErr)
{
    const char *inputSql[] = {
        "INSERT helloTable VALUES (12345, 'Tommy');",
        "INSERT INTO VALUES (12345, 'Tommy');",
        "INSERT INTO as aliasTable(id, name) VALUES (12345, 'Tommy'), (12345, 'Tommy');",
        "INSERT INTO database.helloTable as aliasTable (id, name) VALUES (12345, 'Tommy')(12345, 'Tommy');",
        "INSERT INTO helloTable DEFAULT;",
        "INSERT INTO helloTable (id, name select * from tableTest;",
        "INTO helloTable DEFAULT VALUES;",
        "INSERT INTO helloTable DEFAULT VALUES INSERT INTO hellTable DEFAULT VALUES;",
        "INSERT INTO helloTable DEFAULT; VALUES INSERT INTO hellTable DEFAULT VALUES;",
        "INSERT INTO helloTable VALUES (12345, 'Tommy') DEFAULT VALUES;",
    };

    for (uint32_t i = 0; i < sizeof(inputSql) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, inputSql[i], &parsedList);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}

/*
 * TestCase for normal upsert stmt parser begin
 * Testcase1: without conflict target + DO-NOTHING;
 * Testcase2: without conflict target + where-clause + DO-NOTHING;
 * Testcase3: without conflict target + multiple-where-clause + DO-NOTHING;
 * Testcase4: one conflict target + DO-UPDATE + one set-clause;
 * Testcase5: multiple conflict target + DO-UPDATE + one set-clause;
 * Testcase6: one conflict target + DO-UPDATE + one set-clause(use excluded flag) + where-clause;
 * TestCase for normal upsert stmt parser end
 */
TEST_F(UtEmbSqlParser, SqlUpsertNormal)
{
    const UpsertTestCaseT input[] = {
        {"INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT DO NOTHING;", 0, 0, false},
        {"INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT(id) WHERE id = 12345 DO NOTHING;", 1, 0, false},
        {"INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT(id, name) "
         "WHERE id = 12345 AND name = 'JOY' DO NOTHING;",
            2, 0, false},
        {"INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT(id) DO UPDATE SET id = excluded.id + 1;", 1, 1, true},
        {"INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT(id, name) DO UPDATE "
         "SET id = excluded.id + 1;",
            2, 1, true},
        {"INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT(id) DO UPDATE "
         "SET id = excluded.id + 1 WHERE id = 12345;",
            1, 1, true},
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(UpsertTestCaseT); ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedlist);
        ASSERT_EQ(GMERR_OK, ret);
        CheckUpsertSyntax(&parsedlist, input[i].numConflictTarget, input[i].numSetClause, input[i].isDoUpdate);
    }
}

/*
 * TestCase for abnormal upsert stmt parser begin
 * Testcase1: without keyword "ON"
 * Testcase2: without keyword "CONFLICT"
 * Testcase3: without keyword "DO"
 * Testcase4: without operation
 * Testcase5: UPDATE without keyword "SET"
 * Testcase6: UPDATE without set-clause
 * Testcase7: with an error set-clause
 * TestCase for abnormal upsert stmt parser end
 */
TEST_F(UtEmbSqlParser, SqlUpsertAbnormal)
{
    const char *input[] = {
        "INSERT INTO helloTable VALUES(12345, 'JOY') CONFLICT DO NOTHING;",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON WHERE id = 12345 DO NOTHING;",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT WHERE id = 12345 AND name = 'JOY' NOTHING;",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT DO SET name = 'JOY';",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT DO;",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT DO UPDATE id = 12345, name = 'JOY';",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT DO UPDATE SET;",
        "INSERT INTO helloTable VALUES(12345, 'JOY') ON CONFLICT DO UPDATE SET id helloTable.id + 1;",
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedlist);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
/* TestCase for insert stmt Parser End */

/* TestCase for delete stmt Parser Begin */
/* =========== normal scenarios begin =========== */
/* Testcase design dimension:
 *  0.statement: single / multi
 *  1.table: table / database.table / table as alias / database.table as alias
 *  2.delete condition: non-condition / single-condition / multi-conditions
 *  3.index: indexed by / not indexed / null
 */
typedef struct DeleteTestCase {
    const char *sql;
    uint32_t cmdNum;
    TargetTableTypeE tableType[TEST_CASE_CMD_MAX];
    IndexedTypeE indexedType[TEST_CASE_CMD_MAX];
    uint32_t conditionNum[TEST_CASE_CMD_MAX];
} DeleteTestCaseT;

void CheckDeleteStmtSyntax(SqlParsedListT *parsedList, const DeleteTestCaseT *tCase)
{
    ASSERT_EQ(tCase->cmdNum, DbListGetItemCnt(&parsedList->cmdList));
    for (uint32_t i = 0; i < tCase->cmdNum; ++i) {
        SqlDeleteStmtT *stmt = *(SqlDeleteStmtT **)DbListItem(&parsedList->cmdList, i);
        ASSERT_EQ(T_SQL_DELETE_STMT, stmt->tag);
        CheckTableItems(tCase->tableType[i], stmt->tblName);
        CheckIndexedItems(tCase->indexedType[i], stmt->indexBy);
        CheckConditionItems(tCase->conditionNum[i], stmt->whereClause);
    }
}

TEST_F(UtEmbSqlParser, SqlDeleteNormal)
{
    const DeleteTestCaseT input[] = {
        {"DELETE FROM tbl;", 1, {TargetTableTypeE::TARGET_TABLE}, {IndexedTypeE::INDEXED_BUTT}, {0}},
        {"DELETE FROM tbl WHERE col = 1;", 1, {TargetTableTypeE::TARGET_TABLE}, {IndexedTypeE::INDEXED_BUTT}, {1}},
        {"DELETE FROM tbl WHERE col = 1 AND col2 = 'constStr';", 1, {TargetTableTypeE::TARGET_TABLE},
            {IndexedTypeE::INDEXED_BUTT}, {2}},
        {"DELETE FROM database.tbl;", 1, {TargetTableTypeE::TARGET_DB_DOT_TABLE}, {IndexedTypeE::INDEXED_BUTT}, {0}},
        {"DELETE FROM tbl AS aliasTbl;", 1, {TargetTableTypeE::TARGET_TABLE_ALIAS}, {IndexedTypeE::INDEXED_BUTT}, {0}},
        {"DELETE FROM database.tbl AS aliasTbl;", 1, {TargetTableTypeE::TARGET_DB_DOT_TABLE_ALIAS},
            {IndexedTypeE::INDEXED_BUTT}, {0}},
        {"DELETE FROM tbl;DELETE FROM database.tbl AS aliasTbl;", 2,
            {TargetTableTypeE::TARGET_TABLE, TargetTableTypeE::TARGET_DB_DOT_TABLE_ALIAS},
            {IndexedTypeE::INDEXED_BUTT, IndexedTypeE::INDEXED_BUTT}, {0, 0}}};

    for (uint32_t i = 0; i < sizeof(input) / sizeof(DeleteTestCaseT); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        CheckDeleteStmtSyntax(&parsedList, &input[i]);
    }
}
/* =========== normal scenarios end =========== */

/* =========== abnormal scenarios begin =========== */
/*
 *  1.Missing "FROM"
 *  2.Missing table
 *  3.Missing "AS"
 *  4.Missing "WHERE"
 *  5.Missing "INDEXED" or "BY" or "NOT"
 *  6.Missing ";"
 */
TEST_F(UtEmbSqlParser, SqlDeleteabnormal)
{
    const char *input[] = {"DELETE tbl;", "DELETE FROM;", "DELETE FROM tbl BY idx;", "DELETE FROM tbl INDEXED idx;",
        "DELETE FROM tbl NOT;", "DELETE FROM tbl AS;", "DELETE FROM tbl DELETE FROM tbl AS aliasTbl;",
        "DELETE FROM tbl col = 1;", "DELETE FROM tbl WHERE col = 1 col2 = 'constStr';"};

    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedList);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
/* =========== abnormal scenarios end =========== */
/* TestCase for delete stmt Parser End */

/* TestCase for create index stmt Parser Begin */
/* Testcase design dimension :
 *   1. if has unique
 *   2. if not exist
 *   3. index name format
 *   4. if has where expr
 *   5. single/multi columns
 *   6. collate
 *   7. ASC.DESC
 */

typedef struct {
    const char *sql;
    uint32_t numCmd;
    bool unique;
    bool ifNotExist;
    bool isWithDatabase;
    const char *collate;
    uint8_t sortType;
    uint32_t numIndexCols;
    bool hasWhereExpr;
} CreateIndexTestCaseT;

void CheckCreateIndexSyntax(SqlParsedListT *parsedList, const CreateIndexTestCaseT *testcase)
{
    ASSERT_EQ(DbListGetItemCnt(&parsedList->cmdList), testcase->numCmd);
    for (uint32_t i = 0; i < testcase->numCmd; ++i) {
        SqlCreateIndexStmtT *createIndexStmt = *(SqlCreateIndexStmtT **)DbListItem(&parsedList->cmdList, i);
        ASSERT_EQ(createIndexStmt->isUnique, testcase->unique);
        ASSERT_EQ(createIndexStmt->ifNotExists, testcase->ifNotExist);
        if (testcase->isWithDatabase) {
            ASSERT_EQ(strcmp("testDatabase", createIndexStmt->indexInfo->database), 0);
        } else {
            ASSERT_EQ(createIndexStmt->indexInfo->database, (char *)NULL);
        }
        ASSERT_EQ(strcmp("indexName", createIndexStmt->indexInfo->indexName), 0);
        ASSERT_EQ(DbStrCmp("tableName", createIndexStmt->indexInfo->tableName, true), 0);
        ASSERT_EQ(DbListGetItemCnt(createIndexStmt->indexColList), testcase->numIndexCols);
        // check index-column
        for (uint32_t j = 0; j < testcase->numIndexCols; ++j) {
            SqlIndexColumnT *indexColumn = *(SqlIndexColumnT **)DbListItem(createIndexStmt->indexColList, j);
            if (testcase->collate != NULL) {
                ASSERT_EQ(strcmp(testcase->collate, indexColumn->collate), 0);
            } else {
                ASSERT_EQ(indexColumn->collate, (char *)NULL);
            }
            ASSERT_EQ(indexColumn->sortType, testcase->sortType);
            ASSERT_EQ(indexColumn->columnExpr->op->type, SQL_EXPR_ITEM_COLUMN);
        }
        if (testcase->hasWhereExpr) {
            ASSERT_EQ(createIndexStmt->whereClause->op->type, SQL_EXPR_ITEM_COLUMN);
        } else {
            ASSERT_EQ(createIndexStmt->whereClause, (SqlExprT *)NULL);
        }
    }
}

/* =========== normal scenarios begin =========== */
/* Testcase1:  a simple create index stmt */
/* Testcase2:  has unique + if_not_exist + simple index-name format */
/* Testcase3:  database.indexName format + single index column + where 1 */
/* Testcase4:  multi index columns + collate(BINARY) + ASC */
/* Testcase5:  unique + if_not_exist + database.indexName + multi-columns + DESC + collate(RTRIM) + DESC */
/* Testcase6:  multi create index stmt */
/* =========== normal scenarios end =========== */

TEST_F(UtEmbSqlParser, SqlCreateIndexNormal)
{
    const CreateIndexTestCaseT input[] = {
        {"create index indexName on tableName(name);", 1, false, false, false, NULL, SQL_SORT_ASC, 1, false},
        {"create unique index if not exists indexName on tableName(name);", 1, true, true, false, NULL, SQL_SORT_ASC, 1,
            false},
        {"create index testDatabase.indexName on tableName(name);", 1, false, false, true, NULL, SQL_SORT_ASC, 1,
            false},
        {"create index indexName on tableName(name);create index indexName on tableName(name);", 2, false, false, false,
            NULL, SQL_SORT_ASC, 1, false}};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(CreateIndexTestCaseT); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        CheckCreateIndexSyntax(&parsedList, &(input[i]));
    }
}

/* abnormal scenarios */
/* : wrong keywords */
/* : unknown sort type */
/* : missing some keywords */
TEST_F(UtEmbSqlParser, SqlCreateIndexSyntaxErr)
{
    const char *inputSql[] = {
        "create iBdex indexName on tableName(name);",
        "create uniqueA index if not exists indexName on tableName(name);",
        "create index testDatabase.indexName onM tableName(name) where someExpr;",
        "create indexName om tableName(name);",
        "create index testDatabase.indexName on tableName(name) someExpr;",
        "create index indexName on tableName();create index indexName on tableName(name);",
    };

    for (uint32_t i = 0; i < sizeof(inputSql) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, inputSql[i], &parsedList);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
/* TestCase for create index stmt Parser End */

/* TestCase for update stmt Parser Begin */
/* =========== normal scenarios begin =========== */
/* Testcase design dimension:
 *  0.statement: single / multi
 *  1.or-conflicts: not specified / specified
 *  2.table: table / database.table / table as alias / database.table as alias
 *  3.index: indexed by / not indexed / null
 *  4.sets: single-set / multi-sets
 *              single-column / multi-columns
 *  5.from clause: table / select-stmt / join-clause
 *  6.update condition: non-condition / single-condition / multi-conditions
 *  7.others: update-from clause restrictions on target table
 */

#define UPDATE_COLUMN_SET_NUM_MAX 3
#define UPDATE_COLUMN_SET_NUM_TWO 2

typedef struct UpdateTestCase {
    const char *sql;
    uint32_t cmdNum;
    SqlResolveTypeE resolveType[TEST_CASE_CMD_MAX];
    TargetTableTypeE tableType[TEST_CASE_CMD_MAX];
    IndexedTypeE indexedType[TEST_CASE_CMD_MAX];
    uint32_t setNum[TEST_CASE_CMD_MAX];
    uint32_t setPairs[TEST_CASE_CMD_MAX][UPDATE_COLUMN_SET_NUM_MAX];
    uint32_t conditionNum[TEST_CASE_CMD_MAX];
} UpdateTestCaseT;

static void CheckResolveItems(SqlResolveTypeE type, SqlResolveTypeE actType)
{
    ASSERT_EQ(type, actType);
}

static void CheckSetItemsForColumn(uint32_t id, const DbListT *setClause)
{
    const char *colList[] = {"col", "col2"};
    const char *exprList[] = {"exprSet", "exprSet2"};

    SqlSetExprRefT *item = *(SqlSetExprRefT **)DbListItem(setClause, id);

    ASSERT_EQ(false, item->isSetList);
    EXPECT_STRCASEEQ(colList[id], item->columnName);

    SqlExprConstT *exprItem = (SqlExprConstT *)item->expr->op;
    ASSERT_EQ(SQL_EXPR_ITEM_CONST, exprItem->expr.type);
    ASSERT_EQ(DB_DATATYPE_STRING, exprItem->arg.type);
    EXPECT_STRCASEEQ(exprList[id], (const char *)exprItem->arg.value.strAddr);
}

static void CheckSetItemsForColumnList(uint32_t id, const DbListT *setClause)
{
    const char *colList[] = {"col", "col2", "col3", "col4"};
    const char *exprList[] = {"exprSet", "exprSet2", "exprSet3", "exprSet4"};

    SqlSetExprRefT *item = *(SqlSetExprRefT **)DbListItem(setClause, id);

    ASSERT_EQ(true, item->isSetList);
    uint32_t colNum = DbListGetItemCnt(item->columnList);
    ASSERT_EQ((uint32_t)UPDATE_COLUMN_SET_NUM_TWO, colNum);
    for (uint32_t i = 0; i < colNum; ++i) {
        char *colName = *(char **)DbListItem(item->columnList, i);
        EXPECT_STRCASEEQ(colList[2 * id + i], colName);
    }

    SqlExprListT *exprItem = (SqlExprListT *)item->expr->op;
    ASSERT_EQ(SQL_EXPR_ITEM_LIST, exprItem->expr.type);
    uint32_t exprNum = DbListGetItemCnt(exprItem->exprList);
    ASSERT_EQ((uint32_t)UPDATE_COLUMN_SET_NUM_TWO, exprNum);
    for (uint32_t i = 0; i < exprNum; ++i) {
        SqlExprConstT *exprItemTmp = (SqlExprConstT *)(void *)(*(SqlExprT **)DbListItem(exprItem->exprList, i))->op;
        ASSERT_EQ(SQL_EXPR_ITEM_CONST, exprItemTmp->expr.type);
        ASSERT_EQ(DB_DATATYPE_STRING, exprItemTmp->arg.type);
        EXPECT_STRCASEEQ(exprList[2 * id + i], (const char *)exprItemTmp->arg.value.strAddr);
    }
}

static void CheckSetItems(const uint32_t exprSetNum, const uint32_t exprSetPairs[], const DbListT *setClause)
{
    EXPECT_TRUE(setClause != NULL);
    uint32_t actSetNum = DbListGetItemCnt(setClause);
    ASSERT_EQ(exprSetNum, actSetNum);

    for (uint32_t i = 0; i < actSetNum; ++i) {
        if (exprSetPairs[i] == 1) {
            CheckSetItemsForColumn(i, setClause);
        } else if (exprSetPairs[i] == 2) {
            CheckSetItemsForColumnList(i, setClause);
        }
    }
}

static void CheckUpdateStmtSyntax(SqlParsedListT *parsedList, const UpdateTestCaseT *tCase)
{
    ASSERT_EQ(tCase->cmdNum, DbListGetItemCnt(&parsedList->cmdList));
    for (uint32_t i = 0; i < tCase->cmdNum; ++i) {
        SqlUpdateStmtT *stmt = *(SqlUpdateStmtT **)DbListItem(&parsedList->cmdList, i);
        ASSERT_EQ(T_SQL_UPDATE_STMT, stmt->tag);
        CheckResolveItems(tCase->resolveType[i], stmt->conflictStrategy);
        CheckTableItems(tCase->tableType[i], stmt->tblName);
        CheckIndexedItems(tCase->indexedType[i], stmt->indexBy);
        CheckSetItems(tCase->setNum[i], tCase->setPairs[i], stmt->setClause);
        CheckConditionItems(tCase->conditionNum[i], stmt->whereClause);
    }
}

TEST_F(UtEmbSqlParser, SqlUpdateNormal)
{
    const UpdateTestCaseT input[] = {
        {"UPDATE tbl SET col = 'exprSet', col2 = 'exprSet2';", 1, {RESOLVE_NONE}, {TargetTableTypeE::TARGET_TABLE},
            {IndexedTypeE::INDEXED_BUTT}, {2}, {{1, 1}}, {0}},
        {"UPDATE tbl SET col = 'exprSet', col2 = 'exprSet2'  WHERE col = 1 AND col2 = 'constStr';", 1, {RESOLVE_NONE},
            {TargetTableTypeE::TARGET_TABLE}, {IndexedTypeE::INDEXED_BUTT}, {2}, {{1, 1}}, {0}},
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(UpdateTestCaseT); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        CheckUpdateStmtSyntax(&parsedList, &input[i]);
    }
}
/* =========== normal scenarios end =========== */
/* =========== abnormal scenarios begin =========== */
/*
 *  1.Missing keyword: "UPDATE", "OR", "INDEXED", "BY", "NOT", "SET", "WHERE" or conflict keyword
 *  2.Missing param: table, column, index or expr
 *  3.Missing symbol: "=", "(", ")", ",", ";"
 */
TEST_F(UtEmbSqlParser, SqlUpdateAbnormal)
{
    const char *input[] = {"OR ABORT tbl SET col = 'exprSet';", "UPDATE OR tbl SET col = 'exprSet';",
        "UPDATE OR ABORT SET col = 'exprSet';", "UPDATE OR ABORT tbl col = 'exprSet';",
        "UPDATE OR ABORT tbl SET = 'exprSet';", "UPDATE OR ABORT tbl SET col =;",
        "UPDATE OR ABORT tbl SET col = 'exprSet' col2 = 'exprSet2';",
        "UPDATE tbl SET col, col2) = ('exprSet', 'exprSet2'), (col3, col4) = ('exprSet3', 'exprSet4');",
        "UPDATE tbl SET (col, col2) = ('exprSet', 'exprSet2', (col3, col4) = ('exprSet3', 'exprSet4');",
        "UPDATE tbl SET col = 'exprSet' UPDATE tbl SET col = 'exprSet';",
        "UPDATE tbl SET col = 'exprSet' WHERE col = 1 col2 = 'constStr';"};

    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedList);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
/* =========== abnormal scenarios end =========== */
/* TestCase for update stmt Parser End */

/* TestCase for drop stmt Parser Begin */
/* Testcase design dimension:
 *  1. ifExists:    true/ false
 *  2. table(index):   table(index) / database.table(index)
 *  3. DropTypeT:   0/1
 */

/* =========== normal scenarios begin =========== */
/* Testcase1:  table(index) */
/* Testcase2:  database.table(index) */
/* Testcase3: ifexists + table(index) */
/* Testcase4: ifexists + database.table(index) */
#define DROP_CASE_CMD_MAX 3
typedef struct DropTestCase {
    const char *sql;
    uint32_t cmdNum;
    bool withIfExists[DROP_CASE_CMD_MAX];
    bool withDatabase[DROP_CASE_CMD_MAX];
    int dropType[DROP_CASE_CMD_MAX];
} DropTestCaseT;

void CheckDropTableItems(const SqlDropStmtT *stmt, bool withIfExists, bool withDatabase, int dropType)
{
    const char *tableName = "t1";
    const char *dbName = "database";

    EXPECT_STRCASEEQ((stmt->name)->name, tableName);
    EXPECT_TRUE(stmt->dropElemType == dropType);
    if (withIfExists) {
        EXPECT_TRUE(stmt->ifExists == true);
    } else {
        EXPECT_TRUE(stmt->ifExists == false);
    }
    if (withDatabase) {
        EXPECT_STRCASEEQ((stmt->name)->database, dbName);
    } else {
        EXPECT_TRUE((stmt->name)->database == NULL);
    }
}

void CheckDropIndexItems(const SqlDropStmtT *stmt, bool withIfExists, bool withDatabase, int dropType)
{
    const char *indexName = "idx1";
    const char *tableName = "t1";
    const char *dbName = "database";

    EXPECT_STRCASEEQ((stmt->name)->name, tableName);
    EXPECT_STRCASEEQ((stmt->name)->indexName, indexName);
    EXPECT_TRUE(stmt->dropElemType == dropType);
    if (withIfExists) {
        EXPECT_TRUE(stmt->ifExists == true);
    } else {
        EXPECT_TRUE(stmt->ifExists == false);
    }
    if (withDatabase) {
        EXPECT_STRCASEEQ((stmt->name)->database, dbName);
    } else {
        EXPECT_TRUE((stmt->name)->database == NULL);
    }
}

void CheckDropStmtSyntax(SqlParsedListT *parsedList, const DropTestCaseT *tCase)
{
    ASSERT_EQ(DbListGetItemCnt(&parsedList->cmdList), tCase->cmdNum);
    for (uint32_t i = 0; i < tCase->cmdNum; ++i) {
        SqlDropStmtT *stmt = *(SqlDropStmtT **)DbListItem(&parsedList->cmdList, i);
        ASSERT_EQ(stmt->tag, T_SQL_DROP_STMT);
        if (tCase->dropType[i] == DROP_TABLE) {
            CheckDropTableItems(stmt, tCase->withIfExists[i], tCase->withDatabase[i], tCase->dropType[i]);
        }
        if (tCase->dropType[i] == DROP_INDEX) {
            CheckDropIndexItems(stmt, tCase->withIfExists[i], tCase->withDatabase[i], tCase->dropType[i]);
        }
    }
}

TEST_F(UtEmbSqlParser, SqlDropNormal)
{
    const DropTestCaseT input[] = {
        {"drop table t1;", 1, {0}, {0}, {DROP_TABLE}},
        {"drop table database.t1;", 1, {0}, {1}, {DROP_TABLE}},
        {"drop table if exists t1;", 1, {1}, {0}, {DROP_TABLE}},
        {"drop table if exists database.t1;", 1, {1}, {1}, {DROP_TABLE}},
        {"drop table t1;drop table if exists database.t1;", 2, {0, 1}, {0, 1}, {DROP_TABLE, DROP_TABLE}},

        {"drop index t1.idx1;", 1, {0}, {0}, {DROP_INDEX}},
        {"drop index database.t1.idx1;", 1, {0}, {1}, {DROP_INDEX}},
        {"drop index if exists t1.idx1;", 1, {1}, {0}, {DROP_INDEX}},
        {"drop index if exists database.t1.idx1;", 1, {1}, {1}, {DROP_INDEX}},
        {"drop index t1.idx1;drop index if exists database.t1.idx1;", 2, {0, 1}, {0, 1}, {DROP_INDEX, DROP_INDEX}},
    };
    for (uint32_t i = 0; i < sizeof(input) / sizeof(DropTestCaseT); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        ASSERT_EQ(GMERR_OK, ret);
        CheckDropStmtSyntax(&parsedList, &input[i]);
    }
}

/* =========== normal scenarios end =========== */

/* =========== abnormal scenarios begin =========== */
/*
 *  1.Missing "table"/"index"
 *  2.Missing "if"
 *  3.Missing "exists"
 *  4.Missing "database"
 *  5.Missing "tableName"/"indexName"
 */
TEST_F(UtEmbSqlParser, SqlDropAbnormal)
{
    const char *input[] = {"drop if exists database.t1;", "drop table  exists database.t1;",
        "drop table if  database.t1;", "drop table if exists .t1;", "drop table if exists database.;",
        "drop if exists database.t1;", "drop index  exists database.t1;", "drop index if  database.t1;",
        "drop index if exists .t1;", "drop index if exists database.;"};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedList);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}

/* =========== abnormal scenarios end =========== */
/* TestCase for drop stmt Parser End */

/* TestCase for CREATE/DROP VIEW stmt Parser begin */
/* =========== normal scenarios begin =========== */
TEST_F(UtEmbSqlParser, SqlCreateViewBaseCase)
{
    const char *input = "create view testView as select * from test2;";
    SqlParsedListT parsedlist;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedlist);
#ifndef IDS_HAOTIAN
    ASSERT_EQ(ret, GMERR_OK);
#else
    ASSERT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
#endif
    SqlCreateViewStmtT *stmt = *(SqlCreateViewStmtT **)DbListItem(&parsedlist.cmdList, 0);
    EXPECT_EQ(stmt->tag, T_SQL_CREATE_VIEW_STMT);
    EXPECT_EQ(stmt->isTemp, false);
    EXPECT_EQ(stmt->ifNotExists, false);
    EXPECT_STREQ(stmt->viewName->name, "TESTVIEW");
    EXPECT_EQ(stmt->viewName->database, (void *)NULL);
    EXPECT_EQ(stmt->viewColList, (void *)NULL);
}

// 待view功能支持完全，可放开此用例，并同时删除SqlCreateViewAbnormal用例
TEST_F(UtEmbSqlParser, DISABLED_SqlCreateViewBaseCase2)
{
    const char *input = "create temp view if not exists test.testView(age) as select * from test2;";
    SqlParsedListT parsedlist;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedlist);
    ASSERT_EQ(ret, GMERR_OK);

    SqlCreateViewStmtT *stmt = *(SqlCreateViewStmtT **)DbListItem(&parsedlist.cmdList, 0);
    EXPECT_EQ(stmt->tag, T_SQL_CREATE_VIEW_STMT);
    EXPECT_EQ(stmt->ifNotExists, true);
    EXPECT_STREQ(stmt->viewName->name, "testView");
    EXPECT_EQ(stmt->isTemp, true);
    EXPECT_STREQ(stmt->viewName->database, "test");
    EXPECT_STREQ(*(char **)DbListItem(stmt->viewColList, 0), "age");
}

TEST_F(UtEmbSqlParser, SqlDropViewBaseCase)
{
    const char *input = "drop view if exists testView;";
    SqlParsedListT parsedlist;
    Status ret = SqlParse(g_parserMemCtx, input, &parsedlist);
    ASSERT_EQ(ret, GMERR_OK);

    SqlDropStmtT *stmt = *(SqlDropStmtT **)DbListItem(&parsedlist.cmdList, 0);
    EXPECT_EQ(stmt->tag, T_SQL_DROP_STMT);
    EXPECT_EQ(stmt->ifExists, true);
    EXPECT_TRUE((stmt->name)->database == NULL);
    // 当前表名都按照大写字母存储
    EXPECT_STREQ(stmt->name->name, "TESTVIEW");
    EXPECT_EQ(stmt->dropElemType, DROP_VIEW);
}
/* =========== normal scenarios end =========== */

/* =========== abnormal scenarios begin =========== */
/*
 *  1.Do not support "temp" view
 *  2.Do not support viewName use database prefix as "test.xxx"
 *  3.Do not support specify view column list as "viewName(xxx)"
 */
TEST_F(UtEmbSqlParser, SqlCreateViewAbnormal)
{
    const char *input[] = {"create temp view if not exists test.testView(age) as select * from test2;",
        "create view if not exists test.testView(age) as select * from test2;",
        "create view if not exists testView(age) as select * from test2;",
        "create view if not exists testView as select * from test2;"};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *) - 1; ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedlist);
        ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
}

/*
 *  1.Missing "view"
 *  2.Missing "if"
 *  3.Wrong view name "test."
 *  4.Missing "as"
 *  5.Missing "select"
 */
TEST_F(UtEmbSqlParser, SqlCreateViewAbnormal2)
{
    const char *input[] = {"create test.testView(age) as select * from test2;",
        "create view not exists test.testView(age) as select * from test2;",
        "create view test. as select * from test2;", "create view test.testView select * from test2;",
        "create view test.testView as * from test2;"};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedlist);
        ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
/* =========== abnormal scenarios end =========== */
/* TestCase for CREATE/DROP VIEW stmt Parser End */

/* TestCase for transaction stmt Parser begin */
/* =========== normal scenarios begin =========== */
typedef struct BeginTransTestCase {
    const char *sql;
    uint32_t cmdNum;
    SqlTrxActionT action;
} BeginTransTestCaseT;

typedef struct CommitTransTestCase {
    const char *sql;
    uint32_t cmdNum;
} CommitTransTestCaseT;

typedef struct RollbackTransTestCase {
    const char *sql;
    uint32_t cmdNum;
    const char *savepointName;
} RollbackTransTestCaseT;

void CheckBeginTransStmtSyntax(SqlParsedListT *parsedList, const BeginTransTestCaseT *tCase)
{
    EXPECT_EQ(DbListGetItemCnt(&parsedList->cmdList), tCase->cmdNum);
    for (uint32_t i = 0; i < tCase->cmdNum; ++i) {
        SqlBeginTransStmtT *stmt = *(SqlBeginTransStmtT **)DbListItem(&parsedList->cmdList, i);
        EXPECT_EQ(stmt->tag, T_SQL_BEGIN_TRANSACTION_STMT);
        EXPECT_EQ(stmt->trxAction, tCase->action);
    }
}

void CheckCommitTransStmtSyntax(SqlParsedListT *parsedList, const CommitTransTestCaseT *tCase)
{
    EXPECT_EQ(DbListGetItemCnt(&parsedList->cmdList), tCase->cmdNum);
    for (uint32_t i = 0; i < tCase->cmdNum; ++i) {
        SqlCommitTransStmtT *stmt = *(SqlCommitTransStmtT **)DbListItem(&parsedList->cmdList, i);
        EXPECT_EQ(stmt->tag, T_SQL_COMMIT_TRANSACTION_STMT);
    }
}

void CheckRollbackTransStmtSyntax(SqlParsedListT *parsedList, const RollbackTransTestCaseT *tCase)
{
    EXPECT_EQ(DbListGetItemCnt(&parsedList->cmdList), tCase->cmdNum);
    for (uint32_t i = 0; i < tCase->cmdNum; ++i) {
        SqlRollbackTransStmtT *stmt = *(SqlRollbackTransStmtT **)DbListItem(&parsedList->cmdList, i);
        EXPECT_EQ(stmt->tag, T_SQL_ROLLBACK_TRANSACTION_STMT);
        EXPECT_STRCASEEQ(stmt->savepointName, tCase->savepointName);
    }
}

TEST_F(UtEmbSqlParser, SqlBeginTransNormal)
{
    const BeginTransTestCaseT input[] = {
        {"begin;", 1, TRX_ACTION_DEFERRED}, {"begin transaction;", 1, TRX_ACTION_DEFERRED}};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(input[0]); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        EXPECT_EQ(GMERR_OK, ret);
        CheckBeginTransStmtSyntax(&parsedList, &input[i]);
    }
}

TEST_F(UtEmbSqlParser, SqlCommitTransNormal)
{
    const CommitTransTestCaseT input[] = {
        {"commit;", 1}, {"commit transaction;", 1}, {"end;", 1}, {"end transaction;", 1}};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(input[0]); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        EXPECT_EQ(GMERR_OK, ret);
        CheckCommitTransStmtSyntax(&parsedList, &input[i]);
    }
}

TEST_F(UtEmbSqlParser, SqlRollbackTransNormal)
{
    const RollbackTransTestCaseT input[] = {
        {"rollback;", 1, NULL},
        {"rollback transaction;", 1, NULL},
    };
    for (uint32_t i = 0; i < sizeof(input) / sizeof(input[0]); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedList);
        EXPECT_EQ(GMERR_OK, ret);
        CheckRollbackTransStmtSyntax(&parsedList, &input[i]);
    }
}
/* =========== normal scenarios end =========== */
/* =========== abnormal scenarios begin =========== */
TEST_F(UtEmbSqlParser, SqlTransAbnormal)
{
    const char *input[] = {"begin transaction1;", "begin deferre transaction;", "begin immediate transaction abcd;",
        "begin exclusive exclusive transaction;", "commit transaction2 to name3;", "end commit transaction;",
        "rollback to savepoint;", "rollback to savepoint savepoint;", "rollback transaction savepoint;",
        "rollback transaction1 to name3;", "rollback transaction savepoint name4;"};
    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedList;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedList);
        EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
/* =========== abnormal scenarios end =========== */

typedef struct {
    const char *sql;
    uint8_t funcNum;            // 函数个数
    uint8_t argNum;             // 参数个数
    SqlExprFuncTypeE funcType;  // 函数名对应的函数宏
    bool aggDistinct;           // 函数参数是否包含关键字DISTINCT
    bool aggStar;               // 函数参数是否包含*
} BuiltInTestCaseT;

void CheckSelectBuiltInFuncSyntax(SqlParsedListT *parsedlist, const BuiltInTestCaseT *input)
{
    SqlSelectStmtT *selectStmt = *(SqlSelectStmtT **)DbListItem(&parsedlist->cmdList, 0);
    ASSERT_EQ(selectStmt->tag, T_SQL_SELECT_STMT);

    SqlExprFuncTypeE funcType[] = {FUNC_TYPE_SUM, FUNC_TYPE_MIN};
    for (uint32_t i = 0; i < input->funcNum; i++) {
        SqlTargetT *targetList = *(SqlTargetT **)DbListItem(selectStmt->targetList, i);
        ASSERT_EQ(targetList->aliasName == NULL, true);
        ASSERT_EQ(targetList->isStar, false);

        SqlExprFuncT *funcExpr = (SqlExprFuncT *)targetList->value->op;
        ASSERT_EQ(funcExpr->expr.type, SQL_EXPR_OP_FUN);
        ASSERT_EQ(funcExpr->aggArgs.aggDistinct, input->aggDistinct);
        ASSERT_EQ(funcExpr->aggArgs.aggStar, input->aggStar);
        if (input->funcNum == 1) {
            ASSERT_EQ(funcExpr->funcType, input->funcType);
        } else {
            ASSERT_EQ(funcExpr->funcType, funcType[i]);
        }

        if (funcExpr->args == NULL) {
            continue;
        }

        for (uint32_t j = 0; j < input->argNum; j++) {
            SqlExprT *argsExpr = *(SqlExprT **)DbListItem(funcExpr->args, j);
            ASSERT_EQ(argsExpr != NULL, true);
            if (argsExpr->op->type == SQL_EXPR_ITEM_COLUMN) {
                SqlExprColumnT *colArgs = (SqlExprColumnT *)argsExpr->op;
                // cmd: select sum(id), min(age) from tbl;
                ASSERT_STREQ(colArgs->columnName, i == 0 ? "id" : "age");
            } else if (argsExpr->op->type == SQL_EXPR_ITEM_CONST) {
                DmValueT value = ((SqlExprConstT *)argsExpr->op)->arg;
                if (value.type == DB_DATATYPE_INT64) {
                    // cmd: select max(1, 2) from tbl;
                    ASSERT_EQ(value.value.intValue, j == 0 ? 1 : 2);
                } else if (value.type == DB_DATATYPE_STRING) {
                    // cmd: select time('now') ...;
                    ASSERT_STREQ((char *)value.value.strAddr, "now");
                }
            }
        }
    }

    return;
}

/*
 * TestCase for normal built-in func parser begin
 * Testcase1: function args with DISTINCT keyword;
 * Testcase2: function args with normal column name;
 * Testcase3: function args with expr numberic add;
 * Testcase4: function args with expr column name add;
 * Testcase5: function args with multiple args;
 * Testcase6: function args with special symbol *;
 * Testcase7: function args with '';
 * Testcase8: multiple function in a statement;
 * Testcase9: select statement without contact table;
 * Testcase10: select statement function use in where clause;
 * Testcase11: select statement multiple function use in where clause;
 * TestCase for normal built-in func parser end
 */
TEST_F(UtEmbSqlParser, SqlBuiltInFuncNormal)
{
    const BuiltInTestCaseT input[] = {
        {"select sum(DISTINCT id) from tbl;", 1, 1, FUNC_TYPE_SUM, true, false},
        {"select sum(id) from tbl;", 1, 1, FUNC_TYPE_SUM, false, false},
        {"select sum(1 + 2) from tbl;", 1, 1, FUNC_TYPE_SUM, false, false},
        {"select sum(id + age) from tbl;", 1, 1, FUNC_TYPE_SUM, false, false},
        {"select max(1, 2) from tbl;", 1, 2, FUNC_TYPE_MAX, false, false},
        {"select count(*) from tbl;", 1, 0, FUNC_TYPE_COUNT, false, true},
        {"select count() from tbl;", 1, 0, FUNC_TYPE_COUNT, false, false},
        {"select time('now') from tbl;", 1, 1, FUNC_TYPE_TIME, false, false},
        {"select sum(id), min(age) from tbl;", 2, 1, FUNC_TYPE_SUM, false, false},
        {"select time('now');", 1, 1, FUNC_TYPE_TIME, false, false},
        {"select sum(length(id)) from tbl;", 1, 1, FUNC_TYPE_SUM, false, false},
        {"select sum(id) from tbl where length(age) > 1;", 1, 1, FUNC_TYPE_SUM, false, false},
        {"select sum(id), min(age) from tbl where length(age) > 1 and max(id) < 5;", 1, 1, FUNC_TYPE_SUM, false, false},
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(BuiltInTestCaseT); ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedlist);
        printf("===cmd: %s ret = %d\n", input[i].sql, ret);
        ASSERT_EQ(ret, GMERR_OK);
        CheckSelectBuiltInFuncSyntax(&parsedlist, &input[i]);
    }
}

/*
 * note: function args verify will process in analyzer, so cmd like: select sum(sum(age)) from tbl; will
   not drop error in parser.
 * TestCase for normal built-in func parser begin
 * Testcase1: function name error
 * Testcase2: function not support
 * Testcase3: function with not support keyword
 * TestCase for normal built-in func parser end
 */
TEST_F(UtEmbSqlParser, SqlBuiltInFuncAbnormal)
{
    const char *input[] = {
        "select sam(age) from tbl;",
        "select round(age) from tbl;",
        "select sum(DISTINCT) from tbl;",
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedlist);
        printf("===cmd: %s ret:%d\n", input[i], ret);
        ASSERT_EQ(ret, i < 2 ? GMERR_FEATURE_NOT_SUPPORTED : GMERR_SYNTAX_ERROR);
    }
}

typedef struct ConflictTestCase {
    const char *sql;
    SqlResolveTypeE resolveType;
    bool isInsert;
} ConflictTestCaseT;

void CheckConflictSyntax(SqlParsedListT *parsedlist, SqlResolveTypeE resolveType, bool isInsert)
{
    if (isInsert) {
        SqlInsertStmtT *stmt = *(SqlInsertStmtT **)DbListItem(&parsedlist->cmdList, 0);
        ASSERT_EQ(stmt->conflictStrategy, resolveType);
    } else {
        SqlUpdateStmtT *stmt = *(SqlUpdateStmtT **)DbListItem(&parsedlist->cmdList, 0);
        ASSERT_EQ(stmt->conflictStrategy, resolveType);
    }
    return;
}

TEST_F(UtEmbSqlParser, SqlConflictNormal)
{
    const ConflictTestCaseT input[] = {
        {"insert or abort into t_insert_conflict VALUES(3, 19, 'Tony');", RESOLVE_ABORT, true},
        {"insert or fail into t_insert_conflict VALUES(3, 19, 'Tony');", RESOLVE_FAIL, true},
        {"insert or rollback into t_insert_conflict VALUES(3, 19, 'Tony');", RESOLVE_ROLLBACK, true},
        {"insert or ignore into t_insert_conflict VALUES(3, 19, 'Tony');", RESOLVE_IGNORE, true},
        {"insert or replace into t_insert_conflict VALUES(3, 19, 'Tony');", RESOLVE_REPLACE, true},
        {"update or abort t_update_unique_conflict_2 set id =1 where age =19;", RESOLVE_ABORT, false},
        {"update or fail t_update_unique_conflict_2 set id =1 where age =19;", RESOLVE_FAIL, false},
        {"update or rollback t_update_unique_conflict_2 set id =1 where age =19;", RESOLVE_ROLLBACK, false},
        {"update or ignore t_update_unique_conflict_2 set id =1 where age =19;", RESOLVE_IGNORE, false},
        {"update or replace t_update_unique_conflict_2 set id =1 where age =19;", RESOLVE_REPLACE, false},
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(ConflictTestCaseT); ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i].sql, &parsedlist);
        ASSERT_EQ(ret, GMERR_OK);
        CheckConflictSyntax(&parsedlist, input[i].resolveType, input[i].isInsert);
    }
}

/* TestCase for returning stmt */
TEST_F(UtEmbSqlParser, SqlInsertReturningNormal)
{
    const char *input[] = {
        "insert into t2(a,b,c) values(1,2,3) returning  a>1, a aa, b as bb, *;",
    };
    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); i++) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedlist);
        EXPECT_EQ(GMERR_OK, ret);
        SqlInsertStmtT *insertStmt = *(SqlInsertStmtT **)DbListItem(&parsedlist.cmdList, 0);
        EXPECT_EQ(T_SQL_INSERT_STMT, insertStmt->tag);
        DbListT *returningClause = insertStmt->returningClause;

        SqlTargetT *a1 = *(SqlTargetT **)DbListItem(returningClause, 0);
        SqlExprT *exprBinary = a1->value;
        EXPECT_EQ(SQL_EXPR_OP_GT, exprBinary->op->type);

        SqlTargetT *aa = *(SqlTargetT **)DbListItem(returningClause, 1);
        EXPECT_STREQ("aa", aa->aliasName);
        SqlExprColumnT *exprColumn = (SqlExprColumnT *)aa->value->op;
        EXPECT_EQ(SQL_EXPR_ITEM_COLUMN, exprColumn->expr.type);
        EXPECT_STREQ("a", exprColumn->columnName);

        SqlTargetT *bb = *(SqlTargetT **)DbListItem(returningClause, 2);
        EXPECT_STREQ("bb", bb->aliasName);
        exprColumn = (SqlExprColumnT *)bb->value->op;
        EXPECT_EQ(SQL_EXPR_ITEM_COLUMN, exprColumn->expr.type);
        EXPECT_STREQ("b", exprColumn->columnName);

        SqlTargetT *star = *(SqlTargetT **)DbListItem(returningClause, 3);
        EXPECT_EQ(true, star->isStar);
    }
}

TEST_F(UtEmbSqlParser, SqlReturningSyntaxErr)
{
    const char *input[] = {
        "update t1 set a=1 where id =1 returning ;",
        "update t1 set a=1 where id =1 returning ,,;",
        "update t1 set a=1 where id =1 returning * AS x;",
        "update t1 set a=1 where id =1 returning *d;",
        "delete from t1 where id =1 returning *a d;",
    };
    for (uint32_t i = 0; i < sizeof(input) / sizeof(char *); i++) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(g_parserMemCtx, input[i], &parsedlist);
        EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    }
}
