/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 */

#include <string>
#include <numeric>
#include "ut_emb_sql_common.h"
#include "vector_ut_executor_common.h"

using namespace std;

#define MAX_SQL_UT_LEN 256
#define VECTOR_UT_DIMENSION 128

class VectorUtIndexBuild : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("ipcrm -a");
        Status ret = BaseInit(false, g_defaultSqlCfgFileName);
        ASSERT_EQ(ret, GMERR_OK);
        SqlSetGlobalSysTableAmNull();
    }

    static void TearDownTestCase()
    {
        BaseUninit();
        system("ipcrm -a");
    }

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        ASSERT_NE(nullptr, memCtx);
        oldMemCtx = DbMemCtxSwitchTo(memCtx);
        EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
        session = (Session *)conn->session;
    }

    virtual void TearDown()
    {
        QryTestReleaseSession(conn);
        DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbDeleteDynMemCtx(memCtx);
        clearAllStub();
    }

protected:
    DbMemCtxT *memCtx;
    DbMemCtxT *oldMemCtx;
    DrtConnectionT *conn;
    SessionT *session;
};

// 模拟用 cState.memCtx为索引构建分配足够大的内存,因大内存只在diskann,lpas场景下使用所以用例写在了sql/ 而不是 common/里
TEST_F(VectorUtIndexBuild, LargeDynMemAlloc_allocSize_exceed_appMemSize_01)
{
    // 大概申请 1537 M 应用区内存，如果总共2G ,系统区配512M，那必然申请失败
    size_t recNum = 392800;
    size_t size = recNum * (4096 + 8);
    DbMemCtxArgsT args = {0};
    DbMemCtxT *cStateMemCtx =
        DbCreateDynMemCtx((DbMemCtxT *)DbSrvGetAppDynCtx(DbGetProcGlobalId()), false, "CState memCtx", &args);
    ASSERT_NE(nullptr, cStateMemCtx);
    system("free -m");  // 顺便查看当前内存使用情况
    uint8_t *vec = (uint8_t *)DbDynMemCtxAlloc(cStateMemCtx, size);
    ASSERT_TRUE(vec == NULL);
    DbDeleteDynMemCtx(cStateMemCtx);
}

TEST_F(VectorUtIndexBuild, LargeDynMemAlloc_allocSize_under_appMemSize_02)
{
    // 大概申请 1530 M 应用区内存，如果总共2G ,系统区配512M，那可申请成功
    size_t recNum = 391000;
    size_t size = recNum * (4096 + 8);
    DbMemCtxArgsT args = {0};
    DbMemCtxT *cStateMemCtx =
        DbCreateDynMemCtx((DbMemCtxT *)DbSrvGetAppDynCtx(DbGetProcGlobalId()), false, "CState memCtx", &args);
    ASSERT_NE(nullptr, cStateMemCtx);
    system("free -m");  // 顺便查看当前内存使用情况
    uint8_t *vec = (uint8_t *)DbDynMemCtxAlloc(cStateMemCtx, size);
    ASSERT_TRUE(vec != NULL);
    // 必须初使化以验证所有的页都可写
    std::iota(vec, vec + size, 1);
    DbDynMemCtxFree(cStateMemCtx, vec);
    DbDeleteDynMemCtx(cStateMemCtx);
}
