/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test for analyzer of delete in SQL
 * Author: SQL
 * Create: 2024-02-05
 */

#include <string>
#include "ut_emb_sql_common.h"
#include "cpl_sql_compiler.h"

using namespace std;

class UtEmbSqlAnalyzeDelete : public testing::Test {
protected:
    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args{};
        memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "Sql analyzer dynMemCtx", &args);
        ASSERT_NE(nullptr, memCtx);
        oldMemCtx = DbMemCtxSwitchTo(memCtx);
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
        ASSERT_NE(nullptr, session);
    }
    virtual void TearDown()
    {
        QrySessionRelease(session);
        DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbDeleteDynMemCtx(memCtx);
        clearAllStub();
    }
    static void SetUpTestCase()
    {
        system("ipcrm -a");
        Status ret = BaseInit(false);
        ASSERT_EQ(GMERR_OK, ret);
        SqlSetGlobalSysTableAmNull();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
        system("ipcrm -a");
    };
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}

protected:
    DbMemCtxT *memCtx;
    DbMemCtxT *oldMemCtx;
    SessionT *session;
};

TEST_F(UtEmbSqlAnalyzeDelete, SqlDeleteNoFilter)
{
    // Create Table
    string createSql = "CREATE TABLE deleteTbl(id int primary key, name text, rank int unique);";
    SqlUtExecuteStmt(memCtx, session, createSql.c_str());

    // Test
    string deleteSql = "DELETE FROM deleteTbl;";
    SqlParsedListT parsedlist{};
    Status ret = SqlParse(memCtx, deleteSql.c_str(), &parsedlist);
    ASSERT_EQ(GMERR_OK, ret);

    DbListT irStmtList{};
    ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    string tableName = "deleteTbl";
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel((char *)tableName.c_str());
    ASSERT_NE(nullptr, vertexLabel);
    ASSERT_STRCASEEQ(tableName.c_str(), vertexLabel->metaCommon.metaName);

    // Check1: 逻辑Delete算子
    SqlIrStmtT *stmt = *(SqlIrStmtT **)DbListItem(&irStmtList, 0);
    ASSERT_NE(nullptr, stmt);
    IRExprT *deleteOp = stmt->irPlan->root;
    ASSERT_NE(nullptr, deleteOp);
    ASSERT_TRUE(SqlUtCheckDmlOp(IR_LOGOP_DELETE, (uint32_t)IR_OPARITY_ONE, deleteOp, vertexLabel));

    // Check2: 逻辑Scan算子
    IRExprT *scanOp = deleteOp->children[0];
    ASSERT_NE(nullptr, scanOp);
    ASSERT_TRUE(SqlUtCheckScanOp(scanOp, vertexLabel));

    // Drop Table
    string dropSql = "DROP TABLE deleteTbl;";
    SqlUtExecuteStmt(memCtx, session, dropSql.c_str());
}

TEST_F(UtEmbSqlAnalyzeDelete, SqlDeleteByOneFilter)
{
    // Create Table
    string createSql = "CREATE TABLE deleteTbl(id int primary key, name text, rank int unique);";
    SqlUtExecuteStmt(memCtx, session, createSql.c_str());

    // Test
    string deleteSql = "DELETE FROM deleteTbl where id = 1;";
    SqlParsedListT parsedlist{};
    Status ret = SqlParse(memCtx, deleteSql.c_str(), &parsedlist);
    ASSERT_EQ(GMERR_OK, ret);

    DbListT irStmtList{};
    ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    string tableName = "deleteTbl";
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel((char *)tableName.c_str());
    ASSERT_NE(nullptr, vertexLabel);
    ASSERT_STRCASEEQ(tableName.c_str(), vertexLabel->metaCommon.metaName);

    // Check1: 逻辑Delete算子
    SqlIrStmtT *stmt = *(SqlIrStmtT **)DbListItem(&irStmtList, 0);
    ASSERT_NE(nullptr, stmt);
    IRExprT *deleteOp = stmt->irPlan->root;
    ASSERT_NE(nullptr, deleteOp);
    ASSERT_TRUE(SqlUtCheckDmlOp(IR_LOGOP_DELETE, (uint32_t)IR_OPARITY_ONE, deleteOp, vertexLabel));

    // Check2: 逻辑Select算子
    IRExprT *selectOp = deleteOp->children[0];
    ASSERT_NE(nullptr, selectOp);
    ASSERT_TRUE(SqlUtCheckSelectOp(selectOp, vertexLabel));

    // Check3: 逻辑Scan算子
    IRExprT *scanOp = selectOp->children[0];
    ASSERT_NE(nullptr, scanOp);
    ASSERT_TRUE(SqlUtCheckScanOp(scanOp, vertexLabel));

    // Check4: Item
    IRExprT *itemOp = selectOp->children[1];
    ASSERT_NE(nullptr, itemOp);
    DmValueT exptValue{};
    exptValue.type = DB_DATATYPE_INT64;
    exptValue.value.longValue = 1;
    ASSERT_TRUE(SqlUtCheckItemOpWithCmp(itemOp, COMP_OP_EQ, 0, &exptValue));

    // Drop Table
    string dropSql = "DROP TABLE deleteTbl;";
    SqlUtExecuteStmt(memCtx, session, dropSql.c_str());
}

TEST_F(UtEmbSqlAnalyzeDelete, SqlDeleteByMultiFilter)
{
    // Create Table
    string createSql = "CREATE TABLE deleteTbl(id int primary key, name text, rank int unique);";
    SqlUtExecuteStmt(memCtx, session, createSql.c_str());

    // Test
    string deleteSql = "DELETE FROM deleteTbl as aliasTbl where id = 1 or aliasTbl.name < 'n1' and rank > 10;";
    SqlParsedListT parsedlist{};
    Status ret = SqlParse(memCtx, deleteSql.c_str(), &parsedlist);
    ASSERT_EQ(GMERR_OK, ret);

    DbListT irStmtList{};
    ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    string tableName = "deleteTbl";
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel((char *)tableName.c_str());
    ASSERT_NE(nullptr, vertexLabel);
    ASSERT_STRCASEEQ(tableName.c_str(), vertexLabel->metaCommon.metaName);

    // Check1: 逻辑Delete算子
    SqlIrStmtT *stmt = *(SqlIrStmtT **)DbListItem(&irStmtList, 0);
    ASSERT_NE(nullptr, stmt);
    IRExprT *deleteOp = stmt->irPlan->root;
    ASSERT_NE(nullptr, deleteOp);
    ASSERT_TRUE(SqlUtCheckDmlOp(IR_LOGOP_DELETE, (uint32_t)IR_OPARITY_ONE, deleteOp, vertexLabel));

    // Check2: 逻辑Select算子
    IRExprT *selectOp = deleteOp->children[0];
    ASSERT_NE(nullptr, selectOp);
    ASSERT_TRUE(SqlUtCheckSelectOp(selectOp, vertexLabel));

    // Check3: 逻辑Scan算子
    IRExprT *scanOp = selectOp->children[0];
    ASSERT_NE(nullptr, scanOp);
    ASSERT_TRUE(SqlUtCheckScanOp(scanOp, vertexLabel));

    // Check4: Item
    IRExprT *itemOr = selectOp->children[1];
    ASSERT_NE(nullptr, itemOr);
    // Check4.1: Item OR
    ASSERT_TRUE(SqlUtCheckItemOpWithLogic(itemOr, LOGIC_OP_OR, COMP_OP_EQ, LOGIC_OP_AND));
    // Check4.2: Item EQ
    IRExprT *itemEq = itemOr->children[0];
    ASSERT_NE(nullptr, itemEq);
    DmValueT intValue{};
    intValue.type = DB_DATATYPE_INT64;
    intValue.value.longValue = 1;
    ASSERT_TRUE(SqlUtCheckItemOpWithCmp(itemEq, COMP_OP_EQ, 0, &intValue));
    // Check4.3: Item AND
    IRExprT *itemAnd = itemOr->children[1];
    ASSERT_NE(nullptr, itemAnd);
    ASSERT_TRUE(SqlUtCheckItemOpWithLogic(itemAnd, LOGIC_OP_AND, COMP_OP_LT, COMP_OP_GT));
    // Check4.4: Item LT
    IRExprT *itemLt = itemAnd->children[0];
    ASSERT_NE(nullptr, itemLt);
    DmValueT strValue{};
    strValue.type = DB_DATATYPE_STRING;
    strValue.value.strAddr = (char *)"n1";
    strValue.value.length = strlen((char *)"n1") + 1;
    ASSERT_TRUE(SqlUtCheckItemOpWithCmp(itemLt, COMP_OP_LT, 1, &strValue));
    // Check4.5: Item GT
    IRExprT *itemGt = itemAnd->children[1];
    ASSERT_NE(nullptr, itemGt);
    intValue.value.longValue = 10;
    ASSERT_TRUE(SqlUtCheckItemOpWithCmp(itemGt, COMP_OP_GT, 2, &intValue));

    // Drop Table
    string dropSql = "DROP TABLE deleteTbl;";
    SqlUtExecuteStmt(memCtx, session, dropSql.c_str());
}

typedef struct UtEmbSqlDeleteErrCase {
    const char *sql;
    Status errType;
} UtEmbSqlDeleteErrCaseT;

TEST_F(UtEmbSqlAnalyzeDelete, SqlDeleteErrCase)
{
    // Create Table
    string createSql = "CREATE TABLE deleteTbl(id int primary key, name text, rank int unique);";
    SqlUtExecuteStmt(memCtx, session, createSql.c_str());

    // Test & Check
    const UtEmbSqlDeleteErrCaseT input[] = {
        {"DELETE FROM tbl;", GMERR_UNDEFINED_TABLE},
        {"DELETE FROM deleteTbl where prop = 1;", GMERR_UNDEFINE_COLUMN},
        {"DELETE FROM deleteTbl where tbl.id = 1;", GMERR_UNDEFINE_COLUMN},
        {"DELETE FROM deleteTbl as aliasTbl where deleteTbl.id = 1;", GMERR_UNDEFINE_COLUMN},
    };

    for (uint32_t i = 0; i < sizeof(input) / sizeof(UtEmbSqlDeleteErrCaseT); ++i) {
        printf("UtEmbSqlDeleteErrCase[%d]: %s\n", i, input[i].sql);
        SqlParsedListT parsedlist{};
        Status ret = SqlParse(memCtx, input[i].sql, &parsedlist);
        ASSERT_EQ(GMERR_OK, ret);

        DbListT irList{};
        ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irList);
        ASSERT_EQ(input[i].errType, ret);
    }

    // Drop Table
    string dropSql = "DROP TABLE deleteTbl;";
    SqlUtExecuteStmt(memCtx, session, dropSql.c_str());
}
