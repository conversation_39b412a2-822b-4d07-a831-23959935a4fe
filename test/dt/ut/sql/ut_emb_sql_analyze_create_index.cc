/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test for create index analyze of SQL
 * Author: SQL Team
 * Create: 2024-02-06
 */

#include "ut_emb_sql_common.h"

using namespace std;

class UtEmbSqlAnalyzeCreateIndex : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("ipcrm -a");
        Status ret = BaseInit(false);
        ASSERT_EQ(ret, GMERR_OK);
        SqlSetGlobalSysTableAmNull();
    }

    static void TearDownTestCase()
    {
        BaseUninit();
        system("ipcrm -a");
    }

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        ASSERT_NE(nullptr, memCtx);
        oldMemCtx = DbMemCtxSwitchTo(memCtx);
        EXPECT_EQ(GMERR_OK, QryTestAllocSession(&conn, NULL));
        session = (Session *)conn->session;
    }

    virtual void TearDown()
    {
        QryTestReleaseSession(conn);
        DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbDeleteDynMemCtx(memCtx);
        clearAllStub();
    }

protected:
    DbMemCtxT *memCtx;
    DbMemCtxT *oldMemCtx;
    DrtConnectionT *conn;
    SessionT *session;
};

TEST_F(UtEmbSqlAnalyzeCreateIndex, AnalyzeCreateIndex)
{
    // 1. 建表
    SqlUtExecuteStmt(memCtx, session, (char *)"CREATE TABLE t1(id int, idkey int, name text);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel((char *)"t1");
    EXPECT_STRCASEEQ(vertexLabel->metaCommon.metaName, (char *)"t1");

    // 2. 创建索引
    const char *input = "create index idx1 on t1(id);";
    SqlParsedListT parsedlist;
    Status ret = SqlParse(memCtx, input, &parsedlist);
    ASSERT_EQ(ret, GMERR_OK);
    DbListT irStmtList;
    ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
    ASSERT_EQ(ret, GMERR_OK);

    SqlIrStmtT *stmt = *(SqlIrStmtT **)DbListItem(&irStmtList, 0);
    CreateIndexStmtT *node = (CreateIndexStmtT *)stmt->utilityStmt;

    // CheckCreateIndex
    DmVlIndexLabelT *idxLabel = node->indexLabel;
    EXPECT_STRCASEEQ(idxLabel->indexName, "idx1");
    ASSERT_EQ(idxLabel->indexNameLen, (uint32_t)5);
    ASSERT_EQ(idxLabel->idxLabelBase.indexConstraint, NON_UNIQUE);
    ASSERT_EQ(idxLabel->isNullable, true);
    ASSERT_EQ(idxLabel->idxLabelBase.isLabelLatchMode, false);
    ASSERT_EQ(idxLabel->propeNum, (uint32_t)1);
    ASSERT_EQ(idxLabel->fixedPropeNum, (uint32_t)1);
    ASSERT_EQ(idxLabel->idxLabelBase.indexType, BTREE_INDEX);
    // CheckSortType
    ASSERT_EQ(idxLabel->sortType[0], DM_SORT_ASC);
}

// 创建唯一索引 + 多列索引
TEST_F(UtEmbSqlAnalyzeCreateIndex, AnalyzeCreateUniqueIndex)
{
    // 1. 建表
    SqlUtExecuteStmt(memCtx, session, (char *)"CREATE TABLE t2(id int, idkey int, name text);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel((char *)"t2");
    EXPECT_STRCASEEQ(vertexLabel->metaCommon.metaName, (char *)"t2");

    // 2. 创建索引
    const char *input = "create unique index idx1 on t2(id asc, name desc);";
    SqlParsedListT parsedlist;
    Status ret = SqlParse(memCtx, input, &parsedlist);
    ASSERT_EQ(ret, GMERR_OK);
    DbListT irStmtList;
    ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
    ASSERT_EQ(ret, GMERR_OK);

    SqlIrStmtT *stmt = *(SqlIrStmtT **)DbListItem(&irStmtList, 0);
    CreateIndexStmtT *node = (CreateIndexStmtT *)stmt->utilityStmt;

    // CheckCreateIndex
    DmVlIndexLabelT *idxLabel = node->indexLabel;
    EXPECT_STRCASEEQ(idxLabel->indexName, "idx1");
    ASSERT_EQ(idxLabel->indexNameLen, (uint32_t)5);
    ASSERT_EQ(idxLabel->idxLabelBase.indexConstraint, UNIQUE);
    ASSERT_EQ(idxLabel->isNullable, true);
    ASSERT_EQ(idxLabel->idxLabelBase.isLabelLatchMode, false);
    ASSERT_EQ(idxLabel->propeNum, (uint32_t)2);
    ASSERT_EQ(idxLabel->fixedPropeNum, (uint32_t)1);
    ASSERT_EQ(idxLabel->idxLabelBase.indexType, BTREE_INDEX);

    // CheckindexProperties
    uint32_t *propIds = idxLabel->propIds;
    DmPropertySchemaT *properties = idxLabel->properties;

    DmPropertySchemaT property0 = properties[propIds[0]];
    ASSERT_EQ(property0.nameLen, strlen("id") + 1);
    ASSERT_EQ(strcmp(property0.name, "id"), 0);
    ASSERT_EQ(property0.dataType, DB_DATATYPE_INT64);

    DmPropertySchemaT property1 = properties[propIds[1]];
    ASSERT_EQ(property1.nameLen, strlen("name") + 1);
    ASSERT_EQ(strcmp(property1.name, "name"), 0);
    ASSERT_EQ(property1.dataType, DB_DATATYPE_STRING);

    // CheckSortType
    ASSERT_EQ(idxLabel->sortType[0], DM_SORT_ASC);
    ASSERT_EQ(idxLabel->sortType[1], DM_SORT_DESC);
}

TEST_F(UtEmbSqlAnalyzeCreateIndex, AnalyzeCreateIndexIfNotExists)
{
    // 1. 建表
    SqlUtExecuteStmt(memCtx, session, (char *)"CREATE TABLE tif(id int, idkey int, name text);");
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel((char *)"tif");
    EXPECT_STRCASEEQ(vertexLabel->metaCommon.metaName, (char *)"tif");

    // 2. 创建索引
    SqlUtExecuteStmt(memCtx, session, (char *)"create index idxif1 on tif(id, idkey);");

    // 3. 创建同名索引
    const char *input = "create index idxif1 on tif(id);";
    SqlParsedListT parsedlist;
    Status ret = SqlParse(memCtx, input, &parsedlist);
    ASSERT_EQ(ret, GMERR_OK);
    DbListT irStmtList;
    ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
    // 预期失败
    ASSERT_EQ(ret, GMERR_DUPLICATE_OBJECT);
}

typedef struct CreateIndexErrCase {
    const char *sql;
    Status ret;
} CreateTableErrCaseT;

TEST_F(UtEmbSqlAnalyzeCreateIndex, AnalyzeCreateIndexErrCase)
{
    // 1. 建表
    SqlUtExecuteStmt(memCtx, session, (char *)"CREATE TABLE test1(id int, idkey int, name text);");
    SqlUtExecuteStmt(memCtx, session, (char *)"CREATE TABLE test2(id int, idkey int, name text);");

    const CreateTableErrCaseT input[] = {
        {"create index idx1 on test3(id) ;", GMERR_UNDEFINED_TABLE},
        {"create index test2 on test1(id);", GMERR_DUPLICATE_TABLE},
        {"create index idxtest1 on test1(test);", GMERR_UNDEFINE_COLUMN},
        {"create index unspidx on test1(id) where id > 10;", GMERR_FEATURE_NOT_SUPPORTED},
        {"create index "
         "nameaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
         "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa on test1(id);",
            GMERR_NAME_TOO_LONG},
    };

    uint32_t count = ELEMENT_COUNT(input);
    for (uint32_t i = 0; i < count; ++i) {
        SqlParsedListT parsedlist;
        Status ret = SqlParse(memCtx, input[i].sql, &parsedlist);
        ASSERT_EQ(ret, GMERR_OK);
        DbListT irStmtList;
        ret = SqlUtAnalyze(session, memCtx, &parsedlist, &irStmtList);
        ASSERT_EQ(ret, input[i].ret);
    }
}
