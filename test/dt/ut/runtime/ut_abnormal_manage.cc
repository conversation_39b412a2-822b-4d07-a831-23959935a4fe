/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "ut_drt_common.h"

static int32_t g_testCount = 0;
static int32_t g_testCount2 = 0;
static int32_t g_testCount3 = 0;

class UtDrtAbnormity : public testing::Test {
protected:
    virtual void SetUp()
    {
        testStartTime = UtDrtNoteStartTime();
    }
    virtual void TearDown()
    {
        UtDrtNoteEndTime(testStartTime);
    }
    static void SetUpTestCase()
    {
        init();
        UtDrtCommonInit();
        int32_t ret = CommonInitDrtInstanceInEuler(false);
        ASSERT_EQ(DB_SUCCESS, ret);
    }
    static void TearDownTestCase()
    {
        DrtInstanceDestroy(NULL);
        CommonRelease();
        clearAllStub();
    }

private:
    uint64_t testStartTime;
};

ShmemPtrT DbShmemCtxAllocStub(void *curCtx, uint32_t size);
int32_t DrtTimerUnregisterStub(const TimerHandleT *timerHandle, TimerModeE mode);

extern "C" {
DrtAbnTaskDefT *DrtAbnGetTaskByName(DrtAbnormityMgrT *abnMgr, const char *taskName);
ShmemPtrT DrtAllocAbnTask(const DrtAbnormityMgrT *abnMgr);
int32_t DrtAbnRegistTaskTimer(DrtAbnTaskDefT *task);
void DrtFreeAbnTask(ShmemPtrT taskShmPtr);
int32_t DrtAbnDestroyTask(DrtAbnormityMgrT *abnMgr, ShmemPtrT taskShmPtr);
DrtAbnNoticeT *DrtAbnGetNoticeByName(DrtAbnTaskDefT *task, const char *name);
}

errno_t memset_s_stub(void *dest, size_t destMax, int c, size_t count)
{
    DB_UNUSED(dest);
    DB_UNUSED(destMax);
    DB_UNUSED(c);
    DB_UNUSED(count);
    return GMERR_ERROR_IN_ASSIGNMENT;
}

errno_t strncpy_s_stub(char *strDest, size_t destMax, const char *strSrc, size_t count)
{
    DB_UNUSED(strDest);
    DB_UNUSED(destMax);
    DB_UNUSED(strSrc);
    DB_UNUSED(count);
    return GMERR_ERROR_IN_ASSIGNMENT;
}

void *DbGetShmemCtxByIdStub(uint32_t ctxId, uint32_t instanceId)
{
    DB_UNUSED(ctxId);
    DB_UNUSED(instanceId);
    return NULL;
}

DrtAbnTaskDefT *DrtAbnGetTaskByNameStub(DrtAbnormityMgrT *abnMgr, const char *taskName)
{
    DB_UNUSED(taskName);
    return NULL;
}

ShmemPtrT DrtAllocAbnTaskStub(const DrtAbnormityMgrT *abnMgr)
{
    return DB_INVALID_SHMPTR;
}

int32_t DrtAbnRegistTaskTimerStub(DrtAbnTaskDefT *task)
{
    return GMERR_NO_DATA;
}

int32_t DrtTimerRegisterStub(DbTimerT *timer, TimerHandleT *timerHandle)
{
    return GMERR_DATA_EXCEPTION;
}

void DrtFreeAbnTaskStub(ShmemPtrT taskShmPtr)
{
    return;
}

DrtAbnNoticeT *DrtAbnGetNoticeByNameStub(DrtAbnTaskDefT *task, const char *name)
{
    DB_UNUSED(name);
    task->noticeCount = DB_MAX_ABN_NOTICE_NUM + 1;
    return {0};
}

AbnStatusE AbnormityTaskCheckTest(void *count)
{
    int32_t *abnCount = (int32_t *)count;
    if (*abnCount == 2) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_WARNING;
    } else if (*abnCount == 3) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_MINOR;
    } else if (*abnCount == 4) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_MAJOR;
    } else if (*abnCount == 5) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_STATUS_CRITICAL;
    } else {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_NORMAL;
    }
}

AbnStatusE AbnormityTaskCheckTest2(void *count)
{
    int32_t *abnCount = (int32_t *)count;
    if (*abnCount == 2) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_WARNING;
    } else if (*abnCount == 3) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_MINOR;
    } else if (*abnCount == 4) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_MAJOR;
    } else if (*abnCount == 5) {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_STATUS_CRITICAL;
    } else {
        printf("abnCount: %d .\n", *abnCount);
        *abnCount = *abnCount + 1;
        return ABN_SATUS_NORMAL;
    }
}

Status AbnormityNotifyTest1(void *notifyCtx, const char *taskName, const AbnStatusE status)
{
    if (taskName != NULL) {
        printf("------------------------\n");
        printf("%s got abnormity for Low-level.", taskName);
        switch (status) {
            case ABN_SATUS_WARNING:
                printf("ABN_SATUS_WARNING abnormity.\n");
                break;
            case ABN_SATUS_MINOR:
                printf("ABN_SATUS_MINOR abnormity.\n");
                break;
            case ABN_SATUS_MAJOR:
                printf("ABN_SATUS_MAJOR abnormity.\n");
                break;
            case ABN_STATUS_CRITICAL:
                printf("ABN_STATUS_CRITICAL abnormity.\n");
                break;
            default:
                printf("Get ERROR abnormity stat.\n");
        }
    }
    DB_UNUSED(notifyCtx);
    return GMERR_OK;
}
Status AbnormityNotifyTest2(void *notifyCtx, const char *taskName, const AbnStatusE status)
{
    if (taskName != NULL) {
        printf("------------------------\n");
        printf("%s got abnormity for Mid-level.", taskName);
        switch (status) {
            case ABN_SATUS_WARNING:
                printf("ABN_SATUS_WARNING abnormity.\n");
                break;
            case ABN_SATUS_MINOR:
                printf("ABN_SATUS_MINOR abnormity.\n");
                break;
            case ABN_SATUS_MAJOR:
                printf("ABN_SATUS_MAJOR abnormity.\n");
                break;
            case ABN_STATUS_CRITICAL:
                printf("ABN_STATUS_CRITICAL abnormity.\n");
                break;
            default:
                printf("Get ERROR abnormity.\n");
        }
    }
    DB_UNUSED(notifyCtx);
    return GMERR_OK;
}

Status AbnormityNotifyTest3(void *notifyCtx, const char *taskName, const AbnStatusE status)
{
    if (taskName != NULL) {
        printf("------------------------\n");
        printf("%s got abnormity for Hight Level.", taskName);
        switch (status) {
            case ABN_SATUS_WARNING:
                printf("ABN_SATUS_WARNING abnormity.\n");
                break;
            case ABN_SATUS_MINOR:
                printf("ABN_SATUS_MINOR abnormity.\n");
                break;
            case ABN_SATUS_MAJOR:
                printf("ABN_SATUS_MAJOR abnormity.\n");
                break;
            case ABN_STATUS_CRITICAL:
                printf("ABN_STATUS_CRITICAL abnormity.\n");
                break;
            default:
                printf("Get ERROR abnormity.\n");
        }
    }
    DB_UNUSED(notifyCtx);
    return GMERR_OK;
}

TEST_F(UtDrtAbnormity, runtime_abnormal_normal_001)
{
    char name[] = {"AbnormityTest"};
    uint16_t nameLen = 14;

    DrtTaskInfoT taskTest = {0};
    taskTest.checkPeriodic = 500;
    int32_t ret = strncpy_s(taskTest.taskName, nameLen, name, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest.abnCheck = AbnormityTaskCheckTest;
    taskTest.proCtx = (void *)&g_testCount;

    DrtInstanceT *instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);

    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_EQ(GMERR_OK, ret);

    DrtAbnNoticeT noticeTest;
    char noticeName[] = {"noticeTest"};
    nameLen = 11;
    ret = strncpy_s(noticeTest.noticeName, nameLen, noticeName, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest.abnNotify = AbnormityNotifyTest1;
    noticeTest.notifyCtx = NULL;
    noticeTest.noticeLevel = NOTICE_LEVEL_LOW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest);
    EXPECT_EQ(GMERR_OK, ret);

    DrtAbnNoticeT noticeTest2;
    char noticeName2[] = {"noticeTest2"};
    nameLen = 12;
    ret = strncpy_s(noticeTest2.noticeName, nameLen, noticeName2, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest2.abnNotify = AbnormityNotifyTest2;
    noticeTest2.notifyCtx = NULL;
    noticeTest2.noticeLevel = NOTICE_LEVEL_MEDIUW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest2);
    EXPECT_EQ(GMERR_OK, ret);

    DrtAbnNoticeT noticeTest3;
    char noticeName3[] = {"noticeTest3"};
    nameLen = 12;
    ret = strncpy_s(noticeTest3.noticeName, nameLen, noticeName3, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest3.abnNotify = AbnormityNotifyTest3;
    noticeTest3.notifyCtx = NULL;
    noticeTest3.noticeLevel = NOTICE_LEVEL_HIGH;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest3);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_testCount < 6) {
        DbUsleep(1000);
    }

    ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, name, noticeName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, name);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtDrtAbnormity, runtime_abnormal_error_002)
{
    char name[] = {"AbnormityTest"};
    uint16_t nameLen = 14;

    DrtTaskInfoT taskTest = {0};
    taskTest.checkPeriodic = 500;
    int32_t ret = strncpy_s(taskTest.taskName, nameLen, name, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest.abnCheck = AbnormityTaskCheckTest;
    taskTest.proCtx = (void *)&g_testCount2;

    DrtInstanceT *instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);

    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);

    DrtTaskInfoT taskTest2 = {0};
    taskTest2.checkPeriodic = 500;
    ret = strncpy_s(taskTest2.taskName, nameLen, name, nameLen - 1);
    taskTest2.abnCheck = NULL;
    taskTest2.proCtx = (void *)&g_testCount2;
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest2);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    DrtAbnNoticeT noticeTest;
    char noticeName[] = {"noticeTest"};
    nameLen = 11;
    ret = strncpy_s(noticeTest.noticeName, nameLen, noticeName, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest.abnNotify = AbnormityNotifyTest1;
    noticeTest.notifyCtx = NULL;
    noticeTest.noticeLevel = NOTICE_LEVEL_LOW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest);
    EXPECT_EQ(GMERR_OK, ret);

    DrtAbnNoticeT noticeTest2;
    nameLen = 11;
    ret = strncpy_s(noticeTest2.noticeName, nameLen, noticeName, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest2.abnNotify = AbnormityNotifyTest1;
    noticeTest2.notifyCtx = NULL;
    noticeTest2.noticeLevel = NOTICE_LEVEL_LOW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest2);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);

    DrtAbnNoticeT noticeTest4;
    char noticeName4[] = {"noticeTest4"};
    nameLen = 12;
    ret = strncpy_s(noticeTest4.noticeName, nameLen, noticeName4, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest4.abnNotify = NULL;
    noticeTest4.notifyCtx = NULL;
    noticeTest4.noticeLevel = NOTICE_LEVEL_LOW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest4);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    while (g_testCount2 < 7) {
        DbUsleep(1000);
    }

    ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, name, noticeName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, name, noticeName);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char nameError[] = {"AbnormityTestError"};
    ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, nameError, noticeName);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, nameError);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
}

TEST_F(UtDrtAbnormity, runtime_abnormal_error_003)
{
    char name[] = {"AbnormityTest"};
    uint16_t nameLen = 14;

    DrtTaskInfoT taskTest = {0};
    taskTest.checkPeriodic = 500;
    int32_t ret = strncpy_s(taskTest.taskName, nameLen, name, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest.abnCheck = AbnormityTaskCheckTest;
    taskTest.proCtx = (void *)&g_testCount3;

    DrtInstanceT *instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);

    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_EQ(GMERR_OK, ret);

    char name2[] = {"AbnormityTest2"};
    uint16_t nameLen2 = 15;
    DrtTaskInfoT taskTest2 = {0};
    taskTest2.checkPeriodic = 500;
    ret = strncpy_s(taskTest2.taskName, nameLen2, name2, nameLen2 - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest2.abnCheck = AbnormityTaskCheckTest2;
    taskTest2.proCtx = (void *)&g_testCount3;

    DrtAbnNoticeT noticeTest;
    char noticeName[] = {"noticeTest"};
    nameLen = 11;
    ret = strncpy_s(noticeTest.noticeName, nameLen, noticeName, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest.abnNotify = AbnormityNotifyTest1;
    noticeTest.notifyCtx = NULL;
    noticeTest.noticeLevel = NOTICE_LEVEL_LOW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest);
    EXPECT_EQ(GMERR_OK, ret);

    DrtAbnNoticeT noticeTest2;
    char noticeName2[] = {"noticeTest2"};
    nameLen = 12;
    ret = strncpy_s(noticeTest2.noticeName, nameLen, noticeName2, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest2.abnNotify = AbnormityNotifyTest2;
    noticeTest2.notifyCtx = NULL;
    noticeTest2.noticeLevel = NOTICE_LEVEL_MEDIUW;
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest2);
    EXPECT_EQ(GMERR_OK, ret);

    DrtAbnNoticeT noticeTest3;
    char noticeName3[] = {"noticeTest3"};
    nameLen = 12;
    ret = strncpy_s(noticeTest3.noticeName, nameLen, noticeName3, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest3.abnNotify = AbnormityNotifyTest3;
    noticeTest3.notifyCtx = NULL;
    noticeTest3.noticeLevel = NOTICE_LEVEL_HIGH;

    bool flag = true;
    while (g_testCount3 < 7) {
        DbUsleep(1000);
        if (g_testCount3 == 4 && flag) {
            ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, name, noticeName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest3);
            EXPECT_EQ(GMERR_OK, ret);
            flag = false;
        }
    }

    ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, name, noticeName3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("unnotice notice3 is ok ");

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("destroy task is ok ");
}

TEST_F(UtDrtAbnormity, runtime_abnormal_error_004)
{
    char name[] = {"AbnormityTest"};
    uint16_t nameLen = 14;

    DrtTaskInfoT taskTest = {0};
    taskTest.checkPeriodic = 500;
    int32_t ret = strncpy_s(taskTest.taskName, nameLen, name, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest.abnCheck = AbnormityTaskCheckTest;
    taskTest.proCtx = (void *)&g_testCount;

    DrtInstanceT *instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);

    (void)setStubC((void *)DbShmemCtxAlloc, (void *)DbShmemCtxAllocStub);
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_NE(GMERR_OK, ret);

    (void)setStubC((void *)DbGetShmemCtxById, (void *)DbGetShmemCtxByIdStub);
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_NE(GMERR_OK, ret);

    (void)setStubC((void *)DrtAbnGetTaskByName, (void *)DrtAbnGetTaskByNameStub);
    DrtAbnormityMgrT *drtAbnormityMgrT = &instance->abnormityMgr;
    int32_t countBkp = drtAbnormityMgrT->count;
    drtAbnormityMgrT->count = DB_MAX_ABN_TASK_NUM + 1;
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_NE(GMERR_OK, ret);
    drtAbnormityMgrT->count = countBkp;

    char maxLenTaskName[] = {"AbnormityTest:The task name exceeds the maximum length."};
    ret = strncpy_s(taskTest.taskName, sizeof(maxLenTaskName), maxLenTaskName, sizeof(maxLenTaskName) - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_NE(GMERR_OK, ret);

    clearAllStub();
}

TEST_F(UtDrtAbnormity, runtime_abnormal_error_005)
{
    // destroy test
    char name[] = {"AbnormityTest"};
    uint16_t nameLen = 14;

    DrtTaskInfoT taskTest = {0};
    taskTest.checkPeriodic = 500;
    int32_t ret = strncpy_s(taskTest.taskName, nameLen, name, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest.abnCheck = AbnormityTaskCheckTest;
    taskTest.proCtx = (void *)&g_testCount;

    DrtInstanceT *instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);

    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, "");
    EXPECT_NE(GMERR_OK, ret);

    ret = DrtAbnDestroyTask(&instance->abnormityMgr, DB_INVALID_SHMPTR);
    EXPECT_NE(GMERR_OK, ret);

    // notice test
    instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_EQ(GMERR_OK, ret);
    DrtAbnNoticeT noticeTest;
    char noticeName[] = {"noticeTest"};
    nameLen = 11;
    ret = strncpy_s(noticeTest.noticeName, nameLen, noticeName, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    noticeTest.abnNotify = AbnormityNotifyTest3;
    noticeTest.notifyCtx = NULL;
    noticeTest.noticeLevel = NOTICE_LEVEL_HIGH;

    (void)setStubC((void *)DrtAbnGetNoticeByName, (void *)DrtAbnGetNoticeByNameStub);
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest);
    EXPECT_NE(GMERR_OK, ret);

    (void)setStubC((void *)DrtAbnGetTaskByName, (void *)DrtAbnGetTaskByNameStub);
    ret = DrtAbnNoticeTask(&instance->abnormityMgr, name, &noticeTest);
    EXPECT_NE(GMERR_OK, ret);

    ret = DrtAbnUnNoticeTask(&instance->abnormityMgr, "", noticeName);
    EXPECT_NE(GMERR_OK, ret);

    clearAllStub();

    ret = DrtAbnDestroyTaskByName(&instance->abnormityMgr, name);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtDrtAbnormity, runtime_abnormal_timer_error_006)
{
    char name[] = {"AbnormityTest"};
    uint16_t nameLen = 14;

    DrtTaskInfoT taskTest = {0};
    taskTest.checkPeriodic = 500;
    int32_t ret = strncpy_s(taskTest.taskName, nameLen, name, nameLen - 1);
    EXPECT_EQ(GMERR_OK, ret);
    taskTest.abnCheck = AbnormityTaskCheckTest;
    taskTest.proCtx = (void *)&g_testCount;

    DrtInstanceT *instance = DrtGetInstance(NULL);
    EXPECT_TRUE(NULL != instance);

    (void)setStubC((void *)DrtFreeAbnTask, (void *)DrtFreeAbnTaskStub);

    (void)setStubC((void *)DbTimerRegister, (void *)DrtTimerRegisterStub);
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_NE(GMERR_OK, ret);

    (void)setStubC((void *)strncpy_s, (void *)strncpy_s_stub);
    ret = DrtAbnRegistTask(&instance->abnormityMgr, &taskTest);
    EXPECT_NE(GMERR_OK, ret);

    clearAllStub();
}
