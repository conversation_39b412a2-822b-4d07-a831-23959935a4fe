/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2021-07-23
 */

#include "gtest/gtest.h"
#include "dm_data_print.h"
#include "adpt_string.h"
#include "adpt_time.h"
#include "ctype.h"
#include "db_log.h"
#include <arpa/inet.h>

class UtFormatPrint : public testing::Test {
protected:
    virtual void SetUp()
    {}

    virtual void TearDown()
    {}

    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

// 纯数字串转ipv4
TEST_F(UtFormatPrint, ipv4_01)
{
    char ipv4[] = "1234";
    char dst[20] = {0};
    Status ret = DmIpv4Print(ipv4, 4, dst, 20, false);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("*********", dst);
}

// 目的字符串大小不够ipv4
TEST_F(UtFormatPrint, ipv4_02)
{
    char ipv4[] = "1234";
    char dst[15] = {0};
    Status ret = DmIpv4Print(ipv4, 4, dst, 15, false);
    EXPECT_NE(GMERR_OK, ret);
}

// 全0串转ipv4
TEST_F(UtFormatPrint, ipv4_03)
{
    char ipv4[] = "0000";
    char dst[20] = {0};
    Status ret = DmIpv4Print(ipv4, 4, dst, 20, false);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("0.0.0.0", dst);
}

// 非全数字字符串转ipv4
TEST_F(UtFormatPrint, ipv4_04)
{
    char ipv4[] = "369a";
    char dst[20] = {0};
    Status ret = DmIpv4Print(ipv4, 4, dst, 20, false);
    EXPECT_STREQ("*********", dst);
    EXPECT_EQ(GMERR_OK, ret);
}

// 目的地址为空
TEST_F(UtFormatPrint, ipv4_05)
{
    char ipv4[] = "1234";
    char *dst = NULL;
    Status ret = DmIpv4Print(ipv4, 4, dst, 20, false);
    EXPECT_NE(GMERR_OK, ret);
}

// 数据过长
TEST_F(UtFormatPrint, ipv4_06)
{
    char ipv4[] = "123456";
    char dst[20] = {0};
    Status ret = DmIpv4Print(ipv4, strlen(ipv4), dst, 20, false);
    EXPECT_NE(GMERR_OK, ret);
}

TEST_F(UtFormatPrint, ipv4_07)
{
    char ipv4[4] = {'3'};
    char dst[20] = {0};
    Status ret = DmIpv4Print(ipv4, sizeof(int32_t), dst, 20, false);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("*******", dst);
}

// 正数转ipv6
TEST_F(UtFormatPrint, ipv6_01)
{
    char ipv6[] = "20722625464";
    char dst[64] = {0};
    Status ret = DmIpv6Print(ipv6, sizeof(ipv6), dst, 64);
    printf("%s to ipv6 is %s\n", ipv6, dst);
    EXPECT_EQ(GMERR_OK, ret);
}

// 传入空串
TEST_F(UtFormatPrint, ipv6_02)
{
    char ipv6[] = "20722625464";
    char *dst = NULL;
    Status ret = DmIpv6Print(ipv6, sizeof(ipv6), dst, 64);
    EXPECT_NE(GMERR_OK, ret);
}

// 负数转ipv6，
TEST_F(UtFormatPrint, ipv6_03)
{
    char ipv6[] = "-20722625464";
    char dst[64] = {0};
    Status ret = DmIpv6Print(ipv6, sizeof(ipv6), dst, 64);
    EXPECT_EQ(GMERR_OK, ret);
}

// 整数打印16进制
TEST_F(UtFormatPrint, hex_01)
{
    char hex[] = "50420";
    char dst1[64] = {0};
    char dst2[64] = {0};
    Status ret = DmHexPrint(hex, strlen(hex), dst1, 64, DB_DATATYPE_UINT32);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("0x0000c4f4", dst1);
    DmBytesToHex((uint8_t *)hex, strlen(hex), true, dst2, 64);
    EXPECT_STREQ("0x3530343230", dst2);
}

// 打印字符0的16进制表示
TEST_F(UtFormatPrint, hex_02)
{
    char hex[] = "0";
    char dst[64] = {0};
    Status ret = DmHexPrint(hex, strlen(hex), dst, 64, DB_DATATYPE_STRING);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("0x30", dst);
}

// 传入空串
TEST_F(UtFormatPrint, hex_03)
{
    char hex[] = "0";
    char *dst = NULL;
    Status ret = DmHexPrint(hex, strlen(hex), dst, 64, DB_DATATYPE_STRING);
    EXPECT_NE(GMERR_OK, ret);
}

#ifndef EXPERIMENTAL_GUANGQI
TEST_F(UtFormatPrint, hex_04)
{
    char hex[] = "22698504206546";
    char dst[64] = {0};
    Status ret = DmHexPrint(hex, strlen(hex), dst, 64, DB_DATATYPE_UINT64);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("0x000014a4e847b8d2", dst);
}

TEST_F(UtFormatPrint, hex_05)
{
    char hex[] = "-22698504206546";
    char dst[64] = {0};
    Status ret = DmHexPrint(hex, strlen(hex), dst, 64, DB_DATATYPE_INT64);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("0xffffeb5b17b8472e", dst);
}
#endif

// 数字字符串转时间串
TEST_F(UtFormatPrint, time_01)
{
    char *validTime = (char *)"12345678";
    char dst[TIME_STR_MAX_SIZE] = {0};
    Status ret = DmTimePrint(validTime, strlen(validTime), dst, TIME_STR_MAX_SIZE);
    EXPECT_EQ(GMERR_OK, ret);

    char *invalidTime1 = (char *)"123456789";
    ret = DmTimePrint(invalidTime1, strlen(invalidTime1), dst, TIME_STR_MAX_SIZE);
    EXPECT_NE(GMERR_OK, ret);
    char *invalidTime2 = (char *)"1234567";
    ret = DmTimePrint(invalidTime2, strlen(invalidTime2), dst, TIME_STR_MAX_SIZE);
    EXPECT_NE(GMERR_OK, ret);
}

// 数字字符串转时间串
TEST_F(UtFormatPrint, time_02)
{
    char time[] = "10";
    char *dst = NULL;
    Status ret = DmTimePrint(time, 8, dst, TIME_STR_MAX_SIZE);
    EXPECT_NE(GMERR_OK, ret);
}
// 单字符转时间串
TEST_F(UtFormatPrint, time_03)
{
    char time[] = "C";
    char dst[TIME_STR_MAX_SIZE] = {0};
    Status ret = DmTimePrint(time, strlen(time), dst, strlen(dst));
    EXPECT_NE(GMERR_OK, ret);
}

// 6个byte字符串转MAC
TEST_F(UtFormatPrint, mac_01)
{
    char time[] = "123456";
    char dst[32] = {0};
    Status ret = DmMacPrint(time, strlen(time), dst, 32);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("31-32-33-34-35-36", dst);
}

// 传入空串
TEST_F(UtFormatPrint, mac_02)
{
    char time[] = "123456";
    char *dst = NULL;
    Status ret = DmMacPrint(time, strlen(time), dst, 32);
    EXPECT_NE(GMERR_OK, ret);
}

// 字符串长度不符合max要求
TEST_F(UtFormatPrint, mac_03)
{
    char time[] = "1234561";
    char dst[32] = {0};
    Status ret = DmMacPrint(time, strlen(time), dst, 32);
    EXPECT_NE(GMERR_OK, ret);
}

// 字符串无非打印字符
TEST_F(UtFormatPrint, string_01)
{
    char time[] = "1234561-*#/?";
    char dst[128] = {0};
    Status ret = DmStringPrint(time, strlen(time), dst, 128);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("1234561-*#/?", dst);
}

// 传入空串
TEST_F(UtFormatPrint, string_02)
{
    char time[] = "1234561\073";
    char *dst = NULL;
    Status ret = DmStringPrint(time, strlen(time), dst, 128);
    EXPECT_NE(GMERR_OK, ret);
}
