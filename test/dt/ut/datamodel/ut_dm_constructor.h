/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: ut file for dm constructor
 * Author: hufangzhou
 * Create: 2021-3-17
 */

#ifndef UT_DM_CONSTRUCTOR_H
#define UT_DM_CONSTRUCTOR_H

#ifdef __cplusplus
extern "C" {
#endif

#include "dm_data_basic.h"
#include "dm_meta_schema.h"
#include "dm_data_print.h"
#include "dm_data_prop.h"
#include "dm_meta_prop_label.h"

extern DbMemCtxT *g_topShmemCtx;

DmPropertySchemaT *GetProperty(DbMemCtxT *memctx, uint32_t propertyNum);

DmPropertySchemaT *GetPropertyWithBitfied(DbMemCtxT *memctx, uint32_t propertyNum);

DmVertexLabelT *GetVertexLabelSimpleTreeSchema(DbMemCtxT *memctx);

DmVertexLabelT *GetVertexLabelSimpleTreeSchema2(DbMemCtxT *memctx);

DmVertexLabelT *GetVertexLabelTreeSchemaWith3Levels(DbMemCtxT *memctx);

DmVertexLabelT *GetVertexLabelTreeSchema(DbMemCtxT *memctx);

DmVertexLabelT *GetVertexLabelMultiPropertiesTreeSchema(DbMemCtxT *memctx);

/**
 * @brief 构建带数组嵌套的vertexLabel
 *
 * @param arrayOrVector true表示数组嵌套；false表示vector嵌套
 * @return DmVertexLabelT*
 */
DmVertexLabelT *GetNestVertexLabelTreeSchema(DbMemCtxT *memctx, bool arrayOrVector);

DmVertexLabelT *GetNestVertexLabelTreeSchemaWithNoCopy(DbMemCtxT *memctx, bool arrayOrVector);

DmVertexLabelT *GetVertexLabelMergeTreeSchema(DbMemCtxT *memctx);

void SetArrayNestVertexValue(DmVertexT *vertex, uint32_t fixedValue, char *strValue);

void SetVectorNestVertexValue(DmVertexT *vertex, uint32_t fixedValue, char *strValue, bool isChangedStr);

void SetVectorNestVertexValueDelta(DmVertexT *vertex, uint32_t fixedValue, char *strValue);

void InitSysPropertyByType(DbMemCtxT *memctx, DmSysPropeTypeE sysPropeType, DmPropertySchemaT *property,
    uint32_t propeId, const char *name = SYS_PROPE_CHECK_VERSION_NAME);

DmPropertySchemaT *GetPropertyTreeSchemaforVarintCheck(DbMemCtxT *memctx, uint32_t propertyNum);

// 含有系统属性，只能在vertex层
DmPropertySchemaT *GetPropertyWithLongString(DbMemCtxT *memctx, uint32_t propertyNum);

/**
 * @brief 初始化一个StringBuilder对象
 * @param sb 待初始化的sb对象
 * @param memCtx 该memCtx会用于该sb的所有内存分配和释放操作
 */
static inline void DmSbInit(StringBuilderT *sb, DbMemCtxT *memCtx)
{
    errno_t err = memset_s(sb, sizeof(StringBuilderT), 0, sizeof(StringBuilderT));
    DB_ASSERT(err == EOK);
    sb->memCtx = memCtx;
}

#ifdef __cplusplus
}
#endif

#endif
