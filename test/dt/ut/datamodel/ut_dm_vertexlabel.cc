#include <limits.h>
#include <jansson.h>
#include "gtest/gtest.h"
#include <vector>
#include <time.h>
#include <stdlib.h>
#include <string>
#include "adpt_atomic.h"
#include <securec.h>
#include "common_init.h"
#include "adpt_memory.h"
#include "db_mem_context.h"
#include "dm_meta_prop_label.h"
#include "ut_dm_constructor.h"
#include "ut_dm_common.h"
#if defined(FEATURE_SQL)
#include "db_json.h"
#else
#include "db_json_common.h"
#endif

using namespace std;
static const uint32_t VERTEX_LABEL_DESERI_INCORRECT_BUFFER_TEST_LOOP = 100;
static const uint32_t VERTEX_LABEL_INCORRECT_BUFFER_MAX_LENGTH = 5000;
static const uint32_t UINT8_MAX_LENGTH = 256;

#ifdef __cplusplus
extern "C" {
#endif

extern Status InvParseVlSchemaJson(const DmVertexLabelT *vertexLabel, char **jsonStr);
extern Status InvParseVlConfigJson(const DmVertexLabelT *vertexLabel, char **jsonStr);
extern Status JsonObjectAppendTspName(DbInstanceHdT dbInstance, DbJsonT *schemaObj, char *key, uint32_t tspId);
extern Status JsonObjectSetString(DbJsonT *object, char *key, char const *value);
extern void *jsonp_malloc(size_t size) JANSSON_ATTRS((warn_unused_result));
extern void jsonp_free(void *ptr);

#ifdef __cplusplus
}
#endif

DmPropertySchemaT *Get3Property1(DbMemCtxT *memCtx)
{
    DmPropertySchemaT *property = AllocProperties(memCtx, 3);
    property[0].propeId = 0;
    property[0].isValid = true;
    property[0].dataType = DB_DATATYPE_UINT16;
    property[0].nameLen = DM_STR_LEN("c1");
    property[0].name = (char *)DbDynMemCtxAlloc(memCtx, property[0].nameLen);
    strcpy(property[0].name, "c1");
    property[0].isFixed = true;
    property[0].size = 2;

    property[1].propeId = 1;
    property[1].isValid = true;
    property[1].dataType = DB_DATATYPE_UINT32;
    property[1].nameLen = DM_STR_LEN("c2");
    property[1].name = (char *)DbDynMemCtxAlloc(memCtx, property[1].nameLen);
    strcpy(property[1].name, "c2");
    property[1].isFixed = true;
    property[1].size = 4;

    property[2].propeId = 2;
    property[2].isValid = true;
    property[2].dataType = DB_DATATYPE_UINT32;
    property[2].nameLen = DM_STR_LEN("c3");
    property[2].name = (char *)DbDynMemCtxAlloc(memCtx, property[2].nameLen);
    strcpy(property[2].name, "c3");
    property[2].isFixed = true;
    property[2].size = 4;
    return property;
}

DmVlIndexLabelT *GetIndex1(DbMemCtxT *memCtx)
{
    DmVlIndexLabelT *index = (DmVlIndexLabelT *)DbDynMemCtxAlloc(memCtx, sizeof(DmVlIndexLabelT));
    (void)memset_s(index, sizeof(DmVlIndexLabelT), 0, sizeof(DmVlIndexLabelT));
    index->idxLabelBase.dbId = 1;
    index->idxLabelBase.indexId = 12;
    index->idxLabelBase.indexConstraint = PRIMARY;
    index->idxLabelBase.srcLabelId = 4;
    index->propeNum = 3;
    index->nullInfoBytes = DmGetNullInfoBytes(index->propeNum);
    index->commentLen = strlen("it is index") + 1;
    index->comment = (char *)DbDynMemCtxAlloc(memCtx, index->commentLen);
    strcpy(index->comment, "it is index");
    index->indexNameLen = strlen("index name") + 1;
    index->indexName = (char *)DbDynMemCtxAlloc(memCtx, index->indexNameLen);
    strcpy(index->indexName, "index name");
    index->idxLabelBase.srcLabelNameLen = strlen("source label name") + 1;
    index->idxLabelBase.srcLabelName = (char *)DbDynMemCtxAlloc(memCtx, index->idxLabelBase.srcLabelNameLen);
    strcpy(index->idxLabelBase.srcLabelName, "source label name");
    index->idxLabelBase.indexType = HASH_INDEX;
    index->properties = Get3Property1(memCtx);
    index->propIds = AllocPropIds(memCtx, 3);
    index->indexFilter.conditionNum = 1;
    index->indexFilter.operatorType = DM_LOGICAL_OPERATOR_AND;
    index->indexFilter.conditions = (DmFilterConditionT *)DbDynMemCtxAlloc(memCtx, sizeof(DmFilterConditionT));
    (void)memset_s(index->indexFilter.conditions, sizeof(DmFilterConditionT), 0, sizeof(DmFilterConditionT));
    index->indexFilter.conditions->value.type = DB_DATATYPE_FIXED;
    index->indexFilter.conditions->value.value.strAddr = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("str"));
    errno_t err = strcpy_s((char *)index->indexFilter.conditions->value.value.strAddr, DM_STR_LEN("str"), "str");
    DB_ASSERT(err == EOK);
    index->indexFilter.conditions->value.value.length = DM_STR_LEN("str");
    index->indexFilter.conditions->compareType = DM_COND_EQUAL;
    index->indexFilter.conditions->property = AllocProperties(memCtx, 1);
    index->indexFilter.conditions->property[0].propeId = 0;
    index->indexFilter.conditions->property[0].isValid = true;
    index->indexFilter.conditions->property[0].dataType = DB_DATATYPE_UINT16;
    index->indexFilter.conditions->property[0].nameLen = DM_STR_LEN("c1");
    index->indexFilter.conditions->property[0].name = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("c1"));
    err = strcpy_s(index->indexFilter.conditions->property[0].name, DM_STR_LEN("c1"), "c1");
    DB_ASSERT(err == EOK);
    index->indexFilter.conditions->property[0].isFixed = true;
    index->indexFilter.conditions->property[0].size = 2;
    return index;
}

DmSchemaT *GetSchemaT3property1(DbMemCtxT *memCtx)
{
    DmSchemaT *schema = (DmSchemaT *)DbDynMemCtxAlloc(memCtx, sizeof(DmSchemaT));
    (void)memset_s(schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->isFlat = true;
    schema->propeNum = 3;
    schema->properties = Get3Property1(memCtx);
    schema->nodeNum = 0;
    schema->nodes = NULL;
    return schema;
}

void InitialVertexLabelValue1(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    srand((unsigned)time(NULL));
    vertexLabel->metaCommon.dbId = rand();
    vertexLabel->metaCommon.metaId = rand();
    vertexLabel->metaCommon.metaName = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("printstring().c_str()"));
    ASSERT_FALSE(vertexLabel->metaCommon.metaName == NULL);
    strcpy(vertexLabel->metaCommon.metaName, "printstring().c_str()");
    vertexLabel->metaVertexLabel->comment = NULL;
    const char *json = "printstring().c_str()";
    vertexLabel->metaVertexLabel->labelJson = (char *)DbDynMemCtxAlloc(memCtx, strlen(json) + 1);
    strcpy(vertexLabel->metaVertexLabel->labelJson, "printstring().c_str()");
    vertexLabel->metaVertexLabel->configJson = NULL;
    vertexLabel->metaCommon.version = rand();
    vertexLabel->metaVertexLabel->secIndexNum = 1;
    VertexLabelCommonInfoT *commonInfo =
        (VertexLabelCommonInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(VertexLabelCommonInfoT));
    memset_s(commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    vertexLabel->commonInfo = commonInfo;
    vertexLabel->commonInfo->refCount = 1;
    vertexLabel->commonInfo->edgeLabelNum = 0;
    vertexLabel->commonInfo->relatedEdgeLabels = NULL;
    vertexLabel->commonInfo->labelLatchShmAddr = DB_INVALID_SHMPTR;
    vertexLabel->commonInfo->heapInfo.maxVertexNum = rand();
    vertexLabel->metaVertexLabel->pkIndex = GetIndex1(memCtx);
    vertexLabel->metaVertexLabel->secIndexes = GetIndex1(memCtx);
    vertexLabel->metaVertexLabel->schema = GetSchemaT3property1(memCtx);
    vertexLabel->commonInfo->heapInfo.heapShmAddr.offset = rand();
    vertexLabel->commonInfo->heapInfo.heapShmAddr.segId = rand();
    uint32_t creatorLen = 5;
    vertexLabel->commonInfo->creator = (char *)DbDynMemCtxAlloc(memCtx, creatorLen);
    errno_t err = strcpy_s(vertexLabel->commonInfo->creator, creatorLen, "root");
    DB_ASSERT(err == EOK);
    vertexLabel->commonInfo->metaInfoShm = DB_INVALID_SHMPTR;
}

void InitialVertexLabelValueWithDatalogResourceInfo(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    InitialVertexLabelValue1(memCtx, vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    commonInfo->datalogLabelInfo = (DmDatalogLabelInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDatalogLabelInfoT));
    errno_t err = memset_s(commonInfo->datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->labelType = DM_DTL_RESOURCE_SEQUENTIAL;
    commonInfo->datalogLabelInfo->resourceInfo =
        (DmDtlResourceInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDtlResourceInfoT));
    (void)memset_s(
        commonInfo->datalogLabelInfo->resourceInfo, sizeof(DmDtlResourceInfoT), 0x00, sizeof(DmDtlResourceInfoT));
    commonInfo->datalogLabelInfo->resourceInfo->inputCount = 2;
    commonInfo->datalogLabelInfo->resourceInfo->inputPropIds =
        (uint32_t *)DbDynMemCtxAlloc(memCtx, 2 * sizeof(uint32_t));
    commonInfo->datalogLabelInfo->resourceInfo->inputPropIds[0] = 0;
    commonInfo->datalogLabelInfo->resourceInfo->inputPropIds[1] = 1;
    commonInfo->datalogLabelInfo->resourceInfo->outputCount = 2;
    commonInfo->datalogLabelInfo->resourceInfo->outputPropIds =
        (uint32_t *)DbDynMemCtxAlloc(memCtx, 2 * sizeof(uint32_t));
    commonInfo->datalogLabelInfo->resourceInfo->outputPropIds[0] = 2;
    commonInfo->datalogLabelInfo->resourceInfo->outputPropIds[1] = 6;
    commonInfo->datalogLabelInfo->resourceInfo->defaultResVals =
        (DbValueT *)DbDynMemCtxAlloc(memCtx, 2 * sizeof(DbValueT));
    commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[0].type = DB_DATATYPE_INT8;
    commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[0].value.byteValue = 2;
    commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[1].type = DB_DATATYPE_STRING;
    commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[1].value.strAddr =
        (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("c5"));
    err = strcpy_s(
        (char *)commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[1].value.strAddr, DM_STR_LEN("c5"), "c5");
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[1].value.length = DM_STR_LEN("c5");
}

void InitialVertexLabelValueWithDatalogUpdateInfo(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    InitialVertexLabelValue1(memCtx, vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    commonInfo->datalogLabelInfo = (DmDatalogLabelInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDatalogLabelInfoT));
    errno_t err = memset_s(commonInfo->datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->unionTableNum = 2;
    commonInfo->datalogLabelInfo->unionTableNames = (DmShmTextT *)DbDynMemCtxAlloc(memCtx, sizeof(DmShmTextT) * 2);
    commonInfo->datalogLabelInfo->unionTableNames[0].str = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("u0"));
    err = strcpy_s((char *)commonInfo->datalogLabelInfo->unionTableNames[0].str, DM_STR_LEN("u0"), "u0");
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->unionTableNames[0].len = DM_STR_LEN("u0");
    commonInfo->datalogLabelInfo->unionTableNames[1].str = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("u1"));
    err = strcpy_s((char *)commonInfo->datalogLabelInfo->unionTableNames[1].str, DM_STR_LEN("u1"), "u1");
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->unionTableNames[1].len = DM_STR_LEN("u1");
    commonInfo->datalogLabelInfo->labelType = DM_DTL_UPDATE;
    commonInfo->datalogLabelInfo->updateInfo = (DmDtlUpdateInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDtlUpdateInfoT));
    (void)memset_s(commonInfo->datalogLabelInfo->updateInfo, sizeof(DmDtlUpdateInfoT), 0x00, sizeof(DmDtlUpdateInfoT));
    commonInfo->datalogLabelInfo->updateInfo->updatePartial = true;
    commonInfo->datalogLabelInfo->updateInfo->updateByRank = true;
    commonInfo->datalogLabelInfo->updateInfo->cmpUdfName = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("c5"));
    err = strcpy_s((char *)commonInfo->datalogLabelInfo->updateInfo->cmpUdfName, DM_STR_LEN("c5"), "c5");
    DB_ASSERT(err == EOK);
}

void InitialVertexLabelValueWithDatalogTbmInfo(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    InitialVertexLabelValue1(memCtx, vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    commonInfo->datalogLabelInfo = (DmDatalogLabelInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDatalogLabelInfoT));
    errno_t err = memset_s(commonInfo->datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->labelType = DM_DTL_TBM;
    commonInfo->datalogLabelInfo->tbmUdfName = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("c5"));
    err = strcpy_s((char *)commonInfo->datalogLabelInfo->tbmUdfName, DM_STR_LEN("c5"), "c5");
    DB_ASSERT(err == EOK);
}

void InitialVertexLabelValueWithDatalogMsgNotifyInfo(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    InitialVertexLabelValue1(memCtx, vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    commonInfo->datalogLabelInfo = (DmDatalogLabelInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmDatalogLabelInfoT));
    errno_t err = memset_s(commonInfo->datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
    DB_ASSERT(err == EOK);
    commonInfo->datalogLabelInfo->labelType = DM_DTL_MSG_NOTIFY;
    commonInfo->datalogLabelInfo->msgNotifyUdfName = (char *)DbDynMemCtxAlloc(memCtx, DM_STR_LEN("c5"));
    err = strcpy_s((char *)commonInfo->datalogLabelInfo->msgNotifyUdfName, DM_STR_LEN("c5"), "c5");
    DB_ASSERT(err == EOK);
}

void InitialVertexLabelValueWithResColInfo(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    InitialVertexLabelValue1(memCtx, vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    commonInfo->resColInfo = (DmResColInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmResColInfoT));
    errno_t err = memset_s(commonInfo->resColInfo, sizeof(DmResColInfoT), 0, sizeof(DmResColInfoT));
    DB_ASSERT(err == EOK);
    DmInitResColInfo(commonInfo->resColInfo);
}

DbMemCtxT *memCtxVertexLabel = NULL;
DbMemCtxT *jsonMemCtx = NULL;
class UtDmVertexLabel : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        CommonInit();
        DbMemCtxArgsT args = {0};
        memCtxVertexLabel =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo((DbMemCtxT *)memCtxVertexLabel);
        jsonMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "json memory context", &args);
        EXPECT_TRUE(jsonMemCtx != NULL);
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx((DbMemCtxT *)memCtxVertexLabel);
        DbDeleteDynMemCtx((DbMemCtxT *)jsonMemCtx);
        CommonRelease();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

bool HeapInfoEquals(DmHeapInfoT *heapInfo1, DmHeapInfoT *heapInfo2)
{
    if (PtrBothNull(heapInfo1, heapInfo2)) {
        return true;
    }
    if (!PtrBothNonNull(heapInfo1, heapInfo2)) {
        return false;
    }
    if (!IsShmemPtrEqual(heapInfo1->heapShmAddr, heapInfo2->heapShmAddr)) {
        return false;
    }
    if (heapInfo1->maxVertexNum != heapInfo2->maxVertexNum) {
        return false;
    }
    if (heapInfo1->trxId != heapInfo2->trxId) {
        return false;
    }
    if (heapInfo1->trxCommitTime != heapInfo2->trxCommitTime) {
        return false;
    }
    if (heapInfo1->ccType != heapInfo2->ccType) {
        return false;
    }
    if (heapInfo1->maxVertexNumCheck != heapInfo2->maxVertexNumCheck) {
        return false;
    }
    if (heapInfo1->needDefragmentation != heapInfo2->needDefragmentation) {
        return false;
    }
    if (heapInfo1->supportUndeterminedLength != heapInfo2->supportUndeterminedLength) {
        return false;
    }
    if (heapInfo1->isolationLevel != heapInfo2->isolationLevel) {
        return false;
    }
    if (heapInfo1->trxType != heapInfo2->trxType) {
        return false;
    }
    return true;
}

bool ResColInfoEquals(DmResColInfoT *resColInfo1, DmResColInfoT *resColInfo2)
{
    if (PtrBothNull(resColInfo1, resColInfo2)) {
        return true;
    }
    if (!PtrBothNonNull(resColInfo1, resColInfo2)) {
        return false;
    }
    if (resColInfo1->resColCount != resColInfo2->resColCount) {
        return false;
    }
    if (!Uint32ArrayEquals(resColInfo1->resPropeId, resColInfo2->resPropeId, DM_RES_COL_MAX_COUNT)) {
        return false;
    }

    if (!IsShmemPtrEqual(resColInfo1->resColPool, resColInfo2->resColPool)) {
        return false;
    }
    if (resColInfo1->labelLatchId != resColInfo2->labelLatchId) {
        return false;
    }
    if (resColInfo1->labelLatchVersionId != resColInfo2->labelLatchVersionId) {
        return false;
    }
    return true;
}

bool EdgeLabelInfoArrEquals(DmEdgeLabelInfoT *edgeLabelInfos1[], DmEdgeLabelInfoT *edgeLabelInfos2[], uint32_t num)
{
    if (PtrBothNull(edgeLabelInfos1, edgeLabelInfos2)) {
        return true;
    }
    if (!PtrBothNonNull(edgeLabelInfos1, edgeLabelInfos2)) {
        return false;
    }
    for (uint32_t i = 0; i < num; i++) {
        DmEdgeLabelInfoT *edgeLabelInfo1 = edgeLabelInfos1[i];
        DmEdgeLabelInfoT *edgeLabelInfo2 = edgeLabelInfos2[i];
        if (edgeLabelInfo1->edgeLabelId != edgeLabelInfo2->edgeLabelId) {
            return false;
        }
        if (edgeLabelInfo1->edgeNameLen != edgeLabelInfo2->edgeNameLen) {
            return false;
        }
        if (!StringEquals(edgeLabelInfo1->edgeName, edgeLabelInfo2->edgeName)) {
            return false;
        }
    }
    return true;
}

bool DtlResInfoEquals(DmDtlResourceInfoT *dtlResInfo1, DmDtlResourceInfoT *dtlResInfo2)
{
    if (PtrBothNull(dtlResInfo1, dtlResInfo2)) {
        return true;
    }
    if (!PtrBothNonNull(dtlResInfo1, dtlResInfo2)) {
        return false;
    }
    if (dtlResInfo1->inputCount != dtlResInfo2->inputCount) {
        return false;
    }
    if (!Uint32ArrayEquals(dtlResInfo1->inputPropIds, dtlResInfo2->inputPropIds, dtlResInfo1->inputCount)) {
        return false;
    }
    if (dtlResInfo1->outputCount != dtlResInfo2->outputCount) {
        return false;
    }
    if (!Uint32ArrayEquals(dtlResInfo1->outputPropIds, dtlResInfo2->outputPropIds, dtlResInfo1->outputCount)) {
        return false;
    }
    for (uint32_t i = 0; i < dtlResInfo1->outputCount; i++) {
        if (!DmValueIsEqual(&dtlResInfo1->defaultResVals[i], &dtlResInfo2->defaultResVals[i])) {
            return false;
        }
    }
    if (dtlResInfo1->resColId != dtlResInfo2->resColId) {
        return false;
    }
    return true;
}

bool DatalogLabelInfoEquals(DmDatalogLabelInfoT *dtlInfo1, DmDatalogLabelInfoT *dtlInfo2)
{
    if (PtrBothNull(dtlInfo1, dtlInfo2)) {
        return true;
    }
    if (!PtrBothNonNull(dtlInfo1, dtlInfo2)) {
        return false;
    }
    if (dtlInfo1->labelType != dtlInfo2->labelType) {
        return false;
    }
    if (dtlInfo1->inoutType != dtlInfo2->inoutType) {
        return false;
    }
    if (dtlInfo1->postProc != dtlInfo2->postProc) {
        return false;
    }
    if (dtlInfo1->soId != dtlInfo2->soId) {
        return false;
    }
    if (dtlInfo1->timeoutPropId != dtlInfo2->timeoutPropId) {
        return false;
    }
    if (dtlInfo1->timeoutIndexSeqNum != dtlInfo2->timeoutIndexSeqNum) {
        return false;
    }
    if (!StringEquals(dtlInfo1->timeoutUdfName, dtlInfo2->timeoutUdfName)) {
        return false;
    }
    if (dtlInfo1->withTimeout != dtlInfo2->withTimeout) {
        return false;
    }
    if (dtlInfo1->isAllFieldsPk != dtlInfo2->isAllFieldsPk) {
        return false;
    }
    if (dtlInfo1->withVariant != dtlInfo2->withVariant) {
        return false;
    }
    if (dtlInfo1->unionTableNum != dtlInfo2->unionTableNum) {
        return false;
    }
    for (uint32_t i = 0; i < dtlInfo1->unionTableNum; i++) {
        if (!StringEquals(dtlInfo1->unionTableNames[i].str, dtlInfo2->unionTableNames[i].str)) {
            return false;
        }
    }
    if (dtlInfo1->batchMsgSize != dtlInfo2->batchMsgSize) {
        return false;
    }
    switch (dtlInfo1->labelType) {
        case DM_DTL_RESOURCE_SEQUENTIAL:
        case DM_DTL_RESOURCE_PUBSUB:
            if (!DtlResInfoEquals(dtlInfo1->resourceInfo, dtlInfo2->resourceInfo)) {
                return false;
            }
            break;
        case DM_DTL_UPDATE:
            if (dtlInfo1->updateInfo->updatePartial != dtlInfo2->updateInfo->updatePartial) {
                return false;
            }
            if (dtlInfo1->updateInfo->updateByRank != dtlInfo2->updateInfo->updateByRank) {
                return false;
            }
            if (!StringEquals(dtlInfo1->updateInfo->cmpUdfName, dtlInfo2->updateInfo->cmpUdfName)) {
                return false;
            }
            break;
        case DM_DTL_TRANSIENT_FIELD:
            if (dtlInfo1->transientPropId != dtlInfo2->transientPropId) {
                return false;
            }
            break;
        case DM_DTL_TBM:
            if (!StringEquals(dtlInfo1->tbmUdfName, dtlInfo2->tbmUdfName)) {
                return false;
            }
            break;
        case DM_DTL_MSG_NOTIFY:
            if (!StringEquals(dtlInfo1->msgNotifyUdfName, dtlInfo2->msgNotifyUdfName)) {
                return false;
            }
            break;
        default:
            break;
    }
    return true;
}

bool VertexLabelCommonInfoEquals(VertexLabelCommonInfoT *commonInfo1, VertexLabelCommonInfoT *commonInfo2)
{
    if (PtrBothNull(commonInfo1, commonInfo2)) {
        return true;
    }
    if (!PtrBothNonNull(commonInfo1, commonInfo1)) {
        return false;
    }
    if (!StringEquals(commonInfo1->creator, commonInfo2->creator)) {
        return false;
    }
    if (commonInfo1->vertexLabelLatchId != commonInfo2->vertexLabelLatchId) {
        return false;
    }
    if (commonInfo1->vertexLabelLatchVersionId != commonInfo2->vertexLabelLatchVersionId) {
        return false;
    }
    if (!HeapInfoEquals(&commonInfo1->heapInfo, &commonInfo2->heapInfo)) {
        return false;
    }
    if (!IsShmemPtrEqual(commonInfo1->labelLatchShmAddr, commonInfo2->labelLatchShmAddr)) {
        return false;
    }
    if (!ResColInfoEquals(commonInfo1->resColInfo, commonInfo2->resColInfo)) {
        return false;
    }

    if (commonInfo1->autoIncrPropNum != commonInfo2->autoIncrPropNum) {
        return false;
    }

    for (uint32_t i = 0; i < commonInfo1->autoIncrPropNum; ++i) {
        if (commonInfo1->autoIncrPropInfo[i].type != commonInfo2->autoIncrPropInfo[i].type) {
            return false;
        }
        if (commonInfo1->autoIncrPropInfo[i].autoIncrPropId != commonInfo2->autoIncrPropInfo[i].autoIncrPropId) {
            return false;
        }
        if (commonInfo1->autoIncrPropInfo[i].autoIncrValue != commonInfo2->autoIncrPropInfo[i].autoIncrValue) {
            return false;
        }
        if (commonInfo1->autoIncrPropInfo[i].autoIncrMaxValue != commonInfo2->autoIncrPropInfo[i].autoIncrMaxValue) {
            return false;
        }
        if (commonInfo1->autoIncrPropInfo[i].autoIncrStartValue !=
            commonInfo2->autoIncrPropInfo[i].autoIncrStartValue) {
            return false;
        }
    }

    if (!DatalogLabelInfoEquals(commonInfo1->datalogLabelInfo, commonInfo2->datalogLabelInfo)) {
        return false;
    }
    if (commonInfo1->edgeLabelNum != commonInfo2->edgeLabelNum) {
        return false;
    }
    DmEdgeLabelInfoT **relatedEdgeLabels1 = &commonInfo1->relatedEdgeLabels;
    DmEdgeLabelInfoT **relatedEdgeLabels2 = &commonInfo2->relatedEdgeLabels;
    if (!EdgeLabelInfoArrEquals(relatedEdgeLabels1, relatedEdgeLabels2, commonInfo1->edgeLabelNum)) {
        return false;
    }
    if (commonInfo1->edgeLabelNum != commonInfo2->edgeLabelNum) {
        return false;
    }
    if (commonInfo1->refCount != commonInfo2->refCount) {
        return false;
    }
    if (commonInfo1->pushAgeVertexBatch != commonInfo2->pushAgeVertexBatch) {
        return false;
    }
    if (commonInfo1->objPrivVersion != commonInfo2->objPrivVersion) {
        return false;
    }
    if (commonInfo1->degradedTimes != commonInfo2->degradedTimes) {
        return false;
    }
    if (commonInfo1->maxVersion != commonInfo2->maxVersion) {
        return false;
    }
    if (commonInfo1->hasUpd != commonInfo2->hasUpd) {
        return false;
    }
    if (commonInfo1->versionCnt != commonInfo2->versionCnt) {
        return false;
    }
    if (commonInfo1->canSub != commonInfo2->canSub) {
        return false;
    }
    if (commonInfo1->dlrInfo.incrId != commonInfo2->dlrInfo.incrId) {
        return false;
    }
    if (commonInfo1->dlrInfo.isDataSyncLabel != commonInfo2->dlrInfo.isDataSyncLabel) {
        return false;
    }
    if (!IsShmemPtrEqual(commonInfo1->statusMergeList, commonInfo2->statusMergeList)) {
        return false;
    }
    if (!PtrBothNull(commonInfo1->accCheckAddr, commonInfo2->accCheckAddr) &&
        !PtrBothNonNull(commonInfo1->accCheckAddr, commonInfo2->accCheckAddr)) {
        return false;
    }
    if (PtrBothNonNull(commonInfo1->accCheckAddr, commonInfo2->accCheckAddr) &&
        !IsShmemPtrEqual(commonInfo1->accCheckShm, commonInfo2->accCheckShm)) {
        return false;
    }
    return true;
}

// vertexDesc、 memCtx 不检验
bool VertexLabelEquals(DmVertexLabelT *vl1, DmVertexLabelT *vl2)
{
    if (PtrBothNull(vl1, vl2)) {
        return true;
    }
    if (!PtrBothNonNull(vl1, vl2)) {
        return false;
    }
    if (!MetaCommonEquals(&vl1->metaCommon, &vl2->metaCommon)) {
        return false;
    }
    if (!VertexLabelCommonInfoEquals(vl1->commonInfo, vl2->commonInfo)) {
        return false;
    }
    if (!StringEquals(vl1->metaVertexLabel->topRecordName, vl2->metaVertexLabel->topRecordName)) {
        return false;
    }
    if (!StringEquals(vl1->metaVertexLabel->comment, vl2->metaVertexLabel->comment)) {
        return false;
    }
    if (!StringEquals(vl1->metaVertexLabel->labelJson, vl2->metaVertexLabel->labelJson)) {
        return false;
    }
    if (!StringEquals(vl1->metaVertexLabel->configJson, vl2->metaVertexLabel->configJson)) {
        return false;
    }
    if (!SchemaEquals(vl1->metaVertexLabel->schema, vl2->metaVertexLabel->schema)) {
        return false;
    }
    if (!IndexLabelEquals(vl1->metaVertexLabel->pkIndex, vl2->metaVertexLabel->pkIndex)) {
        return false;
    }
    if (vl1->metaVertexLabel->secIndexNum != vl2->metaVertexLabel->secIndexNum) {
        return false;
    }
    if (!IndexLabelEquals(vl1->metaVertexLabel->secIndexes, vl2->metaVertexLabel->secIndexes)) {
        return false;
    }
    if (vl1->metaVertexLabel->containerType != vl2->metaVertexLabel->containerType) {
        return false;
    }
    if (vl1->metaVertexLabel->isFixedLabel != vl2->metaVertexLabel->isFixedLabel) {
        return false;
    }
    if (vl1->metaVertexLabel->hcIndexNum != vl2->metaVertexLabel->hcIndexNum) {
        return false;
    }
    if (!IsShmemPtrEqual(vl1->metaVertexLabel->containerShmAddr, vl2->metaVertexLabel->containerShmAddr)) {
        return false;
    }
    if (vl1->metaVertexLabel->defaultIndexLabelCfg.hashIdxType !=
        vl2->metaVertexLabel->defaultIndexLabelCfg.hashIdxType) {
        return false;
    }
    if (vl1->metaVertexLabel->defaultIndexLabelCfg.initHashCapacity !=
        vl2->metaVertexLabel->defaultIndexLabelCfg.initHashCapacity) {
        return false;
    }
    if (vl1->metaVertexLabel->labelSubsType != vl2->metaVertexLabel->labelSubsType) {
        return false;
    }
    if (vl1->metaVertexLabel->uuid != vl2->metaVertexLabel->uuid) {
        return false;
    }
    if (vl1->metaVertexLabel->vertexLabelType != vl2->metaVertexLabel->vertexLabelType) {
        return false;
    }
    if (vl1->metaVertexLabel->checkValidity != vl2->metaVertexLabel->checkValidity) {
        return false;
    }
    if (vl1->metaVertexLabel->disableSubBackPressure != vl2->metaVertexLabel->disableSubBackPressure) {
        return false;
    }
    if (vl1->metaVertexLabel->subFlowControlSleepTime != vl2->metaVertexLabel->subFlowControlSleepTime) {
        return false;
    }
    if (vl1->metaVertexLabel->labelLevel != vl2->metaVertexLabel->labelLevel) {
        return false;
    }
    if (vl1->metaVertexLabel->vlSize != vl2->metaVertexLabel->vlSize) {
        return false;
    }
    return true;
}

static void TestDmCopySeriDeseriVertexLabel(DmVertexLabelT *vertexLabel)
{
    // copy
    DmVertexLabelT *retVertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, DmCopyVertexLabel(vertexLabel, retVertexLabel));
    // copy 后是否相等
    EXPECT_TRUE(VertexLabelEquals(vertexLabel, retVertexLabel));
    DmDestroyVertexLabel(retVertexLabel);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));
    // 反序列化
    Status ret =
        DmDeSerializeVertexLabelWithMemCtx((DbMemCtxT *)memCtxVertexLabel, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    // 反序列化之后判断是否相等
    EXPECT_TRUE(VertexLabelEquals(vertexLabel, retVertexLabel));
    DmDestroyVertexLabel(retVertexLabel);
    DbDynMemFree(buffer.buf);
}

TEST_F(UtDmVertexLabel, VertexLabelTestSerilizeAndDeseriWithMemCtx)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValue1(memCtxVertexLabel, vertexLabel);
    vertexLabel->commonInfo->edgeLabelNum = 1;
    vertexLabel->commonInfo->relatedEdgeLabels =
        (DmEdgeLabelInfoT *)DbDynMemCtxAlloc(memCtxVertexLabel, sizeof(DmEdgeLabelInfoT));
    memset_s(vertexLabel->commonInfo->relatedEdgeLabels, sizeof(DmEdgeLabelInfoT), 0, sizeof(DmEdgeLabelInfoT));
    vertexLabel->commonInfo->relatedEdgeLabels[0].edgeLabelId = 999;
    vertexLabel->commonInfo->relatedEdgeLabels[0].edgeNameLen = DM_STR_LEN("edge1");
    vertexLabel->commonInfo->relatedEdgeLabels[0].edgeName =
        (char *)DbDynMemCtxAlloc(memCtxVertexLabel, DM_STR_LEN("edge1"));
    errno_t err = strcpy_s(vertexLabel->commonInfo->relatedEdgeLabels[0].edgeName, DM_STR_LEN("edge1"), "edge1");
    DB_ASSERT(err == EOK);
#if defined(EXPERIMENTAL_NERGC) || defined(FEATURE_REPLICATION)
    vertexLabel->structSize = sizeof(DmVertexLabelT);
#endif

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));
    // 反序列化
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret =
        DmDeSerializeVertexLabelWithMemCtx((DbMemCtxT *)memCtxVertexLabel, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // 反序列化之后判断是否相等
    EXPECT_TRUE(VertexLabelEquals(vertexLabel, retVertexLabel));
    DmDestroyVertexLabel(vertexLabel);
    DmDestroyVertexLabel(retVertexLabel);
    DbDynMemCtxFree(memCtxVertexLabel, buffer.buf);
}

#if defined(EXPERIMENTAL_NERGC) || defined(FEATURE_REPLICATION)
TEST_F(UtDmVertexLabel, VertexLabelTestSerilizeAndDeseriWithMemCtx4Extend)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValue1(memCtxVertexLabel, vertexLabel);
    vertexLabel->commonInfo->edgeLabelNum = 1;
    vertexLabel->commonInfo->relatedEdgeLabels =
        (DmEdgeLabelInfoT *)DbDynMemCtxAlloc(memCtxVertexLabel, sizeof(DmEdgeLabelInfoT));
    memset_s(vertexLabel->commonInfo->relatedEdgeLabels, sizeof(DmEdgeLabelInfoT), 0, sizeof(DmEdgeLabelInfoT));
    vertexLabel->commonInfo->relatedEdgeLabels[0].edgeLabelId = 999;
    vertexLabel->commonInfo->relatedEdgeLabels[0].edgeNameLen = DM_STR_LEN("edge1");
    vertexLabel->commonInfo->relatedEdgeLabels[0].edgeName =
        (char *)DbDynMemCtxAlloc(memCtxVertexLabel, DM_STR_LEN("edge1"));
    errno_t err = strcpy_s(vertexLabel->commonInfo->relatedEdgeLabels[0].edgeName, DM_STR_LEN("edge1"), "edge1");
    DB_ASSERT(err == EOK);
    vertexLabel->structSize = sizeof(DmVertexLabelT);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 模拟高版本向低版本发送 高版本多一个uint32的字段
    DmBuffer newBuffer;
    newBuffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    newBuffer.len = buffer.len + sizeof(uint32_t);
    newBuffer.buf = (uint8_t *)DbDynMemCtxAlloc(newBuffer.memCtx, newBuffer.len);
    uint32_t extend1 = 2;
    uint32_t vertexLabelSize = sizeof(DmVertexLabelT);
    uint16_t vertexLabelNewSize = vertexLabelSize + sizeof(uint32_t);  // 构造的新数据结构多了个uint32
    memcpy_s(newBuffer.buf, vertexLabelSize, buffer.buf, vertexLabelSize);
    DmVertexLabelT *tmp = (DmVertexLabelT *)newBuffer.buf;
    tmp->structSize = vertexLabelNewSize;
    memcpy_s(newBuffer.buf + vertexLabelSize, sizeof(uint32_t), &extend1, sizeof(uint32_t));
    memcpy_s(newBuffer.buf + vertexLabelSize + sizeof(uint32_t), newBuffer.len - vertexLabelSize,
        buffer.buf + vertexLabelSize, newBuffer.len - vertexLabelSize);

    // 反序列化
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret = DmDeSerializeVertexLabelWithMemCtx(
        (DbMemCtxT *)memCtxVertexLabel, newBuffer.buf, newBuffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // 反序列化之后判断是否相等
    EXPECT_TRUE(VertexLabelEquals(vertexLabel, retVertexLabel));
    DmDestroyVertexLabel(vertexLabel);
    DmDestroyVertexLabel(retVertexLabel);
    DbDynMemCtxFree(memCtxVertexLabel, buffer.buf);
    DbDynMemCtxFree(memCtxVertexLabel, newBuffer.buf);
}
#endif

TEST_F(UtDmVertexLabel, DatalogVertexLabelTestSerilizeAndDeseriWithMemCtx)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValueWithDatalogResourceInfo(memCtxVertexLabel, vertexLabel);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 反序列化
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret =
        DmDeSerializeVertexLabelWithMemCtx((DbMemCtxT *)memCtxVertexLabel, buffer.buf, buffer.len, &retVertexLabel);
    ASSERT_EQ(GMERR_OK, ret);
    VertexLabelCommonInfoT *commonInfo = retVertexLabel->commonInfo;
    EXPECT_EQ(commonInfo->datalogLabelInfo->labelType, DM_DTL_RESOURCE_SEQUENTIAL);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->inputCount, (uint32_t)2);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->inputPropIds[0], (uint32_t)0);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->inputPropIds[1], (uint32_t)1);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->outputCount, (uint32_t)2);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->outputPropIds[0], (uint32_t)2);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->outputPropIds[1], (uint32_t)6);
    EXPECT_EQ(commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[0].value.byteValue, (int8_t)2);
    EXPECT_EQ(strcmp((char *)commonInfo->datalogLabelInfo->resourceInfo->defaultResVals[1].value.strAddr, "c5"), 0);
}

TEST_F(UtDmVertexLabel, ResColVertexLabelTestSerilizeAndDeseriWithMemCtx)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValueWithDatalogResourceInfo(memCtxVertexLabel, vertexLabel);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 反序列化
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret =
        DmDeSerializeVertexLabelWithMemCtx((DbMemCtxT *)memCtxVertexLabel, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    // 反序列化之后判断是否相等
    EXPECT_TRUE(VertexLabelEquals(vertexLabel, retVertexLabel));
    DmDestroyVertexLabel(vertexLabel);
    DmDestroyVertexLabel(retVertexLabel);
    DbDynMemCtxFree(memCtxVertexLabel, buffer.buf);
}

TEST_F(UtDmVertexLabel, VlCopySeriDeseriTestWithDatalogResourceInfo)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValueWithDatalogResourceInfo(memCtxVertexLabel, vertexLabel);

    TestDmCopySeriDeseriVertexLabel(vertexLabel);

    DmDestroyVertexLabel(vertexLabel);
}

TEST_F(UtDmVertexLabel, VlCopySeriDeseriTestWithDatalogUpdateInfo)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValueWithDatalogUpdateInfo(memCtxVertexLabel, vertexLabel);

    TestDmCopySeriDeseriVertexLabel(vertexLabel);

    DmDestroyVertexLabel(vertexLabel);
}

TEST_F(UtDmVertexLabel, VlCopySeriDeseriTestWithDatalogTbmInfo)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValueWithDatalogTbmInfo(memCtxVertexLabel, vertexLabel);

    TestDmCopySeriDeseriVertexLabel(vertexLabel);

    DmDestroyVertexLabel(vertexLabel);
}

TEST_F(UtDmVertexLabel, VlCopySeriDeseriTestWithDatalogMsgNotifyInfo)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValueWithDatalogMsgNotifyInfo(memCtxVertexLabel, vertexLabel);

    TestDmCopySeriDeseriVertexLabel(vertexLabel);

    DmDestroyVertexLabel(vertexLabel);
}

// 随机数反序列化，类似fuzz测试。
TEST_F(UtDmVertexLabel, VertexLabelTestDeseriWithIncorrectBuffer)
{
    DmVertexLabelT *vertexLabel;
    uint32_t testCount = 0;
    uint32_t len;
    uint8_t *buf;
    uint8_t *bufCursor;
    while (testCount < VERTEX_LABEL_DESERI_INCORRECT_BUFFER_TEST_LOOP) {
        srandom(time(NULL));
        len = random() % VERTEX_LABEL_INCORRECT_BUFFER_MAX_LENGTH;
        buf = (uint8_t *)DbDynMemCtxAlloc(memCtxVertexLabel, len);
        ASSERT_NE((void *)NULL, buf);
        bufCursor = buf;
        for (uint32_t i = 0; i < len; i++) {
            *bufCursor = random() % UINT8_MAX_LENGTH;
            bufCursor++;
        }
        (void)DmDeSerializeVertexLabelWithMemCtx((DbMemCtxT *)memCtxVertexLabel, buf, len, &vertexLabel);
        DbDynMemCtxFree(memCtxVertexLabel, buf);
        DmDestroyVertexLabel(vertexLabel);
        vertexLabel = nullptr;
        buf = nullptr;
        testCount++;
    }
}

TEST_F(UtDmVertexLabel, VertexLabelTestSerilizeAndDeseri)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValue1(memCtxVertexLabel, vertexLabel);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 反序列化
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // 反序列化之后判断是否相等
    EXPECT_EQ(retVertexLabel->metaCommon.dbId, vertexLabel->metaCommon.dbId);
    EXPECT_EQ(retVertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaId);
    EXPECT_EQ(retVertexLabel->metaCommon.version, vertexLabel->metaCommon.version);
    EXPECT_STREQ(retVertexLabel->metaCommon.metaName, vertexLabel->metaCommon.metaName);

    // copy
    DmVertexLabelT *copyVertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &copyVertexLabel);

    EXPECT_EQ(GMERR_OK, DmCopyVertexLabel(vertexLabel, copyVertexLabel));
    EXPECT_EQ(copyVertexLabel->metaCommon.dbId, vertexLabel->metaCommon.dbId);
    EXPECT_EQ(copyVertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaId);
    EXPECT_EQ(copyVertexLabel->metaCommon.version, vertexLabel->metaCommon.version);
    EXPECT_STREQ(copyVertexLabel->metaCommon.metaName, vertexLabel->metaCommon.metaName);
    printf("copyVertexLabel creator:%s\n", copyVertexLabel->commonInfo->creator);
    DmDestroyVertexLabel(vertexLabel);
    DmDestroyVertexLabel(retVertexLabel);
    DmDestroyVertexLabel(copyVertexLabel);
    DbDynMemCtxFree(memCtxVertexLabel, buffer.buf);
}

TEST_F(UtDmVertexLabel, VertexLabelTestSerilizeAndDeseriComment)
{
    DmVertexLabel *vertexLabel = GetNestVertexLabelTreeSchemaWithNoCopy((DbMemCtxT *)memCtxVertexLabel, false);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 反序列化
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // 反序列化之后判断是否相等
    EXPECT_EQ(retVertexLabel->metaCommon.dbId, vertexLabel->metaCommon.dbId);
    EXPECT_EQ(retVertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaId);
    EXPECT_EQ(retVertexLabel->metaCommon.version, vertexLabel->metaCommon.version);
    EXPECT_STREQ(retVertexLabel->metaCommon.metaName, vertexLabel->metaCommon.metaName);
    EXPECT_STREQ(retVertexLabel->metaVertexLabel->schema->properties[0].comment,
        vertexLabel->metaVertexLabel->schema->properties[0].comment);
    EXPECT_STREQ(retVertexLabel->metaVertexLabel->schema->nodes[0].comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].comment);
    EXPECT_STREQ(retVertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].comment);
    EXPECT_STREQ(retVertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].schema->nodes[0].comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].schema->nodes[0].comment);
    EXPECT_STREQ(retVertexLabel->metaVertexLabel->schema->nodes[0]
                     .schema->nodes[0]
                     .schema->nodes[0]
                     .schema->properties[0]
                     .comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].schema->nodes[0].schema->properties[0].comment);

    // copy
    DmVertexLabelT *copyVertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &copyVertexLabel);

    EXPECT_EQ(GMERR_OK, DmCopyVertexLabel(vertexLabel, copyVertexLabel));
    EXPECT_EQ(copyVertexLabel->metaCommon.dbId, vertexLabel->metaCommon.dbId);
    EXPECT_EQ(copyVertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaId);
    EXPECT_EQ(copyVertexLabel->metaCommon.version, vertexLabel->metaCommon.version);
    EXPECT_STREQ(copyVertexLabel->metaCommon.metaName, vertexLabel->metaCommon.metaName);
    EXPECT_STREQ(copyVertexLabel->metaVertexLabel->schema->properties[0].comment,
        vertexLabel->metaVertexLabel->schema->properties[0].comment);
    EXPECT_STREQ(copyVertexLabel->metaVertexLabel->schema->nodes[0].comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].comment);
    EXPECT_STREQ(copyVertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].comment);
    EXPECT_STREQ(copyVertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].schema->nodes[0].comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].schema->nodes[0].comment);
    EXPECT_STREQ(copyVertexLabel->metaVertexLabel->schema->nodes[0]
                     .schema->nodes[0]
                     .schema->nodes[0]
                     .schema->properties[0]
                     .comment,
        vertexLabel->metaVertexLabel->schema->nodes[0].schema->nodes[0].schema->nodes[0].schema->properties[0].comment);

    DmDestroyVertexLabel(vertexLabel);
    DmDestroyVertexLabel(retVertexLabel);
    DmDestroyVertexLabel(copyVertexLabel);
    DbDynMemCtxFree(memCtxVertexLabel, buffer.buf);
}

TEST_F(UtDmVertexLabel, VertexLabelPropSchemaTestSerilizeAndDeseriComment)
{
    DmVertexLabel *vertexLabel = GetNestVertexLabelTreeSchemaWithNoCopy((DbMemCtxT *)memCtxVertexLabel, false);

    // 序列化
    DmPropertySchemaT propSchema = vertexLabel->metaVertexLabel->schema->properties[0];
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(memCtxVertexLabel, DmGetPropertySchemaSeriBufLength(&propSchema));
    EXPECT_TRUE(buf != NULL);
    uint8_t *bufCursor = buf;
    DmSerializePropertySchema(&propSchema, &buf);

    // 反序列化
    DmPropertySchemaT retPropSchema = {0};
    EXPECT_EQ(GMERR_OK, DmDeSerializePropertySchema(&retPropSchema, &bufCursor));

    // 反序列化之后判断是否相等
    EXPECT_EQ(retPropSchema.dataType, propSchema.dataType);
    EXPECT_EQ(retPropSchema.size, propSchema.size);
    EXPECT_STREQ(retPropSchema.name, propSchema.name);
    EXPECT_STREQ(retPropSchema.comment, propSchema.comment);
    EXPECT_EQ(retPropSchema.nameLen, propSchema.nameLen);
    EXPECT_EQ(retPropSchema.commentLen, propSchema.commentLen);
    EXPECT_EQ(retPropSchema.isValid, propSchema.isValid);
    EXPECT_EQ(retPropSchema.isFixed, propSchema.isFixed);
}

TEST_F(UtDmVertexLabel, VertexLabelTestSerilizeAndDeseriFail1)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValue1(memCtxVertexLabel, vertexLabel);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 正常反序列化，成功
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // 修改buffer中的内容后，失败
    uint8_t *bufCursor = buffer.buf + buffer.len - 6;
    *(uint32_t *)bufCursor = 0xFFFFFFFF;
    ret = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
}

TEST_F(UtDmVertexLabel, VertexLabelTestSerilizeAndDeseriFail2)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValue1(memCtxVertexLabel, vertexLabel);

    // 序列化
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    EXPECT_EQ(GMERR_OK, DmSerializeVertexLabel(vertexLabel, &buffer));

    // 正常反序列化，成功
    DmVertexLabelT *retVertexLabel = NULL;
    Status ret = DmDeSerializeVertexLabelWithMemCtx(buffer.memCtx, buffer.buf, buffer.len, &retVertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // 构造一个更长的buffer，失败
    DmBuffer longBuf;
    longBuf.memCtx = (DbMemCtxT *)memCtxVertexLabel;
    longBuf.len = buffer.len + 1;
    longBuf.buf = (uint8_t *)DbDynMemCtxAlloc(memCtxVertexLabel, longBuf.len);
    DB_ASSERT(longBuf.buf != NULL);
    errno_t err = memcpy_s(longBuf.buf, buffer.len, buffer.buf, buffer.len);
    DB_ASSERT(err == EOK);
    ret = DmDeSerializeVertexLabelWithMemCtx(longBuf.memCtx, longBuf.buf, longBuf.len, &retVertexLabel);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
}

void *VlViewMalloc(size_t size)
{
    return DbDynMemCtxAlloc(jsonMemCtx, size);
}

void VlViewFree(void *ptr)
{
    DbDynMemCtxFree(jsonMemCtx, ptr);
}

Status TestJsonObjectAppendTspName(DbInstanceHdT dbInstance, DbJsonT *schemaObj, char *key, uint32_t tspId)
{
    return JsonObjectSetString(schemaObj, key, "test_table_space_name");
}

TEST_F(UtDmVertexLabel, VertexLabelTestView)
{
    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(memCtxVertexLabel, &vertexLabel);
    InitialVertexLabelValue1(memCtxVertexLabel, vertexLabel);
    int stub1 = setStubC((void *)jsonp_malloc, (void *)VlViewMalloc);
    int stub2 = setStubC((void *)jsonp_free, (void *)VlViewFree);
    int stub3 = setStubC((Status *)JsonObjectAppendTspName, (Status *)TestJsonObjectAppendTspName);

    // 调用视图逆解析函数前,jsonMemCtx 已使用内存为 0
    EXPECT_EQ(0u, jsonMemCtx->totalAllocSize);

    char *jsonStr = NULL;
    InvParseVlConfigJson(vertexLabel, &jsonStr);
    jsonp_free(jsonStr);

    jsonStr = NULL;
    InvParseVlSchemaJson(vertexLabel, &jsonStr);
    jsonp_free(jsonStr);

    // 调用视图逆解析函数后,jsonMemCtx 已使用内存仍为 0,无内存泄露
    EXPECT_EQ(0u, jsonMemCtx->totalAllocSize);

    clearStub(stub1);
    clearStub(stub2);
    clearStub(stub3);
    DmDestroyVertexLabel(vertexLabel);
}
