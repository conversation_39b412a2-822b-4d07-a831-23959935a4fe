
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: ut file for heap reliability
 */
#include "ut_reliability.h"
#include "dm_meta_prop_strudefs.h"
#include "heap_op_helper.h"
#include "db_table_space.h"
#include "se_resource_session_pub.h"
#include "se_heap_page.h"
#include "se_heap_access_inner.h"
#include "se_heap_persist_inner.h"
#include "se_undo.h"
#include "se_undo_trx_resource.h"
#include "persistence_common.h"
#include "se_heap_stats.h"
#include "se_heap_fetch.h"
#include "db_config_file.h"
#include "adpt_init.h"

const static uint32_t maxItemNum = 1000000;
static SeInstanceT *seIns = NULL;
static DbMemCtxT *topShmCtx = NULL;
static DbMemCtxT *topDynCtx = NULL;
static DmVertexLabelT *varLabel = NULL;

static uint64_t g_namespaceTrxIdArray[100] = {0};

static StatusInter HeapLabelDeSerialHpTupleStub(HpReadRowInfoT *readRowInfo, void *userData)
{
    return STATUS_OK_INTER;
}

static void UndoKeepThreadAliveStub(
    const TrxT *trx, uint64_t *splitStartTime, uint32_t undoTotalCnt, uint32_t *undoProcessedCnt)
{
    return;
}

static bool SeIsLabelLockedStub(SeRunCtxT *seRunCtx, uint32_t labelId)
{
    return true;
}

static StatusInter RedoFlushOnTrxCommitStub(RedoMgrT *redoMgr)
{
    return STATUS_OK_INTER;
}

static void UndoRecoveryReleaseLabelHandleStub(TrxT *trx, uint32_t labelId, DmLabelTypeE labelType, void *label)
{
    DB_ASSERT(false);
}

static Status SeTransCommitStub(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    DbSleep(100);
    DbMemSetThreadUnlimited();  // 为了保证事务提交完整性，动态内存的申请不受系统限制
    SeRunCtxT *runCtx = seRunCtx;
    if (SECUREC_UNLIKELY(seRunCtx->isPersistence)) {
        SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
    }
    StatusInter ret = TrxCommit((TrxT *)runCtx->trx);
    DbMemSetThreadLimited();  // 恢复系统限制
    if (SECUREC_UNLIKELY(seRunCtx->isPersistence)) {
        SeSetCurRedoCtx(NULL);
    }
    return DbGetExternalErrno(ret);
}

static Status UtGetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance)
{
    if (trxId == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *trxId = g_namespaceTrxIdArray[labelId];
    *trxIdLastModify = g_namespaceTrxIdArray[labelId];
    return GMERR_OK;
}

static Status UtSetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance)
{
    g_namespaceTrxIdArray[labelId] = trxId;
    return GMERR_OK;
}

static Status UtGetLabelNameByEdgeLabelId(uint32_t elId, char *labelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelName);
    return GMERR_DATA_EXCEPTION;
}

class HeapUpdateCompactPersistentTest : public UtReliability {
public:
    static void SetUpTestCase()
    {
        init();
        system("ipcrm -a");
        system("rm -rf /data/gmdb");
        // 按需持久化下，关闭redo所以这里需要对这个全局变量进行初始化
        RedoAmFuncT UtOnDemandRedoAm = {0};
        RedoSetAmFunc(&UtOnDemandRedoAm);
        DbCommonInitCfgT cfg = {
            .env = ADPT_RTOS_SERVER,
            .configFileName = "gmserver_ondemand_durable.ini",
            .isBackGround = false,
        };
        DbSetServerThreadFlag();
        DbAdapterInit();
        Status ret = DbCommonInit(&cfg, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        SeConfigT seConfig = {0};
        (void)StorageConfigGet(NULL, &seConfig, NULL);
        seConfig.compressSpaceEnable = true;
        seConfig.bufferPoolSize = 20480;
        topShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
        ASSERT_NE(nullptr, topShmCtx);
        topDynCtx = (DbMemCtxT *)DbGetTopDynMemCtx(NULL);
        ASSERT_NE(nullptr, topDynCtx);
        ret = SeCreateInstance(NULL, topShmCtx, &seConfig, &seIns);
        ASSERT_EQ(GMERR_OK, ret);
        setStubC((void *)HeapLabelDeSerialHpTuple, (void *)HeapLabelDeSerialHpTupleStub);
        setStubC((void *)UndoKeepThreadAlive, (void *)UndoKeepThreadAliveStub);
        setStubC((void *)SeIsLabelLocked, (void *)SeIsLabelLockedStub);
        setStubC((void *)RedoFlushOnTrxCommit, (void *)RedoFlushOnTrxCommitStub);
        setStubC((void *)UndoRecoveryReleaseLabelHandle, (void *)UndoRecoveryReleaseLabelHandleStub);
        HeapPersistentTestCreateVertexLabel(topShmCtx, &varLabel);
        OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelLastTrxIdAndTrxCommitTimeById, UtGetLabelLastTrxIdAndTrxCommitTimeById,
            UtGetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[TRX_CHECK_READVIEW_NUM] = {
            UtSetLabelLastTrxIdAndTrxCommitTimeById, UtSetLabelLastTrxIdAndTrxCommitTimeById,
            UtSetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId};
        SeInitTrxMgrCheckFunc(DbGetProcGlobalId(), setFunc, getFunc, getLabelName);
    }
    static void TearDownTestCase()
    {
        SeDestroyInstance(seIns->instanceId);
        DbCommonFinalize(NULL);
        clearAllStub();
    }

    SeRunCtxT *CreateSeRunCtx(void);
    void ReleaseSeRunCtx(SeRunCtxT *seRunCtx);
    static uint32_t stubForRedo;
};

uint32_t HeapUpdateCompactPersistentTest::stubForRedo = 0;

SeRunCtxT *HeapUpdateCompactPersistentTest::CreateSeRunCtx(void)
{
    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(DbGetProcGlobalId(), topDynCtx, NULL, &seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
    ret = SeOpenResSession(seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
    return seRunCtx;
}

void HeapUpdateCompactPersistentTest::ReleaseSeRunCtx(SeRunCtxT *seRunCtx)
{
    Status ret = SeClose(seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
}

TEST_F(HeapUpdateCompactPersistentTest, Var_Heap_Extend_no_reserve_space)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[3];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.4);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.4);
    ASSERT_NE(nullptr, buf[1].buf);
    buf[2] = heapHandle.generateData(heapRunCtx, 0.6);
    ASSERT_NE(nullptr, buf[2].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    ASSERT_EQ(opInfo[0].addr.back().pageId, opInfo[1].addr.back().pageId);
    // 扩展第一条数据
    opInfo[0].copy(buf[2].bufSize, buf[2].buf);
    // 扩展第一条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[0], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扩展第二条数据
    opInfo[1].copy(buf[2].bufSize, buf[2].buf);
    // 扩展第二条数据，生成普通行
    rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

void HeapGetRowHead(HeapRunCtxT *heapRunCtx, HTHRowInfoT *opInfo, HpNormalRowHeadT *rowHead)
{
    HVPageHeadT *varPage = NULL;
    PageSizeT slotOffset = 0;
    HpRowSlotT *slot = NULL;
    uint32_t pageId = opInfo->addr.back().pageId;
    uint32_t slotId = opInfo->addr.back().slotId;
    PageIdT pageAddr = DeserializePageId(heapRunCtx->pageMgr, pageId);
    (void)SeGetPage(heapRunCtx->pageMgr, pageAddr, (uint8_t **)&varPage, ENTER_PAGE_NORMAL, false);
    (void)HeapVarPageGetSlotBySlotId(varPage, slotId, &slotOffset, &slot);
    if (opInfo->rowType == HEAP_VAR_NORMAL_ROW) {
        *rowHead = *(HpNormalRowHeadT *)HeapVarPageGetRowState(varPage, slot);
    } else {
        *(HpLinkRowHeadT *)rowHead = *(HpLinkRowHeadT *)HeapVarPageGetRowState(varPage, slot);
    }
    SeLeavePage(heapRunCtx->pageMgr, pageAddr, false);
}

TEST_F(HeapUpdateCompactPersistentTest, Var_Heap_Extend_Link_Row_To_Normal_Row)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    // 事务1插入数据
    SeRunCtxT *transaction1 = CreateSeRunCtx();
    HeapTestHelper heapHandle(transaction1, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx_insert = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx_insert);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 事务2更新数据
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 生成三条数据
    HeapTupleBufT buf[4];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.6);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[1].buf);
    buf[2] = heapHandle.generateData(heapRunCtx, 0.9);
    ASSERT_NE(nullptr, buf[2].buf);
    buf[3] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[3].buf);
    HTHRowInfoT opInfo[2];
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx_insert, buf, opInfo, 2);
    heapHandle.releaseRunCtx(heapRunCtx_insert);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    ASSERT_EQ(opInfo[1].addr.back().pageId, opInfo[0].addr.back().pageId);

    // 获取第二条数据行头信息
    uint32_t oldPageId1 = opInfo[1].addr.back().pageId;
    HpNormalRowHeadT oldRowHead = {0};
    HeapGetRowHead(heapRunCtx, &opInfo[1], &oldRowHead);

    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的60%，生成一次跳转行
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    EXPECT_NE(oldPageId1, opInfo[1].addr.back().pageId);
    HpLinkRowHeadT newLinkRowHead = {0};
    HeapGetRowHead(heapRunCtx, &opInfo[1], (HpNormalRowHeadT *)&newLinkRowHead);

    // // 看护事务信息
    ASSERT_EQ(newLinkRowHead.dstInfo.trxId.trxIdH, oldRowHead.trxInfo.trxId.trxIdH);
    ASSERT_EQ(newLinkRowHead.dstInfo.trxId.trxIdL, oldRowHead.trxInfo.trxId.trxIdL);

    // 更新第一条成页面的90%，变为跳转行
    opInfo[0].copy(buf[2].bufSize, buf[2].buf);
    rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[0], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 再更新第二条成页面的70%，第一条reserve已清除，将变回普通行
    opInfo[1].copy(buf[3].bufSize, buf[3].buf);
    rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    EXPECT_EQ(oldPageId1, opInfo[1].addr.back().pageId);

    // 看护rollbackReserveSize
    HpNormalRowHeadT newNormalRowHead = {0};
    HeapGetRowHead(heapRunCtx, &opInfo[1], &newNormalRowHead);
    EXPECT_EQ(newNormalRowHead.rollbackReserveSize, newNormalRowHead.size);
    EXPECT_GE(newNormalRowHead.rollbackReserveSize, oldRowHead.rollbackReserveSize);

    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction2);
}

TEST_F(HeapUpdateCompactPersistentTest, Var_Heap_Extend_Link_Row_To_Link_Row)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[3];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[1].buf);
    buf[2] = heapHandle.generateData(heapRunCtx, 0.8);
    ASSERT_NE(nullptr, buf[2].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将dst row的页面地址存储下来
    uint32_t pageAddr = opInfo[1].addr.back().pageId;
    // 由于第一个页面数据1更新成了跳转行，此时不断插入数据2，直到第一个页面被填满插入第二个页面
    do {
        ret = heapHandle.insert(heapRunCtx, &buf[1], &opInfo[2]);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[2].rowType);
        if (pageAddr != opInfo[2].addr.back().pageId) {
            DB_ASSERT(opInfo[2].addr.back().pageId == opInfo[0].addr.back().pageId);
        }
    } while (pageAddr != opInfo[2].addr.back().pageId);
    // 将第二条数据更新成页面的80%
    opInfo[1].copy(buf[2].bufSize, buf[2].buf);
    // 更新第二条数据，再生成一次跳转行
    rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 此时dstRow应该去一个新页
    EXPECT_NE(pageAddr, opInfo[1].addr.back().pageId);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapUpdateCompactPersistentTest, Var_Heap_Extend_Lfs)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[3];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.6);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.2);
    ASSERT_NE(nullptr, buf[1].buf);
    buf[2] = heapHandle.generateData(heapRunCtx, 0.9);
    ASSERT_NE(nullptr, buf[2].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    ASSERT_EQ(opInfo[1].addr.back().pageId, opInfo[0].addr.back().pageId);

    // 将第一条数据更新成页面的90%
    opInfo[0].copy(buf[2].bufSize, buf[2].buf);
    // 更新第一条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[0], &rowType, 1, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 启动事务2
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成一条数据
    HeapTupleBufT buf2 = heapHandle.generateData(heapRunCtx2, 0.3);
    ASSERT_NE(nullptr, buf2.buf);
    HTHRowInfoT opInfo2;
    // 插入数据应在更新为了跳转行的Src行页面上
    ret = heapHandle.insert(heapRunCtx2, &buf2, &opInfo2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo2.rowType);
    ASSERT_EQ(opInfo[1].addr.back().pageId, opInfo2.addr.front().pageId);
    ASSERT_NE(opInfo[0].addr.back().pageId, opInfo2.addr.front().pageId);
    heapHandle.releaseRunCtx(heapRunCtx2);
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
    ReleaseSeRunCtx(transaction2);
}

typedef struct TagHeapRowLockConcurrentTestThreadCtx {
    SeRunCtxT *seRunCtx;
    HeapTestHelper *heapHandle;
    uint32_t itemNum;
    HTHRowInfoT opInfo;
    HTHRowTypeE rowType;
    StatusInter ret;
    bool isCompact;
} HeapRowLockConcurrentTestThreadCtxT;

void *HeapUpdateCompactConcurrentTestThread(void *heapHandle)
{
    DbSetServerThreadFlag();
    HeapRowLockConcurrentTestThreadCtxT *handle = (HeapRowLockConcurrentTestThreadCtxT *)heapHandle;
    handle->ret = handle->heapHandle->transBegin(handle->seRunCtx);
    if (handle->ret != STATUS_OK_INTER) {
        return NULL;
    }
    HeapRunCtxT *heapRunCtx = NULL;
    handle->ret = handle->heapHandle->allocRunCtx(handle->seRunCtx, &heapRunCtx);
    if (handle->ret != STATUS_OK_INTER) {
        return NULL;
    }
    for (uint32_t i = 0; i < handle->itemNum; ++i) {
        handle->ret = handle->heapHandle->update(heapRunCtx, &handle->opInfo, &handle->rowType, 1, handle->isCompact);
        if (handle->ret != STATUS_OK_INTER) {
            break;
        }
    }
    handle->heapHandle->releaseRunCtx(heapRunCtx);
    if (handle->ret != STATUS_OK_INTER) {
        (void)handle->heapHandle->transRollback(handle->seRunCtx);
    } else {
        handle->ret = handle->heapHandle->transCommit(handle->seRunCtx);
    }
    return NULL;
}

TEST_F(HeapUpdateCompactPersistentTest, ConcurrentSenario)
{
    int stubIdx = setStubC((void *)SeTransCommit, (void *)SeTransCommitStub);
    uint32_t dataNum = 1000;
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper varHeap(seRunCtx, &varConfig, (void *)varLabel);

    // 预置一条数据
    StatusInter ret = varHeap.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = varHeap.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf1 = varHeap.generateData(heapRunCtx, 0.5);
    ASSERT_NE(nullptr, buf1.buf);
    HeapTupleBufT buf2 = varHeap.generateData(heapRunCtx, 0.5);
    ASSERT_NE(nullptr, buf2.buf);
    ret = varHeap.insert(heapRunCtx, &buf1, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    varHeap.releaseRunCtx(heapRunCtx);
    ret = varHeap.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);

    HeapRowLockConcurrentTestThreadCtxT varHeapOpCtx1 = {
        .seRunCtx = CreateSeRunCtx(),
        .heapHandle = &varHeap,
        .itemNum = dataNum,
        .opInfo = opInfo,
        .rowType = HEAP_VAR_NORMAL_ROW,
        .ret = STATUS_OK_INTER,
        .isCompact = true,
    };

    HTHRowInfoT opInfo1;
    opInfo1.addr.push_back(opInfo.addr.front());
    opInfo1.copy(buf2.bufSize, buf2.buf);

    HeapRowLockConcurrentTestThreadCtxT varHeapOpCtx2 = {
        .seRunCtx = CreateSeRunCtx(),
        .heapHandle = &varHeap,
        .itemNum = dataNum,
        .opInfo = opInfo1,
        .rowType = HEAP_VAR_NORMAL_ROW,
        .ret = STATUS_OK_INTER,
        .isCompact = false,
    };

    pthread_t threadIds[2] = {0};
    ASSERT_EQ(pthread_create(&threadIds[0], NULL, HeapUpdateCompactConcurrentTestThread, &varHeapOpCtx1), 0);
    ASSERT_EQ(pthread_create(&threadIds[1], NULL, HeapUpdateCompactConcurrentTestThread, &varHeapOpCtx2), 0);

    ASSERT_EQ(pthread_join(threadIds[0], NULL), 0);
    ASSERT_EQ(pthread_join(threadIds[1], NULL), 0);

    ASSERT_EQ(STATUS_OK_INTER, varHeapOpCtx1.ret == RESTRICT_VIOLATION_INTER ? STATUS_OK_INTER : varHeapOpCtx1.ret);
    ASSERT_EQ(STATUS_OK_INTER, varHeapOpCtx2.ret == RESTRICT_VIOLATION_INTER ? STATUS_OK_INTER : varHeapOpCtx2.ret);

    ReleaseSeRunCtx(varHeapOpCtx2.seRunCtx);
    ReleaseSeRunCtx(varHeapOpCtx1.seRunCtx);
    clearStub(stubIdx);
}

TEST_F(HeapUpdateCompactPersistentTest, Var_Heap_Extend_fin_with_no_lock)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapTupleBufT buf = {0};
    buf = heapHandle.generateData(heapRunCtx, 0.4);
    HTHRowInfoT opInfo[1];
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, opInfo, 1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    // 扩展第一条数据
    (void)HeapLabelOpen(heapRunCtx, HEAP_OPTYPE_UPDATE, (DbMemCtxT *)heapRunCtx->seRunCtx->sessionMemCtx);
    ret = DbGetStatusInterErrno(
        HeapLabelUpdateCompactWithHpTupleBuf(heapRunCtx, &opInfo[0].buf, *(HpTupleAddr *)(void *)&opInfo[0].addr[0]));
    ASSERT_EQ(heapRunCtx->pageSorter.pageNum, 0);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}
