/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: ut file for heap reliability
 */
#include "ut_reliability.h"
#include "dm_meta_prop_strudefs.h"
#include "heap_op_helper.h"
#include "db_table_space.h"
#include "se_resource_session_pub.h"
#include "se_heap_page.h"
#include "se_heap_access_inner.h"
#include "se_heap_persist_inner.h"
#include "se_undo.h"
#include "se_undo_trx_resource.h"
#include "persistence_common.h"
#include "se_heap_stats.h"
#include "se_heap_fetch.h"
#include "adpt_init.h"
#include "se_daemon.h"
#include "db_server_purger.h"
#include "se_undo_purger.h"

extern "C" {
StatusInter HeapLocateLinkDstVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);
StatusInter HeapReLocateLinkSrcAfterReLatch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool *isPageChange);
StatusInter HeapLocateLinkDstVarRowCheck(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, uint32_t fetchTryTimes, bool isSrcRelatch, StatusInter ret);
StatusInter HeapFetchVarRowSetRowInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);
void HeapUpdateForUpdateFailedClear(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);
StatusInter RedoFlushOnTrxCommit(RedoMgrT *redoMgr);
}

const static uint32_t maxItemNum = 1000000;
static SeInstanceT *seIns = NULL;
static DbMemCtxT *topShmCtx = NULL;
static DbMemCtxT *topDynCtx = NULL;
static DmVertexLabelT *varLabel = NULL;
static DmVertexLabelT *fixLabel = NULL;

static StatusInter HeapLabelDeSerialHpTupleStub(HpReadRowInfoT *readRowInfo, void *userData)
{
    return STATUS_OK_INTER;
}

static void UndoKeepThreadAliveStub(
    const TrxT *trx, uint64_t *splitStartTime, uint32_t undoTotalCnt, uint32_t *undoProcessedCnt)
{
    return;
}

static uint64_t g_namespaceTrxIdArray[100] = {0};

static Status UtGetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance)
{
    if (trxId == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *trxId = g_namespaceTrxIdArray[labelId];
    *trxIdLastModify = g_namespaceTrxIdArray[labelId];
    return GMERR_OK;
}

static Status UtSetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance)
{
    g_namespaceTrxIdArray[labelId] = trxId;
    return GMERR_OK;
}

static Status UtGetLabelNameByEdgeLabelId(uint32_t elId, char *labelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelName);
    return GMERR_DATA_EXCEPTION;
}

static void UndoRecoveryReleaseLabelHandleStub(TrxT *trx, uint32_t labelId, DmLabelTypeE labelType, void *label)
{
    DB_ASSERT(false);
}

static HeapTestHelper *g_heapHandle = NULL;
static Status StubPurgerGetHeapRunCtx(
    uint32_t labelId, DbMemCtxT *memCtx, SeRunCtxHdT seCtx, DmLabelTypeE labelType, HpRunHdlT *heapRunCtx)
{
    HeapRunCtxT *heapRunHdl = NULL;
    StatusInter ret = g_heapHandle->allocRunCtx(seCtx, &heapRunHdl);
    DB_ASSERT(ret == STATUS_OK_INTER);
    ret = DbGetStatusInterErrno(HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_UNDO_PURGER, memCtx));
    DB_ASSERT(ret == STATUS_OK_INTER);
    *heapRunCtx = heapRunHdl;
    return STATUS_OK_INTER;
}

static Status StubPurgerReleaseLabelAndLatch(DmLabelTypeE tupleType, void *label)
{
    return GMERR_OK;
}

static void TriggerPurger(uint16_t instanceId)
{
    UndoPurgerCfgT cfg = {
        .getHeapRunCtxFunc = StubPurgerGetHeapRunCtx,
        .getFixedHeapRunCtxFunc = NULL,
        .releaseLabelAndLatchFunc = StubPurgerReleaseLabelAndLatch,
        .setKeyCmpCallbackFunc = PurgerSetIdxKeyCmpCallback,
        .splitTime = 10000000,
        .instanceId = instanceId,
    };
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    static UndoPurgerRunCtxT *purgerCtx = NULL;
    if (purgerCtx == NULL) {
        Status ret = PurgerCtxInit(&purgerCtx, &cfg);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        SeSetCurRedoCtx(purgerCtx->redoCtx);
    }
    // 主逻辑，获取事务提交情况，清理undo log
    (void)UndoPurgerMain(purgerCtx, DbRdtsc(), DB_INVALID_ID16);
    SeSetCurRedoCtx(redoCtx);
}

class HeapRowLockPersistentTest : public UtReliability {
public:
    static void SetUpTestCase()
    {
        init();
        system("ipcrm -a");
        system("rm -rf /data/gmdb");
        DbAdapterInit();
        DbCommonInitCfgT cfg = {
            .env = ADPT_RTOS_SERVER,
            .configFileName = "gmserver_incre_persist.ini",
            .isBackGround = false,
        };
        DbSetServerThreadFlag();
        Status ret = DbCommonInit(&cfg, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        SeConfigT seConfig = {0};
        ret = StorageConfigGet(NULL, &seConfig, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        seConfig.bufferPoolSize = 20480;
        topShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
        ASSERT_NE(nullptr, topShmCtx);
        topDynCtx = (DbMemCtxT *)DbGetTopDynMemCtx(NULL);
        ASSERT_NE(nullptr, topDynCtx);
        ret = SeCreateInstance(NULL, topShmCtx, &seConfig, &seIns);
        ASSERT_EQ(GMERR_OK, ret);
        HeapPersistentTestCreateVertexLabel(topShmCtx, &fixLabel);
        HeapPersistentTestCreateVertexLabel(topShmCtx, &varLabel);
        setStubC((void *)HeapLabelDeSerialHpTuple, (void *)HeapLabelDeSerialHpTupleStub);
        setStubC((void *)UndoKeepThreadAlive, (void *)UndoKeepThreadAliveStub);
        setStubC((void *)UndoRecoveryReleaseLabelHandle, (void *)UndoRecoveryReleaseLabelHandleStub);
        OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelLastTrxIdAndTrxCommitTimeById, UtGetLabelLastTrxIdAndTrxCommitTimeById,
            UtGetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[TRX_CHECK_READVIEW_NUM] = {
            UtSetLabelLastTrxIdAndTrxCommitTimeById, UtSetLabelLastTrxIdAndTrxCommitTimeById,
            UtSetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId};
        SeInitTrxMgrCheckFunc(DbGetProcGlobalId(), setFunc, getFunc, getLabelName);
    }
    static void TearDownTestCase()
    {
        SeDestroyInstance(seIns->instanceId);
        DbCommonFinalize(NULL);
        clearAllStub();
    }

    void DestroyAfterTearDown() override
    {
        RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
        RedoRunCtxT *tmpCtx = NULL;
        (void)RedoCtxCreate(seIns->redoMgr, &tmpCtx);
        DB_ASSERT(tmpCtx != NULL);
        SeSetCurRedoCtx(tmpCtx);
        UtReplyTearDown(seIns);
        SeSetCurRedoCtx(redoCtx);
        RedoCtxRelease(tmpCtx);
    }

    void InitBeforeSetUp() override
    {
        UtReplySetUp(seIns);
    }

    SeRunCtxT *CreateSeRunCtx(void);

    void ReleaseSeRunCtx(SeRunCtxT *seRunCtx);
    static uint32_t stubForRedo;
};

uint32_t HeapRowLockPersistentTest::stubForRedo = 0;

SeRunCtxT *HeapRowLockPersistentTest::CreateSeRunCtx(void)
{
    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(DbGetProcGlobalId(), topDynCtx, NULL, &seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
    ret = SeOpenResSession(seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
    return seRunCtx;
}

void HeapRowLockPersistentTest::ReleaseSeRunCtx(SeRunCtxT *seRunCtx)
{
    Status ret = SeClose(seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Create)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    ASSERT_EQ(GMERR_OK, heapHandle.clear(seRunCtx));
    ReleaseSeRunCtx(seRunCtx);
}

// iterate insert
TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Normal_Row_0)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.8);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

// batch insert
TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Normal_Row_1)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.1);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

// iterate insert
TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Normal_Row_2)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.8);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    uint64_t recCnt = 0;
    Status status = HeapStatGetRecCnt(heapRunCtx, &recCnt);
    ASSERT_EQ(GMERR_OK, status);
    ASSERT_EQ((uint64_t)0, recCnt);
    heapHandle.releaseRunCtx(heapRunCtx);
    ReleaseSeRunCtx(seRunCtx);
}

// batch insert
TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Normal_Row_3)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.1);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    uint64_t recCnt = 0;
    Status status = HeapStatGetRecCnt(heapRunCtx, &recCnt);
    ASSERT_EQ(GMERR_OK, status);
    ASSERT_EQ((uint64_t)0, recCnt);
    heapHandle.releaseRunCtx(heapRunCtx);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Normal_Row_To_Normal_Row)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Normal_Row_To_Link_Row_0)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据，刚好能凑满一个页
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.2);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[2];
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，此时应该变成跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Normal_Row_To_Link_Row_1)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);

    SeRunCtxT *transaction1 = CreateSeRunCtx();
    StatusInter ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据，刚好能凑满一个页
    HeapTupleBufT buf1[2];
    buf1[0] = heapHandle.generateData(heapRunCtx1, 0.3);
    ASSERT_NE(nullptr, buf1[0].buf);
    buf1[1] = heapHandle.generateData(heapRunCtx1, 0.6);
    ASSERT_NE(nullptr, buf1[1].buf);

    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapTupleBufT buf2 = heapHandle.generateData(heapRunCtx2, 0.3);
    ASSERT_NE(nullptr, buf2.buf);

    // 生成两条数据，刚好能凑满一个页
    HTHRowInfoT opInfo1;
    ret = heapHandle.insert(heapRunCtx1, buf1, &opInfo1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo1.rowType);
    HTHRowInfoT opInfo2;
    ret = heapHandle.insert(heapRunCtx2, &buf2, &opInfo2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo2.rowType);
    ASSERT_EQ(opInfo1.addr.front().pageId, opInfo2.addr.front().pageId);

    // 回滚第二条数据，把页面空出来
    ret = heapHandle.transRollback(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx2);
    ReleaseSeRunCtx(transaction2);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 将第二条数据更新成页面的70%
    opInfo1.copy(buf1[1].bufSize, buf1[1].buf);
    // 更新第二条数据，此时应该发生页内整理, 然后原地更新成功
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    ReleaseSeRunCtx(seRunCtx);
}

static StatusInter HeapAllocVarRowMock(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    return INT_ERR_HEAP_UPD_ALLOC_SAME_PAG;
}

static void HeapUpdateDstSrcRowSamePageCheckRowType(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    EXPECT_EQ(HEAP_VAR_ROW_NORMAL_ROW, opInfo->allocRowInfo->newRowInfo.rowType);
}

/**
 * ThreadA：更新操作数据页PageA—>空间不够，申请DstRow->AllocVarRow->空间够，ret=ALLOC_SAME_PAG->仍用跳转行RowUpdate
 * ThreadB：-------------------------Relatch----> 对PageA页整理，挪出做够空间 -> --------------------------------
 * Result：DstRow与SrcRow同数据页时，应用normalRow申请。
 * 如果仍用link row会导致在SrcRow读取DstRow时，可能发现DstRow为Normal Row，校验失败。
 */
TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Normal_Row_To_Link_Row_To_Normal_Row_2)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };

    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据，刚好能凑满一个页
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.2);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[2];
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，此时应该变成跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    opInfo[1].rowType = rowType;
    int stubIdx = setStubC((void *)HeapAllocVarRow, (void *)HeapAllocVarRowMock);
    EXPECT_GT(stubIdx, 0);
    int stubIdxTwo = setStubC((void *)HeapUpdateForUpdateFailedClear, (void *)HeapUpdateDstSrcRowSamePageCheckRowType);
    EXPECT_GT(stubIdxTwo, 0);
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    // 清理环境退出
    clearStub(stubIdx);
    clearStub(stubIdxTwo);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Link_Row_To_Link_Row_0)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[3];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[1].buf);
    buf[2] = heapHandle.generateData(heapRunCtx, 0.8);
    ASSERT_NE(nullptr, buf[2].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将dst row的页面地址存储下来
    uint32_t pageAddr = opInfo[1].addr.back().pageId;
    // 由于第一个页面数据1更新成了跳转行，此时不断插入数据2，直到第一个页面被填满插入第二个页面
    do {
        ret = heapHandle.insert(heapRunCtx, &buf[1], &opInfo[2]);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[2].rowType);
        if (pageAddr != opInfo[2].addr.back().pageId) {
            DB_ASSERT(opInfo[2].addr.back().pageId == opInfo[0].addr.back().pageId);
        }
    } while (pageAddr != opInfo[2].addr.back().pageId);
    // 将第二条数据更新成页面的80%
    opInfo[1].copy(buf[2].bufSize, buf[2].buf);
    // 更新第二条数据，再生成一次跳转行
    rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 此时dstRow应该去一个新页
    EXPECT_NE(pageAddr, opInfo[1].addr.back().pageId);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Link_Row_To_Link_Row_1)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);

    // 启动事务1
    SeRunCtxT *transaction1 = CreateSeRunCtx();
    StatusInter ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据
    HeapTupleBufT buf1[2];
    buf1[0] = heapHandle.generateData(heapRunCtx1, 0.7);
    ASSERT_NE(nullptr, buf1[0].buf);
    buf1[1] = heapHandle.generateData(heapRunCtx1, 0.8);
    ASSERT_NE(nullptr, buf1[1].buf);

    // 启动事务2
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 生成一条数据
    HeapTupleBufT buf2 = heapHandle.generateData(heapRunCtx2, 0.2);
    ASSERT_NE(nullptr, buf2.buf);

    // 插入前两条
    HTHRowInfoT opInfo1;
    ret = heapHandle.insert(heapRunCtx1, buf1, &opInfo1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo1.rowType);
    HTHRowInfoT opInfo2;
    ret = heapHandle.insert(heapRunCtx2, &buf2, &opInfo2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo2.rowType);
    ASSERT_EQ(opInfo1.addr.front().pageId, opInfo2.addr.front().pageId);

    // 将第一条数据更新成页面的80%, 更新第一条数据，生成一次跳转行
    opInfo1.copy(buf1[1].bufSize, buf1[1].buf);
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 删除事务二插入的数据，把页面空出来
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx2, &opInfo2, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx2);
    ReleaseSeRunCtx(transaction2);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 更新第二条数据，再生成一次跳转行,此时应该发生页面整理，数据原地更新
    rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    ReleaseSeRunCtx(seRunCtx);
}

static StatusInter HeapFetchVarRowInPageAgainStub(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    // 此时预期curRowInfo就是srcRowInfo，所以将curRowInfo的rowHeadPtr随机偏移一下，HeapVarPageGetRowState会纠正这个偏移
    // 用于模拟relatch + compress的情况
    fetchRowInfo->curRowInfo->rowHeadPtr.rowState += 2;
    HVPageHeadT *pageHead = fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
    fetchRowInfo->srcRowInfo.rowHeadPtr.rowState = HeapVarPageGetRowState(pageHead, fetchRowInfo->srcRowInfo.slot);
    // 这个接口内部操作的是curRowInfo，如果curRowInfo不是srcRowInfo，那么这里应该会core（内部有魔法数字检查）
    return HeapFetchVarRowSetRowInfo(ctx, opInfo);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Link_Row_To_Link_Row_2)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);

    // 启动事务1
    SeRunCtxT *transaction1 = CreateSeRunCtx();
    StatusInter ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据
    HeapTupleBufT buf1[2];
    buf1[0] = heapHandle.generateData(heapRunCtx1, 0.7);
    ASSERT_NE(nullptr, buf1[0].buf);
    buf1[1] = heapHandle.generateData(heapRunCtx1, 0.8);
    ASSERT_NE(nullptr, buf1[1].buf);

    // 启动事务2
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 生成一条数据
    HeapTupleBufT buf2 = heapHandle.generateData(heapRunCtx2, 0.2);
    ASSERT_NE(nullptr, buf2.buf);

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    // 插入数据前，先申请两个页，保证总会做到跳转行申请的页ID小于外部行，让外部行发生relatch
    PageIdT pageAddr[2];
    // 持久化不使用rsmUndo
    AllocPageParamT allocPageParam =
        SeInitAllocPageParam(DB_DEFAULT_TABLE_SPACE_INDEX, 1, RSM_INVALID_LABEL_ID, NULL, NULL);
    ret = SeAllocPage((PageMgrT *)transaction1->pageMgr, &allocPageParam, &pageAddr[0]);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = SeAllocPage((PageMgrT *)transaction1->pageMgr, &allocPageParam, &pageAddr[1]);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    RedoLogEnd(redoCtx, true);

    // 插入前两条
    HTHRowInfoT opInfo1;
    ret = heapHandle.insert(heapRunCtx1, buf1, &opInfo1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo1.rowType);
    HTHRowInfoT opInfo2;
    // 插入其他的行
    ret = heapHandle.insert(heapRunCtx2, &buf2, &opInfo2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo2.rowType);
    ASSERT_EQ(opInfo1.addr.front().pageId, opInfo2.addr.front().pageId);

    // 将第一条数据更新成页面的80%, 更新第一条数据，生成一次跳转行
    opInfo1.copy(buf1[1].bufSize, buf1[1].buf);
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    RedoLogBegin(redoCtx);
    FreePageParamT freePageParam0 =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, pageAddr[0], NULL, NULL, SE_INVALID_LABEL_ID, false);
    ret = SeFreePage((PageMgrT *)transaction1->pageMgr, &freePageParam0);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    FreePageParamT freePageParam1 =
        SeInitFreePageParam(DB_DEFAULT_TABLE_SPACE_INDEX, pageAddr[1], NULL, NULL, SE_INVALID_LABEL_ID, false);
    ret = SeFreePage((PageMgrT *)transaction1->pageMgr, &freePageParam1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    RedoLogEnd(redoCtx, true);

    int stubIdx = setStubC((void *)HeapVarRowRefetch, (void *)HeapFetchVarRowInPageAgainStub);
    // 这个时候如果新的跳转行pageId小于外部行的pageId，就应该发生relatch
    do {
        ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        if (opInfo1.addr[0].pageId > opInfo1.addr[1].pageId) {
            break;
        }
    } while (true);
    clearStub(stubIdx);

    // 提交 清理
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx2);
    ret = heapHandle.transCommit(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction2);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Link_Row_To_Normal_Row)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的10%
    opInfo[1].copy(buf[1].bufSize, buf[1].buf);
    // 更新第二条数据，此时可以原地更新了，应该变回normal row
    rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Delete_Normal_Row)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    HeapRunCtxT *heapRunCtx = NULL;

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.8);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 全部删除
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得到0条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)0, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Delete_Link_Row_0)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据，刚好能凑满一个页
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.2);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[2];
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，此时应该变成跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 全部删除
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得到0条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)0, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Delete_Link_Row_2)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据，刚好能凑满一个页
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.2);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[2];
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，此时应该变成跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 删除后rollback
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得2条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)2, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

static StatusInter HeapFsmGetPageBySizeStub(HeapRunCtxT *ctx, PageSizeT requireSize, HeapPageAllocCtx *heapPageAllocCtx)
{
    return OUT_OF_MEMORY_INTER;
}

static StatusInter TrxUndoReportRowOperationStub(
    SeUndoCtxT *undoCtx, TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t *rollPtr)
{
    return OUT_OF_MEMORY_INTER;
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    // 构造数据页申请不足的场景
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Failed_Case_Undo_Out_Of_Memory_0)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)TrxUndoReportRowOperation, (void *)TrxUndoReportRowOperationStub);
    ASSERT_NE(0, stubIdx);
    // 构造Undo空间不足，回滚
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Insert_Failed_Case_Undo_Out_Of_Memory_1)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo[10];
    HeapTupleBufT buf[10];
    for (uint32_t i = 0; i < 10; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.7);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 10, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)TrxUndoReportRowOperation, (void *)TrxUndoReportRowOperationStub);
    ASSERT_NE(0, stubIdx);
    // 构造Undo空间不足，回滚
    ret = heapHandle.insert(heapRunCtx, buf, opInfo);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Normal_To_Normal_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 这里即使给申请页的接口打桩，依旧不会失败，因为是原地更新不需要多余的页面
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Normal_To_Link_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成两条数据，刚好能凑满一个页
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.2);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[2];
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 构造更新生成跳转行，申请不到页面的场景
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    // 更新第二条数据，此时应该变成跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Link_To_Link_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[3];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[1].buf);
    buf[2] = heapHandle.generateData(heapRunCtx, 0.8);
    ASSERT_NE(nullptr, buf[2].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将dst row的页面地址存储下来
    uint32_t pageAddr = opInfo[1].addr.back().pageId;
    // 由于第一个页面数据1更新成了跳转行，此时不断插入数据2，直到第一个页面被填满插入第二个页面
    do {
        ret = heapHandle.insert(heapRunCtx, &buf[1], &opInfo[2]);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[2].rowType);
        if (pageAddr != opInfo[2].addr.back().pageId) {
            DB_ASSERT(opInfo[2].addr.back().pageId == opInfo[0].addr.back().pageId);
        }
    } while (pageAddr != opInfo[2].addr.back().pageId);
    // 将第二条数据更新成页面的80%
    opInfo[1].copy(buf[2].bufSize, buf[2].buf);
    // 构造更新生成跳转行，申请不到页面的场景
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Link_To_Normal_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 生成三条数据
    HeapTupleBufT buf[2];
    buf[0] = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf[0].buf);
    buf[1] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[1].buf);
    HTHRowInfoT opInfo[3];
    // 插入前两条
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, 2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[0].rowType);
    ASSERT_EQ(HEAP_VAR_NORMAL_ROW, opInfo[1].rowType);
    // 将第二条数据更新成页面的70%
    opInfo[1].copy(buf[0].bufSize, buf[0].buf);
    // 更新第二条数据，生成一次跳转行
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 将第二条数据更新成页面的10%
    opInfo[1].copy(buf[1].bufSize, buf[1].buf);
    // 构造更新生成跳转行，申请不到页面的场景, 但由于回到原地更新，此时不需要多余的页面，应该依旧可以成功
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    // 更新第二条数据，此时可以原地更新了，应该变回normal row
    rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo[1], &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

// 因为update都是提前记Undo的，不论任何场景，只用测一种情况即可
TEST_F(HeapRowLockPersistentTest, Var_Heap_Update_Failed_Case_Undo_Out_Of_Memory)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx, 0.7);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)TrxUndoReportRowOperation, (void *)TrxUndoReportRowOperationStub);
    ASSERT_NE(0, stubIdx);
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo, &rowType);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Trasaction_Normal_Row_Version_Chain)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    g_heapHandle = &heapHandle;
    HeapTupleBufT buf[4];

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    for (uint32_t i = 0; i < 4; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.1);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    HTHRowInfoT opInfo;
    ret = heapHandle.insert(heapRunCtx, &buf[0], &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    SeRunCtxT *transaction1 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo1 = opInfo;
    opInfo1.copy(buf[1].bufSize, buf[1].buf);
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo2 = opInfo;
    opInfo2.copy(buf[2].bufSize, buf[2].buf);
    SeRunCtxT *transaction3 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx3 = NULL;
    ret = heapHandle.allocRunCtx(transaction3, &heapRunCtx3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo3 = opInfo;
    opInfo3.copy(buf[3].bufSize, buf[3].buf);

    // 三个事务，更新同一行数据，应该产生三个版本
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx2, &opInfo2, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx2, &opInfo3, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> versionChain;
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)3, versionChain.size());

    // 此时回滚事务2，版本链变为两个
    heapHandle.releaseRunCtx(heapRunCtx2);
    ret = heapHandle.transRollback(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction2);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)2, versionChain.size());

    // 提交事务1，回滚事务3，版本链上只有事务1提交的版本和最老的版本
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    heapHandle.releaseRunCtx(heapRunCtx3);
    ret = heapHandle.transRollback(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction3);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)2, versionChain.size());

    // 拉起事务4
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.fetch(heapRunCtx, &opInfo.addr.front(), &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 此时应该读到事务1的更新
    ASSERT_TRUE(opInfo1 == opInfo);
    heapHandle.releaseRunCtx(heapRunCtx);
    ReleaseSeRunCtx(seRunCtx);
    TriggerPurger(GET_INSTANCE_ID);
    g_heapHandle = NULL;
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Trasaction_Long_Normal_Row_Version_Chain)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    g_heapHandle = &heapHandle;
    HeapTupleBufT buf;

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    buf = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf.buf);
    HTHRowInfoT opInfo;
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    uint32_t chainLen = 1000;
    SeRunCtxT *transaction = CreateSeRunCtx();
    HeapRunCtxT *transCtx = NULL;
    ret = heapHandle.allocRunCtx(transaction, &transCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    for (uint32_t i = 1; i < chainLen; ++i) {
        ret = heapHandle.transBegin(transaction);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ret = heapHandle.update(transCtx, &opInfo, &rowType);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ret = heapHandle.transCommit(transaction);
        ASSERT_EQ(STATUS_OK_INTER, ret);
    }
    heapHandle.releaseRunCtx(transCtx);
    ReleaseSeRunCtx(transaction);

    std::vector<HTHRowInfoT> versionChain;
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)chainLen, versionChain.size());

    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    TriggerPurger(GET_INSTANCE_ID);
    heapHandle.releaseRunCtx(heapRunCtx);
    ReleaseSeRunCtx(seRunCtx);
    TriggerPurger(GET_INSTANCE_ID);
    g_heapHandle = NULL;
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Trasaction_Link_Row_Version_Chain_0)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    g_heapHandle = &heapHandle;
    HeapTupleBufT buf[4];

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    for (uint32_t i = 0; i < 4; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.7);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    HTHRowInfoT opInfo;
    ret = heapHandle.insert(heapRunCtx, &buf[0], &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    SeRunCtxT *transaction1 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo1 = opInfo;
    opInfo1.copy(buf[1].bufSize, buf[1].buf);
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo2 = opInfo;
    opInfo2.copy(buf[2].bufSize, buf[2].buf);
    SeRunCtxT *transaction3 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx3 = NULL;
    ret = heapHandle.allocRunCtx(transaction3, &heapRunCtx3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo3 = opInfo;
    opInfo3.copy(buf[3].bufSize, buf[3].buf);

    // 三个事务，更新同一行数据，应该产生四个版本
    // trx3->trx2->trx1->committed data
    HTHRowTypeE rowType = HEAP_VAR_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx2, &opInfo2, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx3, &opInfo3, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> versionChain;
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)4, versionChain.size());

    // 此时回滚事务2，版本链变为三个
    // trx3->trx1->committed data
    heapHandle.releaseRunCtx(heapRunCtx2);
    // 走 HeapUpdateRollbackInVersionChain
    ret = heapHandle.transRollback(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction2);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)3, versionChain.size());

    // 提交事务1，回滚事务3，版本链上只有事务1提交的版本和最老的版本
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    heapHandle.releaseRunCtx(heapRunCtx3);
    ret = heapHandle.transRollback(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction3);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)2, versionChain.size());

    // 拉起事务4
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.fetch(heapRunCtx, &opInfo.addr.front(), &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 此时应该读到事务1的更新
    ASSERT_TRUE(opInfo1 == opInfo);
    heapHandle.releaseRunCtx(heapRunCtx);
    ReleaseSeRunCtx(seRunCtx);
    TriggerPurger(GET_INSTANCE_ID);
    g_heapHandle = NULL;
}

TEST_F(HeapRowLockPersistentTest, Var_Heap_Trasaction_Link_Row_Version_Chain_1)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    g_heapHandle = &heapHandle;
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    HeapTupleBufT buf[4];

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    for (uint32_t i = 0; i < 4; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.8);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    HTHRowInfoT opInfo;
    ret = heapHandle.insert(heapRunCtx, &buf[0], &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    buf[0] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[0].buf);
    ret = heapHandle.insert(heapRunCtx, &buf[0], &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    SeRunCtxT *transaction1 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo1 = opInfo;
    opInfo1.copy(buf[1].bufSize, buf[1].buf);
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo2 = opInfo;
    opInfo2.copy(buf[2].bufSize, buf[2].buf);
    SeRunCtxT *transaction3 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx3 = NULL;
    ret = heapHandle.allocRunCtx(transaction3, &heapRunCtx3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo3 = opInfo;
    opInfo3.copy(buf[3].bufSize, buf[3].buf);

    // 三个事务，更新同一行数据，应该产生四个版本
    // trx3->trx2->trx1->committed data
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx2, &opInfo2, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx3, &opInfo3, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> versionChain;
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)4, versionChain.size());

    // 此时回滚事务2，版本链变为三个
    // trx3->trx1->committed data
    heapHandle.releaseRunCtx(heapRunCtx2);
    ret = heapHandle.transRollback(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction2);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)3, versionChain.size());

    // 提交事务1，回滚事务3，版本链上只有事务1提交的版本和最老的版本
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    heapHandle.releaseRunCtx(heapRunCtx3);
    ret = heapHandle.transRollback(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction3);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)2, versionChain.size());
    TriggerPurger(GET_INSTANCE_ID);
    g_heapHandle = NULL;
}

static HpItemPointerT g_nextItemPtr = {DB_MAX_UINT32, DB_MAX_UINT32};
static StatusInter HeapLocateLinkDstVarRowStub(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (fetchRowInfo->nextItemPtr.pageId != g_nextItemPtr.pageId ||
        fetchRowInfo->nextItemPtr.slotId != g_nextItemPtr.slotId) {
        g_nextItemPtr = fetchRowInfo->nextItemPtr;
        return NO_DATA_HEAP_PAGE_NOT_EXIST;
    }
    if (!HeapIsValidAddr(fetchRowInfo->nextItemPtr)) {
        // 在脏读情况下, 可能查询到未关联 linkDst 的 linkSrc. 当前分析, 应该只有大对象分片有这个状态:
        // 事务A刚刚插入了linkSrc(大对象分片), 但是linkDst和各个subRows还没有插入; 此时, 其他事务扫描到该 linkSrc,
        // 则它的addr无效
        DB_ASSERT(!HeapIsOnNormalTrxLockMode(ctx) && fetchRowInfo->isSliceRows);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    StatusInter ret;
    uint32_t tryTimes = 0;
    const uint32_t maxRetry = 10;  // 加上重试上限，避免并发问题导致死循.
    bool isSrcRowReLatch;
    for (; tryTimes < maxRetry; tryTimes++) {
        // 设置访问 dstRow, 继续访问
        fetchRowInfo->curRowInfo = &fetchRowInfo->dstRowInfo;
        fetchRowInfo->curRowInfo->itemPtr = fetchRowInfo->nextItemPtr;
        fetchRowInfo->canAccessLinkDst = true;

        ret = HeapFetchGetPage(ctx, fetchRowInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "fetch page unsucc, pageId:%" PRIu32 ", slotId:%" PRIu32,
                fetchRowInfo->curRowInfo->itemPtr.pageId, fetchRowInfo->curRowInfo->itemPtr.slotId);
            return ret;
        }

        // 跨页访问, 可能有reLatch. 检查一下 主行(linkSrc)是否有变化
        isSrcRowReLatch = SePgLatchSorterIsPageReLatch(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
        if (SECUREC_UNLIKELY(isSrcRowReLatch)) {
            // 清除 reLatch标记, 然后重新访问linkSrc, 看看发生改变
            SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
            bool isPageChange = false;
            ret = HeapReLocateLinkSrcAfterReLatch(ctx, opInfo, &isPageChange);
            if (ret != STATUS_OK_INTER || fetchRowInfo->isGetBuf) {
                return ret;
            }
            if (isPageChange) {  // page改变, 需要重新访问
                continue;
            }
        }
        break;
    }
    if (tryTimes == maxRetry) {
        // 重试达到循环上限,未获取到数据
        DB_LOG_WARN(GMERR_NO_DATA, "try fetch tuple's dstRow too much, pageId:%" PRIu32 ", slotId:%" PRIu32,
            opInfo->fetchRowInfo->srcRowInfo.itemPtr.pageId, opInfo->fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    // 从page中获取 linkDstRow
    ret = HeapFetchVarRowInPage(ctx, opInfo);
    return HeapLocateLinkDstVarRowCheck(ctx, fetchRowInfo, tryTimes, isSrcRowReLatch, ret);
}
// 同样的步骤，构造DstPage找不到的场景
TEST_F(HeapRowLockPersistentTest, Var_Heap_Trasaction_Link_Row_Version_Chain_2)
{
    int stubIdx = setStubC((void *)HeapLocateLinkDstVarRow, (void *)HeapLocateLinkDstVarRowStub);
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = maxItemNum,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    g_heapHandle = &heapHandle;
    HTHRowTypeE rowType = HEAP_VAR_LINK_ROW;
    HeapTupleBufT buf[4];

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    for (uint32_t i = 0; i < 4; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx, 0.8);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    HTHRowInfoT opInfo;
    ret = heapHandle.insert(heapRunCtx, &buf[0], &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    buf[0] = heapHandle.generateData(heapRunCtx, 0.1);
    ASSERT_NE(nullptr, buf[0].buf);
    ret = heapHandle.insert(heapRunCtx, &buf[0], &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    SeRunCtxT *transaction1 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx1 = NULL;
    ret = heapHandle.allocRunCtx(transaction1, &heapRunCtx1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo1 = opInfo;
    opInfo1.copy(buf[1].bufSize, buf[1].buf);
    SeRunCtxT *transaction2 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx2 = NULL;
    ret = heapHandle.allocRunCtx(transaction2, &heapRunCtx2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo2 = opInfo;
    opInfo2.copy(buf[2].bufSize, buf[2].buf);
    SeRunCtxT *transaction3 = CreateSeRunCtx();
    ret = heapHandle.transBegin(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx3 = NULL;
    ret = heapHandle.allocRunCtx(transaction3, &heapRunCtx3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo3 = opInfo;
    opInfo3.copy(buf[3].bufSize, buf[3].buf);

    // 三个事务，更新同一行数据，应该产生四个版本
    // trx3->trx2->trx1->committed data
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx2, &opInfo2, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx3, &opInfo3, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.update(heapRunCtx1, &opInfo1, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> versionChain;
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)4, versionChain.size());

    // 此时回滚事务2，版本链变为三个
    // trx3->trx1->committed data
    heapHandle.releaseRunCtx(heapRunCtx2);
    ret = heapHandle.transRollback(transaction2);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction2);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)3, versionChain.size());

    // 提交事务1，回滚事务3，版本链上只有事务1提交的版本和最老的版本
    heapHandle.releaseRunCtx(heapRunCtx1);
    ret = heapHandle.transCommit(transaction1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction1);
    heapHandle.releaseRunCtx(heapRunCtx3);
    ret = heapHandle.transRollback(transaction3);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(transaction3);
    versionChain.clear();
    ret = heapHandle.fetchVersionChain(heapRunCtx, opInfo.addr.front(), &versionChain);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)2, versionChain.size());

    clearStub(stubIdx);
    TriggerPurger(GET_INSTANCE_ID);
    g_heapHandle = NULL;
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Insert_Normal_Row_0)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Insert_Normal_Row_1)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Update_Normal_Row)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowTypeE rowType = HEAP_FIX_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Delete_Normal_Row_0)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    HeapRunCtxT *heapRunCtx = NULL;

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 全部删除
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得到0条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)0, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Delete_Normal_Row_1)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    HeapRunCtxT *heapRunCtx = NULL;

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 删除，然后回滚
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得到100条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)100, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Delete_Normal_Row_2)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    HeapRunCtxT *heapRunCtx = NULL;

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 全部删除
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, insertNum, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得到0条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)0, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Delete_Normal_Row_3)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    HeapRunCtxT *heapRunCtx = NULL;

    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t insertNum = 100;
    HeapTupleBufT buf[insertNum];
    HTHRowInfoT opInfo[insertNum];
    for (uint32_t i = 0; i < insertNum; ++i) {
        buf[i] = heapHandle.generateData(heapRunCtx);
        ASSERT_NE(nullptr, buf[i].buf);
    }
    ret = heapHandle.insert(heapRunCtx, buf, opInfo, insertNum);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 删除，然后回滚
    ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.erase(heapRunCtx, opInfo, true, insertNum, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // 扫描Heap，应该得到100条数据
    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    std::vector<HTHRowInfoT> scanInfo;
    ret = heapHandle.scan(heapRunCtx, &scanInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ((size_t)100, scanInfo.size());
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Insert_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    // 构造数据页申请不足的场景
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Insert_Failed_Case_Undo_Out_Of_Memory)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)TrxUndoReportRowOperation, (void *)TrxUndoReportRowOperationStub);
    ASSERT_NE(0, stubIdx);
    // 构造Undo空间不足，回滚
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

TEST_F(HeapRowLockPersistentTest, Fix_Heap_Update_Failed_Case_Data_Out_Of_Memory)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    // 这里即使给申请页的接口打桩，依旧不会失败，因为是原地更新不需要多余的页面
    int stubIdx = setStubC((void *)HeapFsmGetPageBySize, (void *)HeapFsmGetPageBySizeStub);
    ASSERT_NE(0, stubIdx);
    HTHRowTypeE rowType = HEAP_FIX_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo, &rowType);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

// 因为update都是提前记Undo的，不论任何场景，只用测一种情况即可
TEST_F(HeapRowLockPersistentTest, Fix_Heap_Update_Failed_Case_Undo_Out_Of_Memory)
{
    HeapAccessCfgT fixConfig = {
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 200,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = fixLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &fixConfig, (void *)fixLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    int stubIdx = setStubC((void *)TrxUndoReportRowOperation, (void *)TrxUndoReportRowOperationStub);
    ASSERT_NE(0, stubIdx);
    HTHRowTypeE rowType = HEAP_FIX_NORMAL_ROW;
    ret = heapHandle.update(heapRunCtx, &opInfo, &rowType);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    clearStub(stubIdx);
    heapHandle.releaseRunCtx(heapRunCtx);
    ret = heapHandle.transRollback(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ReleaseSeRunCtx(seRunCtx);
}

static StatusInter RedoFlushOnTrxCommitStub(RedoMgrT *redoMgr)
{
    HeapRowLockPersistentTest::stubForRedo++;
    return STATUS_OK_INTER;
}
// redoFlushByTrx 设置为只读不应该刷盘
TEST_F(HeapRowLockPersistentTest, redoFlushByTrx_readOnlyTrx)
{
    HeapAccessCfgT varConfig = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = DbGetProcGlobalId(),
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = PESSIMISTIC_TRX,
        .isolation = READ_COMMITTED,
        .labelId = varLabel->metaCommon.metaId,
        .heapFileId = 1,
        .heapFsmFileId = 2,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = 100,
    };
    SeRunCtxT *seRunCtx = CreateSeRunCtx();
    HeapTestHelper heapHandle(seRunCtx, &varConfig, (void *)varLabel);
    StatusInter ret = heapHandle.transBegin(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HeapRunCtxT *heapRunCtx = NULL;
    ret = heapHandle.allocRunCtx(seRunCtx, &heapRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    HTHRowInfoT opInfo;
    HeapTupleBufT buf = heapHandle.generateData(heapRunCtx, 0.8);
    ASSERT_NE(nullptr, buf.buf);
    ret = heapHandle.insert(heapRunCtx, &buf, &opInfo, 1);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    ret = heapHandle.transBegin(seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = heapHandle.fetch(heapRunCtx, &opInfo.addr.front(), &opInfo);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    auto stubcId = setStubC((void *)RedoFlushOnTrxCommit, (void *)RedoFlushOnTrxCommitStub);
    EXPECT_GT(stubcId, 0);
    // 只读，预期commit 不会进入RedoFlushOnTrxCommitStub
    ret = heapHandle.transCommit(seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    clearStub(stubcId);
    ASSERT_EQ(HeapRowLockPersistentTest::stubForRedo, 0);  // not coming RedoFlushOnTrxCommitStub
    ReleaseSeRunCtx(seRunCtx);
}
