/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: persistence ctrl file ut
 * Author:
 * Create: 2025-3-1
 */
#include "gtest/gtest.h"
#include "stub.h"
#include "securec.h"
#include "se_persist.h"
#include "persistence_common.h"
#include "common_init.h"
#include "heap_util_ut.h"
#include "se_undo_inner.h"
#include "ee_init.h"
#include "se_database.h"
#include "se_space_ddl.h"

const char *g_oldDir = "/data/gmdb";
const char *g_newDir = "/data/gmdb_new";

class DirSwitchUt : public PersistenceUtTest, public testing::Test {
public:
    static int stubNo;

protected:
    virtual void SetUp()
    {
        system("rm -rf /data/gmdb");
        system("rm -rf /data/gmdb_new");
        system("ipcrm -a");
        DbSetServerThreadFlag();
        ConstructSeInsAndSeRun();
    }

    virtual void TearDown()
    {
        DestroySeIns();
        CommonRelease();
        DbClearServerThreadFlag();
        system("rm -rf /data/gmdb");
        system("rm -rf /data/gmdb_new");
    }

    static void SetUpTestCase(){};

    static void TearDownTestCase(){};
};

int DirSwitchUt::stubNo = 0;

Status DbPwriteFileError(int32_t fd, const char *buf, uint32_t count, int64_t offset)
{
    return GMERR_FILE_OPERATE_FAILED;
}

TEST_F(DirSwitchUt, DiskErrorAndSwap)
{
    Status ret;
    ASSERT_EQ(GMERR_OK, UtTransBegin(PersistenceUtTest::seRunCtx, PESSIMISTIC_TRX, READ_COMMITTED));
    SeSpaceCreateCfgT baseCfg;
    baseCfg.maxSize = SIZE_T(1);
    errno_t err = sprintf_s(baseCfg.fileName, DB_MAX_NAME_LEN, "testSpaceName");
    ASSERT_GE(err, 0);

    uint32_t spcId;
    StatusInter status = SeSpaceCreateImpl(PersistenceUtTest::seRunCtx, &baseCfg, &spcId);
    ASSERT_EQ(status, STATUS_OK_INTER);

    // 提交事务
    ASSERT_EQ(GMERR_OK, SeTransCommit(PersistenceUtTest::seRunCtx));

    SeSetCurRedoCtx((RedoRunCtxT *)PersistenceUtTest::seRunCtx->redoCtx);
    AllocPageParamT param = {spcId, 0, 0, NULL, NULL};
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    RedoLogBegin(SeGetCurRedoCtx());
    ret = SeAllocPage((PageMgrT *)PersistenceUtTest::seIns->pageMgr, &param, &pageAddr);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = RedoLogEnd(SeGetCurRedoCtx(), true);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    DirSwitchUt::stubNo = setStub(DbPwriteFile, DbPwriteFileError);
    ASSERT_TRUE(DirSwitchUt::stubNo >= 0);

    // 磁盘故障，会触发锁库
    ret = CkptTrigger(PersistenceUtTest::seIns, CKPT_MODE_FULL, true, WAIT_MSECONDS_FOR_CHECKPOINT);
    ASSERT_EQ(ret, GMERR_DATABASE_NOT_AVAILABLE);

    clearStub(DirSwitchUt::stubNo);

    ret = SeUnloadWorkingDir(PersistenceUtTest::seIns);
    ASSERT_EQ(ret, GMERR_OK);

    SeSetStorageNotEmergency(PersistenceUtTest::seIns);

    ret = SeLoadWorkingDir(PersistenceUtTest::seIns, g_newDir);
    ASSERT_EQ(ret, GMERR_OK);

    // 切换目录成功后，会解除锁库
    ret = SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE);
    ASSERT_EQ(ret, GMERR_OK);

    ret = CkptTrigger(PersistenceUtTest::seIns, CKPT_MODE_FULL, true, WAIT_MSECONDS_FOR_CHECKPOINT);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(DirSwitchUt, LoadError)
{
    // 卸载目录A
    Status ret = SeUnloadWorkingDir(PersistenceUtTest::seIns);
    ASSERT_EQ(ret, GMERR_OK);

    // 切入目录B
    ret = SeLoadWorkingDir(PersistenceUtTest::seIns, g_newDir);
    ASSERT_EQ(ret, GMERR_OK);

    // 切入目录B后redo重演失败，需要卸载目录B
    ret = SeUnloadWorkingDir(PersistenceUtTest::seIns);
    ASSERT_EQ(ret, GMERR_OK);

    // 上层再次调用目录切换，会check新目录B，因为目录B没有创建完成，是一个损坏的目录，无法切换
    ret = SeCheckNewWorkingDir(PersistenceUtTest::seIns, g_oldDir, g_newDir);
    ASSERT_EQ(ret, GMERR_DATA_CORRUPTION);
}

Status QryRebuildVertexLabelIndexStub(SeRunCtxHdT seRunCtx)
{
    return STATUS_OK_INTER;
}

TEST_F(DirSwitchUt, DISABLED_Base)
{
    // ut没有创建系统表
    int stubNo = setStub(QryRebuildVertexLabelIndex, QryRebuildVertexLabelIndexStub);
    ASSERT_TRUE(stubNo >= 0);

    // step 1:
    // 在路径1插入数据
    Status ret;
    ShmemPtrT heapShm;
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm);
    HpItemPointerT addr;
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr, 0.2));
    ASSERT_EQ(0, ret);
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr));
    ASSERT_EQ(0, ret);

    // step 2:
    // 预检查一下新路径
    ret = SeCheckNewWorkingDir(PersistenceUtTest::seIns, g_oldDir, g_newDir);
    ASSERT_EQ(ret, GMERR_OK);

    // 清理Heap内存资源; 需要上层遍历所有Heap调用HeapLabelReleaseMemFields来重置内存资源
    ret = HeapLabelReleaseMemFields(PersistenceUtTest::seRunCtx, heapShm);
    ASSERT_EQ(ret, GMERR_OK);

    // 切换路径
    ret = SeUnloadWorkingDir(PersistenceUtTest::seIns);
    ASSERT_EQ(ret, GMERR_OK);
    ret = SeLoadWorkingDir(PersistenceUtTest::seIns, g_newDir);
    ASSERT_EQ(ret, GMERR_OK);

    // 在路径2插入数据
    ShmemPtrT heapShm2;
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm2);
    HpItemPointerT addr2;
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOp(heapShm2, HEAP_OPTYPE_INSERT, &addr2, 0.2));
    ASSERT_EQ(0, ret);
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOpFetch(heapShm2, HEAP_OPTYPE_NORMALREAD, &addr2));
    ASSERT_EQ(0, ret);

    // step 3:
    // 检查一下旧路径
    ret = SeCheckNewWorkingDir(PersistenceUtTest::seIns, g_newDir, g_oldDir);
    ASSERT_EQ(ret, GMERR_OK);
    // 清理Heap内存资源
    ret = HeapLabelReleaseMemFields(PersistenceUtTest::seRunCtx, heapShm2);
    ASSERT_EQ(ret, GMERR_OK);

    // 切换回路径1
    ret = SeUnloadWorkingDir(PersistenceUtTest::seIns);
    ASSERT_EQ(ret, GMERR_OK);
    ret = SeLoadWorkingDir(PersistenceUtTest::seIns, g_oldDir);
    ASSERT_EQ(ret, GMERR_OK);

    SeSetCurRedoCtx(PersistenceUtTest::redoCtx);
    // 重新初始化Heap内存态资源
    ret = HeapLabelInitMemFields(PersistenceUtTest::seRunCtx, heapShm);
    ASSERT_EQ(ret, GMERR_OK);

    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr));
    ASSERT_EQ(0, ret);

    clearStub(stubNo);
}

StatusInter SeCtrlInitImplOOM(SeInstanceT *seIns)
{
    return OUT_OF_MEMORY_MEM_FAILED;
}

TEST_F(DirSwitchUt, DISABLED_Abnormal)
{
    // ut没有创建系统表
    int stubNo = setStub(QryRebuildVertexLabelIndex, QryRebuildVertexLabelIndexStub);
    ASSERT_TRUE(stubNo >= 0);

    // step 1:
    // 在路径1插入数据
    Status ret;
    ShmemPtrT heapShm;
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm);
    HpItemPointerT addr;
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr, 0.2));
    ASSERT_EQ(0, ret);
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr));
    ASSERT_EQ(0, ret);

    // step 2:
    // 预检查一下新路径
    ret = SeCheckNewWorkingDir(PersistenceUtTest::seIns, g_oldDir, g_newDir);
    ASSERT_EQ(ret, GMERR_OK);

    // 清理Heap内存资源; 需要上层遍历所有Heap调用HeapLabelReleaseMemFields来重置内存资源
    ret = HeapLabelReleaseMemFields(PersistenceUtTest::seRunCtx, heapShm);
    ASSERT_EQ(ret, GMERR_OK);

    // 切换路径时，内存不足
    ret = SeUnloadWorkingDir(PersistenceUtTest::seIns);
    ASSERT_EQ(ret, GMERR_OK);
    int errStub = setStub(SeCtrlInitImpl, SeCtrlInitImplOOM);
    ret = SeLoadWorkingDir(PersistenceUtTest::seIns, g_newDir);
    ASSERT_EQ(ret, GMERR_OUT_OF_MEMORY);
    clearStub(errStub);

    // step 3:
    // 切换回路径1，内存已经恢复
    ret = SeLoadWorkingDir(PersistenceUtTest::seIns, g_oldDir);
    ASSERT_EQ(ret, GMERR_OK);

    SeSetCurRedoCtx(PersistenceUtTest::redoCtx);
    // 重新初始化Heap内存态资源
    ret = HeapLabelInitMemFields(PersistenceUtTest::seRunCtx, heapShm);
    ASSERT_EQ(ret, GMERR_OK);

    // 检查heap数据
    WITH_TRX(PersistenceUtTest::seRunCtx, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr));
    ASSERT_EQ(0, ret);
}
