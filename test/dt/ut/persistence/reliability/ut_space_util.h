/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: ut space header file for pege leak
 */

#ifndef UT_SPACE_H
#define UT_SPACE_H

#include "se_space_inner.h"

extern "C" {
StatusInter SpaceGetFreePageCnt(SeInstanceT *seIns, SpaceT *space, uint32_t *totalFreePageCnt);
}

class UtSpaceUtil {
public:
    // space已分配的页的水位线，只增不减
    static uint32_t SpaceGetAllocedPageHwm(SeInstanceT *seIns, SpaceT *space)
    {
        return space->usedSize / SIZE_K(seIns->seConfig.pageSize);
    }

    static uint32_t SpaceGetFreePageNum(SeInstanceT *seIns, SpaceT *space)
    {
        uint32_t totalFreePageCnt = 0;
        StatusInter ret = SpaceGetFreePageCnt(seIns, space, &totalFreePageCnt);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        return totalFreePageCnt;
    }
};
#endif
