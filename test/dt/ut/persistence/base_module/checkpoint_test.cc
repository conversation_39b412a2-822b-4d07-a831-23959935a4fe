/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: ut test for checkpoint
 * Author:wangyixuan
 * Create: 2024-2-4
 */
#include <vector>
#include "persistence_common.h"
#include "adpt_sleep.h"
#include "se_ckpt_inner.h"
#include "se_database.h"
#include "se_space.h"
#include "se_redo_inner.h"
#include "se_page_mgr.h"
#include "se_redo.h"
#include "se_datafile.h"
#include "se_database.h"
#include "stub.h"
#include "se_buffer_pool_inner.h"
#include "se_buffer_pool_bucket.h"

typedef struct BufEnqueArg BufEnqueArgT;

using namespace std;
extern "C" {
void CkptClearGroup(SeInstanceT *seInsPtr);
bool CkptPushGroup(CkptGroupT *const group, BufDescT *buf);
void CkptSortGroup(CkptGroupT *group);
void CkptEnQueue(SeInstanceT *seInsPtr, BufDescT *buf);
void CkptPopQueue(SeInstanceT *seInsPtr, BufDescT *buf);
BufDescT *CkptDeQueue(SeInstanceT *seInsPtr);
void CkptReset(SeInstanceT *seIns);
StatusInter CkptFlush(SeInstanceT *seInsPtr);
StatusInter CkptFlushItem(CkptCtxT *ckptCtx, const CkptSortItemT *item, uint32_t zoneId);
StatusInter CkptUpdateCoreRedoPoint(SeInstanceT *seInsPtr);
Status StartupDatabase(SeInstanceT *seIns);
StatusInter RedoLogFileFlush(RedoLogFileSetT *fileSet, const uint8_t *data, uint32_t size);
BufBucketT *TryGetBucketAndDescFromBufpool(BufpoolMgrT *mgr, PageIdT pageId, bool incRef, BufDescT **outBufDesc);
StatusInter BufAllocHwm(const BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescT **bufDesc);
StatusInter BufpoolRecycleByNormal(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc);
StatusInter CkptTriggerAndCheckTimeout(SeInstanceT *seIns, uint64_t *startTime, uint64_t *triggerWarnTimes);
}
const uint32_t SPACE_INDEX = 0;
const uint32_t CKPT_TRM_ID = 3;
typedef struct TagPageCkptTest {
    uint8_t checkValid8;
    uint16_t checkValid16;
    uint32_t checkValid32;
    uint64_t checkValid64;
} PageCkptTestT;
static void ModifyPage(uint8_t *page)
{
    PageCkptTestT *ckptPage = (PageCkptTestT *)(page + sizeof(PageHeadT));
    ckptPage->checkValid8 = 8;
    ckptPage->checkValid16 = 16;
    ckptPage->checkValid32 = 32;
    ckptPage->checkValid64 = 64;
}
static void ResetPage(uint8_t *page)
{
    PageCkptTestT *ckptPage = (PageCkptTestT *)(page + sizeof(PageHeadT));
    ckptPage->checkValid8 = 0;
    ckptPage->checkValid16 = 0;
    ckptPage->checkValid32 = 0;
    ckptPage->checkValid64 = 0;
}
static void FreeUsedPage(
    SeInstanceT *seInsPtr, const vector<PageIdT> &addrVector, uint32_t pageNum, void (*func)(uint8_t *) = nullptr)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    PageMgrT *pageMgr = (PageMgrT *)(seInsPtr->pageMgr);
    uint8_t *page;
    for (size_t index = 0; index < pageNum; index++) {
        RedoLogBegin(redoCtx);
        SeGetPage(pageMgr, addrVector[index], &page, ENTER_PAGE_NORMAL, true);
        if (func != nullptr) {
            func(page);
        }
        SeLeavePage(pageMgr, addrVector[index], true);
        FreePageParamT freePageParam =
            SeInitFreePageParam(SPACE_INDEX, addrVector[index], NULL, NULL, SE_INVALID_LABEL_ID, false);
        SeFreePage(pageMgr, &freePageParam);
        RedoLogEnd(redoCtx, true);
    }
}
static void AddDirtyPagesToCkpt(
    SeInstanceT *seInsPtr, int pageNum, vector<PageIdT> &addrVector, void (*func)(uint8_t *) = nullptr)
{
    uint8_t *page;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    PageMgrT *pageMgr = (PageMgrT *)(seInsPtr->pageMgr);
    PageIdT pageAddr;
    for (int index = 0; index < pageNum; index++) {
        RedoLogBegin(redoCtx);
        // 持久化不使用rsmUndo
        AllocPageParamT allocPageParam =
            SeInitAllocPageParam(SPACE_INDEX, CKPT_TRM_ID, RSM_INVALID_LABEL_ID, NULL, NULL);
        SeAllocPage(pageMgr, &allocPageParam, &pageAddr);
        addrVector.push_back(pageAddr);
        SeGetPage(pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, true);
        if (func != nullptr) {
            func(page);
        }
        SeLeavePage(pageMgr, pageAddr, true);
        RedoLogEnd(redoCtx, true);
    }
}
class CheckpointTest : public PersistenceUtTest, public testing::Test {
public:
    static uint32_t stubNum;

protected:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}
    void SetUp() override
    {
        system("ipcrm -a; rm -rf /data/gmdb");
        ModifyIniConfigFile("\"ckptPeriod=50000\"");
        ConstructSeInsAndSeRun(g_seConfigIniPathTmp);
        CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
        pageSize = seIns->seConfig.pageSize * DB_KIBI;
    }
    void TearDown() override
    {
        DestroySeIns();
        RecoverIniConfigFile();
    }
    uint32_t pageSize;
};

uint32_t CheckpointTest::stubNum = 0;

TEST_F(CheckpointTest, InitAndDestroy)
{
    // ConstructSeInsAndSeRun已经起了ckpt线程，这里关掉后重新启动
    size_t repeatNum = 5;
    for (size_t i = 0; i < repeatNum; i++) {
        CkptDestroyImpl(seIns);
        ASSERT_EQ(STATUS_OK_INTER, CkptInitImpl(seIns));
    }
}
TEST_F(CheckpointTest, GroupTest)
{
    CkptGroupT *group = &(seIns->ckptCtx->group);
    CkptClearGroup(seIns);
    const int size = 3;
    BufDescT bufDesc[size];
    PageHeadT *pageArray[size];
    uint32_t fileblk[size][2] = {{1, 1}, {1, 0}, {0, 1}};
    for (int index = 0; index < size; index++) {
        pageArray[index] = (PageHeadT *)new char[pageSize];
        pageArray[index]->addr.deviceId = fileblk[index][0];
        pageArray[index]->addr.blockId = fileblk[index][1];
        bufDesc[index].page = pageArray[index];
        bufDesc[index].pageId = pageArray[index]->addr;
        ASSERT_TRUE(CkptPushGroup(group, &bufDesc[index]));
    }

    EXPECT_EQ(group->items[0].itemId, (uint32_t)0);
    EXPECT_EQ(group->items[1].itemId, (uint32_t)1);
    EXPECT_EQ(group->items[2].itemId, (uint32_t)2);

    CkptSortGroup(group);
    EXPECT_EQ(group->items[0].itemId, (uint32_t)2);
    EXPECT_EQ(group->items[1].itemId, (uint32_t)1);
    EXPECT_EQ(group->items[2].itemId, (uint32_t)0);
    CkptClearGroup(seIns);

    for (int i = 0; i < size; i++) {
        delete[] pageArray[i];
    }
}

TEST_F(CheckpointTest, QueueTest)
{
    // 队列reset 确保是空的
    CkptReset(seIns);
    ASSERT_EQ(0u, seIns->ckptCtx->pubCtx->queue.count);
    const int size = 4;
    BufDescT *bufDesc = (BufDescT *)malloc(sizeof(BufDescT) * size);
    ASSERT_NE(bufDesc, nullptr);
    ASSERT_EQ(EOK, memset_s(bufDesc, sizeof(BufDescT) * size, 0, sizeof(BufDescT) * size));
    PageHeadT *pageArray[size];
    PageIdT pageId[size] = {{2, 1}, {3, 1}, {4, 18}, {7, 16}};
    // 入队三个页
    for (int index = 0; index < size; index++) {
        pageArray[index] = (PageHeadT *)new char[pageSize];
        pageArray[index]->addr.deviceId = pageId[index].deviceId;
        pageArray[index]->addr.blockId = pageId[index].blockId;
        bufDesc[index].page = pageArray[index];
        bufDesc[index].pageId = pageId[index];
        bufDesc[index].ckptFlag = NOT_IN_CKPT_QUEUE;
        CkptEnQueue(seIns, &bufDesc[index]);
    }
    // 检验队列个数
    EXPECT_EQ((uint32_t)size, seIns->ckptCtx->pubCtx->queue.count);
    // 出队
    BufDescT *first = CkptDeQueue(seIns);
    EXPECT_TRUE(DbIsPageIdEqual(first->pageId, pageId[0]));
    EXPECT_EQ((uint32_t)size - 1, seIns->ckptCtx->pubCtx->queue.count);
    // Pop并检验还在队列里的页 应该还有3,1和7,16
    CkptPopQueue(seIns, &bufDesc[2]);
    EXPECT_EQ((uint32_t)size - 2, seIns->ckptCtx->pubCtx->queue.count);
    EXPECT_TRUE(DbIsPageIdEqual(seIns->ckptCtx->pubCtx->queue.first->pageId, pageId[1]));
    EXPECT_TRUE(DbIsPageIdEqual(seIns->ckptCtx->pubCtx->queue.last->pageId, pageId[3]));

    CkptReset(seIns);

    for (int i = 0; i < size; i++) {
        delete[] pageArray[i];
    }
}
static void CheckPage(SeInstanceT *seInsPtr, vector<PageIdT> addrVector, uint32_t pageNum)
{
    EXPECT_LE((size_t)pageNum, addrVector.size());
    PageMgrT *pageMgr = (PageMgrT *)(seInsPtr->pageMgr);
    for (size_t index = 0; index < pageNum; index++) {
        uint8_t *page;
        SeGetPage(pageMgr, addrVector[index], &page, ENTER_PAGE_NORMAL, false);
        PageCkptTestT *ckptPage = (PageCkptTestT *)(page + sizeof(PageHeadT));
        EXPECT_EQ(8u, ckptPage->checkValid8);
        EXPECT_EQ(16u, ckptPage->checkValid16);
        EXPECT_EQ(32u, ckptPage->checkValid32);
        EXPECT_EQ(64u, ckptPage->checkValid64);
        SeLeavePage(pageMgr, addrVector[index], false);
    }
}

// 测试增量刷盘
TEST_F(CheckpointTest, TriggerIncTest001)
{
    // 刷所有脏页下去
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 记录一下ckpt视图快照
    uint64_t oldFlushPageCount = ckptCtx->stat.flushPageCount;
    uint64_t oldPerformCount = ckptCtx->stat.performCount;
    uint64_t oldTriggerCount = ckptCtx->stat.triggerCount;
    // 添加150个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 150;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 增量刷盘
    CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    // 一次最多刷128个页下去
    EXPECT_EQ(oldFlushPageCount + (uint64_t)SE_CKPT_GROUP_SIZE, ckptCtx->stat.flushPageCount);
    EXPECT_EQ(oldTriggerCount + 1, ckptCtx->stat.triggerCount);
    EXPECT_EQ(oldPerformCount + 1, ckptCtx->stat.performCount);
    // 重启 检查前128个页
    DestroySeIns();
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
    // 只检查和释放已经落盘的页这里-1是因为有其他非显示申请的页加入了脏页队列，如每个device的首页
    CheckPage(seIns, addrVector, SE_CKPT_GROUP_SIZE - 1);
    FreeUsedPage(seIns, addrVector, SE_CKPT_GROUP_SIZE - 1, ResetPage);
}

Status StartupDatabaseMockForCkpt(SeInstanceT *seIns)
{
    return GMERR_INTERNAL_ERROR;
}

/**
 * 0. 问题描述：Db启动异常，CkptStopProc触发刷盘覆盖正常启动core信息。
 * 1. 第一次正常启动：core信息落盘，记录truncPoint1 (blockId == 29, batchId == 7).
 * 2. 第二次异常启动失败：core信息落盘，记录初始化truncPoint2 (blockId == 1, batchId == 0)覆盖truncPoint1.
 * 3. 第三次启动成功，恢复从truncPoint2开始，读取到了已回收的redo日志。
 */
TEST_F(CheckpointTest, TriggerIncTest002)
{
    // 刷所有脏页下去
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 添加150个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 150;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 增量刷盘
    CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    // 记录正常启动后的core信息
    StatusInter ret = CkptUpdateCoreRedoPoint(seIns);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    uint64_t blockIdBefore = seIns->db->core.truncPoint.blockId;
    uint64_t batchIdBefore = seIns->db->core.truncPoint.batchId;
    DestroySeIns();

    // 异常启动，预期失败
    int index = setStubC((void *)StartupDatabase, (void *)StartupDatabaseMockForCkpt);
    EXPECT_GT(index, 0);
    CreateSeInsAbnormal(g_seConfigIniPathTmp, true, GMERR_INTERNAL_ERROR, false);
    clearStub(index);
    DbCommonFinalize(NULL);

    // 重新正常启动，预期成功
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
    // 校验core信息字段，预期跟正常启动一致
    EXPECT_EQ(blockIdBefore, seIns->db->core.truncPoint.blockId);
    EXPECT_EQ(batchIdBefore, seIns->db->core.truncPoint.batchId);
}

TEST_F(CheckpointTest, TriggerIncTest003)
{
    // 刷所有脏页下去
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 添加10个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 10;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 增量刷盘
    CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    // 记录正常启动后的core信息
    StatusInter ret = CkptUpdateCoreRedoPoint(seIns);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    uint64_t blockIdBefore = seIns->db->core.truncPoint.blockId;
    uint64_t batchIdBefore = seIns->db->core.truncPoint.batchId;
    DestroySeIns();

    // 正常重启不走恢复流程，校验redo curPoint
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
    EXPECT_EQ(blockIdBefore, seIns->redoMgr->pubCtx->curPoint.blockId);
    EXPECT_EQ(batchIdBefore, seIns->redoMgr->pubCtx->curPoint.batchId);
}

TEST_F(CheckpointTest, TriggerIncTest004)
{
    uint32_t fsnBefore = seIns->ckptCtx->truncPoint.fsn;
    uint64_t blockIdBefore = seIns->ckptCtx->truncPoint.blockId;
    uint64_t batchIdBefore = seIns->ckptCtx->truncPoint.batchId;
    StatusInter ret = CkptUpdateCoreRedoPoint(seIns);
    EXPECT_EQ(STATUS_OK_INTER, ret);

    // 模拟重启redo扫描场景，此时enable为false, 此时ckpt刷盘不应更新truncPoint
    int pageNum = 10;
    vector<PageIdT> addrVector;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    RedoSetEnable(seIns->redoMgr, false);
    CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    EXPECT_EQ(fsnBefore, seIns->ckptCtx->truncPoint.fsn);
    EXPECT_EQ(blockIdBefore, seIns->ckptCtx->truncPoint.blockId);
    EXPECT_EQ(batchIdBefore, seIns->ckptCtx->truncPoint.batchId);
    RedoSetEnable(seIns->redoMgr, true);
}

#define TEST_CKPT_FAILED_ITEM_ID 64

static StatusInter CkptFlushItemStub(CkptCtxT *ckptCtx, const CkptSortItemT *item, uint32_t zoneId)
{
    DB_POINTER2(ckptCtx, item);
    if (item->itemId > TEST_CKPT_FAILED_ITEM_ID) {
        return INTERNAL_ERROR_INTER;
    }
    CkptGroupT *group = &ckptCtx->group;
    uint32_t partNum = item->itemId / group->dwrBlockNum;
    uint32_t pageIndex = item->itemId % group->dwrBlockNum;
    uint64_t offset = partNum * SafeFileGetPartFileLen(&ckptCtx->group) + sizeof(SafeFileHeadT) +
                      (uint64_t)pageIndex * ckptCtx->group.pageSize;
    PageHeadT *page = (PageHeadT *)(void *)(ckptCtx->group.buf + offset);

    StatusInter ret = SpaceWriteBlock(ckptCtx->seIns, page, item->buf->pageId, zoneId, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Checkpoint unable to flush page addr:%" PRIu32 ",%" PRIu32 ", pageId:%" PRIu32 ",%" PRIu32,
            page->addr.deviceId, page->addr.blockId, item->buf->pageId.deviceId, item->buf->pageId.blockId);
        return ret;
    }

    return STATUS_OK_INTER;
}

// 测试增量刷盘
TEST_F(CheckpointTest, TriggerIncTest005)
{
    // 刷所有脏页下去
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 添加128个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 128;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 增量刷盘
    int32_t stubIndex = setStubC((void *)CkptFlushItem, (void *)CkptFlushItemStub);
    CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    ASSERT_EQ(pageNum, ckptCtx->group.count);
    for (uint32_t i = TEST_CKPT_FAILED_ITEM_ID + 1; i < ckptCtx->group.count; ++i) {
        ASSERT_EQ(1, ckptCtx->group.items[i].buf->inCkptBuf);
    }
    clearStub(stubIndex);
    CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    for (uint32_t i = 0; i < ckptCtx->group.count; ++i) {
        ASSERT_EQ(0, ckptCtx->group.items[i].buf->inCkptBuf);
    }
}

// 测试全量刷盘
TEST_F(CheckpointTest, TriggerFullTest)
{
    // 刷所有脏页下去
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 记录一下ckpt视图快照
    uint64_t oldFlushPageCount = ckptCtx->stat.flushPageCount;
    uint64_t oldPerformCount = ckptCtx->stat.performCount;
    uint64_t oldTriggerCount = ckptCtx->stat.triggerCount;
    // 添加150个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 150;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 全量刷盘 应该要刷两次
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 注意有非显示申请的页入脏页队列了
    ASSERT_LE(oldFlushPageCount + (uint64_t)pageNum, ckptCtx->stat.flushPageCount);
    ASSERT_EQ(oldTriggerCount + 1, ckptCtx->stat.triggerCount);
    ASSERT_EQ(oldPerformCount + 2, ckptCtx->stat.performCount);
    // 重启 检查所有页
    DestroySeIns();
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
    // 检查和释放所有页
    CheckPage(seIns, addrVector, addrVector.size());
    FreeUsedPage(seIns, addrVector, addrVector.size(), ResetPage);
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
}
// 测试周期刷盘 超过设定时间后 预期应该刷盘
TEST_F(CheckpointTest, PeriodFlushTest)
{
    DestroySeIns();
    ModifyIniConfigFile("\"ckptPeriod=2\"");
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    RedoSetEnable(seIns->redoMgr, true);
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    uint64_t oldFlushPageCount = ckptCtx->stat.flushPageCount;
    uint64_t oldPerformCount = ckptCtx->stat.performCount;
    uint64_t oldTriggerCount = ckptCtx->stat.triggerCount;
    // 添加3个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 3;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector);
    uint32_t repeatCount = 0;
    while (true) {
        if (repeatCount > 10) {
            printf("repeat count is more than 10 times!\n");
            break;
        }
        if (ckptCtx->stat.flushPageCount < (uint64_t)pageNum) {
            DbSleep(USECONDS_IN_MSECOND);
            repeatCount++;
        } else {
            break;
        }
    }
    ASSERT_LE((uint64_t)pageNum + oldFlushPageCount, ckptCtx->stat.flushPageCount);
    ASSERT_EQ(oldTriggerCount, ckptCtx->stat.triggerCount);
    ASSERT_EQ(oldPerformCount + 1, ckptCtx->stat.performCount);
    DestroySeIns();
    RecoverIniConfigFile();
    ModifyIniConfigFile("\"ckptPeriod=50000\"");
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
}

// 测试超过阈值刷盘 超过页数阈值后 预期应该刷盘
TEST_F(CheckpointTest, ckptThresholdTest)
{
    DestroySeIns();
    ModifyIniConfigFile("\"ckptThreshold=1024\"");
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    uint64_t oldFlushPageCount = ckptCtx->stat.flushPageCount;
    uint64_t oldPerformCount = ckptCtx->stat.performCount;
    uint64_t oldTriggerCount = ckptCtx->stat.triggerCount;
    // 添加1000个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 1000;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector);
    ASSERT_EQ(oldFlushPageCount, ckptCtx->stat.flushPageCount);
    ASSERT_EQ(oldTriggerCount, ckptCtx->stat.triggerCount);
    ASSERT_EQ(oldPerformCount, ckptCtx->stat.performCount);
    vector<PageIdT> addrVectorInc;
    int pageNumInc = 25;
    AddDirtyPagesToCkpt(seIns, pageNumInc, addrVectorInc);
    uint32_t repeatCount = 0;
    while (true) {
        if (repeatCount > 10) {
            printf("repeat count is more than 10 times!\n");
            break;
        }
        if (ckptCtx->stat.flushPageCount < SE_CKPT_GROUP_SIZE) {
            DbSleep(USECONDS_IN_MSECOND);
            repeatCount++;
        } else {
            break;
        }
    }
    // 至少应该刷一次盘下去 128个页
    ASSERT_LE(oldFlushPageCount + SE_CKPT_GROUP_SIZE, ckptCtx->stat.flushPageCount);
    ASSERT_EQ(oldTriggerCount, ckptCtx->stat.triggerCount);
    ASSERT_LE(1, ckptCtx->stat.performCount);
    DestroySeIns();
    RecoverIniConfigFile();
    ModifyIniConfigFile("\"ckptPeriod=50000\"");
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
}
StatusInter SpcWriteDatafileMock(SeInstanceT *seIns, DbFileT *df, int64_t offset, int32_t handle, BufT buf)
{
    StatusInter ret;
    DbSpinLock(&df->lock);
    buf.size = sizeof(PageHeadT) + sizeof(uint8_t) + sizeof(uint16_t);
    if (DbFileIsOnline(df->ctrl)) {
        ret = VfdFileWrite(seIns, handle, offset, buf);
    } else {
        ret = INT_ERR_DATAFILE_ALREADY_CLOSE;
    }
    DbSpinUnlock(&df->lock);
    return ret;
}
// 测试部分写发生 重启后能否恢复 目前非终端场景下bufferpool不会针对部分写做相应处理 所以Disable
TEST_F(CheckpointTest, DISABLED_ckptPartWrite)
{
    // 添加5个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 5;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    int index = setStubC((void *)SpcWriteDatafile, (void *)SpcWriteDatafileMock);
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    clearStub(index);
    DestroySeIns();
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp, true);
    RedoSetEnable(seIns->redoMgr, true);
    PageMgrT *pageMgr = (PageMgrT *)(seIns->pageMgr);
    for (size_t index = 0; index < addrVector.size(); index++) {
        uint8_t *page;
        SeGetPage(pageMgr, addrVector[index], &page, ENTER_PAGE_NORMAL, false);
        PageCkptTestT *ckptPage = (PageCkptTestT *)(page + sizeof(PageHeadT));
        uint8_t first = ckptPage->checkValid8;
        EXPECT_EQ((uint16_t)first * 2, ckptPage->checkValid16);
        EXPECT_EQ((uint32_t)first * 4, ckptPage->checkValid32);
        EXPECT_EQ((uint64_t)first * 8, ckptPage->checkValid64);
        SeLeavePage(pageMgr, addrVector[index], false);
    }
}
void CheckCkptWork(SeInstanceT *seInsPtr)
{
    // 刷所有脏页下去
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    CkptTriggerImpl(seInsPtr, CKPT_MODE_FULL, true, 0);
    // 记录一下ckpt视图快照
    uint64_t oldFlushPageCount = ckptCtx->stat.flushPageCount;
    uint64_t oldPerformCount = ckptCtx->stat.performCount;
    uint64_t oldTriggerCount = ckptCtx->stat.triggerCount;
    // 添加20个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 20;
    AddDirtyPagesToCkpt(seInsPtr, pageNum, addrVector, ModifyPage);
    // 增量刷盘
    CkptTriggerImpl(seInsPtr, CKPT_MODE_INC, true, 0);
    EXPECT_LE(oldFlushPageCount + (uint64_t)pageNum, ckptCtx->stat.flushPageCount);
    EXPECT_EQ(oldTriggerCount + 1, ckptCtx->stat.triggerCount);
    EXPECT_EQ(oldPerformCount + 1, ckptCtx->stat.performCount);
    FreeUsedPage(seInsPtr, addrVector, addrVector.size(), ResetPage);
    CkptTriggerImpl(seInsPtr, CKPT_MODE_FULL, true, 0);
}

// 测试入队出队ckptFlag标记位正确性
TEST_F(CheckpointTest, ckptEnQueue)
{
    CkptQueueT *queue = &seIns->ckptCtx->pubCtx->queue;
    uint32_t oldCount = queue->count;
    uint32_t count = 3u;
    BufDescT *bufDescPtr = (BufDescT *)malloc(sizeof(BufDescT) * count);
    (void)memset_s(bufDescPtr, sizeof(BufDescT), 0, sizeof(BufDescT));

    for (uint32_t i = 0; i < count; ++i) {
        bufDescPtr[i].ckptFlag = NOT_IN_CKPT_QUEUE;
        bufDescPtr[i].page = NULL;
    }
    CkptEnQueue(seIns, &bufDescPtr[0]);
    EXPECT_EQ(queue->count, oldCount + 1);
    EXPECT_EQ(bufDescPtr[0].ckptFlag, IN_CKPT_QUEUE);

    CkptEnQueue(seIns, &bufDescPtr[1]);
    EXPECT_EQ(queue->count, oldCount + 2);
    EXPECT_EQ(bufDescPtr[1].ckptFlag, IN_CKPT_QUEUE);

    CkptEnQueue(seIns, &bufDescPtr[2]);
    EXPECT_EQ(queue->count, oldCount + 3);
    EXPECT_EQ(bufDescPtr[2].ckptFlag, IN_CKPT_QUEUE);

    CkptPopQueue(seIns, &bufDescPtr[0]);
    EXPECT_EQ(queue->count, oldCount + 2);
    EXPECT_EQ(bufDescPtr[0].ckptFlag, NOT_IN_CKPT_QUEUE);

    CkptPopQueue(seIns, &bufDescPtr[1]);
    EXPECT_EQ(queue->count, oldCount + 1);
    EXPECT_EQ(bufDescPtr[1].ckptFlag, NOT_IN_CKPT_QUEUE);

    CkptPopQueue(seIns, &bufDescPtr[2]);
    EXPECT_EQ(queue->count, oldCount);
    EXPECT_EQ(bufDescPtr[2].ckptFlag, NOT_IN_CKPT_QUEUE);

    free(bufDescPtr);
}
// 测试CKPT_MODE_NONE模式
TEST_F(CheckpointTest, CkptProcess)
{
    // 刷所有脏页下去
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 添加5个脏页进去
    vector<PageIdT> addrVector;
    int pageNum = 5;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    // 记录一下ckpt视图快照
    uint64_t oldQueuePonit = ckptCtx->pubCtx->queue.count;
    // CKPT_MODE_NONE 理应不会刷盘 队列数不能变
    CkptTriggerImpl(seIns, CKPT_MODE_NONE, true, 0);
    EXPECT_EQ(ckptCtx->pubCtx->queue.count, oldQueuePonit);
}

static StatusInter g_redoLogFlushRet = STATUS_OK_INTER;
static StatusInter g_ckptPageFlushRet = STATUS_OK_INTER;
static StatusInter g_ckptCtrlSaveRet = STATUS_OK_INTER;
static RedoPointT g_lrpPointRet = {.fsn = 1, .padding = 0, .blockId = 2, .batchId = 3};
static uint32_t g_ckptFlushPageCount = 0;
static uint32_t g_processCount = 0;
static bool g_enableSleep = false;

static StatusInter RedoLogFlushMock(RedoMgrT *redoMgr, RedoPointT *point)
{
    if (g_enableSleep) {
        DbSleep((0 + 1) * 1000);
    }
    if (g_redoLogFlushRet == STATUS_OK_INTER) {
        *point = g_lrpPointRet;
        g_processCount++;
    }
    return g_redoLogFlushRet;
}

static StatusInter CkptFlushMock(SeInstanceT *seInsPtr)
{
    if (g_ckptPageFlushRet == STATUS_OK_INTER) {
        if (seInsPtr->ckptCtx->pubCtx->queue.count >= g_ckptFlushPageCount) {
            seInsPtr->ckptCtx->pubCtx->queue.count -= g_ckptFlushPageCount;
        }
        if (seInsPtr->ckptCtx->group.count >= g_ckptFlushPageCount) {
            seInsPtr->ckptCtx->group.count -= g_ckptFlushPageCount;
        }
    }
    return g_ckptPageFlushRet;
}

static StatusInter CkptSaveCtrlMock(SeInstanceT *seInsPtr)
{
    if (g_ckptCtrlSaveRet == STATUS_OK_INTER) {
        seInsPtr->db->core.lrpPoint = g_lrpPointRet;
    }
    return g_ckptCtrlSaveRet;
}

// 测试RedoLogFlush失败后 恢复能否正常工作
TEST_F(CheckpointTest, CkptProces002)
{
    g_redoLogFlushRet = FATAL_ERROR_INTER;
    vector<PageIdT> addrVector;
    int32_t stubIndex = setStubC((void *)RedoLogFlush, (void *)RedoLogFlushMock);
    int pageNum = 2;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    auto triggerRet = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // ckpt 在此期间无法成功刷盘, 即使触发了ckpt刷盘线程，也无法刷盘成功
    EXPECT_NE(triggerRet, STATUS_OK_INTER);

    (void)clearStub(stubIndex);
    CheckCkptWork(seIns);
    FreeUsedPage(seIns, addrVector, addrVector.size(), ResetPage);
    triggerRet = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    EXPECT_EQ(triggerRet, STATUS_OK_INTER);
}
static void CheckLrpPoint(RedoPointT point, RedoPointT expectPoint)
{
    ASSERT_EQ(point.fsn, expectPoint.fsn);
    ASSERT_EQ(point.blockId, expectPoint.blockId);
    ASSERT_EQ(point.batchId, expectPoint.batchId);
}
// 测试CkptFlush失败后 恢复能否正常工作
TEST_F(CheckpointTest, CkptProces003)
{
    g_redoLogFlushRet = STATUS_OK_INTER;
    g_ckptPageFlushRet = FATAL_ERROR_INTER;
    int32_t stubIndex1 = setStubC((void *)RedoLogFlush, (void *)RedoLogFlushMock);
    int32_t stubIndex2 = setStubC((void *)CkptFlush, (void *)CkptFlushMock);
    vector<PageIdT> addrVector;
    int pageNum = 2;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    auto triggerRet = CkptTriggerImpl(seIns, CKPT_MODE_FULL, false, 0);

    (void)clearStub(stubIndex1);
    (void)clearStub(stubIndex2);
    CheckCkptWork(seIns);
    FreeUsedPage(seIns, addrVector, addrVector.size(), ResetPage);
    triggerRet = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    EXPECT_EQ(triggerRet, STATUS_OK_INTER);
}
// 测试CkptSaveCtrl失败后 恢复能否正常工作
TEST_F(CheckpointTest, CkptProces004)
{
    g_redoLogFlushRet = STATUS_OK_INTER;
    g_ckptPageFlushRet = STATUS_OK_INTER;
    seIns->ckptCtx->pubCtx->queue.count = 1;  // Suppose there is a page in queue
    seIns->ckptCtx->group.count = 1;
    g_ckptFlushPageCount = 1;
    g_ckptCtrlSaveRet = FATAL_ERROR_INTER;
    int32_t stubIndex1 = setStubC((void *)RedoLogFlush, (void *)RedoLogFlushMock);
    int32_t stubIndex2 = setStubC((void *)CkptFlush, (void *)CkptFlushMock);
    int32_t stubIndex3 = setStubC((void *)CkptUpdateCoreRedoPoint, (void *)CkptSaveCtrlMock);
    auto triggerRet = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    EXPECT_NE(triggerRet, STATUS_OK_INTER);

    // ckpt 在此期间无法成功刷盘, 即使触发了ckpt刷盘线程，也无法刷盘成功
    EXPECT_EQ(seIns->ckptCtx->pubCtx->queue.count, 0u);  // the only page been flushed
    (void)clearStub(stubIndex1);
    (void)clearStub(stubIndex2);
    (void)clearStub(stubIndex3);
    CheckCkptWork(seIns);
}

static const uint32_t g_testIncrFlushTime = 10;

void *CkptTestIncFlushThread(void *args)
{
    StatusInter ret = STATUS_OK_INTER;
    CkptCtxT *ctx = (CkptCtxT *)args;
    for (uint32_t i = 0; i < g_testIncrFlushTime; ++i) {
        ret = CkptTriggerImpl(ctx->seIns, CKPT_MODE_INC, true, 0);
        DB_ASSERT(ret == STATUS_OK_INTER);
    }
    return NULL;
}

TEST_F(CheckpointTest, BackToBackFlush)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;

    // Push DirtyPage
    RedoLogBegin(redoCtx);
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    // 持久化不使用rsmUndo
    AllocPageParamT allocPageParam = SeInitAllocPageParam(SPACE_INDEX, CKPT_TRM_ID, RSM_INVALID_LABEL_ID, NULL, NULL);
    StatusInter ret = SeAllocPage(pageMgr, &allocPageParam, &pageAddr);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = RedoLogEnd(redoCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // Lock this page then flush
    RedoLogBegin(redoCtx);
    uint8_t *page = NULL;
    ret = SeGetPage(pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = CkptTriggerImpl(seIns, CKPT_MODE_FULL, false, 0);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    pthread_t tid = 0;
    ASSERT_EQ(pthread_create(&tid, NULL, CkptTestIncFlushThread, (void *)seIns->ckptCtx), 0);
    ASSERT_EQ(pthread_join(tid, NULL), 0);
    ASSERT_EQ(CKPT_MODE_FULL, seIns->ckptCtx->trigger);
    SeLeavePage(pageMgr, pageAddr, true);
    ret = RedoLogEnd(redoCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // wait flush finish
    sleep(1);
    ASSERT_EQ(CKPT_MODE_NONE, seIns->ckptCtx->trigger);
}

static bool g_flushTestOver = false;
static const uint32_t g_pageOpPerIter = 32;
static const uint32_t g_testFullFlushTime = 5;
void *CkptTestPageOpThread(void *args)
{
    DbSetServerThreadFlag();
    SeInstanceT *seIns = (SeInstanceT *)args;
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    PageIdT pageAddr[g_pageOpPerIter] = {0};
    RedoRunCtxT *redoRunCtx = NULL;
    StatusInter ret = RedoCtxCreate(seIns->redoMgr, &redoRunCtx);
    DB_ASSERT(ret == STATUS_OK_INTER);
    SeSetCurRedoCtx(redoRunCtx);
    // 持久化不使用rsmUndo
    AllocPageParamT allocPageParam = SeInitAllocPageParam(SPACE_INDEX, CKPT_TRM_ID, RSM_INVALID_LABEL_ID, NULL, NULL);
    while (!g_flushTestOver) {
        RedoLogBegin(redoRunCtx);
        for (uint32_t i = 0; i < g_pageOpPerIter; ++i) {
            ret = SeAllocPage(pageMgr, &allocPageParam, &pageAddr[i]);
            DB_ASSERT(ret == STATUS_OK_INTER);
        }
        ret = RedoLogEnd(redoRunCtx, true);
        RedoLogBegin(redoRunCtx);
        for (uint32_t i = 0; i < g_pageOpPerIter; ++i) {
            FreePageParamT freePageParam =
                SeInitFreePageParam(SPACE_INDEX, pageAddr[i], NULL, NULL, SE_INVALID_LABEL_ID, false);
            ret = SeFreePage(pageMgr, &freePageParam);
            DB_ASSERT(ret == STATUS_OK_INTER);
        }
        ret = RedoLogEnd(redoRunCtx, true);
    }
    SeSetCurRedoCtx(NULL);
    RedoCtxRelease(redoRunCtx);
    return NULL;
}

TEST_F(CheckpointTest, FullFlushWhileBusy)
{
    g_flushTestOver = false;
    pthread_t tid = 0;
    ASSERT_EQ(pthread_create(&tid, NULL, CkptTestPageOpThread, (void *)seIns), 0);
    for (uint32_t i = 0; i < g_testFullFlushTime; ++i) {
        sleep(1);
        StatusInter ret = CkptTriggerImpl(seIns, CKPT_MODE_FULL, false, 0);
        ASSERT_EQ(STATUS_OK_INTER, ret);
    }
    g_flushTestOver = true;
    ASSERT_EQ(pthread_join(tid, NULL), 0);
    StatusInter ret = CkptTriggerImpl(seIns, CKPT_MODE_FULL, false, 0);
    ASSERT_EQ(STATUS_OK_INTER, ret);
}

static StatusInter RedoLogFileFlushStubc(RedoLogFileSetT *fileSet, const uint8_t *data, uint32_t size)
{
    // 设置db 不可用
    return DbGetStatusInterErrno(GMERR_FILE_OPERATE_FAILED);
}

// DTS2025010618464 测试故障时候，trigger会退出循环
TEST_F(CheckpointTest, TryGetPageWhileCkptFull)
{
    // 添加1个脏页进去
    vector<PageIdT> addrVector;
    AddDirtyPagesToCkpt(seIns, 1, addrVector, ModifyPage);
    // 增量刷盘
    int32_t stubIndex = setStubC((void *)RedoLogFileFlush, (void *)RedoLogFileFlushStubc);
    auto ckptTriggerRet = CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    ASSERT_EQ(ckptTriggerRet, DATABASE_NOT_AVAILABLE_INTER);
    clearStub(stubIndex);
    ASSERT_EQ(SeGetStorageStatus(seIns), SE_ON_DISK_EMRGNCY);
    ckptTriggerRet = CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    ASSERT_EQ(ckptTriggerRet, DATABASE_NOT_AVAILABLE_INTER);
    seIns->db->core.coreStatus = SE_ON_DISK_ONLINE;
}

BufBucketT *BufTryGetDescStub(BufpoolMgrT *mgr, PageIdT pageId, BufGetPageStatusE purpose, BufDescT **outBufDesc)
{
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, pageId);
    BucketMgrT *bucketMgr = BufpoolGetBucketMgr(mgr->seIns, bufPool->currBucketMgrShm);
    // pageId to bucket，first hashId, then bucketArr, then bucket
    BufBucketT *bucket = BufpoolGetBucketViaPageAddr(bucketMgr, pageId);
    return bucket;
}

StatusInter BufAllocHwmStub(const BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescT **bufDesc)
{
    return OUT_OF_MEMORY_INTER;
}

StatusInter BufpoolRecycleByNormalStub(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc)
{
    return STATUS_OK_INTER;
}

StatusInter CkptTriggerAndCheckTimeoutStub(SeInstanceT *seIns, uint64_t *startTime, uint64_t *triggerWarnTimes)
{
    return DATABASE_NOT_AVAILABLE_INTER;
}

// DTS2025011018081
TEST_F(CheckpointTest, TryGetPageWhileDbLockDown)
{
    // 添加1个脏页进去
    vector<PageIdT> addrVector;
    AddDirtyPagesToCkpt(seIns, 1, addrVector, ModifyPage);
    // 增量刷盘
    (void)setStubC((void *)TryGetBucketAndDescFromBufpool, (void *)BufTryGetDescStub);
    (void)setStubC((void *)BufAllocHwm, (void *)BufAllocHwmStub);
    (void)setStubC((void *)BufpoolRecycleByNormal, (void *)BufpoolRecycleByNormalStub);
    (void)setStubC((void *)CkptTriggerAndCheckTimeout, (void *)CkptTriggerAndCheckTimeoutStub);
    uint8_t *page = NULL;
    StatusInter ret = SeGetPage((PageMgrT *)seIns->pageMgr, addrVector[0], &page, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(ret, DATABASE_NOT_AVAILABLE_INTER);
    clearAllStub();
    ret = SeGetPage((PageMgrT *)seIns->pageMgr, addrVector[0], &page, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(ret, STATUS_OK_INTER);
}

static uint32_t g_testIncrFlushWriteTime = 2;
StatusInter CkptFlushItemWriteFailMock(CkptCtxT *ckptCtx, const CkptSortItemT *item, uint32_t zoneId)
{
    if (g_testIncrFlushWriteTime > 0) {
        clearStub(CheckpointTest::stubNum);
        StatusInter status = CkptFlushItem(ckptCtx, item, zoneId);
        g_testIncrFlushWriteTime--;
        CheckpointTest::stubNum = setStubC((void *)CkptFlushItem, (void *)CkptFlushItemWriteFailMock);
        return status;
    }
    return DATABASE_NOT_AVAILABLE_INTER;
}

TEST_F(CheckpointTest, ckptFlushByPage001)
{
    int pageNum = 20;
    // set effect works times.
    g_testIncrFlushWriteTime = 2;
    vector<PageIdT> addrVector;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    CheckpointTest::stubNum = setStubC((void *)CkptFlushItem, (void *)CkptFlushItemWriteFailMock);
    auto ckptTriggerRet = CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    ASSERT_EQ(DATABASE_NOT_AVAILABLE_INTER, ckptTriggerRet);
    // expect: the first two pages are not in ckptbuf
    ASSERT_EQ(0, seIns->ckptCtx->group.items[0].buf->inCkptBuf);
    ASSERT_EQ(0, seIns->ckptCtx->group.items[1].buf->inCkptBuf);
    // expect: the reset of the pages are in ckptbuf and waiting to flush.
    for (uint32_t i = seIns->ckptCtx->group.flushStartPoint; i < pageNum; i++) {
        ASSERT_EQ(1, seIns->ckptCtx->group.items[i].buf->inCkptBuf);
    }
    clearStub(CheckpointTest::stubNum);
    CkptClearGroup(seIns);
}

TEST_F(CheckpointTest, ckptFlushByPage002)
{
    int pageNum = 20;
    // set effect works times.
    g_testIncrFlushWriteTime = 2;
    vector<PageIdT> addrVector;
    AddDirtyPagesToCkpt(seIns, pageNum, addrVector, ModifyPage);
    CheckpointTest::stubNum = setStubC((void *)CkptFlushItem, (void *)CkptFlushItemWriteFailMock);
    StatusInter ckptTriggerRet = CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
    ASSERT_EQ(DATABASE_NOT_AVAILABLE_INTER, ckptTriggerRet);
    // expect: the first two pages are not in ckptbuf
    ASSERT_EQ(0, seIns->ckptCtx->group.items[0].buf->inCkptBuf);
    ASSERT_EQ(0, seIns->ckptCtx->group.items[1].buf->inCkptBuf);

    // Mock the desc has been changed to other page by bufferpool recycle.
    seIns->ckptCtx->group.items[0].buf->pageId = {.deviceId = 1, .blockId = 0};
    seIns->ckptCtx->group.items[1].buf->pageId = {.deviceId = 1, .blockId = 0};
    clearStub(CheckpointTest::stubNum);
    int retryTimes = 1;  // 有线程仍使用打桩函数，需要重试
    while (ckptTriggerRet == DATABASE_NOT_AVAILABLE_INTER && retryTimes >= 0) {
        ckptTriggerRet = CkptTriggerImpl(seIns, CKPT_MODE_INC, true, 0);
        retryTimes--;
    }
    ASSERT_EQ(STATUS_OK_INTER, ckptTriggerRet);
}

// 测试动态申请groupBuf
TEST_F(CheckpointTest, dynAllocGroupBuf)
{
    DestroySeIns();
    RecoverIniConfigFile();
    system("ipcrm -a;rm -rf /data/gmdb");

    // 将bufferpoolSize设置为128KB
    ModifyIniConfigFile("\"bufferPoolSize=128\" \"pageSize=4\"");
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp);
    StatusInter ret = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    ASSERT_EQ(DbGetExternalErrno(ret), GMERR_OK);

    // checkpoint刷完盘应该就会释放groupBuf
    ASSERT_EQ(seIns->ckptCtx->group.buf, nullptr);
}

void *DynamicAlgoAllocErr(DbMemCtxT *ctx, size_t size)
{
    clearStub(CheckpointTest::stubNum);
    return NULL;
}

void *DbDynMemCtxAllocErr(void *ctx, size_t size)
{
    clearStub(CheckpointTest::stubNum);
    // 打桩，第二次申请内存，使用普通的内存申请失败，会触发使用逃生通道内存申请
    CheckpointTest::stubNum = setStubC((void *)DynamicAlgoAlloc, (void *)DynamicAlgoAllocErr);
    //  第一次申请内存失败
    return NULL;
}

StatusInter StubBeforeCkptInitGroup(SeInstanceT *seInsPtr, uint32_t groupSize)
{
    clearStub(CheckpointTest::stubNum);
    // 打桩，第一次申请内存失败，会触发使用逃生通道申请
    CheckpointTest::stubNum = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocErr);
    return CkptInitGroup(seInsPtr, groupSize);
}

// 测试使用逃生通道内存申请groupBuf
TEST_F(CheckpointTest, dynAllocGroupBufWithEscapeMem)
{
    DestroySeIns();
    RecoverIniConfigFile();
    system("ipcrm -a;rm -rf /data/gmdb");

    // 将bufferpoolSize设置为128KB
    ModifyIniConfigFile("\"bufferPoolSize=128\" \"pageSize=4\"");
    ConstructSeInsAndSeRun(g_seConfigIniPathTmp);

    // 桩函数内部会清除桩
    CheckpointTest::stubNum = setStubC((void *)CkptInitGroup, (void *)StubBeforeCkptInitGroup);
    EXPECT_GT(CheckpointTest::stubNum, 0);
    StatusInter ret = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    ASSERT_EQ(DbGetExternalErrno(ret), GMERR_OK);

    // checkpoint刷完盘应该就会释放groupBuf
    ASSERT_EQ(seIns->ckptCtx->group.buf, nullptr);
}
