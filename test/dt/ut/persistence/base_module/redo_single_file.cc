/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: ut test for single redo
 * Author: xujing
 * Create: 2025-2-18
 */
#include <sys/types.h>
#include <unistd.h>
#include <thread>
#include "gtest/gtest.h"
#include "stub.h"
#include "securec.h"
#include "adpt_types.h"
#include "db_mem_context.h"
#include "adpt_thread.h"
#include "adpt_atomic.h"
#include "se_trx_inner.h"
#include "persistence_common.h"
#include "se_redo_inner.h"
#include "se_redo_file.h"
#include "se_redo_buf.h"
#include "common_init.h"
#include "se_ckpt.h"
#include "stub.h"
#include "se_database.h"
#include "se_recovery_inner.h"
#include "heapLob_ut_common.h"
#include "btree_dml_reliable_test.h"

static const uint32_t REDO_CHECK_SUM = 123477;

using namespace std;

class RedoSingleFile : public PersistenceUtTest, public testing::Test {
protected:
    virtual void SetUp()
    {
        system("rm -rf /data/gmdb");
        UtModifyConfig("gmserver_incre_persist.ini", "redo_temp_config.ini",
            "\"redoFileSize=1\" \"redoFileCount=1\" \"redoPubBufSize=256\"");
        ConstructSeInsAndSeRun("redo_temp_config.ini");
    }

    virtual void TearDown()
    {
        clearAllStub();
        DestroySeIns();
        CommonRelease();
        system("rm -rf redo_temp_config.ini");
    }
};

typedef struct TestRedoRecord {
    uint32_t checkSum;
} TestRedoRecordT;

typedef struct ThreadParam {
    SeInstanceT *seIns;
    StatusInter ret;
} ThreadParamT;

static StatusInter TestRedoReplay(RedoReplayArgsT *arg)
{
    TestRedoRecordT log = *(TestRedoRecordT *)(void *)arg->data;
    if (log.checkSum != REDO_CHECK_SUM) {
        return INT_ERR_REDO_REPLAY_ERROR;
    }
    return STATUS_OK_INTER;
}

TEST_F(RedoSingleFile, HeapLobOperate)
{
    DestroySeIns();
    HeapLobUtCommon heapLobCommon;
    heapLobCommon.ConstructSeInsAndSeRun("redo_temp_config.ini");
    // 插入1MB大对象
    const uint32_t dataSize = SIZE_M(1);
    HpTupleAddr addr;
    Status ret = heapLobCommon.UtHeapInsertData(dataSize, &addr);
    ASSERT_EQ(ret, GMERR_OK);
    // 更新为32MB大对象
    const uint32_t newDataSize = SIZE_M(32);
    ret = heapLobCommon.UtHeapUpdateData(addr, newDataSize);
    ASSERT_EQ(ret, GMERR_OK);
    // 更新回1MB大对象
    ret = heapLobCommon.UtHeapUpdateData(addr, dataSize);
    ASSERT_EQ(ret, GMERR_OK);
    // 删除
    ret = heapLobCommon.UtHeapDeleteData(addr);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(RedoSingleFile, DISABLED_BtreeCheckInsert01)
{
    CheckInsert01(false);
}

TEST_F(RedoSingleFile, RecycleFile1)
{
    StatusInter ret = RedoLogReplayRegister(PersistenceUtTest::seIns->redoMgr, REDO_LEAVE_PAGE, TestRedoReplay);
    ASSERT_EQ(ret, 0);
    for (uint32_t i = 0; i < 2048; i++) {
        RedoLogBegin(PersistenceUtTest::redoCtx);
        PageIdT pageAddr = {0, 0};
        // 大redo
        uint8_t redoData[1024] = {1};
        TestRedoRecordT *record = (TestRedoRecordT *)(void *)redoData;
        record->checkSum = REDO_CHECK_SUM;
        RedoLogWrite(PersistenceUtTest::redoCtx, REDO_LEAVE_PAGE, &pageAddr, redoData, sizeof(redoData));

        ret = RedoLogEnd(PersistenceUtTest::redoCtx, true);
        ASSERT_EQ(ret, STATUS_OK_INTER);

        ret = RedoFlushToCurrPoint(PersistenceUtTest::seIns->redoMgr);
        ASSERT_EQ(ret, STATUS_OK_INTER);
    }

    RedoPointT startPoint = PersistenceUtTest::seIns->db->core.truncPoint;
    RedoPointT lrpPoint = startPoint;
    RedoSetEnableImpl(PersistenceUtTest::seIns->redoMgr, false);
    ret = RecoveryRedo(PersistenceUtTest::seIns->redoMgr, &startPoint, &lrpPoint);
    ASSERT_EQ(DbGetExternalErrno(ret), GMERR_OK);
    RedoSetEnableImpl(PersistenceUtTest::seIns->redoMgr, true);
}

TEST_F(RedoSingleFile, RecycleFile2)
{
    StatusInter ret = RedoLogReplayRegister(PersistenceUtTest::seIns->redoMgr, REDO_LEAVE_PAGE, TestRedoReplay);
    uint32_t fileSizse = 1024 * 1024;
    uint32_t fileBlockSize = 512;
    uint32_t blockNum = fileSizse / fileBlockSize;
    for (uint32_t i = 0; i < blockNum + 1; i++) {
        RedoLogBegin(PersistenceUtTest::redoCtx);

        PageIdT pageAddr = {0, 0};
        // 小redo，可以正好写到文件尾
        uint8_t redoData[128] = {1};
        TestRedoRecordT *record = (TestRedoRecordT *)(void *)redoData;
        record->checkSum = REDO_CHECK_SUM;
        RedoLogWrite(PersistenceUtTest::redoCtx, REDO_LEAVE_PAGE, &pageAddr, redoData, sizeof(redoData));

        ret = RedoLogEnd(PersistenceUtTest::redoCtx, true);
        ASSERT_EQ(ret, STATUS_OK_INTER);

        ret = RedoFlushToCurrPoint(PersistenceUtTest::seIns->redoMgr);
        ASSERT_EQ(ret, STATUS_OK_INTER);
    }

    RedoPointT startPoint = PersistenceUtTest::seIns->db->core.truncPoint;
    RedoPointT lrpPoint = startPoint;
    RedoSetEnableImpl(PersistenceUtTest::seIns->redoMgr, false);
    ret = RecoveryRedo(PersistenceUtTest::seIns->redoMgr, &startPoint, &lrpPoint);
    ASSERT_EQ(DbGetExternalErrno(ret), GMERR_OK);
    RedoSetEnableImpl(PersistenceUtTest::seIns->redoMgr, true);
}

void *WriteRedoThreadFunc(void *arg)
{
    DbSetServerThreadFlag();
    ThreadParamT *param = (ThreadParamT *)arg;
    SeInstanceT *seIns = param->seIns;
    RedoRunCtxT *redoCtx = NULL;
    param->ret = RedoCtxCreate(seIns->redoMgr, &redoCtx);
    if (param->ret != STATUS_OK_INTER) {
        return NULL;
    }
    SeSetCurRedoCtx(redoCtx);
    for (uint32_t i = 0; i < 1024; i++) {
        RedoLogBegin(redoCtx);

        PageIdT pageAddr = {0, 0};
        uint8_t redoData[128] = {1};
        TestRedoRecordT *record = (TestRedoRecordT *)(void *)redoData;
        record->checkSum = REDO_CHECK_SUM;
        RedoLogWrite(redoCtx, REDO_LEAVE_PAGE, &pageAddr, redoData, sizeof(redoData));

        param->ret = RedoLogEnd(redoCtx, true);
        if (param->ret != STATUS_OK_INTER) {
            return NULL;
        }
    }
    RedoCtxRelease(redoCtx);
    return NULL;
}

TEST_F(RedoSingleFile, concurWriteRedo)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t threadCount = 10;
    ThreadParam param[threadCount] = {0};
    pthread_t threadId[threadCount];
    for (int i = 0; i < threadCount; i++) {
        param[i].seIns = PersistenceUtTest::seIns;
        param[i].ret = STATUS_OK_INTER;
        int err = pthread_create(&threadId[i], NULL, WriteRedoThreadFunc, &param[i]);
        ASSERT_EQ(GMERR_OK, err);
    }

    for (int i = 0; i < threadCount; i++) {
        pthread_join(threadId[i], NULL);
        ASSERT_EQ(param[i].ret, STATUS_OK_INTER);
    }

    ret = RedoFlushToCurrPoint(PersistenceUtTest::seIns->redoMgr);
    ASSERT_EQ(ret, STATUS_OK_INTER);

    RedoPointT startPoint = PersistenceUtTest::seIns->db->core.truncPoint;
    RedoPointT lrpPoint = startPoint;
    RedoSetEnableImpl(PersistenceUtTest::seIns->redoMgr, false);
    ret = RecoveryRedo(PersistenceUtTest::seIns->redoMgr, &startPoint, &lrpPoint);
    ASSERT_EQ(DbGetExternalErrno(ret), GMERR_OK);
    RedoSetEnableImpl(PersistenceUtTest::seIns->redoMgr, true);
}
