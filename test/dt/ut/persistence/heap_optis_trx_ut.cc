/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: ut file for heap reliability
 */

#include <vector>
#include "ut_reliability.h"
#include "se_heap.h"
#include "se_heap_page.h"
#include "se_heap_utils.h"
#include "se_heap_inner.h"
#include "heap_util_ut.h"
#include "db_dynmem_algo.h"
#include "db_table_space.h"

using namespace std;

typedef struct TagInsertedUserData {
    uint32_t index;
    HpItemPointerT addr;
} InsertedUserData;

class HeapOptisTrxUt : public UtReliability, public PersistenceUtTest {
public:
    static void SetUpTestCase()
    {
        system("ipcrm -a");
        system("rm -rf /data/gmdb");
        DbSetServerThreadFlag();
        ConstructSeInsAndSeRun(SE_CONFIG_INI_PATH);
        UtOptiTrxMgrInit();
    }
    static void TearDownTestCase()
    {
        DestroySeIns();
    }

    void DestroyAfterTearDown() override
    {
        UtHeapDrop(heapShm);
        // 有些用例中会提交或回滚事务，并将redoCtx置为NULL，这里重新set一下
        SeSetCurRedoCtx((RedoRunCtxT *)g_seRunCtx->redoCtx);
        UtReplyTearDown(seIns);
        clearAllStub();
    }

    void InitBeforeSetUp() override
    {
        UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ);
        UtReplySetUp(seIns);
    }

protected:
    bool undoByPass;
    ShmemPtrT heapShm;
};

// 用不同seRunCtx里的trx来模拟并发事务插入
TEST_F(HeapOptisTrxUt, insert_01)
{
    // 开启事务1，插入1
    SeRunCtxT *seRunCtx1 = UtTransBeginForOptiRR();
    HpItemPointerT addr1;
    uint64_t itemNum = 0;
    Status ret = UtHeapOpWithKey(heapShm, seRunCtx1, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6);
    ASSERT_EQ(0, ret);

    // 开启事务2，插入2
    HpItemPointerT addr2;
    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_INSERT, 2, &addr2, 0.5);
    ASSERT_EQ(0, ret);

    // 提交事务1
    ret = SeTransCommit(seRunCtx1);
    ASSERT_EQ(0, ret);

    // 回滚事务2
    ret = UtTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    // 事务1，插入成功
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 1, &addr1));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(1u, itemNum);
}

TEST_F(HeapOptisTrxUt, insert_concurrent_with_limited_maxRecCnt_02)
{
    uint64_t itemNum = 0;

    UtHeapDrop(heapShm);
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ, 1);
    SeRunCtxT *seRunCtx1 = UtTransBeginForOptiRR();
    HpItemPointerT addr1;
    Status ret = UtHeapOpWithKey(heapShm, seRunCtx1, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6);
    ASSERT_EQ(0, ret);

    HpItemPointerT addr2;
    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_INSERT, 2, &addr2, 0.5);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx1);
    ASSERT_EQ(0, ret);

    // 新事务能读到提交后的记录数
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(1u, itemNum);

    // 忽略回滚时fetch 的deserialize 逻辑,保证回滚生效
    int stubIdx0 = setStub(DmDeSerialize2ExistsVertex, UtStubDmDeSerialize2ExistsVertex);
    ASSERT_TRUE(stubIdx0 >= 0);
    ret = SeTransCommit(seRunCtx2);
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    // 事务1，插入成功
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 1, &addr1));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr2));
    ASSERT_EQ(GMERR_NO_DATA, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(1u, itemNum);
}

TEST_F(HeapOptisTrxUt, insert_concurrent_with_limited_maxRecCnt_03)
{
    UtHeapDrop(heapShm);
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ, 1);
    SeRunCtxT *seRunCtx1 = UtTransBeginForOptiRR();
    HpItemPointerT addr1;
    Status ret = UtHeapOpWithKey(heapShm, seRunCtx1, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6);
    ASSERT_EQ(0, ret);

    // 在事务2开始之前提交，事务2插入必然超过规格且无冲突
    ret = SeTransCommit(seRunCtx1);
    ASSERT_EQ(0, ret);

    HpItemPointerT addr2 = {0};
    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_INSERT, 2, &addr2, 0.5);
    ASSERT_EQ(0, ret);

    // 回滚只是走个流程，没有具体work，因为前面insert很快报错没有产生新row
    ret = SeTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    // 事务1，插入成功
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 1, &addr1));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr2));
    ASSERT_EQ(0, ret);

    uint64_t itemNum = 0;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(1u, itemNum);
}

TEST_F(HeapOptisTrxUt, insert_concurrent_with_limited_maxRecCnt_04)
{
    UtHeapDrop(heapShm);
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ, 1);
    SeRunCtxT *seRunCtx1 = UtTransBeginForOptiRR();
    HpItemPointerT addr1;
    Status ret = UtHeapOpWithKey(heapShm, seRunCtx1, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6);
    ASSERT_EQ(0, ret);

    HpItemPointerT addr2 = {0};
    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();

    ret = SeTransCommit(seRunCtx1);
    ASSERT_EQ(0, ret);

    // 事务1将共用的g_gmdbCurRedoCtx 复位了，这里手动重新置位（实际多线程不会这样）
    SeSetCurRedoCtx((RedoRunCtxT *)g_seRunCtx->redoCtx);
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_INSERT, 2, &addr2, 0.5);
    ASSERT_EQ(0, ret);

    // 回滚只是走个流程，没有具体work，因为前面insert很快报错没有产生新row
    ret = SeTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    // 事务1，插入成功
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 1, &addr1));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr2));
    ASSERT_EQ(0, ret);

    uint64_t itemNum = 0;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(1u, itemNum);
}

TEST_F(HeapOptisTrxUt, row_cnt_01)
{
    UtHeapDrop(heapShm);
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ, 20);
    const int n = 10;
    HpItemPointerT addr[n];
    Status ret = 0;
    // 一次事务，多次open + insert，提交时会将curRecCnt 刷到HeapT 上
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtInsertMultiple(heapShm, 0.6, n, addr));
    ASSERT_EQ(0, ret);

    uint64_t itemNum = 0;
    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    HpItemPointerT addr2[n];
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);

    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_UPDATE, g_topDynMemCtx);
    ASSERT_EQ(0, ret);
    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_UPDATE, 1, &addr[1], 0.4);
    ASSERT_EQ(0, ret);

    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n, itemNum);

    // 一次reset + 多次insert
    ret = HeapLabelResetOpType(heapHdl, HEAP_OPTYPE_INSERT, false);
    ASSERT_EQ(0, ret);

    ret = UtInsertMultipleWithCtx(heapHdl, 0.2, n, addr2);
    ASSERT_EQ(0, ret);

    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);
    ASSERT_EQ(0, ret);
    // 尽管未提交 ，但理应看到本事务自己操作的条数
    ASSERT_EQ(n, itemNum);

    // 本事务能看到的recCnt 已经达到max，不能再插入
    ret = UtHeapOpWithKey(heapShm, seCtx1, HEAP_OPTYPE_INSERT, 1, &addr[0], 0.3);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seCtx1);
    ASSERT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    HeapLabelResetCtx(heapHdl);
    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);  // 实际上层不会没有开启事务和HeapOpen就调用此函数
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n, itemNum);
    HeapHandleClose(heapHdl);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n, itemNum);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr[0], 0.6));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, row_cnt_02)
{
    UtHeapDrop(heapShm);
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ, 10);
    const int n = 10;
    HpItemPointerT addr[n];
    Status ret = 0;
    // 一次事务，多次open + insert，提交时会将curRecCnt 刷到HeapT 上
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtInsertMultiple(heapShm, 0.6, n, addr));
    ASSERT_EQ(0, ret);

    uint64_t itemNum = 0;
    // 只读事务直接读count
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n, itemNum);

    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);

    ASSERT_EQ(0, HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, g_topDynMemCtx));
    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_DELETE, 0, &addr[0], 0);
    ASSERT_EQ(0, ret);

    ret = HeapLabelResetOpType(heapHdl, HEAP_OPTYPE_INSERT, false);
    ASSERT_EQ(0, ret);
    // 前面删了一条，还可再插一条
    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_INSERT, 1, &addr[0], 0.1);
    ASSERT_EQ(0, ret);

    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);
    ASSERT_TRUE(0 == ret && n == itemNum);

    // 本事务能看到的recCnt 已经达到max，不能再插入
    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_INSERT, 1, &addr[0], 0.3);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seCtx1);
    ASSERT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    seCtx1 = UtBeginTrxRRInternal(seCtx1);
    HeapLabelResetCtx(heapHdl);
    ASSERT_EQ(0, HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, g_topDynMemCtx));
    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);
    ASSERT_TRUE(0 == ret && n == itemNum);
    ret = SeTransCommit(seCtx1);
    ASSERT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);
    HeapHandleClose(heapHdl);
}

// dml 连续回滚
TEST_F(HeapOptisTrxUt, row_cnt_03)
{
    UtHeapDrop(heapShm);
    const int n = 10;
    UtHeapCreate(HEAP_VAR_LEN_ROW_PAGE, &heapShm, OPTIMISTIC_TRX, REPEATABLE_READ, n);
    HpItemPointerT addr[n];
    Status ret = 0;
    // 一次事务，多次open + insert，提交时会将curRecCnt 刷到HeapT 上
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtInsertMultiple(heapShm, 0.6, n - 1, addr));
    ASSERT_EQ(0, ret);

    uint64_t itemNum = 0;
    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);

    ASSERT_EQ(0, HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, g_topDynMemCtx));
    ASSERT_NE(heapHdl->heapTrxCtx, nullptr);

    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_DELETE, 0, &addr[0], 0);
    ASSERT_EQ(0, ret);
    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n - 1, itemNum);

    ret = SeTransRollback(seCtx1, false);
    ASSERT_EQ(0, ret);

    UtBeginTrxRRInternal(seCtx1);  // 复用先前的seRunCtx

    HeapLabelResetCtx(heapHdl);  // HeapOpen不能嵌套调用
    ASSERT_EQ(0, HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, g_topDynMemCtx));

    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_INSERT, 1, &addr[0], 0.1);
    ASSERT_EQ(0, ret);

    // 事务获取item num之前必须调用过HeapOpen以拿到事务开启时的recCnt 快照值
    ret = HeapLabelGetPhyItemNum(heapHdl, &itemNum);
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n - 1, itemNum);

    ret = SeTransRollback(seCtx1, false);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpGetUserDefItemNum(heapShm, &itemNum));
    ASSERT_EQ(0, ret);
    ASSERT_EQ(n - 1, itemNum);

    HeapHandleClose(heapHdl);
}

TEST_F(HeapOptisTrxUt, heapRunCtx_call_HeapOpen_before_HeapResetOpType_21)
{
    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);
    ASSERT_EQ(heapHdl->heapTrxCtx, nullptr);

    ASSERT_EQ(GMERR_INTERNAL_ERROR, HeapLabelResetOpType(heapHdl, HEAP_OPTYPE_INSERT, false));
    HeapHandleClose(heapHdl);
}

TEST_F(HeapOptisTrxUt, heapRunCtx_trx_begin_before_HeapOpen_22)
{
    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);
    ASSERT_EQ(heapHdl->heapTrxCtx, nullptr);
    Status ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, g_topDynMemCtx);
    ASSERT_EQ(0, ret);
    ret = SeTransCommit(seCtx1);
    ASSERT_EQ(0, ret);

    // 创建新的seRunCtx实例
    SeRunCtxT *seCtx2 = UtTransBeginForOptiRR();
    HeapLabelResetCtx(heapHdl);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, g_topDynMemCtx);
    ASSERT_EQ(GMERR_TRANS_MODE_MISMATCH, ret);
    ret = SeTransRollback(seCtx2, false);
    ASSERT_EQ(0, ret);

    HeapHandleClose(heapHdl);
}

TEST_F(HeapOptisTrxUt, heapRunCtx_HeapOpen_cannot_nested_23)
{
    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);
    ASSERT_EQ(heapHdl->heapTrxCtx, nullptr);
    Status ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_DELETE, g_topDynMemCtx);
    ASSERT_EQ(0, ret);

    ASSERT_DEATH(HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, g_topDynMemCtx), "");
    HeapHandleClose(heapHdl);
}

TEST_F(HeapOptisTrxUt, heapRunCtx_trx_active_before_dml_24)
{
    SeRunCtxT *seCtx1 = UtTransBeginForOptiRR();
    HpRunHdlT heapHdl = NULL;
    UtHeapRunCtxCreate(seCtx1, heapShm, &heapHdl);
    Status ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, g_topDynMemCtx);
    ASSERT_EQ(0, ret);
    ret = SeTransCommit(seCtx1);
    ASSERT_EQ(0, ret);

    // 创建新的seRunCtx实例
    SeRunCtxT *seCtx2 = UtTransBeginForOptiRR();
    HpItemPointerT addr;
    ret = UtHeapOpWithCtx(heapHdl, HEAP_OPTYPE_INSERT, 1, &addr, 0.1);
    ASSERT_EQ(GMERR_TRANS_MODE_MISMATCH, ret);
    ret = SeTransRollback(seCtx2, false);
    ASSERT_EQ(0, ret);

    HeapHandleClose(heapHdl);
}

TEST_F(HeapOptisTrxUt, update_normalrow_01)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.2));
    ASSERT_EQ(ret, 0);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR,
        UtHeapFetchWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, (HpItemPointerT *)&addr1));
    ASSERT_EQ(0, ret);
}

// 两个事务更新后，一个回滚，一个提交
TEST_F(HeapOptisTrxUt, update_rollback_and_update_commit_02)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.5);
    ASSERT_EQ(0, ret);

    // 连续本事务更新，会从master version中拿 prev version，之后判定为undo 可合并
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_UPDATE, 4, &addr1, 0.5);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_UPDATE, 3, &addr1, 0.5);
    ASSERT_EQ(0, ret);

    // 回滚事务2 , 回滚时fetch读不到主版本，需要从undo中读
    ret = UtTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx3);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 3, &addr1));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, update_rollback_and_delete_commit_03)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.5);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_DELETE, -1, &addr1);
    ASSERT_EQ(0, ret);

    ret = UtTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx3);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr1, true));
    ASSERT_EQ(GMERR_NO_DATA, ret);
}

TEST_F(HeapOptisTrxUt, delete_insert_rollback_and_update_delete_commit_04)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_DELETE, -1, &addr1);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.2);
    ASSERT_EQ(0, ret);

    HpItemPointerT addr2;
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_INSERT, 3, &addr2);
    ASSERT_EQ(0, ret);
    ASSERT_TRUE(!(addr1.pageId == addr2.pageId && addr1.slotId == addr2.slotId));

    ret = UtHeapOpWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_DELETE, -1, &addr1);
    ASSERT_EQ(0, ret);

    ret = UtTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx3);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr1, true));
    ASSERT_EQ(GMERR_NO_DATA, ret);
}

TEST_F(HeapOptisTrxUt, delete_fetch_rollback_and_update_commit_05)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_DELETE, -1, &addr1);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.2);
    ASSERT_EQ(0, ret);

    // 不能读到主版本的数据，因此读undo版本链, 发现row deleted
    ret = UtHeapFetchWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_NORMALREAD, 1, &addr1);
    ASSERT_EQ(GMERR_NO_DATA, ret);

    ret = SeTransCommit(seRunCtx3);
    ASSERT_EQ(0, ret);

    // 回滚事务2
    ret = UtTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr1));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, delete_fetchSelfDeleted_rollback_and_update_commit_06)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_DELETE, -1, &addr1);
    ASSERT_EQ(0, ret);

    // 能看见主版本，但已经删除了且不能访问mark-del row，但由于fetch设置了可以读本事务删除的数据，因此读undo
    ret = UtHeapFetchSelfDeletedWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_NORMALREAD, 1, &addr1);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.2);
    ASSERT_EQ(0, ret);

    // 不能读到主版本的数据，因此读undo版本链, 会发现row deleted.
    // 但如果是本事务自己删除的，则可以读取标识删除前的可见版本
    ret = UtHeapFetchSelfDeletedWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_NORMALREAD, 1, &addr1);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx3);
    ASSERT_EQ(0, ret);

    // 回滚事务2
    ret = UtTransRollback(seRunCtx2, false);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr1));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, update_commit_and_fetchOldest_07)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.8);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx2);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();

    // 如HeapFetchHpOldestVisibleTupleBuffer注释所说，当master version不是本事务修改时，行为同HeapFetch
    ret = UtHeapFetchOldestWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_NORMALREAD, 2, &addr1);
    ASSERT_EQ(0, ret);

    // 回滚事务2
    ret = UtTransRollback(seRunCtx3, false);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr1));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, delete_commit_and_fetchOldest_08)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_DELETE, -1, &addr1);
    ASSERT_EQ(0, ret);

    ret = UtHeapFetchWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_NORMALREAD, 1, (HpItemPointerT *)&addr1);
    ASSERT_EQ(GMERR_NO_DATA, ret);

    ret = SeTransCommit(seRunCtx2);
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx3 = UtTransBeginForOptiRR();

    // 如HeapFetchHpOldestVisibleTupleBuffer注释所说，当master version不是本事务修改时，行为同HeapFetch
    ret = UtHeapFetchOldestWithKey(heapShm, seRunCtx3, HEAP_OPTYPE_NORMALREAD, 2, &addr1);
    ASSERT_EQ(GMERR_NO_DATA, ret);

    // 回滚事务2
    ret = UtTransRollback(seRunCtx3, false);
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapFetchWithKey(heapShm, seRunCtx, HEAP_OPTYPE_NORMALREAD, 2, &addr1));
    ASSERT_EQ(GMERR_NO_DATA, ret);
}

TEST_F(HeapOptisTrxUt, update_normalrow_rollback_09)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_INSERT, 22, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    UtOptisTrxBegin(g_seRunCtx);
    ret = UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_UPDATE, 33, &addr1, 0.2);
    ASSERT_EQ(ret, 0);

    // 忽略回滚时fetch 的deserialize 逻辑,保证回滚生效
    int stubIdx0 = setStub(DmDeSerialize2ExistsVertex, UtStubDmDeSerialize2ExistsVertex);
    ASSERT_TRUE(stubIdx0 >= 0);

    // 走到 HeapUpdateVarRow的回滚逻辑，恢复rowHead 的 rollbackReserveSize
    ret = SeTransRollback(g_seRunCtx, false);
    ASSERT_EQ(ret, 0);

    WITH_TRX(g_seRunCtx, UtHeapFetchWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_NORMALREAD, 22, (HpItemPointerT *)&addr1));
    ASSERT_EQ(0, ret);
}

// 原来的link dst row会在purger中释放
TEST_F(HeapOptisTrxUt, update_link_to_normal_10)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(seRunCtx, OPTIS_RR, UtHeapOpWithKey(heapShm, seRunCtx, HEAP_OPTYPE_INSERT, 1, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    SeRunCtxT *seRunCtx2 = UtTransBeginForOptiRR();
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_UPDATE, 2, &addr1, 0.8);
    ASSERT_EQ(0, ret);

    // 更新成link row后，此次更新拿prev version,然后决定undo 合并
    ret = UtHeapOpWithKey(heapShm, seRunCtx2, HEAP_OPTYPE_UPDATE, 3, &addr1, 0.5);
    ASSERT_EQ(0, ret);

    ret = SeTransCommit(seRunCtx2);
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, update_link_to_normal_rollback)
{
    const int n = 10;
    HpItemPointerT addr[n];
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtInsertMultiple(heapShm, 0.6, n, addr));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtUpdateMultiple(heapShm, 0.8, n, addr));
    ASSERT_EQ(0, ret);

    UtOptisTrxBegin(g_seRunCtx);
    // 更新部分
    for (int i = 0; i < n; i += 2) {
        ret = UtHeapOpWithKey(heapShm, g_seRunCtx, HEAP_OPTYPE_UPDATE, 99, &addr[i], 0.5);
        ASSERT_EQ(ret, 0);
    }

    // 忽略回滚时fetch 的deserialize 逻辑
    int stubIdx0 = setStub(DmDeSerialize2ExistsVertex, UtStubDmDeSerialize2ExistsVertex);
    ASSERT_TRUE(stubIdx0 >= 0);

    ret = SeTransRollback(g_seRunCtx, false);
    ASSERT_EQ(ret, 0);

    // 查到的是第一次update后的数据
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtFetchMultiple(heapShm, n, addr, true));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, update_pre_optimistic_alloc_failed)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr1));
    ASSERT_EQ(0, ret);

    StubController::stubCallCnt = 0;
    StubController::stubEffectTimes = 1;
    StubController::stubNo = setStubC((void *)LfsGetAvailBlock, (void *)LfsGetAvailBlockMock);

    // 在HeapUpdatePreWithOptimisticTrx 分配页失败
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.8));
    ASSERT_EQ(ret, GMERR_UNEXPECTED_NULL_VALUE);

    // normal -> normal
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.5));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, update_pre_optimistic_release_page_failed)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr1));
    ASSERT_EQ(0, ret);

    StubController::stubCallCnt = 0;
    StubController::stubEffectTimes = 1;
    StubController::stubNo = setStubC((void *)LfsSetBlockFreeSpace, (void *)LfsSetBlockFreeSpaceMock);

    // 在HeapUpdatePreWithOptimisticTrx 分配页成功，但归还fsm时失败。手动回滚时要删dst row
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.8));
    ASSERT_EQ(ret, GMERR_UNEXPECTED_NULL_VALUE);

    // normal -> normal
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.5));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, update_link_to_link)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOpFetch(heapShm, HEAP_OPTYPE_NORMALREAD, &addr1));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.8));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.5));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, delete_01)
{
    const int n = 100;
    HpItemPointerT addr[n];
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtInsertMultiple(heapShm, 0.6, n, addr));
    ASSERT_EQ(0, ret);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UtDeleteMultiple(heapShm, n / 2, addr));
    ASSERT_EQ(0, ret);
}

TEST_F(HeapOptisTrxUt, DISABLED_alloc_page_failed01)
{
    int stub1 = setStubC((void *)LfsGetAvailBlock, (void *)LfsGetAvailBlockMock);
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6));
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    clearStub(stub1);
}

TEST_F(HeapOptisTrxUt, DISABLED_insert_malloc_failed01)
{
    StubController::stubCallCnt = 0;
    // 可能需要gdb 调试方可试出此值
    StubController::stubEffectTimes = 5;  // 刚好控制 TrxUndoCreate 分配失败
    StubController::stubNo = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocMock2);
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6));
    ASSERT_EQ(ret, GMERR_OUT_OF_MEMORY);
    clearStub(StubController::stubNo);
}

TEST_F(HeapOptisTrxUt, DISABLED_insert_redoLogEnd_failed01)
{
    StubController::stubCallCnt = 0;
    StubController::stubEffectTimes = 2;
    StubController::stubNo = setStubC((void *)RedoLogEndImpl, (void *)RedoLogEndImplMock2);
    HpItemPointerT addr1;
    Status ret;

    Status trxRet = UtTransBegin(g_seRunCtx, OPTIMISTIC_TRX, REPEATABLE_READ);
    DB_ASSERT(trxRet == 0);
    ret = UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6);
    clearStub(StubController::stubNo);

    StubController::stubCallCnt = 0;
    StubController::stubEffectTimes = 1;
    StubController::stubNo =
        setStubC((void *)HeapSetblockFreeSizeAfterDelete, (void *)HeapSetblockFreeSizeAfterDeleteMock);
    if (ret == STATUS_OK_INTER) {
        trxRet = SeTransCommit(g_seRunCtx);
        DB_ASSERT(trxRet == 0);
    } else {
        trxRet = SeTransRollback(g_seRunCtx, false);
        DB_ASSERT(trxRet == 0);
    }

    ASSERT_EQ(ret, GMERR_INTERNAL_ERROR);
    clearStub(StubController::stubNo);
}

TEST_F(HeapOptisTrxUt, update_malloc_failed01)
{
    HpItemPointerT addr1;
    Status ret;
    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_INSERT, &addr1, 0.6));

    StubController::stubCallCnt = 0;
    StubController::stubEffectTimes = 3;  // TrxUndoCreate malloc failed
    StubController::stubNo = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocMock2);

    WITH_TRX_MODE(g_seRunCtx, OPTIS_RR, UTHeapOp(heapShm, HEAP_OPTYPE_UPDATE, &addr1, 0.8));
    // stubEffectTimes 设置为3时，只是undo缓存页alloc失败，本身DML操作依然可以执行成功
    ASSERT_EQ(ret, GMERR_OK);
    clearStub(StubController::stubNo);
}
