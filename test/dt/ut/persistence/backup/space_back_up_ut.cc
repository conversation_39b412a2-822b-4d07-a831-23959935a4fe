#include <vector>
#include "gtest/gtest.h"
#include "stub.h"
#include "db_common_init.h"
#include "se_define.h"
#include "se_database.h"
#include "persistence_common.h"
#include "se_space_backup_base.h"
#include "se_ckpt.h"
#include "se_ctrl_page_pool.h"
#include "se_db_file.h"
#include "se_redo.h"
#include "se_recovery_inner.h"
#include "adpt_init.h"
#include "se_redo_inner.h"

typedef struct TagTestBackupCtx {
    SeRunCtxT *seRunCtx;       // seRunCtx;
    SeBackupCtxT *backupCtx;   // backupCtx
    uint64_t memUsageAtAlloc;  // memusage when create, for mem-leak check
} TestBackupCtxT;

static SeInstanceT *seIns = NULL;

class SpaceBackupUt : public testing::Test {
public:
    static void SetUpTestCase()
    {
        init();
        UtModifyConfig("gmserver_incre_persist.ini", "space_backup.ini", "\"dataFileDirPath=./backup/active/gmdb\"");
    }
    static void TearDownTestCase()
    {
        system("rm -f space_backup.ini");
        clearAllStub();
    }
    virtual void SetUp()
    {
        system("rm -rf ./backup/active");
        system("ipcrm -a");
        Status ret = DbAdapterInit();
        ASSERT_EQ(GMERR_OK, ret);
        DbCommonInitCfgT cfg = {
            .env = ADPT_RTOS_SERVER,
            .configFileName = "space_backup.ini",
            .isBackGround = false,
        };
        DbSetServerThreadFlag();
        ret = DbCommonInit(&cfg, NULL, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        SeConfigT seConfig = {0};
        ret = StorageConfigGet(NULL, &seConfig, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        DbMemCtxT *topShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
        ASSERT_NE(nullptr, topShmCtx);
        ret = SeCreateInstance(NULL, topShmCtx, &seConfig, &seIns);
        ASSERT_EQ(GMERR_OK, ret);
        ret = CkptTrigger(seIns, CKPT_MODE_BOOT_FULL, true, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE);
        ASSERT_EQ(GMERR_OK, ret);
        printf("Current Active Path (%s)\n", seIns->seConfig.dataFileDirPath[SeGetMainZoneId(seIns)]);
    };
    virtual void TearDown()
    {
        SeDestroyInstance(seIns->instanceId);
        DbCommonFinalize(NULL);
        DbAdapterClose();
        system("rm -rf ./backup/active");
    };
    void BackupTestCreateBackupCtx(SeInstanceT *seIns, TestBackupCtxT *backupCtx);
    void BackupTestRemoveBackupCtx(TestBackupCtxT *backupCtx);
    void BackupTestCheckBackupFile(SeInstanceT *seIns, const SeBackupArgsT *args, bool complete);
    void BackupTestRebootFromBackup(const char *path, Status expectRet);
};

void SpaceBackupUt::BackupTestRebootFromBackup(const char *path, Status expectRet)
{
    // stop active storage
    SeDestroyInstance(seIns->instanceId);
    DbCommonFinalize(NULL);
    DbAdapterClose();

    char configPath[DB_MAX_WHOLE_PATH];
    uint32_t len = sprintf_s(configPath, DB_MAX_WHOLE_PATH, "\"dataFileDirPath=%s\"", path);
    ASSERT_GT(len, 0);
    UtModifyConfig("space_backup.ini", "space_backup_reboot_test.ini", configPath);
    system("ipcrm -a");

    Status ret = DbAdapterInit();
    ASSERT_EQ(GMERR_OK, ret);
    DbCommonInitCfgT cfg = {
        .env = ADPT_RTOS_SERVER,
        .configFileName = "space_backup_reboot_test.ini",
        .isBackGround = false,
    };
    DbSetServerThreadFlag();
    ret = DbCommonInit(&cfg, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    SeConfigT seConfig = {0};
    ret = StorageConfigGet(NULL, &seConfig, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    DbMemCtxT *topShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    ASSERT_NE(nullptr, topShmCtx);
    ret = SeCreateInstance(NULL, topShmCtx, &seConfig, &seIns);
    ASSERT_EQ(expectRet, ret);
    if (expectRet == GMERR_OK) {
        RedoRunCtxT *redoCtx = NULL;
        ret = DbGetExternalErrno(RedoCtxCreate(seIns->redoMgr, &redoCtx));
        ASSERT_EQ(GMERR_OK, ret);
        SeSetCurRedoCtx(redoCtx);
        ret = RecoveryRedo(seIns->redoMgr, &(seIns->db->core.truncPoint), &(seIns->db->core.lrpPoint));
        ASSERT_EQ(GMERR_OK, ret);
        RedoCtxRelease(redoCtx);
        ret = CkptTrigger(seIns, CKPT_MODE_BOOT_FULL, true, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE);
        ASSERT_EQ(GMERR_OK, ret);
        printf("Current Active Path (%s)\n", seIns->seConfig.dataFileDirPath[SeGetMainZoneId(seIns)]);
        SeDestroyInstance(seIns->instanceId);
    }
    DbCommonFinalize(NULL);
    DbAdapterClose();
    system("rm -f space_backup_reboot_test.ini");
    system("ipcrm -a");

    ret = DbAdapterInit();
    ASSERT_EQ(GMERR_OK, ret);
    cfg = {
        .env = ADPT_RTOS_SERVER,
        .configFileName = "space_backup.ini",
        .isBackGround = false,
    };
    DbSetServerThreadFlag();
    ret = DbCommonInit(&cfg, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = StorageConfigGet(NULL, &seConfig, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    topShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    ASSERT_NE(nullptr, topShmCtx);
    ret = SeCreateInstance(NULL, topShmCtx, &seConfig, &seIns);
    ASSERT_EQ(GMERR_OK, ret);
    ret = CkptTrigger(seIns, CKPT_MODE_BOOT_FULL, true, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE);
    ASSERT_EQ(GMERR_OK, ret);
    printf("Current Active Path (%s)\n", seIns->seConfig.dataFileDirPath[SeGetMainZoneId(seIns)]);
}

void SpaceBackupUt::BackupTestCreateBackupCtx(SeInstanceT *seIns, TestBackupCtxT *backupCtx)
{
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &backupCtx->seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);

    backupCtx->memUsageAtAlloc = ((DbMemCtxT *)backupCtx->seRunCtx->sessionMemCtx)->maxTotalPhySize;
    ret = DbGetExternalErrno(SeAllocBackupCtx(backupCtx->seRunCtx, &backupCtx->backupCtx));
    ASSERT_EQ(GMERR_OK, ret);
}

void SpaceBackupUt::BackupTestRemoveBackupCtx(TestBackupCtxT *backupCtx)
{
    SeFreeBackupCtx(backupCtx->seRunCtx, backupCtx->backupCtx);
    ASSERT_EQ(backupCtx->memUsageAtAlloc, ((DbMemCtxT *)backupCtx->seRunCtx->sessionMemCtx)->maxTotalPhySize);
    Status ret = SeClose(backupCtx->seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
}

typedef struct TagTestCheckCtrlCtx {
    CtrlPagePoolT pool;
    CtrlGroupT spaceGroup;
    CtrlGroupT fileGroup;
    CtrlGroupT deviceGroup;
    StCoreCtrlT *core;
} TestCheckCtrlCtxT;

static StatusInter BackupTestInitCheckCtrlCtx(SeInstanceT *seIns, TestCheckCtrlCtxT *ctx)
{
    StatusInter ret =
        SeCtrlPagePoolInit(seIns, (DbMemCtxT *)seIns->seServerMemCtx, &ctx->pool, 0, seIns->db->ctrlFile.blockSize);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    CtrlGroupInit(&ctx->spaceGroup, (DbMemCtxT *)seIns->seServerMemCtx, sizeof(SpaceCtrlT));
    CtrlGroupInit(&ctx->fileGroup, (DbMemCtxT *)seIns->seServerMemCtx, sizeof(DbFileCtrlT));
    CtrlGroupInit(&ctx->deviceGroup, (DbMemCtxT *)seIns->seServerMemCtx, sizeof(DevCtrlT));
    return STATUS_OK_INTER;
}

static void BackupTestDestroyCheckCtrlCtx(TestCheckCtrlCtxT *ctx)
{
    CtrlGroupClear(&ctx->spaceGroup, NULL, NULL);
    CtrlGroupClear(&ctx->fileGroup, NULL, NULL);
    CtrlGroupClear(&ctx->deviceGroup, NULL, NULL);
    SeCtrlPagePoolClear(&ctx->pool);
}

static StatusInter BackupTestLoadBackupSpaceCtrl(TestCheckCtrlCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t cursorAddr = ctx->core->spaceCtrlEntry;
    PageHeadT *page = NULL;
    do {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, cursorAddr, false, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        uint32_t spaceCtrlCnt =
            (seIns->db->ctrlFile.blockSize - sizeof(PageHeadT) - page->freeSize) / sizeof(SpaceCtrlT);
        for (uint32_t i = 0; i < spaceCtrlCnt; i++) {
            SpaceCtrlT *ctrl = (SpaceCtrlT *)((uintptr_t)page + sizeof(PageHeadT) + i * sizeof(SpaceCtrlT));
            ret = CtrlGroupAddItem(&ctx->spaceGroup, ctrl->id, (uint8_t *)ctrl);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
        cursorAddr = page->nextPageId.blockId;
    } while (cursorAddr != SE_INVALID_BLOCK_ID);
    return ret;
}

static StatusInter BackupTestLoadBackupFileCtrl(TestCheckCtrlCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t cursorAddr = ctx->core->fileCtrlEntry;
    PageHeadT *page = NULL;
    do {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, cursorAddr, false, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        uint32_t fileCtrlCnt =
            (seIns->db->ctrlFile.blockSize - sizeof(PageHeadT) - page->freeSize) / sizeof(DbFileCtrlT);
        for (uint32_t i = 0; i < fileCtrlCnt; i++) {
            DbFileCtrlT *ctrl = (DbFileCtrlT *)((uintptr_t)page + sizeof(PageHeadT) + i * sizeof(DbFileCtrlT));
            ret = CtrlGroupAddItem(&ctx->fileGroup, ctrl->id, (uint8_t *)ctrl);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
        cursorAddr = page->nextPageId.blockId;
    } while (cursorAddr != SE_INVALID_BLOCK_ID);
    return ret;
}

static StatusInter BackupTestLoadBackupDevCtrl(TestCheckCtrlCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t cursorAddr = ctx->core->devCtrlEntry;
    PageHeadT *page = NULL;
    do {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, cursorAddr, false, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        uint32_t devCnt = (seIns->db->ctrlFile.blockSize - sizeof(PageHeadT) - page->freeSize) / sizeof(DevCtrlT);
        for (uint32_t i = 0; i < devCnt; ++i) {
            DevCtrlT *ctrl = (DevCtrlT *)((uintptr_t)page + sizeof(PageHeadT) + i * sizeof(DevCtrlT));
            ret = CtrlGroupAddItem(&ctx->deviceGroup, ctrl->id, (uint8_t *)ctrl);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
        cursorAddr = page->nextPageId.blockId;
    } while (cursorAddr != SE_INVALID_BLOCK_ID);
    return ret;
}

static StatusInter BackupTestLoadBackupCtrlFile(SeInstanceT *seIns, int32_t fd, TestCheckCtrlCtxT *ctx)
{
    uint8_t *page = NULL;
    StDatabaseT *db = seIns->db;
    StatusInter ret = SeCtrlPagePoolLoadPage(&ctx->pool, SeGetCoreCtrlPageId(seIns), true, &page);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    BufT bufData = {.buf = page, .size = db->ctrlFile.blockSize};
    ret = SeFileRead(fd, SeGetCtrlFilePageOffset(&db->ctrlFile, SeGetCoreCtrlPageId(seIns)), bufData);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ctx->core = (StCoreCtrlT *)(page + sizeof(PageHeadT));
    for (uint32_t i = SeGetCoreCtrlPageId(seIns) + 1; i < ctx->core->ctrlPageMgr.blockHwm; ++i) {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, i, true, &page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        bufData = {.buf = page, .size = db->ctrlFile.blockSize};
        ret = SeFileRead(fd, SeGetCtrlFilePageOffset(&db->ctrlFile, i), bufData);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    ret = BackupTestLoadBackupSpaceCtrl(ctx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = BackupTestLoadBackupFileCtrl(ctx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    return BackupTestLoadBackupDevCtrl(ctx);
}

static StatusInter BackupTestExecCheckBackupCtrlFile(
    SeInstanceT *seIns, const SeBackupArgsT *args, bool complete, TestCheckCtrlCtxT *ctx)
{
    if (complete && ctx->core->coreStatus == SE_ON_DISK_BACKING_UP) {
        return DATA_EXCEPTION_INTER;
    }

    if (!complete && ctx->core->coreStatus != SE_ON_DISK_BACKING_UP) {
        return DATA_EXCEPTION_INTER;
    }

    for (uint32_t i = 0; i < CtrlGroupGetSize(&ctx->spaceGroup); ++i) {
        SpaceCtrlT *ctrl = (SpaceCtrlT *)CtrlGroupGetItem(&ctx->spaceGroup, i);
        if (SeBackupShouldKeepSpace(args, ctrl->id)) {
            SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, i);
            errno_t err = memcmp(space->ctrl, ctrl, sizeof(SpaceCtrlT));
            if (err != EOK) {
                return DATA_EXCEPTION_INTER;
            }
        } else {
            if (ctrl->used) {
                return DATA_EXCEPTION_INTER;
            }
        }
    }

    for (uint32_t i = 0; i < CtrlGroupGetSize(&ctx->fileGroup); ++i) {
        DbFileCtrlT *ctrl = (DbFileCtrlT *)CtrlGroupGetItem(&ctx->fileGroup, i);
        if (SeBackupShouldKeepSpace(args, ctrl->spaceId)) {
            DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
            errno_t err = memcmp(file->ctrl, ctrl, offsetof(DbFileCtrlT, pageNum));
            if (err != EOK) {
                return DATA_EXCEPTION_INTER;
            }
        } else {
            if (DbFileIsOnline(ctrl)) {
                return DATA_EXCEPTION_INTER;
            }
        }
    }

    for (uint32_t i = 0; i < CtrlGroupGetSize(&ctx->deviceGroup); ++i) {
        DevCtrlT *ctrl = (DevCtrlT *)CtrlGroupGetItem(&ctx->deviceGroup, i);
        if (SeBackupShouldKeepSpace(args, ctrl->spcId)) {
            DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, i);
            errno_t err = memcmp(device->ctrl, ctrl, sizeof(DevCtrlT));
            if (err != EOK) {
                return DATA_EXCEPTION_INTER;
            }
        } else {
            if (ctrl->online) {
                return DATA_EXCEPTION_INTER;
            }
        }
    }

    return STATUS_OK_INTER;
}

static StatusInter BackupTestCheckBackupCtrlFile(
    SeInstanceT *seIns, const SeBackupArgsT *args, bool complete, const char *ctrlPath, TestCheckCtrlCtxT *ctrlCtx)
{
    int32_t fd = DB_INVALID_FD;
    StatusInter ret = SeFileOpen(ctrlPath, SeGetFileOpExtraFlag(seIns), &fd);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    PersCtrlFileHeadT head = {0};
    BufT buf = {.buf = &head, .size = sizeof(PersCtrlFileHeadT)};
    ret = SeFileRead(fd, 0, buf);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

    if (head.checkMagic != seIns->db->checkMagic) {
        ret = DATA_EXCEPTION_INTER;
        goto EXIT;
    }

    ret = BackupTestInitCheckCtrlCtx(seIns, ctrlCtx);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

    ret = BackupTestLoadBackupCtrlFile(seIns, fd, ctrlCtx);
    if (ret != STATUS_OK_INTER) {
        BackupTestDestroyCheckCtrlCtx(ctrlCtx);
        goto EXIT;
    }

    ret = BackupTestExecCheckBackupCtrlFile(seIns, args, complete, ctrlCtx);
EXIT:
    SeFileClose(&fd);
    return ret;
}

void SpaceBackupUt::BackupTestCheckBackupFile(SeInstanceT *seIns, const SeBackupArgsT *args, bool complete)
{
    TestCheckCtrlCtxT ctrlCtx = {0};
    char filePath[DB_MAX_WHOLE_PATH];
    StatusInter ret = SeFileConstructPath(args->path, seIns->seConfig.ctrlFileName, filePath, DB_MAX_WHOLE_PATH);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    if (!complete) {
        if (DbFileExist(filePath)) {
            ret = BackupTestCheckBackupCtrlFile(seIns, args, complete, filePath, &ctrlCtx);
        } else {
            return;
        }
    } else {
        ASSERT_EQ(true, DbFileExist(filePath));
        ret = BackupTestCheckBackupCtrlFile(seIns, args, complete, filePath, &ctrlCtx);
    }
    ASSERT_EQ(STATUS_OK_INTER, ret);
}

TEST_F(SpaceBackupUt, basic)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    BackupTestRemoveBackupCtx(&backupCtx);
}

TEST_F(SpaceBackupUt, backup_empty_path)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    SeBackupArgsT args = {
        .path = "",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(INVALID_PARAMETER_VALUE_INTER, ret);
    BackupTestRemoveBackupCtx(&backupCtx);
}

TEST_F(SpaceBackupUt, backup_null_path)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    SeBackupArgsT args = {
        .path = NULL,
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(INVALID_PARAMETER_VALUE_INTER, ret);
    BackupTestRemoveBackupCtx(&backupCtx);
}

TEST_F(SpaceBackupUt, backup_concanical_path)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    SeBackupArgsT args = {
        .path = "../persistence/backup/active/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, ret);
    BackupTestRemoveBackupCtx(&backupCtx);
}

TEST_F(SpaceBackupUt, backup_active_relative_path)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    SeBackupArgsT args = {
        .path = seIns->seConfig.dataFileDirPath[SeGetMainZoneId(seIns)],
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, ret);
    BackupTestRemoveBackupCtx(&backupCtx);
}

TEST_F(SpaceBackupUt, backup_active_full_path)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    char fullPath[DB_MAX_PATH];
    Status ret = DbAdptRealPath(seIns->seConfig.dataFileDirPath[SeGetMainZoneId(seIns)], fullPath);
    ASSERT_EQ(GMERR_OK, ret);
    SeBackupArgsT args = {
        .path = fullPath,
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    ret = DbGetExternalErrno(BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx));
    ASSERT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    BackupTestRemoveBackupCtx(&backupCtx);
}

TEST_F(SpaceBackupUt, backup_create_dir)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    SeBackupArgsT args = {
        .path = "backup/backup/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(true, DbDirExist("./backup/backup/gmdb"));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf ./backup/backup");
}

TEST_F(SpaceBackupUt, backup_replace)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_non_replace)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = false,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, ret);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));

    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_full_ctrl)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = false,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = SeBackupCtrlFile(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(STATUS_OK_INTER, ret);

    BackupTestCheckBackupFile(seIns, &args, false);

    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_part_ctrl)
{
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    uint32_t spaceId = 0;
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = false,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = 1,
        .spaceIds = &spaceId,
    };
    StatusInter ret = BackupPrepareDir(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = SeBackupCtrlFile(backupCtx.seRunCtx, &args, backupCtx.backupCtx);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(STATUS_OK_INTER, ret);

    BackupTestCheckBackupFile(seIns, &args, false);

    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_full)
{
    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = false,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    ret = SeBackupDatabase(seRunCtx, &args);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbSystemSpace"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbUndoSpace"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbUserSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbSafeFile"));
    ret = SeClose(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_Redo)
{
    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    // 写redo日志
    RedoLogBegin((RedoRunCtxT *)seRunCtx->redoCtx);
    PageIdT pageId = {0, 0};
    uint32_t dataSize = 1024;
    uint8_t data[dataSize] = {0};
    RedoLogWrite((RedoRunCtxT *)seRunCtx->redoCtx, REDO_OP_INSERT, &pageId, data, dataSize);
    StatusInter retInt = RedoLogEnd((RedoRunCtxT *)seRunCtx->redoCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, retInt);
    retInt = RedoLogFlush(seIns->redoMgr, &seIns->db->core.lrpPoint);
    ASSERT_EQ(STATUS_OK_INTER, retInt);
    // 备份redo文件
    SeBackupCtxT *ctx = NULL;
    retInt = SeAllocBackupCtx(seRunCtx, &ctx);
    ASSERT_EQ(STATUS_OK_INTER, retInt);
    retInt = BackupPrepareDir(seRunCtx, &args, ctx);
    ASSERT_EQ(STATUS_OK_INTER, retInt);
    retInt = SeBackupCtrlFile(seRunCtx, &args, ctx);
    ASSERT_EQ(STATUS_OK_INTER, retInt);
    retInt = SeBackupRedoFile(seRunCtx, &args, ctx);
    ASSERT_EQ(STATUS_OK_INTER, retInt);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/gmdb_redo_0"));
    SeFreeBackupCtx(seRunCtx, ctx);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_part)
{
    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t spaceId = 0;
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = 1,
        .spaceIds = &spaceId,
    };
    ret = SeBackupDatabase(seRunCtx, &args);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbSystemSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbUndoSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbUserSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbSafeFile"));
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);

    BackupTestRebootFromBackup("backup/gmdb", GMERR_OK);

    system("rm -rf backup/gmdb");
}

typedef struct TagBackupTestThreadCtx {
    uint32_t spaceId;
    std::vector<PageIdT> pageList;
} BackupTestThreadCtxT;

void *BackupTestContainerRandomOp(void *args)
{
    BackupTestThreadCtxT *ctx = (BackupTestThreadCtxT *)args;

    DbSetServerThreadFlag();
    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &seRunCtx);
    DB_ASSERT(ret == GMERR_OK);

    ret = SeOpenResSession(seRunCtx);
    DB_ASSERT(ret == GMERR_OK);

    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);

    StatusInter interRet = STATUS_OK_INTER;
    AllocPageParamT pageArg = {
        .spaceId = ctx->spaceId,
        .trmId = 0,
        .labelId = DB_INVALID_UINT32,
        .dbInstance = NULL,
        .labelRsmUndo = NULL,
    };
    for (uint32_t i = 0; i < 1000; ++i) {
        RedoLogBegin(SeGetCurRedoCtx());
        if (ctx->pageList.size() != 0) {
            uint32_t index = rand() % ctx->pageList.size();
            PageIdT pageAddr = ctx->pageList[index];
            uint32_t opType = rand() % 3;
            if (opType == 0) {
                FreePageParamT freePageParam = {
                    .spaceId = ctx->spaceId, .addr = pageAddr, .dbInstance = NULL, .labelRsmUndo = NULL};
                SeInitCachePagePara(&freePageParam.cachePagePara);
                interRet = SeFreePage((PageMgrT *)seRunCtx->pageMgr, &freePageParam);
                DB_ASSERT(interRet == STATUS_OK_INTER);
                ctx->pageList.erase(ctx->pageList.begin() + index);
            }
            if (opType == 1) {
                uint8_t *page = NULL;
                interRet = SeGetPage((PageMgrT *)seRunCtx->pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, true);
                DB_ASSERT(interRet == STATUS_OK_INTER);
                uint32_t offset = rand() % (SIZE_K(seIns->seConfig.pageSize) - sizeof(PageHeadT));
                *(uint32_t *)(page + sizeof(PageHeadT) + offset) = rand();
                RedoPageMdLogT log = RedoPageMdLogCreate(sizeof(PageHeadT) + offset, sizeof(uint32_t));
                RedoPageMdLogWrite(SeGetCurRedoCtx(), &pageAddr, &log, page + sizeof(PageHeadT) + offset);
                SeLeavePage((PageMgrT *)seRunCtx->pageMgr, pageAddr, true);
            }
        }
        PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
        interRet = SeAllocPage((PageMgrT *)seRunCtx->pageMgr, &pageArg, &pageAddr);
        DB_ASSERT(interRet == STATUS_OK_INTER);
        ctx->pageList.push_back(pageAddr);
        interRet = RedoLogEnd(SeGetCurRedoCtx(), true);
        DB_ASSERT(interRet == STATUS_OK_INTER);
    }

    ret = SeClose(seRunCtx);
    DB_ASSERT(ret == GMERR_OK);
    return NULL;
}

#if !(defined COVERAGE)
TEST_F(SpaceBackupUt, backup_full_with_concurrent_op)
{
    uint32_t threadNum = 3;
    pthread_t threadIds[threadNum] = {0};
    BackupTestThreadCtxT ctx[threadNum] = {0};

    for (uint32_t i = 0; i < threadNum; ++i) {
        ctx[i].spaceId = i % 3;
        ASSERT_EQ(pthread_create(&threadIds[i], NULL, BackupTestContainerRandomOp, &ctx[i]), 0);
    }

    sleep(1);

    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = SE_BACK_UP_KEEP_ALL_SPACE,
        .spaceIds = NULL,
    };
    ret = SeBackupDatabase(seRunCtx, &args);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbSystemSpace"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbUndoSpace"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbUserSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbSafeFile"));
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < threadNum; ++i) {
        ASSERT_EQ(pthread_join(threadIds[i], NULL), 0);
    }

    BackupTestRebootFromBackup("backup/gmdb", GMERR_OK);

    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, backup_part_with_concurrent_op)
{
    uint32_t threadNum = 3;
    pthread_t threadIds[threadNum] = {0};
    BackupTestThreadCtxT ctx[threadNum] = {0};

    for (uint32_t i = 0; i < threadNum; ++i) {
        ctx[i].spaceId = i % 3;
        ASSERT_EQ(pthread_create(&threadIds[i], NULL, BackupTestContainerRandomOp, &ctx[i]), 0);
    }

    sleep(1);

    SeRunCtxT *seRunCtx = NULL;
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)DbGetTopDynMemCtx(NULL), NULL, &seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t spaceId = 0;
    SeBackupArgsT args = {
        .path = "backup/gmdb",
        .replace = true,
        .increment = false,
        .compress = false,
        .encrypt = false,
        .spaceNum = 1,
        .spaceIds = &spaceId,
    };
    ret = SeBackupDatabase(seRunCtx, &args);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(true, DbDirExist("backup/gmdb"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbCtrlFile"));
    ASSERT_EQ(true, DbFileExist("backup/gmdb/dbSystemSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbUndoSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbUserSpace"));
    ASSERT_EQ(false, DbFileExist("backup/gmdb/dbSafeFile"));
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ret = SeClose(seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < threadNum; ++i) {
        ASSERT_EQ(pthread_join(threadIds[i], NULL), 0);
    }

    BackupTestRebootFromBackup("backup/gmdb", GMERR_OK);

    system("rm -rf backup/gmdb");
}
#endif

bool DrtKeepThisWorkerAlive(DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    return false;
}

TEST_F(SpaceBackupUt, register_backup)
{
    seIns->seConfig.seKeepThreadAlive = DrtKeepThisWorkerAlive;
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    ctrl->backupRegistered = true;
    StatusInter ret = SePersistRegisterBackup(backupCtx.seRunCtx);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->backupRegistered = false;
    ctrl->swapRegistered = true;
    ret = SePersistRegisterBackup(backupCtx.seRunCtx);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->swapRegistered = false;
    ctrl->ddlOpCnt = 1;
    ret = SePersistRegisterBackup(backupCtx.seRunCtx);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->ddlOpCnt = 0;
    ret = SePersistRegisterBackup(backupCtx.seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    SeSetStorageEmergency(seIns, (char *)"register_backup");
    ret = SePersistRegisterBackup(backupCtx.seRunCtx);
    ASSERT_EQ(DATABASE_NOT_AVAILABLE_INTER, ret);
    SeSetStorageNotEmergency(seIns);
    ASSERT_EQ(GMERR_OK, SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, register_flush)
{
    seIns->seConfig.seKeepThreadAlive = DrtKeepThisWorkerAlive;
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    ctrl->swapRegistered = true;
    StatusInter ret = SeRegisterFlush(backupCtx.seRunCtx, 100);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->swapRegistered = false;
    ctrl->flushRegistered = true;
    ret = SeRegisterFlush(backupCtx.seRunCtx, 100);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->flushRegistered = false;
    ctrl->dmlOpCnt = 1;
    ret = SeRegisterFlush(backupCtx.seRunCtx, 100);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->dmlOpCnt = 0;
    ret = SeRegisterFlush(backupCtx.seRunCtx, 100);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    SeSetStorageEmergency(seIns, (char *)"register_flush ");
    ret = SeRegisterFlush(backupCtx.seRunCtx, 100);
    ASSERT_EQ(DATABASE_NOT_AVAILABLE_INTER, ret);
    SeSetStorageNotEmergency(seIns);
    ASSERT_EQ(GMERR_OK, SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, register_dataop)
{
    seIns->seConfig.seKeepThreadAlive = DrtKeepThisWorkerAlive;
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    ctrl->swapRegistered = true;
    StatusInter ret = SeRegisterDataOpImpl(backupCtx.seRunCtx, true);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->swapRegistered = false;
    ctrl->flushRegistered = true;
    ret = SeRegisterDataOpImpl(backupCtx.seRunCtx, true);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ret = SeRegisterDataOpImpl(backupCtx.seRunCtx, false);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    ctrl->flushRegistered = false;
    ret = SeRegisterDataOpImpl(backupCtx.seRunCtx, true);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    SeSetStorageEmergency(seIns, (char *)"register_dataop");
    ret = SeRegisterDataOpImpl(backupCtx.seRunCtx, true);
    ASSERT_EQ(DATABASE_NOT_AVAILABLE_INTER, ret);
    SeSetStorageNotEmergency(seIns);
    ASSERT_EQ(GMERR_OK, SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, register_ddlop)
{
    seIns->seConfig.seKeepThreadAlive = DrtKeepThisWorkerAlive;
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    ctrl->dmlOpCnt = 1;
    ctrl->backupRegistered = true;
    Status ret = SePersistRegisterDDLOp(backupCtx.seRunCtx);
    ASSERT_EQ(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ctrl->backupRegistered = false;
    ret = SePersistRegisterDDLOp(backupCtx.seRunCtx);
    ASSERT_EQ(GMERR_OK, ret);
    SeSetStorageEmergency(seIns, (char *)"register_ddlop");
    ret = SePersistRegisterDDLOp(backupCtx.seRunCtx);
    ASSERT_EQ(GMERR_DATABASE_NOT_AVAILABLE, ret);
    SeSetStorageNotEmergency(seIns);
    ASSERT_EQ(GMERR_OK, SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}

TEST_F(SpaceBackupUt, register_swap)
{
    seIns->seConfig.seKeepThreadAlive = DrtKeepThisWorkerAlive;
    TestBackupCtxT backupCtx = {0};
    BackupTestCreateBackupCtx(seIns, &backupCtx);
    system("mkdir backup/gmdb");
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    ctrl->swapRegistered = true;
    StatusInter ret = SePersistRegisterSwap(backupCtx.seRunCtx);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->swapRegistered = false;
    ret = SePersistRegisterSwap(backupCtx.seRunCtx);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    ctrl->dmlOpCnt = 0;
    ctrl->dqlOpCnt = 0;
    ctrl->backupRegistered = false;
    ctrl->flushRegistered = false;
    ret = SePersistRegisterSwap(backupCtx.seRunCtx);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    SeSetStorageEmergency(seIns, (char *)"register_swap");
    ret = SePersistRegisterSwap(backupCtx.seRunCtx);
    ASSERT_EQ(LOCK_NOT_AVAILABLE_INTER, ret);
    SeSetStorageNotEmergency(seIns);
    ASSERT_EQ(GMERR_OK, SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE));
    BackupTestRemoveBackupCtx(&backupCtx);
    system("rm -rf backup/gmdb");
}
