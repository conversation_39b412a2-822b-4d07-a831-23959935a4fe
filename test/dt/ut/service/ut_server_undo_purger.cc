/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: ut_server_undo_purger.cc
 * Description: db_server_undo_purger.c ut
 * Author: zhaoyongluo
 * Create: 2022.08.08
 */

#include "gtest/gtest.h"
#include "common_init.h"
#include "gmc_errno.h"
#include "db_server_undo_purger.h"
#include "stub.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#include "se_instance.h"
#include "drt_instance.h"
#include "drt_worker_manager.h"
#include "se_index.h"
#include "ee_key_cmp.h"
#include "se_heap_inner.h"
#include "se_heap_utils.h"
#include "ee_topo_label.h"
#include "se_trx.h"
#include "se_table_space_pub.h"
#include "db_sysapp_context.h"
#include "drt_worker_manager.h"
#include "dm_meta_kv_label.h"
#include "dm_meta_basic_in.h"

#define UT_LABEL_ID 0xFACEBEEF
#ifdef __cplusplus
extern "C" {
#endif

extern Status PurgerSetIdxKeyCmpCallback(SeRunCtxHdT seRunCtx, uint32_t labelId, IndexOpenCfgT *idxOpenCfg);

extern Status PurgerGetHeapHandle(
    uint32_t labelId, DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, DmLabelTypeE labelType, HpRunHdlT *heapRunCtx);

extern Status PurgerGetFixedHeapHandle(
    uint32_t labelId, DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, FixedHeapRunHdlT *edgeTopoCtxOut);

extern Status PurgerReleaseLabelAndLatch(DmLabelTypeE labelType, void *label);

#ifdef __cplusplus
}
#endif

class UtServerUndoPurger : public testing::Test {
public:
    static DbTopShmemCtxT *gTopShmMemCtx;
    static SeInstanceHdT gSeIns;
    static DbMemCtxT *gTopDynamicMemCtx;
    static DmVertexLabelT *stubVertexLabel;
    static DmKvLabelT *stubKvLabel;
    static DmEdgeLabelT *stubEdgeLabel;

protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        gTopShmMemCtx = (DbTopShmemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
        ASSERT_NE((DB_UINTPTR)gTopShmMemCtx, (DB_UINTPTR)NULL);

        DbMemCtxArgsT dyargs = {0};
        dyargs.ctxSize = sizeof(DbDynamicMemCtxT);
        dyargs.memType = DB_DYNAMIC_MEMORY;
        dyargs.init = DynamicAlgoInit;
        gTopDynamicMemCtx =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "Top Dynamic MemCtx", &dyargs);
        ASSERT_TRUE(gTopDynamicMemCtx != nullptr);
        // set up default dynamic memctx
        DbMemCtxSwitchTo((DbMemCtxT *)gTopDynamicMemCtx);

        SeConfigT config = {0};
        config.deviceSize = SE_DEFAULT_DEV_SIZE;
        config.pageSize = SE_DEFAULT_PAGE_SIZE;
        config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
        config.instanceId = GET_INSTANCE_ID;
        config.maxTrxNum = MAX_CONN_NUM;

        gSeIns = NULL;
        ret = SeCreateInstance(NULL, (DbMemCtxT *)gTopShmMemCtx, &config, (SeInstanceHdT *)&gSeIns);
        ASSERT_EQ(GMERR_OK, ret);

        ret = CommonInitDrtInstanceInEuler(false);
        ASSERT_EQ(GMERR_OK, ret);

        HeapAccessCfgT heapCfg = {
            .pageType = HEAP_VAR_LEN_ROW_PAGE,
            .tupleType = HEAP_TUPLE_TYPE_VERTEX,
            .fixRowSize = 0,
            .slotExtendSize = 0,
            .seInstanceId = GET_INSTANCE_ID,
            .isYangBigStore = false,
            .isStatusMergeSubs = false,
            .isPersistent = false,
            .isLabelLockSerializable = true,
            .isUseRsm = false,
            .ccType = CONCURRENCY_CONTROL_NORMAL,
            .trxType = OPTIMISTIC_TRX,
            .isolation = REPEATABLE_READ,
            .labelId = UT_LABEL_ID,
            .heapFileId = 0,
            .heapFsmFileId = 0,
            .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
            .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
            .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
            .maxItemNum = DB_MAX_UINT64,
        };
        ShmemPtrT heapShmAddr;
        ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
        ASSERT_EQ(GMERR_OK, ret);
        stubVertexLabel = (DmVertexLabelT *)DbDynMemCtxAlloc(gTopDynamicMemCtx, sizeof(DmVertexLabelT));
        ASSERT_NE((DB_UINTPTR)stubVertexLabel, (DB_UINTPTR)NULL);
        ShmemPtrT commonInfoShmPtr;
        uint32_t commonInfoSize = sizeof(VertexLabelCommonInfoT);
        MetaShmAlloc(
            (DbMemCtxT *)gTopShmMemCtx, (void **)&stubVertexLabel->commonInfo, &commonInfoShmPtr, commonInfoSize);
        stubVertexLabel->commonInfoShmPtr = commonInfoShmPtr;
        ASSERT_NE((DB_UINTPTR)stubVertexLabel->commonInfo, (DB_UINTPTR)NULL);
        stubVertexLabel->commonInfo->heapInfo.heapShmAddr = heapShmAddr;
        stubVertexLabel->metaCommon.isPersistent = false;
        stubVertexLabel->metaCommon.isUseRsm = false;
        stubVertexLabel->metaCommon.isDeleted = 0;

        heapCfg.tupleType = HEAP_TUPLE_TYPE_KV;
        heapCfg.labelId++;
        ShmemPtrT heapShmAddrKv;
        ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddrKv);
        stubKvLabel = (DmKvLabelT *)DbDynMemCtxAlloc(gTopDynamicMemCtx, sizeof(DmKvLabelT));
        ASSERT_NE((DB_UINTPTR)stubKvLabel, (DB_UINTPTR)NULL);
        stubKvLabel->heapShmAddr = heapShmAddrKv;
        stubKvLabel->metaCommon.isPersistent = false;
        stubKvLabel->metaCommon.isUseRsm = false;

        EdgeTopoStorageCfgT cfg = {.seInstanceId = GET_INSTANCE_ID, .rowSize = (uint16_t)64, .labelId = 2};
        ShmemPtrT edgeTopoIns;
        ret = EdgeTopoLabelCreate(&cfg, &edgeTopoIns, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        stubEdgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc(gTopDynamicMemCtx, sizeof(DmEdgeLabelT));
        ASSERT_NE((DB_UINTPTR)stubEdgeLabel, (DB_UINTPTR)NULL);
        stubEdgeLabel->topoShmAddr = edgeTopoIns;
        stubVertexLabel->metaCommon.isPersistent = false;
        stubEdgeLabel->metaCommon.isUseRsm = false;

        uint32_t labelLatchId, labelLatchVersionId;
        ShmemPtrT labelLatchShmAddr;
        ret = InitLabelLatch(&labelLatchId, &labelLatchVersionId, &labelLatchShmAddr, NULL);
        ASSERT_EQ(GMERR_OK, ret);

        stubVertexLabel->commonInfo->vertexLabelLatchId = labelLatchId;
        stubVertexLabel->commonInfo->vertexLabelLatchVersionId = labelLatchVersionId;
        stubVertexLabel->commonInfo->labelLatchShmAddr = labelLatchShmAddr;

        stubKvLabel->labelLatchId = labelLatchId;
        stubKvLabel->labelLatchVersionId = labelLatchVersionId;
        stubKvLabel->labelLatchShmAddr = labelLatchShmAddr;

        stubEdgeLabel->edgeLabelLatchId = labelLatchId;
        stubEdgeLabel->edgeLabelLatchVersionId = labelLatchVersionId;
        stubEdgeLabel->labelLatchShmAddr = labelLatchShmAddr;
        stubEdgeLabel->metaCommon.metaId = 2;
    }
    static void TearDownTestCase()
    {
        DrtInstanceDestroy(NULL);
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        CommonRelease();
    }
};

SeInstanceHdT UtServerUndoPurger::gSeIns = NULL;
DbTopShmemCtxT *UtServerUndoPurger::gTopShmMemCtx = NULL;
DbMemCtxT *UtServerUndoPurger::gTopDynamicMemCtx = NULL;
DmVertexLabelT *UtServerUndoPurger::stubVertexLabel = NULL;
DmKvLabelT *UtServerUndoPurger::stubKvLabel = NULL;
DmEdgeLabelT *UtServerUndoPurger::stubEdgeLabel = NULL;

Status StubCataGetVertexLabelByIdNotFound(DbInstanceHdT dbInstance, uint32_t vlId, DmVertexLabelT **vertexLabel)
{
    return GMERR_UNDEFINED_TABLE;
}

Status StubCataGetVertexLabelById(DbInstanceHdT dbInstance, uint32_t vlId, DmVertexLabelT **vertexLabel)
{
    *vertexLabel = UtServerUndoPurger::stubVertexLabel;
    return GMERR_OK;
}

Status StubCataGetLabelById(uint32_t labelId, DmKvLabelT **label, DbInstanceHdT dbInstance)
{
    *label = UtServerUndoPurger::stubKvLabel;
    return GMERR_OK;
}

Status StubCataGetEdgeLabelById(uint32_t elId, DmEdgeLabelT **edgeLabel, DbInstanceHdT dbInstance)
{
    *edgeLabel = UtServerUndoPurger::stubEdgeLabel;
    return GMERR_OK;
}

Status StubCataReleaseVertexLabel(DmVertexLabelT *vertexLabel)
{
    return GMERR_OK;
}

Status StubCataReleaseLabel(DmKvLabelT *label)
{
    return GMERR_OK;
}

Status StubCataReleaseEdgeLabel(DmEdgeLabelT *edgeLabel)
{
    return GMERR_OK;
}

Status StubHeapLabelAllocAndInitRunctx(HeapRunCtxAllocCfgT *cfg, HpRunHdlT *heapRunHdl)
{
    return GMERR_MEMORY_OPERATE_FAILED;
}

Status StubHeapLabelOpen(HpRunHdlT heapRunHdl, HpOpTypeE opType, DbMemCtxT *usrMemCtx)
{
    return GMERR_INTERNAL_ERROR;
}

Status StubEdgeTopoOpen(ShmemPtrT edgeTopoIns, SeRunCtxHdT seRunCtx, DbMemCtxHdlT usrMemCtx, DmEdgeLabelT *edgeLabel,
    FixedHpOpTypeE opType, FixedHeapRunHdlT *edgeTopoCtx)
{
    return GMERR_OUT_OF_MEMORY;
}

ShmemPtrT StubGetLabelRWLatchShmPtrById(uint32_t labelLatchId, DbInstanceHdT dbInstance)
{
    return DB_INVALID_SHMPTR;
}

Status DrtStartNewBgServiceStub(WorkerMgrT *workerMgr, const WorkerParaT *servicePara, uint16_t *workerId)
{
    return GMERR_INSUFFICIENT_RESOURCES;
}

TEST_F(UtServerUndoPurger, StartUndoPurgerWorker)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    int32_t ret = ServerCreateUndoPurger(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // stop success
    DrtStopAllWorker(&drtInstance->workerMgr);
}

TEST_F(UtServerUndoPurger, StartUndoPurgerWorkerFailTest)
{
    int stubIdx = setStubC((void *)DrtStartNewBgService, (void *)DrtStartNewBgServiceStub);
    ASSERT_GE(stubIdx, 0);
    int32_t ret = ServerCreateUndoPurger(NULL);
    EXPECT_EQ(GMERR_INSUFFICIENT_RESOURCES, ret);

    clearAllStub();
}

TEST_F(UtServerUndoPurger, PurgerSetIdxKeyCmpCallbackTest)
{
    SeRunCtxHdT seRunCtx = NULL;
    SeOpen(GET_INSTANCE_ID, UtServerUndoPurger::gTopDynamicMemCtx, NULL, &seRunCtx);
    ASSERT_NE((DB_UINTPTR)seRunCtx, (DB_UINTPTR)NULL);
    int stubIdx = setStubC((void *)CataGetVertexLabelById, (void *)StubCataGetVertexLabelByIdNotFound);
    ASSERT_GE(stubIdx, 0);
    IndexOpenCfgT idxCfg = {0};
    PurgerSetIdxKeyCmpCallback(seRunCtx, 0, &idxCfg);
    EXPECT_EQ((DB_UINTPTR)idxCfg.callbackFunc.keyCmp, (DB_UINTPTR)QryKvHashCompare);
    clearStub(stubIdx);

    stubIdx = setStubC((void *)CataGetVertexLabelById, (void *)StubCataGetVertexLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseVertexLabel, (void *)StubCataReleaseVertexLabel);
    ASSERT_GE(stubIdx, 0);
    idxCfg = {0};
    PurgerSetIdxKeyCmpCallback(seRunCtx, 0, &idxCfg);
    EXPECT_EQ((DB_UINTPTR)idxCfg.callbackFunc.keyCmp, (DB_UINTPTR)QryVertexHashCompare);
    clearAllStub();
    SeClose(seRunCtx);
}

void StPurgerGetHandleSuccess(SeRunCtxHdT seRunCtx)
{
    HpRunHdlT heapRunCtx = NULL;
    PurgerGetHeapHandle(0, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, VERTEX_LABEL, &heapRunCtx);
    EXPECT_NE((DB_UINTPTR)heapRunCtx, (DB_UINTPTR)NULL);

    DmVertexLabelT *vertexLabel = (DmVertexLabelT *)heapRunCtx->dmDetail.dmInfo;
    ASSERT_EQ((DB_UINTPTR)vertexLabel, (DB_UINTPTR)UtServerUndoPurger::stubVertexLabel);
    heapRunCtx->heapTrxCtx->heapRunHdl = NULL;  // HeapLabelOpen在purger场景下会设置容器内句柄为自身，置空防止多次释放
    HeapLabelResetCtx(heapRunCtx);
    HeapLabelReleaseRunctx(heapRunCtx);
    // 单进程多实例下防止core
    vertexLabel->memCtx = NULL;

    PurgerReleaseLabelAndLatch(VERTEX_LABEL, vertexLabel);

    // 获取kv heap上下文
    int32_t ret = PurgerGetHeapHandle(1, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, KV_TABLE, &heapRunCtx);
    EXPECT_EQ(ret, 0);
    EXPECT_NE((DB_UINTPTR)heapRunCtx, (DB_UINTPTR)NULL);

    DmKvLabelT *kvLabel = (DmKvLabelT *)heapRunCtx->dmDetail.dmInfo;
    ASSERT_EQ((DB_UINTPTR)kvLabel, (DB_UINTPTR)UtServerUndoPurger::stubKvLabel);
    heapRunCtx->heapTrxCtx->heapRunHdl = NULL;  // HeapLabelOpen在purger场景下会设置容器内句柄为自身，置空防止多次释放
    HeapLabelResetCtx(heapRunCtx);
    HeapLabelReleaseRunctx(heapRunCtx);

    PurgerReleaseLabelAndLatch(KV_TABLE, kvLabel);

    // 获取FixedHeap上下文
    FixedHeapRunHdlT fixedHeapCtx = NULL;
    PurgerGetFixedHeapHandle(2, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, &fixedHeapCtx);
    EXPECT_NE((DB_UINTPTR)fixedHeapCtx, (DB_UINTPTR)NULL);

    EdgeTopoClose(fixedHeapCtx);

    PurgerReleaseLabelAndLatch(EDGE_LABEL, UtServerUndoPurger::stubEdgeLabel);
}

TrxCfgT GetDefaultTrxCfg()
{
    TrxCfgT trxCfg = {0};
    trxCfg.connId = 8888;
    trxCfg.trxType = OPTIMISTIC_TRX;
    trxCfg.isolationLevel = REPEATABLE_READ;
    return trxCfg;
}

TEST_F(UtServerUndoPurger, PurgerGetHandleTest)
{
    int stubIdx = setStubC((void *)CataGetVertexLabelById, (void *)StubCataGetVertexLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataGetLabelById, (void *)StubCataGetLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataGetEdgeLabelById, (void *)StubCataGetEdgeLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseVertexLabel, (void *)StubCataReleaseVertexLabel);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseLabel, (void *)StubCataReleaseLabel);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseEdgeLabel, (void *)StubCataReleaseEdgeLabel);
    ASSERT_GE(stubIdx, 0);
    SeRunCtxHdT seRunCtx = NULL;
    SeOpen(GET_INSTANCE_ID, UtServerUndoPurger::gTopDynamicMemCtx, NULL, &seRunCtx);
    ASSERT_NE((DB_UINTPTR)seRunCtx, (DB_UINTPTR)NULL);

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    SeTransBegin(seRunCtx, &trxCfg);

    StPurgerGetHandleSuccess(seRunCtx);

    SeTransCommit(seRunCtx);
    SeClose(seRunCtx);

    clearAllStub();
}

TEST_F(UtServerUndoPurger, PurgerGetHandleFailedTest1)
{
    int stubIdx = setStubC((void *)CataGetVertexLabelById, (void *)StubCataGetVertexLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataGetLabelById, (void *)StubCataGetLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataGetEdgeLabelById, (void *)StubCataGetEdgeLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseVertexLabel, (void *)StubCataReleaseVertexLabel);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseLabel, (void *)StubCataReleaseLabel);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseEdgeLabel, (void *)StubCataReleaseEdgeLabel);
    ASSERT_GE(stubIdx, 0);
    SeRunCtxHdT seRunCtx = NULL;
    SeOpen(GET_INSTANCE_ID, UtServerUndoPurger::gTopDynamicMemCtx, NULL, &seRunCtx);
    ASSERT_NE((DB_UINTPTR)seRunCtx, (DB_UINTPTR)NULL);

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    SeTransBegin(seRunCtx, &trxCfg);

    // 获取Heap上下文时，分配runctx失败
    stubIdx = setStubC((void *)HeapLabelAllocAndInitRunctx, (void *)StubHeapLabelAllocAndInitRunctx);
    ASSERT_GE(stubIdx, 0);

    HpRunHdlT heapRunCtx = NULL;
    Status ret = PurgerGetHeapHandle(0, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, VERTEX_LABEL, &heapRunCtx);
    EXPECT_EQ(GMERR_MEMORY_OPERATE_FAILED, ret);

    int clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    // 获取Heap上下文时Open失败
    stubIdx = setStubC((void *)HeapLabelOpen, (void *)StubHeapLabelOpen);
    ASSERT_GE(stubIdx, 0);
    heapRunCtx = NULL;
    ret = PurgerGetHeapHandle(0, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, VERTEX_LABEL, &heapRunCtx);
    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);

    clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    // 获取kv heap上下文，分配runctx失败
    stubIdx = setStubC((void *)HeapLabelAllocAndInitRunctx, (void *)StubHeapLabelAllocAndInitRunctx);
    ASSERT_GE(stubIdx, 0);
    ret = PurgerGetHeapHandle(1, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, KV_TABLE, &heapRunCtx);
    EXPECT_EQ(GMERR_MEMORY_OPERATE_FAILED, ret);

    clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    // 获取kv heap上下文，分配runctx失败
    stubIdx = setStubC((void *)HeapLabelOpen, (void *)StubHeapLabelOpen);
    ASSERT_GE(stubIdx, 0);
    ret = PurgerGetHeapHandle(1, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, KV_TABLE, &heapRunCtx);
    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);

    clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    // 获取FixedHeap上下文，Open失败
    stubIdx = setStubC((void *)EdgeTopoOpen, (void *)StubEdgeTopoOpen);
    ASSERT_GE(stubIdx, 0);
    FixedHeapRunHdlT fixedHeapCtx = NULL;
    ret = PurgerGetFixedHeapHandle(2, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, &fixedHeapCtx);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    // 最后测试失败后锁等资源都正确释放，可以正常获取handle
    StPurgerGetHandleSuccess(seRunCtx);

    SeTransCommit(seRunCtx);
    SeClose(seRunCtx);

    clearAllStub();
}

void StubLabelRLatchAcquireFixHeapFail(LabelRWLatchT *labelRWLatch)
{
    UtServerUndoPurger::stubEdgeLabel->edgeLabelLatchVersionId = 10;
    DbRWLatchR(&labelRWLatch->rwlatch);
}

TEST_F(UtServerUndoPurger, PurgerGetHandleFailedTest2)
{
    int stubIdx = setStubC((void *)CataReleaseVertexLabel, (void *)StubCataReleaseVertexLabel);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataGetLabelById, (void *)StubCataGetLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataGetEdgeLabelById, (void *)StubCataGetEdgeLabelById);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseLabel, (void *)StubCataReleaseLabel);
    ASSERT_GE(stubIdx, 0);
    stubIdx = setStubC((void *)CataReleaseEdgeLabel, (void *)StubCataReleaseEdgeLabel);
    SeRunCtxHdT seRunCtx = NULL;
    SeOpen(GET_INSTANCE_ID, UtServerUndoPurger::gTopDynamicMemCtx, NULL, &seRunCtx);
    ASSERT_NE((DB_UINTPTR)seRunCtx, (DB_UINTPTR)NULL);

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    SeTransBegin(seRunCtx, &trxCfg);

    // 获取Label失败情况
    stubIdx = setStubC((void *)CataGetVertexLabelById, (void *)StubCataGetVertexLabelByIdNotFound);
    ASSERT_GE(stubIdx, 0);
    HpRunHdlT heapRunCtx = NULL;
    Status ret = PurgerGetHeapHandle(0, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, VERTEX_LABEL, &heapRunCtx);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    int clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    stubIdx = setStubC((void *)CataGetVertexLabelById, (void *)StubCataGetVertexLabelById);
    ASSERT_GE(stubIdx, 0);

    // 获取锁失败的情况
    stubIdx = setStubC((void *)GetLabelRWLatchShmPtrById, (void *)StubGetLabelRWLatchShmPtrById);
    ASSERT_GE(stubIdx, 0);
    ret = PurgerGetHeapHandle(0, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, VERTEX_LABEL, &heapRunCtx);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    clearRet = clearStub(stubIdx);
    ASSERT_GE(clearRet, 0);

    // 加锁失败的情况, 那label的latchVersionId改掉来模拟
    uint32_t latchVersionId = UtServerUndoPurger::stubVertexLabel->commonInfo->vertexLabelLatchVersionId;
    UtServerUndoPurger::stubVertexLabel->commonInfo->vertexLabelLatchVersionId = 10;
    ret = PurgerGetHeapHandle(0, UtServerUndoPurger::gTopDynamicMemCtx, seRunCtx, VERTEX_LABEL, &heapRunCtx);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    UtServerUndoPurger::stubVertexLabel->commonInfo->vertexLabelLatchVersionId = latchVersionId;

    StPurgerGetHandleSuccess(seRunCtx);

    SeTransCommit(seRunCtx);
    SeClose(seRunCtx);

    clearAllStub();
}
