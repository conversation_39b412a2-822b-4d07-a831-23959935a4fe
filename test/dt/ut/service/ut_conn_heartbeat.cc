#ifdef HPE
#include "gtest/gtest.h"
#include "common_init.h"
#include "stub.h"
#include "adpt_rdtsc.h"
#include "srv_conn_service.h"
#include "db_rpc_conn_msg.h"
#include "ee_session.h"
#include "ee_session_interface.h"
#include "ee_cltcache.h"

class UtConnCltStat : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        CommonInit();
    }
    static void TearDownTestCase()
    {
        CommonRelease();
        clearAllStub();
    }
};

extern "C" Status ConnServiceEntry(
    DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx, uint16_t serviceId, uint32_t opCode, void *userCtx);
extern "C" Status ConnCompleteCltStat(DrtProcCtxT *procCtx, FixBufferT *response, Status opStatus);

static void DrtFreeProcCtxStub(DrtProcCtxT *procCtx)
{
    return;
}

static void DrtFreeMsgStub(FixBufferT *msg)
{
    return;
}

extern "C" Status ConnCltStat(ConnServiceT *connService, DrtProcCtxT *procCtx, DrtConnectionT *conn);
Status ConnCltStatStub(ConnServiceT *connService, DrtProcCtxT *procCtx, DrtConnectionT *conn)
{
    return GMERR_CONNECTION_FAILURE;
}

int32_t FixBufPutDataStubF(FixBufferT *buffer, const void *data, uint32_t size)
{
    return DB_ERROR;
}

int32_t DrtCompleteStubF(DrtConnectionT *conn, DrtProcCtxT *procCtx, FixBufferT *response, int32_t opStatus)
{
    return DB_ERROR;
}

DrtNodeT *DrtGetNodeStubTSYNC(DrtNodeMgrT *nodeMgr, uint8_t nodeType, uint16_t nodeId)
{
    static DrtNodeT drtNode;
    drtNode.nodeId.flag = GMC_CONN_TYPE_SYNC;
    return &drtNode;
}

int32_t DrtAllocResponseStubF(DrtConnectionT *conn, FixBufferT *response)
{
    return DB_ERROR;
}

static int32_t DrtAllocResponseStubT(DrtConnectionT *conn, FixBufferT *response)
{
    static uint8_t data[CS_PACK_SIZE];
    FixBufInit(response, data, sizeof(data), 0, 0, NULL);
    FixBufInitPut(response, MSG_HEADER_ALIGN_SIZE);
    return DB_SUCCESS;
}

Status ConnCompleteCltStatStubF(DrtProcCtxT *procCtx, FixBufferT *response, Status opStatus)
{
    return GMERR_CONNECTION_FAILURE;
}

void UtQueryCltStatResetRequest(FixBufferT *request, CltConnDfxInfoT *cltDfx)
{
    EXPECT_TRUE(request != NULL);
    FixBufInit(request, (uint8_t *)&cltDfx, sizeof(CltConnDfxInfoT), sizeof(CltConnDfxInfoT), 0, NULL);
}

TEST_F(UtConnCltStat, updateCltStat)
{
    // 构造待更新统计结构（dst）
    DrtConnectionT conn;
    (void)memset_s(&conn, sizeof(DrtConnectionT), 0, sizeof(DrtConnectionT));
    SessionT session;
    (void)memset_s(&session.cltDfxInfo, sizeof(CltConnDfxInfoT), 0, sizeof(CltConnDfxInfoT));
    conn.session = (void *)&session;
    // 构造传入的统计信息（src）
    CltConnDfxInfoT cltDfxSrc = {.sndSuccessCnt = 10, .sndFailCnt = 10, .rcvSuccessCnt = 10, .rcvFailCnt = 10};
    for (uint32_t i = 0; i < GMC_SUB_EVENT_BUTT; i++) {
        cltDfxSrc.subEventCount[i] = 10;
    }
    // 更新
    QryUpdateSessionCltDfxInfo(&conn, &cltDfxSrc);
    EXPECT_EQ(session.cltDfxInfo.sndSuccessCnt, 10U);
    EXPECT_EQ(session.cltDfxInfo.sndSuccessCnt, 10U);
    EXPECT_EQ(session.cltDfxInfo.sndSuccessCnt, 10U);
    EXPECT_EQ(session.cltDfxInfo.sndSuccessCnt, 10U);
    for (uint32_t i = 0; i < GMC_SUB_EVENT_BUTT; i++) {
        EXPECT_EQ(session.cltDfxInfo.subEventCount[i], 10U);
    }
}

TEST_F(UtConnCltStat, sendCltStatResponse)
{
    DrtConnectionT conn;
    DrtProcCtxT procCtx;
    procCtx.conn = &conn;
    procCtx.msg = {0};
    FixBufferT response = {0};
    // put应答数据失败
    setStubC((void *)FixBufPutData, (void *)FixBufPutDataStubF);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    Status ret = ConnCompleteCltStat(&procCtx, &response, GMERR_OK);
    EXPECT_EQ(DB_ERROR, ret);
    clearAllStub();
    // 发送失败
    setStub(FixBufPutData);
    setStubC((void *)DrtCompleteImpl, (void *)DrtCompleteStubF);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = ConnCompleteCltStat(&procCtx, &response, GMERR_OK);
    EXPECT_EQ(DB_ERROR, ret);
    clearAllStub();
    // 发送成功
    setStub(FixBufPutData);
    setStub(DrtComplete);
    ret = ConnCompleteCltStat(&procCtx, &response, GMERR_OK);
    EXPECT_EQ(DB_SUCCESS, ret);
    clearAllStub();
}

TEST_F(UtConnCltStat, synsConn)
{
    // 构造合法入参
    DrtConnectionT conn;
    (void)memset_s(&conn, sizeof(DrtConnectionT), 0, sizeof(DrtConnectionT));
    conn.nodeId.flag = (uint8_t)GMC_CONN_TYPE_SYNC;

    SessionT session;
    (void)memset_s(&session.cltDfxInfo, sizeof(CltConnDfxInfoT), 0, sizeof(CltConnDfxInfoT));
    conn.session = &session;

    DrtProcCtxT procCtx;
    procCtx.msg = {0};
    procCtx.conn = &conn;
    CltConnDfxInfoT cltDfx;

    UtQueryCltStatResetRequest(&procCtx.msg, &cltDfx);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    setStub(FixBufRelease);
    setStub(CltUpdateSysView);

    ConnServiceT connService;
    DrtInstance drtInstance;
    connService.drtInstance = &drtInstance;
    Status ret = ConnCltStat(&connService, &procCtx, &conn);
    EXPECT_EQ(DB_SUCCESS, ret);

    clearAllStub();
}

TEST_F(UtConnCltStat, cltStatSuccess)
{
    // 构造合法入参
    DrtConnectionT conn;
    (void)memset_s(&conn, sizeof(DrtConnectionT), 0, sizeof(DrtConnectionT));
    conn.id = 3;
    SessionT session;
    (void)memset_s(&session.cltDfxInfo, sizeof(CltConnDfxInfoT), 0, sizeof(CltConnDfxInfoT));
    conn.session = &session;
    conn.nodeId.flag = GMC_CONN_TYPE_ASYNC;

    DrtProcCtxT procCtx;
    procCtx.conn = &conn;
    procCtx.msg = {0};
    CltConnDfxInfoT cltDfx;

    UtQueryCltStatResetRequest(&procCtx.msg, &cltDfx);
    setStubC((void *)DrtAllocResponseImpl, (void *)DrtAllocResponseStubT);
    setStub(ConnCompleteCltStat);

    ConnServiceT connService;
    DrtInstance drtInstance;
    connService.drtInstance = &drtInstance;
    Status ret = ConnCltStat(&connService, &procCtx, &conn);
    EXPECT_EQ(GMERR_OK, ret);

    clearAllStub();
}

// Abn1：解析数据报错
TEST_F(UtConnCltStat, cltStatAbn1)
{
    DrtConnectionT conn;
    conn.type = CONN_TYPE_NORMAL;  // 非逃生通道

    DrtProcCtxT procCtx;
    procCtx.conn = &conn;
    procCtx.msg = {0};

    ConnServiceT connService;
    DrtInstance drtInstance;
    connService.drtInstance = &drtInstance;
    Status ret = ConnCltStat(&connService, &procCtx, &conn);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    clearAllStub();
}

// Abn2：获取node报错、申请response失败、发送response失败
TEST_F(UtConnCltStat, cltStatAbn2)
{
    // 构造合法入参
    DrtConnectionT conn;
    (void)memset_s(&conn, sizeof(DrtConnectionT), 0, sizeof(DrtConnectionT));
    conn.id = 3;
    SessionT session;
    (void)memset_s(&session.cltDfxInfo, sizeof(CltConnDfxInfoT), 0, sizeof(CltConnDfxInfoT));
    conn.session = &session;
    DrtInstance drtInstance;
    conn.connMgr = &drtInstance.connMgr;
    conn.nodeId.flag = GMC_CONN_TYPE_ASYNC;

    DrtProcCtxT procCtx;
    procCtx.conn = &conn;
    procCtx.msg = {0};
    CltConnDfxInfoT cltDfx;
    // 申请response失败
    UtQueryCltStatResetRequest(&procCtx.msg, &cltDfx);
    setStub(FixBufRelease);
    setStub(CltUpdateSysView);
    setStubC((void *)DrtAllocResponseImpl, (void *)DrtAllocResponseStubF);
    Status ret = GMERR_OK;

    ConnServiceT connService;
    connService.drtInstance = &drtInstance;
    ret = ConnCltStat(&connService, &procCtx, &conn);
    EXPECT_EQ(DB_ERROR, ret);

    clearAllStub();
    // 发送response失败
    UtQueryCltStatResetRequest(&procCtx.msg, &cltDfx);
    setStub(FixBufRelease);
    setStub(CltUpdateSysView);
    setStubC((void *)DrtAllocResponseImpl, (void *)DrtAllocResponseStubT);
    setStubC((void *)ConnCompleteCltStat, (void *)ConnCompleteCltStatStubF);

    ret = ConnCltStat(&connService, &procCtx, &conn);
    EXPECT_EQ(GMERR_CONNECTION_FAILURE, ret);

    EXPECT_EQ(DB_SUCCESS, ret);
    clearAllStub();
}

TEST_F(UtConnCltStat, connServiceAbn)
{
    DrtConnectionT conn = {0};
    MsgHeaderT msgHeader;
    ConnServiceT ctx;
    // alloc req
    DrtProcCtxT procCtx;
    procCtx.conn = &conn;
    procCtx.msg = {0};
    uint32_t size = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    uint8_t *buf = (uint8_t *)malloc(size);
    FixBufInit(&procCtx.msg, buf, size, size, 0, NULL);
    FixBufSeek(&procCtx.msg, size);
    // conn异常
    conn.status = CONN_STATUS_CLOSED;
    DrtConnMgrT connMgr = {0};
    conn.connMgr = &connMgr;
    DrtConnListAppendNodeNoLock(&connMgr.connLists[conn.status], &conn);
    DrtPipeT pipe;
    conn.drtPipe = &pipe;

    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = &ctx;
    procCtx.conn = &conn;
    procCtx.msgHeader = &msgHeader;
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    Status ret = ConnServiceEntry(&serviceCtx, &procCtx, DRT_SERVICE_CONN, MSG_OP_RPC_CONNECT, NULL);
    EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);

    // opCode异常
    DrtSetConnStatus(&conn, CONN_STATUS_NORMAL);

    OpHeaderT *opHdr = ProtocolPeekFirstOpHeader(&procCtx.msg);
    opHdr->opCode = MSG_OP_RPC_NONE;
    // update serviceCtx
    serviceCtx.ctx = &ctx;
    procCtx.conn = &conn;
    procCtx.msgHeader = &msgHeader;
    (void)setStub((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    ret = ConnServiceEntry(&serviceCtx, &procCtx, DRT_SERVICE_CONN, MSG_OP_RPC_NONE, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 更新统计数据失败
    opHdr->opCode = MSG_OP_RPC_HEARTBEAT;
    setStubC((void *)ConnCltStat, (void *)ConnCltStatStub);
    // update serviceCtx
    serviceCtx.ctx = &ctx;
    procCtx.conn = &conn;
    procCtx.msgHeader = &msgHeader;
    ret = ConnServiceEntry(&serviceCtx, &procCtx, DRT_SERVICE_CONN, MSG_OP_RPC_HEARTBEAT, NULL);
    EXPECT_EQ(GMERR_CONNECTION_FAILURE, ret);

    clearAllStub();
    free(procCtx.msg.buf);
}
#endif
