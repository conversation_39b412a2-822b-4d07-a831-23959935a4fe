project(GMDB_CLIENT_UT)

set(CLIENT_UT_INCLUDE_PATH ${CMAKE_CURRENT_SOURCE_DIR}/include)

include_directories(${CLIENT_UT_INCLUDE_PATH})

# 生成可执行文件
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/../common_test_include COMMON_TEST_INCLUDE_LIST)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common_test_include)
aux_source_directory(. CLIENT_UT_LIST)
aux_source_directory(./interface_check CLIENT_UT_LIST)
aux_source_directory(./utils CLIENT_UT_LIST)

link_libraries(rt)
add_executable(ut_client ${CLIENT_UT_LIST} ${COMMON_TEST_INCLUDE_LIST})

target_link_libraries(ut_client ${DEPS_GMDB} stub)
