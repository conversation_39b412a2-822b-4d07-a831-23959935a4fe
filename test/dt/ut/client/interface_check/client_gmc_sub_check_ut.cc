#include "client_ut_base.h"

#include "gmc_subscription_check.h"

class UtClientInterfaceSubCheck : public UtClientCheckBase {};

TEST_F(UtClientInterfaceSubCheck, TestGmcSubscribeCheck)
{
    stmt.conn->connType = GMC_CONN_TYPE_ASYNC;
    Status ret = GmcSubscribeCheck(&stmt, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    stmt.conn->connType = GMC_CONN_TYPE_SYNC;
    ret = GmcSubscribeCheck(&stmt, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    GmcSubConfigT config = {};
    GmcConnT conn2 = {};
    GmcSubCallbackT cb = [](auto, auto, auto) {};
    ret = GmcSubscribeCheck(&stmt, &config, &conn2, cb);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    config.subsName = "aaa";
    config.configJson = "bbb";
    ret = GmcSubscribeCheck(&stmt, &config, &conn2, cb);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    conn2.connType = GMC_CONN_TYPE_SUB;
    ret = GmcSubscribeCheck(&stmt, &config, &conn2, cb);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtClientInterfaceSubCheck, TestGmcSubSetFetchModeCheck)
{
    stmt.stmtType = CLT_STMT_TYPE_VERTEX;
    Status ret = GmcSubSetFetchModeCheck(&stmt, GMC_SUB_FETCH_NEW);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    stmt.stmtType = CLT_STMT_TYPE_SUB_VERTEX;
    ret = GmcSubSetFetchModeCheck(&stmt, GMC_SUB_FETCH_BUTT);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    stmt.stmtType = CLT_STMT_TYPE_SUB_VERTEX;
    ret = GmcSubSetFetchModeCheck(&stmt, GMC_SUB_FETCH_NEW);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtClientInterfaceSubCheck, TestGmcSubGetKeyCheck)
{
    Status ret = GmcSubGetKeyCheck(&stmt, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    const void *temp;
    ret = GmcSubGetKeyCheck(&stmt, &temp);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    stmt.stmtType = CLT_STMT_TYPE_SUB_VERTEX;
    ret = GmcSubGetKeyCheck(&stmt, &temp);
    EXPECT_EQ(GMERR_OK, ret);
}
