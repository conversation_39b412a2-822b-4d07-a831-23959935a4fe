#include "client_ut_base.h"

#include "gmc_privilege_check.h"

class UtClientInterfacePrivilegeCheck : public UtClientCheckBase {};

TEST_F(UtClientInterfacePrivilegeCheck, TestGmcObjectPrivsCheck)
{
    Status ret = GmcObjectPrivsCheck(&stmt, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    std::string userName(MAX_OS_USER_NAME_LENGTH - 1, 'a');
    std::string processName(DB_MAX_PROC_NAME_LEN + 1, 'a');
    GmcObjectPrivsT obPr = {};
    ret = GmcObjectPrivsCheck(&stmt, userName.c_str(), processName.c_str(), &obPr);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);

    std::string processName2(DB_MAX_PROC_NAME_LEN - 1, 'a');
    ret = GmcObjectPrivsCheck(&stmt, userName.c_str(), processName2.c_str(), &obPr);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}
