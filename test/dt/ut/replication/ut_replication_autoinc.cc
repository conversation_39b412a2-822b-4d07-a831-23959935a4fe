/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
#include "gtest/gtest.h"
#include "stub.h"
#include "cstdio"
#include "cstdlib"
#include "dm_meta_prop_strudefs.h"
#include "dm_data_index.h"
#include "dm_data_prop.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

const int THREAD_NUM = 2;
const int STUB_NUM = 1;
const int PROPID_UNUSED = 0;

Status DmVertexGetPropeByIdMock(const DmVertexT *vertex, uint32_t propeId, DmValueT *propeValue)
{
    propeValue->type = DB_DATATYPE_UINT64;
    propeValue->value.ulongValue = vertex->edgeAddrsEntry->first;
    return GMERR_OK;
}

class UtReplicationAutoInc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        EXPECT_EQ(GMERR_OK, init());
        EXPECT_EQ(STUB_NUM, setStubC((void *)(DmVertexGetPropeById), (void *)(DmVertexGetPropeByIdMock)));
    }
    static void TearDownTestCase()
    {
        EXPECT_EQ(STUB_NUM, clearAllStub());
    }
};

void *ThreadAutoInc(void *arg)
{
    DmAutoIncrPropInfoT *autoIncrInfo = (DmAutoIncrPropInfoT *)arg;
    uint64_t preAutoinc = 0;
    DmVertexT vertex = {0};
    DmEdgeAddrsT tempUint64 = {0};
    vertex.edgeAddrsEntry = &tempUint64;  // 选取了一个含有uint64的结构体，作为mock函数的入参，没有实际意义

    for (int i = 0; i < 10000; i++) {
        preAutoinc = autoIncrInfo->autoIncrValue;
        vertex.edgeAddrsEntry->first = (rand() % (DB_MAX_UINT64 - 1)) + 1;

        if (vertex.edgeAddrsEntry->first > autoIncrInfo->autoIncrMaxValue) {
            EXPECT_EQ(GMERR_DATA_EXCEPTION, AutoIncProcByOverwrite(&vertex, PROPID_UNUSED, autoIncrInfo));
            EXPECT_EQ(true, (autoIncrInfo->autoIncrValue - 1) >= (preAutoinc - 1));
            continue;
        }

        EXPECT_EQ(GMERR_OK, AutoIncProcByOverwrite(&vertex, PROPID_UNUSED, autoIncrInfo));
        EXPECT_EQ(true, (autoIncrInfo->autoIncrValue - 1) >= (preAutoinc - 1));
    }
    return NULL;
}

void *ThreadAutoIncReachLimit(void *arg)
{
    DmAutoIncrPropInfoT *autoIncrInfo = (DmAutoIncrPropInfoT *)arg;
    uint64_t preAutoinc = 0;
    DmVertexT vertex = {0};
    DmEdgeAddrsT tempUint64 = {0};
    vertex.edgeAddrsEntry = &tempUint64;  // 选取了一个含有uint64的结构体，作为mock函数的入参，没有实际意义

    for (int i = 0; i < 10000; i++) {
        preAutoinc = autoIncrInfo->autoIncrValue;
        vertex.edgeAddrsEntry->first = (rand() % (DB_MAX_UINT64 - 1)) + 1;
        EXPECT_EQ(GMERR_OK, AutoIncProcByOverwrite(&vertex, PROPID_UNUSED, autoIncrInfo));
        EXPECT_EQ(true, (autoIncrInfo->autoIncrValue - 1) >= (preAutoinc - 1));
    }

    vertex.edgeAddrsEntry->first = DB_MAX_UINT64;
    EXPECT_EQ(GMERR_OK, AutoIncProcByOverwrite(&vertex, PROPID_UNUSED, autoIncrInfo));
    EXPECT_EQ(true, autoIncrInfo->autoIncrValue == 0);

    for (int i = 0; i < 10000; i++) {
        preAutoinc = autoIncrInfo->autoIncrValue;
        vertex.edgeAddrsEntry->first = (rand() % (DB_MAX_UINT64 - 1)) + 1;
        EXPECT_EQ(GMERR_OK, AutoIncProcByOverwrite(&vertex, PROPID_UNUSED, autoIncrInfo));
        EXPECT_EQ(true, autoIncrInfo->autoIncrValue == 0);
    }

    return NULL;
}

// 一般情况，不超出最大值
TEST_F(UtReplicationAutoInc, Autoinc01)
{
    DmAutoIncrPropInfoT autoIncrInfo = {.type = DB_DATATYPE_UINT64,
        .autoIncrPropId = 1,
        .autoIncrStartValue = 1,
        .autoIncrValue = 1,
        .autoIncrMaxValue = DB_MAX_UINT64};

    pthread_t threads[THREAD_NUM] = {0};
    EXPECT_EQ(GMERR_OK, pthread_create(&threads[0], NULL, ThreadAutoInc, (void *)&autoIncrInfo));
    EXPECT_EQ(GMERR_OK, pthread_create(&threads[1], NULL, ThreadAutoInc, (void *)&autoIncrInfo));

    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        pthread_join(threads[i], NULL);
    }
}

// 无效值检测
TEST_F(UtReplicationAutoInc, Autoinc02)
{
    DmAutoIncrPropInfoT autoIncrInfo = {.type = DB_DATATYPE_UINT64,
        .autoIncrPropId = 1,
        .autoIncrStartValue = 1,
        .autoIncrValue = 1,
        .autoIncrMaxValue = 800};

    pthread_t threads[THREAD_NUM] = {0};
    EXPECT_EQ(GMERR_OK, pthread_create(&threads[0], NULL, ThreadAutoInc, (void *)&autoIncrInfo));
    EXPECT_EQ(GMERR_OK, pthread_create(&threads[1], NULL, ThreadAutoInc, (void *)&autoIncrInfo));

    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        pthread_join(threads[i], NULL);
    }
}

// 写入达到临界值检测
TEST_F(UtReplicationAutoInc, Autoinc03)
{
    DmAutoIncrPropInfoT autoIncrInfo = {.type = DB_DATATYPE_UINT64,
        .autoIncrPropId = 1,
        .autoIncrStartValue = 1,
        .autoIncrValue = 1,
        .autoIncrMaxValue = DB_MAX_UINT64};

    pthread_t threads[THREAD_NUM] = {0};
    EXPECT_EQ(GMERR_OK, pthread_create(&threads[0], NULL, ThreadAutoIncReachLimit, (void *)&autoIncrInfo));
    EXPECT_EQ(GMERR_OK, pthread_create(&threads[1], NULL, ThreadAutoIncReachLimit, (void *)&autoIncrInfo));

    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        pthread_join(threads[i], NULL);
    }
}
