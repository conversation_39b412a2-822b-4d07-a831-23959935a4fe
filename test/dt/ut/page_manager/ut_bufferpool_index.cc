/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: system test
 * Author:
 * Create:
 */
#include <vector>
#include <map>
#include <thread>
#include <string>
#include <cmath>
#include "gtest/gtest.h"
#include "se_buffer_pool_page_buffer.h"
#include "stub.h"
#include "db_config.h"
#include "ut_bufferpool_base.h"
#include "se_define.h"
#include "se_redo.h"
#include "se_ckpt.h"
#include "se_page_desc.h"
#include "se_space_inner.h"
#include "pagemgr_common.h"
#include "db_mem_context.h"
#include "se_log.h"
#include "se_persist_inner.h"
#include "se_buffer_pool_priority_recycle.h"
#include "se_buffer_pool_bucket.h"

using namespace std;

const uint32_t UT_BUFFERPOOL_EXTEND_SIZE_K = 64;
const uint32_t UT_BUFFERPOOL_SIZE_K = 4096;
const uint32_t UT_BUFFERPOOL_PAGE_SIZE_K = 8;
const uint32_t UT_BUFFERPOOL_DEVICE_SIZE_K = SIZE_K(8);
const uint32_t UT_BUFFERPOOL_CAPACITY = (uint32_t)(UT_BUFFERPOOL_SIZE_K / UT_BUFFERPOOL_PAGE_SIZE_K);
const uint32_t UT_BUFFERPOOL_EXTEND_BUF_SIZE_2M = 2048;
const uint32_t UT_DEFAULT_BUFFERPOOL_PRIORITY_RATIO = 40;

static bool g_enableMutex = false;
static pthread_mutex_t g_spaceMutex;

typedef struct TagBufPoolThreadCtx {
    pthread_mutex_t mutexLock;
    uint32_t opCount;
    BufpoolMgrT *pageMgr;
    std::vector<PageIdT> pageArr;
    StatusInter res;
    uint32_t successCount;
} BufPoolThreadCtx;

extern "C" {
StatusInter SpaceLoadPageFromDisk(SeInstanceT *seIns, PageIdT pageId, void *page);
void SeInitSingleProcess(SeInstanceT *seIns);
}

static void *DbDynMemCtxAllocMock(void *ctx, size_t size)
{
    return (void *)malloc(size);
}

static void DbDynMemCtxFreeMock(void *ctx, void *ptr)
{
    free(ptr);
}

static void CheckLRUListRefCount(MemUtilsT *memCtx, BufDescListT *bufLruList, uint32_t maxLruLen, uint32_t maxPageId)
{
    EXPECT_LE(bufLruList->count, maxLruLen);
    BufDescT *bufDesc = BUF_DESC_LIST_TAIL_ENTRY(bufLruList);
    uint32_t i = 0;
    while (bufDesc != NULL) {
        ASSERT_LE(bufDesc->pageId.blockId, maxPageId);
        EXPECT_EQ(bufDesc->refCount, 0u);
        bufDesc = BUF_DESC_PREV_ENTRY(bufDesc);
        i++;
    }
    ASSERT_LE(i, maxLruLen);
}

static string GetUtPageContent(PageIdT *pageId)
{
    string retStr = "UT_PAGE_CONTENT_0123456789";
    retStr += "DeviceId:";
    retStr += to_string(pageId->deviceId);
    retStr += "BlockId:";
    retStr += to_string(pageId->blockId);
    return retStr;
}

static void GetAndLeavePage(
    BufpoolMgrT *pageMgr, PageIdT pageId, PageOptionE option, bool needLeavePage = true, void *arg = NULL)
{
    PageHeadT *page = NULL;
    if (arg == NULL) {
        ASSERT_EQ(BufpoolGetPage(pageMgr, pageId, (uint8_t **)&page, option, false), STATUS_OK_INTER);
    } else {
        ASSERT_EQ(BufpoolGetPageWithArg(pageMgr, pageId, option, arg, (uint8_t **)&page), STATUS_OK_INTER);
    }
    ASSERT_EQ(((PageHeadT *)page)->addr.deviceId, pageId.deviceId);
    ASSERT_EQ(((PageHeadT *)page)->addr.blockId, pageId.blockId);
    if (needLeavePage) {
        BufpoolLeavePage(pageMgr, pageId, false);
    }
}

static inline bool RedoReplayRegisteredMock(RedoMgrT *redoMgr, RedoLogTypeE type)
{
    EXPECT_TRUE(redoMgr == NULL);
    return true;
}

static StatusInter RedoLogReplayRegisterMock(RedoMgrT *redoMgr, RedoLogTypeE type, RedoLogReplayFuncT replayFunc)
{
    EXPECT_TRUE(redoMgr == NULL);
    return STATUS_OK_INTER;
}
static StatusInter SpaceLoadPageFromDiskMock(SeInstanceT *seIns, PageIdT pageId, void *page)
{
    if (g_enableMutex) {
        pthread_mutex_lock(&g_spaceMutex);
    }
    ((PageHeadT *)page)->addr.deviceId = pageId.deviceId;
    ((PageHeadT *)page)->addr.blockId = pageId.blockId;
    uint32_t pageContentSize = seIns->seConfig.pageSize * DB_KIBI - sizeof(PageHeadT);
    string contentStr = GetUtPageContent(&pageId);
    errno_t ret =
        memcpy_s((uint8_t *)page + sizeof(PageHeadT), pageContentSize, contentStr.c_str(), contentStr.size() + 1);
    EXPECT_EQ(ret, EOK);
    if (g_enableMutex) {
        pthread_mutex_unlock(&g_spaceMutex);
    }
    return STATUS_OK_INTER;
}

static StatusInter RedoLogWriteMock(
    RedoRunCtxT *redoCtx, RedoLogTypeE type, const PageIdT *addr, const uint8_t *data, uint32_t size)
{
    return STATUS_OK_INTER;
}

static Status CkptTriggerMock(SeInstanceT *seInsPtr, CkptModeT mode, bool wait)
{
    return STATUS_OK_INTER;
}

static void UtStubFunc()
{
    uint32_t stubIndex = setStubC((void *)RedoReplayRegistered, (void *)RedoReplayRegisteredMock);
    ASSERT_TRUE(stubIndex > 0);
    stubIndex = setStubC((void *)RedoLogReplayRegister, (void *)RedoLogReplayRegisterMock);
    ASSERT_TRUE(stubIndex > 0);
    stubIndex = setStubC((void *)SpaceLoadPageFromDisk, (void *)SpaceLoadPageFromDiskMock);
    ASSERT_TRUE(stubIndex > 0);
    stubIndex = setStubC((void *)RedoLogWrite, (void *)RedoLogWriteMock);
    ASSERT_TRUE(stubIndex > 0);
    stubIndex = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocMock);
    ASSERT_TRUE(stubIndex > 0);
    stubIndex = setStubC((void *)DbDynMemCtxFree, (void *)DbDynMemCtxFreeMock);
    ASSERT_TRUE(stubIndex > 0);
    stubIndex = setStubC((void *)CkptTrigger, (void *)CkptTriggerMock);
    ASSERT_TRUE(stubIndex > 0);
}

static void CheckBufDescOnLruList(BufpoolMgrT *bufpoolMgr, int listId, BufDescT *expectBufDescs, uint32_t expectNum)
{
    BufDescListT *bufLruList = &bufpoolMgr->bufPool->list[listId];
    EXPECT_EQ(bufLruList->count, expectNum);
    BufDescT *bufDesc = BUF_DESC_LIST_TAIL_ENTRY(bufLruList);
    uint32_t i = 0;
    while (bufDesc != NULL) {
        ASSERT_LT(i, expectNum);
        string contentStr = GetUtPageContent(&expectBufDescs[i].pageId);
        ASSERT_STREQ((const char *)bufDesc->page + sizeof(PageHeadT), contentStr.c_str()) << ", i:" << i;
        ASSERT_EQ(((PageHeadT *)bufDesc->page)->addr.deviceId, expectBufDescs[i].pageId.deviceId);
        ASSERT_EQ(((PageHeadT *)bufDesc->page)->addr.blockId, expectBufDescs[i].pageId.blockId);
        EXPECT_EQ(bufDesc->priority, expectBufDescs[i].priority) << ", pageId:" << expectBufDescs[i].pageId.blockId;
        ASSERT_EQ(bufDesc->refCount, expectBufDescs[i].refCount) << ", pageId:" << expectBufDescs[i].pageId.blockId;
        ASSERT_EQ(bufDesc->descId, expectBufDescs[i].descId) << ", pageId:" << expectBufDescs[i].pageId.blockId;
        ASSERT_EQ(bufDesc->isDirty, expectBufDescs[i].isDirty) << ", pageId:" << expectBufDescs[i].pageId.blockId;
        ASSERT_EQ(bufDesc->isPinned, expectBufDescs[i].isPinned) << ", pageId:" << expectBufDescs[i].pageId.blockId;
        ASSERT_EQ(bufDesc->listId, expectBufDescs[i].listId) << ", pageId:" << expectBufDescs[i].pageId.blockId;
        bufDesc = BUF_DESC_PREV_ENTRY(bufDesc);
        i++;
    }
    ASSERT_EQ(i, expectNum);
}

class UtBufPoolPolicyFunc : public testing::Test {
public:
    static SeInstanceT g_seIns;

protected:
    static void InitSeInstance(SeInstanceT *seInsPtr)
    {
        seInsPtr->bufpoolMgr = NULL;
        seInsPtr->seConfig.persCompMode = 1;
        seInsPtr->seConfig.deviceSize = UT_BUFFERPOOL_DEVICE_SIZE_K;
        seInsPtr->seConfig.pageSize = UT_BUFFERPOOL_PAGE_SIZE_K;
        seInsPtr->seConfig.bufferPoolSize = UT_BUFFERPOOL_SIZE_K;
        seInsPtr->seConfig.bufferPoolNum = 1;
        seInsPtr->seConfig.bufferPoolPolicy = BUF_RECYCLE_INDEX;
        seInsPtr->seConfig.bufferPoolPriorityRatio = UT_DEFAULT_BUFFERPOOL_PRIORITY_RATIO;
    }

    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    virtual void SetUp()
    {
        init();
        system("rm -rf /data/gmdb");
        system("mkdir -p " TEST_GMDB_DATA_DIR);
        SeSetPersistMode(PERSIST_INCREMENT);
        DbMemCtxArgsT args = {0};
        DbInitTopDynMemCtx(NULL);
        g_seIns.seServerMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "SeTopMemCtx", &args);
        ASSERT_TRUE(g_seIns.seServerMemCtx != NULL);
        InitSeInstance(&g_seIns);
        SeInitSingleProcess(&g_seIns);
        // 打桩，否则因为SeGetStorageStatus接口访问到空指针
        int stubIdx = setStubC((void *)SeGetStorageStatus, (void *)SeGetStorageStatusStub);
        EXPECT_GE(stubIdx, 0);
        stubIdx = setStubC((void *)DbCommonIsServer, (void *)DbCommonIsServerMock);
        EXPECT_GE(stubIdx, 0);
    }

    virtual void TearDown()
    {
        clearAllStub();
    }
};
SeInstanceT UtBufPoolPolicyFunc::g_seIns = {0};

static void UtInitBufDesc(BufDescT *bufDesc, PageIdT pageId, uint32_t refCount, uint32_t descId, uint32_t priority)
{
    bufDesc->pageId = pageId;
    bufDesc->refCount = refCount;
    bufDesc->descId = descId;
    bufDesc->priority = priority;
    if (priority == NORMAL_PAGE) {
        bufDesc->listId = LRU_LIST_NORMAL;
    } else {
        bufDesc->listId = LRU_LIST_PRIORITY;
    }
}

static DbMemCtxT g_Ctx = {0};

/**
 * @tc.name: BufferPoolPolicyFunc001
 * @tc.desc: test init buffer pool with specified recycle type
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc001)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX, check result
     * @tc.expected: step1. Return STATUS_OK_INTER, seInsPtr->bufpoolMgr is not null
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_TRUE(seIns->bufpoolMgr != NULL);
    EXPECT_EQ(seIns->bufpoolMgr->pageSize, UT_BUFFERPOOL_PAGE_SIZE_K * DB_KIBI);
    EXPECT_EQ(SeGetBufpoolCurrentSize(seIns), UT_BUFFERPOOL_SIZE_K * DB_KIBI);
    EXPECT_TRUE(
        BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity == UT_BUFFERPOOL_SIZE_K / UT_BUFFERPOOL_PAGE_SIZE_K);
    BucketMgrT *bucketMgr = BufpoolGetBucketMgr(seIns, seIns->bufpoolMgr->bufPool->currBucketMgrShm);
    EXPECT_EQ(bucketMgr->bucketNum, (uint32_t)UT_BUFFERPOOL_SIZE_K / UT_BUFFERPOOL_PAGE_SIZE_K * BUCKET_TIMES);
    EXPECT_EQ(seIns->seConfig.bufferPoolPolicy, BUF_RECYCLE_INDEX);
    uint32_t expectedPriorityMinSize =
        (uint32_t)floor(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity * PRIORITY_PAGE_RATIO_MIN);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->priorityListMinimum, expectedPriorityMinSize);
    /**
     * @tc.steps: step2. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc002
 * @tc.desc: test to get page with all normal page while policy is BUF_RECYCLE_INDEX
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc002)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    DbSetServerThreadFlag();
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId , page option:ENTER_PAGE_NORMAL
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = NORMAL_PAGE;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, NORMAL_PAGE);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, (uint32_t)UT_BUFFERPOOL_CAPACITY);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescs, UT_BUFFERPOOL_CAPACITY);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc003
 * @tc.desc: test to get page with all priority page while policy is BUF_RECYCLE_INDEX
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc003)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId , page option:ENTER_PAGE_NORMAL
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = i + 1;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc004
 * @tc.desc: make normal list full and insert a priority page
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc004)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId , page option:ENTER_PAGE_NORMAL
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = NORMAL_PAGE;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, NORMAL_PAGE);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_BUFFERPOOL_CAPACITY);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. Get new priority Page:33, eliminate page: refCount is ZERO, NOT dirty, not PIN
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    PageIdT newPageId = {0, UT_BUFFERPOOL_CAPACITY + 1};
    PriorityRecyArgT arg = {.flag = SE_RECYCLE_PRIORITY_MULTI, .priority = LOWEST_PRIORITY_PAGE};
    BufDescT newBufDesc = {0};
    UtInitBufDesc(&newBufDesc, newPageId, 0, 0, LOWEST_PRIORITY_PAGE);
    GetAndLeavePage(seIns->bufpoolMgr, newPageId, ENTER_PAGE_NORMAL, true, &arg);
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_BUFFERPOOL_CAPACITY - 1);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY].count, 1u);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescs + 1, UT_BUFFERPOOL_CAPACITY - 1);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, &newBufDesc, 1);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc005
 * @tc.desc: make priority list full and insert a normal page
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc005)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId , page option:ENTER_PAGE_NORMAL
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = LOWEST_PRIORITY_PAGE;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, LOWEST_PRIORITY_PAGE);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. Get new normal Page:33, eliminate page: refCount is ZERO, NOT dirty, not PIN
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    PageIdT newPageId = {0, UT_BUFFERPOOL_CAPACITY + 1};
    PriorityRecyArgT arg = {.flag = 0, .priority = NORMAL_PAGE};
    BufDescT newBufDesc = {0};
    UtInitBufDesc(&newBufDesc, newPageId, 0, 0, NORMAL_PAGE);
    GetAndLeavePage(seIns->bufpoolMgr, newPageId, ENTER_PAGE_NORMAL, true, &arg);
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs + 1, UT_BUFFERPOOL_CAPACITY - 1);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, &newBufDesc, 1);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc006
 * @tc.desc: make normal list full and insert priority pages until normal list empty
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc006)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId , page option:ENTER_PAGE_NORMAL
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = NORMAL_PAGE;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, NORMAL_PAGE);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_BUFFERPOOL_CAPACITY);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY].count, 0u);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. Get new priority Pages until normal list empty
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, UT_BUFFERPOOL_CAPACITY + i + 1, pages[i]);
        args[i].priority = i + 1;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, i + 1);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 0u);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY].count, UT_BUFFERPOOL_CAPACITY);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc007
 * @tc.desc: make priority list full and insert normal pages until priority list to minimum length
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc007)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId , page option:ENTER_PAGE_NORMAL
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = i + 1;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, i + 1);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 0u);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY].count, UT_BUFFERPOOL_CAPACITY);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. Get normal pages until priority list to minimum length
     * @tc.expected: step3. Return STATUS_OK_INTER
     */

    uint32_t expectedPriorityMinSize =
        (uint32_t)floor(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity * PRIORITY_PAGE_RATIO_MIN);
    uint32_t expectedNormalSize = BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity - expectedPriorityMinSize;

    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, UT_BUFFERPOOL_CAPACITY + i + 1, pages[i]);
        args[i].priority = NORMAL_PAGE;
        args[i].flag = 0;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i % (expectedNormalSize), NORMAL_PAGE);
    }
    /**
     * pages in normal list:
     * descId  :  1 ,2, 3,4 ... ,27,28,0,1,2
     * blockId :  36,37,38,..., 62,63,64
     */
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);

    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, expectedNormalSize);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY].count, expectedPriorityMinSize);

    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescs + expectedPriorityMinSize, expectedNormalSize);
    BufDescT bufDescsPriority[expectedPriorityMinSize] = {0};
    for (uint32_t i = 0; i < expectedPriorityMinSize; ++i) {
        uint32_t blockId = UT_BUFFERPOOL_CAPACITY - expectedPriorityMinSize + i + 1;
        uint32_t priority = (uint32_t)blockId;
        PageIdT page = {0, blockId};
        UtInitBufDesc(&bufDescsPriority[i], page, 0, blockId - 1, priority);
    }
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority, expectedPriorityMinSize);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc008
 * @tc.desc: make priority list full and half pages' priorities are 2,others are 3
 *           then recycle pages until all priority 1 pages recycled
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc008)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. make priority list full and half pages' priorities are 2,others are 3
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = (i & 1) + 2;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 0u);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. recycle pages until all priority 2 pages recycled
     * @tc.expected: step3. Return STATUS_OK_INTER
     */

    uint32_t listLen = UT_BUFFERPOOL_CAPACITY / 2;
    BufDescT bufDescsPriority[listLen] = {0};
    BufDescT bufDescsNormal[listLen] = {0};
    for (uint32_t i = 0; i < listLen; i++) {
        UtPageMgrConvertValidPageId(seIns, UT_BUFFERPOOL_CAPACITY + i + 1, pages[i]);
        args[i].priority = NORMAL_PAGE;
        args[i].flag = 0;
        UtInitBufDesc(&bufDescsPriority[i], {0, 2 * (i + 1)}, 0, 1 + 2 * i, 3);
        UtInitBufDesc(&bufDescsNormal[i], pages[i], 0, 2 * i, args[i].priority);
    }

    for (uint32_t i = 0; i < listLen; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);

    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescsNormal, listLen);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority, listLen);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc009
 * @tc.desc: make priority list full and half pages' priorities are 1,others are 2
 *           then recycle pages until all priority 1 pages recycled
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc009)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. make priority list full and half pages' priorities are 2,others are 3
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = (i & 1) + 1;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 0u);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. recycle pages until all priority 2 pages recycled
     * @tc.expected: step3. Return STATUS_OK_INTER
     */

    uint32_t listLen = UT_BUFFERPOOL_CAPACITY / 2;
    BufDescT bufDescsPriority[listLen] = {0};
    BufDescT bufDescsNormal[listLen] = {0};
    for (uint32_t i = 0; i < listLen; i++) {
        UtPageMgrConvertValidPageId(seIns, UT_BUFFERPOOL_CAPACITY + i + 1, pages[i]);
        args[i].priority = NORMAL_PAGE;
        args[i].flag = 0;
        UtInitBufDesc(&bufDescsPriority[i], {0, 2 * (i + 1)}, 0, 1 + 2 * i, 2);
        UtInitBufDesc(&bufDescsNormal[i], pages[i], 0, 2 * i, args[i].priority);
    }

    for (uint32_t i = 0; i < listLen; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);

    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescsNormal, listLen);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority, listLen);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

static void MarkBufDescDirtyAndPin(MemUtilsT *memCtx, BufDescListT *list)
{
    BufDescT *bufDesc = BUF_DESC_LIST_TAIL_ENTRY(list);
    uint32_t i = 0;
    while (bufDesc != NULL) {
        if (i % 3 > 0 && i < UT_BUFFERPOOL_CAPACITY - 2) {
            bufDesc->isDirty = true;
        }
        if (i == UT_BUFFERPOOL_CAPACITY - 2) {
            bufDesc->isPinned = true;
        }
        i++;
        bufDesc = BUF_DESC_PREV_ENTRY(bufDesc);
    }
}

/**
 * @tc.name: BufferPoolPolicyFunc010
 * @tc.desc:  test to skip pages which pageCount > 1 but refCount NOT ZERO, DIRTY or PINNED, while get page
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc010)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId, make page:32's refCount ZERO, NOT dirty, NOT pinned,
     *      other page's tableId should at least exist one page in the same, which unsatisfied with elimination.
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = LOWEST_PRIORITY_PAGE;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
        if (i % 3 > 0 && i < UT_BUFFERPOOL_CAPACITY - 2) {
            bufDescs[i].isDirty = true;
        }
        if (i % 3 != 1 && i < UT_BUFFERPOOL_CAPACITY - 2) {
            bufDescs[i].refCount = 1;
        }
        if (i == UT_BUFFERPOOL_CAPACITY - 2) {
            bufDescs[i].isPinned = true;
        }
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {  // Set page:31 as PIN
        bool needLeavePage = (i % 3) == 1 || i >= UT_BUFFERPOOL_CAPACITY - 2;
        GetAndLeavePage(seIns->bufpoolMgr, pages[i],
            ((i == (UT_BUFFERPOOL_CAPACITY - 2)) ? ENTER_PAGE_PINNED : ENTER_PAGE_NORMAL), needLeavePage, &args[i]);
    }
    MarkBufDescDirtyAndPin(&seIns->memUtils, &(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY]));
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. Get new Page:33, eliminate page:32, which refCount is ZERO, NOT dirty, not PIN
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    PageIdT newPageId = {0, UT_BUFFERPOOL_CAPACITY + 1};
    PriorityRecyArgT arg = {.flag = SE_RECYCLE_PRIORITY_MULTI, .priority = LOWEST_PRIORITY_PAGE};  // New page

    GetAndLeavePage(seIns->bufpoolMgr, newPageId, ENTER_PAGE_NORMAL, true, &arg);
    // LRU order should be 33, 31, 30...2, 1
    bufDescs[UT_BUFFERPOOL_CAPACITY - 1].pageId = newPageId;
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 0u);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc011
 * @tc.desc:  while priority list length > minLen, recycle LRU page(check in normal and priority list)
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc011)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. make priority list full and half pages' priorities are 2,others are 3
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    uint32_t listLen = UT_BUFFERPOOL_CAPACITY / 2;
    BufDescT bufDescsPriority[listLen + 1] = {0};
    BufDescT bufDescsNormal[listLen + 1] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        if (i & 1) {
            args[i].priority = LOWEST_PRIORITY_PAGE;
            args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
            UtInitBufDesc(&bufDescsPriority[i / 2], pages[i], 0, i, args[i].priority);
        } else {
            args[i].priority = NORMAL_PAGE;
            args[i].flag = DB_RECYCLE_TABLE_ADD;
            UtInitBufDesc(&bufDescsNormal[i / 2], pages[i], 0, i, args[i].priority);
        }
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescsNormal, listLen);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority, listLen);

    /**
     * @tc.steps: step3. Get new normal Page:33, eliminate page in normal list
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    PageIdT newPageId = {0, UT_BUFFERPOOL_CAPACITY + 1};
    PriorityRecyArgT arg = {.flag = 0, .priority = NORMAL_PAGE};
    UtInitBufDesc(&bufDescsNormal[listLen], newPageId, 0, 0, NORMAL_PAGE);

    GetAndLeavePage(seIns->bufpoolMgr, newPageId, ENTER_PAGE_NORMAL, true, &arg);
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescsNormal + 1, listLen);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority, listLen);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

/**
 * @tc.name: BufferPoolPolicyFunc012
 * @tc.desc:  while priority list length > minLen, recycle LRU page(check in normal and priority list)
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc012)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. make priority list full and half pages' priorities are 2,others are 3
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    uint32_t listLen = UT_BUFFERPOOL_CAPACITY / 2;
    BufDescT bufDescsPriority[listLen + 1] = {0};
    BufDescT bufDescsNormal[listLen + 1] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        if (i & 1) {
            args[i].priority = NORMAL_PAGE;
            args[i].flag = DB_RECYCLE_TABLE_ADD;
            UtInitBufDesc(&bufDescsNormal[i / 2], pages[i], 0, i, args[i].priority);
        } else {
            args[i].priority = LOWEST_PRIORITY_PAGE;
            args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
            UtInitBufDesc(&bufDescsPriority[i / 2], pages[i], 0, i, args[i].priority);
        }
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescsNormal, listLen);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority, listLen);

    /**
     * @tc.steps: step3. Get new normal Page:33, eliminate page in priority list
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    PageIdT newPageId = {0, UT_BUFFERPOOL_CAPACITY + 1};
    PriorityRecyArgT arg = {.flag = SE_RECYCLE_PRIORITY_MULTI, .priority = LOWEST_PRIORITY_PAGE};
    UtInitBufDesc(&bufDescsPriority[listLen], newPageId, 0, 0, LOWEST_PRIORITY_PAGE);

    GetAndLeavePage(seIns->bufpoolMgr, newPageId, ENTER_PAGE_NORMAL, true, &arg);
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_NORMAL, bufDescsNormal, listLen);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescsPriority + 1, listLen);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

static void MarkBufDescDirtyAndPin2(MemUtilsT *memCtx, BufDescListT *list)
{
    BufDescT *bufDesc = BUF_DESC_LIST_TAIL_ENTRY(list);
    bufDesc->isPinned = true;
    bufDesc = BUF_DESC_PREV_ENTRY(bufDesc);
    bufDesc->isDirty = true;
}

/**
 * @tc.name: BufferPoolPolicyFunc013
 * @tc.desc:  check the priority of recycling rules :able to recycle > priority > LRU
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc013)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_INDEX
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Get Page with different PageId, make page:32's refCount ZERO, NOT dirty, NOT pinned,
     *      other page's tableId should at least exist one page in the same, which unsatisfied with elimination.
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    PageIdT pages[UT_BUFFERPOOL_CAPACITY] = {0};
    PriorityRecyArgT args[UT_BUFFERPOOL_CAPACITY] = {0};
    BufDescT bufDescs[UT_BUFFERPOOL_CAPACITY + 3] = {0};
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {
        UtPageMgrConvertValidPageId(seIns, i + 1, pages[i]);
        args[i].priority = UT_BUFFERPOOL_CAPACITY - i;
        args[i].flag = SE_RECYCLE_PRIORITY_MULTI;
        UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
        if (i == 0) {
            bufDescs[i].isPinned = true;
            args[i].priority = LOWEST_PRIORITY_PAGE;
            UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
        } else if (i == 1) {
            bufDescs[i].isDirty = true;
            args[i].priority = LOWEST_PRIORITY_PAGE;
            UtInitBufDesc(&bufDescs[i], pages[i], 0, i, args[i].priority);
        } else if (i == 2) {
            bufDescs[i].refCount = 1;
            args[i].priority = LOWEST_PRIORITY_PAGE;
            UtInitBufDesc(&bufDescs[i], pages[i], 1, i, args[i].priority);
        }
    }
    for (uint32_t i = 0; i < UT_BUFFERPOOL_CAPACITY; i++) {  // Set page:31 as PIN
        if (i == 0) {
            GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_PINNED, true, &args[i]);
        } else if (i == 2) {
            GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, false, &args[i]);
        } else {
            GetAndLeavePage(seIns->bufpoolMgr, pages[i], ENTER_PAGE_NORMAL, true, &args[i]);
        }
    }
    MarkBufDescDirtyAndPin2(&seIns->memUtils, &(seIns->bufpoolMgr->bufPool->list[LRU_LIST_PRIORITY]));
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step3. Get new Page:33, eliminate page:32, which refCount is ZERO, NOT dirty, not PIN
     * @tc.expected: step3. Return STATUS_OK_INTER
     */
    PageIdT newPageId = {0, UT_BUFFERPOOL_CAPACITY + 1};
    PriorityRecyArgT arg = {.flag = SE_RECYCLE_PRIORITY_MULTI, .priority = LOWEST_PRIORITY_PAGE};  // New page

    GetAndLeavePage(seIns->bufpoolMgr, newPageId, ENTER_PAGE_NORMAL, true, &arg);
    // LRU order should be 33,3,2,1,31,29,28,...,6,5,4
    bufDescs[UT_BUFFERPOOL_CAPACITY - 1] = bufDescs[0];
    bufDescs[UT_BUFFERPOOL_CAPACITY] = bufDescs[1];
    bufDescs[UT_BUFFERPOOL_CAPACITY + 1] = bufDescs[2];
    UtInitBufDesc(&bufDescs[UT_BUFFERPOOL_CAPACITY + 2], newPageId, 0, UT_BUFFERPOOL_CAPACITY - 1, arg.priority);

    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    EXPECT_EQ(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 0u);
    CheckBufDescOnLruList(seIns->bufpoolMgr, LRU_LIST_PRIORITY, bufDescs + 3, UT_BUFFERPOOL_CAPACITY);

    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    BufpoolDestroy(seIns);
}

static void *ThreadGetPage(void *arg)
{
    BufPoolThreadCtx *ctx = (BufPoolThreadCtx *)arg;
    PriorityRecyArgT recyArg = {.flag = SE_RECYCLE_PRIORITY_MULTI, .priority = 1};
    const uint32_t pageRangeMax = 1000;
    srand((int)time(NULL));
    for (uint32_t i = 0; i < ctx->opCount; i++) {
        StatusInter ret = STATUS_OK_INTER;
        uint32_t blockId = (rand() % pageRangeMax) + 1;  // get pageId by random:[1, 1000]
        PageIdT pageId = {0, blockId};
        uint8_t *page = NULL;
        if (i == 0) {
            ret = BufpoolGetPage(ctx->pageMgr, pageId, &page, ENTER_PAGE_NORMAL, false);
        } else if (i % 3 == 0) {
            recyArg.priority = 2;
            ret = BufpoolGetPageWithArg(ctx->pageMgr, pageId, ENTER_PAGE_NORMAL, &recyArg, &page);
        } else {
            recyArg.priority = 1;
            ret = BufpoolGetPageWithArg(ctx->pageMgr, pageId, ENTER_PAGE_NORMAL, &recyArg, &page);
        }
        EXPECT_EQ(ret, STATUS_OK_INTER);
        if (ret == STATUS_OK_INTER) {
            ctx->successCount++;
        }
        BufpoolLeavePage(ctx->pageMgr, pageId, false);
    }
    return NULL;
}

/**
 * @tc.name: BufferPoolPolicyFunc014
 * @tc.desc: test to concurrent get page with 32 threads while index first
 */
TEST_F(UtBufPoolPolicyFunc, BufferPoolPolicyFunc014)
{
    /**
     * @tc.steps: step1. Init buffer pool with recycle type: BUF_RECYCLE_TABLE
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    UtStubFunc();
    SeInstanceT *seIns = &UtBufPoolPolicyFunc::g_seIns;
    StatusInter ret = BufpoolInit(seIns, &g_Ctx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolCreatePageMgr(seIns, &g_Ctx, (PageMgrT **)&seIns->pageMgr, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    /**
     * @tc.steps: step2. Create 32 threads used to get page, 10000 times each thread
     * @tc.expected: step2. Execute successfully
     */
    const uint32_t threadNum = 4;
    pthread_t threads[threadNum] = {0};
    BufPoolThreadCtx threadCtxs[threadNum] = {0};
    for (uint32_t i = 0; i < threadNum; i++) {
        threadCtxs[i].pageMgr = BufpoolGetPageMgr(seIns);
        threadCtxs[i].opCount = 100000;
        ASSERT_EQ(pthread_create(&threads[i], NULL, ThreadGetPage, &threadCtxs[i]), GMERR_OK);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        EXPECT_EQ(threadCtxs[i].opCount, threadCtxs[i].successCount);
    }

    /**
     * @tc.steps: step3. Check LRU list in buffer pool, length:32, pageId's range:[1, 1000], refCount == 0
     * @tc.expected: step3. Execute successfully
     */
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity, UT_BUFFERPOOL_CAPACITY);
    EXPECT_LE(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, UT_BUFFERPOOL_CAPACITY);
    EXPECT_LE(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_BUFFERPOOL_CAPACITY);
    CheckLRUListRefCount(
        &seIns->memUtils, &(seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL]), UT_BUFFERPOOL_CAPACITY, 1000);
    /**
     * @tc.steps: step4. Destroy buffer pool
     */
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
    BufpoolDestroy(seIns);
}
