/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test designed for durablememdata.
 * Author: zhangzhaonan
 * Create: 2024-2-22
 */
#include "gtest/gtest.h"
#include "stub.h"
#include "db_mem_context.h"
#include "se_page_mgr.h"
#include "se_define.h"
#include "se_durable_memdata.h"
#include "pagemgr_common.h"

using namespace std;
#define CMD_MAX_LEN 1024
#define SE_CONFIG_INI_PATH_TEST "persistence_config_test.ini"
#define MODIFY_CONFIG_INI_PATH_TEST "persistence_config_test_temp.ini"

extern "C" {
StatusInter BufDescAllocDescDoubleStack(BufDescMgrT *descMgr, uint32_t *descId);
}

StatusInter BufDescAllocDescDoubleStackStub(BufDescMgrT *descMgr, uint32_t *descId)
{
    BufDescT *cursor = NULL;
    uint32_t segmentIndex = descMgr->hwm / descMgr->segSize;
    if (descMgr->hwm % descMgr->segSize == 0 && descMgr->hwm < descMgr->segSize) {
        uint64_t segmentSize = sizeof(BufDescT) * descMgr->segSize;
        if (descMgr->shmem) {
            ShmemPtrT *segmentPtr = (ShmemPtrT *)descMgr->arr;
            segmentPtr[segmentIndex] = DbShmemCtxAlloc(descMgr->memCtx, segmentSize);
            if (!DbIsShmPtrValid(segmentPtr[segmentIndex])) {
                SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc double stack shmem bufdesc %" PRIu32 ", size %" PRIu64,
                    segmentIndex, segmentSize);
                return OUT_OF_MEMORY_INTER;
            }
        } else {
            BufDescT **segmentPtr = (BufDescT **)descMgr->arr;
            segmentPtr[segmentIndex] = (BufDescT *)DbDynMemCtxAlloc(descMgr->memCtx, segmentSize);
            if (segmentPtr[segmentIndex] == NULL) {
                SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc double stack dynmem bufdesc %" PRIu32 ", size %" PRIu64,
                    segmentIndex, segmentSize);
                return OUT_OF_MEMORY_INTER;
            }
        }
        *descId = descMgr->hwm++;
        cursor = BufDescGet(descMgr, *descId);
        (void)memset_s(cursor, sizeof(BufDescT), 0, sizeof(BufDescT));
        DbSpinInit(&cursor->lock);
        cursor->descId = *descId;
    } else {
        *descId = descMgr->hwm++;
    }
    return STATUS_OK_INTER;
}

class UtDurableMemdataConfig : public PageMgrUtTest {
public:
protected:
    virtual void SetUp()
    {
        init();
        DbSetServerThreadFlag();
        setStubC((void *)BufDescAllocDescDoubleStack, (void *)BufDescAllocDescDoubleStackStub);
    }

    virtual void TearDown()
    {
        clearAllStub();
    }
};

TEST_F(UtDurableMemdataConfig, durable_memdata_page_size_4)
{
    uint32_t pageSize = 4;
    system("rm -rf /data/gmdb");
    system("mkdir -p " TEST_GMDB_DATA_DIR);
    char cfgModifyCmd[CMD_MAX_LEN] = {0};
    int32_t ret = snprintf_s(cfgModifyCmd, CMD_MAX_LEN, CMD_MAX_LEN - 1,
        "\"persistentMode=1\"  \"pageSize=%u\" \"maxSeMem=1048552\" \"deviceSize=1024\" "
        "\"maxTotalShmSize=1048576\" "
        "\"maxTotalDynSize=1048576\" \"maxSysShmSize=12\" \"maxSysDynSize=1048000\" "
        "\"featureNames=MEMDATA,DURABLEMEMDATA,BUFFERPOOL,FASTPATH,TRM,PERSISTENCE\"",
        pageSize);
    ASSERT_GE(ret, 0) << "cmd max len:" << CMD_MAX_LEN;
    UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH_TEST, cfgModifyCmd);
    ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH_TEST);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // check durableMemdata Mgr init successfully.
    EXPECT_EQ(seIns->seConfig.pageSize, pageSize);
    DestroySeIns();
    CommonRelease();
}

TEST_F(UtDurableMemdataConfig, durable_memdata_page_size_8)
{
    uint32_t pageSize = 8;
    system("rm -rf /data/gmdb");
    system("mkdir -p " TEST_GMDB_DATA_DIR);
    char cfgModifyCmd[CMD_MAX_LEN] = {0};
    int32_t ret = snprintf_s(cfgModifyCmd, CMD_MAX_LEN, CMD_MAX_LEN - 1,
        "\"persistentMode=1\"  \"pageSize=%u\" \"maxSeMem=1048552\" \"deviceSize=1024\" "
        "\"maxTotalShmSize=1048576\" "
        "\"maxTotalDynSize=1048576\" \"maxSysShmSize=12\" \"maxSysDynSize=1048000\" "
        "\"featureNames=MEMDATA,DURABLEMEMDATA,BUFFERPOOL,FASTPATH,TRM,PERSISTENCE\"",
        pageSize);
    ASSERT_GE(ret, 0) << "cmd max len:" << CMD_MAX_LEN;
    UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH_TEST, cfgModifyCmd);
    ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH_TEST);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // check durableMemdata Mgr init successfully.
    EXPECT_EQ(seIns->seConfig.pageSize, pageSize);
    DestroySeIns();
    CommonRelease();
}

TEST_F(UtDurableMemdataConfig, durable_memdata_page_size_16)
{
    uint32_t pageSize = 16;
    system("rm -rf /data/gmdb");
    system("mkdir -p " TEST_GMDB_DATA_DIR);
    char cfgModifyCmd[CMD_MAX_LEN] = {0};
    int32_t ret = snprintf_s(cfgModifyCmd, CMD_MAX_LEN, CMD_MAX_LEN - 1,
        "\"persistentMode=1\"  \"pageSize=%u\" \"maxSeMem=1048552\" \"deviceSize=1024\" "
        "\"maxTotalShmSize=1048576\" "
        "\"maxTotalDynSize=1048576\" \"maxSysShmSize=12\" \"maxSysDynSize=1048000\" "
        "\"featureNames=MEMDATA,DURABLEMEMDATA,BUFFERPOOL,FASTPATH,TRM,PERSISTENCE\"",
        pageSize);
    ASSERT_GE(ret, 0) << "cmd max len:" << CMD_MAX_LEN;
    UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH_TEST, cfgModifyCmd);
    ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH_TEST);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // check durableMemdata Mgr init successfully.
    EXPECT_EQ(seIns->seConfig.pageSize, pageSize);
    DestroySeIns();
    CommonRelease();
}

TEST_F(UtDurableMemdataConfig, durable_memdata_page_size_32)
{
    uint32_t pageSize = 32;
    system("rm -rf /data/gmdb");
    system("mkdir -p " TEST_GMDB_DATA_DIR);
    char cfgModifyCmd[CMD_MAX_LEN] = {0};
    int32_t ret = snprintf_s(cfgModifyCmd, CMD_MAX_LEN, CMD_MAX_LEN - 1,
        "\"persistentMode=1\"  \"pageSize=%u\" \"maxSeMem=1048552\" \"deviceSize=1024\" "
        "\"maxTotalShmSize=1048576\" "
        "\"maxTotalDynSize=1048576\" \"maxSysShmSize=12\" \"maxSysDynSize=1048000\" "
        "\"featureNames=MEMDATA,DURABLEMEMDATA,BUFFERPOOL,FASTPATH,TRM,PERSISTENCE\"",
        pageSize);
    ASSERT_GE(ret, 0) << "cmd max len:" << CMD_MAX_LEN;
    UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH_TEST, cfgModifyCmd);
    ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH_TEST);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // check durableMemdata Mgr init successfully.
    EXPECT_EQ(seIns->seConfig.pageSize, pageSize);
    DestroySeIns();
    CommonRelease();
}

TEST_F(UtDurableMemdataConfig, durable_memdata_page_size_64)
{
    uint32_t pageSize = 32;
    system("rm -rf /data/gmdb");
    system("mkdir -p " TEST_GMDB_DATA_DIR);
    char cfgModifyCmd[CMD_MAX_LEN] = {0};
    int32_t ret = snprintf_s(cfgModifyCmd, CMD_MAX_LEN, CMD_MAX_LEN - 1,
        "\"persistentMode=1\"  \"pageSize=%u\" \"maxSeMem=1048552\" \"deviceSize=1024\" "
        "\"maxTotalShmSize=1048576\" "
        "\"maxTotalDynSize=1048576\" \"maxSysShmSize=12\" \"maxSysDynSize=1048000\" "
        "\"featureNames=MEMDATA,DURABLEMEMDATA,BUFFERPOOL,FASTPATH,TRM,PERSISTENCE\"",
        pageSize);
    ASSERT_GE(ret, 0) << "cmd max len:" << CMD_MAX_LEN;
    UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH_TEST, cfgModifyCmd);
    ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH_TEST);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // check durableMemdata Mgr init successfully.
    EXPECT_EQ(seIns->seConfig.pageSize, pageSize);
    DestroySeIns();
    CommonRelease();
}
