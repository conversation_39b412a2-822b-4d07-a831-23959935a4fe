/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-1-6
 */
#include "gtest/gtest.h"
#include "stub.h"
#include "db_mem_context.h"
#include "se_buffer_pool_file_map.h"
#include "pagemgr_common.h"
#include "se_page_mgr.h"
#include "se_replay_inner.h"
#include "se_space_inner.h"
#include "se_redo_inner.h"
#include "se_ckpt.h"
#include "se_define.h"
#include "clt_conn.h"
#include "db_rpc_conn_msg.h"
#include "clt_da_read.h"
#include "ut_bufferpool_base.h"

using namespace std;
static StatusInter g_redoReplayError = STATUS_OK_INTER;
#define UT_BUFFERPOOL_PAGE_SIZE_32K (32 * DB_KIBI)
#define UT_EXPECT_NUMBER_0 0
#define UT_EXPECT_NUMBER_1 1
#define TEST_DATAFILE_NAME "user_data"
#define UT_BUFFERPOOL_SIZE_K (1 * DB_KIBI)
#define UT_BUFFERPOOL_PAGE_SIZE_IN_CONFIG 32
#define UT_BUFFERPOOL_SIZE_4M (4096 * DB_KIBI)
#define UT_BUFFERPOOL_CAPACITY (UT_BUFFERPOOL_SIZE_4M / UT_BUFFERPOOL_PAGE_SIZE_32K)  // capacity 128 pages
#define UT_LRU_INIT_SIZE 4                                                            // 1 undo page + 3 spaceHead page.
#define UT_SPACE_CTRL_RESIDENT_PAGE 3
#define CMD_BUFF_MAX_LEN 1024
#define UT_BUFFERPOOL_GET_PAGE_PRE_THREAD 35

static bool g_enableMutex = false;
static bool g_isEnterRedoModify = false;
static pthread_mutex_t g_spaceMutex;
static PageIdT g_spaceAllocPageId = {0, 1};
static uint32_t g_stubCallCnt = 0;
static uint32_t g_stubExpectAffectTimes = 0;
static int g_stubNo = 0;

extern "C" {
Status CltDirectOpen(GmcConnT *conn, CliConnectResponseT *rsp, const GmcConnOptionsT *options);
Status DirectReadOpen(GmcConnT *conn, uint32_t sessionId, uint16_t trxSlot, DbMemCtxT *memCtx, DrRunCtxT **drRunCtx);
uint64_t BufpoolGetHeaderSize(uint64_t pageBufSize, uint32_t pageSize);
StatusInter BufpoolMemInit(SeInstanceT *seIns, BufpoolMgrT *mgr, bool forceInit);
StatusInter BufpoolMgrInit(SeInstanceT *seIns, BufpoolMgrT *mgr);
StatusInter CkptInitGroup(SeInstanceT *seInsPtr, uint32_t groupSize);
}

static inline void ClearTestUserData()
{
    int32_t ret = system("rm -f " TEST_DATAFILE_NAME "*");
    ASSERT_NE(ret, -1);
}

static void InitSeInstanceConfig(SeInstanceT *seInsPtr)
{
    seInsPtr->seConfig.pageSize = UT_BUFFERPOOL_PAGE_SIZE_IN_CONFIG;
    seInsPtr->seConfig.bufferPoolPolicy = BUF_RECYCLE_NORMAL;
    seInsPtr->seConfig.bufferPoolNum = 1;
}

static void GetAndLeavePage(
    BufpoolMgrT *pageMgr, PageIdT pageId, PageOptionE option, bool needLeavePage = true, void *arg = NULL)
{
    PageHeadT *page = NULL;
    if (arg == NULL) {
        ASSERT_EQ(BufpoolGetPage(pageMgr, pageId, (uint8_t **)&page, option, false), STATUS_OK_INTER);
    } else {
        ASSERT_EQ(BufpoolGetPageWithArg(pageMgr, pageId, option, arg, (uint8_t **)&page), STATUS_OK_INTER);
    }
    ASSERT_EQ(((PageHeadT *)page)->addr.deviceId, pageId.deviceId);
    ASSERT_EQ(((PageHeadT *)page)->addr.blockId, pageId.blockId);
    if (needLeavePage) {
        BufpoolLeavePage(pageMgr, pageId, false);
    }
}

static StatusInter RedoLogReplayRegisterMock(RedoMgrT *redoMgr)
{
    return g_redoReplayError;
}

class UtBufferpool : public PageMgrUtTest {
public:
protected:
    virtual void SetUp()
    {
        init();
        system("ipcrm -a");
        system("rm -rf /data/gmdb");
        system("mkdir -p " TEST_GMDB_DATA_DIR);
        DbSetServerThreadFlag();
        DbInitTopDynMemCtx(NULL);

        char cfgModifyCmd[CMD_BUFF_MAX_LEN] = {0};
        int32_t ret = snprintf_s(cfgModifyCmd, CMD_BUFF_MAX_LEN, CMD_BUFF_MAX_LEN - 1,
            "\"persistentMode=%d\" \"bufferPoolPolicy=%d\" \"maxConnNum=%d\"", 1, 0, 150);
        ASSERT_GE(ret, 0) << "cmd max len:" << CMD_BUFF_MAX_LEN;
        UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH, cfgModifyCmd);
        int stubIndex = setStubC((void *)RedoLogReplayRegister, (void *)RedoLogReplayRegisterMock);
        ASSERT_TRUE(stubIndex > 0);
        ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH);
        InitSeInstanceConfig(UtBufferpool::seIns);
        ASSERT_EQ(UT_EXPECT_NUMBER_0, UtBufferpool::seIns->seConfig.bufferPoolPolicy);
        ClearTestUserData();
    }

    virtual void TearDown()
    {
        DestroySeIns();
        CommonRelease();
        ClearTestUserData();
    }
};

/**
 * @tc.name: BufpoolInit
 * @tc.desc: test to bufferpool init and create page manager.
 */
TEST_F(UtBufferpool, BufpoolInit)
{
    /**
     * @tc.steps: step1. Init buffer pool
     * @tc.expected: step1. Return STATUS_OK_INTER
     */
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_TRUE(seInsPtr->bufpoolMgr != NULL);
    EXPECT_EQ(seInsPtr->bufpoolMgr->pageSize, UT_BUFFERPOOL_PAGE_SIZE_32K);
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->size, UT_BUFFERPOOL_SIZE_4M);
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->capacity, UT_BUFFERPOOL_SIZE_4M / UT_BUFFERPOOL_PAGE_SIZE_32K);

    /**
     * @tc.steps: step2. Get BufpoolMgrT, check parameters pass through RedoLogReplayRegisterMock
     * @tc.expected: step2. Return STATUS_OK_INTER
     */
    BufpoolMgrT *pageMgr = BufpoolGetPageMgr(seInsPtr);
    EXPECT_TRUE(pageMgr == seInsPtr->bufpoolMgr);
    /**
     * @tc.steps: step3. checkout the status of LRU List.
     * @tc.expected: step3. LRU size equals to the number of init pages.
     */
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE);
}

extern "C" void RehashFreeAllocatedBucket(BufpoolMgrT *bufPoolMgr, uint32_t allocBuffpoolOkCnt);

TEST_F(UtBufferpool, BufpoolRehashFreeBucket)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_TRUE(seInsPtr->bufpoolMgr != NULL);
    BufpoolMgrT *bufPoolMgr = seInsPtr->bufpoolMgr;
    BufPoolT *bufPool = bufPoolMgr->bufPool;
    uint64_t descSize = bufPool->capacity * sizeof(BufDescT);
    uint64_t reserveForMgrSize = (uint64_t)BufGetBucketMgrReserveSize();
    uint64_t bucketMgrSize = reserveForMgrSize + (uint64_t)(BUCKET_TIMES * bufPool->capacity) * sizeof(BufBucketT);
    uint64_t alignedSize = CACHELINE_SIZE - 1;
    BucketMgrT *bucketMgr =
        (BucketMgrT *)DbDynMemCtxAlloc(seInsPtr->seServerMemCtx, descSize + bucketMgrSize + alignedSize);
    DB_ASSERT(bucketMgr != NULL);
    BufBucketT *alignedPtr =
        (BufBucketT *)(uintptr_t)(((uintptr_t)bucketMgr + reserveForMgrSize + alignedSize) & ~(alignedSize));
    // bucket 的结构体大小是16， 更容易对齐
    bucketMgr->bucketNum = BUCKET_TIMES * bufPool->capacity;
    bucketMgr->bucketOffset = (uint32_t)((uintptr_t)alignedPtr - (uintptr_t)bucketMgr);
    bufPool->rehashBucketWrapper.backBucketMgrShm = DbAddrToDynShmemPtr(bucketMgr);
    RehashFreeAllocatedBucket(bufPoolMgr, 1);
}

TEST_F(UtBufferpool, BufpoolGetCapacityTest)
{
    // Test to get BufpoolGetHeaderSize interface.
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_TRUE(seInsPtr->bufpoolMgr != NULL);
    EXPECT_EQ(seInsPtr->seConfig.pageSize * DB_KIBI, seInsPtr->bufpoolMgr->pageSize);
    uint64_t pageSize = seInsPtr->bufpoolMgr->pageSize;
    // BufPoolT + bufDescs + buckets(bucketNum -- ) BufpoolGetHeaderSize
    uint32_t bufpoolHeaderSize = BufpoolGetHeaderSize(seInsPtr->seConfig.bufferPoolSize * DB_KIBI, pageSize);
    uint32_t capacity = seInsPtr->bufpoolMgr->bufPool->capacity;
    uint32_t reserveForMgrSize = BufGetBucketMgrReserveSize();
    uint64_t expectHeaderSize =
        sizeof(BufPoolT) + capacity * sizeof(BufDescT) + BUCKET_TIMES * capacity * sizeof(BufBucketT);
    expectHeaderSize = expectHeaderSize + BufTableStatGetBufSize(capacity) + sizeof(TableStatT);
    expectHeaderSize += reserveForMgrSize;
    EXPECT_EQ(expectHeaderSize, bufpoolHeaderSize);
}

TEST_F(UtBufferpool, BufpoolReleasePagesInMemory)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_TRUE(seInsPtr->bufpoolMgr != NULL);
    Status ret = BufpoolEmptyAllPagesInMem(seInsPtr);
    EXPECT_EQ(GMERR_OK, ret);
    InitSeInstanceConfig(seInsPtr);
    EXPECT_TRUE(seInsPtr->bufpoolMgr != NULL);
    EXPECT_EQ(seInsPtr->seConfig.pageSize * DB_KIBI, seInsPtr->bufpoolMgr->pageSize);
    EXPECT_EQ(seInsPtr->bufpoolMgr->pageSize, UT_BUFFERPOOL_PAGE_SIZE_32K);
    EXPECT_EQ(UT_BUFFERPOOL_SIZE_4M, seInsPtr->bufpoolMgr->bufPool->size);
    EXPECT_EQ(UT_BUFFERPOOL_SIZE_4M / UT_BUFFERPOOL_PAGE_SIZE_32K, seInsPtr->bufpoolMgr->bufPool->capacity);
    EXPECT_EQ(UT_EXPECT_NUMBER_0, (int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count);
    EXPECT_EQ(UT_EXPECT_NUMBER_0, (int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_STATS_SCAN].count);
    EXPECT_EQ(UT_EXPECT_NUMBER_0, seInsPtr->bufpoolMgr->bufPool->hwm);
}

/**
 * Aiming to create memory operation crash in allocation of bufDescs.
 */
void *DbDynMemCtxAllocByBufferpoolMock(void *ctx, size_t size)
{
    g_stubCallCnt++;
    if (g_stubCallCnt < g_stubExpectAffectTimes) {
        clearStub(g_stubNo);
        void *allocMemory = DbDynMemCtxAlloc(ctx, size);
        g_stubNo = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocByBufferpoolMock);
        return allocMemory;
    }
    return NULL;
}

TEST_F(UtBufferpool, BufpoolResetTest)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_TRUE(seInsPtr->bufpoolMgr != NULL);
    // Reset bufferpool
    g_stubCallCnt = 0;
    // Expect Bufferpool unable to alloc dynamic memory and free bufpool.
    g_stubExpectAffectTimes = 3;
    g_stubNo = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocByBufferpoolMock);
    StatusInter status = BufpoolMemInit(seInsPtr, seInsPtr->bufpoolMgr, false);
    EXPECT_EQ(OUT_OF_MEMORY_MEM_FAILED, status);
    EXPECT_EQ(NULL, seInsPtr->bufpoolMgr->bufPool);
    clearStub(g_stubNo);
    status = BufpoolMemInit(seInsPtr, seInsPtr->bufpoolMgr, false);
    EXPECT_EQ(STATUS_OK_INTER, status);
}

TEST_F(UtBufferpool, BufpoolGetPage000)
{
    SeInstanceT *seInsPtr = UtBufferpool::seIns;
    // alloc page
    PageIdT pageId = {2, 2};
    uint8_t *page = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageId);
    EXPECT_EQ((int32_t)pageId.deviceId, 0);
    EXPECT_EQ((int32_t)pageId.blockId, 1);
    EXPECT_EQ(STATUS_OK_INTER, ret);

    // pin the page.
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageId, &page, ENTER_PAGE_PINNED, false);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    BufDescLinkedListT *list = &seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL];
    BufDescT *bufDesc = BUF_DESC_LIST_HEAD_ENTRY(list);
    EXPECT_TRUE(bufDesc != NULL);
    EXPECT_EQ(bufDesc->pageId.deviceId, pageId.deviceId);
    EXPECT_EQ(bufDesc->pageId.blockId, pageId.blockId);
    EXPECT_TRUE(bufDesc->isPinned);
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageId, &page, ENTER_PAGE_NORMAL, false);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    EXPECT_TRUE(bufDesc->isPinned);
    EXPECT_EQ(2, bufDesc->refCount);
    // reset the refcount of page to 0.
    BufpoolLeavePage(seInsPtr->bufpoolMgr, pageId, false);
    BufpoolLeavePage(seInsPtr->bufpoolMgr, pageId, false);

    // free page and check status of the page, expect normal.
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    ret = BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    EXPECT_FALSE(bufDesc->isPinned);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

/**
 * @tc.name: BufpoolGetPage.
 * @tc.desc: test to get a page and insert to LRU list.
 */
TEST_F(UtBufferpool, BufpoolGetPage001)
{
    SeInstanceT *seInsPtr = UtBufferpool::seIns;
    /**
     * @tc.steps: step2. alloc a page.
     */
    PageIdT pageId = {2, 2};
    uint8_t *page = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageId);
    EXPECT_EQ((int32_t)pageId.deviceId, 0);
    EXPECT_EQ((int32_t)pageId.blockId, 1);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    /**
     * @tc.steps: step3. Get Page with specified PageId
     * page option:ENTER_PAGE_NORMAL, check LRU list
     * @tc.expected: Return STATUS_OK_INTER
     */
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageId, &page, ENTER_PAGE_NORMAL, false);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(((PageHeadT *)page)->addr.deviceId, pageId.deviceId);
    EXPECT_EQ(((PageHeadT *)page)->addr.blockId, pageId.blockId);

    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    BufDescLinkedListT *list = &seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL];
    BufDescT *bufDesc = BUF_DESC_LIST_HEAD_ENTRY(list);
    EXPECT_TRUE(bufDesc != NULL);
    EXPECT_EQ(bufDesc->pageId.deviceId, pageId.deviceId);
    EXPECT_EQ(bufDesc->pageId.blockId, pageId.blockId);
    /**
     * @tc.steps: step 4 check LRU counts of list.
     * @tc.expected: return lruList.count is 5.
     */
    BufpoolLeavePage(seInsPtr->bufpoolMgr, pageId, false);
    // free all the pages used in this testcase.
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

TEST_F(UtBufferpool, BufpoolGetPage002)
{
    SeInstanceT *seInsPtr = UtBufferpool::seIns;
    /**
     * @tc.steps: step2. alloc a page.
     */
    PageIdT pageId = {2, 2};
    uint8_t *page = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageId);
    EXPECT_EQ((int32_t)pageId.deviceId, 0);
    EXPECT_EQ((int32_t)pageId.blockId, 1);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);

    /**
     * @tc.steps: step3. Get Page with specified PageId, and set isWrite to false.
     * page option:ENTER_PAGE_NORMAL, check LRU list
     * @tc.expected: Return STATUS_OK_INTER, and redoCtx->bufPos is 0.
     */
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageId, &page, ENTER_PAGE_NORMAL, true);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    EXPECT_EQ(((PageHeadT *)page)->addr.deviceId, pageId.deviceId);
    EXPECT_EQ(((PageHeadT *)page)->addr.blockId, pageId.blockId);

    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    BufDescLinkedListT *list = &seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL];
    BufDescT *bufDesc = BUF_DESC_LIST_HEAD_ENTRY(list);
    EXPECT_TRUE(bufDesc != NULL);
    EXPECT_EQ(pageId.deviceId, bufDesc->pageId.deviceId);
    EXPECT_EQ(pageId.blockId, bufDesc->pageId.blockId);
    // isWrite为true，isDirty，isWriting应该也为true
    EXPECT_TRUE(bufDesc->isDirty);
    EXPECT_TRUE(bufDesc->isWriting);
    BufpoolLeavePage(seInsPtr->bufpoolMgr, pageId, false);
    // 没有记redo日志，所以这里会走进RedoDirtyPageReset
    ASSERT_EQ((uint32_t)0, redoCtx->bufPos);
    RedoLogEndImpl(redoCtx, true);

    /**
     * @tc.steps: step 4 check LRU counts of list.
     * @tc.expected: return lruList.count is 5.
     */
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    // free the page used in this testcase.
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

/**
 * @tc.name: BufpoolGetPageMultiSpace.
 * @tc.desc: test to get a page from multiple spaces.
 */
TEST_F(UtBufferpool, BufpoolGetPageMultiSpace)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    /**
     * @tc.steps: step1. alloc a page to space 0 and space 1 separately.
     */
    PageIdT pageIdFirst = {2, 2};
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageIdFirst);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    // for space 1.
    PageIdT pageIdSecond = {0, 0};
    allocPageParam.spaceId = 1;
    ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageIdSecond);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    /**
     * @tc.steps: step2. check the page getting from space 0 and space 1.
     */
    uint8_t *pageSpcFirst = NULL;
    uint8_t *pageSpcSecond = NULL;
    // check get page in space 0.
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageIdFirst, &pageSpcFirst, ENTER_PAGE_NORMAL, false);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(((PageHeadT *)pageSpcFirst)->addr.deviceId, pageIdFirst.deviceId);
    EXPECT_EQ(((PageHeadT *)pageSpcFirst)->addr.blockId, pageIdFirst.blockId);
    // check get page in space 1.
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageIdSecond, &pageSpcSecond, ENTER_PAGE_NORMAL, false);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(((PageHeadT *)pageSpcSecond)->addr.deviceId, pageIdSecond.deviceId);
    EXPECT_EQ(((PageHeadT *)pageSpcSecond)->addr.blockId, pageIdSecond.blockId);
    // free all the pages in this testcase.
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageIdFirst, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    freePageParam.addr = pageIdSecond;
    freePageParam.spaceId = 1;
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

/**
 * @tc.name: BufpoolAllocPage.
 * @tc.desc: test to alloc page through buffer pool and the LRU list size.
 */
TEST_F(UtBufferpool, BufpoolAllocPage)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    /**
     * @tc.steps: step2. Alloc a page, Desc is reused.
     * @tc.expected: step2. Return STATUS_OK_INTER and LRU List count maintain the same size.
     */
    PageIdT pageId = {2, 2};
    uint8_t *page = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageId);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pageId, &page, ENTER_PAGE_NORMAL, false);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((int32_t)pageId.deviceId, 0);
    EXPECT_EQ((int32_t)pageId.blockId, 1);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    BufDescLinkedListT *list = &seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL];
    BufDescT *bufDesc = BUF_DESC_LIST_HEAD_ENTRY(list);
    EXPECT_TRUE(bufDesc != NULL);
    EXPECT_EQ(bufDesc->pageId.deviceId, pageId.deviceId);
    EXPECT_EQ(bufDesc->pageId.blockId, pageId.blockId);
    BufpoolLeavePage(seInsPtr->bufpoolMgr, pageId, false);

    /**
     * @tc.steps: step2. Alloc a page, new Desc is created.
     * @tc.expected: step2. Return STATUS_OK_INTER and LRU List size add 1.
     */
    PageIdT newPageId = {3, 2};
    ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &newPageId);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 2);
    // free all the pages used in this testcase.
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    freePageParam.addr = newPageId;
    freePageParam.spaceId = 0;
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

/**
 * @tc.name: BufpoolAllocFreeAlloc.
 * @tc.desc: test to alloc then free a page and check the LRU length.
 */
TEST_F(UtBufferpool, BufpoolAllocFreeAlloc)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    /**
     * @tc.steps: step1. Alloc a page then free.
     * @tc.expected: step1. Return STATUS_OK_INTER.
     */
    PageIdT pageId = {2, 2};
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageId);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    /**
     * @tc.steps: step2. Alloc another page.
     * @tc.expected: step2. Return STATUS_OK_INTER and LRU length as the same.
     */
    PageIdT newPageId = {3, 2};
    ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &newPageId);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    freePageParam.addr = newPageId;
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

/**
 * @tc.name: BufpoolFreePinAndAllo.
 * @tc.desc: test to operate on a pinned page.
 */
TEST_F(UtBufferpool, BufpoolFreePinAndAlloc)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageIdT pinPageId = {2, 2};
    uint8_t *pinPage = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    /**
     * @tc.steps: set a resident page then free.
     * @tc.expected: step1. Return STATUS_OK_INTER, isResident status should be modified.
     */
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    EXPECT_EQ(BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pinPageId), STATUS_OK_INTER);
    uint8_t pinDescIndex = seInsPtr->bufpoolMgr->bufPool->hwm - 1;
    BufDescT *bufDescs = (BufDescT *)DbDynShmemPtrToAddr(seInsPtr->bufpoolMgr->bufPool->bufDescShm);
    BufDescT pinDesc = bufDescs[pinDescIndex];
    EXPECT_EQ(pinDesc.pageId.deviceId, pinPageId.deviceId);
    EXPECT_EQ(pinDesc.pageId.blockId, pinPageId.blockId);
    EXPECT_FALSE(pinDesc.isResident);
    uint32_t lenBefore = seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count;
    StatusInter ret = BufpoolGetPage(seInsPtr->bufpoolMgr, pinPageId, &pinPage, ENTER_PAGE_RESIDENT, false);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_TRUE(bufDescs[pinDescIndex].isResident);
    EXPECT_EQ(lenBefore - seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, 1u);
    BufpoolLeavePage(seInsPtr->bufpoolMgr, pinPageId, false);
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pinPageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
}

/**
 * @tc.name: BufpoolEnterScanList
 * @tc.desc: In scan, add a page to scan list.
 */
TEST_F(UtBufferpool, BufpoolEnterScanList)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    PageIdT pagesList[UT_BUFFERPOOL_CAPACITY * 3] = {0};
    int64_t buffpoolCapacity = (int32_t)seInsPtr->bufpoolMgr->bufPool->capacity;
    /**
     * @tc.steps: Make buffer pool full
     * @tc.expected: step1. Return STATUS_OK_INTER, and lru size reach the maximum capacity.
     */
    for (int i = 0; i < buffpoolCapacity * 3; i++) {
        int32_t spaceId = 0;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = (uint32_t)spaceId,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, buffpoolCapacity);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_STATS_SCAN].count, 0);
    int lenBefore = (int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count;
    /**
     * @tc.steps: Alloc a page assuming for Scan.
     * @tc.expected: step2. Return STATUS_OK_INTER, a scan page enter to the list.
     */
    PageIdT scanPageId = pagesList[4];
    uint8_t *scanPage = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    StatusInter ret = BufpoolGetPage(seInsPtr->bufpoolMgr, scanPageId, &scanPage, ENTER_PAGE_SCAN, false);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, lenBefore - 1);
    EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_STATS_SCAN].count, 1);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);

    // free all pages.
    for (int i = 0; i < buffpoolCapacity * 3; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        FreePageParamT freePageParam = {.spaceId = 0, .addr = pagesList[i], .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
}

/**
 * @tc.name: BufferpoolEvictPage001
 * @tc.desc: bufferpool is full and begins to evict pages.
 */
TEST_F(UtBufferpool, BufpoolEvictPage001)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    /**
     * @tc.steps: Make buffer pool full
     * @tc.expected: step2. Return STATUS_OK_INTER, and lru size reach the maximum capacity.
     */

    // todo: confirm three pinned pages in bufferpool.
    PageIdT pagesList[UT_BUFFERPOOL_CAPACITY - UT_LRU_INIT_SIZE] = {0};
    uint8_t pageCount = 0;
    uint8_t buffpoolCapacity = seInsPtr->bufpoolMgr->bufPool->capacity;
    while (seInsPtr->bufpoolMgr->bufPool->hwm != buffpoolCapacity) {
        pagesList[pageCount].deviceId = 0;
        pagesList[pageCount].blockId = pageCount + 1;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = 0,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        EXPECT_EQ(BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[pageCount]), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
        pageCount++;
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);
    uint8_t fullBufpoolLruSize = seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count;
    /**
     * @tc.steps: step3. evict the pages and get page.
     * @tc.expected: step4. Return STATUS_OK_INTER, and new page in lru list.
     */
    PageIdT pageId = {2, 2};
    uint8_t *page = NULL;
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    AllocPageParamT allocPageParam = {.spaceId = 0,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    EXPECT_EQ(BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pageId), STATUS_OK_INTER);
    EXPECT_EQ(BufpoolGetPage(seInsPtr->bufpoolMgr, pageId, &page, ENTER_PAGE_NORMAL, false), STATUS_OK_INTER);
    /**
     * @tc.steps: step5. check the evicted pages whether in the head of lru list.
     * @tc.expected:step6. insert to the head of lru list and lru size same.
     */
    BufDescLinkedListT *list = &seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL];
    BufDesc *lruFirst = BUF_DESC_LIST_HEAD_ENTRY(list);
    PageIdT lruPageId = lruFirst->pageId;
    EXPECT_EQ(lruPageId.deviceId, pageId.deviceId);
    EXPECT_EQ(lruPageId.blockId, pageId.blockId);
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, fullBufpoolLruSize);
    // free all the pages used in this testcase.
    FreePageParamT freePageParam = {.spaceId = 0, .addr = pageId, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    for (int i = 0; i < pageCount; i++) {
        PageIdT pageSetId = pagesList[i];
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        freePageParam.addr = pageSetId;
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
}

/**
 * @tc.name: BufferpoolEvictPage002
 * @tc.desc: bufferpool is full and begins to check evict status.
 */
TEST_F(UtBufferpool, BufpoolEvictPage002)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    /**
     * @tc.steps: Make buffer pool full
     * @tc.expected: step1. Return STATUS_OK_INTER, and insert to lru size bufpool reach the maximum capacity.
     */
    PageIdT pagesList[UT_BUFFERPOOL_CAPACITY] = {0};
    uint8_t pageCount = 0;
    uint8_t buffpoolCapacity = seInsPtr->bufpoolMgr->bufPool->capacity;
    uint8_t pinDescIndex = -1;
    uint8_t *residentPage = NULL;
    for (int i = 0; i < buffpoolCapacity * 3; i++) {
        pagesList[pageCount].deviceId = 0;
        pagesList[pageCount].blockId = pageCount + 1;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = 0,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        EXPECT_EQ(BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[pageCount]), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
        /**
         * @tc.steps: Set the pinned page.
         * @tc.expected: step2. the lru size no change.
         */
        if (pageCount == 0) {  // set resident page.
            pinDescIndex = seInsPtr->bufpoolMgr->bufPool->hwm - 1;
            EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE + 1);
            RedoLogBeginImpl(SeGetCurRedoCtxImpl());
            EXPECT_EQ(BufpoolGetPage(seInsPtr->bufpoolMgr, pagesList[0], &residentPage, ENTER_PAGE_RESIDENT, false),
                STATUS_OK_INTER);
            // set all the pages in LRU not dirty to avoid triggering flush.
            BufpoolLeavePage(seInsPtr->bufpoolMgr, pagesList[0], false);
            RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
            EXPECT_EQ((int32_t)seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count, UT_LRU_INIT_SIZE);
        }
        pageCount++;
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);
    BufDescT *bufDescs = (BufDescT *)DbDynShmemPtrToAddr(seInsPtr->bufpoolMgr->bufPool->bufDescShm);
    EXPECT_TRUE(bufDescs[pinDescIndex].isResident);
}

Status DirectReadOpenMock(GmcConnT *conn, uint32_t sessionId, uint16_t trxSlot, DbMemCtxT *memCtx, DrRunCtxT **drRunCtx)
{
    return GMERR_MEMORY_OPERATE_FAILED;
}

/**
 * @tc.name: BufpoolNotSupportCltDirectRead
 * @tc.desc: Bufferpool not support with clientside opening direct read, CS mode only.
 */
TEST_F(UtBufferpool, BufpoolNotSupportCltDirectRead)
{
    (void)setStubC((void *)DirectReadOpen, (void *)DirectReadOpenMock);
    // 模拟建立链接，在bufferpool场景下以直连读的方式打开
    GmcConnT conn = {};
    conn.openDirectRead = true;
    conn.instanceId = GET_INSTANCE_ID;
    conn.remoteId = 0;
    conn.memCtx = (DbMemCtxT *)UtBufferpool::seRunCtx->sessionMemCtx;
    CliConnectResponseT rsp = {0};
    const GmcConnOptionsT options = {0};
    /**
     * @tc.steps: 以直连读的方式打开客户端
     * @tc.expected: 直连读自动关闭，走CS模式
     */
    Status status = CltDirectOpen(&conn, &rsp, &options);
    EXPECT_EQ(GMERR_MEMORY_OPERATE_FAILED, status);
    EXPECT_EQ(false, conn.openDirectRead);
    EXPECT_EQ(GMC_TRANS_USED_IN_CS, conn.transMode);
}

typedef struct TagBufPoolThreadArgs {
    SeInstanceT *seIns;
    std::vector<PageIdT> pageArr;
    uint32_t successCount;
    RedoRunCtxT *redoCtx;
    DbSpinLockT lock;
    SeRunCtxT *seRunCtxT;
    int16_t allocNumber;
    int32_t getPageTimes;
    int16_t getPageListSize;
    PageIdT *getPageList;
    uint32_t pageLoadOrder;
    uint32_t threadId;
    PageIdT specificPageIdList[UT_BUFFERPOOL_GET_PAGE_PRE_THREAD];
} BufPoolThreadArgs;

static void *ThreadAllocPage(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    BufpoolMgrT *pageMgr = BufpoolGetPageMgr(arg->seIns);
    SeInstanceT *seIns = arg->seIns;
    SeRunCtxT *seRunCtx = arg->seRunCtxT;
    int16_t threadAllocPages = arg->allocNumber;
    EXPECT_EQ(pageMgr->bufPool->size, UT_BUFFERPOOL_SIZE_4M);
    EXPECT_EQ(pageMgr->pageSize, UT_BUFFERPOOL_PAGE_SIZE_32K);
    SeOpen(seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, &seRunCtx);
    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
    arg->redoCtx = SeGetCurRedoCtxImpl();
    for (int i = 0; i < threadAllocPages; i++) {
        StatusInter ret;
        PageIdT tmp = SE_INVALID_PAGE_ADDR;
        RedoLogBeginImpl(arg->redoCtx);
        AllocPageParamT allocPageParam = {.spaceId = 0,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        ret = BufpoolAllocPage(pageMgr, &allocPageParam, &tmp);
        if (ret == STATUS_OK_INTER) {
            arg->successCount++;
            arg->pageArr.push_back(tmp);
            RedoLogEndImpl(arg->redoCtx, true);
        } else {
            DB_ASSERT(0);
        }
    }
    SeClose(seRunCtx);
    return NULL;
}

// 因为涉及刷盘和换入换出，同时具有测试SpaceWriteBlock并发写入datafile的功能。
TEST_F(UtBufferpool, BufpoolConAlloc001)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
#define THREAD_NUM 16
    g_enableMutex = true;
    ASSERT_EQ(pthread_mutex_init(&g_spaceMutex, NULL), GMERR_OK);
    g_spaceAllocPageId = {0, 1};
    /**
     * @tc.steps: step2. Create 16 threads used to alloc page, 30 times each thread
     * @tc.expected: step2. Execute successfully
     */
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    int16_t threadAllocPages = 35;
    for (int i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].seRunCtxT = NULL;
        threadCtxs[i].allocNumber = threadAllocPages;
        ASSERT_EQ(pthread_create(&threads[i], NULL, ThreadAllocPage, &threadCtxs[i]), GMERR_OK);
    }

    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }

    for (int i = 0; i < THREAD_NUM; i++) {
        EXPECT_EQ((int32_t)threadCtxs[i].successCount, threadAllocPages);
        for (size_t j = 0; j < threadCtxs[i].pageArr.size(); j++) {
            RedoLogBeginImpl(SeGetCurRedoCtxImpl());
            PageIdT freePageId = threadCtxs[i].pageArr[j];
            // alloc出来的页重新被getPage，不会有重复页，预期成功。
            FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
            SeInitCachePagePara(&freePageParam.cachePagePara);
            EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
            RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
        }
    }
#undef THREAD_NUM
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->capacity, UT_BUFFERPOOL_CAPACITY);
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
}

static void *ThreadGetMultiPages(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    SeInstanceT *seIns = arg->seIns;
    SeRunCtxT *seRunCtx = arg->seRunCtxT;
    int16_t threadGetPages = arg->allocNumber;

    SeOpen(seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, &seRunCtx);
    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
    arg->redoCtx = SeGetCurRedoCtxImpl();
    RedoLogBeginImpl(arg->redoCtx);
    for (int i = 0; i < threadGetPages; i++) {
        StatusInter ret;
        PageIdT getPageId = arg->specificPageIdList[i];
        uint8_t *page = NULL;
        ret = BufpoolGetPage(seIns->bufpoolMgr, getPageId, &page, ENTER_PAGE_NORMAL, true);
        if (ret == STATUS_OK_INTER) {
            arg->successCount++;
            BufpoolLeavePage(seIns->bufpoolMgr, getPageId, false);
        } else {
            DB_ASSERT(0);
        }
    }
    // 去掉下面两行代码，可构造出Bufferpool无法换入换出的场景
    (void)RedoLogEndImpl(arg->redoCtx, true);
    SeClose(seRunCtx);

    return NULL;
}

TEST_F(UtBufferpool, BufpoolConAllocAndGetMultiPages002)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
#define THREAD_NUM 4
    g_enableMutex = true;
    ASSERT_EQ(pthread_mutex_init(&g_spaceMutex, NULL), GMERR_OK);
    g_spaceAllocPageId = {0, 1};
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    int16_t threadAllocPages = UT_BUFFERPOOL_GET_PAGE_PRE_THREAD;
    uint32_t totalAllocNum = THREAD_NUM * threadAllocPages;
    PageIdT pagesList[totalAllocNum] = {0};

    for (uint32_t i = 0; i < totalAllocNum; i++) {
        int32_t spaceId = 0;
        pagesList[i].deviceId = 0;
        pagesList[i].blockId = i;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = (uint32_t)spaceId,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);

    for (int i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = seInsPtr;
        threadCtxs[i].seRunCtxT = NULL;
        threadCtxs[i].allocNumber = threadAllocPages;
        // 按pageId有小到大顺序获取页，每个线程拿的页不会相同
        for (uint32_t j = 0; j < threadAllocPages; j++) {
            uint32_t targetPageIdIndex = i * threadAllocPages + j;
            threadCtxs[i].specificPageIdList[j].deviceId = pagesList[targetPageIdIndex].deviceId;
            threadCtxs[i].specificPageIdList[j].blockId = pagesList[targetPageIdIndex].blockId;
        }
        ASSERT_EQ(pthread_create(&threads[i], NULL, ThreadGetMultiPages, &threadCtxs[i]), GMERR_OK);
    }

    // 等待线程执行完操作，不然会进入TearDown流程
    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }

    for (uint32_t i = 0; i < totalAllocNum; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = pagesList[i];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }
#undef THREAD_NUM
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
}

StatusInter CkptInitGroupMock(SeInstanceT *seInsPtr, uint32_t groupSize)
{
    return OUT_OF_MEMORY_INTER;
}

// DTS2024102906873
// 此用例模拟checkpoint因内存不足后，bufferpool无法换入换出的问题。
TEST_F(UtBufferpool, BufpoolGetMultiPagesAndCkptOutOfMemory002)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    int16_t threadAllocPages = UT_BUFFERPOOL_GET_PAGE_PRE_THREAD;
    uint32_t totalAllocNum = 10 * threadAllocPages;
    PageIdT pagesList[totalAllocNum] = {0};

    uint32_t spaceId = 0;
    AllocPageParamT allocPageParam = {.spaceId = (uint32_t)spaceId,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        pagesList[i].deviceId = 0;
        pagesList[i].blockId = i;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(STATUS_OK_INTER, ret);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);

    // checkpoint打桩，构造内存不足的场景
    int stubIndex = setStubC((void *)CkptInitGroup, (void *)CkptInitGroupMock);
    DB_ASSERT(stubIndex > 0);
    PageIdT errPageList[totalAllocNum] = {0};
    uint32_t errPageAllocNum = 0;
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        errPageList[i].deviceId = 0;
        errPageList[i].blockId = i;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &errPageList[i]);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
        if (ret == STATUS_OK_INTER) {
            errPageAllocNum++;
            continue;
        } else {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, DbGetExternalErrno(ret));
            return;
        }
    }
    PageIdT errPageId = {0};
    // Due to checkpoint out of memory, bufpool will exit if new desc cannot enter memory.
    RedoLogBeginImpl(SeGetCurRedoCtxImpl());
    StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &errPageId);
    ASSERT_EQ(OUT_OF_MEMORY_INTER, ret);
    RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    clearStub(stubIndex);

    for (uint32_t i = 0; i < totalAllocNum; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = pagesList[i];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(STATUS_OK_INTER, BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam));
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }

    for (uint32_t i = 0; i < errPageAllocNum; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = errPageList[i];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(STATUS_OK_INTER, BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam));
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }
}

static void *ThreadGetPage(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    SeInstanceT *seIns = arg->seIns;
    SeRunCtxT *seRunCtxT = arg->seRunCtxT;
    int16_t getPageTimes = arg->getPageListSize;
    SeOpen(seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, &seRunCtxT);
    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtxT->redoCtx);
    arg->redoCtx = SeGetCurRedoCtxImpl();
    for (int i = 0; i < getPageTimes; i++) {
        StatusInter ret;
        PageIdT getPageId = arg->getPageList[i];
        uint8_t *page = NULL;
        RedoLogBeginImpl(arg->redoCtx);
        ret = BufpoolGetPage(seIns->bufpoolMgr, getPageId, &page, ENTER_PAGE_NORMAL, false);
        bool isDevIdEqual = ((PageHeadT *)page)->addr.deviceId == getPageId.deviceId;
        bool isBlockIdEqual = ((PageHeadT *)page)->addr.blockId == getPageId.blockId;
        if (ret == STATUS_OK_INTER && isDevIdEqual && isBlockIdEqual) {
            arg->successCount++;
            BufpoolLeavePage(seIns->bufpoolMgr, getPageId, false);
            RedoLogEndImpl(arg->redoCtx, true);
        } else {
            DB_ASSERT(0);
        }
    }
    SeClose(seRunCtxT);
    return NULL;
}

// 因为涉及刷盘和换入换出，同时具有测试SpaceReadBlock并发读取的功能。
TEST_F(UtBufferpool, BufpoolConGetPage001)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    uint32_t deviceAmount = 5;
    uint32_t totalAllocNum = UT_BUFFERPOOL_CAPACITY * deviceAmount;
    PageIdT pagesList[totalAllocNum] = {0};
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        int32_t spaceId = 0;
        pagesList[i].deviceId = 0;
        pagesList[i].blockId = i;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = (uint32_t)spaceId,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);

    // store pageId to the list to getPage.
    uint8_t threadGetPageTimes = 10;
    PageIdT getPageList[threadGetPageTimes] = {0};
    for (uint8_t i = 0; i < threadGetPageTimes; i++) {
        uint32_t pageIdIndex = 6 + i * 3;
        ASSERT_TRUE(pageIdIndex < totalAllocNum);
        // 多个线程读取同一批pageId的页
        getPageList[i] = pagesList[pageIdIndex];
    }

#define THREAD_NUM 128
    // Concurry get pages by read.
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].seRunCtxT = NULL;
        threadCtxs[i].getPageListSize = threadGetPageTimes;
        threadCtxs[i].getPageList = getPageList;
        ASSERT_EQ(pthread_create(&threads[i], NULL, ThreadGetPage, &threadCtxs[i]), GMERR_OK);
    }

    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }
#undef THREAD_NUM
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = pagesList[i];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }

    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
}

static void generateRandomPageId(
    PageIdT *getPageList, uint8_t threadGetPageTimes, PageIdT *pagesList, uint32_t loadOrder)
{
    uint8_t loadPageIdFromFront = 0;
    if (loadOrder == loadPageIdFromFront) {
        for (uint32_t i = 0; i < threadGetPageTimes; i++) {
            getPageList[i] = pagesList[i];
        }
    } else {  // load PageId from tail.
        for (uint32_t i = 0; i < threadGetPageTimes; i++) {
            getPageList[i] = pagesList[threadGetPageTimes - 1 - i];
        }
    }
}

// 同时具有测试SpaceRead/WriteBlock并发读写的功能。
TEST_F(UtBufferpool, BufpoolConGetPage002)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // alloc一定数量的页，且能触发换入换出, 就有读写并发
    uint32_t ratio = 2;
    uint32_t totalAllocNum = UT_BUFFERPOOL_CAPACITY * ratio;
    PageIdT pagesList[totalAllocNum] = {0};
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        int32_t spaceId = 0;
        pagesList[i].deviceId = 0;
        pagesList[i].blockId = i;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = (uint32_t)spaceId,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);

#define THREAD_NUM 128
    // Concurry get pages in either read or write.
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    uint8_t threadGetPageTimes = totalAllocNum;
    for (uint32_t i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].seRunCtxT = NULL;
        // 设定每个线程计划需要操作的pageId
        threadCtxs[i].pageLoadOrder = i % 2;
        threadCtxs[i].getPageListSize = threadGetPageTimes;
        generateRandomPageId(
            threadCtxs[i].getPageList, threadCtxs[i].getPageListSize, pagesList, threadCtxs[i].pageLoadOrder);
        ASSERT_EQ(pthread_create(&threads[i], NULL, ThreadGetPage, &threadCtxs[i]), GMERR_OK);
    }

    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }

    for (uint32_t i = 0; i < totalAllocNum; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = pagesList[i];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }
#undef THREAD_NUM
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
}

void RedoModifyChangedPageMock(RedoRunCtxT *redoCtx)
{
    g_isEnterRedoModify = true;
    sleep(1);
}

static void *ThreadGetSpecificPage(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    SeInstanceT *seIns = arg->seIns;
    SeRunCtxT *seRunCtx = arg->seRunCtxT;
    SeOpen(seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, &seRunCtx);
    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
    // get specific page.
    PageIdT getPageId = arg->getPageList[8];
    uint8_t *page = NULL;
    arg->redoCtx = SeGetCurRedoCtxImpl();
    RedoLogBeginImpl(arg->redoCtx);
    StatusInter ret = BufpoolGetPage(seIns->bufpoolMgr, getPageId, &page, ENTER_PAGE_NORMAL, true);
    BufpoolLeavePage(seIns->bufpoolMgr, getPageId, false);
    DB_ASSERT(ret == STATUS_OK_INTER);
    // 延长进入RedoModifyChangedPage的流程，睡眠足够的时间，确保在脏页队列的BufDesc被换出。
    int stubIndex = setStubC((void *)RedoModifyChangedPage, (void *)RedoModifyChangedPageMock);
    DB_ASSERT(stubIndex > 0);
    RedoLogEndImpl(arg->redoCtx, true);
    RedoModifyChangedPage(arg->redoCtx);
    SeClose(seRunCtx);
    return NULL;
}

static void *ThreadAllocPageLater(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    BufpoolMgrT *pageMgr = BufpoolGetPageMgr(arg->seIns);
    SeInstanceT *seIns = arg->seIns;
    SeRunCtxT *seRunCtx = arg->seRunCtxT;
    int16_t threadAllocPages = arg->allocNumber;
    SeOpen(seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, &seRunCtx);
    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
    arg->redoCtx = SeGetCurRedoCtxImpl();
    for (int i = 0; i < threadAllocPages; i++) {
        StatusInter ret;
        PageIdT tmp = SE_INVALID_PAGE_ADDR;
        RedoLogBeginImpl(arg->redoCtx);
        AllocPageParamT allocPageParam = {.spaceId = 0,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        ret = BufpoolAllocPage(pageMgr, &allocPageParam, &tmp);
        if (ret == STATUS_OK_INTER) {
            arg->successCount++;
            arg->pageArr.push_back(tmp);
            while (!g_isEnterRedoModify) {
                // do nothing until thread 0 enter redoModify.
            }
            clearAllStub();
            RedoLogEndImpl(arg->redoCtx, true);
        } else {
            DB_ASSERT(0);
        }
    }
    SeClose(seRunCtx);
    return NULL;
}

// DTS2024022716032
// 此用例模拟未记redo日志的流程后，RedoLogEnd的并发问题。
/**
 * 并发问题描述：两个线程并发，分别为ThreadA，ThreadB
 * ThreadA：RedoLogEnd | bufPos ==
 0（RedoDirtyPageReset)Desc标记为非脏|----------时间间隙------|RedoModifyChangedPage
 * ThreadB：-------------Bufferpool换入换出-------------------------------|Desc被换出|被初始化|
 * Result：ThreadA出现：bufDesc->threadId != redoCtx->threadId
 */
TEST_F(UtBufferpool, BufpoolConGetAllocPage003)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    // alloc一定数量的页，且能触发换入换出
    uint32_t ratio = 2;
    uint32_t totalAllocNum = UT_BUFFERPOOL_CAPACITY * ratio;
    PageIdT pagesList[totalAllocNum] = {0};
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        int32_t spaceId = 0;
        pagesList[i].deviceId = 0;
        pagesList[i].blockId = i;
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        AllocPageParamT allocPageParam = {.spaceId = (uint32_t)spaceId,
            .trmId = 0,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = NULL,
            .labelRsmUndo = NULL};  // 持久化不使用rsmUndo
        StatusInter ret = BufpoolAllocPage(seInsPtr->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), true);
    }
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, seInsPtr->bufpoolMgr->bufPool->capacity);

#define THREAD_NUM 2
    g_enableMutex = true;
    ASSERT_EQ(pthread_mutex_init(&g_spaceMutex, NULL), GMERR_OK);
    g_spaceAllocPageId = {0, 1};
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    int16_t threadAllocPages = 150;
    for (int i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].seRunCtxT = NULL;
        threadCtxs[i].allocNumber = threadAllocPages;
        threadCtxs[i].threadId = i;
        threadCtxs[i].getPageList = pagesList;
    }
    ASSERT_EQ(pthread_create(&threads[0], NULL, ThreadGetSpecificPage, &threadCtxs[0]), GMERR_OK);
    ASSERT_EQ(pthread_create(&threads[1], NULL, ThreadAllocPageLater, &threadCtxs[1]), GMERR_OK);

    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }

    EXPECT_EQ((int32_t)threadCtxs[1].successCount, threadAllocPages);
    for (size_t j = 0; j < threadCtxs[1].pageArr.size(); j++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = threadCtxs[1].pageArr[j];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }

    for (uint32_t i = 0; i < totalAllocNum; i++) {
        RedoLogBeginImpl(SeGetCurRedoCtxImpl());
        PageIdT freePageId = pagesList[i];
        FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
        RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
    }
#undef THREAD_NUM
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->capacity, UT_BUFFERPOOL_CAPACITY);
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
}

/**
 * @tc.name: BufpoolHighConcurrency
 * @tc.desc: Concurrently, alloc 1000 pages pre thread, 32 threads, expected 1min.
 */
TEST_F(UtBufferpool, DISABLED_BufpoolHighConcurrency)
{
    SeInstanceT *seInsPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
#define THREAD_NUM 32
    g_enableMutex = true;
    ASSERT_EQ(pthread_mutex_init(&g_spaceMutex, NULL), GMERR_OK);
    g_spaceAllocPageId = {0, 1};
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    int16_t threadAllocPages = 1000;
    for (int i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].seRunCtxT = NULL;
        threadCtxs[i].allocNumber = threadAllocPages;
        ASSERT_EQ(pthread_create(&threads[i], NULL, ThreadAllocPage, &threadCtxs[i]), GMERR_OK);
    }

    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(pthread_join(threads[i], NULL), GMERR_OK);
    }

    int16_t lruNumber = seInsPtr->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL].count;
    EXPECT_EQ(lruNumber + UT_SPACE_CTRL_RESIDENT_PAGE, (int16_t)UT_BUFFERPOOL_CAPACITY);
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->capacity, UT_BUFFERPOOL_CAPACITY);
    EXPECT_EQ(seInsPtr->bufpoolMgr->bufPool->hwm, UT_BUFFERPOOL_CAPACITY);
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;

    for (int i = 0; i < THREAD_NUM; i++) {
        EXPECT_EQ((int32_t)threadCtxs[i].successCount, threadAllocPages);
        for (size_t j = 0; j < threadCtxs[i].pageArr.size(); j++) {
            RedoLogBeginImpl(SeGetCurRedoCtxImpl());
            PageIdT freePageId = threadCtxs[i].pageArr[j];
            FreePageParamT freePageParam = {.spaceId = 0, .addr = freePageId, .dbInstance = NULL, .labelRsmUndo = NULL};
            SeInitCachePagePara(&freePageParam.cachePagePara);
            EXPECT_EQ(BufpoolFreePage(seInsPtr->bufpoolMgr, &freePageParam), STATUS_OK_INTER);
            RedoLogEndImpl(SeGetCurRedoCtxImpl(), false);
        }
    }
#undef THREAD_NUM
    pthread_mutex_destroy(&g_spaceMutex);
    g_enableMutex = false;
}
