/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: ut for compressed buffer pool
 * Author:
 * Create: 2024-12
 */
#include "gtest/gtest.h"
#include "stub.h"
#include "se_space_virtual_disk.h"
#include "adpt_compress.h"
#include "pagemgr_common.h"
#include "se_page_mgr.h"
#include "se_ckpt.h"
#include "se_define.h"
#include "ut_bufferpool_base.h"
#include "clt_conn.h"
#include "db_rpc_conn_msg.h"
#include "clt_da_read.h"
#include "se_database.h"
#include "se_buffer_pool_page_buffer.h"

using namespace std;
#define PAGE_SIZE (16 * DB_KIBI)
#define BUFPOOL_CAPACITY 128
#define BUFPOOL_PAPGE_UNFULL_SIZE 100
const uint32_t cmdBuffMaxLen = 1024;

static int g_stubNo = 0;
static pthread_mutex_t g_spaceMutex;
static bool g_enableMutex = false;

extern "C" {
static ALWAYS_INLINE uint8_t *CtrlGroupGetItem(CtrlGroupT *group, uint32_t index);
Status CltDirectOpen(GmcConnT *conn, CliConnectResponseT *rsp, const GmcConnOptionsT *options);
Status DirectReadOpen(GmcConnT *conn, uint32_t sessionId, uint16_t trxSlot, DbMemCtxT *memCtx, DrRunCtxT **drRunCtx);
}

static inline void ClearTestUserData()
{}

Status DbDataCompressMock(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    uint32_t compressedsize = sourceLen / 2;
    (void)memcpy_s((void *)dest, sourceLen / 2, source, sourceLen / 2);
    *destLen = compressedsize;
    DbSleep(2);
    return GMERR_OK;
}

Status DbDataDecompressMock(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    uint32_t compressedsize = sourceLen * 2;
    (void)memcpy_s((void *)destLen, sizeof(uint32_t), &compressedsize, sizeof(uint32_t));
    (void)memcpy_s((void *)dest, sourceLen, source, sourceLen);
    (void)memcpy_s((void *)(dest + sourceLen), sourceLen, source, sourceLen);
    return GMERR_OK;
}

class UtCompressArea : public PageMgrUtTest {
public:
protected:
    virtual void SetUp()
    {
        init();
        system("ipcrm -a");
        system("rm -rf /data/gmdb");
        system("mkdir -p " TEST_GMDB_DATA_DIR);
        // 按需持久化下，关闭redo所以这里需要对这个全局变量进行初始化
        RedoAmFuncT UtOnDemandRedoAm = {0};
        RedoSetAmFunc(&UtOnDemandRedoAm);
        DbSetServerThreadFlag();
        DbInitTopDynMemCtx(NULL);

        char cfgModifyCmd[cmdBuffMaxLen] = {0};
        int32_t ret = snprintf_s(cfgModifyCmd, cmdBuffMaxLen, cmdBuffMaxLen - 1,
            "\"persistentMode=%d\" \"bufferPoolPolicy=%d\" \"compressSpaceEnable=%d\" \"bufferpoolMemType=%d\" "
            "\"pageSize=%d\"",
            0, 0, 1, 1, 16);
        ASSERT_GE(ret, 0) << "cmd max len:" << cmdBuffMaxLen;
        UtPageMgrModifyConfig(SE_CONFIG_INI_PATH, MODIFY_CONFIG_INI_PATH, cfgModifyCmd);
        DbCompressFuncsT func = {DbDataCompressMock, DbDataDecompressMock};
        DbAdptRegCompressFuncs(&func);
        DbAdptRegSpaceCompressFuncs(&func);
        ConstructSeInsAndSeRun(MODIFY_CONFIG_INI_PATH);
        ClearTestUserData();
    }

    virtual void TearDown()
    {
        DestroySeIns();
        CommonRelease();
        ClearTestUserData();
    }
};

Status CmpDirectReadOpenMock(
    GmcConnT *conn, uint32_t sessionId, uint16_t trxSlot, DbMemCtxT *memCtx, DrRunCtxT **drRunCtx)
{
    return GMERR_MEMORY_OPERATE_FAILED;
}

TEST_F(UtCompressArea, ShmemBufPoolSupportDirectRead)
{
    (void)setStubC((void *)DirectReadOpen, (void *)CmpDirectReadOpenMock);
    // 模拟建立链接，在bufferpool场景下以直连读的方式打开
    GmcConnT conn = {};
    conn.openDirectRead = true;
    conn.instanceId = GET_INSTANCE_ID;
    conn.remoteId = 0;
    conn.memCtx = (DbMemCtxT *)UtCompressArea::seRunCtx->sessionMemCtx;
    CliConnectResponseT rsp = {0};
    const GmcConnOptionsT options = {0};
    /**
     * @tc.steps: 以直连读的方式打开客户端
     * @tc.expected: 直连读自动关闭，走CS模式
     */
    Status status = CltDirectOpen(&conn, &rsp, &options);
    EXPECT_EQ(GMERR_MEMORY_OPERATE_FAILED, status);

    // bufferpool部署共享内存后，打开直连读
    EXPECT_EQ(true, conn.openDirectRead);
    EXPECT_EQ(GMC_TRANS_NONE, conn.transMode);
}

/**
 * @tc.name: CompAreaInit
 * @tc.desc: test to initialize compress area.
 */

TEST_F(UtCompressArea, CompAreaInit)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    EXPECT_TRUE(mgr != NULL);
    EXPECT_TRUE(mgr->memCtx != NULL);
    const SeConfigT *cfg = &seIns->seConfig;
    uint32_t expCapacity = cfg->dbFilesMaxCnt * (((uint64_t)cfg->dbFileSize) / cfg->pageSize);
    EXPECT_EQ(expCapacity, mgr->stat.capacity);
    EXPECT_EQ(0, mgr->stat.usedSize);
    EXPECT_EQ(0, mgr->stat.swapInCnt);
    EXPECT_EQ(0, mgr->stat.compressedPageCnt);
}

void *DbDynMemCtxAllocByCompressMock(void *ctx, size_t size)
{
    return NULL;
}

TEST_F(UtCompressArea, CompAreaInitAllocMemFailed)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    EXPECT_TRUE(mgr != NULL);
    SeVirtualDiskDestroy(seIns);
    EXPECT_TRUE(seIns->db->vd == NULL);
    // 日志里预期会出现一条oom的报错
    g_stubNo = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocByCompressMock);
    StatusInter ret = SeVirtualDiskCreate(seIns);
    clearStub(g_stubNo);
    EXPECT_EQ(OUT_OF_MEMORY_INTER, ret);
    EXPECT_TRUE(seIns->db->vd == NULL);
}

TEST_F(UtCompressArea, AddPageToCompressArea)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    VirtualDiskMgrT *vdMgr = (VirtualDiskMgrT *)seIns->db->vd;
    EXPECT_TRUE(vdMgr != NULL);
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    PageIdT pageId = {2, 2};
    uint8_t page[pageSize];
    (void)memset_s(page, pageSize, 0x1, pageSize);
    ((PageHeadT *)page)->addr = pageId;
    StatusInter ret = SeVirtualDiskWriteBlock(seIns, pageId, page, VD_COMMIT_CHANGE);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    uint32_t seriPageId = SeGetPageCntPerDev(seIns) * pageId.deviceId + pageId.blockId;
    VDNodeT *node = vdMgr->node[seriPageId];
    PageHeadT *pageHead = (PageHeadT *)node->page;
    EXPECT_EQ(pageSize / 2, node->size);
    EXPECT_EQ(pageId.blockId, pageHead->addr.blockId);
    EXPECT_EQ(pageId.deviceId, pageHead->addr.deviceId);
    EXPECT_EQ(NODE_COMPRESSED, node->state);
}

TEST_F(UtCompressArea, ReadPageToCompressArea)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    VirtualDiskMgrT *vdMgr = (VirtualDiskMgrT *)seIns->db->vd;
    EXPECT_TRUE(vdMgr != NULL);
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    PageIdT pageId = {2, 2};
    uint8_t page[pageSize];
    (void)memset_s(page, pageSize, 0x1, pageSize);
    ((PageHeadT *)page)->addr = pageId;
    // insert a page into compress area.
    StatusInter ret = SeVirtualDiskWriteBlock(seIns, pageId, page, VD_COMMIT_CHANGE);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    // read page:
    uint8_t readPage[pageSize];
    (void)memset_s(readPage, pageSize, 0x0, pageSize);
    ret = SeVirtualDiskReadBlock(seIns, pageId, readPage);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    PageHeadT *readHead = (PageHeadT *)readPage;
    EXPECT_EQ(pageId.deviceId, readHead->addr.deviceId);
    EXPECT_EQ(pageId.blockId, readHead->addr.blockId);
}

TEST_F(UtCompressArea, AddFreePageToCompressArea)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    VirtualDiskMgrT *vdMgr = (VirtualDiskMgrT *)seIns->db->vd;
    EXPECT_TRUE(vdMgr != NULL);
    PageIdT pageId = {2, 2};
    StatusInter ret = SeVirtualDiskWriteBlock(seIns, pageId, NULL, VD_FREE);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t seriPageId = SeGetPageCntPerDev(seIns) * pageId.deviceId + pageId.blockId;
    EXPECT_EQ(NULL, vdMgr->node[seriPageId]);

    // read freed page
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    uint8_t readPage[pageSize];
    (void)memset_s(readPage, pageSize, 0x0, pageSize);
    ret = SeVirtualDiskReadBlock(seIns, pageId, readPage);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    PageHeadT *freePageHead = (PageHeadT *)readPage;
    EXPECT_EQ(SE_INVALID_DEVICE_ID, freePageHead->addr.deviceId);
    EXPECT_EQ(SE_INVALID_BLOCK_ID, freePageHead->addr.blockId);
    EXPECT_EQ(PAGE_UNINIT, freePageHead->pageState);
}

static void BufPoolAllocMultiPage(PageIdT *pageIdList, uint32_t allocPageNumber)
{
    StatusInter ret;
    PageIdT pageId = SE_INVALID_PAGE_ADDR;
    // 持久化不使用rsmUndo
    AllocPageParamT allocPageParam = SeInitAllocPageParam(0, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
    for (uint32_t i = 0; i < allocPageNumber; i++) {
        ret = SeAllocPage((PageMgrT *)UtCompressArea::seIns->pageMgr, &allocPageParam, &pageId);
        EXPECT_EQ(STATUS_OK_INTER, ret);
        pageIdList[i] = pageId;
    }
}

TEST_F(UtCompressArea, BufpoolAllocPageAndAddToCompressArea001)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    uint32_t bufpoolPageNum = BUFPOOL_PAPGE_UNFULL_SIZE;
    PageIdT pageList[BUFPOOL_PAPGE_UNFULL_SIZE] = {0};
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;

    BufPoolAllocMultiPage(pageList, bufpoolPageNum);  // bufpool申请100个page
    for (uint32_t i = 0; i < bufpoolPageNum; i++) {
        uint32_t blockId = pageList[i].blockId;
        uint32_t deviceId = pageList[i].deviceId;
        uint32_t descId = deviceId * SeGetPageCntPerDev(seIns) + blockId;
        // 校验PageId, 预期不成功，因为没有触发刷盘
        EXPECT_EQ(nullptr, mgr->node[descId]);
    }
}

Status DbDataCompressCompressLargerSizeMock(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    uint32_t compressedsize = PAGE_SIZE + 10086;
    (void)memcpy_s((void *)destLen, sizeof(uint32_t), &compressedsize, sizeof(uint32_t));
    (void)memcpy_s((void *)dest, sourceLen, source, sourceLen);
    return GMERR_OK;
}

static void BufDescReset(BufDescT *bufDesc)
{
    bufDesc->pageId = SE_INVALID_PAGE_ADDR;
}
/**
 * ThreadA: BufGetDesc(pageA) -----Recycle(Desc1)---Reset(Desc1', id=invalid)---CheckBucket(Desc1')---PutLruTail(Desc1')
 * ThreadB: BufGetDesc(PageA)----Recycle(Desc2)----Reset(Desc2)----AddBucket(Desc2)-----GetPageFinish
 * 接下来拿到Desc1'时，触发save压缩区的操作，把pageId==invalid保存入压缩区
 */
TEST_F(UtCompressArea, CompressAreaSaveInvalidPageId)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    VirtualDiskMgrT *vdMgr = (VirtualDiskMgrT *)seIns->db->vd;
    EXPECT_TRUE(vdMgr != NULL);

    StatusInter ret = STATUS_OK_INTER;
    uint32_t totalAllocNum = 5;
    PageIdT pagesList[totalAllocNum] = {0};
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        pagesList[i].deviceId = 0;
        pagesList[i].blockId = i;
        // 持久化不使用rsmUndo
        AllocPageParamT allocPageParam = SeInitAllocPageParam(1, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
        ret = SeAllocPage((PageMgrT *)seIns->pageMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(STATUS_OK_INTER, ret);
    }
    // 从LRU链尾获取Desc
    BufDescListT *list = &seIns->bufpoolMgr->bufPool->list[LRU_LIST_NORMAL];
    BufDescT *item = BUF_DESC_LIST_TAIL_ENTRY(list);
    PageIdT expPageId = {.deviceId = item->pageId.deviceId, .blockId = item->pageId.blockId};
    PageHeadT *page = (PageHeadT *)item->page;
    // save to corresponding position in compression space.
    PageIdT pageId = item->pageId;
    ret = SeVirtualDiskWriteBlock(seIns, item->pageId, (uint8_t *)page, VD_COMMIT_CHANGE);
    EXPECT_EQ(STATUS_OK_INTER, ret);

    // ResetDesc
    BufDescReset(item);

    // verify the content of the item still exist.
    uint32_t seriPageId = SeGetPageCntPerDev(seIns) * pageId.deviceId + pageId.blockId;
    VDNodeT *node = vdMgr->node[seriPageId];
    EXPECT_TRUE(node != NULL);
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    EXPECT_EQ(pageSize / 2, node->size);
    PageHeadT *pageHead = (PageHeadT *)node->page;
    EXPECT_EQ(expPageId.blockId, pageHead->addr.blockId);
    EXPECT_EQ(expPageId.deviceId, pageHead->addr.deviceId);
    EXPECT_EQ(NODE_COMPRESSED, node->state);
}

typedef struct TagBufPoolThreadArgs {
    SeInstanceT *seIns;
    std::vector<PageIdT> pageArr;
    int16_t allocNumber;
    uint32_t flushTimes;
    uint32_t successCount;
    PageIdT targetPageId;
} BufPoolThreadArgs;

static void *ThreadAllocPageLater(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    SeInstanceT *seIns = arg->seIns;
    BufpoolMgrT *pageMgr = seIns->bufpoolMgr;
    int16_t threadAllocPages = arg->allocNumber;
    for (int i = 0; i < threadAllocPages; i++) {
        StatusInter ret;
        PageIdT tmp = SE_INVALID_PAGE_ADDR;
        // 持久化不使用rsmUndo
        AllocPageParamT allocPageParam = SeInitAllocPageParam(0, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
        ret = SeAllocPage((PageMgrT *)pageMgr, &allocPageParam, &tmp);
        EXPECT_EQ(STATUS_OK_INTER, ret);
    }
    return NULL;
}

static void *ThreadGetAndFlushPage(void *args)
{
    DbSetServerThreadFlag();
    BufPoolThreadArgs *arg = (BufPoolThreadArgs *)args;
    SeInstanceT *seIns = arg->seIns;
    uint32_t flushTimes = arg->flushTimes;
    PageIdT targetPageId = arg->targetPageId;
    for (uint32_t i = 0; i < flushTimes; i++) {
        uint8_t *page = NULL;
        StatusInter ret = SeGetPage((PageMgrT *)seIns->pageMgr, targetPageId, &page, ENTER_PAGE_NORMAL, true);
        EXPECT_EQ(STATUS_OK_INTER, ret);
        SeLeavePage((PageMgrT *)seIns->pageMgr, targetPageId, false);
        EXPECT_EQ(STATUS_OK_INTER, ret);
    }
    return NULL;
}

/**
 * 看护打开压缩区并发问题
 * 线程A：换出Page A --- | remove Bucket | rm LRU | -------------------------把PageA新版本保存到压缩区|
 * 线程B：-----------------------------------------| 拿到PageA，版本为老版本 | 发生脏读 |---------------信息不匹配core |
 */
TEST_F(UtCompressArea, CompressAreaConcurrency001)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    // alloc一定数量的页，且能触发换入换出
    uint32_t ratio = 2;
    uint32_t totalAllocNum = BUFPOOL_CAPACITY * ratio;
    PageIdT pagesList[totalAllocNum] = {0};
    int32_t spaceId = 0;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        // 持久化不使用rsmUndo
        AllocPageParamT allocPageParam = SeInitAllocPageParam((uint32_t)spaceId, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
        ret = SeAllocPage((PageMgrT *)seIns->pageMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    PageIdT targetPageId = pagesList[6];
    uint8_t *page = NULL;
    ret = SeGetPage((PageMgrT *)seIns->bufpoolMgr, targetPageId, &page, ENTER_PAGE_NORMAL, true);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    SeLeavePage((PageMgrT *)seIns->bufpoolMgr, targetPageId, false);

#define THREAD_NUM 10
    g_enableMutex = true;
    ASSERT_EQ(pthread_mutex_init(&g_spaceMutex, NULL), GMERR_OK);
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    int16_t threadAllocPages = 20;
    for (int i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].allocNumber = threadAllocPages;
        threadCtxs[i].targetPageId = targetPageId;
        threadCtxs[i].flushTimes = 5000;
    }

    uint32_t flushThreadTotal = 2;
    for (uint32_t i = 0; i < flushThreadTotal; i++) {
        ASSERT_EQ(GMERR_OK, pthread_create(&threads[i], NULL, ThreadGetAndFlushPage, &threadCtxs[i]));
    }
    for (uint32_t i = flushThreadTotal; i < THREAD_NUM; i++) {
        ASSERT_EQ(GMERR_OK, pthread_create(&threads[i], NULL, ThreadAllocPageLater, &threadCtxs[i]));
    }
    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(GMERR_OK, pthread_join(threads[i], NULL));
    }
#undef THREAD_NUM
    g_enableMutex = false;
    ASSERT_EQ(GMERR_OK, pthread_mutex_destroy(&g_spaceMutex));
}

/**
 * 看护打开压缩区并发问题
 * 线程A：换出Page A |remove Bucket&LRU|-DescInit:Desc->PageId = (0, 0), pageAddr(A,B)--------------------------------|
 * 线程B：-------------| GmcFlush |---save Desc->PageId = (0, 0)到压缩区----|--把Page(A，B)保存到了压缩区(0, 0)的位置
 */
TEST_F(UtCompressArea, CompressAreaConcurrency002)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    // alloc一定数量的页，且能触发换入换出
    uint32_t ratio = 2;
    uint32_t totalAllocNum = BUFPOOL_CAPACITY * ratio;
    PageIdT pagesList[totalAllocNum] = {0};
    int32_t spaceId = 0;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < totalAllocNum; i++) {
        // 持久化不使用rsmUndo
        AllocPageParamT allocPageParam = SeInitAllocPageParam((uint32_t)spaceId, 0, RSM_INVALID_LABEL_ID, NULL, NULL);
        ret = SeAllocPage((PageMgrT *)seIns->bufpoolMgr, &allocPageParam, &pagesList[i]);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    EXPECT_EQ(BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->hwm, BufpoolGetPageBufferMgr(seIns->bufpoolMgr)->capacity);
    PageIdT targetPageId = pagesList[6];
    uint8_t *page = NULL;
    ret = SeGetPage((PageMgrT *)seIns->bufpoolMgr, targetPageId, &page, ENTER_PAGE_NORMAL, true);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    SeLeavePage((PageMgrT *)seIns->bufpoolMgr, targetPageId, false);

#define THREAD_NUM 20
    g_enableMutex = true;
    ASSERT_EQ(pthread_mutex_init(&g_spaceMutex, NULL), GMERR_OK);
    pthread_t threads[THREAD_NUM] = {0};
    BufPoolThreadArgs threadCtxs[THREAD_NUM] = {0};
    int16_t threadAllocPages = 20;
    for (int i = 0; i < THREAD_NUM; i++) {
        threadCtxs[i].seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        threadCtxs[i].allocNumber = threadAllocPages;
        threadCtxs[i].targetPageId = targetPageId;
        threadCtxs[i].flushTimes = 50;
        ASSERT_EQ(GMERR_OK, pthread_create(&threads[i], NULL, ThreadAllocPageLater, &threadCtxs[i]));
    }

    for (int i = 0; i < THREAD_NUM; i++) {
        ASSERT_EQ(GMERR_OK, pthread_join(threads[i], NULL));
    }
#undef THREAD_NUM
    g_enableMutex = false;
    ASSERT_EQ(GMERR_OK, pthread_mutex_destroy(&g_spaceMutex));
}

TEST_F(UtCompressArea, CompressAreaConcurrency003)
{
    SeInstanceT *seIns = UtCompressArea::seIns;
    StatusInter ret = SeVirtualDiskFullCompressInit(seIns);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    uint32_t pageCntPerDev = SeGetPageCntPerDev(seIns);
    uint32_t devSize = SIZE_K(seIns->seConfig.deviceSize);
    uint32_t devNum = 5;
    uint8_t *device = (uint8_t *)malloc(devNum * devSize);
    uint8_t *cursor = NULL;
    PageIdT addr = SE_INVALID_PAGE_ADDR;
    for (uint32_t i = 0; i < devNum * pageCntPerDev; ++i) {
        cursor = device + SIZE_K(seIns->seConfig.pageSize) * i;
        addr = (PageIdT){i / pageCntPerDev, i % pageCntPerDev};
        SeInitPageHead(cursor, 0, SIZE_K(seIns->seConfig.pageSize), addr, true);
    }
    for (uint32_t i = 0; i < devNum; ++i) {
        ret = SeVirtualDiskImportDevice(seIns, i, device + i * devSize, devSize);
        ASSERT_EQ(STATUS_OK_INTER, ret);
    }
    ret = SeVirtualDiskFullCompressStart(seIns);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    for (uint32_t i = 0; i < devNum * pageCntPerDev; ++i) {
        addr = (PageIdT){i / pageCntPerDev, i % pageCntPerDev};
        ret = SeGetPage((PageMgrT *)seIns->bufpoolMgr, addr, &cursor, ENTER_PAGE_NORMAL, true);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ASSERT_EQ(0, ((PageHeadT *)cursor)->lsn);
        ((PageHeadT *)cursor)->lsn = 1;
        SeLeavePage((PageMgrT *)seIns->bufpoolMgr, addr, true);
    }

    ret = SeVirtualDiskFullCompressEnd(seIns, false);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    for (uint32_t i = 0; i < devNum * pageCntPerDev; ++i) {
        addr = (PageIdT){i / pageCntPerDev, i % pageCntPerDev};
        ret = SeGetPage((PageMgrT *)seIns->bufpoolMgr, addr, &cursor, ENTER_PAGE_NORMAL, false);
        ASSERT_EQ(STATUS_OK_INTER, ret);
        ASSERT_EQ(1, ((PageHeadT *)cursor)->lsn);
        SeLeavePage((PageMgrT *)seIns->bufpoolMgr, addr, false);
    }

    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    ASSERT_EQ(devNum * pageCntPerDev, mgr->stat.compressedPageCnt);
    for (uint32_t i = 0; i < devNum * pageCntPerDev; ++i) {
        ASSERT_TRUE(mgr->validMap[i]);
        ASSERT_NE(nullptr, mgr->node[i]);
        ASSERT_EQ(NODE_COMPRESSED, mgr->node[i]->state);
        ASSERT_EQ(1, ((PageHeadT *)mgr->node[i]->page)->lsn);
    }
    for (uint32_t i = devNum * pageCntPerDev; i < mgr->stat.capacity; ++i) {
        ASSERT_FALSE(mgr->validMap[i]);
        ASSERT_EQ(nullptr, mgr->node[i]);
    }
    free(device);
}
