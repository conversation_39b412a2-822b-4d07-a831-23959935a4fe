CREATE TABLE T_REPORT(log_time INTEGER,vsys_id INTEGER, ip_version INTEGER, src_ip inet, dst_ip inet,nat_src_ip INTEGER, nat_dst_ip INTEGER, in_intf_id INTEGER, out_intf_id INTEGER,src_loc_id INTEGER, dst_loc_id INTEGER, src_country_id INTEGER, dst_country_id INTEGER,user_id INTEGER, app_category_id INTEGER, sub_app_id INTEGER, app_id INTEGER,app_casa_flag INTEGER, app_casa_behavior INTEGER, sec_policy_id INTEGER, session_num INTEGER,consession_num INTEGER, send_byte INTEGER, receive_byte INTEGER, total_byte INTEGER) WITH (time_col = 'log_time', interval = '1 hour', compression = 'fast', cache_size = 49999);
