package main

import (
	"bufio"
	"flag"
	"fmt"
	"log"
	. "odbc_api"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unsafe"
)

/*
#cgo CFLAGS: -D_GNU_SOURCE -I../build/include

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include "sql.h"
#include "sqlext.h"
#include "col_desc.h"

typedef struct AppHdl {
	int32_t maxColId;
	int32_t rowSize;
	int32_t batchSize;
	int32_t colId;
	ColumnDescT *colDesc;
	int8_t *pos;
	int8_t *rows;
} AppHdlT;

AppHdlT *AppHdlInit(int32_t batchSize, ColumnDescT *colDesc, int32_t maxColId) {
	int32_t rowSize = 0;
	for (int32_t i = 0; i <= maxColId; i++) {
		rowSize += colDesc[i].colSize;
	}
	AppHdlT *hdl = (AppHdlT *)malloc(sizeof(AppHdlT) + rowSize * batchSize);
	if (hdl != NULL) {
		hdl->maxColId = maxColId;
		hdl->rowSize = rowSize;
		hdl->batchSize = batchSize;
		hdl->colId = 0;
		hdl->colDesc = colDesc;
		hdl->rows = (int8_t *)(hdl + 1);
		hdl->pos = hdl->rows;
	}
	return hdl;
}

int32_t AppHdlGetColType(AppHdlT *hdl, int32_t colId) {
	if (hdl == NULL) {
		return SQL_UNKNOWN_TYPE;
	}
	return hdl->colDesc[colId].colType;
}

int32_t AppHdlGetColSize(AppHdlT *hdl, int32_t colId) {
	if (hdl == NULL) {
		return -1;
	}
	return hdl->colDesc[colId].colSize;
}

void *AppHdlSetCurRow(AppHdlT *hdl, int32_t rowId) {
	if (hdl == NULL) {
		return NULL;
	}
	hdl->colId = 0;
	hdl->pos = hdl->rows + rowId * hdl->rowSize;
	return (void *)hdl->pos;
}

void *AppHdlGetNextCol(AppHdlT *hdl) {
	if (hdl == NULL || hdl->colId > hdl->maxColId) {
		return NULL;
	}
	hdl->pos += hdl->colDesc[hdl->colId].colSize;
	hdl->colId++;
	if (hdl->colId > hdl->maxColId) {
		hdl->colId = 0;
	}
	return (void *)hdl->pos;
}

*/
import "C"

type ColDescHdl interface {
	GetColDesc() (ret *C.ColumnDescT)
	GetMaxColId() (ret C.int32_t)
}

func AppHdlGetColType(hdl *C.AppHdlT, colId C.int32_t) C.int32_t {
	offset := uintptr(unsafe.Pointer(hdl.colDesc)) + uintptr(unsafe.Sizeof(C.ColumnDescT{})*uintptr(colId))
	colDesc := *(*C.ColumnDescT)(unsafe.Pointer(offset))
	return colDesc.colType
}

func AppHdlGetColSize(hdl *C.AppHdlT, colId C.int32_t) C.int32_t {
	offset := uintptr(unsafe.Pointer(hdl.colDesc)) + uintptr(unsafe.Sizeof(C.ColumnDescT{})*uintptr(colId))
	colDesc := *(*C.ColumnDescT)(unsafe.Pointer(offset))
	return colDesc.colSize
}

func AppHdlSetCurRow(hdl *C.AppHdlT, rowId C.int32_t) uintptr {
	offset := uintptr(unsafe.Pointer(hdl.rows)) + uintptr(uintptr(hdl.rowSize)*uintptr(rowId))
	hdl.pos = (*C.int8_t)(unsafe.Pointer(offset))
	hdl.colId = 0
	return offset
}

func AppHdlGetNextCol(hdl *C.AppHdlT) uintptr {
	if hdl.colId > hdl.maxColId {
		return uintptr(0)
	}
	offset := uintptr(unsafe.Pointer(hdl.colDesc)) + uintptr(unsafe.Sizeof(C.ColumnDescT{})*uintptr(hdl.colId))
	colDesc := *(*C.ColumnDescT)(unsafe.Pointer(offset))
	offset = uintptr(unsafe.Pointer(hdl.pos)) + uintptr(colDesc.colSize)
	hdl.pos = (*C.int8_t)(unsafe.Pointer(offset))
	hdl.colId++
	if hdl.colId > hdl.maxColId {
		hdl.colId = 0
	}
	return offset
}

// Flag vars
var (
	tableName       string
	fromFile        string
	fromExec        string
	workers         int
	batchSize       int
	reportingPeriod time.Duration
	verbose         bool
	rowCount        int64
	startTime       time.Time
)

type batch struct {
	rows []string
}

// Parse args
func init() {
	flag.StringVar(&tableName, "table", "", "Destination table for insertions")
	flag.StringVar(&fromFile, "file", "", "File to read data from")
	flag.StringVar(&fromExec, "exec", "", "Exec command to read data from")
	flag.IntVar(&batchSize, "batch-size", 10000, "Number of rows per insert")
	flag.IntVar(&workers, "workers", 1, "Number of parallel requests to make")
	flag.DurationVar(&reportingPeriod, "reporting-period", 0*time.Second, "Period to report insert stats; if 0s, intermediate results will not be reported")
	flag.BoolVar(&verbose, "verbose", false, "Print more information about inserting statistics")
	flag.Parse()
}

func main() {
	runtime.GOMAXPROCS(100)
	var scanner *bufio.Scanner
	if len(fromFile) > 0 {
		file, err := os.Open(fromFile)
		if err != nil {
			log.Fatal(err)
		}
		defer file.Close()

		scanner = bufio.NewScanner(file)
	} else if len(fromExec) > 0 {
		args := strings.Split(fromExec, " ")
		cmd := exec.Command(args[0], args[1:]...)
		cmdout, err := cmd.StdoutPipe()
		if err != nil {
			panic(err)
		}
		scanner = bufio.NewScanner(cmdout)
		err = cmd.Start()
		if err != nil {
			panic(err)
		}
	} else {
		scanner = bufio.NewScanner(os.Stdin)
	}

	var wg sync.WaitGroup
	batchChan := make(chan *batch, workers)

	// Generate INSERT workers
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go processBatches(&wg, batchChan)
	}
	startTime = time.Now()

	// Reporting thread
	if reportingPeriod > (0 * time.Second) {
		go report()
	}
	rowsRead := scan(batchSize, scanner, batchChan)
	close(batchChan)
	wg.Wait()
	end := time.Now()
	took := end.Sub(startTime)
	rowRate := float64(rowsRead) / float64(took.Seconds())

	res := fmt.Sprintf("INSERT %d", rowsRead)
	if verbose {
		res += fmt.Sprintf(", took %v with %d worker(s) (mean rate %f/sec)", took, workers, rowRate)
	}
	fmt.Println(res)
}

// report periodically prints the write rate in number of rows per second
func report() {
	prevTime := startTime
	prevRowCount := int64(0)

	for now := range time.NewTicker(reportingPeriod).C {
		rCount := atomic.LoadInt64(&rowCount)
		if rCount == 0 {
			startTime = time.Now()
			prevTime = startTime
			prevRowCount = int64(0)
		} else {
			took := now.Sub(prevTime)
			rowrate := float64(rCount-prevRowCount) / float64(took.Seconds())
			overallRowrate := float64(rCount) / float64(now.Sub(startTime).Seconds())
			fmt.Printf("at %s, row rate %f/sec (period), row rate %f/sec (overall), %E total rows\n", time.Now(), rowrate, overallRowrate, float64(rCount))
			prevRowCount = rCount
			prevTime = now
		}
	}
}

// scan reads lines from a bufio.Scanner, each which should be in CSV format
// with a delimiter specified by --split (comma by default)
func scan(itemsPerBatch int, scanner *bufio.Scanner, batchChan chan *batch) int64 {
	rows := make([]string, 0, itemsPerBatch)
	var linesRead int64

	for scanner.Scan() {
		linesRead++
		rows = append(rows, scanner.Text())
		if len(rows) >= itemsPerBatch { // dispatch to INSERT worker & reset
			batchChan <- &batch{rows}
			rows = make([]string, 0, itemsPerBatch)
		}
	}
	if err := scanner.Err(); err != nil {
		log.Fatalf("Error reading input: %s", err.Error())
	}
	// Finished reading input, make sure last batch goes out.
	if len(rows) > 0 {
		batchChan <- &batch{rows}
	}
	return linesRead
}

func GetColDescHdl() ColDescHdl {
	return new(CommonHdl)
}

// processBatches reads batches from C and writes them to the target server, while tracking stats on the write.
func processBatches(wg *sync.WaitGroup, C chan *batch) {
	var hEnv SQLHENV
	var hDbc SQLHDBC
	var hStmt SQLHSTMT

	ret := SQLAllocHandle(SQL_HANDLE_ENV, SQLHANDLE(SQL_NULL_HANDLE), (*SQLHANDLE)(&hEnv))
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLAllocHandle failed")
	}
	ret = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, SQLPOINTER(SQL_OV_ODBC3), 0)
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLSetEnvAttr failed")
	}
	ret = SQLAllocHandle(SQL_HANDLE_DBC, SQLHANDLE(hEnv), (*SQLHANDLE)(&hDbc))
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLAllocHandle failed")
	}
	ret = MOCK_SQLConnect(hDbc)
	if ret != SQL_SUCCESS {
		log.Fatalf("MOCK_SQLConnnect failed")
	}
	ret = SQLAllocHandle(SQL_HANDLE_STMT, SQLHANDLE(hDbc), (*SQLHANDLE)(&hStmt))
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLAllocHandle failed")
	}
	colDescHdl := GetColDescHdl()
	appHdl := C.AppHdlInit(C.int32_t(batchSize), colDescHdl.GetColDesc(), colDescHdl.GetMaxColId())
	if appHdl == nil {
		log.Fatalf("AppHdlInit failed")
	}
	ret = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_BIND_TYPE, SQLPOINTER(uintptr(appHdl.rowSize)), 0)
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLSetStmtAttr failed")
	}
	ret = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, SQLPOINTER(uintptr(batchSize)), 0)
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLSetStmtAttr failed")
	}
	targetValuePtr := SQLPOINTER(AppHdlSetCurRow(appHdl, 0))
	for i := 0; i <= int(appHdl.maxColId); i++ {
		ret = SQLBindCol(hStmt, SQLUSMALLINT(i+1), SQLSMALLINT(AppHdlGetColType(appHdl, C.int(i))),
			targetValuePtr, SQLLEN(AppHdlGetColSize(appHdl, C.int(i))), nil)
		if ret != SQL_SUCCESS {
			log.Fatalf("SQLBindCol failed")
		}
		targetValuePtr = SQLPOINTER(AppHdlGetNextCol(appHdl))
	}
	AppHdlSetCurRow(appHdl, 0)
	command := "SELECT * FROM " + tableName
	ret = SQLPrepare(hStmt, command, SQL_NTS)
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLPrepare failed for: " + command)
	}

	var colCount SQLSMALLINT
	ret = SQLNumResultCols(hStmt, &colCount)
	if ret != SQL_SUCCESS {
		log.Fatalf("SQLNumResultCols failed")
	}

	for batch := range C {
		for rowId, line := range batch.rows {
			if len(line) == 0 {
				continue
			}
			cols := strings.Split(line, ",")
			if len(cols) != int(colCount) {
				log.Fatalf("strings.Split column failed")
			}
			colPos := SQLPOINTER(AppHdlSetCurRow(appHdl, C.int(rowId)))
			for i := 0; i < int(colCount); i++ {
				switch AppHdlGetColType(appHdl, C.int(i)) {
				case C.SQL_C_SLONG:
					data, err := strconv.ParseInt(cols[i], 10, 32)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*int32)(colPos)) = int32(data)
				case C.SQL_C_ULONG:
					data, err := strconv.ParseUint(cols[i], 10, 32)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*uint32)(colPos)) = uint32(data)
				case C.SQL_C_SBIGINT:
					data, err := strconv.ParseInt(cols[i], 10, 64)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*int64)(colPos)) = int64(data)
				case C.SQL_C_SSHORT:
					data, err := strconv.ParseInt(cols[i], 10, 16)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*int16)(colPos)) = int16(data)
				case C.SQL_C_USHORT:
					data, err := strconv.ParseUint(cols[i], 10, 16)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*uint16)(colPos)) = uint16(data)
				case C.SQL_C_STINYINT:
					data, err := strconv.ParseInt(cols[i], 10, 8)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*int8)(colPos)) = int8(data)
				case C.SQL_C_UTINYINT:
					data, err := strconv.ParseUint(cols[i], 10, 8)
					if err != nil {
						log.Printf("colId: %d", i)
						log.Fatal(err)
					}
					*((*uint8)(colPos)) = uint8(data)
				case C.SQL_C_CHAR:
					colSize := AppHdlGetColSize(appHdl, C.int(i))
					dataFrom := StringToBytes(cols[i])
					if len(dataFrom) > int(colSize) {
						log.Fatalf("StringToBytes failed", len(dataFrom), int(colSize))
					}
					for j := 0; j < len(dataFrom); j++ {
						dataTo := (*int8)(unsafe.Pointer(uintptr(colPos) + uintptr(j)))
						*dataTo = int8(dataFrom[j])
					}
					dataTo := (*int8)(unsafe.Pointer(uintptr(colPos) + uintptr(len(dataFrom))))
					*dataTo = 0
				default:
					log.Fatalf("Unsupported column type", AppHdlGetColType(appHdl, C.int(i)))
				}
				colPos = SQLPOINTER(AppHdlGetNextCol(appHdl))
			}
		}

		if len(batch.rows) != batchSize {
			ret = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, SQLPOINTER(uintptr(len(batch.rows))), 0)
			if ret != SQL_SUCCESS {
				log.Fatalf("SQLSetStmtAttr failed")
			}
		}

		ret = SQLBulkOperations(hStmt, SQL_ADD)
		if ret != SQL_SUCCESS {
			log.Fatalf("SQLBulkOperations failed")
		}
		atomic.AddInt64(&rowCount, int64(len(batch.rows)))
	}

	SQLFreeHandle(SQL_HANDLE_STMT, SQLHANDLE(hStmt))
	SQLDisconnect(hDbc)
	SQLFreeHandle(SQL_HANDLE_DBC, SQLHANDLE(hDbc))
	SQLFreeHandle(SQL_HANDLE_ENV, SQLHANDLE(hEnv))

	wg.Done()
}
