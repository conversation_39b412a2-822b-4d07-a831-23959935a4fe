/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: nlog_batch_insert_nlog_traffic_mrg_no_id_name.c
 * Description: nlog batch insert
 * Create: 2024-1-25
 */
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <pthread.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include <string.h>
#include <float.h>
#include <limits.h>
#include <ctype.h>
#include "data_reader.h"
#include "nlog_schema.h"
#include "ts.h"
#include "cpu_affinity.h"

#define COL_NUM 25

// clang-format off
QdbTsColumnInfoT g_tmlTrfMrglogColumns[COL_NUM] = {
    {"log_time", SQL_C_SBIGINT, 0},
    {"vsys_id", SQL_C_USHORT, 0},
    {"ip_version", SQL_C_USHORT, 0},
    {"src_ip", SQL_C_ULONG, 0},
    {"dst_ip", SQL_C_ULONG, 0},
    {"nat_src_ip", SQL_C_SLONG, 0},
    {"nat_dst_ip", SQL_C_SLONG, 0},
    {"in_intf_id", SQL_C_USHORT, 0},
    {"out_intf_id", SQL_C_USHORT, 0},
    {"src_loc_id", SQL_C_SLONG, 0},
    {"dst_loc_id", SQL_C_SLONG, 0},
    {"src_country_id", SQL_C_USHORT, 0},
    {"dst_country_id", SQL_C_USHORT, 0},
    {"user_id", SQL_C_SLONG, 0},
    {"app_category_id", SQL_C_USHORT, 0},
    {"sub_app_id", SQL_C_USHORT, 0},
    {"app_id", SQL_C_USHORT, 0},
    {"app_casa_flag", SQL_C_USHORT, 0},
    {"app_casa_behavior", SQL_C_USHORT, 0},
    {"sec_policy_id", SQL_C_USHORT, 0},
    {"session_num", SQL_C_USHORT, 0},
    {"consession_num", SQL_C_SLONG, 0},
    {"send_byte", SQL_C_USHORT, 0},
    {"receive_byte", SQL_C_USHORT, 0},
    {"total_byte", SQL_C_SLONG, 0}};
// clang-format on

#define IP_VERSION 4
#define INTF_ID_CARD 33
#define APP_CATEGORY_ID_CARD 5
#define SUB_APP_ID_CARD 57
#define APP_ID_CARD 6087
#define SEC_POLICY_ID_CARD 4
#define SESSION_NUM_CARD 10000
#define CONSESSION_NUM_CARD 5000
#define MAX_PORT 65536

uint32_t SetRow(ColumnPushDataT *columns, int index, int plusSec)
{
    // avoid time consuming
    int randVal = rand();

    size_t colIdx = 0;
    columns[colIdx++].data.int64s[index] = plusSec;  // log_time

    columns[colIdx++].data.uint16s[index] = 1;
    columns[colIdx++].data.uint16s[index] = IP_VERSION;
    columns[colIdx++].data.uint32s[index] = GetFromDataReader(SRC_IP);
    columns[colIdx++].data.uint32s[index] = GetFromDataReader(DST_IP);
    columns[colIdx++].data.int32s[index] = 0;
    columns[colIdx++].data.int32s[index] = 0;

    columns[colIdx++].data.uint16s[index] = g_stdy29[randVal % INTF_ID_CARD].id;  // in_intf_id
    columns[colIdx++].data.uint16s[index] = g_stdy29[randVal % INTF_ID_CARD].id;  // out_intf_id
    columns[colIdx++].data.int32s[index] = 0;
    columns[colIdx++].data.int32s[index] = 0;

    columns[colIdx++].data.uint16s[index] = 0;
    columns[colIdx++].data.uint16s[index] = 0;
    columns[colIdx++].data.int32s[index] = 0;  // user id

    columns[colIdx++].data.uint16s[index] = g_stdy1[randVal % APP_CATEGORY_ID_CARD].id;  // app_category_id
    columns[colIdx++].data.uint16s[index] = g_stdy2[randVal % SUB_APP_ID_CARD].id;       // sub_app_id
    columns[colIdx++].data.uint16s[index] = randVal % APP_ID_CARD;                       // app_id
    columns[colIdx++].data.uint16s[index] = 0;
    columns[colIdx++].data.uint16s[index] = 0;
    columns[colIdx++].data.uint16s[index] = g_stdy15[randVal % SEC_POLICY_ID_CARD].id;  // sec_policy_id
    columns[colIdx++].data.uint16s[index] = randVal % SESSION_NUM_CARD;                 // session_num
    columns[colIdx++].data.int32s[index] = randVal % CONSESSION_NUM_CARD;               // consession_num

    int sendByte = randVal % MAX_PORT;
    int receiveByte = randVal % MAX_PORT;
    columns[colIdx++].data.uint16s[index] = sendByte;
    columns[colIdx++].data.uint16s[index] = receiveByte;
    columns[colIdx++].data.int32s[index] = sendByte + receiveByte;
#ifdef CSV
    for (int i = 0; i < colIdx; i++) {
        PrintData(&columns[i], index);
        printf("%s", (i == colIdx - 1) ? "\n" : ",");
    }
#endif
    return 0;
}

#define MAX_ARG_NUM 6

int main(int argc, char *argv[])
{
    if (argc != MAX_ARG_NUM) {
        printf("Usage: ./nlog_batch_insert_T_REPORT totalNum batchSize startTick enableIpsCache tableName\n");
        return 0;
    }

    // connect to database
    int totalNum = atoi(argv[1]);
    int batchSize = atoi(argv[2]);
    int count = totalNum / batchSize;
    // for perf benchmark, please set startTick to 1702546200 ("2023-12-21 17:30:00")
    int plusSec = atoi(argv[3]);
    int enableIpsCache = atoi(argv[4]);
    char *tableName = argv[5];

    SQLHENV hEnv = SQL_NULL_HENV;
    SQLHDBC hDbc = SQL_NULL_HDBC;
    SQLHSTMT hStmt = SQL_NULL_HSTMT;

    SQLRETURN ret = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &hEnv);
    if (ret != SQL_SUCCESS) {
        printf("SQLAllocHandle failed\n");
        return ret;
    }
    ret = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, &hDbc);
    if (ret != SQL_SUCCESS) {
        printf("SQLAllocHandle failed\n");
        return ret;
    }
#ifdef GMDB
    FILE *channelFd = fopen("./channel.cfg", "r");
    char channel[MAX_LEN] = "usocket:/run/verona/unix_emserver";
    if (channelFd != NULL) {
        if (fgets(channel, MAX_LEN, channelFd) != NULL) {
            channel[strlen(channel) - 1] = 0;
        }
        (void)fclose(channelFd);
    }
    ret = SQLConnect(hDbc, channel, SQL_NTS, "", 0, "user", 0);
    if (ret != SQL_SUCCESS) {
        printf("SQLConnect failed\n");
        return ret;
    }
#else
    ret = SQLDriverConnect(hDbc, NULL, "Driver=SQLite3;Database=test.db", SQL_NTS, NULL, 0, NULL, SQL_DRIVER_NOPROMPT);
    if (ret != SQL_SUCCESS) {
        printf("SQLDriverConnect failed\n");
        return ret;
    }
#endif
    ret = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, &hStmt);
    if (ret != SQL_SUCCESS) {
        printf("SQLAllocHandle failed\n");
        return ret;
    }

    // initialize push table
    QdbExpBatchPushTableT table;
    (void)memset_s(&table, sizeof(QdbExpBatchPushTableT), 0, sizeof(QdbExpBatchPushTableT));
    table.name = strdup(tableName);
    /// Initialize table data
    table.data.rowCount = batchSize;
    table.data.colCount = COL_NUM;

    ColumnPushDataT *columns = malloc(sizeof(ColumnPushDataT) * table.data.colCount);
    if (columns == NULL) {
        printf("malloc failed\n");
        return 0;
    }
    InitializeColumns(columns, &g_tmlTrfMrglogColumns[0], batchSize, COL_NUM);
    table.data.columns = (QdbExpBatchPushColumnT *)columns;

#ifndef CSV
    ret = BindColumns(hStmt, &table);
    if (ret != SQL_SUCCESS) {
        char errmsg[100];
        SQLError(hEnv, hDbc, hStmt, NULL, NULL, errmsg, sizeof(errmsg), NULL);
        /* Assume that print_error is defined */
        printf("%s\n", errmsg);
        return ret;
    }
#endif

    if (!PrepareDataReader(totalNum, enableIpsCache)) {
        printf("PrepareDataReader failed\n");
        return 0;
    }

    struct timeval tmBegin, tmEnd;
    gettimeofday(&tmBegin, NULL);

    // struct timeval batchTmBegin, batchTmEnd;
    for (int i = 0; i < count; i++) {
        for (int j = 0, k = 0; j < batchSize; j++, k++) {
            const int rowsPerSec = 4000;
            if (k == rowsPerSec) {
                plusSec++;
                k = 0;
            }
            ret = SetRow(columns, j, plusSec);
            if (ret != 0) {
                return ret;
            }
        }
        ret = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)(int64_t)batchSize, 0);
        if (ret != SQL_SUCCESS) {
            printf("SQLSetStmtAttr failed\n");
            return ret;
        }
        if (!SchedSetAffinityCPU0()) {
            return SQL_ERROR;
        }
#ifndef CSV
        ret = SQLBulkOperations(hStmt, SQL_ADD);
        if (ret != SQL_SUCCESS) {
            printf("SQLBulkOperations failed\n");
            goto EXIT;
        }
#endif
        if (!SchedSetAffinityNonCPU0()) {
            goto EXIT;
        }
    }

EXIT:
    SQLFreeHandle(SQL_HANDLE_STMT, (SQLHANDLE)hStmt);
    SQLDisconnect(hDbc);
    SQLFreeHandle(SQL_HANDLE_DBC, (SQLHANDLE)hDbc);
    SQLFreeHandle(SQL_HANDLE_ENV, (SQLHANDLE)hEnv);

    CleanDataReader();
    gettimeofday(&tmEnd, NULL);

    long long totalMicrosecond, opsSecond;
    const int usecPerSec = 1000000;
    totalMicrosecond = (tmEnd.tv_sec - tmBegin.tv_sec) * usecPerSec + (tmEnd.tv_usec - tmBegin.tv_usec);  // 微秒数
    opsSecond = ((double)totalNum / totalMicrosecond) * usecPerSec;

    // get ops only to be received by shell
    const int kOps = 1000;
#ifndef CSV
    printf("%ld\n", opsSecond / kOps);
#endif
    return 0;
}
