package main

/*
#cgo CFLAGS: -D_GNU_SOURCE -I../build/include

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include "sql.h"
#include "sqlext.h"
#include "col_desc.h"

ColumnDescT g_T_COMMON[] = {
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256},
	{"common", SQL_C_CHAR, 256}
};

int32_t g_T_COMMON_MAX = sizeof(g_T_COMMON) / sizeof(g_T_COMMON[0]) - 1;

*/
import "C"

type CommonHdl struct {
}

func (hdl *CommonHdl) GetColDesc() (ret *C.ColumnDescT) {
	return &C.g_T_COMMON[0]
}

func (hdl *CommonHdl) GetMaxColId() (ret C.int32_t) {
	return C.g_T_COMMON_MAX
}
