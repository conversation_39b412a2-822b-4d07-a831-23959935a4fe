/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for ts query
 * Author: lihainuo
 * Create: 2023/9/19
 */

#include <gtest/gtest.h>
#include <vector>
#include <string>
#include <unordered_map>
#include <iostream>
#include "adpt_string.h"
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc_sql.h"
#include "clt_ts.h"
#include "adpt_printf.h"
#include "gmc_sysview.h"
#include "st_common.h"

using namespace std;

static char *g_cfgPersist = "ts/gmserver_ts.ini";

class StTsQuerySysviewGmc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(100);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(100);
    }
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

static void InitConnAndStmt(GmcConnT **conn, GmcStmtT **stmt)
{
    static GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(*conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static void UnInitConnAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    Status ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

static Status BulkInsert(GmcStmtT *stmt, char *tableName, int64_t count, int columnSize, ...)
{
    Status ret;
    va_list varList;
    int64_t *columnArray[columnSize + 1];
    columnArray[0] = nullptr;

    va_start(varList, columnSize);
    for (int i = 0; i < columnSize; i++) {
        columnArray[i] = va_arg(varList, int64_t *);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);

    for (int n = 0; n < columnSize; n++) {
        ret = GmcBindCol(stmt, n, (GmcDataTypeE)DB_DATATYPE_INT64, columnArray[n], 0, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageTableName)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table 'V$STORAGE_DISK_USAGE_TABLE'(id integer, time integer, num integer) "
                                   "with (time_col = 'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table 'v$DISK_USAGE_TABLE'(id integer, time integer, num integer) with "
                                   "(time_col = 'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table 'V$STORAGE_DISK_USAGE_TABLE';";
    char dropCommand2[commandLen] = "drop table 'v$DISK_USAGE_TABLE';";
    char qryCommand1[200] = "select * from 'V$STORAGE_DISK_USAGE';";
    char qryCommand2[200] = "select * from 'v$storage_disk_usage';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand1, 200);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, 200);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, value);

    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageOfTable)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char qryCommand1[commandLen] = "select * from 'V$STORAGE_DISK_USAGE';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
        num[i] = 50000 - i;
    }
    ret = BulkInsert(stmt, "testdb", count, 3, id, time, num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)2, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageByWhere)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char qryCommand1[commandLen] = "select * from 'V$STORAGE_DISK_USAGE' where table_name = 'testdb';";
    char qryCommand2[commandLen] = "select * from 'V$STORAGE_DISK_USAGE' where table_name = 'testdb' or 'testdba';";
    char qryCommand3[commandLen] = "select * from 'V$STORAGE_DISK_USAGE' where 'testdb';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
        num[i] = 50000 - i;
    }
    ret = BulkInsert(stmt, "testdb", count, 3, id, time, num);
    EXPECT_EQ(GMERR_OK, ret);
    // wrong expr
    ret = GmcExecDirect(stmt, qryCommand2, commandLen);
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    ret = GmcExecDirect(stmt, qryCommand3, commandLen);
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)1, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageCols)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char qryCommand1[200] = "select * from 'V$STORAGE_DISK_USAGE';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
        num[i] = 50000 - i;
    }
    ret = BulkInsert(stmt, "testdb", count, 3, id, time, num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(value, (uint32_t)7);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)2, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageColsByWhere)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char qryCommand1[200] = "select * FROM 'V$STORAGE_DISK_USAGE' where table_name = 'testdb';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand1, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, 200);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
        num[i] = 50000 - i;
    }
    ret = BulkInsert(stmt, "testdb", count, 3, id, time, num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)1, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageObTableName)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] = "select * from 'V$STORAGE_DISK_USAGE';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)3, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)3, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, DISABLED_QueryDiskUsageGbDiskLimit)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] = "select disk_limit, disk_usage from 'V$STORAGE_DISK_USAGE';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("sum(DISK_USAGE):         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)3, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageSumWhere)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] = "select * from 'V$STORAGE_DISK_USAGE' where disk_limit=52428800;";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("sum(DISK_USAGE):         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)2, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageLimit)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] = "select * from 'V$STORAGE_DISK_USAGE' limit 2 offset 1;";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)2, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereAnd)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] =
        "select * from 'V$STORAGE_DISK_USAGE' where disk_limit < 104857600 and table_name = 'testdb';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)1, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereOr)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] =
        "select * from 'V$STORAGE_DISK_USAGE' where table_name = 'testdb' or table_name = 'testdba';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)2, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereAndOr)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] = "select * from 'V$STORAGE_DISK_USAGE' where (table_name = 'testdb' or table_name = "
                                   "'testdba') and disk_limit = 104857600;";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tableNameLen = 128;
    char tableName[tableNameLen] = {0};
    int64_t diskLimit = 0;
    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tableNameLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tableName, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("TABLE_NAME:         %30s\n", tableName);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &diskLimit, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_LIMIT:         %30lu\n", diskLimit);
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("DISK_USAGE:         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)1, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageSumTableGroup)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = "create table testdb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '50 MB');";
    char ddlCommand2[commandLen] = "create table testdba(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '100 MB');";
    char ddlCommand3[commandLen] = "create table testdbb(id integer, time integer, num integer) with (time_col = "
                                   "'time', interval= '1 hour', disk_limit = '20 MB');";
    char dropCommand1[commandLen] = "drop table testdb;";
    char dropCommand2[commandLen] = "drop table testdba;";
    char dropCommand3[commandLen] = "drop table testdbb;";
    char qryCommand1[commandLen] = "select * from 'v$storage_disk_usage' where table_name = 'testdb' or "
                                   "table_name = 'testdba' or table_name = 'testdbb';";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, ddlCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;

    int64_t diskUsage = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)3, value);
    uint32_t i = 0;
    eof = false;
    while (true && i < 10) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &diskUsage, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("sum(DISK_USAGE):         %30lu\n", diskUsage);
        i++;
    }
    EXPECT_EQ((uint32_t)3, i);

    ret = GmcExecDirect(stmt, dropCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand2, commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand3, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, DISABLED_QueryDiskUsageNotTsdb)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char qryCommand1[commandLen] = "select * from 'v$storage_disk_usage';";

    char labelSchema[512] =
        "[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\" : false , \"default\" : 1},{\"name\":\"F2\", "
        "\"type\":\"int\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    char configJson[128] = "{\"max_record_count\" : 10000}";
    char labelName[128] = "T39";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, labelSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand1, commandLen);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, value);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereTableName)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = {0};
    char dropCommand1[commandLen] = {0};
    const uint32_t tblNameLen = 10;
    char tableName[tblNameLen] = {0};

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    for (uint32_t i = 0; i < 200; i++) {
        (void)snprintf_s(ddlCommand1, commandLen, commandLen - 1,
            "create table testdb%" PRIu32
            "(id integer, time integer, num integer) with (time_col = 'time', interval= '1 hour');",
            i);
        ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t count = 50000;
        int64_t id[count] = {0};
        int64_t time[count] = {0};
        int64_t num[count] = {0};
        for (uint32_t j = 0; j < count; j++) {
            id[j] = j + 1;
            time[j] = 1695042000 + j;
            num[j] = 50000 - j;
        }
        (void)snprintf_s(tableName, tblNameLen, tblNameLen - 1, "testdb%" PRIu32, i);
        ret = BulkInsert(stmt, tableName, count, 3, id, time, num);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint64_t startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where table_name = 'testdb39' or table_name = "
           "'testdb29';\"");
    uint64_t benchmarkExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb29';\"");
    uint64_t totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    EXPECT_LT(totalExecuteTime, benchmarkExecuteTime);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where table_name = 'testdb39';\"");
    totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    EXPECT_LT(totalExecuteTime, benchmarkExecuteTime);

    ret = GmcExecDirect(stmt, "select * from 'V$STORAGE_DISK_USAGE' where table_name = 'testdb29';", commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tblResLen = 128;
    char tblRes[tblResLen] = {0};
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tblResLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tblRes, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(tblRes, "testdb29");
        cnt++;
    }
    EXPECT_EQ((uint32_t)1, cnt);

    ret = GmcExecDirect(stmt, "select * from 'V$PHY_TBL_DISK_USAGE' where table_name = 'testdb39';", commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)10, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)14, value);
    cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        EXPECT_EQ(tblResLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, tblRes, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(tblRes, "testdb39");
        cnt++;
    }
    EXPECT_EQ((uint32_t)14, cnt);

    for (uint32_t i = 0; i < 200; i++) {
        (void)snprintf_s(dropCommand1, commandLen, commandLen - 1, "drop table testdb%" PRIu32 ";", i);
        ret = GmcExecDirect(stmt, dropCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereTableNameAnd1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = {0};
    char dropCommand1[commandLen] = {0};
    const uint32_t tblNameLen = 10;
    char tableName[tblNameLen] = {0};

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(ddlCommand1, commandLen, commandLen - 1,
            "create table testdb%" PRIu32
            "(id integer, time integer, num integer) with (time_col = 'time', interval= '1 hour');",
            i);
        ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t count = 50000;
        int64_t id[count] = {0};
        int64_t time[count] = {0};
        int64_t num[count] = {0};
        for (uint32_t j = 0; j < count; j++) {
            id[j] = j + 1;
            time[j] = 1695042000 + j;
            num[j] = 50000 - j;
        }
        (void)snprintf_s(tableName, tblNameLen, tblNameLen - 1, "testdb%" PRIu32, i);
        ret = BulkInsert(stmt, tableName, count, 3, id, time, num);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint64_t startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb39' and disk_limit = "
           "0;\"");
    uint64_t totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb39' and disk_limit = "
           "0 or table_name = 'testdb39' and disk_limit = 0;\"");
    uint64_t benchmarkExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime);
    EXPECT_LT(totalExecuteTime, benchmarkExecuteTime);

    ret = GmcExecDirect(
        stmt, "select * from 'V$STORAGE_DISK_USAGE' where table_name = 'testdb39' and disk_limit = 0;", commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tblResLen = 128;
    char tblRes[tblResLen] = {0};
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tblResLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tblRes, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(tblRes, "testdb39");
        cnt++;
    }
    EXPECT_EQ((uint32_t)1, cnt);

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(dropCommand1, commandLen, commandLen - 1, "drop table testdb%" PRIu32 ";", i);
        ret = GmcExecDirect(stmt, dropCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereTableNameAnd2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = {0};
    char dropCommand1[commandLen] = {0};
    const uint32_t tblNameLen = 10;
    char tableName[tblNameLen] = {0};

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(ddlCommand1, commandLen, commandLen - 1,
            "create table testdb%" PRIu32
            "(id integer, time integer, num integer) with (time_col = 'time', interval= '1 hour');",
            i);
        ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t count = 50000;
        int64_t id[count] = {0};
        int64_t time[count] = {0};
        int64_t num[count] = {0};
        for (uint32_t j = 0; j < count; j++) {
            id[j] = j + 1;
            time[j] = 1695042000 + j;
            num[j] = 50000 - j;
        }
        (void)snprintf_s(tableName, tblNameLen, tblNameLen - 1, "testdb%" PRIu32, i);
        ret = BulkInsert(stmt, tableName, count, 3, id, time, num);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint64_t startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where table_name = 'testdb29' and "
           "UPPER_BOUND = 1695049200;\"");
    uint64_t totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where table_name = 'testdb29' or "
           "UPPER_BOUND = 1695049200;\"");
    uint64_t benchmarkExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime);
    EXPECT_LT(totalExecuteTime, benchmarkExecuteTime);

    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tblResLen = 128;
    char tblRes[tblResLen] = {0};
    ret = GmcExecDirect(stmt,
        "select * from 'V$PHY_TBL_DISK_USAGE' where table_name = 'testdb29' and upper_bound = 1695049200;", commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)10, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1, value);
    uint32_t cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        EXPECT_EQ(tblResLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, tblRes, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(tblRes, "testdb29");
        cnt++;
    }
    EXPECT_EQ((uint32_t)1, cnt);

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(dropCommand1, commandLen, commandLen - 1, "drop table testdb%" PRIu32 ";", i);
        ret = GmcExecDirect(stmt, dropCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereTableNameOr)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = {0};
    char dropCommand1[commandLen] = {0};
    const uint32_t tblNameLen = 10;
    char tableName[tblNameLen] = {0};

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(ddlCommand1, commandLen, commandLen - 1,
            "create table testdb%" PRIu32
            "(id integer, time integer, num integer) with (time_col = 'time', interval= '1 hour');",
            i);
        ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t count = 50000;
        int64_t id[count] = {0};
        int64_t time[count] = {0};
        int64_t num[count] = {0};
        for (uint32_t j = 0; j < count; j++) {
            id[j] = j + 1;
            time[j] = 1695042000 + j;
            num[j] = 50000 - j;
        }
        (void)snprintf_s(tableName, tblNameLen, tblNameLen - 1, "testdb%" PRIu32, i);
        ret = BulkInsert(stmt, tableName, count, 3, id, time, num);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint64_t startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb39' and table_name = "
           "'testdb40' order by table_name;\"");
    uint64_t benchmarkExecuteTime1 = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime1);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where table_name = 'testdb49' and "
           "table_name = 'testdb10' order by table_name;\"");
    uint64_t benchmarkExecuteTime2 = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime2);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb39' or table_name = "
           "'testdb40' order by table_name;\"");
    uint64_t totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    EXPECT_GT(totalExecuteTime, benchmarkExecuteTime1);  // 该场景未优化，性能较差

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where table_name = 'testdb49' or "
           "table_name = 'testdb10' order by table_name;\"");
    totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    EXPECT_GT(totalExecuteTime, benchmarkExecuteTime2);  // 该场景未优化，性能较差

    ret = GmcExecDirect(stmt,
        "select * from 'V$STORAGE_DISK_USAGE' where table_name = 'testdb39' or table_name = 'testdb40' order by "
        "table_name;",
        commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    bool eof = false;
    uint32_t size = 0;
    bool isNull = false;
    // SQL_NAME_LEN_MAX
    const uint32_t tblResLen = 128;
    char tblRes[tblResLen] = {0};
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)7, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)2, value);
    uint32_t cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(tblResLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, tblRes, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt == 0) {
            EXPECT_STREQ(tblRes, "testdb39");
        } else {
            EXPECT_STREQ(tblRes, "testdb40");
        }
        cnt++;
    }
    EXPECT_EQ((uint32_t)2, cnt);

    ret = GmcExecDirect(stmt,
        "select * from 'V$PHY_TBL_DISK_USAGE' where table_name = 'testdb49' or table_name = 'testdb10' "
        "order by table_name;",
        commandLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)10, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)28, value);
    cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        EXPECT_EQ(tblResLen, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, tblRes, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt < 14) {
            EXPECT_STREQ(tblRes, "testdb10");
        } else {
            EXPECT_STREQ(tblRes, "testdb49");
        }
        cnt++;
    }
    EXPECT_EQ((uint32_t)28, cnt);

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(dropCommand1, commandLen, commandLen - 1, "drop table testdb%" PRIu32 ";", i);
        ret = GmcExecDirect(stmt, dropCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQuerySysviewGmc, QueryDiskUsageWhereTableNameWithOtherFilter)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    const uint32_t commandLen = 200;
    char ddlCommand1[commandLen] = {0};
    char dropCommand1[commandLen] = {0};
    const uint32_t tblNameLen = 10;
    char tableName[tblNameLen] = {0};

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(ddlCommand1, commandLen, commandLen - 1,
            "create table testdb%" PRIu32
            "(id integer, time integer, num integer) with (time_col = 'time', interval= '1 hour');",
            i);
        ret = GmcExecDirect(stmt, ddlCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t count = 50000;
        int64_t id[count] = {0};
        int64_t time[count] = {0};
        int64_t num[count] = {0};
        for (uint32_t j = 0; j < count; j++) {
            id[j] = j + 1;
            time[j] = 1695042000 + j;
            num[j] = 50000 - j;
        }
        (void)snprintf_s(tableName, tblNameLen, tblNameLen - 1, "testdb%" PRIu32, i);
        ret = BulkInsert(stmt, tableName, count, 3, id, time, num);
        EXPECT_EQ(GMERR_OK, ret);
    }

    uint64_t startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb39' and table_name = "
           "'testdb40' and disk_usage >= 4301680;\"");
    uint64_t benchmarkExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where (table_name = 'testdb39' or table_name = "
           "'testdb40') and disk_usage >= 4301680;\"");
    uint64_t totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    EXPECT_GT(totalExecuteTime, benchmarkExecuteTime);  // 该场景未优化，性能较差

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where (table_name = 'testdb49' and "
           "table_name = 'testdb40') and row_cnt <= 50000;\"");
    benchmarkExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Benchmark query time:%" PRIu64 " ms.\n", benchmarkExecuteTime);

    startTime = DbRdtsc();
    system("gmsysview_ts -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where (table_name = 'testdb49' or "
           "table_name = 'testdb40') or row_cnt <= 50000;\"");
    totalExecuteTime = DbToMseconds(DbRdtsc() - startTime);
    printf("Query time:%" PRIu64 " ms.\n", totalExecuteTime);
    EXPECT_GT(totalExecuteTime, benchmarkExecuteTime);  // 该场景未优化，性能较差

    for (uint32_t i = 0; i < 50; i++) {
        (void)snprintf_s(dropCommand1, commandLen, commandLen - 1, "drop table testdb%" PRIu32 ";", i);
        ret = GmcExecDirect(stmt, dropCommand1, commandLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    UnInitConnAndStmt(conn, stmt);
}
