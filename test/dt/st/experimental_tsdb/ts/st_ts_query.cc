/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for ts query
 * Author: lihainuo
 * Create: 2023/9/19
 */

#include <gtest/gtest.h>
#include "adpt_string.h"
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "odbc_ts.h"

const static char *g_cfgPersist = "ts/gmserver_ts.ini";

class StTsQuery : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(100);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(100);
    }
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

TEST_F(StTsQuery, QueryOpSimple1)
{
    SQLHENV hEnv = SQL_NULL_HENV;
    SQLHDBC hDbc = SQL_NULL_HDBC;
    SQLHSTMT hStmt = SQL_NULL_HSTMT;
    SQLRETURN RETCODE = SQL_SUCCESS;
    SQLCHAR ddlCommand[200] =
        "create table tablequery(age integer, id integer, worktime integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    SQLCHAR qryCommand1[40] = "select worktime from tablequery;";
    SQLCHAR qryCommand2[50] = "select age from tablequery where age > 29;";
    SQLCHAR qryCommand3[70] = "select * from tablequery where id >= 2 and age = 30;";
    SQLCHAR qryCommand4[70] = "select age, worktime from tablequery;";
    SQLINTEGER rowNum = 3;
    int64_t id[] = {1, 2, 4};
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    SQLCHAR stmtText[30] = " select   * from   tablequery";  // multi space check
    StatementHandleT *stmt = NULL;

    RETCODE = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, (void *)SQL_OV_ODBC3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, &hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLConnect(hDbc, (SQLCHAR *)"usocket:/run/verona/unix_emserver", SQL_NTS, (SQLCHAR *)"usr", 0, NULL, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, &hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    stmt = (StatementHandleT *)hStmt;
    EXPECT_EQ(stmt->status, STMT_STATUS_INIT);

    RETCODE = SQLExecDirect(hStmt, ddlCommand, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)rowNum, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, id, sizeof(id[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, age, sizeof(age[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, worktime, sizeof(worktime[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    // prepare twice to test if it can be reused
    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_PREPARE);

    SQLSMALLINT numResCol;
    RETCODE = SQLNumResultCols(hStmt, &numResCol);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(numResCol, 3);

    RETCODE = SQLBulkOperations(hStmt, SQL_ADD);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BULKOP);

    int64_t idRes = 0;
    int64_t ageRes = 0;
    int64_t worktimeRes = 0;

    // cmd 1
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &worktimeRes, sizeof(worktimeRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand1, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    uint32_t i = 0;
    while ((RETCODE = SQLFetch(hStmt)) == SQL_SUCCESS) {
        EXPECT_EQ(worktimeRes, worktime[i]);
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    // cmd 2
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand2, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    i = 0;
    while ((RETCODE = SQLFetch(hStmt)) == SQL_SUCCESS) {
        EXPECT_EQ(ageRes, age[1]);
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    // cmd 3
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &idRes, sizeof(idRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, &worktimeRes, sizeof(worktimeRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLExecDirect(hStmt, qryCommand3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    RETCODE = SQLFetch(hStmt);
    EXPECT_EQ(ageRes, age[1]);
    EXPECT_EQ(idRes, id[1]);
    EXPECT_EQ(worktimeRes, worktime[1]);
    EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);

    // cmd 4
    SQLCHAR worktimeResChr[7];
    SQLCHAR worktimeChr[][21] = {"24", "24", "25"};
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_CHAR, worktimeResChr, 21, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand4, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    i = 0;
    while ((RETCODE = SQLFetch(hStmt)) == SQL_SUCCESS) {
        EXPECT_EQ(ageRes, age[i]);
        EXPECT_EQ(DbStrCmp((const char *)worktimeResChr, (const char *)worktimeChr[i], true), 0);
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    RETCODE = SQLFreeHandle(SQL_HANDLE_STMT, hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLDisconnect(hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_DBC, hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_ENV, hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
}

static void CreateTableAndBulkInsert(SQLHSTMT hStmt)
{
    StatementHandleT *stmt = (StatementHandleT *)hStmt;
    SQLCHAR ddlCommand[200] = "create table tablequery2(name char(10), age integer, id integer, worktime integer,"
                              " salary integer) with (time_col = 'worktime', interval = '1 hour',"
                              " compression = 'fast(rapidlz)', ttl = '3 hours');";
    SQLINTEGER rowNum = 6;
    SQLCHAR name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, 34};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};
    SQLCHAR stmtText[35] = " select   * from   tablequery2";  // multi space check
    SQLRETURN RETCODE = SQLExecDirect(hStmt, ddlCommand, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)rowNum, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, id, sizeof(id[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, age, sizeof(age[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 4, SQL_C_SBIGINT, worktime, sizeof(worktime[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 5, SQL_C_SBIGINT, salary, sizeof(salary[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_CHAR, name, 10, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_PREPARE);

    SQLSMALLINT numResCol;
    RETCODE = SQLNumResultCols(hStmt, &numResCol);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(numResCol, 5);

    RETCODE = SQLBulkOperations(hStmt, SQL_ADD);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BULKOP);
}

static void ExecQueryCmd(SQLHSTMT hStmt)
{
    SQLCHAR qryCommand[150] = "select name, id, worktime, salary from tablequery2 "
                              "where worktime <= 12 and salary <= 30000;";
    StatementHandleT *stmt = (StatementHandleT *)hStmt;
    SQLRETURN RETCODE = SQLExecDirect(hStmt, qryCommand, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);
}

TEST_F(StTsQuery, QueryOpSimple2)
{
    SQLHENV hEnv = SQL_NULL_HENV;
    SQLHDBC hDbc = SQL_NULL_HDBC;
    SQLHSTMT hStmt = SQL_NULL_HSTMT;
    SQLRETURN RETCODE = SQL_SUCCESS;

    StatementHandleT *stmt = NULL;

    RETCODE = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, (void *)SQL_OV_ODBC3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, &hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLConnect(hDbc, (SQLCHAR *)"usocket:/run/verona/unix_emserver", SQL_NTS, (SQLCHAR *)"usr", 0, NULL, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, &hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    stmt = (StatementHandleT *)hStmt;
    EXPECT_EQ(stmt->status, STMT_STATUS_INIT);

    CreateTableAndBulkInsert(hStmt);

    int64_t idRes = 0;
    int8_t worktimeRes = 0;
    SQLCHAR nameRes[10] = {0};
    SQLCHAR salaryRes[21] = {0};

    // cmd
    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &idRes, sizeof(idRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_STINYINT, &worktimeRes, sizeof(worktimeRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 4, SQL_C_CHAR, salaryRes, 21, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_CHAR, nameRes, 10, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    ExecQueryCmd(hStmt);

    uint32_t i = 0;
    while ((RETCODE = SQLFetch(hStmt)) && RETCODE == SQL_SUCCESS_WITH_INFO) {
        switch (i) {
            case 0: {
                EXPECT_EQ(idRes, 9);
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_EQ(DbStrCmp((const char *)salaryRes, "30000", true), 0);
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "olivia", true), 0);
                break;
            }
            case 1: {
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(DbStrCmp((const char *)salaryRes, "10000", true), 0);
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "lucy", true), 0);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    idRes = 0;
    worktimeRes = 0;
    nameRes[10] = {0};

    // num of bind cols smaller than table cols
    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &idRes, sizeof(idRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_STINYINT, &worktimeRes, sizeof(worktimeRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_CHAR, nameRes, 10, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    ExecQueryCmd(hStmt);

    i = 0;
    while ((RETCODE = SQLFetch(hStmt)) && RETCODE == SQL_SUCCESS_WITH_INFO) {
        switch (i) {
            case 0: {
                EXPECT_EQ(idRes, 9);
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "olivia", true), 0);
                break;
            }
            case 1: {
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "lucy", true), 0);
                break;
            }
            default:
                EXPECT_EQ(0, 1);
        }
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    RETCODE = SQLFreeHandle(SQL_HANDLE_STMT, hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLDisconnect(hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_DBC, hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_ENV, hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
}

TEST_F(StTsQuery, QueryOpCountStarErr)
{
    SQLHENV hEnv = SQL_NULL_HENV;
    SQLHDBC hDbc = SQL_NULL_HDBC;
    SQLHSTMT hStmt = SQL_NULL_HSTMT;
    SQLRETURN RETCODE = SQL_SUCCESS;
    SQLCHAR ddlCommand[200] = "create table errquery(age integer, id integer, worktime integer) with"
                              " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',"
                              " ttl = '3 hours');";
    SQLCHAR qryCommand1[60] = "select count(*) from errquery group by age limit 100;";
    SQLINTEGER rowNum = 3;
    int64_t id[] = {1, 2, 4};
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    SQLCHAR stmtText[30] = " select   * from   errquery";  // multi space check
    StatementHandleT *stmt = NULL;

    RETCODE = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, (void *)SQL_OV_ODBC3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, &hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLConnect(hDbc, (SQLCHAR *)"usocket:/run/verona/unix_emserver", SQL_NTS, (SQLCHAR *)"usr", 0, NULL, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, &hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    stmt = (StatementHandleT *)hStmt;
    EXPECT_EQ(stmt->status, STMT_STATUS_INIT);

    RETCODE = SQLExecDirect(hStmt, ddlCommand, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)rowNum, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, id, sizeof(id[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, age, sizeof(age[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, worktime, sizeof(worktime[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    // prepare twice to test if it can be reused
    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_PREPARE);

    SQLSMALLINT numResCol;
    RETCODE = SQLNumResultCols(hStmt, &numResCol);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(numResCol, 3);

    RETCODE = SQLBulkOperations(hStmt, SQL_ADD);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BULKOP);

    int64_t countRes = 0;

    // cmd 1
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &countRes, sizeof(countRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand1, 0);
    EXPECT_EQ(RETCODE, SQL_ERROR);

    RETCODE = SQLFreeHandle(SQL_HANDLE_STMT, hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLDisconnect(hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_DBC, hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_ENV, hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
}

TEST_F(StTsQuery, QueryErr4Sum)
{
    SQLHENV hEnv = SQL_NULL_HENV;
    SQLHDBC hDbc = SQL_NULL_HDBC;
    SQLHSTMT hStmt = SQL_NULL_HSTMT;
    SQLRETURN RETCODE = SQL_SUCCESS;
    SQLCHAR ddlCommand[200] =
        "create table tablequery3(age integer, id integer, name CHAR(4), worktime integer, salary integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    // 以下三句sql均会失败，因为字符类型参与了加法
    SQLCHAR qryCommand1[200] = "select age, sum(id + name) from tablequery3 group by age;";
    SQLCHAR qryCommand2[200] = "select age, sum(id + name) from tablequery3 group by age order by sum(id + name);";
    SQLCHAR qryCommand3[200] = "select age, count(id) from tablequery3 group by age order by sum(id + name);";
    // 以下这个sql会值溢出
    SQLCHAR qryCommand4[200] = "select age, sum(id + age) from tablequery3 group by age;";
    // 以下这个sql会值溢出
    SQLCHAR qryCommand5[200] = "select age, sum(id + worktime + salary) from tablequery3 group by age;";
    SQLINTEGER rowNum = 3;
    int64_t id[] = {1, 2, 4};
    int64_t age[] = {29, 30, INT64_MAX};
    int64_t worktime[] = {24, 24, 25};
    int64_t salary[] = {21, 11, 3};
    SQLCHAR name[3][10] = {"aaa", "bbb", "ccc"};
    SQLCHAR stmtText[30] = " select * from tablequery3";  // multi space check
    StatementHandleT *stmt = NULL;

    RETCODE = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, (void *)SQL_OV_ODBC3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, &hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLConnect(hDbc, (SQLCHAR *)"usocket:/run/verona/unix_emserver", SQL_NTS, (SQLCHAR *)"usr", 0, NULL, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, &hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    stmt = (StatementHandleT *)hStmt;
    EXPECT_EQ(stmt->status, STMT_STATUS_INIT);

    RETCODE = SQLExecDirect(hStmt, ddlCommand, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)rowNum, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, age, sizeof(age[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, id, sizeof(id[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_CHAR, name, sizeof(char) * 4, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 4, SQL_C_SBIGINT, worktime, sizeof(worktime[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 5, SQL_C_SBIGINT, salary, sizeof(salary[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    // prepare twice to test if it can be reused
    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_PREPARE);

    SQLSMALLINT numResCol;
    RETCODE = SQLNumResultCols(hStmt, &numResCol);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(numResCol, 5);

    RETCODE = SQLBulkOperations(hStmt, SQL_ADD);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BULKOP);

    int64_t ageRes = 0;
    int64_t worktimeRes = 0;

    // cmd 1
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &worktimeRes, sizeof(worktimeRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand1, 0);
    EXPECT_EQ(RETCODE, SQL_ERROR);

    // cmd 2
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand2, 0);
    EXPECT_EQ(RETCODE, SQL_ERROR);

    // cmd 3
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLExecDirect(hStmt, qryCommand3, 0);
    EXPECT_EQ(RETCODE, SQL_ERROR);

    // cmd 4
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLExecDirect(hStmt, qryCommand4, 0);
    EXPECT_EQ(RETCODE, SQL_ERROR);

    // cmd 5
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLExecDirect(hStmt, qryCommand5, 0);
    EXPECT_EQ(RETCODE, SQL_ERROR);

    RETCODE = SQLFreeHandle(SQL_HANDLE_STMT, hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLDisconnect(hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_DBC, hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_ENV, hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
}

TEST_F(StTsQuery, QueryOpSimpleWithConst)
{
    SQLHENV hEnv = SQL_NULL_HENV;
    SQLHDBC hDbc = SQL_NULL_HDBC;
    SQLHSTMT hStmt = SQL_NULL_HSTMT;
    SQLRETURN RETCODE = SQL_SUCCESS;
    SQLCHAR ddlCommand[200] =
        "create table T(age integer, id integer, worktime integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    SQLCHAR qryCommand1[40] = "select 1, worktime from T;";
    SQLCHAR qryCommand2[50] = "select age, 1, 2 from T where age > 20;";
    SQLCHAR qryCommand3[100] = "select age, count(id), 42 from T where id >= 1 group by age order by age;";
    SQLCHAR qryCommand4[50] = "select 1, 2 from T;";

    SQLINTEGER rowNum = 3;
    int64_t id[] = {1, 2, 4};
    int64_t age[] = {29, 19, 19};
    int64_t worktime[] = {24, 24, 25};

    SQLCHAR stmtText[30] = " select   * from   T";  // multi space check
    StatementHandleT *stmt = NULL;

    RETCODE = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, (void *)SQL_OV_ODBC3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, &hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLConnect(hDbc, (SQLCHAR *)"usocket:/run/verona/unix_emserver", SQL_NTS, (SQLCHAR *)"usr", 0, NULL, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, &hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    stmt = (StatementHandleT *)hStmt;
    EXPECT_EQ(stmt->status, STMT_STATUS_INIT);

    RETCODE = SQLExecDirect(hStmt, ddlCommand, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLSetStmtAttr(hStmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)rowNum, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, id, sizeof(id[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, age, sizeof(age[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, worktime, sizeof(worktime[0]), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    // prepare twice to test if it can be reused
    RETCODE = SQLPrepare(hStmt, stmtText, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_PREPARE);

    SQLSMALLINT numResCol;
    RETCODE = SQLNumResultCols(hStmt, &numResCol);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(numResCol, 3);

    RETCODE = SQLBulkOperations(hStmt, SQL_ADD);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BULKOP);

    int64_t ageRes = 0;
    int64_t worktimeRes = 0;
    int64_t const1 = 0;
    int64_t const2 = 0;
    int64_t count = 0;

    // cmd 1
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &const1, sizeof(const1), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &worktimeRes, sizeof(worktimeRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand1, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    uint32_t i = 0;
    while ((RETCODE = SQLFetch(hStmt)) == SQL_SUCCESS) {
        EXPECT_EQ(const1, 1);
        EXPECT_EQ(worktimeRes, worktime[i]);
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    // cmd 2
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &const1, sizeof(const1), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, &const2, sizeof(const2), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_BIND);

    RETCODE = SQLExecDirect(hStmt, qryCommand2, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    i = 0;
    while ((RETCODE = SQLFetch(hStmt)) == SQL_SUCCESS) {
        EXPECT_EQ(ageRes, 29);
        EXPECT_EQ(const1, 1);
        EXPECT_EQ(const2, 2);
        EXPECT_EQ(stmt->status, STMT_STATUS_FETCH);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);
    EXPECT_EQ(RETCODE, SQL_NO_DATA);

    // cmd 3
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &ageRes, sizeof(ageRes), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &count, sizeof(count), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 3, SQL_C_SBIGINT, &const1, sizeof(const1), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLExecDirect(hStmt, qryCommand3, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    RETCODE = SQLFetch(hStmt);
    EXPECT_EQ(ageRes, 19);
    EXPECT_EQ(count, 2);
    EXPECT_EQ(const1, 42);

    RETCODE = SQLFetch(hStmt);
    EXPECT_EQ(ageRes, 29);
    EXPECT_EQ(count, 1);
    EXPECT_EQ(const1, 42);

    // cmd 4
    RETCODE = SQLBindCol(hStmt, 1, SQL_C_SBIGINT, &const1, sizeof(const1), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLBindCol(hStmt, 2, SQL_C_SBIGINT, &const2, sizeof(const2), 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLExecDirect(hStmt, qryCommand4, 0);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
    EXPECT_EQ(stmt->status, STMT_STATUS_EXEC_DIR);

    RETCODE = SQLFetch(hStmt);
    EXPECT_EQ(RETCODE, GMERR_OK);

    RETCODE = SQLFreeHandle(SQL_HANDLE_STMT, hStmt);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLDisconnect(hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_DBC, hDbc);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);

    RETCODE = SQLFreeHandle(SQL_HANDLE_ENV, hEnv);
    EXPECT_EQ(RETCODE, SQL_SUCCESS);
}
