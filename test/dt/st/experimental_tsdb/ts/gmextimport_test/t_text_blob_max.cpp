/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:
 * Author: ts team
 * Create: 2025/7/23
 */

#include <stdint.h>
#include <unistd.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <time.h>
#include <sqlite3.h>

#define MAX_ARG_NUM 5
// 生成随机字符串
inline void GenerateRandomString(char *str, size_t size)
{
    // 定义用于生成随机字符串的字符集
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    for (size_t n = 0; n < size - 1; n++) {
        int key = rand() % (int)(sizeof(charset) - 1);
        str[n] = charset[key];
    }
    str[size - 1] = '\0';
}

int main(int argc, char *argv[])
{
    if (argc > MAX_ARG_NUM) {
        printf("Usage: ./t_text_blob_max -p [cmd file path] -n [total row num]\n");
        return -1;
    }
    char *filePath = NULL;
    int numRecords = 1500000;
    if (argc > 1) {
        uint32_t i = 1;
        while (i < argc) {
            if (strcmp(argv[i], "-p") == 0 && i + 1 < argc) {
                filePath = argv[i + 1];
                i += 2;
                continue;
            } else if (strcmp(argv[i], "-n") == 0 && i + 1 < argc) {
                numRecords = atoi(argv[i + 1]);
                i += 2;
                continue;
            } else {
                printf("Usage: ./t_text_blob_max -p [cmd file path] -n [total row num]\n");
                return -1;
            }
        }
    }
    sqlite3 *db;
    char *errMsg = 0;
    int rc;

    // 打开 SQLite 数据库
    if (filePath == NULL) {
        rc = sqlite3_open("Text_Blob_Max.db", &db);
    } else {
        rc = sqlite3_open(filePath, &db);
    }
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 1;
    }

    // 创建 T_Text_Blob_Max 表的 SQL 语句
    char *sqlCreateTable = "CREATE TABLE IF NOT EXISTS T_Text_Blob_Max("
                           "log_time INTEGER,"
                           "name TEXT,"
                           "comment BLOB"
                           ");";

    // 执行创建表的 SQL 语句
    rc = sqlite3_exec(db, sqlCreateTable, 0, 0, &errMsg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "SQL 错误: %s\n", errMsg);
        sqlite3_free(errMsg);
        sqlite3_close(db);
        return 1;
    }

    // 准备插入数据的 SQL 语句
    char *sqlInsert = "INSERT INTO T_Text_Blob_Max ("
                      "log_time, name, comment"
                      ") VALUES (?,?,?)";

    sqlite3_exec(db, "begin;", 0, 0, 0);
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sqlInsert, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法准备语句: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 1;
    }

    // 初始化随机数种子
    srand(time(NULL));

    int logTime = time(NULL);
    char name[65535] = "log_brief";
    char comment[65535] = "module_name";
    GenerateRandomString(name, sizeof(name));        // 生成长度为65535的随机字符串
    GenerateRandomString(comment, sizeof(comment));  // 生成长度为65535的随机字符串
    int timeStep = 0;
    // 插入随机数据
    for (int i = 0; i < numRecords; i++) {
        // 生成随机数据
        timeStep++;
        if (timeStep >= 4000) {
            logTime++;
            timeStep = 0;
        }

        // 绑定参数
        sqlite3_bind_int(stmt, 1, logTime);
        sqlite3_bind_text(stmt, 2, name, -1, SQLITE_TRANSIENT);
        sqlite3_bind_blob(stmt, 3, comment, -1, SQLITE_TRANSIENT);

        // 执行插入操作
        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE) {
            fprintf(stderr, "执行失败: %s\n", sqlite3_errmsg(db));
        }

        // 重置语句以便下一次插入
        sqlite3_reset(stmt);
    }

    // 结束语句并关闭数据库连接
    sqlite3_finalize(stmt);
    sqlite3_exec(db, "commit;", 0, 0, 0);
    sqlite3_close(db);

    printf("随机数据已成功插入到 T_Text_Blob_Max 表中。\n");

    return 0;
}
