/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for ts ddl
 * Author: lihainuo
 * Create: 2023/7/26
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc.h"
#include "gmc_sql.h"
#include "dm_meta_prop_strudefs.h"
#include "clt_stmt.h"

const static uint32_t waitTime = 1000;  // ms
constexpr uint32_t cmdLen = 256;

const static char *g_cfgPersist = "ts/gmserver_priv.ini";

static GmcConnOptionsT *connOptions = NULL;

class StTsPrivCheck : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(waitTime);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(waitTime);
        GmcInit();
        GmcConnOptionsCreate(&connOptions);
        GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
        GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
        GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
        GmcConnOptionsSetCSRead(connOptions);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcUnInit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

TEST_F(StTsPrivCheck, connectTest)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(tmpStmt);
    GmcDisconnect(tmpConn);
}

TEST_F(StTsPrivCheck, sysPrivTest)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";

    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    sysRet = system("gmrule -c import_policy -f ./ts/tspolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);
    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(tmpStmt);
    GmcDisconnect(tmpConn);
}

TEST_F(StTsPrivCheck, grantRevokeTest)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    sysRet = system("gmrule -c import_policy -f ./ts/tspolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";

    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    // 无权限，priv_execsql返回失败
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_NE(GMERR_OK, sysRet);

    char grantCmd[cmdLen] = "GRANT SELECT ON testdb TO 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_OK, ret);

    // 赋予权限后，priv_execsql返回成功
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_EQ(0, sysRet);

    char revokeCmd[cmdLen] = "REVOKE SELECT ON testdb FROM 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(GMERR_OK, ret);

    // 撤销权限后，priv_execsql返回失败
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_NE(0, sysRet);

    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(tmpStmt);
    GmcDisconnect(tmpConn);
}

char *g_tmpObjPolicy = R"({
    "object_privilege_config": [{
        "obj_name": "testdb",
        "obj_type": "VERTEX_LABEL",
        "namespace": "public",
        "privs": [{
            "user": "root",
            "process": "priv_execsql",
            "privs_type": ["SELECT"]
        }]
    }]
})";

// gmrule导入对象权限， revoke撤销对象权限
TEST_F(StTsPrivCheck, grantRevokeTestGmruleImportObjPriv)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    sysRet = system("gmrule -c import_policy -f ./ts/tspolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";

    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    // 无权限，priv_execsql返回失败
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_NE(GMERR_OK, sysRet);

    char grantCmd[cmdLen] = "GRANT SELECT ON testdb TO 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_OK, ret);

    // 通过gmrule导入权限，priv_execsql返回成功
    FILE *fp = fopen("./tmpobject.gmpolicy", "w");
    fprintf(fp, "%s", g_tmpObjPolicy);
    fclose(fp);
    sysRet = system("gmrule -c import_policy -f ./tmpobject.gmpolicy -s usocket:/run/verona/unix_emserver");
    EXPECT_EQ(0, sysRet);

    // 赋予权限后，priv_execsql返回成功
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_EQ(0, sysRet);
    system("rm ./tmpobject.gmpolicy");

    char revokeCmd[cmdLen] = "REVOKE SELECT ON testdb FROM 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(GMERR_OK, ret);

    // 撤销权限后，priv_execsql返回失败
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_NE(0, sysRet);

    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(tmpStmt);
    GmcDisconnect(tmpConn);
}

char *g_tmpPolicy = R"({
    "system_privilege_config": [{
        "users": [
            {"user": "root", "process": "priv_execsql"}
        ],
        "privs": [{
                "obj_type": "VERTEX_LABEL",
                "privs_type": [
                    "CREATE",
                    "DROP",
                    "ALTER"
                ]
            }
        ]
    }]
})";

TEST_F(StTsPrivCheck, sysPrivImportTest)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    sysRet = system("gmrule -c import_policy -f ./ts/tspolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";

    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(tmpStmt);
    GmcDisconnect(tmpConn);
    // 无权限，priv_execsql返回失败
    sysRet = system("./priv_execsql \"ALTER TABLE testdb ADD COLUMN new_col2 INTEGER;\"");
    EXPECT_NE(GMERR_OK, sysRet);
    sysRet = system("./priv_execsql \"drop table testdb;\"");
    EXPECT_NE(GMERR_OK, sysRet);

    FILE *fp = fopen("./tmp.gmpolicy", "w");
    fprintf(fp, "%s", g_tmpPolicy);
    fclose(fp);
    // 导入权限，priv_execsql返回成功
    sysRet = system("gmrule -c import_policy -f ./tmp.gmpolicy -s usocket:/run/verona/unix_emserver");
    EXPECT_EQ(0, sysRet);
    sysRet = system("./priv_execsql \"ALTER TABLE testdb ADD COLUMN new_col2 INTEGER;\"");
    EXPECT_EQ(GMERR_OK, sysRet);
    sysRet = system("./priv_execsql \"drop table testdb;\"");
    EXPECT_EQ(GMERR_OK, sysRet);
    system("rm ./tmp.gmpolicy");
}

TEST_F(StTsPrivCheck, creatorDefaultPrivTest)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    sysRet = system("gmrule -c import_policy -f ./ts/tspolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(0, sysRet);
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb2(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rowNum = 3;
    int64_t startTime[] = {1, 2, 3};
    int64_t endTime[] = {1000001, 1000002, 1000003};
    ret = GmcPrepareStmtByLabelName(tmpStmt, "testdb2", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(tmpStmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(tmpStmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, startTime, sizeof(startTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(tmpStmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, endTime, sizeof(endTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(tmpStmt);
    EXPECT_EQ(ret, GMERR_OK);

    char selectCmd[cmdLen] = "select count(*) from testdb2 where end_time > 4;";
    ret = GmcExecDirect(tmpStmt, selectCmd, strlen(selectCmd));
    EXPECT_EQ(GMERR_OK, ret);
    char agingCmd[cmdLen] = "select tsdb_aging('testdb2');";
    ret = GmcExecDirect(tmpStmt, agingCmd, strlen(agingCmd));
    EXPECT_EQ(GMERR_OK, ret);
    char dropCommand[cmdLen] = "drop table testdb2;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
}

static const uint32_t g_maxLen = 1024;
const char *g_labelName = "testdb";

void PrintUserStatView(const char *fileName)
{
    Status ret;
    char command[g_maxLen] = {};
#if (defined RTOSV2 || defined RTOSV2X)
    (void)snprintf_s(
        command, g_maxLen, g_maxLen - 1, "gmsysview -q V\\$PRIVILEGE_USER_STAT -s channel: > %s", fileName);
#else
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1,
        "gmsysview -q V\\$PRIVILEGE_USER_STAT -s usocket:/run/verona/unix_emserver > %s", fileName);
#endif
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);
}

void PrintVertexLabelView(const char *vlName, const char *fileName)
{
    Status ret;
    char command[g_maxLen] = {};
#if (defined RTOSV2 || defined RTOSV2X)
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1,
        "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s -s channel: | grep -A 25 OBJ_PRIVILEGE > %s",
        vlName, fileName);
#else
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1,
        "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s -s "
        "usocket:/run/verona/unix_emserver | grep -E 'name: OBJ_PRIVILEGE]|ROLE_NAME:|PROCESS_NAME:|PRIVILEGES:'  > %s",
        vlName, fileName);
#endif
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);
}

static bool AreTwoFileSame(const char *filePath1, const char *filePath2)
{
    // 打开两个文件
    FILE *file1 = fopen(filePath1, "r");
    FILE *file2 = fopen(filePath2, "r");

    // 如果任何一个文件无法打开，则返回错误
    if (file1 == NULL || file2 == NULL) {
        return false;
    }

    bool same = true;
    // 读取文件的内容并比较
    while (1) {
        int ch1 = fgetc(file1);
        int ch2 = fgetc(file2);
        // 如果两个文件的内容不相同，则返回错误
        if (ch1 != ch2) {
            same = false;
            break;
        }

        // 如果到达文件的末尾，则返回成功
        if (ch1 == EOF && ch2 == EOF) {
            break;
        }
    }

    // 关闭文件
    fclose(file1);
    fclose(file2);

    return same;
}

// 导入白名单，赋系统权限重启并校验，撤系统权限重启并校验
// 导入白名单，赋vertexLabel对象权限重启并校验，撤对象权限重启并校验
TEST_F(StTsPrivCheck, DISABLED_GrantRevokeSysOrObjPrivAndReboot)
{
    system("rm -f ./sysprivtmp*.txt");
    system("rm -f ./objprivtmp*.txt");
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表时会自动赋予此用户对此表的部分操作权限
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(ret, GMERR_OK);

    // 赋权查询成功
    char grantCmd1[cmdLen] = "GRANT SELECT ON testdb TO public;";
    ret = GmcExecDirect(tmpStmt, grantCmd1, strlen(grantCmd1));
    EXPECT_EQ(GMERR_OK, ret);
    int sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_EQ(0, sysRet);

    char grantCmd2[cmdLen] = "GRANT UPDATE ON testdb TO 'root:priv_execsql', 'root:gmexport';";
    ret = GmcExecDirect(tmpStmt, grantCmd2, strlen(grantCmd2));
    EXPECT_EQ(GMERR_OK, ret);

    // 1、保存赋权后的 vl 对象权限视图和系统权限视图
    PrintVertexLabelView(g_labelName, (const char *)"objprivtmp1.txt");
    PrintUserStatView((const char *)"sysprivtmp1.txt");

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    // 2.1 校验重启前后的权限视图结构是否相同(涉及导白名单、赋系统权限)
    PrintUserStatView((const char *)"sysprivtmp2.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"sysprivtmp1.txt", (const char *)"sysprivtmp2.txt"));
    // 2.2 校验重启前后的 vertexLabel 对象权限视图结构是否相同（涉及赋对象权限）
    PrintVertexLabelView(g_labelName, (const char *)"objprivtmp2.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"objprivtmp1.txt", (const char *)"objprivtmp2.txt"));

    ret = system("gmrule -c remove_allowlist -f ./ts/priv_file/remove_allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f ./ts/priv_file/sys_priv_revoke_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 撤权后查询失败
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char revokeCmd1[cmdLen] = "REVOKE SELECT ON testdb FROM public;";
    ret = GmcExecDirect(tmpStmt, revokeCmd1, strlen(revokeCmd1));
    EXPECT_EQ(GMERR_OK, ret);
    sysRet = system("./priv_execsql \"select start_time from testdb;\"");
    EXPECT_NE(0, sysRet);

    char revokeCmd2[cmdLen] = "REVOKE UPDATE ON testdb FROM 'root:priv_execsql', 'root:gmexport';";
    ret = GmcExecDirect(tmpStmt, revokeCmd2, strlen(revokeCmd2));
    EXPECT_EQ(GMERR_OK, ret);

    // 3、保存撤权后的 vl 对象权限视图和系统权限视图
    PrintVertexLabelView(g_labelName, (const char *)"objprivtmp3.txt");
    PrintUserStatView((const char *)"sysprivtmp3.txt");

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    // 4.1 校验重启前后的权限视图结构是否相同(涉及删除白名单用户、撤系统权限)
    PrintUserStatView((const char *)"sysprivtmp4.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"sysprivtmp3.txt", (const char *)"sysprivtmp4.txt"));
    // 4.2 校验重启前后的 vertexLabel 对象权限视图结构是否相同（涉及撤销对象权限）
    PrintVertexLabelView(g_labelName, (const char *)"objprivtmp4.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"objprivtmp3.txt", (const char *)"objprivtmp4.txt"));

    ret = system("gmrule -c remove_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f ./ts/priv_file/sys_priv_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 删表
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    ASSERT_EQ(GMERR_OK, ret);
    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);

    system("rm -f ./sysprivtmp*.txt");
    system("rm -f ./objprivtmp*.txt");
}
