/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for ts ddl
 * Author: lihainuo
 * Create: 2023/7/26
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc.h"
#include "gmc_sql.h"
#include "dm_meta_prop_strudefs.h"
#include "clt_stmt.h"

const static uint32_t cmdLen = 200;
const static uint32_t waitTime = 1000;  // ms

const static char *g_cfgPersist = "ts/gmserver_ts.ini";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;

class StTsDdlGmc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(waitTime);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(waitTime);
        GmcInit();
        GmcConnOptionsCreate(&connOptions);
        GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
        GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
        GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
        GmcConnOptionsSetCSRead(connOptions);
        GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
        GmcConnOptionsDestroy(connOptions);
        GmcAllocStmt(conn, &stmt);
    }
    static void TearDownTestCase()
    {
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        GmcUnInit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

TEST_F(StTsDdlGmc, testPrepareNotCsMode)
{
    GmcConnOptionsT *connOptions2 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;

    GmcConnOptionsCreate(&connOptions2);
    GmcConnOptionsSetServerLocator(connOptions2, "usocket:/run/verona/unix_emserver");
    GmcConnOptionsSetRequestTimeout(connOptions2, 1000000);
    GmcConnOptionsSetMsgReadTimeout(connOptions2, 1000000);
    GmcConnOptionsSetCSRead(connOptions2);
    connOptions2->isCsMode = false;
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions2, &conn2);
    GmcConnOptionsDestroy(connOptions2);
    GmcAllocStmt(conn2, &stmt2);

    char createCommand[cmdLen] = "create table tablename1h(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[20] = "tablename1h";
    ret = GmcPrepareStmtByLabelName(stmt2, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    char *dropCommand = "drop table tablename1h";
    ret = GmcExecDirect(stmt2, dropCommand, 200);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt2);
    GmcDisconnect(conn2);
}

TEST_F(StTsDdlGmc, testCreateTsTable1h)
{
    char createCommand[cmdLen] = "create table tablename1h(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, DISABLED_testCreateTsTable1hr)
{
    char createCommand[cmdLen] = "create table tablename1h(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 hr', ttl = '3 hours');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTable1d)
{
    char createCommand[cmdLen] = "create table tablename1d(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
}

TEST_F(StTsDdlGmc, testCreateTsTable1m)
{
    char createCommand[cmdLen] = "create table tablename1m(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 month', ttl = '3 months');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
}

TEST_F(StTsDdlGmc, testCreateTsTable1y)
{
    char createCommand[cmdLen] = "create table tablename1y(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 year', ttl = '1 year');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
}

TEST_F(StTsDdlGmc, testCreateTsTableWithIfExists)
{
    char createCommand[cmdLen] = "create table if not exists tablename1y(start_time integer, end_time integer, "
                                 "name char(9)) with (time_col = 'end_time', interval = '1 year', ttl = '1 year');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

// DTS2025010325234
TEST_F(StTsDdlGmc, DISABLED_testCreateTsTableWithDiffCaseTimeCol)
{
    char createCommand[cmdLen] = "create table tablename1h(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'eNd_tIme', interval = '1 hour', ttl = '3 hours');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testCreateTsMemTable)
{

    char cmd[cmdLen] = "drop table memtable;";
    (void)GmcExecDirect(stmt, cmd, cmdLen);
    char createCommand[cmdLen] = "create table memtable(name char(20), end_time integer, id integer"
                                 ", index memidx(end_time)) "
                                 "with (engine = 'memory', time_col = 'id', interval = '1 h', max_size = 10000);";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testCreateTsMemTableWithIndexNameExceeds128B)
{
    char *table =
        "abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789"
        "abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789";
    char createCommand[500] = {0};
    errno_t err = sprintf_s(createCommand, 500,
        "create table memtable128(name char(20), end_time integer, id integer"
        ", index %s(end_time)) with (engine = 'memory', time_col = 'id', interval = '1 h', max_size = 10000);",
        table);
    EXPECT_LT(0, err);
    Status ret = GmcExecDirect(stmt, createCommand, 500);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTableExceeds128B)
{
    char *table = "abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789"
                  "abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789"
                  "abcdefghij0123456789abcdefghij012345678901234567";
    char createCommand[500] = {0};
    errno_t err = sprintf_s(createCommand, 500,
        "create table %s(start_time integer, end_time integer, name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');",
        table);
    EXPECT_LT(0, err);
    Status ret = GmcExecDirect(stmt, createCommand, 500);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTableWith256Cols)
{
    uint32_t longCmdLen = 65535;
    char cols[4096] = {0};
    char *pointer = cols;
    uint32_t colLen = 16;
    errno_t err = sprintf_s(pointer, colLen, "id%d integer", 0);
    EXPECT_LT(0, err);
    pointer += strlen("id0 integer");
    for (int32_t i = 1; i < 256; i++) {
        char tmp[colLen] = {0};
        err = sprintf_s(tmp, colLen, ", id%d integer", i);
        EXPECT_LT(0, err);
        err = strcpy_s(pointer, colLen, tmp);
        EXPECT_EQ(0, err);
        pointer += strlen(tmp);
    }

    char createCommand[longCmdLen] = {0};
    err = sprintf_s(createCommand, longCmdLen,
        "create table tablewith256Cols(%s) with (time_col = 'id0', interval = '1 year', ttl = '1 year');", cols);
    EXPECT_LT(0, err);
    Status ret = GmcExecDirect(stmt, createCommand, longCmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTableErrCase)
{
    // invalid interval
    char createCommand[cmdLen] = "create table errt1(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '3 month', ttl = '3 month');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    // wrong type for time col
    char createCommand2[cmdLen] = "create table errt2(start_time integer, end_time integer, name char(9))"
                                  " with (time_col = 'name', interval = '1 year', ttl = '1 year');";
    ret = GmcExecDirect(stmt, createCommand2, cmdLen);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // invalid name for time col
    char createCommand3[cmdLen] = "create table errt3(start_time integer, end_time integer, name char(9))"
                                  " with (time_col = 'time', interval = '1 year', ttl = '1 year');";
    ret = GmcExecDirect(stmt, createCommand3, cmdLen);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // invalid with option
    char createCommand4[cmdLen] = "create table errt4(start_time integer, end_time integer, name char(9))"
                                  " with (undefined = 'start_time', interval = '1 year', ttl = '1 year');";
    ret = GmcExecDirect(stmt, createCommand4, cmdLen);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    // invalid col type
    char createCommand5[cmdLen] = "create table errt5(start_time undefined, end_time integer, name char(9))"
                                  " with (time_col = 'end_time', interval = '1 year', ttl = '1 year');";
    ret = GmcExecDirect(stmt, createCommand5, cmdLen);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // invalid ttl config
    char createCommand6[cmdLen] = "create table errt6(start_time integer, end_time integer, name char(9))"
                                  " with (time_col = 'end_time', interval = '1 year', ttl = '1 hour');";
    ret = GmcExecDirect(stmt, createCommand6, cmdLen);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    // invalid char length
    char createCommand7[cmdLen] = "create table errt7(start_time integer, end_time integer, name char('00111'))"
                                  " with (time_col = 'end_time', interval = '3 month', ttl = '3 month');";
    ret = GmcExecDirect(stmt, createCommand7, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // time col text
    char createCommand8[cmdLen] = "create table errt8(start_time integer, end_time integer, name text)"
                                  " with (time_col = 'name', interval = '1 h', ttl = '3 h');";
    ret = GmcExecDirect(stmt, createCommand8, cmdLen);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // duplicate col
    char createCommand9[cmdLen] = "create table errt9(start_time integer, end_time integer, end_TIME text)"
                                  " with (time_col = 'start_time', interval = '1 h', ttl = '3 h');";
    ret = GmcExecDirect(stmt, createCommand9, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);

    // wrong interval (lowest binary search)
    char createCommand10[cmdLen] = "create table errt10(start_time integer, end_time integer, name text)"
                                   " with (time_col = 'start_time', interval = '1 b', ttl = '3 h');";
    ret = GmcExecDirect(stmt, createCommand10, cmdLen);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // wrong interval (highest binary search)
    char createCommand11[cmdLen] = "create table errt11(start_time integer, end_time integer, name text)"
                                   " with (time_col = 'start_time', interval = '1 z', ttl = '3 h');";
    ret = GmcExecDirect(stmt, createCommand11, cmdLen);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTableWith257ColsErr)
{
    uint32_t longCmdLen = 65535;
    char cols[4112] = {0};
    char *pointer = cols;
    uint32_t colLen = 16;
    errno_t err = sprintf_s(pointer, colLen, "id%d integer", 0);
    EXPECT_LT(0, err);
    pointer += strlen("id0 integer");
    for (int32_t i = 1; i <= 256; i++) {
        char tmp[colLen] = {0};
        err = sprintf_s(tmp, colLen, ", id%d integer", i);
        EXPECT_LT(0, err);
        err = strcpy_s(pointer, colLen, tmp);
        EXPECT_EQ(0, err);
        pointer += strlen(tmp);
    }

    char createCommand[longCmdLen] = {0};
    err = sprintf_s(createCommand, longCmdLen,
        "create table tablewith257Cols(%s) with (time_col = 'id0', interval = '1 year', ttl = '1 year');", cols);
    EXPECT_LT(0, err);
    Status ret = GmcExecDirect(stmt, createCommand, longCmdLen);
    EXPECT_EQ(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTableLog)
{
    char createCommand1[300] =
        "create table tsdb1(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with "
        "(time_col = 'time', interval = '2 day', ttl = '3 day', cache_size = '0', disk_limit = '50 MB', "
        "sensitive_col "
        "= 'name', compression = 'fast(rapidlz)');";
    Status ret = GmcExecDirect(stmt, createCommand1, 300);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    char createCommand2[300] =
        "create table tsdb2(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with "
        "(time_col = 'time', interval = '1 day', ttl = '1 year', cache_size = '0', disk_limit = '50 MB', "
        "sensitive_col "
        "= 'name', compression = 'fast(rapidlz)');";
    ret = GmcExecDirect(stmt, createCommand2, 300);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    char createCommand3[250] =
        "create table tsdb3(id integer, time integer, name char(64), ip inet, message blob(160), ns text) "
        "with(time_col = 'time', interval = '1 day', ttl = '1 hour', compression = 'fast(rapidlz)');";
    ret = GmcExecDirect(stmt, createCommand3, 250);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

TEST_F(StTsDdlGmc, testDropTsTable1)
{
    char createCommand[cmdLen] = "create table tablename1(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    char dropCommand[cmdLen] = "drop table tablename1;";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testDropTsTable2)
{
    char createCommand[cmdLen] = "create table tablename2(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    char dropCommand[cmdLen] = "drop table TABLEname2;";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testDropTsMemTable)
{
    char cmd[cmdLen] = "drop table memtabledrop;";
    (void)GmcExecDirect(stmt, cmd, cmdLen);
    char createCommand[cmdLen] = "create table memtabledrop(name char(20), end_time integer, id integer"
                                 ", index memidx(end_time)) "
                                 "with (engine = 'memory', time_col = 'id', interval = '1 h', max_size = 10000);";
    char dropCommand[cmdLen] = "drop table memtabledrop;";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testDropTsTableErrCase)
{
    char createCommand[cmdLen] = "create table tablename1(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    char dropCommand[cmdLen] = "drop table tablename2;";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

TEST_F(StTsDdlGmc, testCreateTsTableWithDiskLimit)
{
    const char *cmdTemplate = "create table tabledisklimit%d(start_time integer, end_time integer, name char(9))"
                              " with (time_col = 'end_time', interval = '1 month', ttl = '3 months', "
                              "disk_limit = '%s');";
    const char *dropTemplate = "drop table tabledisklimit%d;";
    char dropCommand[cmdLen] = {0};
    char createCommand[cmdLen] = {0};
    // disklimit范围应该为4KB到1TB，数字和单位之间应有空格分开
    // 1 TB = 1099511627776 B
    const char *limit[] = {"4 KB", "1 MB", "1 TB", "4096 MB", "1025 mb", "1025 MB", "4096 B", "1099511627776 B"};
    for (int32_t i = 0; i < 8; i++) {
        sprintf_s(createCommand, cmdLen, cmdTemplate, i, limit[i]);
        sprintf_s(dropCommand, cmdLen, dropTemplate, i);
        Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, dropCommand, cmdLen);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

TEST_F(StTsDdlGmc, grantRevokeSyntaxTestErrcase)
{
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    char dropCommand[cmdLen] = "drop table testdb;";

    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(ret, GMERR_OK);

    constexpr size_t buffLen = 128;
    char revokeCmd[buffLen] = {0};
    // username should in quote
    sprintf_s(revokeCmd, buffLen, "REVOKE INSERT ON testdb FROM stcase;");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    // specify priv type and 'ALL' at same time
    sprintf_s(revokeCmd, buffLen, "REVOKE INSERT UPDATE SELECT DELETE ALL ON testdb FROM stcase;");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // ALL priv to public is not allowed
    sprintf_s(revokeCmd, buffLen, "REVOKE ALL ON testdb FROM PUBLIC;");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(GMERR_INVALID_GRANT_OPERATION, ret);

    sprintf_s(revokeCmd, buffLen, "REVOKE ALL PRIVILEGES ON testdb FROM PUBLIC;");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(GMERR_INVALID_GRANT_OPERATION, ret);

    char grantCmd[buffLen] = {0};
    sprintf_s(grantCmd, buffLen, "GRANT ALL PRIVILEGES ON testdb TO PUBLIC;");
    ret = GmcExecDirect(stmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_INVALID_GRANT_OPERATION, ret);

    // grant to, revoke from
    sprintf_s(grantCmd, buffLen, "GRANT ALL PRIVILEGES ON testdb FROM PUBLIC;");
    ret = GmcExecDirect(stmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // only surpport one table
    sprintf_s(grantCmd, buffLen, "GRANT ALL PRIVILEGES ON testdb, testdb2 TO PUBLIC;");
    ret = GmcExecDirect(stmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // no user specify
    sprintf_s(grantCmd, buffLen, "REVOKE ALL PRIVILEGES ON testdb TO;");
    ret = GmcExecDirect(stmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsDdlGmc, grantRevokeBasicTest)
{
    int sysRet = system("gmrule -c import_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(sysRet, 0);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    char dropCommand[cmdLen] = "drop table testdb;";

    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(ret, GMERR_OK);

    constexpr size_t buffLen = 128;
    char revokeCmd[buffLen] = {0};
    // 直接撤销报失败
    sprintf_s(revokeCmd, buffLen, "REVOKE INSERT ON testdb FROM 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_NE(ret, GMERR_OK);

    // 授权预期成功
    sprintf_s(revokeCmd, buffLen, "GRANT INSERT ON testdb TO 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 授权再次撤销预期成功
    sprintf_s(revokeCmd, buffLen, "REVOKE INSERT ON testdb FROM 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 授权多个权限预期成功
    sprintf_s(revokeCmd, buffLen, "GRANT INSERT, DELETE, SELECT, UPDATE ON testdb TO 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 撤销全部权限预期成功
    sprintf_s(revokeCmd, buffLen, "REVOKE ALL ON testdb FROM 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 授权多个权限多个用户预期成功
    sprintf_s(
        revokeCmd, buffLen, "GRANT INSERT, DELETE, SELECT, UPDATE ON testdb TO 'root:st_client', 'root:st_tools';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    sprintf_s(revokeCmd, buffLen, "REVOKE ALL ON testdb FROM 'root:st_client', 'root:st_tools';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 授权多个权限多个用户预期成功
    sprintf_s(
        revokeCmd, buffLen, "GRANT INSERT, DELETE, SELECT, UPDATE ON testdb TO 'root:st_client', 'root:st_tools';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 逐个撤销预期成功
    sprintf_s(revokeCmd, buffLen, "REVOKE INSERT, DELETE, SELECT, UPDATE ON testdb FROM 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    sprintf_s(revokeCmd, buffLen, "REVOKE ALL PRIVILEGES ON testdb FROM 'root:st_tools';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 单个不存在的用户，预期失败
    sprintf_s(revokeCmd, buffLen, "GRANT ALL PRIVILEGES ON testdb TO 'root:st_testcase';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_UNDEFINED_OBJECT);

    // 多个用户中，包含不存在的用户，预期成功
    sprintf_s(revokeCmd, buffLen, "REVOKE ALL PRIVILEGES ON testdb FROM 'root:st_testcase', 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_EQ(ret, GMERR_OK);

    // 多个用户中，授权给自身
    sprintf_s(revokeCmd, buffLen, "REVOKE ALL PRIVILEGES ON testdb FROM 'root:st_experimental', 'root:st_client';");
    ret = GmcExecDirect(stmt, revokeCmd, strlen(revokeCmd));
    EXPECT_NE(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(ret, GMERR_OK);
    sysRet = system("gmrule -c remove_allowlist -f ./ts/tsuser.gmuser -s usocket:/run/verona/unix_emserver");
    ASSERT_EQ(sysRet, 0);
}

TEST_F(StTsDdlGmc, testCreateTsTableWithDiskLimitErrCase)
{
    const char *cmdTemplate = "create table tabledisklimiterror%d(start_time integer, end_time integer, name char(9))"
                              " with (time_col = 'end_time', interval = '1 month', ttl = '3 months',"
                              " disk_limit = '%s');";
    char createCommand[cmdLen] = {0};
    // disklimit范围应该为4KB到1TB，数字和单位之间应有空格分开
    // 1 TB = 1099511627776 B
    const char *limit[] = {"3 KB", "0 MB", "2 TB", "4096 GB", "1025 gb", "1025MB", "4095 B", "1099511627777 B", "1 PB"};
    for (int32_t i = 0; i < 9; i++) {
        sprintf_s(createCommand, cmdLen, cmdTemplate, i, limit[i]);
        Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
        EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }
}

TEST_F(StTsDdlGmc, testTsdbAgingFuncSqlForLogicLabel)
{
    const char *createCommand = "create table table_aging(start_time integer, end_time integer, name char(9))"
                                " with (time_col = 'end_time', interval = '1 month', ttl = '3 months'); ";
    const char *dropCommand = "drop table table_aging;";
    Status ret = GmcExecDirect(stmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    // tsdb_aging参数只支持一个表名，且需要加引号
    // tsdb_aging只支持一种形式select tsdb_aging('table_name');不允许像聚合函数一样使用
    // 表必须存在
    const char *errorSelect[] = {
        "select tsdb_aging(table_aging);",                                         // 参数未加引号
        "select tsdb_aging(12345);",                                               // 数字常量作为参数
        "select tsdb_aging('12345');",                                             // 非法表名1
        "select tsdb_aging('abc@def');",                                           // 非法表名2
        "select tsdb_aging(*);",                                                   // 传*号
        "select tsdb_aging(DISTINCT 'table_aging');",                              // 设置了DISTINCT
        "select tsdb_aging();",                                                    // 未传参数
        "select tsdb_aging('table_aging') from table_aging;",                      // 不允许跟from
        "select tsdb_aging('table_aging'),tsdb_aging('table_aging');",             // 多次调用
        "select tsdb_aging('table_aging', 'table_aging');",                        // 参数太多
        "select start_time from table_aging order by tsdb_aging('table_aging');",  // order by不允许
        "select start_time from table_aging group by start_time order by tsdb_aging('table_aging');",  // order
                                                                                                       // by不允许
        "select tsdb_aging('table_not_exist');",                                                       // 表不存在
    };
    for (int i = 0; i < (sizeof(errorSelect) / sizeof(const char *)); i++) {
        ret = GmcExecDirect(stmt, errorSelect[i], strlen(errorSelect[i]));
        EXPECT_NE(GMERR_OK, ret);
    }

    const char *errLongTableName = "select tsdb_aging('longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglong"
                                   "longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglongtablename')";
    ret = GmcExecDirect(stmt, errLongTableName, strlen(errLongTableName));
    EXPECT_NE(GMERR_OK, ret);

    const char *cmdSelect = "select tsdb_aging('table_aging');";
    ret = GmcExecDirect(stmt, cmdSelect, strlen(cmdSelect));
    EXPECT_EQ(GMERR_NO_DATA, ret);
    ret = GmcExecDirect(stmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
}

// empty table
TEST_F(StTsDdlGmc, testTsdbAgingFuncSqlForMemLabel_1)
{
    const char *createCommand =
        "create table table_aging(start_time integer, end_time integer, name char(9))"
        " with (engine = 'memory', time_col = 'end_time', interval = '1 month', ttl = '3 months'); ";
    const char *dropCommand = "drop table table_aging;";
    Status ret = GmcExecDirect(stmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    const char *cmdSelect = "select tsdb_aging('table_aging');";
    ret = GmcExecDirect(stmt, cmdSelect, strlen(cmdSelect));
    EXPECT_EQ(GMERR_NO_DATA, ret);
    ret = GmcExecDirect(stmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
}

// one-interval table
TEST_F(StTsDdlGmc, testTsdbAgingFuncSqlForMemLabel_2)
{
    const char *createCommand = "create table table_aging(start_time integer, end_time integer, name char(9))"
                                " with (engine = 'memory', time_col = 'end_time', interval = '1 month'); ";
    const char *dropCommand = "drop table table_aging;";
    const char *selectCommand = "select * from table_aging;";

    Status ret = GmcExecDirect(stmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    // 操作变量
    uint32_t rowNum = 5;
    uint32_t value = 0;

    // 数据
    int64_t startTime[] = {1, 2, 3, 4, 5};
    int64_t endTime[] = {100, 200, 300, 400, 500};
    char name[][10] = {"adam", "aliyah", "charli", "tim", "who"};
    char tableName[30] = "table_aging";

    // 插入
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, startTime, sizeof(startTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, endTime, sizeof(endTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 9, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 校验插入是否成功
    ret = GmcExecDirect(stmt, selectCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, rowNum);

    const char *cmdSelect = "select tsdb_aging('table_aging');";
    ret = GmcExecDirect(stmt, cmdSelect, strlen(cmdSelect));
    EXPECT_EQ(GMERR_OK, ret);

    // 校验老化是否成功，要求全部删除

    ret = GmcExecDirect(stmt, selectCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    ret = GmcExecDirect(stmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
}

// two-interval table
TEST_F(StTsDdlGmc, testTsdbAgingFuncSqlForMemLabel_3)
{
    const char *createCommand = "create table table_aging(start_time integer, end_time integer, name char(9))"
                                " with (engine = 'memory', time_col = 'end_time', interval = '1 hour'); ";
    const char *dropCommand = "drop table table_aging;";
    const char *selectCommand = "select * from table_aging;";

    Status ret = GmcExecDirect(stmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    // 操作变量
    uint32_t rowNum = 5;
    uint32_t value = 0;

    // 数据
    int64_t startTime[] = {1, 2, 3, 4, 5};
    int64_t endTime[] = {1000, 2000, 3000, 4000, 5000};
    char name[][10] = {"adam", "aliyah", "charli", "tim", "who"};
    char tableName[30] = "table_aging";

    // 插入
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, startTime, sizeof(startTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, endTime, sizeof(endTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 9, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 校验插入是否成功
    ret = GmcExecDirect(stmt, selectCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, rowNum);

    // 老化
    const char *cmdSelect = "select tsdb_aging('table_aging');";
    ret = GmcExecDirect(stmt, cmdSelect, strlen(cmdSelect));
    EXPECT_EQ(GMERR_OK, ret);

    // 校验老化是否成功，要求最老数据删除
    ret = GmcExecDirect(stmt, selectCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);

    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    int64_t startTimeRes = 0;
    int rowIndex = 0;
    bool isNull = false;

    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ASSERT_LT(rowIndex, 2);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &startTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(startTimeRes, startTime[rowIndex + 3]);
        rowIndex++;
    }

    ret = GmcExecDirect(stmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testAlterTable)
{
    char createCommand[cmdLen] = "create table altertable(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterCommand1[cmdLen] = "ALTER TABLE ALTERTABLE ADD COLUMN new_col INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "ALTERTABLE", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)4);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[0], name);
    EXPECT_STREQ(name, "start_time");
    name = MEMBER_PTR(&properties[1], name);
    EXPECT_STREQ(name, "end_time");
    name = MEMBER_PTR(&properties[2], name);
    EXPECT_STREQ(name, "name");
    name = MEMBER_PTR(&properties[3], name);  // newly added col
    EXPECT_STREQ(name, "new_col");
    // alter repeatedly
    char alterCommand2[cmdLen] = "ALTER TABLE ALTERTABLE ADD COLUMN new_col2 INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand2, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "ALTERTABLE", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)5);
    properties = MEMBER_PTR(schema, properties);
    name = MEMBER_PTR(&properties[4], name);
    EXPECT_STREQ(name, "new_col2");
}

TEST_F(StTsDdlGmc, testAlterTable256)
{
    uint32_t longCmdLen = 65535;
    char cols[4096] = {0};
    char *pointer = cols;
    uint32_t colLen = 16;
    errno_t err = sprintf_s(pointer, colLen, "id%d integer", 0);
    EXPECT_LT(0, err);
    pointer += strlen("id0 integer");
    for (int32_t i = 1; i < 255; i++) {
        char tmp[colLen] = {0};
        err = sprintf_s(tmp, colLen, ", id%d integer", i);
        EXPECT_LT(0, err);
        err = strcpy_s(pointer, colLen, tmp);
        EXPECT_EQ(0, err);
        pointer += strlen(tmp);
    }

    char createCommand[longCmdLen] = {0};
    err = sprintf_s(createCommand, longCmdLen,
        "create table altertblwith256Cols(%s) with (time_col = 'id0', interval = '1 day');", cols);
    EXPECT_LT(0, err);
    Status ret = GmcExecDirect(stmt, createCommand, longCmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterCommand1[cmdLen] = "ALTER TABLE altertblwith256Cols ADD COLUMN id255 INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "altertblwith256Cols", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)256);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[255], name);
    name = MEMBER_PTR(&properties[255], name);  // newly added col
    EXPECT_STREQ(name, "id255");
    char alterCommand2[cmdLen] = "ALTER TABLE altertblwith256Cols ADD COLUMN id256 INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand2, cmdLen);
    EXPECT_EQ(GMERR_INVALID_COLUMN_DEFINITION, ret);  // over 256 cols
}

TEST_F(StTsDdlGmc, testAlterTable10times)
{
    char createCommand[cmdLen] = "create table altertable10(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterCommand[cmdLen] = {0};
    for (int32_t i = 0; i < 10; i++) {
        errno_t err = sprintf_s(alterCommand, cmdLen, "ALTER TABLE ALTERTABLE10 ADD COLUMN new_col%d INTEGER;", i);
        EXPECT_LT(0, err);
        ret = GmcExecDirect(stmt, alterCommand, cmdLen);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcPrepareStmtByLabelName(stmt, "ALTERTABLE10", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)13);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[0], name);
    EXPECT_STREQ(name, "start_time");
    name = MEMBER_PTR(&properties[1], name);
    EXPECT_STREQ(name, "end_time");
    name = MEMBER_PTR(&properties[2], name);
    EXPECT_STREQ(name, "name");
    name = MEMBER_PTR(&properties[12], name);  // newly added col
    EXPECT_STREQ(name, "new_col9");
}

TEST_F(StTsDdlGmc, testAlterTableDrop)
{
    char createCommand[cmdLen] = "create table dropalter(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterCommand1[cmdLen] = "ALTER TABLE dropalter ADD COLUMN new_col INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "dropalter", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)4);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[3], name);  // newly added col
    EXPECT_STREQ(name, "new_col");
    // alter repeatedly
    char alterCommand2[cmdLen] = "ALTER TABLE dropalter ADD COLUMN new_col2 INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand2, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "dropalter", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)5);
    properties = MEMBER_PTR(schema, properties);
    name = MEMBER_PTR(&properties[4], name);
    EXPECT_STREQ(name, "new_col2");
    char dropCommand[cmdLen] = "drop table DROPALTER;";
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "dropalter", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

TEST_F(StTsDdlGmc, tesDroptAlterTable)
{
    char createCommand[cmdLen] = "create table dropalter2(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterCommand1[cmdLen] = "ALTER TABLE dropalter2 ADD COLUMN new_col INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "dropalter2", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)4);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[3], name);  // newly added col
    EXPECT_STREQ(name, "new_col");
    char dropCommand[cmdLen] = "drop table DROPALTER2;";
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    // alter repeatedly
    char alterCommand2[cmdLen] = "ALTER TABLE dropalter2 ADD COLUMN new_col2 INTEGER;";
    ret = GmcExecDirect(stmt, alterCommand2, cmdLen);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

TEST_F(StTsDdlGmc, testAlterTableBlob)
{
    char createCommand[cmdLen] = "create table altertableblob(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterCommand1[cmdLen] = "ALTER TABLE ALTERTABLEBLOB ADD COLUMN new_col BLOB(1600);";
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "ALTERTABLEBLOB", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)4);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[0], name);
    EXPECT_STREQ(name, "start_time");
    name = MEMBER_PTR(&properties[1], name);
    EXPECT_STREQ(name, "end_time");
    name = MEMBER_PTR(&properties[2], name);
    EXPECT_STREQ(name, "name");
    name = MEMBER_PTR(&properties[3], name);
    EXPECT_STREQ(name, "new_col");
    char alterCommand2[cmdLen] = "ALTER TABLE ALTERTABLEBLOB ADD COLUMN new_col2 BLOB;";
    ret = GmcExecDirect(stmt, alterCommand2, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "ALTERTABLEBLOB", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)5);
    properties = MEMBER_PTR(schema, properties);
    name = MEMBER_PTR(&properties[4], name);
    EXPECT_STREQ(name, "new_col2");
}

TEST_F(StTsDdlGmc, testCreateAlterTableWithText)
{
    char createCommand[cmdLen] = "create table tabletext(start_time integer, end_time integer, name text)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char alterCommand[cmdLen] = "alter table tabletext add column remark text;";
    ret = GmcExecDirect(stmt, alterCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char dropCommand[cmdLen] = "drop table tabletext";
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, testCreateAlterTableWithInet)
{
    char createCommand[cmdLen] = "create table tableIp(start_time integer, end_time integer, name text)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char alterCommand[cmdLen] = "alter table tableIp add column ip inet;";
    ret = GmcExecDirect(stmt, alterCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableIp", GMC_OPERATION_SQL_INSERT));

    uint32_t rowNum = 5;
    int64_t startTime[] = {1, 2, 3, 4, 5};
    int64_t endTime[] = {100, 200, 300, 400, 500};
    char name[][10] = {"adam", "aliyah", "charli", "tim", "who"};
    char *names[5];
    for (uint32_t i = 0; i < 5; ++i) {
        names[i] = name[i];
    }
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, startTime, sizeof(startTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, endTime, sizeof(endTime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_STRING, names, sizeof(names[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(ret, GmcExecute(stmt));

    char qryCommand[cmdLen] = "select * from tableIp;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, qryCommand, cmdLen));

    uint32_t value;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    bool eof = false;
    bool isNull = false;
    int64_t startValue;
    int64_t endValue;
    char nameValue[10];
    char ipValue[33];
    uint32_t i = 0;
    uint32_t propSize;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }

        propSize = 8;
        EXPECT_EQ(GmcGetPropertyById((const GmcStmtT *)stmt, 0, &startValue, &propSize, &isNull), GMERR_OK);
        EXPECT_EQ(startValue, startTime[i]);
        propSize = 8;
        EXPECT_EQ(GmcGetPropertyById((const GmcStmtT *)stmt, 1, &endValue, &propSize, &isNull), GMERR_OK);
        EXPECT_EQ(endValue, endTime[i]);
        propSize = 10;
        EXPECT_EQ(GmcGetPropertyById((const GmcStmtT *)stmt, 2, nameValue, &propSize, &isNull), GMERR_OK);
        EXPECT_STREQ(nameValue, names[i]);
        propSize = 33;
        EXPECT_EQ(GmcGetPropertyById((const GmcStmtT *)stmt, 3, ipValue, &propSize, &isNull), GMERR_OK);
        EXPECT_STREQ(ipValue, "00000000");
        i++;
    }
    EXPECT_EQ(i, (uint32_t)5);

    char dropCommand[cmdLen] = "drop table tableIp";
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, createTableWithPath)
{
    char createCommand[cmdLen] =
        "create table tableCustom(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '/home/<USER>/');";
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
    char dropCommand[cmdLen] = "drop table tableCustom";
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char createCommand2[cmdLen] =
        "create table tableCustom2(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '');";
    ret = GmcExecDirect(stmt, createCommand2, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
    char dropCommand2[cmdLen] = "drop table tableCustom2";
    ret = GmcExecDirect(stmt, dropCommand2, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char createCommand3[cmdLen] =
        "create table tableCustom3(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '/home/<USER>/568/');";
    ret = GmcExecDirect(stmt, createCommand3, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
    char dropCommand3[cmdLen] = "drop table tableCustom3";
    ret = GmcExecDirect(stmt, dropCommand3, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    char createCommand4[cmdLen] =
        "create table tableCustom4(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '/home/<USER>/');";
    ret = GmcExecDirect(stmt, createCommand4, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char alterTableCommand1[cmdLen] = "ALTER TABLE TABLECUSTOM4 ADD COLUMN new_col INTEGER;";
    ret = GmcExecDirect(stmt, alterTableCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\"");
    char dropCommand4[cmdLen] = "drop table tableCustom4";
    ret = GmcExecDirect(stmt, dropCommand4, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, createTableWithPathError)
{
    char createCommand1[cmdLen] = "create table tableCustomError1(start_time integer, end_time integer,  name char(9))"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path =);";
    Status ret = GmcExecDirect(stmt, createCommand1, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    char createCommand2[cmdLen] =
        "create table tableCustomError2(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_pth = '/home/');";
    ret = GmcExecDirect(stmt, createCommand2, cmdLen);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    char createCommand3[cmdLen] =
        "create table tableCustomError3(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = './');";
    ret = GmcExecDirect(stmt, createCommand3, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    char createCommand4[cmdLen] =
        "create table tableCustomError4(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '//');";
    ret = GmcExecDirect(stmt, createCommand4, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    char createCommand5[cmdLen] =
        "create table tableCustomError5(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '/home');";
    ret = GmcExecDirect(stmt, createCommand5, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    char createCommand6[cmdLen] =
        "create table tableCustomError6(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '/home/<USER>/');";
    ret = GmcExecDirect(stmt, createCommand6, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    char createCommand7[cmdLen] =
        "create table tableCustomError7(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '////');";
    ret = GmcExecDirect(stmt, createCommand7, cmdLen);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

TEST_F(StTsDdlGmc, createTableWithPathLength)
{
    char tablePath[200] = {0};
    int32_t offset = 0;
    size_t tablePathLen = 200;
    (void)snprintf_s(tablePath, tablePathLen, tablePathLen - 1, "/home/");
    offset = strlen(tablePath);
    memset_s(tablePath + offset, 200 - offset, 'b', 200 - offset);
    tablePath[198] = '/';
    tablePath[199] = '\0';
    char createCommand[1024] = {0};
    size_t createCommandLen = 1024;
    (void)snprintf_s(createCommand, createCommandLen, createCommandLen - 1,
        "create table tableCustom10(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '%s');",
        tablePath);
    Status ret = GmcExecDirect(stmt, createCommand, 1024);
    EXPECT_EQ(GMERR_OK, ret);
    char dropCommand[1024] = "drop table tableCustom10";
    ret = GmcExecDirect(stmt, dropCommand, 1024);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDdlGmc, createTableWithPathLength2)
{
    char tablePath[201] = {0};
    int32_t offset = 0;
    size_t tablePathLen = 200;
    (void)snprintf_s(tablePath, tablePathLen, tablePathLen - 1, "/home/");
    offset = strlen(tablePath);
    memset_s(tablePath + offset, 201 - offset, 'b', 201 - offset);
    tablePath[199] = '/';
    tablePath[200] = '\0';
    char createCommand[1024] = {0};
    size_t createCommandLen = 1024;
    (void)snprintf_s(createCommand, createCommandLen, createCommandLen - 1,
        "create table tableCustom10(start_time integer, end_time integer,  name char(9))"
        " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours', table_path = '%s');",
        tablePath);
    Status ret = GmcExecDirect(stmt, createCommand, 1024);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
}

TEST_F(StTsDdlGmc, CreateVolatileTable)
{
    char command1[300] =
        "create table tableVolatileCreate1(age integer, id integer) with (is_volatile_label = 'false', time_col = "
        "'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";
    Status ret = GmcExecDirect(stmt, command1, 300);
    EXPECT_EQ(ret, GMERR_OK);
    char command2[300] =
        "create table tableVolatileCreate2(age integer, id integer) with (is_volatile_label = 'true', time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";
    ret = GmcExecDirect(stmt, command2, 300);
    EXPECT_EQ(ret, GMERR_OK);
    char command3[300] = "create table tableVolatileCreate3(age integer, id integer) with (is_volatile_label = 'false',"
                         " time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(ret, GMERR_OK);
    char command4[300] =
        "create table tableVolatileCreate4(age integer, id integer) with (is_volatile_label = 'true', time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    ret = GmcExecDirect(stmt, command4, 300);
    EXPECT_EQ(ret, GMERR_INTERNAL_ERROR);
    char command5[300] =
        "create table tableVolatileCreate5(age integer, id integer) with (is_volatile_label = 'true', time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/data/gmdb/');";
    ret = GmcExecDirect(stmt, command5, 300);
    EXPECT_EQ(ret, GMERR_INTERNAL_ERROR);
    char command6[300] =
        "create table tableVolatileCreate6(age integer, id integer) with (is_volatile_label = 'true', time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '//data/');";
    ret = GmcExecDirect(stmt, command6, 300);
    EXPECT_EQ(ret, GMERR_INTERNAL_ERROR);

    char dropCommand1[300] = "drop table tableVolatileCreate1";
    ret = GmcExecDirect(stmt, dropCommand1, 300);
    EXPECT_EQ(GMERR_OK, ret);
    char dropCommand2[300] = "drop table tableVolatileCreate2";
    ret = GmcExecDirect(stmt, dropCommand2, 300);
    EXPECT_EQ(GMERR_OK, ret);
    char dropCommand3[300] = "drop table tableVolatileCreate3";
    ret = GmcExecDirect(stmt, dropCommand3, 300);
    EXPECT_EQ(GMERR_OK, ret);
}
