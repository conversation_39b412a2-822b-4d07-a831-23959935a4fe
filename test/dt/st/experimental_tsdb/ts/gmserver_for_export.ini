# Name : defaultTablespaceMaxSize  DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE
# Description : default table space maxsize, unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [deviceSize, maxSeMem – deviceSize]
# Default : 32
defaultTablespaceMaxSize = 32

# Name : deviceSize  DB_CFG_SE_DEV_SIZE
# Description : size of device, unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 1024]
# Default : 4
deviceSize = 4

# Name : pageSize  DB_CFG_SE_PAGE_SIZE
# Description : size of page, unit: K
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  {4, 8, 16, 32, 64}
# Default : 32
pageSize = 32

# Name : maxSeMem  DB_CFG_SE_MAX_MEM
# Description : total size of memory, unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [8, 16360] , arm32 - [8,3048]
# Default : 1024
maxSeMem = 1024

# Name : instanceId  DB_CFG_SE_GET_INSTANCE_ID
# Description : current storage engine of id
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 2]
# Default : 1
instanceId = 1

instanceType = 1

# Name : localLocatorListened  DB_CFG_LOCAL_LOCATOR
# Description : The local locator listened in the unix socket mode.
# ChangeMode : Not allowed to modify.
# Type : string
# Default : channel:ctl_channel
localLocatorListened = usocket:/run/verona/unix_emserver

# Name : logLengthMax  DB_CFG_LOG_LENGTH_MAX
# Description : The maximum length of one log.
# ChangeMode : Not allowed to modify.
# Type : int
# Range : [128, 1024]
# Default : 512
logLengthMax = 512

# Name : logFileNumMax  DB_CFG_LOG_FILE_NUM_MAX
# Description : The maximum number of log files.
# ChangeMode : Not allowed to modify.
# Type : int
# Range : [1, 1024]
# Default : 16
logFileNumMax = 16

# Name : logFileSizeMax  DB_CFG_LOG_FILE_SIZE_MAX
# Description : The maximum size of one log file.
# ChangeMode : Not allowed to modify.
# Type : int
# Range : [128, 2097152]
# Default : 2097152 (2*1024*1024)
logFileSizeMax = 2097152

# Name : enableLogFold DB_CFG_ENABLE_LOG_FOLD
# Description : use log fold or not.
# ChangeMode : Not limited to modify.
# Type : int
# Range : [0, 1]
# Default : 1
enableLogFold = 1

# Name : logFoldRule DB_CFG_LOG_FOLD_RULE
# Description : log fold rule, max nums of key:value pair is 5. Add a new key:value pair at the end of the rule and separate with comma.
# ChangeMode : Not limited to modify.
# Type : string
# Default : 1:1,3600:50
logFoldRule = 1:1,3600:50

# Name : isUseHugePage  DB_CFG_HUGE_PAGE
# Description : Use huge page for storage or not.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
isUseHugePage = 0

# Name : maxTotalShmSize  DB_CFG_SERVER_TOTAL_SHM_SIZE
# Description : Total size of share memory alloced by server, unit: M.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range :  [32, 16384] , arm32 - [32,3072]
# Default : 2048
maxTotalShmSize = 2048

# Name : maxTotalDynSize  DB_CFG_SERVER_TOTAL_DYN_SIZE
# Description : Total size of dynamic memory alloced by server, unit: M.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range :  [24, 16384] , arm32 - [24,3072]
# Default : 2048
maxTotalDynSize = 2048

# Name : maxSysShmSize  DB_CFG_MAX_SYS_SHM_MEM
# Description : Maximum shared memory that can be used by System partition, unit: M, less than maxTotalShmSize
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [12, 16364] , arm32 - [12,3052]
# Default : 256
maxSysShmSize = 256

# Name : maxSysDynSize  DB_CFG_MAX_SYS_DYN_MEM
# Description : Maximum dynamic memory that can be used by System partition, unit: M, less than maxTotalDynSize
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [12, 16351] , arm32 - [12,3039]
# Default : 512
maxSysDynSize = 1024

# Name : liteDynMemMod  DB_CFG_LITE_DYN_MEM_MOD
# Description : whether to enable light dynamic memory management. Once enabled, memory directly
# allocated from OS.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
liteDynMemMod = 0

# Name : planCacheSize  DB_CFG_PLAN_CACHE_SIZE
# Description : total size of plan cache，unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [8, 1048576]
# Default : 32
planCacheSize = 32

# Name : clientServerFlowControl  DB_CFG_CS_FLOW_CONTROL
# Description : is flow contorl in RPC mode (client ---> main store)
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
clientServerFlowControl = 0

# Name : overloadThreshold  DB_CFG_OVER_LOAD_THRESHOLD
# Description : the over threshold and recover threshold Client/Server:(cpu usage, dynamic memory usage, share memory usage) subscribe:(subscribe queue usage)
# ChangeMode : Not allowed to modify.
# Type : string
# Range :  all number must be in range[0, 100]
# Default : 70,80,80,85,85,90
overloadThreshold = cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;subscribeQueue:70,80,80,85,85,90

# Name : memCompactEnable  DB_CFG_MEM_COMPACT_ENABLE
# Description : back ground memory compact enable flag
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
memCompactEnable = 0

# Name : enableTableLock  DB_CFG_TABLE_LOCK_IS_ENABLED
# Description : table lock enable flag. If memCompactEnable = 1, enableTableLock will change to 0
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
enableTableLock = 0

# Name : minFragmentationRateThreshold  DB_CFG_DEFRAGMENTATION_RATE_THRESHOLD
# Description : the min threshold fragmentation rate to defragmentation for single heap
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 100]
# Default : 50
minFragmentationRateThreshold = 50

# Name : minFragmentationMemThreshold  DB_CFG_DEFRAGMENTATION_MEM_THRESHOLD
# Description : the min threshold memory size to defragmentation for single heap, unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 16384] , arm32 - [0,3072]
# Default : 64
minFragmentationMemThreshold = 64

# Name : longProcTimeThreshold  DB_CFG_LONG_PROC_TIME_THRESHOLD
# Description : Max process time for long process, range from -1 to 500, If the value is -1, slow operation logs are not recorded.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [-1, 500]
# Default : 100
longProcTimeThreshold = 100

# Name : maxUndoSpaceSize  DB_CFG_MAX_UNDO_SPACE_SIZE
# Description : Max memory size of undo space, range from 1 to 1024.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 1024]
# Default : 300
maxUndoSpaceSize = 300

# Name : trxLockWakeupPeriod  DB_CFG_TRX_LOCK_WAKEUP_PERIOD
# Description : period of trx lock wakeup, range from 1 to 1000000 ms.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 1000000]
# Default : 100
trxLockWakeupPeriod = 100

# Name : trxDeadlockCheckPeriod  DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD
# Description : period of trx lock deadlock checkout, range from 1 to 1000000 ms.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 1000000]
# Default : 300
trxDeadlockCheckPeriod = 300

# Name : latchDeadlockDebugTimeout  DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT
# Description : timeout of latch deadlock, range from 1 to 60000000 us.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 60000000]
# Default : 10000000
latchDeadlockDebugTimeout = 10000000

# Name : trxLockJumpQueuePeriod  DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD
# Description : period of trx lock jump-queue, range from 1 to 1000000 ms.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 1000000]
# Default : 500
trxLockJumpQueuePeriod = 500

# Name : trxLockTimeOut  DB_CFG_TRX_LOCK_TIME_OUT
# Description : default timeout of a trx lock waiting for Acquisition, range from 1 to 1000000 ms, it may be overwritten by the setting value from client.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 1000000]
# Default : 1000
trxLockTimeOut = 1000

# Name : trxMonitorEnable  DB_CFG_TRX_MONITOR_ENABLE
# Description : default disable transaction monitor.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
trxMonitorEnable = 1

# Name : trxMonitorThreshold  DB_CFG_TRX_MONITOR_THRESHOLD
# Description : first value is default time threshold of a trx write log and second value is for a trx rollback, both range from 1 to 1000 s.
# ChangeMode : Not allowed to modify.
# Type : string
# Range :  all number must be in range[1, 1000], uint: s
# Default : 60, 120
trxMonitorThreshold = 60,120

# Name : maxSortBufferSize  DB_CFG_MAX_SORT_BUFFER_SIZE
# Description : max size of sort buffer memory alloced by server, unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 256]
# Default : 64
maxSortBufferSize = 64

# Name : monitorWorkerSchedulePeriod  DB_CFG_MONITOR_WORKER_SCHEDULE_PERIOD
# Description : monitor worker schedule period from 0ms to 5000ms and 0 means monitor worker is not exist.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 5000]
# Default : 5000
monitorWorkerSchedulePeriod = 500

# Name : workerHungThreshold  DB_CFG_WORKER_HUNG_THRESHOLD
# Description : three level for worker hung process time,level 1 will write log for hung worker,level 2 will write log and reporting alarms. level 3 will exit DB server. unit:second. Must be at least 10 times greater than longProcTimeThreshold config, and level1 < level2 < level3. The value of level 1 must be more than three times monitorWorkerSchedulePeriod.
# ChangeMode : Not allowed to modify.
# Type : string
# Range :  all number must be in range[3, 1000]
# Default : 20,299,300
workerHungThreshold = 20,299,300

# Name : enableDmlOperStat  DB_CFG_DML_OPER_STAT_IS_ENABLED
# Description : enable dml operation statistics or not
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
enableDmlOperStat = 1

# Name : enableDmlPerfStat  DB_CFG_DML_PERF_STAT_IS_ENABLED
# Description : enable dml performance statistics or not
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
enableDmlPerfStat = 0

# Name : DBA  DB_CFG_DBA_INFO
# Description : database administrator(DBA), manager of database; DBA name and processes are separated by colon, and processes are separated by semicolon
# ChangeMode : Not allowed to modify.
# Type : string
# Range :  DBA name length should be less than 128Byte and each process name should be less than 16Byte, including string terminator
# Default : litedb_admin:gmrule;gmips;gmids;gmadmin
DBA = root:gmrule;gmips;gmids;gmadmin

# Name : agePushSubsBatch  DB_CFG_AGE_PUSH_SUBS_BATCH
# Description : set the amount of data pushed by aging task once.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [1, 200]
# Default : 10
agePushSubsBatch = 10

# Name : userPolicyMode  DB_CFG_USER_POLICY_MODE
# Description : set the authentication mode : 0-disabled  1-permissive  2-enforcing
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1, 2]
# Default : 2
userPolicyMode = 2

# Name : enablePrintAgedInfo  DB_CFG_PRINT_AGED_INFO_ENABLED
# Description : enable print aged information or not
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
enablePrintAgedInfo = 0

# Name : lpm4VrIdMax DB_CFG_LPM4_VRID_MAX
# Description : user can config vrid max value of ipv4 lpm(Longest Prefix Match), default 16, upper limit is 4096, valid vrid range:[16, lpm4VrIdMax).
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [16, 4096]
# Default : 16
lpm4VrIdMax = 16

# Name : lpm4VrfIdMax DB_CFG_LPM4_VRFID_MAX
# Description : user can config vrfid max value of ipv4 lpm(Longest Prefix Match), default 1024, upper limit is 16384, valid vrfid range:[1024, lpm4VrfIdMax).
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [1024, 16384]
# Default : 1024
lpm4VrfIdMax = 1024

# Name : lpm6VrIdMax DB_CFG_LPM6_VRID_MAX
# Description : user can config vrid max value of ipv6 lpm(Longest Prefix Match), default 16, upper limit is 4096, valid vrid range:[16, lpm6VrIdMax).
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [16, 4096]
# Default : 16
lpm6VrIdMax = 16

# Name : lpm6VrfIdMax DB_CFG_LPM6_VRFID_MAX
# Description : user can config vrfid max value of ipv6 lpm(Longest Prefix Match), default 1024, upper limit is 16384, valid vrfid range:[1024, lpm6VrfIdMax).
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [1024, 16384]
# Default : 1024
lpm6VrfIdMax = 1024

# Name : maxConnNum DB_CFG_CONN_MAX
# Description : Maximum number of connections supported by the server, default 100, range:[16, 1024].
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [16, 1024]
# Default : 100
maxConnNum = 100

# Name : maxStmtCnt DB_CFG_MAX_STMT_CNT
# Description : max count of stmt per session in server.
# ChangeMode : Not allowed to set a new value less than the old.
# Type : int32_t
# Range : [1, 65535]
# Default : 10240
maxStmtCnt = 10240

# Name : scheduleMode DB_CFG_SCHEDULE_MODE
# Description : Server schedule mode: 0 = thread per connection, 1 = recv agent, 2 = thread pool, 3 = single thread
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 3]
# Default : 0
scheduleMode = 0

# Name : permanentWorkerNum DB_CFG_PERMANENT_WORKER_NUM
# Description : Core worker number for worker pool.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [1, 1024]
# Default : 4
permanentWorkerNum = 4

# Name : shmCoreDumpMode DB_CFG_SHM_COREDUMP_MODE
# Description : Server shared memory coredump mode, only for storage engine.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1]
# Default : 0
 

# Name : subsChannelGlobalShareMemSizeMax DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX
# Description : Maximum share memory that can be used by all subscription channels, unit: MB
# ChangeMode : Not allowed to modify.
# Type : int
# Range : [1, 2048]
# Default : 512
subsChannelGlobalShareMemSizeMax = 512

# Name : subsChannelGlobalDynamicMemSizeMax DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX
# Description : Maximum dynamic memory that can be used by RTOS subscription channels, unit: MB
# ChangeMode : Not allowed to modify.
# Type : int
# Range : [1, 2048]
# Default : 512
subsChannelGlobalDynamicMemSizeMax = 512

# Name : compatibleV3 DB_CFG_COMPATIBLE_V3
# Description : compatible V3.
# ChangeMode : Not limited to modify.
# Type : int
# Range : [0, 1]
# Default : 1
compatibleV3 = 1

# Name : isFastReadUncommitted DB_CFG_IS_FAST_READ_UNCOMMITTED
# Description : fast read uncommitted or not.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
isFastReadUncommitted = 0

# Name : auditLogEnableDCL DB_CFG_AUDIT_LOG_DCL_ENABLE
# Description : Audit log DCL enable flag.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
auditLogEnableDCL = 1

# Name : auditLogEnableDDL DB_CFG_AUDIT_LOG_DDL_ENABLE
# Description : Audit log DDL enable flag.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
auditLogEnableDDL = 1

# Name : auditLogEnableDML DB_CFG_AUDIT_LOG_DML_ENABLE
# Description : Audit log DML enable flag.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
auditLogEnableDML = 1

# Name : auditLogEnableDQL DB_CFG_AUDIT_LOG_DQL_ENABLE
# Description : Audit log DQL enable flag.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
auditLogEnableDQL = 1

# Name : isCltStatisEnable  DB_CFG_IS_CLT_STATIS_ENABLE
# Description : If true, server enable to receive statistics from client, otherwise, server did not receive.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
isCltStatisEnable = 1

# Name : enableConSubsStatis  DB_CFG_ENABLE_CONN_SUBS_STATIS_DESC
# Description : Sub-pub Statistic enable flag.
# ChangeMode : allow to modify..
# Type : int32_t
# Range :  [0, 1]
# Default : 1
enableConSubsStatis = 1

# Name : enableSchedulePerfStat  DB_CFG_SCHEDULE_PERF_STAT_IS_ENABLED
# Description : enable statistics related to the actual CPU time usage or not, default 0(disabled).
# ChangeMode : allow to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
enableSchedulePerfStat = 0

# Name : messageSecurityCheck  DB_CFG_MESSAGE_SECURITY_CHECK
# Description : If true, server will check whether the messages and buffers are legal, default 1(abled).
# ChangeMode : allow to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 1
messageSecurityCheck = 1

# Name : shmemPermission
# Description : Shared memory permissions, default value is "0600", which means shm's owner can read/write, but group and other users can't read/write/exec.
# ChangeMode : Not allowed to modify after elegant exit and restart.
# Type : string
# DefaultValue : 0600
shmemPermission = 0600

# Name : defaultTransactionType  DB_CFG_DEFAULT_TRANSACTION_TYPE
# Description : Type of the transaction mode. The options are optimistic and pessimistic. 0-PESSIMISTIC_TRX 1-OPTIMISTIC_TRX
# ChangeMode : Not allow to modify.
# Type : int32_t
# Range :  [0, 1]
# DefaultValue : 0
defaultTransactionType = 0

# Name : defaultIsolationLevel  DB_CFG_DEFAULT_ISOLATION_LEVEL
# Description : Isolation Level Type : 0-READ_UNCOMMITTED 1-READ_COMMITTED 2-REPEATABLE_READ 3-SERIALIZABLE
# ChangeMode : Not allow to modify.
# Type : int32_t
# Range :  [0, 1, 2, 3]
# DefaultValue : 1
defaultIsolationLevel = 1

# Name : hashClusterBucketsCount DB_CFG_HASH_CLUSTER_BUCKET_CNT
# Description : HashCluster key's hash buckets count.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [100, 10000000]
# Default : 16384
hashClusterBucketsCount = 16384

# Name : yangBigObjectSize DB_CFG_YANG_BIG_OBJECT_SIZE
# Description : Check whether the response from the server is a large object.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [1, 65536]
# Default : 512
yangBigObjectSize = 512

# Name : maxConnMsgShmMem  DB_CFG_MAX_CONN_MSG_SHM_MEM
# Description : Maximum shared memory that can be used by each connection, excluding subs connections, unit: M
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [8, 16384]
# Default : 2048
maxConnMsgShmMem = 2048

# Name : shareSubMsgTimeout DB_CFG_SHM_SUB_MSG_TIMEOUT
# Description : The client consumes subscription data within the timeout period, and the server reclaims the memory. unit: min
# ChangeMode : allowed to modify.
# Type : int32_t
# Range : [1, 65536]
# Default : 1440
shareSubMsgTimeout = 1440

# Name : datalogTimeoutScheduleInterval  DB_CFG_DATALOG_TIMEOUT_SCHEDULE_INTERVAL
# Description : Datalog Timeout schedule interval is used to set the cycle schedule interval of background task. unit: ms.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [100, 5000]
# Default : 2000
datalogTimeoutScheduleInterval = 2000

# Name : datalogCallBackTimeoutThreshold  DB_CFG_DATALOG_CALLBACK_TIMEOUT_THRESHOLD
# Description : Datalog timeout duration of the pubsub callback. unit: s.
# ChangeMode : Allowed to modify.
# Type : int32_t
# Range : [1, 10]
# Default : 2
datalogCallBackTimeoutThreshold = 2

# Name : udfTimeOut  DB_CFG_DTL_UDF_TIME_OUT
# Description : Timeout of single UDF function, range from 0 to 1000. unit: ms.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1000]
# Default : 1000
udfTimeOut = 1000

# Name : connectTimeout  DB_CFG_CONNECT_TIMEOUT
# Description : Timeout duration of the connection, unit min. If there is no service request within the specified period of time (Direct read is a service request, but heartbeat is not a service request.) The server closes the connection. The value 0 indicates that the function is disabled.
# ChangeMode : Allowed to modify.
# Type : int
# Range : [0, 1000000]
# Default : 0
connectTimeout = 0

# Name : udfMemSizeMax  DB_CFG_UDF_MAX_MEM
# Description : Maximum dynamic memory that can be allocated in udf in one session. unit: KB.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [1, 2048]
# Default : 2048
udfMemSizeMax = 2048

# Name : udfMemAllocAlarmSize  DB_CFG_UDF_MEM_ALLOC_ALARM
# Description : Alarm size of alloc memory in udf. Record alarm information in log when the limit is exceeded. unit: KB
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 2048]
# Default : 2048
udfMemAllocAlarmSize = 2048

# Name : udfEnable DB_CFG_UDF_ENABLE
# Description : If true, server enables loading UDF, otherwise server cannot load.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1]
# Default : 0
udfEnable = 0

# Name : datalogRunLinkLog  DB_CFG_DATALOG_RUN_LINK_LOG
# Description : Datalog Run Link Log Level.
# ChangeMode : Allowed to modify without limit.
# Type : int32_t
# Range : [0, 3]
# Default : 0
datalogRunLinkLog = 0

# Name : enableClusterHash DB_CFG_CLUSTER_HASH_IS_ENABLED
# Description : If true, server enables cluster hash, otherwise server cannot use cluster hash.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1]
# Default : 1
enableClusterHash = 0

# Name : maxNormalTableNum DB_CFG_MAX_NORMAL_TABLE_NUM
# Description : Max count of normal vertexlabel in server.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [1000, 10000]
# Default : 2000
maxNormalTableNum = 2000

# Name : maxYangTableNum DB_CFG_MAX_YANG_TABLE_NUM
# Description : Max count of yang vertexlabel in server.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [1000, 10000]
# Default : 2000
maxYangTableNum = 2000

# Name : featureLibPath DB_CFG_FEATURE_LIB_PATH
# Description : The so lib path for dynamic load features.
# ChangeMode : allow to modify.
# Type : string
# Default : (empty)
featureLibPath =

# Name : featureNames DB_CFG_FEATURE_NAMES
# Description : The feature names of dynamic load features.
# ChangeMode : allow to modify.
# Type : string
# Default : (empty)
featureNames = build_all_type

# Name : schemaLoader DB_CFG_NEED_SCHEMA_LOADER
# Description : If true, server import schema resources when DB start, otherwise server do not import.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1]
# Default : 0
schemaLoader = 0
 
# Name : schemaPath  DB_CFG_SCHEMA_PATH
# Description : File paths where server import schema resources from (Please open schemaLoader first).
# Four paths need to be specified for importing users，sysPrivilege, objests, objPrivilege respectively;
# Paths are separated by semicolon, and can be relative path or absolute path;
# Range :  File paths length should be less than 256Byte, including string terminator
# ChangeMode : Not allowed to modify.
# Type : string
# Default : /etc/gmdb;/etc/gmdb_sys_priv;/etc/gmjson;/etc/gmpolicy;
schemaPath = /etc/gmdb;/etc/gmdb_sys_priv;/etc/gmjson;/etc/gmpolicy;

# Name : persistentMode  DB_CFG_PERSISTENT_MODE
# Description : 0 is on demand, 1 is increment
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
persistentMode = 1
 
# Name : redoPubBufSize DB_CFG_REDO_PUB_BUF_SIZE
# Description : redo public buffer size, unit is KB.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range : [256, 16384]
# Default : 4096
redoPubBufSize = 4096
 
# Name : redoBufParts DB_CFG_REDO_BUF_PARTS
# Description : redo public buffer part count.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range : [1, 16]
# Default : 4
redoBufParts = 4
 
# Name : redoFlushByTrx DB_CFG_REDO_FLUSH_BY_TRX
# Description : Flush redo log on transaction commit
# ChangeMode : Not limited to modify.
# Type : bool
# Range : [0, 1]
# Default : 1
redoFlushByTrx = 1
 
# Name : redoFlushBySize DB_CFG_REDO_FLUSH_BY_SIZE
# Description : redo log will be flushing while buffer size greater than threadhold, unit is KB."0" meats disable.
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range : [0, 4096]
# Default : 1000 if enable
redoFlushBySize = 1000
 
# Name : redoFlushByTime DB_CFG_REDO_FLUSH_BY_TIME
# Description : redo log will be flushing while  time interval greater than threadhold, unit is ms."0" meats disable.
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range : [0, 600000]
# Default : 0, 1000 if enable
redoFlushByTime = 1000

# Name : redoFlushCheckPeriod DB_CFG_REDO_FLUSH_CHECK_PERIOD
# Description : redo flush check period
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range : [10, 300000]
# Default : 500
redoFlushCheckPeriod = 500
 
# Name : redoFileSize DB_CFG_REDO_FILE_SIZE
# Description : redo log file size, unit is MB.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range : [16, 1024]
# Default : 128
redoFileSize = 16
 
# Name : redoFileCount DB_CFG_REDO_FILE_COUNT
# Description : redo log file count.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range : [2, 64]
# Default : 32
redoFileCount = 2

# Name : dbFilesMaxCnt DB_CFG_DB_FILES_MAX_COUNT
# Description : space data file max count.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [3, 1024]
# Default : 3
dbFilesMaxCnt = 1024

# Name : spaceMaxNum DB_CFG_SPACE_MAX_NUM
# Description : space max count. <= dbFilesMaxCnt
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [3, 1024]
# Default : 3
spaceMaxNum = 1024

# Name : dataFileDirPath DB_CFG_DATA_FILE_DIR_PATH
# Description : db data file directory path.
# ChangeMode : Not allowed to modify
# Type : string
# Default : NULL
# Notice : must be specified if the storageMode is 1 or 2
dataFileDirPath = ./data/gmdb,./data/gmdb2

# Name : dbFileSize DB_CFG_DB_FILE_SIZE
# Description : db space file max size, unit:KB
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range :  [4096,33554432]
# Default : 1048576
dbFileSize = 1048576

# Name : ckptPeriod DB_CFG_CKPT_PERIOD
# Description : The period of checkpoint, unit: second.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range : [1, 65535]
# Default : 60
ckptPeriod = 60

# Name : ckptThreshold DB_CFG_CKPT_THLD
# Description : Trigger checkpoint flush if its queue length is greater than threshold
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range : [2, 268435456]
# Default : 10000
ckptThreshold = 10000

# Name : bufferPoolSize DB_CFG_SE_BUFFERPOOL_SIZE
# Description : init bufferpool size by configuration, unit: KB
# Type : uint64_t
# Range : [256, 4194304] larger than the maximum number of pages in a redo operation and less than (4GB - pageSize).
# Default : 4096
bufferPoolSize = 4096

# Name : bufferPoolPolicy DB_CFG_SE_BUFFERPOOL_POLICY
# Description : policy of bufpool to recycle pages
# Type : uint32_t
# Range : [0,2] 0 means normal, 1 means table first, 2 means index first
# Default : 0
bufferPoolPolicy = 0

# Name : tempFileDir DB_CFG_TEMP_FILE_DIR
# Description : Temp file directory.
# ChangeMode : Not allowed to modify.
# Type : string
# Default : /data/gmdb/temp
tempFileDir = /data/gmdb/temp

# Name : forceUseTempFileForQueryResult DB_CFG_FORCE_USE_TEMP_FILE_QUERY_RESULT
# Description : Force using temp file to store query result.
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range :  [0, 1]
# Default : 0
forceUseTempFileForQueryResult = 0

# Name : operatorMemory DB_CFG_OP_MEMORY
# Description : Memory size for storing intermediate results of the operator, unit:M.
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range : [1, 1024]
# Default : 100
operatorMemory = 100

# Name : cStoreDir DB_CFG_CSTORE_DIR
# Description : cStore directory. (Only TS feature used now)
# ChangeMode : Not allowed to modify.
# Type : string
# Default : /data/gmdb/cstore
cStoreDir = ./data/gmdb/cstore

# Name : vectorizedOperationPushDown DB_CFG_ENABLE_VECTORIZED_PUSHDOWN
# Description : whether push down vectorized operation
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range : [0, 1]
# Default : 0
enableVectorizedPushDown=0

# Name : tsLcmCheckPeriod DB_CFG_TS_TCM_CHECK_PERIOD
# Description : the period of LCM background thread check(Only TS feature used now). unit: second
# ChangeMode : allow to modify.
# Type : uint32_t
# Range : [3, 3600]
# Default : 3600
tsLcmCheckPeriod = 3600

# Name : isFileTapeCheckSum DB_CFG_IS_FILE_TAPE_CHECK_SUM_DESC
# Description : Indicates whether to perform checkSum when reading or writing a temporary file block.
# ChangeMode : allow to modify.
# Type : uint32_t
# Range : [0, 1]
# Default : 0
isFileTapeCheckSum = 0

# Name : multizonePersist DB_CFG_MULTIZON_PERSIST
# Description : enable/disable persistent file backup，only when multizonePersist is 1, redoDir2/datafileDirPath2/ctlFileDirPath2 is valid
# ChangeMode : Not limited to modify.
# Type : int32_t
# Range : [1, 2]
# Default : 1
multizonePersistNum = 2
