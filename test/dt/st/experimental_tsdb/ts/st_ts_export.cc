/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: st for ts gmexport tool
 * Author: zhangjinglong
 * Create: 2024-10-18
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc.h"
#include "gmc_sql.h"
#include "dm_meta_prop_strudefs.h"
#include "clt_stmt.h"

const static uint32_t waitTime = 1000;  // ms
const static char *g_cfgPersist = "ts/gmserver_for_export.ini";
static GmcConnOptionsT *connOptions = NULL;
static uint32_t cmdLen = 256;

class StTsExport : public testing::Test {
protected:
    virtual void SetUp()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf ./data/gmdb");
        system("rm -rf ./data/gmdb2");
        DbSleep(waitTime);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(waitTime);
        GmcInit();
        GmcConnOptionsCreate(&connOptions);
        GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
        GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
        GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
        GmcConnOptionsSetCSRead(connOptions);
    }
    virtual void TearDown()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcUnInit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf ./data/gmdb");
        system("rm -rf ./data/gmdb2");
    }
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

static bool AreTwoFileSame(const char *filePath1, const char *filePath2)
{
    // 打开两个文件
    FILE *file1 = fopen(filePath1, "r");
    FILE *file2 = fopen(filePath2, "r");

    // 如果任何一个文件无法打开，则返回错误
    if (file1 == NULL || file2 == NULL) {
        return false;
    }

    bool same = true;
    char line1[1024];
    char line2[1024];
    bool stopComparison = false;

    // 逐行读取文件
    while (fgets(line1, sizeof(line1), file1) != NULL && fgets(line2, sizeof(line2), file2) != NULL) {
        // 查找 "gmsysview" 字符串，若找到则停止比较
        if (stopComparison) {
            break;
        }

        if (strstr(line1, "gmsysview") != NULL || strstr(line2, "gmsysview") != NULL) {
            stopComparison = true;
            continue;  // 不再比较此行和之后的内容
        }

        // 如果当前行不相同，则返回错误
        if (strcmp(line1, line2) != 0) {
            same = false;
            break;
        }
    }

    // 如果文件长度不同，且未达到停止比较的条件，也应认为不相同
    if (!stopComparison && (fgets(line1, sizeof(line1), file1) != NULL || fgets(line2, sizeof(line2), file2) != NULL)) {
        same = false;
    }

    // 关闭文件
    fclose(file1);
    fclose(file2);

    return same;
}

static const uint32_t g_maxLen = 1024;
static void PrintTableData(const char *vlName, const char *fileName)
{
    Status ret;
    char command[g_maxLen] = {};
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1, "gmsysview -sql \"select * from %s\" > %s", vlName, fileName);
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);
}

static void PrintVertexLabelView(const char *vlName, const char *fileName)
{
    Status ret;
    char command[g_maxLen] = {};
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME='%s'\" | grep -v "
        "\"REF_COUNT\" | grep -iv \"id\"> %s",
        vlName, fileName);
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);
}

static void PrintUserRoleView(const char *processName, const char *fileName)
{
    Status ret;
    char command[g_maxLen] = {};
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1,
        "gmsysview -sql \"select * from 'V\\$PRIVILEGE_USER_STAT' where PROCESS_NAME='%s'\" | grep -v \"REF_COUNT\"> "
        "%s",
        processName, fileName);
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);

    (void)snprintf_s(command, g_maxLen, g_maxLen - 1,
        "gmsysview -sql \"select * from 'V\\$PRIVILEGE_ROLE_STAT' where PROCESS_NAME='%s'\" | grep -v \"REF_COUNT\">> "
        "%s",
        processName, fileName);
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);
}

static void CompareFileLastLineWithStr(const char *fileName, const char *str)
{
    char lastLine[g_maxLen] = {};
    FILE *fp = fopen(fileName, "r");

    if (fp == NULL) {
        EXPECT_TRUE(false);
    }

    // 逐行读取，直到文件结束
    while (fgets(lastLine, sizeof(lastLine), fp) != NULL) {
        // 保留最后一行
    }

    // 检查最后一行是否为空
    if (strlen(lastLine) == 0) {
        EXPECT_TRUE(false);  // 文件为空
    }

    // 比较最后一行与给定的字符串
    EXPECT_EQ(strcmp(lastLine, str), 0);

    // 关闭文件
    fclose(fp);
}

static uint32_t GetFileSize(const char *fileName)
{
    struct stat statbuf;
    stat(fileName, &statbuf);
    return (uint32_t)statbuf.st_size;
}

// Property name mismatch
static void MatchStringInLog(const char *str)
{
    Status ret = system("rm -f tmpfile.txt");
    EXPECT_EQ(ret, GMERR_OK);
    char command[g_maxLen] = {0};
    (void)snprintf_s(command, g_maxLen, g_maxLen - 1, "grep -ri \"%s\" ./log > tmpfile.txt", str);
    ret = system(command);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(GetFileSize("tmpfile.txt") > 0);
    ret = system("rm -f tmpfile.txt");
    EXPECT_EQ(ret, GMERR_OK);
}

static void CleanFileBeforeTest(void)
{
    Status ret = system("rm -rf ./data/export");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("rm -rf ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("rm -rf view_*");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("rm -rf data_*");
    EXPECT_EQ(ret, GMERR_OK);
}

static void InsertDataToLogicalTable(GmcStmtT *stmt, const char *tableName)
{
    uint32_t rowNum = 6;
    int64_t start_time[] = {29, 30, 19, 33, 45, 46};
    int64_t end_time[] = {1, 5400, 1800, 3000, 3610, 5000};
    Status ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, start_time, sizeof(start_time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, end_time, sizeof(end_time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

// 0、gmexport 工具导出系统表数据，DB重新启动，迁入目录下无持久化文件，DB 走第一次启动流程；校验两次启动的元数据是否相同
TEST_F(StTsExport, DISABLED_exportToolTest)
{
    CleanFileBeforeTest();

    // 导白名单导系统权限
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy_for_export.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand1[cmdLen] =
        "CREATE TABLE t_thrt_org(log_id INTEGER, log_time INTEGER, src_ip inet, dst_ip inet, packet_id CHAR(256), "
        "end_time INTEGER) WITH (time_col = 'end_time', interval = '1 hour', compression = 'fast');";
    ret = GmcExecDirect(tmpStmt, createCommand1, strlen(createCommand1));
    EXPECT_EQ(GMERR_OK, ret);

    // 赋表级对象权限
    char grantCmd[cmdLen] = "GRANT SELECT ON testdb TO public;";
    ret = GmcExecDirect(tmpStmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_OK, ret);
    char grantCmd1[cmdLen] = "GRANT UPDATE ON t_thrt_org TO 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, grantCmd1, strlen(grantCmd1));
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("mkdir -p ./data/export");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -f ./data/export -c systbl_data -u usertest");
    EXPECT_EQ(ret, GMERR_OK);

    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl1.txt");
    PrintVertexLabelView((const char *)"t_thrt_org", (const char *)"view_t_thrt_org_vl1.txt");
    PrintUserRoleView((const char *)"st_experimental", (const char *)"view_st_experimental_user1.txt");
    PrintUserRoleView((const char *)"gmsysview", (const char *)"view_gmsysview_user1.txt");
    PrintUserRoleView((const char *)"priv_execsql", (const char *)"view_priv_execsql_user1.txt");
    PrintUserRoleView((const char *)"gmtest", (const char *)"view_gmtest_user1.txt");

    ret = system("gmsysview -sql \"select * from 'V\\$CATA_GENERAL_INFO'\"");
    EXPECT_EQ(ret, GMERR_OK);

    const char *newCfgPersist = "ts/gmserver_priv_migrate.ini";
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)newCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl2.txt");
    PrintVertexLabelView((const char *)"t_thrt_org", (const char *)"view_t_thrt_org_vl2.txt");
    PrintUserRoleView((const char *)"st_experimental", (const char *)"view_st_experimental_user2.txt");
    PrintUserRoleView((const char *)"gmsysview", (const char *)"view_gmsysview_user2.txt");
    PrintUserRoleView((const char *)"priv_execsql", (const char *)"view_priv_execsql_user2.txt");
    PrintUserRoleView((const char *)"gmtest", (const char *)"view_gmtest_user2.txt");

    EXPECT_TRUE(AreTwoFileSame((const char *)"view_testdb_vl1.txt", (const char *)"view_testdb_vl2.txt"));
    EXPECT_TRUE(AreTwoFileSame((const char *)"view_t_thrt_org_vl1.txt", (const char *)"view_t_thrt_org_vl2.txt"));
    EXPECT_TRUE(
        AreTwoFileSame((const char *)"view_st_experimental_user1.txt", (const char *)"view_st_experimental_user2.txt"));
    EXPECT_TRUE(AreTwoFileSame((const char *)"view_gmsysview_user1.txt", (const char *)"view_gmsysview_user2.txt"));
    EXPECT_TRUE(
        AreTwoFileSame((const char *)"view_priv_execsql_user1.txt", (const char *)"view_priv_execsql_user2.txt"));
    EXPECT_TRUE(AreTwoFileSame((const char *)"view_gmtest_user1.txt", (const char *)"view_gmtest_user2.txt"));

    ret = system("mkdir -p ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -c systbl_data -f ./data/export1 -s usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand3[cmdLen] = "create table testdb2(start_time integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand3, strlen(createCommand3));
    EXPECT_EQ(ret, GMERR_OK);

    InsertDataToLogicalTable(tmpStmt, (const char *)"testdb");
    InsertDataToLogicalTable(tmpStmt, (const char *)"testdb2");
    PrintTableData((const char *)"testdb", (const char *)"data_testdb.txt");
    PrintTableData((const char *)"testdb2", (const char *)"data_testdb2.txt");

    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand1[cmdLen] = "drop table t_thrt_org;";
    ret = GmcExecDirect(tmpStmt, dropCommand1, strlen(dropCommand1));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand2[cmdLen] = "drop table testdb2;";
    ret = GmcExecDirect(tmpStmt, dropCommand2, strlen(dropCommand2));
    EXPECT_EQ(ret, GMERR_OK);
}

// 1、建 2 张表，写入数据，gmexport 工具导出系统表数据，DB重新启动，迁入目录下有持久化文件，DB
// 走重启动流程；表定义完全一致，校验表元数据存在，表数据也存在
TEST_F(StTsExport, exportToolTest1)
{
    CleanFileBeforeTest();

    // 导白名单导系统权限
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy_for_export.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand1[cmdLen] = "create table testdb2(start_time integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand1, strlen(createCommand1));
    EXPECT_EQ(GMERR_OK, ret);

    // 赋表级对象权限
    char grantCmd[cmdLen] = "GRANT SELECT ON testdb TO public;";
    ret = GmcExecDirect(tmpStmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_OK, ret);
    char grantCmd1[cmdLen] = "GRANT UPDATE ON testdb2 TO 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, grantCmd1, strlen(grantCmd1));
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    InsertDataToLogicalTable(tmpStmt, (const char *)"testdb");
    InsertDataToLogicalTable(tmpStmt, (const char *)"testdb2");
    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl1.txt");
    PrintVertexLabelView((const char *)"testdb2", (const char *)"view_testdb2_vl1.txt");
    PrintTableData((const char *)"testdb", (const char *)"data_testdb_1.txt");
    PrintTableData((const char *)"testdb2", (const char *)"data_testdb2_1.txt");

    ret = system("mkdir -p ./data/export");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb/* ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    // 删表再建表
    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(ret, GMERR_OK);
    char createCommand2[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand2, strlen(createCommand2));
    EXPECT_EQ(GMERR_OK, ret);
    char grantCmd2[cmdLen] = "GRANT SELECT ON testdb TO public;";
    ret = GmcExecDirect(tmpStmt, grantCmd2, strlen(grantCmd2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmexport -s usocket:/run/verona/unix_emserver -c systbl_data -f ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    const char *newCfgPersist = "ts/gmserver_priv_migrate.ini";
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)newCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl2.txt");
    PrintVertexLabelView((const char *)"testdb2", (const char *)"view_testdb2_vl2.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"view_testdb_vl1.txt", (const char *)"view_testdb_vl2.txt"));
    EXPECT_TRUE(AreTwoFileSame((const char *)"view_testdb2_vl1.txt", (const char *)"view_testdb2_vl2.txt"));

    ret = system("mkdir -p ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -c systbl_data -f ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);

    PrintTableData((const char *)"testdb", (const char *)"data_testdb_2.txt");
    PrintTableData((const char *)"testdb2", (const char *)"data_testdb2_2.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"data_testdb_1.txt", (const char *)"data_testdb_2.txt"));
    EXPECT_TRUE(AreTwoFileSame((const char *)"data_testdb2_1.txt", (const char *)"data_testdb2_2.txt"));
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);

    char createCommand3[cmdLen] = "create table testdb3(start_time integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand3, strlen(createCommand3));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand1[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand1, strlen(dropCommand1));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand2[cmdLen] = "drop table testdb2;";
    ret = GmcExecDirect(tmpStmt, dropCommand2, strlen(dropCommand2));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand3[cmdLen] = "drop table testdb3;";
    ret = GmcExecDirect(tmpStmt, dropCommand3, strlen(dropCommand3));
    EXPECT_EQ(ret, GMERR_OK);
}

// 2、先建表再删表，gmexport 工具导出系统表数据，DB重新启动，迁入目录下有持久化文件，DB
// 走重启动流程；校验表元数据不存在，因为以 gmexport 导出的表为准
TEST_F(StTsExport, exportToolTest2)
{
    CleanFileBeforeTest();

    // 导白名单导系统权限
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy_for_export.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand1[cmdLen] =
        "CREATE TABLE t_thrt_org(log_id INTEGER, log_time INTEGER, src_ip inet, dst_ip inet, packet_id CHAR(256), "
        "end_time INTEGER) WITH (time_col = 'end_time', interval = '1 hour', compression = 'fast');";
    ret = GmcExecDirect(tmpStmt, createCommand1, strlen(createCommand1));
    EXPECT_EQ(GMERR_OK, ret);

    // 赋表级对象权限
    char grantCmd[cmdLen] = "GRANT SELECT ON testdb TO public;";
    ret = GmcExecDirect(tmpStmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_OK, ret);
    char grantCmd1[cmdLen] = "GRANT UPDATE ON t_thrt_org TO 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, grantCmd1, strlen(grantCmd1));
    EXPECT_EQ(GMERR_OK, ret);

    // 删表之前将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/export");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb/* ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    InsertDataToLogicalTable(tmpStmt, (const char *)"testdb");
    PrintTableData((const char *)"testdb", (const char *)"data_testdb_1.txt");

    char dropCmd1[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCmd1, strlen(dropCmd1));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCmd2[cmdLen] = "drop table t_thrt_org;";
    ret = GmcExecDirect(tmpStmt, dropCmd2, strlen(dropCmd2));
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("gmexport -c systbl_data -f ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    const char *newCfgPersist = "ts/gmserver_priv_migrate.ini";
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)newCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    // 重启后表不存在，视图查不到
    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl.txt");
    PrintVertexLabelView((const char *)"t_thrt_org", (const char *)"view_t_thrt_org_vl.txt");
    const char *str = "fetched all records, finish!\n";
    CompareFileLastLineWithStr((const char *)"view_testdb_vl.txt", str);
    CompareFileLastLineWithStr((const char *)"view_t_thrt_org_vl.txt", str);
    // 重启后 user 和 role 视图缺少表级对象权限的部分

    ret = system("mkdir -p ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -c systbl_data -f ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);

    char dropCommand[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand, strlen(dropCommand));
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    char createCmd[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                             " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCmd, strlen(createCmd));
    EXPECT_EQ(GMERR_OK, ret);
    InsertDataToLogicalTable(tmpStmt, (const char *)"testdb");
    PrintTableData((const char *)"testdb", (const char *)"data_testdb_2.txt");
    EXPECT_TRUE(AreTwoFileSame((const char *)"data_testdb_1.txt", (const char *)"data_testdb_2.txt"));

    char createCommand2[cmdLen] = "create table testdb2(start_time integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand2, strlen(createCommand2));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand1[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand1, strlen(dropCommand1));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand2[cmdLen] = "drop table t_thrt_org;";
    ret = GmcExecDirect(tmpStmt, dropCommand2, strlen(dropCommand2));
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    char dropCommand3[cmdLen] = "drop table testdb2;";
    ret = GmcExecDirect(tmpStmt, dropCommand3, strlen(dropCommand3));
    EXPECT_EQ(ret, GMERR_OK);
}

// 3、建 2 张表，删 1 张表，gmexport 工具导出系统表数据，再建 1 张表，DB重新启动，迁入目录下有持久化文件，DB
// 走重启动流程；校验表元数据只存在 1 个，因为以 gmexport 导出的表为准
TEST_F(StTsExport, exportToolTest3)
{
    CleanFileBeforeTest();

    // 导白名单导系统权限
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy_for_export.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand1[cmdLen] =
        "CREATE TABLE t_thrt_org(log_id INTEGER, log_time INTEGER, src_ip inet, dst_ip inet, packet_id CHAR(256), "
        "end_time INTEGER) WITH (time_col = 'end_time', interval = '1 hour', compression = 'fast');";
    ret = GmcExecDirect(tmpStmt, createCommand1, strlen(createCommand1));
    EXPECT_EQ(GMERR_OK, ret);

    // 赋表级对象权限
    char grantCmd[cmdLen] = "GRANT SELECT ON testdb TO public;";
    ret = GmcExecDirect(tmpStmt, grantCmd, strlen(grantCmd));
    EXPECT_EQ(GMERR_OK, ret);
    char grantCmd1[cmdLen] = "GRANT UPDATE ON t_thrt_org TO 'root:priv_execsql';";
    ret = GmcExecDirect(tmpStmt, grantCmd1, strlen(grantCmd1));
    EXPECT_EQ(GMERR_OK, ret);

    // 删表之前将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    char dropCmd1[cmdLen] = "drop table t_thrt_org;";
    ret = GmcExecDirect(tmpStmt, dropCmd1, strlen(dropCmd1));
    EXPECT_EQ(ret, GMERR_OK);
    DbSleep(2000);
    ret = system("gmexport -c systbl_data -f ./data/export");  // 只有 testdb 表
    EXPECT_EQ(ret, GMERR_OK);

    char createTbl[cmdLen] = "create table timedb(start_time integer, end_time integer)"
                             " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createTbl, strlen(createTbl));
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb/* ./data/export");  // 有 testdb 和 timedb 两张表
    EXPECT_EQ(ret, GMERR_OK);
    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl1.txt");

    const char *newCfgPersist = "ts/gmserver_priv_migrate.ini";
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)newCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    // 重启后表不存在，视图查不到
    PrintVertexLabelView((const char *)"testdb", (const char *)"view_testdb_vl2.txt");
    PrintVertexLabelView((const char *)"t_thrt_org", (const char *)"view_t_thrt_org_vl2.txt");
    PrintVertexLabelView((const char *)"timedb", (const char *)"view_timedb_vl.txt");
    const char *str = "fetched all records, finish!\n";
    CompareFileLastLineWithStr((const char *)"view_t_thrt_org_vl2.txt", str);
    CompareFileLastLineWithStr((const char *)"view_timedb_vl.txt", str);
    EXPECT_TRUE(AreTwoFileSame((const char *)"view_testdb_vl1.txt", (const char *)"view_testdb_vl2.txt"));

    ret = system("mkdir -p ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -c systbl_data -f ./data/export1");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand2[cmdLen] = "create table testdb2(start_time integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand2, strlen(createCommand2));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand1[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCommand1, strlen(dropCommand1));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand2[cmdLen] = "drop table t_thrt_org;";
    ret = GmcExecDirect(tmpStmt, dropCommand2, strlen(dropCommand2));
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    char dropCommand3[cmdLen] = "drop table testdb2;";
    ret = GmcExecDirect(tmpStmt, dropCommand3, strlen(dropCommand3));
    EXPECT_EQ(ret, GMERR_OK);
    char dropCommand4[cmdLen] = "drop table timedb;";
    ret = GmcExecDirect(tmpStmt, dropCommand4, strlen(dropCommand4));
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
}

// 4、建 1 张表，gmexport 工具导出系统表数据，删 1 张表，再建 1 张同名表，但是有字段名差异，DB重新启动失败
TEST_F(StTsExport, exportToolTest4)
{
    CleanFileBeforeTest();
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy_for_export.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, test_prop integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("mkdir -p ./data/export");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -c systbl_data -f ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    char dropCmd1[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCmd1, strlen(dropCmd1));
    EXPECT_EQ(ret, GMERR_OK);
    DbSleep(1500);
    char createCommand1[cmdLen] = "create table testdb(start_time integer, diff_test_prop integer, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand1, strlen(createCommand1));
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb/* ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    const char *newCfgPersist = "ts/gmserver_priv_migrate.ini";
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)newCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);
    MatchStringInLog((const char *)"Property name mismatch");
}

// 5、建 1 张表，gmexport 工具导出系统表数据，删 1 张表，再建 1 张同名表，但是字段类型差异，DB重新启动失败
TEST_F(StTsExport, exportToolTest5)
{
    CleanFileBeforeTest();
    Status ret = system("gmrule -c import_allowlist -f ./ts/priv_file/allowlist.gmuser");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f ./ts/priv_file/sys_priv_policy_for_export.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 建表
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &tmpConn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(tmpConn, &tmpStmt);
    EXPECT_EQ(GMERR_OK, ret);
    char createCommand[cmdLen] = "create table testdb(start_time integer, test_prop integer, end_time integer)"
                                 " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand, strlen(createCommand));
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("mkdir -p ./data/export");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmexport -c systbl_data -f ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    char dropCmd1[cmdLen] = "drop table testdb;";
    ret = GmcExecDirect(tmpStmt, dropCmd1, strlen(dropCmd1));
    EXPECT_EQ(ret, GMERR_OK);
    DbSleep(1500);
    char createCommand1[cmdLen] = "create table testdb(start_time integer, test_prop inet, end_time integer)"
                                  " with (time_col = 'end_time', interval = '1 hour', ttl = '3 hours');";
    ret = GmcExecDirect(tmpStmt, createCommand1, strlen(createCommand1));
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb/* ./data/export");
    EXPECT_EQ(ret, GMERR_OK);

    const char *newCfgPersist = "ts/gmserver_priv_migrate.ini";
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)newCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);
    MatchStringInLog((const char *)"Property type mismatch");
}

TEST_F(StTsExport, exportToolCleanTest)
{
    CleanFileBeforeTest();
}
