/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: st for ts dfx
 * Author: ts team
 * Create: 2024/09/03
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "adpt_printf.h"
#include "StartDbServer.h"
#include "st_common.h"
#include "InitClt.h"
#include "gmc_sql.h"
#include "gmc_sysview.h"

const static char *g_cfgPersist = "ts/gmserver_ts_dfx.ini";
const static char *g_tmpCfgPersist = "ts/gmserver_ts_dfx_tmp.ini";

class StTsDfxGmc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(100);
        StModifyConfig(g_cfgPersist, g_tmpCfgPersist, "\"tsLcmCheckPeriod=3\"");
        StartDbServer((char *)g_tmpCfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(100);
    }
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

// 超时时间1s
static void InitConnAndStmt(GmcConnT **conn, GmcStmtT **stmt, uint32_t timeoutMs)
{
    static GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, timeoutMs);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcAllocStmt(*conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static Status TsQuerySqlByView(char *querySql)
{
    char gmsysview[] = "gmsysview";
    char sql[] = "-sql";
    char *argv[3] = {gmsysview, sql, querySql};
    return GmcSysview(3, argv, DbPrintfDefault);
}

typedef struct {
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

typedef struct {
    char (*name)[256];
    int64_t *id;
    uint32_t rowNum;
} DataStr;

static Status BulkInsert(GmcStmtT *stmt, Data data, const char *tableName)
{
    assert(tableName != NULL);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    return GmcExecute(stmt);
}

void BulkInsertStr(GmcStmtT *stmt, DataStr data, const char *tableName)
{
    assert(tableName != NULL);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 256, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

void StreamBulkInsert(GmcStmtT *stmt, Data data, const char *tableName)
{
    assert(tableName != NULL);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static Status ExecQuerySql(GmcStmtT *stmt, const char *sql)
{
    assert(sql != NULL);
    return GmcExecDirect(stmt, sql, 150);
}

static void BulkInsert9w(GmcStmtT *stmt, char *tableName)
{
    uint32_t rowNum = 30000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];

    // 按3-6w, 6-9w, 0-3w顺序插入
    // 3-6w
    for (int i = 30000; i < 60000; i++) {
        ages[i - 30000] = i;
        ids[i - 30000] = i;
        worktimes[i - 30000] = i;
        salaries[i - 30000] = 70000;
    }
    Data data1 = {0};
    data1.age = ages;
    data1.id = ids;
    data1.worktime = worktimes;
    data1.salary = salaries;
    data1.rowNum = rowNum;
    (void)BulkInsert(stmt, data1, tableName);

    // 0-3w
    for (int i = 0; i < rowNum; i++) {
        ages[i] = i;
        ids[i] = i;
        worktimes[i] = i;
        salaries[i] = 30000 + i;
    }
    Data data2 = {0};
    data2.age = ages;
    data2.id = ids;
    data2.worktime = worktimes;
    data2.salary = salaries;
    data2.rowNum = rowNum;
    (void)BulkInsert(stmt, data2, tableName);

    // 6-9w
    for (int i = 60000; i < 90000; i++) {
        ages[i - 60000] = i;
        ids[i - 60000] = i;
        worktimes[i - 60000] = 0;
        salaries[i - 60000] = i;
    }
    Data data3 = {0};
    data3.age = ages;
    data3.id = ids;
    data3.worktime = worktimes;
    data3.salary = salaries;
    data3.rowNum = rowNum;
    (void)BulkInsert(stmt, data3, tableName);
}

static Status BulkInsertBigData(GmcStmtT *stmt, char *tableName, uint32_t totalNum)
{
    uint32_t rowNum = 50000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];
    Status ret = GMERR_OK;
    uint32_t batchCnt = totalNum / rowNum;
    for (int i = 0; i < batchCnt; i++) {
        for (int j = 0; j < rowNum; j++) {
            ages[j] = j + i * rowNum;
            ids[j] = j + i * rowNum;
            worktimes[j] = j + i * rowNum;
            salaries[j] = j + i * rowNum;
        }
        Data data1 = {0};
        data1.age = ages;
        data1.id = ids;
        data1.worktime = worktimes;
        data1.salary = salaries;
        data1.rowNum = rowNum;
        ret = BulkInsert(stmt, data1, tableName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static void BulkInsertStr10w(GmcStmtT *stmt, char *tableName)
{
    char names[][256] = {"david", "nut", "bob", "olivia", "tim"};
    int64_t ids[5] = {};

    for (int i = 0; i < 2000; i++) {
        for (int j = 0; j < 5; j++) {
            ids[j] = i * 5 + j;
        }
        DataStr data1 = {0};
        data1.name = names;
        data1.id = ids;
        data1.rowNum = 5;
        BulkInsertStr(stmt, data1, tableName);
    }
}

static void StreamBulkInsert10w(GmcStmtT *stmt, char *tableName)
{
    uint32_t rowNum = 30000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];

    for (int i = 30000; i < 60000; i++) {
        ages[i - 30000] = i;
        ids[i - 30000] = i;
        worktimes[i - 30000] = i;
        salaries[i - 30000] = 70000;
    }
    Data data1 = {0};
    data1.age = ages;
    data1.id = ids;
    data1.worktime = worktimes;
    data1.salary = salaries;
    data1.rowNum = rowNum;
    StreamBulkInsert(stmt, data1, tableName);
}

static Status DropTable(GmcStmtT *stmt, char *tableName)
{
    char ddlCommand[256] = {0};
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "drop table %s;", tableName);
    return GmcExecDirect(stmt, ddlCommand, 60);
}

TEST_F(StTsDfxGmc, DISABLED_SlowQueryDfx_Scan)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 500);

    const char *createTableSql =
        "create table test(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
        "hours');";

    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);

    BulkInsert9w(stmt, "test");

    // 长查询，返回16004
    char *query1 = "select count(id), sum(age), age, 1, salary from test group by id;";
    ret = ExecQuerySql(stmt, query1);
    EXPECT_EQ(ret, GMERR_REQUEST_TIME_OUT);

    // copy to
    system("touch a.csv");
    char *query2 = "copy (select count(id), sum(age), age, 1, salary from test group by id) to './a.csv';";
    ret = ExecQuerySql(stmt, query2);
    EXPECT_EQ(ret, GMERR_REQUEST_TIME_OUT);
    system("rm -rf a.csv");

    char *op3 = "select * from 'v$TS_TBL_OPER_STATIS';";
    EXPECT_EQ(TsQuerySqlByView(op3), GMERR_OK);

    printf("---------------------------insert into-------------------------------\n");
    // insert into
    const char *createTableSql2 =
        "create table test1(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
        "hours');";
    ret = GmcExecDirect(stmt, createTableSql2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char *query3 = "insert into test1 select count(id), sum(age), length(age), age from test group by id, age;";
    ret = ExecQuerySql(stmt, query3);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DropTable(stmt, "test");
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDfxGmc, DISABLED_SlowQueryDfx_Sort)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 400);

    const char *createTableSql =
        "create table testSort(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";

    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);

    BulkInsert9w(stmt, "testSort");

    // 长查询，返回16004
    char *query1 = "select age, length(age), sum(id+id),id from testSort group by age, id order by age;";
    ret = ExecQuerySql(stmt, query1);
    EXPECT_EQ(ret, GMERR_REQUEST_TIME_OUT);

    ret = DropTable(stmt, "testSort");
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StTsDfxGmc, DISABLED_SlowQueryDfx_Like)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100);

    const char *createTableSql =
        "create table testLike(name char(100), id integer) with (time_col = 'id', interval = '1 "
        "hour', compression = 'fast(rapidlz)', ttl = '3 hours');";

    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);

    BulkInsertStr10w(stmt, "testLike");

    printf("-----------------like------------------\n");
    // 长查询，返回16004
    char *query1 =
        "select name, id from testLike where name like '%d%' or name like '%a%' or name like '%n%' group by name, id;";
    ret = ExecQuerySql(stmt, query1);
    EXPECT_EQ(ret, GMERR_REQUEST_TIME_OUT);

    ret = DropTable(stmt, "testLike");
    EXPECT_EQ(GMERR_REQUEST_TIME_OUT, ret);
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StTsDfxGmc, SlowQueryDfx_BgWorker)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 1000);

    const char *createTableSql =
        "create table testbg(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 hours');";

    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);

    BulkInsert9w(stmt, "testbg");

    ret = DropTable(stmt, "testbg");

    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StTsDfxGmc, SlowQueryDfx_AgingFunc)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 1000);

    const char *createTableSql =
        "create table testAge(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 hours');";

    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);

    BulkInsert9w(stmt, "testAge");

    char *query1 = "select tsdb_aging('testAge');";
    ret = ExecQuerySql(stmt, query1);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DropTable(stmt, "testAge");
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

static void *DfxLongQueryFunc(void *args)
{
    printf("----------long query start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    char *query1 = "select sum(id), sum(age), id, age from testLock group by id, age order by id, age;";
    Status ret = ExecQuerySql(stmt, query1);
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------long query end------------\n");
    return NULL;
}

static void *DfxInsertIntoFunc(void *args)
{
    printf("----------insert into start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);

    const char *createTableSql = "create table testInsert(age integer, id integer, worktime integer, salary integer) "
                                 "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 "
                                 "hours', disk_limit = '1 MB');";
    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char *query1 =
        "insert into testInsert select sum(id), sum(age), id, age from testLock group by id, age order by id;";
    ret = ExecQuerySql(stmt, query1);
    int retry = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retry < 5) {
        ret = ExecQuerySql(stmt, query1);
        retry++;
    }
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------insert into end------------\n");
    return NULL;
}

static void *DfxCopyToFunc(void *args)
{
    printf("----------insert into start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    system("touch a.csv");
    char *query1 = "copy (select count(id), sum(age), age, 1, salary from testLock group by id) to './a.csv';";
    Status ret = ExecQuerySql(stmt, query1);
    int retry = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retry < 5) {
        ret = ExecQuerySql(stmt, query1);
        retry++;
    }
    EXPECT_EQ(ret, GMERR_OK);
    system("rm -rf a.csv");
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------insert into end------------\n");
    return NULL;
}

static void *DfxDropFunc(void *args)
{
    printf("----------drop start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    Status ret = DropTable(stmt, "testLock");
    int retry = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retry < 5) {
        ret = DropTable(stmt, "testLock");
        retry++;
    }
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------drop end------------\n");
    return NULL;
}

static void *DfxAlterFunc(void *args)
{
    printf("----------alter start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    char *sql = "alter table testLock set (disk_limit = '40 MB');";
    Status ret = GmcExecDirect(stmt, sql, 200);
    int retry = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retry < 5) {
        ret = GmcExecDirect(stmt, sql, 200);
        retry++;
    }
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------alter end------------\n");
    return NULL;
}

static void *DfxAgingFuncFunc(void *args)
{
    printf("----------aging func start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    char *query1 = "select tsdb_aging('testLock');";
    Status ret = ExecQuerySql(stmt, query1);
    int retry = 0;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retry < 5) {
        ret = ExecQuerySql(stmt, query1);
        retry++;
    }
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------aging func end------------\n");
    return NULL;
}

static void *DfxInsertFunc(void *args)
{
    printf("----------insert start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);

    Status ret = GMERR_OK;
    int totalNum = 5000000;

    uint32_t rowNum = 50000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];
    uint32_t batchCnt = totalNum / rowNum;
    for (int i = 0; i < batchCnt; i++) {
        for (int j = 0; j < rowNum; j++) {
            ages[j] = j + i * rowNum;
            ids[j] = j + i * rowNum;
            worktimes[j] = j + i * rowNum;
            salaries[j] = j + i * rowNum;
        }
        Data data1 = {0};
        data1.age = ages;
        data1.id = ids;
        data1.worktime = worktimes;
        data1.salary = salaries;
        data1.rowNum = rowNum;
        ret = BulkInsert(stmt, data1, "testLock");
        EXPECT_EQ(ret, GMERR_OK);
    }
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------insert end------------\n");
    return NULL;
}

static void *DfxMultiInsertFunc(void *args)
{
    printf("----------insert start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);

    uint32_t rowNum = 100;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];

    for (int i = 0; i < rowNum; i++) {
        ages[i] = 100000000 + i;
        ids[i] = 100000000 + i;
        worktimes[i] = 100000000 + i;
        salaries[i] = 100000000 + i;
    }
    Data data1 = {0};
    data1.age = ages;
    data1.id = ids;
    data1.worktime = worktimes;
    data1.salary = salaries;
    data1.rowNum = rowNum;
    Status ret = BulkInsert(stmt, data1, "testLock");
    int retryCnt;
    while (ret == GMERR_REQUEST_TIME_OUT && retryCnt < 5) {
        ret = BulkInsert(stmt, data1, "testLock");
        retryCnt++;
        printf("----------insert retry:%d------------\n", retryCnt);
    }
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------insert end------------\n");
    return NULL;
}

static void *DfxMultiLongQueryFunc(void *args)
{
    printf("----------long query start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    char *query1 = "select sum(id), id from testLock group by id;";
    Status ret = ExecQuerySql(stmt, query1);
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------long query end------------\n");
    return NULL;
}

static void *DfxViewFunc(void *args)
{
    printf("----------view query start------------\n");
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 100000);
    char gmsysview[] = "gmsysview";
    char sql[] = "-sql";
    char viewName[] = "select * from testLock";
    char *argv1[3] = {gmsysview, sql, viewName};
    Status ret = GmcSysview(3, argv1, DbPrintfDefault);
    int retryCnt;
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 5) {
        ret = GmcSysview(3, argv1, DbPrintfDefault);
        retryCnt++;
        printf("----------view query retry:%d------------\n", retryCnt);
    }
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    printf("----------view query done------------\n");
    return NULL;
}

// 多个读和写线程并发
TEST_F(StTsDfxGmc, SlowQueryDfx_MultiRWThread)
{
    // 预置数据
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 1000000);
    const char *createTableSql =
        "create table testLock(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 hours');";
    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(GMERR_OK, ret);

    int threadNum = 5;
    pthread_t readThreads[threadNum] = {0};
    pthread_t writeThreads[threadNum] = {0};

    for (int i = 0; i < threadNum; i++) {
        ASSERT_EQ(0, pthread_create(&readThreads[i], NULL, DfxMultiLongQueryFunc, NULL));
        DbUsleep(100000);
        ASSERT_EQ(0, pthread_create(&writeThreads[i], NULL, DfxMultiInsertFunc, NULL));
        DbUsleep(100000);
    }

    for (int i = 0; i < threadNum; i++) {
        ASSERT_EQ(0, pthread_join(readThreads[i], NULL));
        ASSERT_EQ(0, pthread_join(writeThreads[i], NULL));
    }

    sleep(5);

    ret = DropTable(stmt, "testLock");
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 锁超时用例，时序dfx特性调试使用，不用上门禁
TEST_F(StTsDfxGmc, DISABLED_SlowQueryDfx_LockTimeout)
{
    // 预置数据
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt, 1000000);
    const char *createTableSql = "create table testLock(age integer, id integer, worktime integer, salary integer) "
                                 "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 "
                                 "hours', disk_limit = '1 MB');";
    Status ret = GmcExecDirect(stmt, createTableSql, 200);
    EXPECT_EQ(ret, GMERR_OK);
    BulkInsertBigData(stmt, "testLock", 50000000);

    pthread_t longQueryThread;
    pthread_t dropThread;
    pthread_t alterThread;
    pthread_t agingFuncThread;
    pthread_t insertThread;
    pthread_t viewThread;
    pthread_t insertIntoThread;
    pthread_t copyToThread;

    // 长查询线程
    ASSERT_EQ(0, pthread_create(&longQueryThread, NULL, DfxLongQueryFunc, NULL));
    DbUsleep(100000);
    // 视图查询线程
    ASSERT_EQ(0, pthread_create(&viewThread, NULL, DfxViewFunc, NULL));
    // 批写线程
    ASSERT_EQ(0, pthread_create(&insertThread, NULL, DfxInsertFunc, NULL));
    DbUsleep(100000);
    // copy to线程
    ASSERT_EQ(0, pthread_create(&copyToThread, NULL, DfxCopyToFunc, NULL));
    DbUsleep(100000);
    // insert into线程
    ASSERT_EQ(0, pthread_create(&insertIntoThread, NULL, DfxInsertIntoFunc, NULL));
    DbUsleep(100000);
    // alter表线程
    ASSERT_EQ(0, pthread_create(&alterThread, NULL, DfxAlterFunc, NULL));
    DbUsleep(100000);
    // agingFunc线程
    ASSERT_EQ(0, pthread_create(&agingFuncThread, NULL, DfxAgingFuncFunc, NULL));
    DbUsleep(1000000);
    // drop表线程
    ASSERT_EQ(0, pthread_create(&dropThread, NULL, DfxDropFunc, NULL));

    ASSERT_EQ(0, pthread_join(longQueryThread, NULL));
    ASSERT_EQ(0, pthread_join(viewThread, NULL));
    ASSERT_EQ(0, pthread_join(insertThread, NULL));
    ASSERT_EQ(0, pthread_join(insertIntoThread, NULL));
    ASSERT_EQ(0, pthread_join(copyToThread, NULL));
    ASSERT_EQ(0, pthread_join(agingFuncThread, NULL));
    ASSERT_EQ(0, pthread_join(alterThread, NULL));
    ASSERT_EQ(0, pthread_join(dropThread, NULL));

    sleep(5);

    ret = DropTable(stmt, "testLock");
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
