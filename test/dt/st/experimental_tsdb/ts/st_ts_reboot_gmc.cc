/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: st for reboot with ts feature
 * Author: tangjunnong
 * Create: 2024/1/23
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "st_common.h"
#include "InitClt.h"
#include "clt_ts.h"
#include "gmc_sql.h"
#include "gmc_test.h"

const static uint32_t cmdLen = 200;
const uint32_t waitTime = 2000;  // ms

const static char *g_cfgPersist = "ts/gmserver_ts.ini";
const static char *g_tmpCfgPersist = "ts/gmserver_stream_tmp.ini";

class StTsRebootGmc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void InitCommon(void)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(waitTime);
    StModifyConfig(g_cfgPersist, g_tmpCfgPersist, "\"instanceType=1\"");
    StartDbServer((char *)g_tmpCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);
}

void InitLargeResult(void)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(waitTime);
    StModifyConfig(g_cfgPersist, g_tmpCfgPersist, "\"forceUseTempFileForQueryResult=1\"");
    StartDbServer((char *)g_tmpCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);
}

static char *GetCmdResult(const char *cmd)
{
    FILE *pipe = popen(cmd, "r");
    if (pipe == NULL) {
        return NULL;
    }
    int length = 200;
    char *output = (char *)malloc(sizeof(char) * length);
    if (fgets(output, length, pipe) == NULL) {
        free(output);
        return NULL;
    }
    pclose(pipe);
    return output;
}

TEST_F(StTsRebootGmc, RebootBulkOpt)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[200] = "create table tablebulk(age integer, id integer) with (time_col = 'id', "
                        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 5400, 1800};
    int64_t age[] = {29, 30, 19};
    char tableName[30] = "TABLEBULK";  // diff case check
    TsBulkInsertStmtT bulkOpInfo = {0};
    DmVertexLabel *logicTbl = NULL;
    uint32_t colCnt = 0;

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    colCnt = DbListGetItemCnt(&stmt->tsInfo->bindColStmts);
    EXPECT_EQ(colCnt, (uint32_t)2);
    for (uint32_t i = 0; i < colCnt; i++) {
        TsBindColStmtT *item = (TsBindColStmtT *)DbListItem(&stmt->tsInfo->bindColStmts, i);
        EXPECT_EQ(item->origVal.dataSize, sizeof(uint64_t));
        EXPECT_EQ(item->origVal.dataType, DB_DATATYPE_INT64);
        EXPECT_EQ(item->schemaDataType, DB_DATATYPE_INT64);
        if (i == 0) {
            EXPECT_EQ(item->colWiseVal.data, &id[0]);
            EXPECT_EQ(item->colId, (uint32_t)1);
        } else {
            EXPECT_EQ(item->colWiseVal.data, &age[0]);
            EXPECT_EQ(item->colId, (uint32_t)0);
        }
    }
    bulkOpInfo = stmt->tsInfo->bulkInsertInfo;
    logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    EXPECT_EQ(bulkOpInfo.rowArraySize, rowNum);
    EXPECT_EQ(bulkOpInfo.logicTblId, logicTbl->metaCommon.metaId);
    DmTsInfoT *tsInfo = (DmTsInfoT *)MEMBER_PTR(&logicTbl->metaVertexLabel->extraInfo, data);
    EXPECT_EQ(bulkOpInfo.interval.microsecond, tsInfo->interval.microsecond);
    EXPECT_EQ(bulkOpInfo.tolerance.microsecond, tsInfo->tolerance.microsecond);
    EXPECT_EQ(bulkOpInfo.compressionMode, TS_COMP_FAST_RAPIDLZ);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
}

static void GmcClientInit(GmcConnT **conn, GmcStmtT **stmt)
{
    static GmcConnOptionsT *connOptions = NULL;
    GmcInit();
    GmcConnOptionsCreate(&connOptions);
    GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    GmcConnOptionsSetCSRead(connOptions);
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    GmcConnOptionsDestroy(connOptions);
    GmcAllocStmt(*conn, stmt);
}

static void GmcClientUnInit(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
}

TEST_F(StTsRebootGmc, RebootAlterTable)
{
    InitCommon();
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char createCommand[cmdLen] = "create table altertablereboot(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    char alterCommand1[cmdLen] = "ALTER TABLE altertablereboot ADD COLUMN new_col char(10);";
    GmcClientInit(&conn, &stmt);
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcClientUnInit(conn, stmt);

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);
    conn = NULL;
    stmt = NULL;
    GmcClientInit(&conn, &stmt);
    // reboot finish

    ret = GmcPrepareStmtByLabelName(stmt, "altertablereboot", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)4);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[0], name);
    EXPECT_STREQ(name, "start_time");
    name = MEMBER_PTR(&properties[1], name);
    EXPECT_STREQ(name, "end_time");
    name = MEMBER_PTR(&properties[2], name);
    EXPECT_STREQ(name, "name");
    name = MEMBER_PTR(&properties[3], name);  // newly added col
    EXPECT_STREQ(name, "new_col");
    EXPECT_EQ(properties[3].dataType, DB_DATATYPE_FIXED);
    EXPECT_EQ(properties[3].size, (uint32_t)10);

    GmcClientUnInit(conn, stmt);
}

TEST_F(StTsRebootGmc, RebootAlterTableBlob)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char createCommand[cmdLen] = "create table altertablerebootblob(start_time integer, end_time integer, name char(9))"
                                 " with (time_col = 'end_time', interval = '1 day', ttl = '3 days');";
    char alterCommand1[cmdLen] = "ALTER TABLE altertablerebootblob ADD COLUMN new_col blob;";
    GmcInit();
    GmcConnOptionsCreate(&connOptions);
    GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    GmcConnOptionsSetCSRead(connOptions);
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcConnOptionsDestroy(connOptions);
    GmcAllocStmt(conn, &stmt);
    Status ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, alterCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);
    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    GmcInit();
    GmcConnOptionsCreate(&connOptions);
    GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    GmcConnOptionsSetCSRead(connOptions);
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcConnOptionsDestroy(connOptions);
    GmcAllocStmt(conn, &stmt);
    // reboot finish

    ret = GmcPrepareStmtByLabelName(stmt, "altertablerebootblob", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexLabelT *logicTbl = (DmVertexLabelT *)CltGetVertexLabel(stmt);
    DmSchemaT *schema = MEMBER_PTR(logicTbl->metaVertexLabel, schema);
    EXPECT_EQ(schema->propeNum, (uint32_t)4);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    const char *name = MEMBER_PTR(&properties[0], name);
    EXPECT_STREQ(name, "start_time");
    name = MEMBER_PTR(&properties[1], name);
    EXPECT_STREQ(name, "end_time");
    name = MEMBER_PTR(&properties[2], name);
    EXPECT_STREQ(name, "name");
    name = MEMBER_PTR(&properties[3], name);  // newly added col
    EXPECT_STREQ(name, "new_col");
    EXPECT_EQ(properties[3].dataType, DB_DATATYPE_BYTES);
    EXPECT_EQ(properties[3].size, 65535);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
}

TEST_F(StTsRebootGmc, RebootCreateTable)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[200] =
        "create table tableCustomCreate(age integer, id integer) with (time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 5400, 1800};
    int64_t age[] = {29, 30, 19};
    char tableName[30] = "tableCustomCreate";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    system("rm -rf /home/<USER>/");
}

TEST_F(StTsRebootGmc, RebootCreateTable2)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[200] =
        "create table tableCustomCreate2(age integer, id integer) with (time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";
    char command2[100] = "select age, id from tableCustomCreate2 order by age";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 5400, 1800};
    int64_t idResult[] = {1800, 1, 5400};
    int64_t age[] = {29, 30, 19};
    int64_t ageResult[] = {19, 29, 30};
    char tableName[30] = "tableCustomCreate2";
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t idRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, command2, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    bool eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    char dropCmd[100] = "drop table tableCustomCreate2;";
    ret = GmcExecDirect(stmt, dropCmd, 100);
    EXPECT_EQ(ret, GMERR_OK);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    system("rm -rf /home/<USER>/");
}

TEST_F(StTsRebootGmc, RebootCreateTable3)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[200] =
        "create table tableCustomCreate3(age integer, id integer) with (time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";
    char command2[100] = "select age, id from tableCustomCreate3 order by age";
    char command3[200] = "create table tableCustomCreate4(age integer, id integer) with (time_col = 'id', "
                         "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 5400, 1800};
    int64_t idResult[] = {1800, 1, 5400};
    int64_t age[] = {29, 30, 19};
    int64_t ageResult[] = {19, 29, 30};
    char tableName[30] = "tableCustomCreate3";
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t idRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, command2, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    bool eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    system("rm -rf /home/<USER>/");
}

void *ShutDownDb(void *args)
{
    DbSleep(500);
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    return NULL;
}

TEST_F(StTsRebootGmc, DISABLED_RebootDuringLargeResultQuery)
{
    InitLargeResult();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[300] =
        "create table tableLargeLimit(age integer, id integer, worktime integer, a integer, b integer, c integer, d "
        "integer, e integer, f integer, g integer, h integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select * from tableLargeLimit;";

    uint32_t rowNum = 30000;
    uint32_t batchNum = 10;
    uint32_t value = 0;
    bool eof = false;
    int64_t id[30000] = {0};
    int64_t age[30000] = {0};
    int64_t worktime[30000] = {0};
    char tableName[30] = "tableLargeLimit";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)11);
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    pthread_t thread;

    uint32_t arg = 0;

    Status res = pthread_create(&thread, nullptr, ShutDownDb, (void *)&arg);
    EXPECT_EQ(res, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand1, 40);
    EXPECT_EQ(ret, GMERR_CONNECTION_RESET_BY_PEER);

    pthread_join(thread, NULL);

    StModifyConfig(g_cfgPersist, g_tmpCfgPersist, "\"forceUseTempFileForQueryResult=1\"");
    StartDbServer((char *)g_tmpCfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    EXPECT_FALSE(DbDirExist("/data/gmdb/temp/large_result"));
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcFetch(stmt, &eof);
    EXPECT_NE(ret, GMERR_OK);

    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

static int32_t GetLargeResultFileNum()
{
    char buf[1024] = {0};
    FILE *pf = popen("ls -A /data/gmdb/temp/large_result 2>/dev/null | wc -l", "r");
    if (pf == NULL) {
        printf("popen error./n");
        return -1;
    }
    fgets(buf, 1024, pf);
    if (strlen(buf) == 0) {
        (void)pclose(pf);
        return -1;
    }

    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    return atoi(buf);
}

TEST_F(StTsRebootGmc, ClientAbnormalExitDuringLargeResultQuery)
{
    InitLargeResult();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[300] =
        "create table tableAbn(age integer, id integer, worktime integer, a integer, b integer, c integer, d "
        "integer, e integer, f integer, g integer, h integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select * from tableAbn;";
    uint32_t rowNum = 300;
    uint32_t batchNum = 10;
    uint32_t value = 0;
    int64_t id[30000] = {0};
    int64_t age[30000] = {0};
    int64_t worktime[30000] = {0};
    char tableName[30] = "tableAbn";

    pid_t p1 = fork();
    if (p1 < 0) {
        printf("unable to fork a new process.");
        ASSERT_TRUE(false);
    } else if (p1 == 0) {
        Status ret = GmcInit();
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsCreate(&connOptions);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetCSRead(connOptions);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
        EXPECT_EQ(ret, GMERR_OK);
        GmcConnOptionsDestroy(connOptions);
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecDirect(stmt, ddlCommand, 300);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, (uint32_t)11);
        for (uint32_t i = 0; i < batchNum; i++) {
            ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
            EXPECT_EQ(ret, GMERR_OK);

            ret = GmcExecute(stmt);
            EXPECT_EQ(ret, GMERR_OK);
        }
        ret = GmcExecDirect(stmt, qryCommand1, 40);
        EXPECT_EQ(ret, GMERR_OK);
        exit(0);
    } else if (p1 > 0) {
        (void)waitpid(p1, NULL, 0);
    }

    EXPECT_EQ(GetLargeResultFileNum(), 0);

    Status ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

// 重启叠加大结果集并发
TEST_F(StTsRebootGmc, DISABLED_RebootDuringLargeResultQueryCC1)
{
    InitLargeResult();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[300] =
        "create table tableRebootCC1(age integer, id integer, worktime integer, a integer, b integer, c integer, d "
        "integer, e integer, f integer, g integer, h integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select * from tableRebootCC1;";
    uint32_t rowNum = 300;
    uint32_t batchNum = 10;
    uint32_t value = 0;
    int64_t id[30000] = {0};
    int64_t age[30000] = {0};
    int64_t worktime[30000] = {0};
    char tableName[30] = "tableRebootCC1";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)11);
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = GmcExecDirect(stmt, qryCommand1, 40);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GetLargeResultFileNum(), 1);

    pid_t p1 = fork();
    if (p1 < 0) {
        printf("unable to fork a new process.");
        ASSERT_TRUE(false);
    } else if (p1 == 0) {
        ret = GmcInit();
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsCreate(&connOptions);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnOptionsSetCSRead(connOptions);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
        EXPECT_EQ(ret, GMERR_OK);
        GmcConnOptionsDestroy(connOptions);
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecDirect(stmt,
            "create table tableTemp(worktime integer) with (time_col = 'worktime', interval = '1 hour', compression = "
            "'fast(rapidlz)', ttl = '3 hours');",
            300);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(GetLargeResultFileNum(), 1);  // 测试新请求将上次的临时文件删除的逻辑,不影响其他查询的结果集存留

        ret = GmcExecDirect(stmt, qryCommand1, 40);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(GetLargeResultFileNum(), 2);
        exit(0);
    }

    wait(NULL);

    EXPECT_EQ(GetLargeResultFileNum(), 1);

    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsRebootGmc, CreateVolatileTableDelReboot)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[300] =
        "create table tableVolatileCreate(age integer, id integer) with (is_volatile_label = 'true', time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_OK);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);

    system("rm -rf /home/<USER>/");

    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    GmcDisconnect(conn);
    GmcUnInit();
    system("rm -rf /home/<USER>/");
}

TEST_F(StTsRebootGmc, EmergencyReboot)
{
    InitCommon();
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[300] =
        "create table tableVolatileCreate(age integer, id integer) with (is_volatile_label = 'true', time_col = 'id', "
        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours', table_path = '/home/<USER>/');";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 触发锁库
    ret = GmcSetDbEmergency(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    DbSleep(waitTime);
    // 锁库不可用，建表失败
    ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_DATABASE_NOT_AVAILABLE);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    DbSleep(waitTime);

    // 重启恢复
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(waitTime);

    connOptions = NULL;
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    GmcDisconnect(conn);
    GmcUnInit();
    system("rm -rf /home/<USER>/");
}
