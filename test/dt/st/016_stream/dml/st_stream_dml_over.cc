/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_stream_ddl.cc
 * Description:
 * Create: 2024-08-16
 */

#include "st_stream_common.h"

#pragma pack(1)
typedef struct StreamStructWriteData {
    int64_t age;
    int64_t water_mark;
    int64_t event_time;
    char name[256];
} StreamStructWriteDataT;
#pragma pack()

#pragma pack(1)
typedef struct StreamStructWriteTextData {
    int64_t age;
    int64_t water_mark;
    int64_t event_time;
    uint16_t nameLen;
    char *name;
} StreamStructWriteTextDataT;
#pragma pack()

/**
 * name 写死GMDB
 */
static uint32_t CheckSimpleTupleValue(GmcStmtT *stmt, int64_t value, bool isIncreased = false)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 0;
    char name[256] = {0};
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(value + 200, val);
        size = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, strcmp(name, "GMDB"));
        i++;
        if (isIncreased) {
            value++;
        }
    }
    return i;
}
/**
 * 校验结果
 * @param stmt
 * @param tupleNum 总条数
 * @param checkProp1Index 不校验就MAX
 * @param checkProp2Index 不校验就MAX
 * @param prop1Value
 * @param prop2Value
 * @return
 */
static uint32_t CheckSimpleTupleValueWithWindow(GmcStmtT *stmt, uint32_t tupleNum, uint32_t checkProp1Index,
    uint32_t checkProp2Index, uint32_t *prop1Value, uint32_t *prop2Value)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 8;
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        if (checkProp1Index != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, checkProp1Index, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)prop1Value[i], val);
        }
        if (checkProp2Index != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, checkProp2Index, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)prop2Value[i], val);
        }
        i++;
    }
    return i;
}

static uint32_t CheckTextTupleValueWithWindow(GmcStmtT *stmt, uint32_t tupleNum, uint32_t colNum)
{
    uint32_t i = 0;
    uint32_t size = 0;
    char stringFixed[20] = {0};
    char *stringGet = stringFixed;
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        if (colNum != UINT32_MAX && i < tupleNum) {
            ret = GmcGetVertexPropertySizeById(stmt, colNum, &size);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(size, DM_STR_LEN("GMDB"));

            ret = GmcGetPropertyById((const GmcStmtT *)stmt, colNum, stringGet, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strcmp("GMDB", stringGet), 0);
        }
        i++;
    }
    return i;
}

static uint32_t CheckSimpleTupleValueWithWindow3(GmcStmtT *stmt, uint32_t tupleNum, uint32_t id1, uint32_t id2,
    uint32_t id3, uint32_t *value1, uint32_t *value2, uint32_t *value3)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 8;
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        if (id1 != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, id1, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)value1[i], val);
        }
        if (id2 != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, id2, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)value2[i], val);
        }
        if (id3 != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, id3, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)value3[i], val);
        }
        i++;
    }
    return i;
}

/**
 *
 * @param stmt
 * @param index
 * @param number 写入条数
 * @param tableName
 * @param operationType
 */
static void StreamStructWriteSimpleTuple(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = 1;
        v1.event_time = value + 200;
        (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/**
 * name 写死GMDB
 */
static void StreamStructWriteSimpleTupleSingle(
    GmcStmtT *stmt, int64_t value, const char *tableName, GmcOperationTypeE operationType)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    v1.age = value;
    v1.water_mark = value;
    v1.event_time = value;
    (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
    EXPECT_EQ(GMERR_OK, ret);
}
/**
 * name写入GMDB+value
 */
static void StreamStructWriteSimpleTupleSingle2(
    GmcStmtT *stmt, int64_t value, const char *tableName, GmcOperationTypeE operationType)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    v1.age = value;
    v1.water_mark = value;
    v1.event_time = value;
    (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s%ld%s", "GMDB", value, "\0");
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
    EXPECT_EQ(GMERR_OK, ret);
}

static void StreamStructWriteTextTupleSingle(
    GmcStmtT *stmt, int64_t value, const char *tableName, GmcOperationTypeE operationType)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteTextDataT v1 = {};
    v1.age = value;
    v1.water_mark = value;
    v1.event_time = value;
    v1.nameLen = value;
    char name[value] = "GMDB";
    v1.name = name;
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
    EXPECT_EQ(GMERR_OK, ret);
}

class StStreamDmlOver : public StStreamCommon {};

TEST_F(StStreamDmlOver, TestInsertStreamTableWindow09)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 500;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), sum_age integer"
        ", min_age integer, max_age integer, count_age integer, avg_age integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[600] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "sum(age) over (partition by window_start,window_end,water_mark), "
                         "min(age) over (partition by window_start,window_end,water_mark), "
                         "max(age) over (partition by window_start,window_end,water_mark), "
                         "count(age) over (partition by window_start,window_end,water_mark), "
                         "avg(age) over (partition by window_start,window_end,water_mark)"
                         "FROM TABLE(HOP(TABLE a, event_time, INTERVAL '1' SECONDS, INTERVAL '30' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, sum_age, min_age, "
                         "max_age,count_age,avg_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 600);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 30, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(9u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(406u, value);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}
TEST_F(StStreamDmlOver, TestInsertStreamTableWindow12)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[600] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name,"
                         "sum(age) over (partition by window_start,window_end,water_mark) "
                         "FROM TABLE(HOP(TABLE a, event_time, INTERVAL '10' SECONDS, INTERVAL '20' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 600);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, -205, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 100, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindow13)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[600] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "sum(age) over (partition by window_start,window_end,water_mark), "
                         "min(age) over (partition by window_start,window_end,water_mark), "
                         "max(age) over (partition by window_start,window_end,water_mark), "
                         "count(age) over (partition by window_start,window_end,water_mark) "
                         "FROM TABLE(HOP(TABLE a, event_time, INTERVAL '3600' SECONDS, INTERVAL '1' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 600);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, -200, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, -199, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 3402, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 3400, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 7000, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 7001, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 100000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg01)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMin integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "min(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, min_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {
        505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 510, 510, 510, 510, 510};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 20, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg02)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "max(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, max_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {
        509, 509, 509, 509, 509, 514, 514, 514, 514, 514, 514, 514, 514, 514, 514, 514, 514, 514, 514, 514};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 20, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg03)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "count(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, count_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 20, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg04)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "sum(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, sum_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {2535, 2535, 2535, 2535, 2535, 5095, 5095, 5095, 5095, 5095, 5095, 5095, 5095, 5095, 5095, 2560,
        2560, 2560, 2560, 2560};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 20, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg05)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time TOLERANT"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "sum(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, sum_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 6, tableName, GMC_OPERATION_SQL_INSERT);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    StreamStructWriteSimpleTuple(stmt, 511, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    uint32_t windowEndValue[] = {710, 710, 710, 710, 710};
    uint32_t ageMin[] = {2535, 2535, 2535, 2535, 2535};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 5, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(5, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg06)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time STRICT"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "sum(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, sum_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 6, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    uint32_t windowEndValue[] = {710, 710, 710, 710, 710};
    uint32_t ageMin[] = {2535, 2535, 2535, 2535, 2535};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 5, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(5, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg07)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS STRICT"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "sum(age) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, sum_age FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 6, tableName, GMC_OPERATION_SQL_INSERT);

    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    StreamStructWriteSimpleTuple(stmt, 512, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    uint32_t windowEndValue[] = {710, 710, 710, 710, 710};
    uint32_t ageMin[] = {2535, 2535, 2535, 2535, 2535};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 5, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(5, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Only Row_Number() with tumple */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber01)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(TUMBLE(TABLE a, event_time, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11u, value);
    uint32_t windowEndValue[] = {710, 710, 710, 710, 710, 720, 720, 720, 720, 720, 1210};
    uint32_t ageMin[] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 11, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(11, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Only Row_Number() with hop */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber02)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(22u, value);
    uint32_t windowEndValue[] = {
        715, 710, 715, 710, 715, 710, 715, 710, 715, 710, 720, 715, 720, 715, 720, 715, 720, 715, 720, 715, 1210, 1205};
    uint32_t ageMin[] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 1, 6, 2, 7, 3, 8, 4, 9, 5, 10, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 22, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(22, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Only Row_Number() with hop and select * */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumberStar)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, name char(256), row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select *, "
                         "ROW_NUMBER() over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(22u, value);
    uint32_t age[] = {
        505, 505, 506, 506, 507, 507, 508, 508, 509, 509, 510, 510, 511, 511, 512, 512, 513, 513, 514, 514, 1000, 1000};
    uint32_t ageMin[] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 1, 6, 2, 7, 3, 8, 4, 9, 5, 10, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 22, 3, 0, ageMin, age);
    EXPECT_EQ(22, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Only Row_Number() with hop with not seq partitionby list */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber03)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY window_end,water_mark,window_start) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(22u, value);
    uint32_t windowEndValue[] = {
        715, 710, 715, 710, 715, 710, 715, 710, 715, 710, 720, 715, 720, 715, 720, 715, 720, 715, 720, 715, 1210, 1205};
    uint32_t ageMin[] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 1, 6, 2, 7, 3, 8, 4, 9, 5, 10, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 22, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(22, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Row_Number() and full agg with hop */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber04)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), min_age integer, "
        "row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "min(age) over (PARTITION BY window_start,window_end,water_mark), ROW_NUMBER() over "
                         "(PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] =
        "CREATE STREAM SINK ab AS select age, window_end, event_time, name, min_age, row_number FROM aview "
        "INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {
        505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 505, 510, 510, 510, 510, 510};
    uint32_t rowNum[] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4, 5};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow3(stmt, 20, 4, 5, 1, ageMin, rowNum, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Only Row_Number() with hop with only window partitionby list */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber05)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY window_end,window_start) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(22u, value);
    uint32_t windowEndValue[] = {
        715, 710, 715, 710, 715, 710, 715, 710, 715, 710, 720, 715, 720, 715, 720, 715, 720, 715, 720, 715, 1210, 1205};
    uint32_t ageMin[] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 1, 6, 2, 7, 3, 8, 4, 9, 5, 10, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 22, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(22, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* Only Row_Number() with hop with not seq partitionby list */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber06)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '2' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY window_end,event_time,window_start) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验行数列数
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(14u, value);

    // 校验列值
    uint32_t window_end_expect[] = {20, 15, 20, 15, 20, 15, 25, 20, 25, 20, 25, 20, 25, 20};
    uint32_t row_number_expect[] = {1, 1, 2, 2, 3, 3, 1, 1, 2, 2, 3, 3, 4, 4};
    int32_t rowNum = 14;
    int32_t propCloNum1 = 4;  // row_number列
    int32_t propCloNum2 = 1;  // window_end列
    uint32_t tupleNum =
        CheckSimpleTupleValueWithWindow(stmt, rowNum, propCloNum1, propCloNum2, row_number_expect, window_end_expect);
    EXPECT_EQ(14, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* over算子 + hop + (over partition使用变长字段) */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber07)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name text,"
                        " watermark for event_time as event_time - INTERVAL '2' SECONDS);";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;
    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name text, row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY window_end,name,window_start) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteTextTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验行数和列数
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(14u, value);

    // 校验text的大小和内容
    int32_t rowNum = 14;
    int32_t propCloNum3 = 3;  // name(text)列
    uint32_t tupleNum = CheckTextTupleValueWithWindow(stmt, rowNum, propCloNum3);
    EXPECT_EQ(14, tupleNum);

    // drop
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/* over算子 + hop + window字段大小写混用 */
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber08)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name text,"
                        " watermark for event_time as event_time - INTERVAL '2' SECONDS);";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;
    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name text, row_number integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, WinDOW_End, event_time, name, "
                         "ROW_NUMBER() over (PARTITION BY WINDOW_START,name,wInDOw_eND) "
                         " FROM TABLE(hop(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, row_number FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteTextTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteTextTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验行数和列数
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(14u, value);

    // 校验text的大小和内容
    int32_t rowNum = 14;
    int32_t propCloNum3 = 3;  // name(text)列
    uint32_t tupleNum = CheckTextTupleValueWithWindow(stmt, rowNum, propCloNum3);
    EXPECT_EQ(14, tupleNum);

    // drop
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}
TEST_F(StStreamDmlOver, TestInsertStreamTableRowNumber09)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '2' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 300;

    char createCommand[cmdLen] =
        "create table tssink(age integer, window_end integer, event_time integer, name char(256), dis_count integer, "
        "row_number integer)"
        " with (time_col = 'window_end', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, water_mark, event_time, name, "
                         "SEQ_DISTINCT_COUNT(age, water_mark, name) "
                         " FROM a;";
    char commandDropView[200] = "drop stream view aview;";
    char command31[300] =
        "CREATE STREAM VIEW bview AS select age, window_end, event_time, name, seq_distinct_count_age_water_mark_name, "
        "ROW_NUMBER() over (PARTITION BY window_end,event_time,window_start) "
        " FROM TABLE(tumble(TABLE aView, event_time, INTERVAL '10' SECONDS));";
    char commandDropView1[200] = "drop stream view bview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, "
                         "seq_distinct_count_age_water_mark_name, row_number FROM bview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command31, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 13, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 18, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 22, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 28, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 28, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 100, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle(stmt, 200, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView1, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    uint32_t disCount[] = {3, 1, 1, 2, 1};
    uint32_t rowNum[] = {1, 1, 1, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 5, 4, 5, disCount, rowNum);
    EXPECT_EQ(5, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAggConst)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ageMax integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[300] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                         "count(2) over (PARTITION BY window_start,window_end,water_mark) "
                         " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[200] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, count_2 FROM aview "
                         "INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 300);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 20, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAggCountWindowParam)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a("
                        "age integer, water_mark integer, event_time integer, name char(256) "
                        ", watermark for event_time as event_time - INTERVAL '1' SECONDS"
                        ");";
    char command1[200] = "drop stream table a;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(GMERR_OK, ret);

    const static uint32_t cmdLen = 400;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), countWs integer, "
        "minWs integer"
        ", maxWs integer, sumWs integer, countWe integer, minWe integer, maxWe integer, sumWe integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, value);

    char command3[1000] = "CREATE STREAM VIEW aview AS select age, window_end, event_time, name, "
                          "count(window_start) over (PARTITION BY window_start,window_end,water_mark), "
                          "max(window_start) over (PARTITION BY window_start,window_end,water_mark), "
                          "min(window_start) over (PARTITION BY window_start,window_end,water_mark), "
                          "sum(window_start) over (PARTITION BY window_start,window_end,water_mark), "
                          "count(window_end) over (PARTITION BY window_start,window_end,water_mark), "
                          "max(window_end) over (PARTITION BY window_start,window_end,water_mark), "
                          "min(window_end) over (PARTITION BY window_start,window_end,water_mark), "
                          "sum(window_end) over (PARTITION BY window_start,window_end,water_mark) "
                          " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    char commandDropView[200] = "drop stream view aview;";
    char command4[1000] = "CREATE STREAM SINK ab AS select age, window_end, event_time, name, count_window_start, "
                          "max_window_start, min_window_start, sum_window_start,count_window_end, max_window_end, "
                          "min_window_end, sum_window_end FROM aview "
                          "INTO tsdb(tssink) with "
                          "(batch_window_size = "
                          "'1');";
    char commandDropSink[200] = "drop stream sink ab;";

    ret = GmcExecDirect(stmt, command3, 1000);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, command4, 1000);
    EXPECT_EQ(GMERR_OK, ret);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 505, 10, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1000, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(12u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(20u, value);
    uint32_t windowEndValue[] = {
        710, 710, 710, 710, 710, 715, 715, 715, 715, 715, 715, 715, 715, 715, 715, 720, 720, 720, 720, 720};
    uint32_t ageMin[] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 20, 4, 1, ageMin, windowEndValue);
    EXPECT_EQ(20, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

// 滚动窗口，over，avg
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg08)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 8, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {4, 4, 4};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}

// 滑动窗口，over，avg
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg09)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(HOP(Table t1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 8, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    uint32_t avgAge[] = {2, 2, 4, 4, 4};
    CheckSimpleTupleValueWithWindow(stmt, 5, 0, UINT32_MAX, avgAge, NULL);
}

// 滚动窗口，over，avg，计算结果是浮点数，小数点后小于0.5
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg10)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 4, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {2, 2, 2};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}
// 滚动窗口，over，avg，计算结果是浮点数，小数点后大于0.5，预期不四舍五入取整
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg11)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {1, 1, 1};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}

// 滚动窗口，over，avg，和非聚合属性一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg12)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(age integer, avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select age, avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t age[] = {1, 2, 3};
    uint32_t avgAge[] = {2, 2, 2};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, 1, age, avgAge);
}
// 滚动窗口，over，avg，和其他聚合一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg13)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1"
                     "(age integer, sum_age integer, count_age integer, max_age integer, avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select "
                     "age, "
                     "sum(age) over (PARTITION BY window_start,window_end), "
                     "count(age) over (PARTITION BY window_start,window_end), "
                     "max(age) over (PARTITION BY window_start,window_end), "
                     "avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t count_age[] = {3, 3, 3};
    uint32_t avgAge[] = {2, 2, 2};
    CheckSimpleTupleValueWithWindow(stmt, 3, 2, 4, count_age, avgAge);
}

// 滚动窗口，over包含非窗口字段，avg，和其他聚合一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg14)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1"
                     "(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select "
                     "avg(age) over (PARTITION BY window_start,window_end,water_mark) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {1, 2, 3};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}

// 滚动窗口，over，avg
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg15)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select avg(age) over (PARTITION BY window_start,window_end)"
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 8, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {4, 4, 4};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}

// 滑动窗口，over，avg
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg16)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select avg(age) over (PARTITION BY window_start,window_end)"
                     "from TABLE(HOP(Table t1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 8, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    uint32_t avgAge[] = {2, 2, 4, 4, 4};
    CheckSimpleTupleValueWithWindow(stmt, 5, 0, UINT32_MAX, avgAge, NULL);
}

// 滚动窗口，over，avg，计算结果是浮点数，小数点后小于0.5
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg17)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select avg(age) over (PARTITION BY window_start,window_end)"
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 4, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {2, 2, 2};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}
// 滚动窗口，over，avg，计算结果是浮点数，小数点后大于0.5，预期不四舍五入取整
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg18)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select avg(age) over (PARTITION BY window_start,window_end)"
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {1, 1, 1};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}

// 滚动窗口，over，avg，和非聚合属性一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg19)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(age integer, avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select age, avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t age[] = {1, 2, 3};
    uint32_t avgAge[] = {2, 2, 2};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, 1, age, avgAge);
}
// 滚动窗口，over，avg，和其他聚合一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg20)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1"
                     "(age integer, sum_age integer, count_age integer, max_age integer, avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select "
                     "age, "
                     "sum(age) over (PARTITION BY window_start,window_end), "
                     "count(age) over (PARTITION BY window_start,window_end), "
                     "max(age) over (PARTITION BY window_start,window_end), "
                     "avg(age) over (PARTITION BY window_start,window_end) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t count_age[] = {3, 3, 3};
    uint32_t avgAge[] = {2, 2, 2};
    CheckSimpleTupleValueWithWindow(stmt, 3, 2, 4, count_age, avgAge);
}

// 滚动窗口，over包含非窗口字段，avg
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg21)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1"
                     "(avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select "
                     "avg(age) over (PARTITION BY window_start,window_end,water_mark) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");
    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");
    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {1, 2, 3};
    CheckSimpleTupleValueWithWindow(stmt, 3, 0, UINT32_MAX, avgAge, NULL);
}

// 滚动窗口，over包含非窗口字段，avg，和ref一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg22)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1"
                     "(ref_age integer, avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream reference aref(integer,integer) with (mini='true');");

    executor.ExecSQL("upsert into streamref aref values (1,100)");
    executor.ExecSQL("upsert into streamref aref values (2,200)");
    executor.ExecSQL("upsert into streamref aref values (3,300)");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select ref['aref'][water_mark], "
                     "avg(age) over (PARTITION BY window_start,window_end,water_mark) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");

    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");

    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {1, 2, 3};
    uint32_t refValue[] = {100, 200, 300};
    CheckSimpleTupleValueWithWindow(stmt, 3, 1, 0, avgAge, refValue);
}

// 滚动窗口，over包含非窗口字段，avg，和format一起
TEST_F(StStreamDmlOver, TestInsertStreamTableWindowOverAgg23)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1"
                     "(format_name char(256), avg_age integer) "
                     "with (time_col = 'avg_age', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream reference aref(integer,integer) with (mini='true');");

    executor.ExecSQL("upsert into streamref aref values (1,100)");
    executor.ExecSQL("upsert into streamref aref values (2,200)");
    executor.ExecSQL("upsert into streamref aref values (3,300)");

    executor.ExecSQL("create stream table t1(age integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream view v1 as select format('name:%s',name), "
                     "avg(age) over (PARTITION BY window_start,window_end,water_mark) "
                     "from TABLE(TUMBLE(Table t1, event_time, INTERVAL '10' SECONDS)) ");

    executor.ExecSQL("create stream sink s1 as select * from v1 "
                     "into TSDB(tstable1) with (batch_window_size = 1)");

    StreamStructWriteSimpleTupleSingle2(stmt, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 2, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTupleSingle2(stmt, 11, "t1", GMC_OPERATION_SQL_INSERT);

    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    uint32_t avgAge[] = {1, 2, 3};
    CheckSimpleTupleValueWithWindow(stmt, 3, 1, UINT32_MAX, avgAge, NULL);
}
