/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: import_export_table.cc
 * Description: testcase for import export bin file
 * Author: GMDBV5 team
 * Create: 2025-01-06
 */
#include "tools_st_common.h"
#include "storage_st_common.h"
#include "rsm_st_common.h"
#include "pthread.h"

class StImportExportTable : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        system("ipcrm -a");
    }
    void SetUp()
    {
        system("mkdir -p ${GMDB_HOME}/exportFileTestPath/");
        string cmd = string("echo -n ${GMDB_HOME}/exportFileTestPath/");
        FILE *pipe = popen(cmd.c_str(), "r");
        char buffer[256];
        if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            g_exportFilePath = string(buffer);
        } else {
            std::cout << "set g_exportFilePath err." << std::endl;
        }
        pclose(pipe);

        ShutDownDbServer();
    }
    void TearDown()
    {
        system("rm -rf ${GMDB_HOME}/exportFileTestPath/");
    }

    string g_exportFilePath;
};

static void CheckVertexData(GmcStmtT *stmt, uint32_t i)
{
    bool isNull;
    uint32_t f0Res;
    Status ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Res, sizeof(f0Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f0Res, i);

    uint32_t f1Res;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Res, sizeof(f1Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f1Res, i + 1);

    uint32_t f2Res;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Res, sizeof(f2Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f2Res, i + 2);

    uint32_t f3Res;
    ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Res, sizeof(f3Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f3Res, i + 3);

    uint32_t f4Res;
    ret = GmcGetVertexPropertyByName(stmt, "F4", &f4Res, sizeof(f4Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f4Res, (i + 4) % 16);

    uint32_t f5Res;
    ret = GmcGetVertexPropertyByName(stmt, "F5", &f5Res, sizeof(f5Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f5Res, (i + 5) % 1024);

    uint32_t f6Res;
    ret = GmcGetVertexPropertyByName(stmt, "F6", &f6Res, sizeof(f6Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f6Res, i + 6);

    uint8_t f7Res;
    ret = GmcGetVertexPropertyByName(stmt, "F7", &f7Res, sizeof(f7Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(f7Res, (i + 7) % 33);
}

static Status LookUpVertexByPk(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GMERR_OK;
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(ret, GMERR_OK);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            std::cout << "look by pk, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ret;
        }

        CheckVertexData(stmt, i);
    }
    return GMERR_OK;
}

static void InsertVertexData(
    GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, Status checkRet = GMERR_OK)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i + 1;
        uint32_t f2 = i + 2;
        uint32_t f3 = i + 3;
        uint32_t f4 = (i + 4) % 16;
        uint32_t f5 = (i + 5) % 1024;
        uint32_t f6 = i + 6;
        uint8_t f7 = (i + 7) % 33;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &f5, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &f6, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7, sizeof(uint8_t)));
        EXPECT_EQ(checkRet, GmcExecute(stmt));
    }
}

TEST_F(StImportExportTable, TestGmcExportFileAndClearFile)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 插入数据
    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    // 清空目录下所有导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // "ls path | grep -c ".*\.bin_data"
    string findAllExportFile = string("ls -l ") + g_exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);

    // 导出该表
    const char *exportTables[] = {labelName};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 1;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    // 确认文件存在
    // "ls path | grep -c ".*rsm_heap_table.*\.bin_data"
    string findFile =
        string("ls ") + g_exportFilePath + string(" | grep -c \".*") + string(labelName) + string(".*\\.bin_data\"");
    pipe = popen(findFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);  // 有一个对应该表名的文件
    }
    pclose(pipe);

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();

    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 使用工具导入文件
    string cmd = string("gmimport -c bin_data -f ") + g_exportFilePath;
    int result = system(cmd.c_str());
    EXPECT_EQ(0, result);
    sleep(5);

    // 确认数据恢复成功
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    // 清除导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    // 确认清除文件成功
    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);
    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcExportDuplicateFileAndClearFile)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *labelName2 = "rsm_heap_table2";
    EXPECT_EQ(GMERR_OK, GmcDuplicateVertexLabelWithName(stmt, labelName, labelName2, labelConfig));

    // 插入数据
    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    InsertVertexData(stmt, 1, 100, labelName2);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName2));

    // 清空目录下所有导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // "ls path | grep -c ".*\.bin_data"
    string findAllExportFile = string("ls -l ") + g_exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);

    // 导出该表
    const char *exportTables[] = {labelName, labelName2};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 2;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    // 确认文件存在
    // "ls path | grep -c ".*rsm_heap_table.*\.bin_data"
    string findFile =
        string("ls ") + g_exportFilePath + string(" | grep -c \".*") + string(labelName) + string(".*\\.bin_data\"");
    pipe = popen(findFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(2, fileNum);  // 有2个对应该表名的文件
    }
    pclose(pipe);

    string findFile2 =
        string("ls ") + g_exportFilePath + string(" | grep -c \".*") + string(labelName2) + string(".*\\.bin_data\"");
    pipe = popen(findFile2.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);  // 有一个对应该表名的文件
    }
    pclose(pipe);

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();

    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建基表，预期子表会被自动创建出来
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 使用工具导入文件
    string cmd = string("gmimport -c bin_data -f ") + g_exportFilePath;
    int result = system(cmd.c_str());
    EXPECT_EQ(0, result);
    sleep(5);

    // 确认数据恢复成功
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName2));

    // 清除导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    // 确认清除文件成功
    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);
    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcExportFileOverWriteOldVersion)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 插入数据
    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    // 清空目录下所有导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // "ls path | grep -c ".*\.bin_data"
    string findAllExportFile = string("ls -l ") + g_exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);

    // 导出该表
    const char *exportTables[] = {labelName};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 1;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    // 确认文件存在
    // "ls path | grep -c ".*rsm_heap_table.*\.bin_data"
    string findFile =
        string("ls ") + g_exportFilePath + string(" | grep -c \".*") + string(labelName) + string(".*\\.bin_data\"");
    pipe = popen(findFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);  // 有一个对应该表名的文件
    }
    pclose(pipe);

    // 插入新数据
    InsertVertexData(stmt, 100, 200, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 100, 200, labelName));

    // 再次导出该表
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    // 确认文件存在
    pipe = popen(findFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);  // 有一个对应该表名的文件
    }
    pclose(pipe);

    DestroyConnectionAndStmt(conn, stmt);
    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();

    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 使用工具导入文件
    string cmd = string("gmimport -c bin_data -f ") + g_exportFilePath;
    int result = system(cmd.c_str());
    EXPECT_EQ(0, result);
    sleep(5);

    // 确认数据恢复成功，检测后新数据
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 100, 200, labelName));

    // 清除导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    // 确认清除文件成功
    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);
    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
}

static void InsertVertexDataLabel2(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i + 1;
        uint32_t f2 = i + 2;
        uint32_t f3 = i + 3;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
}

static Status CheckVertexDataLabel2(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GMERR_OK;
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(ret, GMERR_OK);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            std::cout << "look by pk, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ret;
        }

        bool isNull;
        uint32_t f0Res;
        Status ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Res, sizeof(f0Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(f0Res, i);

        uint32_t f1Res;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Res, sizeof(f1Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(f1Res, i + 1);

        uint32_t f2Res;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Res, sizeof(f2Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(f2Res, i + 2);

        uint32_t f3Res;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Res, sizeof(f3Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(f3Res, i + 3);
    }

    return GMERR_OK;
}

TEST_F(StImportExportTable, TestGmcExportMultiTables)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    char *heapLabelSchema2 = NULL;
    const char *labelName2 = "rsm_fixedheap_age";
    (void)ReadJsonFile("./schema/rsm_fixedheap_age.gmjson", &heapLabelSchema2);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema2, labelConfig));

    // 插入数据
    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    InsertVertexDataLabel2(stmt, 1, 100, labelName2);
    EXPECT_EQ(GMERR_OK, CheckVertexDataLabel2(stmt, 1, 100, labelName2));

    // 清空目录下所有导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // "ls path | grep -c ".*\.bin_data"
    string findAllExportFile = string("ls -l ") + g_exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);

    // 导出两个表
    const char *exportTables[] = {labelName, labelName2};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 2;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);

    // 确认文件存在
    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(2, fileNum);  // 有2个对应该表名的文件
    }
    pclose(pipe);
    DestroyConnectionAndStmt(conn, stmt);
    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();

    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema2, labelConfig));

    // 使用工具导入文件
    string cmd = string("gmimport -c bin_data -f ") + g_exportFilePath;
    int result = system(cmd.c_str());
    EXPECT_EQ(0, result);
    sleep(5);

    // 确认数据恢复成功，检测后新数据
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));
    EXPECT_EQ(GMERR_OK, CheckVertexDataLabel2(stmt, 1, 100, labelName2));

    // 清除导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    // 确认清除文件成功
    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);
    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
}

TEST_F(StImportExportTable, TestInvalidExportFilePath)
{
    // 启动db
    const char *invalidPath1 = "/";
    const char *invalidPath2 = "./abc";
    const char *invalidPath3 = "/1/2/3/4/5/6/7/8/9/10/11";
    const char *path[] = {invalidPath1, invalidPath2, invalidPath3};
    for (int i = 0; i < 3; i++) {
        string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
        StartDbServerWithConfig(startCfg.c_str());

        // 建连
        st_clt_init();
        Status ret = GMERR_OK;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        // 创建表
        const char *labelConfig = R"({"max_record_count":1000000})";
        char *heapLabelSchema = NULL;
        const char *labelName = "rsm_heap_table";
        (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

        // 插入数据
        InsertVertexData(stmt, 1, 10, labelName);
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 10, labelName));

        // 导出该表
        const char *exportTables[] = {labelName};
        GmcExportParaT exportPara = {0};
        exportPara.exportDirPath = path[i];
        exportPara.tablesName = exportTables;
        exportPara.tableNum = 1;
        exportPara.threadNum = 1;
        exportPara.timeoutMs = 0;
        ret = GmcExportTables(stmt, &exportPara);
        EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

        ret = GmcClearExportFile(stmt, path[i]);
        EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

        DestroyConnectionAndStmt(conn, stmt);
        st_clt_uninit();
        ShutDownDbServer();
    }
}

TEST_F(StImportExportTable, TestSysPrivilegeWhenExport)
{
    // 启动时开启强鉴权模式，并且自动导入权限
    string startCfg = string("\"schemaLoader=1\" "
                             "\"schemaPath=./schema/schema_loader3/gmuser;./schema/schema_loader3/sys_priv3;./schema/"
                             "schema_loader3/sys_priv;\" "
                             "\"isFastReadUncommitted=1\" "
                             "\"userPolicyMode=2\" \"isUseRsm=0\" \"maxSeMem=512\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 插入数据
    InsertVertexData(stmt, 1, 10, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 10, labelName));

    // 取消掉REPLACE_ANY|SELECT_ANY|INSERT_ANY权限，预期没有权限导出失败
    system("gmrule -c revoke_policy -f ./schema/schema_loader3/sys_priv2/sys_priv_replace_select_insert.gmpolicy");

    const char *exportTables[] = {labelName};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 1;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 添加SELECT_ANY权限，预期导出成功
    system("gmrule -c import_policy -f ./schema/schema_loader3/sys_priv2/sys_priv_select.gmpolicy");
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    string findFile =
        string("ls ") + g_exportFilePath + string(" | grep -c \".*") + string(labelName) + string(".*\\.bin_data\"");
    FILE *pipe = popen(findFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);  // 有一个对应该表名的文件
    }
    pclose(pipe);

    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
}

static void InsertKvData(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GMERR_OK;
    uint32_t maxKeyLen = 128;
    EXPECT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt, labelName));
    for (uint32_t i = start; i < end; i++) {
        char key[128];
        (void)sprintf_s(key, maxKeyLen, "key_%u", i);
        int32_t value = i;
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

TEST_F(StImportExportTable, TestGmcExportMultiTables2)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000, "reboot_persistence": true})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    const char *kvLabelConfig = R"({"reboot_persistence": true})";
    const char *labelName2 = "kvTable_store";
    ret = GmcKvCreateTable(stmt, labelName2, kvLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    InsertKvData(stmt, 1, 100, labelName2);

    // 工具导出
    string args = string("gmexport -c bin_data -st disk -f ") + g_exportFilePath;
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);

    string findAllExportFile = string("ls -l ") + g_exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(3, fileNum);
    }
    pclose(pipe);

    // 使用接口导出 预期覆盖
    const char *labelName3 = "T_GMDB";
    const char *exportTables[] = {labelName, labelName2, labelName3};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 3;
    exportPara.threadNum = 2;
    exportPara.timeoutMs = 10000;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(3, fileNum);
    }
    pclose(pipe);
    DestroyConnectionAndStmt(conn, stmt);
    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();

    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    ret = GmcKvCreateTable(stmt, labelName2, kvLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用工具导入文件
    string cmd = string("gmimport -c bin_data -f ") + g_exportFilePath;
    int result = system(cmd.c_str());
    EXPECT_EQ(0, result);

    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
}

TEST_F(StImportExportTable, GmcDeleteExportFileByTableName)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000, "reboot_persistence": true})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    const char *kvLabelConfig = R"({"reboot_persistence": true})";
    const char *labelName2 = "kvTable_store";
    ret = GmcKvCreateTable(stmt, labelName2, kvLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    InsertKvData(stmt, 1, 100, labelName2);

    // 使用接口导出
    const char *labelName3 = "T_GMDB";
    const char *exportTables[] = {labelName, labelName2, labelName3};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 3;
    exportPara.threadNum = 2;
    exportPara.timeoutMs = 10000;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    string findAllExportFile = string("ls -l ") + g_exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(3, fileNum);
    }
    pclose(pipe);

    const char *deleteTables[] = {labelName2, labelName3};

    ret = GmcDeleteExportFileByTableName(stmt, g_exportFilePath.c_str(), deleteTables, 2);
    EXPECT_EQ(GMERR_OK, ret);
    pipe = popen(findAllExportFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);
    }
    pclose(pipe);

    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
}

TEST_F(StImportExportTable, GmcExportTablesDiffFileSystem)
{
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    string exportFilePath = string("/data/gmdb_st_test/export/");
    system("mkdir -p /data/gmdb_st_test/export/");
    StartDbServerWithConfig(startCfg.c_str());

    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"max_record_count":1000000, "reboot_persistence": true})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    const char *exportTables[] = {labelName};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 1;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 10000;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);

    string findAllExportFile = string("ls -l ") + exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);
    }
    pclose(pipe);

    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
    system("rm -rf /data/gmdb_st_test");
}

TEST_F(StImportExportTable, TestToolExportTableWithoutCRC)
{
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=0\"");
    string exportFilePath = string("/data/gmdb_st_test/export/");
    system("mkdir -p /data/gmdb_st_test/export/");
    StartDbServerWithConfig(startCfg.c_str());

    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"max_record_count":1000000, "reboot_persistence": true})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    InsertVertexData(stmt, 1, 100, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));

    // 工具导出
    string args = string("gmexport -c bin_data -f ") + g_exportFilePath + string(" -st disk -rc none");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);

    st_clt_uninit();
    ShutDownDbServer();
    StartDbServerWithConfig(startCfg.c_str());
    st_clt_init();
    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));

    // 使用工具导入文件
    string cmd = string("gmimport -c bin_data -f ") + g_exportFilePath;
    int result = system(cmd.c_str());
    EXPECT_EQ(0, result);

    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
    system("rm -rf /data/gmdb_st_test");
}

TEST_F(StImportExportTable, TestGmcImportTablesArgsCheck1)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *importDirPath = "/dir";
    const char *importTables[] = {"table1", "table2"};
    GmcImportParaT importPara;
    importPara.importDirPath = NULL;
    importPara.tablesName = importTables;
    importPara.tableNum = 2;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;

    // importDirPath为NULL
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // tablesName为NULL
    importPara.importDirPath = importDirPath;
    importPara.tablesName = NULL;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    importPara.tablesName = importTables;
    // stmt为NULL
    ret = GmcImportTables(NULL, &importPara);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // importPara为NULL
    ret = GmcImportTables(stmt, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();
}

TEST_F(StImportExportTable, TestGmcImportTablesArgsCheck2)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *importTables[] = {"table1", "table2"};
    GmcImportParaT importPara;
    importPara.tablesName = importTables;
    importPara.threadNum = 1;
    importPara.tableNum = 2;
    importPara.timeoutMs = 0;

    // 根目录
    const char *errPath = "/";
    importPara.importDirPath = errPath;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 空路径
    const char *errPath1 = "";
    importPara.importDirPath = errPath1;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // 不支持相对路径
    const char *errPath2 = ".";
    importPara.importDirPath = errPath2;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    const char *errPath3 = "./dir";
    importPara.importDirPath = errPath3;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    const char *errPath4 = "/path/not/exists/";
    importPara.importDirPath = errPath4;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    char longPath[DB_MAX_PATH + 1];
    for (uint32_t i = 0; i < DB_MAX_PATH; i++) {
        longPath[i] = 'a';
    }
    longPath[DB_MAX_PATH] = '\0';
    importPara.importDirPath = (const char *)longPath;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();
}

TEST_F(StImportExportTable, TestGmcImportTablesArgsCheck3)
{
    // 启动db
    string startCfg =
        string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\" \"maxNormalTableNum=2000\"");
    StartDbServerWithConfig(startCfg.c_str());
    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *importDirPath = "/dir";
    const char *importTables[] = {"table1", "table2"};
    GmcImportParaT importPara;
    importPara.importDirPath = importDirPath;
    importPara.tablesName = importTables;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;

    // 表数量范围
    importPara.tableNum = 0;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    importPara.tableNum = 2001;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 空表名
    importPara.tableNum = 2;
    const char *importTables2[] = {"table1", ""};
    importPara.tablesName = importTables2;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();
}

static void StExportBinFile(string &exportFilePath, uint32_t start, uint32_t end)
{
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *labelName2 = "rsm_heap_table2";
    EXPECT_EQ(GMERR_OK, GmcDuplicateVertexLabelWithName(stmt, labelName, labelName2, labelConfig));

    // 插入数据
    InsertVertexData(stmt, start, end, labelName);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, start, end, labelName));

    InsertVertexData(stmt, start, end, labelName2);
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, start, end, labelName2));

    // 清空目录下所有导出文件
    ret = GmcClearExportFile(stmt, exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // "ls path | grep -c ".*\.bin_data"
    string findAllExportFile = string("ls -l ") + exportFilePath + string(" | grep -c \".*\\.bin_data\"");
    FILE *pipe = popen(findAllExportFile.c_str(), "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(0, fileNum);  // 预期没有对应文件
    }
    pclose(pipe);

    // 导出该表
    const char *exportTables[] = {labelName, labelName2};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 2;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);
    EXPECT_EQ(GMERR_OK, ret);
    // 确认文件存在
    // "ls path | grep -c ".*rsm_heap_table.*\.bin_data"
    string findFile =
        string("ls ") + exportFilePath + string(" | grep -c \".*") + string(labelName) + string(".*\\.bin_data\"");
    pipe = popen(findFile.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(2, fileNum);  // 有2个对应该表名的文件
    }
    pclose(pipe);

    string findFile2 =
        string("ls ") + exportFilePath + string(" | grep -c \".*") + string(labelName2) + string(".*\\.bin_data\"");
    pipe = popen(findFile2.c_str(), "r");
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        int fileNum = atoi(buffer);
        EXPECT_EQ(1, fileNum);  // 有一个对应该表名的文件
    }
    pclose(pipe);

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();
}

TEST_F(StImportExportTable, TestGmcImportTables1)
{
    StExportBinFile(g_exportFilePath, 1, 100);
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);
    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    const char *labelName2 = "rsm_heap_table2";
    const char *importTables[] = {labelName, labelName2};
    // 创建基表，预期子表会被自动创建出来
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importTables;
    importPara.tableNum = 2;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    system("gmsysview count");

    // 确认数据恢复成功
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName2));

    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcImportTablesWithDuplicateTables)
{
    StExportBinFile(g_exportFilePath, 1, 100);
    // 启动db
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    // 启动
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    const char *labelName2 = "rsm_heap_table2";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *importDupTables[] = {labelName, labelName, labelName2, labelName2, labelName2};
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importDupTables;
    importPara.tableNum = 5;
    importPara.threadNum = 2;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    system("gmsysview count");

    // 确认数据恢复成功
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 100, labelName2));

    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcImportTablesTimeout)
{
    StExportBinFile(g_exportFilePath, 1, 100);
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    const char *labelName2 = "rsm_heap_table2";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *importDupTables[] = {labelName, labelName, labelName2, labelName2, labelName2};
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importDupTables;
    importPara.tableNum = 5;
    importPara.threadNum = 1;
    importPara.timeoutMs = 1;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    sleep(1);

    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcImportTablesFileNotExist)
{
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    const char *labelName2 = "rsm_heap_table2";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *importDupTables[] = {labelName, labelName, labelName2, labelName2, labelName2};
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importDupTables;
    importPara.tableNum = 5;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // 导入文件不存在
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcImportTablesTableNumErr)
{
    StExportBinFile(g_exportFilePath, 1, 100);
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    const char *labelName2 = "rsm_heap_table2";
    const char *labelName3 = "rsm_heap_table3";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *importTables[] = {labelName, labelName2, labelName3};
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestGmcImportTablesFileNameErr)
{
    StExportBinFile(g_exportFilePath, 1, 100);
    system("mv ${GMDB_HOME}/exportFileTestPath/*rsm_heap_table2*.bin_data "
           "${GMDB_HOME}/exportFileTestPath/rsm_heap_table2.bin_data");
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);

    // 建连
    st_clt_init();
    Status ret = GMERR_OK;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelConfig = R"({"max_record_count":1000000})";
    char *heapLabelSchema = NULL;
    const char *labelName = "rsm_heap_table";
    (void)ReadJsonFile("./schema/rsm_heap_table.gmjson", &heapLabelSchema);
    const char *labelName2 = "rsm_heap_table2";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, heapLabelSchema, labelConfig));
    const char *importDupTables[] = {labelName, labelName, labelName2, labelName2, labelName2};
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importDupTables;
    importPara.tableNum = 5;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    DestroyConnectionAndStmt(conn, stmt);

    st_clt_uninit();
}

TEST_F(StImportExportTable, TestExportUpgradeTablesAndImport)
{
    const char *labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
    const char *labelName = "rsm_heap_table";
    char *labelSchema;
    char *labelSchemaV2;
    (void)ReadJsonFile("./schema/rsm_table_v1.gmjson", &labelSchema);
    (void)ReadJsonFile("./schema/rsm_table_v2.gmjson", &labelSchemaV2);
    string startCfg = string("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\" \"isUseRsm=1\"");
    StartDbServerWithConfig(startCfg.c_str());
    st_clt_init();

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelSchema, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertVertex(stmt, 0, 10, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = LookUpVertex(stmt, 0, 10, true, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    UpgradeVertexLabel(stmt, labelSchemaV2, labelName, GMERR_OK);
    ret = InsertVertexWithVersion(stmt, 10, 20, labelName, 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = LookUpVertexWithVersion(stmt, 10, 20, true, labelName, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 清空目录下所有导出文件
    ret = GmcClearExportFile(stmt, g_exportFilePath.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    // 导出该表
    const char *exportTables[] = {labelName};
    GmcExportParaT exportPara = {0};
    exportPara.exportDirPath = g_exportFilePath.c_str();
    exportPara.tablesName = exportTables;
    exportPara.tableNum = 1;
    exportPara.threadNum = 1;
    exportPara.timeoutMs = 0;
    ret = GmcExportTables(stmt, &exportPara);
    ASSERT_EQ(GMERR_OK, ret);
    system("gmsysview record rsm_heap_table");

    DestroyConnectionAndStmt(conn, stmt);

    // 关闭db
    st_clt_uninit();
    ShutDownDbServer();

    // 重启db
    StartDbServerWithConfig(startCfg.c_str());
    sleep(1);
    st_clt_init();

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, labelSchema, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    UpgradeVertexLabel(stmt, labelSchemaV2, labelName, GMERR_OK);

    const char *importTables[] = {labelName};
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportFilePath.c_str();
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(stmt, &importPara);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = LookUpVertexWithVersion(stmt, 10, 20, true, labelName, 2);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview record rsm_heap_table");
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    FreeJsonBuf(&labelSchema);
    FreeJsonBuf(&labelSchemaV2);
    DestroyConnectionAndStmt(conn, stmt);
    st_clt_uninit();
}
