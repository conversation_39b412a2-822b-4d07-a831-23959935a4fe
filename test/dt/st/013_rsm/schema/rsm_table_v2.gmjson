[{"type": "record", "name": "rsm_heap_table", "schema_version": 2, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}], "keys": [{"node": "rsm_heap_table", "name": "pk", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]