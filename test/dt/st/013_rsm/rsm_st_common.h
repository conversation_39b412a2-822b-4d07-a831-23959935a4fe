/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: rsm_st_common.h
 * Description: Implementation of rsm common
 * Author:
 * Create:
 */

#ifndef RSM_ST_COMMON_H
#define RSM_ST_COMMON_H

#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <sys/epoll.h>
#include "query_subs_st_base.h"
#include "client_common_st.h"
#include "gmc_connection.h"
#include "rsm_st_gmc_util.h"

// 读取json文件
long ReadJsonFile(const char *path, char **buf);
// 释放jsonbuf
void FreeJsonBuf(char **buf);
// 读取表名
void GetLabelNameFromJson(char *jsonBuf, char **labelNameBuf);

void RsmStartServerAndGmcInit(bool warmReboot, const char *config = NULL);

void RsmStartServerAndGmcInitWithClusteredHash(bool warmReboot);

void RsmStartServerAndGmcInitWithClusteredHashAndCompress(bool warmReboot);

void RsmStartServerAndGmcInitWithCompress(bool warmReboot);

void RsmStopServerAndGmcUnInit(void);

inline void RsmStartServerAndInit(bool warmReboot, const char *serverConfig = NULL)
{
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInit(warmReboot, serverConfig);
}

inline void RsmStartServerAndInitWithUnInit(bool warmReboot, const char *serverConfig = NULL)
{
    ShutDownDbServer();
    RsmStartServerAndGmcInit(warmReboot, serverConfig);
}

void CreateSubPubConnect(GmcConnT **subConn);

void FreeSubPubConnect(GmcConnT **subConn);

void CreateSubScription(
    GmcStmtT *stmt, SubVerifyT *subVerify, GmcConnT *subConn, const char *subsName, char *configJson);

void CreateSubScription1(GmcStmtT *stmt, GmcConnT *subConn, const char *subsName, char *configJson);

void DestroySubScription(GmcStmtT *stmt, const char *subsName);

void CreateAsyncConnect(GmcConnT **asyncConn, GmcStmtT **stmt);

void FreeAsyncConnect(GmcConnT **asyncConn, GmcStmtT **stmt);

Status InsertVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName);

Status ReplaceVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, bool isLayout = false);

Status DeleteVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName);

Status LookUpVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName);

Status UpdateVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName);

Status LookUpVertexAfterUpdate(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName);

Status InsertVertexWithVersion(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, uint32_t version);
Status LookUpVertexWithVersion(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName, uint32_t version);

void UpgradeVertexLabel(GmcStmtT *stmt, const char *labelJson, const char *labelName, Status checkRet);

void DowngradeVertexLabel(GmcStmtT *stmt, const char *labelName, uint32_t schemaVersion, Status checkRet);

Status SetKv(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName);

Status GetKv(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName);

Status RemoveKv(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName);

inline void GetCommandResult(const char *command, char *result, int resLen)
{
    printf("%s\n", command);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(result, resLen, pf)) {
    }
    pclose(pf);
}

void RsmWaitRecoveryFinish(void);

#endif
