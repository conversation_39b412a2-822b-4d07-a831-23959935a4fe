/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: client_st_rsm_ch_label_vertex.cc
 * Description: testcase for rsm ch label
 * Author: lujiahao
 * Create: 2024-01-11
 */
#include "tools_st_common.h"
#include "storage_st_common.h"
#include "rsm_st_common.h"
#include "pthread.h"

class StRecoveryRsmChVertex : public RsmStGmcUtil, public StStorage {
protected:
    uint32_t setUpLogNum = 0;
    uint32_t tearDownLogNum = 0;
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    void SetUp()
    {
        DoSetUp();
        FILE *fp = popen("grep -rn 'topRsmCtx check' ./log/run/rgmserver/* | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        setUpLogNum = atoi(errorNum);
    }
    void TearDown()
    {
        FILE *fp = popen("grep -rn 'topRsmCtx check' ./log/run/rgmserver/* | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        tearDownLogNum = atoi(errorNum);
        EXPECT_EQ(tearDownLogNum, setUpLogNum);  // 预期该测试套用例都不应该有memctx对账过程中删掉的内存
        DoTearDownCheckAndUninit();
        ShutDownDbServer();
        SaveLogAfterFailed();
    }
};

TEST_F(StRecoveryRsmChVertex, TestClientConnect)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

static const char *g_labelConfig =
    R"({"max_record_count":1000000, "is_support_reserved_memory":1, "isFastReadUncommitted":true})";
static const char *g_labelName = "rsm_clustered_hash_table";
static const char *g_labelSchema =
    R"([{
        "type":"record",
        "name":"rsm_clustered_hash_table",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_clustered_hash_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true }
                },
                {
                    "node":"rsm_clustered_hash_table",
                    "name":"local",
                    "fields":["F1"],
                    "index":{"type":"local"},
                    "constraints":{ "unique":true }
                }
            ]
        }])";

static const char *g_mergeSubTablelabelConfig =
    R"({"max_record_count":1000000, "is_support_reserved_memory":1, "status_merge_sub":true})";
static const char *g_mergeSubTablelabelName = "rsm_merge_sub_table";
static const char *g_mergeSubTablelabelSchema =
    R"([{
        "type":"record",
        "name":"rsm_merge_sub_table",
        "subs_type":"status_merge",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_merge_sub_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelCreateAndDrop)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelCreateAndDropWithTspName)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    static const char *labelConfig =
        R"({"max_record_count":1000000,
            "is_support_reserved_memory":1,
            "rsm_tablespace_name": "abcd",
            "isFastReadUncommitted":true})";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelInsertAndDelete)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview record rsm_clustered_hash_table");

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DeleteVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecovery)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview record rsm_clustered_hash_table");

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview record rsm_clustered_hash_table");

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelConfig)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    static const char *labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    char command[512];
    char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    printf("command = %s\n", command);

    ret = executeCommand(command, "SUPPORT_RESERVED_MEMORY: 1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelTruncate)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    char command[512];
    char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    printf("command = %s\n", command);

    ret = executeCommand(command, "ENTRY_USED: 0", "SUPPORT_RESERVED_MEMORY: 1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// 合并订阅表恢复后查看数据-聚簇容器
TEST_F(StRecoveryRsmChVertex, TestMergeSubTableRsmChLabelRecovery)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview record rsm_merge_sub_table");

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview record rsm_merge_sub_table");

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

void WaitAgedFinish(GmcStmtT *stmt, const char *labelName, uint32_t expectCnt)
{
    GmcCheckInfoT *checkInfo = NULL;
    uint32_t checkCnt = 0;
    const uint32_t printCnt = 10;
    do {
        ASSERT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo));
        if ((++checkCnt) % printCnt == 0) {
            cout << "RealAgedCnt:" << checkInfo->realAgedCnt << ", expectCnt:" << expectCnt << endl;
        }
    } while (checkInfo->realAgedCnt < expectCnt);
}

void TestRsmChLabelCheck(GmcStmtT *stmt, const char *labelName)
{
    uint32_t recordNum = 1000;
    Status ret = InsertVertex(stmt, 0, recordNum, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview count");

    ret = LookUpVertex(stmt, 0, recordNum, true, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, GMC_FULL_TABLE);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t replaceNum = recordNum / 10;
    ret = ReplaceVertex(stmt, 0, replaceNum, labelName, true);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, labelName, GMC_FULL_TABLE, false);
    ASSERT_EQ(GMERR_OK, ret);

    WaitAgedFinish(stmt, labelName, recordNum - replaceNum);
}

// 老化数据恢复后查看数据-聚簇容器
TEST_F(StRecoveryRsmChVertex, TestRsmChLabelCheckRecovery)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);

    TestRsmChLabelCheck(stmt, g_labelName);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview count");

    DestroyConnectionAndStmt(conn, stmt);
}

// 合并订阅表老化数据恢复后查看数据-聚簇容器
TEST_F(StRecoveryRsmChVertex, TestMergeSubTableRsmChLabelCheckRecovery)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    ASSERT_EQ(GMERR_OK, ret);

    TestRsmChLabelCheck(stmt, g_mergeSubTablelabelName);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview count");

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecoveryTableView)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char command[512];
    char const *viewName = "V\\$CATA_LABEL_RECOVERY_INFO";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    printf("command = %s\n", command);
    system(command);

    ret = executeCommand(command, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 0");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    system(command);

    ret = executeCommand(command, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecoveryInterceptRequest)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char command[512];
    char const *viewName = "V\\$CATA_LABEL_RECOVERY_INFO";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    printf("command = %s\n", command);
    system(command);

    ret = executeCommand(command, "TOTAL_LABEL_CNT: 2", "RECOVERY_LABEL_CNT: 0");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    system(command);

    ret = executeCommand(command, "TOTAL_LABEL_CNT: 2", "RECOVERY_LABEL_CNT: 1", "LABEL_NAME: rsm_merge_sub_table");
    EXPECT_EQ(GMERR_OK, ret);

    sleep(1);
    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 10, 20, g_labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

void RsmStartServerAndGmcInitWithErrorConfig(bool warmReboot)
{
    g_clientServerWarmReboot = warmReboot;
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\"", true, false, "usocket:/run/verona/unix_emserver");
    char command[100] = "gmsysview -q V\\$DB_SERVER";
    EXPECT_EQ(GMERR_OK, executeCommand(command, "ErrorCode: 1002002"));
    g_clientServerWarmReboot = false;
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecoveryWithErrorConfig)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithErrorConfig(true);
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char command[512];
    char const *viewName = "V\\$CATA_LABEL_RECOVERY_INFO";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    printf("command = %s\n", command);
    system(command);

    ret = executeCommand(command, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 0");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    system(command);

    ret = executeCommand(command, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

void RsmStartServerAndGmcInitWithErrorGlobalConfig(bool warmReboot)
{
    g_clientServerWarmReboot = warmReboot;
    uint32_t testNum = 7;
    const char *testArr[testNum] = {
        "\"maxSeMem=512\"  \"isUseRsm=1\" \"enableClusterHash=1\" \"deviceSize=1\"",
        "\"maxSeMem=512\"  \"isUseRsm=1\" \"enableClusterHash=1\" \"pageSize=4\"",
        "\"maxSeMem=512\"  \"isUseRsm=1\" \"enableClusterHash=1\" \"RsmBlockSize=64\"",
        "\"maxSeMem=512\"  \"isUseRsm=1\" \"enableClusterHash=1\" \"RsmKeyRange=0,2\"",
        "\"maxSeMem=512\"  \"isUseRsm=1\" \"enableClusterHash=0\"",
        "\"maxSeMem=512\"  \"isUseRsm=1\" \"enableClusterHash=1\" \"maxWriteCacheSize=64\"",
        "\"maxSeMem=512\"  \"isUseRsm=0\" \"enableClusterHash=1\"",
    };
    for (uint32_t i = 0; i < testNum; i++) {
        StartDbServerWithConfig(testArr[i], true, false, "usocket:/run/verona/unix_emserver");
        char command[100] = "gmsysview -q V\\$DB_SERVER";
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ErrorCode: 1002002"));
    }
    g_clientServerWarmReboot = false;
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecoveryWithErrorGlobalConfig)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();

    RsmStartServerAndGmcInitWithErrorGlobalConfig(true);
    RsmStartServerAndGmcInitWithClusteredHash(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecoveryWithDuplicateTable)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(1);
    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

static uint32_t g_threadNum = 0;

void *RsmRecoveryCreateSameVertex(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    if (ret != GMERR_OK && ret != GMERR_DUPLICATE_TABLE) {
        std::cout << "recovery table unsucc, ret = " << ret << std::endl;
        EXPECT_EQ(GMERR_OK, ret);
    }

    DestroyConnectionAndStmt(conn, stmt);
    DbAtomicInc(&g_threadNum);
    return NULL;
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecoveryWithConcurrency)
{
    g_threadNum = 0;
    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertVertex(stmt, 0, 10, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);

    // 服务端重启，warm reboot
    RsmStopServerAndGmcUnInit();
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int threadNum = 10;
    int err = 0;
    pthread_t pthread[threadNum];
    for (int i = 0; i < threadNum; i++) {
        err = pthread_create(&pthread[i], NULL, RsmRecoveryCreateSameVertex, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < threadNum; i++) {
        err = pthread_join(pthread[i], NULL);
        EXPECT_EQ(GMERR_OK, err);
    }

    ret = GmcCreateVertexLabel(stmt, g_mergeSubTablelabelSchema, g_mergeSubTablelabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    RsmWaitRecoveryFinish();

    ret = LookUpVertex(stmt, 0, 10, true, g_mergeSubTablelabelName);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_threadNum != 10) {
        sleep(1);
        ret = LookUpVertex(stmt, 0, 10, true, g_labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    DestroyConnectionAndStmt(conn, stmt);
}

#ifdef ENABLE_CRASHPOINT

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelInsertCollapseRecovery)
{
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 10, 11, g_labelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 10"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 11, false, g_labelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_labelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit(true);
}

static void InsertVertexWithKeyViolation(
    GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, Status checkRet = GMERR_OK)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = 0;  // F1 字段构造冲突
        uint32_t f2 = i * 3;
        uint32_t f3 = i * 4;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        EXPECT_EQ(checkRet, GmcExecute(stmt));
    }
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelInsertRollbackCollapseRecovery)
{
    uint32_t testNum = 4;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        InsertVertexWithKeyViolation(stmt, 10, 11, g_labelName, GMERR_CONNECTION_RESET_BY_PEER);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 10"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 11, false, g_labelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_labelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit(true);
}

const char *g_bigLabelName = "rsm_clustered_hash_big_table";
const char *g_bigLabelConfig =
    R"({"max_record_count":1000000, "is_support_reserved_memory":1, "isFastReadUncommitted":true})";
char *g_bigLabelSchema = NULL;
TEST_F(StRecoveryRsmChVertex, TestRsmChLabelExpandCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    uint32_t testNum = 19;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        // SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,       // 升降级才会涉及到
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        // SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,                   // 没有走到
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        // SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,                  // 升降级才会涉及到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,                // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,                  // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_BEFORE,  // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF,    // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_AFTER,   // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,                 // 没有走到
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_FAIL,                 // 没有走到
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 37391, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 37391, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "DIRECTORY_PAGE_COUNT: 1", "ENTRY_USED: 37391"));

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 37391, 37392, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 37391"));

        ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 37391, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 40000, 41000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 40000, 41000, true, g_bigLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 38391"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelExpandCollapseRecovery1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    uint32_t testNum = 16;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        // SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,       // 升降级才会涉及到
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        // SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,                   // 没有走到，因为dir页也扩容
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        // SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,                  // 升降级才会涉及到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,                // 没有走到，因为dir页也扩容
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,                  // 没有走到，因为dir页也扩容
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_BEFORE,  // 没有走到，因为dir页也扩容
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF,    // 没有走到，因为dir页也扩容
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_AFTER,   // 没有走到，因为dir页也扩容
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,                 // 没有走到，因为dir页也扩容
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE,           // 没有走到，因为没有申请新dir页
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT,    // 没有走到，因为没有申请新dir页
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,       // 没有走到，因为没有更新旧dir
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_FAIL,                 // 没有走到，因为没有申请新dir页失败
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 57, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 57, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "SEGMENT_PAGE_COUNT: 1", "ENTRY_USED: 57"));

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 57, 58, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 57"));

        ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 57, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 500, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 500, true, g_bigLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 457"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelExpandCollapseRecovery2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        // SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,       // 升降级才会涉及到
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        // SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,                  // 升降级才会涉及到
        SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_BEFORE,  // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF,    // 没有走到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_AFTER,   // 没有走到
        SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,               // 没有走到，因为dir没有扩容
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE,           // 没有走到，因为dir没有扩容
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT,    // 没有走到，因为dir没有扩容
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,       // 没有走到，因为dir没有扩容
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,       // 没有走到，因为dir没有扩容
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_FAIL,                 // 没有走到，因为dir没有扩容
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER,                // 没有走到，因为dir没有扩容
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 125, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 125, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "SEGMENT_PAGE_COUNT: 3", "ENTRY_USED: 125"));

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 125, 126, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 125"));

        ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 125, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 200, 600, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 200, 600, true, g_bigLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 525"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelDeleteCollapseRecovery)
{
    uint32_t testNum = 4;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 9, 10, g_labelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 10"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_labelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelUpdateCollapseRecovery)
{
    uint32_t testNum = 3;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_UPDATE_BEFORE,
        SHM_CRASH_CH_UPDATE_CHANGE_ROW,
        SHM_CRASH_CH_UPDATE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        UpdateVertex(stmt, 9, 10, g_labelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
        RsmWaitRecoveryFinish();
        system("gmsysview record rsm_clustered_hash_table");

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 10"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexAfterUpdate(stmt, 9, 10, false, g_labelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_labelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit(true);
}

static void UpdateVertexWithKeyViolation(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = 0;
        uint32_t f2 = i * 9;
        uint32_t f3 = i * 10;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, GmcExecute(stmt));
    }
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelUpdateRollbakCollapseRecovery)
{
    uint32_t testNum = 2;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_UPDATE_ROLLBACK_BEFORE,
        SHM_CRASH_CH_UPDATE_ROLLBACK_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        UpdateVertexWithKeyViolation(stmt, 9, 10, g_labelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
        RsmWaitRecoveryFinish();
        system("gmsysview record rsm_clustered_hash_table");

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 10"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_labelName));

        EXPECT_EQ(GMERR_OK, LookUpVertexAfterUpdate(stmt, 9, 10, false, g_labelName));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_labelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecyclePageCollapseRecovery)
{
    uint32_t testNum = 3;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_RECYCLE_PAGE_BEFORE,
        SHM_CRASH_CH_RECYCLE_PAGE_FREE_PAGE,
        SHM_CRASH_CH_RECYCLE_PAGE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_labelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 0, 996, g_labelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 4"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 996, false, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 996, 1000, true, g_labelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 500, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 500, true, g_labelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 1000, 2000, g_labelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 1000, 2000, true, g_labelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit(true);
}

// 待补充一个用例测试一次性回收多个dir页
TEST_F(StRecoveryRsmChVertex, TestRsmChLabelScaleInDirCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    uint32_t testNum = 3;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_SCALEIN_DIR_BEFORE,
        SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE,
        SHM_CRASH_CH_SCALEIN_DIR_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100000, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "DIRECTORY_PAGE_COUNT: 3", "ENTRY_USED: 100000"));

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 0, 99949, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 51"));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100000, 110000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100000, 110000, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10000, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

char *g_bigLabelSchemaV2 = NULL;
TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionAllocNewPageCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_EXPAND_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_CHAIN,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_ADD_LOCK,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_DIR,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_OUTPUT,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 100, 101, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionInsertCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 100, 101, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionInsertRollbackCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 4;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);
        InsertVertexWithKeyViolation(stmt, 100, 101, g_bigLabelName, GMERR_CONNECTION_RESET_BY_PEER);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 101, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionDeleteCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 4;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 50, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 50, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 50"));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 50, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 50, 100, true, g_bigLabelName));

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 99, 100, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionUpdateCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE,
        // SHM_CRASH_CH_UPDATE_MULVERSION_NO_CHANGE,        // 没有走到
        SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE_DELETE,
        SHM_CRASH_CH_UPDATE_MULVERSION_DELETE,
        SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE_INSERT,
        SHM_CRASH_CH_UPDATE_MULVERSION_INSERT,
        SHM_CRASH_CH_UPDATE_MULVERSION_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);
        UpdateVertex(stmt, 99, 100, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexAfterUpdate(stmt, 99, 100, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionUpdateRollbackCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_BEFORE,
        SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_DELETE,
        SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_UPDATE_STAT,
        SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_INSERT,
        SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 50, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 50, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 50, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 50, 100, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        DbShmCrashPointSet(testCases[i]);
        UpdateVertexWithKeyViolation(stmt, 49, 50, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexAfterUpdate(stmt, 99, 100, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

char *g_bigLabelSchemaV3 = NULL;
TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionExpandCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table3.gmjson", &g_bigLabelSchemaV3);
    uint32_t testNum = 18;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,  // 升降级才会涉及到
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        // SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,                   // 没有走到，走DOUBLE分支
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,  // 升降级才会涉及到
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,                // 没有走到，走DOUBLE分支
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,                  // 没有走到，走DOUBLE分支
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_BEFORE,  // 没有走到，走DOUBLE分支
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF,    // 没有走到，走DOUBLE分支
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_AFTER,   // 没有走到，走DOUBLE分支
        // SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,                 // 没有走到，走DOUBLE分支
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE,           // 没有走到，没有申请新dir页
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT,    // 没有走到，没有申请新dir页
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,       // 没有走到，没有申请新dir页
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,
        // SHM_CRASH_CH_EXPAND_DOUBLE_DIR_FAIL,                 // 没有走到
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 20, 52, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 52, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 52"));

        DbShmCrashPointSet(testCases[i]);
        InsertVertex(stmt, 52, 53, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 52"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 52, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 52, 53, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    FreeJsonBuf(&g_bigLabelSchemaV3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionRecycleFirstPageCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table3.gmjson", &g_bigLabelSchemaV3);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_CHANGE_ALL_SEG,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_CHANGE_CUR_SEG,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FREE_PAGE_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FREE_PAGE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 20, 30, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 30, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 30"));

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 20, 30, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        if (i < 4) {
            char checkCommand[512];
            char const *chekcViewName = "V\\$STORAGE_RSM_BLOCK_STAT";
            (void)snprintf(checkCommand, 512, "gmsysview -q %s", chekcViewName);
            std::cout << "Before recovery, check command = " << checkCommand << std::endl;
            system(checkCommand);
            EXPECT_EQ(
                GMERR_OK, executeCommand(checkCommand, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 124"));

            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
            RsmWaitRecoveryFinish();

            std::cout << "After recovery, check command = " << checkCommand << std::endl;
            system(checkCommand);
            EXPECT_EQ(
                GMERR_OK, executeCommand(checkCommand, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 125"));
        } else {
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
            RsmWaitRecoveryFinish();
        }

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 20"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 20, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 30, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    FreeJsonBuf(&g_bigLabelSchemaV3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionRecycleMiddlePageCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table3.gmjson", &g_bigLabelSchemaV3);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_GET_PRE_ADDR,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_CHANGE_SEG,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_FREE_PAGE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 20, 30, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 30, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 30"));

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 10, 20, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        if (i == 1 || i == 2) {
            char checkCommand[512];
            char const *chekcViewName = "V\\$STORAGE_RSM_BLOCK_STAT";
            (void)snprintf(checkCommand, 512, "gmsysview -q %s", chekcViewName);
            std::cout << "Before recovery, check command = " << checkCommand << std::endl;
            system(checkCommand);
            EXPECT_EQ(
                GMERR_OK, executeCommand(checkCommand, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 124"));

            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
            RsmWaitRecoveryFinish();

            std::cout << "After recovery, check command = " << checkCommand << std::endl;
            system(checkCommand);
            EXPECT_EQ(
                GMERR_OK, executeCommand(checkCommand, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 125"));
        } else {
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
            RsmWaitRecoveryFinish();
        }

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 20"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 30, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    FreeJsonBuf(&g_bigLabelSchemaV3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionRecycleLastPageCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table3.gmjson", &g_bigLabelSchemaV3);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_GET_PRE_ADDR,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_CHANGE_SEG,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_FREE_PAGE,
        SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10, 20, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 20, 30, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 30, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 30"));

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 0, 10, g_bigLabelName);  // 预期崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        if (i == 1 || i == 2) {
            char checkCommand[512];
            char const *chekcViewName = "V\\$STORAGE_RSM_BLOCK_STAT";
            (void)snprintf(checkCommand, 512, "gmsysview -q %s", chekcViewName);
            std::cout << "Before recovery, check command = " << checkCommand << std::endl;
            system(checkCommand);
            EXPECT_EQ(
                GMERR_OK, executeCommand(checkCommand, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 124"));

            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
            RsmWaitRecoveryFinish();

            std::cout << "After recovery, check command = " << checkCommand << std::endl;
            system(checkCommand);
            EXPECT_EQ(
                GMERR_OK, executeCommand(checkCommand, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 125"));
        } else {
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
            EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 100, 101, g_bigLabelName));
            UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
            RsmWaitRecoveryFinish();
        }

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 20"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10, false, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10, 20, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 20, 30, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    FreeJsonBuf(&g_bigLabelSchemaV3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelRecyclePageCollapseRecoveryWithRsmBlockStatCheck)
{
    RsmStartServerAndGmcInitWithClusteredHash(false);
    std::cout << "BeforeReboot" << std::endl;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));

    EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_labelName));

    DbShmCrashPointSet(SHM_CRASH_CH_RECYCLE_PAGE_BEFORE);
    DeleteVertex(stmt, 0, 996, g_labelName);  // 预期崩溃恢复

    DbShmCrashPointDetach();
    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();
    // 服务端重启，warm reboot
    RsmStartServerAndGmcInitWithClusteredHash(true);
    std::cout << "RecoveryOnReboot" << std::endl;

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char command[512];
    char const *viewName = "V\\$STORAGE_RSM_BLOCK_STAT";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    std::cout << "Before recovery, command = " << command << std::endl;
    system(command);
    EXPECT_EQ(GMERR_OK, executeCommand(command, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 123"));

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema, g_labelConfig));
    RsmWaitRecoveryFinish();

    std::cout << "After recovery, command = " << command << std::endl;
    system(command);
    EXPECT_EQ(GMERR_OK, executeCommand(command, "RSM_BLOCK_ID: 0", "DEV_ID: 0", "FREE_RSM_SUB_BLOCK_CNT: 124"));

    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 996, false, g_labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 996, 1000, true, g_labelName));
    EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 500, g_labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 500, true, g_labelName));
    EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 1000, 2000, g_labelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 1000, 2000, true, g_labelName));

    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelScaleInDirCollapseRecoveryWithRsmBlockStatCheck)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);

    RsmStartServerAndGmcInitWithClusteredHash(false);
    std::cout << "BeforeReboot" << std::endl;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

    EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100000, g_bigLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100000, true, g_bigLabelName));

    DbShmCrashPointSet(SHM_CRASH_CH_SCALEIN_DIR_BEFORE);
    DeleteVertex(stmt, 0, 99949, g_bigLabelName);  // 预期崩溃恢复

    DbShmCrashPointDetach();
    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();
    // 服务端重启，warm reboot
    RsmStartServerAndGmcInitWithClusteredHash(true);
    std::cout << "RecoveryOnReboot" << std::endl;

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char command[512];
    char const *viewName = "V\\$STORAGE_RSM_BLOCK_STAT";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    std::cout << "Before recovery, command = " << command << std::endl;
    system(command);
    EXPECT_EQ(GMERR_OK, executeCommand(command, "RSM_BLOCK_ID: 0", "DEV_ID: 15", "FREE_RSM_SUB_BLOCK_CNT: 105"));

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
    RsmWaitRecoveryFinish();

    std::cout << "After recovery, command = " << command << std::endl;
    system(command);
    EXPECT_EQ(GMERR_OK, executeCommand(command, "RSM_BLOCK_ID: 0", "DEV_ID: 15", "FREE_RSM_SUB_BLOCK_CNT: 106"));

    EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100000, 110000, g_bigLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100000, 110000, true, g_bigLabelName));
    EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 10000, g_bigLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 10000, true, g_bigLabelName));

    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();

    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

// 用例测试扫描后触发回收空页，因性能问题暂不回收，等后续逻辑补充后放开
TEST_F(StRecoveryRsmChVertex, DISABLED_TestRsmChLabelScaleInFreeEmptyPageCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_BEFORE,
        SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE,
        SHM_CRASH_CH_SCALE_IN_UPDATE_DIR_BEFORE,
        SHM_CRASH_CH_SCALE_IN_UPDATE_PATTERN,
        SHM_CRASH_CH_SCALE_IN_UPDATE_SEG_DEPTH,
        SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_END,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 2000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 2000, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        DbShmCrashPointSet(testCases[i]);

        EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, g_bigLabelName, GMC_FULL_TABLE));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10000, 10100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, GmcEndCheck(stmt, g_bigLabelName, GMC_FULL_TABLE, false));
        sleep(5);  // 休眠5s，等待老化线程触发崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 100"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10000, 10100, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

const char *g_bigLabelConfig2 = R"({
        "max_record_count":1000000,
        "is_support_reserved_memory":1,
        "isFastReadUncommitted":true,
        "defragmentation":true
    })";
TEST_F(StRecoveryRsmChVertex, TestRsmChLabelHorizontalScaleInCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_BEFORE,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_HALF,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_EXIT,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_BUCKET,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_STASH_BUCKET,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_END,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig2));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 0, 170, g_bigLabelName);
        sleep(5);  // 休眠5s，等待缩容线程触发崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig2));
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 830"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 170, 1000, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 1000, 2000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 1000, 2000, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelVerticalScaleInCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_VERTICAL_SCALE_IN_BEFORE,
        SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_HALF,
        SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_BUCKET,
        SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_STASH_BUCKET,
        SHM_CRASH_CH_VERTICAL_SCALE_IN_END,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig2));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 1000, 1005, g_bigLabelName));

        sleep(5);  // 休眠5s，等待缩容线程触发崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig2));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 10000, 10001, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1005"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 1000, 1005, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 10000, 11000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 10000, 11000, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRecoveryRsmChVertex, TestRsmChLabelMulVersionScaleInNormalCollapseRecovery)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table.gmjson", &g_bigLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table2.gmjson", &g_bigLabelSchemaV2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_big_table3.gmjson", &g_bigLabelSchemaV3);
    uint32_t testNum = 12;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_PART_SUCC,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_RECORD_COUNT,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_HALF,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_PAGE_CNT,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_PAGE_CHAIN,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_HALF,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_UPDATE_COUNT,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_DIR_BEFORE,
        // SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_BEFORE,       // 非正常流程，后续利用故障注入工具构造用例验证
        // SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_SRC_PAGE,     // 非正常流程，后续利用故障注入工具构造用例验证
        // SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_DST_PAGE,     // 非正常流程，后续利用故障注入工具构造用例验证
        // SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_UPDATE_COUNT, // 非正常流程，后续利用故障注入工具构造用例验证
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FINISHED,
        SHM_CRASH_CH_MUL_VERSION_SCALE_IN_END,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig2));

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 100, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 100, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 100, 200, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 100, 200, true, g_bigLabelName));

        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);

        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 200, 300, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 200, 300, true, g_bigLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;
        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 300"));

        DbShmCrashPointSet(testCases[i]);
        DeleteVertex(stmt, 0, 90, g_bigLabelName);
        DeleteVertex(stmt, 100, 190, g_bigLabelName);
        DeleteVertex(stmt, 200, 289, g_bigLabelName);
        sleep(61);  // 休眠61s，等待触发下次缩容任务
        DeleteVertex(stmt, 289, 290, g_bigLabelName);
        sleep(3);  // 休眠3s，等待缩容线程触发崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_bigLabelSchema, g_bigLabelConfig2));
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 1000, 1001, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV2, g_bigLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, InsertVertex(stmt, 1000, 1001, g_bigLabelName));
        UpgradeVertexLabel(stmt, g_bigLabelSchemaV3, g_bigLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        EXPECT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 30"));

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 90, 100, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 190, 200, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 290, 300, true, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 11000, 12000, g_bigLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 11000, 12000, true, g_bigLabelName));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_bigLabelSchema);
    FreeJsonBuf(&g_bigLabelSchemaV2);
    FreeJsonBuf(&g_bigLabelSchemaV3);
    RsmStGmcUtilSetHasUnInit(true);
}

#endif  // ENABLE_CRASHPOINT
