/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: testcase for rsm md
 * Author:
 * Create: 2024-05-13
 */

#include "tools_st_common.h"
#include "storage_st_common.h"
#include "rsm_st_common.h"
#include "gmc_tablespace.h"
#include "storage_st_util.h"
#include "db_crash_debug.h"

using namespace std;

class StRsmPageDfgmt : public RsmStGmcUtil, public testing::Test {
protected:
    // 本测试套不检查对账日志
    // 后台线程与查视图并发，有极小概率出现rsm session memctx需要通过对账回收的情况
    // 删子memctx有2个步骤：2.1.从top链表中摘下来，2.2.释放内存，步骤2.1和步骤2.2中存在gap，
    // 如果正好服务端退出，后续恢复不会再删子ctx，依赖top对账
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    void SetUp()
    {
        DoSetUp();
    }
    void TearDown()
    {
        DoTearDownCheckAndUninit();
        ShutDownDbServer();
        SaveLogAfterFailed();
    }
};

static const char *g_tspName = "rsm_tablespace";
static const char *g_tspName1 = "rsm_tablespace1";
static const char *g_labelConfig =
    R"({
        "max_record_count":1000000,
        "is_support_reserved_memory":1,
        "rsm_tablespace_name": "rsm_tablespace"
    })";
static const char *g_labelConfig1 =
    R"({
        "max_record_count":1000000,
        "is_support_reserved_memory":1,
        "rsm_tablespace_name": "rsm_tablespace1"
    })";
static const char *g_vertexLabelName = "vertex_label";
static const char *g_vertexLabelSchema =
    R"([{
        "type":"record",
        "name":"vertex_label",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"vertex_label",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

static const char *g_simpleLabelName = "simple_label";
static const char *g_simpleLabelSchema =
    R"([{
        "type":"record",
        "name":"simple_label",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"simple_label",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

static const char *g_kvTableName = "kv_table";

static void GetMemCompactTasksStatFieldResult(
    MemTaskTypeE filterTaskType, const char *fieldName, char *result, int resLen)
{
    char command[1024];
    (void)sprintf_s(command, sizeof(command),
        "gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -f TASK_TYPE=%d | grep -wE '%s' | awk -F '[:,]' '{print $2}'",
        (int)filterTaskType, fieldName);
    GetCommandResult(command, result, resLen);
}

void StRsmPageDfgmtWaitFinish(MemTaskTypeE filterTaskType, const char *key)
{
    char cmdOutput[64] = {0};
    uint32_t count = 0;
    do {
        sleep(1);
        GetMemCompactTasksStatFieldResult(filterTaskType, "SUB_TASK_STATUS", cmdOutput, 64);
        if ((++count) % 10 == 0) {
            cout << "TaskType:" << filterTaskType << " Wait:" << key << " But:" << cmdOutput << endl;
        }
    } while (strstr(cmdOutput, key) == NULL);
}

void StRsmPageDfgmtWaitTspCompressFinish(GmcStmtT *stmt, bool isEmpty = false)
{
    // 主动触发一次页搬迁判定
    GmcAlarmDataT alarmData;
    ASSERT_EQ(GMERR_OK, GmcGetAlarmData(stmt, GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, &alarmData));

    // 等待页搬迁完成
    const char *key = isEmpty ? "empty" : "compress_finish";
    StRsmPageDfgmtWaitFinish(MEM_COMPACT_RSM_PAGE, key);
}

static void GetTotalDevCnt(const char *key, char *result, int resLen)
{
    char command[1024];
    (void)sprintf_s(command, sizeof(command),
        "gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT | grep -wE %s | awk -F '[:,]' '{add+=$2} END {print add}'", key);
    GetCommandResult(command, result, resLen);
}

uint32_t StRsmPageDfgmtGetDevCnt(void)
{
    char cmdOutput[64] = {0};
    GetTotalDevCnt("CUR_DEVICE_CNT", cmdOutput, 64);
    return atoi(cmdOutput);
}

uint32_t StRsmPageDfgmtGetUnBindDevCnt(void)
{
    char cmdOutput[64] = {0};
    GetTotalDevCnt("UNBIND_DEVICE_CNT", cmdOutput, 64);
    return atoi(cmdOutput);
}

TEST_F(StRsmPageDfgmt, KvLabelCompress)
{
    // 减少内存大小，控制tableSpace上有两个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=2 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 50000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt, g_kvTableName, g_labelConfig));

    // 插满两个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, SetKv(stmt, 0, recordNum, g_kvTableName));

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    uint32_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcKvTableRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除前一半数据，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, RemoveKv(stmt, 0, newRecordNum, g_kvTableName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt);

    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    // 搬迁后插入数据可插入
    ASSERT_EQ(GMERR_OK, SetKv(stmt, 0, newRecordNum, g_kvTableName));

    // 搬迁后插入数据可找到
    ASSERT_EQ(GMERR_OK, GetKv(stmt, 0, count, g_kvTableName));

    // 搬迁后插入数据可删除
    ASSERT_EQ(GMERR_OK, RemoveKv(stmt, 0, count, g_kvTableName));

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, VertexLabelCompress)
{
    // 减少内存大小，控制tableSpace上有两个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=2 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 50000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满两个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除前一半数据，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt);

    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    // 搬迁后插入数据可插入
    ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    // 搬迁后插入数据可找到
    ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, count, true, g_simpleLabelName));

    // 搬迁后插入数据可删除
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, count, g_simpleLabelName));

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, ChLabelCompress)
{
    // 减少内存大小，控制tableSpace上有两个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=2 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 enableClusterHash=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 50000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满两个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    // 删光，触发页搬迁
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, count, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt, false);

    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    // 聚簇容器不会归还所有页，且散列，故无法重新插入等量数据，仅验证页地址缓存无问题
    // 搬迁后插入数据可插入
    ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1, g_simpleLabelName));

    // 搬迁后插入数据可找到
    ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1, true, g_simpleLabelName));

    // 搬迁后插入数据可删除
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, 1, g_simpleLabelName));

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressWhenStep)
{
    // 减少内存大小，控制tableSpace上有3个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=3 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 2,
        .maxSize = 3,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满3个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(3, StRsmPageDfgmtGetDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除一半数据(第一个dev和第二个dev一半数据)，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt);

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    // 搬迁后tablespace扩展按照2步长，实际1步长，不超过maxSize
    ASSERT_EQ(3, StRsmPageDfgmtGetDevCnt());

    // 搬迁后插入数据可找到
    ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, count, true, g_simpleLabelName));

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressButInitDev)
{
    // 减少内存大小，控制tableSpace上有2个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=2 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 2,
        .stepSize = 1,
        .maxSize = 2,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满2个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除第一个dev数据，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt);

    // 不会回收tablespace初始dev
    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    // 搬迁后插入数据可插入
    ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    // 搬迁后插入数据可找到
    ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, count, true, g_simpleLabelName));

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressEmptyWhenInitDev)
{
    // 减少内存大小，控制tableSpace上有2个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=1 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 1,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    StRsmPageDfgmtWaitTspCompressFinish(stmt, true);

    // 不会回收tablespace初始dev
    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressButOtherSpace)
{
    // 减少内存大小，控制tableSpace上有3个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=3 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满前2个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    GmcTspCfgT tspCfg1 = {
        .tablespaceName = g_tspName1,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 1,
    };
    // 占用第三个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg1));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_vertexLabelSchema, g_labelConfig1));

    // 插满第三个
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_vertexLabelName));

    ASSERT_EQ(3, StRsmPageDfgmtGetDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除前两个dev数据，触发页搬迁
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, count, g_simpleLabelName));

    // 不支持tablespace横向搬迁，不会回收dev
    StRsmPageDfgmtWaitTspCompressFinish(stmt, true);

    ASSERT_EQ(3, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressWhenMultiEmpty)
{
    // 减少内存大小，控制tableSpace上有4个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=4 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 2,
        .stepSize = 2,
        .maxSize = 4,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满4个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(4, StRsmPageDfgmtGetDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除后两个dev数据，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, newRecordNum, count, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt, true);

    // 可回收两个dev
    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressWhenOneFreeDev)
{
    // 减少内存大小，控制tableSpace上有1个device，有2个block，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=1 RsmKeyRange=0,2 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满2个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除第2个block上dev数据，触发缩容页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, newRecordNum, count, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt, true);

    // 可回收1个dev
    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressWhenUnBindDev)
{
    // 减少内存大小，控制block上有4个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=4 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    // 占用第一个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    GmcTspCfgT tspCfg1 = {
        .tablespaceName = g_tspName1,
        .initSize = 2,
        .stepSize = 1,
        .maxSize = 2,
    };
    // 占用第2、3个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg1));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满第一个tablespace的2个dev，即1、4个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(4, StRsmPageDfgmtGetDevCnt());

    // 使第2、3个dev为unBind
    GmcDropTablespace(stmt, g_tspName1);

    ASSERT_EQ(2, StRsmPageDfgmtGetUnBindDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除第1个dev数据，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, newRecordNum, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt);

    // 可回收3个dev
    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceCompressWhenFullNotBindDev)
{
    // 减少内存大小，控制block上有4个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=2 RsmKeyRange=0,2 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    uint32_t recordNum = 100000;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    // 占用第1个block的第1个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    GmcTspCfgT tspCfg1 = {
        .tablespaceName = g_tspName1,
        .initSize = 2,
        .stepSize = 1,
        .maxSize = 2,
    };
    // 占用第1个block的第1个dev和第2个block的第1个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg1));

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_simpleLabelSchema, g_labelConfig));

    // 插满第1个block的第1个dev和第2个block的第2个dev
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, InsertVertex(stmt, 0, recordNum, g_simpleLabelName));

    ASSERT_EQ(4, StRsmPageDfgmtGetDevCnt());

    // 释放第1个block的第2个dev和使第2个block的第1个dev为unBind
    GmcDropTablespace(stmt, g_tspName1);

    ASSERT_EQ(3, StRsmPageDfgmtGetDevCnt());
    ASSERT_EQ(1, StRsmPageDfgmtGetUnBindDevCnt());

    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_simpleLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    cout << "count:" << count << endl;
    ASSERT_LT(count, recordNum);

    // 删除第2个block的第2个dev数据，触发页搬迁
    uint64_t newRecordNum = count / 2;
    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, newRecordNum, count, g_simpleLabelName));

    StRsmPageDfgmtWaitTspCompressFinish(stmt, true);

    // 可回收2个dev
    ASSERT_EQ(1, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StRsmPageDfgmt, TablespaceDropWithNotBindDev)
{
    // 减少内存大小，控制block上有2个device，便于验证
    const char *serverConfig = "deviceSize=1 RsmBlockSize=2 RsmKeyRange=0,1 "
                               "memCompactEnable=1 enableReleaseDevice=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInitWithUnInit(false, serverConfig);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = g_tspName,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 1,
    };
    // 占用第1个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    GmcTspCfgT tspCfg1 = {
        .tablespaceName = g_tspName1,
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 1,
    };
    // 占用第2个dev
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg1));

    ASSERT_EQ(2, StRsmPageDfgmtGetDevCnt());

    // 使第1个dev为unBind
    GmcDropTablespace(stmt, g_tspName);

    ASSERT_EQ(1, StRsmPageDfgmtGetUnBindDevCnt());

    GmcDropTablespace(stmt, g_tspName1);

    // 可回收所有dev
    ASSERT_EQ(0, StRsmPageDfgmtGetDevCnt());

    DestroyConnectionAndStmt(conn, stmt);
}

static const char *g_labelConfig2 = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
static const char *g_labelName2 = "rsm_fixed_heap";
static const char *g_labelSchema2 =
    R"([{
        "type":"record",
        "name":"rsm_fixed_heap",
        "schema_version": 1,
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false, "default": 1},
                {"name":"F5", "type":"uint32", "nullable":false, "default": 1}
            ],
        "keys":
            [
                {"node":"rsm_fixed_heap", "name":"pk", "fields":["F0"], "index":{"type":"primary"},
                 "constraints":{ "unique":true}},
                {"node":"rsm_fixed_heap", "name":"lh", "fields":["F1"], "index":{"type":"localhash"},
                 "constraints":{ "unique":true}},
                {"node":"rsm_fixed_heap", "name":"hc", "fields":["F2"], "index":{"type":"hashcluster"},
                 "constraints":{ "unique":false}},
                {"node":"rsm_fixed_heap", "name":"lc", "fields":["F3"], "index":{"type":"local"},
                 "constraints":{ "unique":false}}
            ]
        }])";

static void StTriggerRsmPageDfgmt(uint32_t succTimes)
{
    // 触发告警
    GmcConnT *alarmConn;
    GmcStmtT *alarmStmt;
    CreateSyncConnectionAndStmt(&alarmConn, &alarmStmt);
    GmcAlarmDataT alarmData;
    memset_s(&alarmData, sizeof(GmcAlarmDataT), 0, sizeof(GmcAlarmDataT));
    Status ret = GmcGetAlarmData(alarmStmt, GMC_ALARM_RSM_TABLE_SPACE_USED_INFO, &alarmData);
    // 16001表明服务端回包前已经执行到crash point
    if (ret != GMERR_CONNECTION_RESET_BY_PEER) {
        EXPECT_EQ(alarmData.succTimes, succTimes);
        DestroyConnectionAndStmt(alarmConn, alarmStmt);
    }
}

const uint32_t g_maxFixedRowCountInOnePage = 429;
TEST_F(StRsmPageDfgmt, CompressBgTask)
{
    uint32_t deviceNum = 4;
    // 当前配置一个页能插入429行
    uint32_t recordNum = g_maxFixedRowCountInOnePage * 32 * deviceNum;  // 32个页，插满1个device(1M)
    // 客户端建链
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    RsmStartServerAndGmcInitWithCompress(false);
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 客户端建表、写数据
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
    ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, recordNum, g_labelName2));

    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    const char *filter = "KEY_ID=\'1\'";
    GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT", filter, cmdOutput1, 64);
    uint64_t curDevCnt = atoi(cmdOutput1);
    EXPECT_EQ(curDevCnt, deviceNum);
    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    StTriggerRsmPageDfgmt(recordNum / g_maxFixedRowCountInOnePage);

    ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, recordNum / 2, g_labelName2));

    StRsmPageDfgmtWaitTspCompressFinish(stmt);

    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    const char *filter1 = "TABLESPACE_NAME=\'reservedMemPublic\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "MOVE_SUB_BLOCK_CNT", filter1, cmdOutput1, 64);
    uint64_t moveSubBlockCnt = atoi(cmdOutput1);
    EXPECT_EQ(moveSubBlockCnt, 32 * deviceNum / 2);
    system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    DestroyConnectionAndStmt(conn, stmt);
}

#ifdef ENABLE_CRASHPOINT
TEST_F(StRsmPageDfgmt, TestRsmHeapVertexCompressMoveSubBlockCrash)
{
    uint32_t testNum = 19;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_BLOCK_MEMCPY_SUB_BLOCK_BEFORE,
        SHM_CRASH_BLOCK_MEMCPY_SUB_BLOCK_AFTER,
        SHM_CRASH_BLOCK_HANDLE_ENTRY_INFO_BEFORE,
        SHM_CRASH_BLOCK_INFO_GET_SUB_BLOCK_BEFORE,
        SHM_CRASH_BLOCK_INFO_GET_DEST_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_GET_SRC_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_LIST_FIRST_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_LIST_FIRST_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_DEV_ID,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_DEV_ID,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_BEGIN_POS,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_OFFSET,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_BEGIN_POS,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_OFFSET,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_FLAG,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_FLAG,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_DEV_FREE_CNT,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_DEV_FREE_CNT,
        SHM_CRASH_BLOCK_HANDLE_ENTRY_INFO_AFTER,
    };
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 当前配置一个页能插入429行
    uint32_t deviceNum = 4;
    uint32_t recordNum = g_maxFixedRowCountInOnePage * 32 * deviceNum;  // 32个页，插满1个device(1M)
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;

        CreateSyncConnectionAndStmt(&conn, &stmt);
        // 客户端建表、写数据
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
        ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, recordNum, g_labelName2));

        char cmdOutput1[64] = {0};
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        const char *filter1 = "KEY_ID=\'1\'";
        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT", filter1, cmdOutput1, 64);
        EXPECT_EQ(atoi(cmdOutput1), deviceNum);

        char cmdOutput2[64] = {0};
        (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
        const char *filter2 = "TABLESPACE_NAME=\'reservedMemPublic\'";
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "FREE_DEVICE_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 0);
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "MOVE_SUB_BLOCK_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 0);

        DbShmCrashPointSet(testCases[i]);
        StTriggerRsmPageDfgmt(recordNum / g_maxFixedRowCountInOnePage);

        ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, recordNum / 2, g_labelName2));

        StTriggerRsmPageDfgmt(recordNum / g_maxFixedRowCountInOnePage);

        sleep(1);
        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
        RsmWaitRecoveryFinish();

        // 因为是后台任务，可能未执行到crashPoint点就被kill掉了，根据重启后的实际情况进行来写预期
        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "FREE_RSM_SUB_BLOCK_CNT", filter1, cmdOutput1, 64);
        uint32_t freeRsmSubBlkCnt = atoi(cmdOutput1);

        StTriggerRsmPageDfgmt(0);

        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT", filter1, cmdOutput1, 64);
        EXPECT_EQ(atoi(cmdOutput1), 2);

        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "FREE_DEVICE_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 2);
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "MOVE_SUB_BLOCK_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 64 - freeRsmSubBlkCnt);

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, recordNum / 2, false, g_labelName2));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_labelName2));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_labelName2));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit();
}

TEST_F(StRsmPageDfgmt, TestRsmHeapVertexCompressFreeDeviceCrash)
{
    uint32_t testNum = 14;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_SPACE_FREE_DEV_BEFORE,
        SHM_CRASH_SPACE_FREE_DEV_FINISHED,
        SHM_CRASH_SPACE_FREE_DEV_UPDATE_DEV_SIZE,
        SHM_CRASH_SPACE_FREE_DEV_UPDATE_SUB_BLOCK_CNT,
        SHM_CRASH_SPACE_FREE_DEV_UPDATE_SUB_BLOCK_MOVE_CNT,
        SHM_CRASH_SPACE_FREE_DEV_AFTER,
        SHM_CRASH_BLOCK_FREE_DEV_BEFORE,
        SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_SUB_BLK_INFO,
        SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_SUB_BLK_STATUS,
        // SHM_CRASH_BLOCK_FREE_DEV_CHANGE_SUB_BLK_STATUS, // 没有走到
        SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_BLOCK_FREE_ID,
        SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_DEV_FIRST_ID,
        SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_BLOCK_DEV_CNT,
        SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_DEV_STATUS,
        // SHM_CRASH_BLOCK_FREE_DEV_CHANGE_BLOCK_DEV_CNT, // 没有走到
        SHM_CRASH_BLOCK_FREE_DEV_AFTER,
    };
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 当前配置一个页能插入429行
    uint32_t deviceNum = 4;
    uint32_t recordNum = g_maxFixedRowCountInOnePage * 32 * deviceNum;  // 32个页，插满1个device(1M)
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;

        CreateSyncConnectionAndStmt(&conn, &stmt);
        // 客户端建表、写数据
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
        ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, recordNum, g_labelName2));

        char cmdOutput1[64] = {0};
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        const char *filter1 = "KEY_ID=\'1\'";
        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT", filter1, cmdOutput1, 64);
        ASSERT_EQ(atoi(cmdOutput1), deviceNum);

        char cmdOutput2[64] = {0};
        (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
        const char *filter2 = "TABLESPACE_NAME=\'reservedMemPublic\'";
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "FREE_DEVICE_CNT", filter2, cmdOutput2, 64);
        ASSERT_EQ(atoi(cmdOutput2), 0);
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "MOVE_SUB_BLOCK_CNT", filter2, cmdOutput2, 64);
        ASSERT_EQ(atoi(cmdOutput2), 0);

        StTriggerRsmPageDfgmt(recordNum / g_maxFixedRowCountInOnePage);

        ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, recordNum / 2, g_labelName2));

        DbShmCrashPointSet(testCases[i]);

        StTriggerRsmPageDfgmt(recordNum / g_maxFixedRowCountInOnePage);

        sleep(5);
        system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT");
        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
        RsmWaitRecoveryFinish();
        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");

        StTriggerRsmPageDfgmt(0);

        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");

        ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, recordNum / 2, false, g_labelName2));
        ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, recordNum / 2, recordNum, true, g_labelName2));
        ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_labelName2));
        ASSERT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_labelName2));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit();
}

TEST_F(StRsmPageDfgmt, TestRsmChVertexCompressMoveSubBlockCrash)
{
    uint32_t testNum = 19;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_BLOCK_MEMCPY_SUB_BLOCK_BEFORE,
        SHM_CRASH_BLOCK_MEMCPY_SUB_BLOCK_AFTER,
        SHM_CRASH_BLOCK_HANDLE_ENTRY_INFO_BEFORE,
        SHM_CRASH_BLOCK_INFO_GET_SUB_BLOCK_BEFORE,
        SHM_CRASH_BLOCK_INFO_GET_DEST_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_GET_SRC_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_LIST_FIRST_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_LIST_FIRST_SUB_BLOCK,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_DEV_ID,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_DEV_ID,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_BEGIN_POS,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_OFFSET,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_BEGIN_POS,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_OFFSET,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_FLAG,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_FLAG,
        SHM_CRASH_BLOCK_INFO_CHANGE_DEST_DEV_FREE_CNT,
        SHM_CRASH_BLOCK_INFO_CHANGE_SRC_DEV_FREE_CNT,
        SHM_CRASH_BLOCK_HANDLE_ENTRY_INFO_AFTER,
    };
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    uint32_t recordNum = 29215;  // 32个页，插满1个device(1M)
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;

        CreateSyncConnectionAndStmt(&conn, &stmt);
        // 客户端建表、写数据
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
        ASSERT_EQ(GMERR_OK, InsertVertex(stmt, 0, recordNum, g_labelName2));

        char cmdOutput1[64] = {0};
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        const char *filter1 = "KEY_ID=\'1\'";
        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT", filter1, cmdOutput1, 64);
        EXPECT_EQ(atoi(cmdOutput1), 4);

        char cmdOutput2[64] = {0};
        (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
        const char *filter2 = "TABLESPACE_NAME=\'reservedMemPublic\'";
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "FREE_DEVICE_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 0);
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "MOVE_SUB_BLOCK_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 0);

        StTriggerRsmPageDfgmt(128);

        ASSERT_EQ(GMERR_OK, DeleteVertex(stmt, 0, recordNum - 1, g_labelName2));

        DbShmCrashPointSet(testCases[i]);

        StTriggerRsmPageDfgmt(128);

        sleep(1);
        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_labelSchema2, g_labelConfig2));
        RsmWaitRecoveryFinish();

        StTriggerRsmPageDfgmt(0);
        sleep(1);

        system("gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT -f KEY_ID=\'1\'");
        GetViewFieldResultFilter("V\\$STORAGE_RSM_BLOCK_STAT", "CUR_DEVICE_CNT", filter1, cmdOutput1, 64);
        EXPECT_EQ(atoi(cmdOutput1), 1);

        system("gmsysview -q V\\$CATA_TABLESPACE_INFO -f TABLESPACE_NAME=\'reservedMemPublic\'");
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "FREE_DEVICE_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), 3);
        GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "MOVE_SUB_BLOCK_CNT", filter2, cmdOutput2, 64);
        EXPECT_EQ(atoi(cmdOutput2), i == 0 || i == 1 ? 16 : 15);

        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, recordNum - 1, false, g_labelName2));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, recordNum - 1, recordNum, true, g_labelName2));
        EXPECT_EQ(GMERR_OK, InsertVertex(stmt, 0, 1000, g_labelName2));
        EXPECT_EQ(GMERR_OK, LookUpVertex(stmt, 0, 1000, true, g_labelName2));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    RsmStGmcUtilSetHasUnInit();
}

#endif
