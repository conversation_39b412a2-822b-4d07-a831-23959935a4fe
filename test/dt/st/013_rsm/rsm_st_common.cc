/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: rsm_st_common.cc
 * Description: RSM test case common component
 * Author:
 * Create:
 */
#include "rsm_st_common.h"

GmcConnOptionsT *g_connOptions = NULL;
static pthread_t g_pubSubResponseEpollThreadId;
static int g_pubSubResponseEpollFd;

/*=========================================基本解析功能  Start========================================================*/
long ReadJsonFile(const char *path, char **buf)
{
    FILE *fp = NULL;
    fp = fopen(path, "rb");
    if (NULL == fp) {
        printf("[ReadJsonFile] open file:%s fail.\n", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        printf("[ReadJsonFile] fseek file:%s to end failed.\n", path);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        printf("[ReadJsonFile] read file size:%ld failed.\n", size);
        fclose(fp);
        return -1;
    }
    char *pBuffer = (char *)malloc(size + 1);
    if (pBuffer == NULL) {
        printf("[ReadJsonFile] malloc memory:%ld for file:%s failed.\n", size + 1, path);
        fclose(fp);
        return -1;
    }
    rewind(fp);
    long readSize = fread(pBuffer, 1, size, fp);
    if (readSize != size) {
        printf("[ReadJsonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.\n", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    pBuffer[size] = 0;
    rc = fclose(fp);
    if (rc != 0) {
        printf("file close error.\n");
        free(pBuffer);
        return -1;
    }
    *buf = pBuffer;
    return size;
}

void FreeJsonBuf(char **buf)
{
    if (*buf != NULL) {
        free(*buf);
        *buf = NULL;
    }
}

void GetLabelNameFromJson(char *jsonBuf, char **labelNameBuf)
{
    char *pcBegin = strstr(jsonBuf, "\"name");
    char *pcEnd =
        strstr(jsonBuf + (pcBegin - jsonBuf), ",") - strlen("\"");  // 获取第一个逗号之后的部分，去除名字后面的引号
    pcBegin = strstr(pcBegin, ":");                                 // 获取":"及之后的部分
    pcBegin = strstr(pcBegin, "\"") + strlen("\"");  // 获取第一个引号之后的部分，去除名字前面的引号
    long size = pcEnd - pcBegin;
    char *pBuffer = (char *)malloc(size + 1);
    if (pBuffer == NULL) {
        return;
    }
    (void)memcpy_s(pBuffer, size, pcBegin, size);
    pBuffer[size] = 0;
    *labelNameBuf = pBuffer;
    return;
}

/*=========================================基本解析功能 End========================================================*/

/*=========================================WarmReboot功能
 * Start========================================================*/
void RsmStartServerAndGmcInit(bool warmReboot, const char *config)
{
    g_clientServerWarmReboot = warmReboot;
    const char *configItem = config == NULL ? R"(maxSeMem=512  isUseRsm=1)" : config;
    StartDbServerWithConfig(configItem);
    EXPECT_EQ(GMERR_OK, GmcInit());
    g_clientServerWarmReboot = false;
}

void RsmStartServerAndGmcInitWithClusteredHash(bool warmReboot)
{
    g_clientServerWarmReboot = warmReboot;
    StartDbServerWithConfig("\"maxSeMem=512\" \"defaultTablespaceMaxSize=128\"  \"isUseRsm=1\" "
                            "\"enableClusterHash=1\"");
    EXPECT_EQ(GMERR_OK, GmcInit());
    g_clientServerWarmReboot = false;
}

void RsmStartServerAndGmcInitWithClusteredHashAndCompress(bool warmReboot)
{
    g_clientServerWarmReboot = warmReboot;
    StartDbServerWithConfig(
        "\"maxSeMem=512\" \"defaultTablespaceMaxSize=4\"  \"isUseRsm=1\" "
        "\"enableClusterHash=1\" \"deviceSize=1\" \"enableReleaseDevice=1\" \"memCompactEnable=1\"");
    EXPECT_EQ(GMERR_OK, GmcInit());
    g_clientServerWarmReboot = false;
}

void RsmStartServerAndGmcInitWithCompress(bool warmReboot)
{
    g_clientServerWarmReboot = warmReboot;
    StartDbServerWithConfig("\"maxSeMem=512\" \"defaultTablespaceMaxSize=4\"  \"isUseRsm=1\" "
                            "\"deviceSize=1\" \"enableReleaseDevice=1\" \"memCompactEnable=1\" "
                            "\"minFragmentationMemThreshold=1\" \"minFragmentationRateThreshold=30\"");
    EXPECT_EQ(GMERR_OK, GmcInit());
    g_clientServerWarmReboot = false;
}

void RsmStopServerAndGmcUnInit(void)
{
    EXPECT_EQ(GMERR_OK, GmcUnInit());
    ShutDownDbServer();
}

/*=========================================WarmReboot功能 End========================================================*/

/*=========================================订阅相关功能 Start========================================================*/
static bool IsEulerEnv(void)
{
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    return true;
#else
    return false;
#endif
}

static int EpollReg(int fd, GmcEpollCtlTypeE type)
{
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = EPOLLIN;
            return epoll_ctl(g_pubSubResponseEpollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(g_pubSubResponseEpollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

static int EpollRegWithUserData(int fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    struct epoll_event event;
    event.data.fd = fd;
    event.events = events;
    int *epollFd = (int *)(userData);
    switch (type) {
        case GMC_EPOLL_ADD: {
            return epoll_ctl(*epollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_MOD: {
            return epoll_ctl(*epollFd, EPOLL_CTL_MOD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(*epollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

static void SetEpollRegFunc(GmcConnOptionsT *connOptions)
{
    Status ret;
    if (IsEulerEnv()) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(
            connOptions, (GmcEpollRegWithUserDataT)EpollRegWithUserData, &g_pubSubResponseEpollFd);
    } else {
        ret = GmcConnOptionsSetEpollRegFunc(connOptions, (GmcEpollRegT)EpollReg);
    }
    EXPECT_EQ(GMERR_OK, ret);
}

void CreateSubPubConnect(GmcConnT **subConn)
{
    constexpr auto &subConnName = "subConnName4";
    GmcConnOptionsT *connOptions = NULL;
    uint32_t ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    CreateAndStartEpoll(&g_pubSubResponseEpollThreadId, &g_pubSubResponseEpollFd);
    GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    GmcConnOptionsSetConnName(connOptions, subConnName);
    SetEpollRegFunc(connOptions);

    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOptions, subConn);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
}

void FreeSubPubConnect(GmcConnT **subConn)
{
    StopAndDestroyEpoll(g_pubSubResponseEpollThreadId, &g_pubSubResponseEpollFd);
    Status ret = GmcDisconnect(*subConn);
    EXPECT_EQ(GMERR_OK, ret);
    *subConn = NULL;
}

void CreateSubScription(
    GmcStmtT *stmt, SubVerifyT *subVerify, GmcConnT *subConn, const char *subsName, char *configJson)
{
    GmcSubConfigT config;
    DB_ASSERT(subsName != NULL);
    DB_ASSERT(configJson != NULL);
    config.subsName = subsName;
    config.configJson = configJson;
    SubsCommInitAndMallocSubVerify(subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(subVerify);
    };

    for (uint32_t i = 0; i < subVerify->verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify->verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify->verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }

    Status ret = GmcSubscribe(stmt, &config, subConn, SubsCallbackComm, subVerify);
    EXPECT_EQ(GMERR_OK, ret);
}

void CreateSubScription1(GmcStmtT *stmt, GmcConnT *subConn, const char *subsName, char *configJson)
{
    GmcSubConfigT config;
    DB_ASSERT(subsName != NULL);
    DB_ASSERT(configJson != NULL);
    config.subsName = subsName;
    config.configJson = configJson;
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            bool eof1 = false;
            EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, GmcFetch(stmt, &eof1));
            return;
        }
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t f0V;
            bool isNull;

            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
    };

    uint32_t received = 0;
    Status ret = GmcSubscribe(stmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
}
void DestroySubScription(GmcStmtT *stmt, const char *subsName)
{
    Status ret = GmcUnSubscribe(stmt, subsName);
    EXPECT_EQ(GMERR_OK, ret);
}
/*=========================================订阅相关功能 End========================================================*/

/*=========================================后台truncate相关功能 Start================================================*/

// crashpoint用例也调用了此方法
void CreateAsyncConnect(GmcConnT **asyncConn, GmcStmtT **stmt)
{
    constexpr auto &asyncConnName = "async01";
    GmcConnOptionsT *connOptions = NULL;
    uint32_t ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    CreateAndStartEpoll(&g_pubSubResponseEpollThreadId, &g_pubSubResponseEpollFd);
    GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    GmcConnOptionsSetConnName(connOptions, asyncConnName);
    SetEpollRegFunc(connOptions);

    ret = GmcConnect(GMC_CONN_TYPE_ASYNC, connOptions, asyncConn);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(*asyncConn, stmt));
}

void FreeAsyncConnect(GmcConnT **asyncConn, GmcStmtT **stmt)
{
    StopAndDestroyEpoll(g_pubSubResponseEpollThreadId, &g_pubSubResponseEpollFd);
    GmcFreeStmt(*stmt);
    Status ret = GmcDisconnect(*asyncConn);
    EXPECT_EQ(GMERR_OK, ret);
    *asyncConn = NULL;
    *stmt = NULL;
}

/*=========================================后台truncate相关功能 End================================================*/

Status InsertVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i * 2;
        uint32_t f2 = i * 3;
        uint32_t f3 = i * 4;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status ReplaceVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, bool isLayout)
{
    uint32_t layoutOffset = isLayout ? 50000 : 0;
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i * (layoutOffset + 5);
        uint32_t f2 = i * (layoutOffset + 6);
        uint32_t f3 = i * (layoutOffset + 7);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status DeleteVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status LookUpVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint32_t f0Res;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Res, sizeof(f0Res), &isNull);
        if (!isFind) {
            EXPECT_EQ(ret, GMERR_NO_DATA);
            continue;
        }
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f0Res, i);

        uint32_t f1Res;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Res, sizeof(f1Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f1Res, i * 2);
        } else {
            EXPECT_NE(f1Res, i * 2);
        }

        uint32_t f2Res;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Res, sizeof(f2Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f2Res, i * 3);
        } else {
            EXPECT_NE(f2Res, i * 3);
        }

        uint32_t f3Res;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Res, sizeof(f3Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f3Res, i * 4);
        } else {
            EXPECT_NE(f3Res, i * 4);
        }
    }
    return GMERR_OK;
}

Status UpdateVertex(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i * 8;
        uint32_t f2 = i * 9;
        uint32_t f3 = i * 10;

        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));

        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status LookUpVertexAfterUpdate(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint32_t f0Res;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Res, sizeof(f0Res), &isNull);

        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_NE(isNull, true);
        EXPECT_EQ(f0Res, i);

        uint32_t f1Res;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Res, sizeof(f1Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f1Res, i * 8);
        } else {
            EXPECT_NE(f1Res, i * 8);
        }

        uint32_t f2Res;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Res, sizeof(f2Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f2Res, i * 9);
        } else {
            EXPECT_NE(f2Res, i * 9);
        }

        uint32_t f3Res;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Res, sizeof(f3Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f3Res, i * 10);
        } else {
            EXPECT_NE(f3Res, i * 10);
        }
    }
    return GMERR_OK;
}

Status InsertVertexWithVersion(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, uint32_t version)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, version, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i * 2;
        uint32_t f2 = i * 3;
        uint32_t f3 = i * 4;
        uint32_t f4 = i * 5;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status LookUpVertexWithVersion(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName, uint32_t version)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, version, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint32_t f0Res;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Res, sizeof(f0Res), &isNull);
        if (!isFind) {
            EXPECT_EQ(ret, GMERR_NO_DATA);
            continue;
        }
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f0Res, i);

        uint32_t f1Res;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Res, sizeof(f1Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f1Res, i * 2);
        } else {
            EXPECT_NE(f1Res, i * 2);
        }

        uint32_t f2Res;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Res, sizeof(f2Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f2Res, i * 3);
        } else {
            EXPECT_NE(f2Res, i * 3);
        }

        uint32_t f3Res;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Res, sizeof(f3Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f3Res, i * 4);
        } else {
            EXPECT_NE(f3Res, i * 4);
        }

        uint32_t f4Res;
        ret = GmcGetVertexPropertyByName(stmt, "F4", &f4Res, sizeof(f4Res), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (isFind) {
            EXPECT_NE(isNull, isFind);
            EXPECT_EQ(f4Res, i * 5);
        } else {
            EXPECT_NE(f4Res, i * 5);
        }
    }
    return GMERR_OK;
}

void UpgradeVertexLabel(GmcStmtT *stmt, const char *labelJson, const char *labelName, Status checkRet = GMERR_OK)
{
    Status ret = GmcAlterVertexLabelWithName(stmt, labelJson, true, labelName);
    ASSERT_EQ(checkRet, ret);
}

void DowngradeVertexLabel(GmcStmtT *stmt, const char *labelName, uint32_t schemaVersion, Status checkRet = GMERR_OK)
{
    Status ret = GmcDegradeVertexLabel(stmt, labelName, schemaVersion);
    ASSERT_EQ(checkRet, ret);
}

Status SetKv(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        char key[32] = {0};
        sprintf_s(key, sizeof(key), "key_%" PRIu32, i);
        uint32_t value = i;
        ret = GmcKvSet(stmt, key, sizeof(key), &value, sizeof(value));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status GetKv(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        char key[32] = {0};
        sprintf_s(key, sizeof(key), "key_%" PRIu32, i);
        uint32_t value = 0;
        uint32_t valueLen = sizeof(value);
        ret = GmcKvGet(stmt, key, sizeof(key), &value, &valueLen);
        if (ret != GMERR_OK) {
            printf("%s\n", key);
            return ret;
        }
        if (value != i) {
            EXPECT_EQ(i, value);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

Status RemoveKv(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    Status ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        char key[32] = {0};
        sprintf_s(key, sizeof(key), "key_%" PRIu32, i);
        ret = GmcKvRemove(stmt, key, sizeof(key));
        if (ret != GMERR_OK) {
            printf("%s\n", key);
            return ret;
        }
    }
    return GMERR_OK;
}

void RsmWaitRecoveryFinish(void)
{
    char cmdOutput[64] = {0};
    uint32_t count = 0;
    bool isRecoveryFinish = false;
    do {
        sleep(1);
        const char *command =
            "gmsysview -q V\\$QRY_RECOVERY_TASK_STAT | grep -wE 'IS_RECOVERY_FINISHED' | awk -F '[:,]' '{print $2}'";
        GetCommandResult(command, cmdOutput, sizeof(cmdOutput));
        isRecoveryFinish = atoi(cmdOutput);
        if ((++count) % 10 == 0) {
            cout << "CmdOutput:" << cmdOutput << endl;
        }
        if (!isRecoveryFinish && count > 300) {
            // 用例如果达到3级挂死时间还没恢复完成，打屏后退出，请检查是否为用例或代码问题
            cout << "Rsm wait recovery finish timeout." << endl;
            ASSERT_EQ(true, isRecoveryFinish);
            break;
        }
    } while (!isRecoveryFinish);
}
