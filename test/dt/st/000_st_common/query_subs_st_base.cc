#include <sys/epoll.h>
#include "query_subs_st_base.h"

#ifdef __cplusplus
extern "C" {
#endif

void SubsCommInitSubVerifyRow(
    SubVerifyRowT *verifyRow, uint32_t expectVerifyRowsNum, uint8_t fetchRowsTypeNum, char *rowDataBuf)
{
    verifyRow->expectVerifyRowsNum = expectVerifyRowsNum;
    verifyRow->fetchRowsTypeNum = fetchRowsTypeNum;
    verifyRow->rowDataBuf = rowDataBuf;
}

void SubsCommInitSubVerifyHeader(
    SubVerifyHeaderT *verifyHeader, SubRowStatisT *subRowStatis, uint16_t labelCount, const char *subscriptionName)
{
    *subRowStatis = {0};
    verifyHeader->headerStatis = (void *)subRowStatis;
    verifyHeader->expectMsgInfo.labelCount = labelCount;
    verifyHeader->expectMsgInfo.subscriptionName = subscriptionName;
}

void SubsCallbackHeaderStatisComm(const GmcSubMsgInfoT *info, void *headerStatis)
{
    SubRowStatisT *subsStatis = (SubRowStatisT *)headerStatis;
    __atomic_fetch_add(&subsStatis->typeNum[info->eventType], 1, __ATOMIC_SEQ_CST);
    subsStatis->allNum++;
}

void SubsCallbackHeaderComm(const GmcSubMsgInfoT *info, const GmcSubMsgInfoT *expectMsgInfo)
{
    EXPECT_EQ(expectMsgInfo->labelCount, info->labelCount);
    EXPECT_STREQ(expectMsgInfo->subscriptionName, info->subscriptionName);
}

void SubsCallbackDataComm(RowDataT *rowGet, SubVerifyRowDataT *rowExpect)
{
    EXPECT_EQ(rowGet->len, rowExpect->expecValue.len);
    EXPECT_EQ(rowGet->isNull, rowExpect->expecValue.isNull);
    if (rowExpect->expecValue.isInt) {
        EXPECT_EQ(rowExpect->expecValue.valInt, *(int32_t *)rowGet->val);
        printf("int:%d\n", *(int32_t *)rowGet->val);
    } else {
        if (memcmp(rowGet->val, rowExpect->expecValue.val, rowGet->len) == 0) {
            EXPECT_EQ(0, 0);
        } else {
            EXPECT_EQ(0, 1);
        }
        printf("str:%s\n", rowGet->val);
    }
}

void SubsCommInitAndMallocSubVerify(SubVerifyT *subVerify, const char *subscriptionName, uint32_t subsVerifyTypeNum,
    uint32_t subsVerifyRowsNum, uint32_t maxBufLen)
{
    subVerify->needSkipFetch = false;
    void *buf =
        malloc(sizeof(SubRowStatisT) + maxBufLen + subsVerifyRowsNum * subsVerifyTypeNum * sizeof(SubVerifyRowDataT));
    SubRowStatisT *subRowStatis = (SubRowStatisT *)buf;
    buf = (char *)buf + sizeof(SubRowStatisT);
    char *rowDataBuf = (char *)buf;
    buf = (char *)buf + maxBufLen;
    SubsCommInitSubVerifyHeader(&subVerify->verifyHeader, subRowStatis, 1, subscriptionName);
    SubsCommInitSubVerifyRow(&subVerify->verifyRow, subsVerifyRowsNum, subsVerifyTypeNum, rowDataBuf);
    subVerify->verifyHeader.headerStatisFunc = SubsCallbackHeaderStatisComm;
    subVerify->verifyHeader.verifyHeaderFunc = SubsCallbackHeaderComm;

    for (uint32_t i = 0; i < subVerify->verifyRow.fetchRowsTypeNum; i++) {
        subVerify->verifyRow.expecRows[i] = (SubVerifyRowDataT *)buf;
        buf = (char *)buf + subVerify->verifyRow.expectVerifyRowsNum * sizeof(SubVerifyRowDataT);
    }
}

void SubsCommDestroySubVerify(SubVerifyT *subVerify)
{
    free(subVerify->verifyHeader.headerStatis);
}

void SubsCallbackComm(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userdata)
{
    SubVerifyT *subVerify = reinterpret_cast<SubVerifyT *>(userdata);
    if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_END || info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN ||
        info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF || subVerify->needSkipFetch) {
        if (subVerify->verifyHeader.verifyHeaderFunc != NULL) {
            subVerify->verifyHeader.verifyHeaderFunc(info, &subVerify->verifyHeader.expectMsgInfo);
        }
        if (subVerify->verifyHeader.headerStatisFunc != NULL) {
            subVerify->verifyHeader.headerStatisFunc(info, subVerify->verifyHeader.headerStatis);
        }
        return;
    }
    bool eof = false;
    bool executed = false;
    for (;;) {
        Status ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        if (!executed) {
            executed = true;
            if (subVerify->verifyHeader.verifyHeaderFunc != NULL) {
                subVerify->verifyHeader.verifyHeaderFunc(info, &subVerify->verifyHeader.expectMsgInfo);
            }
            if (subVerify->verifyHeader.headerStatisFunc != NULL) {
                subVerify->verifyHeader.headerStatisFunc(info, subVerify->verifyHeader.headerStatis);
            }
        }
        subVerify->receivedRows++;
        for (uint8_t i = 0; i < subVerify->verifyRow.fetchRowsTypeNum; i++) {
            if (__atomic_load_n(&subVerify->verifyRow.fetchRowsIndex, __ATOMIC_SEQ_CST) >=
                subVerify->verifyRow.expectVerifyRowsNum) {
                break;
            }
            SubVerifyRowDataT *expecRowData =
                &subVerify->verifyRow
                     .expecRows[i][__atomic_load_n(&subVerify->verifyRow.fetchRowsIndex, __ATOMIC_SEQ_CST)];
            RowDataT rowValue = {0};
            rowValue.val = subVerify->verifyRow.rowDataBuf;
            if (expecRowData == NULL) {
                return;
            }

            GmcSubFetchModeE mode = expecRowData->fetchMode;
            if ((info->eventType == GMC_SUB_EVENT_DELETE) || (info->eventType == GMC_SUB_EVENT_AGED)) {
                mode = GMC_SUB_FETCH_OLD;
            }
            Status ret = 0;
            uint32_t msgType = 0;
            ret = GmcSubGetMsgType(subStmt, &msgType);
            EXPECT_EQ(GMERR_OK, ret);
            if (msgType & GMC_SUB_MSG_KEY_DATA) {
                const void *outKey = NULL;
                uint32_t outKeyLen;
                ret = GmcSubGetKey(subStmt, &outKey, &outKeyLen);
                EXPECT_EQ(GMERR_OK, ret);
            }
            ret = GmcSubSetFetchMode(subStmt, mode);
            EXPECT_EQ(GMERR_OK, ret);
            if (expecRowData->getPropType == ST_GET_PROPERY_TYPE_NAME) {
                ret = GmcGetVertexPropertySizeByName(subStmt, expecRowData->propertyName, &rowValue.len);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(
                    subStmt, expecRowData->propertyName, rowValue.val, rowValue.len, &rowValue.isNull);
                EXPECT_EQ(GMERR_OK, ret);
            } else if (expecRowData->getPropType == ST_GET_PROPERY_TYPE_ID) {
                ret = GmcGetVertexPropertySizeById(subStmt, expecRowData->properyId, &rowValue.len);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyById(
                    subStmt, expecRowData->properyId, rowValue.val, rowValue.len, &rowValue.isNull);
                EXPECT_EQ(GMERR_OK, ret);
            } else if (expecRowData->getPropType == ST_GET_PROPERY_TYPE_TREE) {
                GmcNodeT *node;
                ret = GmcGetChildNode(subStmt, expecRowData->treeProp.treePropertyName[0], &node);
                EXPECT_EQ(GMERR_OK, ret);
                for (uint8_t j = 1; j < expecRowData->treeProp.treePropertyLevel - 1; j++) {
                    GmcNodeT *nodeTmp;
                    ret = GmcNodeGetChild(node, expecRowData->treeProp.treePropertyName[j], &nodeTmp);
                    EXPECT_EQ(GMERR_OK, ret);
                    node = nodeTmp;
                }
                ret = GmcNodeGetPropertySizeByName(node,
                    expecRowData->treeProp.treePropertyName[expecRowData->treeProp.treePropertyLevel - 1],
                    &rowValue.len);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(node,
                    expecRowData->treeProp.treePropertyName[expecRowData->treeProp.treePropertyLevel - 1], rowValue.val,
                    rowValue.len, &rowValue.isNull);
                EXPECT_EQ(GMERR_OK, ret);
            }
            // 此处不进行数据的校验工作，对于不同的容器全量推送的顺序不一致
        }
        __atomic_fetch_add(&subVerify->verifyRow.fetchRowsIndex, 1, __ATOMIC_SEQ_CST);
    }
}

void SubsCallbackSimple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userdata)
{
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *(uint32_t *)userdata;

    for (bool eof;;) {
        auto ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ++received;
        uint32_t msgType;
        ret = GmcSubGetMsgType(subStmt, &msgType);
        EXPECT_EQ(GMERR_OK, ret);
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT:
            case GMC_SUB_EVENT_MODIFY:
            case GMC_SUB_EVENT_INITIAL_LOAD:
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);
                break;
            case GMC_SUB_EVENT_DELETE:
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);
                break;
            default:
                break;
        }
    }
}

void SubsCallbackDeadlock(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userdata)
{
    SubDeadlockT *subDeadlock = reinterpret_cast<SubDeadlockT *>(userdata);
    while (subDeadlock->needRecvWait) {
        DbSleep(300);
    }
    subDeadlock->typeNum[info->eventType]++;
    subDeadlock->allNum++;
}

int SubEpollRegWithUserData(int fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    struct epoll_event event;
    event.data.fd = fd;
    event.events = events;
    int epollFd = *(volatile int *)(userData);
    switch (type) {
        case GMC_EPOLL_ADD: {
            return epoll_ctl(epollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_MOD: {
            return epoll_ctl(epollFd, EPOLL_CTL_MOD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(epollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

#ifdef __cplusplus
}
#endif
