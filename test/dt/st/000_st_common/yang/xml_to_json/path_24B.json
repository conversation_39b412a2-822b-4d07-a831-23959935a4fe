[{"path": "/ietf-alarms/alarms/alarm-profile", "name": "al:alarm-profile.1"}, {"path": "/ietf-alarms/alarms/alarm-profile/alarm-severity-assignment-profile/severity-level", "name": "al:severity-level.2"}, {"path": "/ietf-alarms/alarms/control/alarm-shelving/shelf", "name": "al:shelf.1"}, {"path": "/ietf-alarms/alarms/control/alarm-shelving/shelf/alarm-type", "name": "al:alarm-type.1"}, {"path": "/ietf-alarms/alarms/control/alarm-shelving/shelf/resource", "name": "al:resource.1"}, {"path": "/ietf-alarms/alarms/control/x733-mapping", "name": "x733:x733-mapping.1"}, {"path": "/an-protection-group/protection-groups/protection-group", "name": "an-pg:protection-group.1"}, {"path": "/an-protection-group/protection-groups/protection-group/member", "name": "an-pg:member.1"}, {"path": "/an-protection-group/protection-groups/dual-parenting-peer-node", "name": "an-pon-pg:dual-parenting-peer-node.1"}, {"path": "/an-protection-group/protection-groups/dual-parenting-sync/local-node-ports", "name": "an-pon-pg:local-node-ports.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile", "name": "bbf-fpprof:frame-processing-profile.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/egress-rewrite/push-tag", "name": "bbf-fpprof:push-tag.2"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/exclude-criteria/ethernet-frame-type", "name": "bbf-fpprof:ethernet-frame-type.2"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/exclude-criteria/frame-destination-filter/destination-ipv4-address", "name": "bbf-fpprof:destination-ipv4-address.2"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/exclude-criteria/frame-destination-filter/destination-ipv6-address", "name": "bbf-fpprof:destination-ipv6-address.2"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/exclude-criteria/frame-destination-filter/destination-mac-address", "name": "bbf-fpprof:destination-mac-address.2"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/exclude-criteria/protocol", "name": "bbf-fpprof:protocol.2"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/ingress-rewrite/copy-from-tags-to-marking-list", "name": "bbf-fpprof:copy-from-tags-to-marking-list.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/ingress-rewrite/push-tag", "name": "bbf-fpprof:push-tag.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/match-criteria/ethernet-frame-type", "name": "bbf-fpprof:ethernet-frame-type.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/match-criteria/frame-destination-filter/destination-ipv4-address", "name": "bbf-fpprof:destination-ipv4-address.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/match-criteria/frame-destination-filter/destination-ipv6-address", "name": "bbf-fpprof:destination-ipv6-address.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/match-criteria/frame-destination-filter/destination-mac-address", "name": "bbf-fpprof:destination-mac-address.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/match-criteria/protocol", "name": "bbf-fpprof:protocol.1"}, {"path": "/bbf-frame-processing-profiles/frame-processing-profiles/frame-processing-profile/match-criteria/vlans/tag", "name": "bbf-fpprof:tag.1"}, {"path": "/bbf-l2-dhcpv4-relay/l2-dhcpv4-relay-profiles/l2-dhcpv4-relay-profile", "name": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1"}, {"path": "/bbf-l2-dhcpv4-relay/l2-dhcpv4-relay-profiles/l2-dhcpv4-relay-profile/option82-format/suboptions", "name": "bbf-l2-d4r:suboptions.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarder-policy-profiles/forwarder-policy-profile", "name": "bbf-fwd-prof:forwarder-policy-profile.1"}, {"path": "/bbf-l2-forwarding/forwarding/flooding-policies-profiles/flooding-policies-profile", "name": "bbf-l2-fwd:flooding-policies-profile.1"}, {"path": "/bbf-l2-forwarding/forwarding/flooding-policies-profiles/flooding-policies-profile/flooding-policy", "name": "bbf-l2-fwd:flooding-policy.1"}, {"path": "/bbf-l2-forwarding/forwarding/flooding-policies-profiles/flooding-policies-profile/flooding-policy/out-interface-usages/interface-usages", "name": "bbf-l2-fwd:interface-usages.2"}, {"path": "/bbf-l2-forwarding/forwarding/flooding-policies-profiles/flooding-policies-profile/flooding-policy/in-interface-usages/interface-usages", "name": "bbf-l2-fwd:interface-usages.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarders/forwarder-policy", "name": "bbf-fwd-prof:forwarder-policy.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarders/forwarder", "name": "bbf-l2-fwd:forwarder.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarders/forwarder/flooding-policies/flooding-policy", "name": "bbf-l2-fwd:flooding-policy.2"}, {"path": "/bbf-l2-forwarding/forwarding/forwarders/forwarder/port-groups/port-group", "name": "bbf-l2-fwd:port-group.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarders/forwarder/port-groups/port-group/port", "name": "bbf-l2-fwd:port.2"}, {"path": "/bbf-l2-forwarding/forwarding/forwarders/forwarder/ports/port", "name": "bbf-l2-fwd:port.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarding-databases/forwarding-database", "name": "bbf-l2-fwd:forwarding-database.1"}, {"path": "/bbf-l2-forwarding/forwarding/forwarding-databases/forwarding-database/static-mac-address", "name": "bbf-l2-fwd:static-mac-address.1"}, {"path": "/bbf-l2-forwarding/forwarding/mac-learning-control-profiles/mac-learning-control-profile", "name": "bbf-l2-fwd:mac-learning-control-profile.1"}, {"path": "/bbf-l2-forwarding/forwarding/mac-learning-control-profiles/mac-learning-control-profile/mac-learning-rule", "name": "bbf-l2-fwd:mac-learning-rule.1"}, {"path": "/bbf-l2-forwarding/forwarding/mac-learning-control-profiles/mac-learning-control-profile/mac-learning-rule/mac-can-not-move-to", "name": "bbf-l2-fwd:mac-can-not-move-to.1"}, {"path": "/bbf-l2-forwarding/forwarding/split-horizon-profiles/split-horizon-profile", "name": "bbf-l2-fwd:split-horizon-profile.1"}, {"path": "/bbf-l2-forwarding/forwarding/split-horizon-profiles/split-horizon-profile/split-horizon", "name": "bbf-l2-fwd:split-horizon.1"}, {"path": "/bbf-l2-forwarding/forwarding/split-horizon-profiles/split-horizon-profile/split-horizon/out-interface-usage", "name": "bbf-l2-fwd:out-interface-usage.1"}, {"path": "/bbf-ldra/dhcpv6-ldra-profiles/dhcpv6-ldra-profile", "name": "bbf-ldra:dhcpv6-ldra-profile.1"}, {"path": "/bbf-ldra/dhcpv6-ldra-profiles/dhcpv6-ldra-profile/options/option", "name": "bbf-ldra:option.1"}, {"path": "/bbf-link-table/link-table/link-table", "name": "bbf-lt:link-table.2"}, {"path": "/bbf-link-table/link-table/link-table/to-interface", "name": "bbf-lt:to-interface.1"}, {"path": "/bbf-mgmd/multicast/mgmd/interface-to-cascade", "name": "bbf-mgmd-if:interface-to-cascade.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-proxy-profile", "name": "bbf-mgmd:multicast-proxy-profile.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-snoop-transparent-profile", "name": "bbf-mgmd:multicast-snoop-transparent-profile.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-snoop-with-proxy-reporting-profile", "name": "bbf-mgmd:multicast-snoop-with-proxy-reporting-profile.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn", "name": "bbf-mgmd:multicast-vpn.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/channel-match-range", "name": "bbf-mgmd-channel:channel-match-range.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-channel", "name": "bbf-mgmd:multicast-channel.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-channel/interface-to-cascades", "name": "bbf-mgmd-if:interface-to-cascades.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-channel/interface-to-host", "name": "bbf-mgmd:interface-to-host.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-interface-to-host", "name": "bbf-mgmd:multicast-interface-to-host.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-interface-to-host/multicast-package", "name": "bbf-mgmd:multicast-package.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-network-interface", "name": "bbf-mgmd:multicast-network-interface.1"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-package", "name": "bbf-mgmd:multicast-package.2"}, {"path": "/bbf-mgmd/multicast/mgmd/multicast-vpn/multicast-package/multicast-channel-admission-control", "name": "bbf-mgmd:multicast-channel-admission-control.1"}, {"path": "/bbf-mgmd/multicast/mgmd/preview-parameters-profile", "name": "bbf-mgmd:preview-parameters-profile.1"}, {"path": "/bbf-pppoe-intermediate-agent/pppoe-profiles/pppoe-profile", "name": "bbf-pppoe-ia:pppoe-profile.1"}, {"path": "/bbf-pppoe-intermediate-agent/pppoe-profiles/pppoe-profile/pppoe-vendor-specific-tag/padding", "name": "bbf-pppoe-ia:padding.1"}, {"path": "/bbf-pppoe-intermediate-agent/pppoe-profiles/pppoe-profile/pppoe-vendor-specific-tag/subtag", "name": "bbf-pppoe-ia:subtag.1"}, {"path": "/bbf-qos-classifiers/classifiers/classifier-entry", "name": "bbf-qos-cls:classifier-entry.1"}, {"path": "/bbf-qos-classifiers/classifiers/classifier-entry/classifier-action-entry-cfg", "name": "bbf-qos-cls:classifier-action-entry-cfg.1"}, {"path": "/bbf-qos-classifiers/classifiers/classifier-entry/classifier-action-entry-cfg/pbit-marking-cfg/pbit-marking-list", "name": "bbf-qos-cls:pbit-marking-list.1"}, {"path": "/bbf-qos-classifiers/classifiers/classifier-entry/pbit-marking-list", "name": "bbf-qos-cpsfilt:pbit-marking-list.2"}, {"path": "/bbf-qos-classifiers/classifiers/classifier-entry/pbit-marking-list/pbit-value", "name": "bbf-qos-cpsfilt:pbit-value.2"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter", "name": "bbf-qos-cpsfilt:composite-filter.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter", "name": "bbf-qos-cpsfilt:filter.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/dei-marking-list", "name": "bbf-qos-cpsfilt:dei-marking-list.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/ethernet-frame-type", "name": "bbf-qos-cpsfilt:ethernet-frame-type.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/flow-color", "name": "bbf-qos-cpsfilt:flow-color.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/pbit-marking-list", "name": "bbf-qos-cpsfilt:pbit-marking-list.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/pbit-marking-list/pbit-value", "name": "bbf-qos-cpsfilt:pbit-value.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/protocol", "name": "bbf-qos-cpsfilt:protocol.1"}, {"path": "/bbf-qos-composite-filters/composite-filters/composite-filter/filter/vlans/tag", "name": "bbf-qos-cpsfilt:tag.1"}, {"path": "/bbf-qos-filters/filters/filter", "name": "bbf-qos-filt:filter.1"}, {"path": "/bbf-qos-policing-profiles/car-threshold-profiles/car-threshold-profile", "name": "bbf-qos-plc-prof:car-threshold-profile.1"}, {"path": "/bbf-qos-policing-profiles/priority-group-policing-profiles/priority-group-policing-profile", "name": "bbf-qos-plc-prof:priority-group-policing-profile.1"}, {"path": "/bbf-qos-policing-profiles/priority-group-policing-profiles/priority-group-policing-profile/priority-groups/priority-group", "name": "bbf-qos-plc-prof:priority-group.1"}, {"path": "/bbf-qos-policing/policing-profiles/policing-profile", "name": "bbf-qos-plc:policing-profile.1"}, {"path": "/bbf-qos-policies/policies/policy", "name": "bbf-qos-pol:policy.1"}, {"path": "/bbf-qos-policies/policies/policy/classifiers", "name": "bbf-qos-pol:classifiers.1"}, {"path": "/bbf-qos-policies/qos-policy-profiles/policy-profile", "name": "bbf-qos-pol:policy-profile.1"}, {"path": "/bbf-qos-policies/qos-policy-profiles/policy-profile/policy-list", "name": "bbf-qos-pol:policy-list.1"}, {"path": "/bbf-qos-priority-profiles/cos-group-profiles/cos-group-profile", "name": "bbf-qos-pri-prof:cos-group-profile.1"}, {"path": "/bbf-qos-priority-profiles/cos-group-profiles/cos-group-profile/cos-groups/cos-group", "name": "bbf-qos-pri-prof:cos-group.1"}, {"path": "/bbf-qos-priority-profiles/dscp-to-dscp-mapping-profiles/dscp-to-dscp-mapping-profile", "name": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1"}, {"path": "/bbf-qos-priority-profiles/dscp-to-dscp-mapping-profiles/dscp-to-dscp-mapping-profile/dscps/dscp", "name": "bbf-qos-pri-prof:dscp.2"}, {"path": "/bbf-qos-priority-profiles/dscp-to-dscp-mapping-profiles/dscp-to-dscp-mapping-profile/dscps/dscp/colors/color", "name": "bbf-qos-pri-prof:color.4"}, {"path": "/bbf-qos-priority-profiles/dscp-to-pbit-mapping-profiles/dscp-to-pbit-mapping-profile", "name": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1"}, {"path": "/bbf-qos-priority-profiles/dscp-to-pbit-mapping-profiles/dscp-to-pbit-mapping-profile/dscps/dscp", "name": "bbf-qos-pri-prof:dscp.1"}, {"path": "/bbf-qos-priority-profiles/dscp-to-pbit-mapping-profiles/dscp-to-pbit-mapping-profile/dscps/dscp/colors/color", "name": "bbf-qos-pri-prof:color.3"}, {"path": "/bbf-qos-priority-profiles/ipprec-to-pbit-mapping-profiles/ipprec-to-pbit-mapping-profile", "name": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1"}, {"path": "/bbf-qos-priority-profiles/ipprec-to-pbit-mapping-profiles/ipprec-to-pbit-mapping-profile/ipprecs/ipprec", "name": "bbf-qos-pri-prof:ipprec.1"}, {"path": "/bbf-qos-priority-profiles/ipprec-to-pbit-mapping-profiles/ipprec-to-pbit-mapping-profile/ipprecs/ipprec/colors/color", "name": "bbf-qos-pri-prof:color.2"}, {"path": "/bbf-qos-priority-profiles/pbit-to-pbit-mapping-profiles/pbit-to-pbit-mapping-profile", "name": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1"}, {"path": "/bbf-qos-priority-profiles/pbit-to-pbit-mapping-profiles/pbit-to-pbit-mapping-profile/pbits/pbit", "name": "bbf-qos-pri-prof:pbit.1"}, {"path": "/bbf-qos-priority-profiles/pbit-to-pbit-mapping-profiles/pbit-to-pbit-mapping-profile/pbits/pbit/colors/color", "name": "bbf-qos-pri-prof:color.1"}, {"path": "/bbf-qos-priority-profiles/queue-mapping-profiles/queue-mapping-profile", "name": "bbf-qos-pri-prof:queue-mapping-profile.1"}, {"path": "/bbf-qos-traffic-mngt-profiles/queue-policy-profiles/queue-policy-profile", "name": "bbf-qos-tm-prof:queue-policy-profile.1"}, {"path": "/bbf-qos-traffic-mngt-profiles/queue-policy-profiles/queue-policy-profile/queue-buffer-max-size/queues/queue", "name": "bbf-qos-tm-prof:queue.3"}, {"path": "/bbf-qos-traffic-mngt-profiles/queue-policy-profiles/queue-policy-profile/queue-wred/queues/queue", "name": "bbf-qos-tm-prof:queue.2"}, {"path": "/bbf-qos-traffic-mngt-profiles/queue-wred/queues/queue", "name": "bbf-qos-tm-prof:queue.1"}, {"path": "/bbf-qos-traffic-mngt/tm-profiles/shaper-profile", "name": "bbf-qos-shap:shaper-profile.1"}, {"path": "/bbf-qos-traffic-mngt/tm-profiles/bac-entry", "name": "bbf-qos-tm:bac-entry.1"}, {"path": "/bbf-qos-traffic-mngt/tm-profiles/tc-id-2-queue-id-mapping-profile", "name": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1"}, {"path": "/bbf-qos-traffic-mngt/tm-profiles/tc-id-2-queue-id-mapping-profile/mapping-entry", "name": "bbf-qos-tm:mapping-entry.1"}, {"path": "/bbf-subscriber-profiles/subscriber-profiles/subscriber-profile", "name": "bbf-subprof:subscriber-profile.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile", "name": "bbf-vsi-prof:vsi-profile.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/egress-rewrite/push-tag", "name": "bbf-vsi-prof-fp:push-tag.2"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule", "name": "bbf-vsi-prof-fp:rule.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/exclude-criteria/ethernet-frame-type", "name": "bbf-vsi-prof-fp:ethernet-frame-type.2"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/exclude-criteria/frame-destination-filter/destination-ipv4-address", "name": "bbf-vsi-prof-fp:destination-ipv4-address.2"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/exclude-criteria/frame-destination-filter/destination-ipv6-address", "name": "bbf-vsi-prof-fp:destination-ipv6-address.2"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/exclude-criteria/frame-destination-filter/destination-mac-address", "name": "bbf-vsi-prof-fp:destination-mac-address.2"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/exclude-criteria/protocol", "name": "bbf-vsi-prof-fp:protocol.2"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/ingress-rewrite/push-tag", "name": "bbf-vsi-prof-fp:push-tag.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/match-criteria/ethernet-frame-type", "name": "bbf-vsi-prof-fp:ethernet-frame-type.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/match-criteria/frame-destination-filter/destination-ipv4-address", "name": "bbf-vsi-prof-fp:destination-ipv4-address.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/match-criteria/frame-destination-filter/destination-ipv6-address", "name": "bbf-vsi-prof-fp:destination-ipv6-address.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/match-criteria/frame-destination-filter/destination-mac-address", "name": "bbf-vsi-prof-fp:destination-mac-address.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/match-criteria/protocol", "name": "bbf-vsi-prof-fp:protocol.1"}, {"path": "/bbf-vlan-sub-interface-profiles/vsi-profiles/vsi-profile/frame-processing-rules/ingress-rule/rule/match-criteria/vlans/tag", "name": "bbf-vsi-prof-fp:tag.1"}, {"path": "/bbf-xpon/xpon/onu-alm-policy-profiles/onu-alm-policy-profile", "name": "alm-policy-profile:onu-alm-policy-profile.1"}, {"path": "/bbf-xpon/xpon/onu-alm-policy-profiles/onu-alm-policy-profile/alarm-lists", "name": "alm-policy-profile:alarm-lists.1"}, {"path": "/bbf-xpon/xpon/optic-alm-threshold-profiles/optic-alm-threshold-profile", "name": "alm-threshold-profile:optic-alm-threshold-profile.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile", "name": "bbf-ani-profile:ani-profile.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/gem-bundles/gem-bundle", "name": "bbf-ani-profile:gem-bundle.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/gem-bundles/gem-bundle/slave-gem", "name": "bbf-ani-profile:slave-gem.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/gemports/gemport", "name": "bbf-ani-profile:gemport.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/gemports/gemport/frame-processing/rule", "name": "bbf-ani-profile:rule.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/link-aggregations/link-aggregation", "name": "bbf-ani-profile:link-aggregation.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/link-aggregations/link-aggregation/slave-port", "name": "bbf-ani-profile:slave-port.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/port-bundles/port-bundle", "name": "bbf-ani-profile:port-bundle.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/port-bundles/port-bundle/port-list", "name": "bbf-ani-profile:port-list.1"}, {"path": "/bbf-xpon/xpon/ani-profiles/ani-profile/tconts/tcont", "name": "bbf-ani-profile:tcont.1"}, {"path": "/bbf-xpon/xpon/uni-profiles/uni-profile", "name": "bbf-uni-profile:uni-profile.1"}, {"path": "/bbf-xpon/xpon/uni-profiles/uni-profile/ont-ports/ont-port", "name": "bbf-uni-profile:ont-port.1"}, {"path": "/bbf-xpon/xpon/uni-profiles/uni-profile/ont-ports/ont-port/frame-processing/rule", "name": "bbf-uni-profile:rule.1"}, {"path": "/bbf-xpon/xpon/uni-profiles/uni-profile/packet-forwards/packet-forward", "name": "bbf-uni-profile:packet-forward.1"}, {"path": "/bbf-xpon/xpon/uni-profiles/uni-profile/tpids/tpid", "name": "bbf-uni-profile:tpid.1"}, {"path": "/bbf-xpon/xpon/ictp/all-ictp-proxies-all-channel-groups/proxy", "name": "bbf-xpon:proxy.1"}, {"path": "/bbf-xpon/xpon/ictp/all-ictp-proxies-all-channel-groups/proxy/all-channel-terminations-proxied-by-this-proxy/channel-termination", "name": "bbf-xpon:channel-termination.1"}, {"path": "/bbf-xpon/xpon/multicast-gemports/multicast-gemport", "name": "bbf-xpon:multicast-gemport.1"}, {"path": "/bbf-xpon/xpon/wavelength-profiles/wavelength-profile", "name": "bbf-xpon:wavelength-profile.1"}, {"path": "/bbf-xpon/xpon/onu-power-shedding-profiles/onu-power-shedding-profile", "name": "onu-power-shedding-profile:onu-power-shedding-profile.1"}, {"path": "/bbf-xpon/xpon/onu-tr069-server-profiles/onu-tr069-server-profile", "name": "onu-tr069-profile:onu-tr069-server-profile.1"}, {"path": "/bbf-xpon/xpon/traffic-alarm-profiles/traffic-alarm-profile", "name": "traffic-alarm-profile:traffic-alarm-profile.1"}, {"path": "/bbf-xponani-power-management/xponani-power-management-profiles/power-management-profile", "name": "bbf-xponani-pwr:power-management-profile.1"}, {"path": "/bbf-xponani-power-management/xponani-power-management-profiles/power-management-profile/mode", "name": "bbf-xponani-pwr:mode.1"}, {"path": "/bbf-xpongemtcont/xpongemtcont/gem-bundle/gem-bundle", "name": "bbf-gpon-gemport:gem-bundle.2"}, {"path": "/bbf-xpongemtcont/xpongemtcont/gem-bundle/gem-bundle/slave-gem/slave-gem", "name": "bbf-gpon-gemport:slave-gem.2"}, {"path": "/bbf-xpongemtcont/xpongemtcont/gemports/gemport", "name": "bbf-xpongemtcont:gemport.2"}, {"path": "/bbf-xpongemtcont/xpongemtcont/tconts/tcont", "name": "bbf-xpongemtcont:tcont.2"}, {"path": "/bbf-xpongemtcont/xpongemtcont/traffic-descriptor-profiles/traffic-descriptor-profile", "name": "bbf-xpongemtcont:traffic-descriptor-profile.1"}, {"path": "/huawei-device/config-virtual-license/item-list", "name": "dev:item-list.2"}, {"path": "/huawei-device/device/agile-controller-whitelists/agile-controller-whitelist", "name": "dev:agile-controller-whitelist.1"}, {"path": "/huawei-device/device/agile-controllers/agile-controller", "name": "dev:agile-controller.1"}, {"path": "/huawei-device/set-alarm-threshold/alarm-threshold", "name": "dev:alarm-threshold.1"}, {"path": "/ieee802-dot1ax/lag-system/aggregating-system", "name": "dot1ax:aggregating-system.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge", "name": "dot1q:bridge.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component", "name": "dot1q:component.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-mst/fid-to-mstid", "name": "dot1q:fid-to-mstid.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-mst/fid-to-mstid-allocation", "name": "dot1q:fid-to-mstid-allocation.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-mst/mstid", "name": "dot1q:mstid.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-vlan/fid-to-vid-allocation", "name": "dot1q:fid-to-vid-allocation.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-vlan/protocol-group-database", "name": "dot1q:protocol-group-database.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-vlan/vid-to-fid", "name": "dot1q:vid-to-fid.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-vlan/vid-to-fid-allocation", "name": "dot1q:vid-to-fid-allocation.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/bridge-vlan/vlan", "name": "dot1q:vlan.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/filtering-database/filtering-entry", "name": "dot1q:filtering-entry.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/filtering-database/filtering-entry/port-map", "name": "dot1q:port-map.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/filtering-database/vlan-registration-entry", "name": "dot1q:vlan-registration-entry.1"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/filtering-database/vlan-registration-entry/port-map", "name": "dot1q:port-map.2"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/permanent-database/filtering-entry", "name": "dot1q:filtering-entry.2"}, {"path": "/ieee802-dot1q-bridge/bridges/bridge/component/permanent-database/filtering-entry/port-map", "name": "dot1q:port-map.3"}, {"path": "/huawei-energy-management-an/energy-management-an/energy-saving-modes/energy-saving-mode", "name": "hw-energy-mgnt-an:energy-saving-mode.1"}, {"path": "/huawei-energy-management-an/energy-management-an/energy-saving-modes/energy-saving-mode/methods/method", "name": "hw-energy-mgnt-an:method.1"}, {"path": "/huawei-energy-management-an/energy-management-an/energy-saving-modes/energy-saving-mode/methods/method/parameters/parameter", "name": "hw-energy-mgnt-an:parameter.1"}, {"path": "/huawei-resource-pool-an/resource-pool-an/resource-pool-esns/resource-pool-esn", "name": "hw-res-pool-an:resource-pool-esn.1"}, {"path": "/huawei-resource-pool-an/resource-pool-an/resource-pool-esns/resource-pool-esn/consumptions/consumption", "name": "hw-res-pool-an:consumption.1"}, {"path": "/huawei-resource-pool-an/resource-pool-an/resource-pool-esns/resource-pool-esn/rtu-consumptions/rtu-consumption", "name": "hw-res-pool-an:rtu-consumption.1"}, {"path": "/huawei-resource-pool-an/resource-pool-an/resource-pools/resource-pool", "name": "hw-res-pool-an:resource-pool.1"}, {"path": "/ietf-hardware/hardware/component", "name": "hw:component.1"}, {"path": "/ietf-hardware/hardware/component/uri", "name": "hw:uri.1"}, {"path": "/ietf-interfaces/interfaces/interface", "name": "if:interface.1"}, {"path": "/ietf-interfaces/interfaces/interface/port-layer-if", "name": "bbf-if-port-ref:port-layer-if.1"}, {"path": "/ietf-interfaces/interfaces/interface/tm-root/queue", "name": "bbf-qos-tm:queue.2"}, {"path": "/ietf-interfaces/interfaces/interface/inline-frame-processing/ingress-rule/rule", "name": "bbf-subif:rule.1"}, {"path": "/ietf-interfaces/interfaces/interface/inline-frame-processing/ingress-rule/rule/flexible-match/match-criteria/tag", "name": "bbf-subif-tag:tag.1"}, {"path": "/ietf-interfaces/interfaces/interface/inline-frame-processing/ingress-rule/rule/ingress-rewrite/push-tag", "name": "bbf-subif-tag:push-tag.1"}, {"path": "/ietf-interfaces/interfaces/interface/channel-pair/tcont-groups/tcont-group", "name": "bbf-xpon-channel:tcont-group.1"}, {"path": "/ietf-interfaces/interfaces/interface/ani/ont-port-bundle/eth-bundle", "name": "bbf-xpon-ani:eth-bundle.1"}, {"path": "/ietf-interfaces/interfaces/interface/ani/ont-port-bundle/eth-bundle/port-list/port", "name": "bbf-xpon-ani:port.1"}, {"path": "/ietf-interfaces/interfaces/interface/ani/snmp-route/onu-snmp-route", "name": "bbf-xpon-ani:onu-snmp-route.1"}, {"path": "/ietf-interfaces/interfaces/interface/v-ani/tpid", "name": "bbf-xpon-v-ani:tpid.1"}, {"path": "/ietf-interfaces/interfaces/interface/aggregator/ports/port", "name": "an-dot1ax:port.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/egress-vid-translations", "name": "dot1q:egress-vid-translations.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/pcp-decoding-table/pcp-decoding-map", "name": "dot1q:pcp-decoding-map.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/pcp-decoding-table/pcp-decoding-map/priority-map", "name": "dot1q:priority-map.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/pcp-encoding-table/pcp-encoding-map", "name": "dot1q:pcp-encoding-map.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/pcp-encoding-table/pcp-encoding-map/priority-map", "name": "dot1q:priority-map.2"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/protocol-group-vid-set", "name": "dot1q:protocol-group-vid-set.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/protocol-group-vid-set/vid", "name": "dot1q:vid.2"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/traffic-class/traffic-class-map", "name": "dot1q:traffic-class-map.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/traffic-class/traffic-class-map/available-traffic-class", "name": "dot1q:available-traffic-class.1"}, {"path": "/ietf-interfaces/interfaces/interface/bridge-port/vid-translations", "name": "dot1q:vid-translations.1"}, {"path": "/ietf-interfaces/interfaces/interface/ipv4/address", "name": "ip:address.1"}, {"path": "/ietf-keystore/keystore/asymmetric-keys/asymmetric-key", "name": "ks:asymmetric-key.1"}, {"path": "/ietf-keystore/keystore/asymmetric-keys/asymmetric-key/certificates/certificate", "name": "ks:certificate.1"}, {"path": "/ieee802-dot1ab-lldp/lldp/port", "name": "lldp:port.1"}, {"path": "/ietf-netconf-acm/nacm/groups/group", "name": "nacm:group.1"}, {"path": "/ietf-netconf-acm/nacm/groups/group/user-name", "name": "nacm:user-name.1"}, {"path": "/ietf-netconf-acm/nacm/rule-list", "name": "nacm:rule-list.1"}, {"path": "/ietf-netconf-acm/nacm/rule-list/group", "name": "nacm:group.2"}, {"path": "/ietf-netconf-acm/nacm/rule-list/rule", "name": "nacm:rule.1"}, {"path": "/huawei-network-instance/network-instance/instances/instance", "name": "ni:instance.1"}, {"path": "/openconfig-telemetry/telemetry-system/destination-groups/destination-group", "name": "oc-telemetry:destination-group.1"}, {"path": "/openconfig-telemetry/telemetry-system/destination-groups/destination-group/destinations/destination", "name": "oc-telemetry:destination.1"}, {"path": "/openconfig-telemetry/telemetry-system/sensor-groups/sensor-group", "name": "oc-telemetry:sensor-group.1"}, {"path": "/openconfig-telemetry/telemetry-system/sensor-groups/sensor-group/sensor-paths/sensor-path", "name": "oc-telemetry:sensor-path.1"}, {"path": "/openconfig-telemetry/telemetry-system/subscriptions/persistent/subscription", "name": "oc-telemetry:subscription.1"}, {"path": "/openconfig-telemetry/telemetry-system/subscriptions/persistent/subscription/destination-groups/destination-group", "name": "oc-telemetry:destination-group.2"}, {"path": "/openconfig-telemetry/telemetry-system/subscriptions/persistent/subscription/sensor-profiles/sensor-profile", "name": "oc-telemetry:sensor-profile.1"}, {"path": "/bbf-xpon-ont-load/xpon-ont-load/ont-selects/ont-select", "name": "ontload:ont-select.1"}, {"path": "/huawei-protection-group/protection-groups/protection-group", "name": "ps:protection-group.1"}, {"path": "/huawei-protection-group/protection-groups/protection-group/member", "name": "ps:member.1"}, {"path": "/ietf-routing/routing/control-plane-protocols/control-plane-protocol", "name": "rt:control-plane-protocol.2"}, {"path": "/ietf-routing/routing/control-plane-protocols/control-plane-protocol/static-routes/ipv4/route", "name": "v4ur:route.1"}, {"path": "/ietf-routing/routing/control-plane-protocols/control-plane-protocol/static-routes/ipv4/route/next-hop/next-hop-list/next-hop", "name": "v4ur:next-hop.2"}, {"path": "/ietf-snmp/snmp/community", "name": "snmp:community.1"}, {"path": "/ietf-snmp/snmp/engine/listen", "name": "snmp:listen.1"}, {"path": "/ietf-snmp/snmp/notify", "name": "snmp:notify.1"}, {"path": "/ietf-snmp/snmp/notify-filter-profile", "name": "snmp:notify-filter-profile.1"}, {"path": "/ietf-snmp/snmp/notify-filter-profile/exclude", "name": "snmp:exclude.1"}, {"path": "/ietf-snmp/snmp/notify-filter-profile/include", "name": "snmp:include.1"}, {"path": "/ietf-snmp/snmp/proxy", "name": "snmp:proxy.1"}, {"path": "/ietf-snmp/snmp/target", "name": "snmp:target.1"}, {"path": "/ietf-snmp/snmp/target-params", "name": "snmp:target-params.1"}, {"path": "/ietf-snmp/snmp/target/tag", "name": "snmp:tag.1"}, {"path": "/ietf-snmp/snmp/tlstm/cert-to-name", "name": "snmp:cert-to-name.1"}, {"path": "/ietf-snmp/snmp/usm/local/user", "name": "snmp:user.1"}, {"path": "/ietf-snmp/snmp/usm/remote", "name": "snmp:remote.1"}, {"path": "/ietf-snmp/snmp/usm/remote/user", "name": "snmp:user.2"}, {"path": "/ietf-snmp/snmp/vacm/group", "name": "snmp:group.1"}, {"path": "/ietf-snmp/snmp/vacm/group/access", "name": "snmp:access.1"}, {"path": "/ietf-snmp/snmp/vacm/group/member", "name": "snmp:member.1"}, {"path": "/ietf-snmp/snmp/vacm/group/member/security-model", "name": "snmp:security-model.1"}, {"path": "/ietf-snmp/snmp/vacm/view", "name": "snmp:view.1"}, {"path": "/ietf-snmp/snmp/vacm/view/exclude", "name": "snmp:exclude.2"}, {"path": "/ietf-snmp/snmp/vacm/view/include", "name": "snmp:include.2"}, {"path": "/ietf-system/system/dhcpv6-global/dhcpv6-option-permit-forwarding", "name": "bbf-ldra-ext:dhcpv6-option-permit-forwarding.1"}, {"path": "/ietf-system/system/subscriber-management/aggregation-circuit-id-format", "name": "bbf-raioprof:aggregation-circuit-id-format.1"}, {"path": "/ietf-system/system/subscriber-management/sub-option-switch", "name": "bbf-raioprof:sub-option-switch.1"}, {"path": "/ietf-system/system/authentication/user", "name": "sys:user.1"}, {"path": "/ietf-system/system/authentication/user/authorized-key", "name": "sys:authorized-key.1"}, {"path": "/ietf-system/system/authentication/user-clients/user-client", "name": "system-ext:user-client.1"}, {"path": "/ietf-system/system/dns-resolver/search", "name": "sys:search.1"}, {"path": "/ietf-system/system/dns-resolver/server", "name": "sys:server.2"}, {"path": "/ietf-system/system/ntp/server", "name": "sys:server.1"}, {"path": "/ietf-system/system/radius/server", "name": "sys:server.3"}, {"path": "/ietf-system/system/auto-save-policys/auto-save-policy", "name": "system-ext:auto-save-policy.1"}, {"path": "/ietf-system/system/management-modes/management-mode", "name": "system-ext:management-mode.1"}, {"path": "/ietf-system/system/network-server/network-server-source-interfaces/network-server-source-interface", "name": "system-ext:network-server-source-interface.1"}, {"path": "/ietf-system/system/network-server/network-server-source-ips/network-server-source-ip", "name": "system-ext:network-server-source-ip.1"}, {"path": "/ietf-syslog/syslog/actions/global-params/severity-filters/severity-filter", "name": "syslog-ext:severity-filter.1"}, {"path": "/ietf-syslog/syslog/actions/logs/log", "name": "syslog-ext:log.1"}, {"path": "/ietf-syslog/syslog/actions/console/facility-filter/facility-list", "name": "syslog:facility-list.1"}, {"path": "/ietf-syslog/syslog/actions/file/log-file", "name": "syslog:log-file.1"}, {"path": "/ietf-syslog/syslog/actions/file/log-file/facility-filter/facility-list", "name": "syslog:facility-list.2"}, {"path": "/ietf-syslog/syslog/actions/remote/destination", "name": "syslog:destination.1"}, {"path": "/ietf-syslog/syslog/actions/remote/destination/syslog-filter/facility-list", "name": "syslog-ext:facility-list.1"}, {"path": "/ietf-syslog/syslog/actions/remote/destination/syslog-filter/severity-list", "name": "syslog-ext:severity-list.1"}, {"path": "/ietf-syslog/syslog/actions/remote/destination/facility-filter/facility-list", "name": "syslog:facility-list.3"}, {"path": "/ietf-syslog/syslog/actions/remote/destination/signing/cert-signers/cert-signer", "name": "syslog:cert-signer.1"}, {"path": "/ietf-syslog/syslog/actions/remote/destination/tls/hello-params/cipher-suites/cipher-suite", "name": "syslog:cipher-suite.1"}, {"path": "/ietf-syslog/syslog/actions/remote/destination/tls/hello-params/tls-versions/tls-version", "name": "syslog:tls-version.1"}, {"path": "/ietf-trust-anchors/trust-anchors/pinned-certificates", "name": "ta:pinned-certificates.1"}, {"path": "/ietf-trust-anchors/trust-anchors/pinned-certificates/pinned-certificate", "name": "ta:pinned-certificate.1"}, {"path": "/ietf-trust-anchors/trust-anchors/pinned-host-keys", "name": "ta:pinned-host-keys.1"}, {"path": "/ietf-trust-anchors/trust-anchors/pinned-host-keys/pinned-host-key", "name": "ta:pinned-host-key.1"}]