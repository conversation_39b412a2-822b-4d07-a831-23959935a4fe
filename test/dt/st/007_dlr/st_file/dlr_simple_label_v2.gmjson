[{"type": "record", "name": "dlr_simple_label", "schema_version": 2, "special_complex": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": true}], "keys": [{"node": "dlr_simple_label", "name": "Simple_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]