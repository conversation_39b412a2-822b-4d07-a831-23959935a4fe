/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st file for path search
 * Author: GQL
 * Create: 2023-09-11
 */

#include <stdio.h>
#include "client_common_st.h"
#include "client_option.h"
#include "gmc_gql.h"
#include "fes_tables_data_structure.h"
#include "fes_common.h"

static DbMemCtxT *g_basicMem = NULL;
const uint32_t WAIT_TIME = 1000;  // ms

class PathPathSearch : public StClient {
public:
    static void SetUpTestCase()
    {
        // FES PATH需要硬件加速提供的hash
        StartDbServer((char *)"gmserver_gql.ini");
        DbSleep(WAIT_TIME);  // Wait server ready
        st_clt_init();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(WAIT_TIME);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
        DbMemCtxArgsT args = {0};
        g_basicMem = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memory context", &args);
        ASSERT_NE(nullptr, g_basicMem);
    }

    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(g_basicMem);
        StopAndDestroyEpoll(responseEpollThreadId, &responseEpollFd);
        StopAndDestroyEpoll(timeoutEpollThreadId, &timeoutEpollFd);
        st_clt_uninit();
        ShutDownDbServer();
    }
};

static void CreatePath(GmcStmtT *stmt)
{
    /* two path
     *   T1
     * | OR \
     * T2   T3
     * |     \
     * T4    T4
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1) HASH_CAP 10000
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1) HASH_CAP 10000
        );
        CREATE VERTEXLABEL T3 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1) HASH_CAP 10000
            HAC_HASH INDEX idx1(property2) UNIQUE HASH_CAP 10000
        );
        CREATE VERTEXLABEL T4 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1) HASH_CAP 10000
            HAC_HASH INDEX idx1(property2) UNIQUE HASH_CAP 10000
        );
        CREATE VERTEXLABEL T5 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1) HASH_CAP 10000
            HAC_HASH INDEX idx1(property2) UNIQUE HASH_CAP 10000
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property1 == T2.property1
        ;
        CREATE EDGE E13
        FROM T1 TO T3
        WHERE T1.property1 == T3.property1
        ;
        CREATE EDGE E24
        FROM T2 TO T4
        WHERE T2.property1 == T4.property1
        ;
        CREATE EDGE E34
        FROM T3 TO T4
        WHERE T3.property2 == T4.property2
        ;
        CREATE EDGE E25
        FROM T2 TO T5
        WHERE T2.property1 == T5.property1
        ;
        CREATE EDGE E35
        FROM T3 TO T5
        WHERE T3.property2 == T5.property2
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12|:E13]->(
                (:T2)-[:E24]->(:T4) |
                (:T3)-[:E34]->(:T4)
            )
            RETURN
            (
                REPLACE T1.property1 300
            )
        );
        CREATE PATH P2 (
            MATCH
            (:T1)-[:E12|:E13]->(
                (:T2)-[:E25]->(:T5) |
                (:T3)-[:E35]->(:T5)
            )
            RETURN
            (
                REPLACE T1.property1 300
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void PathUserCb(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    if (dataType == PATH_TYPE) {
        // 解析第一个4字节数目
        uint32_t pathNum = 0;
        Bytes2Uint32(data, &pathNum);
        *(uint32_t *)userData += pathNum;
    }
}

static void FillTable(GmcStmtT *stmt, const char *tableName)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith2PropT obj = {
        .property1 = 1,
        .property2 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void FillTable2(GmcStmtT *stmt, const char *tableName)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith2PropT obj = {
        .property1 = 2,
        .property2 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void PrepareData1(GmcStmtT *stmt)
{
    FillTable(stmt, "T1");
    FillTable2(stmt, "T1");
    FillTable(stmt, "T2");
    FillTable2(stmt, "T3");
}

static void InsertTriggerData1(GmcStmtT *stmt)
{
    FillTable(stmt, "T4");
}

static void DropPath(GmcStmtT *stmt)
{
    /* two path
     *   T1
     * | OR \
     * T2   T3
     * |     \
     * T4    T4
     */
    const char *dropPaths = R"(
        DROP PATH P1;
        DROP PATH P2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
        DROP EDGE E13;
        DROP EDGE E24;
        DROP EDGE E34;
        DROP EDGE E25;
        DROP EDGE E35;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
        DROP VERTEXLABEL T3;
        DROP VERTEXLABEL T4;
        DROP VERTEXLABEL T5;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

/* two path
 *   T1(1, 3) (2, 3)
 * | OR       \
 * T2(1, 3)   T3(2, 3)
 * |            \
 * T4(1, 3)    T4(1, 3)
 * 1. two path instance
 */
TEST_F(PathPathSearch, PathPathSearch1)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    PrepareData1(stmt);
    const char *createTriggerRule = R"(
        CREATE TRIGGER t1 ON REPLACE T4 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData1(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 2u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void PrepareData2(GmcStmtT *stmt)
{
    FillTable(stmt, "T1");
    FillTable(stmt, "T2");
    FillTable2(stmt, "T3");
}

static void InsertTriggerData2(GmcStmtT *stmt)
{
    FillTable(stmt, "T5");
}

/* two path
 *   T1(1, 3)
 * | OR       \ unconnected
 * T2(1, 3)   T3(2, 3)
 * |            \
 * T5(1, 3)    T5(1, 3)
 * 1. one path instance
 */
TEST_F(PathPathSearch, PathPathSearch2)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P2
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    PrepareData2(stmt);
    const char *createTriggerRule = R"(
        CREATE TRIGGER t2 ON REPLACE T5 WHEN TRUE DO SEARCH P2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData2(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath3(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index1(property2)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property1 == T2.property2
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)
            RETURN
            (
                REPLACE T2.property1 300
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void DropPath3(GmcStmtT *stmt)
{
    const char *dropPaths = R"(
        DROP PATH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

static void PrepareData3(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith3PropT obj = {
        .property1 = 10,
        .property2 = 3,
        .property3 = 0,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    obj.property1 = 20;
    obj.property2 = 3;
    obj.property3 = 0;
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void InsertTriggerData3(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith2PropT obj = {
        .property1 = 3,
        .property2 = 1,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void PathUserCb3(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    uint32_t newPathCnt = pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    ASSERT_EQ(tlv.pathNum, newPathCnt);
    ASSERT_EQ(tlv.pathNum, 1u);
    PathTlvT *paths = tlv.paths;
    PathTlvT path = paths[0];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 2u);
    uint16_t encapType = 300;
    uint16_t vertexLen = 4;
    VertexTlvT vertex = path.vertexs[0];
    uint32_t property1;
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 10u);
    vertex = path.vertexs[1];
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 20u);
    return;
}

/* one path pattern
 *      T1(3, 1)
 * |                \
 * T2(10, 3, 0)   T2(20, 3, 0)
 * 1. one path instance
 */
TEST_F(PathPathSearch, PathPathSearch3)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath3(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb3, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    PrepareData3(stmt);
    const char *createTriggerRule = R"(
        CREATE TRIGGER t3 ON REPLACE T1 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData3(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 1u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t3;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath3(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath4(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index1(property2)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index1(property3)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property2 == T2.property3
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)
            RETURN
            (
                REPLACE T2.property1 300
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void DropPath4(GmcStmtT *stmt)
{
    const char *dropPaths = R"(
        DROP PATH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

static void PrepareData4(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith3PropT obj = {
        .property1 = 10,
        .property2 = 1,
        .property3 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    obj.property1 = 20;
    obj.property2 = 1;
    obj.property3 = 3;
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void InsertTriggerData4(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith2PropT obj = {
        .property1 = 1,
        .property2 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    obj.property1 = 2;
    obj.property2 = 3;
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void PathUserCb4(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    uint32_t newPathCnt = pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    ASSERT_EQ(tlv.pathNum, newPathCnt);
    ASSERT_EQ(tlv.pathNum, 2u);
    PathTlvT *paths = tlv.paths;

    // path instance 1
    PathTlvT path = paths[0];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 2u);
    uint16_t encapType = 300;
    uint16_t vertexLen = 4;
    VertexTlvT vertex = path.vertexs[0];
    uint32_t property1;
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 10u);
    vertex = path.vertexs[1];
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 20u);

    // path instance 2
    path = paths[1];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 2u);
    vertex = path.vertexs[0];
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 10u);
    vertex = path.vertexs[1];
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 20u);
    return;
}

/* one path pattern
 *      T1(1, 3), (2, 3)
 * |                \
 * T2(10, 1, 3)   T2(20, 1, 3)
 * 1. two path instance
 */
TEST_F(PathPathSearch, PathPathSearch4)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath4(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb4, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    PrepareData4(stmt);
    const char *createTriggerRule = R"(
        CREATE TRIGGER t4 ON REPLACE T1 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData4(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 2u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 2u);
    printf("Path received: %d\n", pathNum);

    const char *dropTriggerRule = R"(
        DROP TRIGGER t4;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggerRule));

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DropPath4(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath5(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT64,
            property2 UINT64,
            property3 UINT64
            PRIMARY INDEX pk(property1, property2, property3)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT64,
            property2 UINT64,
            property3 UINT64
            PRIMARY INDEX pk(property1, property2, property3)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property1 == T2.property1
        AND T1.property2 == T2.property2
        AND T1.property3 == T2.property3
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)
            RETURN
            (
                REPLACE T2.property1 300
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void DropPath5(GmcStmtT *stmt)
{
    const char *dropPaths = R"(
        DROP PATH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

static void PrepareData5(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith3Prop2T obj = {
        .property1 = 1,
        .property2 = 2,
        .property3 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void InsertTriggerData5(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith3Prop2T obj = {
        .property1 = 1,
        .property2 = 2,
        .property3 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

/* one path pattern, traversal from key(len == 24 bytes)
 * T1(1, 2, 3) insert first
 * |
 * T2(1, 2, 3) insert second
 * 1. one path instance
 */
TEST_F(PathPathSearch, PathPathSearch5)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath5(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    PrepareData5(stmt);
    const char *createTriggerRule = R"(
        CREATE TRIGGER t5 ON REPLACE T2 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData5(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 1u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t5;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath5(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath6(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     * |
     * T3
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T3 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property2 == T2.property2
        ;
        CREATE EDGE E23
        FROM T2 TO T3
        WHERE T2.property1 == T3.property1
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)-[:E23]->(:T3)
            RETURN
            (
                REPLACE T1.property1 100, T2.property1 200, T3.property1 300
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void DropPath6(GmcStmtT *stmt)
{
    const char *dropPaths = R"(
        DROP PATH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
        DROP EDGE E23;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
        DROP VERTEXLABEL T3;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

static void InsertTriggerData6(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith2PropT obj = {
        .property1 = 1,
        .property2 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    obj.property1 = 2;
    obj.property2 = 3;
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void PathUserCb6(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    pathNum = 1;
    ASSERT_EQ(tlv.pathNum, pathNum);
    PathTlvT path1 = tlv.paths[0];
    uint32_t pathIdInApp = 1;
    ASSERT_EQ(path1.pathIdInApp, pathIdInApp);
    uint32_t vertexNum = 3;
    ASSERT_EQ(path1.vertexNum, vertexNum);
    VertexTlvT vertex = path1.vertexs[2];
    uint16_t encapType = 300;
    ASSERT_EQ(vertex.encapType, encapType);
    return;
}

/* one path pattern
 * T1
 * |
 * T2
 * |
 * T3
 * 1. 预置数据
 * T1(1, 3)
 *
 * T3(1, 3)
 * 2. 插入点T2(1, 3), T2(2, 3)
 * T1(1, 3)
 * |          \
 * T2(1, 3)   T2(2, 3)
 * |
 * T3(1, 3)
 * 下发path num 1，vertex num 3
 */
TEST_F(PathPathSearch, PathPathSearch6)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath6(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb6, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    FillTable(stmt, "T1");
    FillTable(stmt, "T3");
    const char *createTriggerRule = R"(
        CREATE TRIGGER t6 ON REPLACE T2 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData6(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 1u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t6;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath6(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath7(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     * |
     * T3
     * |
     * T4
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T3 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T4 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property2 == T2.property2
        ;
        CREATE EDGE E23
        FROM T2 TO T3
        WHERE T2.property1 == T3.property1
        ;
        CREATE EDGE E34
        FROM T3 TO T4
        WHERE T3.property1 == T4.property1
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)-[:E23]->(:T3)-[:E34]->(:T4)
            RETURN
            (
                REPLACE T1.property1 100, T2.property1 200, T3.property1 300, T4.property1 400
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void DropPath7(GmcStmtT *stmt)
{
    const char *dropPaths = R"(
        DROP PATH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
        DROP EDGE E23;
        DROP EDGE E34;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
        DROP VERTEXLABEL T3;
        DROP VERTEXLABEL T4;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

static void InsertTriggerData7(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T3", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith2PropT obj = {
        .property1 = 2,
        .property2 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void PathUserCb7(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    pathNum = 1;
    ASSERT_EQ(tlv.pathNum, pathNum);
    PathTlvT path1 = tlv.paths[0];
    uint32_t pathIdInApp = 1;
    ASSERT_EQ(path1.pathIdInApp, pathIdInApp);
    uint32_t vertexNum = 4;
    ASSERT_EQ(path1.vertexNum, vertexNum);
    VertexTlvT vertex = path1.vertexs[3];
    uint16_t encapType = 400;
    ASSERT_EQ(vertex.encapType, encapType);
    return;
}

/* one path pattern
 * T1
 * |
 * T2
 * |
 * T3
 * |
 * T4
 * 1. 预置数据
 * T1(1, 3)
 * |            \
 * T2(1, 3)     T(2, 3)
 * |
 * T3(1, 3)
 * |
 * T4(1, 3)
 * 2. 插入点T3(2, 3)
 * T1(1, 3)
 * |            \
 * T2(1, 3)     T2(2, 3)
 * |            |
 * T3(1, 3)     T3(2, 3)
 * |
 * T4(1, 3)
 * 下发path num 1，vertex num 4
 */
TEST_F(PathPathSearch, PathPathSearch7)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath7(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb7, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    FillTable(stmt, "T1");
    FillTable(stmt, "T3");
    FillTable(stmt, "T4");
    InsertTriggerData6(stmt);
    const char *createTriggerRule = R"(
        CREATE TRIGGER t7 ON REPLACE T3 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData7(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 1u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t7;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath7(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath8(GmcStmtT *stmt)
{
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T3 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
        CREATE VERTEXLABEL T4 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX idx1(property2)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property1 == T2.property1
        ;
        CREATE EDGE E13
        FROM T1 TO T3
        WHERE T1.property1 == T3.property1
        ;
        CREATE EDGE E24
        FROM T2 TO T4
        WHERE T2.property1 == T4.property1
        ;
        CREATE EDGE E34
        FROM T3 TO T4
        WHERE T3.property1 == T4.property1
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (V1:T1)-[:E12]->(:T2)-[:E24]->(V4:T4),
            (V1)-[:E13]->(:T3)-[:E34]->(V4)
            RETURN
            (
                REPLACE T1.property1 100, T2.property1 200, T3.property1 300, T4.property1 400
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
    const char *createTrigger = R"(
        CREATE TRIGGER t4 ON REPLACE T4 WHEN TRUE DO SEARCH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTrigger));
}

static void DropPath8(GmcStmtT *stmt)
{
    const char *dropTrigger = R"(
        DROP TRIGGER t4;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTrigger));
    const char *dropPaths = R"(
        DROP PATH P1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropPaths));
    const char *dropEdges = R"(
        DROP EDGE E12;
        DROP EDGE E13;
        DROP EDGE E24;
        DROP EDGE E34;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdges));
    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
        DROP VERTEXLABEL T3;
        DROP VERTEXLABEL T4;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
}

static void PathUserCb8(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    pathNum = 1;
    ASSERT_EQ(tlv.pathNum, pathNum);
    PathTlvT path1 = tlv.paths[0];
    uint32_t pathIdInApp = 1;
    ASSERT_EQ(path1.pathIdInApp, pathIdInApp);
    uint32_t vertexNum = 5;  // 正搜时，节点未去重，有两个T4的vertex
    ASSERT_EQ(path1.vertexNum, vertexNum);
    uint16_t encapType = 400;
    VertexTlvT vertex = path1.vertexs[3];
    ASSERT_EQ(vertex.encapType, encapType);
    vertex = path1.vertexs[4];
    ASSERT_EQ(vertex.encapType, encapType);
    return;
}

static void ReplaceTableWithTwoProp(
    GmcConnT *conn, GmcStmtT *stmt, const char *tableName, uint32_t (*vertexs)[2], uint32_t rowNum)
{
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE));
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith2PropT tmpVertex = {0};
    for (uint32_t i = 0; i < rowNum; i++) {
        tmpVertex.property1 = vertexs[i][0];  // primary key
        tmpVertex.property2 = vertexs[i][1];
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
}

/**
 * diamond path instance
 * path pattern
 *   T1
 * |    \
 * T2   T3
 * \    /
 *   T4
 * all edges connected by table.property1
 * check path instance TLV
 **/
TEST_F(PathPathSearch, PathPathSearch8)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath8(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb8, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    uint32_t vertexs[1][2] = {{1, 2}};
    uint32_t rowNum = sizeof(vertexs) / sizeof(vertexs[0]);

    // replace T1 (1,2)
    ReplaceTableWithTwoProp(conn, stmt, "T1", vertexs, rowNum);

    // replace T2 (1,2)
    ReplaceTableWithTwoProp(conn, stmt, "T2", vertexs, rowNum);

    // replace T3 (1,2)
    ReplaceTableWithTwoProp(conn, stmt, "T3", vertexs, rowNum);

    // replace T4 (1,2)
    ReplaceTableWithTwoProp(conn, stmt, "T4", vertexs, rowNum);

    uint32_t sleepCnt = 0;
    uint32_t expectPathNum = 1;
    while (pathNum != expectPathNum) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    ASSERT_EQ(expectPathNum, pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DropPath8(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void PathUserCb9(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    uint32_t newPathCnt = pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    ASSERT_EQ(tlv.pathNum, newPathCnt);
    ASSERT_EQ(tlv.pathNum, 1u);
    PathTlvT *paths = tlv.paths;
    PathTlvT path = paths[0];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 1u);
    uint16_t encapType = 300;
    uint16_t vertexLen = 8;
    VertexTlvT vertex = path.vertexs[0];
    uint32_t property1;
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 1u);
    return;
}

/* one path pattern, publish one vertex
 * T1(1, 2, 3) insert second
 * |
 * T2(1, 2, 3) insert first
 * 1. one path instance
 */
TEST_F(PathPathSearch, PathPathSearch9)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath5(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb9, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    InsertTriggerData5(stmt);
    /* trigger pvc */
    const char *createTriggerRule = R"(
        CREATE TRIGGER t9 ON REPLACE T1 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    PrepareData5(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 1u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t9;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath5(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath10(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT64,
            property2 UINT64,
            property3 UINT64
            PRIMARY INDEX pk(property1, property2, property3)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT64,
            property2 UINT64,
            property3 UINT64
            PRIMARY INDEX pk(property1, property2, property3)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property1 == T2.property1
        AND T1.property2 == T2.property2
        AND T1.property3 == T2.property3
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)
            RETURN
            (
                REPLACE T1.property1 300, T2.property2 400
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void PathUserCb10(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    uint32_t newPathCnt = pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    ASSERT_EQ(tlv.pathNum, newPathCnt);
    ASSERT_EQ(tlv.pathNum, 2u);
    PathTlvT *paths = tlv.paths;

    PathTlvT path = paths[0];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 2u);
    uint16_t encapType = 300;
    uint16_t vertexLen = 8;
    VertexTlvT vertex = path.vertexs[0];
    uint32_t property1;
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 1u);
    vertex = path.vertexs[1];
    encapType = 400;
    uint32_t property2 = 0;
    Bytes2Uint32(vertex.vertex, &property2);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property2, 2u);

    path = paths[1];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 2u);
    encapType = 300;
    vertex = path.vertexs[0];
    Bytes2Uint32(vertex.vertex, &property1);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property1, 2u);
    vertex = path.vertexs[1];
    encapType = 400;
    Bytes2Uint32(vertex.vertex, &property2);
    ASSERT_EQ(vertex.encapType, encapType);
    ASSERT_EQ(vertex.vertexLen, vertexLen);
    ASSERT_EQ(property2, 2u);
    return;
}

static void PrepareData10(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith3Prop2T obj = {
        .property1 = 1,
        .property2 = 2,
        .property3 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    obj.property1 = 2;
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void InsertTriggerData10(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_REPLACE));
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    FesTestTableWith3Prop2T obj = {
        .property1 = 1,
        .property2 = 2,
        .property3 = 3,
    };
    /* prepare dml value */
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    obj.property1 = 2;
    ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

/* one path pattern, publish two vertex
 * T1(1, 2, 3) insert second
 * |
 * T2(1, 2, 3) insert first
 * 1. one path instance
 */
TEST_F(PathPathSearch, PathPathSearch10)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath10(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb10, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    PrepareData10(stmt);
    /* trigger pvc */
    const char *createTriggerRule = R"(
        CREATE TRIGGER t10 ON REPLACE T1 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    InsertTriggerData10(stmt);
    uint32_t sleepCnt = 0;
    while (pathNum != 2u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 2u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t10;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath5(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath11(GmcStmtT *stmt)
{
    /* one path pattern
     * T1
     * |
     * T2
     */
    const char *createTables = R"(
        CREATE VERTEXLABEL T1 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL T2 (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property2)
            MULTI_HASH INDEX idx1(property1)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    const char *createEdges = R"(
        CREATE EDGE E12
        FROM T1 TO T2
        WHERE T1.property1 == T2.property1
        ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdges));
    const char *createPaths = R"(
        CREATE PATH P1 (
            MATCH
            (:T1)-[:E12]->(:T2)
            RETURN
            (
                REPLACE T1.property1 300, T2.property2 400
            )
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void PathUserCb11(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    // 解析第一个4字节数目
    uint32_t pathNum = 0;
    Bytes2Uint32(data, &pathNum);
    *(uint32_t *)userData += pathNum;
    uint32_t newPathCnt = pathNum;
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    ASSERT_EQ(tlv.pathNum, newPathCnt);
    ASSERT_EQ(tlv.pathNum, 1u);
    PathTlvT *paths = tlv.paths;

    PathTlvT path = paths[0];
    ASSERT_EQ(path.pathIdInApp, 1u);
    ASSERT_EQ(path.vertexNum, 4u);
    return;
}

/* one path pattern, remove part of duplicate roots
 * T1(2, 1) insert first
 * |
 * T2(2, 1), (2, 2) (2, 3) insert second
 * 1. one path instance
 */
TEST_F(PathPathSearch, PathPathSearch11)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath11(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    P1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb11, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    /* trigger pvc */
    const char *createTriggerRule = R"(
        CREATE TRIGGER t11 ON REPLACE T2 WHEN TRUE DO SEARCH P1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    uint32_t vertexs[1][2] = {{2, 1}};
    uint32_t rowNum = sizeof(vertexs) / sizeof(vertexs[0]);

    // replace T1 (1,2)
    ReplaceTableWithTwoProp(conn, stmt, "T1", vertexs, rowNum);

    uint32_t vertexs2[3][2] = {{2, 1}, {2, 2}, {2, 3}};
    rowNum = sizeof(vertexs2) / sizeof(vertexs2[0]);

    // replace T2 (2, 1), (2, 2) (2, 3)
    ReplaceTableWithTwoProp(conn, stmt, "T2", vertexs2, rowNum);

    uint32_t sleepCnt = 0;
    while (pathNum != 1u) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 30) {
            printf("===========!!!ERROR, recvd time out, recvd = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    ASSERT_EQ(pathNum, 1u);
    printf("Path received: %d\n", pathNum);

    // drop subscription
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(syncStmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    const char *dropTriggers = R"(
        DROP TRIGGER t11;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
    DropPath5(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}
