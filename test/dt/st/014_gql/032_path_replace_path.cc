/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: st file for replace path
 * Author: GQL
 * Create: 2024-02-22
 */

#include <string>
#include <pthread.h>
#include "gmc_gql.h"
#include "client_option.h"
#include "client_common_st.h"
#include "fes_common.h"
#include "fes_tables_data_structure.h"

const uint32_t WAIT_TIME = 1000;  // ms
static DbMemCtxT *g_basicMem = NULL;
class PathReplacePath : public StClient {
protected:
    static void SetUpTestCase()
    {
        // FES PATH需要硬件加速提供的hash
        StartDbServer((char *)"gmserver_gql.ini");
        DbSleep(WAIT_TIME);  // Wait server ready
        st_clt_init();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(WAIT_TIME);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
        DbMemCtxArgsT args = {0};
        g_basicMem = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memory context", &args);
        ASSERT_NE(nullptr, g_basicMem);
    }

    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(g_basicMem);
        StopAndDestroyEpoll(responseEpollThreadId, &responseEpollFd);
        StopAndDestroyEpoll(timeoutEpollThreadId, &timeoutEpollFd);
        st_clt_uninit();
        ShutDownDbServer();
    }
};

static void PathUserCb(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    if (dataType == PATH_TYPE) {
        // 解析第一个4字节数目
        uint32_t pathNum = 0;
        Bytes2Uint32(data, &pathNum);
        *(uint32_t *)userData += pathNum;
    }
}

static void ReplaceXTest1(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_REPLACE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    FesTestTableWith3PropT obj = {
        .property1 = 1,
        .property2 = 1,
        .property3 = 1,
    };
    for (uint32_t i = 1; i <= 5; i++) {
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = i;
        /* prepare dml value */
        ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void ReplaceYTest1(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableY", GMC_OPERATION_REPLACE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    FesTestTableWith3PropT obj = {
        .property1 = 1,
        .property2 = 1,
        .property3 = 1,
    };
    for (uint32_t i = 1; i <= 5; i++) {
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = i * 10;
        /* prepare dml value */
        ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void CheckHeadTableTest1(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[5][3] = {{1, 1, 10}, {2, 2, 20}, {3, 3, 30}, {4, 4, 40}, {5, 5, 50}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(5u, cnt);
}

static void CreatePath1(GmcStmtT *stmt)
{
    const char *createBaseTables = R"(
        CREATE VERTEXLABEL tableX(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index2(property2)
        );
        CREATE VERTEXLABEL tableY (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index2(property2)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createBaseTables));

    const char *createEdge = R"(
        CREATE EDGE eXY
        FROM tableX TO tableY
        WHERE tableX.property2 == tableY.property2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    const char *createReplacePath = R"(
        CREATE PATH replacepath1 (
            MATCH (:tableX)-[:eXY]->(:tableY)
            RETURN
            (
                REPLACE tableX.property1 100, tableX.property2 100, tableX.property3 100
            )
            FOR REPLACE tableX
            SET property3 = property3
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createReplacePath));
}

static void DropPath1(GmcStmtT *stmt)
{
    const char *dropStuff = R"(
        DROP PATH replacepath1;
        DROP EDGE eXY;
        DROP VERTEXLABEL tableX;
        DROP VERTEXLABEL tableY;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
}

/*
 * 【Scenario 1】用例001：create
 * ReplacePath，tableX->tableY，向尾结点插入数据进行搜索生成path实例，检查pathnum和头表的数据是否符合预期 步骤：
 * 1、create ReplacePath，tableX->tableY，tableX.property2 == tableY.property2
 * 2、replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5),
 * 3、replace tableY (1,1,10)(2,2,20)(3,3,30)(4,4,40)(5,5,50),
 * 4、check path instance num:5
 * 5、check the data in tableX:(1,1,10)(2,2,20)(3,3,30)(4,4,40)(5,5,50),
 */
TEST_F(PathReplacePath, ReplacePathForSearch_001)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath1(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableY
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // replace tableY (1,1,10)(2,2,20)(3,3,30)(4,4,40)(5,5,50)
    ReplaceYTest1(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t expectCnt = 5;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,1,10)(2,2,20)(3,3,30)(4,4,40)(5,5,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest1(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    DropPath1(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath2(GmcStmtT *stmt)
{
    const char *createBaseTables = R"(
        CREATE VERTEXLABEL tableX(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL tableY (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createBaseTables));

    const char *createEdge = R"(
        CREATE EDGE eXY
        FROM tableX TO tableY
        WHERE tableX.property1 == tableY.property1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    const char *createReplacePath = R"(
        CREATE PATH replacepath1 (
            MATCH (:tableX)-[:eXY]->(:tableY)
            RETURN
            (
                REPLACE tableX.property1 100, tableX.property2 100, tableX.property3 100
            )
            FOR REPLACE tableX
            SET property2 = property2,
                property3 = property3
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createReplacePath));
}

static void ReplaceYTest2(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableY", GMC_OPERATION_REPLACE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    FesTestTableWith3PropT obj = {
        .property1 = 1,
        .property2 = 10,
        .property3 = 10,
    };
    for (uint32_t i = 1; i <= 5; i++) {
        obj.property1 = i;
        obj.property2 = i * 10;
        obj.property3 = i * 10;
        /* prepare dml value */
        ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void CheckHeadTableTest2(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[5][3] = {{1, 10, 10}, {2, 20, 20}, {3, 30, 30}, {4, 40, 40}, {5, 50, 50}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(5u, cnt);
}

/*
 * 【Scenario 2】用例002：create
 * ReplacePath，tableX->tableY，向头结点插入数据进行搜索生成path实例，检查pathnum和头表的数据是否符合预期 步骤：
 * 1、create ReplacePath，tableX->tableY，tableX.property1 == tableY.property1
 * 2、replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50),
 * 3、replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5),
 * 4、check path instance num:5
 * 5、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
 */
TEST_F(PathReplacePath, ReplacePathForSearch_002)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath2(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableX
        WHEN TRUE DO SEARCH replacepath1;

        CREATE TRIGGER t2
        ON REPLACE tableY
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    ReplaceYTest2(stmt);

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t expectCnt = 5;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest2(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
        DROP TRIGGER t2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    DropPath1(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath3(GmcStmtT *stmt)
{
    const char *createBaseTables = R"(
        CREATE VERTEXLABEL tableX(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index2(property3)
        );
        CREATE VERTEXLABEL tableY (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
            MULTI_HASH INDEX index2(property3)
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createBaseTables));

    const char *createEdge = R"(
        CREATE EDGE eXY
        FROM tableX TO tableY
        WHERE tableX.property3 == tableY.property3;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    const char *createReplacePath = R"(
        CREATE PATH replacepath1 (
            MATCH (:tableX)-[:eXY]->(:tableY)
            RETURN
            (
                REPLACE tableX.property1 100, tableX.property2 100, tableX.property3 100
            )
            FOR REPLACE tableX
            SET property2 = property2
        );
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createReplacePath));
}

static void CheckHeadTableTest3(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[5][3] = {{1, 1, 1}, {2, 2, 2}, {3, 3, 3}, {4, 4, 4}, {5, 5, 5}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(5u, cnt);
}

/*
 * 【Scenario 3】用例003：create
 * ReplacePath，tableX->tableY，向头结点插入数据进行搜索但是没有生成path实例，检查pathnum和头表的数据是否符合预期 步骤：
 * 1、create ReplacePath，tableX->tableY，tableX.property3 == tableY.property3
 * 2、replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50),
 * 3、replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5),
 * 4、check path instance num:0
 * 5、check the data in tableX:(1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
 */
TEST_F(PathReplacePath, ReplacePathForSearch_003)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath3(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableX
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    ReplaceYTest2(stmt);

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t expectCnt = 0;
    DbSleep(100);
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest3(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    DropPath1(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath4(GmcStmtT *stmt)
{
    const char *createBaseTables = R"(
        CREATE VERTEXLABEL tableX(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL tableY (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL v1(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createBaseTables));

    const char *createEdge = R"(
        CREATE EDGE eXY
            FROM tableX TO tableY
            WHERE tableX.property1 == tableY.property1;
        CREATE EDGE e1X
            FROM v1 TO tableX
            WHERE v1.property1 == tableX.property1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    const char *createReplacePath = R"(
        CREATE PATH replacepath1 (
            MATCH (:tableX)-[:eXY]->(:tableY)
            RETURN
            (
                REPLACE tableX.property1 100, tableX.property2 100, tableX.property3 100
            )
            FOR REPLACE tableX
            SET property2 = property2,
                property3 = property3
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createReplacePath));

    const char *createNormalPath = R"(
        CREATE PATH normalpath1(
            MATCH 
            (:v1)-[:e1X]->(:tableX)
            RETURN
            (
                REPLACE v1.property1 100, v1.property2 100, v1.property3 100, tableX.property1 200, tableX.property2 200, tableX.property3 200
            )
        )
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createNormalPath));
}

static void DropPath4(GmcStmtT *stmt)
{
    const char *dropStuff = R"(
        DROP PATH replacepath1;
        DROP PATH normalpath1;
        DROP EDGE eXY;
        DROP EDGE e1X;
        DROP VERTEXLABEL tableX;
        DROP VERTEXLABEL tableY;
        DROP VERTEXLABEL v1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
}

static void ReplaceV1Test4(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "v1", GMC_OPERATION_REPLACE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    FesTestTableWith3PropT obj = {
        .property1 = 1,
        .property2 = 1,
        .property3 = 10,
    };
    for (uint32_t i = 1; i <= 5; i++) {
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = i * 10;
        /* prepare dml value */
        ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void CheckHeadTableTest4(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[5][3] = {{1, 10, 10}, {2, 20, 20}, {3, 30, 30}, {4, 40, 40}, {5, 50, 50}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(5u, cnt);
}

/*
 * 【Scenario 4】用例004：创建ReplacePath 和 normalPath，normalPath包含ReplacePath的头结点,创建replace触发器，
            发送replace dml，检查pathNum和tlv是否符合预期
 * 步骤：
 * 1、ReplacePath 和 normalPath，normalPath包含ReplacePath的头结点
 * 2、replace v1 (1,1,10)(2,2,20)(3,3,30)(4,4,40)(5,5,50)
 * 3、replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
 * 4、replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
 * 5、check the data in tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50), check the pathNum：5,pathNum2：5
 */
TEST_F(PathReplacePath, ReplacePathForSearch_004)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath4(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));

    channelName = "channel2";
    GmcConnT *channel2 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel2, channelName));

    uint32_t pathNum = 0;
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    uint32_t pathNum2 = 0;
    createSub = R"(CREATE SUBSCRIPTION sub2 ON PATH
    normalpath1
    BY CHANNEL channel2;)";
    GmcSubUdfT udf2 = {.userCb = PathUserCb, .userData = &pathNum2};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf2));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableX
        WHEN TRUE DO SEARCH replacepath1,normalpath1;

        CREATE TRIGGER t2
        ON REPLACE tableY
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace v1 (1,1,10)(2,2,20)(3,3,30)(4,4,40)(5,5,50)
    ReplaceV1Test4(stmt);

    // replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    ReplaceYTest2(stmt);

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t expectCnt = 5;
    while ((pathNum != expectCnt) || (pathNum2 != expectCnt)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);
    EXPECT_EQ(expectCnt, pathNum2);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest4(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
        DROP TRIGGER t2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    dropSub = R"(
        DROP SUBSCRIPTION sub2;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel2));
    DropPath4(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath5(GmcStmtT *stmt)
{
    const char *createBaseTables = R"(
        CREATE VERTEXLABEL tableX(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL tableY (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createBaseTables));

    const char *createEdge = R"(
        CREATE EDGE eXY
        FROM tableX TO tableY
        WHERE tableX.property1 == tableY.property1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    const char *createReplacePath = R"(
        CREATE PATH replacepath1 (
            MATCH (:tableX)-[:eXY]->(:tableY)
            RETURN
            (
                REPLACE tableX.property1 100, tableX.property2 100, tableX.property3 100, tableY.property1 200, tableY.property2 200, tableY.property3 200
                DELETE tableX.property1 10, tableX.property2 10, tableX.property3 10, tableY.property1 20, tableY.property2 20, tableY.property3 20
            )
            WITH MODE 3
            FOR REPLACE tableX
            SET property2 = property2,
                property3 = property3
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createReplacePath));
}

static void DeleteYTest5(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableY", GMC_OPERATION_DELETE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    uint32_t pkValue = 0;
    for (uint32_t i = 3; i <= 5; i++) {
        pkValue = i;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void CheckTailTableTest5(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableY", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[2][3] = {{1, 10, 10}, {2, 20, 20}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(2u, cnt);
}

/*
 * 【Scenario 5】用例005：create
 * ReplacePath，tableX->tableY，删除尾结点数据数据触发搜索搜索生成path实例，检查检查pathnum和头表的数据是否符合预期
 * 步骤：
 * 1、create ReplacePath，tableX->tableY，tableX.property1 == tableY.property1, set delete mode 3
 * 2、replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5),
 * 3、replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50),
 * 4、check path instance num:5
 * 5、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
 * 6、delete tableY (3,30,30)(4,40,40)(5,50,50), check path instance num: 8
 * 7、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
 */
TEST_F(PathReplacePath, ReplacePathForSearch_005)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath5(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));

    uint32_t pathNum = 0;
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableY
        WHEN TRUE DO SEARCH replacepath1;

        CREATE TRIGGER t2
        ON DELETE tableY
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    ReplaceYTest2(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t expectCnt = 5;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest4(stmt);

    // delete tableY (3,30,30)(4,40,40)(5,50,50)
    DeleteYTest5(stmt);
    // check the data in Tailtable:tableY (1,10,10)(2,20,20)
    CheckTailTableTest5(stmt);

    expectCnt = 8;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest4(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
        DROP TRIGGER t2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DropPath1(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CheckHeadTableTest6(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[3][3] = {{1, 10, 10}, {2, 20, 20}, {3, 30, 30}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(3u, cnt);
}

static void DeleteXTest6(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_DELETE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    uint32_t pkValue = 0;
    for (uint32_t i = 4; i <= 5; i++) {
        pkValue = i;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

/*
 * 【Scenario 5 - 2】用例006：create
 * ReplacePath，tableX->tableY，删除头结点数据触发搜索搜索生成path实例，检查头表的数据是否符合预期 步骤： 1、create
 * ReplacePath，tableX->tableY，tableX.property1 == tableY.property1，set delete mode 3 2、replace tableX
 * (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5), 3、replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50), 4、check path
 * instance num:5 5、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50) 6、delete tableX
 * (4,40,40)(5,50,50), 7、check the pathNum：7 8、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)
 */
TEST_F(PathReplacePath, ReplacePathForSearch_006)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath5(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));

    uint32_t pathNum = 0;
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableX
        WHEN TRUE DO SEARCH replacepath1;

        CREATE TRIGGER t2
        ON REPLACE tableY
        WHEN TRUE DO SEARCH replacepath1;

        CREATE TRIGGER t3
        ON DELETE tableX
        WHEN TRUE DO SEARCH replacepath1;

        CREATE TRIGGER t4
        ON DELETE tableY
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // replace tableY (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    ReplaceYTest2(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t expectCnt = 5;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest4(stmt);

    // delete tableX (4,40,40)(5,50,50)
    DeleteXTest6(stmt);

    expectCnt = 7;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest6(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
        DROP TRIGGER t2;
        DROP TRIGGER t3;
        DROP TRIGGER t4;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DropPath1(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}

static void CreatePath7(GmcStmtT *stmt)
{
    const char *createBaseTables = R"(
        CREATE VERTEXLABEL tableX(
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL tableY (
            property1 UINT32,
            property2 UINT16
            PRIMARY INDEX pk(property1)
        );
        CREATE VERTEXLABEL tableZ (
            property1 UINT32,
            property2 UINT32,
            property3 UINT16
            PRIMARY INDEX pk(property1)
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createBaseTables));

    const char *createEdge = R"(
        CREATE EDGE eXY
        FROM tableX TO tableY
        WHERE tableX.property1 == tableY.property1;
        CREATE EDGE eYZ
        FROM tableY TO tableZ
        WHERE tableY.property1 == tableZ.property1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    const char *createReplacePath = R"(
        CREATE PATH replacepath1 (
            MATCH (:tableX)-[:eXY]->(:tableY)-[:eYZ]->(:tableZ)
            RETURN
            (
                REPLACE tableX.property1 100, tableX.property2 100, tableX.property3 100
            )
            FOR REPLACE tableX
            SET property2 = property2,
                property3 = property3
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createReplacePath));
}

static void DropPath7(GmcStmtT *stmt)
{
    const char *dropStuff = R"(
        DROP PATH replacepath1;
        DROP EDGE eXY;
        DROP EDGE eYZ;
        DROP VERTEXLABEL tableX;
        DROP VERTEXLABEL tableY;
        DROP VERTEXLABEL tableZ;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
}

static void ReplaceYTest7(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableY", GMC_OPERATION_REPLACE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    FesTestTableWith2PropT obj = {
        .property1 = 1,
        .property2 = 1,
    };
    for (uint32_t i = 1; i <= 5; i++) {
        obj.property1 = i;
        obj.property2 = i;
        /* prepare dml value */
        ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void ReplaceZTest7(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableZ", GMC_OPERATION_REPLACE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    FesTestTableWith3PropT obj = {
        .property1 = 1,
        .property2 = 10,
        .property3 = 10,
    };
    for (uint32_t i = 1; i <= 5; i++) {
        obj.property1 = i;
        obj.property2 = i * 10;
        obj.property3 = i * 10;
        /* prepare dml value */
        ret = GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void DeleteXTest7(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_DELETE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    uint32_t pkValue = 5;
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t)));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void DeleteZTest7(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "tableZ", GMC_OPERATION_DELETE));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = MODEL_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    uint32_t pkValue = 5;
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t)));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    /* execute */
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void CheckHeadTableTest7(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableX", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[4][3] = {{1, 10, 10}, {2, 20, 20}, {3, 30, 30}, {4, 40, 40}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(4u, cnt);
}

static void CheckTailTableTest7(GmcStmtT *stmt)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, "tableZ", GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t properties[4][3] = {{1, 10, 10}, {2, 20, 20}, {3, 30, 30}, {4, 40, 40}};
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t property1 = 0;
        uint32_t property2 = 0;
        uint16_t property3 = 0;
        uint32_t size = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][0], property1);
        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][1], property2);
        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(properties[cnt][2], property3);
        cnt++;
    }
    ASSERT_EQ(4u, cnt);
}

/*
 * 【Scenario 5 - 3】用例007：create
 * ReplacePath，tableX->tableY，删除头结点数据触发搜索搜索生成path实例，检查头表的数据是否符合预期 步骤： 1、create
 * ReplacePath，tableX->tableY->tableZ，tableX.property1 == tableY.property1，tableY.property1 == tableZ.property1
 * 2、replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5),
 * 3、replace tableY (1,1)(2,2)(3,3)(4,4)(5,5),
 * 4、replace tableZ (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50),
 * 5、check path instance num:5
 * 6、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
 * 7、delete tableX (5,50,50),
 * 8、check the data in tableX:(1,10,10)(2,20,20)(3,30,30)(4,40,40)
 * 9、delete tableZ (5,50,50)
 * 10、check the data in tableZ:(1,10,10)(2,20,20)(3,30,30)(4,40,40)
 */
TEST_F(PathReplacePath, ReplacePathForSearch_007)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    CreatePath7(stmt);

    // create subscription
    const char *channelName = "channel1";
    GmcConnT *channel1 = NULL;
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&channel1, channelName));

    uint32_t pathNum = 0;
    const char *createSub = R"(CREATE SUBSCRIPTION sub1 ON PATH
    replacepath1
    BY CHANNEL channel1;)";
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub, &udf));

    const char *triggers4BaseTables = R"(
        CREATE TRIGGER t1
        ON REPLACE tableX
        WHEN TRUE DO SEARCH replacepath1;

        CREATE TRIGGER t2
        ON REPLACE tableZ
        WHEN TRUE DO SEARCH replacepath1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, triggers4BaseTables));

    // replace tableX (1,1,1)(2,2,2)(3,3,3)(4,4,4)(5,5,5)
    ReplaceXTest1(stmt);

    // replace tableY (1,1)(2,2)(3,3)(4,4)(5,5)
    ReplaceYTest7(stmt);

    // replace tableZ (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    ReplaceZTest7(stmt);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t expectCnt = 5;
    while (pathNum != expectCnt) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            printf("===========!!!ERROR, time out for wait, pathNum = %u\n", pathNum);
            break;
        }
    }
    EXPECT_EQ(expectCnt, pathNum);

    // check the data in Headtable:tableX (1,10,10)(2,20,20)(3,30,30)(4,40,40)(5,50,50)
    DbSleep(100);  // 插入数据与检查数据的时间间隔太近，为防止数据未及时写入
    CheckHeadTableTest4(stmt);

    // delete tableX (5,50,50)
    DeleteXTest7(stmt);
    CheckHeadTableTest7(stmt);

    // delete tableZ (5,50,50)
    DeleteZTest7(stmt);
    CheckTailTableTest7(stmt);

    const char *dropStuff = R"(
        DROP TRIGGER t1;
        DROP TRIGGER t2;
    )";
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropStuff));
    const char *dropSub = R"(
        DROP SUBSCRIPTION sub1;
    )";
    EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(channel1));
    DropPath7(stmt);
    DestroyConnectionAndStmt(conn, stmt);
}
