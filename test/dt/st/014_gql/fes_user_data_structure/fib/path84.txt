CREATE PATH P84 (
    MATCH
    (:T7)-[:E183|:E84302|:E5]->(
        (:T37)-[:E85640]->(:T10)|
        (V3452:T3452)-[:E84301]->(V36:T36)-[:E108|:E203423|:E107]->(
            (:T1)-[:E83031]->(:T279)|
            (:T359)-[:E83013]->(:T357)|
            (NULL)
        ),
        (V36:T36)-[:E83557|:E86614|:E83169|:E121|:E82498|:E84110]->(
            (:T3378)|
            (:T3860)|
            (:T2169)|
            (:T279)|
            (:T2726)|
            (:T2153)
        ),
        (V3452:T3452)-[:E203319]->(:T4171)|
        (V9:T9)-[:E2]->(:T10),
        (V9:T9)-[:E13|:E3|:E81146]->(
            (NULL)|
            (:T10)|
            (:T2009)-[:E81132|:E87797|:E81151|:E81152|:E83240|:E81401|:E82449]->(
                (:T2337)-[:E81133]->(:T2336)|
                (:T279)|
                (:T2010)|
                (:T2011)|
                (:T2869)|
                (:T2563)|
                (:T2745)
            )
        )
    )
    RETURN
    (
        REPLACE T7.uiVrIndex 300, T357.uiPdtInfo 660, T357.usVlanId 660, T357.uiVrId 660, T9.uiPrimaryLable 18, T9.uiBackupIID 18, T9.uiBackupLabel 18, T9.uiGIIDIndex 18, T9.uiAttributeID 18, T9.uiPrimaryIID 18, T2010.uiRingIndex 4832, T2010.uiNRBIndex 4832, T3378.ucSRPrefer 5509
    )
);
