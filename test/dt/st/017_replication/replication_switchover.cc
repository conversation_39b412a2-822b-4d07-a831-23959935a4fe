/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: replication_switchover.cc
 * Description:
 * Create: 2025-02-22
 */

#include <sys/epoll.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "gmc_test.h"
#include "gmc_persist.h"
#include "InitClt.h"
#include "test_ha.h"
#include "client_common_st.h"

static const char *g_serverLocator1 = "usocket:/run/verona/unix_emserver1";
static const char *g_serverLocator2 = "usocket:/run/verona/unix_emserver2";
static const char *g_url1 = "tcp:host=127.0.0.1,port=5757";
static const char *g_url2 = "tcp:host=127.0.0.1,port=5858";

static pthread_t g_responseEpollThreadId;
static int g_responseEpollFd;

static int32_t EpollReg(int fd, GmcEpollCtlTypeE type)
{
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = EPOLLIN;
            return epoll_ctl(g_responseEpollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(g_responseEpollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

class SwitchOver : public testing::Test {
protected:
    virtual void SetUp()
    {
        GmcDetachAllShmSeg();
        system("./gmserver1 -b -p gmserver1.ini");
        system("./gmserver2 -b -p gmserver2.ini");
        sleep(2);
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator1, GMC_DB_ROLE_MASTER, g_url1));
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url2));

        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待主机可访问
        ASSERT_EQ(GMERR_OK, CSModeConnectWrapperSetTimeOut(
                                GMC_CONN_TYPE_SYNC, g_serverLocator1, "017_replication", "conn1", 10000, &conn1));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn1, &stmt1));
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备可访问
        ASSERT_EQ(GMERR_OK, CSModeConnectWrapperSetTimeOut(
                                GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", 10000, &conn2));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));

        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url2));
    }

    virtual void TearDown()
    {
        DestroyConnectionAndStmt(conn1, stmt1);
        DestroyConnectionAndStmt(conn2, stmt2);
        ASSERT_EQ(GMERR_OK, GmcSlaveOffline(g_serverLocator1));
        system("kill -9 `pidof gmserver2`");
        system("kill -9 `pidof gmserver1`");

        system("rm -rf /data/gmdb*");
        system("ipcrm -a");
    }

    static void SetUpTestCase()
    {
        system("kill -9 `pidof gmserver`");
        system("kill -9 `pidof gmserver1`");
        system("kill -9 `pidof gmserver2`");
        system("rm -rf /data/gmdb*");
        system("rm -rf /dev/shm/_run_verona_unix_emserver*");
        system("rm -f gmserver1");
        system("rm -f gmserver2");
        system("ln -s ../../../../output/euler/aarch64/bin/gmserver gmserver1; chmod 777 gmserver1");
        system("ln -s ../../../../output/euler/aarch64/bin/gmserver gmserver2; chmod 777 gmserver2");

        GmcInit();
        CreateAndStartEpoll(&g_responseEpollThreadId, &g_responseEpollFd);
        ASSERT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(EpollReg));
        ASSERT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(EpollReg));
    }

    static void TearDownTestCase()
    {
        GmcUnInit();
        StopAndDestroyEpoll(g_responseEpollThreadId, &g_responseEpollFd);
        system("kill -9 `pidof gmserver1`");
        system("kill -9 `pidof gmserver2`");
        system("ipcrm -a");
    }

    void SwitchOver2()
    {
        DestroyConnectionAndStmt(conn1, stmt1);
        system("kill -9 `pidof gmserver1`");
        GmcUnInitByInstance(1);

        ASSERT_EQ(GMERR_OK, GmcToMaster(g_serverLocator2));
        sleep(2);
        system("./gmserver1 -b -p gmserver1.ini");
        sleep(2);
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator1, GMC_DB_ROLE_SLAVE, g_url1));
        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator2, g_url1));

        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待主机可访问
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator1, "017_replication", "conn1", &conn1));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn1, &stmt1));
    }

    void SwitchOver1()
    {
        DestroyConnectionAndStmt(conn2, stmt2);
        system("kill -9 `pidof gmserver2`");
        sleep(2);
        GmcUnInitByInstance(2);

        ASSERT_EQ(GMERR_OK, GmcToMaster(g_serverLocator1));

        system("./gmserver2 -b -p gmserver2.ini");
        sleep(2);
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url2));
        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url2));

        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待主机可访问
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", &conn2));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));
    }

    void Reboot2()
    {
        DestroyConnectionAndStmt(conn2, stmt2);
        system("kill -9 `pidof gmserver2`");
        sleep(2);
        GmcUnInitByInstance(2);
        system("./gmserver2 -b -p gmserver2.ini");
        ASSERT_EQ(GMERR_OK, GmcSlaveOffline(g_serverLocator1));
        sleep(2);
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url2));
        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url2));

        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备机可访问
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", &conn2));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));
    }

    void RebootAll()
    {
        DestroyConnectionAndStmt(conn1, stmt1);
        DestroyConnectionAndStmt(conn2, stmt2);
        system("kill -9 `pidof gmserver1`");
        system("kill -9 `pidof gmserver2`");
        sleep(2);
        GmcUnInitByInstance(1);
        GmcUnInitByInstance(2);
        system("./gmserver1 -b -p gmserver1.ini");
        system("./gmserver2 -b -p gmserver2.ini");
        sleep(2);
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator1, GMC_DB_ROLE_MASTER, g_url1));
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url2));
        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url2));
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备机可访问
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备机可访问
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator1, "017_replication", "conn1", &conn1));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn1, &stmt1));
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", &conn2));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));
    }

    void Reboot12()
    {
        DestroyConnectionAndStmt(conn1, stmt1);
        DestroyConnectionAndStmt(conn2, stmt2);
        system("kill -9 `pidof gmserver1`");
        system("kill -9 `pidof gmserver2`");
        sleep(2);
        GmcUnInitByInstance(1);
        GmcUnInitByInstance(2);
        system("sh start_gmserver.sh");
        system("./gmserver2 -b -p gmserver2.ini");
        sleep(2);
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator1, GMC_DB_ROLE_MASTER, g_url1));
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url2));
        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url2));
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备机可访问
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备机可访问
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator1, "017_replication", "conn1", &conn1));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn1, &stmt1));
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", &conn2));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));
    }

protected:
    GmcConnT *conn1 = NULL, *conn2 = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
};

static void CommonAsyncDDLCb(void *userData, int32_t status, const char *errMsg)
{
    ASSERT_EQ(GMERR_OK, status);
    uint32_t *received = reinterpret_cast<uint32_t *>(userData);
    (*received)++;
}

// 建连
static void CreateAsyncConnAndStmt(const char *serverLocator, GmcConnT **conn, GmcStmtT **stmt)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    GmcConnOptionsT *connOptions;
    // 设置异步连接的选项
    EXPECT_EQ(GMERR_OK, GmcConnOptionsCreate(&connOptions));
    EXPECT_EQ(GMERR_OK, GmcConnOptionsSetServerLocator(connOptions, serverLocator));
    EXPECT_EQ(GMERR_OK, GmcConnOptionsSetEpollRegFunc(connOptions, EpollReg));
    EXPECT_EQ(GMERR_OK, GmcConnect(GMC_CONN_TYPE_ASYNC, connOptions, &tmpConn));
    GmcConnOptionsDestroy(connOptions);
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(tmpConn, &tmpStmt));

    *conn = tmpConn;
    *stmt = tmpStmt;
}

// 交互式小事务中发生主备倒换（强一致的 Vertex)
// 在新主删表
TEST_F(SwitchOver, TX01)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    SwitchOver2();
    ASSERT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt2, labelName));
}

// 交互式小事务中发生主备倒换（强一致的 Kv）
// 倒换回原主删表
TEST_F(SwitchOver, TX02)
{
    const char *tableName = "KV514";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, tableName, configJson));
    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));
    SwitchOver2();
    ASSERT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, GmcTransCommit(conn1));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, tableName));
    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    SwitchOver1();
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, tableName));
}

// 交互式大事务中发生主备倒换（强一致的 Vertex）
// 倒换回原主删表
TEST_F(SwitchOver, TX03)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 5000;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    SwitchOver2();
    ASSERT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    SwitchOver1();
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 交互式大事务中发生主备倒换（强一致的 Kv）
// 在新主删表
TEST_F(SwitchOver, TX04)
{
    const char *tableName = "KV514";
    const char *configJson = R"({"max_record_count":100000, "replication":2})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, tableName, configJson));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    for (uint32_t i = 0; i < 10000; i++) {
        uint32_t key = 1000 + i, value = 2000 + i;
        ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));
    }

    SwitchOver2();
    ASSERT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, GmcTransCommit(conn1));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, tableName));

    for (uint32_t i = 0; i < 10000; i++) {
        uint32_t key = 1000 + i, value = 0;
        uint32_t valueLen = sizeof(value);
        ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    }

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt2, tableName));
}

// 全量复制过程中发生主备倒换(NameSpace)
TEST_F(SwitchOver, Full01)
{
    for (uint32_t i = 0; i < 50; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "ns%" PRIu32 "", i);
        ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, name, "FullReplication"));
    }
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    SwitchOver2();
    sleep(1);
    // 2->1全量复制h过程中倒换到1
    SwitchOver1();
    for (uint32_t i = 0; i < 50; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "ns%" PRIu32 "", i);
        (void)GmcDropNamespace(stmt1, name);
        ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, name, "FullReplication"));
    }

    for (uint32_t i = 0; i < 50; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "ns%" PRIu32 "", i);
        ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, name));
    }
}

// 全量复制过程中发生主备倒换(VertexLabel)
TEST_F(SwitchOver, Full02)
{
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    uint32_t count = 500;
    for (uint32_t i = 0; i < count; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "T%" PRIu32 "", i);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, name));
    }

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    SwitchOver2();
    sleep(1);
    // 2->1全量复制h过程中倒换到1
    SwitchOver1();

    for (uint32_t i = 0; i < count; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "T%" PRIu32 "", i);
        (void)GmcDropVertexLabel(stmt1, name);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, name));
    }

    for (uint32_t i = 0; i < count; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "T%" PRIu32 "", i);
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, name));
    }
}

// 全量复制过程中发生主备倒换(KvLabel)
TEST_F(SwitchOver, Full03)
{
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    for (uint32_t i = 0; i < 1000; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "T%" PRIu32 "", i);
        ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, name, configJson));
    }

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    SwitchOver2();
    sleep(1);
    // 2->1全量复制h过程中倒换到1
    SwitchOver1();

    for (uint32_t i = 0; i < 1000; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "T%" PRIu32 "", i);
        (void)GmcKvDropTable(stmt1, name);
        ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, name, configJson));
    }

    for (uint32_t i = 0; i < 1000; i++) {
        char name[100] = {};
        sprintf_s(name, sizeof(name), "T%" PRIu32 "", i);
        ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, name));
    }
}

// 全量复制过程中发生主备倒换(Vertex)
TEST_F(SwitchOver, Full04)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    uint32_t count = 10000;
    for (uint32_t i = 0; i < count; i++) {
        int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    SwitchOver2();
    sleep(3);
    // 2->1全量复制h过程中倒换到1
    SwitchOver1();
    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt1, labelName, NULL, &count2));
    ASSERT_GT(count2, 0);
}

// 后台Truncate的过成中发生主备倒换
TEST_F(SwitchOver, Truncate01)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10000;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));
    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(count, count2);
    ASSERT_EQ(GMERR_OK, GmcDeleteAllFast(stmt1, labelName));
    SwitchOver2();
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt2, labelName));
}

// 异步Truncate NameSpace的过程中发生主备倒换
TEST_F(SwitchOver, TruncateNamespace)
{
    const char *nameSpace = "replication";
    const char *userName = "userXXX";
    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));

    const char *configJson = R"({"max_record_count":10000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    uint64_t labelNum = 100;
    for (uint32_t i = 0; i < labelNum; i++) {
        char labelName[100] = {};
        sprintf_s(labelName, sizeof(labelName), "XT%" PRIu32, i);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
        const int32_t f0 = 0 + i, f1 = 1 + i, f2 = 2 + i, f3 = 3 + i;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    for (uint32_t i = 0; i < labelNum; i++) {
        char tableName[100] = {};
        sprintf_s(tableName, sizeof(tableName), "KV%" PRIu32, i);
        ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, tableName, configJson));
        ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));
        uint32_t key = 1000 + i, value = 2000 + i;
        ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));
    }

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    CreateAsyncConnAndStmt(g_serverLocator1, &connAsync, &stmtAsync);
    uint32_t received = 0;
    ASSERT_EQ(GMERR_OK, GmcTruncateNamespaceAsync(stmtAsync, nameSpace, CommonAsyncDDLCb, &received));
    sleep(1);
    SwitchOver2();
    DestroyConnectionAndStmt(connAsync, stmtAsync);
    CreateAsyncConnAndStmt(g_serverLocator2, &connAsync, &stmtAsync);

    // TruncateNamespace现在是一个表一个事务，不支持真正的原子性,但是需要能够继续处理
    ASSERT_EQ(GMERR_OK, GmcTruncateNamespaceAsync(stmtAsync, nameSpace, CommonAsyncDDLCb, &received));
    WAIT_WHILE(received != 2);
    DestroyConnectionAndStmt(connAsync, stmtAsync);
    ASSERT_EQ(GMERR_OK, GmcClearNamespace(stmt2, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt2, nameSpace));
}

// 异步Clear NameSpace的过程中发生主备倒换
TEST_F(SwitchOver, ClearNamespace)
{
    const char *nameSpace = "replication";
    const char *userName = "userXXX";
    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));

    const char *configJson = R"({"max_record_count":10000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    uint64_t labelNum = 100;
    for (uint32_t i = 0; i < labelNum; i++) {
        char labelName[100] = {};
        sprintf_s(labelName, sizeof(labelName), "XT%" PRIu32, i);
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
        const int32_t f0 = 0 + i, f1 = 1 + i, f2 = 2 + i, f3 = 3 + i;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    for (uint32_t i = 0; i < labelNum; i++) {
        char tableName[100] = {};
        sprintf_s(tableName, sizeof(tableName), "KV%" PRIu32, i);
        ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, tableName, configJson));
        ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));
        uint32_t key = 1000 + i, value = 2000 + i;
        ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));
    }

    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));

    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    CreateAsyncConnAndStmt(g_serverLocator1, &connAsync, &stmtAsync);
    uint32_t received = 0;
    ASSERT_EQ(GMERR_OK, GmcClearNamespaceAsync(stmtAsync, nameSpace, CommonAsyncDDLCb, &received));
    sleep(1);
    SwitchOver2();
    DestroyConnectionAndStmt(connAsync, stmtAsync);

    // ClearNamespace现在是一个表一个事务，不支持真正的原子性,但是需要能够继续处理
    ASSERT_EQ(GMERR_OK, GmcClearNamespace(stmt2, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt2, nameSpace));
}

// 反复主备倒换10次
TEST_F(SwitchOver, Repeat01)
{
    const char *userName = "userXXX";

    const char *configJson1 = R"({"max_record_count":10000, "replication":1})";
    const char *configJson2 = R"({"max_record_count":10000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    for (uint32_t i = 0; i < 10; i++) {
        char nameSpace[100] = {};
        sprintf_s(nameSpace, sizeof(nameSpace), "ns-%u", i);
        ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));

        for (uint32_t j = 0; j < 10; j++) {
            char labelName[100] = {};
            char kvName[100] = {};
            sprintf_s(labelName, sizeof(labelName), "vl-%u", j);
            sprintf_s(kvName, sizeof(kvName), "kv-%u", j);
            if (j % 2 == 0) {
                ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson1, labelName));
                ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, kvName, configJson1));
            } else {
                ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson2, labelName));
                ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, kvName, configJson2));
            }

            for (uint32_t k = 0; k < 1000; k++) {
                ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, kvName));
                uint32_t key = 1000 + k, value = 2000 + k;
                ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

                ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
                const int32_t f0 = 0 + k, f1 = 1 + k, f2 = 2 + k, f3 = 3 + k;
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
                ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
            }
        }
    }

    for (uint32_t i = 0; i < 10; i++) {
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20));
        SwitchOver2();
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20));
        SwitchOver1();
    }

    for (uint32_t i = 0; i < 10; i++) {
        char nameSpace[100] = {};
        sprintf_s(nameSpace, sizeof(nameSpace), "ns-%u", i);
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
        for (uint32_t j = 0; j < 10; j++) {
            char labelName[100] = {};
            char kvName[100] = {};
            sprintf_s(labelName, sizeof(labelName), "vl-%u", j);
            sprintf_s(kvName, sizeof(kvName), "kv-%u", j);
            uint64_t count = 0;
            ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt1, labelName, NULL, &count));
            ASSERT_EQ(1000, count);
            uint32_t count2 = 0;
            ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, kvName));
            ASSERT_EQ(GMERR_OK, GmcKvTableRecordCount(stmt1, &count2));
            ASSERT_EQ(1000, count2);
            ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
            ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, kvName));
        }
        ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
    }
}

// 备机反复上线下线10次
TEST_F(SwitchOver, Repeat02)
{
    const char *userName = "userXXX";

    const char *configJson1 = R"({"max_record_count":10000, "replication":1})";
    const char *configJson2 = R"({"max_record_count":10000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    for (uint32_t i = 0; i < 10; i++) {
        char nameSpace[100] = {};
        sprintf_s(nameSpace, sizeof(nameSpace), "ns-%u", i);
        ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));

        for (uint32_t j = 0; j < 10; j++) {
            char labelName[100] = {};
            char kvName[100] = {};
            sprintf_s(labelName, sizeof(labelName), "vl-%u", j);
            sprintf_s(kvName, sizeof(kvName), "kv-%u", j);
            if (j % 2 == 0) {
                ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson1, labelName));
                ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, kvName, configJson1));
            } else {
                ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson2, labelName));
                ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, kvName, configJson2));
            }

            for (uint32_t k = 0; k < 1000; k++) {
                ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, kvName));
                uint32_t key = 1000 + k, value = 2000 + k;
                ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

                ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
                const int32_t f0 = 0 + k, f1 = 1 + k, f2 = 2 + k, f3 = 3 + k;
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
                ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
            }
        }
    }

    for (uint32_t i = 0; i < 10; i++) {
        Reboot2();
        sleep(2);
    }
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20));

    for (uint32_t i = 0; i < 10; i++) {
        char nameSpace[100] = {};
        sprintf_s(nameSpace, sizeof(nameSpace), "ns-%u", i);
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
        for (uint32_t j = 0; j < 10; j++) {
            char labelName[100] = {};
            char kvName[100] = {};
            sprintf_s(labelName, sizeof(labelName), "vl-%u", j);
            sprintf_s(kvName, sizeof(kvName), "kv-%u", j);
            uint64_t count = 0;
            ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt1, labelName, NULL, &count));
            ASSERT_EQ(1000, count);
            uint32_t count2 = 0;
            ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, kvName));
            ASSERT_EQ(GMERR_OK, GmcKvTableRecordCount(stmt1, &count2));
            ASSERT_EQ(1000, count2);
            ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
            ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, kvName));
        }
        ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
    }
}

// 主备双复位
// KV还不支持持久化
TEST_F(SwitchOver, RebootAll)
{
    const char *userName = "userXXX";

    const char *configJson1 = R"({"max_record_count":10000, "replication":1})";
    const char *configJson2 = R"({"max_record_count":10000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    for (uint32_t i = 0; i < 10; i++) {
        char nameSpace[100] = {};
        sprintf_s(nameSpace, sizeof(nameSpace), "ns-%u", i);
        ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));

        for (uint32_t j = 0; j < 10; j++) {
            char labelName[100] = {};
            sprintf_s(labelName, sizeof(labelName), "vl-%u", j);
            if (j % 2 == 0) {
                ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson1, labelName));
            } else {
                ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson2, labelName));
            }

            for (uint32_t k = 0; k < 1000; k++) {
                ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
                const int32_t f0 = 0 + k, f1 = 1 + k, f2 = 2 + k, f3 = 3 + k;
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
                ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
                ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
            }
        }
        GmcFlushData(stmt1, NULL, GMC_DATABASE_BACKUP_FULL);
    }
    RebootAll();
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 30));

    for (uint32_t i = 0; i < 10; i++) {
        char nameSpace[100] = {};
        sprintf_s(nameSpace, sizeof(nameSpace), "ns-%u", i);
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
        ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt2, nameSpace));
        for (uint32_t j = 0; j < 10; j++) {
            char labelName[100] = {};
            sprintf_s(labelName, sizeof(labelName), "vl-%u", j);
            uint64_t count = 0;
            ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt1, labelName, NULL, &count));
            ASSERT_EQ(1000, count);
            ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count));
            ASSERT_EQ(1000, count);
            ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
        }
        ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
    }
}

// 主备倒换+主键自增
TEST_F(SwitchOver, AutoIncrement01)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 100; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 100; i < 200; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 200; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 200; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 反复主备倒换+主键自增
TEST_F(SwitchOver, AutoIncrement02)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 100; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 100; i < 200; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    SwitchOver1();
    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 200; i < 300; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    for (uint32_t i = 0; i < 300; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 300; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 主备倒换+主键自增+update操作(测试update变replace后备机自增元数据是否保持不变)
TEST_F(SwitchOver, AutoIncrement03)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 100; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_UPDATE));
    for (uint32_t i = 0; i < 50; i++) {
        f3 = 666, f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 100; i < 200; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 200; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 200; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 主备倒换+主键自增+truncate操作(测试truncate后备机自增元数据是否被清理)
TEST_F(SwitchOver, AutoIncrement04)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 100; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt1, labelName));

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 100; i < 200; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 100; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 100; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 主备倒换+主键自增+事务回滚(测试备机自增元数据是否正常回滚)
TEST_F(SwitchOver, AutoIncrement05)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 100; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    f2 = 22, f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    ASSERT_EQ(GMERR_OK, GmcTransRollBack(conn1));

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 100; i < 200; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 200; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 200; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 1;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 主备倒换+主键自增+临界值
TEST_F(SwitchOver, AutoIncrement06)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":4294967246, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 50; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    f2 = 22, f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcExecute(stmt1));

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 10; i++) {
        f1 = i + 11, f2 = i + 22, f3 = i + 33;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 50; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 4294967246;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 50; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 4294967246;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 主备倒换+主键自增+倒换后新主插入临界值
TEST_F(SwitchOver, AutoIncrement07)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":4294967246, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 40; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 10; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 10; i++) {
        f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcExecute(stmt2));
    }

    for (uint32_t i = 0; i < 50; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i + 4294967246;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }

    for (uint32_t i = 0; i < 50; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i + 4294967246;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F0", &f0, sizeof(f0), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

// 反复主备倒换+非主键自增
TEST_F(SwitchOver, AutoIncrement08)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 100; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 100; i < 200; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    SwitchOver1();
    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 200; i < 300; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    for (uint32_t i = 0; i < 300; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f1, sizeof(f1), &isNull));
        EXPECT_EQ(isNull, false);
        EXPECT_EQ(i + 1, f1);
    }

    for (uint32_t i = 0; i < 300; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F1", &f1, sizeof(f1), &isNull));
        EXPECT_EQ(isNull, false);
        EXPECT_EQ(i + 1, f1);
    }
}

// 主备倒换+非主键自增+临界值
TEST_F(SwitchOver, AutoIncrement09)
{
    const char *labelName = "XT60";

    const char *configJson = R"({"max_record_count":10000, "auto_increment":4294967246, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false, "auto_increment":true},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    uint32_t f0, f1, f2, f3;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < 49; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    SwitchOver2();
    GmcWait(g_serverLocator2, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 49; i < 50; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    }

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 50; i < 60; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcExecute(stmt2));
    }

    SwitchOver1();
    GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 20);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 60; i < 70; i++) {
        f0 = i, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcExecute(stmt1));
    }

    for (uint32_t i = 0; i < 50; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K0"));
        f0 = i;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f1, sizeof(f1), &isNull));
        EXPECT_EQ(isNull, false);
        EXPECT_EQ(i + 4294967246, f1);
    }

    for (uint32_t i = 0; i < 50; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        f0 = i;
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
        EXPECT_EQ(false, eof);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F1", &f1, sizeof(f1), &isNull));
        EXPECT_EQ(isNull, false);
        EXPECT_EQ(i + 4294967246, f1);
    }
}

// 测试日志需要重新启动，所以放在这个测试套
// 计算通过环境变量配置so的路径
TEST_F(SwitchOver, Log)
{
    system("rm -f orm.log");
    system("mkdir ./aa/");
    system("gcc -shared -fPIC -o ./aa/libdblog_adapter.so orm_log.c");
    Reboot12();
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcDropVertexLabel(stmt1, "CCCC"));
    system("rm -f ./aa/libdblog_adapter.so");
    // 打开文件
    FILE *file = fopen("orm.log", "r");
    ASSERT_NE(file, nullptr) << "Failed to open file";

    // 读取文件内容
    char buffer[1024];
    char *found = NULL;
    while (found == NULL && fgets(buffer, sizeof(buffer), file) != nullptr) {
        found = strstr(buffer, "CCCC");  // 检查字符串是否存在
    }

    // 使用 gtest 断言
    ASSERT_NE(found, nullptr) << "String 'CCCC' not found in file";

    // 关闭文件
    fclose(file);
}

// 测试systemd喂狗需要重新启动，所以放在这个测试套
// 计算通过环境变量配置so的路径
TEST_F(SwitchOver, sd_notify)
{
    system("rm -f sd.log");
    system("rm -fr ./aa");
    system("mkdir -p ./aa/");
    // 函数通过dlopen加载，加载顺序为：优先从libsystemd.so中加载，找不到这个so，再从libsystemd.so.0中加载
    // 如果只创建一个libsystemd.so.0，环境的系统中可能自带libsystemd.so，导致加载不到我们创建的so，所以两个都要编
    system("gcc -shared -fPIC -o ./aa/libsystemd.so.0 sd.c");
    system("gcc -shared -fPIC -o ./aa/libsystemd.so sd.c");
    Reboot12();  // 里面已经有sleep了

    char currentDir[1024];
    if (getcwd(currentDir, sizeof(currentDir)) != NULL) {
        printf("[TEST] current working directory: %s\n", currentDir);
    } else {
        printf("[TEST] failed to get currentDir\n");
    }

    // 打开文件
    FILE *file = fopen("sd.log", "r");
    ASSERT_NE(file, nullptr) << "Failed to open file";

    // 读取文件内容
    char buffer[128];
    ASSERT_NE(fgets(buffer, sizeof(buffer), file), nullptr);
    ASSERT_STREQ("READY=1\n", buffer);
    ASSERT_NE(fgets(buffer, sizeof(buffer), file), nullptr);
    ASSERT_STREQ("WATCHDOG=1\n", buffer);

    // 关闭文件
    fclose(file);
    system("rm -fr ./aa");
}

// 测试主备通信中断
// 由于难以模拟，所以直接复位备机
TEST_F(SwitchOver, Interrupted)
{
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    system("kill -9 `pidof gmserver2`");
    ASSERT_EQ(GMERR_REQUEST_TIME_OUT, GmcCreateVertexLabel(stmt1, labelJson, configJson));
}
