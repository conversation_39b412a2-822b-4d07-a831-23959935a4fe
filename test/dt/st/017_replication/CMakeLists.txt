project(GMDB_ST_017_REPLICATION)

set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${GMDB_ROOT}/test/tools/test_ha)

aux_source_directory(. SRC_ST_017_REPLICATION_BASE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/st_file SRC_ST_FILE_LIST)

set(SRC_ST_017_REPLICATION ${SRC_ST_017_REPLICATION_BASE_LIST})
add_executable(st_017_replication ${SRC_ST_017_REPLICATION})

target_link_libraries(st_017_replication StartDbServer orm_ha ${DEPS_GMDB} stub)
