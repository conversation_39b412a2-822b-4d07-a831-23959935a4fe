#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# used for build db entry
#
set -e

REPO_URL="ssh://***************************:2222/VDBInfer/vinfer_datasets.git"
TARGET_DIR="./10m_128d_realdata"
BRANCH="10m_128d_realdata"

# 设置临时环境变量 GIT_SSL_NO_VERIFY
export GIT_SSL_NO_VERIFY=true

function main() {

    # 开始时间
    local start_time=$(date +%s)

    if [ -d "$TARGET_DIR" ]; then
        echo " $TARGET_DIR has existed."
    else
        # 拉取完整测试数据
        git clone -b $BRANCH $REPO_URL $TARGET_DIR
        # 拉取lfs大文件
        cd $TARGET_DIR
        git lfs install
        git lfs pull
        cd ..
    fi

    # 查找并输出每个 .fbin文件的大小
    find "$TARGET_DIR" -type f -name "*.fbin" -exec du -h {} \; | sort -h

    if [ $? -eq 0 ]; then
        echo "Git clone succeeded."
    else
        echo "Git clone failed."
        exit 1
    fi

    # 取消设置 GIT_SSL_NO_VERIFY 环境变量
    unset GIT_SSL_NO_VERIFY

    # 结束时间
    local end_time=$(date +%s)

    # 计算时间差
    local elapsed_time=$((end_time - start_time))

    # 输出结果
    echo "Git clone completed in $elapsed_time seconds."
}

main
