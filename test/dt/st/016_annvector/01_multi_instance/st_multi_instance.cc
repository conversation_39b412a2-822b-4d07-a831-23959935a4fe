/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2024-08-06
 */
#include "st_multi_instance_common.h"

// 启动服务端数量
constexpr int SERVER_INSTANCE_NUM = 4;
constexpr int QUERY_NUM = 30;
// CLIENT_THREAD 单个客户端创建线程数量
constexpr int CLIENT_THREAD = 1;
class StMultiInstance : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {
        ShutDownDbServer();
    }
    void SetUp()
    {}
    void TearDown()
    {}
};
const char *g_serverFile = "./st_multi_server";
const char *g_clientFile = "./st_multi_client";
static int ServerStart(int instanceId)
{
    // 第1个参数为 instanceId
    Status ret = GMERR_OK;
    char cmd[BUFF_LEN] = {0};
    int32_t bufferRet = snprintf_s(cmd, BUFF_LEN, BUFF_LEN - 1, "%s %d", g_serverFile, instanceId);
    if (bufferRet < 0) {
        printf("./st_multi_server failed\n");
        ret = GMERR_INVALID_BUFFER;
    }
    EXPECT_EQ(GMERR_OK, ret);
    printf("server cmd: %s\n", cmd);
    int runStatus = system(cmd);
    if (!WIFEXITED(runStatus))
        return GMERR_INTERNAL_ERROR;
    return WEXITSTATUS(runStatus);
}
static int ClientStart(int instanceId)
{
    // 此处默认通信socket文件名格式与st_server.cc中一致，否则不能连接
    // 第1个参数为 instanceId
    // 第2个参数为创建线程数量
    // 第3个参数为起始表序号
    // 第4个参数为读取数据集大小
    Status ret = GMERR_OK;
    char cmd[BUFF_LEN] = {0};
    int32_t bufferRet = snprintf_s(
        cmd, BUFF_LEN, BUFF_LEN - 1, "%s %d %d %d %d", g_clientFile, instanceId, CLIENT_THREAD, 0, QUERY_NUM);
    if (bufferRet < 0) {
        printf("./st_multi_client failed\n");
        ret = GMERR_INVALID_BUFFER;
    }
    EXPECT_EQ(GMERR_OK, ret);
    printf("client cmd: %s\n", cmd);
    int runStatus = system(cmd);
    if (!WIFEXITED(runStatus))
        return GMERR_INTERNAL_ERROR;
    return WEXITSTATUS(runStatus);
}

// 批量测试
TEST_F(StMultiInstance, ServerBatchNotBind)
{
    // 拉起服务端
    for (int i = 1; i <= SERVER_INSTANCE_NUM; ++i) {
        EXPECT_EQ(ServerStart(i), RUN_SUCCESS);
    }
    // 拉起客户端
    for (int i = 1; i <= SERVER_INSTANCE_NUM; ++i) {
        EXPECT_EQ(ClientStart(i), RUN_SUCCESS);
    }
}

// 实例ID测试
TEST_F(StMultiInstance, ServerMultiInstanceID)
{
    // 预计失败
    // 下限
    EXPECT_EQ(ServerStart(0), RUN_FAILED);
    // 上限
    EXPECT_EQ(ServerStart(193), RUN_FAILED);
    // 预计成功
    EXPECT_EQ(ServerStart(1), RUN_SUCCESS);
    EXPECT_EQ(ServerStart(192), RUN_SUCCESS);
}
