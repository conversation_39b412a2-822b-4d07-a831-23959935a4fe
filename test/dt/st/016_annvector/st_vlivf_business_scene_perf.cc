/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author: g00832243
 * Create: 2024-07-11
 */

#include <thread>
#include "st_annvector_common.h"

using namespace std::chrono;
using namespace std;

const size_t SINGLE_INSERT_NUM = 100;
const int NUM_THREADS = 48;
const int TIME_INTERVAL = 3000000;

float g_vlIvfQueryTimeEveryThread[NUM_THREADS] = {0};
float g_vlIvfLoadTimeEveryThread[NUM_THREADS] = {0};

class StVlIvfBusinessScenePerf : public testing::Test {
protected:
    void SetUp()
    {}
    void TearDown()
    {
        g_longSeqRuntime.ReleaseDataAndTearDown();
    }
};

void TenMBusinessSceneExecute(int threadId, bool isNoIndex)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    CreateSyncConnectionAndStmt(&conn, &stmt);
    VectorLabelDataMgr dataMgr(conn, stmt);
    string tableName = g_longSeqRuntime.para.labelName + to_string(threadId);

    JsonValueModifier modifier(g_longSeqRuntime.para.labelJson);
    modifier.ModifyLabelName(tableName.c_str());
    char *labelJson = modifier.FinishAndDump();

    Status ret = dataMgr.CreateTable(labelJson);
    modifier.ClearDumpedStr(labelJson);
    EXPECT_EQ(GMERR_OK, ret);

    VectorTestCost cost;

    cost.Begin();
    // 批量导入中心点
    if (!isNoIndex) {
        ret = dataMgr.BatchLoad(
            g_longSeqRuntime.vecBuf.centset.num, LongSeqDefaultLoadCentSetFields, &g_longSeqRuntime.vecBuf, false);
        ASSERT_EQ(GMERR_OK, ret);
        g_vlIvfLoadTimeEveryThread[threadId] += cost.Continue();
    }

    // 批量插入数据
    ret = dataMgr.BatchInsert(g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM, LongSeqDefaultSetFields,
        &g_longSeqRuntime.vecBuf, false);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t dim = g_longSeqRuntime.vecBuf.dim;
    VectorAnnScanner scanner(stmt, tableName.c_str(), dim);
    VectorAnnScanner::UserCtx userCtx(LongSeqScanDefaultLabelCallbackCheck, NULL);
    const uint32_t limit = 100;
    uint32_t expectedCnt = limit * g_longSeqRuntime.vecBuf.queryset.num;

    // 循环插入
    for (uint32_t insertPointer = g_longSeqRuntime.vecBuf.dataset.num + 1 - SINGLE_INSERT_NUM;
         insertPointer < g_longSeqRuntime.vecBuf.dataset.num; insertPointer++) {
        uint32_t totalCnt = 0;

        // query 64条数据一组
        auto startQuery = steady_clock::now();
        if (isNoIndex) {
            ret = scanner.LoopSeqScan(&userCtx, &g_longSeqRuntime.vecBuf.queryset, &totalCnt);
        } else {
            ret = scanner.LoopIndexScan(&userCtx, 1, &g_longSeqRuntime.vecBuf.queryset, &totalCnt);
        }
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(totalCnt, expectedCnt);

        // 如果时间不够3000ms则等待
        float timeBatch = duration_cast<microseconds>(steady_clock::now() - startQuery).count();
        g_vlIvfQueryTimeEveryThread[threadId] += timeBatch;
        if (timeBatch < TIME_INTERVAL) {
            usleep(TIME_INTERVAL - (useconds_t)timeBatch);
        }

        // 单条插入
        ret = GmcPrepareStmtByLabelName(stmt, tableName.c_str(), GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        float *originVector = g_longSeqRuntime.vecBuf.dataset.set + insertPointer * dim;

        uint32_t id = insertPointer;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &id, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, originVector, dim * sizeof(float));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 删除表
    dataMgr.DropTable();
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
}

TEST_F(StVlIvfBusinessScenePerf, Scene1)
{
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&g_10MSeqDataSet);
    ASSERT_EQ(GMERR_OK, ret);
    thread businessSceneThreads[NUM_THREADS];
    float fullQueryTime = 0;
    float fullLoadTime = 0;

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i] = thread(TenMBusinessSceneExecute, i, false);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i].join();
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        fullQueryTime += g_vlIvfQueryTimeEveryThread[i];
        fullLoadTime += g_vlIvfLoadTimeEveryThread[i];
    }

    printf("\nAverage Time Cost of Querying Vertex With Auto Quantization in %d Threads: %f us\n", NUM_THREADS,
        fullQueryTime / (NUM_THREADS * g_longSeqRuntime.vecBuf.queryset.num *
                            (g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM)));
    printf("\nFull Time Cost of Loading %u Centroids in %d Threads: %f ms\n", g_longSeqRuntime.vecBuf.centset.num,
        NUM_THREADS, fullLoadTime / NUM_THREADS);
}

const char *g_128DimLabelJson =
    R"([{
        "type":"record",
        "name":"QuantTable",
        "config": {
            "auto_vector_quantization":
                [
                    {"type":"lvq", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":5}
                ]
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":512}
            ],
        "keys":
            [
                { "node":"QuantTable", "name":"PK", "fields":["F0"], "index":{"type":"primary"} },
                {
                    "node":"QuantTable",
                    "name":"vlivfIndex",
                    "fields":["F1"],
                    "index":{"type":"vlivf"},
                    "ncentroids":[500]
                }
            ]
        }])";

TEST_F(StVlIvfBusinessScenePerf, SceneDim128)
{
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&g_10MSeqDataSet, true, NULL, g_128DimLabelJson);
    ASSERT_EQ(GMERR_OK, ret);

    thread businessSceneThreads[NUM_THREADS];
    float fullQueryTime = 0;
    float fullLoadTime = 0;

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i] = thread(TenMBusinessSceneExecute, i, false);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i].join();
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        fullQueryTime += g_vlIvfQueryTimeEveryThread[i];
        fullLoadTime += g_vlIvfLoadTimeEveryThread[i];
    }

    printf("\nAverage Time Cost of Querying Vertex With Auto Quantization in %d Threads: %f us\n", NUM_THREADS,
        fullQueryTime / (NUM_THREADS * g_longSeqRuntime.vecBuf.queryset.num *
                            (g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM)));
    printf("\nFull Time Cost of Loading %u Centroids in %d Threads: %f ms\n", g_longSeqRuntime.vecBuf.centset.num,
        NUM_THREADS, fullLoadTime / NUM_THREADS);
}

const DataSetPath stSeqDataSetNoCent = {.dataFile = "./10m_data/10comb_8192_split_1.fbin",
    .queryFile = "./10m_data/10comb_query_split_1.fbin",
    .centFile = NULL,
    .dataNum = 28353,
    .queryNum = 30,
    .centNum = 0};

TEST_F(StVlIvfBusinessScenePerf, Scene10MDim128NoIndex)
{
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&stSeqDataSetNoCent, true, NULL, g_128DimNoIndexLabelJson);
    ASSERT_EQ(GMERR_OK, ret);

    thread businessSceneThreads[NUM_THREADS];
    float fullQueryTime = 0;

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i] = thread(TenMBusinessSceneExecute, i, true);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i].join();
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        fullQueryTime += g_vlIvfQueryTimeEveryThread[i];
    }

    printf("\nAverage Time Cost of Querying Vertex With Auto Quantization in %d Threads: %f us\n", NUM_THREADS,
        fullQueryTime / (NUM_THREADS * g_longSeqRuntime.vecBuf.queryset.num *
                            (g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM)));
}

TEST_F(StVlIvfBusinessScenePerf, SceneGQADim128NoIndex)
{
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&g_GQASeqDataSet, true, NULL, g_128DimNoIndexLabelJson);
    ASSERT_EQ(GMERR_OK, ret);

    thread businessSceneThreads[NUM_THREADS];
    float fullQueryTime = 0;

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i] = thread(TenMBusinessSceneExecute, i, true);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i].join();
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        fullQueryTime += g_vlIvfQueryTimeEveryThread[i];
    }

    printf("\nAverage Time Cost of Querying Vertex With Auto Quantization in %d Threads: %f us\n", NUM_THREADS,
        fullQueryTime / (NUM_THREADS * g_longSeqRuntime.vecBuf.queryset.num *
                            (g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM)));
}

TEST_F(StVlIvfBusinessScenePerf, SceneGQADim128NoIndexRealData)
{
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&g_10MSeqRealDataSet, true, NULL, g_128DimNoIndexLabelJson);
    ASSERT_EQ(GMERR_OK, ret);

    thread businessSceneThreads[NUM_THREADS];
    float fullQueryTime = 0;

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i] = thread(TenMBusinessSceneExecute, i, true);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i].join();
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        fullQueryTime += g_vlIvfQueryTimeEveryThread[i];
    }

    printf("\nAverage Time Cost of Querying Vertex With Auto Quantization in %d Threads: %f us\n", NUM_THREADS,
        fullQueryTime / (NUM_THREADS * g_longSeqRuntime.vecBuf.queryset.num *
                            (g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM)));
}

TEST_F(StVlIvfBusinessScenePerf, SceneDim128NoVector)
{
    g_longSeqRuntime.para.dim = 128;
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&g_GQASeqDataSet, true, NULL, g_128DimNoQuantizationLabelJson);
    ASSERT_EQ(GMERR_OK, ret);

    thread businessSceneThreads[NUM_THREADS];
    float fullQueryTime = 0;

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i] = thread(TenMBusinessSceneExecute, i, true);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        businessSceneThreads[i].join();
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        fullQueryTime += g_vlIvfQueryTimeEveryThread[i];
    }

    printf("\nAverage Time Cost of Querying Vertex in %d Threads: %f us\n", NUM_THREADS,
        fullQueryTime / (NUM_THREADS * g_longSeqRuntime.vecBuf.queryset.num *
                            (g_longSeqRuntime.vecBuf.dataset.num - SINGLE_INSERT_NUM)));
}
