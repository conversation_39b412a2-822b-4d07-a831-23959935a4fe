/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author: g00832243
 * Create: 2024-07-11
 */

#ifndef ST_ANNVECTOR_COMMON_H
#define ST_ANNVECTOR_COMMON_H

#include <chrono>
#include <cfloat>
#include "st_common.h"
#include "gmc_graph.h"
#include "gmc_vector.h"
#include "gmc_types.h"
#include "db_vector_distance.h"
#include "db_lvq.h"
#include "jansson.h"

using namespace std::chrono;
using namespace std;

struct JsonValueModifier {
    json_t *json;
    json_t *root;
    explicit JsonValueModifier(const char *labelJson);
    void ModifyLabelName(const char *newName);
    void ModifyAutoQuantConfigString(uint32_t id, const char *key, const char *value);
    void ModifyAutoQuantConfigUint32(uint32_t id, const char *key, uint32_t value);
    void ModifyVlIvfCentroidNum(uint32_t keyId, uint32_t *centNum, uint32_t nCent);
    void ModifyVlIvfFiled(uint32_t keyId, const char *fieldName);
    char *FinishAndDump(void);
    void ClearDumpedStr(char *str);
    bool IsAutoQuant();
};

struct VectorLabelParameter {
    const char *labelJson;
    char labelName[DM_MAX_NAME_LENGTH];
    uint32_t dim;
    uint32_t keyId;
    char keyName[DM_MAX_NAME_LENGTH];
    char keyFieldName[DM_MAX_NAME_LENGTH];
    void ParseLabelName(json_t *root);
    void ParseAutoQuant(json_t *root);
    void ParseAnnIdx(json_t *root);
    void Init(const char *json);
};

struct VectorDataBuffer {
    struct SingleBuffer {
        uint32_t num;
        float *set;
        SingleBuffer() : num(0), set(NULL)
        {}
        Status Init(uint32_t n, uint32_t d);
        void Release(void);
    };
    SingleBuffer dataset;
    SingleBuffer queryset;
    SingleBuffer centset;
    uint32_t dim;

    VectorDataBuffer() : dataset(), queryset(), centset(), dim(0)
    {}

    Status Init(uint32_t dimension, uint32_t ndata, uint32_t nquery, uint32_t ncent = 0);
    void Release(void);
};

struct VectorLabelDataMgr {
    GmcConnT *conn;
    GmcStmtT *stmt;
    VectorLabelParameter para;
    VectorLabelDataMgr(GmcConnT *connection, GmcStmtT *statement) : conn(connection), stmt(statement)
    {}
    Status CreateTable(const char *labelJson);
    void DropTable(void);
    Status BatchLoad(uint32_t, Status (*)(GmcStmtT *, void *, uint32_t), void *, bool printSysview = false);
    Status BatchInsert(uint32_t, Status (*)(GmcStmtT *, void *, uint32_t), void *, bool printSysview = false);
    Status LoadVlIvfIndex(uint32_t, Status (*)(GmcStmtT *, void *, uint32_t), void *);
};

Status LongSeqDefaultSetFields(GmcStmtT *stmt, void *userctx, uint32_t cnt);
void LongSeqScanDefaultLabelCallbackCheck(GmcStmtT *stmt, void *ctx);
Status LongSeqDefaultLoadCentSetFields(GmcStmtT *stmt, void *userctx, uint32_t cnt);
Status LongSeqDefaultSetVlIvfKeyField(GmcStmtT *stmt, void *userctx, uint32_t cnt);

struct DataSetPath {
    const char *dataFile;
    const char *queryFile;
    const char *centFile;
    uint32_t dataNum;
    uint32_t queryNum;
    uint32_t centNum;
};

extern const DataSetPath g_sysViewDataSet;
extern const DataSetPath g_128kSeqDataSet;
extern const DataSetPath g_GQASeqDataSet;
extern const DataSetPath g_10MSeqRealDataSet;
extern const DataSetPath g_10MSeqDataSet;
extern const DataSetPath g_sysViewLimitDataSet;

extern const char *g_128DimNoIndexLabelJson;
extern const char *g_128DimNoQuantizationLabelJson;

struct LongSeqTestRuntime {
    VectorLabelParameter para;
    VectorDataBuffer vecBuf;
    bool serverStarted;
    uint32_t LoadData(const char *dataPath, float *buf, uint32_t expectNum);
    Status LoadDataAndStartUp(const DataSetPath *dataset, bool startServer = true, const char *config = NULL,
        const char *systemViewJson = NULL);
    void ReleaseDataAndTearDown(void);
};

extern LongSeqTestRuntime g_longSeqRuntime;

struct VectorTestCost {
    steady_clock::time_point begin;
    void Begin(void)
    {
        begin = steady_clock::now();
    }
    float Continue(void)
    {
        steady_clock::time_point now = steady_clock::now();
        float usCost = duration_cast<microseconds>(now - begin).count();
        begin = now;
        return usCost / USECONDS_IN_MSECOND;
    }
    float Finish(void)
    {
        float usCost = duration_cast<microseconds>(steady_clock::now() - begin).count();
        return usCost / USECONDS_IN_MSECOND;
    }
};

struct VectorAnnScanner {
    GmcStmtT *stmt;
    const char *labelName;
    uint32_t dim;
    uint32_t limit;
    GmcFilterStructT filter;
    struct UserCtx {
        void (*callback)(GmcStmtT *, void *);
        void *ctx;
        UserCtx(void (*cb)(GmcStmtT *, void *), void *userctx) : callback(cb), ctx(userctx)
        {}
    };
    VectorAnnScanner(GmcStmtT *statement, const char *lbName, uint32_t d)
        : stmt(statement), labelName(lbName), dim(d), limit(100)
    {
        filter.fieldId = 0;
        filter.nodeName = NULL;
        filter.compOp = GMC_OP_IP_VECTOR_SIMILARITY;
        filter.valueLen = (uint32_t)sizeof(float) * dim;
    }
    void SetFilterInfo(uint32_t fieldId, const char *nodeName, int32_t compOp);
    void SetLimit(uint32_t l);

    struct LoopScanPara {
        float *querys;
        uint32_t queryNum;
    };

    Status SeqScan(UserCtx *, float *, uint32_t *);
    Status LoopSeqScan(UserCtx *, VectorDataBuffer::SingleBuffer *, uint32_t *, float *timeCost = NULL);
    Status IndexScan(UserCtx *, uint32_t, float *, uint32_t *);
    Status LoopIndexScan(UserCtx *, uint32_t, VectorDataBuffer::SingleBuffer *, uint32_t *, float *timeCost = NULL);
};

struct KeyBaseDesc {
    const char *name;
    const char *type;
    uint32_t nFields;
    bool uniqueSetted;
    bool unique;
    const char *fields[DM_MAX_KEY_PROPE_NUM];
    KeyBaseDesc(const char *keyName, const char *keyType)
        : name(keyName), type(keyType), nFields(0), uniqueSetted(false), unique(false)
    {}
    KeyBaseDesc(const char *keyName, const char *keyType, const char *field) : KeyBaseDesc(keyName, keyType)
    {
        AppendField(field);
    }
    KeyBaseDesc(const char *keyName, const char *keyType, const char *field1, const char *field2)
        : KeyBaseDesc(keyName, keyType)
    {
        AppendField(field1);
        AppendField(field2);
    }
    void AppendField(const char *field)
    {
        fields[nFields] = field;
        nFields++;
    }
    void SetUnique(bool isUnique)
    {
        unique = isUnique;
        uniqueSetted = true;
    }
    virtual void Serialize(json_t *keyJson)
    {
        json_object_set_new(keyJson, "name", json_string(name));
        json_t *keyFieldsJson = json_array();
        for (uint32_t i = 0; i < nFields; i++) {
            json_array_append_new(keyFieldsJson, json_string(fields[i]));
        }
        json_object_set_new(keyJson, "fields", keyFieldsJson);
        json_t *indexJson = json_object();
        json_object_set_new(indexJson, "type", json_string(type));
        json_object_set_new(keyJson, "index", indexJson);
        if (uniqueSetted) {
            json_t *constraintsJson = json_object();
            json_object_set_new(constraintsJson, "unique", unique ? json_true() : json_false());
            json_object_set_new(keyJson, "constraints", constraintsJson);
        }
    }
};

struct VlIvfKeyDesc : public KeyBaseDesc {
    static constexpr uint32_t MAX_IVF_LEVEL = 10;
    bool scanRatioSetted;
    bool candidateRatioSetted;
    const char *metric;
    uint32_t nLevel;
    uint32_t nCent[MAX_IVF_LEVEL];
    float scanRatio;
    float candidateRatio;
    explicit VlIvfKeyDesc(const char *keyName)
        : KeyBaseDesc(keyName, "vlivf"), scanRatioSetted(false), candidateRatioSetted(false), metric(NULL), nLevel(0)
    {}
    VlIvfKeyDesc(const char *keyName, const char *field) : VlIvfKeyDesc(keyName)
    {
        AppendField(field);
    }
    VlIvfKeyDesc(const char *keyName, uint32_t nCent) : VlIvfKeyDesc(keyName)
    {
        AppendCentNum(nCent);
    }
    VlIvfKeyDesc(const char *keyName, const char *field, uint32_t nCent) : VlIvfKeyDesc(keyName)
    {
        AppendField(field);
        AppendCentNum(nCent);
    }
    VlIvfKeyDesc(const char *keyName, const char *field, uint32_t nCent, const char *metricStr) : VlIvfKeyDesc(keyName)
    {
        AppendField(field);
        AppendCentNum(nCent);
        SetMetric(metricStr);
    }

    void SetScanRatio(float ratio)
    {
        scanRatio = ratio;
        scanRatioSetted = true;
    }
    void SetCandidateRatio(float ratio)
    {
        candidateRatio = ratio;
        candidateRatioSetted = true;
    }
    void SetMetric(const char *metricStr)
    {
        metric = metricStr;
    }
    void AppendCentNum(uint32_t n)
    {
        nCent[nLevel] = n;
        nLevel++;
    }
    void Serialize(json_t *keyJson)
    {
        KeyBaseDesc::Serialize(keyJson);
        json_t *nCentJson = json_array();
        for (uint32_t i = 0; i < nLevel; i++) {
            json_array_append_new(nCentJson, json_integer(nCent[i]));
        }
        json_object_set_new(keyJson, "ncentroids", nCentJson);
        if (metric != NULL) {
            json_object_set_new(keyJson, "metric", json_string(metric));
        }
        if (scanRatioSetted) {
            json_object_set_new(keyJson, "scan_ratio", json_real(scanRatio));
        }
        if (candidateRatioSetted) {
            json_object_set_new(keyJson, "candidate_ratio", json_real(candidateRatio));
        }
    }
};

struct AnnSchemaGenerator {
    struct DumpedLinkNode {
        char *str;
        struct DumpedLinkNode *next;
    };
    json_t *rootJson;
    DumpedLinkNode *dumpedLink;
    json_free_t freeFunc;
    char labelName[MAX_TABLE_NAME_LEN];
    explicit AnnSchemaGenerator(const char *name);
    ~AnnSchemaGenerator();
    void AppendDumpedLink(char *str);
    void ClearDumpedLink(void);
    void SetSchemaVersion(uint32_t version);
    void AppendLvqQuant(const char *src, const char *field, const char *metric, uint8_t ncodebits);
    void AppendField(const char *name, const char *type, bool nullable);
    void AppendField(const char *name, const char *type, uint32_t size);
    void AppendField(const char *name, const char *type, uint32_t size, bool nullable);
    void AppendVectorField(const char *name, uint32_t dim);
    void AppendKey(KeyBaseDesc *keyDesc);
    void AppendKey(const char *name, const char *type, const char **fields, uint32_t nFields, bool unique);
    void AppendPK(const char *name, const char **fields, uint32_t nFields);
    void AppendPK(const char *name, const char *field);
    void AppendVlIvfKey(const char *name, const char *field, uint32_t nCent);
    void AppendVlIvfKey(const char *name, const char *field, uint32_t nCent, const char *metric);
    char *Dump(void);
    const char *GetLabelName(void);
};

extern const int OOD_NUM;
extern const int CMD_LEN;
// 获取视图中某个字段的数量
int GetViewFieldCount(const char *viewName, const char *fieldName, char *result, int resLen);

// 获取视图中某个字段的值的合，字段必须为数字
int GetViewResultSum(const char *viewName, const char *fieldName, char *result, int resLen);

// 校验fieldName字段字符串值是否为matchStr
bool CheckRangeNa(const char *viewName, const char *fieldName, const char *matchStr, char *result, int resLen);

// 校验fieldName字段字符串值是否存在matchStr
bool CheckFieldValue(const char *viewName, const char *fieldName, const char *matchStr, char *result, int resLen);

#endif  // ST_ANNVECTOR_COMMON_H
