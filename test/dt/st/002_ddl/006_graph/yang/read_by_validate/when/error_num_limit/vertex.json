[{"name": "huawei-ifm:ifm", "type": "container", "clause": [{"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "when", "formula": "/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}], "presence": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "presence": true, "fields": []}], "keys": [{"node": "huawei-ifm:ifm", "name": "PK", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]