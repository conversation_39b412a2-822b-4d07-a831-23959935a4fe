[{"name": "yang", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}], "keys": [{"node": "yang", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ds", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"node": "ds", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ietf-yang-library:yang-library", "type": "container", "is_config": false, "fields": [{"name": "content-id", "type": "string", "nullable": false, "is_config": false}]}], "keys": [{"node": "ietf-yang-library", "name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "is_config": false}, {"name": "value", "type": "string", "nullable": false, "is_config": false}], "keys": [{"node": "ietf-yang-library:yang-library::module-set", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]