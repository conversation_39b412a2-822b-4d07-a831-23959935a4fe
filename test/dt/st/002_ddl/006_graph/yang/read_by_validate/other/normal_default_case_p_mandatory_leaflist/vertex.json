[{"name": "huawei-ifm:ifm::interfaces::interface", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string", "default": "main-interface"}, {"name": "type", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "huawei-dhcp:dhcp-client-if", "type": "container", "presence": true, "fields": [{"name": "address-allocation", "type": "string"}, {"name": "host-name", "type": "string"}]}, {"name": "huawei-ip:ipv4", "type": "container", "fields": [{"name": "address", "type": "choice", "fields": [{"name": "common-address", "type": "case", "default": true, "fields": [{"name": "addresses-list", "type": "container", "presence": true, "fields": []}]}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface", "name": "PK", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "addresses-list", "type": "leaf-list", "min-elements": 100, "max-elements": 10000, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"node": "addresses-list", "name": "PK", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]