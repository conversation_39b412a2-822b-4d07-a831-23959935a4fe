/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: yang tree batch ddl
 * Author:
 * Create: 2024-03-22
 */
#include "yang/yang_common_st.h"
#include "yang/yang_testframe.h"
#include "gmc_yang_types.h"

static const char *g_cfgJson = R"({"auto_increment": 1, "isFastReadUncommitted": 0, "yang_model": 1})";

// fix:DTS2024032108409
TEST_F(StYang, TestBatchDDL)
{
    const char *vertexFile = "006_graph/yang/read_by_validate/default/default_choice_case_choice_case/vertex.json";
    // 创建tablespace
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(stmt, 8));

    // 预处理batch
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    std::string vertexJson = GetFileContext(vertexFile);
    ASSERT_EQ(GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, NULL, vertexJson.c_str(), g_cfgJson));

    // 执行batch
    std::atomic_uint32_t localStep{0};
    ASSERT_EQ(GMERR_OK, GmcBatchExecuteAsync(batch, AsyncBatchOperationCb, &localStep));
    EpollWaitAndCheck(localStep, 1);

    // 清空namespace
    ASSERT_NO_FATAL_FAILURE(LltClearNamespaceAsync(stmt, "running"));
    // 删除tablespace
    ASSERT_NO_FATAL_FAILURE(LltDropTablespaceAndNamespaceAsync(stmt));
}
