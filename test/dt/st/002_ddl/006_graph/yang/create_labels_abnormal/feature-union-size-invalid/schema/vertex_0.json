[{"name": "yang", "alias": "yang", "type": "container", "rfc7951_invisible": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ds", "alias": "ds", "rfc7951_invisible": true, "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "huawei-snmp", "alias": "huawei-snmp", "rfc7951_invisible": true, "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "E0", "type": "empty"}, {"name": "U0", "type": "union", "size": 7, "union_types": ["int32", "uint32", "int64", "uint64", "empty", "boolean", "string"]}, {"type": "container", "name": "huawei-snmp:snmp", "fields": [{"type": "container", "name": "usm-users", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "huawei-snmp:snmp::usm-users::usm-user", "alias": "usm-user", "max-elements": 20, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "E1", "type": "empty", "nullable": false}, {"name": "U1", "type": "union", "union_types": ["int32", "uint32", "int64", "uint64", "empty", "boolean", "string"], "nullable": false}, {"name": "group-name", "type": "string"}, {"name": "auth-protocol", "type": "string", "nullable": false}, {"name": "auth-key", "type": "string", "nullable": false}, {"name": "priv-protocol", "type": "string", "default": "noPriv"}, {"name": "priv-key", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false}, {"name": "left-lock-time", "type": "uint32", "is_config": false}, {"type": "container", "name": "T1", "fields": [{"name": "E2", "type": "empty", "nullable": false}, {"name": "U2", "type": "union", "union_types": ["int32", "uint32", "int64", "uint64", "empty", "boolean", "string"], "nullable": false}, {"type": "choice", "name": "Ch0", "fields": [{"type": "case", "name": "CA", "fields": [{"name": "E3", "type": "empty", "nullable": false}, {"name": "U3", "type": "union", "union_types": ["int32", "uint32", "int64", "uint64", "empty", "boolean", "string"], "nullable": false}]}, {"type": "case", "name": "CB", "default": true, "fields": [{"name": "E4", "type": "empty", "nullable": true}, {"name": "U4", "type": "union", "union_types": ["int32", "uint32", "int64", "uint64", "empty", "boolean", "string"], "nullable": true}, {"name": "F4", "type": "string", "default": "test-F4"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "user-name", "E1"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}]