/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: test cases for yang constraint parser
 * Author: Chengzhangpei
 * Create: 2022-7-18
 */

#include <cmath>
#include "yang/yang_common_st.h"

#ifdef YANG_SUPPORT_GRAPH
// 测试对range、length、pattern、invert_pattern的解析，全部为正确场景，正确解析
TEST_F(StYang, ConstraintParser1)
{
    const char *gLabelName = "ConstraintParser1";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser1",
                "fields":[
                    {"name":"pk", "type":"uint32", "nullable":false},
                    {"name":"F0", "type":"string", "nullable":false, "length":"min..5"},
                    {"name":"F1", "type":"string", "nullable":false, "length":"5..max"},
                    {"name":"F2", "type":"string", "nullable":false, "length":"5"},
                    {"name":"F3", "type":"string", "nullable":false, "length":"5..10"},
                    {"name":"F4", "type":"int32", "nullable":false, "range":"-5..5"},
                    {"name":"F5", "type":"uint32", "nullable":false, "range":"8..9"},
                    {"name":"F6", "type":"double", "nullable":false, "range":"6.6..7.9"},
                    {"name":"F7", "type":"int32", "nullable":false, "range":"min..5"},
                    {"name":"F8", "type":"int32", "nullable":false, "range":"5..max"},
                    {"name":"F9", "type":"int32", "nullable":false, "range":"8"},
                    {"name":"F10", "type":"int32", "nullable":false, "range":"max"},
                    {"name":"F11", "type":"string", "nullable":false, "pattern":["^abc$"]},
                    {"name":"F12", "type":"string", "nullable":false, "invert_pattern":["^abc$"]},
                    {"name":"F13", "type":"string", "nullable":false, "pattern":["^[0-9a-f]*$", "^[3-8a-z]*$"]},
                    {"name":"F14", "type":"string", "nullable":false, "invert_pattern":["^[0-8c-p]*$", "^[1-5a-z]*$"]},
                    {"name":"F15", "type":"string", "nullable":false, "length":
                        "1|1..2|1..3|1..4|1..5|1..6|1..7|1..8|1..9|1..10|1..11|1..12|1..13|1..14|1..15|1..16|1..17|1..18|1..19|1..20|1..21|1..22|1..23|1..24|1..25|1..26|1..27|1..28|1..29|1..30|1..31|1..32"},
                    {"name":"F16", "type":"int32", "nullable":false, "range":
                        "1|1..2|1..3|1..4|1..5|1..6|1..7|1..8|1..9|1..10|1..11|1..12|1..13|1..14|1..15|1..16|1..17|1..18|1..19|1..20|1..21|1..22|1..23|1..24|1..25|1..26|1..27|1..28|1..29|1..30|1..31|1..32"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser1",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(
        GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, gLabelName, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
}
#endif

// 测试length格式的异常场景，正确的格式"length":"5..6|6..7",每个length范围之间通过|分开
TEST_F(StYang, ConstraintParser2)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser2",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "length":"5..6..7"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser2",
                        "name":"test1",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ])";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试length设置最多是32个，这个用例测试超出32个范围的异常情况
TEST_F(StYang, ConstraintParser3)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser3",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "length":
                        "1|1..2|1..3|1..4|1..5|1..6|1..7|1..8|1..9|1..10|1..11|1..12|1..13|1..14|1..15|1..16|1..17|1..18|1..19|1..20|1..21|1..22|1..23|1..24|1..25|1..26|1..27|1..28|1..29|1..30|1..31|1..32|1..33"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser3",
                        "name":"test2",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试range格式的异常场景，正确的格式"range":"5..6|6..7",每个range范围之间通过|分开
TEST_F(StYang, ConstraintParser4)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
                {
                    "type":"container",
                    "name":"ConstraintParser4",
                    "fields":[
                        {"name":"F0", "type":"int32", "nullable":false, "range":"5..6..7"}
                    ],
                    "keys":[
                        {
                            "node":"ConstraintParser4",
                            "name":"test3",
                            "fields":["F0"],
                            "index":{"type":"primary"},
                            "constraints":{"unique":true}
                        }
                    ]
                }
            ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试range设置最多是32个，这个用例测试超出32个范围的异常情况
TEST_F(StYang, ConstraintParser5)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser5",
                "fields":[
                    {"name":"F0", "type":"int32", "nullable":false, "range":
                        "1|1..2|1..3|1..4|1..5|1..6|1..7|1..8|1..9|1..10|1..11|1..12|1..13|1..14|1..15|1..16|1..17|1..18|1..19|1..20|1..21|1..22|1..23|1..24|1..25|1..26|1..27|1..28|1..29|1..30|1..31|1..32|1.33"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser5",
                        "name":"test4",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试invert_pattern非数组异常情况，正确格式为json数组格式，"invert_pattern":["^abc$"]
TEST_F(StYang, ConstraintParser6)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser6",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "invert_pattern":"^abc$"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser6",
                        "name":"test5",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试length格式异常字符，不能有max+1这种表达式
TEST_F(StYang, ConstraintParser7)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser7",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "length":"5..max+1"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser7",
                        "name":"test6",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}
// 测试length为负数，string,length不能为负数
TEST_F(StYang, ConstraintParser8)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser8",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "length":"-1..10"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser8",
                        "name":"test7",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试length范围, begin需要小于end
TEST_F(StYang, ConstraintParser9)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser9",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "length":"9..8"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser9",
                        "name":"test8",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试pattern非数组异常情况，正确格式为json数组格式，"pattern":["^abc$"]
TEST_F(StYang, ConstraintParser10)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser10",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "pattern":"^abc$"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser10",
                        "name":"test10",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试range范围值超出类型范围值情况
TEST_F(StYang, ConstraintParser11)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser11",
                "fields":[
                    {"name":"F0", "type":"int8", "nullable":false, "range":"-129..10"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser11",
                        "name":"test11",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试range,"range":"10..5"，begin>end这种异常场景
TEST_F(StYang, ConstraintParser12)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser12",
                "fields":[
                    {"name":"F0", "type":"int32", "nullable":false, "range":"10..5"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser12",
                        "name":"test12",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// length只支持string类型，不支持int,类型不匹配
TEST_F(StYang, ConstraintParser13)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser13",
                "fields":[
                    {"name":"F0", "type":"int32", "nullable":false, "length":"1..8"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser13",
                        "name":"test13",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_DATATYPE_MISMATCH>, &step));
    EpollWaitAndCheck(step, 1);
}

// range只支持数值型，类型不匹配
TEST_F(StYang, ConstraintParser14)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser14",
                "fields":[
                    {"name":"F0", "type":"string", "nullable":false, "range":"0..10"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser14",
                        "name":"test14",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_DATATYPE_MISMATCH>, &step));
    EpollWaitAndCheck(step, 1);
}

// range约束为3-5、8-9，设置为10，不满足约束条件
TEST_F(StYang, ConstraintParser15)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser15",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F0", "type":"int32", "nullable":false, "range":"-5...5"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser15",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser16)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser16",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F0", "type":"int32", "nullable":false, "range":".."}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser16",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser17)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser17",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F0", "type":"int32", "nullable":false, "range":""}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser17",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser18)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser18",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F0", "type":"int32", "nullable":false, "range":"-5.. 5"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser18",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

#ifdef YANG_SUPPORT_GRAPH
TEST_F(StYang, ConstraintParser19)
{
    const char *gLabelName = "ConstraintParser19";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser19",
                "fields":[
                    {"name":"pk", "type":"uint32", "nullable":false},
                    {"name":"F0", "type":"string", "nullable":false, "length":"0"}
                    ],
                "keys":[
                    {
                        "node":"ConstraintParser19",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(
        GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, gLabelName, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
}
#endif

TEST_F(StYang, ConstraintParser20)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser20",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F0", "type":"string", "nullable":false, "length":"-*"}
                    ],
                "keys":[
                    {
                        "node":"ConstraintParser20",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser21)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser21",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":
                        "1|1..2|1..3|1..4|1..5|1..6|1..7|1..8|1..9|1..10|1..11|1..12|1..13|1..14|1..15|1..16|1..17|1..18|1..19|1..20|1..21|1..22|1..23|1..24|1..25|1..26|1..27|1..28|1..29|1..30|1..31|1..32|"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser21",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
}

#ifdef YANG_SUPPORT_GRAPH
TEST_F(StYang, ConstraintParser22)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser22",
                "fields":[
                    {"name":"pk", "type":"uint32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|1..2.8"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser22",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(
        GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
}
#endif

TEST_F(StYang, ConstraintParser23)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"record",
                "name":"ConstraintParser23",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|1..2.8"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_JSON_CONTENT>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser24)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser24",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|1..00.3"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser25)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser25",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|1..-0.3"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser26)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser26",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|-5..-3"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser27)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser27",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|2..31*/"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser28)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser28",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"char", "nullable":false, "range":"1|2..31"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_DATATYPE_MISMATCH>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser29)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser29",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"int32", "nullable":false, "range":"1|2..31", "range":"1|2..31"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_JSON_CONTENT>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser30)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser30",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|2..31", "length":"1|2..31"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_JSON_CONTENT>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser31)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser31",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"int32", "nullable":false, "length":"1|2..31"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_DATATYPE_MISMATCH>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser32)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser32",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|-5..-3"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

#ifdef YANG_SUPPORT_GRAPH
TEST_F(StYang, ConstraintParser33)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser33",
                "fields":[
                    {"name":"pk", "type":"uint32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"0"}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser33",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(
        GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
}
#endif

TEST_F(StYang, ConstraintParser34)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser34",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F12", "type":"int32", "nullable":false, "invert_pattern":["^abc$"]}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_DATATYPE_MISMATCH>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser35)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser35",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1||2..3|||3..5"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser36)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser36",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "length":"1|2..3|3..5|"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser37)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser36",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F0", "type":"uint32", "auto_increment":true, "length":"1|2..3|3..5|"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_JSON_CONTENT>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser38)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser38",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"uint32", "nullable":false, "range":"1|-2..31"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser39)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser39",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"uint8", "nullable":false, "range":"1|2..257"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser40)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser40",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"uint8", "nullable":false, "range":""}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

TEST_F(StYang, ConstraintParser41)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser41",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"string", "nullable":false, "size":7, "length":"1|2..3|3..8"}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_JSON_CONTENT>, &step));
    EpollWaitAndCheck(step, 1);
}

#ifdef YANG_SUPPORT_GRAPH
// range约束为3-5、8-9，设置为10，不满足约束条件
TEST_F(StYang, ConstraintParser42)
{
    const char *gLabelName = "ConstraintParser42";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser42",
                "fields":[
                    {"name":"pk", "type":"uint32", "nullable":false},
                    {"name":"F0", "type":"string", "nullable":false, "length":"min..5"},
                    {"name":"F1", "type":"uint32", "nullable":false, "range":"3..5|8..9"},
                    {"name":"F2", "type":"uint32", "nullable":false, "range":"3..5|8..9"}
                ],
                "keys":[
                        {
                            "node":"ConstraintParser42",
                            "name":"test0",
                            "fields":["pk"],
                            "index":{"type":"primary"},
                            "constraints":{"unique":true}
                        }
                        ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(
        GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);

    int ret = GmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcPropValueT f1Value;
    f1Value.type = GMC_DATATYPE_UINT32;
    uint32_t f1Val = 8;
    f1Value.size = sizeof(f1Val);
    f1Value.value = &f1Val;
    strcpy(f1Value.propertyName, "F1");
    ret = GmcYangSetVertexProperty(stmt, &f1Value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcPropValueT f2Value;
    f2Value.type = GMC_DATATYPE_UINT32;
    uint32_t f2Val = 10;
    f2Value.size = sizeof(f2Val);
    f2Value.value = &f2Val;
    strcpy(f2Value.propertyName, "F2");
    ret = GmcYangSetVertexProperty(stmt, &f2Value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, gLabelName, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
}
#endif

TEST_F(StYang, ConstraintParser43)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser43",
                "fields":[
                    {"name":"pk", "type":"int32", "nullable":false},
                    {"name":"F15", "type":"double", "nullable":false, "range":"1|2..257."}
                ],
                "keys":[
                    {
                        "node":"test0",
                        "name":"test0",
                        "fields":["pk"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

#ifndef YANG_SUPPORT_GRAPH
// 补充全打散建表失败用例
TEST_F(StYang, ConstraintParser44)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser42",
                "fields":[
                    {"name":"pk", "type":"uint32", "nullable":false},
                    {"name":"F0", "type":"string", "nullable":false, "length":"min..5"},
                    {"name":"F1", "type":"uint32", "nullable":false, "range":"3..5|8..9"},
                    {"name":"F2", "type":"uint32", "nullable":false, "range":"3..5|8..9"}
                ],
                "keys":[
                        {
                            "node":"ConstraintParser42",
                            "name":"test0",
                            "fields":["pk"],
                            "index":{"type":"primary"},
                            "constraints":{"unique":true}
                        }
                        ]
            }
        ]
        )";

    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
}
#endif

// 测试pattern长度上限4k
TEST_F(StYang, ConstraintParser45)
{
    const char *testConfigJson = R"({"max_record_count":1000, "auto_increment": 1, "yang_model":1})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser45",
                "fields":[
                    {"name":"F0", "type":"uint32"},
                    {"name":"F1", "type":"string", "nullable":false, "pattern":["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"]}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser45",
                        "name":"test45",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson,
                            AsyncOperationCb<GMERR_INVALID_PROPERTY>, &step));
    EpollWaitAndCheck(step, 1);
}

// 测试pattern长度上限4k
TEST_F(StYang, ConstraintParser46)
{
    const char *testConfigJson = R"({"max_record_count":1000, "auto_increment": 1, "yang_model":1})";
    const char *testConstraintLabelJson =
        R"([
            {
                "type":"container",
                "name":"ConstraintParser45",
                "fields":[
                    {"name":"F0", "type":"uint32"},
                    {"name":"F1", "type":"string", "nullable":false, "pattern":["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"]}
                ],
                "keys":[
                    {
                        "node":"ConstraintParser45",
                        "name":"test45",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ]
        )";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, testConstraintLabelJson, testConfigJson, AsyncOperationCb<GMERR_OK>, &step));
    EpollWaitAndCheck(step, 1);
}
