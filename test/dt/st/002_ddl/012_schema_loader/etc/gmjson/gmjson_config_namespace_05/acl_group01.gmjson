{"comment": "ACLGroup表", "version": "2.0", "type": "record", "name": "acl_group", "config": {"check_validity": true, "max_record_count": 88000}, "max_record_count": 1024, "fields": [{"name": "acl_groupid", "type": "uint32", "comment": "ACLGroup的索引"}, {"name": "isipv6", "type": "uint8", "comment": "是否IPv6，1是，0否"}, {"name": "vsid", "type": "uint16", "comment": "域标志"}, {"name": "vsysid", "type": "uint32", "comment": "为防火墙提供，vsys索引"}, {"name": "acl_number", "type": "uint32", "comment": "ACL编号"}, {"name": "acl_group_type", "type": "uint32", "comment": "ACLGroup类型"}, {"name": "acl_name", "type": "fixed", "size": 65, "comment": "ACL名称"}], "keys": [{"name": "acl_group_pk", "index": {"type": "primary"}, "node": "acl_group", "fields": ["acl_groupid", "isipv6"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "acl_name_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "acl_group", "fields": ["vsid", "vsysid", "isipv6", "acl_name"], "constraints": {"unique": false}, "comment": "根据vsid、vsysid、isipv6和acl_name索引"}, {"name": "acl_number_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "acl_group", "fields": ["vsid", "vsysid", "acl_number", "isipv6"], "constraints": {"unique": false}, "comment": "根据vsid、vsysid、acl_number和isipv6索引"}]}