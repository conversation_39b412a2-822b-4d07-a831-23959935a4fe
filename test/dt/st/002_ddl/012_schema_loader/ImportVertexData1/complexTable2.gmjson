{"version": "2.0", "max_record_count": 10, "type": "record", "name": "complexTable2", "fields": [{"name": "F15", "type": "string", "size": 40}, {"name": "F16", "type": "string", "size": 40}, {"name": "F17", "type": "bytes", "size": 4}, {"name": "F19", "type": "fixed", "size": 5}, {"name": "F3", "type": "int16"}, {"name": "F9", "type": "int32"}, {"name": "F10", "type": "int64"}, {"name": "F14", "type": "time"}, {"name": "F1", "type": "uint8"}, {"name": "F2", "type": "uint8"}, {"name": "F4", "type": "uint16"}, {"name": "F5", "type": "int32"}, {"name": "F6", "type": "uint32"}, {"name": "F7", "type": "int64"}, {"name": "F8", "type": "uint64"}, {"name": "F11", "type": "float"}, {"name": "F12", "type": "double"}, {"name": "F13", "type": "boolean"}, {"name": "F18", "type": "bytes", "size": 7}, {"name": "T1", "type": "record", "fixed_array": true, "size": 8, "fields": [{"name": "T1_F1", "type": "int8"}, {"name": "T1_F2", "type": "uint8"}, {"name": "T1_F3", "type": "string", "size": 40}, {"name": "T1_F4", "type": "bytes", "size": 6}]}], "keys": [{"name": "pk", "node": "complexTable2", "fields": ["F15", "F16", "F17", "F19", "F3", "F9", "F10", "F14"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}