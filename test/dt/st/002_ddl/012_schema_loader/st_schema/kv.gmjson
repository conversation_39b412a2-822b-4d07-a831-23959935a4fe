{"name": "kv", "version": "2.0", "configs": [{"name": "board_type", "type": "string", "size": 39, "comment": "通过驱动接口从电子标签获取，对应 BoardType"}, {"name": "bar_code", "type": "string", "size": 21, "comment": "通过驱动接口从电子标签获取，对应 BarCode"}, {"name": "esn_item", "type": "string", "size": 24, "comment": "通过驱动接口从电子标签获取，对应 Item"}, {"name": "esn_description", "type": "string", "size": 247, "comment": "通过驱动接口从电子标签获取，对应 Description"}, {"name": "manu_time", "type": "string", "size": 11, "comment": "通过驱动接口从电子标签获取，对应 Manufactured"}, {"name": "vendor_name", "type": "string", "size": 38, "comment": "通过驱动接口从电子标签获取，对应 VendorName"}, {"name": "issue_number", "type": "string", "size": 7, "comment": "通过驱动接口从电子标签获取，对应 IssueNumber"}, {"name": "clei_code", "type": "string", "size": 15, "comment": "通过驱动接口从电子标签获取，对应 CLEICode"}, {"name": "esn_bom", "type": "string", "size": 45, "comment": "通过驱动接口从电子标签获取，对应 BOM"}, {"name": "board_name", "type": "string", "size": 39, "comment": "通过驱动接口从电子标签获取，对应 Model"}, {"name": "elabel_version", "type": "string", "size": 6, "comment": "通过驱动接口从电子标签获取，对应 /$ElabelVersion"}, {"name": "system_mac_num", "type": "uint32", "comment": "系统分配mac个数，调用驱动提供的接口drv_mac_get_addr_num获取"}, {"name": "system_mac", "type": "bytes", "size": 6, "comment": "系统首个可用mac地址，调用驱动提供的接口drv_mac_get_addr获取"}]}