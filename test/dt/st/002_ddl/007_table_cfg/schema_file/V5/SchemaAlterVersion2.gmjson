{"version": "2.0", "type": "record", "name": "test_table", "config": {"check_validity": true, "reboot_persistence": true}, "schema_version": 2, "fields": [{"name": "F0", "type": "uint32"}, {"name": "T1", "type": "record", "fields": [{"name": "E1", "type": "uint32"}, {"name": "E2", "type": "uint32"}]}, {"name": "F1", "type": "string"}, {"name": "T0", "type": "record", "fields": [{"name": "E0", "type": "uint32"}, {"name": "E1", "type": "uint32"}]}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32", "default": "0"}, {"name": "F4", "type": "string", "default": "add"}, {"name": "T2", "type": "record", "nullable": true, "fields": [{"name": "E3", "type": "uint32"}, {"name": "E4", "type": "uint32"}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "test_table", "fields": ["F0"], "constraints": {"unique": true}}]}