[{"destination_prefix": "destination_prefix1", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "destination_prefix2", "description": "description2", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "0", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "1", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "2", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "3", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "4", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "5", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "6", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "7", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "8", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}, {"destination_prefix": "9", "description": "description1", "fib_cfg_info": {"outgoing_interface": "outgoing_interface1", "special_next_hop": "special_next_hop1", "next_hop_address": "next_hop_address1"}, "fib_alias": {"tb_id": 1, "fa_tos": 1, "fa_type": 1, "fa_state": 1, "fa_slen": 1, "fib_info": {"fib_treeref": 1, "fib_prefsrc": 1, "fib_priority": 1, "fib_metrics": 1, "fib_nhs": 1, "fib_flags": 1, "fib_dead": 1, "fib_protocol": 1, "fib_scope": 1, "fib_type": 1, "fib_nh": [{"nh_flags": 1, "nh_weight": 1, "nh_power": 1, "nh_tclassid": 1, "nh_oif": 1, "nh_gw": 1, "nh_saddr_genid": 1, "nh_scope": 1}, {"nh_flags": 2, "nh_weight": 2, "nh_power": 2, "nh_tclassid": 2, "nh_oif": 2, "nh_gw": 2, "nh_saddr_genid": 2, "nh_scope": 2}]}}}]