[{"version": "2.0", "type": "record", "name": "type2", "fields": [{"name": "a0", "type": "uint32"}, {"name": "a1", "type": "char"}, {"name": "a2", "type": "uint8"}, {"name": "a3", "type": "int8"}, {"name": "a4", "type": "uint16"}, {"name": "a5", "type": "int16"}, {"name": "a6", "type": "int32"}, {"name": "a7", "type": "uint64"}, {"name": "a8", "type": "int32"}, {"name": "a9", "type": "boolean"}, {"name": "a10", "type": "float"}, {"name": "a11", "type": "double"}, {"name": "a12", "size": 10, "type": "string"}, {"name": "a13", "size": 1024, "type": "bytes"}, {"name": "a14", "size": 8, "type": "bitmap"}], "keys": [{"name": "table_pk", "index": {"type": "primary"}, "node": "type2", "fields": ["a0"], "constraints": {"unique": true}}]}]