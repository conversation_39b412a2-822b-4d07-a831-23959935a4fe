/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for schema upd
 * Author: jinfanglin
 * Create: 2022-08-02
 */
#include "client_common_st.h"
class ClientStructUpdateCompatible : public StClient {
public:
    sem_t *semLock = nullptr;
    ClientStructUpdateCompatible()
    {
        semLock = sem_open("lock.lock", O_CREAT | O_EXCL, S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH, 0);
    }
    ~ClientStructUpdateCompatible() override
    {
        sem_close(semLock);
        sem_unlink("lock.lock");
    }
};

static void WaitingDegradeEnd(GmcStmtT *stmt, const char *labelName)
{
    Status ret;
    uint32_t degradeProcess;
    while ((ret = GmcGetVertexLabelDegradeProgress(stmt, labelName, &degradeProcess)) == GMERR_OK) {
    };
    ASSERT_EQ(GMERR_NO_DATA, ret);
}

const char *g_myLabelName = "access_list";
const char *g_simpleCompatibleName = "simpleLabel";
const char *g_simpleCompatibleV3Name = "simpleLabelCompatibleV3";
const char *g_specComplexCompatibleV3Name = "specComplexLabelCompatibleV3";
const char *g_complexName = "complexLabel";
const char *g_genCompatibleName = "generalComplexLabel";

const uint32_t TYPICAL_FIELD_BITMAP_LEN = 16;  // 单位: bit  (建议size: 16, 8192)
const uint32_t TYPICAL_FIELD_FIXED_LEN = 16;   // 单位: byte (建议size: 16, 4096)

uint32_t gTypicalFieldBytesLen = 4096;   // 单位: byte (建议size: 16, 4096, 8019, 13312)
uint32_t gTypicalFieldStringLen = 4096;  // 单位: byte (建议size: 16, 4096, 8019, 13312)

typedef enum TagTypicalChildOperType {
    GT_TYPICAL_CHILD_OPER_INSERT = 0,
    GT_TYPICAL_CHILD_OPER_MODIFY = 1,
    GT_TYPICAL_CHILD_OPER_DELETE = 2,
} GtTypicalVertexChildOperTypeE;
typedef struct TagTypicalVertexCfg {
    int32_t startVal;     // 主键或其他非成员索引的起始值
    uint32_t count;       // 主键或其他非成员索引的数量
    int32_t startMkVal;   // 成员索引的起始值
    uint32_t childCount;  // 子节点数量
    int32_t coefficient;  // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    GmcOperationTypeE optType;                    // 操作数据类型, 通过replace/merge/insert
    GtTypicalVertexChildOperTypeE childOperType;  // 更新子节点的操作类型 (注意该配置仅update/merge操作有效)
} GtTypicalVertexCfgT;

// 简单表升级兼容性校验
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd01)
{
    GmcDropVertexLabel(syncStmt, g_simpleCompatibleName);
    // 创建简单表schema_v1
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v1.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // v1->v2 更改原本字段顺序
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v2.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, g_simpleCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // V1->v3 增加变长属性
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, g_simpleCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // V1->v4 增加索引数量
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, g_simpleCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // V1->v5 减少索引数量
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v5.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, g_simpleCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v6 升级 相同的schemaVersionId
    string schemaV6 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v6.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV6.c_str(), true, g_simpleCompatibleName);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    // v1->v7 增加node节点
    string schemaV7 = GetFileContext("./008_schema_upgrade/st_data/simple_compatible_v7.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV7.c_str(), true, g_simpleCompatibleName);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_simpleCompatibleName));
}

// 简单表升级兼容V3
TEST_F(ClientStructUpdateCompatible, structureSchemaUpdCompatibleV3)
{
    GmcDropVertexLabel(syncStmt, g_simpleCompatibleV3Name);
    // 创建简单表schema_v1
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel1_compatibleV3.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // v1->v2 新增定长字段nullable为false且未设置默认值
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel2_compatibleV3.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v3 非法版本号
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel3_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v4 新增定长字段且修改原有字段的comment信息，升级成功
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel4_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_EQ(GMERR_OK, ret);
    // v1->v5 升级时未显式设置版本号
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel5_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v6 仅修改comment信息
    string schemaV6 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel6_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV6.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v7 新增定长字段nullable为false且设置默认值，升级成功
    string schemaV7 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel7_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV7.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_EQ(GMERR_OK, ret);
    // v1->v8 新增fixed字段且size为15K，升级成功
    string schemaV8 = GetFileContext("./008_schema_upgrade/st_data/simpleLabel8_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV8.c_str(), true, g_simpleCompatibleV3Name);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_simpleCompatibleV3Name));
}

// 特殊复杂表兼容性升级校验
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd02)
{
    GmcDropVertexLabel(syncStmt, g_myLabelName);
    // 创建一个schema_v1
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_schema_base.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 基于schema_v1, 插入数据

    // 进行schema升级(第一层增加变长和带有vector的node节点)，预期升级失败
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_schema_upd1.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_myLabelName);
    ASSERT_NE(GMERR_OK, ret);
    // 进行schema升级(第一层增加变长和带有array的node节点)，预期升级失败
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_schema_upd2.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, (char *)g_myLabelName);
    ASSERT_NE(GMERR_OK, ret);
    // 进行schema升级，预期升级成功
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_schema_upd.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, (char *)g_myLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    // 子节点下增加子节点预期失败
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_schema_upd4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, (char *)g_myLabelName);
    EXPECT_NE(GMERR_OK, ret);
    string schemaV6 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_schema_upd5.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV6.c_str(), true, (char *)g_myLabelName);
    EXPECT_NE(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_myLabelName));
}

// 特殊复杂表升级兼容V3
TEST_F(ClientStructUpdateCompatible, structureSpecComplexSchemaUpdCompatibleV3)
{
    GmcDropVertexLabel(syncStmt, g_specComplexCompatibleV3Name);
    // 创建一个schema_v1
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/specComplexLabel1_compatibleV3.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // v1->v2 新增变长字段nullable为false且未设置默认值
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/specComplexLabel2_compatibleV3.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_specComplexCompatibleV3Name);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v3 非法版本号
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/specComplexLabel3_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, (char *)g_specComplexCompatibleV3Name);
    ASSERT_NE(GMERR_OK, ret);
    // v1->v4 新增变长字段且修改原有字段的comment信息，升级成功
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/specComplexLabel4_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, (char *)g_specComplexCompatibleV3Name);
    ASSERT_EQ(GMERR_OK, ret);
    // v1->v5 新增变长字段nullable为false且设置默认值，升级成功
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/specComplexLabel5_compatibleV3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, (char *)g_specComplexCompatibleV3Name);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_specComplexCompatibleV3Name));
}

// 一般复杂表：分多次升级
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd03)
{
    // 创建v1的一般复杂表，并写入数据，然后使用V1版本读取
    GmcDropVertexLabel(syncStmt, g_complexName);
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v1.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 升级到V2
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v2.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V3
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V4
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V5
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v5.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V6
    string schemaV6 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v6.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV6.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V7
    string schemaV7 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v7.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV7.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 继续升级到版本号异常的V9,版本号为2，升级失败
    string schemaV9 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v9.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV9.c_str(), true, (char *)g_complexName);
    EXPECT_NE(GMERR_OK, ret);
    // 升级到V8
    string schemaV8 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v8.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV8.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 继续升级V10，升级失败
    string schemaV10 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_schema_v10.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV10.c_str(), true, (char *)g_complexName);
    EXPECT_NE(GMERR_OK, ret);
    // prepare一个无效的版本号，预期应该报错
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_complexName, 100000, GMC_OPERATION_SCAN);
    EXPECT_NE(GMERR_OK, ret);
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_complexName));
}

// 聚簇容器相关约束
// 不配置support_undetermined_length, 非大对象定长升级为大对象, enableClusterHash开/关，预期失败
TEST_F(ClientStructUpdateCompatible, SchemaUpdateWhenSupportUndeterminedLength1)
{
    GmcDropVertexLabel(syncStmt, g_complexName);
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_fix_v1.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 升级到V2
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_32k.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_complexName));
}

// 不配置support_undetermined_length, 大对象定长升级为大对象, enableClusterHash开/关，预期成功
TEST_F(ClientStructUpdateCompatible, SchemaUpdateWhenSupportUndeterminedLength2)
{
    GmcDropVertexLabel(syncStmt, g_complexName);
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_32k.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 升级到V2
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_32k_v3.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_complexName));
}

// 配置support_undetermined_length, 非大对象定长升级为大对象, enableClusterHash开/关，预期成功
TEST_F(ClientStructUpdateCompatible, SchemaUpdateWhenSupportUndeterminedLength3)
{
    const char *cfgJson = R"({"max_record_count":100, "support_undetermined_length": true})";
    GmcDropVertexLabel(syncStmt, g_complexName);
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_fix_v1.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), cfgJson));
    // 升级到V2
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/gen_complex_32k.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_complexName));
}

TEST_F(ClientStructUpdateCompatible, testSubPushOldVertexOnDeleteWithUpdate)
{
    /* 1. create old table */

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson1 = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* 2. subscribe old table */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "delete", "msgTypes": ["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_NE(GMERR_OK, ret);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t f0V, f1V, f2V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, f0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received + 1, f1V);

            // 订阅老表看不到新字段
            ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* 3. insert 3 records to old table */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 3; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = total + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* 4. update new table */

    constexpr auto labelJson2 = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "schema_version": 1,
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcAlterVertexLabelWithName(syncStmt, labelJson2, true, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    /* 5. insert 7 records to new table */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    total = 3;

    for (; total < 10; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = total + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 因为未升级成功，所以找不到这个字段，应该失败
        uint32_t f2Value = total + 2;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* 6. delete 10 records to make data for subscription */

    total = 0;

    for (; total < 10; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &total, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* 7. wait till all events are received */

    WAIT_WHILE(received != total);
}

// 填充uint8数组
void GtFillBytes(uint8_t target[], uint32_t len, char ch, bool isEof)
{
    errno = 0;
    if (len < 2) {
        printf("len must greater than %d, actual is %d", 8, len);
    }

    (void)memset_s(target, len, ch, len);
    if (errno != GMERR_OK) {
        printf("memset failed, errno = %d, errmsg = %s", errno, strerror(errno));
    }

    if (isEof) {
        target[len - 1] = 0;
    }
}

int SetVertexLabelUpdateRootProperty(GmcStmtT *stmt, int32_t coefficient)
{
    uint8_t eleBit = abs(coefficient) % 255;
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';

    int64_t a1 = 100;
    int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t a2 = 200;
    ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t a3 = 200;
    ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    float a4 = coefficient;
    ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
    EXPECT_EQ(GMERR_OK, ret);
    double a5 = coefficient;
    ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
    GtFillBytes(bits, sizeof(bits), eleBit, false);
    GmcBitMapT a6 = {0};
    a6.beginPos = 0;
    a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
    a6.bits = bits;
    ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));

    uint8_t a7[13 * 1024] = {0};
    GtFillBytes(a7, sizeof(a7), eleFixed, false);
    ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t a8[gTypicalFieldBytesLen] = {0};
    GtFillBytes(a8, sizeof(a8), eleFixed, false);
    ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
    EXPECT_EQ(GMERR_OK, ret);

    string a9 = "helloWorld";
    ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9.c_str(), a9.length());
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int SetVertexLabelUpdateRootPropertyNew(GmcStmtT *stmt, int32_t coefficient)
{
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';
    uint8_t c11[13 * 1024] = {0};
    GtFillBytes(c11, sizeof(c11), eleFixed, false);
    int ret = GmcSetVertexProperty(stmt, "C11", GMC_DATATYPE_FIXED, &c11, sizeof(c11));
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int SetVertexLabel1AllRootProperty(GmcStmtT *stmt, int32_t coefficient)
{
    // 设置主键属性
    int32_t a0 = 1;
    int ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置非主键属性
    ret = SetVertexLabelUpdateRootProperty(stmt, coefficient);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int SetVertexLabelArrayElement(GmcNodeT *childNode, int32_t coefficient)
{
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';

    GmcNodeT *elementNode = NULL;
    int ret = GmcNodeAppendElement(childNode, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t b0 = 1;
    ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t b1 = coefficient;
    ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t b2[gTypicalFieldBytesLen] = {0};
    GtFillBytes(b2, sizeof(b2), eleBytes, false);
    ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t b3[gTypicalFieldBytesLen] = {0};
    GtFillBytes(b3, sizeof(b3), eleBytes, false);
    ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t b4[gTypicalFieldStringLen] = {0};
    GtFillBytes(b4, sizeof(b4), eleStr, true);
    ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t b5[gTypicalFieldStringLen] = {0};
    GtFillBytes(b5, sizeof(b5), eleStr, true);
    ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int InsertVertexLabel1(GmcStmtT *stmt, GtTypicalVertexCfgT vertexCfg, const char *labelName)
{
    int32_t coefficient = vertexCfg.coefficient;
    Status ret = GmcPrepareStmtByLabelName(stmt, g_myLabelName, GMC_OPERATION_INSERT);
    // 插入一条数据
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置所有根节点属性
    ret = SetVertexLabel1AllRootProperty(stmt, coefficient);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置所有子节点属性
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "M0", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SetVertexLabelArrayElement(childNode, coefficient);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int SetVertexLabel2UpdateRootPropertyNew(GmcStmtT *stmt, int32_t coefficient)
{
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    for (int i = 0; i < 1024; i++) {
        int64_t eleNum = i + coefficient;
        GmcNodeT *elementNode = NULL;
        int32_t b0 = i;
        int ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t b1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t b2[gTypicalFieldBytesLen] = {0};
        GtFillBytes(b2, sizeof(b2), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t b3[gTypicalFieldBytesLen] = {0};
        GtFillBytes(b3, sizeof(b3), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t b4[gTypicalFieldStringLen] = {0};
        GtFillBytes(b4, sizeof(b4), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t b5[gTypicalFieldStringLen] = {0};
        GtFillBytes(b5, sizeof(b5), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
        EXPECT_EQ(GMERR_OK, ret);
    }
    return GMERR_OK;
}

int SetVertexLabel2AllRootProperty(GmcStmtT *stmt, int32_t coefficient)
{
    // 设置主键属性
    int32_t a0 = 11;
    int ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置非主键属性
    ret = SetVertexLabelUpdateRootProperty(stmt, coefficient);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置非主键新增字段属性
    ret = SetVertexLabelUpdateRootPropertyNew(stmt, coefficient);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int SetVertexLabel2ArrayElement(GmcNodeT *childNode, int32_t coefficient)
{
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    uint8_t eleBit = abs(coefficient) % 255;
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';
    int ret = 0;
    for (int i = 0; i < 10; i++) {
        int64_t eleNum = i + coefficient;
        GmcNodeT *elementNode = NULL;
        ret = GmcNodeAppendElement(childNode, &elementNode);
        EXPECT_EQ(GMERR_OK, ret);

        int32_t b0 = i;
        ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t b1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t b2[gTypicalFieldBytesLen] = {0};
        GtFillBytes(b2, sizeof(b2), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t b3[gTypicalFieldBytesLen] = {0};
        GtFillBytes(b3, sizeof(b3), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t b4[gTypicalFieldStringLen] = {0};
        GtFillBytes(b4, sizeof(b4), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t b5[gTypicalFieldStringLen] = {0};
        GtFillBytes(b5, sizeof(b5), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
        EXPECT_EQ(GMERR_OK, ret);
        // c0-c10
        int32_t c0 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "C0", GMC_DATATYPE_INT32, &c0, sizeof(c0));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t c1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "C1", GMC_DATATYPE_INT64, &c1, sizeof(c1));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t c2 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "C2", GMC_DATATYPE_UINT32, &c2, sizeof(c2));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t c3 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "C3", GMC_DATATYPE_UINT64, &c3, sizeof(c3));
        EXPECT_EQ(GMERR_OK, ret);
        float c4 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "C4", GMC_DATATYPE_FLOAT, &c4, sizeof(c4));
        EXPECT_EQ(GMERR_OK, ret);
        double c5 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "C5", GMC_DATATYPE_DOUBLE, &c5, sizeof(c5));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
        GtFillBytes(bits, sizeof(bits), eleBit, false);
        GmcBitMapT c6 = {0};
        c6.beginPos = 0;
        c6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
        c6.bits = bits;
        ret = GmcNodeSetPropertyByName(elementNode, "C6", GMC_DATATYPE_BITMAP, &c6, sizeof(c6));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t c7[TYPICAL_FIELD_FIXED_LEN] = {0};
        GtFillBytes(c7, sizeof(c7), eleFixed, false);
        ret = GmcNodeSetPropertyByName(elementNode, "C7", GMC_DATATYPE_FIXED, &c7, sizeof(c7));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t c8[gTypicalFieldBytesLen] = {0};
        GtFillBytes(c8, sizeof(c8), eleFixed, false);
        ret = GmcNodeSetPropertyByName(elementNode, "C8", GMC_DATATYPE_BYTES, c8, sizeof(c8));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t c9[gTypicalFieldStringLen] = {0};
        GtFillBytes(c9, sizeof(c9), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "C9", GMC_DATATYPE_STRING, c9, strlen((char *)c9));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t c10[13 * 1024] = {0};
        GtFillBytes(c10, sizeof(c10), eleFixed, false);
        ret = GmcNodeSetPropertyByName(elementNode, "C10", GMC_DATATYPE_FIXED, &c10, sizeof(c10));
        EXPECT_EQ(GMERR_OK, ret);
    }
    return GMERR_OK;
}

int InsertVertexLabel2(GmcStmtT *stmt, GtTypicalVertexCfgT vertexCfg, const char *labelName)
{
    int32_t coefficient = vertexCfg.coefficient;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_myLabelName, 1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置所有根节点属性
    ret = SetVertexLabel2AllRootProperty(stmt, coefficient);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置所有子节点属性
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "M0", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SetVertexLabel2ArrayElement(childNode, coefficient);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int GetDataByVertexLabel1(GmcStmtT *stmt)
{
    // 用老版本读新版本数据
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, (char *)g_myLabelName, 0, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // 主键索引 A0
    int32_t a0 = 11;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(a0));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"PrimaryKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    bool isNull;
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint64_t data;
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    uint32_t strLen;
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    string strVal = "helloWorld";
    char a9[20];
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // local索引 A2=200
    uint32_t a2 = 200;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &a2, sizeof(a2));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"LocalKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // localhash索引 A3=200
    uint64_t a3 = 200;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"LocalHashKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // hashcluster索引 A3=200
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"HashClusterKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // 解析不存在的数据：老的schema解析新的schema中增加的字段，预期报GMERR_INVALID_PROPERTY，无效的属性名字
    // 解析根节点新增属性
    uint32_t addUint32Len;
    ret = GmcGetVertexPropertySizeByName(stmt, "C11", &addUint32Len);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 解析子节点新增属性
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *elementNode = NULL;
    // c0-c7
    ret = GmcNodeGetChild(rootNode, "C0", &elementNode);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcNodeGetChild(rootNode, "C1", &elementNode);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    return GMERR_OK;
}
// 用新版本读老版本的数据
int GetDataByVertexLabel2(GmcStmtT *stmt)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, (char *)g_myLabelName, 1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // 主键索引 A0
    int32_t a0 = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(a0));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"PrimaryKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    bool isNull;
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint64_t data;
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    uint32_t strLen;
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    string strVal = "helloWorld";
    char a9[20];
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // local索引 A2=200
    uint32_t a2 = 200;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &a2, sizeof(a2));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"LocalKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // localhash索引 A3=200
    uint64_t a3 = 200;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"LocalHashKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // hashcluster索引 A3=200
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, (char *)"HashClusterKey");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "A3", &data, sizeof(data), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, data);
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);

    // 解析根节点新增属性
    uint32_t addUint32Len;
    ret = GmcGetVertexPropertySizeByName(stmt, "C11", &addUint32Len);
    EXPECT_EQ(GMERR_OK, ret);

    // 解析子节点新增属性
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *elementNode = NULL;
    // c0-c7
    ret = GmcNodeGetChild(rootNode, "M0", &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t nodeVal;
    ret = GmcNodeGetPropertyByName(elementNode, "C0", &nodeVal, sizeof(nodeVal), &isNull);
    EXPECT_EQ(true, isNull);

    return GMERR_OK;
}

// 同步replace新版本大对象vertex数据
int ReplaceVertexLabel2(GmcStmtT *stmt, GtTypicalVertexCfgT vertexCfg, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, (char *)g_myLabelName, 1, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置所有根节点属性
    ret = SetVertexLabel2AllRootProperty(stmt, vertexCfg.coefficient);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置所有子节点属性
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "M0", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SetVertexLabel2ArrayElement(childNode, vertexCfg.coefficient);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 读一个replace之后的数据
    uint32_t strLen;
    bool isNull;
    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &strLen);
    EXPECT_EQ(GMERR_OK, ret);
    string strVal = "helloWorld";
    char a9[20];
    ret = GmcGetVertexPropertyByName(stmt, "A9", a9, strLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(a9, strVal.c_str(), strLen), 0);
    return GMERR_OK;
}

int DeleteVertexLabel2ByIndex(GmcStmtT *stmt, GtTypicalVertexCfgT vertexCfg, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, (char *)g_myLabelName, 1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
    EXPECT_EQ(GMERR_OK, ret);
    int32_t a0 = 11;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(a0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

// 018.小对象升级成大对象约1MB的表后的普通replace/delete新数据
// 1.DB启动成功
// 2.一般复杂表(添加record节点含所有数据类型)升级成功
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd04)
{
    GmcDropVertexLabel(syncStmt, g_myLabelName);
    // 创建一个schema_v1
    string schema_v1 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_littleobject.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schema_v1.c_str(), NULL));
    GtTypicalVertexCfgT vertexCfg = {10, 100, 10, 60, 0, GMC_OPERATION_INSERT};
    // 基于schema_v1, 插入数据
    Status ret = InsertVertexLabel1(syncStmt, vertexCfg, g_myLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    /****************** 进行schema升级，预期升级成功 ******************************/
    // 创建一个schema_v2
    string schema_v2 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_bigobject.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schema_v2.c_str(), true, (char *)g_myLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 新版本按大对象规格插入数据，对新增加的字段赋值
    // 基于schema_v2, 插入数据
    ret = InsertVertexLabel2(syncStmt, vertexCfg, g_myLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    /****************** 使用schemaV1、schemaV2来读取上述两条数据 ******************************/
    // 测试点：用老版本读新版本数据
    ret = GetDataByVertexLabel1(syncStmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 测试点：用新版本读老版本的数据
    ret = GetDataByVertexLabel2(syncStmt);
    ASSERT_EQ(GMERR_OK, ret);

    /****************** replace/delete 大对象vertex数据 ******************************/
    // 测试点：replace若干条数据
    vertexCfg = {10, 100, 10, 2, 0, GMC_OPERATION_REPLACE};
    ret = ReplaceVertexLabel2(syncStmt, vertexCfg, g_myLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 测试点: 二级索引delete数据
    ret = DeleteVertexLabel2ByIndex(syncStmt, vertexCfg, g_myLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_myLabelName));
}

const char *g_labelNameComplex = "access_list";
const char *g_labelNameComplexKey = "access_control_list_key";
void CreataSchemaV1InsertData(GmcStmtT *stmt)
{
    GmcDropVertexLabel(stmt, g_labelNameComplex);
    // 创建一个schema_v1
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_base.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), NULL));
    // 基于schema_v1, 插入数据
    Status ret = GmcPrepareStmtByLabelName(stmt, g_labelNameComplex, GMC_OPERATION_INSERT);
    // 插入一条数据
    uint32_t nftableTableFlags = 10;
    ret = GmcSetVertexProperty(stmt, "nftable_table_flags", GMC_DATATYPE_UINT32, &nftableTableFlags, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t nftableTableUse = 20;
    ret = GmcSetVertexProperty(stmt, "nftable_table_use", GMC_DATATYPE_UINT32, &nftableTableUse, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char nftableTableName[30] = "myname_access_list";
    ret = GmcSetVertexProperty(
        stmt, "nftable_table_name", GMC_DATATYPE_STRING, nftableTableName, strlen(nftableTableName));
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode = NULL;
    GmcNodeT *nftableChainNode = NULL;
    GmcNodeT *elementNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &nftableChainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(nftableChainNode, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    string nftableChainName = "myName";
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_chain_name", GMC_DATATYPE_STRING, nftableChainName.c_str(), nftableChainName.length());
    EXPECT_EQ(GMERR_OK, ret);
    string nftableChainType = "myType";
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_chain_type", GMC_DATATYPE_STRING, nftableChainType.c_str(), nftableChainType.length());
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *nftableRuleNode = NULL;
    ret = GmcNodeGetChild(nftableChainNode, "nftable_rule", &nftableRuleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(nftableRuleNode, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t nftableRuleHandle = 40;
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_rule_handle", GMC_DATATYPE_UINT32, &nftableRuleHandle, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    string nftableRuleTable = "myRule";
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_rule_table", GMC_DATATYPE_STRING, nftableRuleTable.c_str(), nftableRuleTable.length());
    EXPECT_EQ(GMERR_OK, ret);
    // 进行插入操作
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入成功后读取数据
    ret = GmcPrepareStmtByLabelName(stmt, g_labelNameComplex, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, nftableTableName, strlen(nftableTableName));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据, 直接解析
    uint32_t nftableTableUseV1 = 0;
    bool isNull;
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(nftableTableUse, nftableTableUseV1);
}

void CreateSchemaV2InsertData(GmcStmtT *stmt)
{
    /******************* 进行schema升级，预期升级成功 **************************************/
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_upd.gmjson");
    Status ret = GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, (char *)g_labelNameComplex);
    ASSERT_EQ(GMERR_OK, ret);
    // 新版本插入一条数据，对新增加的字段赋值
    // 基于schema_v2, 插入数据
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelNameComplex, 2, GMC_OPERATION_INSERT);
    // 插入一条数据
    uint32_t nftableTableFlags = 100;
    ret = GmcSetVertexProperty(stmt, "nftable_table_flags", GMC_DATATYPE_UINT32, &nftableTableFlags, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t nftableTableUse = 200;
    ret = GmcSetVertexProperty(stmt, "nftable_table_use", GMC_DATATYPE_UINT32, &nftableTableUse, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char nftableTableName1[30] = "myname_access_list+1";
    ret = GmcSetVertexProperty(
        stmt, "nftable_table_name", GMC_DATATYPE_STRING, nftableTableName1, strlen(nftableTableName1));
    EXPECT_EQ(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 31, NULL};
    uint8_t bits[32 / 8];
    uint32_t bitLen = 4;
    (void)memset_s(bits, bitLen, 0xFF, bitLen);
    bits[32 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "add_bitmap", GMC_DATATYPE_BITMAP, &bitMap, sizeof(GmcBitMapT));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t bitField16 = 1;
    ret = GmcSetVertexProperty(stmt, "add_bitfield16_1", GMC_DATATYPE_BITFIELD16, &bitField16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t bitField162 = 2;
    ret = GmcSetVertexProperty(stmt, "add_bitfield16_2", GMC_DATATYPE_BITFIELD16, &bitField162, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode = NULL;
    GmcNodeT *nftableChainNode = NULL;
    GmcNodeT *elementNode = NULL;
    GmcNodeT *nftableRuleNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &nftableChainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(nftableChainNode, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    string nftableChainName1 = "myName+1";
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_chain_name", GMC_DATATYPE_STRING, nftableChainName1.c_str(), nftableChainName1.length());
    EXPECT_EQ(GMERR_OK, ret);
    string nftableChainType1 = "myType+1";
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_chain_type", GMC_DATATYPE_STRING, nftableChainType1.c_str(), nftableChainType1.length());
    EXPECT_EQ(GMERR_OK, ret);
    // node中增加bitmap和bitField
    ret = GmcNodeSetPropertyByName(elementNode, "node_add_bitmap", GMC_DATATYPE_BITMAP, &bitMap, sizeof(GmcBitMapT));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        elementNode, "node_add_bitfield16_1", GMC_DATATYPE_BITFIELD16, &bitField16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        elementNode, "node_add_bitfield16_2", GMC_DATATYPE_BITFIELD16, &bitField162, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(nftableChainNode, "nftable_rule", &nftableRuleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(nftableRuleNode, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t nftableRuleHandle = 400;
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_rule_handle", GMC_DATATYPE_UINT32, &nftableRuleHandle, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    string nftableRuleTable1 = "myRule+1";
    ret = GmcNodeSetPropertyByName(
        elementNode, "nftable_rule_table", GMC_DATATYPE_STRING, nftableRuleTable1.c_str(), nftableRuleTable1.length());
    EXPECT_EQ(GMERR_OK, ret);

    // 对新增字段赋值，先对record节点赋值，再对定长、变长属性赋值
    GmcNodeT *nftableChainNode1 = NULL;
    ret = GmcNodeGetChild(rootNode, "nftable_chain1", &nftableChainNode1);
    EXPECT_EQ(GMERR_OK, ret);
    string nftableChainNameAdd = "myName+1+add";
    ret = GmcNodeSetPropertyByName(nftableChainNode1, "nftable_chain_name1", GMC_DATATYPE_STRING,
        nftableChainNameAdd.c_str(), nftableChainNameAdd.length());
    EXPECT_EQ(GMERR_OK, ret);
    string nftableChainTypeAdd = "myType+1+add";
    ret = GmcNodeSetPropertyByName(nftableChainNode1, "nftable_chain_type1", GMC_DATATYPE_STRING,
        nftableChainTypeAdd.c_str(), nftableChainTypeAdd.length());
    EXPECT_EQ(GMERR_OK, ret);

    string addVar = "add_var";
    ret = GmcSetVertexProperty(stmt, "add_var", GMC_DATATYPE_STRING, addVar.c_str(), addVar.length());
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t add_fixed = 200;
    ret = GmcSetVertexProperty(stmt, "add_fixed", GMC_DATATYPE_UINT32, &add_fixed, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 进行插入操作
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入成功后读取数据
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelNameComplex, 2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, nftableTableName1, strlen(nftableTableName1));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    bool isNull;
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t nftableTableUseV1 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(nftableTableUse, nftableTableUseV1);
    // 读取bitMap数据
    char bitMapArr[32];
    uint32_t bitMapSize;
    ret = GmcGetVertexPropertySizeByName(stmt, "add_bitmap", &bitMapSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "add_bitmap", bitMapArr, bitMapSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(0, memcmp(bitMapArr, bitMap.bits, bitMapSize / 8));

    // 获取node节点下的属性值
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &nftableChainNode1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(nftableChainNode1, "node_add_bitmap", bitMapArr, bitMapSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(0, memcmp(bitMapArr, bitMap.bits, bitMapSize / 8));
    uint32_t bitFieldSize;
    uint16_t bitFieldValue;

    ret = GmcNodeGetPropertySizeByName(nftableChainNode1, "node_add_bitfield16_1", &bitFieldSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcNodeGetPropertyByName(nftableChainNode1, "node_add_bitfield16_1", &bitFieldValue, bitFieldSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bitFieldValue, bitField16);

    ret = GmcNodeGetPropertySizeByName(nftableChainNode1, "node_add_bitfield16_2", &bitFieldSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcNodeGetPropertyByName(nftableChainNode1, "node_add_bitfield16_2", &bitFieldValue, bitFieldSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bitFieldValue, bitField162);
}

// 新老版本互读
void ReadDataByDiffSchema(GmcStmtT *stmt)
{
    /****************** 使用schemaV1、schemaV2来读取上述两条数据 ******************************/
    // 读新版本插入的数据
    // 使用schemaV1读上述新版本插入的数据
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelNameComplex, 1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char nftableTableName1[30] = "myname_access_list+1";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, nftableTableName1, strlen(nftableTableName1));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    bool isNull;
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t nftableTableUseV1;
    ret = GmcGetVertexPropertyByName(stmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(200u, nftableTableUseV1);
    // 解析不存在的数据：老的schema解析新的schema中增加的字段，预期报GMERR_INVALID_PROPERTY，无效的属性名字
    char addVar[40];
    uint32_t addUint32;
    uint32_t addUint32Len;
    ret = GmcGetVertexPropertySizeByName(stmt, "add_var", &addUint32Len);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcGetVertexPropertyByName(stmt, "add_var", addVar, addUint32Len, &isNull);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcGetVertexPropertyByName(stmt, "add_fixed", &addUint32, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *nftableChainNode1 = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain1", &nftableChainNode1);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    // 使用schemaV2读上述老版本插入的数据
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelNameComplex, 2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char nftableTableName[30] = "myname_access_list";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, nftableTableName, strlen(nftableTableName));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(20u, nftableTableUseV1);
    // 解析新增字段
    // 新版本schema来解析老版本插入的数据，由于老数据中新增字段没有值，所以读取来的值应该是null
    uint32_t addVarLen = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "add_var", &addVarLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "add_var", addVar, addVarLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isNull);
    ret = GmcGetVertexPropertyByName(stmt, "add_fixed", &addUint32, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isNull);
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain1", &nftableChainNode1);
    EXPECT_EQ(GMERR_OK, ret);
}

void UpdateAndReadDataByDiffSchema(GmcStmtT *syncStmt)
{
    // 使用老版本更新新数据，然后用新版本来读取数据
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(ret, GMERR_OK);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t uintValue = 2000;
    ret = GmcNodeSetPropertyByName(rootNode, "nftable_table_use", GMC_DATATYPE_UINT32, &uintValue, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(syncStmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    char nftableTableName1[30] = "myname_access_list+1";
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, nftableTableName1, strlen(nftableTableName1));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 用新版本来读取更新的数据
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, nftableTableName1, strlen(nftableTableName1));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(syncStmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    bool isNull;
    ret = GmcFetch(syncStmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t nftableTableUseV1 = 0;
    ret = GmcGetVertexPropertyByName(
        syncStmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(uintValue, nftableTableUseV1);
    // 老版本读新数据的新增字段
    // 解析不存在的数据：老的schema解析新的schema中增加的字段，预期报GMERR_INVALID_PROPERTY，无效的属性名字
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t addVarLen = 0;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "add_var", &addVarLen);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    uint32_t addUint32;
    ret = GmcGetVertexPropertyByName(syncStmt, "add_fixed", &addUint32, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *nftableChainNode1 = NULL;
    ret = GmcNodeGetChild(rootNode, "nftable_chain1", &nftableChainNode1);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    // 使用新版本更新旧数据，然后老版本读取老数据
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    uintValue = 20000;
    ret = GmcNodeSetPropertyByName(rootNode, "nftable_table_use", GMC_DATATYPE_UINT32, &uintValue, sizeof(uint32_t));
    GmcBitMapT bitMap = {0, 31, NULL};
    uint8_t bits[32 / 8];
    uint32_t bitLen = 4;
    (void)memset_s(bits, bitLen, 0xFF, bitLen);
    bits[32 / 8 - 1] = '\0';
    bits[32 / 8 - 2] = 0xbb;
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(syncStmt, "add_bitmap", GMC_DATATYPE_BITMAP, &bitMap, sizeof(GmcBitMapT));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newFixedValue = 10086;
    ret = GmcSetVertexProperty(syncStmt, "add_fixed", GMC_DATATYPE_UINT32, &newFixedValue, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);

    uint16_t bitField16 = 11;
    ret = GmcSetVertexProperty(syncStmt, "add_bitfield16_1", GMC_DATATYPE_BITFIELD16, &bitField16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t bitField162 = 22;
    ret = GmcSetVertexProperty(syncStmt, "add_bitfield16_2", GMC_DATATYPE_BITFIELD16, &bitField162, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(syncStmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    char nftableTableName[30] = "myname_access_list";
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, nftableTableName, strlen(nftableTableName));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 用老版本来读取老数据的更新版本
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, nftableTableName, strlen(nftableTableName));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(syncStmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    ret = GmcFetch(syncStmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(
        syncStmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(uintValue, nftableTableUseV1);

    // 新版本读老数据的新增字段
    // 老数据中新增字段的值应该为NULL
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, nftableTableName, strlen(nftableTableName));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(syncStmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    ret = GmcFetch(syncStmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeByName(syncStmt, "add_var", &addVarLen);
    EXPECT_EQ(GMERR_OK, ret);
    char addVar[40];
    ret = GmcGetVertexPropertyByName(syncStmt, "add_var", addVar, addVarLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isNull);
    ret = GmcGetVertexPropertyByName(syncStmt, "add_fixed", &addUint32, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(addUint32, newFixedValue);
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain1", &nftableChainNode1);
    EXPECT_EQ(GMERR_OK, ret);
    // 新版本读取老数据中新增的bitmap
    // 读取bitMap数据
    char bitMapArr[32];
    uint32_t bitMapSize;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "add_bitmap", &bitMapSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(syncStmt, "add_bitmap", bitMapArr, bitMapSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(0, memcmp(bitMapArr, bitMap.bits, bitMapSize / 8));

    // 新版本读取老数据中新增的bitField
    uint32_t bitFieldSize;
    uint16_t bitFieldValue;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "add_bitfield16_1", &bitFieldSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(syncStmt, "add_bitfield16_1", &bitFieldValue, bitFieldSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bitFieldValue, bitField16);

    ret = GmcGetVertexPropertySizeByName(syncStmt, "add_bitfield16_2", &bitFieldSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(syncStmt, "add_bitfield16_2", &bitFieldValue, bitFieldSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bitFieldValue, bitField162);

    // 用新版本来读取更新的新数据
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameComplex, 2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, nftableTableName1, strlen(nftableTableName1));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(syncStmt, g_labelNameComplexKey);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);
    // 只有一条数据，直接解析
    ret = GmcFetch(syncStmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(
        syncStmt, "nftable_table_use", &nftableTableUseV1, sizeof(nftableTableUseV1), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, false);
    EXPECT_EQ(2000u, nftableTableUseV1);
    ret = GmcGetVertexPropertySizeByName(syncStmt, "add_var", &addVarLen);
    EXPECT_EQ(GMERR_OK, ret);
    char addVar1[40];
    ret = GmcGetVertexPropertyByName(syncStmt, "add_var", addVar1, addVarLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    string addVars = "add_var";
    EXPECT_EQ(0, strcmp(addVar1, addVars.c_str()));
    ret = GmcGetVertexPropertyByName(syncStmt, "add_fixed", &addUint32, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(200u, addUint32);
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain1", &nftableChainNode1);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t chainNameLen;
    char chainName[50];
    ret = GmcNodeGetPropertySizeByName(nftableChainNode1, "nftable_chain_name1", &chainNameLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(nftableChainNode1, "nftable_chain_name1", chainName, chainNameLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(0, strcmp(chainName, (char *)"myName+1+add"));
    ret = GmcNodeGetPropertySizeByName(nftableChainNode1, "nftable_chain_type1", &chainNameLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(nftableChainNode1, "nftable_chain_type1", chainName, chainNameLen, &isNull);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(0, strcmp(chainName, (char *)"myType+1+add"));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_labelNameComplex));
}

// 用例：一般复杂表：创建一个schema，写入数据，然后进行升级，升级后，新版本写入数据（新增字段要赋值），此时用新版本
//      查阅新老版本中的数据，然后在用新老版本查阅同一条数据。
// 前置条件：1、db启动成功；2、创建一般复杂表成功。
// 操作：不同版本插入数据，然后新老版本查阅数据
// 预期：可以正确查询到数据。
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd05)
{
    CreataSchemaV1InsertData(syncStmt);
    CreateSchemaV2InsertData(syncStmt);
    ReadDataByDiffSchema(syncStmt);
    UpdateAndReadDataByDiffSchema(syncStmt);
}

TEST_F(ClientStructUpdateCompatible, structureSchemaUpd06)
{
    // 创建v1的一般简单表，并写入数据，然后使用V1版本读取
    GmcDropVertexLabel(syncStmt, g_simpleCompatibleName);
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/partitionSimpleLabel.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 升级到V2
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/partitionSimpleLabel1.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_simpleCompatibleName);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_simpleCompatibleName));
}

// 一般复杂表兼容性校验
// 情景: 1、根节点增加非record node节点，预期失败
//       2、非根节点下增加各类node节点，预期失败
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd07)
{
    GmcDropVertexLabel(syncStmt, g_genCompatibleName);
    // 创建一张表
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabel.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 情景1: 根节点增加vector node、array node
    Status ret;
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabel_compatible1.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabel_compatible2.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // 情景2：非根节点下增加vector node、array node、record node
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabel_compatible3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabel_compatible4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    string schemaV6 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabel_compatible5.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV6.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // 正常升级，预期成功
    string schemaV7 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabelUpd1.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV7.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_EQ(GMERR_OK, ret);
    // 再次升级，预期成功
    string schemaV8 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabelUpd2.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV8.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_EQ(GMERR_OK, ret);
    // 升级到相同版本，预期失败
    string schemaV9 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabelUpd2.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV9.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    // 升级到一个旧版本，预期失败
    string schemaV10 = GetFileContext("./008_schema_upgrade/st_data/generalComplexLabelUpd1.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV10.c_str(), true, (char *)g_genCompatibleName);
    ASSERT_NE(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_genCompatibleName));
}

// 单层一般复杂表：增加node节点
TEST_F(ClientStructUpdateCompatible, structureSchemaUpd08)
{
    // 创建v1的一般复杂表
    GmcDropVertexLabel(syncStmt, g_complexName);
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v1.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));
    // 升级到V2，新增定长字段
    string schemaV2 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v2.gmjson");
    Status ret = GmcAlterVertexLabelWithName(syncStmt, schemaV2.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V3，新增变长字段，不允许为空，失败
    string schemaV3 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v3.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV3.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 升级到V4，新增变长字段，带默认值
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V5，新增node节点
    string schemaV5 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v5.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV5.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V6，node节点新增属性
    string schemaV6 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v6.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV6.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V7，新增node节点
    string schemaV7 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v7.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV7.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 升级到V8，仅在最后一个node中新增属性
    string schemaV8 = GetFileContext("./008_schema_upgrade/st_data/complex_schema_level1_v8.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV8.c_str(), true, (char *)g_complexName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_complexName));
}

TEST_F(ClientStructUpdateCompatible, structureSchemaUpd)
{
    const char *structureSchemaUpdLabelName = "ip4forward00000";
    for (uint32_t i = 0; i < 10; i++) {
        string oriSchema = GetFileContext("./008_schema_upgrade/st_data/fixed_schema_struct.gmjson");
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
        string updSchema = GetFileContext("./008_schema_upgrade/st_data/fixed_schema_struct_upd.gmjson");
        Status ret = GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, structureSchemaUpdLabelName));
    }
}

TEST_F(ClientStructUpdateCompatible, structureSchemaUpdWithName)
{
    const char *structureSchemaUpdLabelName1 = "ip4forward11111";
    for (uint32_t i = 0; i < 10; i++) {
        string oriSchema = GetFileContext("./008_schema_upgrade/st_data/fixed_schema_struct.gmjson");
        ASSERT_EQ(
            GMERR_OK, GmcCreateVertexLabelWithName(syncStmt, oriSchema.c_str(), NULL, structureSchemaUpdLabelName1));
        string updSchema = GetFileContext("./008_schema_upgrade/st_data/fixed_schema_struct_upd.gmjson");
        Status ret = GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, structureSchemaUpdLabelName1);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, structureSchemaUpdLabelName1));
    }
}

TEST_F(ClientStructUpdateCompatible, structureSchemaUpdBatch)
{
    const char *vertexLabel1 = "vsys";
    const char *vertexLabel2 = "vsys::rule";
    const char *vertexLabel3 = "vsys::rule::source_ip";
    for (uint32_t i = 0; i < 10; i++) {
        string oriSchema = GetFileContext("./008_schema_upgrade/st_data/client_vertexLabelBatch.json");
        ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
        string updSchema = GetFileContext("./008_schema_upgrade/st_data/client_vertexLabelBatch_upd.gmjson");
        Status ret = GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, vertexLabel1));
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, vertexLabel2));
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, vertexLabel3));
    }
}

TEST_F(ClientStructUpdateCompatible, structureSchemaUpdByBatch)
{
    const char *batchLabelName1 = "label1";
    const char *batchLabelName2 = "label2";
    const char *batchLabelName3 = "label3";
    GmcBatchOptionT batchOption;
    ASSERT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    ASSERT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI));
    ASSERT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    GmcBatchT *batch = NULL;
    ASSERT_EQ(GMERR_OK, GmcBatchPrepare(syncConn, &batchOption, &batch));
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/fixed_schema_struct.gmjson");
    // 批量添加DDL命令：建表
    ASSERT_EQ(
        GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, batchLabelName1, oriSchema.c_str(), NULL));
    ASSERT_EQ(
        GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, batchLabelName2, oriSchema.c_str(), NULL));
    ASSERT_EQ(
        GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, batchLabelName3, oriSchema.c_str(), NULL));
    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchRetT batchRet;
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    EXPECT_EQ(3U, totalNum);
    EXPECT_EQ(3U, successNum);

    ASSERT_EQ(GMERR_OK, GmcBatchReset(batch));
    // 批量添加DDL命令：表结构升级
    string updSchema = GetFileContext("./008_schema_upgrade/st_data/fixed_schema_struct_upd.gmjson");
    ASSERT_EQ(
        GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_ALTER_VERTEX_LABEL, batchLabelName1, updSchema.c_str(), NULL));
    ASSERT_EQ(
        GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_ALTER_VERTEX_LABEL, batchLabelName2, updSchema.c_str(), NULL));
    ASSERT_EQ(
        GMERR_OK, GmcBatchAddDDL(batch, GMC_OPERATION_ALTER_VERTEX_LABEL, batchLabelName3, updSchema.c_str(), NULL));
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    EXPECT_EQ(3U, totalNum);
    EXPECT_EQ(3U, successNum);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, batchLabelName1));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, batchLabelName2));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, batchLabelName3));
}

const char *g_labelNameIp4 = "ip4forward00000";
char g_labelName[20] = "ip4forward00000";

Status SimpleVertexInsert(GmcStmtT *syncStmt)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t int32Value;
    char *f20Value = (char *)"10.121212";
    bool f21Value = true;
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t byteNum = 128 / 8;
    uint8_t bits[byteNum];
    memset_s(bits, byteNum, 0xff, byteNum);
    bits[byteNum - 1] = '\0';
    bitMap.bits = bits;
    for (int32_t i = 0; i < 10; ++i) {
        int32Value = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F3", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F20", GMC_DATATYPE_FIXED, f20Value, strlen(f20Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F21", GMC_DATATYPE_BOOL, &f21Value, sizeof(bool));
        EXPECT_EQ(GMERR_OK, ret);
        float f22Value = 22 * i;
        ret = GmcSetVertexProperty(syncStmt, "F22", GMC_DATATYPE_FLOAT, &f22Value, sizeof(float));
        EXPECT_EQ(GMERR_OK, ret);
        double f23Value = 23 * i;
        ret = GmcSetVertexProperty(syncStmt, "F23", GMC_DATATYPE_DOUBLE, &f23Value, sizeof(double));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F24", GMC_DATATYPE_BITMAP, &bitMap, sizeof(GmcBitMapT));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t f25Value = 25 * i;
        ret = GmcSetVertexProperty(syncStmt, "F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(uint8_t));
        ret = GmcExecute(syncStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void SimpleVertexFetch(GmcStmtT *syncStmt)
{
    Status ret;
    bool isFinish;
    int32_t propValue = 1;
    int32_t getValue;
    bool isNull;
    uint8_t byteNum = 128 / 8;
    uint8_t bits[byteNum];
    memset_s(bits, byteNum, 0xff, byteNum);
    bits[byteNum - 1] = '\0';
    char *f20Value = (char *)"10.121212";
    bool f21Value = true;

    while (1) {
        ASSERT_EQ(GmcFetch(syncStmt, &isFinish), GMERR_OK);
        if (isFinish) {
            break;
        }
        EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F0", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
        EXPECT_EQ(propValue, getValue);
        EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F1", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
        EXPECT_EQ(propValue, getValue);
        EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F2", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
        EXPECT_EQ(propValue, getValue);
        EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F3", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
        EXPECT_EQ(propValue, getValue);

        char getF20[strlen(f20Value)];
        ret = GmcGetVertexPropertyByName(syncStmt, "F20", &getF20, strlen(f20Value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);

        bool getF21;
        ret = GmcGetVertexPropertyByName(syncStmt, "F21", &getF21, sizeof(bool), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(f21Value, getF21);

        float f22Value = 22 * propValue;
        float getf22;
        ret = GmcGetVertexPropertyByName(syncStmt, "F22", &getf22, sizeof(float), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(f22Value, getf22);

        double f23Value = 23 * propValue;
        double getf23;
        ret = GmcGetVertexPropertyByName(syncStmt, "F23", &getf23, sizeof(double), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(f23Value, getf23);

        uint8_t getbits[128 / 8];
        getbits[128 / 8 - 1] = '\0';
        uint32_t size;
        ret = GmcGetVertexPropertySizeByName(syncStmt, "F24", &size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(syncStmt, "F24", &getbits, size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(memcmp(getbits, bits, 16), 0);
        uint8_t f25Value = 25 * propValue;
        uint8_t getf25;
        ret = GmcGetVertexPropertyByName(syncStmt, "F25", &getf25, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(f25Value, getf25);
        propValue++;
    }
}

void SimpleVertexScanByIndex(GmcStmtT *syncStmt, int32_t keyValue)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(syncStmt, "ip4_key"));
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void SimpleVertexScanByHashCluster(GmcStmtT *syncStmt, int32_t keyValue)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(syncStmt, "ip4_hashcluster"));
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void SimpleVertexScanByLocal(GmcStmtT *syncStmt, int32_t keyValue)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(syncStmt, "ip4_key_local"));
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void SimpleVertexDelete(GmcStmtT *syncStmt)
{
    Status ret = GmcPrepareStmtByLabelName(syncStmt, g_labelNameIp4, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t int32Value = 1;
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "ip4_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void SimpleVertexUpdate(GmcStmtT *syncStmt)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t keyValue = 1;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(syncStmt, "ip4_key"));
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t newValue = 2;

    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &newValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_INT32, &newValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(syncStmt, "F3", GMC_DATATYPE_INT32, &newValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    char *f20Value = (char *)"11.212121";
    bool f21Value = false;
    ret = GmcSetVertexProperty(syncStmt, "F20", GMC_DATATYPE_FIXED, f20Value, strlen(f20Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F21", GMC_DATATYPE_BOOL, &f21Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    float f22Value = 2;
    ret = GmcSetVertexProperty(syncStmt, "F22", GMC_DATATYPE_FLOAT, &f22Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double f23Value = 2;
    ret = GmcSetVertexProperty(syncStmt, "F23", GMC_DATATYPE_DOUBLE, &f23Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t byteNum = 128 / 8;
    uint8_t bits[byteNum];
    memset_s(bits, byteNum, 0x0, byteNum);
    bits[byteNum - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(syncStmt, "F24", GMC_DATATYPE_BITMAP, &bitMap, sizeof(GmcBitMapT));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f25Value = 2;
    ret = GmcSetVertexProperty(syncStmt, "F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(uint8_t));

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    SimpleVertexScanByIndex(syncStmt, 1);

    bool isFinish;
    int32_t getValue;
    bool isNull;
    ASSERT_EQ(GmcFetch(syncStmt, &isFinish), GMERR_OK);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F0", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)1, getValue);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F1", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)2, getValue);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F2", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)2, getValue);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F3", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)2, getValue);

    char getF20[strlen(f20Value)];
    ret = GmcGetVertexPropertyByName(syncStmt, "F20", &getF20, strlen(f20Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);

    bool getF21;
    ret = GmcGetVertexPropertyByName(syncStmt, "F21", &getF21, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f21Value, getF21);

    float getf22;
    ret = GmcGetVertexPropertyByName(syncStmt, "F22", &getf22, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f22Value, getf22);

    double getf23;
    ret = GmcGetVertexPropertyByName(syncStmt, "F23", &getf23, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f23Value, getf23);

    uint8_t getbits[128 / 8];
    getbits[128 / 8 - 1] = '\0';
    uint32_t size;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "F24", &size);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(syncStmt, "F24", &getbits, size, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(memcmp(getbits, bits, 16), 0);
    uint8_t getf25;
    ret = GmcGetVertexPropertyByName(syncStmt, "F25", &getf25, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f25Value, getf25);
}

void SimpleVertexUpdateWithOldVersion(GmcStmtT *syncStmt)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 0, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t keyValue = 1;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(syncStmt, "ip4_key"));
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t newValue = 3;

    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &newValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_INT32, &newValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(syncStmt, "F3", GMC_DATATYPE_INT32, &newValue, sizeof(int32_t));
    EXPECT_NE(GMERR_OK, ret);

    char *f20Value = (char *)"11.212121";
    bool f21Value = false;
    ret = GmcSetVertexProperty(syncStmt, "F20", GMC_DATATYPE_FIXED, f20Value, strlen(f20Value));
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F21", GMC_DATATYPE_BOOL, &f21Value, sizeof(bool));
    EXPECT_NE(GMERR_OK, ret);
    float f22Value = 2;
    ret = GmcSetVertexProperty(syncStmt, "F22", GMC_DATATYPE_FLOAT, &f22Value, sizeof(float));
    EXPECT_NE(GMERR_OK, ret);
    double f23Value = 2;
    ret = GmcSetVertexProperty(syncStmt, "F23", GMC_DATATYPE_DOUBLE, &f23Value, sizeof(double));
    EXPECT_NE(GMERR_OK, ret);
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t byteNum = 128 / 8;
    uint8_t bits[byteNum];
    memset_s(bits, byteNum, 0x0, byteNum);
    bits[byteNum - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(syncStmt, "F24", GMC_DATATYPE_BITMAP, &bitMap, sizeof(GmcBitMapT));
    EXPECT_NE(GMERR_OK, ret);
    uint8_t f25Value = 2;
    ret = GmcSetVertexProperty(syncStmt, "F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(uint8_t));

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    SimpleVertexScanByIndex(syncStmt, 1);

    bool isFinish;
    int32_t getValue;
    bool isNull;
    ASSERT_EQ(GmcFetch(syncStmt, &isFinish), GMERR_OK);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F0", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)1, getValue);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F1", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)3, getValue);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F2", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)3, getValue);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F3", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ((int32_t)2, getValue);

    char getF20[strlen(f20Value)];
    ret = GmcGetVertexPropertyByName(syncStmt, "F20", &getF20, strlen(f20Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);

    bool getF21;
    ret = GmcGetVertexPropertyByName(syncStmt, "F21", &getF21, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f21Value, getF21);

    float getf22;
    ret = GmcGetVertexPropertyByName(syncStmt, "F22", &getf22, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f22Value, getf22);

    double getf23;
    ret = GmcGetVertexPropertyByName(syncStmt, "F23", &getf23, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f23Value, getf23);

    uint8_t getbits[128 / 8];
    getbits[128 / 8 - 1] = '\0';
    uint32_t size;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "F24", &size);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(syncStmt, "F24", &getbits, size, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(memcmp(getbits, bits, 16), 0);
    uint8_t getf25;
    ret = GmcGetVertexPropertyByName(syncStmt, "F25", &getf25, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f25Value, getf25);
}

// 简单表末尾添加string字段进行升级失败
TEST_F(ClientStructUpdateCompatible, simpleSchemaAlterfailed)
{
    GmcDropVertexLabel(syncStmt, g_labelNameIp4);
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
    string updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_addVarProp.gmjson");
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
}

// 连续升级八次
TEST_F(ClientStructUpdateCompatible, SchemaAlterCount)
{
    GmcDropVertexLabel(syncStmt, g_labelNameIp4);
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
    uint8_t pathMax = 128;
    char path[pathMax] = {0};
    for (uint32_t i = 1; i <= 7; i++) {
        sprintf_s(path, pathMax, "./008_schema_upgrade/st_data/simpleVertex_alter%" PRIu32 ".gmjson", i);
        string updSchema = GetFileContext(path);
        EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    }
    // 升级第8次失败
    sprintf_s(path, pathMax, "./008_schema_upgrade/st_data/simpleVertex_alter%" PRIu32 ".gmjson", 8);
    string updSchema = GetFileContext(path);
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
}

// 复杂表升级失败
TEST_F(ClientStructUpdateCompatible, complexSchemaAlterfailed)
{
    GmcDropVertexLabel(syncStmt, g_labelNameIp4);
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/complexSchema.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
    string updSchema = GetFileContext("./008_schema_upgrade/st_data/complexSchema_addFixedLast.gmjson");
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    updSchema = GetFileContext("./008_schema_upgrade/st_data/complexSchema_addRecordLast.gmjson");
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
}

// 末尾添加符合的所有定长字段类型数据进行表升级后普通insert/delete新数据
TEST_F(ClientStructUpdateCompatible, simpleSchemaAlterInsertAndDelete)
{
    GmcDropVertexLabel(syncStmt, g_labelNameIp4);
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
    string updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter1.gmjson");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    ASSERT_EQ(GMERR_OK, SimpleVertexInsert(syncStmt));

    SimpleVertexScanByIndex(syncStmt, 1);
    SimpleVertexFetch(syncStmt);
    SimpleVertexScanByHashCluster(syncStmt, 1);
    SimpleVertexFetch(syncStmt);
    SimpleVertexScanByLocal(syncStmt, 1);
    SimpleVertexFetch(syncStmt);
    SimpleVertexDelete(syncStmt);
}

// 表升级测试version
TEST_F(ClientStructUpdateCompatible, simpleSchemaAlterTestVersion)
{
    GmcDropVertexLabel(syncStmt, g_labelNameIp4);
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
    string updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter5.gmjson");
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter4.gmjson");
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter_maxVersion.gmjson");
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter_maxValidVersion.gmjson");
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
    updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter8.gmjson");
    EXPECT_NE(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
}

// 预置操作：建表V0，然后升级到v1,用老版本插入一条数据，新版本来读取数据
void SimpleVertexInsertAndReadData(GmcStmtT *syncStmt)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t int32Value = 1000;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 使用新版本去读老版本插入的新数据
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_labelNameIp4, 1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t keyValue = int32Value;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(syncStmt, "ip4_key"));
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int32_t getValue;
    bool isNull;
    ASSERT_EQ(GmcFetch(syncStmt, &isFinish), GMERR_OK);
    EXPECT_EQ(GmcGetVertexPropertyByName(syncStmt, "F0", &getValue, sizeof(int32_t), &isNull), GMERR_OK);
    EXPECT_EQ(int32Value, getValue);
    uint8_t byteNum = 128 / 8;
    uint8_t bits[byteNum];
    memset_s(bits, byteNum, 0x0, byteNum);
    uint8_t readBits[byteNum];
    memset_s(readBits, byteNum, 0x0, byteNum);
    uint32_t size;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "F24", &size);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(syncStmt, "F24", &bits, size, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(memcmp(readBits, bits, 16), 0);
}

// 老版本更新新版本的数据
TEST_F(ClientStructUpdateCompatible, SchemaAlterInsertAndReadData)
{
    GmcDropVertexLabel(syncStmt, g_labelNameIp4);
    string oriSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, oriSchema.c_str(), NULL));
    pid_t newtask = fork();
    if (newtask < 0) {
        printf("new task fail ]\n");
    } else if (newtask == 0) {  // 子进程
        string updSchema = GetFileContext("./008_schema_upgrade/st_data/simpleVertex_alter1.gmjson");
        ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(syncStmt, updSchema.c_str(), true, g_labelName));
        ASSERT_EQ(GMERR_OK, SimpleVertexInsert(syncStmt));
        SimpleVertexInsertAndReadData(syncStmt);
        SimpleVertexUpdate(syncStmt);
        sem_post(semLock);
        exit(0);
    } else {
        sem_wait(semLock);
        SimpleVertexUpdateWithOldVersion(syncStmt);
        ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, g_labelNameIp4));
    }
}

// 测试场景：建立表V1->V2进行订阅，此时降级发现失败，然后取消订阅后，降级，此时订阅降级后的版本，无法读到降级的字段。
TEST_F(ClientStructUpdateCompatible, subVertexWithGoBackSchemaTest)
{
    /* 1. create old table */

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson1 = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* 3. update new table */

    constexpr auto labelJson2 = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "schema_version": 1,
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcAlterVertexLabelWithName(syncStmt, labelJson2, true, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    /* 4. subscribe new table */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "delete", "msgTypes": ["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_NE(GMERR_OK, ret);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t f0V, f1V, f2V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, f0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received + 1, f1V);

            ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config2;
    config2.subsName = "subVertexLabel2";
    config2.configJson = R"(
    {
        "label_name": "subLabel1",
        "schema_version": 1,
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "delete", "msgTypes": ["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback2 = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_NE(GMERR_OK, ret);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t f0V, f1V, f2V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, f0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received + 1, f1V);

            ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            if (received > 2) {
                EXPECT_FALSE(isNull);
                EXPECT_EQ(received + 1, f1V);
            } else {
                EXPECT_TRUE(isNull);
            }
        }
    };
    uint32_t received2 = 0;
    ret = GmcSubscribe(syncStmt, &config2, subConn, callback2, &received2);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config2.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* 2. insert 3 records to old table */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 0;
    for (; total < 3; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = total + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* 5. insert 7 records to new table */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    total = 3;

    for (; total < 10; ++total) {
        ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, labelName, 1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = total + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = total + 2;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* 6. delete 10 records to make data for subscription */

    total = 0;

    for (; total < 10; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &total, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* 7. wait till all events are received */
    // 降级
    ret = GmcDegradeVertexLabel(syncStmt, labelName, 0);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    // 取消订阅
    ret = GmcUnSubscribe(syncStmt, config2.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 降级成功
    ret = GmcDegradeVertexLabel(syncStmt, labelName, 0);
    EXPECT_EQ(GMERR_OK, ret);
    WaitingDegradeEnd(syncStmt, labelName);
    // 订阅已降级的版本
    ret = GmcSubscribe(syncStmt, &config2, subConn, callback2, &received2);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // 再次升级
    constexpr auto labelJson3 = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "schema_version": 1,
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F3", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcAlterVertexLabelWithName(syncStmt, labelJson3, true, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    // 重新订阅，
    ret = GmcSubscribe(syncStmt, &config2, subConn, callback2, &received2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试场景：带有自增列的表可升级,但是升级的字段不能是自增列
TEST_F(ClientStructUpdateCompatible, autoIncPropUpgrade)
{
    constexpr auto labelName = "simpleLabel";
    string ori_schema = GetFileContext("./008_schema_upgrade/st_data/autoincrementSimpleLabel.gmjson");
    string upd_schema1 = GetFileContext("./008_schema_upgrade/st_data/autoincrementSimpleLabel1.gmjson");
    string upd_schema2 = GetFileContext("./008_schema_upgrade/st_data/autoincrementSimpleLabel2.gmjson");
    const char *labelConfig = R"(
        {
            "max_record_count":1000000,
            "auto_increment":10,
            "support_undetermined_length":true,
            "isFastReadUncommitted":1,
            "enableTableLock":0
    })";
    /* 创建一张带有自增列的表 */
    auto ret = GmcCreateVertexLabel(syncStmt, ori_schema.c_str(), labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* 升级带有自增列的表，新增的字段是非自增列，预期是能升级成功的 */
    ret = GmcAlterVertexLabelWithName(syncStmt, upd_schema1.c_str(), true, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    /* 升级带有自增列的表，新增的字段是自增列，预期是升级失败的 */
    ret = GmcAlterVertexLabelWithName(syncStmt, upd_schema2.c_str(), true, labelName);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);
}

// 测试场景：升级的字段不能是自增列
TEST_F(ClientStructUpdateCompatible, autoIncPropUpgrade2)
{
    constexpr auto labelName = "simpleLabel";
    string ori_schema = GetFileContext("./008_schema_upgrade/st_data/autoincrementSimpleLabel0.gmjson");
    string upd_schema1 = GetFileContext("./008_schema_upgrade/st_data/autoincrementSimpleLabel.gmjson");
    const char *labelConfig = R"(
        {
            "max_record_count":1000000,
            "auto_increment":10,
            "support_undetermined_length":true,
            "isFastReadUncommitted":1,
            "enableTableLock":0
    })";
    /* 创建一张普通的表，不带有自增列 */
    auto ret = GmcCreateVertexLabel(syncStmt, ori_schema.c_str(), labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };
    /* 升级普通的表，新增的字段是自增列，预期是升级失败的 */
    ret = GmcAlterVertexLabelWithName(syncStmt, upd_schema1.c_str(), true, labelName);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);
}
