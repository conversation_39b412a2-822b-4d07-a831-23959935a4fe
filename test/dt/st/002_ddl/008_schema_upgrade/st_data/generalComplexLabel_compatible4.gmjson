[{"type": "record", "name": "generalComplexLabel", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "size": 100, "nullable": true}, {"name": "F15", "type": "fixed", "nullable": false, "size": 16}, {"name": "F16", "type": "bytes", "nullable": false, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "F17", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F18", "type": "uint32: 17", "nullable": false, "default": "0x1ffff"}, {"name": "F19", "type": "bitmap", "size": 16}, {"name": "F20", "type": "uint32", "nullable": false}, {"name": "F21", "type": "uint32", "nullable": false}, {"name": "T1", "type": "record", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "V4", "type": "int16", "nullable": true}, {"name": "V5", "type": "uint16", "nullable": true}, {"name": "V6", "type": "int8", "nullable": true}, {"name": "V7", "type": "uint8", "nullable": true}, {"name": "V8", "type": "boolean", "nullable": true}, {"name": "V9", "type": "float", "nullable": true}, {"name": "V10", "type": "double", "nullable": true}, {"name": "V11", "type": "time", "nullable": true}, {"name": "V12", "type": "char", "nullable": true}, {"name": "V13", "type": "uchar", "nullable": true}, {"name": "V14", "type": "string", "size": 100, "nullable": true}, {"name": "V15", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "V16", "type": "bytes", "nullable": false, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "V17", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "V18", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "V19", "type": "uint32: 17", "nullable": false, "default": "0x1ffff"}, {"name": "V20", "type": "uint64: 35", "nullable": false, "default": "0x7ffffffff"}, {"name": "N1", "type": "record", "array": true, "size": 3, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "V4", "type": "int16", "nullable": true}, {"name": "V5", "type": "uint16", "nullable": true}, {"name": "V6", "type": "int8", "nullable": true}, {"name": "V7", "type": "uint8", "nullable": true}, {"name": "V8", "type": "boolean", "nullable": true}, {"name": "V9", "type": "float", "nullable": true}, {"name": "V10", "type": "double", "nullable": true}, {"name": "V11", "type": "time", "nullable": true}, {"name": "V12", "type": "char", "nullable": true}, {"name": "V13", "type": "uchar", "nullable": true}, {"name": "V14", "type": "string", "size": 100, "nullable": true}, {"name": "V15", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "V16", "type": "bytes", "nullable": false, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "V17", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "V18", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "V19", "type": "uint32: 17", "nullable": false, "default": "0x1ffff"}, {"name": "V20", "type": "uint64: 35", "nullable": false, "default": "0x7ffffffff"}]}]}], "keys": [{"node": "generalComplexLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "generalComplexLabel", "name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"node": "generalComplexLabel", "name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "generalComplexLabel", "name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "generalComplexLabel", "name": "lpm6_key", "fields": ["F20", "F21", "F15", "F7"], "index": {"type": "lpm6_tree_bitmap"}, "constraints": {"unique": true}}, {"node": "T1", "name": "member_key", "index": {"type": "none"}, "fields": ["V0"], "constraints": {"unique": true}}]}]