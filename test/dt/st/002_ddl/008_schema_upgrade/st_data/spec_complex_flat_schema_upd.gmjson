{"comment": "前缀表", "version": "2.0", "type": "record", "name": "ip4forward00001", "config": {"check_validity": true}, "schema_version": 20, "special_complex": true, "max_record_count": 4000000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "originString", "type": "string", "size": 8}, {"name": "newString", "type": "string", "size": 8}], "keys": [{"name": "ip4_key", "index": {"type": "primary"}, "node": "ip4forward00001", "fields": ["vr_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}