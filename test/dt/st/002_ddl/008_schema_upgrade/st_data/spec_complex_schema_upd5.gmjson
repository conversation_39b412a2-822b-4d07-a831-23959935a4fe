{"name": "access_list", "version": "2.0", "type": "record", "special_complex": true, "schema_version": 4294967295, "fields": [{"name": "nftable_table_flags", "type": "uint32"}, {"name": "nftable_table_use", "type": "uint32"}, {"name": "nftable_table_name", "type": "uint32"}, {"name": "nftable_table_family", "type": "string"}, {"type": "record", "name": "nftable_chain", "nullable": true, "vector": true, "size": 128, "fields": [{"name": "nftable_chain_name", "type": "string"}, {"name": "nftable_chain_type", "type": "string"}, {"name": "nftable_chain_policy", "type": "uint32"}, {"name": "nftable_chain_hooknum", "type": "uint32"}, {"name": "nftable_chain_handle", "type": "uint64"}, {"name": "nftable_chain_basechainflag", "type": "uint8"}, {"name": "nftable_chain_policyflag", "type": "uint8"}, {"type": "record", "name": "nftable_rule", "nullable": true, "vector": true, "size": 128, "fields": [{"name": "nftable_rule_handle", "type": "uint32"}, {"name": "nftable_rule_position", "type": "uint32"}, {"name": "nftable_rule_table", "type": "string"}, {"name": "nftable_rule_chain", "type": "string"}]}], "super_fields": [{"name": "superfield1", "id": 0, "comment": "test", "fields": {"begin": "nftable_chain_policy", "end": "nftable_chain_hooknum"}}]}, {"name": "add_nftable_table_family", "type": "string"}, {"type": "record", "name": "add_node", "nullable": true, "fields": [{"name": "add_rule_handle", "type": "uint32"}, {"name": "add_rule_position", "type": "uint32"}, {"name": "add_rule_table", "type": "string"}, {"name": "add_rule_chain", "type": "string"}]}, {"name": "nftable_table_family", "type": "string"}], "super_fields": [{"name": "superfield0", "id": 0, "comment": "test", "fields": {"begin": "nftable_table_flags", "end": "nftable_table_use"}}], "keys": [{"name": "access_control_list_key", "node": "access_list", "fields": ["nftable_table_name"], "index": {"type": "primary"}}, {"name": "nftable_chain_key", "node": "nftable_chain", "fields": ["nftable_chain_name"], "index": {"type": "none"}, "constraints": {"unique": true}}, {"name": "nftable_rule_key", "node": "nftable_rule", "fields": ["nftable_rule_handle"], "index": {"type": "none"}, "constraints": {"unique": true}}]}