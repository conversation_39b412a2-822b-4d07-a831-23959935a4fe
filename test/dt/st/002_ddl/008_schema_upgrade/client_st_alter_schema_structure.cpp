/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: st for schema update structure
 * Author: hebaisheng
 * Create: 2022-8-03
 */

#include "client_common_st.h"
#include "gmc_batch_check.h"
#include "gmc_graph_check.h"

#define STRUCT_VAR_SIZE (sizeof(uint16_t) + sizeof(uint8_t *))
const uint32_t MAX_KEY_FIELD_NUM = 8;
const uint32_t INIT_STACK_SIZE = 20;
const uint32_t DEFAULT_STR_LEN = 8;

const char *g_schemaJsonName = "ip4forward00001";

static DbMemCtxT *alterMemCtx = NULL;

class StClientAlterSchemaStruct : public StClient {
public:
    StClientAlterSchemaStruct()
    {
        DbMemCtxArgsT args = {0};
        // client st 会初始化g_gmdbCltInstance.cltCommCtx，这里直接使用
        alterMemCtx = DbCreateDynMemCtx(g_gmdbCltInstance.cltCommCtx, false, "StClientAlterSchemaStruct st", &args);
    }

    ~StClientAlterSchemaStruct() override
    {
        // 统一回收中间申请的内存
        DbDeleteDynMemCtx(alterMemCtx);
    }
};

class StClientAlterSchemaStructPageSize : public StClientAlterSchemaStruct {
public:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"udfEnable=1\" \"enableDmlPerfStat=1\" \"auditLogEnableDML=0\" "
                                "\"maxNormalTableNum=10000\" \"pageSize=4\"");
        st_clt_init();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
    }
};

struct AlterStructTestCtx {
    bool isKey;
    DmVertexTypeE type;
    int value;
    int vertexSize;
    char *objAddr;
    int objSize;
    GmcStmtT *stmt;
};

struct StructTestCtx {
    bool haveBitmap;
    bool isFixKey;
    bool useExternalMem;
    int value;
    uint32_t keyId;
    uint32_t vertexSize;
    uint32_t keySize;
    uint32_t idx;
    uint32_t useSize;
    uint32_t totalSize;
    uint32_t memMax;
    uint32_t memSize;
    void **memAddr;
    GmcStmtT *stmt;
    uint32_t *lenStack;
    uint32_t initStack[INIT_STACK_SIZE];
    bool *keyNullInfo;
};

// ########################################### sample fib ###########################################

#pragma pack(1)
struct Ip4forwordT2 {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint16_t strSize;
    char *originString;
};
#pragma pack()

#pragma pack(1)
struct Ip4forwordTUpd {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint16_t strSize;
    char *originString;
    uint16_t newStrSize;
    char *newString;
};
#pragma pack()

void SetValueIntoStruct(Ip4forwordT2 *d, int value)
{
    d->vr_id = 0;
    d->vrf_index = 1;
    d->dest_ip_addr = value;

    d->strSize = DEFAULT_STR_LEN;
    char *buf = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->originString = buf;
    char str[] = "1234567";
    (void)memcpy_s(d->originString, DEFAULT_STR_LEN, str, DEFAULT_STR_LEN);
}

void SetValueIntoUpdStruct(Ip4forwordTUpd *d, int value)
{
    d->vr_id = 2;
    d->vrf_index = 3;
    d->dest_ip_addr = value;

    d->strSize = DEFAULT_STR_LEN;
    char *buf = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->originString = buf;
    char str[] = "abc1234";
    (void)memcpy_s(d->originString, DEFAULT_STR_LEN, str, DEFAULT_STR_LEN);

    d->newStrSize = DEFAULT_STR_LEN;
    char *buf2 = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf2, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->newString = buf2;
    char value2[] = "abcdefg";
    (void)memcpy_s(d->newString, DEFAULT_STR_LEN, value2, DEFAULT_STR_LEN);
}

void SetValueIntoUpdStruct2(Ip4forwordTUpd *d, int value)
{
    d->vr_id = 0;
    d->vrf_index = 77;
    d->dest_ip_addr = value;

    d->strSize = DEFAULT_STR_LEN;
    char *buf = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->originString = buf;
    char str[] = "aaaaaaa";
    (void)memcpy_s(d->originString, DEFAULT_STR_LEN, str, DEFAULT_STR_LEN);

    d->newStrSize = DEFAULT_STR_LEN;
    char *buf2 = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf2, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->newString = buf2;
    char value2[] = "bbbbbbb";
    (void)memcpy_s(d->newString, DEFAULT_STR_LEN, value2, DEFAULT_STR_LEN);
}

int GetBitFieldSize(DmPropertySchemaT *p, uint32_t *begin, uint32_t end)
{
    uint32_t index = *begin;
    int sum = 0;
    if (p[index].dataType == DB_DATATYPE_BITFIELD8) {
        sum = 1;
    } else if (p[index].dataType == DB_DATATYPE_BITFIELD16) {
        sum = 2;
    } else if (p[index].dataType == DB_DATATYPE_BITFIELD32) {
        sum = 4;
    } else if (p[index].dataType == DB_DATATYPE_BITFIELD64) {
        sum = 8;
    } else {
        DB_ASSERT(0);
    }
    uint8_t bitfieldOffset = p[index].bitfieldOffset;
    DbDataTypeE dataType = p[index].dataType;
    for (index = index + 1; index < end; ++index) {
        if (p[index].dataType == dataType && p[index].bitfieldOffset > bitfieldOffset) {
            bitfieldOffset = p[index].bitfieldOffset;
            continue;
        }
        break;
    }
    *begin = index - 1;
    return sum;
}

void SeriVertexRecord(
    GmcSeriT *s, DmSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot = true, uint32_t *size = NULL)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    uint32_t newVal, newSize;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType != DM_FIXED_VERTEX) {
        DmConvertUint32ToVarint(c->lenStack[c->idx++], &newVal, &newSize);
        *(uint32_t *)*destBuf = newVal;
        *destBuf += newSize;
    }
    // fixed propertires
    int fixedLen = c->lenStack[c->idx++];
    DmConvertUint32ToVarint(fixedLen, &newVal, &newSize);
    *(uint32_t *)*destBuf = newVal;
    *destBuf += newSize;
    uint32_t propNum = schema->propeNum;
    if (isRoot) {
        if (fixedLen > 1) {
            (void)memcpy_s(*destBuf, fixedLen - 1, srcBuf, fixedLen - 1);
        }
        *(*destBuf + fixedLen - 1) = 0;
        propNum -= 1;
    } else {
        if (fixedLen > 0) {
            (void)memcpy_s(*destBuf, fixedLen, srcBuf, fixedLen);
        }
    }
    *destBuf += fixedLen;
    // nullable
    DmConvertUint32ToVarint(schema->propeNum, &newVal, &newSize);
    *(uint32_t *)*destBuf = newVal;
    *destBuf += newSize;
    uint32_t index;
    uint32_t index1;
    uint32_t index2 = (schema->propeNum + 7) / 8;
    uint8_t *nullableInfo = *destBuf;
    // init, all field are valid
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (index = 0; index < propNum; ++index) {
        if (!properties[index].isValid) {
            continue;
        }
        DmSetBitTrueIntoUint8Arr(*destBuf, index);
    }
    *destBuf += index2;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX) {
        return;
    }
    // var fields
    if (isRoot) {
        --fixedLen;
    }
    int structSize = fixedLen;
    int32_t nodeId = -1;
    for (index = 0; index < schema->propeNum; ++index) {
        if (properties[index].dataType == DB_DATATYPE_BITFIELD8 ||
            properties[index].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[index].dataType == DB_DATATYPE_BITFIELD32 ||
            properties[index].dataType == DB_DATATYPE_BITFIELD64) {
            continue;
        }
        if (!properties[index].isFixed) {
            break;
        }
        if (!properties[index].isValid) {
            nodeId = index;
            break;
        }
    }
    uint8_t **varAddr = NULL;
    for (index1 = index; index1 < schema->propeNum; ++index1) {
        if (!properties[index1].isValid) {
            if (nodeId == -1) {
                nodeId = index1;
            }
            break;
        }
        if (properties[index1].isFixed) {
            break;
        }
        structSize += STRUCT_VAR_SIZE;
        index2 = *(uint16_t *)(srcBuf + fixedLen + (index1 - index) * STRUCT_VAR_SIZE);
        if (index2 > properties[index1].size) {
            printf("[SeriVertexRecord] warn: fieldName:%s, maxSize:%u, inputSize:%u.\n",
                MEMBER_PTR(&properties[index1], name), properties[index1].size, index2);
        }
        DmConvertUint32ToVarint(index2, &newVal, &newSize);
        *(uint32_t *)*destBuf = newVal;
        *destBuf += newSize;
        if (index2 > 0) {
            varAddr = (uint8_t **)(srcBuf + fixedLen + (index1 - index) * STRUCT_VAR_SIZE + sizeof(uint16_t));
            (void)memcpy_s(*destBuf, index2, *varAddr, index2);
            *destBuf += index2;
        }
    }
    // nullable for node
    if (nodeId == -1 && schema->nodeNum > 0) {
        printf("[SeriVertexRecord] warn: schema->nodeNum = %d, but none node found.\n", schema->nodeNum);
    }
    uint8_t *nodeBase = srcBuf + fixedLen + (index1 - index) * STRUCT_VAR_SIZE;
    uint8_t *nodeLen;
    for (index = 0; index < schema->nodeNum; ++index) {
        nodeLen = nodeBase + index * STRUCT_VAR_SIZE;
        varAddr = (uint8_t **)(nodeBase + index * STRUCT_VAR_SIZE + sizeof(uint16_t));
        if (*(uint16_t *)nodeLen == 0 || *varAddr == NULL) {
            nullableInfo[nodeId >> 3] |=
                (uint8_t)(1 << (7 - (nodeId & 7)));  // 将空的node的nullableInfo位置0, 实际此处置1, 待修改
            ++nodeId;
        }
    }
    if (size) {
        *size = structSize + STRUCT_VAR_SIZE * schema->nodeNum;
    }
}

void SeriVertexSubNode(GmcSeriT *s, DmSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot = true)
{
    uint32_t i, j, newValue, newSize, elementNum, offset = 0;
    // nodeNum
    DmConvertUint32ToVarint(schema->nodeNum, &newValue, &newSize);
    *(uint32_t *)*destBuf = newValue;
    *destBuf += newSize;
    if (schema->nodeNum == 0) {
        return;
    }
    uint8_t *nodeBase = srcBuf;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / 8);
            continue;
        }
        if (!properties[i].isValid) {
            break;
        }
        if (properties[i].isFixed) {
            nodeBase += properties[i].size;
        } else {
            nodeBase += STRUCT_VAR_SIZE;
        }
    }
    // nodes
    uint8_t *nodeLen = NULL;
    uint8_t **nodeAddr = NULL;
    DmNodeSchemaT *nodes = MEMBER_PTR(schema, nodes);
    for (i = 0; i < schema->nodeNum; i++) {
        *(uint32_t *)*destBuf = 0xff;
        *destBuf += 1;  // isCreated标记
        nodeLen = nodeBase + i * STRUCT_VAR_SIZE;
        elementNum = *(uint16_t *)nodeLen;
        nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
        DmConvertUint32ToVarint(elementNum, &newValue, &newSize);
        *(uint32_t *)*destBuf = newValue;
        *destBuf += newSize;
        if (*nodeAddr == NULL) {
            continue;
        }
        for (j = 0; j < elementNum; ++j) {
            DmSchemaT *schema = MEMBER_PTR(&nodes[i], schema);
            SeriVertexRecord(s, schema, *nodeAddr + offset * j, destBuf, false, &offset);
            SeriVertexSubNode(s, schema, *nodeAddr + offset * j, destBuf, false);
        }
    }
    return;
}

Status SeriStructVertex(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    StructTestCtx *c = (StructTestCtx *)s->userData;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    c->idx = 0;
    // vertex type
    *(uint8_t *)destBuf = cltCataLabel->vertexLabel->vertexDesc->vertexType;
    if (c->haveBitmap) {
        *(uint8_t *)destBuf = *(uint8_t *)destBuf | (uint8_t)0x10;
    }
    // tree, vertext length
    uint8_t *buf = (uint8_t *)destBuf + 1;
    uint32_t newVal, newSize;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_TREE_VERTEX) {
        DmConvertUint32ToVarint(s->bufSize, &newVal, &newSize);
        *(uint32_t *)buf = newVal;
        buf += newSize;
    }
    SeriVertexRecord(s, schema, s->obj, &buf);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX ||
        cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FLAT_VERTEX) {
        return GMERR_OK;
    }
    SeriVertexSubNode(s, schema, s->obj, &buf, false);
    return GMERR_OK;
}

void DeSeriFreeDynMem(StructTestCtx *c, bool isAll = false)
{
    if (c->totalSize > INIT_STACK_SIZE) {
        if (c->lenStack) {
            DbDynMemCtxFree(alterMemCtx, c->lenStack);
        }
        c->lenStack = c->initStack;
        c->totalSize = INIT_STACK_SIZE;
    }
    if (c->useExternalMem) {
        return;
    }
    if (c->memAddr == NULL) {
        c->memMax = 0;
        c->memSize = 0;
        return;
    }
    for (uint32_t i = 0; i < c->memSize; ++i) {
        if (c->memAddr[i]) {
            DbDynMemCtxFree(alterMemCtx, c->memAddr[i]);
            c->memAddr[i] = NULL;
        }
    }
    c->memSize = 0;
    if (isAll) {
        DbDynMemCtxFree(alterMemCtx, c->memAddr);
        c->memAddr = NULL;
        c->memMax = 0;
    }
}

void ExtendLenStack(StructTestCtx *c)
{
    if (c->totalSize == 0) {
        c->totalSize = INIT_STACK_SIZE;
        c->lenStack = c->initStack;
        return;
    }
    if (c->useSize < c->totalSize) {
        return;
    }
    uint32_t stepSize;
    if (c->totalSize < 20000) {
        stepSize = c->totalSize * 10;
    } else {
        stepSize = c->totalSize + 10000;
    }
    uint32_t *newVal = (uint32_t *)DbDynMemCtxAlloc(alterMemCtx, stepSize * sizeof(uint32_t));
    if (newVal == NULL) {
        printf("ExtendLenStack(from %u to %u) failed.\n", c->totalSize, stepSize);
        DB_ASSERT(0);
    }
    (void)memset_s(newVal, sizeof(uint32_t) * stepSize, 0, sizeof(uint32_t) * stepSize);
    (void)memcpy_s(newVal, sizeof(uint32_t) * c->totalSize, c->lenStack, sizeof(uint32_t) * c->totalSize);
    if (c->totalSize > INIT_STACK_SIZE) {  // 第一次扩展时, 使用的是栈变量, 不需要free
        DbDynMemCtxFree(alterMemCtx, c->lenStack);  // 释放上一次内存, 最后一次要用户手动调用deSeriFreeDynMem释放
    }
    c->lenStack = newVal;
    c->totalSize = stepSize;
}

int GetRecordSize(GmcSeriT *s, DmSchemaT *schema, uint8_t *addr, bool isRoot = true, uint32_t *size = NULL)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    uint32_t newVal, newSize;
    int id = -1;
    CltCataLabelT *cltLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltLabel->vertexLabel->vertexDesc->vertexType != DM_FIXED_VERTEX) {
        ExtendLenStack(c);
        id = c->useSize++;
    }
    // fixed properties
    uint32_t i, fieldLen, fixedLen = 0;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            fixedLen += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            c->haveBitmap = true;
            fixedLen += (properties[i].size / 8);
            continue;
        }
        if (!properties[i].isFixed || !properties[i].isValid) {
            break;
        }
        fixedLen += properties[i].size;
    }
    fieldLen = fixedLen;
    if (isRoot) {
        if (i < schema->propeNum) {
            fieldLen += 1;  // sys_version
        } else {
            fixedLen -= 1;  // the offset of struct
        }
    }
    uint32_t structSize = fixedLen;
    // the length of fixed properties
    DmConvertUint32ToVarint(fieldLen, &newVal, &newSize);
    int sum = fieldLen + newSize;
    ExtendLenStack(c);
    c->lenStack[c->useSize++] = fieldLen;  // fixedLen
    // propNum
    DmConvertUint32ToVarint(schema->propeNum, &newVal, &newSize);
    sum += newSize;
    // nullable
    sum += (schema->propeNum + 7) / 8;
    if (cltLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX) {
        return sum;
    }
    int j = i;
    for (; i < schema->propeNum; ++i) {
        if (!properties[i].isValid || properties[i].isFixed) {
            break;
        }
        structSize += STRUCT_VAR_SIZE;
        fieldLen = *(uint16_t *)(addr + fixedLen + (i - j) * STRUCT_VAR_SIZE);
        DmConvertUint32ToVarint(fieldLen, &newVal, &newSize);
        sum += newSize;
        sum += fieldLen;
    }
    if (size) {
        // 结构体偏移一整个数组元素(定长 + 变长 + node), 偏移到下一个元素开头
        *size = structSize + STRUCT_VAR_SIZE * schema->nodeNum;
    }
    DmConvertUint32ToVarint(sum, &newVal, &newSize);
    sum += newSize;
    c->lenStack[id] = sum;  // 定长 + 变长 + recordLen
    return sum;
}

// ------>> inner funtion
int GetSubNodeSize(GmcSeriT *s, DmSchemaT *schema, uint8_t *addr)
{
    uint32_t i, j, newSize, offset = 0;
    // nodeNum
    DmGetVarintLength(schema->nodeNum, &newSize);
    int sum = newSize;
    if (schema->nodeNum == 0) {
        return sum;
    }
    uint8_t *nodeBase = addr;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / 8);
            continue;
        }
        if (!properties[i].isValid) {
            break;
        }
        if (properties[i].isFixed) {
            nodeBase += properties[i].size;
        } else {
            nodeBase += STRUCT_VAR_SIZE;
        }
    }
    uint32_t elementNum;
    uint8_t *buf = NULL;
    uint8_t **nodeAddr = NULL;
    DmNodeSchemaT *nodes = MEMBER_PTR(schema, nodes);
    for (i = 0; i < schema->nodeNum; i++) {
        buf = nodeBase + i * STRUCT_VAR_SIZE;
        elementNum = *(uint16_t *)buf;
        nodeAddr = (uint8_t **)(buf + sizeof(uint16_t));
        DmGetVarintLength(elementNum, &newSize);
        sum += newSize;
        if (*nodeAddr == NULL) {
            continue;
        }
        DmSchemaT *schema = MEMBER_PTR(&nodes[i], schema);
        for (j = 0; j < elementNum; ++j) {
            sum += GetRecordSize(s, schema, *nodeAddr + offset * j, false, &offset);
            sum += GetSubNodeSize(s, schema, *nodeAddr + offset * j);
        }
        sum++;  // isCreated标记
    }
    return sum;
}

// ######## Entry funtion 3: GetSerialVertexLength
void GetSerialVertexLength(GmcSeriT *s)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX && c->vertexSize > 0) {
        s->bufSize = c->vertexSize;
        return;
    }
    c->useSize = 0;
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    uint8_t *addr = s->obj;
    // vertextType
    c->vertexSize = 1;
    // root node
    c->vertexSize += GetRecordSize(s, schema, addr);
    s->bufSize = c->vertexSize;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX ||
        cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FLAT_VERTEX) {
        return;
    }
    // sub nodes
    c->vertexSize += GetSubNodeSize(s, schema, addr);
    uint32_t newSize;
    DmGetVarintLength(c->vertexSize, &newSize);
    c->vertexSize += newSize;
    s->bufSize = c->vertexSize;
}

void ExtendDynArray(StructTestCtx *c)
{
    if (c->memSize < c->memMax) {
        return;
    }
    uint32_t stepSize;
    if (c->memMax == 0) {
        stepSize = 10;
    } else {
        stepSize = c->memMax * 2;
    }
    if (stepSize <= 0) {
        return;
    }
    void **newAddr = (void **)DbDynMemCtxAlloc(alterMemCtx, sizeof(void *) * stepSize);
    if (newAddr == NULL) {
        printf("ExtendDynArray(from %u to %u) failed.\n", c->memMax, stepSize);
        DB_ASSERT(0);
    }
    (void)memset_s(newAddr + c->memMax, sizeof(void *) * c->memMax, 0, sizeof(void *) * c->memMax);
    if (c->memMax > 0) {
        (void)memcpy_s(newAddr, sizeof(void *) * c->memMax, c->memAddr, sizeof(void *) * c->memMax);
        DbDynMemCtxFree(alterMemCtx, c->memAddr);
    }
    c->memAddr = newAddr;
    c->memMax = stepSize;
}

void DeSeriStructRecord(GmcDeseriT *d, DmSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data)
{
    StructTestCtx *c = (StructTestCtx *)d->userData;
    uint32_t i, totalLen, fixedLen, length, offset;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &totalLen, &offset);
    const uint8_t *addrEnd = srcBuf + *sum + totalLen;
    *sum += offset;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &fixedLen, &offset);
    *sum += offset;
    if (fixedLen > 0) {
        (void)memcpy_s(data, fixedLen, srcBuf + *sum, fixedLen);
        *sum += fixedLen;
    }
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &length, &offset);
    *sum += offset;
    *sum += (length + 7) / 8;
    uint8_t *addrLen = NULL;
    uint8_t **addrVal = NULL;
    i = 0;
    while (srcBuf + *sum < addrEnd) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &length, &offset);
        *sum += offset;
        addrLen = data + fixedLen + i * STRUCT_VAR_SIZE;
        *(uint16_t *)addrLen = length;
        addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
        if (!c->useExternalMem) {
            if ((length + 1) < 1) {
                return;
            }
            *addrVal = (uint8_t *)DbDynMemCtxAlloc(alterMemCtx, length + 1);
            if (*addrVal == NULL) {
                printf("[DeSeriStructRecord] warn: DbMalloc memory(%u) failed.\n", length + 1);
                return;
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *addrVal;
        }
        (void)memcpy_s(*addrVal, length, srcBuf + *sum, length);
        *(*addrVal + length) = 0;
        *sum += length;
        ++i;
    }
}

void DeSeriStructSubNode(GmcDeseriT *d, DmSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data)
{
    StructTestCtx *c = (StructTestCtx *)d->userData;
    uint32_t i, j, size, nodeNum, elementNum, newSize;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &nodeNum, &newSize);
    *sum += newSize;
    if (nodeNum != schema->nodeNum) {
        printf("[DeSeriStructSubNode] warn: schema->nodeNum = %" PRIu32 ", msg->nodeNum = %" PRIu32 ".\n", nodeNum,
            schema->nodeNum);
    }
    if (nodeNum == 0) {
        return;
    }
    uint8_t *nodeBase = data;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (!properties[i].isValid) {
            break;
        }
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / 8);
            continue;
        }
        if (properties[i].isFixed) {
            nodeBase += properties[i].size;
        } else {
            nodeBase += STRUCT_VAR_SIZE;
        }
    }
    uint8_t *nodeLen;
    uint8_t **nodeAddr;
    DmSchemaT *s;
    DmNodeSchemaT *nodes = MEMBER_PTR(schema, nodes);
    for (i = 0; i < nodeNum; ++i) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum + 1), &elementNum, &newSize);
        *sum += newSize;
        *sum += 1;  // isCreated标记
        nodeLen = nodeBase + i * STRUCT_VAR_SIZE;
        nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
        *(uint16_t *)nodeLen = elementNum;
        if (elementNum == 0) {
            *nodeAddr = NULL;
            continue;
        }
        s = MEMBER_PTR(&nodes[i], schema);
        DmPropertySchemaT *properties = MEMBER_PTR(s, properties);

        size = 0;
        for (j = 0; j < s->propeNum; ++j) {
            if (!properties[j].isValid) {
                size += STRUCT_VAR_SIZE;
            }
            if (properties[j].dataType == DB_DATATYPE_BITFIELD8 || properties[j].dataType == DB_DATATYPE_BITFIELD16 ||
                properties[j].dataType == DB_DATATYPE_BITFIELD32 || properties[j].dataType == DB_DATATYPE_BITFIELD64) {
                size += GetBitFieldSize(properties, &j, s->propeNum);
                continue;
            }
            if (properties[j].dataType == DB_DATATYPE_BITMAP) {
                size += (properties[j].size / 8);
                continue;
            }
            if (properties[j].isFixed) {
                size += properties[j].size;
            } else {
                size += STRUCT_VAR_SIZE;
            }
        }
        if (!c->useExternalMem) {
            if ((size * elementNum) <= 0) {
                return;
            }
            *nodeAddr = (uint8_t *)DbDynMemCtxAlloc(alterMemCtx, size * elementNum);
            if (*nodeAddr == NULL) {
                printf(
                    "[DeSeriStructSubNode] warn: DbMalloc memory for nodeAddr failed, elementNum = %u, elementSize = "
                    "%u.\n",
                    elementNum, size);
                return;
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *nodeAddr;
        }
        for (j = 0; j < elementNum; ++j) {
            DeSeriStructRecord(d, s, srcBuf, sum, *nodeAddr + size * j);
            DeSeriStructSubNode(d, s, srcBuf, sum, *nodeAddr + size * j);
        }
    }
}

// ######## Entry funtion 6: DeSeriStructVertex 该函数只适合非Tree表
Status DeSeriStructVertex(void *deSeri, const uint8_t *srcBuf, uint32_t srcLen, GmcStructureResvT *reservedSize)
{
    GmcDeseriT *d = (GmcDeseriT *)deSeri;
    StructTestCtx *c = (StructTestCtx *)d->userData;
    uint32_t i, length, offset, totalLen, fixedLen;
    CltCataLabelT *cltLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltLabel->vertexLabel->vertexDesc->vertexType == DM_TREE_VERTEX) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (cltLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + 1), &length, &offset);
        (void)memcpy_s(d->obj, length - 1, srcBuf + offset + 1, length - 1);
        return GMERR_OK;
    }
    // free dynamic memory
    DeSeriFreeDynMem(c);
    // var fields, tree model
    uint8_t *addrLen = NULL;
    uint8_t **addrVal = NULL;
    const uint8_t *addrEnd = NULL;
    uint32_t sum = 1;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &totalLen, &offset);
    addrEnd = srcBuf + sum + totalLen;
    sum += offset;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &fixedLen, &offset);
    sum += offset;
    if (fixedLen > 1) {
        (void)memcpy_s(d->obj, fixedLen - 1, srcBuf + sum, fixedLen - 1);
    }
    sum += fixedLen;
    --fixedLen;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &length, &offset);
    sum += offset;
    sum += (length + 7) / 8;
    i = 0;
    while (srcBuf + sum < addrEnd) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &length, &offset);
        sum += offset;
        // 老版本读新版本数据时，需要终止往后读。
        // 当前只考虑了定长+变长的情况，不包含子节点，如果后续加了子节点，需要重新计算objSize，适配反序列化逻辑。
        if (d->objSize <= fixedLen + i * STRUCT_VAR_SIZE) {
            break;
        }
        addrLen = d->obj + fixedLen + i * STRUCT_VAR_SIZE;
        *(uint16_t *)addrLen = (uint16_t)length;
        addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
        if (!c->useExternalMem) {
            if ((length + 1) <= 0) {
                return GMERR_DATA_EXCEPTION;
            }
            *addrVal = (uint8_t *)DbDynMemCtxAlloc(alterMemCtx, length + 1);
            if (*addrVal == NULL) {
                printf("[DeSeriStructVertex] warn: DbMalloc memory(%u) failed.\n", length + 1);
                return GMERR_DATA_EXCEPTION;
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *addrVal;
        }
        (void)memcpy_s(*addrVal, length, srcBuf + sum, length);
        *(*addrVal + length) = 0;
        sum += length;
        ++i;
    }
    return GMERR_OK;
}

// ######## Entry funtion 4: SeriPrimaryKeySctruct
static Status SeriPrimaryKeySctruct(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    StructTestCtx *c = (StructTestCtx *)s->userData;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    DmVlIndexLabelT *pk = NULL;
    uint32_t i, k, j = 0;
    if (c->keyId == 0) {
        *destBuf = 0xff;  // 主键 不允许为空
        pk = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, pkIndex);
    } else {
        if (c->keyId < 0 || c->keyId > cltCataLabel->vertexLabel->metaVertexLabel->secIndexNum) {
            DB_ASSERT(0);
        } else {
            pk = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, secIndexes) + c->keyId - 1;
            uint8_t keyInfo = 0;
            if (c->keyNullInfo) {  // 用户有传 keyNullInfo 数组, 按用户传的来处理 (暂不支持)
                bool *keyNullInfo = c->keyNullInfo;
                for (i = 0; i < MAX_KEY_FIELD_NUM; ++i) {
                    keyInfo = keyInfo << 1;
                    if (keyNullInfo[i] != 0) {
                        keyInfo |= 1;
                    }
                }
            } else {  // 用户没有传 keyNullInfo 数组, 默认按 索引字段全不为空 来处理
                keyInfo = ((uint32_t)1 << (pk->propeNum)) - 1;
            }
            *destBuf = keyInfo;

            // lpm 不允许为空; 所有索引字段的 nullable 都为 false 时, 置为 0xff
            if (pk->idxLabelBase.indexType == LPM4_INDEX || pk->idxLabelBase.indexType == LPM6_INDEX ||
                !pk->isNullable) {
                *destBuf = 0xff;
            }
        }
    }
    uint32_t offset = 1;
    uint8_t *srcBuf = s->obj;
    uint8_t **newAddr = NULL;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    DmPropertySchemaT *pkproperties = MEMBER_PTR(pk, properties);
    uint32_t *propIds = MEMBER_PTR(pk, propIds);
    for (i = 0; i < pk->propeNum; ++i) {
        srcBuf = s->obj;
        for (j = 0; j < schema->propeNum; ++j) {
            char *pkName = MEMBER_PTR(&pkproperties[propIds[i]], name);
            char *name2 = MEMBER_PTR(&properties[j], name);
            if (0 == strcmp(pkName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (properties[j].dataType == DB_DATATYPE_BITFIELD8 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD16 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD32 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD64) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == DB_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / 8);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;
        if (pkproperties[propIds[i]].isFixed) {
            (void)memcpy_s(destBuf + offset, pkproperties[propIds[i]].size, srcBuf, pkproperties[propIds[i]].size);
            offset += pkproperties[propIds[i]].size;
            srcBuf += pkproperties[propIds[i]].size;
        } else {
            k = *(uint16_t *)srcBuf;
            *(uint16_t *)(destBuf + offset) = k;
            offset += sizeof(uint16_t);
            newAddr = (uint8_t **)(srcBuf + sizeof(uint16_t));
            (void)memcpy_s(destBuf + offset, k, *newAddr, k);
            offset += k;
            srcBuf += STRUCT_VAR_SIZE;
        }
    }
    return GMERR_OK;
}

// ######## Entry funtion 5: GetSerialKeyLength
void GetSerialKeyLength(GmcSeriT *s)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    if (c->isFixKey && c->keySize > 0) {
        s->bufSize = c->keySize;
        return;
    }
    CltCataLabelT *cltLabel = CltGetCltCataLabelInStmt(c->stmt);
    DmSchemaT *schema = MEMBER_PTR(cltLabel->vertexLabel->metaVertexLabel, schema);
    DmVlIndexLabelT *pk = NULL;
    if (c->keyId == 0) {
        pk = MEMBER_PTR(cltLabel->vertexLabel->metaVertexLabel, pkIndex);
    } else {
        if (c->keyId < 0 || c->keyId > cltLabel->vertexLabel->metaVertexLabel->secIndexNum) {
            DB_ASSERT(0);
        } else {
            pk = MEMBER_PTR(cltLabel->vertexLabel->metaVertexLabel, secIndexes) + c->keyId - 1;
        }
    }
    uint8_t *srcBuf = s->obj;
    uint32_t i, j = 0;
    c->keySize = 1;
    c->isFixKey = true;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    DmPropertySchemaT *pkproperties = MEMBER_PTR(pk, properties);
    uint32_t *propIds = MEMBER_PTR(pk, propIds);
    for (i = 0; i < pk->propeNum; ++i) {
        srcBuf = s->obj;
        for (j = 0; j < schema->propeNum; ++j) {
            char *pkName = MEMBER_PTR(&pkproperties[propIds[i]], name);
            char *name2 = MEMBER_PTR(&properties[j], name);
            if (0 == strcmp(pkName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (properties[j].dataType == DB_DATATYPE_BITFIELD8 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD16 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD32 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD64) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == DB_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / 8);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;

        if (pkproperties[propIds[i]].isFixed) {
            c->keySize += pkproperties[propIds[i]].size;
            srcBuf += pkproperties[propIds[i]].size;
        } else {
            c->keySize += *(uint16_t *)srcBuf;
            srcBuf += STRUCT_VAR_SIZE;
            c->isFixKey = false;
        }
    }
    s->bufSize = c->keySize;
}

template <typename StructObjT>
void SetKeySeri(
    GmcStmtT *stmt, StructObjT *structObj, GmcSeriT *seri, StructTestCtx *seriCtx, uint32_t keyId, bool *keyNullInfo)
{
    seriCtx->stmt = stmt;
    seriCtx->keyId = keyId;
    seriCtx->keyNullInfo = keyNullInfo;
    seri->seriFunc = SeriPrimaryKeySctruct;
    seri->version = GMC_SERI_VERSION_DEFAULT;
    seri->obj = (uint8_t *)structObj;
    seri->userData = seriCtx;
    GetSerialKeyLength(seri);
}

TEST_F(StClientAlterSchemaStruct, TestAlterSchemaInsert)
{
    Status ret;
    GmcDropVertexLabel(syncStmt, g_schemaJsonName);
    // 创建一个schema_v1
    string schema_v1 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_flat_schema_base.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schema_v1.c_str(), NULL));
    ret = GmcPrepareStmtByLabelName(syncStmt, g_schemaJsonName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    Ip4forwordT2 obj;
    StructTestCtx ctx = (StructTestCtx){0};
    ctx.stmt = syncStmt;
    GmcSeriT serStr;
    serStr.seriFunc = SeriStructVertex;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.userData = &ctx;
    for (uint32_t i = 0; i < 1; ++i) {
        SetValueIntoStruct(&obj, i);
        GetSerialVertexLength(&serStr);
        ret = GmcSetVertexWithBuf(syncStmt, &serStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbDynMemCtxFree(alterMemCtx, obj.originString);
    // schema 升级
    string schema_v2 = GetFileContext("./008_schema_upgrade/st_data/spec_complex_flat_schema_upd.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schema_v2.c_str(), true, (char *)g_schemaJsonName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写新版本
    Ip4forwordTUpd updateObj;
    StructTestCtx updCtx = (StructTestCtx){0};
    updCtx.stmt = syncStmt;
    GmcSeriT updSerStr;
    updSerStr.seriFunc = SeriStructVertex;
    updSerStr.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr.obj = (uint8_t *)&updateObj;
    updSerStr.userData = &updCtx;
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_schemaJsonName, 20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; ++i) {
        SetValueIntoUpdStruct(&updateObj, i);
        GetSerialVertexLength(&updSerStr);
        ret = GmcSetVertexWithBuf(syncStmt, &updSerStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbDynMemCtxFree(alterMemCtx, updateObj.originString);
    DbDynMemCtxFree(alterMemCtx, updateObj.newString);
    // 普通读
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_schemaJsonName, 20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vr_id = 2;
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(syncStmt, "ip4_key");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);

    char newString[9] = {0};
    bool isNull;
    ret = GmcFetch(syncStmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t propSize = 0;
    ret = GmcGetVertexPropertySizeByName(syncStmt, "newString", &propSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(syncStmt, "newString", newString, propSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(strcmp(newString, "abcdefg"), 0);

    // 结构化新版本读新版本数据
    Ip4forwordTUpd newObj = (Ip4forwordTUpd){0};
    StructTestCtx deseriCtx = (StructTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    deseriCtx.useExternalMem = false;
    deseriCtx.stmt = syncStmt;
    deseri.deseriFunc = DeSeriStructVertex;
    deseri.objSize = sizeof(Ip4forwordTUpd);
    deseri.version = GMC_SERI_VERSION_DEFAULT;
    deseri.obj = (uint8_t *)&newObj;
    deseri.userData = &deseriCtx;
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_schemaJsonName, 20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        newObj.vr_id = 2;
        StructTestCtx seriCtx0 = (StructTestCtx){0};
        GmcSeriT seri = (GmcSeriT){0};
        SetKeySeri(syncStmt, &newObj, &seri, &seriCtx0, 0, NULL);
        ret = GmcSetIndexKeyWithBuf(syncStmt, 0, &seri);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(syncStmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexDeseri(syncStmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(strcmp(newObj.newString, "abcdefg"), 0);
        EXPECT_EQ(newObj.vr_id == 2, true);
    }
    DeSeriFreeDynMem(&deseriCtx, true);
    // 结构化老版本读新版本数据
    Ip4forwordT2 newObj1 = (Ip4forwordT2){0};
    StructTestCtx deseriCtx1 = (StructTestCtx){0};
    GmcDeseriT deseri1 = (GmcDeseriT){0};
    deseriCtx1.useExternalMem = false;
    deseriCtx1.stmt = syncStmt;
    deseri1.deseriFunc = DeSeriStructVertex;
    deseri1.objSize = sizeof(Ip4forwordT2);
    deseri1.version = GMC_SERI_VERSION_DEFAULT;
    deseri1.obj = (uint8_t *)&newObj1;
    deseri1.userData = &deseriCtx1;
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_schemaJsonName, 20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        newObj1.vr_id = 2;
        StructTestCtx seriCtx0 = (StructTestCtx){0};
        GmcSeriT seri = (GmcSeriT){0};
        SetKeySeri(syncStmt, &newObj1, &seri, &seriCtx0, 0, NULL);
        ret = GmcSetIndexKeyWithBuf(syncStmt, 0, &seri);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(syncStmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexDeseri(syncStmt, &deseri1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(strcmp(newObj1.originString, "abc1234"), 0);
        EXPECT_EQ(newObj1.vr_id == 2, true);
    }
    DeSeriFreeDynMem(&deseriCtx1, true);

    // 结构化新版本读老版本数据
    Ip4forwordTUpd newObj2 = (Ip4forwordTUpd){0};
    StructTestCtx deseriCtx2 = (StructTestCtx){0};
    GmcDeseriT deseri2 = (GmcDeseriT){0};
    deseriCtx2.useExternalMem = false;
    deseriCtx2.stmt = syncStmt;
    deseri2.deseriFunc = DeSeriStructVertex;
    deseri2.objSize = sizeof(Ip4forwordTUpd);
    deseri2.version = GMC_SERI_VERSION_DEFAULT;
    deseri2.obj = (uint8_t *)&newObj2;
    deseri2.userData = &deseriCtx2;
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_schemaJsonName, 0, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        newObj2.vr_id = 0;
        StructTestCtx seriCtx0 = (StructTestCtx){0};
        GmcSeriT seri = (GmcSeriT){0};
        SetKeySeri(syncStmt, &newObj2, &seri, &seriCtx0, 0, NULL);
        ret = GmcSetIndexKeyWithBuf(syncStmt, 0, &seri);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(syncStmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexDeseri(syncStmt, &deseri2);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(strcmp(newObj2.originString, "1234567"), 0);
        EXPECT_EQ(newObj2.vr_id == 0, true);
    }
    DeSeriFreeDynMem(&deseriCtx2, true);

    // 新版本更新老版本数据
    Ip4forwordTUpd updateObj3;
    StructTestCtx updCtx3 = (StructTestCtx){0};
    updCtx3.stmt = syncStmt;
    GmcSeriT updSerStr3 = (GmcSeriT){0};
    updSerStr3.seriFunc = SeriStructVertex;
    updSerStr3.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr3.obj = (uint8_t *)&updateObj3;
    updSerStr3.userData = &updCtx3;
    ret = GmcPrepareStmtByLabelNameWithVersion(syncStmt, g_schemaJsonName, 0, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    for (uint32_t i = 0; i < 1; ++i) {
        SetValueIntoUpdStruct2(&updateObj3, i);
        GetSerialVertexLength(&updSerStr3);
        ret = GmcSetVertexWithBuf(syncStmt, &updSerStr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(affectRows, 2);
    EXPECT_EQ(strcmp(updateObj3.originString, "aaaaaaa"), 0);
    DbDynMemCtxFree(alterMemCtx, updateObj3.originString);
    DbDynMemCtxFree(alterMemCtx, updateObj3.newString);
    ret = GmcDropVertexLabel(syncStmt, g_schemaJsonName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 版本1
#pragma pack(1)
typedef struct TagSpeciallabelVertexT1V2 {
    int64_t v0;
} GtSpeciallabelVertexT1V2T;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertexV2 {
    int64_t f0;
    uint64_t f1;
    uint16_t f2Len;
    char *f2;
    uint16_t t1VCount;
    GtSpeciallabelVertexT1V2T *t1;
} GtSpeciallabelVertexV2T;
#pragma pack()

// 版本2
#pragma pack(1)
typedef struct TagSpeciallabelVertexT1V3 {
    int64_t v0;
    int64_t v1;
} GtSpeciallabelVertexT1V3T;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertexT2V3 {
    int64_t v0;
    int64_t v1;
} GtSpeciallabelVertexT2V3T;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertexV3 {
    int64_t f0;
    uint64_t f1;
    uint16_t f2Len;
    char *f2;
    uint16_t t1VCount;
    GtSpeciallabelVertexT1V3T *t1;
    uint16_t t2VCount;
    GtSpeciallabelVertexT2V3T *t2;
} GtSpeciallabelVertexV3T;
#pragma pack()

static void GtSpeciallabel2GetNodeT1(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *t1V = t1;
}

static void GtSpeciallabel2GetNodeT2(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t2V)
{
    GmcNodeT *Root, *t2;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *t2V = t2;
}

// 版本1序列化写入一条数据
static void WriteDataForSpecialV1(
    GmcStmtT *stmt, uint32_t index, uint32_t version, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, version, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV2T *v1 = (GtSpeciallabelVertexV2T *)malloc(sizeof(GtSpeciallabelVertexV2T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV2T), 0x00, sizeof(GtSpeciallabelVertexV2T));
    v1->f0 = index;
    v1->f1 = index;
    char *f2Value = (char *)malloc(8);
    (void)snprintf((char *)f2Value, 8, "%s", "abc");
    v1->f2Len = strlen(f2Value);
    v1->f2 = f2Value;
    v1->t1VCount = 1;

    GtSpeciallabelVertexT1V2T *t1 = (GtSpeciallabelVertexT1V2T *)malloc(sizeof(GtSpeciallabelVertexT1V2T));
    EXPECT_EQ(true, t1 != NULL);
    (void)memset_s(t1, sizeof(GtSpeciallabelVertexT1V2T), 0x00, sizeof(GtSpeciallabelVertexT1V2T));
    t1->v0 = index;
    v1->t1 = t1;

    // 结构化写数据
    StructTestCtx updCtx = (StructTestCtx){0};
    updCtx.stmt = stmt;
    GmcSeriT updSerStr;
    updSerStr.seriFunc = SeriStructVertex;
    updSerStr.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr.obj = (uint8_t *)v1;
    updSerStr.userData = &updCtx;

    GetSerialVertexLength(&updSerStr);
    ret = GmcSetVertexWithBuf(stmt, &updSerStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(v1);
    free(f2Value);
    free(t1);
}

// 版本4序列化写入一条数据
static void WriteDataForSpecialV4(
    GmcStmtT *stmt, uint32_t index, uint32_t version, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, version, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV3T *v1 = (GtSpeciallabelVertexV3T *)malloc(sizeof(GtSpeciallabelVertexV3T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV3T), 0x00, sizeof(GtSpeciallabelVertexV3T));
    v1->f0 = index;
    v1->f1 = index;
    char *f2Value = (char *)malloc(8);
    (void)snprintf((char *)f2Value, 8, "%s", "abc");
    v1->f2Len = strlen(f2Value);
    v1->f2 = f2Value;
    v1->t1VCount = 1;
    v1->t2VCount = 1;

    GtSpeciallabelVertexT1V3T *t1 = (GtSpeciallabelVertexT1V3T *)malloc(sizeof(GtSpeciallabelVertexT1V3T));
    EXPECT_EQ(true, t1 != NULL);
    (void)memset_s(t1, sizeof(GtSpeciallabelVertexT1V3T), 0x00, sizeof(GtSpeciallabelVertexT1V3T));
    t1->v0 = index;
    v1->t1 = t1;

    GtSpeciallabelVertexT2V3T *t2 = (GtSpeciallabelVertexT2V3T *)malloc(sizeof(GtSpeciallabelVertexT2V3T));
    EXPECT_EQ(true, t2 != NULL);
    (void)memset_s(t2, sizeof(GtSpeciallabelVertexT2V3T), 0x00, sizeof(GtSpeciallabelVertexT2V3T));
    t2->v0 = index;
    v1->t2 = t2;

    // 结构化写数据
    StructTestCtx updCtx = (StructTestCtx){0};
    updCtx.stmt = stmt;
    GmcSeriT updSerStr;
    updSerStr.seriFunc = SeriStructVertex;
    updSerStr.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr.obj = (uint8_t *)v1;
    updSerStr.userData = &updCtx;

    GetSerialVertexLength(&updSerStr);
    ret = GmcSetVertexWithBuf(stmt, &updSerStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(v1);
    free(f2Value);
    free(t1);
    free(t2);
}

// update旧表
static void UpdateDataForSpecialV1(
    GmcStmtT *stmt, uint32_t index, uint32_t delta, uint32_t version, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, version, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV2T *v1 = (GtSpeciallabelVertexV2T *)malloc(sizeof(GtSpeciallabelVertexV2T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV2T), 0x00, sizeof(GtSpeciallabelVertexV2T));
    // 序列化key buf
    StructTestCtx seriCtx0 = (StructTestCtx){0};
    GmcSeriT seri = (GmcSeriT){0};
    v1->f0 = index;
    SetKeySeri(stmt, v1, &seri, &seriCtx0, 0, NULL);
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &seri);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置属性
    index += delta;
    uint64_t f1Value = index;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *f2Value = "abcde";
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_STRING, f2Value, strlen(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新t1节点
    GmcNodeT *root = NULL;
    GmcNodeT *t1 = NULL;
    GmcNodeT *t2 = NULL;
    GtSpeciallabel2GetNodeT1(stmt, &root, &t1);
    int64_t t1Value = index;
    ret = GmcNodeSetPropertyByName(t1, (char *)"V0", GMC_DATATYPE_INT64, &t1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (version == 3) {
        GtSpeciallabel2GetNodeT2(stmt, &root, &t2);
        int64_t t2Value = index;
        ret = GmcNodeSetPropertyByName(t2, (char *)"V0", GMC_DATATYPE_INT64, &t2Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(v1);
}

// 读取数据进行校验
void ReadDataAndCompare(
    GmcStmtT *stmt, uint32_t index, uint32_t version, uint32_t delea, bool dataIsNull, GmcOperationTypeE opt)
{
    const char *specialName = "specialLabel";
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, specialName, version, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        return;
    }
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    int64_t f0Value = (int64_t)index;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isFinish);
    GmcNodeT *root = NULL, *t1Node = NULL, *t2Node = NULL;
    EXPECT_EQ(true, version == 2 || version == 3);
    // 校验公共属性
    GtSpeciallabel2GetNodeT1(stmt, &root, &t1Node);
    int64_t f0GetValue;
    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0GetValue, sizeof(int64_t), &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f0GetValue, index);  // 主键不会更新
    uint64_t f1GetValue;
    ret = GmcNodeGetPropertyByName(root, (char *)"F1", &f1GetValue, sizeof(uint64_t), &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(index + delea, f1GetValue);
    if (opt == GMC_OPERATION_INSERT) {
        const char *f2Value = "abc";
        char f2GetValue[100];
        uint32_t f2ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(root, "F2", &f2ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F2", f2GetValue, f2ValueLen, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        f2GetValue[f2ValueLen] = '\0';
        EXPECT_EQ(strcmp(f2Value, f2GetValue), 0);
    } else {
        const char *f2Value = "abcde";
        char f2GetValue[100];
        uint32_t f2ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(root, "F2", &f2ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F2", f2GetValue, f2ValueLen, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        f2GetValue[f2ValueLen] = '\0';
        EXPECT_EQ(strcmp(f2Value, f2GetValue), 0);
    }

    int64_t t1GetValue;
    ret = GmcNodeGetPropertyByName(t1Node, (char *)"V0", &t1GetValue, sizeof(int64_t), &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, false);
    EXPECT_EQ(index + delea, t1GetValue);

    if (version == 3) {
        GtSpeciallabel2GetNodeT2(stmt, &root, &t2Node);
        int64_t t2GetValue;
        ret = GmcNodeGetPropertyByName(t2Node, (char *)"V0", &t2GetValue, sizeof(int64_t), &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (dataIsNull) {
            EXPECT_EQ(isFinish, true);
        } else {
            EXPECT_EQ(isFinish, false);
            EXPECT_EQ(true, (t2GetValue == index + delea || t2GetValue == index));
        }
    }
}

TEST_F(StClientAlterSchemaStruct, specialRecordNodeTest02)
{
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(syncStmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/special_v2.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));

    // V2->v4
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/special_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, specialName);
    ASSERT_EQ(GMERR_OK, ret);

    // 版本2 结构化写入10条数据
    for (uint32_t i = 0; i < 100; i++) {
        WriteDataForSpecialV1(syncStmt, i, 2, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, true, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // 版本3 结构化写入一条数据
    for (uint32_t i = 100; i < 200; i++) {
        WriteDataForSpecialV4(syncStmt, i, 3, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // update : v3更新数据
    for (uint32_t i = 0; i < 200; i++) {
        uint32_t delta = 10;
        UpdateDataForSpecialV1(syncStmt, i, delta, 3, specialName, GMC_OPERATION_UPDATE);
        // 版本3校验数据
        ReadDataAndCompare(syncStmt, i, 3, delta, false, GMC_OPERATION_UPDATE);
        // 版本2校验数据
        ReadDataAndCompare(syncStmt, i, 2, delta, false, GMC_OPERATION_UPDATE);
    }
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, specialName));
}

TEST_F(StClientAlterSchemaStruct, specialRecordNodeTest02_1)
{
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(syncStmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/special_v2.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));

    // V2->v4
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/special_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, specialName);
    ASSERT_EQ(GMERR_OK, ret);

    // 版本2 结构化写入10条数据
    for (uint32_t i = 0; i < 100; i++) {
        WriteDataForSpecialV1(syncStmt, i, 2, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, true, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // 版本3 结构化写入一条数据
    for (uint32_t i = 100; i < 200; i++) {
        WriteDataForSpecialV4(syncStmt, i, 3, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // update : v2更新数据
    for (uint32_t i = 0; i < 200; i++) {
        uint32_t delta = 10;
        UpdateDataForSpecialV1(syncStmt, i, delta, 2, specialName, GMC_OPERATION_UPDATE);
        // 版本3校验数据
        if (i < 100) {
            ReadDataAndCompare(syncStmt, i, 3, delta, true, GMC_OPERATION_UPDATE);
        } else {
            ReadDataAndCompare(syncStmt, i, 3, delta, false, GMC_OPERATION_UPDATE);
        }
        // 版本2校验数据
        ReadDataAndCompare(syncStmt, i, 2, delta, false, GMC_OPERATION_UPDATE);
    }
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, specialName));
}

TEST_F(StClientAlterSchemaStruct, specialRecordNodeTest03)
{
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(syncStmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/special_v2.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));

    // V2->v4
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/special_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, specialName);
    ASSERT_EQ(GMERR_OK, ret);

    // 版本2 结构化写入10条数据
    for (uint32_t i = 0; i < 100; i++) {
        WriteDataForSpecialV1(syncStmt, i, 2, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, true, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // 版本3 结构化写入一条数据
    for (uint32_t i = 100; i < 200; i++) {
        WriteDataForSpecialV4(syncStmt, i, 3, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // merge
    for (uint32_t i = 0; i < 200; i++) {
        uint32_t delta = 10;
        UpdateDataForSpecialV1(syncStmt, i, delta, 3, specialName, GMC_OPERATION_MERGE);
        // 版本3校验数据
        ReadDataAndCompare(syncStmt, i, 3, delta, false, GMC_OPERATION_MERGE);
        // 版本2校验数据
        ReadDataAndCompare(syncStmt, i, 2, delta, false, GMC_OPERATION_MERGE);
    }
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, specialName));
}

TEST_F(StClientAlterSchemaStruct, specialRecordNodeTest03_1)
{
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(syncStmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/special_v2.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));

    // V2->v4
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/special_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, specialName);
    ASSERT_EQ(GMERR_OK, ret);

    // 版本2 结构化写入10条数据
    for (uint32_t i = 0; i < 100; i++) {
        WriteDataForSpecialV1(syncStmt, i, 2, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, true, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // 版本3 结构化写入一条数据
    for (uint32_t i = 100; i < 200; i++) {
        WriteDataForSpecialV4(syncStmt, i, 3, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // merge
    for (uint32_t i = 0; i < 200; i++) {
        uint32_t delta = 10;
        UpdateDataForSpecialV1(syncStmt, i, delta, 2, specialName, GMC_OPERATION_MERGE);
        // 版本2校验数据
        if (i < 100) {
            ReadDataAndCompare(syncStmt, i, 3, delta, true, GMC_OPERATION_MERGE);
        } else {
            ReadDataAndCompare(syncStmt, i, 3, delta, false, GMC_OPERATION_MERGE);
        }
        // 版本3校验数据
        ReadDataAndCompare(syncStmt, i, 2, delta, false, GMC_OPERATION_MERGE);
    }
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, specialName));
}

TEST_F(StClientAlterSchemaStructPageSize, alterSchemaWithPageSize)
{
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(syncStmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("./008_schema_upgrade/st_data/special_v2.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, schemaV1.c_str(), NULL));

    // V2->v4
    string schemaV4 = GetFileContext("./008_schema_upgrade/st_data/special_v4.gmjson");
    ret = GmcAlterVertexLabelWithName(syncStmt, schemaV4.c_str(), true, specialName);
    ASSERT_EQ(GMERR_OK, ret);

    // 版本2 结构化写入10条数据
    for (uint32_t i = 0; i < 100; i++) {
        WriteDataForSpecialV1(syncStmt, i, 2, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, true, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // 版本3 结构化写入一条数据
    for (uint32_t i = 100; i < 200; i++) {
        WriteDataForSpecialV4(syncStmt, i, 3, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(syncStmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(syncStmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // merge
    for (uint32_t i = 0; i < 200; i++) {
        uint32_t delta = 10;
        UpdateDataForSpecialV1(syncStmt, i, delta, 2, specialName, GMC_OPERATION_MERGE);
        // 版本2校验数据
        if (i < 100) {
            ReadDataAndCompare(syncStmt, i, 3, delta, true, GMC_OPERATION_MERGE);
        } else {
            ReadDataAndCompare(syncStmt, i, 3, delta, false, GMC_OPERATION_MERGE);
        }
        // 版本3校验数据
        ReadDataAndCompare(syncStmt, i, 2, delta, false, GMC_OPERATION_MERGE);
    }
    // 删除表
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, specialName));
}
