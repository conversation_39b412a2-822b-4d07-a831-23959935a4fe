[{"type": "record", "name": "generalComplexLabel", "schema_version": 0, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "size": 100, "nullable": true}, {"name": "F15", "type": "fixed", "nullable": false, "size": 16}, {"name": "F16", "type": "bytes", "nullable": false, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "F17", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F18", "type": "uint32: 17", "nullable": false, "default": "0x1ffff"}, {"name": "F19", "type": "bitmap", "size": 16}, {"name": "F20", "type": "uint32", "nullable": false}, {"name": "F21", "type": "uint32", "nullable": false}], "keys": [{"node": "generalComplexLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]