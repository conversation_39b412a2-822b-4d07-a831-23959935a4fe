[{"type": "record", "special_complex": true, "name": "<PERSON><PERSON><PERSON><PERSON>", "schema_version": 3, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "T1", "type": "record", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "V4", "type": "int16", "nullable": true}, {"name": "V5", "type": "uint16", "nullable": true}]}, {"name": "T2", "type": "record", "nullable": true, "fields": [{"name": "V0", "type": "int8", "nullable": true}, {"name": "V1", "type": "uint8", "nullable": true}, {"name": "V2", "type": "boolean", "nullable": true}, {"name": "V3", "type": "float", "nullable": true}, {"name": "V4", "type": "double", "nullable": true}, {"name": "V5", "type": "time", "nullable": true}]}, {"name": "T3", "type": "record", "nullable": true, "fields": [{"name": "V0", "type": "int8", "nullable": true}, {"name": "V1", "type": "uint8", "nullable": true}, {"name": "V2", "type": "boolean", "nullable": true}, {"name": "V3", "type": "float", "nullable": true}, {"name": "V4", "type": "double", "nullable": true}, {"name": "V5", "type": "time", "nullable": true}]}], "keys": [{"node": "<PERSON><PERSON><PERSON><PERSON>", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]