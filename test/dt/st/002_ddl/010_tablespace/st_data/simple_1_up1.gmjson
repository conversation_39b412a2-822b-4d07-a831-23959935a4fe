{"type": "record", "name": "simpleLabel", "schema_version": 1, "tablespace": "tsp1", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "int32", "nullable": true}, {"name": "F4", "type": "int32", "nullable": true}], "keys": [{"name": "ip4_key", "index": {"type": "primary"}, "node": "simpleLabel", "fields": ["F0"], "constraints": {"unique": true}}, {"name": "ip4_hashcluster", "index": {"type": "hashcluster"}, "node": "simpleLabel", "fields": ["F1"]}, {"name": "ip4_key_local", "index": {"type": "local"}, "node": "simpleLabel", "fields": ["F2"]}]}