{"type": "record", "name": "simpleLabel2", "tablespace": "tsp2", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "int32", "nullable": true}], "keys": [{"name": "ip4_key", "index": {"type": "primary"}, "node": "simpleLabel2", "fields": ["F0"], "constraints": {"unique": true}}, {"name": "ip4_hashcluster", "index": {"type": "hashcluster"}, "node": "simpleLabel2", "fields": ["F1"]}, {"name": "ip4_key_local", "index": {"type": "local"}, "node": "simpleLabel2", "fields": ["F2"]}]}