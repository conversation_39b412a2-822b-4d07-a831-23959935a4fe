[{"type": "record", "name": "TestOptimistic2", "fields": [{"name": "F0", "type": "uint64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "uint64", "nullable": false}, {"name": "F3", "type": "uint64", "nullable": true}, {"name": "F4", "type": "uint64", "nullable": true}], "keys": [{"node": "TestOptimistic2", "name": "T35_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]