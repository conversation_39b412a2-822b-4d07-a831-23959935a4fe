[{"type": "record", "name": "TestDefragmentation", "fields": [{"name": "F0", "type": "uint64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "uint64", "nullable": false}, {"name": "F3", "type": "uint64", "nullable": true}, {"name": "F4", "type": "uint64", "nullable": true}, {"name": "F5", "type": "uint64", "nullable": true}, {"name": "F6", "type": "uint64", "nullable": true}, {"name": "F7", "type": "uint64", "nullable": true}, {"name": "F8", "type": "uint64", "nullable": true}, {"name": "F9", "type": "uint64", "nullable": true}, {"name": "F10", "type": "uint64", "nullable": true}, {"name": "F11", "type": "uint64", "nullable": true}, {"name": "F12", "type": "uint64", "nullable": true}, {"name": "F13", "type": "uint64", "nullable": true}, {"name": "F14", "type": "uint64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "uint64", "nullable": true}, {"name": "F17", "type": "uint64", "nullable": true}, {"name": "F18", "type": "uint64", "nullable": true}, {"name": "F19", "type": "uint64", "nullable": true}, {"name": "F20", "type": "uint64", "nullable": true}, {"name": "F21", "type": "uint64", "nullable": true}, {"name": "F22", "type": "uint64", "nullable": true}, {"name": "F23", "type": "uint64", "nullable": true}, {"name": "F24", "type": "uint64", "nullable": true}, {"name": "F25", "type": "uint64", "nullable": true}, {"name": "F26", "type": "uint64", "nullable": true}, {"name": "F27", "type": "uint64", "nullable": true}, {"name": "F28", "type": "uint64", "nullable": true}, {"name": "F29", "type": "uint64", "nullable": true}, {"name": "F30", "type": "uint64", "nullable": true}, {"name": "F31", "type": "uint64", "nullable": true}, {"name": "F32", "type": "uint64", "nullable": true}, {"name": "F33", "type": "uint64", "nullable": true}, {"name": "F34", "type": "uint64", "nullable": true}, {"name": "F35", "type": "uint64", "nullable": true}, {"name": "F36", "type": "uint64", "nullable": true}, {"name": "F37", "type": "uint64", "nullable": true}, {"name": "F38", "type": "uint64", "nullable": true}, {"name": "F39", "type": "uint64", "nullable": true}, {"name": "F40", "type": "uint64", "nullable": true}, {"name": "F41", "type": "uint64", "nullable": true}, {"name": "F42", "type": "uint64", "nullable": true}, {"name": "F43", "type": "uint64", "nullable": true}, {"name": "F44", "type": "uint64", "nullable": true}, {"name": "F45", "type": "uint64", "nullable": true}, {"name": "F46", "type": "uint64", "nullable": true}, {"name": "F47", "type": "uint64", "nullable": true}, {"name": "F48", "type": "uint64", "nullable": true}, {"name": "F49", "type": "uint64", "nullable": true}, {"name": "F50", "type": "uint64", "nullable": true}, {"name": "F51", "type": "uint64", "nullable": true}, {"name": "F52", "type": "uint64", "nullable": true}, {"name": "F53", "type": "uint64", "nullable": true}, {"name": "F54", "type": "uint64", "nullable": true}], "keys": [{"node": "TestDefragmentation", "name": "T35_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}, "config": {"init_hash_capacity": 8000000}}, {"node": "TestDefragmentation", "name": "localIndex", "fields": ["F1"], "index": {"type": "local"}, "constraints": {"unique": true}}, {"node": "TestDefragmentation", "name": "hashclusterIndex", "fields": ["F2"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}]}]