/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: test cases for optimistic
 * Create: 2022-08-05
 */
#include <climits>
#include <unistd.h>
#include <sys/epoll.h>
#include "gtest/gtest.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "dm_data_basic.h"
#include "connection/clt_conn.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "dm_data_prop.h"
#include "db_utils.h"
#include "stub.h"
#include "db_list.h"
#include "clt_stmt.h"
#include "adpt_sleep.h"
#include "storage_st_common.h"
#include "file_op.h"
#include "adpt_string.h"
#include "cstring"
#include "tools_st_common.h"
#include "gmc_internal.h"
#include "st_common.h"
#include "yang/yang_testframe.h"

/*
测试功能：
OptimisiticTrx_001、OptimisiticTrx_002、OptimisiticTrx_003：测试namespace冲突检测功能(表级别)。

OptimisiticTrx_098、OptimisiticTrx_099：测试gc线程回收。

用例测试场景（提前预置好数据）：
OptimisiticTrx_001---两个事务操作相同namespace下的同表不同行，预期发生冲突，包括vertex和kv表测试。
OptimisiticTrx_002---两个事务操作相同namespace下的不同表不同行，预期没有发生冲突。
OptimisiticTrx_003---两个事务操作不同namespace下的行，预期没有发生冲突。

OptimisiticTrx_012---测试乐观事务下，建边流程undoLog的优化
OptimisiticTrx_013---测试乐观事务下，建边流程undoLog的优化+savePoint的处理
OptimisiticTrx_014---测试乐观事务下，建边流程undoLog的优化+savePoint的处理(大对象，无法优化顶点情况，只能优化边)
OptimisiticTrx_015---测试乐观事务下，建边流程undoLog的优化+savePoint的处理(跳转行非大对象，无法优化顶点情况，只能优化边)

OptimisiticTrx_017---测试乐观事务下，undoLog的优化，测试在masterVersion上\undo版本链上的合并
OptimisiticTrx_018---测试乐观事务下，undoLog的优化，测试在masterVersion上\undo版本链上的合并(var)
OptimisiticTrx_019---测试乐观事务下，undoLog的优化，测试在masterVersion上\undo版本链上的合并（大对象）
OptimisiticTrx_020---测试乐观事务下，undoLog的优化，大对象反复更新，确保能回收dst行和分片行，不会影响

OptimisiticTrx_021---测试乐观事务下，跳转行的异常分支释放流程

OptimisiticTrx_022---测试乐观事务下，undoLog的优化，测试在masterVersion上\undo版本链上的合并(fix)

OptimisiticTrx_023---测试乐观事务支持独立事务冲突检测接口，分别测试乐观事务冲突与非冲突，该接口返回是否正确
OptimisiticTrx_024---测试乐观事务支持独立事务冲突检测接口，测试悲观事务调用此接口，该接口返回是否正确

OptimisiticTrx_025---测试乐观事务下，创造大数据量的undoLog，然后回滚到savePoint，循环20次
OptimisiticTrx_026---测试savePoint回滚到头，undoRec为0，再回滚整个事务，retainedUndo的处理情况，预期不会有错误日志
    预置2条记录，三个事务，1.trx3、trx2先begin，trx2开启事务，创建savePoint，执行update，然后回滚到savePoint，再提交。
    2.trx1开启事务，创建savePoint，执行update操作，然后回滚到savePoint，再回滚整个事务，预期不会有错误日志
    3.trx3提交，清理各种资源
OptimisiticTrx_027---用例026的场景，trx1最后改成提交事务

OptimisiticTrx_030：乐观下两个事务，执行删除/更新，回滚/提交，进行组合序列测试，
包含定长、变长、大对象、跳转行这些情况

OptimisiticTrx_031：显示开启只读事务，阻塞purge回收，期间不断执行更新操作，使得undo积压，通过视图查找到一直执行更新操作的线程信息

OptimisiticTrx_095---测试乐观后台purge线程与truncate操作的并发，测试处理已被truncate的数据的undoLog的处理
OptimisiticTrx_095_drop---测试乐观后台purge线程与drop操作的并发，测试purge对vertexLabel的引用计数正确

OptimisiticTrx_098---测试乐观事务undo清理能力，调小undo空间使其限制undo只能申请两页，预期以下操作正常
    开启事务，插入并更新单条数据，提交事务。重复两次，预期成功，不会有申请undo页失败的错误。
OptimisiticTrx_099---查heap视图获取当前占用page个数，显示开启trx1不做操作，另起trx2，执行delete操作删除所有记录，trx2提交，
    再次查heap视图，预期占用page个数不发生改变，提交trx1后休眠一段时间，第3次查heap视图，预期占用page个数为0。

OptimisiticTrx_100---测试视图savepoint字段。1.创建savepoint：sp1，然后进行dml操作，检查视图savepoint数量，名字是否匹配。
    2.创建savepoint：sp2，检查视图字段。3.创建1022个匿名savepoint，检查视图字段。4.回滚到sp2检查视图字段。5.回滚到sp1检查视图字段。
OptimisiticTrx_101---测试视图savepoint最大显示数量（1024个）。1.创建3000个savepoint，查视图savepoint数量为3000，显示最新1024个。
    2.回滚到第1001个，检查视图字段，savepoint数量为1001个。
OptimisiticTrx_102---测试乐观RR事务，主键索引为CCEH场景下，视图 STORAGE_HASH_INDEX_STAT 的 MULTIVERSION_PAGE_NUM
    值变化情况是否符合预期
OptimisiticTrx_103---测试非乐观RR事务，主键索引为CCEH场景下，视图 STORAGE_HASH_INDEX_STAT 的
    MULTIVERSION_PAGE_NUM 值是否恒为0

OptimisiticTrx_110---测试乐观事务下，undoLog的优化，测试在masterVersion上\undo版本链上的合并(边表)，覆盖函数FixedHeapUpdateInVersionChain
OptimisiticTrx_111---测试乐观事务下，多个事务操作相同namespace下的同表不同行，预期发生冲突，打印DFX增强信息
OptimisiticTrx_112---测试乐观事务下，多事务交叉时max_record_count结果
OptimisiticTrx_113-OptimisiticTrx_115---测试purge连续回收版本的结果
OptimisiticTrx_116-OptimisiticTrx_117---测试冲突校验支持表级别读写粒度

OptimisiticTrx_118---测试乐观事务下，undoLog的优化，测试在masterVersion上\undo版本链上的合并，更新时是否会漏删除索引
*/

class StStorageOptimisiticTrx : public StStorage {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    void SetUp()
    {
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
    }
    void TearDown()
    {
        ShutDownDbServer();
        SaveLogAfterFailed();
    }
};

static const char *g_labelConfig = R"({"max_record_count":500001, "defragmentation":false})";
static const char *g_labelName = "TestOptimistic";
static const char *g_labelName2 = "TestOptimistic2";
static const char *g_kvLabelName = "TestKvOptimistic";

static const char *g_indexName = "T35_K0";
static const char *g_indexField = "F0";

typedef struct TestParamInfo {
    bool isTrx1FirEnd;  // 第1个事务是否先提交/回滚（决定了是在masterVersion上/版本链上提交/回滚）
    bool isTrx1Commit;
    bool isTrx2Commit;
    bool isTrx1Update;
    bool isTrx2Update;
} TestParamInfoT;

static inline void DefaultConf(GmcTxConfigT *config)
{
    DB_POINTER(config);
    config->transMode = GMC_TRANS_USED_IN_CS;
    config->type = GMC_TX_ISOLATION_REPEATABLE;
    config->trxType = GMC_OPTIMISTIC_TRX;
    config->readOnly = false;
}

static void CLientInit(GmcConnT **connection, GmcStmtT **stmt)
{
    CreateSyncConnectionAndStmt(connection, stmt);
}

static void CLientFinal(GmcConnT *connection, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    int32_t ret = GmcDisconnect(connection);
    EXPECT_EQ(ret, GMERR_OK);
}

static int32_t InsertLabelData(GmcStmtT *stmt, uint64_t value, bool isKvLabel = false)
{
    if (!isKvLabel) {
        int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        return ret;
    } else {
        int32_t ret = GmcKvSet(stmt, &value, sizeof(value), &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
}

static int32_t InsertVertexAsync(GmcStmtT *stmt, GmcAsyncRequestDoneContextT *context, uint64_t value)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecuteAsync(stmt, context);
    return ret;
}

static int32_t InsertVertexWithPrimaryValue(GmcStmtT *stmt, uint64_t value, uint64_t primaryValue)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &primaryValue, sizeof(primaryValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

static int32_t InsertVertexWithPrimaryValueAsync(
    GmcStmtT *stmt, GmcAsyncRequestDoneContextT *context, uint64_t value, uint64_t primaryValue)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &primaryValue, sizeof(primaryValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecuteAsync(stmt, context);
    return ret;
}

static int32_t ReplaceVertexAll(
    GmcStmtT *stmt, uint64_t value, const char *index, uint64_t indexValue, bool isNeedChangeBufSize = false)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, index, GMC_DATATYPE_UINT64, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    if (isNeedChangeBufSize) {
        char str[1024] = "aaaa";
        for (uint32_t i = 0; i < indexValue; i++) {
            strcat_s(str, sizeof(str), "DmGetVertexLabelSeriBufLength");
        }
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    return ret;
}

static int32_t ReplaceVertexAllAsync(
    GmcStmtT *stmt, GmcAsyncRequestDoneContextT *context, uint64_t value, const char *index, uint64_t indexValue)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, index, GMC_DATATYPE_UINT64, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecuteAsync(stmt, context);
    return ret;
}

static int32_t MergeVertexAll(GmcStmtT *stmt, uint64_t value, const char *index, uint64_t indexValue)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

static int32_t DeleteLabelData(GmcStmtT *stmt, uint64_t value, bool isKvLabel = false)
{
    if (!isKvLabel) {
        // 删除顶点
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, g_indexName));
        int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        return GmcExecute(stmt);
    } else {
        return GmcKvRemove(stmt, &value, sizeof(value));
    }
}

static int32_t DeleteLabelDataAsync(
    GmcStmtT *stmt, GmcAsyncRequestDoneContextT *context, uint64_t value, bool isKvLabel = false)
{
    if (!isKvLabel) {
        // 删除顶点
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, g_indexName));
        int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        return GmcExecuteAsync(stmt, context);
    } else {
        return GmcKvRemoveAsync(stmt, &value, sizeof(value), context->deleteCb, context->userData);
    }
}

static int32_t FetchVertexF1(GmcStmtT *stmt, uint64_t priK, uint64_t expect)
{
    // 查询
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, g_indexName));
    int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &priK, sizeof(uint64_t));
    do {
        ret = GmcExecute(stmt);
    } while (ret != GMERR_OK);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    // 校验F1的值
    unsigned int sizeValue = 0;
    bool isNull = false;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &sizeValue);
    EXPECT_EQ(GMERR_OK, ret);
    if (sizeValue == 8) {
        char *pValue = (char *)malloc(sizeValue);
        EXPECT_NE((void *)NULL, pValue);
        ret = GmcGetVertexPropertyByName(stmt, "F1", pValue, sizeValue, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (!isNull) {
            EXPECT_EQ(expect, (*(uint64_t *)pValue));
        } else {
            EXPECT_EQ(0, 1);
        }
        free(pValue);
    }
    return ret;
}

static int32_t FetchVertexF1Err(GmcStmtT *stmt, uint64_t priK, Status Gmerr)
{
    int32_t ret = GmcSetIndexKeyName(stmt, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &priK, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    unsigned int sizeValue = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &sizeValue);
    EXPECT_EQ(Gmerr, ret);
    return ret;
}

static int32_t OptimisiticPrePareData(uint32_t recordNum, string jsonPath, GmcStmtT *stmt, bool isKvLabel = false)
{
    int ret;
    if (!isKvLabel) {
        // 建表
        string test_schema = GetFileContext(jsonPath);
        ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
        EXPECT_EQ(GMERR_OK, ret);

        // 插入初始数据
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcKvCreateTable(stmt, g_kvLabelName, g_labelConfig);
        EXPECT_EQ(GMERR_OK, ret);

        // 插入初始数据
        ret = GmcKvPrepareStmtByLabelName(stmt, g_kvLabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(stmt, i, isKvLabel);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

static void OptimisiticPrePare(bool isSetPageSize = false)
{
    // 目前kv表测试嵌套在同一个用例，不启动server由前一个负责启动
    if (isSetPageSize) {
        StartDbServerWithConfig(
            " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"pageSize=4\"");
    } else {
        StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                                "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    }
}

static int32_t OptimisiticPrePareForTable(GmcConnT **connection, GmcStmtT **stmt, const char *labelName,
    const char *schemaPath, uint32_t recordNum, bool isFirst)
{
    DB_POINTER2(connection, stmt);
    int32_t ret;
    if (isFirst) {
        StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                                "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
        ret = GmcInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    CLientInit(connection, stmt);

    // 建表
    string test_schema = GetFileContext(schemaPath);
    ret = GmcCreateVertexLabel(*stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入初始数据
    ret = GmcPrepareStmtByLabelName(*stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(*stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

static int32_t OptimisiticPrePareForTableWithNamespace(GmcConnT **connection, GmcStmtT **stmt, const char *labelName,
    const char *schemaPath, const char *nameSpace, uint32_t recordNum, bool isFirst)
{
    int32_t ret;
    if (isFirst) {
        StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                                "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
        ret = GmcInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    CLientInit(connection, stmt);

    // 建立命名空间
    GmcNspCfgT nspCfg = {0};
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = nameSpace;
    nspCfg.userName = NULL;
    nspCfg.trxCfg = {.trxType = GMC_OPTIMISTIC_TRX, .isolationLevel = GMC_TX_ISOLATION_REPEATABLE};
    ret = GmcCreateNamespaceWithCfg(*stmt, &nspCfg);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(*stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    string test_schema = GetFileContext(schemaPath);
    ret = GmcCreateVertexLabel(*stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入初始数据
    ret = GmcPrepareStmtByLabelName(*stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(*stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

static int32_t OptimisiticClear(GmcConnT *connection, GmcStmtT *stmt, const char *labelName, bool isKvLabel = false)
{
    if (!isKvLabel) {
        GmcFreeIndexKey(stmt);
        GmcDropVertexLabel(stmt, labelName);
    } else {
        GmcKvDropTable(stmt, g_kvLabelName);
    }
    CLientFinal(connection, stmt);
    return 0;
}

static int32_t OptimisiticClearForTables(GmcConnT *connection, GmcStmtT *stmt, const char *labelName, bool isLast)
{
    GmcFreeIndexKey(stmt);
    GmcDropVertexLabel(stmt, labelName);
    CLientFinal(connection, stmt);
    if (isLast) {
        int32_t ret = GmcUnInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    return 0;
}

static int32_t OptimisiticClearForTablesWithNamespace(
    GmcConnT *connection, GmcStmtT *stmt, const char *labelName, const char *nameSpace, bool isLast)
{
    GmcFreeIndexKey(stmt);
    GmcDropVertexLabel(stmt, labelName);
    GmcDropNamespace(stmt, nameSpace);
    CLientFinal(connection, stmt);
    if (isLast) {
        int32_t ret = GmcUnInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    return 0;
}

void OptimisiticTrxCheckGcHandleHistoryUndoLog()
{
    //  校验Gc处理过程中是否会有问题
    int32_t retry = 100;
    char cmdOutput[64] = {0};
    uint32_t purgeUndoLogSum1;
    uint32_t purgeUndoLogSum2;
    while (true) {
        if (retry > 0) {
            (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
            GetViewFieldResult("V\\$STORAGE_UNDO_PURGER_INFO", "PURGE_UNDO_LOG_SUM", cmdOutput, 64);
            purgeUndoLogSum1 = atoi(cmdOutput);
            DbSleep(1000);
            (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
            GetViewFieldResult("V\\$STORAGE_UNDO_PURGER_INFO", "PURGE_UNDO_LOG_SUM", cmdOutput, 64);
            purgeUndoLogSum2 = atoi(cmdOutput);
            if (purgeUndoLogSum1 == purgeUndoLogSum2 && purgeUndoLogSum2 != 0) {
                break;
            } else {
                printf("retry: %d, TASK_STATUS:%s\n", retry, cmdOutput);
            }
        } else {
            ASSERT_EQ(0, 1);
        }
        DbSleep(1000);
        retry--;
    }
}

// 校验undo purge视图字段数值
void StCheckUndoPurgerField(const char *fieldName, int32_t num, int32_t tryTime)
{
    char cmdOutput[64] = {0};
    char numStr[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    sprintf_s(numStr, sizeof(numStr), " %" PRIu32 "\n", num);  // GetViewFieldResult里获取的输出前面带了空格
    // 检查后台清理线程的执行情况
    int32_t retry = tryTime;
    int cmpRet = 1;
    do {
        (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
        GetViewFieldResult("V\\$STORAGE_UNDO_PURGER_INFO", fieldName, cmdOutput, 64);
        cmpRet = strncmp(numStr, cmdOutput, strlen(numStr));
        DbSleep(1000);
        retry--;
        if (retry <= 0) {
            EXPECT_STREQ(numStr, cmdOutput);
        }
    } while (cmpRet != 0 && retry > 0);
}

void OptimisiticTrx001(bool isKvLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, isKvLabel);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);

    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    if (!isKvLabel) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_DELETE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, g_kvLabelName));
    }
    int64_t indexValue = 0;
    ret = DeleteLabelData(stmt1, indexValue, isKvLabel);
    EXPECT_EQ(GMERR_OK, ret);

    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    if (!isKvLabel) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_DELETE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, g_kvLabelName));
    }
    indexValue = 1;
    ret = DeleteLabelData(stmt2, indexValue, isKvLabel);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);  // label级别的冲突域
    ret = GmcTransRollBack(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    OptimisiticClear(conn, stmt, g_labelName, isKvLabel);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_001)
{
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    OptimisiticTrx001(false);  // test vertex label
#ifndef FEATURE_PERSISTENCE
    // kv 表不支持持久化
    OptimisiticTrx001(true);  // test kv label
#endif
}

#ifndef FEATURE_PERSISTENCE
// 持久化场景页至少 8K
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_001_pageSize)
{
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"pageSize=4\"");
    OptimisiticTrx001(false);  // test vertex label
    OptimisiticTrx001(true);   // test kv label
}
#endif

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_002)
{
    const char *tables[] = {g_labelName, g_labelName2};
    const char *schemaFiles[] = {"./st_file/optimisiticTrx.gmjson", "./st_file/optimisiticTrx2.gmjson"};
    const int32_t tableCount = 2;
    const int32_t stmtCount = 2;
    GmcConnT *connArr[tableCount] = {0};
    GmcStmtT *stmtArr[stmtCount] = {0};
    int32_t ret;
    bool isfirst = true;
    uint32_t insertNum = 2;
    for (uint32_t i = 0; i < tableCount; ++i) {
        isfirst = i == 0 ? true : false;
        ret = OptimisiticPrePareForTable(&connArr[i], &stmtArr[i], tables[i], schemaFiles[i], insertNum, isfirst);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 事务1删除表1的索引值是0的行
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, tables[0], GMC_OPERATION_DELETE));
    int64_t indexValue = 0;
    ret = DeleteLabelData(stmt1, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2删除表2的索引值是0的行
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, tables[1], GMC_OPERATION_DELETE));
    indexValue = 0;
    ret = DeleteLabelData(stmt2, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);  // label级别的冲突域

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    for (uint32_t i = 0; i < tableCount; ++i) {
        if (i == tableCount - 1) {
            OptimisiticClearForTables(connArr[i], stmtArr[i], tables[i], true);
        } else {
            OptimisiticClearForTables(connArr[i], stmtArr[i], tables[i], false);
        }
    }
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_003)
{
    const char *namespaces[] = {"test_namespace1", "test_namespace2"};
    const char *tables[] = {g_labelName, g_labelName2};
    const char *schemaFiles[] = {"./st_file/optimisiticTrx.gmjson", "./st_file/optimisiticTrx2.gmjson"};
    const int32_t tableCount = 2;
    const int32_t stmtCount = 2;
    GmcConnT *connArr[tableCount] = {0};
    GmcStmtT *stmtArr[stmtCount] = {0};
    int32_t ret;
    bool isfirst = true;
    uint32_t insertNum = 2;
    for (uint32_t i = 0; i < tableCount; ++i) {
        isfirst = i == 0 ? true : false;
        ret = OptimisiticPrePareForTableWithNamespace(
            &connArr[i], &stmtArr[i], tables[i], schemaFiles[i], namespaces[i], insertNum, isfirst);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 事务1删除表1的索引值是0的行
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    EXPECT_EQ(GMERR_OK, ret);
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    ret = GmcUseNamespace(stmt1, namespaces[0]);  // 建立连接后需要指定命名空间
    EXPECT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, tables[0], GMC_OPERATION_DELETE));
    int64_t indexValue = 0;
    ret = DeleteLabelData(stmt1, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2删除表2的索引值是0的行
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    EXPECT_EQ(GMERR_OK, ret);
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    ret = GmcUseNamespace(stmt2, namespaces[1]);
    EXPECT_EQ(GMERR_OK, ret);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, tables[1], GMC_OPERATION_DELETE));
    indexValue = 0;
    ret = DeleteLabelData(stmt2, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);  // label级别的冲突域
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    for (uint32_t i = 0; i < tableCount; ++i) {
        if (i == tableCount - 1) {
            OptimisiticClearForTablesWithNamespace(connArr[i], stmtArr[i], tables[i], namespaces[i], true);
        } else {
            OptimisiticClearForTablesWithNamespace(connArr[i], stmtArr[i], tables[i], namespaces[i], false);
        }
    }
}

void Trx1FirstTrx1DeleteCommitAndTrx2UpdateRollback(string jsonPath, bool isSetPageSize = false)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    OptimisiticPrePare(isSetPageSize);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启事务1删除索引值为1的数据
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue = 1;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_OK);  // 执行删除前可以查到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_DELETE));
    indexValue = 1;
    ret = DeleteLabelData(stmt1, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_NO_DATA);  // 删除后查不到，此过程是事务1执行期间

    // 开启事务2修改索引值为1的数据为200
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    int64_t expectValue = 1;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务2执行期间不受事务1影响，可以查到原来的数值
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE));
    int64_t replaceValue = 200;
    indexValue = 1;
    ret = ReplaceVertexAll(stmt2, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 200;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务2执行期间自己的修改有效

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_NO_DATA);  // 事务1不受事务2执行期间的影响

    // 先后提交事务1和2
    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(
        stmt2, indexValue, GMERR_OK);  // 此次操作在事务2中是成功的,不受事务1提交影响,可以读取到事务1修改前的旧版本
    FetchVertexF1(stmt2, indexValue, expectValue);

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt2, indexValue, GMERR_NO_DATA);  // 事务2修改冲突被回滚，事务1提交生效

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TrxEnd(bool isTrxCommit, GmcConnT *conn)
{
    Status ret;
    if (isTrxCommit) {
        ret = GmcTransCommit(conn);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcTransRollBack(conn);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TrxDeleteData(GmcConnT *conn, GmcStmtT *stmt, uint32_t insertNum)
{
    GmcTxConfigT config;
    DefaultConf(&config);
    int32_t ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue;
    for (uint32_t i = 0; i < insertNum; i++) {
        indexValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
        FetchVertexF1Err(stmt, indexValue, GMERR_OK);  // 执行删除前可以查到
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE));
        ret = DeleteLabelData(stmt, indexValue);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
        FetchVertexF1Err(stmt, indexValue, GMERR_NO_DATA);  // 删除后查不到，此过程是事务1执行期间
    }
}

void TrxUpdateData(GmcConnT *conn, GmcStmtT *stmt, uint32_t insertNum, bool isSecondUpdate)
{
    GmcTxConfigT config;
    DefaultConf(&config);
    int32_t ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue = 1;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    int64_t expectValue = 1;
    int64_t replaceValue = 200;
    for (uint32_t i = 0; i < insertNum; i++) {
        indexValue = (int64_t)i;
        expectValue = isSecondUpdate ? replaceValue : (int64_t)i;
        FetchVertexF1(stmt, indexValue, expectValue);  // 事务2执行期间不受事务1影响，可以查到原来的数值
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE));
        ret = ReplaceVertexAll(stmt, replaceValue, g_indexField, indexValue);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
        expectValue = 200;
        FetchVertexF1(stmt, indexValue, expectValue);  // 事务2执行期间自己的修改有效
    }
}

void MakeLinkRow(GmcStmtT *stmt, uint32_t insertNum)
{
    char cmdOutput[64] = {0};
    const char *filter = "LABEL_NAME=\'TestOptimistic\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);
    uint32_t heapPageNum = atoi(cmdOutput);
    EXPECT_GE(heapPageNum, 2u);  // 预期插入表超过1个页，方便构造跳转行

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE));
    int32_t ret;
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t value = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        // 这里为啥要设置这么多变长字段？
        // heap的fsm的0级链表是一个区间，在insert过程中大概率无法使得一个页正好被用满，用到一定程度就放到0级链表了
        // 因此在执行update的时候，会触发页面整理寻找连续空间，因此变长字段设置得少会导致buf过短，无法构造出跳转行
        const char *stringValue =
            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);  // 原地更新不够，更新构造成跳转行
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void Trx1AndTrx2HandleFunc(string jsonPath, TestParamInfoT testParamInfo, uint32_t insertNum, bool isLinkRow)
{
    if (testParamInfo.isTrx2Commit && testParamInfo.isTrx1Commit) {
        ASSERT_EQ(0, 1);  // 两个事务不能同时提交
    }
    GmcConnT *conn;
    GmcStmtT *stmt;
    CLientInit(&conn, &stmt);
    int32_t ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 构造跳转行
    if (isLinkRow) {
        MakeLinkRow(stmt, insertNum);
    }

    // 开启事务1删除
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    if (testParamInfo.isTrx1Update) {
        TrxUpdateData(conn1, stmt1, insertNum, false);
    } else {
        TrxDeleteData(conn1, stmt1, insertNum);
    }

    // 开启事务2删除
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    if (testParamInfo.isTrx2Update) {
        TrxUpdateData(conn2, stmt2, insertNum, false);
    } else {
        TrxDeleteData(conn2, stmt2, insertNum);
    }

    // 构造undoRec合并的场景
    if (testParamInfo.isTrx2Update) {
        TrxUpdateData(conn2, stmt2, insertNum, true);
    }
    if (testParamInfo.isTrx1Update) {
        TrxUpdateData(conn1, stmt1, insertNum, true);
    }

    if (testParamInfo.isTrx1FirEnd) {
        TrxEnd(testParamInfo.isTrx1Commit, conn1);
        TrxEnd(testParamInfo.isTrx2Commit, conn2);
    } else {
        TrxEnd(testParamInfo.isTrx2Commit, conn2);
        TrxEnd(testParamInfo.isTrx1Commit, conn1);
    }

    if (testParamInfo.isTrx2Commit || testParamInfo.isTrx1Commit) {
        OptimisiticTrxCheckGcHandleHistoryUndoLog();
    }

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    OptimisiticClear(conn, stmt, g_labelName);
}

uint32_t GetNoDataNumInLog()
{
    // 查看日志中的error数量
    FILE *fp = popen("cat ./log/run/rgmserver/* | grep 1001000 | wc -l", "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    return atoi(errorNum);
}

void Trx1AndTrx2HandleFuncForTest(string jsonPath, TestParamInfoT testParamInfo, uint32_t insertNum)
{
    if (testParamInfo.isTrx2Commit && testParamInfo.isTrx1Commit) {
        ASSERT_EQ(0, 1);  // 两个事务不能同时提交
    }
    GmcConnT *conn;
    GmcStmtT *stmt;
    OptimisiticPrePare(false);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t noDataErrorLog1 = GetNoDataNumInLog();

    // 构造跳转行
    MakeLinkRow(stmt, insertNum);

    // 开启事务1删除
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    if (testParamInfo.isTrx1Update) {
        TrxUpdateData(conn1, stmt1, insertNum, false);
    } else {
        TrxDeleteData(conn1, stmt1, insertNum);
    }

    // 开启事务2删除
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    if (testParamInfo.isTrx2Update) {
        TrxUpdateData(conn2, stmt2, insertNum, false);
    } else {
        TrxDeleteData(conn2, stmt2, insertNum);
    }

    // 构造undoRec合并的场景
    if (testParamInfo.isTrx2Update) {
        TrxUpdateData(conn2, stmt2, insertNum, true);
    }
    if (testParamInfo.isTrx1Update) {
        TrxUpdateData(conn1, stmt1, 1, true);
    }

    ret = GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < insertNum; i++) {
        ret = InsertLabelData(stmt2, i, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (testParamInfo.isTrx1FirEnd) {
        TrxEnd(testParamInfo.isTrx1Commit, conn1);
        TrxEnd(testParamInfo.isTrx2Commit, conn2);
    } else {
        TrxEnd(testParamInfo.isTrx2Commit, conn2);
        TrxEnd(testParamInfo.isTrx1Commit, conn1);
    }

    if (testParamInfo.isTrx2Commit || testParamInfo.isTrx1Commit) {
        OptimisiticTrxCheckGcHandleHistoryUndoLog();
    }
    uint32_t noDataErrorLog2 = GetNoDataNumInLog();
    EXPECT_EQ(noDataErrorLog2, noDataErrorLog1);  // 预期没有新的no_data日志产生

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_0830)
{
    // 事务1先结束；事务1回滚，事务2回滚；事务1更新，事务2删除
    TestParamInfoT testParamInfo = {
        .isTrx1FirEnd = true, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = false};
    string jsonPath = "./st_file/optimisiticTrx_varRow.gmjson";
    Trx1AndTrx2HandleFuncForTest(jsonPath, testParamInfo, 1000);
}

typedef enum EnumTrxHandleArray {
    TRX1_ROLLBACK_DEL_AND_TRX2_COMMIT_DEL = 0,
    TRX1_ROLLBACK_DEL_AND_TRX2_COMMIT_UPD,
    TRX1_ROLLBACK_UPD_AND_TRX2_COMMIT_DEL,
    TRX1_ROLLBACK_UPD_AND_TRX2_COMMIT_UPD,
    TRX1_ROLLBACK_DEL_AND_TRX2_ROLLBACK_DEL,
    TRX1_ROLLBACK_DEL_AND_TRX2_ROLLBACK_UPD,
    TRX1_ROLLBACK_UPD_AND_TRX2_ROLLBACK_DEL,
    TRX1_ROLLBACK_UPD_AND_TRX2_ROLLBACK_UPD,
    TRX1_COMMIT_DEL_AND_TRX2_ROLLBACK_DEL,
    TRX1_COMMIT_DEL_AND_TRX2_ROLLBACK_UPD,
    TRX1_COMMIT_UPD_AND_TRX2_ROLLBACK_DEL,
    TRX1_COMMIT_UPD_AND_TRX2_ROLLBACK_UPD,
    // 以下是trx2先提交/回滚的情况，决定是在版本链还是masterVersion上回滚
    TRX2_ROLLBACK_DEL_AND_TRX1_COMMIT_DEL,
    TRX2_ROLLBACK_DEL_AND_TRX1_COMMIT_UPD,
    TRX2_ROLLBACK_UPD_AND_TRX1_COMMIT_DEL,
    TRX2_ROLLBACK_UPD_AND_TRX1_COMMIT_UPD,
    TRX2_ROLLBACK_DEL_AND_TRX1_ROLLBACK_DEL,
    TRX2_ROLLBACK_DEL_AND_TRX1_ROLLBACK_UPD,
    TRX2_ROLLBACK_UPD_AND_TRX1_ROLLBACK_DEL,
    TRX2_ROLLBACK_UPD_AND_TRX1_ROLLBACK_UPD,
    TRX2_COMMIT_DEL_AND_TRX1_ROLLBACK_DEL,
    TRX2_COMMIT_DEL_AND_TRX1_ROLLBACK_UPD,
    TRX2_COMMIT_UPD_AND_TRX1_ROLLBACK_DEL,
    TRX2_COMMIT_UPD_AND_TRX1_ROLLBACK_UPD,
    TRX_HANDLE_TOP
} TrxHandleArrayE;

TestParamInfoT g_testParamInfo[TRX_HANDLE_TOP] = {
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = false, .isTrx2Update = false},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = false, .isTrx2Update = true},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = true, .isTrx2Update = false},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = true, .isTrx2Update = true},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = false},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = true},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = false},
    {.isTrx1FirEnd = true, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = true},
    {.isTrx1FirEnd = true, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = false},
    {.isTrx1FirEnd = true, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = true},
    {.isTrx1FirEnd = true, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = false},
    {.isTrx1FirEnd = true, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = true},
    // 以下是trx2先提交/回滚的情况，决定是在版本链还是masterVersion上回滚
    {.isTrx1FirEnd = false, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = false},
    {.isTrx1FirEnd = false, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = false},
    {.isTrx1FirEnd = false, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = true},
    {.isTrx1FirEnd = false, .isTrx1Commit = true, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = true},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = false},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = false},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = false, .isTrx2Update = true},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = false, .isTrx1Update = true, .isTrx2Update = true},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = false, .isTrx2Update = false},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = true, .isTrx2Update = false},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = false, .isTrx2Update = true},
    {.isTrx1FirEnd = false, .isTrx1Commit = false, .isTrx2Commit = true, .isTrx1Update = true, .isTrx2Update = true},
};

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_030_normalRow)
{
    OptimisiticPrePare(false);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    string jsonPath = "./st_file/optimisiticTrx_varRow.gmjson";
    for (uint32_t i = 0; i < TRX_HANDLE_TOP; i++) {
        Trx1AndTrx2HandleFunc(jsonPath, g_testParamInfo[i], 10, false);
    }
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_030_fixRow)
{
    OptimisiticPrePare(false);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    string jsonPath = "./st_file/optimisiticTrx_fixRow.gmjson";
    for (uint32_t i = 0; i < TRX_HANDLE_TOP; i++) {
        Trx1AndTrx2HandleFunc(jsonPath, g_testParamInfo[i], 10, false);
    }
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_030_linkRow)
{
    PERSISTENCE_NOT_SUPPORT;
    OptimisiticPrePare(false);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    string jsonPath = "./st_file/optimisiticTrx_varRow.gmjson";
    for (uint32_t i = 0; i < TRX_HANDLE_TOP; i++) {
        Trx1AndTrx2HandleFunc(jsonPath, g_testParamInfo[i], 1000, true);
    }
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#ifndef FEATURE_PERSISTENCE
// 持久化暂不支持：大对象、pageSize=4K
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_030_largeObject)
{
    OptimisiticPrePare(false);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
    for (uint32_t i = 0; i < TRX_HANDLE_TOP; i++) {
        Trx1AndTrx2HandleFunc(jsonPath, g_testParamInfo[i], 10, false);
    }
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_004_pageSize)
{
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    Trx1FirstTrx1DeleteCommitAndTrx2UpdateRollback(jsonPath, true);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_004_varRow_pageSize)
{
    string jsonPath = "./st_file/optimisiticTrx_varRow.gmjson";
    Trx1FirstTrx1DeleteCommitAndTrx2UpdateRollback(jsonPath, true);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_004_largeObject_pageSize)
{
    string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
    Trx1FirstTrx1DeleteCommitAndTrx2UpdateRollback(jsonPath, true);
}

void Trx1DeleteRollbackAndTrx2UpdateCommit(string jsonPath, bool isSetPageSize = false)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    OptimisiticPrePare(isSetPageSize);
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 开启事务1删除索引值为1的数据
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue = 1;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_OK);  // 执行删除前可以查到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_DELETE));
    indexValue = 1;
    ret = DeleteLabelData(stmt1, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_NO_DATA);  // 删除后查不到，此过程是事务1执行期间

    // 开启事务2修改索引值为1的数据为200
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    int64_t expectValue = 1;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务2执行期间不受事务1影响，可以查到原来的数值
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE));
    int64_t replaceValue = 200;
    indexValue = 1;
    ret = ReplaceVertexAll(stmt2, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 200;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务2执行期间自己的修改有效
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));  // 这里有bug
    FetchVertexF1Err(stmt1, indexValue, GMERR_NO_DATA);

    // 先后提交事务2和1
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_NO_DATA);  // 事务1没有提交，还是查到自己删除后的情况（隔离性）

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_OK);  // 事务1删除冲突被回滚，事务2提交生效
    expectValue = 200;
    FetchVertexF1(stmt1, indexValue, expectValue);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_008_pageSize)
{
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    Trx1DeleteRollbackAndTrx2UpdateCommit(jsonPath, true);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_008_varRow_pageSize)
{
    string jsonPath = "./st_file/optimisiticTrx_varRow.gmjson";
    Trx1DeleteRollbackAndTrx2UpdateCommit(jsonPath, true);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_008_largeObject_pageSize)
{
    string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
    Trx1DeleteRollbackAndTrx2UpdateCommit(jsonPath, true);
}

#endif

void Trx1UpdateRollbackAndTrx2DeleteCommit(string jsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    OptimisiticPrePare();
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 事务1更改索引值为1的行
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    uint64_t indexValue = 1;
    uint64_t expectValue = 1;
    FetchVertexF1(stmt1, indexValue, expectValue);  // 事务1的修改前读取
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_REPLACE));
    uint64_t replaceValue = 100;
    ret = ReplaceVertexAll(stmt1, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 100;
    FetchVertexF1(stmt1, indexValue, expectValue);  // 事务1的修改在执行期间自己可见

    // 事务2删除索引值为1的行
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 1;
    FetchVertexF1(
        stmt2, indexValue, expectValue);  // 查询到事务1修改前的值，因为事务具有隔离性,事务2开始时事务1没有提交
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_DELETE));
    ret = DeleteLabelData(stmt2, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt2, indexValue, GMERR_NO_DATA);  // 事务2执行期间内删除生效

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 100;
    FetchVertexF1(stmt1, indexValue, expectValue);  // 事务1不被事务2影响

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1(stmt1, indexValue, expectValue);  // 事务1不被事务2影响

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1Err(stmt1, indexValue, GMERR_NO_DATA);  // 事务2生效事务1回滚

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

int32_t StOptimisiticTransStart(GmcConnT *conn, bool readOnly)
{
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.readOnly = readOnly;
    return GmcTransStart(conn, &config);
}

static void CommonAsyncDCLCb(void *userData, Status status, const char *errMsg)
{
    ASSERT_EQ(GMERR_OK, status);
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

static void CommonAsyncDCLCbError(void *userData, Status status, const char *errMsg)
{
    ASSERT_NE(GMERR_OK, status);
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

#ifndef FEATURE_PERSISTENCE
// 持久化暂不支持边
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_012)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);

    // 建表
    string jsonPath = "./st_file/srcVertex.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    jsonPath = "./st_file/dstVertex.gmjson";
    test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 开启事务
    uint32_t recordNum = 1;
    StOptimisiticTransStart(conn1, false);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_SRC", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(stmt1, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 插入表2触发自动建边
    recordNum = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertVertexWithPrimaryValue(stmt1, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", cmdOutput, 64);
    uint32_t undoRecordNum = atoi(cmdOutput);
    // 因为都是同一个事务插入的点，每个顶点/边都只有1条undo记录，更新首边地址/边链表都能被优化掉，不会产生undoLog
    uint32_t expectRecNum = recordNum * 2 + 1;
    EXPECT_EQ(undoRecordNum, expectRecNum);
    printf("undoRecordNum: %d\n", undoRecordNum);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

static int32_t FetchSrcF1(GmcStmtT *stmt, uint64_t priK, uint64_t expect)
{
    // 查询
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "T_SRC_PK"));
    int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &priK, sizeof(uint64_t));
    do {
        ret = GmcExecute(stmt);
    } while (ret != GMERR_OK);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    // 校验F1的值
    unsigned int sizeValue = 0;
    bool isNull = false;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &sizeValue);
    EXPECT_EQ(GMERR_OK, ret);
    if (sizeValue == 8) {
        char *pValue = (char *)malloc(sizeValue);
        EXPECT_NE((void *)NULL, pValue);
        ret = GmcGetVertexPropertyByName(stmt, "F1", pValue, sizeValue, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (!isNull) {
            EXPECT_EQ(expect, (*(uint64_t *)pValue));
        } else {
            EXPECT_EQ(0, 1);
        }
        free(pValue);
    }
    return ret;
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_031)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);

    // 建表
    string jsonPath = "./st_file/srcVertex.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    jsonPath = "./st_file/dstVertex.gmjson";
    test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 开启事务
    uint32_t recordNum = 1;
    StOptimisiticTransStart(conn1, false);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_SRC", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(stmt1, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    StOptimisiticTransStart(conn, true);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_SCAN));
    FetchSrcF1(stmt, 0, 0);

    StOptimisiticTransStart(conn1, false);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_SRC", GMC_OPERATION_REPLACE));
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(stmt1, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 插入表2触发自动建边
    recordNum = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertVertexWithPrimaryValue(stmt1, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    system("gmsysview -q V\\$STORAGE_HEAP_STAT");
    GetViewFieldResult("V\\$STORAGE_HEAP_STAT", "LAST_OPTIMISTIC_TRX_LABEL_CNT", cmdOutput, 64);
    uint32_t undoRecordNum = atoi(cmdOutput);
    EXPECT_EQ(undoRecordNum, 3u);  // 预期操作了两个顶点表+一个边表
    printf("undoRecordNum: %d\n", undoRecordNum);

    system("gmsysview -q V\\$STORAGE_EDGE_LABEL_STAT");  // 补充边表视图覆盖率
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

void CommonAsyncDMLCb(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    ASSERT_EQ(GMERR_OK, status);

    ASSERT_EQ(1u, affectedRows);
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

void CommonAsyncDMLForUpdCb(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    ASSERT_EQ(GMERR_OK, status);

    ASSERT_EQ(2u, affectedRows);
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

typedef struct TagAsyncUserDataT {
    uint32_t received;
    uint32_t expStatus;  // 预期返回值
} AsyncUserDataT;

void CommonAsyncDMLCbOOM(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (status != GMERR_OK) {
        ASSERT_EQ(GMERR_OUT_OF_MEMORY, status);
    } else {
        ASSERT_EQ(GMERR_OK, status);
        ASSERT_EQ(1u, affectedRows);
    }
    AsyncUserDataT *userDataTmp = (AsyncUserDataT *)userData;
    uint32_t *received = &userDataTmp->received;
    (*received)++;
    userDataTmp->expStatus = status;
}

void AsyncWait(uint32_t *received, uint32_t result)
{
    for (unsigned timeMs = 0, timeoutMs = 60 * 1000;;) {
        DbUsleep(1000);
        ++timeMs;
        if (*received == result) {
            break; /* completed */
        }
        /* return on time out */
        ASSERT_LT(timeMs, timeoutMs);
    }
}

const char *g_msConfigJson = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";

static int32_t testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

static void testInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}

static void testPreYangFieldProperty(
    GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, char *str, bool isList = false)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F3", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F4", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F5", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F6", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F7", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F8", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F9", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
    testInitPropValue(&propValue, "F10", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    EXPECT_EQ(GMERR_OK, ret);
}

static void testInsertYangFieldProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool isList = false)
{
    char str[1024] = {0};
    const char *p = "a";
    for (int j = 0; j < 1023; j++) {
        strcat_s(str, sizeof(str), p);
    }
    testPreYangFieldProperty(node, i, opType, str);
}

static void testUpdateYangFieldProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool isList = false)
{
    char str[1024 * 3] = {0};
    const char *p = "a";
    for (int j = 0; j < 1023 * 3; j++) {
        strcat_s(str, sizeof(str), p);
    }
    testPreYangFieldProperty(node, i, opType, str);
}

typedef struct UserBatchData {
    uint32_t expectedTotalNum;
    uint32_t expectedSuccessNum;
    uint32_t *received;
} UserBatchDataT;

static void BatchCbNew(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    EXPECT_EQ(GMERR_OK, status);
    ASSERT_NE(nullptr, userData);
    UserBatchDataT *data = (UserBatchDataT *)userData;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    auto ret = GmcBatchDeparseRet(batchRet, &totalNum, &successNum);
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(data->expectedTotalNum, totalNum);
    EXPECT_EQ(data->expectedSuccessNum, successNum);
    (*data->received)++;
}

void MakeLinkRowForDiff()
{
    uint32_t receivedForDCL = 0;
    uint32_t resultForDCL = 0;
    uint32_t receivedForDML = 0;
    uint32_t resultForDML = 0;
    GmcBatchT *batch = {0};
    GmcNodeT *rootNode = NULL;

    // 启动事务1，写入一条数据
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    resultForDCL++;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    int ret = GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &receivedForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDCL, resultForDCL);

    ret = testBatchPrepareAndSetDiff(conn1, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    testInsertYangFieldProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    resultForDML++;
    UserBatchDataT data = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = &receivedForDML};
    ret = GmcBatchExecuteAsync(batch, BatchCbNew, &data);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDML, resultForDML);

    // 事务1更新数据，占2个页（跳转行）
    ret = GmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    resultForDML++;
    ret = GmcBatchExecuteAsync(batch, BatchCbNew, &data);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDML, resultForDML);
    // 提交事务1，成功
    resultForDCL++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &receivedForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDCL, resultForDCL);

    // 查heap视图，预期有跳转行产生
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'root\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "HISTORY_LINKROWS", filter, cmdOutput, 64);
    uint32_t linkRow = atoi(cmdOutput);
    EXPECT_EQ(linkRow, 1u);

    DestroyConnectionAndStmt(conn1, stmt1);
}

void InsertForDiff(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcNodeT *rootNode = NULL;
    uint32_t receivedForDML = 0;
    uint32_t resultForDML = 0;
    GmcBatchT *batch = {0};
    int ret = testBatchPrepareAndSetDiff(conn, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 这里重新插入更新后的buf
    ret = GmcBatchAddDML(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    resultForDML++;
    UserBatchDataT data = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = &receivedForDML};
    ret = GmcBatchExecuteAsync(batch, BatchCbNew, &data);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDML, resultForDML);
}

void UpdateForDiff(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcNodeT *rootNode = NULL;
    uint32_t receivedForDML = 0;
    uint32_t resultForDML = 0;
    GmcBatchT *batch = {0};
    int ret = testBatchPrepareAndSetDiff(conn, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    resultForDML++;
    UserBatchDataT data = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = &receivedForDML};
    ret = GmcBatchExecuteAsync(batch, BatchCbNew, &data);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDML, resultForDML);
}

void DeleteForDiff(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcNodeT *rootNode = NULL;
    uint32_t receivedForDML = 0;
    uint32_t resultForDML = 0;
    GmcBatchT *batch = {0};
    int ret = testBatchPrepareAndSetDiff(conn, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_REMOVE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    resultForDML++;
    UserBatchDataT data = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = &receivedForDML};
    ret = GmcBatchExecuteAsync(batch, BatchCbNew, &data);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDML, resultForDML);
}

void TrxBeginForDiff(GmcConnT *conn)
{
    uint32_t receivedForDCL = 0;
    uint32_t resultForDCL = 1;

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    int ret = GmcTransStartAsync(conn, &config, CommonAsyncDCLCb, &receivedForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&receivedForDCL, resultForDCL);
}

void TrxEndForDiff(bool isTrxCommit, GmcConnT *conn)
{
    Status ret;
    uint32_t receivedForDCL = 0;
    uint32_t resultForDCL = 1;
    if (isTrxCommit) {
        ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &receivedForDCL);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&receivedForDCL, resultForDCL);
    } else {
        ret = GmcTransRollBackAsync(conn, CommonAsyncDCLCb, &receivedForDCL);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&receivedForDCL, resultForDCL);
    }
}

void CheckDiff(GmcStmtT *stmt, vector<string> expectDiff)
{
    // 执行diff获取
    std::atomic_uint32_t step{0};
    uint32_t expectStep{1};
    uint64_t startTime = DbGetNsec();
    FetchRetCbParam param = {
        .step = &step,
        .stmt = stmt,
        .startTime = startTime,
        .times = 1,
        .expectStatus = GMERR_OK,
        .filterMode = 0,
        .lastExpectIdx = 0,
        .isSubTree = false,
        .expectReply = expectDiff,
        .printTime = false,
        .isValidate = true,
        .noParseTree = false,
    };
    // 对于延迟获取diff的批操作（即：GMC_YANG_DIFF_DELAY_READ_ON类型），需要Client端再发起一个FetchAndDeparseDiff的操作
    ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(stmt, NULL, AsyncFetchRetCb, &param));
    AsyncWait((uint32_t *)&step, expectStep);
}

void Trx1AndTrx2HandleFuncForDiffTest(TestParamInfoT testParamInfo, vector<string> expectDiff)
{
    // 本函数构造root节点跳转行来完成diff校验
    if (testParamInfo.isTrx2Commit && testParamInfo.isTrx1Commit) {
        ASSERT_EQ(0, 1);  // 两个事务不能同时提交
    }

    MakeLinkRowForDiff();

    // 启动事务1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    TrxBeginForDiff(conn1);
    if (testParamInfo.isTrx1Update) {
        UpdateForDiff(conn1, stmt1);
    } else {
        DeleteForDiff(conn1, stmt1);
    }

    // 启动事务2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);
    TrxBeginForDiff(conn2);
    if (testParamInfo.isTrx2Update) {
        UpdateForDiff(conn2, stmt2);
    } else {
        DeleteForDiff(conn2, stmt2);
    }

    if (testParamInfo.isTrx2Update) {
        // 构造undoRec合并的场景
        UpdateForDiff(conn2, stmt2);
    } else {
        // 删除完再插入回去，验证diff
        InsertForDiff(conn2, stmt2);
    }
    if (testParamInfo.isTrx1Update) {
        // 构造undoRec合并的场景
        UpdateForDiff(conn1, stmt1);
    } else {
        // 删除完再插入回去，验证diff
        InsertForDiff(conn1, stmt1);
    }

    CheckDiff(stmt1, expectDiff);
    CheckDiff(stmt2, expectDiff);

    if (testParamInfo.isTrx1FirEnd) {
        TrxEndForDiff(testParamInfo.isTrx1Commit, conn1);
        TrxEndForDiff(testParamInfo.isTrx2Commit, conn2);
    } else {
        TrxEndForDiff(testParamInfo.isTrx2Commit, conn2);
        TrxEndForDiff(testParamInfo.isTrx1Commit, conn1);
    }

    if (testParamInfo.isTrx2Commit || testParamInfo.isTrx1Commit) {
        OptimisiticTrxCheckGcHandleHistoryUndoLog();
    }

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_diff)
{
    PERSISTENCE_NOT_SUPPORT;
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext("./st_file/optimisticTrx_diff.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_msConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    vector<string> expectDiff = {};  // 删除完又插入/更新成一模一样的，预期都没有diff产生
    for (uint32_t i = 0; i < TRX_HANDLE_TOP; i++) {
        ret = GmcTruncateVertexLabel(stmt, "root");
        EXPECT_EQ(GMERR_OK, ret);
        Trx1AndTrx2HandleFuncForDiffTest(g_testParamInfo[i], expectDiff);
    }

    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#ifdef EXPERIMENTAL_GUANGQI
typedef struct BeginOptiTrxWithCloneId {
    uint32_t resultForDCL;
    uint32_t cloneId;
} BeginOptiTrxWithCloneIdOutput;

static void TransStartWithCloneIdCb(void *userData, GmcStartExtResT *tmpRes, Status status, const char *errMsg)
{
    BeginOptiTrxWithCloneIdOutput *output = (BeginOptiTrxWithCloneIdOutput *)(userData);
    output->resultForDCL++;
    GmcTransStartExtResGetCloneId(tmpRes, &output->cloneId);
    ASSERT_EQ(GMERR_OK, status);
}

static void TransStartWithCloneIdErrorCb(void *userData, GmcStartExtResT *tmpRes, Status status, const char *errMsg)
{
    BeginOptiTrxWithCloneIdOutput *output = (BeginOptiTrxWithCloneIdOutput *)(userData);
    output->resultForDCL++;
    GmcTransStartExtResGetCloneId(tmpRes, &output->cloneId);
    ASSERT_NE(GMERR_OK, status);
}

void PrepareTable(bool insertRootNode)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CLientInit(&conn, &stmt);
    string srcJsonPath = "./st_file/srcVertex.gmjson";
    string dstJsonPath = "./st_file/dstVertex.gmjson";
    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    int ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(dstJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    if (insertRootNode) {
        // 预置根节点
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
        ret = InsertLabelData(stmt, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DestroyConnectionAndStmt(conn, stmt);
}

// 测试clone事务开启，包含错误设置事务类型场景
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic01)
{
#ifdef FEATURE_PERSISTENCE
    // 源码：sh build.sh clean && sh build.sh -f debug -s GUANGQI --experimental-guangqi
    // 编译方式sh build.sh -t debug --experimental-guangqi
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdErrorCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    resultForDCL++;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    resultForDCL++;
    ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    GmcTransStartExtOptionDestroy(option);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TestOptiRRTrxClone(bool isParentTrxRollBack)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);
    PrepareTable(true);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    // 创建子事务conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t testNum = 10;
    uint32_t dmlResult = 0;
    uint32_t dmlReceived = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &dmlReceived;
    for (uint32_t i = 0; i < testNum; i++) {
        if (i >= 1) {
            context.insertCb = CommonAsyncDMLForUpdCb;
        }
        BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
        uint32_t resultForDCL1 = 0;
        resultForDCL1++;
        ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&output1.resultForDCL, resultForDCL1);

        // 插入表2触发自动建边（5条dst）
        uint32_t recordNum = 5;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_REPLACE));
        for (uint32_t j = 0; j < recordNum; j++) {
            dmlResult++;
            ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, j);
            EXPECT_EQ(GMERR_OK, ret);
            AsyncWait(&dmlReceived, dmlResult);
        }

        resultForDCL1++;
        ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);  // 子事务提交（内部执行合并）
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&output1.resultForDCL, resultForDCL1);
    }
    // trxUsedSlotCnt的记录数预期4 本用例有32个(每一次克隆合并多出来3个)，视图查询1个，后台purge线程 1个
    uint32_t cmdLen = 64;
    char *cmdOutput = (char *)malloc(cmdLen);
    (void)memset_s(cmdOutput, cmdLen, 0, cmdLen);
    GetViewFieldResult("V\\$STORAGE_TRX_STAT", "TRX_USED_SLOT_NUM", cmdOutput, cmdLen);
    uint32_t trxUsedSlotCnt = atoi(cmdOutput);
    EXPECT_EQ(trxUsedSlotCnt, 34u);

    system("gmsysview -q V\\$STORAGE_TRX_DETAIL -f CLONE_ID=1");

    // 父事务再提交/回滚
    resultForDCL++;
    if (isParentTrxRollBack) {
        ret = GmcTransRollBackAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    } else {
        ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    // trxUsedSlotCnt的记录数预期4 本用例有2个，视图查询1个，后台purge线程 1个
    (void)memset_s(cmdOutput, cmdLen, 0, cmdLen);
    GetViewFieldResult("V\\$STORAGE_TRX_STAT", "TRX_USED_SLOT_NUM", cmdOutput, cmdLen);
    trxUsedSlotCnt = atoi(cmdOutput);
    EXPECT_EQ(trxUsedSlotCnt, 4u);
    free(cmdOutput);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试clone事务开启，连续10次执行clone操作、merge操作，正常提交场景测试
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic02)
{
    TestOptiRRTrxClone(false);
}

// 测试clone事务开启，连续10次执行clone操作、merge操作，回滚场景测试
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic03)
{
    TestOptiRRTrxClone(true);
}

// 测试事务同一时间只能被克隆一次
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic04)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 创建子事务2conn和stmt，预期失败
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);
    GmcStartExtOptionT *option2 = NULL;
    ret = GmcTransCreateStartExtOption(&option2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option2, output1.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output2 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL2 = 0;
    resultForDCL2++;
    ret = GmcTransStartExtAsync(conn2, option2, TransStartWithCloneIdErrorCb, &output2);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output2.resultForDCL, resultForDCL2);

    GmcTransStartExtOptionDestroy(option2);
    DestroyConnectionAndStmt(conn2, stmt2);

    // 子事务1提交（内部执行合并）
    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 父事务再提交
    resultForDCL++;
    ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试子事务自己回滚，父连接cloneId能继续被克隆
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic05)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 子事务1回滚
    resultForDCL1++;
    ret = GmcTransRollBackAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 创建子事务2conn和stmt，预期能正常clone
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);
    GmcStartExtOptionT *option2 = NULL;
    ret = GmcTransCreateStartExtOption(&option2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option2, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output2 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL2 = 0;
    resultForDCL2++;
    ret = GmcTransStartExtAsync(conn2, option2, TransStartWithCloneIdCb, &output2);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output2.resultForDCL, resultForDCL2);

    resultForDCL2++;
    ret = GmcTransRollBackAsync(conn2, CommonAsyncDCLCb, &output2.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output2.resultForDCL, resultForDCL2);

    GmcTransStartExtOptionDestroy(option2);
    DestroyConnectionAndStmt(conn2, stmt2);

    // 父事务能再提交
    resultForDCL++;
    ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TestOptiRRTrxMergeAfterParentTrxEnd(bool isParentTrxRollBack)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);
    PrepareTable(true);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 父事务先提交/回滚
    resultForDCL++;
    if (isParentTrxRollBack) {
        ret = GmcTransRollBackAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    } else {
        ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    uint32_t dmlResult = 0;
    uint32_t dmlReceived = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &dmlReceived;

    // 插入表2触发自动建边（5条dst）
    uint32_t recordNum = 5;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_REPLACE));
    for (uint32_t i = 0; i < recordNum; i++) {
        dmlResult++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&dmlReceived, dmlResult);
    }

    // 子事务1提交，预期失败，只能回滚
    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCbError, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    resultForDCL1++;
    ret = GmcTransRollBackAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试父事务被克隆后，若提交，则子事务会合并失败
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic06)
{
    TestOptiRRTrxMergeAfterParentTrxEnd(false);
}

// 测试父事务被克隆后，若回滚，则子事务会合并失败
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic07)
{
    TestOptiRRTrxMergeAfterParentTrxEnd(true);
}

// 测试子事务所在连接，在执行合并后，能继续开启新事务执行操作。
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic08)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);
    PrepareTable(true);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 子事务1提交（内部执行合并）
    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // conn1开启普通事务
    resultForDCL1++;
    ret = GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    uint32_t dmlResult = 0;
    uint32_t dmlReceived = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &dmlReceived;

    // 插入表2触发自动建边（5条dst）
    uint32_t recordNum = 5;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_REPLACE));
    for (uint32_t i = 0; i < recordNum; i++) {
        dmlResult++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&dmlReceived, dmlResult);
    }

    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 父事务再提交
    resultForDCL++;
    ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试子事务克隆出来后，能读到父事务的修改
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic09)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);
    PrepareTable(false);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    uint32_t dmlResult0 = 1;
    uint32_t dmlReceived0 = 0;
    GmcAsyncRequestDoneContextT context0;
    context0.insertCb = CommonAsyncDMLCb;
    context0.userData = &dmlReceived0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    ret = InsertVertexAsync(stmt, &context0, 0);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&dmlReceived0, dmlResult0);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 子事务1提交（内部执行合并）
    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // conn1开启普通事务
    resultForDCL1++;
    ret = GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    uint32_t dmlResult = 0;
    uint32_t dmlReceived = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &dmlReceived;

    // 插入表2（5条dst）不会触发自动建边
    uint32_t recordNum = 5;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_REPLACE));
    for (uint32_t i = 0; i < recordNum; i++) {
        dmlResult++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&dmlReceived, dmlResult);
    }

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "EDGELABEL_NAME=\'edgelabel\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "FIXED_HEAP_ROW_NUM", filter, cmdOutput, 64);
    uint32_t edgeRowNum = atoi(cmdOutput);
    ASSERT_EQ(edgeRowNum, 0u);

    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 父事务提交失败，因为前面开启的普通事务提交了，写了dst表，所以冲突
    resultForDCL++;
    ret = GmcTransCommitAsync(conn, CommonAsyncDCLCbError, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    resultForDCL++;
    ret = GmcTransRollBackAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试子事务克隆出来后，能读到父事务的修改
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic09_1)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);
    PrepareTable(false);

    uint32_t resultForDCL = 0;
    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    uint32_t dmlResult0 = 1;
    uint32_t dmlReceived0 = 0;
    GmcAsyncRequestDoneContextT context0;
    context0.insertCb = CommonAsyncDMLCb;
    context0.userData = &dmlReceived0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    ret = InsertVertexAsync(stmt, &context0, 0);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&dmlReceived0, dmlResult0);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 子事务1提交（内部执行合并）
    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // conn1开启普通事务
    resultForDCL1++;
    ret = GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    uint32_t dmlResult = 0;
    uint32_t dmlReceived = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &dmlReceived;

    // 插入表2（5条dst）不会触发自动建边
    uint32_t recordNum = 5;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_REPLACE));
    for (uint32_t i = 0; i < recordNum; i++) {
        dmlResult++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&dmlReceived, dmlResult);
    }

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "EDGELABEL_NAME=\'edgelabel\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "FIXED_HEAP_ROW_NUM", filter, cmdOutput, 64);
    uint32_t edgeRowNum = atoi(cmdOutput);
    ASSERT_EQ(edgeRowNum, 0u);

    resultForDCL1++;
    ret = GmcTransRollBackAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 父事务提交成功
    resultForDCL++;
    ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TestOptiRRTrxMergeAndDropTable(bool isParentTrxRollBack)
{
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" \"persistentMode=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\" "
        "\"featureNames=memdata,durablememdata,fastpath,trm,persistence,yang\"");
#else
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" \"scheduleMode=2\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableTrxClone=1\"");
#endif
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);
    // 建表
    string dstJsonPath = "./st_file/dstVertex.gmjson";
    string test_schema = GetFileContext(dstJsonPath);
    uint32_t resultForDCL = 0;
    resultForDCL++;
    BeginOptiTrxWithCloneIdOutput output = {.resultForDCL = 0, .cloneId = 0};
    ret = GmcCreateVertexLabelAsync(stmt, test_schema.c_str(), g_labelConfig, CommonAsyncDCLCb, &output.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    resultForDCL++;
    GmcStartExtOptionT *option = NULL;
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransCreateStartExtOption(&option);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtAsync(conn, option, TransStartWithCloneIdCb, &output);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);
    EXPECT_EQ(output.cloneId, 1u);

    // 创建子事务1conn和stmt
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    GmcStartExtOptionT *option1 = NULL;
    ret = GmcTransCreateStartExtOption(&option1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option1, output.cloneId);
    EXPECT_EQ(GMERR_OK, ret);

    BeginOptiTrxWithCloneIdOutput output1 = {.resultForDCL = 0, .cloneId = 0};
    uint32_t resultForDCL1 = 0;
    resultForDCL1++;
    ret = GmcTransStartExtAsync(conn1, option1, TransStartWithCloneIdCb, &output1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    uint32_t dmlResult = 0;
    uint32_t dmlReceived = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &dmlReceived;

    // 插入表2, 没有src，因此没有边
    uint32_t recordNum = 5;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_REPLACE));
    for (uint32_t i = 0; i < recordNum; i++) {
        dmlResult++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&dmlReceived, dmlResult);
    }

    // 子事务1提交，预期成功
    resultForDCL1++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 子事务1所在连接执行drop表，预期失败
    resultForDCL1++;
    ret = GmcDropVertexLabelAsync(stmt1, "T_DST", CommonAsyncDCLCbError, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    // 父事务提交/回滚
    resultForDCL++;
    if (isParentTrxRollBack) {
        ret = GmcTransRollBackAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    } else {
        ret = GmcTransCommitAsync(conn, CommonAsyncDCLCb, &output.resultForDCL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output.resultForDCL, resultForDCL);

    // 子事务所在连接执行drop表，预期成功
    resultForDCL1++;
    ret = GmcDropVertexLabelAsync(stmt1, "T_DST", CommonAsyncDCLCb, &output1.resultForDCL);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&output1.resultForDCL, resultForDCL1);

    GmcTransStartExtOptionDestroy(option);
    GmcTransStartExtOptionDestroy(option1);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试子事务克隆合并后，另外一个连接来drop表，drop不掉，需等父事务提交后才能drop
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic10)
{
    TestOptiRRTrxMergeAndDropTable(false);
}

// 测试子事务克隆合并后，另外一个连接来drop表，drop不掉，需等父事务回滚后才能drop
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_clone_basic11)
{
    TestOptiRRTrxMergeAndDropTable(true);
}
#endif

void TrxSavePointUndoLog(string srcJsonPath, string dstJsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(dstJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 预置根节点
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    ret = InsertLabelData(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);

    uint32_t received = 0;
    uint32_t result = 1;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &received;

    // 开启事务
    uint32_t recordNum = 1;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCreateSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    // 插入表2触发自动建边（50条dst）
    recordNum = 50;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        result++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    // 再次创建savePoint，再插入50条记录dstVertex，触发自动建边
    result++;
    ret = GmcTransCreateSavepointAsync(conn1, "ddd1", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    recordNum = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 50; i < recordNum; i++) {
        result++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", cmdOutput, 64);
    uint32_t undoRecordNum = atoi(cmdOutput);
    // 大对象顶点的首边地址更新、normal行的顶点的首边地址更新都可以节省undoLog
    // 第1次savePoint产生的101条（50个dstVertex的insert、50条edge的insert、root节点的首边地址更新1条）
    // 第2次savePoint（50条dstVertex的insert、50条edge的insert和邻居边的1条update(新的savePoint创建了，不能节省了)
    // 1条root的首边地址更新）
    uint32_t expectRowNum = (101 + 50 + 50 + 1 + 1);
    EXPECT_EQ(undoRecordNum, expectRowNum);
    printf("undoRecordNum: %d\n", undoRecordNum);

    result++;
    ret = GmcTransRollBackSavepointAsync(conn1, "ddd1", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", cmdOutput, 64);
    undoRecordNum = atoi(cmdOutput);
    expectRowNum = 101;
    EXPECT_EQ(undoRecordNum, expectRowNum);

    printf("undoRecordNum: %d\n", undoRecordNum);

    result++;
    ret = GmcTransRollBackSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    // 查heap视图，回滚完预期heap不占用页
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'T_DST\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);
    uint32_t heapPageNum = atoi(cmdOutput);
    EXPECT_EQ(heapPageNum, 0u);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T_DST\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNum = atoi(cmdOutput);
    EXPECT_EQ(indexEntryUsedNum, 0u);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TrxRollBackToSavePoint(string srcJsonPath, string dstJsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(dstJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 预置根节点
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    ret = InsertLabelData(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);

    uint32_t received = 0;
    uint32_t result = 1;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &received;

    // 开启事务
    uint32_t recordNum = 1;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    for (uint32_t k = 0; k < 20; k++) {
        result++;
        ret = GmcTransCreateSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);

        // 插入表2触发自动建边（500条dst）
        recordNum = 500;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
        for (uint32_t i = 0; i < recordNum; i++) {
            result++;
            ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
            EXPECT_EQ(GMERR_OK, ret);
            AsyncWait(&received, result);
        }

        // 再次创建savePoint，再插入500条记录dstVertex，触发自动建边
        result++;
        ret = GmcTransCreateSavepointAsync(conn1, "ddd1", CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);

        recordNum = 1000;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
        for (uint32_t i = 500; i < recordNum; i++) {
            result++;
            ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
            EXPECT_EQ(GMERR_OK, ret);
            AsyncWait(&received, result);
        }

        result++;
        ret = GmcTransRollBackSavepointAsync(conn1, "ddd1", CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);

        result++;
        ret = GmcTransRollBackSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);

        result++;
        ret = GmcTransReleaseSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    result++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    // 查heap视图，回滚完预期heap不占用页
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'T_DST\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);
    uint32_t heapPageNum = atoi(cmdOutput);
    EXPECT_EQ(heapPageNum, 0u);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T_DST\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNum = atoi(cmdOutput);
    EXPECT_EQ(indexEntryUsedNum, 0u);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TrxSavePointUndoLog_var(string srcJsonPath, string dstJsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(dstJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 预置根节点
    uint32_t srcRowInsertNum = 1000;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < srcRowInsertNum; i++) {
        ret = InsertLabelData(stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char cmdOutput[64] = {0};
    const char *filter = "LABEL_NAME=\'T_SRC\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);
    uint32_t heapPageNum = atoi(cmdOutput);
    EXPECT_GE(heapPageNum, 2u);  // 预期插入Src表超过1个页，方便构造跳转行

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_UPDATE));
    ret = GmcSetIndexKeyName(stmt, "T_SRC_PK");
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    // 这里为啥要设置这么多变长字段？
    // heap的fsm的0级链表是一个区间，在insert过程中大概率无法使得一个页正好被用满，用到一定程度就放到0级链表了
    // 因此在执行update的时候，会触发页面整理寻找连续空间，因此变长字段设置得少会导致buf过短，无法构造出跳转行
    const char *stringValue = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 原地更新不够，root节点构造成跳转行
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);

    uint32_t received = 0;
    uint32_t result = 1;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &received;

    // 开启事务
    uint32_t recordNum = 1;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCreateSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    // 插入表2触发自动建边（50条dst）
    recordNum = 50;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        result++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    // 再次创建savePoint，再插入50条记录dstVertex，触发自动建边
    result++;
    ret = GmcTransCreateSavepointAsync(conn1, "ddd1", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    recordNum = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 50; i < recordNum; i++) {
        result++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", cmdOutput, 64);
    uint32_t undoRecordNum = atoi(cmdOutput);
    // 跳转行，首边地址更新可以节省undoLog
    // 第1次savePoint产生的101条（50个dstVertex的insert、50条edge的insert、Src顶点首边地址更新1条）
    // 第2次savePoint（50条dstVertex的insert、50条edge的insert和邻居边的1条update(新的savePoint创建了，不能节省了)、
    // Src顶点首边地址更新1条）
    uint32_t expectRowNum = (101 + 50 + 50 + 1 + 1);
    EXPECT_EQ(undoRecordNum, expectRowNum);
    printf("undoRecordNum: %d\n", undoRecordNum);

    result++;
    ret = GmcTransRollBackSavepointAsync(conn1, "ddd1", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", cmdOutput, 64);
    undoRecordNum = atoi(cmdOutput);
    expectRowNum = 101;
    EXPECT_EQ(undoRecordNum, expectRowNum);
    printf("undoRecordNum: %d\n", undoRecordNum);

    result++;
    ret = GmcTransRollBackSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_DELETE));
    for (uint32_t i = 0; i < srcRowInsertNum; i++) {
        value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "T_SRC_PK"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    // 查heap视图，删除完预期heap不占用页（oldDstRow都被清理干净了）
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T_SRC\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);
    heapPageNum = atoi(cmdOutput);
    EXPECT_EQ(heapPageNum, 0u);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T_SRC\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNum = atoi(cmdOutput);
    EXPECT_EQ(indexEntryUsedNum, 0u);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

void TrxSavePointUndoLog_varAbnormal(string srcJsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" "
                            "\"maxSeMem=8\" ");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 预置根节点
    uint32_t srcRowInsertNum = 1000;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < srcRowInsertNum; i++) {
        ret = InsertLabelData(stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char cmdOutput[64] = {0};
    const char *filter = "LABEL_NAME=\'T_SRC\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);
    uint32_t heapPageNum = atoi(cmdOutput);
    EXPECT_GE(heapPageNum, 2u);  // 预期插入Src表超过1个页，方便构造跳转行

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_UPDATE));
    ret = GmcSetIndexKeyName(stmt, "T_SRC_PK");
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    // 这里为啥要设置这么多变长字段？
    // heap的fsm的0级链表是一个区间，在insert过程中大概率无法使得一个页正好被用满，用到一定程度就放到0级链表了
    // 因此在执行update的时候，会触发页面整理寻找连续空间，因此变长字段设置得少会导致buf过短，无法构造出跳转行
    const char *stringValue = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 原地更新不够，root节点构造成跳转行
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);

    AsyncUserDataT userData = {.received = 0, .expStatus = 0};
    uint32_t result = 1;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCbOOM;
    context.userData = &userData;

    // 开启事务
    uint32_t updateNum = 1;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &userData.received));
    AsyncWait(&userData.received, result);

    updateNum = 10000;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_SRC", GMC_OPERATION_UPDATE));
    for (uint32_t i = 0; i < updateNum; i++) {
        if (i > 0) {
            // 释放此循环上一次创建的savepoint，避免达到数量上限报错
            result++;
            ret = GmcTransReleaseSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &userData.received);
            EXPECT_EQ(GMERR_OK, ret);
            AsyncWait(&userData.received, result);
        }
        result++;
        ret = GmcSetIndexKeyName(stmt1, "T_SRC_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        // 这里为啥要设置这么多变长字段？
        // heap的fsm的0级链表是一个区间，在insert过程中大概率无法使得一个页正好被用满，用到一定程度就放到0级链表了
        // 因此在执行update的时候，会触发页面整理寻找连续空间，因此变长字段设置得少会导致buf过短，无法构造出跳转行
        ret = GmcSetVertexProperty(stmt1, "F4", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F5", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F6", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F7", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F8", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F9", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt1, &context);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&userData.received, result);
        if (userData.expStatus == GMERR_OUT_OF_MEMORY) {
            break;
        }

        result++;
        ret = GmcTransCreateSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &userData.received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&userData.received, result);
    }

    result++;
    ret = GmcTransRollBackAsync(conn1, CommonAsyncDCLCb, &userData.received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&userData.received, result);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#ifndef FEATURE_PERSISTENCE
// 持久化暂不支持边
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_013)
{
    string srcJsonPath = "./st_file/srcVertex.gmjson";
    string dstJsonPath = "./st_file/dstVertex.gmjson";
    TrxSavePointUndoLog(srcJsonPath, dstJsonPath);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_014)
{
    string srcJsonPath = "./st_file/srcVertex_bigObject.gmjson";
    string dstJsonPath = "./st_file/dstVertex_bigObject.gmjson";
    TrxSavePointUndoLog(srcJsonPath, dstJsonPath);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_015)
{
    string srcJsonPath = "./st_file/srcVertex_var.gmjson";
    string dstJsonPath = "./st_file/dstVertex.gmjson";
    // 需要单独构造跳转行，不能复用TrxSavePointUndoLog
    // Src的节点为变长，并且构造第一个insert的节点为跳转行，作为root节点，与dst表建表
    TrxSavePointUndoLog_var(srcJsonPath, dstJsonPath);
}
#endif

void Trx1UpdateAndTrx2Update_undoLog(string jsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    OptimisiticPrePare();
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 开启事务1修改索引值为1的数据为100
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务2修改索引值为1的数据为200
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_REPLACE));
    int64_t indexValue = 1;
    int64_t replaceValue = 100;
    ret = ReplaceVertexAll(stmt1, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t expectValue = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1(stmt1, indexValue, expectValue);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    indexValue = 1;
    expectValue = 1;
    FetchVertexF1(stmt2, indexValue, expectValue);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE));
    indexValue = 1;
    replaceValue = 200;
    ret = ReplaceVertexAll(stmt2, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 200;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务2执行过程中修改生效

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'TestOptimistic\'";
    GetViewFieldResultFilter("V\\STORAGE_UNDO_STAT", "UNDORECORD_NUM", filter, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'TestOptimistic\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1预期在版本链上合并
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_REPLACE));
    indexValue = 1;
    replaceValue = 300;
    ret = ReplaceVertexAll(stmt1, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    expectValue = 300;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    FetchVertexF1(stmt1, indexValue, expectValue);

    // 事务2预期在masterVersion上合并
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE));
    indexValue = 1;
    replaceValue = 400;
    ret = ReplaceVertexAll(stmt2, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    expectValue = 400;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务2执行过程中修改生效

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'TestOptimistic\'";
    GetViewFieldResultFilter("V\\STORAGE_UNDO_STAT", "UNDORECORD_NUM", filter, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    EXPECT_EQ(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'TestOptimistic\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    EXPECT_EQ(indexEntryUsedNumAfter, indexEntryUsedNumBefore);

    // 先后提交事务1和2
    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    indexValue = 1;
    expectValue = 400;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务具有隔离性，事务2查不到事务1的修改

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
    indexValue = 1;
    expectValue = 300;
    FetchVertexF1(stmt2, indexValue, expectValue);  // 事务1已经提交成功，修改生效变成300

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_017)
{
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    Trx1UpdateAndTrx2Update_undoLog(jsonPath);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_018)
{
    string jsonPath = "./st_file/optimisiticTrx_varRow.gmjson";
    Trx1UpdateAndTrx2Update_undoLog(jsonPath);
}

#ifndef FEATURE_PERSISTENCE
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_019)
{
    string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
    Trx1UpdateAndTrx2Update_undoLog(jsonPath);
}

void Trx1UpdateAndTrx2Update_abnormal(string jsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" "
                            "\"maxSeMem=16\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入初始数据
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t recordNum = 10;
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启事务修改索引值为1的数据
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t updateNum = 10000;  // 大对象重复更新10000次，如果不能合并，则会报错10001
    for (uint32_t i = 0; i < updateNum; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE));
        int64_t indexValue = 0;
        int64_t replaceValue = 100 + i;
        ret = ReplaceVertexAll(stmt, replaceValue, g_indexField, indexValue, true);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t expectValue = replaceValue;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
        FetchVertexF1(stmt, indexValue, expectValue);
    }

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();
    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_020)
{
    string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
    Trx1UpdateAndTrx2Update_abnormal(jsonPath);
}

#endif

// 改 maxSeMem = 8时，持久化场景发生 Device use up
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_021)
{
    // 持久化不支持savepoint
    PERSISTENCE_NOT_SUPPORT;
    string jsonPath = "./st_file/srcVertex_var.gmjson";
    TrxSavePointUndoLog_varAbnormal(jsonPath);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_022)
{
    string jsonPath = "./st_file/optimisiticTrx_fixRow.gmjson";
    Trx1UpdateAndTrx2Update_undoLog(jsonPath);
}

static void TrxConflictCheckAsyncDCLCb1(void *userData, Status status, const char *errMsg)
{
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, status);
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_023)
{
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_DELETE));

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_DELETE));

    uint32_t received = 0;
    uint32_t result = 1;

    GmcAsyncRequestDoneContextT context;
    context.deleteCb = CommonAsyncDMLCb;
    context.userData = &received;

    GmcTxConfigT config;
    DefaultConf(&config);

    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received));  // 开启事务1
    AsyncWait(&received, result);

    int64_t indexValue = 0;

    result++;
    ret = DeleteLabelDataAsync(stmt1, &context, indexValue);  // 事务1删除ID为0的行
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;  // 事务2未开启事务，处于非活跃状态预期返回OK
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn2, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn2, &config, CommonAsyncDCLCb, &received));  // 开启事务2
    AsyncWait(&received, result);

    indexValue = 1;
    result++;
    ret = DeleteLabelDataAsync(stmt2, &context, indexValue);  // 事务2删除ID为1的行
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;  // 事务1提交前调用乐观事务冲突检测接口，预期没有冲突，返回ok
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn1, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received);  // 事务1提交成功
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;  // 事务2提交前调用乐观事务冲突检测接口，预期有冲突，返回GMERR_RESTRICT_VIOLATION
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn2, TrxConflictCheckAsyncDCLCb1, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;  // 事务2提交前调用乐观事务冲突检测接口，预期有冲突，返回GMERR_RESTRICT_VIOLATION
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn2, TrxConflictCheckAsyncDCLCb1, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;  // 事务2提交失败;预期返回 GMERR_RESTRICT_VIOLATION
    ret = GmcTransCommitAsync(conn2, TrxConflictCheckAsyncDCLCb1, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_024)
{
    StartDbServerWithConfig("\"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=0\" \"defaultIsolationLevel=1\" \"trxMonitorEnable=0\"");
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 2;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_DELETE));

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_DELETE));

    uint32_t received = 0;
    uint32_t result = 1;

    GmcAsyncRequestDoneContextT context;
    context.deleteCb = CommonAsyncDMLCb;
    context.userData = &received;

    GmcTxConfigT config;
    DefaultConf(&config);
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_PESSIMISITIC_TRX;  // 悲观事务

    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    result++;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn2, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    int64_t indexValue = 0;
    result++;
    ret = DeleteLabelDataAsync(stmt1, &context, indexValue);  // 事务1删除ID为0的行
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received);  // 事务1提交成功
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    indexValue = 1;
    result++;
    ret = DeleteLabelDataAsync(stmt2, &context, indexValue);  // 事务2删除ID为1的行
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;  // 悲观事务2提交前调用乐观事务冲突检测接口，预期没有冲突，返回OK；
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn2, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCommitAsync(conn2, CommonAsyncDCLCb, &received);  // 事务2提交成功；
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#ifndef FEATURE_PERSISTENCE
// 持久化暂不支持边
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_025)
{
    string srcJsonPath = "./st_file/srcVertex.gmjson";
    string dstJsonPath = "./st_file/dstVertex.gmjson";
    TrxRollBackToSavePoint(srcJsonPath, dstJsonPath);
}
#endif

uint32_t GetErrorNumInLog()
{
#if (defined ASAN || defined UBSAN || defined TSAN)
    // ASAN不检查Err日志数量，有折叠日志影响统计
    return 0;
#else
    // 查看日志中的error数量
    FILE *fp = popen("cat ./log/run/rgmserver/* | grep ERROR | wc -l", "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    return atoi(errorNum);
#endif
}

void AllRollBackToSavePoint(bool isRollBack)
{
    string srcJsonPath = "./st_file/srcVertex.gmjson";
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 预置根节点
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    ret = InsertLabelData(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn3和stmt3
    GmcConnT *conn3;
    GmcStmtT *stmt3;
    CreateAsyncConnectionAndStmt(&conn3, &stmt3);

    uint32_t received3 = 0;
    uint32_t result3 = 1;

    // 开启事务3
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn3, &config, CommonAsyncDCLCb, &received3));
    AsyncWait(&received3, result3);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);

    uint32_t received2 = 0;
    uint32_t result2 = 1;
    GmcAsyncRequestDoneContextT context2;
    context2.insertCb = CommonAsyncDMLCb;
    context2.userData = &received2;

    // 开启事务2
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn2, &config, CommonAsyncDCLCb, &received2));
    AsyncWait(&received2, result2);

    result2++;
    ret = GmcTransCreateSavepointAsync(conn2, "ddd", CommonAsyncDCLCb, &received2);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received2, result2);

    result2++;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, "T_SRC", GMC_OPERATION_UPDATE));
    ret = GmcSetIndexKeyName(stmt2, "T_SRC_PK");
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;
    ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecuteAsync(stmt2, &context2);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received2, result2);

    result2++;
    ret = GmcTransRollBackSavepointAsync(conn2, "ddd", CommonAsyncDCLCb, &received2);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received2, result2);

    result2++;
    ret = GmcTransCommitAsync(conn2, CommonAsyncDCLCb, &received2);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received2, result2);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);

    uint32_t received1 = 0;
    uint32_t result1 = 1;
    GmcAsyncRequestDoneContextT context1;
    context1.insertCb = CommonAsyncDMLCb;
    context1.userData = &received1;

    // 开启事务1
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received1));
    AsyncWait(&received1, result1);

    result1++;
    ret = GmcTransCreateSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received1, result1);

    result1++;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_SRC", GMC_OPERATION_UPDATE));
    ret = GmcSetIndexKeyName(stmt1, "T_SRC_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecuteAsync(stmt1, &context1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received1, result1);

    result1++;
    ret = GmcTransRollBackSavepointAsync(conn1, "ddd", CommonAsyncDCLCb, &received1);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received1, result1);

    uint32_t errNumBeforeTest = GetErrorNumInLog();
    result1++;
    if (isRollBack) {
        ret = GmcTransRollBackAsync(conn1, CommonAsyncDCLCb, &received1);
    } else {
        ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received1);
    }
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received1, result1);
    uint32_t errNumAfterTest = GetErrorNumInLog();
    EXPECT_EQ(errNumAfterTest, errNumBeforeTest);

    result3++;
    ret = GmcTransCommitAsync(conn3, CommonAsyncDCLCb, &received3);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received3, result3);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn3, stmt3);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_026)
{
    // 持久化不支持savepoint
    PERSISTENCE_NOT_SUPPORT;
    AllRollBackToSavePoint(true);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_027)
{
    // 持久化不支持savepoint
    PERSISTENCE_NOT_SUPPORT;
    AllRollBackToSavePoint(false);
}

#ifndef FEATURE_PERSISTENCE
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_095)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" ");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);

    // 建表
    string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t recordNum = 100;
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = InsertLabelData(stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    uint32_t testNum = 30;
    for (uint32_t j = 0; j < testNum; j++) {
        // 开启事务修改数据
        GmcTxConfigT config;
        DefaultConf(&config);
        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t i = 0; i < recordNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE));
            int64_t indexValue = i;
            int64_t replaceValue = 100 + i;
            ret = ReplaceVertexAll(stmt, replaceValue, g_indexField, indexValue, true);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t expectValue = replaceValue;
            EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
            FetchVertexF1(stmt, indexValue, expectValue);
        }

        ret = GmcTransCommit(conn);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcTruncateVertexLabel(stmt, g_labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    OptimisiticTrxCheckGcHandleHistoryUndoLog();
    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_095_drop)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" ");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);

    uint32_t testNum = 30;
    for (uint32_t j = 0; j < testNum; j++) {
        // 建表
        string jsonPath = "./st_file/optimisiticTrx_largeObject.gmjson";
        string test_schema = GetFileContext(jsonPath);
        ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t recordNum = 100;
        for (uint32_t i = 0; i < recordNum; i++) {
            ret = InsertLabelData(stmt, i);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // 开启事务修改数据
        GmcTxConfigT config;
        DefaultConf(&config);
        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t i = 0; i < recordNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE));
            int64_t indexValue = i;
            int64_t replaceValue = 100 + i;
            ret = ReplaceVertexAll(stmt, replaceValue, g_indexField, indexValue, true);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t expectValue = replaceValue;
            EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
            FetchVertexF1(stmt, indexValue, expectValue);
        }

        // 事务提交
        ret = GmcTransCommit(conn);
        EXPECT_EQ(GMERR_OK, ret);

        // 构造purger与drop并发
        ret = GmcDropVertexLabel(stmt, g_labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    OptimisiticTrxCheckGcHandleHistoryUndoLog();
    CLientFinal(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#endif

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_096)
{
    const char *namespaces[] = {"test_namespace1", "test_namespace2"};
    const char *tables[] = {g_labelName, g_labelName2};
    const char *schemaFiles[] = {"./st_file/optimisiticTrx.gmjson", "./st_file/optimisiticTrx2.gmjson"};
    const int32_t tableCount = 2;
    const int32_t stmtCount = 2;
    GmcConnT *connArr[tableCount] = {0};
    GmcStmtT *stmtArr[stmtCount] = {0};
    int32_t ret;
    bool isfirst = true;
    uint32_t insertNum = 2;
    for (uint32_t i = 0; i < tableCount; ++i) {
        isfirst = i == 0 ? true : false;
        ret = OptimisiticPrePareForTableWithNamespace(
            &connArr[i], &stmtArr[i], tables[i], schemaFiles[i], namespaces[i], insertNum, isfirst);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 事务执行replace
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    EXPECT_EQ(GMERR_OK, ret);
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    ret = GmcUseNamespace(stmt1, namespaces[0]);  // 建立连接后需要指定命名空间
    EXPECT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, tables[0], GMC_OPERATION_REPLACE));
    int64_t replaceValue = 100;
    ret = ReplaceVertexAll(stmt1, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt1, namespaces[1]);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, tables[1], GMC_OPERATION_UPDATE));
    int64_t mergeValue = 100;
    ret = MergeVertexAll(stmt1, mergeValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);

    for (uint32_t i = 0; i < tableCount; ++i) {
        if (i == tableCount - 1) {
            OptimisiticClearForTablesWithNamespace(connArr[i], stmtArr[i], tables[i], namespaces[i], true);
        } else {
            OptimisiticClearForTablesWithNamespace(connArr[i], stmtArr[i], tables[i], namespaces[i], false);
        }
    }
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_097)
{
    const char *namespaces[] = {"test_namespace1", "test_namespace2"};
    const char *tables[] = {g_labelName, g_labelName2};
    const char *schemaFiles[] = {"./st_file/optimisiticTrx.gmjson", "./st_file/optimisiticTrx2.gmjson"};
    const int32_t tableCount = 1;
    const int32_t stmtCount = 2;
    GmcConnT *connArr[tableCount] = {0};
    GmcStmtT *stmtArr[stmtCount] = {0};
    int32_t ret;
    bool isfirst = true;
    uint32_t insertNum = 2;
    for (uint32_t i = 0; i < tableCount; ++i) {
        isfirst = i == 0 ? true : false;
        ret = OptimisiticPrePareForTableWithNamespace(
            &connArr[i], &stmtArr[i], tables[i], schemaFiles[i], namespaces[i], insertNum, isfirst);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 事务1执行merge操作
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    EXPECT_EQ(GMERR_OK, ret);
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    ret = GmcUseNamespace(stmt1, namespaces[0]);  // 建立连接后需要指定命名空间
    EXPECT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, tables[0], GMC_OPERATION_MERGE));
    int64_t indexValue = 0;
    int64_t mergeValue = 100;
    ret = MergeVertexAll(stmt1, mergeValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2执行replace操作
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    EXPECT_EQ(GMERR_OK, ret);
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    ret = GmcUseNamespace(stmt2, namespaces[0]);
    EXPECT_EQ(GMERR_OK, ret);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, tables[0], GMC_OPERATION_REPLACE));
    int64_t replaceValue = 100;
    ret = ReplaceVertexAll(stmt2, replaceValue, g_indexField, indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    for (uint32_t i = 0; i < tableCount; ++i) {
        if (i == tableCount - 1) {
            OptimisiticClearForTablesWithNamespace(connArr[i], stmtArr[i], tables[i], namespaces[i], true);
        } else {
            OptimisiticClearForTablesWithNamespace(connArr[i], stmtArr[i], tables[i], namespaces[i], false);
        }
    }
}

// 不断生成undo log直到undo页使用满报错，并返回undoLog数量
int32_t StMakeUndoLogUtilUndoSpaceFull()
{
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 开启一个事务，阻止purger回收，直到写入失败
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    int32_t ret = StOptimisiticTransStart(conn2, false);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;

    int logNum = 0;
    do {
        ret = StOptimisiticTransStart(conn1, false);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = ReplaceVertexAll(stmt1, value, "F0", value);
        if (ret != GMERR_OK) {
            ret = GmcTransRollBack(conn1);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ret = GmcTransCommit(conn1);
        EXPECT_EQ(GMERR_OK, ret);
        logNum++;
    } while (true);
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);
    return logNum;
}

static void OptimisiticPrePareSmallUndo(bool isSetPageSize = false)
{
    if (isSetPageSize) {
        StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                                "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"maxUndoSpaceSize=1\" "
                                "\"trxMonitorEnable=0\" \"pageSize=4\"");
    } else {
        StartDbServerWithConfig(
            " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"maxUndoSpaceSize=1\" \"trxMonitorEnable=0\"");
    }
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext("./st_file/optimisiticTrx.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
}

// asan下运行速度很慢，且跟测试环境强相关，不适合测试这个处理请求较多的场景，不好估计等待时间，故屏蔽
#if (defined ASAN || defined UBSAN || defined TSAN || defined COVERAGE)
#else

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_098)
{
    if (IsCoverageCompile()) {
        std::cout << "coverage compile" << std::endl;
        return;
    }
    OptimisiticPrePareSmallUndo();
    const int waitPurgerTime = 150;  // 持久化场景下时间较长

    int32_t logNum = StMakeUndoLogUtilUndoSpaceFull();

    StCheckUndoPurgerField("PURGE_UNDO_LOG_SUM", logNum - 1, waitPurgerTime);

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    int32_t ret = StOptimisiticTransStart(conn, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_098_pageSize)
{
    // 持久化不支持pageSize=4
    PERSISTENCE_NOT_SUPPORT;
    if (IsCoverageCompile()) {
        std::cout << "coverage compile" << std::endl;
        return;
    }
    // 测试pageSize=4
    OptimisiticPrePareSmallUndo(true);
    const int waitPurgerTime = 5;

    int32_t logNum = StMakeUndoLogUtilUndoSpaceFull();

    StCheckUndoPurgerField("PURGE_UNDO_LOG_SUM", logNum - 1, waitPurgerTime);

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    int32_t ret = StOptimisiticTransStart(conn, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

#ifndef FEATURE_PERSISTENCE
// 持久化场景很慢 ?
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_099)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 100000;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    OptimisiticPrePare();
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    char cmdOutput[64] = {0};
    const char *filter = "LABEL_NAME=\'TestOptimistic\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t pageCountBeforeOp = atoi(cmdOutput);
    printf("pageCountBeforeOp: %d\n", pageCountBeforeOp);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);

    // 开启事务2
    StOptimisiticTransStart(conn1, true);

    // 开启事务1删除所有数据
    StOptimisiticTransStart(conn2, false);
    for (uint32_t i = 0; i < insertNum; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_DELETE));
        ret = DeleteLabelData(stmt2, i);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_SCAN));
        FetchVertexF1Err(stmt2, i, GMERR_NO_DATA);
    }

    // 先后提交事务2和1
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t pageCountAfterDel = atoi(cmdOutput);
    EXPECT_EQ(pageCountBeforeOp, pageCountAfterDel);
    printf("pageCountAfterDel: %d\n", pageCountAfterDel);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = 0; i < insertNum; i++) {
        FetchVertexF1(stmt1, i, i);
    }

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = 0; i < insertNum; i++) {
        FetchVertexF1Err(stmt1, i, GMERR_NO_DATA);
    }

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t pageCountAfterTrx1Commit = atoi(cmdOutput);
    EXPECT_EQ(pageCountAfterTrx1Commit, (uint32_t)0);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNum = atoi(cmdOutput);
    EXPECT_EQ(indexEntryUsedNum, 0u);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

void TrxSavePointTest(string srcJsonPath, string dstJsonPath)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    DbSleep(1000);
    CLientInit(&conn, &stmt);

    // 建表
    string test_schema = GetFileContext(srcJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(dstJsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    const char *edgeLabelJsonFormat =
        R"([{
            "name":"edgelabel",
            "source_vertex_label":"T_SRC",
            "comment":"the edge 7 to 8",
            "dest_vertex_label":"T_DST",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        },
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        }
                    ]
            }
        }])";

    char labelJson[1024] = {0};
    snprintf_s(labelJson, sizeof(labelJson), sizeof(labelJson) - 1, edgeLabelJsonFormat);
    ret = GmcCreateEdgeLabel(stmt, labelJson, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 预置根节点
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT));
    ret = InsertLabelData(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateAsyncConnectionAndStmt(&conn1, &stmt1);

    uint32_t received = 0;
    uint32_t result = 1;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = CommonAsyncDMLCb;
    context.userData = &received;

    // 开启事务
    uint32_t recordNum = 1;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn1, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    result++;
    ret = GmcTransCreateSavepointAsync(conn1, "sp1", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    recordNum = 50;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < recordNum; i++) {
        result++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    result++;
    ret = GmcTransCreateSavepointAsync(conn1, "sp2", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    recordNum = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, "T_DST", GMC_OPERATION_INSERT));
    for (uint32_t i = 50; i < recordNum; i++) {
        result++;
        ret = InsertVertexWithPrimaryValueAsync(stmt1, &context, 0, i);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "SAVE_POINT_NUM: 2", "SAVE_POINT_NAME: sp2",
        "SAVE_POINT_NAME: sp1");
    EXPECT_EQ(GMERR_OK, ret);

    // 创建1022个匿名savepoint
    for (uint32_t i = 0; i < 1022; i++) {
        result++;
        ret = GmcTransCreateSavepointAsync(conn1, NULL, CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "SAVE_POINT_NUM: 1024", "SAVE_POINT_NAME: sp2",
        "SAVE_POINT_NAME: sp1");
    EXPECT_EQ(GMERR_OK, ret);

    result++;
    ret = GmcTransRollBackSavepointAsync(conn1, "sp2", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "SAVE_POINT_NUM: 2", "SAVE_POINT_NAME: sp2",
        "SAVE_POINT_NAME: sp1");
    EXPECT_EQ(GMERR_OK, ret);

    result++;
    ret = GmcTransRollBackSavepointAsync(conn1, "sp1", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "SAVE_POINT_NUM: 1", "SAVE_POINT_NAME: sp1");
    EXPECT_EQ(GMERR_OK, ret);

    result++;
    ret = GmcTransCommitAsync(conn1, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#ifndef FEATURE_PERSISTENCE
// 持久化场景暂不支持边
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_100)
{
    string srcJsonPath = "./st_file/srcVertex.gmjson";
    string dstJsonPath = "./st_file/dstVertex.gmjson";
    TrxSavePointTest(srcJsonPath, dstJsonPath);
}
#endif

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_101)
{
    // 持久化不支持savepoint
    PERSISTENCE_NOT_SUPPORT;
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);
    DbSleep(1000);

    // 创建conn和stmt
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateAsyncConnectionAndStmt(&conn, &stmt);

    uint32_t received = 0;
    uint32_t result = 1;

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn, &config, CommonAsyncDCLCb, &received));
    AsyncWait(&received, result);

    int32_t savePointNameLen = 16;
    char savePointName[savePointNameLen];
    // 目前savepoint数量约束最多为1024
    for (uint32_t idx = 0; idx < 1024; idx++) {
        result++;
        int32_t cmdLen = sprintf_s(savePointName, savePointNameLen, "sp_%u", idx);
        EXPECT_GT(cmdLen, 0);
        ret = GmcTransCreateSavepointAsync(conn, savePointName, CommonAsyncDCLCb, &received);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncWait(&received, result);
    }

    ret = executeCommand(
        (char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "SAVE_POINT_NUM: 1024", "SAVE_POINT_NAME: sp_1023");
    EXPECT_EQ(GMERR_OK, ret);

    result++;
    // 回滚到sp_1000，此时有1001个sp
    ret = GmcTransRollBackSavepointAsync(conn, "sp_1000", CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncWait(&received, result);

    ret = executeCommand(
        (char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "SAVE_POINT_NUM: 1001", "SAVE_POINT_NAME: sp_1000");
    EXPECT_EQ(GMERR_OK, ret);

    // STORAGE_TRX_DETAIL视图要包含RETAINED_UNDO_RECORD_COUNT和NORMAL_UNDO_RECORD_COUNT字段
    ret = executeCommand(
        (char *)"gmsysview -q V\\$STORAGE_TRX_DETAIL", "RETAINED_UNDO_RECORD_COUNT", "NORMAL_UNDO_RECORD_COUNT");
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#define ST_ASYNC_REQUEST_RECV_TIME_MAX_MS (uint32_t)(5 * MSECONDS_IN_SECOND)
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_102)
{
    OptimisiticPrePare();
    // 客户端初始化
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    // 建连
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt, ST_ASYNC_REQUEST_RECV_TIME_MAX_MS);
    // 视图数据
    const char *mvPageNum0[] = {"0"};
    const char *mvPageNum1[] = {"1"};
    const char *mvPageNum2[] = {"2"};
    char *cmd = (char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T35_K0 | grep -E "
                        "'MULTIVERSION_PAGE_NUM' | awk -F ':' '{print $2}'";

    // 建表插数据
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // MULTIVERSION_PAGE_NUM 预期为0
    ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务
    StOptimisiticTransStart(conn, false);
    // 插入数据
    uint32_t insertNum = 10000;
    // 重复插删 insertNum 条相同数据
    for (uint32_t i = 0; i < insertNum; i++) {
        // 插入
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = InsertLabelData(stmt, 0, false);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 1) {
            // MULTIVERSION_PAGE_NUM 预期为1
            ret = StExecuteCommandWithMatch(cmd, mvPageNum1, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }

        // 删除
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = DeleteLabelData(stmt, 0, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // MULTIVERSION_PAGE_NUM 预期为2
    ret = StExecuteCommandWithMatch(cmd, mvPageNum2, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交事务
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表
    GmcDropVertexLabel(stmt, g_labelName);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_103)
{
    StartDbServerWithConfig(NULL);
    // 客户端初始化
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    // 视图数据
    const char *mvPageNum0[] = {"0"};
    char *cmd = (char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T_SRC_PK | grep -E "
                        "'MULTIVERSION_PAGE_NUM' | awk -F ':' '{print $2}'";
    // 建连
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt, ST_ASYNC_REQUEST_RECV_TIME_MAX_MS);
    // 建表
    string jsonPath = "./st_file/srcVertex.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // MULTIVERSION_PAGE_NUM 预期为0
    ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t insertNum = 2;
    // 重复插删 insertNum 条相同数据
    uint64_t value = 2;
    for (uint32_t i = 0; i < insertNum; i++) {
        // 插入
        ret = GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = InsertLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);

        // 删除
        ret = GmcPrepareStmtByLabelName(stmt, "T_SRC", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "T_SRC_PK"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // MULTIVERSION_PAGE_NUM 预期为0
    ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 删表
    GmcDropVertexLabel(stmt, "T_SRC");
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

static void CheckVertexLabelDataNum(const char *vertexLabelName, uint32_t checkNum)
{
    char buf[10000] = {0};
    char cmd[100] = {0};
    char expectStr[100] = {0};
    const int strLen = 100;
    sprintf_s(cmd, strLen, "gmsysview count %s", vertexLabelName);
    sprintf_s(expectStr, strLen, "|   %" PRIu32, checkNum);
    FILE *file = popen(cmd, "r");
    fread(buf, sizeof(buf) - 1, 1, file);
    pclose(file);
    char *ret = strstr(buf, expectStr);
    if (ret == NULL) {
        printf("Actual:\n%s\n", buf);
        printf("Expected num:\n%" PRIu32 "\n", checkNum);
        EXPECT_EQ(false, true);
    }
    EXPECT_EQ(true, true);
}

#define ST_SLEEP_TIME_MAX (uint32_t)(10 * MSECONDS_IN_SECOND)
static uint32_t GetSleepMsBeforQueryView(void)
{
#ifdef ASAN
    return 5000;
#else
    return 500;
#endif
}

#if (defined ASAN || defined UBSAN || defined TSAN || defined COVERAGE)
// asan下下架 OptimisiticTrx_104
#else
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_104)
{
    OptimisiticPrePare();
    // 客户端初始化
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    // 建连
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt, ST_ASYNC_REQUEST_RECV_TIME_MAX_MS * 2);
    // 视图数据
    const char *mvPageNum0[] = {"0"};
    const char *mvPageNum2[] = {"2"};
    char *cmd = (char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T35_K0 | grep -E "
                        "'MULTIVERSION_PAGE_NUM' | awk -F ':' '{print $2}'";

    // 建表
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // step1:预置一条数据， MULTIVERSION_PAGE_NUM 前后均为0
    uint64_t value = 0;
    StOptimisiticTransStart(conn, false);
    // 插入
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertLabelData(stmt, value, false);
    EXPECT_EQ(GMERR_OK, ret);
    // MULTIVERSION_PAGE_NUM 预期为0
    ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交事务
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    // MULTIVERSION_PAGE_NUM 预期为0
    ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // step2:开启事务，重复插删 insertNum 条相同数据后，MULTIVERSION_PAGE_NUM 预期为2，事务回滚后为0
    // 测试数据：6100次插删需要1个mv page，6200次插删需要2个mv page；实测过10w条数据插删，用例时间太久，故调整为1w
    StOptimisiticTransStart(conn, false);
    uint32_t insertNum = 6500;
    value = 1;
    for (uint32_t i = 0; i < insertNum; i++) {
        // 插入
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = InsertLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = DeleteLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = StExecuteCommandWithMatch(cmd, mvPageNum2, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 回滚
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t alreadySleepTime = 0;
    uint32_t timeMs = GetSleepMsBeforQueryView();
    do {
        DbSleep(timeMs);
        alreadySleepTime += timeMs;
        ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
        if (alreadySleepTime >= ST_SLEEP_TIME_MAX) {
            break;
        }
    } while (ret != GMERR_OK);
    EXPECT_EQ(GMERR_OK, ret);
    // 回滚完成后预期只有之前预置的1条数据
    CheckVertexLabelDataNum(g_labelName, 1);

    // 删表
    GmcDropVertexLabel(stmt, g_labelName);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_105)
{
    uint32_t timeMs = GetSleepMsBeforQueryView();
    uint32_t alreadySleepTime = 0;
    OptimisiticPrePare();
    // 客户端初始化
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    // 建连
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt, ST_ASYNC_REQUEST_RECV_TIME_MAX_MS);
    // 视图数据
    const char *mvPageNum0[] = {"0"};
    const char *mvPageNum2[] = {"2"};
    char *cmd = (char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T35_K0 | grep -E "
                        "'MULTIVERSION_PAGE_NUM' | awk -F ':' '{print $2}'";

    // 建表
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // step1:预置一条数据
    uint64_t value = 0;
    StOptimisiticTransStart(conn, false);
    // 插入
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertLabelData(stmt, value, false);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交事务
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // step2:开启事务，重复插删 insertNum 条相同数据后，MULTIVERSION_PAGE_NUM 预期为2，事务提交后为0
    // 测试数据：6100次插删需要1个mv page，6200次插删需要2个mv page；实测过10w条数据插删，用例时间太久，故调整为1w
    StOptimisiticTransStart(conn, false);
    uint32_t insertNum = 10000;
    value = 1;
    for (uint32_t i = 0; i < insertNum; i++) {
        // 插入
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = InsertLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = DeleteLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = StExecuteCommandWithMatch(cmd, mvPageNum2, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交，由于事务提交前最后一次dml是删除，故 insertNum 次dml实际对表数据没有产生影响
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    do {
        DbSleep(timeMs);
        alreadySleepTime += timeMs;
        ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
        if (alreadySleepTime >= ST_SLEEP_TIME_MAX) {
            break;
        }
    } while (ret != GMERR_OK);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交完成后预期只有之前预置的1条数据
    CheckVertexLabelDataNum(g_labelName, 1);

    // step3:开启事务，重复插删 insertNum 条相同数据后，MULTIVERSION_PAGE_NUM 预期为2，事务提交后为0
    StOptimisiticTransStart(conn, false);
    for (uint32_t i = 0; i < insertNum; i++) {
        // 删除
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = DeleteLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = InsertLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = StExecuteCommandWithMatch(cmd, mvPageNum2, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交，由于事务提交前最后一次dml是插入，故 insertNum 次dml实际对表数据会新增一条数据
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    alreadySleepTime = 0;
    do {
        DbSleep(timeMs);
        alreadySleepTime += timeMs;
        ret = StExecuteCommandWithMatch(cmd, mvPageNum0, 1);
        if (alreadySleepTime >= ST_SLEEP_TIME_MAX) {
            break;
        }
    } while (ret != GMERR_OK);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交完成后预期只有之前预置的2条数据
    CheckVertexLabelDataNum(g_labelName, 2);

    // 删表
    GmcDropVertexLabel(stmt, g_labelName);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_106)
{
    OptimisiticPrePare();
    // 客户端初始化
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    // 建连
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt, ST_ASYNC_REQUEST_RECV_TIME_MAX_MS);

    // 建表
    string jsonPath = "./st_file/optimisiticTrx_local_index.gmjson";
    string test_schema = GetFileContext(jsonPath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // step1:预置一条数据
    uint64_t value = 0;
    StOptimisiticTransStart(conn, false);
    // 插入
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertLabelData(stmt, value, false);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交事务
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    StOptimisiticTransStart(conn, false);
    uint32_t insertNum = 2;
    value = 1;
    for (uint32_t i = 0; i < insertNum; i++) {
        // 插入
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = InsertLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = DeleteLabelData(stmt, value, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 删表
    GmcDropVertexLabel(stmt, g_labelName);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_110)
{
    // 增量持久化不支持建边表
    PERSISTENCE_NOT_SUPPORT;
    // edgeLabel
    const char *cfgJson = R"({"max_record_count":1000})";
    const char *labelJson =
        R"([{
            "name":"autoEdgelable",
            "source_vertex_label":"edgeAutoSrcVertex",
            "comment":"test",
            "dest_vertex_label":"edgeAutoDstVertex",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";
    // srcVertexLabel
    const char *edgeSrcVertexLabelName = "edgeAutoSrcVertex";
    const char *srccfgJson1 = R"({"max_record_count":1000})";
    const char *edgeSrcVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoSrcVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoSrcVertex",
                        "name":"edgeAutoSrcVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    // destVertexLabel
    const char *edgeDstVertexLabelName = "edgeAutoDstVertex";
    const char *dstcfgJson1 = R"({"max_record_count":1000})";
    const char *edgeDstVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDstVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDstVertex",
                        "name":"edgeAutoDstVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 拉起gmserver
    int32_t ret;
    OptimisiticPrePare();
    ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(stmt, edgeSrcVertexLabel, srccfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(stmt, edgeDstVertexLabel, dstcfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(stmt, labelJson, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // set srcVertex for first row data
    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int valueF0 = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    int valueF1 = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    int valueF2 = 123;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    int valueF3 = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &valueF3, sizeof(valueF3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 验证插入数据
    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoSrcVertex_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    int value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    int valueTmp;
    bool isNull;
    while (true) {
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F1", &valueTmp, sizeof(valueTmp), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, valueTmp);
    }

    // set dstVertex for fisrt row data
    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 11;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 12;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 123;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    valueF3 = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &valueF3, sizeof(valueF3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 验证插入数据
    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDstVertex_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    value = 11;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    while (true) {
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F1", &valueTmp, sizeof(valueTmp), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(12, valueTmp);
    }

    // 开启事务1在dstVertex插入一条数据，再更新该数据
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务2在dstVertex插入一条数据
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务1插入顶点数据后，满足条件自动建边
    ret = GmcPrepareStmtByLabelName(stmt1, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 13;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 14;
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 123;
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    valueF3 = 321;
    ret = GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &valueF3, sizeof(valueF3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2插入顶点数据后满足条件后自动建边
    ret = GmcPrepareStmtByLabelName(stmt2, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 15;
    ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 16;
    ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 123;
    ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    valueF3 = 321;
    ret = GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_INT32, &valueF3, sizeof(valueF3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务1更新插入的顶点数据，自动删除边，事务1的该变动会在边表的版本链中合并更新
    ret = GmcPrepareStmtByLabelName(stmt1, edgeDstVertexLabelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    valueF3 = 322;
    ret = GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &valueF3, sizeof(valueF3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt1, (const char *)"edgeAutoDstVertex.F0=13");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 先后提交事务1和2
    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2应当冲突回退
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务1已经提交成功，此处应当修改生效
    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDstVertex_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    value = 13;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    while (true) {
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F3", &valueTmp, sizeof(valueTmp), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(322, valueTmp);
    }

    // 释放两个事务的连接
    OptimisiticTrxCheckGcHandleHistoryUndoLog();
    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    // 清空客户端资源
    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_111)
{
    // 增量持久化不支持建边表
    PERSISTENCE_NOT_SUPPORT;
    char vl1Name[] = "vertexlabel_1";
    char vl2Name[] = "vertexlabel_2";
    char vl3Name[] = "vertexlabel_3";
    char kvlName[] = "kvlabel";
    string vl1Path = "./st_file/optimisiticTrx_dfx_table1.gmjson";
    string vl2Path = "./st_file/optimisiticTrx_dfx_table2.gmjson";
    string vl3Path = "./st_file/optimisiticTrx_dfx_table3.gmjson";
    string elPath = "./st_file/optimisiticTrx_dfx_table4.gmjson";

    // 拉起gmserver
    int32_t ret;
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableLogFold=0\"");
    ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    string test_schema = GetFileContext(vl1Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(vl2Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(vl3Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(elPath);
    ret = GmcCreateEdgeLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvCreateTable(stmt, kvlName, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < 3; ++i) {
        // 开启事务1
        GmcConnT *conn1;
        GmcStmtT *stmt1;
        CreateSyncConnectionAndStmt(&conn1, &stmt1);
        GmcTxConfigT config;
        DefaultConf(&config);
        ret = GmcTransStart(conn1, &config);
        EXPECT_EQ(GMERR_OK, ret);

        // 开启事务2
        GmcConnT *conn2;
        GmcStmtT *stmt2;
        CreateSyncConnectionAndStmt(&conn2, &stmt2);
        DefaultConf(&config);
        ret = GmcTransStart(conn2, &config);
        EXPECT_EQ(GMERR_OK, ret);

        // 开启事务3
        GmcConnT *conn3;
        GmcStmtT *stmt3;
        CreateSyncConnectionAndStmt(&conn3, &stmt3);
        DefaultConf(&config);
        ret = GmcTransStart(conn3, &config);
        EXPECT_EQ(GMERR_OK, ret);

        // 事务1插入vl1和vl2和kvl, 同时会建边，会操作到el
        ret = GmcPrepareStmtByLabelName(stmt1, vl1Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t valueF0 = 10 + i;
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t valueF1 = 11 + i;
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t valueF2 = 12 + i;
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(stmt1, vl2Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        valueF0 = 10 + i;
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
        EXPECT_EQ(GMERR_OK, ret);
        valueF1 = 11 + i;
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
        EXPECT_EQ(GMERR_OK, ret);
        valueF2 = 12 + i;
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcKvPrepareStmtByLabelName(stmt1, kvlName);
        EXPECT_EQ(GMERR_OK, ret);
        char key[3] = "F0";
        valueF0 = 10 + i;
        ret = GmcKvSet(stmt1, key, sizeof(key), &valueF0, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 事务2插入vl2 和 vl3
        ret = GmcPrepareStmtByLabelName(stmt2, vl2Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        valueF0 = 20 + i;
        ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
        EXPECT_EQ(GMERR_OK, ret);
        valueF1 = 21 + i;
        ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
        EXPECT_EQ(GMERR_OK, ret);
        valueF2 = 22 + i;
        ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(stmt2, vl3Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        valueF0 = 20 + i;
        ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
        EXPECT_EQ(GMERR_OK, ret);
        valueF1 = 21 + i;
        ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
        EXPECT_EQ(GMERR_OK, ret);
        valueF2 = 22 + i;
        ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);

        // 事务3 插入vl3 和 kvl
        ret = GmcPrepareStmtByLabelName(stmt3, vl3Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        valueF0 = 30 + i;
        ret = GmcSetVertexProperty(stmt3, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
        EXPECT_EQ(GMERR_OK, ret);
        valueF1 = 31 + i;
        ret = GmcSetVertexProperty(stmt3, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
        EXPECT_EQ(GMERR_OK, ret);
        valueF2 = 32 + i;
        ret = GmcSetVertexProperty(stmt3, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcKvPrepareStmtByLabelName(stmt3, kvlName);
        EXPECT_EQ(GMERR_OK, ret);
        char key2[3] = "F0";
        valueF0 = 30 + i;
        ret = GmcKvSet(stmt3, key2, sizeof(key2), &valueF0, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        FILE *fp = popen("cat ./log/run/rgmserver/* | grep -rn 'conflict because trx' | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        uint32_t beforeLogNum = atoi(errorNum);

        if (i == 0) {
            // 提交事务顺序: 1, 2, 3
            ret = GmcTransCommit(conn1);
            EXPECT_EQ(GMERR_OK, ret);

            // 事务2应当冲突回退
            ret = GmcTransCommit(conn2);
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn2);
            EXPECT_EQ(GMERR_OK, ret);

            // 事务3应当冲突回退
            ret = GmcTransCommit(conn3);
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn3);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i == 1) {
            // 提交事务顺序: 2, 1, 3
            ret = GmcTransCommit(conn2);
            EXPECT_EQ(GMERR_OK, ret);

            // 事务1应当冲突回退
            ret = GmcTransCommit(conn1);
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn1);
            EXPECT_EQ(GMERR_OK, ret);

            // 事务3应当冲突回退
            ret = GmcTransCommit(conn3);
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn3);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i == 2) {
            // 提交事务顺序: 3, 1, 2
            ret = GmcTransCommit(conn3);
            EXPECT_EQ(GMERR_OK, ret);

            // 事务1应当冲突回退
            ret = GmcTransCommit(conn1);
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn1);
            EXPECT_EQ(GMERR_OK, ret);

            // 事务3应当冲突回退
            ret = GmcTransCommit(conn2);
            EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn2);
            EXPECT_EQ(GMERR_OK, ret);
        }

        fp = popen("cat ./log/run/rgmserver/* | grep -rn 'conflict because trx' | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        uint32_t afterLogNum = atoi(errorNum);
        if (afterLogNum != beforeLogNum + 2 && afterLogNum != beforeLogNum + 4) {
            EXPECT_EQ(0, 1);
        }

        // 释放3个事务的连接
        DestroyConnectionAndStmt(conn1, stmt1);
        DestroyConnectionAndStmt(conn2, stmt2);
        DestroyConnectionAndStmt(conn3, stmt3);
    }

    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
/*
事务A：写入4条
事务B：      begin --------------------------> openHeap(获取到curCnt=2)删除4条记录 --> insert(校验最大记录数) --> commit
事务C：           begin --> 删除2条 --> commit
*/
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_112)
{
    StartDbServerWithConfig(" \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
                            "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\"");
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 4;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);

    GmcTxConfigT config;
    DefaultConf(&config);
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn1, &config));

    DefaultConf(&config);
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn2, &config));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, DeleteLabelData(stmt2, 1, false));
    EXPECT_EQ(GMERR_OK, DeleteLabelData(stmt2, 0, false));

    EXPECT_EQ(GMERR_OK, GmcTransCommit(conn2));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, DeleteLabelData(stmt1, 0, false));
    EXPECT_EQ(GMERR_OK, DeleteLabelData(stmt1, 1, false));
    EXPECT_EQ(GMERR_OK, DeleteLabelData(stmt1, 2, false));
    EXPECT_EQ(GMERR_OK, DeleteLabelData(stmt1, 3, false));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_INSERT));
    EXPECT_EQ(GMERR_OK, InsertLabelData(stmt1, 4));

    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, GmcTransCommit(conn1));

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    OptimisiticClear(conn, stmt, g_labelName, false);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

/*
测试purge线程的批量回收策略，最简单的情况:
预置一条记录，开启事务1阻塞purge回收，再先后分2个事务：开启事务、更新、提交事务。
测试从masterVersion上连续回收两个版本
提交事务1，purge连续回收2个版本，预期不会有错误日志产生
*/
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_113)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 1;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    OptimisiticPrePare();
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);

    // 创建conn3和stmt3
    GmcConnT *conn3;
    GmcStmtT *stmt3;
    CreateSyncConnectionAndStmt(&conn3, &stmt3);

    // 开启事务1，阻塞purge回收
    StOptimisiticTransStart(conn1, true);

    // 开启事务2更新所有数据，然后提交
    StOptimisiticTransStart(conn2, false);
    ret = GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt2, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务3更新所有数据，然后提交
    StOptimisiticTransStart(conn3, false);
    ret = GmcPrepareStmtByLabelName(stmt3, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt3, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn3);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看日志中的error数量
    FILE *fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v "
                     "'GMERR-1009010' | grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t beforeLogNum = atoi(errorNum);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v 'GMERR-1009010' | "
               "grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t afterLogNum = atoi(errorNum);
    EXPECT_EQ(afterLogNum, beforeLogNum);  // 预期不会有错误日志产生

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn3, stmt3);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

/*
测试purge线程的批量回收策略，最简单的情况:
预置一条记录，开启事务1阻塞purge回收，再先后分2个事务：开启事务、更新、提交事务。
再开启事务4删除该条记录，测试从版本链上连续回收两个版本
提交事务1，purge连续回收2个版本，预期不会有错误日志产生
提交事务4，预期不会有错误日志产生
*/
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_114)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 1;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    OptimisiticPrePare();
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);

    // 创建conn3和stmt3
    GmcConnT *conn3;
    GmcStmtT *stmt3;
    CreateSyncConnectionAndStmt(&conn3, &stmt3);

    // 创建conn4和stmt4
    GmcConnT *conn4;
    GmcStmtT *stmt4;
    CreateSyncConnectionAndStmt(&conn4, &stmt4);

    // 开启事务1，阻塞purge回收
    StOptimisiticTransStart(conn1, true);

    // 开启事务2更新所有数据，然后提交
    StOptimisiticTransStart(conn2, false);
    ret = GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt2, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务3更新所有数据，然后提交
    StOptimisiticTransStart(conn3, false);
    ret = GmcPrepareStmtByLabelName(stmt3, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt3, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn3);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务3更新所有数据，然后提交
    StOptimisiticTransStart(conn4, false);
    ret = GmcPrepareStmtByLabelName(stmt4, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue = 0;
    ret = DeleteLabelData(stmt4, indexValue, false);

    // 查看日志中的error数量
    FILE *fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v "
                     "'GMERR-1009010' | grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t beforeLogNum = atoi(errorNum);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v 'GMERR-1009010' | "
               "grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t afterLogNum = atoi(errorNum);
    EXPECT_EQ(afterLogNum, beforeLogNum);  // 预期不会有错误日志产生

    ret = GmcTransCommit(conn4);
    EXPECT_EQ(GMERR_OK, ret);

    fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v 'GMERR-1009010' | "
               "grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t afterLogNum2 = atoi(errorNum);
    EXPECT_EQ(afterLogNum2, afterLogNum);  // 预期不会有错误日志产生

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn3, stmt3);
    DestroyConnectionAndStmt(conn4, stmt4);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

/*
测试purge线程的批量回收策略，最简单的情况:
预置一条记录，开启事务1阻塞purge回收，再先后分2个事务：开启事务、更新、提交事务。
再开启事务4删除该条记录，提交事务4
提交事务1，purge测试从版本链上连续回收两个版本(预期不会处理删除的undoRec)，最后再处理删除的undoRec
*/
TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_115)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t insertNum = 1;
    string jsonPath = "./st_file/optimisiticTrx.gmjson";
    OptimisiticPrePare();
    int32_t ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);
    CLientInit(&conn, &stmt);
    ret = OptimisiticPrePareData(insertNum, jsonPath, stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建conn1和stmt1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建conn2和stmt2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);

    // 创建conn3和stmt3
    GmcConnT *conn3;
    GmcStmtT *stmt3;
    CreateSyncConnectionAndStmt(&conn3, &stmt3);

    // 创建conn4和stmt4
    GmcConnT *conn4;
    GmcStmtT *stmt4;
    CreateSyncConnectionAndStmt(&conn4, &stmt4);

    // 开启事务1，阻塞purge回收
    StOptimisiticTransStart(conn1, true);

    // 开启事务2更新所有数据，然后提交
    StOptimisiticTransStart(conn2, false);
    ret = GmcPrepareStmtByLabelName(stmt2, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt2, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务3更新所有数据，然后提交
    StOptimisiticTransStart(conn3, false);
    ret = GmcPrepareStmtByLabelName(stmt3, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ReplaceVertexAll(stmt3, 0, "F0", 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn3);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务3更新所有数据，然后提交
    StOptimisiticTransStart(conn4, false);
    ret = GmcPrepareStmtByLabelName(stmt4, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t indexValue = 0;
    ret = DeleteLabelData(stmt4, indexValue, false);
    ret = GmcTransCommit(conn4);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看日志中的error数量
    FILE *fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v "
                     "'GMERR-1009010' | grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t beforeLogNum = atoi(errorNum);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    OptimisiticTrxCheckGcHandleHistoryUndoLog();

    fp = popen("grep -rn 'GMERR-' ./log/run/rgmserver/ | grep -v 'GMERR-1009009' | grep -v 'GMERR-1009010' | "
               "grep -v 'This log was folded for' | wc -l",
        "r");
    EXPECT_TRUE(fp != NULL);
    fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t afterLogNum = atoi(errorNum);
    EXPECT_EQ(afterLogNum, beforeLogNum);  // 预期不会有错误日志产生

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn3, stmt3);
    DestroyConnectionAndStmt(conn4, stmt4);

    OptimisiticClear(conn, stmt, g_labelName);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_116)
{
    // 增量持久化不支持建边表
    PERSISTENCE_NOT_SUPPORT;
    char vl1Name[] = "vertexlabel_1";
    char vl2Name[] = "vertexlabel_2";
    string vl1Path = "./st_file/optimisiticTrx_dfx_table1.gmjson";
    string vl2Path = "./st_file/optimisiticTrx_dfx_table2.gmjson";

    // 拉起gmserver
    int32_t ret;
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableLogFold=0\"");
    ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    string test_schema = GetFileContext(vl1Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(vl2Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, vl1Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t valueF0 = 10;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t valueF1 = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t valueF2 = 12;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务1读表1，写表2
    ret = GmcPrepareStmtByLabelName(stmt1, vl1Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1, "vertexlabel_1_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    int value = 10;
    ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt1, vl2Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 20;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 21;
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 22;
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2写表1
    ret = GmcPrepareStmtByLabelName(stmt2, vl1Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 20;
    ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 21;
    ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 22;
    ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_117)
{
    // 增量持久化不支持建边表
    PERSISTENCE_NOT_SUPPORT;
    char vl1Name[] = "vertexlabel_1";
    char vl2Name[] = "vertexlabel_2";
    char vl3Name[] = "vertexlabel_3";
    string vl1Path = "./st_file/optimisiticTrx_dfx_table1.gmjson";
    string vl2Path = "./st_file/optimisiticTrx_dfx_table2.gmjson";
    string vl3Path = "./st_file/optimisiticTrx_dfx_table3.gmjson";

    // 拉起gmserver
    int32_t ret;
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableLogFold=0\"");
    ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建表
    string test_schema = GetFileContext(vl1Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(vl2Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    test_schema = GetFileContext(vl3Path);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, vl1Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t valueF0 = 10;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t valueF1 = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t valueF2 = 12;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务1
    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务2
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSyncConnectionAndStmt(&conn2, &stmt2);
    DefaultConf(&config);
    ret = GmcTransStart(conn2, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务1读表1，写表2
    ret = GmcPrepareStmtByLabelName(stmt1, vl1Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1, "vertexlabel_1_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    int value = 10;
    ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt1, vl2Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 20;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 21;
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 22;
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2读表1，写表3
    ret = GmcPrepareStmtByLabelName(stmt2, vl1Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2, "vertexlabel_1_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    value = 10;
    ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt2, vl3Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    valueF0 = 20;
    ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    valueF1 = 21;
    ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    valueF2 = 22;
    ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn2, stmt2);

    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

static Status GetMemberValueOfSysViewCmd(char *cmd, char *memberName, int64_t *memberValue)
{
    char finalCmd[512] = {0};
    (void)sprintf_s(finalCmd, sizeof(finalCmd), "%s | grep \'%s:\' | awk -F\':\' \'{print $2}\'", cmd, memberName);
    FILE *pipe = popen(finalCmd, "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        *memberValue = atoi(buffer);
        pclose(pipe);
        return GMERR_OK;
    }
    pclose(pipe);
    return -1;
}

static Status InserOrUpdateDataForTestOptimistic3(
    GmcStmtT *stmt, uint32_t keyValue, char *tableName, GmcOperationTypeE type, uint32_t startValue)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, type);
    EXPECT_EQ(GMERR_OK, ret);
    if (type == GMC_OPERATION_INSERT) {
        uint64_t valueF0 = keyValue;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &valueF0, sizeof(valueF0));
        EXPECT_EQ(GMERR_OK, ret);
    } else if (type == GMC_OPERATION_UPDATE) {
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t value = keyValue;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        return -1;
    }
    uint64_t valueF1 = startValue + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t valueF2 = startValue + 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &valueF2, sizeof(valueF2));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t valueF3 = startValue + 3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT64, &valueF3, sizeof(valueF3));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t valueF4 = startValue + 4;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT64, &valueF4, sizeof(valueF4));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

TEST_F(StStorageOptimisiticTrx, OptimisiticTrx_118)
{
    char tableName[] = "TestOptimistic3";
    string tablePath = "./st_file/optimisiticTrx3.gmjson";

    // 拉起gmserver
    int32_t ret;
    StartDbServerWithConfig(
        " \"workerHungThreshold=6,200,300\" \"isFastReadUncommitted=0\" "
        "\"defaultTransactionType=1\" \"defaultIsolationLevel=2\" \"trxMonitorEnable=0\" \"enableLogFold=0\"");
    ret = GmcInit();
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string test_schema = GetFileContext(tablePath);
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InserOrUpdateDataForTestOptimistic3(stmt, 0, tableName, GMC_OPERATION_INSERT, 0);
    ASSERT_EQ(GMERR_OK, ret);

    GmcConnT *conn1;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);
    GmcTxConfigT config;
    DefaultConf(&config);
    ret = GmcTransStart(conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=TestOptimistic3_key1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=TestOptimistic3_key2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";
    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    ASSERT_EQ(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    ASSERT_EQ(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    ASSERT_EQ(ret, 0);

    EXPECT_EQ(undoCnt, 0);
    EXPECT_EQ(key1Cnt, 1);
    EXPECT_EQ(key2Cnt, 1);

    // 先更新一条
    ret = InserOrUpdateDataForTestOptimistic3(stmt1, 0, tableName, GMC_OPERATION_UPDATE, 4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    ASSERT_EQ(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    ASSERT_EQ(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    ASSERT_EQ(ret, 0);

    EXPECT_EQ(undoCnt, 1);
    EXPECT_EQ(key1Cnt, 2);
    EXPECT_EQ(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 10000; ++i) {
        ret = InserOrUpdateDataForTestOptimistic3(stmt1, 0, tableName, GMC_OPERATION_UPDATE, i * 4);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    EXPECT_EQ(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    EXPECT_EQ(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    EXPECT_EQ(ret, 0);

    EXPECT_EQ(undoCnt, 1);
    EXPECT_EQ(key1Cnt, 2);
    EXPECT_EQ(key2Cnt, 2);

    ret = GmcTransCommit(conn1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    ASSERT_EQ(ret, 0);
    EXPECT_EQ(undoCnt, 0);

    int cnt = 600;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        ASSERT_EQ(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        ASSERT_EQ(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    EXPECT_EQ(key1Cnt, 1);
    EXPECT_EQ(key2Cnt, 1);

    DestroyConnectionAndStmt(conn1, stmt1);
    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
