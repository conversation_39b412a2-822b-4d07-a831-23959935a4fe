/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_simprel_sysview.cc
 * Description:
 * Create:
 */
#include "st_emb_simprel_com.h"

using namespace std;

class StSimpRelMemory : public StSimpRel {};

TEST_F(StSimpRelMemory, TestImportMemory)
{
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    cout << "start rss:" << endl;
    system("pmap -x `pidof st_015_emb_simrel` | grep 'total' | awk '{print $4}'");
    cout << "start dyn:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps | grep \"rw-p\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    cout << "start shm:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps| grep \"rw-s\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_DYN_MEMCTX, &sysviewArgs, &result));
    cout << "DB_TPC_SYSVIEW_GET_DYN_MEMCTX:" << endl << result << endl;

    sysviewArgs.pucDbName = (VOS_UINT8 *)"testImport";
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./dcdm/data/master24_desc.txt";
    sysviewArgs.importType = DB_RESTORETYPE_DISCARD;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_IMPORT_TXT, &sysviewArgs, &result));
    cout << "DB_TPC_SYSVIEW_IMPORT_TXT :" << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_DYN_MEMCTX, &sysviewArgs, &result));
    cout << "DB_TPC_SYSVIEW_GET_DYN_MEMCTX:" << endl << result << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    cout << "DB_TPC_SYSVIEW_GET_SHM_MEMCTX:" << endl << result << endl;

    FILE *fp = popen("pmap -x `pidof st_015_emb_simrel` | grep 'total' | awk '{print $4}'", "r");

    pclose(fp);
    cout << "end rss:" << endl;
    system("pmap -x `pidof st_015_emb_simrel` | grep 'total' | awk '{print $4}'");
    cout << "end dyn:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps | grep \"rw-p\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    cout << "end shm:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps| grep \"rw-s\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");

    uint32_t dbId = 0;
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, sysviewArgs.pucDbName, &dbId));

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs2 = {0};
    VOS_UINT8 *result2 = NULL;
    sysviewArgs2.pucFilePath = (VOS_UINT8 *)"./dcdm/data/output";
    sysviewArgs2.ulDbId = dbId;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs2, &result2));
    cout << "DB_TPC_SYSVIEW_EXPORT_TXT :" << endl;
    system("rm -rf ./dcdm/data/output");
    CommonDropDB4Test((const char *)sysviewArgs.pucDbName, dbId);
}

#pragma pack(1)
typedef struct TestMemTestRecord {
    float v0;
    double v1;
    uint8_t v2[32];
    uint8_t v3;
    char v4[64];
    uint16_t v5;
    uint32_t v6;
    uint32_t v7;
    uint32_t v8;
    uint32_t v9;
    uint32_t v10;
    char v11[300];
    uint8_t v12[32];
} TestMemTestRecordT;
#pragma pack(0)

void TestMemTestInitOneRecord(TestMemTestRecordT *record, uint32_t base)
{
    (void)memset_s(record, sizeof(TestMemTestRecordT), 0x00, sizeof(TestMemTestRecordT));
    record->v0 = base;
    record->v1 = base;
    uint8_t baseV = (uint8_t)(base % 256);
    (void)memset_s(record->v2, 32, baseV, 32);
    record->v3 = baseV;
    (void)sprintf_s(record->v4, 64, "test_mem_str_value_%u", base);
    record->v5 = base % 0xFFFF;
    record->v6 = base;
    record->v7 = base;
    record->v8 = base;
    record->v9 = base;
    record->v10 = base;
    int32_t charV = (int32_t)((base % 26) + 'a');
    (void)memset_s(record->v11, 299, charV, 299);
    record->v11[299] = '\0';
    *(uint16_t *)record->v12 = base % 10 + 10;
    (void)sprintf_s((char *)&record->v12[2], 30, "vbytes_%u", base);
}

inline static void TestMemSizePrintInfo()
{
    printf("one record size = %u KB\n", (uint32_t)sizeof(TestMemTestRecordT));
    printf("total size = %.2f MB\n", (double)(sizeof(TestMemTestRecordT) * 100000) / 1024 / 1024);
}

TEST_F(StSimpRelMemory, TestInsertMemSize1)
{
    uint32_t currPid = (uint32_t)getpid();
    printf("Pid is %u\n", currPid);
    char procCmd[256] = {0};
    (void)sprintf_s(procCmd, 256, "cat /proc/%u/status", currPid);
    TestMemSizePrintInfo();

    char dbName[] = "undoMem";
    uint32_t ulDbId = 0xFFFF;
    CommonCreateDB4Test(dbName, &ulDbId);

    Status ret = GMERR_OK;
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/mem_test/testMemRelDef.json", &stRelDef));
    uint16_t usRelId = 0xFFFF;
    ASSERT_EQ(GMERR_OK, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
    StUtilFreeAllRelDef(&stRelDef);

    DB_DSBUF_STRU stBuff = {.usRecLen = 0,
        .usRecNum = 0,
        .StdBuf = {.ulBufLen = 0, .ulActLen = sizeof(TestMemTestRecordT), .pucData = NULL}};

    ret = TPC_BeginCDBByID(ulDbId, 10);
    ASSERT_EQ(ret, GMERR_OK);
    TestMemTestRecordT record;
    for (uint32_t ulLoop = 0; ulLoop < stRelDef.ulMaxSize; ulLoop++) {
        TestMemTestInitOneRecord(&record, ulLoop);
        stBuff.StdBuf.pucData = (VOS_UINT8 *)&record;
        ret = TPC_InsertRec(10, ulDbId, usRelId, &stBuff);
        ASSERT_EQ(DB_SUCCESS, ret);
    }
    ret = TPC_CommitCDB(10);
    ASSERT_EQ(DB_SUCCESS, ret);

    printf("\n\n============================================");
    system(procCmd);
    printf("============================================\n\n");
    ASSERT_EQ(DB_SUCCESS, TPC_DropTbl(ulDbId, usRelId, 1));
    CommonDropDB4Test(dbName, ulDbId);
}

TEST_F(StSimpRelMemory, TestUpdateMemSize1)
{
    uint32_t currPid = (uint32_t)getpid();
    printf("Pid is %u\n", currPid);
    char procCmd[256] = {0};
    (void)sprintf_s(procCmd, 256, "cat /proc/%u/status", currPid);
    TestMemSizePrintInfo();

    char dbName[] = "undoMem";
    uint32_t ulDbId = 0xFFFF;
    CommonCreateDB4Test(dbName, &ulDbId);

    Status ret = GMERR_OK;
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/mem_test/testMemRelDef.json", &stRelDef));
    uint16_t usRelId = 0xFFFF;
    ASSERT_EQ(GMERR_OK, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
    StUtilFreeAllRelDef(&stRelDef);

    DB_DSBUF_STRU stBuff = {.usRecLen = 0,
        .usRecNum = 0,
        .StdBuf = {.ulBufLen = 0, .ulActLen = sizeof(TestMemTestRecordT), .pucData = NULL}};

    TestMemTestRecordT record;
    for (uint32_t ulLoop = 0; ulLoop < stRelDef.ulMaxSize; ulLoop++) {
        TestMemTestInitOneRecord(&record, ulLoop);
        stBuff.StdBuf.pucData = (VOS_UINT8 *)&record;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff);
        ASSERT_EQ(DB_SUCCESS, ret);
    }
    printf("\n\n============================================");
    system(procCmd);
    printf("============================================\n\n");

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    uint32_t udpNum = 0;
    DB_COND_STRU stCond = {.usCondNum = 1};
    stCond.aCond[0].ucFieldId = 6;
    stCond.aCond[0].enOp = DB_OP_EQUAL;

    ret = TPC_BeginCDBByID(ulDbId, 10);
    ASSERT_EQ(DB_SUCCESS, ret);
    for (uint32_t ulLoop = 0; ulLoop < stRelDef.ulMaxSize; ulLoop++) {
        TestMemTestInitOneRecord(&record, ulLoop + 100000);
        *(uint32_t *)stCond.aCond[0].aucValue = ulLoop;
        ret = TPC_UpdateRec(10, ulDbId, usRelId, &stCond, &stFldFilter, &stBuff, &udpNum);
        ASSERT_EQ(DB_SUCCESS, ret);
        ASSERT_EQ(udpNum, 1);
    }
    ret = TPC_CommitCDB(10);
    ASSERT_EQ(DB_SUCCESS, ret);

    printf("\n\n============================================");
    system(procCmd);
    printf("============================================\n\n");

    ASSERT_EQ(DB_SUCCESS, TPC_DropTbl(ulDbId, usRelId, 1));
    CommonDropDB4Test(dbName, ulDbId);
}

TEST_F(StSimpRelMemory, TestDeleteMemSize1)
{
    uint32_t currPid = (uint32_t)getpid();
    printf("Pid is %u\n", currPid);
    char procCmd[256] = {0};
    (void)sprintf_s(procCmd, 256, "cat /proc/%u/status", currPid);
    TestMemSizePrintInfo();

    char dbName[] = "undoMem";
    uint32_t ulDbId = 0xFFFF;
    CommonCreateDB4Test(dbName, &ulDbId);

    Status ret = GMERR_OK;
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/mem_test/testMemRelDef.json", &stRelDef));
    uint16_t usRelId = 0xFFFF;
    ASSERT_EQ(GMERR_OK, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
    StUtilFreeAllRelDef(&stRelDef);

    DB_DSBUF_STRU stBuff = {.usRecLen = 0,
        .usRecNum = 0,
        .StdBuf = {.ulBufLen = 0, .ulActLen = sizeof(TestMemTestRecordT), .pucData = NULL}};

    TestMemTestRecordT record;
    for (uint32_t ulLoop = 0; ulLoop < stRelDef.ulMaxSize; ulLoop++) {
        TestMemTestInitOneRecord(&record, ulLoop);
        stBuff.StdBuf.pucData = (VOS_UINT8 *)&record;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff);
        ASSERT_EQ(DB_SUCCESS, ret);
    }
    printf("\n\n============================================");
    system(procCmd);
    printf("============================================\n\n");

    uint32_t delNum = 0;
    DB_COND_STRU stCond = {.usCondNum = 1};
    stCond.aCond[0].ucFieldId = 6;
    stCond.aCond[0].enOp = DB_OP_EQUAL;

    ret = TPC_BeginCDBByID(ulDbId, 10);
    ASSERT_EQ(DB_SUCCESS, ret);
    for (uint32_t ulLoop = 0; ulLoop < stRelDef.ulMaxSize; ulLoop++) {
        *(uint32_t *)stCond.aCond[0].aucValue = ulLoop;
        ret = TPC_DeleteRec(10, ulDbId, usRelId, &stCond, &delNum);
        ASSERT_EQ(DB_SUCCESS, ret);
        ASSERT_EQ(delNum, 1);
    }
    ret = TPC_CommitCDB(10);
    ASSERT_EQ(DB_SUCCESS, ret);

    printf("\n\n============================================");
    system(procCmd);
    printf("============================================\n\n");

    ASSERT_EQ(DB_SUCCESS, TPC_DropTbl(ulDbId, usRelId, 1));
    CommonDropDB4Test(dbName, ulDbId);
}
