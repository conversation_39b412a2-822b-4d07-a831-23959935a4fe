/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_simprel_ddl.cc
 * Description:
 * Create:
 */
#include "st_emb_simprel_com.h"
#include "gme_sql_api.h"

using namespace std;

class StSimpRelDBDDL : public StSimpRelDB {};

TEST_F(StSimpRelDBDDL, DBDDLTestTpcInitRepeat)
{
    // StSimpRelDBDDL 中已经初始化，后续初始化直接返回
    ASSERT_EQ(GMERR_OK, DB_Init(NULL));
    ASSERT_EQ(GMERR_OK, DB_Init("/usr/local/file/gmserver.ini"));
}

// 测试未手动初始化时，自动初始化
TEST_F(StSimpRelDBDDL, DBDDLTestCfgDbTpcAutoInit)
{
    DB_UnInit();
    system("cp config/st_emb_simprel.ini /usr/local/file/compatV1_gmserver.ini");
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "TestDB";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 ulDbId2 = 0;
    const int openTime = 10;
    for (int i = 0; i < openTime; i++) {
        EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId2));
        EXPECT_EQ(ulDbId, ulDbId2);
    }
    for (int i = 0; i <= openTime; i++) {
        EXPECT_EQ(GMERR_OK, DB_CloseDB(ulDbId));
    }
    EXPECT_EQ(GMERR_OK, DB_DropDB((VOS_UINT8 *)dbName, TPC_NOWAIT));
    DB_UnInit();
    EXPECT_EQ(GMERR_OK, DB_Init("./config/st_emb_simprel.ini"));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCfgDbTpcCompInit)
{
    DB_UnInit();
    system("cp /usr/local/file/gmserver.ini /usr/local/file/compatV1_gmserver.ini");
    ASSERT_EQ(GMERR_OK, DB_Init("/usr/local/file/compatV1_gmserver.ini"));
    char dbName[10] = "TestDB";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    EXPECT_EQ(GMERR_OK, DB_DropDB((VOS_UINT8 *)dbName, TPC_NOWAIT));
    system("rm -f /usr/local/file/compatV1_gmserver.ini");
    DB_UnInit();
    EXPECT_EQ(GMERR_OK, DB_Init("./config/st_emb_simprel.ini"));
}

TEST_F(StSimpRelDBDDL, DBDDLTestDBCreateAndDrop)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 ulDbId2 = 0;
    const int openTime = 10;
    for (int i = 0; i < openTime; i++) {
        EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId2));
        EXPECT_EQ(ulDbId, ulDbId2);
    }
    for (int i = 0; i <= openTime; i++) {
        EXPECT_EQ(GMERR_OK, DB_CloseDB(ulDbId));
    }
    EXPECT_EQ(GMERR_OK, DB_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateAndDropMultiDb)
{
    char dbName0[] = "db0";
    char dbName1[] = "db1";
    DB_INST_CONFIG_STRU dbCfg = {0};
    // create db 0、1
    EXPECT_EQ(GMERR_OK, DB_CreateDB((VOS_UINT8 *)dbName0, NULL, &dbCfg));
    EXPECT_EQ(GMERR_OK, DB_CreateDB((VOS_UINT8 *)dbName1, NULL, &dbCfg));

    // open db 0、1
    uint32_t dbId0, dbId1;
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName0, &dbId0));
    printf("db0 id is %u.\n", dbId0);
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName1, &dbId1));
    printf("db1 id is %u.\n", dbId1);

    // colse db 0、1
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId0));
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId1));

    // drop db 0
    EXPECT_EQ(GMERR_OK, DB_DropDB((VOS_UINT8 *)dbName0, 1));

    // reopen db 1
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName1, &dbId1));
    printf("db1 id is %u.\n", dbId1);

    // recreate db 0
    EXPECT_EQ(GMERR_OK, DB_CreateDB((VOS_UINT8 *)dbName0, NULL, &dbCfg));
    // reopen db 0
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, (VOS_UINT8 *)dbName0, &dbId0));
    printf("db0 id is %u.\n", dbId0);

    // close and drop db 0
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId0));
    EXPECT_EQ(GMERR_OK, DB_DropDB((VOS_UINT8 *)dbName0, 1));

    // drop db 1 without close
    EXPECT_EQ(VOS_ERRNO_DB_DROP_NOTALLOWED, DB_DropDB((VOS_UINT8 *)dbName1, 1));
    // close and drop db 1
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId1));
    EXPECT_EQ(GMERR_OK, DB_DropDB((VOS_UINT8 *)dbName1, 1));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithNullCfg)
{
    // db cfg 传入 null
    uint8_t *dbName = (uint8_t *)"db";
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_CreateDB(dbName, NULL, NULL));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidDescLen)
{
    // desc max len 不合法
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.ulMaxDBDescInfoSize = 0xffffffff;
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDINPUT, DB_CreateDB(dbName, NULL, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidDbDir0)
{
    // 配置持久化但是路径为null
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_CreateDB(dbName, NULL, &dbCfg));

    dbCfg.enPersistent = DB_CKP_INCR;
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_CreateDB(dbName, NULL, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidDbDir1)
{
    // 配置持久化但是路径为首字符为 '\0'
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    const uint32_t pathLen = 30;
    uint8_t path[pathLen];
    (void)memcpy_s(path, pathLen, "/usr/test.dat", pathLen);
    path[0] = '\0';
    EXPECT_EQ(VOS_ERRNO_DB_OPENFILE_ERROR, DB_CreateDB(dbName, path, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidDbDir2)
{
    // 配置持久化但是路径长度大于256
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    const uint32_t pathLen = 256;
    uint8_t path[257];
    for (uint32_t i = 0; i < pathLen; i++) {
        path[i] = 'a';
    }
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_DBPATH, DB_CreateDB(dbName, path, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbExWithStorage)
{
    // 测试参数storage
    DB_INST_CONFIG_STRU dbCfg = {0};
    uint8_t *dbName = (uint8_t *)"db";
    // DB_DATA_STORAGE_RAM
    EXPECT_EQ(GMERR_OK, DB_CreateDBEx(dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM));
    EXPECT_EQ(GMERR_OK, DB_DropDB(dbName, 0));

    // DB_DATA_STORAGE_RSM
    EXPECT_EQ(VOS_ERRNO_DB_NOTSUPPORT, DB_CreateDBEx(dbName, NULL, &dbCfg, DB_DATA_STORAGE_RSM));

    // DB_DATA_STORAGE_BUTT and others
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDINPUT, DB_CreateDBEx(dbName, NULL, &dbCfg, DB_DATA_STORAGE_BUTT));
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDINPUT, DB_CreateDBEx(dbName, NULL, &dbCfg, (DB_DATA_STORAGE_ENUM)5));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidName0)
{
    // db name 传入 null
    DB_INST_CONFIG_STRU dbCfg = {0};
    uint8_t *dbName = NULL;
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_CreateDB(dbName, NULL, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidName1)
{
    // db name 第一个字符为 '\0'
    char dbName[] = "testName";
    dbName[0] = '\0';
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_DBNAME, DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDbWithInvalidName2)
{
    // db name 长度大于 16
    const int32_t nameLen = 17;
    char dbName[nameLen];
    for (uint32_t i = 0; i < nameLen; i++) {
        dbName[i] = 'a';
    }
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_DBNAME, DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
}

TEST_F(StSimpRelDBDDL, DBDDLTestDropDbWithoutClose)
{
    // 未close db删除db
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB(dbName, NULL, &dbCfg));
    uint32_t dbId;
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, dbName, &dbId));
    EXPECT_EQ(VOS_ERRNO_DB_DROP_NOTALLOWED, DB_DropDB(dbName, 1));
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId));
    EXPECT_EQ(GMERR_OK, DB_DropDB(dbName, 1));
}

TEST_F(StSimpRelDBDDL, DBDDLTestDropDbWithoutClose2)
{
    // open两次，close一次，删除DB
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB(dbName, NULL, &dbCfg));
    uint32_t dbId;
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, dbName, &dbId));
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, dbName, &dbId));
    EXPECT_EQ(VOS_ERRNO_DB_DROP_NOTALLOWED, DB_DropDB(dbName, 1));
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId));
    EXPECT_EQ(VOS_ERRNO_DB_DROP_NOTALLOWED, DB_DropDB(dbName, 1));
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId));
    EXPECT_EQ(GMERR_OK, DB_DropDB(dbName, 1));
}

typedef struct DropPara {
    uint32_t ulDbId;
    uint32_t relId;
    bool ready;
    uint8_t *dbName;
} DropParaT;

void *DBDDLDropDbNotCloseProc(void *args)
{
    DropParaT *para = (DropParaT *)args;
    EXPECT_EQ(VOS_ERRNO_DB_DROP_NOTALLOWED, DB_DropDB(para->dbName, DROP_NOWAIT));
    para->ready = true;
    EXPECT_EQ(GMERR_OK, DB_DropDB(para->dbName, DROP_WAIT));
    return NULL;
}

TEST_F(StSimpRelDBDDL, DBDDLTestDropDBWait)
{
    // open两次，close一次，删除DB
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB(dbName, NULL, &dbCfg));
    uint32_t dbId;
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, dbName, &dbId));

    pthread_t tid1;
    DropParaT para = {.ulDbId = dbId, .relId = 0, .ready = false, .dbName = dbName};
    ASSERT_EQ(GMERR_OK, pthread_create(&tid1, NULL, DBDDLDropDbNotCloseProc, &para));
    while (!para.ready) {
    }

    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId));
    EXPECT_EQ(GMERR_OK, pthread_join(tid1, NULL));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCloseDbTimeLargeThanOpen)
{
    // close次数大于open
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB(dbName, NULL, &dbCfg));
    uint32_t dbId;
    EXPECT_EQ(GMERR_OK, DB_OpenDB(NULL, dbName, &dbId));
    EXPECT_EQ(GMERR_OK, DB_CloseDB(dbId));
    EXPECT_EQ(VOS_ERRNO_DB_DATABASE_NOT_OPENED, DB_CloseDB(dbId));
    EXPECT_EQ(GMERR_OK, DB_DropDB(dbName, 0));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateDuplicateDB)
{
    // 创建重复DB
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB(dbName, NULL, &dbCfg));
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_DBNAME, DB_CreateDB(dbName, NULL, &dbCfg));
    EXPECT_EQ(GMERR_OK, DB_DropDB(dbName, 0));
}

TEST_F(StSimpRelDBDDL, DBDDLTestDropNonExistDb)
{
    // 删除不存在的DB
    uint8_t *dbName = (uint8_t *)"db";
    DB_INST_CONFIG_STRU dbCfg = {0};
    EXPECT_EQ(GMERR_OK, DB_CreateDB(dbName, NULL, &dbCfg));
    EXPECT_EQ(GMERR_OK, DB_DropDB(dbName, 0));
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_DBNAME, DB_DropDB(dbName, 0));
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithInvalidPara)
{
    // 测试建表入参为NULL
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t relId;
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_CreateTbl(ulDbId, NULL, &relId));
    DB_REL_DEF_STRU stRelDef = {0};
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_CreateTbl(ulDbId, &stRelDef, NULL));
    CommonDBDropDB4Test(NULL, ulDbId);
}

void DBDDLTestCreateTblInitRelDefWithoutIdx(DB_REL_DEF_STRU *stRelDef, const char *tblName)
{
    // 此表定义没有设置索引
    const char tableName[] = "nameindex";
    const uint32_t fldNum = 5;
    DB_FIELD_DEF_STRU *astFlds = (DB_FIELD_DEF_STRU *)malloc(fldNum * sizeof(DB_FIELD_DEF_STRU));
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32};
    const uint32_t fldSizes[fldNum] = {4, 4, 4, 4, 4};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
        astFlds[i].ulDefVal = 0xFFFFFFFF;
    }

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 100;
    stRelDef->ulMaxSize = 100;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = 0;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithInvalidRelType)
{
    // 测试无效表类型
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    stRelDef.enTableType = DB_TABLE_BUTT;  // 设置为无效的表类型
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_TBLTYPE, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithInvalidRelSize)
{
    // 表定义中初始化大小和最大大小不合法
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    // 最大大小为0
    stRelDef.ulMaxSize = 0;
    EXPECT_EQ(VOS_ERRNO_DB_ERROR_FOR_INC_MAXREC, DB_CreateTbl(ulDbId, &stRelDef, &relId));

    // 最大大小达到上限
    stRelDef.ulMaxSize = VOS_NULL_LONG;
    EXPECT_EQ(VOS_ERRNO_DB_ERROR_FOR_INC_MAXREC, DB_CreateTbl(ulDbId, &stRelDef, &relId));

    // 初始化大小大于最大大小
    stRelDef.ulMaxSize = 100;
    stRelDef.ulIntialSize = 101;
    EXPECT_EQ(VOS_ERRNO_DB_ERROR_FOR_INC_MAXREC, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithInvalidIdxDef)
{
    // 测试索引定义不合法
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    // 索引数量大于上限（上限为64）
    stRelDef.ulNIdxs = 256;
    DB_INDEX_DEF_STRU pstIdxLst[stRelDef.ulNIdxs] = {0};  // 这里只保证不为null
    stRelDef.pstIdxLst = pstIdxLst;
    EXPECT_EQ(VOS_ERRNO_DB_TOOMANYINDICES, DB_CreateTbl(ulDbId, &stRelDef, &relId));

    // 索引数量大于0，但是数组为null
    stRelDef.ulNIdxs = 1;
    stRelDef.pstIdxLst = NULL;
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDINDEX, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWith255Indices)
{
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef = {0};
    const char tableName[] = "nameindex";
    const uint32_t fldNum = 253;
    const uint32_t idxNum = 255;
    DB_FIELD_DEF_STRU astFlds[fldNum] = {0};
    DB_INDEX_DEF_STRU pstIdxLst[idxNum] = {0};

    uint32_t i = 0;
    for (i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = DBT_UINT32;
        astFlds[i].usSize = 0;
        astFlds[i].ulDefVal = 0xFFFFFFFF;

        (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_NAME_LEN, "idx%d", i);
        pstIdxLst[i].enIndexType = DBDDL_INDEXTYPE_TTREE;
        pstIdxLst[i].ucUniqueFlag = 0;
        pstIdxLst[i].ucIdxFldNum = 1;
        pstIdxLst[i].aucFieldID[0] = i;
    }

    (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_NAME_LEN, "idx%d", i);
    pstIdxLst[i].enIndexType = DBDDL_INDEXTYPE_TTREE;
    pstIdxLst[i].ucUniqueFlag = 0;
    pstIdxLst[i].ucIdxFldNum = 2;
    pstIdxLst[i].aucFieldID[0] = 0;
    pstIdxLst[i].aucFieldID[1] = 1;

    i++;
    (void)sprintf_s((char *)pstIdxLst[i].aucIndexName, DB_NAME_LEN, "idx%d", i);
    pstIdxLst[i].enIndexType = DBDDL_INDEXTYPE_TTREE;
    pstIdxLst[i].ucUniqueFlag = 0;
    pstIdxLst[i].ucIdxFldNum = 2;
    pstIdxLst[i].aucFieldID[0] = 1;
    pstIdxLst[i].aucFieldID[1] = 2;

    (void)strncpy_s((char *)stRelDef.aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = 100;
    stRelDef.ulNCols = fldNum;
    stRelDef.pstFldLst = astFlds;
    stRelDef.ulNIdxs = idxNum;
    stRelDef.pstIdxLst = pstIdxLst;

    ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithInvalidFldDef)
{
    // 测试字段定义不合法
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    // 修改字段数量为0
    stRelDef.ulNCols = 0;
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_FIELDNUM, DB_CreateTbl(ulDbId, &stRelDef, &relId));

    // 修改字段数量大于上限（上限为255）
    stRelDef.ulNCols = 256;
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_FIELDNUM, DB_CreateTbl(ulDbId, &stRelDef, &relId));

    // 把字段释放
    stRelDef.ulNCols = 5;
    free(stRelDef.pstFldLst);
    stRelDef.pstFldLst = NULL;
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_FIELDNUM, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateAndDropTblNormal)
{
    // 表定义合法，建删表 - 成功
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    uint16_t usRelId = 0;
    Status ret = DB_CreateTbl(ulDbId, &stRelDef, &usRelId);
    TestFreeRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DB_DropTbl(ulDbId, usRelId, 0);
    EXPECT_EQ(ret, GMERR_OK);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateAndDropTblByIdNormal)
{
    // 表定义合法，指定Id建表，删表 - 成功
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    uint16_t usRelId = 3;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    TestFreeRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DB_DropTbl(ulDbId, usRelId, 0);
    EXPECT_EQ(ret, GMERR_OK);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblExceedUpperLimit)
{
    TPC_UnInit();
    system("sed -i \"s/^maxNormalTableNum =.*$/maxNormalTableNum = 1000/g\" ./config/st_emb_simprel.ini");
    EXPECT_EQ(GMERR_OK, TPC_Init("./config/st_emb_simprel.ini"));
    system("sed -i \"s/^maxNormalTableNum =.*$/maxNormalTableNum = 65535/g\" ./config/st_emb_simprel.ini");
    // 测试表数量达到上限
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 1000; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        EXPECT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    }

    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
    (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, "exceed", DB_NAME_LEN);
    EXPECT_EQ(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblOver15K)
{
    TPC_UnInit();
    system("sed -i \"s/^maxNormalTableNum =.*$/maxNormalTableNum = 15000/g\" ./config/st_emb_simprel.ini");
    ASSERT_EQ(GMERR_OK, TPC_Init("./config/st_emb_simprel.ini"));
    system("sed -i \"s/^maxNormalTableNum =.*$/maxNormalTableNum = 65535/g\" ./config/st_emb_simprel.ini");
    // 测试表数量达到上限
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);

    uint16_t relId;
    char tblName[DB_NAME_LEN];
    for (uint32_t i = 0; i < 15000; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s(tblName, DB_NAME_LEN, "tbl%u", i);
        (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
        ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    }

    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
    (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, "exceed", DB_NAME_LEN);
    EXPECT_EQ(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, DB_CreateTbl(ulDbId, &stRelDef, &relId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestDropNonExistTbl)
{
    // 删不存在的表 - 失败
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t usRelId = 3;
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDREL, DB_DropTbl(ulDbId, usRelId, 0));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestModTableId)
{
    // 仅修改表ID
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    uint16_t usRelId = 0;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    TestFreeRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t newRelId = 3;
    EXPECT_EQ(GMERR_OK, DB_ModifyTblNameAndID(ulDbId, usRelId, NULL, newRelId));
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDREL, DB_DropTbl(ulDbId, usRelId, 0));
    EXPECT_EQ(GMERR_OK, DB_DropTbl(ulDbId, newRelId, 0));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestModTableName)
{
    // 仅修改表名字
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    uint16_t usRelId = 3;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    TestFreeRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t *newRelName = (uint8_t *)"newName";
    EXPECT_EQ(GMERR_OK, DB_ModifyTblNameAndID(ulDbId, usRelId, newRelName, 0xFFFF));
    EXPECT_EQ(GMERR_OK, DB_DropTbl(ulDbId, usRelId, 0));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestModTableIdAndName)
{
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);

    uint32_t tblNum;
    DB_REL_DEF_STRU stRelDefs[5];
    ASSERT_EQ(GMERR_OK,
        StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/ddl/testModTblNameAndIdReldef.json", stRelDefs, &tblNum));
    ASSERT_EQ(5, tblNum);

    uint16_t relIds[5] = {1, 2, 3, 4, 5};
    for (uint32_t i = 0; i < tblNum; i++) {
        ASSERT_EQ(GMERR_OK, DB_CreateTblByID(ulDbId, relIds[i], &stRelDefs[i]));
    }
    ASSERT_EQ(GMERR_OK, DB_ModifyTblNameAndID(ulDbId, 4, NULL, 0));
    StUtilFreeAllRelDef(stRelDefs, tblNum);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestSetMaxRecNumOfTable)
{
    // 修改表的最大记录数，原始最大为100
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    uint16_t usRelId = 3;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    TestFreeRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);

    EXPECT_EQ(GMERR_OK, DB_SetMaxRecNumOfTable(ulDbId, usRelId, 0, 0, 101));
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_MAX_REC, DB_SetMaxRecNumOfTable(ulDbId, usRelId, 0, 0, 100));
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDREL, DB_SetMaxRecNumOfTable(ulDbId, 6, 0, 0, 100));
    EXPECT_EQ(GMERR_OK, DB_DropTbl(ulDbId, usRelId, 0));
    CommonDBDropDB4Test(NULL, ulDbId);
}

uint32_t g_testDbDdlDbId = 0;
void *DBDDLCreateTblProc(void *args)
{
    Status ret;
    for (int i = 0; i < 10; i++) {
        uint16_t testRelId = 0;
        DB_REL_DEF_STRU testRelDef = {0};
        DBDDLTestCreateTblInitRelDefWithoutIdx(&testRelDef, NULL);

        // 建表
        ret = DB_CreateTbl(g_testDbDdlDbId, &testRelDef, &testRelId);
        if (ret != GMERR_OK) {
            EXPECT_NE(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
        TestFreeRelDef(&testRelDef);
    }
    return NULL;
}

void *DBDDLCreateTblByIdProc(void *args)
{
    Status ret;
    for (int i = 0; i < 10; i++) {
        uint16_t testRelId = 3;
        DB_REL_DEF_STRU testRelDef = {0};
        DBDDLTestCreateTblInitRelDefWithoutIdx(&testRelDef, "createById");
        // 指定表ID建表
        ret = DB_CreateTblByID(g_testDbDdlDbId, testRelId, &testRelDef);
        if (ret != GMERR_OK) {
            EXPECT_NE(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
        TestFreeRelDef(&testRelDef);
    }
    return NULL;
}

void *DBDDLModifyTblNameAndIdProc(void *args)
{
    Status ret;
    for (int i = 0; i < 10; i++) {
        // 修改表名和表ID
        uint32_t newRelId = i;
        uint16_t testRelId = 0;
        const char newTblName[] = "newTblName";
        ret = DB_ModifyTblNameAndID(g_testDbDdlDbId, testRelId, (VOS_UINT8 *)newTblName, newRelId);
        if (ret != GMERR_OK) {
            EXPECT_NE(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}

void *DBDDLSetMaxRecNumOfTableProc(void *args)
{
    Status ret;
    for (int i = 0; i < 10; i++) {
        // 修改表的最大记录数
        uint16_t testRelId = 0;
        uint32_t ulNewMaxRecNum = 10001 + i;
        ret = DB_SetMaxRecNumOfTable(g_testDbDdlDbId, testRelId, 0, 0, ulNewMaxRecNum);
        if (ret != GMERR_OK) {
            EXPECT_NE(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}

void *DBDDLSetExtendRecNumProc(void *args)
{
    Status ret;
    for (int i = 0; i < 10; i++) {
        // 设置扩容
        uint16_t testRelId = 0;
        ret = DB_SetExtendRecNum(g_testDbDdlDbId, testRelId, 0);
        if (ret != GMERR_OK) {
            EXPECT_NE(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}

void *DBDDLDropTblProc(void *args)
{
    Status ret;
    for (int i = 0; i < 10; i++) {
        // 删除表
        uint16_t testRelId = 0;
        ret = DB_DropTbl(g_testDbDdlDbId, testRelId, 0);
        if (ret != GMERR_OK) {
            EXPECT_NE(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}

// 并发一个线程一个接口循环调所有接口（同一张表），预期无core
TEST_F(StSimpRelDBDDL, DBDDLTestTblApiConncurrentInvacation)
{
    Status ret;
    for (uint32_t i = 0; i < 10; i++) {
        CommonDBCreateDB4Test(NULL, &g_testDbDdlDbId);
        pthread_t tid1, tid2, tid3, tid4, tid5, tid6;
        ret = pthread_create(&tid1, NULL, DBDDLCreateTblProc, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid2, NULL, DBDDLCreateTblByIdProc, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid3, NULL, DBDDLModifyTblNameAndIdProc, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid4, NULL, DBDDLSetMaxRecNumOfTableProc, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid5, NULL, DBDDLSetExtendRecNumProc, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid6, NULL, DBDDLDropTblProc, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_join(tid1, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_join(tid2, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_join(tid3, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_join(tid4, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_join(tid5, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_join(tid6, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        CommonDBDropDB4Test(NULL, g_testDbDdlDbId);
    }
}

TEST_F(StSimpRelDBDDL, DBDDLTestTblWithInvalidFieldLen)
{
    // 表字段的定义长度不符合约束
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    stRelDef.ulNCols = 1;
    uint32_t typesNum = 7;
    const DB_DATATYPE_ENUM_V1 dataTypes[typesNum] = {
        DBT_BIT, DBT_BYTES, DBT_VBYTES, DBT_STRING, DBT_MIBSTR, DBT_BCD, DBT_BLOCK};
    const uint32_t invalidFldSizes[typesNum] = {33, 65534, 65534, 65535, 65535, 0, 0};
    uint16_t usRelId;
    for (uint32_t i = 0; i < typesNum; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl", i);
        stRelDef.pstFldLst[0].enDataType = dataTypes[i];
        stRelDef.pstFldLst[0].usSize = invalidFldSizes[i];
        EXPECT_EQ(VOS_ERRNO_DB_INVALIDFLDLEN, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    }

    // 数据长度超过最大值
    stRelDef.ulNCols = 2;
    stRelDef.pstFldLst[0].enDataType = dataTypes[0];
    stRelDef.pstFldLst[0].usSize = 32;
    stRelDef.pstFldLst[1].enDataType = dataTypes[6];
    stRelDef.pstFldLst[1].usSize = 65532;
    EXPECT_EQ(VOS_ERRNO_DB_MAXRECLEN_EXCEEDED, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithHugeFieldLen)
{
    // 设置固定长度类型字段的长度为随机值（记录长度超过表上限），结果不报错
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    stRelDef.ulNCols = 4;
    uint32_t typesNum = 4;
    const DB_DATATYPE_ENUM_V1 dataTypes[typesNum] = {
        DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_MIBSTR, DBT_BCD, DBT_BLOCK};
    const uint32_t invalidFldSizes[typesNum] = {20, 35552, 2202, 35525};
    uint16_t usRelId;
    for (uint32_t i = 0; i < typesNum; i++) {
        (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl", i);
        stRelDef.pstFldLst[i].enDataType = dataTypes[i];
        stRelDef.pstFldLst[i].usSize = invalidFldSizes[i];
    }

    EXPECT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithStrEndSymbol)
{
    // 表定义合法，建删表 - 成功
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    (void)memset_s(stRelDef.pstFldLst[0].aucFieldName, DB_NAME_LEN, '\0', DB_NAME_LEN);
    uint16_t usRelId;
    EXPECT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    (void)memset_s(stRelDef.aucRelName, DB_NAME_LEN, '\0', DB_NAME_LEN);

    (void)memcpy_s(stRelDef.aucRelName, DB_NAME_LEN, "testTbl", strlen("testTbl"));
    (void)memset_s(stRelDef.pstFldLst[1].aucFieldName, DB_NAME_LEN, '\0', DB_NAME_LEN);
    EXPECT_EQ(VOS_ERRNO_DB_FIELD_REPEATED, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    TestFreeRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

// 从文件读取表结构
TEST_F(StSimpRelDBDDL, DBDDLTestGetTblDefFromFile)
{
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef[3];
    uint32_t defNum = 3;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./util/tbldefs/example.json", stRelDef, &defNum));
    ASSERT_EQ(2, defNum);

    uint16_t relIds[2];
    for (uint32_t i = 0; i < defNum; i++) {
        ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef[i], &relIds[i]));
    }

    StUtilFreeAllRelDef(stRelDef, defNum);
    CommonDBDropDB4Test(NULL, ulDbId);
}

static void DmlTestCreateTblInitIndexRelDef(DB_REL_DEF_STRU *stRelDef, const char *tblName)
{
    const char tableName[] = "dmlTableName";
    const char indexName[] = "dmlIndexName";
    const uint32_t fldNum = 9;
    const uint32_t idxNum = 1;

    DB_FIELD_DEF_STRU *astFlds = (DB_FIELD_DEF_STRU *)malloc(fldNum * sizeof(DB_FIELD_DEF_STRU));
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_STRING, DBT_UINT32, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64, DBT_UINT64};
    const uint32_t fldSizes[fldNum] = {16, 4, 2, 4, 1, 2, 4, 8, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
        astFlds[i].ulDefVal = 0xFFFFFFFF;
    }

    DB_INDEX_DEF_STRU *astIdx = (DB_INDEX_DEF_STRU *)malloc(idxNum * sizeof(DB_INDEX_DEF_STRU));
    astIdx[0].ucUniqueFlag = 1;
    (void)strncpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 1;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_TEMP;
    stRelDef->ulIntialSize = g_defaultV1TestMaxRecordNums;
    stRelDef->ulMaxSize = g_defaultV1TestMaxRecordNums;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

static void DmlTestCreateEdgeDef(DB_EDGE_DEF_STRU *stEdgeDef, bool isPst, uint16_t label1, uint16_t label2,
    uint32_t fieldNum, uint8_t *field1, uint8_t *field2)
{
    stEdgeDef->isPersistent = isPst;
    stEdgeDef->relInfos[0].relId = label1;
    stEdgeDef->relInfos[0].FieldNum = fieldNum;
    for (uint32_t i = 0; i < fieldNum; i++) {
        stEdgeDef->relInfos[0].aucField[i] = field1[i];
    }
    stEdgeDef->relInfos[1].relId = label2;
    stEdgeDef->relInfos[1].FieldNum = fieldNum;
    for (uint32_t i = 0; i < fieldNum; i++) {
        stEdgeDef->relInfos[1].aucField[i] = field2[i];
    }
}

TEST_F(StSimpRelDBDDL, DBDDLTestCloseAndOpenPersistDB)
{
    uint32_t ulRet;
    DB_INST_CONFIG_STRU pstCfg = {0};
    pstCfg.ulInitialSize = 1024 * 1024;
    pstCfg.ulTempSize = 1024 * 1024;
    pstCfg.ulExtendSize = 1024 * 1024;
    pstCfg.ulMaxDBDescInfoSize = 1043;
    pstCfg.enPersistent = DB_CKP_COMPLETE;
    uint8_t *dbName = (uint8_t *)"persist";
    uint8_t *dir = (uint8_t *)"./test.db2";

    uint32_t dbId;
    ulRet = DB_CreateDB(dbName, dir, &pstCfg);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    ulRet = DB_OpenDB(dir, dbName, &dbId);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    DB_REL_DEF_STRU testRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&testRelDef, "createById");
    uint16_t usRelId = 0;
    ulRet = DB_CreateTbl(dbId, &testRelDef, &usRelId);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    ulRet = DB_CloseDB(dbId);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    ulRet = DB_OpenDB(dir, dbName, &dbId);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    CommonDBDropDB4Test((char *)dbName, dbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestDBWithSamePersistPath)
{
    uint32_t ulRet = GMERR_OK;
    char filePath[256] = "./dcdm/data/VRP.bindata";
    char aucDBName[16] = "VRP";
    DB_INST_CONFIG_STRU stCfg = {0};
    stCfg.enPersistent = DB_CKP_INCR;
    ulRet = DB_CreateDB((uint8_t *)aucDBName, (uint8_t *)filePath, &stCfg);
    ASSERT_EQ(ulRet, GMERR_OK);

    ulRet = DB_CreateDB((uint8_t *)"VRP222", (uint8_t *)filePath, &stCfg);
    ASSERT_EQ(ulRet, VOS_ERRNO_DB_INVALIDINPUT);

    /* To Drop a DataBase */
    ulRet = DB_DropDB((uint8_t *)aucDBName, DROP_WAIT);
    ASSERT_EQ(ulRet, GMERR_OK);
}

// 测试设置无效的默认值
TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithInvalidDefaultValue)
{
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/common/tesRelDefWithAllType.json", &stRelDef));

    uint16_t usRelId;
    for (uint32_t i = 0; i < stRelDef.ulNCols; i++) {
        (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl%u", i);
        TestSetInvalidDefaultValue4RelDef(&stRelDef);
        switch (i) {
            case DBT_UINT16:
                stRelDef.pstFldLst[i].ulDefVal = 0xFFFF + 1;
                ASSERT_EQ(VOS_ERRNO_DB_INVALID_DEFVAL, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
                stRelDef.pstFldLst[i].ulDefVal = 0xFFFF;
                ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
                break;
            case DBT_UINT8:
            case DBT_STRING:
                stRelDef.pstFldLst[i].ulDefVal = 0xFF + 1;
                ASSERT_EQ(VOS_ERRNO_DB_INVALID_DEFVAL, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
                stRelDef.pstFldLst[i].ulDefVal = 0xFF;
                ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
                break;
            case DBT_MIBSTR:  // -1 跳过DBT_VSTRING不可用类型
                stRelDef.pstFldLst[i - 1].ulDefVal = 0xFF + 1;
                ASSERT_EQ(VOS_ERRNO_DB_INVALID_DEFVAL, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
                stRelDef.pstFldLst[i - 1].ulDefVal = 0xFF;
                ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
                break;
            default:
                break;
        }
    }
    StUtilFreeAllRelDef(&stRelDef);
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCloneTbl)
{
    // 测试建表入参为NULL
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);

    uint32_t dstDbId;
    DB_INST_CONFIG_STRU pstCfg = {0};
    uint8_t dstDbName[] = "cloneDb";
    ASSERT_EQ(GMERR_OK, DB_CreateDB(dstDbName, NULL, &pstCfg));
    ASSERT_EQ(GMERR_OK, DB_OpenDB(NULL, dstDbName, &dstDbId));  // replace

    DB_REL_DEF_STRU testRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&testRelDef, "createTable");
    uint16_t usRelId = 0;
    uint32_t ulRet = DB_CreateTbl(ulDbId, &testRelDef, &usRelId);
    ASSERT_EQ(DB_SUCCESS, ulRet);
    uint16_t dstRelId = 10;
    ulRet = DB_CloneTable(ulDbId, usRelId, dstDbId, dstRelId);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    ulRet = DB_DropTbl(dstDbId, dstRelId, 0);
    ASSERT_EQ(DB_SUCCESS, ulRet);

    ulRet = DB_CloseDB(dstDbId);
    ASSERT_EQ(DB_SUCCESS, ulRet);
    ASSERT_EQ(DB_SUCCESS, DB_DropDB(dstDbName, 1));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestSetMaxRecNumOfTableDTS2025021110673)
{
    // 修改表的最大记录数，原始最大为100
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    DBDDLTestCreateTblInitRelDefWithoutIdx(&stRelDef, NULL);
    uint16_t usRelId = 3;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    TestFreeRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t maxRecord = 0xFFFFFFFF - 3;
    EXPECT_EQ(GMERR_OK, DB_SetMaxRecNumOfTable(ulDbId, usRelId, 0, 0, maxRecord));
    maxRecord += 1;
    EXPECT_EQ(VOS_ERRNO_DB_INVALID_MAX_REC, DB_SetMaxRecNumOfTable(ulDbId, usRelId, 0, 0, maxRecord));
    EXPECT_EQ(GMERR_OK, DB_DropTbl(ulDbId, usRelId, 0));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestRelIdxWith20Flds)
{
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/ddl/testTblWithIdsHas20Flds.json", &stRelDef));
    ASSERT_EQ(stRelDef.pstIdxLst[0].ucIdxFldNum, 20);
    uint16_t usRelId = 3;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    StUtilFreeAllRelDef(&stRelDef);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GMERR_OK, DB_DropTbl(ulDbId, usRelId, 0));
    CommonDBDropDB4Test(NULL, ulDbId);
}

TEST_F(StSimpRelDBDDL, DBDDLTestCreateTblWithTpcDB)
{
    uint32_t ulDbId;
    CommonCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/ddl/testTblWithIdsHas20Flds.json", &stRelDef));
    ASSERT_EQ(stRelDef.pstIdxLst[0].ucIdxFldNum, 20);
    uint16_t usRelId = 3;
    Status ret = DB_CreateTblByID(ulDbId, usRelId, &stRelDef);
    StUtilFreeAllRelDef(&stRelDef);
    EXPECT_EQ(ret, VOS_ERRNO_DB_INVALID_DATABASE);
    CommonDropDB4Test(NULL, ulDbId);
}
