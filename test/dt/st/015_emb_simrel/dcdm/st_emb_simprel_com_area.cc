/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_simprel_com_area.cc
 * Description:
 * Create:
 */
#include <string>
#include <cctype>
#include "st_emb_simprel_com.h"

using namespace std;

static uint32_t g_comDbId;
class StSimpRelComArea : public StSimpRel {
protected:
    virtual void SetUp()
    {
        CommonCreateDB4Test(NULL, &g_comDbId);
    }
    virtual void TearDown()
    {
        CommonDropDB4Test(NULL, g_comDbId);
    }
};

typedef enum { TBL_0 = 0, TBL_1, TBL_2 } TestTblIdE;

#pragma pack(1)
typedef struct {
    uint32_t f0;
    uint32_t f1;
    uint32_t f2;
    uint32_t f3;
    char f4[64];
} TestComAreaTbl0RecT;

typedef struct {
    uint32_t f0;
    char f1[16];
    uint8_t f2[256];
} TestComAreaTbl1RecT;

typedef struct {
    uint32_t f0;
    char f1[256];
    uint8_t f2[40];
} TestComAreaTbl2RecT;
#pragma pack()

void TestCommonAreaGenerateTbl(uint32_t dbId = g_comDbId, bool isTpc = true)
{
    Status ret = GMERR_OK;
    DB_REL_DEF_STRU stRelDef[3];
    uint32_t tblCnt = 3;
    ret = StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/common/tesComAreaRelDefs.json", stRelDef, &tblCnt);
    ASSERT_EQ(GMERR_OK, ret);
    if (isTpc) {
        ret = TPC_CreateTblByID(dbId, TBL_0, &stRelDef[0]);
        ASSERT_EQ(GMERR_OK, ret);
        ret = TPC_CreateTblByID(dbId, TBL_1, &stRelDef[1]);
        ASSERT_EQ(GMERR_OK, ret);
        ret = TPC_CreateTblByID(dbId, TBL_2, &stRelDef[2]);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ret = DB_CreateTblByID(dbId, TBL_0, &stRelDef[0]);
        ASSERT_EQ(GMERR_OK, ret);
        ret = DB_CreateTblByID(dbId, TBL_1, &stRelDef[1]);
        ASSERT_EQ(GMERR_OK, ret);
        ret = DB_CreateTblByID(dbId, TBL_2, &stRelDef[2]);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestCommonAreaSetOneRec(uint32_t base, uint8_t *rec, uint16_t relId)
{
    if (relId == TBL_0) {
        TestComAreaTbl0RecT *rec0 = (TestComAreaTbl0RecT *)rec;
        rec0->f0 = base;
        rec0->f1 = base;
        rec0->f2 = base;
        rec0->f3 = base;
        (void)sprintf_s(rec0->f4, sizeof(rec0->f4), "str_val%u", base);
    } else if (relId == TBL_1) {
        TestComAreaTbl1RecT *rec1 = (TestComAreaTbl1RecT *)rec;
        rec1->f0 = base;
        (void)sprintf_s(rec1->f1, sizeof(rec1->f1), "str_val%u", base);
        (void)memset_s(rec1->f2, sizeof(rec1->f2), (uint8_t)base, sizeof(rec1->f2));
    } else {
        TestComAreaTbl2RecT *rec2 = (TestComAreaTbl2RecT *)rec;
        rec2->f0 = base;
        (void)sprintf_s(rec2->f1, sizeof(rec2->f1), "str_val%u", base);
        (void)memset_s(rec2->f2, sizeof(rec2->f2), (uint8_t)base, sizeof(rec2->f2));
    }
}

void TestCommonAreaGenerateSetData(uint32_t *limitCnts, uint32_t dbId = g_comDbId, bool isTpc = true)
{
    Status ret = GMERR_OK;
    TestComAreaTbl0RecT tbl0Rec;
    TestComAreaTbl1RecT tbl1Rec;
    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf;
    uint32_t innerLimitCnts[3] = {50};
    if (limitCnts != NULL) {
        uint32_t copySize = 3 * sizeof(uint32_t);
        (void)memcpy_s(innerLimitCnts, copySize, limitCnts, copySize);
    }

    for (uint16_t i = 0; i < 3; i++) {
        switch (i) {
            case 0:
                dsBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
                dsBuf.StdBuf.pucData = (uint8_t *)&tbl0Rec;
                break;
            case 1:
                dsBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
                dsBuf.StdBuf.pucData = (uint8_t *)&tbl1Rec;
                break;
            case 2:
                dsBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
                dsBuf.StdBuf.pucData = (uint8_t *)&tbl2Rec;
                break;
            default:
                break;
        }
        for (uint32_t j = 0; j < innerLimitCnts[i]; j++) {
            TestCommonAreaSetOneRec(j, dsBuf.StdBuf.pucData, i);
            ret = isTpc ? TPC_InsertRec(TPC_GLOBAL_CDB, dbId, i, &dsBuf) : DB_InsertRec(dbId, i, &dsBuf);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

TEST_F(StSimpRelComArea, TestComAreaGenerateFile)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    uint32_t insertCnt[] = {10, 5000, 5};
    TestCommonAreaGenerateSetData(insertCnt);

    uint8_t filePath[64] = "./commonArea.db2";
    ret = TPC_BkpPhy(g_comDbId, filePath);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StSimpRelComArea, TestComAreaRestoreAndBkp)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    uint8_t filePath[64] = "./commonArea.db2";
    VOS_UINT8 binDbName[] = "comArDB";
    ret = TPC_Restore(filePath, binDbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t dbId;
    ret = TPC_OpenDB(NULL, binDbName, &dbId);
    ASSERT_EQ(ret, GMERR_OK);

    uint8_t filePath2[64] = "./commonArea_bkp.db2";
    ret = TPC_BkpPhy(dbId, filePath2);
    ASSERT_EQ(ret, GMERR_OK);

    CommonDropDB4Test((char *)binDbName, dbId);
}

TEST_F(StSimpRelComArea, TestComAreaRestoreAndSysview)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    uint8_t filePath[64] = "./commonArea.db2";
    VOS_UINT8 binDbName[] = "comArDB";
    ret = TPC_Restore(filePath, binDbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t dbId;
    ret = TPC_OpenDB(NULL, binDbName, &dbId);
    ASSERT_EQ(ret, GMERR_OK);

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = dbId;
    VOS_UINT8 *result = NULL;

    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);

    CommonDropDB4Test((char *)binDbName, dbId);
}

TEST_F(StSimpRelComArea, TestComAreaRestoreAndDml)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    uint8_t filePath[64] = "./commonArea.db2";
    VOS_UINT8 binDbName[] = "comArDB";
    ret = TPC_Restore(filePath, binDbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t dbId;
    ret = TPC_OpenDB(NULL, binDbName, &dbId);
    ASSERT_EQ(ret, GMERR_OK);

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf;
    dsBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf.StdBuf.pucData = (uint8_t *)&tbl0Rec;
    for (uint32_t j = 10; j < 20; j++) {
        TestCommonAreaSetOneRec(j, dsBuf.StdBuf.pucData, 0);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, dbId, 0, &dsBuf);
        ASSERT_EQ(GMERR_OK, ret);
    }

    CommonDropDB4Test((char *)binDbName, dbId);
}

TEST_F(StSimpRelComArea, TestComAreaRestoreAndCdb)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    uint8_t filePath[64] = "./commonArea.db2";
    VOS_UINT8 binDbName[] = "comArDB";
    ret = TPC_Restore(filePath, binDbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t dbId;
    ret = TPC_OpenDB(NULL, binDbName, &dbId);
    ASSERT_EQ(ret, GMERR_OK);

    ret = TPC_BeginCDBByID(dbId, 0);
    ASSERT_EQ(ret, GMERR_OK);

    ret = TPC_BeginCDBByID(dbId, 1);
    ASSERT_EQ(ret, GMERR_OK);

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf;
    dsBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf.StdBuf.pucData = (uint8_t *)&tbl0Rec;
    for (uint32_t j = 10; j < 20; j++) {
        TestCommonAreaSetOneRec(j, dsBuf.StdBuf.pucData, TBL_0);
        ret = TPC_InsertRec(0, dbId, TBL_0, &dsBuf);
        ASSERT_EQ(GMERR_OK, ret);
    }

    TestComAreaTbl2RecT tbl2Rec;
    dsBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf.StdBuf.pucData = (uint8_t *)&tbl2Rec;
    for (uint32_t j = 10; j < 20; j++) {
        TestCommonAreaSetOneRec(j, dsBuf.StdBuf.pucData, TBL_2);
        ret = TPC_InsertRec(1, dbId, TBL_2, &dsBuf);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = TPC_CommitCDB(1);
    ASSERT_EQ(ret, GMERR_OK);

    ret = TPC_RollbackCDB(0);
    ASSERT_EQ(ret, GMERR_OK);

    DB_COND_STRU stCond = {.usCondNum = 0};
    TestComAreaTbl2RecT tbl2Recs[20];
    DB_BUF_STRU selBuf;
    selBuf.ulRecNum = 20;
    selBuf.ulBufLen = 20 * sizeof(TestComAreaTbl2RecT);
    selBuf.pBuf = (void *)tbl2Recs;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, dbId, TBL_2, &stCond, &g_stFldFilter, &selBuf);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(selBuf.ulRecNum, 15);

    TestComAreaTbl0RecT tbl0Recs[20];
    selBuf.ulRecNum = 20;
    selBuf.ulBufLen = 20 * sizeof(TestComAreaTbl0RecT);
    selBuf.pBuf = (void *)tbl0Recs;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, dbId, TBL_0, &stCond, &g_stFldFilter, &selBuf);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(selBuf.ulRecNum, 10);

    CommonDropDB4Test((char *)binDbName, dbId);
}

TEST_F(StSimpRelComArea, TestComAreaFetchSelect)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    uint32_t insertCnt[] = {10, 50, 5};
    TestCommonAreaGenerateSetData(insertCnt);

    DB_COND_STRU stCond = {0};
    DB_SELHANDLE selHdl;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_comDbId, 0, &stCond, &g_stFldFilter, &selHdl);
    ASSERT_EQ(ret, GMERR_OK);

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU selBuf = {0};
    selBuf.StdBuf.ulBufLen = sizeof(TestComAreaTbl0RecT);
    selBuf.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    for (uint32_t i = 0; i < 10; i++) {
        selBuf.usRecNum = 1;
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selHdl, &selBuf);
        ASSERT_EQ(ret, GMERR_OK);
        printf("%u %s\n", tbl0Rec.f0, tbl0Rec.f4);
    }
    ASSERT_EQ(tbl0Rec.f0, 9);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selHdl);
    ASSERT_EQ(ret, GMERR_OK);

    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_comDbId, 2, &stCond, &g_stFldFilter, &selHdl);
    ASSERT_EQ(ret, GMERR_OK);

    TestComAreaTbl2RecT tbl2Rec;
    selBuf.StdBuf.ulBufLen = sizeof(TestComAreaTbl2RecT);
    selBuf.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    for (uint32_t i = 0; i < 5; i++) {
        selBuf.usRecNum = 1;
        ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, selHdl, &selBuf);
        ASSERT_EQ(ret, GMERR_OK);
        printf("%u %s\n", tbl2Rec.f0, tbl2Rec.f1);
    }
    ASSERT_EQ(tbl2Rec.f0, 4);

    ret = TPC_EndSelect(TPC_GLOBAL_CDB, selHdl);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StSimpRelComArea, TestComAreaCheckDbRel)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    uint32_t insertCnt[] = {10, 10, 20};
    TestCommonAreaGenerateSetData(insertCnt);

    char filePath[64] = "./testComArae.db2";
    ASSERT_EQ(DB_SUCCESS, DB_BkpPhy(g_comDbId, (VOS_UINT8 *)filePath));

    DB_REG_FEATURE_STRU stFeature = {0};
    uint16_t usGrowthRate = 50;
    VOS_UINT32 ulMaxStepSize = 100;
    ret = DB_RegFlexExtendFeature(usGrowthRate, ulMaxStepSize, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    DB_INST_CONFIG_STRU stCfg = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    DB_GetTblConvHook pfnTblConv = NULL;
    ret = DB_RegTableConvFunc(pfnTblConv, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_DISCARD;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    VOS_BOOL bPersistent = 0;
    ret = DB_RegDbRestoreConfig(bPersistent, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_RAM;
    ret = DB_RegDbDataStorage(enStorage, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    VOS_BOOL bChecksumEnable = VOS_TRUE;
    ret = DB_RegCheckSumFeature(bChecksumEnable, NULL, NULL, NULL, &stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t dbName[DB_NAME_LEN] = "checkDB";
    ASSERT_EQ(DB_SUCCESS, DB_RestoreDB2((uint8_t *)filePath, dbName, NULL, &stFeature));
    ret = DB_FreeRegFeatureStruct(&stFeature);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t ulDbId = 0;
    ASSERT_EQ(DB_SUCCESS, DB_OpenDB(NULL, dbName, &ulDbId));
    uint16_t failIdList[] = {TBL_0, TBL_1, TBL_2};
    uint32_t failCnt = 3;
    failIdList[0] = 0;
    ASSERT_EQ(DB_SUCCESS, DB_CheckDBAllRelDes(ulDbId, failIdList, &failCnt));
    ASSERT_EQ(0, failCnt);

    CommonDBDropDB4Test((char *)dbName, ulDbId);
    DBClearPersistentFile(filePath);
}

TEST_F(StSimpRelComArea, TestComAreaDmlAndCheck)
{
    Status ret = GMERR_OK;
    DB_REG_FEATURE_STRU stFeature = {0};
    DB_INST_CONFIG_STRU stCfg = {0};
    ret = DB_RegDbConfigStructure(&stCfg, &stFeature);
    ASSERT_EQ(ret, GMERR_OK);

    VOS_BOOL bPersistent = 0;
    ret = DB_RegDbRestoreConfig(bPersistent, &stFeature);
    ASSERT_EQ(ret, GMERR_OK);

    DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_RAM;
    ret = DB_RegDbDataStorage(enStorage, &stFeature);
    ASSERT_EQ(ret, GMERR_OK);

    DBHOOK_CheckSumCalc_t pfCalcChecksum = NULL;
    DBHOOK_CheckSumNegate_t pfNegateChecksum = NULL;
    DBHOOK_CheckSumCompare_t pfCompChecksum = NULL;
    VOS_BOOL bChecksumEnable = 3;
    bChecksumEnable = VOS_TRUE;
    ret = DB_RegCheckSumFeature(bChecksumEnable, pfCalcChecksum, pfNegateChecksum, pfCompChecksum, &stFeature);
    ASSERT_EQ(ret, GMERR_OK);

    uint8_t dbName[DB_NAME_LEN] = "checSumDB";
    ret = DB_CreateDB2(dbName, NULL, &stFeature);
    ASSERT_EQ(ret, GMERR_OK);

    ret = DB_FreeRegFeatureStruct(&stFeature);
    ASSERT_EQ(ret, GMERR_OK);

    uint32_t ulDbId;
    ret = DB_OpenDB(NULL, dbName, &ulDbId);
    ASSERT_EQ(ret, GMERR_OK);

    TestCommonAreaGenerateTbl(ulDbId, false);
    uint32_t insertCnt[] = {10, 10, 20};
    TestCommonAreaGenerateSetData(insertCnt, ulDbId, false);

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU incBuf = {0};
    incBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    incBuf.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU udpBuf = {0};
    udpBuf.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    udpBuf.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_COND_STRU stCond = {.usCondNum = 1};
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;
    uint32_t affectRows = 0;
    for (uint32_t i = 0; i < 5; i++) {
        *(uint32_t *)stCond.aCond[0].aucValue = i;
        ret = DB_DeleteRec(ulDbId, TBL_0, &stCond, &affectRows);
        ASSERT_EQ(ret, GMERR_OK);
        ASSERT_EQ(affectRows, 1);

        TestCommonAreaSetOneRec(i + 20, incBuf.StdBuf.pucData, TBL_1);
        ret = DB_InsertRec(ulDbId, TBL_1, &incBuf);
        ASSERT_EQ(ret, GMERR_OK);

        TestCommonAreaSetOneRec(1 + 30, udpBuf.StdBuf.pucData, TBL_2);
        ret = DB_UpdateRec(ulDbId, TBL_2, &stCond, &g_stFldFilter, &udpBuf, &affectRows);
        ASSERT_EQ(ret, GMERR_OK);
        ASSERT_EQ(affectRows, 1);
    }

    uint16_t failIdList[] = {TBL_0, TBL_1, TBL_2};
    uint32_t failCnt = 3;
    failIdList[0] = 0;
    ASSERT_EQ(DB_SUCCESS, DB_CheckDBAllRelDes(ulDbId, failIdList, &failCnt));
    ASSERT_EQ(0, failCnt);

    CommonDBDropDB4Test((char *)dbName, ulDbId);
}

void FindTargetStr(string sourceStr, string targetStr, uint64_t *value)
{
    // 1. 查找"size: "的起始位置
    size_t pos = sourceStr.find(targetStr);
    if (pos != std::string::npos) {
        pos += targetStr.size();  // 移到数字起始位置
        std::string numberStr;

        // 2. 提取连续的数字字符
        while (pos < sourceStr.size() && std::isdigit(sourceStr[pos])) {
            numberStr += sourceStr[pos++];
        }

        // 3. 将字符串转换为long long类型
        *value = std::stoll(numberStr);
    }
}

void TestCommonAreaGenerateTblWithIndex()
{
    Status ret = GMERR_OK;
    DB_REL_DEF_STRU stRelDef[3];
    uint32_t tblCnt = 3;
    ret = StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/common/tesComAreaRelDefsWithIndex.json", stRelDef, &tblCnt);
    ASSERT_EQ(GMERR_OK, ret);
    stRelDef[0].ulIntialSize = 0;
    stRelDef[1].ulIntialSize = 0;
    stRelDef[2].ulIntialSize = 0;
    ret = TPC_CreateTblByID(g_comDbId, TBL_0, &stRelDef[0]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = TPC_CreateTblByID(g_comDbId, TBL_1, &stRelDef[1]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = TPC_CreateTblByID(g_comDbId, TBL_2, &stRelDef[2]);
    ASSERT_EQ(GMERR_OK, ret);
}

static bool TestCommonAreaCheckOneRec(uint32_t base, uint8_t *rec, uint16_t relId)
{
    char formatStr[100] = {0};
    (void)sprintf_s(formatStr, 100, "str_val%u", base);
    if (relId == TBL_0) {
        TestComAreaTbl0RecT *rec0 = (TestComAreaTbl0RecT *)rec;
        if (rec0->f0 != base || rec0->f1 != base || rec0->f2 != base || rec0->f3 != base) {
            return false;
        }
        if (strcmp(rec0->f4, formatStr) != 0) {
            return false;
        }
    } else if (relId == TBL_1) {
        TestComAreaTbl1RecT *rec1 = (TestComAreaTbl1RecT *)rec;
        if (rec1->f0 != base) {
            return false;
        }
        if (strcmp(rec1->f1, formatStr) != 0) {
            return false;
        }
        if (*(uint8_t *)rec1->f2 != (uint8_t)base) {
            return false;
        }
    } else {
        TestComAreaTbl2RecT *rec2 = (TestComAreaTbl2RecT *)rec;
        if (rec2->f0 != base) {
            return false;
        }
        if (strcmp(rec2->f1, formatStr) != 0) {
            return false;
        }
        if (*(uint8_t *)rec2->f2 != (uint8_t)base) {
            return false;
        }
    }
    return true;
}

// 0号RDB 每张表插入 300条 数据， 预期 数据全部落在 共享空间
static void TestCommonAreaStep1(uint32_t recCnt)
{
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    uint32_t loop = recCnt;
    for (uint32_t j = 0; j < loop; j++) {
        TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0));
    }
    for (uint32_t j = 0; j < loop; j++) {
        TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1));
    }
    for (uint32_t j = 0; j < loop; j++) {
        TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2));
    }
    // 1000条数据查完后，应该查询不到 table1 table2 在共享空间之中
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    if (loop == 300) {
        ASSERT_TRUE(NULL != strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 300"));
        ASSERT_TRUE(NULL != strstr((char *)result, "index:1, table id: 1, table name: table2, record num: 300"));
        ASSERT_TRUE(NULL != strstr((char *)result, "index:2, table id: 2, table name: table3, record num: 300"));
    }

    TPC_FreeSysviewResult(&result);
}

// 对表 0 1 2 开启CDB 插入 数据 300， 使 1 2 触发共享空间的迁出， 验证迁出是否成功，全量数据是否正确
static void TestCommonAreaStep2()
{
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    uint32_t dataBase = 300;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));

    TestCommonAreaSetOneRec(dataBase, dsBuf0.StdBuf.pucData, TBL_0);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0));

    TestCommonAreaSetOneRec(dataBase, dsBuf1.StdBuf.pucData, TBL_1);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1));

    ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(10));

    TestCommonAreaSetOneRec(dataBase, dsBuf2.StdBuf.pucData, TBL_2);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2));

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_TRUE(NULL == strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 301"));
    ASSERT_TRUE(NULL == strstr((char *)result, "index:1, table id: 1, table name: table2, record num: 300"));
    ASSERT_TRUE(NULL == strstr((char *)result, "index:2, table id: 2, table name: table3, record num: 300"));

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;

    TestComAreaTbl0RecT tbl0Recs;
    DB_BUF_STRU selBuf0;
    selBuf0.ulRecNum = 1;
    selBuf0.ulBufLen = sizeof(TestComAreaTbl0RecT);
    selBuf0.pBuf = (void *)&tbl0Recs;

    TestComAreaTbl1RecT tbl1Recs;
    DB_BUF_STRU selBuf1;
    selBuf1.ulRecNum = 1;
    selBuf1.ulBufLen = sizeof(TestComAreaTbl1RecT);
    selBuf1.pBuf = (void *)&tbl1Recs;

    TestComAreaTbl2RecT tbl2Recs;
    DB_BUF_STRU selBuf2;
    selBuf2.ulRecNum = 1;
    selBuf2.ulBufLen = sizeof(TestComAreaTbl2RecT);
    selBuf2.pBuf = (void *)&tbl2Recs;

    for (uint32_t i = 0; i < 300; i++) {
        *(uint32_t *)stCond.aCond[0].aucValue = i;

        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &stCond, &g_stFldFilter, &selBuf0));
        ASSERT_EQ(1, selBuf0.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl0Recs, TBL_0));

        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf1));
        ASSERT_EQ(1, selBuf1.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl1Recs, TBL_1));

        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &g_stFldFilter, &selBuf2));
        ASSERT_EQ(1, selBuf2.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl2Recs, TBL_2));
    }

    TPC_FreeSysviewResult(&result);
}

// 删除表 0 ，1 ，2 各100条数据，插入50条数据， 更新 50 条数据， 验证数据是否正确，各表均正常DML操作
static void TestCommonAreaStep3()
{
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    // 删除 0 - 199 中 的奇数数据（100条）
    for (uint32_t i = 0; i <= 199; i++) {
        if (i % 2 == 0) {
            continue;
        }
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        *(uint32_t *)stCond.aCond[0].aucValue = i;

        uint32_t delRecNum = 0;
        ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &stCond, &delRecNum));
        ASSERT_EQ(1, delRecNum);
        ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum));
        ASSERT_EQ(1, delRecNum);
        ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum));
        ASSERT_EQ(1, delRecNum);
    }

    // 插入 0 - 50 中被 删除的奇数数据（25条），再插入301-325（25条）
    for (uint32_t i = 0; i < 50; i++) {
        if (i % 2 == 0) {
            continue;
        }
        TestCommonAreaSetOneRec(i, dsBuf0.StdBuf.pucData, TBL_0);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0));
        TestCommonAreaSetOneRec(i, dsBuf1.StdBuf.pucData, TBL_1);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1));
        TestCommonAreaSetOneRec(i, dsBuf2.StdBuf.pucData, TBL_2);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2));
    }
    for (uint32_t i = 301; i <= 325; i++) {
        TestCommonAreaSetOneRec(i, dsBuf0.StdBuf.pucData, TBL_0);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0));
        TestCommonAreaSetOneRec(i, dsBuf1.StdBuf.pucData, TBL_1);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1));
        TestCommonAreaSetOneRec(i, dsBuf2.StdBuf.pucData, TBL_2);
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2));
    }

    // 更新数据 将 200 - 299 中的 奇数数据 更新成 100 - 199 的奇数数据
    for (uint32_t i = 200; i <= 299; i++) {
        if (i % 2 == 0) {
            continue;
        }
        DB_COND_STRU pstCond;
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 0;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint32_t *)pstCond.aCond[0].aucValue = i;
        uint32_t pulRecNum = 0;
        DB_FIELDFILTER_STRU pstFldFilter;
        pstFldFilter.ucFieldNum = DB_FIELD_ALL;
        TestCommonAreaSetOneRec(i - 100, dsBuf0.StdBuf.pucData, TBL_0);
        ASSERT_EQ(
            GMERR_OK, TPC_UpdateRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &pstCond, &pstFldFilter, &dsBuf0, &pulRecNum));
        ASSERT_EQ(1u, pulRecNum);

        TestCommonAreaSetOneRec(i - 100, dsBuf1.StdBuf.pucData, TBL_1);
        ASSERT_EQ(
            GMERR_OK, TPC_UpdateRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &pstCond, &pstFldFilter, &dsBuf1, &pulRecNum));
        ASSERT_EQ(1u, pulRecNum);

        TestCommonAreaSetOneRec(i - 100, dsBuf2.StdBuf.pucData, TBL_2);
        ASSERT_EQ(
            GMERR_OK, TPC_UpdateRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &pstCond, &pstFldFilter, &dsBuf2, &pulRecNum));
        ASSERT_EQ(1u, pulRecNum);
    }

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;

    TestComAreaTbl0RecT tbl0Recs;
    DB_BUF_STRU selBuf0;
    selBuf0.ulRecNum = 1;
    selBuf0.ulBufLen = sizeof(TestComAreaTbl0RecT);
    selBuf0.pBuf = (void *)&tbl0Recs;

    TestComAreaTbl1RecT tbl1Recs;
    DB_BUF_STRU selBuf1;
    selBuf1.ulRecNum = 1;
    selBuf1.ulBufLen = sizeof(TestComAreaTbl1RecT);
    selBuf1.pBuf = (void *)&tbl1Recs;

    TestComAreaTbl2RecT tbl2Recs;
    DB_BUF_STRU selBuf2;
    selBuf2.ulRecNum = 1;
    selBuf2.ulBufLen = sizeof(TestComAreaTbl2RecT);
    selBuf2.pBuf = (void *)&tbl2Recs;

    // 验证数据正确性 (50 - 99)（200 - 300）的奇数没有，（0 - 325）
    for (uint32_t i = 0; i <= 325; i++) {
        if ((50 < i && i < 100) || (200 < i && i < 300)) {
            if (i % 2 == 1) {
                continue;
            }
        }
        *(uint32_t *)stCond.aCond[0].aucValue = i;

        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &stCond, &g_stFldFilter, &selBuf0));
        ASSERT_EQ(1, selBuf0.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl0Recs, TBL_0));

        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf1));
        ASSERT_EQ(1, selBuf1.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl1Recs, TBL_1));

        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &g_stFldFilter, &selBuf2));
        ASSERT_EQ(1, selBuf2.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl2Recs, TBL_2));
    }

    // 验证共享空间的数据情况
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_TRUE(NULL == strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 251"));

    TPC_FreeSysviewResult(&result);
}

// 删除表 1， 2 数据只剩下 4 条 0-3， CDB插入表 1 的 数据 10， RDB插入 表 2 的数据 10， 预期 表12回到共享空间
static void TestCommonAreaStep4()
{
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    for (uint32_t i = 4; i <= 325; i++) {
        if ((50 < i && i < 100) || (200 < i && i < 300)) {
            if (i % 2 == 1) {
                continue;
            }
        }
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        *(uint32_t *)stCond.aCond[0].aucValue = i;

        uint32_t delRecNum = 0;
        ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum));
        ASSERT_EQ(1, delRecNum);
    }
    for (uint32_t i = 4; i <= 325; i++) {
        if ((50 < i && i < 100) || (200 < i && i < 300)) {
            if (i % 2 == 1) {
                continue;
            }
        }
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        *(uint32_t *)stCond.aCond[0].aucValue = i;

        uint32_t delRecNum = 0;
        ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum));
        ASSERT_EQ(1, delRecNum);
    }

    // 验证共享空间的数据情况
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_TRUE(NULL == strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 251"));

    // 等待后台回收
    sleep(10);

    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));
    TestCommonAreaSetOneRec(10, dsBuf1.StdBuf.pucData, TBL_1);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1));
    ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(10));
    TestCommonAreaSetOneRec(10, dsBuf2.StdBuf.pucData, TBL_2);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2));

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_TRUE(NULL == strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 251"));
    ASSERT_TRUE(NULL != strstr((char *)result, "index:0, table id: 1, table name: table2, record num: 5"));
    ASSERT_TRUE(NULL != strstr((char *)result, "index:1, table id: 2, table name: table3, record num: 5"));

    TPC_FreeSysviewResult(&result);
}

// 删除表 1 ，2 的 0， 5 数据， 更新 数据6 为 66， 插入 数据 77， 验证数据正确 且 在共享空间中
static void TestCommonAreaStep5()
{
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;

    *(uint32_t *)stCond.aCond[0].aucValue = 0;

    uint32_t delRecNum = 0;
    ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum));
    ASSERT_EQ(1, delRecNum);
    ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum));
    ASSERT_EQ(1, delRecNum);

    *(uint32_t *)stCond.aCond[0].aucValue = 2;
    ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum));
    ASSERT_EQ(1, delRecNum);
    ASSERT_EQ(GMERR_OK, TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum));
    ASSERT_EQ(1, delRecNum);

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond.aCond[0].aucValue = 3;
    uint32_t pulRecNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    TestCommonAreaSetOneRec(66, dsBuf1.StdBuf.pucData, TBL_1);
    ASSERT_EQ(GMERR_OK, TPC_UpdateRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &pstCond, &pstFldFilter, &dsBuf1, &pulRecNum));
    ASSERT_EQ(1u, pulRecNum);

    TestCommonAreaSetOneRec(66, dsBuf2.StdBuf.pucData, TBL_2);
    ASSERT_EQ(GMERR_OK, TPC_UpdateRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &pstCond, &pstFldFilter, &dsBuf2, &pulRecNum));
    ASSERT_EQ(1u, pulRecNum);

    TestCommonAreaSetOneRec(77, dsBuf1.StdBuf.pucData, TBL_1);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1));
    TestCommonAreaSetOneRec(77, dsBuf2.StdBuf.pucData, TBL_2);
    ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2));

    // 验证共享空间的数据情况
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_TRUE(NULL == strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 251"));
    ASSERT_TRUE(NULL != strstr((char *)result, "index:0, table id: 1, table name: table2, record num: 4"));
    ASSERT_TRUE(NULL != strstr((char *)result, "index:1, table id: 2, table name: table3, record num: 4"));

    // 验证数据正确性
    TestComAreaTbl1RecT tbl1Recs;
    DB_BUF_STRU selBuf1;
    selBuf1.ulRecNum = 1;
    selBuf1.ulBufLen = sizeof(TestComAreaTbl1RecT);
    selBuf1.pBuf = (void *)&tbl1Recs;

    TestComAreaTbl2RecT tbl2Recs;
    DB_BUF_STRU selBuf2;
    selBuf2.ulRecNum = 1;
    selBuf2.ulBufLen = sizeof(TestComAreaTbl2RecT);
    selBuf2.pBuf = (void *)&tbl2Recs;

    // 验证数据正确性 (50 - 99)（200 - 300）的奇数没有，（0 - 325）
    for (uint32_t i = 0; i <= 77; i++) {
        if ((i <= 3 && i != 0 && i != 2 && i != 3) || i == 66 || i == 77) {
            *(uint32_t *)stCond.aCond[0].aucValue = i;

            ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf1))
                << i;
            ASSERT_EQ(1, selBuf1.ulRecNum);
            ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl1Recs, TBL_1));

            ASSERT_EQ(
                GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &g_stFldFilter, &selBuf2));
            ASSERT_EQ(1, selBuf2.ulRecNum);
            ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl2Recs, TBL_2));
        }
    }

    TPC_FreeSysviewResult(&result);
}

// 更新表1数据为其原数据 + 10000
static void TestCommonAreaStep6()
{
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    for (uint32_t i = 0; i < 1000; i++) {
        DB_COND_STRU pstCond;
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 0;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint32_t *)pstCond.aCond[0].aucValue = i;
        uint32_t pulRecNum = 0;
        DB_FIELDFILTER_STRU pstFldFilter;
        pstFldFilter.ucFieldNum = DB_FIELD_ALL;
        TestCommonAreaSetOneRec(i + 10000, dsBuf0.StdBuf.pucData, TBL_0);
        ASSERT_EQ(
            GMERR_OK, TPC_UpdateRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &pstCond, &pstFldFilter, &dsBuf0, &pulRecNum));
        ASSERT_EQ(1u, pulRecNum);
    }

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;

    TestComAreaTbl0RecT tbl0Recs;
    DB_BUF_STRU selBuf0;
    selBuf0.ulRecNum = 1;
    selBuf0.ulBufLen = sizeof(TestComAreaTbl0RecT);
    selBuf0.pBuf = (void *)&tbl0Recs;

    // 验证数据正确性 (50 - 99)（200 - 300）的奇数没有，（0 - 325）
    for (uint32_t i = 0; i < 1000; i++) {
        *(uint32_t *)stCond.aCond[0].aucValue = i + 10000;
        ASSERT_EQ(GMERR_OK, TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &stCond, &g_stFldFilter, &selBuf0));
        ASSERT_EQ(1, selBuf0.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i + 10000, (uint8_t *)&tbl0Recs, TBL_0));
    }
}

// 建表，插入数据，验证共享空间DFX能力
TEST_F(StSimpRelComArea, TestComAreaCase1)
{
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 10;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 正常场景
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    cout << "after insert data total DFX: " << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    char *pos = strstr((char *)result, "index:2, table id: 2, table name: table3, record num: 30, record len: 300");
    ASSERT_TRUE(NULL != pos);

    sysviewArgs.usRelNo = TBL_0;
    cout << "after insert data simgle DFX: " << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    pos = strstr((char *)result, "table id: 0, table name: table1, record num: 30, record len: 80");
    ASSERT_TRUE(NULL != pos);

    // 异常场景
    // DBid 正确 表id不正确
    sysviewArgs.usRelNo = 100;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    pos = strstr((char *)result, "common area details:table nums: 3");
    ASSERT_TRUE(NULL != pos);

    // 传入空指针
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, NULL));

    // 传入空指针
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, NULL));

    // DBid 不正确
    sysviewArgs.ulDbId = 100;
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));

    TPC_FreeSysviewResult(&result);
}

// 共享空间单表触发迁出场景
TEST_F(StSimpRelComArea, TestComAreaCase2)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    uint32_t stepSize = 100;
    uint32_t loop = 3;
    // A 100 B 100 C 100 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            if (j == 110) {
                ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));
                ret = TPC_InsertRec(10, g_comDbId, TBL_1, &dsBuf1);
                ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(10));
            } else {
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            }
            ASSERT_EQ(GMERR_OK, ret);
        }
        // i 为 0 时，应该有100条数据在共享空间之中
        if (i == 0) {
            ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
            cout << result << endl;
            char *pos = strstr((char *)result, "table id: 1, table name: table2, record num: 100");
            ASSERT_TRUE(NULL != pos);
        }
    }
    // 300条数据查完后，应该查询不到 table2 在共享空间之中
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    char *pos = strstr((char *)result, "index:0, table id: 1, table name: table2");
    ASSERT_TRUE(NULL == pos);

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;
    TestComAreaTbl1RecT tbl1Recs;
    DB_BUF_STRU selBuf;
    selBuf.ulRecNum = 1;
    selBuf.ulBufLen = sizeof(TestComAreaTbl1RecT);
    selBuf.pBuf = (void *)&tbl1Recs;
    for (uint32_t i = 0; i < stepSize * loop; i++) {
        *(uint32_t *)stCond.aCond[0].aucValue = i;
        ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, selBuf.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl1Recs, TBL_1));
    }

    TPC_FreeSysviewResult(&result);
}

// 共享空间多表表触发迁出场景
TEST_F(StSimpRelComArea, TestComAreaCase3)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    uint32_t stepSize = 100;
    uint32_t loop = 30;
    // A 100 B 100 C 100 -->循环30次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            if (j == 2100) {
                ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));
                ret = TPC_InsertRec(10, g_comDbId, TBL_0, &dsBuf0);
                ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(10));
            } else {
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            }
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            if (j == 110) {
                ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));
                ret = TPC_InsertRec(10, g_comDbId, TBL_1, &dsBuf1);
                ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(10));
            } else {
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            }
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
        // i 为 0 时，应该有100条数据在共享空间之中
        if (i == 0) {
            ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
            cout << result << endl;
            char *pos = strstr((char *)result, "index:0, table id: 0, table name: table1, record num: 100");
            ASSERT_TRUE(NULL != pos);
        }
    }
    // 3000条数据查完后，应该查询不到 table1 table2 在共享空间之中
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    char *pos = strstr((char *)result, "index:1, table id: 1, table name: table2");
    ASSERT_TRUE(NULL == pos);

    pos = strstr((char *)result, "index:0, table id: 0, table name: table1");
    ASSERT_TRUE(NULL == pos);

    // 查询验证
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;

    TestComAreaTbl0RecT tbl0Recs;
    DB_BUF_STRU selBuf0;
    selBuf0.ulRecNum = 1;
    selBuf0.ulBufLen = sizeof(TestComAreaTbl0RecT);
    selBuf0.pBuf = (void *)&tbl0Recs;

    TestComAreaTbl1RecT tbl1Recs;
    DB_BUF_STRU selBuf1;
    selBuf1.ulRecNum = 1;
    selBuf1.ulBufLen = sizeof(TestComAreaTbl1RecT);
    selBuf1.pBuf = (void *)&tbl1Recs;

    TestComAreaTbl2RecT tbl2Recs;
    DB_BUF_STRU selBuf2;
    selBuf2.ulRecNum = 1;
    selBuf2.ulBufLen = sizeof(TestComAreaTbl2RecT);
    selBuf2.pBuf = (void *)&tbl2Recs;

    for (uint32_t i = 0; i < stepSize * loop; i++) {
        *(uint32_t *)stCond.aCond[0].aucValue = i;

        ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &stCond, &g_stFldFilter, &selBuf0);
        ASSERT_EQ(GMERR_OK, ret) << i;
        ASSERT_EQ(1, selBuf0.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl0Recs, TBL_0));

        ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf1);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, selBuf1.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl1Recs, TBL_1));

        ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &g_stFldFilter, &selBuf2);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, selBuf2.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl2Recs, TBL_2));
    }

    TPC_FreeSysviewResult(&result);
}

// 共享空间单表触发迁出场景，删除数据触发迁入场景
TEST_F(StSimpRelComArea, TestComAreaCase4)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    uint32_t stepSize = 100;
    uint32_t loop = 3;
    // A 100 B 100 C 100 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            if (j == 110) {
                ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));
                ret = TPC_InsertRec(10, g_comDbId, TBL_1, &dsBuf1);
                ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(10));
            } else {
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            }
            ASSERT_EQ(GMERR_OK, ret);
        }
        // i 为 0 时，应该有100条数据在共享空间之中
        if (i == 0) {
            ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
            cout << result << endl;
            char *pos = strstr((char *)result, "table id: 1, table name: table2, record num: 100");
            ASSERT_TRUE(NULL != pos);
        }
    }
    // 300条数据查完后，应该查询不到 table2 在共享空间之中
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    char *pos = strstr((char *)result, "index:0, table id: 1, table name: table2");
    ASSERT_TRUE(NULL == pos);

    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 0;
    TestComAreaTbl1RecT tbl1Recs;
    DB_BUF_STRU selBuf;
    selBuf.ulRecNum = 1;
    selBuf.ulBufLen = sizeof(TestComAreaTbl1RecT);
    selBuf.pBuf = (void *)&tbl1Recs;
    for (uint32_t i = 0; i < stepSize * loop; i++) {
        *(uint32_t *)stCond.aCond[0].aucValue = i;
        ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, selBuf.ulRecNum);
        ASSERT_EQ(true, TestCommonAreaCheckOneRec(i, (uint8_t *)&tbl1Recs, TBL_1));
    }

    // 删除数据 只保留 6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize - 2; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
    }

    // 删除时使用的RDB事务提交是没法触发迁移的，只有新的事务开启时才能做迁入
    cout << "after delete :" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    pos = strstr((char *)result, "index:0, table id: 1, table name: table2");
    ASSERT_TRUE(NULL == pos);

    // 等待后台线程执行
    sleep(10);

    // 触发迁入共享空间
    TestCommonAreaSetOneRec(1000, dsBuf1.StdBuf.pucData, TBL_1);
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    pos = strstr((char *)result, "table id: 1, table name: table2");
    ASSERT_TRUE(NULL != pos);

    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize + stepSize - 2; j < i * stepSize + stepSize; j++) {
            *(uint32_t *)stCond.aCond[0].aucValue = j;
            ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &g_stFldFilter, &selBuf);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, selBuf.ulRecNum);
            ASSERT_EQ(true, TestCommonAreaCheckOneRec(j, (uint8_t *)&tbl1Recs, TBL_1));
        }
    }

    TPC_FreeSysviewResult(&result);
}

// 共享空间综合用例，验证多表场景迁入迁出的DML操作
TEST_F(StSimpRelComArea, TestComAreaCase5)
{
    // 建 3 张表 0 1 2
    TestCommonAreaGenerateTblWithIndex();

    // 0号RDB 每张表插入 300条 数据， 预期 数据全部落在 共享空间
    TestCommonAreaStep1(300);

    // 对表 0 1 2 开启CDB 插入 数据 300， 使 1 2 触发共享空间的迁出， 验证迁出是否成功，全量数据是否正确
    TestCommonAreaStep2();

    // 删除表 0 ，1 ，2 各100条数据，插入50条数据， 更新 50 条数据， 验证数据是否正确，各表均正常DML操作
    TestCommonAreaStep3();

    // 删除表 1， 2 数据只剩下 10 条 0-9， CDB插入表 1 的 数据 10， RDB插入 表 2 的数据 10， 预期 表12回到共享空间
    TestCommonAreaStep4();

    // 删除表 0 ，1 ，2 的 0， 5 数据， 更新 数据6 为 66， 插入 数据 77， 验证数据正确 且 在共享空间中
    TestCommonAreaStep5();
}

// 验证表压缩
TEST_F(StSimpRelComArea, TestComAreaCase6)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();

    sleep(10);

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    uint32_t stepSize = 10;
    uint32_t loop = 10;
    // A 10 B 10 C 10 -->循环10次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 再插入90000条表1，90条表0，900条表2
    for (uint32_t i = 10000; i < 100000; i++) {
        TestCommonAreaSetOneRec(i, dsBuf1.StdBuf.pucData, TBL_1);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
        ASSERT_EQ(GMERR_OK, ret);
        if (i % 1000 == 0) {
            TestCommonAreaSetOneRec(i, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);
        }
        if (i % 100 == 0) {
            TestCommonAreaSetOneRec(i, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "insert table 0 1 2:" << endl;
    // 取数据大小，共享空间占用大小和内存占用大小
    uint64_t totalInsertDataSize = 0;
    uint64_t totalInsertComAreaSize = 0;
    uint64_t totalInsertMemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    std::string strResult((char *)result);
    FindTargetStr(strResult, "total data size: ", &totalInsertDataSize);
    FindTargetStr(strResult, "total area size: ", &totalInsertComAreaSize);
    cout << "total data size: " << totalInsertDataSize << endl;
    cout << "total area size: " << totalInsertComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalInsertMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalInsertMemSize << endl;

    // 将前面的 各 100条数据删除，构造首页为空页的情况
    /****************共享空间表删完数据如何释放页*********************/
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
    }

    // 插空去删除 表1 数据，保留 45000条
    for (uint32_t i = 10000; i < 100000; i++) {
        if (i % 2 == 0) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = i;
            uint32_t delRecNum = 0;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
    }

    sleep(5);
    cout << "delete table 0 1 2: " << endl;
    // 删除数据记录共享空间开销和内存开销
    uint64_t totalDeleteDataSize = 0;
    uint64_t totalDeleteComAreaSize = 0;
    uint64_t totalDeleteMemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalDeleteDataSize);
    FindTargetStr(strResult, "total area size: ", &totalDeleteComAreaSize);
    cout << "total data size: " << totalDeleteDataSize << endl;
    cout << "total area size: " << totalDeleteComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalDeleteMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalDeleteMemSize << endl;

    sleep(5);
    // 压缩数据
    for (uint32_t i = 0; i < 100; i++) {
        DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = DBCompressTableNotifyFunc;
        ASSERT_EQ(DB_SUCCESS, TPC_CompressTable(g_comDbId, TBL_0, 10000, notifyFunc));
    }
    cout << "compress tabel0: " << endl;
    uint64_t totalCompressDataSize = 0;
    uint64_t totalCompressComAreaSize = 0;
    uint64_t totalCompressMemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalCompressDataSize);
    FindTargetStr(strResult, "total area size: ", &totalCompressComAreaSize);
    cout << "total data size: " << totalCompressDataSize << endl;
    cout << "total area size: " << totalCompressComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalCompressMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalCompressMemSize << endl;

    ASSERT_GE(totalInsertDataSize, totalDeleteDataSize);
    ASSERT_GE(totalDeleteMemSize, totalCompressMemSize);

    TPC_FreeSysviewResult(&result);
}

// 测迁移
TEST_F(StSimpRelComArea, TestComAreaCase7)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
#if TABLE12
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;
#endif
    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 30;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
#if TABLE12
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);
#endif
            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }
    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize - 2; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;
#if TABLE12
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
#endif
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_BeginCDBByID(g_comDbId, 100);
    TPC_RollbackCDB(100);

    TPC_FreeSysviewResult(&result);
}

// 测迁移
TEST_F(StSimpRelComArea, TestComAreaCase8)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 30;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }
    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize - 2; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// 测迁移和压缩
TEST_F(StSimpRelComArea, TestComAreaCase9)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 30;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }
    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize - 2; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// 验证表压缩
TEST_F(StSimpRelComArea, TestComAreaCase10)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    uint32_t stepSize = 10;
    uint32_t loop = 10;
    // A 10 B 10 C 10 -->循环10次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 再插入90000条表1，90条表0，900条表2
    for (uint32_t i = 10000; i < 100000; i++) {
        TestCommonAreaSetOneRec(i, dsBuf1.StdBuf.pucData, TBL_1);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
        ASSERT_EQ(GMERR_OK, ret);
        if (i % 1000 == 0) {
            TestCommonAreaSetOneRec(i, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);
        }
        if (i % 100 == 0) {
            TestCommonAreaSetOneRec(i, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "insert table 0 1 2:" << endl;
    // 取数据大小，共享空间占用大小和内存占用大小
    uint64_t totalInsertDataSize = 0;
    uint64_t totalInsertComAreaSize = 0;
    uint64_t totalInsertMemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    std::string strResult((char *)result);
    FindTargetStr(strResult, "total data size: ", &totalInsertDataSize);
    FindTargetStr(strResult, "total area size: ", &totalInsertComAreaSize);
    cout << "total data size: " << totalInsertDataSize << endl;
    cout << "total area size: " << totalInsertComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalInsertMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalInsertMemSize << endl;

    // 将前面的 各 100条数据删除，构造首页为空页的情况
    /****************共享空间表删完数据如何释放页*********************/
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
    }

    for (uint32_t i = 10500; i < 100000; i++) {
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        *(uint32_t *)stCond.aCond[0].aucValue = i;
        uint32_t delRecNum = 0;
        ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, delRecNum);
    }

    sleep(5);
    cout << "delete table 0 1 2: " << endl;
    // 删除数据记录共享空间开销和内存开销
    uint64_t totalDeleteDataSize = 0;
    uint64_t totalDeleteComAreaSize = 0;
    uint64_t totalDeleteMemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalDeleteDataSize);
    FindTargetStr(strResult, "total area size: ", &totalDeleteComAreaSize);
    cout << "total data size: " << totalDeleteDataSize << endl;
    cout << "total area size: " << totalDeleteComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalDeleteMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalDeleteMemSize << endl;

    sleep(10);
    // 压缩数据
    for (uint32_t i = 0; i < 1; i++) {
        DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = DBCompressTableNotifyFunc;
        ASSERT_EQ(DB_SUCCESS, TPC_CompressTable(g_comDbId, TBL_1, 10000, notifyFunc));
    }
    for (uint32_t i = 0; i < 400; i++) {
        DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = DBCompressTableNotifyFunc;
        ASSERT_EQ(DB_SUCCESS, TPC_CompressTable(g_comDbId, TBL_0, 100000, notifyFunc));
    }
    cout << "compress tabel0: " << endl;
    uint64_t totalCompressDataSize = 0;
    uint64_t totalCompressComAreaSize = 0;
    uint64_t totalCompressMemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalCompressDataSize);
    FindTargetStr(strResult, "total area size: ", &totalCompressComAreaSize);
    cout << "total data size: " << totalCompressDataSize << endl;
    cout << "total area size: " << totalCompressComAreaSize << endl;
    cout << result << endl;
    sleep(10);
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalCompressMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalCompressMemSize << endl;

    ASSERT_GE(totalInsertDataSize, totalDeleteDataSize);
    ASSERT_GE(totalDeleteMemSize, totalCompressMemSize);

    // 删除数据到10条
    for (uint32_t i = 10000; i < 10500 - 10; i++) {
        DB_COND_STRU stCond;
        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        *(uint32_t *)stCond.aCond[0].aucValue = i;
        uint32_t delRecNum = 0;
        ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, delRecNum) << i;
    }
    sleep(5);
    cout << "delete table 1: " << endl;
    // 删除数据记录共享空间开销和内存开销
    uint64_t totalDeleteTbl1DataSize = 0;
    uint64_t totalDeleteTbl1ComAreaSize = 0;
    uint64_t totalDeleteTbl1MemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalDeleteTbl1DataSize);
    FindTargetStr(strResult, "total area size: ", &totalDeleteTbl1ComAreaSize);
    cout << "total data size: " << totalDeleteTbl1DataSize << endl;
    cout << "total area size: " << totalDeleteTbl1ComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalDeleteTbl1MemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalDeleteTbl1MemSize << endl;

    sleep(10);
    // 压缩数据
    for (uint32_t i = 0; i < 2; i++) {
        DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = DBCompressTableNotifyFunc;
        ASSERT_EQ(DB_SUCCESS, TPC_CompressTable(g_comDbId, TBL_1, 10000, notifyFunc));
    }

    cout << "compress table 1: " << endl;
    // 删除数据记录共享空间开销和内存开销
    uint64_t totalCompressTbl1DataSize = 0;
    uint64_t totalCompressTbl1ComAreaSize = 0;
    uint64_t totalCompressTbl1MemSize = 0;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalCompressTbl1DataSize);
    FindTargetStr(strResult, "total area size: ", &totalCompressTbl1ComAreaSize);
    cout << "total data size: " << totalCompressTbl1DataSize << endl;
    cout << "total area size: " << totalCompressTbl1ComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalCompressTbl1MemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalCompressTbl1MemSize << endl;

    TPC_FreeSysviewResult(&result);
}

// 测压缩
TEST_F(StSimpRelComArea, TestComAreaCase11)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();
    sleep(10);
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    uint32_t stepSize = 30;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < 3; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;

    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    /****************共享空间表删完数据如何释放页*********************/
    for (uint32_t i = 0; i < 3; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
    }

    sleep(5);

    cout << "delete table 0 1 2: " << endl;
    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    sleep(5);

    // 压缩数据
    DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = DBCompressTableNotifyFunc;
    ASSERT_EQ(DB_SUCCESS, TPC_CompressTable(g_comDbId, TBL_0, 1000, notifyFunc));

    cout << "compress tabel0: " << endl;
    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    sleep(5);

    cout << "wait 5 s: " << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// 测压缩
TEST_F(StSimpRelComArea, DISABLED_TestComAreaCase12)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();
    sleep(10);
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    uint32_t stepSize = 10;
    uint32_t loop = 10;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);

            TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    for (uint32_t i = 10000; i < 100000; i++) {
        TestCommonAreaSetOneRec(i, dsBuf1.StdBuf.pucData, TBL_1);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
        ASSERT_EQ(GMERR_OK, ret);
        if (i % 1000 == 0) {
            TestCommonAreaSetOneRec(i, dsBuf0.StdBuf.pucData, TBL_0);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
            ASSERT_EQ(GMERR_OK, ret);
        }
        if (i % 100 == 0) {
            TestCommonAreaSetOneRec(i, dsBuf2.StdBuf.pucData, TBL_2);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "insert table 0 1 2:" << endl;
    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    std::string strResult;
    uint64_t totalMemSize = 0;
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalMemSize << endl;

    /****************共享空间表删完数据如何释放页*********************/
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
    }

    for (uint32_t i = 10000; i < 100000; i++) {
        if (i % 2 == 0) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = i;
            uint32_t delRecNum = 0;
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        if (i % 10000 == 0) {
            printf("-------%u-----\n", i);
        }
    }

    sleep(5);

    cout << "delete table 0 1 2: " << endl;
    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalMemSize << endl;
    sleep(5);
    // 压缩数据
    for (uint32_t i = 0; i < 100; i++) {
        DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = DBCompressTableNotifyFunc;
        ASSERT_EQ(DB_SUCCESS, TPC_CompressTable(g_comDbId, TBL_0, 10000, notifyFunc));
    }
    cout << "compress tabel0: " << endl;
    // 测试SysviewLastError
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    cout << result << endl;

    sleep(5);

    cout << "wait 5 s: " << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalMemSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_UNDO_STAT, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// 不带事务不走迁出，下次带了事务进来才走迁出
TEST_F(StSimpRelComArea, TestComAreaCase13)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 100;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            if (j == 104) {
                cout << "insert j:" << j << endl;
                ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
                cout << result << endl;
            }
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }
    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// CDB插入，触发迁出
TEST_F(StSimpRelComArea, TestComAreaCase14)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 100;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            if (j == 101) {
                DB_COND_STRU stCond;
                stCond.usCondNum = 1;
                stCond.aCond[0].enOp = DB_OP_EQUAL;
                stCond.aCond[0].ucFieldId = 0;
                *(uint32_t *)stCond.aCond[0].aucValue = 33;

                uint32_t delRecNum = 0;

                ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, delRecNum);
            }
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            if (j == 110) {
                TPC_BeginCDBByID(g_comDbId, 10);
                ret = TPC_InsertRec(10, g_comDbId, TBL_1, &dsBuf1);
                TPC_ForceCommitCDB(10);
            } else {
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            }

            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            if (j == 33) {
                continue;
            }
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// RDB插入，触发迁出
TEST_F(StSimpRelComArea, TestComAreaCase15)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 100;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            if (j == 104) {
                ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
                cout << result << endl;
            }
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// CDB插入，触发迁出，测迁入
TEST_F(StSimpRelComArea, TestComAreaCase16)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    uint32_t stepSize = 100;
    uint32_t loop = 3;
    // A 20 B 20 C 20 -->循环3次
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
            TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
            if (j == 110) {
                TPC_BeginCDBByID(g_comDbId, 10);
                ret = TPC_InsertRec(10, g_comDbId, TBL_1, &dsBuf1);
                TPC_ForceCommitCDB(10);
            } else {
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
            }

            ASSERT_EQ(GMERR_OK, ret);
        }
        cout << "insert " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // 保留6条数据
    for (uint32_t i = 0; i < loop; i++) {
        for (uint32_t j = i * stepSize; j < i * stepSize + stepSize - 2; j++) {
            DB_COND_STRU stCond;
            stCond.usCondNum = 1;
            stCond.aCond[0].enOp = DB_OP_EQUAL;
            stCond.aCond[0].ucFieldId = 0;
            *(uint32_t *)stCond.aCond[0].aucValue = j;

            uint32_t delRecNum = 0;

            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, delRecNum);
            if (j == 296) {
                TestCommonAreaSetOneRec(1000, dsBuf1.StdBuf.pucData, TBL_1);
                TPC_BeginCDBByID(g_comDbId, 10);
                ret = TPC_InsertRec(10, g_comDbId, TBL_1, &dsBuf1);
                TPC_ForceCommitCDB(10);
            }
        }
        cout << "delete " << i << endl;
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
        cout << result << endl;
    }

    cout << "after delete" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// undo回收
TEST_F(StSimpRelComArea, DISABLED_TestComAreaCase17)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTbl();

    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    cout << "before insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    // A 20 B 20 C 20 -->循环3次

    for (uint32_t j = 0; j < 74; j++) {
        TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
        ASSERT_EQ(GMERR_OK, ret);

        TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
        ASSERT_EQ(GMERR_OK, ret);

        TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    cout << "start rss:" << endl;
    system("pmap -x `pidof st_015_emb_simrel` | grep 'total' | awk '{print $4}'");
    cout << "start dyn:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps | grep \"rw-p\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    cout << "start shm:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps| grep \"rw-s\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(g_comDbId, 10));
    for (uint32_t i = 73; i < 50000; i++) {
        DB_COND_STRU pstCond;
        pstCond.usCondNum = 1;
        pstCond.aCond[0].ucFieldId = 0;
        pstCond.aCond[0].enOp = DB_OP_EQUAL;
        *(uint32_t *)pstCond.aCond[0].aucValue = i;
        uint32_t pulRecNum = 0;
        DB_FIELDFILTER_STRU pstFldFilter;
        pstFldFilter.ucFieldNum = DB_FIELD_ALL;
        TestCommonAreaSetOneRec(i + 1, dsBuf1.StdBuf.pucData, TBL_1);
        pulRecNum = 0;
        ret = TPC_UpdateRec(10, g_comDbId, TBL_1, &pstCond, &pstFldFilter, &dsBuf1, &pulRecNum);
        ASSERT_EQ(DB_SUCCESS, ret);
        ASSERT_EQ(1u, pulRecNum);
    }
    ASSERT_EQ(GMERR_OK, TPC_CommitCDB(10));
    cout << "start rss:" << endl;
    system("pmap -x `pidof st_015_emb_simrel` | grep 'total' | awk '{print $4}'");
    cout << "start dyn:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps | grep \"rw-p\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    cout << "start shm:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps| grep \"rw-s\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");

    cout << "after insert" << endl;
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    cout << result << endl;

    sleep(10);
    cout << "start rss:" << endl;
    system("pmap -x `pidof st_015_emb_simrel` | grep 'total' | awk '{print $4}'");
    cout << "start dyn:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps | grep \"rw-p\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    cout << "start shm:" << endl;
    system("cat /proc/`pidof st_015_emb_simrel`/smaps| grep \"rw-s\" -A 5 | grep '^Rss:' | awk '{sum += $2} END{print "
           "sum}'");
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_UNDO_STAT, &sysviewArgs, &result));
    cout << result << endl;

    TPC_FreeSysviewResult(&result);
}

// 迁出之后，更新数据
TEST_F(StSimpRelComArea, TestComAreaCase18)
{
    // 建 3 张表 0 1 2
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    // 0号RDB 每张表插入 1000条 数据， 预期 数据全部落在 共享空间
    TestCommonAreaStep1(1000);

    // 更新表数据为基础数据 + 10000
    TestCommonAreaStep6();
}

// 多次迁入迁出查看内存
TEST_F(StSimpRelComArea, TestComAreaCase19)
{
    // 建 3 张表
    Status ret = GMERR_OK;
    TestCommonAreaGenerateTblWithIndex();
    sleep(10);
    TestComAreaTbl0RecT tbl0Rec;
    DB_DSBUF_STRU dsBuf0;
    dsBuf0.StdBuf.ulActLen = sizeof(TestComAreaTbl0RecT);
    dsBuf0.StdBuf.pucData = (uint8_t *)&tbl0Rec;

    TestComAreaTbl1RecT tbl1Rec;
    DB_DSBUF_STRU dsBuf1;
    dsBuf1.StdBuf.ulActLen = sizeof(TestComAreaTbl1RecT);
    dsBuf1.StdBuf.pucData = (uint8_t *)&tbl1Rec;

    TestComAreaTbl2RecT tbl2Rec;
    DB_DSBUF_STRU dsBuf2;
    dsBuf2.StdBuf.ulActLen = sizeof(TestComAreaTbl2RecT);
    dsBuf2.StdBuf.pucData = (uint8_t *)&tbl2Rec;
    uint32_t stepSize = 100;
    uint32_t loop = 10;

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = g_comDbId;
    sysviewArgs.usRelNo = DB_INVALID_UINT16;
    VOS_UINT8 *result = NULL;
    std::string strResult;
    uint64_t totalDeleteDataSize = 0;
    uint64_t totalDeleteComAreaSize = 0;
    uint64_t totalDeleteMemSize = 0;
    for (uint32_t k = 0; k < loop; k++) {
        // A 10 B 10 C 10 -->循环10次
        for (uint32_t i = 0; i < loop; i++) {
            for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
                TestCommonAreaSetOneRec(j, dsBuf0.StdBuf.pucData, TBL_0);
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &dsBuf0);
                ASSERT_EQ(GMERR_OK, ret);

                TestCommonAreaSetOneRec(j, dsBuf1.StdBuf.pucData, TBL_1);
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &dsBuf1);
                ASSERT_EQ(GMERR_OK, ret);

                TestCommonAreaSetOneRec(j, dsBuf2.StdBuf.pucData, TBL_2);
                ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &dsBuf2);
                ASSERT_EQ(GMERR_OK, ret);
            }
        }
        // 将前面的 各 100条数据删除，构造首页为空页的情况
        /****************共享空间表删完数据如何释放页*********************/
        for (uint32_t i = 0; i < loop; i++) {
            for (uint32_t j = i * stepSize; j < i * stepSize + stepSize; j++) {
                DB_COND_STRU stCond;
                stCond.usCondNum = 1;
                stCond.aCond[0].enOp = DB_OP_EQUAL;
                stCond.aCond[0].ucFieldId = 0;
                *(uint32_t *)stCond.aCond[0].aucValue = j;

                uint32_t delRecNum = 0;
                ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_0, &stCond, &delRecNum);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, delRecNum) << j << " " << k;

                ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_1, &stCond, &delRecNum);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, delRecNum);

                ret = TPC_DeleteRec(TPC_GLOBAL_CDB, g_comDbId, TBL_2, &stCond, &delRecNum);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, delRecNum);
            }
        }
        ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
        strResult = (char *)result;
        FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalDeleteMemSize);
        cout << "devMgrMemctx ctx share memory tree physics size: " << totalDeleteMemSize << endl;
    }

    sleep(5);
    cout << "delete table 0 1 2: " << endl;
    // 删除数据记录共享空间开销和内存开销
    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "total data size: ", &totalDeleteDataSize);
    FindTargetStr(strResult, "total area size: ", &totalDeleteComAreaSize);
    cout << "total data size: " << totalDeleteDataSize << endl;
    cout << "total area size: " << totalDeleteComAreaSize << endl;

    ASSERT_EQ(GMERR_OK, TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, &sysviewArgs, &result));
    strResult = (char *)result;
    FindTargetStr(strResult, "devMgrMemctx ctx share memory tree physics size: ", &totalDeleteMemSize);
    cout << "devMgrMemctx ctx share memory tree physics size: " << totalDeleteMemSize << endl;

    TPC_FreeSysviewResult(&result);
}
