/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_simprel_dcl.cc
 * Description:
 * Create:
 */
#include "st_emb_simprel_com.h"
#include "gme_sql_api.h"

using namespace std;

class StSimpRelDCL : public StSimpRel {};
TEST_F(StSimpRelDCL, TestBeginCdb)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 cdbId;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(ulDbId, &cdbId));
    ASSERT_EQ(GMERR_OK, TPC_RollbackCDB(cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDCL, TestBeginCdbErr)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, TPC_BeginCDB(ulDbId, NULL));

    VOS_UINT32 cdbId = 240;
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_BeginCDBByID(ulDbId, cdbId));
    for (VOS_UINT32 i = 0; i < 240; i++) {
        ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(ulDbId, i));
    }
    ASSERT_EQ(VOS_ERRNO_DB_TPC_NOCDBBLK, TPC_BeginCDB(ulDbId, &cdbId));
    cdbId = 0;
    ASSERT_EQ(VOS_ERRNO_DB_TPC_CDBID_ALREADY_USED, TPC_BeginCDBByID(ulDbId, cdbId));
    for (VOS_UINT32 i = 0; i < 240; i++) {
        ASSERT_EQ(GMERR_OK, TPC_RollbackCDB(i));
    }
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDCL, TestBeginCdbWithInvalidDb)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));

    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));

    VOS_UINT32 cdbId = 0;
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, TPC_BeginCDB(ulDbId + 1, &cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(VOS_ERRNO_DB_DATABASE_NOT_OPENED, TPC_BeginCDB(ulDbId, &cdbId));

    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    cdbId = 0;
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, TPC_BeginCDBByID(ulDbId + 1, cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(VOS_ERRNO_DB_DATABASE_NOT_OPENED, TPC_BeginCDBByID(ulDbId, cdbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDCL, TestBeginCdbById)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 cdbId = 0;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(ulDbId, cdbId));
    ASSERT_EQ(GMERR_OK, TPC_RollbackCDB(cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDCL, TestCommitCdb)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 cdbId = 0;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(ulDbId, cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CommitCDB(cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDCL, TestCommitCdbErr)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 cdbId = 0;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(ulDbId, cdbId));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_CommitCDB(240));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_ForceCommitCDB(cdbId + 1));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_ForceCommitCDB(240));
    ASSERT_EQ(GMERR_OK, TPC_CommitCDB(cdbId));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_CommitCDB(cdbId));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_ForceCommitCDB(cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

TEST_F(StSimpRelDCL, TestRollbackCdb)
{
    VOS_UINT32 ulDbId = 0;
    char dbName[10] = "stTest";
    DB_INST_CONFIG_STRU dbCfg = {0};
    ASSERT_EQ(GMERR_OK, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg));
    ASSERT_EQ(GMERR_OK, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId));
    VOS_UINT32 cdbId = 0;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDBByID(ulDbId, cdbId));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_RollbackCDB(240));
    ASSERT_EQ(GMERR_OK, TPC_RollbackCDB(cdbId));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_INVALID_CDBID, TPC_RollbackCDB(cdbId));
    ASSERT_EQ(GMERR_OK, TPC_CloseDB(ulDbId));
    ASSERT_EQ(GMERR_OK, TPC_DropDB((VOS_UINT8 *)dbName, 1));
}

typedef struct TestForceCommitArg {
    uint32_t dbId;
    uint16_t relId;
    uint16_t tid;
    uint32_t fldCnt;
    uint32_t recCnt;
} TestForceCommitArgT;

static uint32_t g_exit;
static uint32_t g_err[24];

void *MultiThreadForceCommit(void *testArgs)
{
    Status ret = GMERR_OK;
    TestForceCommitArgT *args = (TestForceCommitArgT *)testArgs;

    VOS_UINT32 ulCDBId;
    DB_COND_STRU stCond;
    uint32_t recData[4];
    DB_DSBUF_STRU stBuff;
    stBuff.StdBuf.ulActLen = sizeof(uint32_t) * args->fldCnt;
    stBuff.StdBuf.pucData = (VOS_UINT8 *)&recData;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    uint32_t loop = 0;
    printf("ForceCommit_task Thread %u START\n", args->tid);
    while (g_exit == 0) {
        printf("Thread %u Loop %u\n", args->tid, loop);
        ret = TPC_BeginCDB(args->dbId, &ulCDBId);
        if (GMERR_OK != ret) {
            printf("Thread %u Error in TPC_BeginCDB, CDB No.%u ret=%#x\n", args->tid, ulCDBId, ret);
            (void)TPC_RollbackCDB(ulCDBId);
            g_err[args->tid] = ret;
            g_exit++;
            printf("1Exited Task - %u\n", args->tid);
            return NULL;
        }

        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        uint32_t udpCnt = 0xFFFFFFFF;
        for (uint32_t i = 0; i < args->recCnt; i++) {
            *(uint32_t *)stCond.aCond[0].aucValue = i;
            for (uint32_t j = 0; j < args->fldCnt; j++) {
                recData[j] = i;
            }

            ret = TPC_UpdateRec(ulCDBId, args->dbId, args->relId, &stCond, &stFldFilter, &stBuff, &udpCnt);
            if (GMERR_OK != ret || udpCnt != 1) {
                printf("Error in TPC_UpdateRec, ret=%#x Loop %u Rec Cnt %u\n", ret, i, udpCnt);
                TPC_RollbackCDB(ulCDBId);
                ret = (ret != 0) ? ret : -1;
                g_err[args->tid] = ret;
                g_exit++;
                return NULL;
            }
        }

        ret = TPC_ForceCommitCDB(ulCDBId);
        if (GMERR_OK != ret) {
            printf("TPC_ForceCommitCDB Failed ret = %#x.\n", ret);
            (void)TPC_RollbackCDB(ulCDBId);
            g_err[args->tid] = ret;
            g_exit++;
            return NULL;
        }

        if (loop == 500) {
            g_err[args->tid] = GMERR_OK;
            g_exit++;
            printf("5Exited Task - %u\n", args->tid);
            return NULL;
        }
        loop++;
    }

    g_err[args->tid] = GMERR_OK;
    g_exit++;
    printf("ForceCommit_task Thread %u Exit\n", args->tid);
    return NULL;
}

void *MultiThreadRollback(void *testArgs)
{
    Status ret = GMERR_OK;
    TestForceCommitArgT *args = (TestForceCommitArgT *)testArgs;

    VOS_UINT32 ulCDBId;
    DB_COND_STRU stCond;
    uint32_t recData[4];
    DB_DSBUF_STRU stBuff;
    stBuff.StdBuf.ulActLen = sizeof(uint32_t) * args->fldCnt;
    stBuff.StdBuf.pucData = (VOS_UINT8 *)&recData;
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    uint32_t loop = 0;
    printf("Rollback_task Thread %u START\n", args->tid);
    while (g_exit == 0) {
        printf("Thread %u Loop %u\n", args->tid, loop);
        ret = TPC_BeginCDB(args->dbId, &ulCDBId);
        if (GMERR_OK != ret) {
            printf("Thread %u Error in TPC_BeginCDB, CDB No.%u ret=%#x\n", args->tid, ulCDBId, ret);
            (void)TPC_RollbackCDB(ulCDBId);
            g_err[args->tid] = ret;
            g_exit++;
            printf("1Exited Task - %u\n", args->tid);
            return NULL;
        }

        stCond.usCondNum = 1;
        stCond.aCond[0].enOp = DB_OP_EQUAL;
        stCond.aCond[0].ucFieldId = 0;
        uint32_t udpCnt = 0xFFFFFFFF;
        for (uint32_t i = 0; i < args->recCnt; i++) {
            *(uint32_t *)stCond.aCond[0].aucValue = i;
            for (uint32_t j = 0; j < args->fldCnt; j++) {
                recData[j] = i;
            }

            ret = TPC_UpdateRec(ulCDBId, args->dbId, args->relId, &stCond, &stFldFilter, &stBuff, &udpCnt);
            if (GMERR_OK != ret || udpCnt != 1) {
                printf("Error in TPC_UpdateRec, ret=%#x Loop %u Rec Cnt %u\n", ret, i, udpCnt);
                TPC_RollbackCDB(ulCDBId);
                ret = (ret != 0) ? ret : -1;
                g_err[args->tid] = ret;
                g_exit++;
                return NULL;
            }
        }

        ret = TPC_RollbackCDB(ulCDBId);
        if (GMERR_OK != ret) {
            printf("TPC_RollbackCDB Failed.\n");
            (void)TPC_RollbackCDB(ulCDBId);
            g_err[args->tid] = ret;
            g_exit++;
            return NULL;
        }

        if (loop == 500) {
            g_err[args->tid] = GMERR_OK;
            g_exit++;
            printf("5Exited Task - %u\n", args->tid);
            return NULL;
        }
        loop++;
    }

    g_err[args->tid] = GMERR_OK;
    g_exit++;
    printf("Rollback_task Thread %u Exit\n", args->tid);
    return NULL;
}

TEST_F(StSimpRelDCL, TestMultiThreadForceCommit)
{
    Status ret = GMERR_OK;
    uint32_t dbId;
    CommonCreateDB4Test(NULL, &dbId);

    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dcl/testMultiThreadForceCommit.json", &stRelDef));

    uint16_t relId;
    ASSERT_EQ(GMERR_OK, TPC_CreateTbl(dbId, &stRelDef, &relId));

    DB_DSBUF_STRU stDsBuf;
    stDsBuf.StdBuf.ulActLen = sizeof(uint32_t) * 4;
    for (uint32_t i = 0; i < 100; i++) {
        uint32_t rec[4] = {i, i, i, i};
        stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&rec;
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, dbId, relId, &stDsBuf));
    }

    g_exit = 0;
    for (uint32_t i = 0; i < 24; i++) {
        g_err[i] = GMERR_OK;
    }

    char threadName[DB_NAME_LEN];
    pthread_t tid1, tid2, tid3;
    TestForceCommitArgT args1 = {.dbId = dbId, .relId = relId, .tid = 1, .fldCnt = 4, .recCnt = 100};
    ret = pthread_create(&tid1, NULL, MultiThreadForceCommit, &args1);
    EXPECT_EQ(GMERR_OK, ret);
    (void)sprintf_s(threadName, DB_NAME_LEN, "Commit%u", args1.tid);
    (void)pthread_setname_np(tid1, threadName);
    usleep(100);

    TestForceCommitArgT args2 = {.dbId = dbId, .relId = relId, .tid = 2, .fldCnt = 4, .recCnt = 100};
    ret = pthread_create(&tid2, NULL, MultiThreadForceCommit, &args2);
    EXPECT_EQ(GMERR_OK, ret);
    (void)sprintf_s(threadName, DB_NAME_LEN, "Commit%u", args2.tid);
    (void)pthread_setname_np(tid2, threadName);
    usleep(100);

    TestForceCommitArgT args3 = {.dbId = dbId, .relId = relId, .tid = 3, .fldCnt = 4, .recCnt = 100};
    ret = pthread_create(&tid3, NULL, MultiThreadRollback, &args3);
    EXPECT_EQ(GMERR_OK, ret);
    (void)sprintf_s(threadName, DB_NAME_LEN, "Rollback%u", args3.tid);
    (void)pthread_setname_np(tid3, threadName);
    usleep(100);

    ret = pthread_join(tid1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_join(tid2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_join(tid3, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(g_err[1], GMERR_OK);
    EXPECT_EQ(g_err[2], GMERR_OK);
    EXPECT_EQ(g_err[3], GMERR_OK);
    CommonDropDB4Test(NULL, dbId);
}

TEST_F(StSimpRelDCL, TestFroceCommitMultiTbl)
{
    uint32_t dbId1, dbId2;
    CommonCreateDB4Test("db1", &dbId1);
    CommonCreateDB4Test("db2", &dbId2);

    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dcl/testMultiThreadForceCommit.json", &stRelDef));
    // 每个DB建两张表
    (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl%u", 1);
    ASSERT_EQ(GMERR_OK, TPC_CreateTblByID(dbId1, 1, &stRelDef));
    ASSERT_EQ(GMERR_OK, TPC_CreateTblByID(dbId2, 1, &stRelDef));
    (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl%u", 2);
    ASSERT_EQ(GMERR_OK, TPC_CreateTblByID(dbId1, 2, &stRelDef));
    ASSERT_EQ(GMERR_OK, TPC_CreateTblByID(dbId2, 2, &stRelDef));

    uint32_t cdb11, cdb12, cdb21, cdb22;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(dbId1, &cdb11));
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(dbId1, &cdb12));
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(dbId2, &cdb21));
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(dbId2, &cdb22));

    DB_DSBUF_STRU stDsBuf;
    stDsBuf.StdBuf.ulActLen = sizeof(uint32_t) * 4;
    for (uint32_t i = 0; i < 10; i++) {
        uint32_t rec[4] = {i, i, i, i};
        stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&rec;
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb11, dbId1, 1, &stDsBuf));
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb11, dbId1, 2, &stDsBuf));
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb21, dbId2, 1, &stDsBuf));
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb21, dbId2, 2, &stDsBuf));
    }

    for (uint32_t i = 10; i < 20; i++) {
        uint32_t rec[4] = {i, i, i, i};
        stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&rec;
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb12, dbId1, 1, &stDsBuf));
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb12, dbId1, 2, &stDsBuf));
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb22, dbId2, 1, &stDsBuf));
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb22, dbId2, 2, &stDsBuf));
    }

    ASSERT_EQ(GMERR_OK, TPC_CommitCDB(cdb11));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_RDB_MODIFIED, TPC_CommitCDB(cdb12));
    ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(cdb12));

    ASSERT_EQ(GMERR_OK, TPC_CommitCDB(cdb21));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_RDB_MODIFIED, TPC_CommitCDB(cdb22));
    ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(cdb22));

    uint32_t recCnt;
    ASSERT_EQ(GMERR_OK, TPC_GetRelActRec(dbId1, 1, &recCnt));
    ASSERT_EQ(recCnt, 20);
    ASSERT_EQ(GMERR_OK, TPC_GetRelActRec(dbId1, 2, &recCnt));
    ASSERT_EQ(recCnt, 20);
    ASSERT_EQ(GMERR_OK, TPC_GetRelActRec(dbId2, 1, &recCnt));
    ASSERT_EQ(recCnt, 20);
    ASSERT_EQ(GMERR_OK, TPC_GetRelActRec(dbId2, 2, &recCnt));
    ASSERT_EQ(recCnt, 20);
    CommonDropDB4Test("db1", dbId1);
    CommonDropDB4Test("db2", dbId2);
}

TEST_F(StSimpRelDCL, TestFroceCommitWithSelect)
{
    uint32_t dbId1;
    CommonCreateDB4Test("db1", &dbId1);
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dcl/testMultiThreadForceCommit.json", &stRelDef));
    // 建两张表
    (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl%u", 1);
    ASSERT_EQ(GMERR_OK, TPC_CreateTblByID(dbId1, 1, &stRelDef));
    (void)sprintf_s((char *)stRelDef.aucRelName, DB_NAME_LEN, "tbl%u", 2);
    ASSERT_EQ(GMERR_OK, TPC_CreateTblByID(dbId1, 2, &stRelDef));

    DB_DSBUF_STRU stDsBuf;
    stDsBuf.StdBuf.ulActLen = sizeof(uint32_t) * 4;
    for (uint32_t i = 0; i < 10; i++) {
        uint32_t rec[4] = {i, i, i, i};
        stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&rec;
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(TPC_GLOBAL_CDB, dbId1, 2, &stDsBuf));
    }

    uint32_t cdb11, cdb12;
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(dbId1, &cdb11));
    ASSERT_EQ(GMERR_OK, TPC_BeginCDB(dbId1, &cdb12));
    for (uint32_t i = 0; i < 10; i++) {
        uint32_t rec[4] = {i, i, i, i};
        stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&rec;
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb11, dbId1, 1, &stDsBuf));
    }

    for (uint32_t i = 10; i < 20; i++) {
        uint32_t rec[4] = {i, i, i, i};
        stDsBuf.StdBuf.pucData = (VOS_UINT8 *)&rec;
        ASSERT_EQ(GMERR_OK, TPC_InsertRec(cdb12, dbId1, 1, &stDsBuf));
    }

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_COND_STRU stCond;
    stCond.usCondNum = 0;
    DB_BUF_STRU selBuff = {.ulBufLen = 2000, .ulRecNum = DB_SELECT_ALL, .usRecLen = 0};
    selBuff.pBuf = malloc(selBuff.ulBufLen);
    Status ret = TPC_SelectAllRecEx(cdb11, dbId1, 2, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(10, selBuff.ulRecNum);

    uint32_t pulInsRecNum;
    void *ppInsRecList;
    uint32_t pulDelRecNum;
    void *ppDelRecList;
    uint32_t pulUpdRecNum;
    void *ppUpdRecList;
    ret = TPC_SelectCdbDiffData(
        cdb11, 1, &pulInsRecNum, &ppInsRecList, &pulDelRecNum, &ppDelRecList, &pulUpdRecNum, &ppUpdRecList);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(10, pulInsRecNum);
    ASSERT_EQ(0, pulDelRecNum);
    ASSERT_EQ(0, pulUpdRecNum);
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    ret = TPC_SelectCdbDiffData(
        cdb11, 2, &pulInsRecNum, &ppInsRecList, &pulDelRecNum, &ppDelRecList, &pulUpdRecNum, &ppUpdRecList);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, pulInsRecNum);
    ASSERT_EQ(0, pulDelRecNum);
    ASSERT_EQ(0, pulUpdRecNum);
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    ASSERT_EQ(GMERR_OK, TPC_CommitCDB(cdb11));
    ASSERT_EQ(VOS_ERRNO_DB_TPC_RDB_MODIFIED, TPC_CommitCDB(cdb12));
    ASSERT_EQ(GMERR_OK, TPC_ForceCommitCDB(cdb12));

    uint32_t recCnt;
    ASSERT_EQ(GMERR_OK, TPC_GetRelActRec(dbId1, 1, &recCnt));
    ASSERT_EQ(recCnt, 20);
    ASSERT_EQ(GMERR_OK, TPC_GetRelActRec(dbId1, 2, &recCnt));
    ASSERT_EQ(recCnt, 10);
    free(selBuff.pBuf);
    CommonDropDB4Test("db1", dbId1);
}
