/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: emb_simprel_read_reldef.cc
 * Description:
 * Create:
 */
#include "st_emb_simprel_util.h"

#define PRINT_INFO(format, ...) printf("[INFO] " format "\n", ##__VA_ARGS__)
#define PRINT_WARN(format, ...) printf("[WARN] " format "\n", ##__VA_ARGS__)
#define PRINT_ERROR(format, ...) printf("[ERROR] " format "\n", ##__VA_ARGS__)
#define DEFAULT_MAX_REC_NUM (100000)

static void FreeRelDefIdxAndFldLst(DB_REL_DEF_STRU *stRelDef)
{
    if (stRelDef->pstFldLst != NULL) {
        free(stRelDef->pstFldLst);
        stRelDef->pstFldLst = NULL;
    }
    if (stRelDef->pstIdxLst != NULL) {
        free(stRelDef->pstIdxLst);
        stRelDef->pstIdxLst = NULL;
    }
}

void StUtilFreeAllRelDef(DB_REL_DEF_STRU *stRelDefs, uint32_t tblNum)
{
    for (uint32_t i = 0; i < tblNum; i++) {
        FreeRelDefIdxAndFldLst(&stRelDefs[i]);
    }
}

inline static bool GetRelDefCheckJsonObjIsNull(json_t *obj, const char *desc)
{
    if (obj == NULL) {
        PRINT_ERROR("Json obj %s is NULL", desc);
        return true;
    }
    return false;
}

static int32_t GetRelDefCommonInfo(json_t *schemaJson, DB_REL_DEF_STRU *stRelDef)
{
    json_t *commInfoJson = json_object_get(schemaJson, "comm_info");
    if (GetRelDefCheckJsonObjIsNull(commInfoJson, "comm_info")) {
        return -1;
    }

    json_t *nameJson = json_object_get(commInfoJson, "name");
    if (GetRelDefCheckJsonObjIsNull(nameJson, "Table name")) {
        return -1;
    }

    const char *name = json_string_value(nameJson);
    int32_t ret = strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, name, strlen(name));
    if (ret != 0) {
        PRINT_ERROR("Unable to copy table name by json.");
        return -1;
    }

    json_t *relType = json_object_get(commInfoJson, "type");
    if (relType == NULL) {
        stRelDef->enTableType = DB_TABLE_NORMAL;
    } else {
        stRelDef->enTableType = (DB_TABLE_TYPE_ENUM)json_integer_value(relType);
    }

    uint32_t maxRecNum = 0;
    json_t *maxRecJson = json_object_get(commInfoJson, "max_rec_num");
    if (maxRecJson == NULL) {
        maxRecNum = DEFAULT_MAX_REC_NUM;
    } else {
        maxRecNum = (uint32_t)json_integer_value(maxRecJson);
    }
    stRelDef->ulMaxSize = maxRecNum;
    stRelDef->ulIntialSize = maxRecNum;

    json_t *colNumJson = json_object_get(commInfoJson, "col_num");
    if (GetRelDefCheckJsonObjIsNull(colNumJson, "col num")) {
        return -1;
    }
    stRelDef->ulNCols = (uint32_t)json_integer_value(colNumJson);

    json_t *idxNumJson = json_object_get(commInfoJson, "idx_num");
    if (GetRelDefCheckJsonObjIsNull(idxNumJson, "idx num")) {
        return -1;
    }
    stRelDef->ulNIdxs = (uint32_t)json_integer_value(idxNumJson);
    return 0;
}

static int32_t AllocRelDefIdxAndFldLst(DB_REL_DEF_STRU *stRelDef)
{
    const uint32_t fldSize = sizeof(DB_FIELD_DEF_STRU) * stRelDef->ulNCols;
    stRelDef->pstFldLst = (DB_FIELD_DEF_STRU *)malloc(fldSize);
    if (stRelDef->pstFldLst == NULL) {
        PRINT_ERROR("Unable to alloc field list.");
        return -1;
    }
    (void)memset_s(stRelDef->pstFldLst, fldSize, 0x00, fldSize);

    if (stRelDef->ulNIdxs > 0) {
        const uint32_t idxSize = sizeof(DB_INDEX_DEF_STRU) * stRelDef->ulNIdxs;
        stRelDef->pstIdxLst = (DB_INDEX_DEF_STRU *)malloc(idxSize);
        if (stRelDef->pstIdxLst == NULL) {
            PRINT_ERROR("Unable to alloc index list.");
            free(stRelDef->pstFldLst);
            return -1;
        }
        (void)memset_s(stRelDef->pstIdxLst, idxSize, 0x00, idxSize);
    }
    return 0;
}

static int32_t GetRelDefFields(json_t *schemaJson, uint32_t fldNum, DB_FIELD_DEF_STRU *pstFldLst)
{
    json_t *fieldsJson = json_object_get(schemaJson, "fields");
    if (GetRelDefCheckJsonObjIsNull(fieldsJson, "fields")) {
        return -1;
    }

    size_t size = json_array_size(fieldsJson);
    if (size != fldNum) {
        PRINT_ERROR(
            "File item \"idx_num\"(%u) not equal with the size of array \"indices\"(%u).", fldNum, (uint32_t)size);
        return -1;
    }

    int32_t ret = 0;
    for (size_t i = 0; i < size; ++i) {
        json_t *field = json_array_get(fieldsJson, i);
        // 字段名
        json_t *fieldNameJson = json_object_get(field, "name");
        if (GetRelDefCheckJsonObjIsNull(fieldNameJson, "field name")) {
            return -1;
        }
        const char *fieldName = json_string_value(fieldNameJson);

        // 字段类型
        json_t *fieldTypeJson = json_object_get(field, "type");
        if (GetRelDefCheckJsonObjIsNull(fieldNameJson, "field type")) {
            return -1;
        }
        uint16_t fieldType = (uint16_t)json_integer_value(fieldTypeJson);

        // 字段定义长度
        json_t *fieldSizeJson = json_object_get(field, "size");
        if (GetRelDefCheckJsonObjIsNull(fieldNameJson, "field size")) {
            return -1;
        }
        uint16_t fieldSize = (uint16_t)json_integer_value(fieldSizeJson);

        // 字段定义默认值，可不设置，默认为无效值 (0xFFFFFFFF)
        json_t *defaultValJson = json_object_get(field, "defaultVal");
        if (defaultValJson == NULL) {
            pstFldLst[i].ulDefVal = 0xFFFFFFFF;
        } else {
            uint32_t defaultVal = (uint32_t)json_integer_value(defaultValJson);
            pstFldLst[i].ulDefVal = defaultVal;
        }

        ret = strncpy_s((char *)pstFldLst[i].aucFieldName, DB_FLD_NAME_LEN, fieldName, strlen(fieldName));
        if (ret != 0) {
            PRINT_ERROR("Unable to copy field name [%s], len %u.", fieldName, (uint32_t)(strlen(fieldName) + 1));
            return -1;
        }
        pstFldLst[i].enDataType = (DB_DATATYPE_ENUM_V1)fieldType;
        pstFldLst[i].usSize = fieldSize;
    }
    return 0;
}

static int32_t GetRelDefIndices(json_t *schemaJson, uint32_t idxNum, DB_INDEX_DEF_STRU *pstIdxLst)
{
    if (idxNum == 0) {
        return 0;
    }

    json_t *indicesJson = json_object_get(schemaJson, "indices");
    if (GetRelDefCheckJsonObjIsNull(indicesJson, "indicesJson")) {
        return -1;
    }

    size_t size = json_array_size(indicesJson);
    if (size != idxNum) {
        PRINT_ERROR("File item \"idx_num\" not equal with the size of array \"indices\".");
        return -1;
    }

    int32_t ret = 0;
    for (size_t i = 0; i < size; ++i) {
        json_t *key = json_array_get(indicesJson, i);
        json_t *keyNameJson = json_object_get(key, "name");
        if (GetRelDefCheckJsonObjIsNull(keyNameJson, "key name")) {
            return -1;
        }
        const char *idxName = json_string_value(keyNameJson);
        ret = strncpy_s((char *)pstIdxLst[i].aucIndexName, DB_IDX_NAME_LEN, idxName, strlen(idxName));
        if (ret != 0) {
            PRINT_ERROR("Unable to copy index name.");
            return -1;
        }

        json_t *keyTypeJson = json_object_get(key, "type");
        if (GetRelDefCheckJsonObjIsNull(keyTypeJson, "key type")) {
            return -1;
        }
        uint16_t keyType = (uint16_t)json_integer_value(keyTypeJson);
        pstIdxLst[i].enIndexType = (DBDDL_INDEXTYPE_ENUM)keyType;

        json_t *keyUniqueItJson = json_object_get(key, "unique");
        if (GetRelDefCheckJsonObjIsNull(keyUniqueItJson, "key unique")) {
            return -1;
        }
        bool keyUnique = json_boolean_value(keyUniqueItJson);
        pstIdxLst[i].ucUniqueFlag = (keyUnique) ? 1 : 0;

        json_t *keyFieldsJson = json_object_get(key, "fields");
        if (GetRelDefCheckJsonObjIsNull(keyUniqueItJson, "key fields")) {
            return -1;
        }

        for (size_t j = 0, keyFieldsSize = json_array_size(keyFieldsJson); j < keyFieldsSize; ++j) {
            json_t *keyFieldJson = json_array_get(keyFieldsJson, j);
            uint8_t keyField = (uint8_t)json_integer_value(keyFieldJson);
            pstIdxLst[i].aucFieldID[j] = keyField;
        }
        pstIdxLst[i].ucIdxFldNum = (uint8_t)json_array_size(keyFieldsJson);
    }
    return 0;
}

static int32_t GetRelDefFromSchemaJson(json_t *schemaJson, DB_REL_DEF_STRU *stRelDef)
{
    int32_t ret = GetRelDefCommonInfo(schemaJson, stRelDef);
    if (ret != 0) {
        PRINT_ERROR("Unable to get table common info by json.");
        return ret;
    }

    // 依赖 GetRelDefCommonInfo 中获取的字段数量和索引数量为表定义中的数组申请内存
    ret = AllocRelDefIdxAndFldLst(stRelDef);
    if (ret != 0) {
        PRINT_ERROR("Unable to get table common info by json.");
        return ret;
    }

    ret = GetRelDefFields(schemaJson, stRelDef->ulNCols, stRelDef->pstFldLst);
    if (ret != 0) {
        PRINT_ERROR("Unable to get table fields by json.");
        FreeRelDefIdxAndFldLst(stRelDef);
        return ret;
    }

    ret = GetRelDefIndices(schemaJson, stRelDef->ulNIdxs, stRelDef->pstIdxLst);
    if (ret != 0) {
        PRINT_ERROR("Unable to get table fields by json.");
        FreeRelDefIdxAndFldLst(stRelDef);
        return ret;
    }
    return 0;
}

static int32_t GetRelDefLimitCount(json_t *tblsJson, uint32_t *inputCnt, uint32_t *actCnt, uint32_t *limitCnt)
{
    uint32_t fileCnt = (uint32_t)json_array_size(tblsJson);
    if (fileCnt == 0) {
        PRINT_ERROR("The file does not have any relation definition.");
        return -1;
    }

    if (inputCnt == NULL) {
        *limitCnt = 1;
    } else {
        // 文件中表定义数量大于输入数据表数量，限制读取表定义，数量为输入的表数量
        if (fileCnt > *inputCnt) {
            // clang-format off
            PRINT_INFO("The actual number of tables in file is greater than the input para \"tblNum\". "
                       "input num : %u, act num : %u", *inputCnt, fileCnt);
            // clang-format on
            *limitCnt = *inputCnt;
        } else {
            // 输入数据表数量大于文件中表定义数量，读取文件中所有表定义
            *limitCnt = fileCnt;
        }
    }

    *actCnt = fileCnt;
    return 0;
}

static int32_t GetRelDefPrepareTblsJson(const char *filePath, json_t **tblsJson)
{
    json_error_t error;
    json_t *json = json_load_file(filePath, 0, &error);
    if (json == NULL) {
        PRINT_ERROR("Unable to load json file(%s).", filePath);
        return -1;
    }

    json_t *tblsJsonTmp = json_object_get(json, "tables");
    if (tblsJsonTmp == NULL) {
        PRINT_ERROR("Unable to get json format, member \"tables\" not exist.");
        return -1;
    }

    *tblsJson = tblsJsonTmp;
    return 0;
}

int32_t StUtilGetRelDefFromJsonFile(const char *filePath, DB_REL_DEF_STRU *stRelDefs, uint32_t *tblNum)
{
    if (filePath == NULL || stRelDefs == NULL) {
        PRINT_ERROR("There is an input parameter (filePath or stRelDefs) whose value is NULL.");
        return -1;
    }

    json_t *tblsJson = NULL;
    int32_t ret = GetRelDefPrepareTblsJson(filePath, &tblsJson);
    if (ret != 0) {
        return ret;
    }

    // 获取表定义数量，tblNum输入为null 默认读取第一张表定义
    uint32_t limitCnt, actCnt;
    ret = GetRelDefLimitCount(tblsJson, tblNum, &actCnt, &limitCnt);
    if (ret != 0) {
        return ret;
    }

    for (uint32_t i = 0, idx = 0; (i < actCnt) && (idx < limitCnt); ++i) {
        json_t *relDefJson = json_array_get(tblsJson, i);
        json_t *schemaJson = json_object_get(relDefJson, "schema");
        if (schemaJson == NULL) {
            PRINT_WARN("No.%u schema is null.", i);
            continue;
        }

        DB_REL_DEF_STRU *relDef = &stRelDefs[idx];
        ret = GetRelDefFromSchemaJson(schemaJson, relDef);
        if (ret != 0) {
            if (tblNum != NULL) {
                *tblNum = idx;
            }
            PRINT_ERROR("Error occurred when parsing table No.%u schema, %u parsing success.", i, idx);
            return -1;
        }
        idx++;
    }

    if (tblNum != NULL) {
        *tblNum = limitCnt;  // 最终输出的表数量
    }
    return 0;
}
