/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: cache_create_table.cc
 * Description: st test for cache prepare operation.
 * Author: Optimizer team
 * Create: 2025-7-24
 */
#include <pthread.h>
#include "client_common_st.h"
#include "table_schema_struct.h"
#include "cache_test_method.h"
#include "tools_st_common.h"

using namespace std;

static const int WAIT_TIME = 1000;
static pthread_t g_epollThreadId;

class CacheReplace : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"enableClusterHash=1\" \"compatibleV3=1\" \"userPolicyMode=0\"");
        ImportAllowList();
        DbSleep(WAIT_TIME);  // Wait server ready
        st_clt_init();
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
    }

    static void TearDownTestCase()
    {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {}

    virtual void TearDown()
    {}
};

// 001.GmcSetCacheBuf第一个参数传NULL，其余参数正常
TEST_F(CacheReplace, testReplaceCacheTable)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(NULL, labelName));

    IfPortT buf = {0};
    uint32_t bufLen = sizeof(IfPortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_NE(GMERR_OK, GmcSetCacheBuf(NULL, &seri));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 002.GmcSetCacheBuf第二个参数传NULL，其余参数正常
TEST_F(CacheReplace, testReplaceCacheTable2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(NULL, labelName));

    EXPECT_NE(GMERR_OK, GmcSetCacheBuf(stmt, NULL));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 003.GmcSetCacheBuf第二个参数GmcSeriT结构体中的Function传NULL，其余参数正常
TEST_F(CacheReplace, testReplaceCacheTable3)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(NULL, labelName));

    IfPortT buf = {0};
    uint32_t bufLen = sizeof(IfPortT);
    GmcSeriT seri = {.seriFunc = NULL,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_NE(GMERR_OK, GmcSetCacheBuf(stmt, &seri));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 004.GmcExecuteCacheReplace第一个参数传NULL，其余参数正常
TEST_F(CacheReplace, testReplaceCacheTable4)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    IfPortT buf = {0};
    uint32_t bufLen = sizeof(IfPortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_NE(GMERR_OK, GmcExecuteCacheReplace(NULL));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 005.创建if_port表，GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable5)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    IfPortT buf = {0};
    uint32_t bufLen = sizeof(IfPortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 006.覆盖支持的字段类型(char,unchar,int8-int64,uint8-uint64,bool,float,double,time,fixed)，GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable6)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 007.GmcExecuteCacheReplace写相同的数据
TEST_F(CacheReplace, testReplaceCacheTable7)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 008.多次GmcSetCacheBuf设置同一条数据，再GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable8)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 009.多次GmcSetCacheBuf设置不同数据，再GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable9)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    buf.field3 = 100;
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 010.GmcSetCacheBuf设置一次数据，多次GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable10)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    uint32_t loopCnt = 100;
    for (uint32_t i = 0; i < loopCnt; i++) {
        EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

static uint32_t g_threadNum = 100;
static void *ThreadFunc(void *arg)
{
    GmcStmtT *stmt = (GmcStmtT *)arg;
    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    return NULL;
}
// 011.建满表，申请不同的stmt并发100个线程GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable11)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";

    GmcStmtT *stmtArray[g_threadNum];
    for (uint32_t i = 0; i < g_threadNum; i++) {
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmtArray[i]));
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmtArray[i], labelName));
    }

    pthread_t threadId[g_threadNum];
    for (uint32_t i = 0; i < g_threadNum; i++) {
        int ret = pthread_create(&threadId[i], NULL, ThreadFunc, stmtArray[i]);
        ASSERT_EQ(0, ret);
    }

    for (uint32_t i = 0; i < g_threadNum; i++) {
        pthread_join(threadId[i], NULL);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    for (uint32_t i = 0; i < g_threadNum; i++) {
        GmcFreeStmt(stmtArray[i]);
    }
    DestroyConnectionAndStmt(conn, stmt);
}

// 012.创建namespace，GmcExecuteCacheReplace写
TEST_F(CacheReplace, testReplaceCacheTable12)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcCreateNamespace(stmt, "cache", NULL));
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, "cache"));

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropNamespace(stmt, "cache"));
    DestroyConnectionAndStmt(conn, stmt);
}

// 013.GmcExecuteCacheReplace写max_record_count条数据
TEST_F(CacheReplace, DISABLED_testReplaceCacheTable13)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    uint32_t maxRecordCount = 200;
    for (uint32_t i = 0; i < maxRecordCount; i++) {
        SetSimpleBuf(i, &buf);
        EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
        EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 014.GmcExecuteCacheReplace写第max_record_count+1条数据时，主键相同
// 017.GmcExecuteCacheReplace写第max_record_count+1条数据时，主键不同
TEST_F(CacheReplace, DISABLED_testReplaceCacheTable14)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    uint32_t maxRecordCount = 200;
    for (uint32_t i = 0; i < maxRecordCount; i++) {
        SetSimpleBuf(i, &buf);
        EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
        EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    }
    SetSimpleBuf(maxRecordCount - 1, &buf);
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    SetSimpleBuf(maxRecordCount, &buf);
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_NE(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 015.写满数据，删表，重新建表再写满数据
TEST_F(CacheReplace, DISABLED_testReplaceCacheTable15)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    uint32_t maxRecordCount = 200;
    for (uint32_t i = 0; i < maxRecordCount; i++) {
        SetSimpleBuf(i, &buf);
        EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
        EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    for (uint32_t i = 0; i < maxRecordCount; i++) {
        SetSimpleBuf(i, &buf);
        EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
        EXPECT_EQ(GMERR_OK, GmcExecuteCacheReplace(stmt));
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 018.GmcSetCacheBuf后使用GmcExecute
TEST_F(CacheReplace, testReplaceCacheTable18)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_NE(GMERR_OK, GmcExecute(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 020.批量接口报错场景
TEST_F(CacheReplace, testReplaceCacheTable20)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_EQ(GMERR_OK, GmcSetCacheBuf(stmt, &seri));

    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    EXPECT_NE(GMERR_OK, ret);
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 022.gmimport导数据失败
TEST_F(CacheReplace, testReplaceCacheTable22)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/vertex_schema.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "T39";

    char command[256] = {0};
    (void)sprintf_s(
        command, sizeof(command), "gmimport -c vdata -f ./dml/gmcmd_test_file/VertexTable01.vertexdata -t T39");
    Status ret = executeCommand(command, (char *)"unsucc");
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 023.gmexport导出失败
TEST_F(CacheReplace, testReplaceCacheTable23)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/vertex_schema.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "T39";

    char command[256] = {0};
    (void)sprintf_s(
        command, sizeof(command), "gmexport -c vdata -f ./dml/gmcmd_test_file/VertexTable01_output.vertexdata -t T39");
    Status ret = executeCommand(command, (char *)"unsucc.");
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 024.未prepare cache，GmcSetCacheBuf与GmcExecuteCacheReplace不core
TEST_F(CacheReplace, testReplaceCacheTable24)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/simple.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "simple";

    SimplePortT buf = {0};
    uint32_t bufLen = sizeof(SimplePortT);
    GmcSeriT seri = {.seriFunc = SeriSimpleTableObject,
        .version = GMC_SERI_VERSION_DEFAULT,
        .obj = (uint8_t *)&buf,
        .bufSize = bufLen,
        .userData = NULL};
    EXPECT_NE(GMERR_OK, GmcSetCacheBuf(stmt, &seri));
    EXPECT_NE(GMERR_OK, GmcExecuteCacheReplace(stmt));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}
