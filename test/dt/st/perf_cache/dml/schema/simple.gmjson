{"comment": "simple", "version": "2.0", "type": "record", "name": "simple", "config": {"check_validity": false, "direct_write": true, "use_cache": true}, "max_record_count": 200, "fields": [{"name": "field1", "type": "char", "comment": "field1"}, {"name": "field2", "type": "uchar", "comment": "field2"}, {"name": "field3", "type": "int8", "comment": "field3"}, {"name": "field4", "type": "int16", "comment": "field4"}, {"name": "field5", "type": "int32", "comment": "field5"}, {"name": "field6", "type": "int64", "comment": "field6"}, {"name": "field7", "type": "uint8", "comment": "field7"}, {"name": "field8", "type": "uint16", "comment": "field8"}, {"name": "field9", "type": "uint32", "comment": "field9"}, {"name": "field10", "type": "uint64", "comment": "field10"}, {"name": "field11", "type": "boolean", "comment": "field11"}, {"name": "field12", "type": "float", "comment": "field12"}, {"name": "field13", "type": "double", "comment": "field13"}, {"name": "field14", "type": "time", "comment": "field14"}, {"name": "field15", "type": "fixed", "size": 100, "comment": "field15"}], "keys": [{"name": "if_port_pk", "index": {"type": "primary"}, "node": "simple", "fields": ["field1"], "constraints": {"unique": true}}, {"name": "unitport_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "simple", "fields": ["field9"], "constraints": {"unique": false}}]}