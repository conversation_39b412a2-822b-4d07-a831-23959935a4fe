{"comment": "if_port", "version": "2.0", "type": "record", "name": "T0", "config": {"check_validity": false, "direct_write": true, "use_cache": true}, "max_record_count": 4608, "fields": [{"name": "tb", "type": "uint32", "comment": "tb"}, {"name": "tp", "type": "uint32", "comment": "tp"}, {"name": "unit_id", "type": "uint32", "comment": "芯片单元id"}, {"name": "port_id", "type": "uint32", "comment": "端口id"}, {"name": "ifindex", "type": "uint32", "comment": "端口索引"}, {"name": "logicTB", "type": "uint32", "comment": "逻辑tb"}, {"name": "logicTP", "type": "uint32", "comment": "逻辑tp"}, {"name": "globalSvcIfIndex", "type": "uint32", "comment": "svc的全局索引"}, {"name": "isSelf", "type": "uint8", "comment": "是否是本板端口"}, {"name": "holdDownTime", "type": "uint32", "comment": "延迟down时间"}, {"name": "holdUpTime", "type": "uint32", "comment": "延迟up时间"}, {"name": "smoothVersion", "type": "uint64", "comment": "平滑版本号"}], "keys": [{"name": "if_port_pk", "index": {"type": "primary"}, "node": "T0", "fields": ["tb", "tp"], "constraints": {"unique": true}, "comment": "tb tp 键"}, {"name": "unitport_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "T0", "fields": ["unit_id", "port_id"], "constraints": {"unique": false}, "comment": "unit port 键"}, {"name": "logictbtp_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "T0", "fields": ["logicTB", "logicTP"], "constraints": {"unique": false}, "comment": "逻辑tbtp 键"}, {"name": "globalSvcIfIndex_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "T0", "fields": ["globalSvcIfIndex"], "constraints": {"unique": false}, "comment": "globalSvcIfIndex 键"}]}