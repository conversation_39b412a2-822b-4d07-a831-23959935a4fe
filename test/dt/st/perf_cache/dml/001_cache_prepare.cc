/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: cache_create_table.cc
 * Description: st test for cache prepare operation.
 * Author: Optimizer team
 * Create: 2025-7-22
 */
#include <pthread.h>
#include "client_common_st.h"

using namespace std;

static const int WAIT_TIME = 1000;
static pthread_t g_epollThreadId;

class CachePrepare : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"enableClusterHash=1\" \"compatibleV3=0\" \"userPolicyMode=0\"");
        ImportAllowList();
        DbSleep(WAIT_TIME);  // Wait server ready
        st_clt_init();
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
    }

    static void TearDownTestCase()
    {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {}

    virtual void TearDown()
    {}
};

// 001.GmcPrepareStmtByLabelNameWithCache第一个参数传NULL，其余参数正常
TEST_F(CachePrepare, testPrepareIfPortTable)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(NULL, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 002.GmcPrepareStmtByLabelNameWithCache第二个参数传NULL，其余参数正常
TEST_F(CachePrepare, testPrepareIfPortTable2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, NULL));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 003.GmcPrepareStmtByLabelNameWithCache一张不存在的表
TEST_F(CachePrepare, testPrepareIfPortTable3)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelName = "if_port";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 004.GmcPrepareStmtByLabelNameWithCache接口labelName为空串""
TEST_F(CachePrepare, testPrepareIfPortTable4)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelName = "";
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 005.不配置use_cache，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable5)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 006.gmjson中配置use_cache为false，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable6)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_2.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 007.config中配置use_cache为false，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable7)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": false})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 008.创建异步连接申请的stmt，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable8)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    // 创建异步连接
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateAsyncConnectionAndStmt(&conn2, &stmt2);
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithCache(stmt2, labelName));
    DestroyConnectionAndStmt(conn2, stmt2);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 009.创建订阅连接申请的stmt，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable9)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    // 创建订阅连接
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSubConnectionAndStmt(&conn2, &stmt2, "subConnection");
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithCache(stmt2, labelName));
    DestroyConnectionAndStmt(conn2, stmt2);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 010.配置use_cache为true，GmcPrepareStmtByLabelName打开表
TEST_F(CachePrepare, testPrepareIfPortTable10)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 011.配置use_cache为true，GmcPrepareStmtByLabelNameWithVersion打开表
TEST_F(CachePrepare, testPrepareIfPortTable11)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(
        GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 0, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 012.在gmjson中配置use_cache为true，创建if_port表，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable12)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 013.在cofig中配置use_cache为true，创建if_port表，GmcPrepareStmtByLabelNameWithCache打开表
TEST_F(CachePrepare, testPrepareIfPortTable13)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 014.在gmjson中配置use_cache为true，创建if_port表，GmcPrepareStmtByLabelNameWithCache打开同一个表100次
TEST_F(CachePrepare, testPrepareIfPortTable14)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    uint32_t prepareCnt = 100;
    for (uint32_t i = 0; i < prepareCnt; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    }
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

static void replaceAll(string &str, string &toReplace, const string &replacement)
{
    size_t pos = 0;
    while ((pos = str.find(toReplace, pos)) != string::npos) {
        str.replace(pos, toReplace.length(), replacement);
        pos += replacement.length();
    }
}
// 015.在gmjson中配置use_cache为true，创建if_port表，GmcPrepareStmtByLabelNameWithCache打开不同表
TEST_F(CachePrepare, testPrepareIfPortTable15)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    uint32_t prepareCnt = 100;
    for (uint32_t i = 0; i < prepareCnt; i++) {
        string testSchema = GetFileContext("./dml/schema/if_port_with_cache_2.gmjson");
        string toReplace = "T0";
        string replacement = "T" + to_string(i);
        replaceAll(testSchema, toReplace, replacement);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, replacement.c_str()));
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, replacement.c_str()));
    }

    DestroyConnectionAndStmt(conn, stmt);
}

static uint32_t g_threadNum = 3;
static void *ThreadFunc(void *arg)
{
    GmcStmtT *stmt = (GmcStmtT *)arg;
    const char *labelName = "if_port";
    Status ret = GmcPrepareStmtByLabelNameWithCache(stmt, labelName);
    if (ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_OK) {
        EXPECT_TRUE(true);
    } else {
        EXPECT_TRUE(false);
    }
    return NULL;
}
// 016.申请不同的stmt并发GmcPrepareStmtByLabelNameWithCache开表
TEST_F(CachePrepare, testPrepareIfPortTable16)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmtArray[g_threadNum];
    CreateSyncConnectionAndStmt(&conn, &stmtArray[0]);
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmtArray[1]));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmtArray[2]));

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmtArray[0], testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";

    pthread_t threadId[g_threadNum];
    for (uint32_t i = 0; i < g_threadNum; i++) {
        int ret = pthread_create(&threadId[i], NULL, ThreadFunc, stmtArray[i]);
        ASSERT_EQ(0, ret);
    }

    for (uint32_t i = 0; i < g_threadNum; i++) {
        pthread_join(threadId[i], NULL);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmtArray[0], labelName));

    GmcFreeStmt(stmtArray[2]);
    GmcFreeStmt(stmtArray[1]);
    DestroyConnectionAndStmt(conn, stmtArray[0]);
}

// 017.gmjson中use_cache：false，gmconfig中use_cache：true，GmcPrepareStmtByLabelNameWithCache开表成功
TEST_F(CachePrepare, testPrepareIfPortTable17)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_2.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// 018.gmjson中use_cache：true，gmconfig中use_cache：false，GmcPrepareStmtByLabelNameWithCache开表失败
TEST_F(CachePrepare, testPrepareIfPortTable18)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./dml/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"use_cache": false})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcPrepareStmtByLabelNameWithCache(stmt, labelName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

static void *ThreadFunc2(void *arg)
{
    GmcStmtT *stmt = (GmcStmtT *)arg;
    const char *labelName = "if_port";
    Status ret = GmcPrepareStmtByLabelNameWithCache(stmt, labelName);
    EXPECT_EQ(ret, GMERR_OK);
    return NULL;
}
// 019.申请不同的stmt与conn并发GmcPrepareStmtByLabelNameWithCache开表
TEST_F(CachePrepare, testPrepareIfPortTable19)
{
    // 创建同步连接
    GmcConnT *connArray[g_threadNum];
    GmcStmtT *stmtArray[g_threadNum];
    for (uint32_t i = 0; i < g_threadNum; i++) {
        CreateSyncConnectionAndStmt(&connArray[i], &stmtArray[i]);
    }

    string testSchema = GetFileContext("./dml/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmtArray[0], testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";

    pthread_t threadId[g_threadNum];
    for (uint32_t i = 0; i < g_threadNum; i++) {
        int ret = pthread_create(&threadId[i], NULL, ThreadFunc2, stmtArray[i]);
        ASSERT_EQ(0, ret);
    }

    for (uint32_t i = 0; i < g_threadNum; i++) {
        pthread_join(threadId[i], NULL);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmtArray[0], labelName));

    for (uint32_t i = 0; i < g_threadNum; i++) {
        DestroyConnectionAndStmt(connArray[i], stmtArray[i]);
    }
}
