#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
# version V1.0
#
# used for build db entry
#
set -e

CUR_DIR=$(cd "$(dirname $0)";pwd)
GMDB_DIR="$CUR_DIR"/
TMP_DIR="$GMDB_DIR"/tmp

. ../../gmdb_dt_function.sh

function main() {
    if [ "$1" == "clean" ]; then
        clean_build_hisotry "st_perf_cache"
        exit 0
    fi
    dt_build_main --test st --module perf_cache "$@"
}

main "$@"
