/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
#ifndef CACHE_TEST_METHOD_H
#define CACHE_TEST_METHOD_H

#include "gmc_types.h"
#include "table_schema_struct.h"
#include "clt_stmt.h"

int32_t SeriSimpleTableObject(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize);
void SetIfPortBuf(uint32_t value, IfPortT *table);
void SetSimpleBuf(uint32_t value, SimplePortT *table);

#endif
