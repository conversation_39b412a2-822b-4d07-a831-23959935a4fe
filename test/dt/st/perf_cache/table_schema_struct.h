/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
#ifndef TABLE_SCHEMA_STRUCT_H
#define TABLE_SCHEMA_STRUCT_H

#pragma pack(1)
typedef struct ifPort {
    uint32_t tb;
    uint32_t tp;
    uint32_t unit_id;
    uint32_t port_id;
    uint32_t ifindex;
    uint32_t logicTB;
    uint32_t logicTP;
    uint32_t globalSvcIfIndex;
    uint8_t isSelf;
    uint32_t holdDownTime;
    uint32_t holdUpTime;
    uint64_t smoothVersion;
} IfPortT;

// char,unchar,int8-int64,uint8-uint64,bool,float,double,time,fixed
typedef struct simplePort {
    uint8_t field1;
    uint8_t field2;
    int8_t field3;
    int16_t field4;
    int32_t field5;
    int64_t field6;
    uint8_t field7;
    uint16_t field8;
    uint32_t field9;
    uint64_t field10;
    bool field11;
    float field12;
    double field13;
    uint64_t field14;
    uint8_t field15[100];
} SimplePortT;
#pragma pack()

#endif
