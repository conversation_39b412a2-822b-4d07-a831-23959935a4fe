project(GMDB_ST_perf_cache)

set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ddl SRC_ST_CACHE_TEST_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/dml SRC_ST_CACHE_TEST_LIST)
 
set(SRC_CACHE
    ${SRC_ST_CACHE_TEST_LIST}
)

add_executable(st_perf_cache ${SRC_CACHE} cache_test_method.cc)
target_link_libraries(st_perf_cache gmdb stub StartDbServer)
