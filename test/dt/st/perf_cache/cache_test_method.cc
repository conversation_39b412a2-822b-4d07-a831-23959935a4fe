/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
#include "cache_test_method.h"

int32_t SeriSimpleTableObject(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *ser = (GmcSeriT *)seri;
    (void)memcpy_s(destBuf, ser->bufSize, ser->obj, ser->bufSize);
    return GMERR_OK;
}

void SetIfPortBuf(uint32_t value, IfPortT *table)
{
    table->tb = value;
    table->tp = value;
    table->unit_id = value;
    table->port_id = value;
    table->ifindex = value;
    table->logicTB = value;
    table->logicTP = value;
    table->globalSvcIfIndex = value;
    table->isSelf = 1;
    table->holdDownTime = value;
    table->holdUpTime = value;
    table->smoothVersion = value;
}

void SetSimpleBuf(uint32_t value, SimplePortT *table)
{
    table->field1 = value;
    table->field2 = value;
    table->field3 = value;
    table->field4 = value;
    table->field5 = value;
    table->field6 = value;
    table->field7 = value;
    table->field8 = value;
    table->field9 = value;
    table->field10 = value;
    table->field11 = true;
    table->field12 = 1.0;
    table->field13 = 1.0;
    table->field14 = value;
    (void)memset_s(table->field15, 100, 0x1, 100);
}
