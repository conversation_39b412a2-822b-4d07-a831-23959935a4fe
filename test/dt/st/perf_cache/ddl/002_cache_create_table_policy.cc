/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: cache_create_table_policy.cc
 * Description: st test for cache ddl operation.
 * Author: Optimizer team
 * Create: 2025-7-23
 */
#include "client_common_st.h"

using namespace std;

static const int WAIT_TIME = 1000;
static pthread_t g_epollThreadId;

class CacheCreateTablePolicy : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"enableClusterHash=1\" \"compatibleV3=0\" \"userPolicyMode=2\"");
        // 导入用户白名单
        ImportAllowList();
        system("gmrule -c import_allowlist -f ./ddl/policy_files/policy_files.gmuser");
        system("gmrule -c import_policy -f ./ddl/policy_files/privilege_files.gmpolicy");
        DbSleep(WAIT_TIME);  // Wait server ready
        st_clt_init();
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
    }

    static void TearDownTestCase()
    {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {}

    virtual void TearDown()
    {}
};

// [table conf]cache: true && userPolicyMode=2
TEST_F(CacheCreateTablePolicy, testCreateIfPortTableWithPolicy)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}
