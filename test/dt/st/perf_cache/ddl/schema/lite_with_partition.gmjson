{"version": "2.0", "name": "lite_with_partition", "type": "record", "config": {"check_validity": false, "use_cache": true}, "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "part", "type": "partition"}], "keys": [{"node": "lite_with_partition", "name": "PK", "constraints": {"unique": true}, "index": {"type": "primary"}, "fields": ["F0"]}, {"node": "lite_with_partition", "name": "localhash_unique", "constraints": {"unique": true}, "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F1"]}]}