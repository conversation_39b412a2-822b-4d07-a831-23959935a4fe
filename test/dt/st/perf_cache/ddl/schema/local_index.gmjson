{"type": "record", "name": "vlabel_rangescan", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "fixed", "size": 5, "nullable": true}], "keys": [{"node": "vlabel_rangescan", "name": "pkIndex", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "vlabel_rangescan", "name": "rangeIndex", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}]}