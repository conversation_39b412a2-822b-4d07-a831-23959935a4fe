{"comment": "分配bfdSessionId", "version": "2.0", "type": "record", "name": "hpp_bfd_session_index", "config": {"check_validity": false, "use_cache": true}, "fields": [{"name": "myDiscr", "type": "uint32"}, {"name": "bfdSessionId", "type": "resource"}], "keys": [{"name": "primarykey", "index": {"type": "primary"}, "node": "hpp_bfd_session_index", "fields": ["myDiscr"], "constraints": {"unique": true}}]}