[{"version": "2.0", "name": "customer", "type": "record", "check_validity": true, "fields": [{"name": "cid", "type": "uint64"}, {"name": "married", "type": "boolean"}, {"name": "birthday", "type": "time"}, {"name": "phone", "type": "fixed", "size": 11, "nullable": false}, {"name": "bitfield8", "type": "uint8: 4", "nullable": false, "default": 5}, {"name": "bitfield16", "type": "uint16: 9", "nullable": false, "default": 10}, {"name": "bitfield32", "type": "uint32: 17", "nullable": false, "default": 1000}, {"name": "bitfield64", "type": "uint64: 33", "nullable": false, "default": 10000}], "keys": [{"name": "pk", "node": "customer", "fields": ["cid"], "index": {"type": "primary"}}, {"name": "localhash_non_unique", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "customer", "fields": ["birthday"], "constraints": {"unique": false}}]}]