{"comment": "ARP表项定义", "version": "2.0", "type": "record", "name": "arp", "config": {"check_validity": false, "use_cache": true}, "max_record_count": 512000, "fields": [{"name": "ip_address", "type": "fixed", "size": 4, "comment": "ARP的IPv4目的地址"}, {"name": "if_index", "type": "uint32", "comment": "ARP对应的三层接口索引"}, {"name": "vr_id", "type": "uint32", "comment": "ARP表出接口的所在的VR"}, {"name": "vrf_index", "type": "uint32", "comment": "ARP表出接口的所在的VRF(VNP)"}], "keys": [{"name": "arp_key", "index": {"type": "primary"}, "node": "arp", "fields": ["ip_address", "if_index"], "constraints": {"unique": true}, "comment": "主索引"}, {"name": "arp_vpn_ip_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["ip_address", "vr_id", "vrf_index"], "constraints": {"unique": true}, "comment": "VPN+IP的索引"}]}