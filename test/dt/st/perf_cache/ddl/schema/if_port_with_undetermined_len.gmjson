{"comment": "if_port", "version": "2.0", "type": "record", "name": "if_port", "config": {"check_validity": false, "support_undetermined_length": true, "use_cache": true}, "max_record_count": 4608, "fields": [{"name": "tb", "type": "uint32", "comment": "tb"}, {"name": "tp", "type": "uint32", "comment": "tp"}, {"name": "unit_id", "type": "uint32", "comment": "芯片单元id"}, {"name": "port_id", "type": "uint32", "comment": "端口id"}, {"name": "ifindex", "type": "uint32", "comment": "端口索引"}, {"name": "logicTB", "type": "uint32", "comment": "逻辑tb"}, {"name": "logicTP", "type": "uint32", "comment": "逻辑tp"}], "keys": [{"name": "if_port_pk", "index": {"type": "primary"}, "node": "if_port", "fields": ["tb", "tp"], "constraints": {"unique": true}, "comment": "tb tp 键"}, {"name": "unitport_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "if_port", "fields": ["unit_id", "port_id"], "constraints": {"unique": false}, "comment": "unit port 键"}, {"name": "logictbtp_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "if_port", "fields": ["logicTB", "logicTP"], "constraints": {"unique": false}, "comment": "逻辑tbtp 键"}]}