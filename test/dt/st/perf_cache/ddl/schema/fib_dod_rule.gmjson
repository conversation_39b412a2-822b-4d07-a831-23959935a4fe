{"type": "record", "name": "Lpm4IndexInsert", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "vr_id", "type": "uint32"}, {"name": "vrf_index", "type": "uint32"}, {"name": "dest_ip_addr", "type": "uint32"}, {"name": "mask_len", "type": "uint8"}], "keys": [{"node": "Lpm4IndexInsert", "name": "Lpm4IndexInsert_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "Lpm4IndexInsert", "name": "ip4forward_lpm", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "index": {"type": "lpm4_tree_bitmap"}, "constraints": {"unique": true}}]}