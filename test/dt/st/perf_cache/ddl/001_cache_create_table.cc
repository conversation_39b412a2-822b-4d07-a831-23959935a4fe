/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: cache_create_table.cc
 * Description: st test for cache ddl operation.
 * Author: Optimizer team
 * Create: 2025-7-18
 */
#include "client_common_st.h"
#include "tools_st_common.h"

using namespace std;

static const int WAIT_TIME = 1000;
static pthread_t g_epollThreadId;

class CacheCreateTable : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"enableClusterHash=1\" \"compatibleV3=0\" \"userPolicyMode=0\"");
        // 导入用户白名单
        ImportAllowList();
        DbSleep(WAIT_TIME);  // Wait server ready
        st_clt_init();
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
    }

    static void TearDownTestCase()
    {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {}

    virtual void TearDown()
    {}
};

// [table conf]cache: true
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, table_id, space_id, tablespace_name, namespace
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"table_id": 1, "space_id": 0, "tablespace_name": "public", "namespace": "public"})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, hash_type, init_hash_capacity
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache3)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"hash_type": "cceh", "init_hash_capacity": 1000})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, isFastReadUnCommitted, enableTableLock
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache4)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"isFastReadUnCommitted": true, "enableTableLock": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, disable_sub_back_pressure
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache5)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"disable_sub_back_pressure": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, direct_write
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache6)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"direct_write": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && byte property
TEST_F(CacheCreateTable, testCreateTableWithByteProperty)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"use_cache": true})";
    string testSchema = GetFileContext("./ddl/schema/aaa_traffic_group.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && string property
TEST_F(CacheCreateTable, testCreateTableWithStringProperty)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"use_cache": true})";
    string testSchema = GetFileContext("./ddl/schema/acl_group_def.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && node
TEST_F(CacheCreateTable, testCreateTableWithNode)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"use_cache": true})";
    string testSchema = GetFileContext("./ddl/schema/sub_node_record_add_varfield_null.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && local index
TEST_F(CacheCreateTable, testCreateTableWithLocalIndex)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/local_index.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && lpm index
TEST_F(CacheCreateTable, testCreateTableWithLpmIndex)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"use_cache": true})";
    string testSchema = GetFileContext("./ddl/schema/fib_dod_rule.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, hashcluster second index
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache7)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/arp.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "arp";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && resource property
TEST_F(CacheCreateTable, testCreateTableWithResourceColumn)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/hpp_bfd_session_index.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && auto_increment
TEST_F(CacheCreateTable, testCreateTableWithAuto)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_auto.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && rsm_tablespace_name
TEST_F(CacheCreateTable, testCreateTableWithRsmName)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_rsm_name.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && rsm_tablespace_name
TEST_F(CacheCreateTable, testCreateTableWithRsmName2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"rsm_tablespace_name": "abcd", "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && data_sync_label: true
TEST_F(CacheCreateTable, testCreateTableWithDataSync)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_data_sync.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && data_sync_label: true
TEST_F(CacheCreateTable, testCreateTableWithDataSync2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"data_sync_label":true, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && push_age_record_batch
TEST_F(CacheCreateTable, testCreateTableWithPushAge)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_push_age.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && push_age_record_batch
TEST_F(CacheCreateTable, testCreateTableWithPushAge2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"push_age_record_batch":1, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && support_undetermined_length: true
TEST_F(CacheCreateTable, testCreateTableWithUndeterminedLen)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_undetermined_len.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && support_undetermined_length: true
TEST_F(CacheCreateTable, testCreateTableWithUndeterminedLen2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"support_undetermined_length":true, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && status_merge_sub: true
TEST_F(CacheCreateTable, testCreateTableWithStatusMerge)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_status_merge.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && status_merge_sub: true
TEST_F(CacheCreateTable, testCreateTableWithStatusMerge2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"status_merge_sub":true, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && use_write_cache: true
TEST_F(CacheCreateTable, testCreateTableWithWriteCache)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_write_cache.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && use_write_cache: true
TEST_F(CacheCreateTable, testCreateTableWithWriteCache2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_write_cache":true, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && is_support_reserved_memory: true
TEST_F(CacheCreateTable, testCreateTableWithRsm)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_rsm.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && is_support_reserved_memory: true
TEST_F(CacheCreateTable, testCreateTableWithRsm2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"is_support_reserved_memory":true, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && partition property
TEST_F(CacheCreateTable, testCreateTableWithPartition)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/lite_with_partition.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && bitmap property
TEST_F(CacheCreateTable, testCreateTableWithBitmapProperty)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"use_cache": true})";
    string testSchema = GetFileContext("./ddl/schema/simpleVertex_with_bitmap.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && bitfield property
TEST_F(CacheCreateTable, testCreateTableWithBitfieldProperty)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"use_cache": true})";
    string testSchema = GetFileContext("./ddl/schema/customer.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && 连续删除一张表两次
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache8)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && 连续创建/删除表
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache9)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    uint32_t loopCnt = 10;
    for (uint32_t i = 0; i < loopCnt; i++) {
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
        const char *labelName = "if_port";
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    }
    system("rm ./ddl/schema/sysview.txt");
    system("touch ./ddl/schema/sysview.txt");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=if_port > ./ddl/schema/sysview.txt");
    char command[256];
    (void)sprintf_s(command, sizeof(command), "[ $(wc -l < ./ddl/schema/sysview.txt) -eq 1 ]");
    int result = system(command);
    EXPECT_EQ(result, 0);

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && gmimport + gmddl
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache10)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    uint32_t loopCnt = 10;
    for (uint32_t i = 0; i < loopCnt; i++) {
        system("gmimport -c vschema -f ./ddl/schema/if_port_with_cache.gmjson -t if_port");
        const char *labelName = "if_port";
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    }

    for (uint32_t i = 0; i < loopCnt; i++) {
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
        system("gmddl -c drop -t if_port");
    }

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && max_record_count为1
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache11)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    const char *labelConfig = R"({"max_record_count": 1})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && one json multi tables
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache12)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelJson = R"([
    {
    "type":"record",
    "name":"T0",
    "fields":[
        {"name":"F0", "type":"uint32"}
    ],
    "keys":[
       {
            "node":"T0",
            "name":"PK",
            "fields":["F0"],
            "index":{"type":"primary"}
        }]
    },
    {
    "type":"record",
    "name":"T1",
    "fields":[
        {"name":"F0", "type":"uint32"}
    ],
    "keys":[
       {
            "node":"T1",
            "name":"PK",
            "fields":["F0"],
            "index":{"type":"primary"}
        }]
    }
    ])";

    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson, labelConfig));
    const char *labelName = "T0";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    labelName = "T1";
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && upgrade failed
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache13)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_cache.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));
    const char *labelName = "if_port";

    char command[256] = {0};
    (void)sprintf_s(command, sizeof(command), "gmddl -c alter -f ./ddl/schema/if_port_with_cache.gmjson -u online");
    Status ret = executeCommand(command, (char *)"Alter schema upgrade unsuccessfully");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAlterVertexLabelWithName(stmt, testSchema.c_str(), true, labelName);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    (void)memset_s(command, sizeof(command), 0, sizeof(command));
    (void)sprintf_s(command, sizeof(command), "gmddl -c alter -t if_port -d sync");
    ret = executeCommand(command, (char *)"Alter schema degrade unsuccessfully");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDegradeVertexLabel(stmt, labelName, 0);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && isFastReadUncommitted: false
TEST_F(CacheCreateTable, testCreateTableWithFastRead)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port_with_fast_read.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && isFastReadUncommitted: false
TEST_F(CacheCreateTable, testCreateTableWithFastRead2)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"isFastReadUncommitted":false, "use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true && create sub
TEST_F(CacheCreateTable, testCreateTableWithSub)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/if_port.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));
    const char *labelName = "if_port";

    // 创建订阅连接
    GmcConnT *conn2;
    GmcStmtT *stmt2;
    CreateSubConnectionAndStmt(&conn2, &stmt2, "subConnection");
    GmcSubConfigT config;
    config.subsName = "subOutSimple1";
    config.configJson = R"(
    {
        "name": "subOutSimple1",
        "label_name": "if_port",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    // 用户回调
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { EXPECT_TRUE(true); };

    uint32_t received = 0;
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcSubscribe(stmt, &config, conn2, callback, &received));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn2, stmt2);
    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, unique hashcluster second index
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache14)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/arp_2.gmjson");
    const char *labelConfig = R"({"use_cache": true})";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// [table conf]cache: true, unique hashcluster second index
TEST_F(CacheCreateTable, testCreateIfPortTableWithCache15)
{
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    string testSchema = GetFileContext("./ddl/schema/arp_3.gmjson");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcCreateVertexLabel(stmt, testSchema.c_str(), NULL));

    DestroyConnectionAndStmt(conn, stmt);
}
