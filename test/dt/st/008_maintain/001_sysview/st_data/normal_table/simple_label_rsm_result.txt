index = 0
-------
  DB_ID: 0
  NAMESPACE_ID: 1048577
  TABLESPACE_ID: 0
  VERTEX_LABEL_ID: 1048697
  VERTEX_LABEL_NAME: simple_label
  VERSION: 4294967294
  REF_COUNT: 0
  IS_DELETED: 0
  MAX_RECORD_COUNT: 666
  MAX_RECORD_COUNT_CHECK: 1
  CHECK_VALIDITY: 1
  VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE
  SUBS_NUMBER: 0
  SUBS_TYPE: 0
  PARTITION_PROPERTY_ID: 4294967295
  PARTITION_PROPERTY_OFFSET: 4294967295
  SCHEMA_INFO: {
  "schema_version": 4294967294,
  "type": "record",
  "tablespace": "public",
  "id": 1048697,
  "name": "simple_label",
  "comment": "a simple vertexlabel.",
  "special_complex": false,
  "config": {
    "check_validity": true
  },
  "fields": [
    {
      "name": "F0",
      "type": "char",
      "nullable": false
    },
    {
      "name": "F1",
      "type": "uchar",
      "nullable": true
    },
    {
      "name": "F2",
      "type": "int8",
      "nullable": true
    }
  ],
  "keys": [
    {
      "node": "simple_label",
      "name": "simple_label_K0",
      "fields": [
        "F0"
      ],
      "index": {
        "type": "primary"
      },
      "constraints": {
        "unique": true
      },
      "config": {
        "hash_type": "HASH_INDEX",
        "init_hash_capacity": 999
      }
    }
  ]
}
  CONFIG_INFO: {
  "table_id": 1048697,
  "space_id": 0,
  "tablespace_name": "public",
  "persistent": false,
  "max_record_count": "666",
  "max_record_count_check": true,
  "defragmentation": false,
  "support_undetermined_length": false,
  "auto_increment": "0",
  "isFastReadUncommitted": false,
  "enableTableLock": false,
  "push_age_record_batch": 0,
  "data_sync_label": false,
  "direct_write": false,
  "status_merge_sub": 0,
  "disable_sub_back_pressure": false,
  "sub_flow_control": [
    0,
    0,
    0
  ],
  "init_hash_capacity": 999,
  "hash_type": "cceh",
  "use_write_cache": false
}
  IS_USE_RSM: 0
  IS_WARMREBOOT: 0
  WARMREBOOT_TIME_US: 0
  EDGE_LABEL_NUM: 0
  VERTEX_LABEL_TYPE: VERTEX_TYPE_NORMAL
  CONCURRENCY_CONTROL_TYPE: CONCURRENCY_CONTROL_NORMAL
  ISOLATION_LEVEL: READ_COMMITTED
  TRX_TYPE: PESSIMISTIC_TRX
  CONTAINER_TYPE: non-clustered
  IS_DATA_SYNC_LABEL: 0
  ENABLE_DIRECT_WRITE: 0
  Node[id: 29, name: OBJ_PRIVILEGE]
  Node[id: 30, name: KEYS]
    NODE_NAME: simple_label
    INDEX_NAME: simple_label_K0
    CONSTRAINTS_UNIQUE: TRUE
    Node[id: 2, name: FIELDS]
      FIELD_NAME: F0
  Node[id: 31, name: CLIENT_REFCOUNT]

fetched all records, finish!
