index = 0
-------
  DB_ID: 0
  NAMESPACE_ID: 1048577
  TABLESPACE_ID: 0
  VERTEX_LABEL_ID: 1048738
  VERTEX_LABEL_NAME: simplest
  VERSION: 0
  REF_COUNT: 0
  IS_DELETED: 0
  MAX_RECORD_COUNT: 18446744073709551615
  MAX_RECORD_COUNT_CHECK: 1
  CHECK_VALIDITY: 1
  VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE
  SUBS_NUMBER: 0
  SUBS_TYPE: 0
  PARTITION_PROPERTY_ID: 4294967295
  PARTITION_PROPERTY_OFFSET: 4294967295
  SCHEMA_INFO: {
  "schema_version": 0,
  "type": "record",
  "tablespace": "public",
  "id": 1048738,
  "name": "simplest",
  "special_complex": false,
  "config": {
    "check_validity": true
  },
  "fields": [
    {
      "name": "F0",
      "type": "char",
      "nullable": true
    },
    {
      "name": "F1",
      "type": "uchar",
      "nullable": true
    },
    {
      "name": "F2",
      "type": "int8",
      "nullable": true
    }
  ]
}
  CONFIG_INFO: {
  "table_id": 1048738,
  "space_id": 0,
  "tablespace_name": "public",
  "persistent": false,
  "max_record_count": "18446744073709551615",
  "max_record_count_check": true,
  "defragmentation": false,
  "support_undetermined_length": false,
  "auto_increment": "0",
  "isFastReadUncommitted": false,
  "enableTableLock": false,
  "push_age_record_batch": 0,
  "data_sync_label": false,
  "direct_write": false,
  "status_merge_sub": 0,
  "disable_sub_back_pressure": false,
  "sub_flow_control": [
    0,
    0,
    0
  ],
  "init_hash_capacity": 0,
  "hash_type": "cceh",
  "use_write_cache": false
}
  EDGE_LABEL_NUM: 0
  VERTEX_LABEL_TYPE: VERTEX_TYPE_NORMAL
  CONCURRENCY_CONTROL_TYPE: CONCURRENCY_CONTROL_NORMAL
  ISOLATION_LEVEL: READ_COMMITTED
  TRX_TYPE: PESSIMISTIC_TRX
  CONTAINER_TYPE: non-clustered
  IS_DATA_SYNC_LABEL: 0
  ENABLE_DIRECT_WRITE: 0
  Node[id: 26, name: OBJ_PRIVILEGE]
  Node[id: 27, name: KEYS]
  Node[id: 28, name: CLIENT_REFCOUNT]

fetched all records, finish!
