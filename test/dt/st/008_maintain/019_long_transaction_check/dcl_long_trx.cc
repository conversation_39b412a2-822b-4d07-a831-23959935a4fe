/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: long trx st
 * Author: linhuabin
 * Create: 2022-9-8
 */

#include "runtime_st_common.h"
#include "storage_st_common.h"

const int COMMAND_LEN_MAX = 128;

const char *g_longTrxLabelJson =
    R"([{
        "type":"record",
        "name":"LONG_TRX_TEST_LABEL",
        "fields":
        [
            {"name":"F0", "type":"string", "size":3}
        ],
        "keys":
        [
            {
                "node":"LONG_TRX_TEST_LABEL",
                "name":"LONG_TRX_TEST_LABEL_INDEX",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }])";

const char *g_longTrxLabelName = "LONG_TRX_TEST_LABEL";
const char *g_configNoLite = R"({"max_record_count":1000000, "isFastReadUncommitted":0})";

extern void ConnWithUserDefineTrxThreshold(
    GmcConnTypeE type, GmcConnT **connSync, GmcStmtT **stmtSync, uint32_t logThreshold, uint32_t rollBackThreshold);

class St002DclLongTrx : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"trxMonitorThreshold=1,2\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(sleepTime);
#endif
        st_clt_init();
        st_connect();
    }
    static void TearDownTestCase()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
    }
};

static uint32_t GetLongTrxRollbackThreshold(void)
{
    DbCfgValueT cfgValue;
    uint32_t value[TRX_MONITOR_THRESHOLD_NUM];
    // 长事务监控时间阈值，单位：秒
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    Status ret = DbCfgGet(cfgHandle, DB_CFG_TRX_MONITOR_THRESHOLD, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Fail to get transaction monitor threshold config.");
        return ret;
    }
    ret = CfgParamThresholdGetValue(
        cfgValue.str, (uint32_t)strlen(cfgValue.str), value, TRX_MONITOR_THRESHOLD_NUM, TRX_MONITOR_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Fail to get transaction monitor threshold value.");
        return ret;
    }
    // 长事务监控时间阈值的单位是s，需要换算为ms, 其值在config有限制，不会溢出
    return (uint32_t)(MSECONDS_IN_SECOND * value[1]);
}

void DMLCb(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

static void CommitAsyncCb(void *userData, Status status, const char *errMsg)
{
    EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, status);
    uint32_t *received = (uint32_t *)(userData);
    (*received)++;
}

static void CommonAsyncDCLCb(void *userData, Status status, const char *errMsg)
{
    EXPECT_EQ(GMERR_OK, status);
    uint32_t *received = reinterpret_cast<uint32_t *>(userData);
    (*received)++;
}

static int32_t GetTrxMonitorStat(const char *fieldName)
{
    char result[64];
    int32_t resLen = 64;
    char command[COMMAND_LEN_MAX];
    snprintf_s(command, COMMAND_LEN_MAX, COMMAND_LEN_MAX - 1,
        "gmsysview -q V\\$QRY_TRX_MONITOR_STAT |grep -E '%s' |awk -F '[:,]' '{print $2}'", fieldName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(result, resLen, pf))
        ;
    pclose(pf);
    return atoi(result);
}

// 异步长事务
TEST_F(St002DclLongTrx, TestAsyncLongTrx)
{
    pthread_t epollThreadId;
    pthread_create(&epollThreadId, NULL, GmcStartEpoll, NULL);

    int32_t logCount = GetTrxMonitorStat("LONG_TRANS_LOG_COUNT");
    int32_t rollbackCount = GetTrxMonitorStat("LONG_TRANS_ROLLBACK_COUNT");

    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt;
    CreateAsyncConnectionAndStmt(&asyncConn, &asyncStmt);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_longTrxLabelJson, g_configNoLite);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t received = 0;
    GmcAsyncRequestDoneContextT context;
    context.insertCb = DMLCb;
    context.userData = &received;

    // 插入一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(asyncStmt, g_longTrxLabelName, GMC_OPERATION_INSERT));

    char *valueF0 = (char *)"lll";
    ret = GmcSetVertexProperty(asyncStmt, "F0", GMC_DATATYPE_STRING, valueF0, strlen(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecuteAsync(asyncStmt, &context);
    EXPECT_EQ(GMERR_OK, ret);

    // 1. 悲观读已提交类型的namespace
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = "PessRC";
    nspCfg.userName = "XXuser";
    nspCfg.tablespaceName = NULL;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfgAsync(asyncStmt, &nspCfg, CommonAsyncDCLCb, &received));
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(asyncStmt, nspCfg.namespaceName, CommonAsyncDCLCb, &received));
    // 显式开启事务，悲观读已提交
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStartAsync(asyncConn, &config, CommonAsyncDCLCb, &received));

    // 再插入20条数据
    char value[3] = {'a', 'a', '\0'};
    for (int i = 0; i < 20; ++i) {
        value[1] = (char)(i + (int)'a');
        ret = GmcSetVertexProperty(asyncStmt, "F0", GMC_DATATYPE_STRING, value, strlen(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecuteAsync(asyncStmt, &context);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 无动作等超时
    DbSleep(2000);

    // 尝试提交，在异步回调接口中确认status为GMERR_TRANSACTION_ROLLBACK
    ret = GmcTransCommitAsync(asyncConn, CommitAsyncCb, &received);
    EXPECT_EQ(GMERR_OK, ret);

    char grepView[COMMAND_LEN_MAX];
    snprintf_s(grepView, COMMAND_LEN_MAX, COMMAND_LEN_MAX - 1,
        "| grep 'LONG_TRANS_LOG_COUNT: %d\\|LONG_TRANS_ROLLBACK_COUNT: %d' -c | grep '^2$' ", ++logCount,
        ++rollbackCount);

    // 查看视图
    int cmdRet = GetViewFieldResultWithCustomCmd("V\\$QRY_TRX_MONITOR_STAT", NULL, grepView);
    EXPECT_EQ(0, cmdRet);

    // 主动回滚
    ret = GmcTransRollBackAsync(asyncConn, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除namespace
    EXPECT_EQ(GMERR_OK, GmcDropNamespaceAsync(asyncStmt, nspCfg.namespaceName, CommonAsyncDCLCb, &received));

    // 2. 乐观可重复读类型的namespace
    nspCfg.namespaceName = "OptiRR";
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfgAsync(asyncStmt, &nspCfg, CommonAsyncDCLCb, &received));
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(asyncStmt, nspCfg.namespaceName, CommonAsyncDCLCb, &received));
    // 显式开启事务，乐观可重复读
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.trxType = GMC_OPTIMISTIC_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStartAsync(asyncConn, &config, CommonAsyncDCLCb, &received));

    // 无动作等超时
    DbSleep(3000);

    // 尝试提交，在异步回调接口中确认status为GMERR_TRANSACTION_ROLLBACK
    ret = GmcTransCommitAsync(asyncConn, CommitAsyncCb, &received);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf_s(grepView, COMMAND_LEN_MAX, COMMAND_LEN_MAX - 1,
        "| grep 'LONG_TRANS_LOG_COUNT: %d\\|LONG_TRANS_ROLLBACK_COUNT: %d' -c | grep '^2$' ", ++logCount,
        ++rollbackCount);

    // 查看视图
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$QRY_TRX_MONITOR_STAT", NULL, grepView);
    EXPECT_EQ(0, cmdRet);

    // 主动回滚
    ret = GmcTransRollBackAsync(asyncConn, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除namespace
    EXPECT_EQ(GMERR_OK, GmcDropNamespaceAsync(asyncStmt, nspCfg.namespaceName, CommonAsyncDCLCb, &received));

    // 3. 悲观可串行化类型的namespace
    nspCfg.namespaceName = "PessSeri";
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_SERIALIZABLE};
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfgAsync(asyncStmt, &nspCfg, CommonAsyncDCLCb, &received));
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(asyncStmt, nspCfg.namespaceName, CommonAsyncDCLCb, &received));
    // 显式开启事务，悲观可串行化
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.trxType = GMC_PESSIMISITIC_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStartAsync(asyncConn, &config, CommonAsyncDCLCb, &received));

    // 无动作等超时
    DbSleep(3000);

    // 尝试提交，在异步回调接口中确认status为GMERR_TRANSACTION_ROLLBACK
    ret = GmcTransCommitAsync(asyncConn, CommitAsyncCb, &received);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf_s(grepView, COMMAND_LEN_MAX, COMMAND_LEN_MAX - 1,
        "| grep 'LONG_TRANS_LOG_COUNT: %d\\|LONG_TRANS_ROLLBACK_COUNT: %d' -c | grep '^2$' ", ++logCount,
        ++rollbackCount);

    // 查看视图
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$QRY_TRX_MONITOR_STAT", NULL, grepView);
    EXPECT_EQ(0, cmdRet);

    // 主动回滚
    ret = GmcTransRollBackAsync(asyncConn, CommonAsyncDCLCb, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除namespace
    EXPECT_EQ(GMERR_OK, GmcDropNamespaceAsync(asyncStmt, nspCfg.namespaceName, CommonAsyncDCLCb, &received));

    ret = GmcDropVertexLabel(stmt, g_longTrxLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(asyncStmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(asyncConn));

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));

    GmcStopEpoll();
    pthread_join(epollThreadId, NULL);
}

// 同步长事务
TEST_F(St002DclLongTrx, TestSyncLongTrx)
{
    int32_t logCount = GetTrxMonitorStat("LONG_TRANS_LOG_COUNT");
    int32_t rollbackCount = GetTrxMonitorStat("LONG_TRANS_ROLLBACK_COUNT");

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_longTrxLabelJson, g_configNoLite);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_longTrxLabelName, GMC_OPERATION_INSERT));

    char *valueF0 = (char *)"lll";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, valueF0, strlen(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 显式开启事务，悲观读已提交
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = "PessRC";
    nspCfg.userName = "XXuser";
    nspCfg.tablespaceName = NULL;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfg(stmt, &nspCfg));
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, nspCfg.namespaceName));
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn, &config));

    // 无动作等超时
    DbSleep(2000);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);

    char grepView[COMMAND_LEN_MAX];
    snprintf_s(grepView, COMMAND_LEN_MAX, COMMAND_LEN_MAX - 1,
        "| grep 'LONG_TRANS_LOG_COUNT: %d\\|LONG_TRANS_ROLLBACK_COUNT: %d' -c | grep '^2$' ", ++logCount,
        ++rollbackCount);

    // 查看视图
    int cmdRet = GetViewFieldResultWithCustomCmd("V\\$QRY_TRX_MONITOR_STAT", NULL, grepView);
    EXPECT_EQ(0, cmdRet);

    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcDropNamespace(stmt, nspCfg.namespaceName));

    ret = GmcDropVertexLabel(stmt, g_longTrxLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
}

TEST_F(St002DclLongTrx, TestSyncLongTrxNoMonitor)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    ConnWithUserDefineTrxThreshold(GMC_CONN_TYPE_SYNC, &conn, &stmt, DB_MAX_UINT32, DB_MAX_UINT32);

    Status ret = GmcCreateVertexLabel(stmt, g_longTrxLabelJson, g_configNoLite);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_longTrxLabelName, GMC_OPERATION_INSERT));

    char *valueF0 = (char *)"lll";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, valueF0, strlen(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 显式开启事务，悲观读已提交
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = "PessRC";
    nspCfg.userName = "XXuser";
    nspCfg.tablespaceName = NULL;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfg(stmt, &nspCfg));
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, nspCfg.namespaceName));
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn, &config));

    // 无动作等超时
    DbSleep(2000);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcDropNamespace(stmt, nspCfg.namespaceName));

    ret = GmcDropVertexLabel(stmt, g_longTrxLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
}
