%table inpA(a:int4 , b:int4)
%table out2(a:int4 , b:int4)
%table out3(a:int4 , b:int4 , c:int4)
%function propAppend(a:int4 , b:int4 -> c:int4)
%function tupleFilter(a:int4 -> b:int4)

out2(a,b) :- inpA(a,b) , tupleFilter(a,b).
out3(a,b,c) :- out2(a,b) , propAppend(a,b,c).


%table inp1(a:int4, b:int4, c:int4)
%table out1(a:int4, b:int4, c:int4){
    tbm,
    index(0(a))
}
%function init()
%function uninit()
out1(a, b, c) :- inp1(a, b, c).
