/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
} B;

#pragma pack(0)

int32_t dtl_ext_func_func501(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func502(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func503(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func504(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func505(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func506(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func507(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func508(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func509(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func510(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func511(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func512(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func513(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func514(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func515(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func516(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func517(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func518(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func519(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func520(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func521(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func522(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func523(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func524(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func525(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func526(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func527(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func528(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func529(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func530(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func531(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func532(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func533(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func534(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func535(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func536(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func537(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func538(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func539(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func540(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func541(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func542(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func543(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func544(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func545(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func546(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func547(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func548(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func549(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func550(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func551(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func552(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func553(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func554(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func555(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func556(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func557(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func558(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func559(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func560(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func561(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func562(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func563(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func564(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func565(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func566(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func567(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func568(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func569(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func570(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func571(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func572(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func573(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func574(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func575(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func576(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func577(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func578(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func579(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func580(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func581(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func582(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func583(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func584(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func585(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func586(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func587(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func588(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func589(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func590(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func591(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func592(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func593(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func594(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func595(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func596(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func597(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func598(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func599(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func600(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func601(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func602(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func603(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func604(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func605(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func606(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func607(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func608(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func609(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func610(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func611(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func612(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func613(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func614(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func615(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func616(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func617(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func618(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func619(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func620(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func621(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func622(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func623(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func624(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func625(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func626(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func627(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func628(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func629(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func630(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func631(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func632(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func633(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func634(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func635(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func636(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func637(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func638(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func639(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func640(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func641(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func642(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func643(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func644(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func645(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func646(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func647(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func648(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func649(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func650(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func651(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func652(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func653(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func654(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func655(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func656(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func657(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func658(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func659(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func660(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func661(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func662(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func663(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func664(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func665(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func666(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func667(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func668(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func669(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func670(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func671(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func672(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func673(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func674(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func675(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func676(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func677(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func678(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func679(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func680(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func681(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func682(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func683(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func684(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func685(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func686(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func687(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func688(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func689(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func690(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func691(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func692(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func693(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func694(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func695(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func696(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func697(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func698(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func699(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func700(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func701(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func702(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func703(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func704(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func705(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func706(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func707(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func708(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func709(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func710(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func711(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func712(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func713(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func714(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func715(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func716(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func717(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func718(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func719(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func720(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func721(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func722(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func723(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func724(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func725(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func726(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func727(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func728(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func729(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func730(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func731(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func732(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func733(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func734(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func735(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func736(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func737(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func738(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func739(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func740(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func741(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func742(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func743(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func744(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func745(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func746(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func747(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func748(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func749(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func750(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func751(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func752(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func753(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func754(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func755(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func756(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func757(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func758(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func759(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func760(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func761(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func762(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func763(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func764(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func765(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func766(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func767(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func768(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func769(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func770(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func771(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func772(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func773(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func774(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func775(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func776(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func777(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func778(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func779(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func780(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func781(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func782(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func783(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func784(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func785(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func786(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func787(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func788(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func789(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func790(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func791(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func792(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func793(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func794(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func795(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func796(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func797(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func798(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func799(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func800(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func801(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func802(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func803(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func804(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func805(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func806(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func807(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func808(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func809(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func810(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func811(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func812(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func813(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func814(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func815(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func816(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func817(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func818(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func819(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func820(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func821(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func822(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func823(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func824(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func825(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func826(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func827(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func828(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func829(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func830(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func831(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func832(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func833(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func834(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func835(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func836(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func837(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func838(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func839(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func840(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func841(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func842(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func843(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func844(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func845(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func846(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func847(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func848(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func849(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func850(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func851(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func852(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func853(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func854(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func855(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func856(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func857(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func858(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func859(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func860(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func861(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func862(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func863(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func864(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func865(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func866(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func867(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func868(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func869(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func870(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func871(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func872(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func873(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func874(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func875(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func876(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func877(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func878(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func879(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func880(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func881(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func882(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func883(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func884(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func885(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func886(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func887(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func888(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func889(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func890(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func891(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func892(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func893(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func894(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func895(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func896(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func897(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func898(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func899(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func900(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func901(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func902(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func903(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func904(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func905(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func906(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func907(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func908(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func909(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func910(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func911(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func912(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func913(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func914(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func915(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func916(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func917(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func918(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func919(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func920(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func921(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func922(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func923(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func924(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func925(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func926(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func927(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func928(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func929(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func930(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func931(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func932(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func933(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func934(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func935(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func936(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func937(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func938(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func939(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func940(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func941(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func942(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func943(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func944(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func945(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func946(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func947(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func948(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func949(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func950(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func951(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func952(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func953(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func954(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func955(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func956(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func957(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func958(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func959(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func960(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func961(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func962(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func963(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func964(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func965(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func966(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func967(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func968(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func969(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func970(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func971(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func972(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func973(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func974(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func975(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func976(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func977(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func978(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func979(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func980(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func981(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func982(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func983(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func984(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func985(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func986(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func987(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func988(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func989(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func990(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func991(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func992(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func993(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func994(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func995(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func996(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func997(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func998(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func999(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1000(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1001(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1002(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1003(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1004(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1005(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1006(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1007(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1008(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1009(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1010(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1011(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1012(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1013(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1014(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1015(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1016(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1017(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1018(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1019(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1020(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1021(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1022(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1023(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1024(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
