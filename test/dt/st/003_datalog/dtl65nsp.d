namespace A {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace B {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace C {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace D {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace E {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace F {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace G {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace H {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace I {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace J {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace K {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace L {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace M {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace N {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace O {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace P {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace Q {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace R {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace S {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace T {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace U {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace V {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace W {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace X {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace Y {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace Z {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}

namespace AA {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace BB {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace CC {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace DD {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace EE {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace FF {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace GG {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace HH {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace II {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace JJ {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace KK {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace LL {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace MM {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace NN {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace OO {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace PP {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace QQ {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace RR {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace SS {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace TT {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace UU {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace VV {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace WW {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace XX {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace YY {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace ZZ {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}

namespace AAA {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace BBB {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace CCC {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace DDD {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace EEE {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace FFF {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace GGG {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace HHH {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace III {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace JJJ {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace KKK {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace LLL {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
namespace MMM {
%table tableA(a:int1, b:int2)
%table tableB(a:int1, b:int2)
tableB(a, b) :- tableA(a, b).
}
