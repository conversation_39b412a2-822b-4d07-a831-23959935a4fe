// 多字段过滤，多字段投影，多表连接，笛卡尔积综合测试
%table inp00(a:int8, b:int8, c:int8, d:int8)
%table inp01(a:int8, b:int8, c:int8, d:int8)
%table inp02(a:int8, b:int8, c:int8, d:int8)
%table inp03(a:int8, b:int8, c:int8, d:int8)
%table inp04(a:int8, b:int8, c:int8, d:int8)
%table inp05(a:int8, b:int8, c:int8, d:int8)
%table out01(a:int8, b:int8)

out01(a, b) :- inp00(a, -, -, -), inp01(-, -, b, -), inp02(1, -, b, -), inp03(-, 2, b, -), inp04(1, 2, b, 3), inp05(-, -, a, -).


// 字符串与byte过滤
%table inps1(a:int8, b:str, c:int8, d:byte128)
%table inps2(a:int8, b:str, c:int8, d:byte128)
%table inps3(a:int8, b:str, c:int8, d:byte128)
%table inps4(a:int8, b:str, c:int8, d:byte128)
%table outs1(a:int8, b:int8)
outs1(a, c) :- inps1(a, "a", c, "0xabcde").
outs1(a, c) :- inps2(a, "hhhhhhhhhh", c, "0xfff").
outs1(a, c) :- inps3(a, "hhhhhhhhhh", c, "0xfff").
outs1(a, c) :- inps4(a, "12334", -, "0xfff"),inps3(-, "hhhhhhhhhh", c, "0xfff"),inps2(a, "hhhhhhhhhh", c, "0xfff"), inps1(a, "a", c, "0xabcde").

// 投影过滤测试
%table inp1(a:int8, b:int8, c:int8, d:int8)
out01(a, b) :- inp1(a, b, 3, 2).                      
out01(a, b) :- inp1(a, b, 3, 2), inp01(a, b, -, -).
out01(a, b) :- inp1(a, -, 3, 2), inp01(a, b, -, -).
out01(a, b) :- inp1(a, -, -, b), inp01(a, b, 2, 3).
out01(a, b) :- inp1(a, b, 3, 2), inp01(2, 3, a, b).
out01(a, b) :- inp1(a, b, 3, 2), inp01(2, 3, -, b).
out01(a, b) :- inp1(a, b, -, -), inp01(2, 3, a, -).
out01(a, b) :- inp1(a, -, -, -), inp01(2, 3, b, -).
out01(a, b) :- inp1(a, 1, 2, 3), inp01(2, 3, b, -).
out01(a, b) :- inp1(a, -, -, -), inp01(-, -, b, -).
out01(a, b) :- inp1(a, -, -, -), inp01(-, -, b, -).


%table out1(a:int8, b:int8, c:int8)
out1(a, b, c) :- inp1(a, b, c, -).                    
out1(c, b, a) :- inp1(c, a, b, -).
out1(c, b, a) :- inp1(c, -, b, a).
out1(c, b, a) :- inp1(b, c, -, a).


%table inp2(a:int4, b:int8)
%table inp3(a:int8, b:int1, c:int2)
%table inp4(a:int1, b:int2, c:int4)
%table out2(a:int4, c:int1)               
out2(a, c) :- inp2(a, b), inp3(b, c, d), inp4(c, d, -). 



%table inp5(c:int4, d:int8)                             // index0 is not specified
%table inp6(a:int4, b:int8, c:int4)
{ index(0(a,b,c)) }                                     // index0 must contain all fields
%table out3(a:int4, b:int8)
out3(a, b) :- inp5(a, b).
out3(c, b) :- inp5(a, b), inp6(a, b, c).

%table inp61(a:int4, b:int4) {
    index(0(a)),
    index(1(b)),
    index(2(a,b)),
    update
}
%table inp64(a:int4, b:int4, c:int8) {
    index(0(a)),
    index(1(b)),
    index(2(a,b)),
    update_partial
}
%table out61(a:int4, b:int4)
out61(a, b) :- inp61(a, b).
out61(a, b) :- inp64(a, b, -).
out61(a, b) :- inp61(a, b), inp64(a, b, -).

%table inp71(a: int4) { max_size(3) }
%table out71(a: int4)
out71(a) :- inp71(a).
