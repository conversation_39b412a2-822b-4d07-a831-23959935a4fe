/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2022-4-6
 */

#include "gtest/gtest.h"
#include "StartDbServer.h"
#include "adpt_sleep.h"
#include "adpt_atomic.h"
#include "InitClt.h"
#include "pthread.h"
#include "runtime_st_common.h"
#include "tools_st_common.h"
#include "st_common.h"
#include "se_define.h"
#include "se_page_mgr.h"

using namespace std;

#if (defined RTOSV2 || defined RTOSV2X)
#else

const int32_t MAX_CONN = 33;
const int32_t MAX_RESERVED_CONN = 10;
const int32_t RT_ST_MAX_CONN_NUM = 100;

class St001ConnectBase : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        StartDbServer((char *)"./config/gmserver_cfg_maxconn.ini");
        ImportAllowList();
        st_clt_init();
    }
    static void TearDownTestCase()
    {
        st_clt_uninit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
    }
};

class StRuntimeThreadPool : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServer((char *)"./config/gmserver_cfg_thread_pool.ini");
        ImportAllowList();
        st_clt_init();
    }
    static void TearDownTestCase()
    {
        st_clt_uninit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
    }
};

TEST_F(St001ConnectBase, connection_connect_base_001)
{
    bool bTmp = true;
    EXPECT_TRUE(bTmp);
}

TEST_F(St001ConnectBase, connection_connect_reset_conn_002)
{
    GmcConnT *conn[1500] = {0};
    int32_t succ = 0;
    for (int32_t i = 0; i < 50; i++) {
        ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        if (conn[i] != NULL) {
            succ++;
        }
    }
    EXPECT_EQ(MAX_CONN, succ);
    for (int32_t i = 0; i < 50; i++) {
        if (conn[i] != NULL) {
            GmcDisconnect(conn[i]);
            conn[i] = NULL;
            succ--;
        }
    }
    EXPECT_EQ(0, succ);
    for (int32_t i = 0; i < 50; i++) {
        ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        if (conn[i] != NULL) {
            succ++;
        }
    }
    EXPECT_EQ(MAX_CONN, succ);
    for (int32_t i = 0; i < 50; i++) {
        if (conn[i] != NULL) {
            GmcDisconnect(conn[i]);
            conn[i] = NULL;
            succ--;
        }
    }
    EXPECT_EQ(0, succ);
}
#if (defined RTOSV2 || defined RTOSV2X || defined HPE)
#else
// 测试从conn.memCtx中申请内存超上限
TEST_F(St001ConnectBase, ClientConnectLimitMemCtx)
{
    GmcConnT *conn;
    int ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 申请512M,未超上限，返回地址
    char *buf = (char *)DbDynMemCtxAlloc(conn->memCtx, 512 * DB_MEBI);
    EXPECT_NE(buf, nullptr);

    // 再申请513M,超上限，返回空指针
    char *buf2 = (char *)DbDynMemCtxAlloc(conn->memCtx, 513 * DB_MEBI);
    EXPECT_EQ(buf2, nullptr);

    DbDynMemCtxFree(conn->memCtx, buf);

    GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(St001ConnectBase, ClientConnectLimitMemCtx2)
{
    GmcConnT *conn1;
    int ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn1);
    EXPECT_EQ(GMERR_OK, ret);

    // 申请1025M,超上限，返回空指针
    char *buf = (char *)DbDynMemCtxAlloc(conn1->memCtx, 1025 * DB_MEBI);
    EXPECT_EQ(buf, nullptr);

    GmcConnT *conn2;
    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn2);
    EXPECT_EQ(GMERR_OK, ret);

    // 连接2申请50M,成功，返回地址
    char *buf2 = (char *)DbDynMemCtxAlloc(conn1->memCtx, 50 * DB_MEBI);
    EXPECT_NE(buf2, nullptr);

    GmcDisconnect(conn1);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDisconnect(conn2);
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

TEST_F(St001ConnectBase, connection_connect_reserved_conn_003)
{
    GmcConnT *conn[MAX_CONN] = {0};
    int ret;
    int sleepCnt = 0;
    do {
        // 保证当前用例没有其他未释放连接
        ret = executeCommand((char *)"gmsysview -q V\\$DRT_CONN_STAT", "index = 1");
        if (ret == GMERR_OK) {
            sleepCnt++;
            DbSleep(1000);
        }
    } while (ret == GMERR_OK && sleepCnt < 10);
    ASSERT_NE(10, sleepCnt);
    int32_t succ = 0;
    /* 预留两个连接 */
    for (int32_t i = 0; i < MAX_CONN - 4; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        if (ret == GMERR_OK) {
            succ++;
        } else {
            break;
        }
    }
    EXPECT_EQ(MAX_CONN - 4, succ);
    /* 由于gmrule本身占一个连接，因此预留两个连接失败 */
    string args = string("gmrule -c import_allowlist -f ./json/reserve2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    ASSERT_NE(GMERR_OK, ret);

    ret = GmcDisconnect(conn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    conn[0] = NULL;

    /* 多释放一个连接，预留成功，但只预留成功st_runtime 注：由于目前实现失败会回滚，所以这里只导入一个st_runtime用户 */
    args = string("gmrule -c import_allowlist -f ./json/reserve2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    ASSERT_NE(GMERR_OK, ret);

    GmcConnT *reservedConn[2] = {0};
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);

    /* 指定gmsysview连接不上 */
    args = string("gmsysview -rc -q V\\$DRT_CONN_STAT  -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_NE(GMERR_OK, ret);

    /* 普通gmsysview连接得上 */
    args = string("gmsysview -q V\\$DRT_CONN_STAT  -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(conn[1]);
    EXPECT_EQ(GMERR_OK, ret);
    conn[1] = NULL;
    ret = GmcDisconnect(conn[2]);
    EXPECT_EQ(GMERR_OK, ret);
    conn[2] = NULL;

    args = string("gmrule -c import_allowlist -f ./json/reserve2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    /* 指定gmsysview连接得上 */
    args = string("gmsysview -rc -q V\\$DRT_CONN_STAT  -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    /* 断连 */
    for (int32_t i = 0; i < MAX_CONN; i++) {
        if (conn[i] != NULL) {
            ret = GmcDisconnect(conn[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcDisconnect(reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDisconnect(reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(St001ConnectBase, connection_connect_delete_reserved_conn_004)
{
    /* 预留四个连接 runtime:2 gmsys:2 normal:29 */
    string args = string("gmrule -c import_allowlist -f ./json/reserve2.gmuser -s usocket:/run/verona/unix_emserver");
    int ret = system(args.c_str());
    ASSERT_EQ(GMERR_OK, ret);

    GmcConnT *reservedConn[MAX_RESERVED_CONN] = {0};
    /* root:st_runtime使用两个预留连接 runtime:0 gmsys:2 normal:0 */
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除链接用户中失败，打印alarm信息
    args = string("gmrule -c remove_allowlist -f ./json/reserve2.gmuser") +
           string(" -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);

    /* 释放两个普通连接 runtime:2 gmsys:2 normal:2 */
    ret = GmcDisconnect(reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    reservedConn[0] = NULL;
    ret = GmcDisconnect(reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);
    reservedConn[1] = NULL;

    // 删除链接用户中失败，打印alarm信息
    args = string("gmrule -c remove_allowlist -f ./json/reserve2.gmuser") +
           string(" -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(St001ConnectBase, connection_connect_reserved_conn_005)
{
    /* 共计33个连接 */
    /* 预留四个连接 runtime:2 gmsys:2 normal:29 */
    string args = string("gmrule -c import_allowlist -f ./json/reserve2.gmuser -s usocket:/run/verona/unix_emserver");
    int ret = system(args.c_str());
    ASSERT_EQ(GMERR_OK, ret);

    int32_t succ = 0;
    GmcConnT *conn[MAX_CONN] = {0};
    for (int32_t i = 0; i < MAX_CONN; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        if (ret == GMERR_OK) {
            succ++;
        } else {
            break;
        }
    }
    /* 只能连接上29个连接 runtime:2 gmsys:2 normal:0 */
    EXPECT_EQ(29, succ);
    GmcConnT *reservedConn[MAX_RESERVED_CONN] = {0};
    /* root:st_runtime使用两个预留连接 runtime:0 gmsys:2 normal:0 */
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);

    /* 第三个申请失败 */
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[2]);
    EXPECT_EQ(GMERR_INSUFFICIENT_RESOURCES, ret);

    /* 释放一个连接 runtime:1 gmsys:2 normal:0 */
    ret = GmcDisconnect(reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);

    /* 能够再次连上 runtime:2 gmsys:2 normal:0 */
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);

    /* 视图能够连接 runtime:2 gmsys:2 normal:0 */
    args = string("gmsysview -rc -q V\\$DRT_CONN_STAT  -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    /* 释放两个普通连接 runtime:2 gmsys:2 normal:2 */
    ret = GmcDisconnect(conn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    conn[0] = NULL;
    ret = GmcDisconnect(conn[1]);
    EXPECT_EQ(GMERR_OK, ret);
    conn[1] = NULL;

    /* 有连接被占用，下降规格失败 runtime:2 gmsys:2 normal:2 */
    args = string("gmrule -c import_allowlist -f ./json/reserve1.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_NE(GMERR_OK, ret);

    /* 释放一个连接 runtime:1 gmsys:2 normal:2 */
    ret = GmcDisconnect(reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    reservedConn[0] = NULL;

    /* 下降规格成功 runtime:1 gmsys:2 normal:2 */
    args = string("gmrule -c import_allowlist -f ./json/reserve1.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    /* 申请失败 runtime:1 gmsys:2 normal:2 */
    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[0]);
    EXPECT_EQ(GMERR_INSUFFICIENT_RESOURCES, ret);

    /* runtime:1 gmsys:2 normal:0 */
    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[1]);
    EXPECT_EQ(GMERR_OK, ret);

    /* 释放一个连接 runtime:0 gmsys:2 normal:2 */
    ret = GmcDisconnect(reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);
    reservedConn[1] = NULL;

    /* 下降规格成功 runtime:0 gmsys:0 normal:4 */
    args = string("gmrule -c import_allowlist -f ./json/reserve0.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    /* 普通建连四次 runtime:0 gmsys:0 normal:0 */
    for (int i = 29; i < MAX_CONN; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < MAX_CONN; i++) {
        if (conn[i] != NULL) {
            GmcDisconnect(conn[i]);
        }
    }
    GmcConnT *connSync;
    GmcStmtT *stmtSync;
    CreateSyncConnectionAndStmt(&connSync, &stmtSync);
    ret = GmcDropUser(stmtSync, "root", "gmsysview");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(connSync, stmtSync);

    // 删除释放链接的用户，预期成功
    args = string("gmrule -c remove_allowlist -f ./json/reserve2.gmuser") +
           string(" -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);

    args = string("gmsysview -rc -q V\\$DRT_CONN_STAT  -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_NE(GMERR_OK, ret);
}

TEST_F(St001ConnectBase, connection_connect_reserved_conn_006)
{
    string args =
        string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    int ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn[MAX_CONN] = {0};
    for (int32_t i = 0; i < MAX_CONN - 3; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    /* 创建过的角色能够覆盖创建 */
    args = string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *reservedConn[MAX_RESERVED_CONN] = {0};

    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    args = string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);
    args = string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    ret = ConnectUseReservedConn(GMC_CONN_TYPE_SYNC, serverLocator, "root", NULL, &reservedConn[2]);
    EXPECT_EQ(GMERR_INSUFFICIENT_RESOURCES, ret);
    args = string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(reservedConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    reservedConn[0] = NULL;
    args = string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(reservedConn[1]);
    EXPECT_EQ(GMERR_OK, ret);
    reservedConn[1] = NULL;
    args = string("gmrule -c import_allowlist -f ./json/reserveSingle2.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < MAX_CONN; i++) {
        if (conn[i] != NULL) {
            GmcDisconnect(conn[i]);
        }
    }
    args = string("gmrule -c import_allowlist -f ./json/reserve0.gmuser -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    EXPECT_EQ(GMERR_OK, ret);
}

static void *RtMultiCreateUser(void *args)
{
    DB_UNUSED(args);
    string sysview =
        string("gmrule -c import_allowlist -f ./json/reserve5user.gmuser -s usocket:/run/verona/unix_emserver");
    system(sysview.c_str());
    DB_UNUSED(args);
    return NULL;
}

uint32_t g_gmdbRtStMaxConnNum = 0;

static void *RtMultiConConnect(void *args)
{
    DB_POINTER(args);
    GmcConnT **conn = (GmcConnT **)args;
    int ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, conn);
    if (ret == GMERR_OK) {
        DbAtomicInc(&g_gmdbRtStMaxConnNum);
    }
    return NULL;
}

static void *RtMultiConDisConnect(void *args)
{
    DB_UNUSED(args);
    GmcConnT *conn = (GmcConnT *)args;
    if (conn != NULL) {
        EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
        DbAtomicDec(&g_gmdbRtStMaxConnNum);
    }
    return NULL;
}

static void StLogVerifyFileLineCnt(const char *cmd, uint32_t expLineCnt, uint32_t expLineLen)
{
    bool fileExist = DbFileExist("./log/run/rst_001_connect/rst_001_connect.log");
    if (!fileExist) {
        return;
    }
    FILE *fp = popen(cmd, "r");
    if (fp == NULL) {
        EXPECT_EQ(false, true);
    }
    uint32_t lineCnt = 0;
    char line[LOG_MAX_SIZE_OF_LOG_MSG] = {0};
    bool findLine = false;
    while (fgets(line, LOG_MAX_SIZE_OF_LOG_MSG, fp) != NULL) {
        lineCnt++;
        if (strlen(line) == expLineLen) {
            findLine = true;
            break;
        }
        (void)memset_s(line, LOG_MAX_SIZE_OF_LOG_MSG, 0, LOG_MAX_SIZE_OF_LOG_MSG);
    }

    if (expLineLen != DB_INVALID_UINT32) {
        EXPECT_EQ(true, findLine);
    }
    // 不对0取余
    if (expLineCnt != DB_INVALID_UINT32 && expLineCnt != 0) {
        // 重复执行日志用例的时候，上一次的日志可能还在，因此采用取余校验
        printf("lineCnt: %d,expLineCnt: %d\n", lineCnt, expLineCnt);
        EXPECT_LE(lineCnt, expLineCnt);
    }
    pclose(fp);
}

static void RtMultiThreadConnect2Server(uint32_t expectNum)
{
    /* 用光连接 */
    int pConnNum = 40;
    int err = 0;
    GmcConnT *conn[pConnNum];
    pthread_t pConnThread[pConnNum];
    for (int i = 0; i < pConnNum; i++) {
        conn[i] = NULL;
        err = pthread_create(&pConnThread[i], NULL, RtMultiConConnect, &conn[i]);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < pConnNum; i++) {
        err = pthread_join(pConnThread[i], NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    EXPECT_EQ(expectNum, g_gmdbRtStMaxConnNum);
    for (int i = 0; i < pConnNum; i++) {
        err = pthread_create(&pConnThread[i], NULL, RtMultiConDisConnect, conn[i]);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < pConnNum; i++) {
        err = pthread_join(pConnThread[i], NULL);
        EXPECT_EQ(GMERR_OK, err);
        conn[i] = NULL;
    }
    EXPECT_EQ(0U, g_gmdbRtStMaxConnNum);
}

TEST_F(St001ConnectBase, gmsysview_alarm_reserved_conn)
{
    /* 预留四个连接 runtime:2 gmsys:2 normal:29 */
    string args = string("gmrule -c import_allowlist -f ./json/reserve2.gmuser -s usocket:/run/verona/unix_emserver");
    int ret = system(args.c_str());
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *conn[MAX_CONN] = {0};
    int32_t succ = 0;
    /* 剩下四个预留连接 */
    for (int32_t i = 0; i < MAX_CONN - 4; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        if (ret == GMERR_OK) {
            succ++;
        } else {
            break;
        }
    }
    EXPECT_EQ(MAX_CONN - 4, succ);
    args = string("gmsysview -rc alarm");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);
    /* 断连 */
    for (int32_t i = 0; i < MAX_CONN; i++) {
        if (conn[i] != NULL) {
            ret = GmcDisconnect(conn[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 删除链接用户中失败，打印alarm信息
    args = string("gmrule -c remove_allowlist -f ./json/reserve2.gmuser") +
           string(" -s usocket:/run/verona/unix_emserver");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(St001ConnectBase, connection_connect_reserved_conn_007)
{
    RtMultiThreadConnect2Server(33);
    /* 预留十个用户 */
    int pGmruleNum = 5;
    int err = 0;
    g_gmdbRtStMaxConnNum = 0;
    pthread_t pthread[pGmruleNum];
    for (int i = 0; i < pGmruleNum; i++) {
        err = pthread_create(&pthread[i], NULL, RtMultiCreateUser, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < pGmruleNum; i++) {
        err = pthread_join(pthread[i], NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    /* 反复建连 */
    for (int i = 0; i < 5; i++) {
        RtMultiThreadConnect2Server(23);
    }
}

static Status RpcReserveMsgOpHeaderStub(FixBufferT *req)
{
    return GMERR_INTERNAL_ERROR;
}

TEST_F(St001ConnectBase, free_stmt_with_problem_001)
{
    init();
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AllocStmtId(stmt);
    (void)setStubC((void *)RpcReserveMsgOpHeader, (void *)RpcReserveMsgOpHeaderStub);
    GmcFreeStmt(stmt);
    clearAllStub();
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AllocStmtId(stmt);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    finalStub();
}

static void *RtRecvModeConn(void *args)
{
    DB_UNUSED(args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    DestroyConnectionAndStmt(conn, stmt);
    DB_UNUSED(args);
    return NULL;
}

static void *RtThreadPoolConn(void *args)
{
    uint16_t connId = *(uint16_t *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    switch (connId % 3) {
        case 0:
            CreateSyncConnectionAndStmt(&conn, &stmt);
            break;
        case 1:
            CreateAsyncConnectionAndStmt(&conn, &stmt);
            break;
        case 2:
            char connName[MAX_CONN_NAME_LEN];
            snprintf_s(connName, MAX_CONN_NAME_LEN, MAX_CONN_NAME_LEN - 1, "subConnection-%u", connId);
            CreateSubConnectionAndStmt(&conn, &stmt, connName);
            break;
        default:
            return NULL;
    }

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_P(StRuntime, runtime_timeout001)
{
    system("rm -rf ./log/run/rst_001_connect/rst_001_connect.log");
    int ret;
    int32_t connNum = 4;
    GmcConnT *conn[connNum] = {0};
    for (int32_t i = 0; i < connNum; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
        DbSleep(500);
    }

    StLogVerifyFileLineCnt("cat ./log/run/rst_001_connect/rst_001_connect.log | grep -w folded", 4, DB_INVALID_UINT32);

    for (int32_t i = 0; i < connNum; i++) {
        GmcDisconnect(conn[i]);
    }
}
#endif  // end of eulur

GmcConnT *connTest = NULL;

TEST_P(StRuntime, connection_connect_sync_conn_010)
{
    int ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &connTest);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(connTest);
    EXPECT_EQ(GMERR_OK, ret);

    connTest = NULL;
}

TEST_P(StRuntime, connection_connect_get_drtWorkView_011)
{
#if (defined RTOSV2 || defined RTOSV2X)
    int32_t ret = system("gmsysview -q V\\$DRT_WORKER_STAT -s channel:");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s channel:");
    EXPECT_EQ(ret, GMERR_OK);
#else
    int32_t ret = system("gmsysview -q V\\$DRT_WORKER_STAT -s usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
#endif
}

static void RestartDbServerWithConfig(const char *cfgVal)
{
    ShutDownDbServer();
    StartDbServerWithConfig(cfgVal, true, false);
}

TEST_P(StRuntime, connection_connect_set_subs_channel_mem_size_config_012)
{
    RestartDbServerWithConfig("\"subsChannelGlobalShareMemSizeMax=2048\" \"subsChannelGlobalDynamicMemSizeMax=1\"");
    Status ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_EQ(GMERR_OK, ret);
    // [1, 2048]， 超出范围启动失败
    RestartDbServerWithConfig("\"subsChannelGlobalShareMemSizeMax=0\"");
    ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"subsChannelGlobalShareMemSizeMax=2049\"", true, false);
    ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"subsChannelGlobalDynamicMemSizeMax=0\"", true, false);
    ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"subsChannelGlobalDynamicMemSizeMax=2049\"", true, false);
    ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig(
        "\"subsChannelGlobalShareMemSizeMax=1\" \"subsChannelGlobalDynamicMemSizeMax=2048\"", true, false);
    ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_P(StRuntime, runtime_use_app_memctx)
{
    const uint32_t cmdLength = 1024;
    char cmd[cmdLength];

    memset_s(cmd, sizeof(cmd), 0, sizeof(cmd));
    snprintf_s(cmd, cmdLength, cmdLength - 1, "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"RtMsgTopShareMemCtx\"");
    int ret = executeCommand(cmd, "PARENT_CTX_NAME: AppShmContext");
    EXPECT_EQ(ret, GMERR_OK);

    memset_s(cmd, sizeof(cmd), 0, sizeof(cmd));
    snprintf_s(cmd, cmdLength, cmdLength - 1, "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"rtTopShmMemCtx\"");
    ret = executeCommand(cmd, "PARENT_CTX_NAME: AppShmContext");
    EXPECT_EQ(ret, GMERR_OK);
}

const char *g_connTestConfigJson = R"({"max_record_count":400000})";
const char *g_connTestLabelName = "T0";
const char *g_connTestLabelJson =
    R"([
        {"name":"T0",
         "type":"record",
         "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"}
            ],
        "keys":[
            {"node":"T0", "name":"K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"T0", "name":"K1", "fields":["F0"], "index":{"type":"local"}, "constraints":{"unique":true}}
            ]
        }])";

static Status StIdxInsert(GmcStmtT *stmt, int32_t i)
{
    int32_t pk = i;
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &pk, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

TEST_P(StRuntime, flowctrl_client_get_level_002)
{
    const char *cfgJson = R"({"max_record_count":100})";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char kvTableName[] = "student";
    int32_t ret = GmcKvCreateTable(stmt, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDbFlowCtrlLevelE flowCtrlLevel;
    ret = GmcGetConnFlowCtrlLevel(conn, &flowCtrlLevel);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_0, ret);

    GmcConnT *connNull = NULL;
    ret = GmcGetConnFlowCtrlLevel(connNull, &flowCtrlLevel);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    GmcConnT *flowCtrlLevelNull = NULL;
    ret = GmcGetConnFlowCtrlLevel(flowCtrlLevelNull, &flowCtrlLevel);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcKvDropTable(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void ConnCtionFlowCtrlNotice(void *args, GmcDbFlowCtrlLevelE flowCtrllevel)
{
    printf("recive flow control notice : %d", (uint8_t)flowCtrllevel);
    DB_UNUSED(args);
    return;
}

TEST_P(StRuntime, flowctrl_client_level_notice_003)
{
    const char *cfgJson = R"({"max_record_count":100})";
    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcConnOptionsT *connOptions;

    Status ret = GmcConnOptionsCreate(&connOptions);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetFlowCtrlCallback(connOptions, ConnCtionFlowCtrlNotice, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));

    GmcConnOptionsT *connOptNull = NULL;
    ret = GmcConnOptionsSetFlowCtrlCallback(connOptNull, ConnCtionFlowCtrlNotice, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    char kvTableName[] = "student";
    ret = GmcKvCreateTable(stmt, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDbFlowCtrlLevelE flowCtrlLevel;
    ret = GmcGetConnFlowCtrlLevel(conn, &flowCtrlLevel);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_0, ret);

    ret = GmcKvDropTable(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

INSTANTIATE_TEST_CASE_P(ScheduleMode, StRuntime, Values(0, 2));

class StConfigTest : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        StartDbServerWithConfig(NULL);
    }
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
    }
};

TEST_F(StConfigTest, connection_connect_set_page_size_001)
{
    ShutDownDbServer();
#ifdef FEATURE_PERSISTENCE
    StartDbServerWithConfig("\"pageSize=8\"", true, false);
    int32_t ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_MEMDATA_STAT", "PAGE_SIZE: 8192");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_DURABLE_MEMDATA_STAT", "PAGE_SIZE: 8192");
    EXPECT_EQ(GMERR_OK, ret);
#else
    StartDbServerWithConfig("\"pageSize=4\"", true, false);
    int32_t ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_MEMDATA_STAT", "PAGE_SIZE: 4096");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_DURABLE_MEMDATA_STAT", "PAGE_SIZE: 8192");
    EXPECT_NE(GMERR_OK, ret);
#endif
}

TEST_F(StConfigTest, connection_connect_set_invalid_page_size_002)
{
    // pageSize = {4, 8, 16, 32, 64}，设置范围外的值，DB启动不成功
    ShutDownDbServer();
    StartDbServerWithConfig("\"pageSize=3\"", true, false);
    int32_t ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"pageSize=5\"", true, false);
    ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"pageSize=65\"", true, false);
    ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"pageSize=NULL\"", true, false);
    ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    EXPECT_NE(GMERR_OK, ret);

    StartDbServerWithConfig("\"pageSize=\"", true, false);
    ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    EXPECT_NE(GMERR_OK, ret);
}

static int ExecuteworkerPoolCheckWorker(char *cmd, uint32_t *live_num, uint32_t *busy_num, uint32_t *free_num)
{
    char result[256] = {0};

    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        return -1;
    }

    regex_t regex_live, regex_busy, regex_free;
    regmatch_t matches[2];

    // Compile regex for LIVE_NUM, BUSY_NUM & FREE_NUM
    if (regcomp(&regex_live, "LIVE_NUM: *([0-9]+)", REG_EXTENDED) != 0) {
        return -1;
    }
    if (regcomp(&regex_busy, "BUSY_NUM: *([0-9]+)", REG_EXTENDED) != 0) {
        regfree(&regex_live);
        return -1;
    }
    if (regcomp(&regex_free, "FREE_NUM: *([0-9]+)", REG_EXTENDED) != 0) {
        regfree(&regex_live);
        regfree(&regex_busy);
        return -1;
    }

    while (fgets(result, sizeof(result), pf) != NULL) {
        result[strcspn(result, "\n")] = '\0';

        if (regexec(&regex_live, result, 2, matches, 0) == 0) {
            result[matches[1].rm_eo] = '\0';
            *live_num = strtoul(&result[matches[1].rm_so], NULL, 10);
        } else if (regexec(&regex_busy, result, 2, matches, 0) == 0) {
            result[matches[1].rm_eo] = '\0';
            *busy_num = strtoul(&result[matches[1].rm_so], NULL, 10);
        } else if (regexec(&regex_free, result, 2, matches, 0) == 0) {
            result[matches[1].rm_eo] = '\0';
            *free_num = strtoul(&result[matches[1].rm_so], NULL, 10);
        }
    }

    regfree(&regex_live);
    regfree(&regex_busy);
    regfree(&regex_free);
    pclose(pf);
    return 0;
}

#ifndef EXPERIMENTAL_GUANGQI
TEST_F(StConfigTest, connection_workerPool_check_busy_free_number_001)
{
    ShutDownDbServer();
    StartDbServerWithConfig("\"scheduleMode=2\"", true, false);
    uint32_t live_num = 0, busy_num = 0, free_num = 0;
    char cmd[] = "gmsysview -q V\\$DRT_WORKER_POOL_STAT";
    // 在该场景下存在db启动时liveNum增加但是freeNum未增加的情况，sleep1秒等db启动后再查询增加用例稳定性
    DbSleep(1000);
    int32_t ret = ExecuteworkerPoolCheckWorker(cmd, &live_num, &busy_num, &free_num);
    EXPECT_EQ(ret, GMERR_OK);
    printf("LIVE_NUM: %" PRIu32 ", BUSY_NUM: %" PRIu32 ", FREE_NUM: %" PRIu32 "\n", live_num, busy_num, free_num);
    EXPECT_NE(live_num, 0);
    // 校验liveNum >= busyNum + freeNum
    EXPECT_GE(live_num, busy_num + free_num);
}
#endif
