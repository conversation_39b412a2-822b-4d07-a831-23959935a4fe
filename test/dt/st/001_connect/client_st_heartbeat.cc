#include "client_common_st.h"

class StClientHeartbeat : public StClient {
public:
    static void SetUpTestCase()
    {
        printf("StClientHeartbeat-SetUpTestCase\n");
        StClient::SetUpTestCase();
        StClient::CltSysviewOn();
    }
    static void TearDownTestCase()
    {
        printf("StClientHeartbeat-TearDownTestCase\n");
        StClient::TearDownTestCase();
        StClient::CltSysviewOff();
    }
};

static void *LabelOptThread(void *arg)
{
    const char *label_name = "T_heart_beat";
    const char *label_config = R"({"max_record_count":1000})";
    const char *label_schema =
        R"([{
        "type":"record",
        "name":"T_heart_beat",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"T_heart_beat",
                    "name":"T_heart_beat_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    if (StClientHeartbeat::IsEulerEnv()) {
        EXPECT_EQ(GMERR_OK,
            StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollRegWithUserData));
    } else {
        EXPECT_EQ(GMERR_OK, StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollReg));
    }
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label_schema, label_config));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT));
    int F0Value = 10;
    int F1Value = 11;
    int F2Value = 12;
    int F3Value = 13;

    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetOutputFormat(stmt, "F1"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    DestroyConnectionAndStmt(conn, stmt);
    return nullptr;
}

static void *LabelOptThread2(void *arg)
{
    const char *label_name = "T_heartbeat";
    const char *label_config = R"({"max_record_count":1000})";
    const char *label_schema =
        R"([{
        "type":"record",
        "name":"T_heartbeat",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"T_heartbeat",
                    "name":"T_heartbeat_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    if (StClientHeartbeat::IsEulerEnv()) {
        EXPECT_EQ(GMERR_OK,
            StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollRegWithUserData));
    } else {
        EXPECT_EQ(GMERR_OK, StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollReg));
    }
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label_schema, label_config));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT));
    int F0Value = 10;
    int F1Value = 11;
    int F2Value = 12;
    int F3Value = 13;

    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F0Value)));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetOutputFormat(stmt, "F1"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    char kvTableName[] = "hartbeat_KV";
    EXPECT_EQ(GMERR_OK, GmcKvCreateTable(stmt, kvTableName, label_config));
    EXPECT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt, kvTableName));
    char key1[] = "zhangsan1";
    uint32_t key1Len = strlen(key1) + 1;
    int32_t value = 10;
    EXPECT_EQ(GMERR_OK, GmcKvSet(stmt, key1, key1Len, &value, sizeof(int32_t)));
    char output[128] = {};
    uint32_t outputLen = sizeof(output);
    EXPECT_EQ(GMERR_OK, GmcKvGet(stmt, key1, key1Len, output, &outputLen));

    DestroyConnectionAndStmt(conn, stmt);
    return nullptr;
}

static int32_t CltView(const char *viewName, const char *keyWord)
{
    string cmd = R"(gmsysview -q V\$)";
    cmd += viewName;
    FILE *fp = popen(cmd.c_str(), "r");
    if (fp == NULL) {
        printf("popen(%s) error.\n", cmd.c_str());
        return -1;
    }
    char cmdOutput[2048] = {0};
    while (NULL != fgets(cmdOutput, 2048, fp)) {
        if (strstr(cmdOutput, keyWord) != NULL) {
            pclose(fp);
            return 0;
        }
    }
    pclose(fp);
    return -1;
}

static void GmSysview()
{
    int32_t ret = GMERR_OK;
    // 第1次查
    ret = system("gmsysview -q V\\$CLT_PROCESS_LABEL");
    EXPECT_EQ(ret, GMERR_OK);

    // 第2次查
    DbSleep(500);
    ret = system("gmsysview -q V\\$CLT_PROCESS_LABEL");
    EXPECT_EQ(ret, GMERR_OK);

    // 修改isCltStatisEnable 为 0 by gmadmin
    system("gmadmin -s usocket:/run/verona/unix_emserver -cfgName isCltStatisEnable -cfgVal 0");

    // 第3次查
    ret = system("gmsysview -q V\\$CLT_PROCESS_LABEL");
    EXPECT_EQ(ret, GMERR_OK);

    // 修改isCltStatisEnable  为 1 by GmcSetCfg
    GmcConnT *conn;
    GmcStmtT *stmt;
    if (StClientHeartbeat::IsEulerEnv()) {
        EXPECT_EQ(GMERR_OK,
            StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollRegWithUserData));
    } else {
        EXPECT_EQ(GMERR_OK, StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollReg));
    }
    int32_t setValue = 1;
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));
    GmcSetCfg(stmt, "isCltStatisEnable", GMC_DATATYPE_INT32, &setValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 第4次查
    ret = system("gmsysview -q V\\$CLT_PROCESS_LABEL");
    EXPECT_EQ(ret, GMERR_OK);
}

#if (defined RTOSV2 || defined RTOSV2X)
// testHeartbeat001, 测试异步连接发送接收数据
TEST_F(StClientHeartbeat, testHeartbeat001)
{
    int32_t ret = GMERR_OK;
    ret = system("gmsysview -q V\\$CLT_PROCESS_LABEL");
    EXPECT_EQ(ret, GMERR_OK);

    pthread_t thread1;
    pthread_t thread2;
    ret = pthread_create(&thread1, NULL, LabelOptThread, NULL);
    pthread_join(thread1, NULL);
    ret = pthread_create(&thread2, NULL, LabelOptThread2, NULL);
    (void)ret;
    pthread_join(thread2, NULL);
    DbSleep(12000);
    ret = CltView("CLT_PROCESS_INFO", "ROCESS_NAME: st_001_connect");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$CLT_PROCESS_LABEL");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "T_heart_beat"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "T_heartbeat"));
    EXPECT_EQ(GMERR_OK, GmcKvDropTable(syncStmt, "hartbeat_KV"));
}
#endif

// testHeartbeat002, 测试开关CLT_PROCESS_LABEL视图
TEST_F(StClientHeartbeat, testHeartbeat002)
{
    pthread_t thread1;
    pthread_t thread2;
    int32_t ret = pthread_create(&thread1, NULL, LabelOptThread, NULL);
    ASSERT_EQ(0, ret);
    ret = pthread_create(&thread2, NULL, LabelOptThread2, NULL);
    ASSERT_EQ(0, ret);
    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    GmSysview();

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "T_heart_beat"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "T_heartbeat"));
    EXPECT_EQ(GMERR_OK, GmcKvDropTable(syncStmt, "hartbeat_KV"));
}

// 使用GmcRegHeartBeatEpollFuncWithUserData接口注册心跳，预期成功。
TEST_F(StClientHeartbeat, testHeartbeat003)
{
    pthread_t thread1;
    pthread_t thread2;
    GmcStopHeartbeat(NULL);  // SetUpTestCase中的心跳去初始化
    int32_t ret = GmcRegHeartBeatEpollFuncWithUserData(StClientHeartbeat::EpollRegWithUserData, &responseEpollFd);
    ASSERT_EQ(0, ret);
    EXPECT_EQ(GMERR_DUPLICATE_HEARTBEAT_REGISTER,
        GmcRegHeartBeatEpollFuncWithUserData(EpollRegWithUserData, &responseEpollFd));
    EXPECT_EQ(GMERR_DUPLICATE_HEARTBEAT_REGISTER, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    ret = pthread_create(&thread1, NULL, LabelOptThread, NULL);
    ASSERT_EQ(0, ret);
    ret = pthread_create(&thread2, NULL, LabelOptThread2, NULL);
    ASSERT_EQ(0, ret);
    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);
    GmSysview();
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "T_heart_beat"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "T_heartbeat"));
    EXPECT_EQ(GMERR_OK, GmcKvDropTable(syncStmt, "hartbeat_KV"));
}

TEST_F(StClientHeartbeat, ConnFlowCtrl)
{
    int ret = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel;
    GmcConnT *conn = NULL;
    ret = GmcGetConnFlowCtrlLevel(conn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

TEST_F(StClientHeartbeat, testTimeCostView)
{
    const char *label_name = "T_heart_beat";
    const char *label_config = R"({"max_record_num":12000})";
    const char *label_schema =
        R"([{
        "type":"record",
        "name":"T_heart_beat",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"T_heart_beat",
                    "name":"T_heart_beat_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    if (StClientHeartbeat::IsEulerEnv) {
        EXPECT_EQ(GMERR_OK,
            StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollRegWithUserData));
    } else {
        EXPECT_EQ(GMERR_OK, StClientHeartbeat::CreateSyncConnection(&conn, (EpollRegFuncT)StClientHeartbeat::EpollReg));
    }
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label_schema, label_config));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT));
    int32_t i = 0;
    printf("testTimeConsumption i:%d\n", i);
    for (; i < 10010; i++) {
        Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("testTimeConsumption i:%d\n", i);
#if (defined RTOSV2 || defined RTOSV2X)
    sleep(6);
    int32_t ret = CltView("CLT_PROCESS_TIME_CONSUMPTION", "PERIOD_TIME");
    EXPECT_EQ(ret, GMERR_OK);
#endif
    DestroyConnectionAndStmt(conn, stmt);
}
