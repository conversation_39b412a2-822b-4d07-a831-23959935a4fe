/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: system test
 * Author:
 * Create:
 */

#include <thread>
#include <vector>
#include "gtest/gtest.h"
#include "stub.h"
#include "db_option_parser.h"
#include "gmc.h"
#include "gmc_types.h"
#include "StartDbServer.h"
#include "tools_st_common.h"
#include "gmc_connection.h"
#include "tool_utils.h"
#include "tool_main.h"
#include "db_file.h"
#include "adpt_string.h"
#include "gmc_sysview.h"
#include "InitClt.h"
#include "sysview_tool.h"
#include "tools_st_common.h"
#include "adpt_sleep.h"
#include "st_common.h"

static const int MAX_CMD_SIZE = 1024;
class StGmSysviewBigObject : public testing::Test {
protected:
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"yangBigObjectSize=2\"");
        st_clt_init();
        printf("sysview all testCase begin\n");
    }
    static void TearDownTestCase()
    {
        st_clt_uninit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("ipcrm -a");
        printf("sysview all testCase end\n");
    }
    virtual void SetUp()
    {
        printf("testCase begin \n");
    }
    virtual void TearDown()
    {
        printf("testCase end \n");
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

#ifndef ASAN
// 在asan环境下，memctx对内存不做二次管理，有利于检查内存的使用问题，导致用例失败，现屏蔽该用例
const char *g_bigObjLabelName = "bigObjLabel";
static const char *g_bigObjLabelJson =
    R"([{
        "type":"record",
        "name":"bigObjLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"string", "size":2048, "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"bigObjLabel",
                    "name":"bigObjLabel_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
TEST_F(StGmSysviewBigObject, view_gmsysview_big_object_mem_peak_001)
{
    st_connect();
    const int strLenBig = 2048;
    const int strLenNormal = 1024;
    char bigPeakBefore[MAX_CMD_SIZE] = {0};
    char bigPeakAfter[MAX_CMD_SIZE] = {0};
    char bigAvgBefore[MAX_CMD_SIZE] = {0};
    char bigAvgAfter[MAX_CMD_SIZE] = {0};

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateCSModeSyncConnectionAndStmt(&conn, &stmt);
    int32_t ret = GmcCreateVertexLabel(stmt, g_bigObjLabelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_bigObjLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入大对象
    int f0ValueBig = 123;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0ValueBig, sizeof(f0ValueBig));
    int f1ValueBig = 321;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1ValueBig, sizeof(f1ValueBig));
    char f2ValueBig[strLenBig] = {0};
    memset_s(f2ValueBig, sizeof(f2ValueBig), 0xFF, sizeof(f2ValueBig) - 1);
    f2ValueBig[sizeof(f2ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, f2ValueBig, strlen(f2ValueBig));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入普通对象
    int f0ValueNormal = 1234;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0ValueNormal, sizeof(f0ValueNormal));
    int f1ValueNormal = 4321;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1ValueNormal, sizeof(f1ValueNormal));
    char f2ValueNormal[strLenNormal] = {0};
    memset_s(f2ValueNormal, sizeof(f2ValueNormal), 0xFF, sizeof(f2ValueNormal) - 1);
    f2ValueNormal[sizeof(f2ValueNormal) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, f2ValueNormal, strlen(f2ValueNormal));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 查普通对象
    ret = GmcPrepareStmtByLabelName(stmt, g_bigObjLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0ValueNormal, sizeof(f0ValueNormal));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "bigObjLabel_K0");
    EXPECT_EQ(GMERR_OK, ret);

    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_AVRAGE_DYNMEM", bigAvgBefore, MAX_CMD_SIZE);
    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_MAX_DYNMEM", bigPeakBefore, MAX_CMD_SIZE);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_AVRAGE_DYNMEM", bigAvgAfter, MAX_CMD_SIZE);
    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_MAX_DYNMEM", bigPeakAfter, MAX_CMD_SIZE);
    EXPECT_EQ(strcmp(bigAvgAfter, bigAvgBefore), 0);
    EXPECT_EQ(strcmp(bigPeakAfter, bigPeakBefore), 0);

    // 查大对象
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0ValueBig, sizeof(f0ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "bigObjLabel_K0");
    EXPECT_EQ(GMERR_OK, ret);

    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_AVRAGE_DYNMEM", bigAvgBefore, MAX_CMD_SIZE);
    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_MAX_DYNMEM", bigPeakBefore, MAX_CMD_SIZE);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_AVRAGE_DYNMEM", bigAvgAfter, MAX_CMD_SIZE);
    GetValueFromCmdResult("gmsysview -q V\\$QRY_DYNMEM", "BIG_OBJ_MAX_DYNENMEM", bigPeakAfter, MAX_CMD_SIZE);

    EXPECT_NE(strcmp(bigAvgAfter, bigAvgBefore), 0);
    EXPECT_NE(strcmp(bigPeakAfter, bigPeakBefore), 0);

    ret = GmcDropVertexLabel(stmt, g_bigObjLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static const char *g_bigObjLabelJson2 =
    R"([{
        "type":"record",
        "name":"bigObjLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"string", "size":65536, "nullable":false},
                {"name":"F2", "type":"string", "size":65536, "nullable":false},
                {"name":"F3", "type":"string", "size":65536, "nullable":false},
                {"name":"F4", "type":"string", "size":65536, "nullable":false},
                {"name":"F5", "type":"string", "size":65536, "nullable":false},
                {"name":"F6", "type":"string", "size":65536, "nullable":false},
                {"name":"F7", "type":"string", "size":65536, "nullable":false},
                {"name":"F8", "type":"string", "size":65536, "nullable":false},
                {"name":"F9", "type":"string", "size":65536, "nullable":false},
                {"name":"F10", "type":"string", "size":65536, "nullable":false},
                {"name":"F11", "type":"string", "size":65536, "nullable":false},
                {"name":"F12", "type":"string", "size":65536, "nullable":false},
                {"name":"F13", "type":"string", "size":65536, "nullable":false},
                {"name":"F14", "type":"string", "size":65536, "nullable":false},
                {"name":"F15", "type":"string", "size":65536, "nullable":false},
                {"name":"F16", "type":"string", "size":65536, "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"bigObjLabel",
                    "name":"bigObjLabel_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
TEST_F(StGmSysviewBigObject, big_object_limit_002)
{
    st_connect();
    const int strLenBig = 65536;
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateCSModeSyncConnectionAndStmt(&conn, &stmt);
    int32_t ret = GmcCreateVertexLabel(stmt, g_bigObjLabelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_bigObjLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入大对象
    int f0ValueBig = 123;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0ValueBig, sizeof(f0ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f1ValueBig[strLenBig] = {0};
    memset_s(f1ValueBig, sizeof(f1ValueBig), 0xFF, sizeof(f1ValueBig) - 1);
    f1ValueBig[sizeof(f1ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, f1ValueBig, strlen(f1ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f2ValueBig[strLenBig] = {0};
    memset_s(f2ValueBig, sizeof(f2ValueBig), 0xFF, sizeof(f2ValueBig) - 1);
    f2ValueBig[sizeof(f2ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, f2ValueBig, strlen(f2ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f3ValueBig[strLenBig] = {0};
    memset_s(f3ValueBig, sizeof(f3ValueBig), 0xFF, sizeof(f3ValueBig) - 1);
    f3ValueBig[sizeof(f3ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, f3ValueBig, strlen(f3ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f4ValueBig[strLenBig] = {0};
    memset_s(f4ValueBig, sizeof(f4ValueBig), 0xFF, sizeof(f4ValueBig) - 1);
    f4ValueBig[sizeof(f4ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, f4ValueBig, strlen(f4ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f5ValueBig[strLenBig] = {0};
    memset_s(f5ValueBig, sizeof(f5ValueBig), 0xFF, sizeof(f5ValueBig) - 1);
    f5ValueBig[sizeof(f5ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, f5ValueBig, strlen(f5ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f6ValueBig[strLenBig] = {0};
    memset_s(f6ValueBig, sizeof(f6ValueBig), 0xFF, sizeof(f6ValueBig) - 1);
    f6ValueBig[sizeof(f6ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, f6ValueBig, strlen(f6ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f7ValueBig[strLenBig] = {0};
    memset_s(f7ValueBig, sizeof(f7ValueBig), 0xFF, sizeof(f7ValueBig) - 1);
    f7ValueBig[sizeof(f7ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, f7ValueBig, strlen(f7ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f8ValueBig[strLenBig] = {0};
    memset_s(f8ValueBig, sizeof(f8ValueBig), 0xFF, sizeof(f8ValueBig) - 1);
    f8ValueBig[sizeof(f8ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, f8ValueBig, strlen(f8ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f9ValueBig[strLenBig] = {0};
    memset_s(f9ValueBig, sizeof(f9ValueBig), 0xFF, sizeof(f9ValueBig) - 1);
    f9ValueBig[sizeof(f9ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, f9ValueBig, strlen(f9ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f10ValueBig[strLenBig] = {0};
    memset_s(f10ValueBig, sizeof(f10ValueBig), 0xFF, sizeof(f10ValueBig) - 1);
    f10ValueBig[sizeof(f10ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, f10ValueBig, strlen(f10ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f11ValueBig[strLenBig] = {0};
    memset_s(f11ValueBig, sizeof(f11ValueBig), 0xFF, sizeof(f11ValueBig) - 1);
    f11ValueBig[sizeof(f11ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, f11ValueBig, strlen(f11ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f12ValueBig[strLenBig] = {0};
    memset_s(f12ValueBig, sizeof(f12ValueBig), 0xFF, sizeof(f12ValueBig) - 1);
    f12ValueBig[sizeof(f12ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, f12ValueBig, strlen(f12ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f13ValueBig[strLenBig] = {0};
    memset_s(f13ValueBig, sizeof(f13ValueBig), 0xFF, sizeof(f13ValueBig) - 1);
    f13ValueBig[sizeof(f13ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, f13ValueBig, strlen(f13ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f14ValueBig[strLenBig] = {0};
    memset_s(f14ValueBig, sizeof(f14ValueBig), 0xFF, sizeof(f14ValueBig) - 1);
    f14ValueBig[sizeof(f14ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14ValueBig, strlen(f14ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f15ValueBig[strLenBig] = {0};
    memset_s(f15ValueBig, sizeof(f15ValueBig), 0xFF, sizeof(f15ValueBig) - 1);
    f15ValueBig[sizeof(f15ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, f15ValueBig, strlen(f15ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    char f16ValueBig[strLenBig] = {0};
    memset_s(f16ValueBig, sizeof(f16ValueBig), 0xFF, sizeof(f16ValueBig) - 1);
    f16ValueBig[sizeof(f16ValueBig) - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_STRING, f16ValueBig, strlen(f16ValueBig));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    // bufSize超过1M上限，预期不成功
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcDropVertexLabel(stmt, g_bigObjLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}
#endif
