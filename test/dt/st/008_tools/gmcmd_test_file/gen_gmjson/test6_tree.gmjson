[{"version": "2.0", "type": "record", "name": "test5_tree", "fields": [{"name": "a0", "type": "int32"}, {"name": "a1", "type": "record", "fields": [{"name": "b1", "type": "record", "fields": [{"name": "c1", "type": "record", "fields": [{"name": "d1", "type": "record", "fields": [{"name": "e1", "type": "record", "fields": [{"name": "f1", "type": "record", "fields": [{"name": "g1", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "h1", "type": "int64", "nullable": true}, {"name": "h2", "type": "uint64", "nullable": true}]}]}]}]}]}]}, {"name": "b2", "type": "uint32", "nullable": true}]}, {"name": "a2", "type": "record", "vector": true, "fields": [{"name": "b2", "type": "int32", "nullable": true}, {"name": "b3", "type": "uint32", "nullable": true}]}], "keys": [{"name": "table_pk", "index": {"type": "primary"}, "node": "test5_tree", "fields": ["a0"], "constraints": {"unique": true}}]}]