[{"type": "record", "name": "gen_all_support_type_label", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "uint8", "nullable": false}, {"name": "F3", "type": "int8", "nullable": false}, {"name": "F4", "type": "uint16", "nullable": false}, {"name": "F5", "type": "int16", "nullable": false}, {"name": "F6", "type": "boolean", "nullable": false}, {"name": "F7", "type": "uint64", "nullable": false}, {"name": "F8", "type": "int64", "nullable": false}, {"name": "F9", "type": "float", "nullable": false}, {"name": "F10", "type": "double", "nullable": false}, {"name": "F11", "type": "string", "nullable": false, "size": 13}, {"name": "F12", "type": "bytes", "nullable": false, "size": 7}, {"name": "F13", "type": "fixed", "nullable": false, "size": 5}, {"name": "F14", "type": "time", "nullable": false}, {"name": "F15", "type": "uchar", "nullable": false}, {"name": "F16", "type": "char", "nullable": false}, {"name": "F17", "type": "uint8: 8", "nullable": false}, {"name": "F18", "type": "uint16: 8", "nullable": false}, {"name": "F19", "type": "uint32: 8", "nullable": false}, {"name": "F20", "type": "uint64: 8", "nullable": false}, {"name": "F21", "type": "bitmap", "size": 24, "nullable": true}], "keys": [{"node": "gen_all_support_type_label", "name": "gen_all_support_type_label_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]