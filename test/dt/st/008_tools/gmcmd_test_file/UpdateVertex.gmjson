[{"type": "record", "name": "settable", "fields": [{"name": "a0", "type": "uint32"}, {"name": "a1", "type": "uint64"}, {"name": "a2", "type": "record", "fields": [{"name": "b0", "type": "uint32"}, {"name": "b1", "type": "record", "fields": [{"name": "c0", "type": "uint32"}, {"name": "c1", "type": "record", "fields": [{"name": "f1", "type": "char", "nullable": true}, {"name": "f2", "type": "uchar", "nullable": true}, {"name": "f3", "type": "int8", "nullable": true}, {"name": "f4", "type": "uint8", "nullable": true}, {"name": "f5", "type": "int16", "nullable": true}, {"name": "f6", "type": "uint16", "nullable": true}, {"name": "f7", "type": "int32", "nullable": true}, {"name": "f8", "type": "uint32", "nullable": true}, {"name": "f9", "type": "boolean", "nullable": true}, {"name": "f10", "type": "int64", "nullable": true}, {"name": "f11", "type": "uint64", "nullable": true}, {"name": "f12", "type": "float", "nullable": true}, {"name": "f13", "type": "double", "nullable": true}, {"name": "f14", "type": "time", "nullable": true}, {"name": "f15", "type": "string", "default": "qwer<PERSON><PERSON>o", "size": 10}, {"name": "f16", "type": "fixed", "default": "ffffffffff", "size": 10}, {"name": "f17", "type": "bytes", "default": "qwer<PERSON><PERSON><PERSON>", "size": 10}, {"name": "f18", "type": "uint8: 5", "nullable": true}, {"name": "f19", "type": "uint8: 3", "nullable": true}, {"name": "f20", "type": "uint16: 7", "nullable": true}, {"name": "f21", "type": "uint16: 9", "nullable": true}, {"name": "f22", "type": "uint32: 17", "nullable": true}, {"name": "f23", "type": "uint32: 15", "nullable": true}, {"name": "f24", "type": "uint64: 33", "nullable": true}, {"name": "f25", "type": "uint64: 31", "nullable": true}]}]}]}], "keys": [{"name": "F1_PRIMARY", "index": {"type": "primary"}, "node": "settable", "fields": ["a0"]}]}]