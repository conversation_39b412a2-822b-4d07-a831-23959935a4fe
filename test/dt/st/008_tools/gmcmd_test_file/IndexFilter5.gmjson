{"version": "2.0", "type": "record", "name": "IndexFilter5", "config": {"check_validity": false}, "max_record_count": 10000, "fields": [{"name": "f0", "type": "uint32"}, {"name": "f1", "type": "uint32"}, {"name": "f2", "type": "int32"}, {"name": "f3", "type": "uint64"}, {"name": "f4", "type": "int64"}], "keys": [{"name": "PRIMARY", "index": {"type": "primary"}, "constraints": {"unique": true}, "node": "IndexFilter5", "fields": ["f0", "f2", "f3", "f4"]}]}