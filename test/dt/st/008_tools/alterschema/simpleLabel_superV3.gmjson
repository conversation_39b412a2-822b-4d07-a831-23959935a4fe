[{"type": "record", "name": "simpleLabel", "schema_version": 1, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F15", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F16", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F17", "type": "uint32: 17", "nullable": false, "default": "0x1ffff"}, {"name": "F18", "type": "uint64: 35", "nullable": false, "default": "0x7ffffffff"}, {"name": "F19", "type": "bitmap", "size": 16}, {"name": "F20", "type": "uint32", "nullable": false}, {"name": "F21", "type": "uint32", "nullable": false}, {"name": "F22", "type": "uint8: 2", "nullable": false, "default": "0x1"}], "super_fields": [{"name": "superfield_1", "fields": ["F0", "F1", "F2", "F3"]}], "keys": [{"node": "simpleLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "simpleLabel", "name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"node": "simpleLabel", "name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "simpleLabel", "name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "simpleLabel", "name": "lpm4_key", "fields": ["F3", "F20", "F21", "F7"], "index": {"type": "lpm4_tree_bitmap"}, "constraints": {"unique": true}}]}]