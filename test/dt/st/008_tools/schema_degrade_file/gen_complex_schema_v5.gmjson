[{"type": "record", "name": "complexLabel", "schema_version": 48, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"type": "record", "name": "F000", "nullable": true, "vector": true, "size": 128, "fields": [{"name": "F001", "type": "uint32"}, {"name": "F002", "type": "string"}, {"name": "F003", "type": "float", "nullable": true}, {"name": "F004", "type": "string", "nullable": true}, {"name": "F005", "type": "boolean", "nullable": true}, {"name": "F006", "type": "fixed", "nullable": true, "size": 32}, {"name": "F007", "type": "time", "nullable": true}]}, {"name": "F03", "type": "float", "nullable": true}, {"name": "F04", "type": "string", "nullable": true}, {"name": "F05", "type": "boolean", "nullable": true}, {"name": "F06", "type": "fixed", "nullable": true, "size": 32}, {"name": "F07", "type": "time", "nullable": true}], "keys": [{"node": "complexLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]