/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: 018_tools_st_thread_pool.cc
 * Description: Test thread pool of import and export for warm reboot.
 * Author: liangtingting
 * Create: 2024-01-22
 */
#ifdef WARM_REBOOT
#include "gtest/gtest.h"
#include "tool_thread_pool.h"
#include "stub.h"
#include "gmc.h"
#include "tools_st_common.h"
#include "st_common.h"
#include "InitClt.h"

#define TASK_NUM (42)
#define TASK_COST_MS (10)
#define BASE_LOOP_NUM (3000)

#define THREADS_NUM (9)

DbMemCtxT *g_threadpool_memCtx = NULL;
TlsThreadPoolT *g_threadpool = NULL;

class StThreadPool : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        StartDbServerWithConfig(NULL);
        st_clt_init();
        Status ret = st_connect();
        EXPECT_EQ(GMERR_OK, ret);
        DbMemCtxArgsT args = {0};
        g_threadpool_memCtx =
            (DbMemCtxT *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "thread pool ut", &args);
        ASSERT_TRUE(g_threadpool_memCtx != NULL);
        DbMemCtxSwitchTo(g_threadpool_memCtx);

#if defined(HPE) || defined(RTOSV2X) || defined(RTOSV2)
        char *domainName = (char *)"channel:";
#else
        char *domainName = (char *)"usocket:/run/verona/unix_emserver";
#endif
        ret = TlsThreadPoolInit(g_threadpool_memCtx, THREADS_NUM, domainName, GMC_CONN_TYPE_SYNC, &g_threadpool);
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        printf("Start to wait all threads.\n");
        Status ret = TlsThreadPoolWait(g_threadpool);
        EXPECT_EQ(GMERR_OK, ret);
        printf("Start to destroy all threads.\n");
        TlsThreadPoolDestroy(g_threadpool);
        printf("All threads have been destroyed.\n");
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
    }
};

typedef struct StArgs {
    GmcStmtT *stmt;
    uint32_t num;
} StArgsT;

void TaskHandlerCPU(void *data)
{
    StArgsT *args = (StArgsT *)data;
    EXPECT_TRUE(args->stmt != NULL);
    printf("RUN CPU-BOUND TASK : %d\n", args->num);
    int loopNum = BASE_LOOP_NUM * TASK_COST_MS;
    int k;
    for (int i = 0; i < loopNum; i++) {
        k++;
    }
}

TEST_F(StThreadPool, PostTask)
{
    size_t size = sizeof(StArgsT) * TASK_NUM;
    StArgsT *args = (StArgsT *)DbDynMemCtxAlloc(g_threadpool_memCtx, size);
    (void)memset_s(args, size, 0x00, size);
    for (int i = 0; i < TASK_NUM; i++) {
        args[i].num = i;
        Status ret = TlsThreadPoolTaskPost(g_threadpool, TaskHandlerCPU, &args[i]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            return;
        }
    }
}
#endif
