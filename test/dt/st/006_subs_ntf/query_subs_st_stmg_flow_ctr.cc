/* ****************************************************************************
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: query_subs_st_stmg_flow_ctr.cc
 * Description: test the flow control of stmg table
 * Author: qingzhu
 * Create: 2023-03-11
 **************************************************************************** */

#include <sys/wait.h>
#include <sys/epoll.h>
#include "clt_conn.h"
#include "st_common.h"
#include "gmc_types.h"
#include "query_subs_st_base.h"

using namespace std;

static bool IsEulerEnv(void)
{
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    return true;
#else
    return false;
#endif
}

static pthread_t g_responseEpollThreadId;
static int g_responseEpollFd;
static pthread_t g_timeoutEpollThreadId;
static int g_timeoutEpollFd;

class StQuerySubsStmgFlowCtr : public StTestSuitBase {
protected:
    static int TimeoutEpollReg(int fd, GmcEpollCtlTypeE type)
    {
        switch (type) {
            case GMC_EPOLL_ADD: {
                struct epoll_event event;
                event.data.fd = fd;
                event.events = EPOLLIN;
                return epoll_ctl(g_timeoutEpollFd, EPOLL_CTL_ADD, fd, &event);
            }
            case GMC_EPOLL_DEL:
                return epoll_ctl(g_timeoutEpollFd, EPOLL_CTL_DEL, fd, NULL);
            default:
                return -1;
        }
    }
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems("\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;"
                       "shareMemory:70,80,80,85,85,90;subscribeQueue:8,10,12,15,17,20\" \"clientServerFlowControl=1\" "
                       "\"userPolicyMode=0\" ");
        SetDefaultModConf(false);
        StartServerByCfgItems();
        g_cltSameProcessOpen = true;
        CreateAndStartEpoll(&g_responseEpollThreadId, &g_responseEpollFd);
        CreateAndStartEpoll(&g_timeoutEpollThreadId, &g_timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
        GmcSignalRegisterNotify();
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", g_responseEpollFd, g_timeoutEpollFd);
    }
    static void TearDownTestCase()
    {
        g_cltSameProcessOpen = false;
        StopAndDestroyEpoll(g_responseEpollThreadId, &g_responseEpollFd);
        StopAndDestroyEpoll(g_timeoutEpollThreadId, &g_timeoutEpollFd);
        StopServer();
    }
};

typedef struct {
    uint32_t countM;
    uint32_t countD;
    bool wait;
    uint32_t waitTime;
    uint32_t *startIdx;
    uint32_t idxMax;
} StQueryFlowCtrCallDataT;

TEST_F(StQuerySubsStmgFlowCtr, testSubPushStMgVertexOnInsertFlowCtr)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *labelCfgJson = R"({
        "max_record_count":100000,
        "push_age_record_batch":20,
        "isFastReadUncommitted":false,
        "defragmentation":false,
        "status_merge_sub":true
        })";

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "subs_type": "status_merge",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["old object"]}
                ]
        })";

    unsigned int i = 0;
    StQueryFlowCtrCallDataT para = {0};
    para.wait = true;
    para.waitTime = 3;
    // recv buf:default :65536 item=3*4=12;
    // 极端情况下客户端即使不收数据，客户端的buf里也能够容纳最多5461条数据，然后才能导致服务端发送队列数据积压
    // 此时服务端才能开始计算队列占比，设置的过压是8%，基数是10000，计算出来是至少需要800条
    // 所以初步设置为6500
    // 但在全量构建环境里，环境对数据的积压能力远超这个，实际测试出来的值如下：
    // [ RUN      ] StQueryFlowCtr.testSubPushVertexOnInsertFlowCtr
    // *para->startIdx=30000
    // l1Count = 16057, failCount = 0
    // [       OK ] StQueryFlowCtr.testSubPushVertexOnInsertFlowCtr (4060 ms)
    para.idxMax = 16048;
    para.startIdx = &i;

    auto callback = [](GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData) {
        StQueryFlowCtrCallDataT *para = reinterpret_cast<StQueryFlowCtrCallDataT *>(userData);

        // 一般的环境负载没多大，预期的Ns钟应该能够把数据推送到服务端完成数据的插入,这样的分层判断只是为了验证是这样的原因导致的
        while (para->wait) {
            sleep(para->waitTime);
            printf("*para->startIdx=%u\n", *para->startIdx);

            // 在负载较大的环境上，可能Ns后还没有完成对应数据量的插入，没能成功构造数据积压
            while (*para->startIdx < para->idxMax) {
                sleep(para->waitTime);
                printf("*para->startIdx=%u\n", *para->startIdx);
            }
            para->wait = false;
        }
        if (info->eventType == GMC_SUB_EVENT_MODIFY) {
            EXPECT_EQ(1u, info->labelCount);
            para->countM++;
        } else {
            para->countD++;
        }
    };

    ret = GmcSubscribe(stmt, &config, subChan, callback, &para);
    EXPECT_EQ(GMERR_OK, ret);

    int l1Count = 0;
    int okCount = 0;
    int failCount = 0;
    uint32_t total = 30000;
    const char *lastErr = NULL;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
    for (i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        switch (ret) {
            case GMERR_COMMON_STREAM_OVERLOAD:
                lastErr = GmcGetLastError();
                EXPECT_STREQ("Flow control. Failed to insert vertex.", lastErr);
                l1Count++;
                break;
            case GMERR_OK:
                okCount++;
                break;
            default:
                lastErr = GmcGetLastError();
                printf("flow ctr other error [%s]\n", lastErr);
                failCount++;
        }
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_DELETE));
    constexpr auto indexName = "subLabel1_K0";
    ret = GmcSetIndexKeyName(stmt, indexName);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        switch (ret) {
            case GMERR_COMMON_STREAM_OVERLOAD:
                lastErr = GmcGetLastError();
                EXPECT_STREQ("Flow control. Failed to delete vertex.", lastErr);
                l1Count++;
                break;
            case GMERR_OK:
                okCount++;
                break;
            default:
                lastErr = GmcGetLastError();
                printf("flow ctr other error [%s]\n", lastErr);
                failCount++;
        }
    }

    sleep(10);
    printf("l1Count = %d, ok count =%d failCount = %d countM %d countD %d\n", l1Count, okCount, failCount, para.countM,
        para.countD);

    EXPECT_EQ(l1Count, 0);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQuerySubsStmgFlowCtr, testSubPushVertexOnInsertFlowCtr)
{
    int32_t clientQryStatisticEnable = 1;
    EXPECT_EQ(GMERR_OK, GmcSetCltCfg("clientQryStatisticEnable", GMC_DATATYPE_INT32, &clientQryStatisticEnable,
                            (uint32_t)sizeof(int32_t)));
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *labelCfgJson = R"({
        "max_record_count":100000,
        "push_age_record_batch":20,
        "isFastReadUncommitted":false,
        "defragmentation":false
        })";

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["old object"]}
                ]
        })";

    unsigned int i = 0;
    StQueryFlowCtrCallDataT para = {0};
    para.wait = true;
    para.waitTime = 3;
    // recv buf:default :65536 item=3*4=12;
    // 极端情况下客户端即使不收数据，客户端的buf里也能够容纳最多5461条数据，然后才能导致服务端发送队列数据积压
    // 此时服务端才能开始计算队列占比，设置的过压是8%，基数是10000，计算出来是至少需要800条
    // 所以初步设置为6500
    // 但在全量构建环境里，环境对数据的积压能力远超这个，实际测试出来的值如下：
    // [ RUN      ] StQueryFlowCtr.testSubPushVertexOnInsertFlowCtr
    // *para->startIdx=30000
    // l1Count = 16057, failCount = 0
    // [       OK ] StQueryFlowCtr.testSubPushVertexOnInsertFlowCtr (4060 ms)
    para.idxMax = 16048;
    para.startIdx = &i;

    auto callback = [](GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData) {
        StQueryFlowCtrCallDataT *para = reinterpret_cast<StQueryFlowCtrCallDataT *>(userData);

        // 一般的环境负载没多大，预期的Ns钟应该能够把数据推送到服务端完成数据的插入,这样的分层判断只是为了验证是这样的原因导致的
        while (para->wait) {
            sleep(para->waitTime);
            printf("*para->startIdx=%u\n", *para->startIdx);

            // 在负载较大的环境上，可能Ns后还没有完成对应数据量的插入，没能成功构造数据积压
            while (*para->startIdx < para->idxMax) {
                sleep(para->waitTime);
                printf("*para->startIdx=%u\n", *para->startIdx);
            }
            para->wait = false;
        }
        if (info->eventType == GMC_SUB_EVENT_MODIFY) {
            EXPECT_EQ(1u, info->labelCount);
            para->countM++;
        } else {
            para->countD++;
        }
    };

    ret = GmcSubscribe(stmt, &config, subChan, callback, &para);
    EXPECT_EQ(GMERR_OK, ret);

    int l1Count = 0;
    int okCount = 0;
    int failCount = 0;
    uint32_t total = 100000;
    const char *lastErr = NULL;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
    for (i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        switch (ret) {
            case GMERR_COMMON_STREAM_OVERLOAD:
                lastErr = GmcGetLastError();
                EXPECT_STREQ("Overflow control. Sending request reached limit", lastErr);
                l1Count++;
                break;
            case GMERR_OK:
                okCount++;
                break;
            default:
                lastErr = GmcGetLastError();
                // printf("flow ctr other error [%s]\n", lastErr);
                failCount++;
        }
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_DELETE));
    constexpr auto indexName = "subLabel1_K0";
    ret = GmcSetIndexKeyName(stmt, indexName);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        switch (ret) {
            case GMERR_COMMON_STREAM_OVERLOAD:
                lastErr = GmcGetLastError();
                EXPECT_STREQ("Overflow control. Sending request reached limit", lastErr);
                l1Count++;
                break;
            case GMERR_OK:
                okCount++;
                break;
            default:
                lastErr = GmcGetLastError();
                printf("flow ctr other error [%s]\n", lastErr);
                failCount++;
        }
    }
    sleep(10);
    printf("l1Count = %d, ok count =%d failCount = %d countM %d countD %d\n", l1Count, okCount, failCount, para.countM,
        para.countD);

    EXPECT_GT(l1Count, 0);
    system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    system("gmsysview -q V\\$CLT_PROCESS_TIME_CONSUMPTION");
    system("gmsysview -q V\\$CLT_PROCESS_LABEL");

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

class StSubsFlowCtrl : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems("\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;"
                       "shareMemory:70,80,80,85,85,90;subscribeQueue:8,10,12,15,17,20\" \"clientServerFlowControl=1\" "
                       "\"userPolicyMode=0\" \"maxConnNum=1024\" \"flowControlSleepTime=100,200,1000\" ");
        SetDefaultModConf(false);
        StartServerByCfgItems();
    }
    static void TearDownTestCase()
    {
        StopServer();
    }
};

typedef struct {
    uint32_t count;
    uint32_t strBaseSize;
    char *strBaseData;
    bool isSleep;
} CallBackParaT;

void SubsCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userdata)
{
    CallBackParaT *callBackpara = (CallBackParaT *)userdata;
    if (callBackpara->isSleep) {
        sleep(2);
    }
    callBackpara->count++;
}

TEST_F(StSubsFlowCtrl, test_share_msg_node_flow_control)
{
    const char *labelCfgJson = R"({
        "max_record_count":1000,
        "push_age_record_batch":20,
        "isFastReadUncommitted":false,
        "defragmentation":false,
        "status_merge_sub":true
        })";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    GmcConnT *subChan[1024];

    char subConnNameC[20] = "testSubC";
    char subName[30] = {0};
    char subConfigJson[500] = {0};
    char labelName[30] = {0};
    GmcSubConfigT config;
    Status ret;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    char subLabel[1024] = {0};
    for (uint32_t i = 0; i < 100; i++) {
        sprintf(subLabel, R"([{
        "type":"record",
        "name":"subLabel%d",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"subLabel%d",
                    "name":"subLabel1_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])",
            i, i);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, subLabel, labelCfgJson));
    }

    for (uint32_t i = 0; i < 500; i++) {
        sprintf(subConnNameC, "subConnNameC%d", i);
        ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnNameC, &subChan[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    CallBackParaT callBackpara = {0};
    callBackpara.isSleep = true;
    for (uint32_t i = 0; i < 100; i++) {
        // 每张表10个订阅
        for (uint32_t j = 0; j < 5; j++) {
            sprintf(subName, "subVertexLabel%d_%d", i, j);
            sprintf(subConfigJson, R"({
            "label_name":"subLabel%d",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["old object"]}
                ]
        })",
                i);
            config.subsName = subName;
            config.configJson = subConfigJson;
            EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &config, subChan[i * 5 + j], SubsCallback, &callBackpara));
        }
    }
    // insert
    for (uint32_t i = 0; i < 100; i++) {
        uint32_t total = 1000;
        sprintf(labelName, "subLabel%d", i);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT));
        for (unsigned int k = 0; k < total; k++) {
            // insert
            int32_t f0Value = k;
            ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &f0Value, 4);
            EXPECT_EQ(GMERR_OK, ret);
            int32_t f1Value = k + 1;
            ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &f1Value, 4);
            EXPECT_EQ(GMERR_OK, ret);
            int32_t f2Value = k + 2;
            ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_INT32, &f2Value, 4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(syncStmt);
            if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                break;
            }
        }
    }

    for (uint32_t i = 0; i < 100; i++) {
        for (uint32_t j = 0; j < 5; j++) {
            ret = GmcDisconnect(subChan[i * 5 + j]);
            EXPECT_EQ(GMERR_OK, ret);
            sprintf(subName, "subVertexLabel%d_%d", i, j);
            (void)GmcUnSubscribe(syncStmt, subName);
        }
    }

    for (uint32_t i = 0; i < 100; i++) {
        sprintf(labelName, "subLabel%d", i);
        (void)GmcDropVertexLabel(syncStmt, labelName);
    }

    DestroyConnectionAndStmt(syncConn, syncStmt);
}
/********************表级别流控******************/
class StSubsNewFlowCtrlVtxLabelLevel1 : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems(
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;"
            "shareMemory:70,80,80,85,85,90;subscribeQueue:45,50,65,70,75,80\" \"clientServerFlowControl=0;0;0;0\" "
            "\"userPolicyMode=0\"");
        SetDefaultModConf(false);
        StartServerByCfgItems();
    }
    static void TearDownTestCase()
    {
        StopServer();
    }
};
// 1. 基础校验，测试 sub_flow_control 校验是否生效 --
// 未开启全局流控或者全局流控为0（老流控场景），不允许开sub_flow_control 测试sub_flow_control元数据配置是否生效
TEST_F(StSubsNewFlowCtrlVtxLabelLevel1, test_vertexLabel_level_flow_control_config1)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnVertexFlowControl";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "config":{ "sub_flow_control":[100, 200, 1000]},
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 配置表级流控策略
    const char *labelCfgJson0 = R"({
        "max_record_count":100000,
        "sub_flow_control": [100, 200, 300]
        })";
    const char *labelCfgJson1 = R"({
        "max_record_count":100000,
        "sub_flow_control": [100, 200, 300]
        })";
    const char *labelCfgJson2 = R"({
        "max_record_count":100000,
        "sub_flow_control": 1
        })";

    // 未开启全局流控，不允许开启表级流控
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson0);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson1);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // "sub_flow_control":1 参数不合法，必须是合法的数组类型
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson2);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // kv表配置
    const char *kvName = "SubFlowControlKv";
    const char *kvConfig = "{\"max_record_count\":10000, \"sub_flow_control\": 2}";
    ret = GmcKvCreateTable(syncStmt, kvName, kvConfig);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    const char *kvConfig1 = "{\"max_record_count\":10000, \"sub_flow_control\": [100, 200, 1000]}";
    ret = GmcKvCreateTable(syncStmt, kvName, kvConfig1);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    const char *kvConfig2 = "{\"max_record_count\":10000}";
    ret = GmcKvCreateTable(syncStmt, kvName, kvConfig2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(syncStmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// sub_flow_control对老流控不生效，仍然会返回错误码
class StSubsNewFlowCtrlVtxLabelLevel2 : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems(
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;"
            "shareMemory:70,80,80,85,85,90;subscribeQueue:45,50,65,70,75,80\" \"clientServerFlowControl=0;0;0;1\" "
            "\"userPolicyMode=0\"");
        SetDefaultModConf(false);
        StartServerByCfgItems();
    }
    static void TearDownTestCase()
    {
        StopServer();
    }
};
TEST_F(StSubsNewFlowCtrlVtxLabelLevel2, test_vertexLabel_level_flow_control_config2)
{
    GmcConnT *subChan = NULL;
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetConnName(connOptions, "subConnVertexLevelOldFlowControl");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetSubMsgRingSize(connOptions, 4 * 1024u);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOptions, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    // 配置表级流控策略
    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "config":{ "sub_flow_control": [100, 200, 300] },
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 不配置表级流控策略
    const char *labelCfgJson0 = R"({ "max_record_count":100000 })";
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson0);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    const char *subLabelJson2 =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *labelCfgJson2 = R"({ "max_record_count":100000 })";
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson2, labelCfgJson2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, syncStmt);
}

#ifndef ASAN
#ifndef RTOSV2X
// 2. 功能校验 -- 单操作、批操作
class StSubsNewFlowCtrlVtxLabelLevel3 : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems("\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;"
                       "shareMemory:70,80,80,85,85,90;subscribeQueue:45,50,65,70,75,80\" \"clientServerFlowControl=1\" "
                       "\"userPolicyMode=0\" \"flowControlSleepTime=10000,20000,100000\"");
        SetDefaultModConf(false);
        StartServerByCfgItems();
    }
    static void TearDownTestCase()
    {
        StopServer();
    }
};

// 单操作：配置表级别流控，写该表被流控的时间应收到表级别流控影响，不受全局配置影响，check(sleepTime);
TEST_F(StSubsNewFlowCtrlVtxLabelLevel3, test_vertexLabel_level_dml)
{
    GmcConnT *subChan = NULL;
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetConnName(connOptions, "subConnVertexLevelOldFlowControl");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetSubMsgRingSize(connOptions, 4 * 1024u);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOptions, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "config":{ "sub_flow_control": [100000, 200000, 1000000] },
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 配置表级流控策略
    const char *labelCfgJson0 = R"({ "max_record_count":100000 })";
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "is_reliable":true,
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object"]},
                    {"type":"delete", "msgTypes":["old object"]}
                ]
        })";
    CallBackParaT cbPar = {0};
    cbPar.isSleep = true;
    ret = GmcSubscribe(syncStmt, &config, subChan, SubsCallback, &cbPar);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, subLabelName, GMC_OPERATION_INSERT);
    uint32_t count = 2 * 1024 + 50;  // 触发一级流控数量
    for (int i = 0; i < count; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_1;
    ret = GmcGetConnFlowCtrlLevel(syncConn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_1, flowCtrlLevel);
    printf("flow control, level 1\n");

    uint64_t startTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, startTime %" PRIu64 "\n", startTime);
    int32_t val = 3000;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    uint64_t endTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, endTime - startTime %" PRIu64 "\n", endTime - startTime);
    // 大于全局流控sleepTime 10000
    EXPECT_GT(endTime - startTime, 10000);
    // 约等于表级别流控sleepTime 100000
    EXPECT_LE(endTime - startTime, 110000);
    cbPar.isSleep = false;
    WAIT_WHILE_TIMEOUT(cbPar.count != count + 1, 5);
    EXPECT_EQ(cbPar.count, count + 1);

    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // kv表
    const char *kvName = "SubFlowControlKv";
    const char *kvConfig = "{\"max_record_count\":10000, \"sub_flow_control\": [1000, 2000, 10000]}";
    ret = GmcKvCreateTable(syncStmt, kvName, kvConfig);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT subConfigKv;
    subConfigKv.subsName = "subKv1";
    subConfigKv.configJson = R"(
    {
        "label_name": "SubFlowControlKv",
        "comment": "kv subscription",
        "events": [{ "type": "set", "msgTypes":["new object", "old object"]}],
        "is_reliable": true,
        "retry": true
    })";
    cbPar.isSleep = true;
    ret = GmcSubscribe(syncStmt, &subConfigKv, subChan, SubsCallback, &cbPar);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(syncStmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; ++i) {
        char key[32] = {0};
        uint32_t value = i;
        (void)sprintf_s(key, sizeof(key), "key_%" PRIu32 "", i);
        ret = GmcKvSet(syncStmt, key, strlen(key) + 1, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcGetConnFlowCtrlLevel(syncConn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_1, flowCtrlLevel);
    printf("flow control, level 1\n");

    startTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, startTime %" PRIu64 "\n", startTime);
    char key[32] = {0};
    uint32_t value = 4096;
    (void)sprintf_s(key, sizeof(key), "key_%" PRIu32 "", 4096);
    ret = GmcKvSet(syncStmt, key, strlen(key) + 1, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    endTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, endTime - startTime %" PRIu64 "\n", endTime - startTime);
    // 约等于表级流控sleepTime
    EXPECT_GE(endTime - startTime, 1000);
    // 小于全局级别流控sleepTime 10000
    EXPECT_LT(endTime - startTime, 10000);

    cbPar.isSleep = false;

    ret = GmcKvDropTable(syncStmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 单操作：测试二级流控、三级流控，写该表被流控的时间应收到表级别流控影响，不受全局配置影响，check(sleepTime);
TEST_F(StSubsNewFlowCtrlVtxLabelLevel3, test_vertexLabel_level_multi_dml)
{
    GmcConnT *subChan = NULL;
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetConnName(connOptions, "subConnVertexLevelOldFlowControl");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetSubMsgRingSize(connOptions, 4 * 1024u);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOptions, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "config":{ "sub_flow_control": [500, 1000, 1500] },
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 配置表级流控策略
    const char *labelCfgJson0 = R"({ "max_record_count":100000 })";
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "is_reliable":true,
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object"]},
                    {"type":"delete", "msgTypes":["old object"]}
                ]
        })";
    CallBackParaT cbPar = {0};
    cbPar.isSleep = true;
    ret = GmcSubscribe(syncStmt, &config, subChan, SubsCallback, &cbPar);
    EXPECT_EQ(GMERR_OK, ret);

    // 1级流控
    ret = GmcPrepareStmtByLabelName(syncStmt, subLabelName, GMC_OPERATION_INSERT);
    uint32_t count = 2 * 1024 + 50;  // 触发一级流控数量
    for (int i = 0; i < count; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_1;
    ret = GmcGetConnFlowCtrlLevel(syncConn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_1, flowCtrlLevel);
    printf("flow control, level 1");

    uint64_t startTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, startTime %" PRIu64 "\n", startTime);
    int32_t val = 2099;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    uint64_t endTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, endTime - startTime %" PRIu64 "\n", endTime - startTime);
    // 小于全局流控sleepTime 10000
    EXPECT_LT(endTime - startTime, 10000);
    // 约等于表级别流控sleepTime 500
    EXPECT_GE(endTime - startTime, 500);

    // 2级流控
    ret = GmcPrepareStmtByLabelName(syncStmt, subLabelName, GMC_OPERATION_INSERT);
    uint32_t count2 = 1024 + 50;  // 触发一级流控数量
    uint32_t base = 2100;
    for (int i = base; i < base + count2; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_2;
    ret = GmcGetConnFlowCtrlLevel(syncConn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_2, flowCtrlLevel);
    printf("flow control, level 2\n");

    startTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, startTime %" PRIu64 "\n", startTime);
    val = 30000;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    endTime = DbGetNsec() / NSECONDS_IN_USECOND;
    printf("test flow control, endTime - startTime %" PRIu64 "\n", endTime - startTime);
    // 小于全局1级流控sleepTime 10000
    EXPECT_LT(endTime - startTime, 10000);
    // 约等于表级别2级流控sleepTime 1000
    EXPECT_GE(endTime - startTime, 1000);

    // 3级流控
    ret = GmcPrepareStmtByLabelName(syncStmt, subLabelName, GMC_OPERATION_INSERT);
    uint32_t count3 = 500 + 50;  // 触发一级流控数量
    base = 3200;
    for (int i = base; i < base + count3; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_3;
    ret = GmcGetConnFlowCtrlLevel(syncConn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_3, flowCtrlLevel);

    startTime = DbGetNsec() / NSECONDS_IN_USECOND;
    val = 40000;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &val, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    endTime = DbGetNsec() / NSECONDS_IN_USECOND;
    // 小于全局1级流控sleepTime 10000
    EXPECT_LT(endTime - startTime, 5000);
    // 约等于表级别2级流控sleepTime 1500
    EXPECT_GE(endTime - startTime, 1500);

    cbPar.isSleep = false;
    WAIT_WHILE_TIMEOUT(cbPar.count != count + count2 + count3 + 3, 5);
    EXPECT_EQ(cbPar.count, count + count2 + count3 + 3);

    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 批操作：配置表级别流控，写该表被流控的时间应收到表级别流控影响，不受全局配置影响，check(sleepTime);
class StSubsNewFlowCtrlVtxLabelLevel4 : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems("\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;"
                       "shareMemory:70,80,80,85,85,90;subscribeQueue:45,50,65,70,75,80\" \"clientServerFlowControl=1\" "
                       "\"userPolicyMode=0\" \"flowControlSleepTime=80000,90000,100000\"");
        SetDefaultModConf(false);
        StartServerByCfgItems();
    }
    static void TearDownTestCase()
    {
        StopServer();
    }
};
TEST_F(StSubsNewFlowCtrlVtxLabelLevel4, test_vertexLabel_level_batch_dml)
{
    GmcConnT *subChan = NULL;
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetConnName(connOptions, "subConnVertexLevelOldFlowControl");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetSubMsgRingSize(connOptions, 1024u);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOptions, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "config":{ "sub_flow_control": [100000, 200000, 1000000] },
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 配置表级流控策略
    const char *labelCfgJson0 = R"({ "max_record_count":100000 })";
    ret = GmcCreateVertexLabel(syncStmt, subLabelJson, labelCfgJson0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "is_reliable":true,
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object"]},
                    {"type":"delete", "msgTypes":["old object"]}
                ]
        })";
    CallBackParaT cbPar = {0};
    cbPar.isSleep = true;
    ret = GmcSubscribe(syncStmt, &config, subChan, SubsCallback, &cbPar);
    EXPECT_EQ(GMERR_OK, ret);

    // kv表
    const char *kvName = "SubFlowControlKv";
    const char *kvConfig = "{\"max_record_count\":10000, \"sub_flow_control\": [1000,2000,10000]}";
    ret = GmcKvCreateTable(syncStmt, kvName, kvConfig);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT subConfigKv;
    subConfigKv.subsName = "subKv1";
    subConfigKv.configJson = R"(
    {
        "label_name": "SubFlowControlKv",
        "comment": "kv subscription",
        "events": [{ "type": "set", "msgTypes":["new object", "old object"]}],
        "is_reliable": true,
        "retry": true
    })";
    cbPar.isSleep = true;
    ret = GmcSubscribe(syncStmt, &subConfigKv, subChan, SubsCallback, &cbPar);
    EXPECT_EQ(GMERR_OK, ret);

    // 1. 先写到触发服务端1级流控
    ret = GmcPrepareStmtByLabelName(syncStmt, subLabelName, GMC_OPERATION_INSERT);
    uint32_t count = 512 + 50;  // 触发一级流控数量
    for (int i = 0; i < count; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_1;
    ret = GmcGetConnFlowCtrlLevel(syncConn, &flowCtrlLevel);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_DB_FLOW_CTRL_LEVEL_1, flowCtrlLevel);

    // 2. 批量操作
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, subLabelName, GMC_OPERATION_INSERT);
    count = 50;
    for (int i = 1000; i < 1000 + count; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcKvPrepareStmtByLabelName(syncStmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; ++i) {
        char key[32] = {0};
        uint32_t value = i;
        (void)sprintf_s(key, sizeof(key), "key_%" PRIu32 "", i);

        ret = GmcKvInputToStmt(syncStmt, key, strlen(key) + 1, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, syncStmt, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
    }

    uint64_t startTime = DbGetNsec() / NSECONDS_IN_USECOND;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t endTime = DbGetNsec() / NSECONDS_IN_USECOND;

    // 小于全局级别流控sleepTime 80000
    EXPECT_LT(endTime - startTime, 40000);
    // 小于vl表级别流控sleepTime 100000
    EXPECT_LT(endTime - startTime, 50000);

    cbPar.isSleep = false;

    ret = GmcKvDropTable(syncStmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}
#endif
#endif
