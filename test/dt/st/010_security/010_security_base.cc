/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2022-4-6
 */

#include "gtest/gtest.h"

class St010SecurityBase : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

TEST_F(St010SecurityBase, 010_security_base_001)
{
    bool bTmp = true;
    EXPECT_TRUE(bTmp);
}
