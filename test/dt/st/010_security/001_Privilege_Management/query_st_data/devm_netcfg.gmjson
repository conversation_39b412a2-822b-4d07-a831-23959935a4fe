{"comment": "MAC", "version": "2.0", "type": "record", "name": "devm_netcfg", "config": {"check_validity": false}, "max_record_count": 512, "fields": [{"name": "Chassis_ID", "type": "uint32", "comment": "ID1"}, {"name": "Slot_ID", "type": "uint32", "comment": "ID2"}, {"name": "CPU_ID", "type": "uint32", "comment": "CPU ID"}, {"name": "CPU_Port", "type": "uint32", "comment": "Port ID"}, {"name": "IP", "type": "uint32", "comment": "IP"}, {"name": "MAC", "type": "fixed", "size": 6, "comment": "inner_MAC"}, {"name": "Self_CPU", "type": "uint32", "comment": "localCPU"}, {"name": "Logiacl_SlotID", "type": "uint32"}, {"name": "board_type", "type": "uint32"}], "keys": [{"name": "netcfg_key", "index": {"type": "primary"}, "node": "devm_netcfg", "fields": ["Chassis_ID", "Slot_ID", "CPU_ID", "CPU_Port"], "constraints": {"unique": true}}, {"name": "netcfg_hashkey", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "devm_netcfg", "fields": ["Logiacl_SlotID"], "constraints": {"unique": false}}]}