{"comment": "vlan", "version": "2.0", "type": "record", "name": "vlan_voicevlan_security", "config": {"check_validity": false}, "max_record_count": 65535, "fields": [{"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "ifindex", "type": "uint32", "comment": "ifindex"}, {"name": "unit", "type": "uint32", "comment": "unit"}, {"name": "denyid", "type": "uint32", "comment": "denyid"}, {"name": "secenable", "type": "uint32", "comment": "secenable"}], "keys": [{"name": "key_vrid_unit_ifindex_index", "index": {"type": "primary"}, "node": "vlan_voicevlan_security", "fields": ["vrid", "ifindex", "unit"], "constraints": {"unique": true}, "comment": "vlan_voicevlan_security index"}, {"name": "vlan_voicevlan_security_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vlan_voicevlan_security", "fields": ["ifindex"], "constraints": {"unique": false}, "comment": "match vlan_voicevlan_security table"}]}