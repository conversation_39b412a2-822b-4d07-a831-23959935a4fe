{"comment": "vrf_info", "version": "2.0", "type": "record", "name": "vrf", "config": {"check_validity": false}, "max_record_count": 65536, "fields": [{"name": "vr_id", "type": "uint32"}, {"name": "vrf_id", "type": "uint32"}, {"name": "vrf_name", "type": "fixed", "size": 32}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "time_stamp_create", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "vrf", "fields": ["vr_id", "vrf_id"], "constraints": {"unique": true}}, {"name": "localhash_name", "index": {"type": "hashcluster"}, "node": "vrf", "fields": ["vr_id", "vrf_name"], "constraints": {"unique": true}}, {"name": "vrid_hashcluster_key", "index": {"type": "hashcluster"}, "node": "vrf", "fields": ["vr_id"], "comment": "基于VRID遍历获取vrf索引"}], "super_fields": [{"name": "all_data", "fields": ["vr_id", "vrf_id", "vrf_name", "app_source_id", "table_smooth_id", "app_obj_id", "app_version"]}]}