{"comment": "BA映射", "version": "2.0", "type": "record", "name": "qos_ba", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "vird_id", "type": "uint16", "comment": "虚拟路由"}, {"name": "ds_id", "type": "uint32", "comment": "DiffServ域索引"}, {"name": "ba_type", "type": "uint8", "comment": "BA类型"}, {"name": "ba_value", "type": "uint8", "comment": "BA值"}, {"name": "en_service_class", "type": "uint8", "comment": "PHB行为。"}, {"name": "en_color", "type": "uint8", "comment": "标记颜色"}, {"name": "app_source_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "保留字段"}, {"name": "app_version", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "qos_ba_pk", "index": {"type": "primary"}, "node": "qos_ba", "fields": ["vird_id", "ds_id", "ba_type", "ba_value"], "constraints": {"unique": true}, "comment": "主键索引"}]}