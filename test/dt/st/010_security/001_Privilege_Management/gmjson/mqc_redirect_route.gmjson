{"version": "2.0", "type": "record", "name": "mqc_redirect_route", "config": {"check_validity": false}, "max_record_count": 32768, "fields": [{"name": "vr_id", "type": "uint32", "comment": "VR 索引"}, {"name": "vrf_id", "type": "uint32", "comment": "VPN 索引"}, {"name": "ip_type", "type": "uint32", "comment": "IP类型"}, {"name": "ip_address", "type": "fixed", "size": 16, "comment": "IP地址"}, {"name": "out_ifindex", "type": "uint32", "comment": "出口索引"}, {"name": "tunnel_ifindex", "type": "uint32", "comment": "隧道索引"}, {"name": "state", "type": "uint32", "comment": "状态"}, {"name": "fwd_type", "type": "uint16", "comment": "端口类型"}, {"name": "tb", "type": "uint16", "comment": "TB"}, {"name": "tp", "type": "uint16", "comment": "TP"}, {"name": "vsi_id", "type": "uint16", "comment": "是否要封装VNI"}, {"name": "aib_index", "type": "uint32", "comment": "预留对齐使用"}, {"name": "dvp", "type": "uint32", "comment": "出口索引"}, {"name": "if_index", "type": "uint32"}, {"name": "version", "type": "uint32", "comment": "版本"}, {"name": "vlan", "type": "uint16", "comment": "vlan信息"}, {"name": "inner_vlan", "type": "uint16", "comment": "内层vlan信息"}, {"name": "tunnelType", "type": "uint32", "comment": "隧道类型"}, {"name": "nhp_index", "type": "uint32", "comment": "NHP索引,AR上使用"}, {"name": "local", "type": "uint32", "comment": "是否本地优先转发"}], "keys": [{"name": "mqc_rdr_route_key", "index": {"type": "primary"}, "node": "mqc_redirect_route", "fields": ["vr_id", "vrf_id", "ip_type", "ip_address", "out_ifindex", "tunnel_ifindex"], "constraints": {"unique": true}, "comment": "重定向路由关键字名称"}], "super_fields": [{"name": "route_key", "fields": ["vr_id", "vrf_id", "ip_type", "ip_address", "out_ifindex", "tunnel_ifindex", "state", "fwd_type", "tb", "tp", "vsi_id", "aib_index", "dvp", "if_index", "version", "vlan", "inner_vlan", "tunnelType", "nhp_index", "local"]}]}