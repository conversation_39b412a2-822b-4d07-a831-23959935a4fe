{"comment": "AutoDfdWhite578", "version": "2.0", "type": "record", "name": "hsec_defend_autodfdwhite", "config": {"check_validity": false}, "max_record_count": 1536, "fields": [{"name": "vs_id", "type": "uint32", "comment": "暂无用"}, {"name": "policy_id", "type": "uint32", "comment": "策略ID"}, {"name": "white_id", "type": "uint32", "comment": "白名单编号"}, {"name": "acl_num", "type": "uint32", "comment": "白名单对应的ACL"}, {"name": "acl_group", "type": "uint32", "comment": "acl group"}, {"name": "acl_family", "type": "uint32", "comment": "acl family"}, {"name": "ifindex", "type": "uint32", "comment": "索引"}], "keys": [{"name": "autodfdwhite_pk", "index": {"type": "primary"}, "node": "hsec_defend_autodfdwhite", "fields": ["vs_id", "policy_id", "white_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}