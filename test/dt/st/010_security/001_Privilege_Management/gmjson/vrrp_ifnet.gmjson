{"comment": "vrrp接口下配置信息", "version": "2.0", "type": "record", "name": "vrrp_ifnet", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "net_type", "type": "uint32"}, {"name": "ttl_check_flag", "type": "uint8"}, {"name": "if_type", "type": "uint8"}, {"name": "recover_delay", "type": "uint16"}, {"name": "super_vlan_id", "type": "uint16"}, {"name": "send_subvlan_id", "type": "uint16"}, {"name": "ifgroup_id", "type": "uint32"}, {"name": "arp_send_simple", "type": "uint32"}, {"name": "vr_id", "type": "uint32"}, {"name": "app_src_id", "type": "uint32"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "vrrp_ifnet", "fields": ["ifindex", "net_type"], "constraints": {"unique": true}}]}