{"comment": "port num per uint * slot num:  256*16", "version": "2.0", "type": "record", "name": "mtlk_uplink", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "global_group_id", "type": "uint32"}], "keys": [{"name": "pk_ifindex_grpid", "index": {"type": "primary"}, "node": "mtlk_uplink", "fields": ["ifindex", "global_group_id"], "constraints": {"unique": true}}, {"name": "pk_ifindex", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mtlk_uplink", "fields": ["ifindex"], "constraints": {"unique": false}}, {"name": "pk_grpid", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mtlk_uplink", "fields": ["global_group_id"], "constraints": {"unique": false}}]}