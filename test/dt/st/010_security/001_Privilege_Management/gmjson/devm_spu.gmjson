{"comment": "SPU板为区分子卡与CPU的概念使用该表项", "version": "2.0", "type": "record", "name": "devm_spu", "config": {"check_validity": false}, "max_record_count": 512, "fields": [{"name": "Chassis_ID", "type": "uint32", "comment": "框号"}, {"name": "Slot_ID", "type": "uint32", "comment": "槽号"}, {"name": "CPU_ID", "type": "uint32", "comment": "CPU号"}, {"name": "CPU_Hard_Type", "type": "uint32", "comment": "CPU硬件类型"}, {"name": "CPU_Elabel", "type": "uint32", "comment": "CPU电子标签包装类型"}, {"name": "CPU_Reg_Status", "type": "uint32", "comment": "CPU注册状态"}, {"name": "CPU_Present", "type": "uint32", "comment": "CPU在位状态"}, {"name": "CPU_Power_Status", "type": "uint32", "comment": "CPU上电状态"}, {"name": "CPU_Isolated_Status", "type": "uint32", "default": 0, "comment": "CPU隔离状态"}, {"name": "Logic_SlotID", "type": "uint32", "comment": "逻辑槽位号"}], "keys": [{"name": "spu_primary_key", "index": {"type": "primary"}, "node": "devm_spu", "fields": ["Chassis_ID", "Slot_ID", "CPU_ID"], "constraints": {"unique": true}}, {"name": "spu_localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "devm_spu", "fields": ["Logic_SlotID"], "constraints": {"unique": false}}]}