{"comment": "vrrp 选板结果", "version": "2.0", "type": "record", "name": "vrrp_sel_result", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "net_type", "type": "uint32"}, {"name": "vrrp_id", "type": "uint8"}, {"name": "reason", "type": "uint8"}, {"name": "resv", "type": "uint16"}, {"name": "seq_num", "type": "uint32"}, {"name": "pri_node_grpid", "type": "uint32"}, {"name": "slv_node_grpid", "type": "uint32"}, {"name": "app_srcid", "type": "uint32"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "vrrp_sel_result", "fields": ["ifindex", "net_type", "vrrp_id"], "constraints": {"unique": true}}, {"name": "appsrcpid_localhash_key", "index": {"type": "hashcluster"}, "node": "vrrp_sel_result", "fields": ["net_type", "app_srcid"], "constraints": {"unique": false}}]}