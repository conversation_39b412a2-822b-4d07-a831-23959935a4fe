{"comment": "table of cid prefix", "version": "2.0", "type": "record", "name": "cid_prefix", "special_complex": true, "config": {"check_validity": false}, "max_record_count": 102400, "fields": [{"name": "nodeID", "type": "uint32", "comment": "nodeID"}, {"name": "ulCmCibIdxPrefixBitCount", "type": "uint32", "comment": "Cm Cib Index Prefix Bit Count"}, {"name": "ulCmCibIdxPrefixProcSelf", "type": "uint32", "comment": "Cm Cib Index Prefix Proc Self"}, {"name": "ulCmCibIdxPrefixCount", "type": "uint32", "comment": "Cm Cib Index Prefix Count"}, {"name": "ulCmCibIdxPrefixValueBase", "type": "uint32", "comment": "Cm Cib Index Prefix Value Base"}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}, {"name": "stCmCibIdxPrefixTbl", "type": "record", "array": true, "size": 1024, "comment": "Cm Cib Index Prefix Table", "fields": [{"name": "ucIfUsed", "type": "uint8", "comment": "Used Flag"}, {"name": "ucIfProcAlive", "type": "uint8", "comment": "Alive Flag"}, {"name": "usVcpuID", "type": "uint16", "default": 65535, "comment": "VcpuID"}, {"name": "ulIdxPfx", "type": "uint32", "comment": "Index Prefix"}, {"name": "ulNodeID", "type": "uint32", "default": 4294967295, "comment": "NodeID"}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "cid_prefix", "fields": ["nodeID"], "constraints": {"unique": true}, "comment": "key of cid prefix"}]}