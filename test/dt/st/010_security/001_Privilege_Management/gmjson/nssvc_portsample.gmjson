{"comment": "NS采样信息", "version": "2.0", "type": "record", "name": "nssvc_portsample", "config": {"check_validity": false}, "max_record_count": 131072, "fields": [{"name": "vrId", "type": "uint32"}, {"name": "ifIndex", "type": "uint32"}, {"name": "tb", "type": "uint16"}, {"name": "tp", "type": "uint16"}, {"name": "direction", "type": "uint32"}, {"name": "ipv4Enable", "type": "uint8"}, {"name": "ipv6Enable", "type": "uint8"}, {"name": "sampleTypeV4", "type": "uint32"}, {"name": "sampleValueV4", "type": "uint32"}, {"name": "sampleTypeV6", "type": "uint32"}, {"name": "sampleValueV6", "type": "uint32"}, {"name": "ipv4Record", "type": "uint32"}, {"name": "ipv6Record", "type": "uint32"}, {"name": "vxlanRecord", "type": "uint32"}, {"name": "ifIndex16", "type": "uint32"}, {"name": "mainIfIndex", "type": "uint32"}, {"name": "lagId", "type": "uint32"}, {"name": "portType", "type": "uint32"}, {"name": "pvId", "type": "uint16"}, {"name": "vrfId", "type": "uint16"}, {"name": "subIfType", "type": "uint16"}, {"name": "vlanDomain", "type": "uint32"}, {"name": "ovid", "type": "uint16"}, {"name": "ivid", "type": "uint16"}, {"name": "unit", "type": "uint32"}, {"name": "port", "type": "uint32"}, {"name": "sampleId", "type": "uint32"}, {"name": "enpSampleId", "type": "uint32"}], "keys": [{"name": "pk", "index": {"type": "primary"}, "node": "nssvc_portsample", "fields": ["vrId", "ifIndex", "tb", "tp", "direction"], "constraints": {"unique": true}}, {"name": "IfIndexHash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "nssvc_portsample", "fields": ["vrId", "ifIndex", "direction"], "constraints": {"unique": false}}, {"name": "SampleIdHash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "nssvc_portsample", "fields": ["sampleId"], "constraints": {"unique": false}}, {"name": "MainIfIndexHash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "nssvc_portsample", "fields": ["mainIfIndex"], "constraints": {"unique": false}}]}