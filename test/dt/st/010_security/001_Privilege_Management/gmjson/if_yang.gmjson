{"comment": "telemetry快采订阅任务管理", "version": "2.0", "type": "record", "name": "if_yang", "config": {"check_validity": false}, "max_record_count": 300, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟设备id"}, {"name": "taskid", "type": "uint32", "comment": "任务编号"}, {"name": "appid", "type": "uint32", "comment": "app id"}, {"name": "path", "type": "fixed", "size": 512, "comment": "xpath的名称"}, {"name": "interval", "type": "uint32", "comment": "采样时间间隔"}, {"name": "encoding", "type": "uint8", "comment": "编码方式：JSON和GPB"}, {"name": "optype", "type": "uint8", "comment": "下发或删除task"}, {"name": "checkflag", "type": "uint8", "comment": "检查标记"}, {"name": "depth", "type": "uint8", "comment": "解析xpath的深度，当前快采只支持一层"}, {"name": "isselfdefevt", "type": "uint8", "comment": "自定义事件"}, {"name": "redundent", "type": "uint8", "comment": "是否支持冗余"}, {"name": "heartbeat<PERSON><PERSON><PERSON>", "type": "uint32", "comment": "心跳周期"}, {"name": "suppressPeriod", "type": "uint32", "comment": "抑制周期"}, {"name": "conditionCnt", "type": "uint32", "comment": "查询条件数量"}, {"name": "tlmcondition", "type": "record", "array": true, "size": 32, "comment": "查询条件", "fields": [{"name": "fieldname", "type": "fixed", "size": 65, "comment": "字段名称"}, {"name": "valuetype", "type": "uint8", "comment": "字段值"}, {"name": "valuesize", "type": "uint16", "comment": "字段长度"}, {"name": "value", "type": "fixed", "size": 33, "comment": "数据内容"}]}, {"name": "thresholdcnt", "type": "uint32", "comment": "门限数量"}, {"name": "thresholdoprelation", "type": "uint32", "comment": "门限"}, {"name": "tlmth<PERSON>old", "type": "record", "array": true, "size": 2, "comment": "门限内容", "fields": [{"name": "fieldname", "type": "fixed", "size": 65, "comment": "字段名称"}, {"name": "oper", "type": "uint8", "comment": "操作类型"}, {"name": "value", "type": "uint32", "comment": "数据内容"}]}], "keys": [{"name": "if_yang_pk", "index": {"type": "primary"}, "node": "if_yang", "fields": ["vrid", "taskid"], "constraints": {"unique": true}, "comment": "primary key"}]}