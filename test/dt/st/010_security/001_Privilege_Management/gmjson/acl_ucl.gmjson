{"comment": "ACL UCL表", "version": "2.0", "type": "record", "name": "acl_ucl", "config": {"check_validity": true}, "max_record_count": 128000, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟路由器的索引"}, {"name": "acl_index", "type": "uint32", "comment": "ACL的索引"}, {"name": "acl_group_id", "type": "uint32", "comment": "ACLGroup的索引"}, {"name": "vrf_index", "type": "uint32", "nullable": true, "comment": "平台Vrf的索引"}, {"name": "acl_priority", "type": "uint32", "comment": "ACL的优先级"}, {"name": "cond_mask", "type": "uint32", "comment": "条件掩码"}, {"name": "time_range_id", "type": "uint32", "nullable": true, "comment": "Time Range的索引"}, {"name": "time_range_status", "type": "uint8", "nullable": true, "comment": "Time Range的激活状态"}, {"name": "acl_action_type", "type": "uint8", "comment": "ACL动作类型"}, {"name": "src_ip", "type": "uint32", "nullable": true, "comment": "源IP地址"}, {"name": "src_ip_mask", "type": "uint32", "nullable": true, "comment": "源IP地址掩码"}, {"name": "dest_ip", "type": "uint32", "nullable": true, "comment": "目的IP地址"}, {"name": "dest_ip_mask", "type": "uint32", "nullable": true, "comment": "目的IP地址掩码"}, {"name": "s_ippool_index", "type": "uint32", "nullable": true, "comment": "源IP池的索引"}, {"name": "d_ippool_index", "type": "uint32", "nullable": true, "comment": "目的IP池的索引"}, {"name": "b_src_port", "type": "uint16", "nullable": true, "comment": "源起始端口号"}, {"name": "e_src_port", "type": "uint16", "nullable": true, "comment": "源终止端口号"}, {"name": "b_dst_port", "type": "uint16", "nullable": true, "comment": "目的起始端口"}, {"name": "e_dst_port", "type": "uint16", "nullable": true, "comment": "目的终止端口"}, {"name": "s_port_pool_id", "type": "uint32", "nullable": true, "comment": "源端口池的索引"}, {"name": "d_port_pool_id", "type": "uint32", "nullable": true, "comment": "目的端口池索引"}, {"name": "any_flag", "type": "uint8", "nullable": true, "comment": "匹配任意规则的标志"}, {"name": "protocol", "type": "uint8", "nullable": true, "comment": "协议"}, {"name": "established", "type": "uint8", "nullable": true, "comment": "tcp establish"}, {"name": "frag_type", "type": "uint8", "nullable": true, "comment": "分片类型"}, {"name": "tos", "type": "uint8", "nullable": true, "comment": "TOS"}, {"name": "tcp_flag", "type": "uint8", "nullable": true, "comment": "TCP标志"}, {"name": "tcp_flagmask", "type": "uint8", "nullable": true, "comment": "TCP标志掩码"}, {"name": "log_flag", "type": "uint8", "nullable": true, "comment": "log使能"}, {"name": "icmp_type", "type": "uint8", "nullable": true, "comment": "ICMP类型"}, {"name": "icmp_code", "type": "uint8", "nullable": true, "comment": "ICMP码"}, {"name": "dscp", "type": "uint8", "nullable": true, "comment": "DSCP(Diffserv Code Point)"}, {"name": "ip_prec", "type": "uint8", "nullable": true, "comment": "IP优先级"}, {"name": "src_group_id", "type": "uint16", "nullable": true, "comment": "源GroupID"}, {"name": "dst_group_id", "type": "uint16", "nullable": true, "comment": "目的GroupID"}, {"name": "fqdn", "type": "string", "nullable": true, "size": 65, "comment": "FQDN名字认证"}], "keys": [{"name": "acl_ucl_pk", "index": {"type": "primary"}, "node": "acl_ucl", "fields": ["vrid", "acl_index", "acl_group_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "acl_ucl_pri_pk", "index": {"type": "local"}, "node": "acl_ucl", "fields": ["vrid", "acl_group_id", "acl_priority"], "comment": "根据vrid、acl_group_id和acl_priority索引"}]}