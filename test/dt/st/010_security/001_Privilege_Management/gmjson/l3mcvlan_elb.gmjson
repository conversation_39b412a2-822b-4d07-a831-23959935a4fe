{"comment": "二三层混跑ELB", "version": "2.0", "type": "record", "name": "l3mcvlan_elb", "config": {"check_validity": false}, "max_record_count": 262144, "fields": [{"name": "vrIndex", "type": "uint32", "comment": "VR索引"}, {"name": "vrfIndex", "type": "uint32", "comment": "VRF索引"}, {"name": "grpAddr", "type": "uint32", "comment": "组播组地址"}, {"name": "srcAddr", "type": "uint32", "comment": "源地址"}, {"name": "ifIndex", "type": "uint32", "comment": "出端口索引"}, {"name": "vlanId", "type": "uint16", "comment": "vlanId"}, {"name": "ifType", "type": "uint16", "comment": "出接口类型"}, {"name": "mcid", "type": "uint32", "comment": "组播组Id"}, {"name": "mcidVersion", "type": "uint64", "comment": "组播组Id的版本号"}, {"name": "mfibVersion", "type": "uint32", "comment": "mfib和elb一致性判断的版本号，mfib删除场景仅删除<=version的elb"}, {"name": "mlagRole", "type": "uint16", "comment": "mlag的主备角色"}, {"name": "isMlagDual", "type": "uint8", "comment": "是否mlag双归"}, {"name": "isInIfElb", "type": "uint8", "comment": "是否是和入接口同属一个vlan的叶子"}, {"name": "inIfhasMlag", "type": "uint32", "comment": "入接口是否包括mlag口"}, {"name": "appSrcPid", "type": "uint32", "comment": "vrp生产者appid"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑对账版本号"}, {"name": "pathFlags", "type": "uint32", "comment": "表项完备性标识"}, {"name": "svcCtxHighPrio", "type": "fixed", "size": 12, "comment": "从MFIB表中获取的svcCtxHighPrio"}, {"name": "svcCtxNormalPrio", "type": "fixed", "size": 12, "comment": "从MFIB表中获取的svcCtxNormalPrio"}, {"name": "serviceStatus", "type": "fixed", "size": 2, "comment": "下发SVC状态"}, {"name": "errCode", "type": "fixed", "size": 2, "comment": "下发SVC错误码，包括svc enp和svc hpp"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}, {"name": "isOutVxlan", "type": "uint8", "comment": "是否是出隧道的叶子"}, {"name": "resv", "type": "fixed", "size": 3, "comment": "保留字段"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l3mcvlan_elb", "fields": ["vrIndex", "vrfIndex", "grpAddr", "srcAddr", "ifIndex", "vlanId"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "mfib_refresh_key", "index": {"type": "hashcluster"}, "node": "l3mcvlan_elb", "fields": ["vrIndex", "vrfIndex", "grpAddr", "srcAddr"], "constraints": {"unique": false}, "comment": "MFIB反刷索引"}, {"name": "outif_refresh_key", "index": {"type": "hashcluster"}, "node": "l3mcvlan_elb", "fields": ["ifIndex"], "constraints": {"unique": false}, "comment": "接口不完备反刷索引"}, {"name": "cmd_scan_key", "index": {"type": "hashcluster"}, "node": "l3mcvlan_elb", "fields": ["vrIndex", "vrfIndex", "grpAddr", "srcAddr", "vlanId"], "constraints": {"unique": false}, "comment": "查询命令行索引"}, {"name": "srcpid_key", "index": {"type": "hashcluster"}, "node": "l3mcvlan_elb", "fields": ["appSrcPid"], "constraints": {"unique": false}, "comment": "老化查询索引（appSrcPid）"}]}