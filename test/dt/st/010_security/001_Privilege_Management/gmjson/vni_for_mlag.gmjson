{"comment": "MLAG 保留VNI业务表", "version": "2.0", "type": "record", "name": "vni_for_mlag", "config": {"check_validity": true}, "max_record_count": 512000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "VRID值"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "rsv_vni", "type": "uint32", "comment": "保留vni值"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}], "keys": [{"name": "vni_for_mlag_key", "index": {"type": "primary"}, "node": "vni_for_mlag", "fields": ["vr_id", "if_index"], "constraints": {"unique": true}, "comment": "key, 基于 vr_id 和 if_index 查询"}]}