{"comment": "接口的协商地址表", "version": "2.0", "type": "record", "name": "ifm_ipv4_neg", "config": {"check_validity": true}, "max_record_count": 67108864, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "ip", "type": "uint32", "comment": "接口的IP地址"}, {"name": "mask", "type": "int8", "comment": "接口的地址掩码"}, {"name": "description", "type": "fixed", "size": 10, "comment": "生产者描述信息"}], "keys": [{"name": "ipv4_neg_key", "index": {"type": "primary"}, "node": "ifm_ipv4_neg", "fields": ["ifIndex", "ip"], "constraints": {"unique": true}, "comment": "协商地址表的唯一索引"}]}