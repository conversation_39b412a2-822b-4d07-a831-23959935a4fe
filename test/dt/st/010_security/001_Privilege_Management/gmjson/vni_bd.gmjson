{"comment": "BD和VNI的绑定关系配置表，数据来源于FES 1971，PNF数据来源于cfg_vni_bd", "version": "2.0", "type": "record", "name": "vni_bd", "config": {"check_validity": false}, "max_record_count": 512000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "VR索引"}, {"name": "vni_id", "type": "uint32", "comment": "VNI编号"}, {"name": "vni_type", "type": "uint8", "comment": "BD类型，1（BD配置），2（分布式网关虚拟BD），3（任意解封装虚拟BD）"}, {"name": "complete_data", "type": "uint8", "comment": "按bit记录数据完整性"}, {"name": "complete_res", "type": "uint8", "comment": "按bit记录全局资源，bit0（VniValid），bit1（MapVniValid）"}, {"name": "l3vni_type", "type": "uint8", "comment": "l3vni类型，1（local），2（remote），3（local + remote）"}, {"name": "map_vni_id", "type": "uint32", "comment": "Mapping VNI编号"}, {"name": "bd_id", "type": "uint32", "comment": "BD编号"}, {"name": "vsi_id", "type": "uint32", "comment": "VSI资源"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}, {"name": "src_addr", "type": "uint32", "comment": "src_addr"}, {"name": "mcast_group", "type": "uint32", "comment": "组播复制地址，来源于1969"}, {"name": "dwn_flag", "type": "uint32", "comment": "用于标记是否需要下发普通vni"}], "keys": [{"name": "vni_bd_key", "index": {"type": "primary"}, "node": "vni_bd", "fields": ["vr_id", "vni_id", "vni_type"], "constraints": {"unique": true}, "comment": "根据vr_id，vni_id和vni_type索引"}, {"name": "vr_vni_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vni_bd", "fields": ["vr_id", "vni_id"], "constraints": {"unique": false}, "comment": "根据vr_id和vni_id索引"}, {"name": "vr_vsi_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vni_bd", "fields": ["vr_id", "vsi_id"], "constraints": {"unique": false}, "comment": "根据vr_id和vni_id索引"}, {"name": "vr_bd_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vni_bd", "fields": ["vr_id", "bd_id"], "constraints": {"unique": false}, "comment": "根据vr_id和bd_id索引"}, {"name": "vr_mc_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vni_bd", "fields": ["vr_id", "vni_type", "mcast_group"], "constraints": {"unique": false}, "comment": "根据vr_id，vni_type和mcast_group索引"}, {"name": "vr_map_vni_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vni_bd", "fields": ["vr_id", "map_vni_id"], "constraints": {"unique": false}, "comment": "根据vr_id和map_vni_id索引"}]}