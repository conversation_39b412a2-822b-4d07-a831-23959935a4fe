{"comment": "信任接口的diffserv，FES182#", "version": "2.0", "type": "record", "name": "qos_trust", "config": {"check_validity": false}, "max_record_count": 51200, "fields": [{"name": "ifindex", "type": "uint32", "comment": "接口ID"}, {"name": "trust_type", "type": "uint8", "comment": "类型，8021p inner：1，8021P outer：2，dscp：3"}, {"name": "diffserv_id", "type": "uint32", "comment": "diffServ ID"}, {"name": "port_priority", "type": "uint8", "comment": "端口优先级"}, {"name": "if_type", "type": "uint8", "comment": "接口类型"}, {"name": "cfi_flag", "type": "uint8", "comment": "cfi标志位"}, {"name": "dscp_phb_en", "type": "uint32", "comment": "dscp phb行为"}, {"name": "dot1p_phb_en", "type": "uint32", "comment": "dotlp phb行为"}, {"name": "app_source_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "保留字段"}, {"name": "app_version", "type": "uint32", "comment": "保留字段"}, {"name": "default_trust", "type": "uint32", "comment": "是否为默认trust配置，1为默认"}], "keys": [{"name": "qos_trust_pk", "index": {"type": "primary"}, "node": "qos_trust", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "表项唯一索引"}]}