{"comment": "lldp pse info of med and dot3 for lldp", "version": "2.0", "type": "record", "name": "lldp_poe_lgy_pow", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "ifindex", "type": "uint32", "comment": "ifindex of pse port"}, {"name": "nbIndex", "type": "uint32", "comment": "nbIndex of a PSE port"}, {"name": "powerCapability", "type": "uint32", "comment": "Power Capability     "}, {"name": "curPowerMode", "type": "uint32", "comment": "Current Power Mode      "}], "keys": [{"name": "ifindex", "index": {"type": "primary"}, "node": "lldp_poe_lgy_pow", "fields": ["ifindex", "nbIndex"], "constraints": {"unique": true}, "comment": "ifindex + nbindex  of pse port"}]}