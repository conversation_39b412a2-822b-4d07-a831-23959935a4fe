{"comment": "Interface IPV4 addresses", "version": "2.0", "type": "record", "name": "interface_ipv4_addresses", "config": {"check_validity": false}, "max_record_count": 65535, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "Interface Index"}, {"name": "vrfIndex", "type": "uint32", "comment": "Vpn Instance Index"}, {"name": "ipv4_address_list", "type": "record", "vector": true, "size": 256, "extend_size": 2, "fields": [{"name": "address", "type": "uint32", "comment": "ipv4 address in hex format"}, {"name": "<PERSON><PERSON>", "type": "uint8", "comment": "ipv4 mask length(0-32)"}, {"name": "type", "type": "uint8", "default": "0", "comment": "ipv4 address type(main/sub/etc.)"}]}, {"name": "time_stamp_create", "type": "time"}], "keys": [{"name": "ifIndex", "index": {"type": "primary"}, "node": "interface_ipv4_addresses", "fields": ["ifIndex"], "constraints": {"unique": true}, "comment": "Interface Index"}]}