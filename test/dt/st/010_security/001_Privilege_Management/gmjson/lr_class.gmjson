{"comment": "lr", "version": "2.0", "type": "record", "name": "lr_class", "config": {"check_validity": true}, "max_record_count": 4096, "fields": [{"name": "producer_pid", "type": "uint32"}, {"name": "table_id", "type": "uint32"}, {"name": "lr_state_machine", "type": "uint8"}, {"name": "lr_state_version", "type": "uint32"}, {"name": "verify_table_num", "type": "uint32"}, {"name": "age_table_num", "type": "uint32"}, {"name": "time_stamp_verify_begin", "type": "time"}, {"name": "time_stamp_verify_end", "type": "time"}, {"name": "time_stamp_age_begin", "type": "time"}, {"name": "time_stamp_age_end", "type": "time"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "补丁预留保留字段"}], "keys": [{"name": "lr_class_key", "index": {"type": "primary"}, "node": "lr_class", "fields": ["producer_pid", "table_id"], "constraints": {"unique": true}}]}