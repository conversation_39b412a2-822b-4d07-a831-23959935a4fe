{"comment": "VRRP备份组配置信息", "version": "2.0", "type": "record", "name": "vrrp6_oper", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "vrrp_id", "type": "uint8", "comment": "备份组id"}, {"name": "priority", "type": "uint8", "comment": "优先级"}, {"name": "delay_time", "type": "int16", "comment": "延迟抢占时间"}, {"name": "preempt_mode", "type": "uint8", "comment": "抢占模式"}, {"name": "linkbfd_dwn_num", "type": "uint8", "comment": "linkBfd down个数"}, {"name": "hold_multi", "type": "uint8", "comment": "心跳超时倍数，默认3，范围3-10"}, {"name": "resv1", "type": "uint8"}, {"name": "adver_interval", "type": "uint32"}, {"name": "vrrp_type", "type": "uint32"}, {"name": "is_createnovip", "type": "uint32", "comment": "是否不带虚ip创建"}, {"name": "is_backupfwd", "type": "uint32", "comment": "双收标记"}, {"name": "app_src_id", "type": "uint32"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "vrrp6_oper", "fields": ["ifindex", "vrrp_id"], "constraints": {"unique": true}}]}