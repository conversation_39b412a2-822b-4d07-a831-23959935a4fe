{"comment": "table of aaa_vsys_ctrl_data", "version": "2.0", "type": "record", "name": "aaa_vsys_ctrl_data", "config": {"check_validity": false}, "max_record_count": 102400, "fields": [{"name": "vsysID", "type": "uint32", "comment": "vsysID"}, {"name": "ulDefaultNormdomain", "type": "uint32", "comment": "Default Normal Domain"}, {"name": "ulDefaultAdmindomain", "type": "uint32", "comment": "Default Admin Domain"}, {"name": "ulDefaultAuthenSchm", "type": "uint32", "comment": "De<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ulDefaultRdsAuthenSchm", "type": "uint32", "comment": "Default Rds Authen Sc<PERSON>a"}, {"name": "ulDefaultAuthorSchm", "type": "uint32", "comment": "Default Author <PERSON><PERSON><PERSON>"}, {"name": "ulDefaultAcctSchm", "type": "uint32", "comment": "Default Acct Schema"}, {"name": "szDefaultAdminDomainName", "type": "bytes", "nullable": true, "size": 256, "comment": "Default Admin Domain Name"}, {"name": "szDefaultNormalDomainName", "type": "bytes", "nullable": true, "size": 256, "comment": "Default Normal Domain Name"}, {"name": "ucDomainnamePosition", "type": "uint8", "comment": "Domain Name Position"}, {"name": "cDomainNameDelimiter", "type": "int8", "comment": "Domain Name Delimiter"}, {"name": "ucDomainnameParseDir", "type": "uint8", "comment": "Domain Name Parse Dir"}, {"name": "cDomainSecurityDelimiter", "type": "int8", "comment": "Domain Security Delimiter"}, {"name": "ucDomainSecurityDelimiterFlag", "type": "uint8", "comment": "Domain Security Delimiter Flag"}, {"name": "stSystemRecordMethodList", "type": "record", "comment": "System Record Method List Info", "fields": [{"name": "szMethlistName", "type": "bytes", "nullable": true, "size": 256, "comment": "Meth List Name"}, {"name": "ucUsed", "type": "uint32", "comment": "Used Flag"}, {"name": "uiRecordMethodNum", "type": "uint32", "comment": "Record Method Num"}]}, {"name": "stOutboundRecordMethodList", "type": "record", "comment": "Outbound Record Method List Info", "fields": [{"name": "szMethlistName", "type": "bytes", "nullable": true, "size": 256, "comment": "Meth List Name"}, {"name": "ucUsed", "type": "uint8", "comment": "Used Flag"}, {"name": "uiRecordMethodNum", "type": "uint32", "comment": "Record Method Num"}]}, {"name": "stCmdRecordMethodList", "type": "record", "comment": "Cmd Record Method List Info", "fields": [{"name": "szMethlistName", "type": "bytes", "nullable": true, "size": 256, "comment": "Meth List Name"}, {"name": "ucUsed", "type": "uint8", "comment": "Used Flag"}, {"name": "uiRecordMethodNum", "type": "uint32", "comment": "Record Method Num"}]}, {"name": "stAAABypassTime", "type": "record", "comment": "AAA Bypass Time", "fields": [{"name": "usAuthenTime", "type": "uint16", "comment": "Authen Time"}, {"name": "usAuthorTime", "type": "uint16", "comment": "Author Time"}, {"name": "usAuthorCmdTime", "type": "uint16", "comment": "Author Cm<PERSON> Time"}]}, {"name": "per_user_access_limit", "type": "uint32", "comment": "user access limit"}, {"name": "per_user_access_limit_by_type", "type": "bytes", "size": 1024, "comment": "user access limit by type"}, {"name": "ucSecurityNameEnableFlag", "type": "uint8", "comment": "Security Name Enable Flag"}, {"name": "ucSessionTimeoutFlag", "type": "uint8", "comment": "Session Timeout Flag"}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}], "keys": [{"name": "primary_key_index", "index": {"type": "primary"}, "node": "aaa_vsys_ctrl_data", "fields": ["vsysID"], "constraints": {"unique": true}, "comment": "key of aaa_vsys_ctrl_data"}]}