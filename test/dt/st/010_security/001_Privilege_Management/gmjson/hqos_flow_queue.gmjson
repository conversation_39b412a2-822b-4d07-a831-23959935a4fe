{"comment": "flow queue", "version": "2.0", "type": "record", "name": "hqos_flow_queue", "config": {"check_validity": false}, "max_record_count": 131072, "fields": [{"name": "vrid_id", "type": "uint16", "comment": "虚拟路由"}, {"name": "queueId", "type": "uint32", "comment": "fq队列ID"}, {"name": "flowQueTmplId", "type": "uint32", "comment": "fq模板ID"}, {"name": "scheduleMode", "type": "uint8", "comment": "调度方式"}, {"name": "cirMode", "type": "uint8", "comment": "cir配置模式"}, {"name": "pirMode", "type": "uint8", "comment": "pir配置模式"}, {"name": "cirKbps", "type": "uint32", "comment": "cir配置"}, {"name": "cirPct", "type": "uint32", "comment": "cir百分比配置"}, {"name": "cbsByte", "type": "uint32", "comment": "cbs配置"}, {"name": "pirKbps", "type": "uint32", "comment": "pir配置"}, {"name": "pirPct", "type": "uint32", "comment": "pir百分比配置"}, {"name": "pbsByte", "type": "uint32", "comment": "pbs配置"}, {"name": "cirWeight", "type": "uint32", "comment": "cir权重配置"}, {"name": "pirW<PERSON>ght", "type": "uint32", "comment": "pir权重配置"}, {"name": "wredIndex", "type": "uint32", "comment": "wred模板索引"}, {"name": "queueLen", "type": "uint32", "comment": "队列深度"}, {"name": "isCirPercent", "type": "uint8", "comment": "指定用户队列的cir值计算流队列百分比整形速率"}, {"name": "srcPid", "type": "uint32", "comment": "保留字段"}, {"name": "serialId", "type": "uint32", "comment": "保留字段"}, {"name": "objId", "type": "uint64", "comment": "保留字段"}, {"name": "verNo", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "flow_queue_pk", "index": {"type": "primary"}, "node": "hqos_flow_queue", "fields": ["vrid_id", "queueId"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "flow_queue_lk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "hqos_flow_queue", "fields": ["flowQueTmplId"], "constraints": {"unique": false}, "comment": "模板键索引"}, {"name": "wred_index_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "hqos_flow_queue", "fields": ["wredIndex"], "constraints": {"unique": false}, "comment": "wred键索引"}]}