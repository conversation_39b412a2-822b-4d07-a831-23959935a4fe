{"comment": "l2pt接口配置表", "version": "2.0", "type": "record", "name": "l2pt_ifcfg", "config": {"check_validity": false}, "max_record_count": 32786, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "vrId", "type": "uint32", "comment": "虚拟设备号"}, {"name": "bpduMac", "type": "fixed", "size": 6, "comment": "Bpdu mac"}, {"name": "isEnable", "type": "uint32", "comment": "使能标志"}, {"name": "tagFormat", "type": "uint32", "comment": "是否带tag标志"}, {"name": "vlanId", "type": "uint32", "comment": "stp报文指定vlan"}, {"name": "entryId", "type": "uint32", "comment": "ACL实例号"}], "keys": [{"name": "l2pt_ifcfg_pk", "index": {"type": "primary"}, "node": "l2pt_ifcfg", "fields": ["ifIndex", "vrId", "bpduMac"], "constraints": {"unique": true}, "comment": "主键"}]}