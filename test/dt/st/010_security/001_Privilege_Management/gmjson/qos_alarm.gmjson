{"comment": "记录告警发送历史", "version": "2.0", "type": "record", "name": "qos_alarm", "config": {"check_validity": false}, "fields": [{"name": "alarmId", "type": "uint32"}, {"name": "key1", "type": "uint32"}, {"name": "key2", "type": "uint32"}, {"name": "key3", "type": "uint32"}, {"name": "key4", "type": "uint64"}, {"name": "key5", "type": "uint64"}, {"name": "time", "type": "uint32"}, {"name": "status", "type": "uint8"}, {"name": "slotId", "type": "uint32"}, {"name": "vrid", "type": "uint32"}], "keys": [{"name": "alarm_key", "index": {"type": "primary"}, "node": "qos_alarm", "fields": ["alarmId", "key1", "key2", "key3"], "constraints": {"unique": true}}]}