{"comment": "IPv6 ping配置表", "version": "2.0", "type": "record", "name": "cfg_ipv6_ping", "config": {"check_validity": false}, "max_record_count": 400, "fields": [{"name": "vrid", "type": "uint32", "comment": "VR索引"}, {"name": "src_ipv6", "type": "fixed", "size": 16, "comment": "源IPv6地址"}, {"name": "dst_ipv6", "type": "fixed", "size": 16, "comment": "目的IPv6地址"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}, {"name": "state", "type": "uint32", "comment": "下发状态"}], "keys": [{"name": "vrid_src_ipv6_dst_ipv6", "index": {"type": "primary"}, "node": "cfg_ipv6_ping", "fields": ["vrid", "src_ipv6", "dst_ipv6"], "constraints": {"unique": true}, "comment": "key,基于vrid/src_ipv6/dst_ipv6查询"}]}