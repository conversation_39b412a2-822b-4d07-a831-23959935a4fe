{"version": "2.0", "type": "record", "name": "nsres_index_map", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "type", "type": "uint32"}, {"name": "group", "type": "uint32"}, {"name": "block", "type": "uint32"}, {"name": "map", "type": "uint64"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nsres_index_map", "fields": ["type", "group", "block"], "constraints": {"unique": true}}, {"name": "group_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "nsres_index_map", "fields": ["type", "group"], "constraints": {"unique": false}, "comment": "各种资源的位图key"}]}