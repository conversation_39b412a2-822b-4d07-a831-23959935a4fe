{"comment": "bfd 报文模板表", "version": "2.0", "type": "record", "name": "bfd_pkt", "max_record_count": 8192, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "frame_len", "type": "uint16", "comment": "frame_len"}, {"name": "l3_hdr_offset", "type": "uint16", "comment": "l3_hdr_offset"}, {"name": "bfd_pkt_offset", "type": "uint16", "comment": "bfd_pkt_offset"}, {"name": "pkt_flag", "type": "uint8", "comment": "pkt_flag"}, {"name": "pkt_type", "type": "uint8", "comment": "pkt_type"}, {"name": "mlag_flag", "type": "uint8", "comment": "mlag_flag"}, {"name": "frame", "type": "fixed", "size": 128, "comment": "frame"}, {"name": "app_source_id", "type": "uint32", "comment": "app_source_id"}, {"name": "app_serial_id", "type": "uint32", "comment": "app_serial_id"}, {"name": "app_obj_id", "type": "uint64", "comment": "app_obj_id"}, {"name": "app_version", "type": "uint32", "comment": "app_version"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "reserved field."}, {"name": "time_stamp_create", "type": "time", "comment": "table create time"}], "keys": [{"name": "bfd_pkt_pk", "index": {"type": "primary"}, "node": "bfd_pkt", "fields": ["my_discr"], "constraints": {"unique": true}}]}