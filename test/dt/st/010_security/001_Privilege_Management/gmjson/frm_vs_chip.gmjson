{"comment": "FWM FRM VS Chip", "version": "2.0", "type": "record", "name": "frm_vs_chip", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "chip_id", "type": "uint32", "comment": "chip id"}, {"name": "vr_id", "type": "uint32", "comment": "vr id"}, {"name": "vs_id", "type": "uint32", "comment": "vs id"}, {"name": "orchId", "type": "uint32", "comment": "inst id"}, {"name": "reserve", "type": "uint32", "comment": "reserve field"}], "keys": [{"name": "vschip_key", "index": {"type": "primary"}, "node": "frm_vs_chip", "fields": ["chip_id", "vr_id"], "constraints": {"unique": true}, "comment": "vs chip primary key"}]}