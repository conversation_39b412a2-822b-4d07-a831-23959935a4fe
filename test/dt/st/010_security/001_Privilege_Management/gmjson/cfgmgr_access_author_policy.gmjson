{"comment": "table of cfgmgr_access_author_policy", "version": "2.0", "type": "record", "name": "cfgmgr_access_author_policy", "config": {"check_validity": false}, "max_record_count": 2, "fields": [{"name": "policyIndex", "type": "uint32", "comment": "Index of access-author policy"}, {"name": "policyName", "type": "bytes", "size": 33, "comment": "Name of access-author policy"}, {"name": "policyRulesAcpIndex", "type": "record", "array": true, "size": 128, "comment": "Access-author policy rules", "fields": [{"name": "acpIndex", "type": "uint32", "comment": "Access-context profile index"}, {"name": "acpIndexValid", "type": "uint32", "comment": "Valid flag of access-context profile index"}]}, {"name": "policyRulesDomain", "type": "record", "array": true, "size": 128, "comment": "match action domain", "fields": [{"name": "defaultDomain", "type": "bytes", "size": 65, "comment": "Default access-domain"}, {"name": "forceDomain", "type": "bytes", "size": 65, "comment": "Force access-domain"}, {"name": "defaultDomainsAccessType", "type": "record", "array": true, "size": 4, "comment": "Default access-domain with access-type", "fields": [{"name": "domain", "type": "bytes", "size": 65, "comment": "Default access-domain for access-type"}]}, {"name": "forceDomainsAccessType", "type": "record", "array": true, "size": 4, "comment": "Force access-domain with access-type", "fields": [{"name": "domain", "type": "bytes", "size": 65, "comment": "Force access-domain for access-type"}]}]}, {"name": "policyRulesEvent", "type": "record", "array": true, "size": 128, "comment": "match action event", "fields": [{"name": "eventActionCfgs", "type": "record", "array": true, "size": 8, "comment": "match event action configurations", "fields": [{"name": "serviceScheme", "type": "bytes", "size": 33, "comment": "Service scheme"}, {"name": "serverUpAction", "type": "uint8", "comment": "Server-up event action"}]}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "cfgmgr_access_author_policy", "fields": ["policyIndex"], "constraints": {"unique": true}, "comment": "Key of table"}]}