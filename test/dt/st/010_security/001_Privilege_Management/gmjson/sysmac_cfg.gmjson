{"version": "2.0", "type": "record", "name": "sysmac_cfg", "config": {"check_validity": false}, "max_record_count": 2, "fields": [{"name": "lr_id", "type": "uint32", "comment": "lr id"}, {"name": "sys_mac", "type": "fixed", "size": 6, "default": "000000", "comment": "system mac"}, {"name": "mac_num", "type": "uint32", "comment": "mac number"}, {"name": "reserve", "type": "uint32", "comment": "reserve field"}], "keys": [{"name": "sysmac_cfg_key", "index": {"type": "primary"}, "node": "sysmac_cfg", "fields": ["lr_id"], "constraints": {"unique": true}, "comment": "primay key"}]}