{"comment": "端口安全", "version": "2.0", "type": "record", "name": "port_security", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟设备ID"}, {"name": "index", "type": "uint32", "comment": "接口索引"}, {"name": "port_sec_enable", "type": "boolean", "nullable": true, "comment": "使能标志位"}, {"name": "port_stimac_enable", "type": "boolean", "nullable": true, "comment": "stickymac使能标志位"}, {"name": "aging_time", "type": "uint32", "nullable": true, "comment": "范围是1～1440。单位为分钟"}, {"name": "aging_type", "type": "uint8", "nullable": true, "default": 1, "comment": "1:absolute, 2:inactivity"}, {"name": "max_num", "type": "uint16", "nullable": true, "default": 1, "comment": "范围是1～4096"}, {"name": "protect_action", "type": "uint8", "nullable": true, "default": 2, "comment": "1:protect, 2:restrict, 3:shutdown"}, {"name": "current_num", "type": "uint16", "comment": "readonly;当前已学习到的MAC数量"}, {"name": "app_source_id", "type": "uint32", "comment": "应用组件源id"}, {"name": "app_serial_id", "type": "uint32", "comment": "应用组件序列id"}, {"name": "app_obj_id", "type": "uint64", "comment": "应用组件目标id"}, {"name": "app_version", "type": "uint32", "comment": "应用组件版本号"}], "keys": [{"name": "port_security_pk", "index": {"type": "primary"}, "node": "port_security", "fields": ["vrid", "index"], "constraints": {"unique": true}, "comment": "主键"}]}