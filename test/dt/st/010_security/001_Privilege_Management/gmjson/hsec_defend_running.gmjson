{"comment": "存储运行的car值的car表", "version": "2.0", "type": "record", "name": "hsec_defend_running", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "vs_id", "type": "uint16", "comment": "暂无用"}, {"name": "pkt_typeq", "type": "uint16", "comment": "协议类型"}, {"name": "pps_value", "type": "uint32", "comment": "配置的car值"}, {"name": "default_pps", "type": "uint32", "comment": "默认的car值"}, {"name": "current_car", "type": "uint32", "comment": "当前的car值"}, {"name": "policy_run_flag", "type": "uint8", "comment": "运行标记"}, {"name": "reserve", "type": "uint32", "comment": "预留字段"}], "keys": [{"name": "defend_run_pk", "index": {"type": "primary"}, "node": "hsec_defend_running", "fields": ["vs_id", "pkt_typeq"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}