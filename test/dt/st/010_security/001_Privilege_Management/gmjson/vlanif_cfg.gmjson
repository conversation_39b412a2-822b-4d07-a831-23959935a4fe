{"comment": "VLANIF配置", "version": "2.0", "type": "record", "name": "vlanif_cfg", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "ifindex", "type": "uint32", "comment": "vlanif"}, {"name": "vlanid", "type": "uint16", "comment": "[1..4094]"}, {"name": "vrid", "type": "uint32", "comment": "虚拟设备id"}], "keys": [{"name": "if_vlan_pk", "index": {"type": "primary"}, "node": "vlanif_cfg", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "主键"}, {"name": "k_vlan_id", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vlanif_cfg", "fields": ["vlanid", "vrid"], "constraints": {"unique": false}, "comment": "vlan vrid键"}]}