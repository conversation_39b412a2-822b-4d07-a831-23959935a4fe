{"comment": "l3mcv4环回叶子信息", "version": "2.0", "type": "record", "name": "l3mcv4_loop_elb", "config": {"check_validity": false}, "max_record_count": 262144, "fields": [{"name": "vrId", "type": "uint32", "comment": "VR索引"}, {"name": "vrfId", "type": "uint32", "comment": "VRF索引"}, {"name": "groupAddr", "type": "uint32", "comment": "组播组地址"}, {"name": "sourceAddr", "type": "uint32", "comment": "源地址"}, {"name": "outIfIndex", "type": "uint32", "comment": "迭代出接口索引"}, {"name": "vlanId", "type": "uint32", "comment": "VLANID，出接口为vlanif时响应VLAN成员变化时使用此索引"}, {"name": "fwdIfType", "type": "uint32", "comment": "fwdIfType"}, {"name": "ifType", "type": "uint32", "comment": "转发类型"}, {"name": "pathFlags", "type": "uint32", "comment": "表项完备性标识"}, {"name": "svcCtxHighPrio", "type": "fixed", "size": 12, "comment": "存贮 enp svc fwdIfType tb tp"}, {"name": "svcCtxNormalPrio", "type": "fixed", "size": 12, "comment": "存贮 hpp svc fwdIfType tb tp"}, {"name": "mcidVersion", "type": "uint64", "comment": "组播组Id的版本号"}, {"name": "mfibVersion", "type": "uint32", "comment": "mfib和elb一致性判断的版本号，mfib删除场景仅删除<=version的elb"}, {"name": "mcId", "type": "uint32", "comment": "组播组Id"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑版本号，主备倒换各板表项平滑使用"}, {"name": "serviceStatus", "type": "fixed", "size": 2, "comment": "下发SVC状态，包括svc enp和svc hpp"}, {"name": "errCode", "type": "fixed", "size": 2, "comment": "下发SVC错误码，包括svc enp和svc hpp"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}, {"name": "flag", "type": "uint32", "comment": "比特位标志集"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l3mcv4_loop_elb", "fields": ["vrId", "vrfId", "groupAddr", "sourceAddr", "outIfIndex"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "MFIB_refresh_key", "index": {"type": "hashcluster"}, "node": "l3mcv4_loop_elb", "fields": ["vrId", "vrfId", "groupAddr", "sourceAddr"], "constraints": {"unique": false}, "comment": "MFIB反刷索引"}, {"name": "L3IF_refresh_key", "index": {"type": "hashcluster"}, "node": "l3mcv4_loop_elb", "fields": ["outIfIndex"], "constraints": {"unique": false}, "comment": "L3IF反刷索引"}, {"name": "VLAN_member_change_key", "index": {"type": "hashcluster"}, "node": "l3mcv4_loop_elb", "fields": ["vlanId"], "constraints": {"unique": false}, "comment": "VLAN成员变化索引"}]}