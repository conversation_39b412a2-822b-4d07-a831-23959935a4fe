/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: group priv st.
 * Author: wang<PERSON>gyi
 * Create: 2024-01-11
 */
#include "st_common.h"

using namespace std;
extern GmcStmtT *g_stmt;

namespace {
const char *g_labelName = "label1";
const char *g_labelJson = R"(
    [{
        "type": "record",
        "name" : "label1",
        "fields" :
            [
                {"name": "F0", "type": "int32", "nullable": false},
                {"name": "F1", "type": "int32", "nullable": false},
                {"name": "F2", "type": "int32", "nullable": false}
            ],
        "keys":
            [
                {
                    "node": "label1",
                    "name": "label_K0",
                    "fields": ["F0"],
                    "index": {"type": "primary"},
                    "constraints": {"unique": true}
                }
            ]
    }]
)";

const char *g_cfgJson = R"({"max_record_count":100})";
const char *g_kvTableName = "kv_table";

}  // namespace

GmcConnT *g_subChanGroup = NULL;

// 权限兼容V3：1、对账权限修改为检测DELETE 2、订阅改为校验SELECT 3、GET改为校验是否拥有增删改查权限之一
//            4、GmcDeleteAllFast改为校验DELETE权限
class StQueryPrivilegelGroup : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
#ifdef EXPERIMENTAL_GUANGQI
        SetConfigItems("\"DBA=root:gmrule;gmsysview\" \"userPolicyMode=2\" \"persistentMode=0\"");
#else
        SetConfigItems("\"DBA=root:gmrule;gmsysview\"");
#endif
        SetDefaultModConf(false);
        SetImportList(false);
        StartServerByCfgItems();
        string args = string("gmrule -c import_allowlist -f ./001_Privilege_Management/group_policy/user.gmuser");
        Status ret = system(args.c_str());
        EXPECT_EQ(ret, GMERR_OK);

        st_connect();

        const char *subConnName = "subConnName1";
        ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &g_subChanGroup);
        ASSERT_EQ(ret, GMERR_OK);
    }

    static void TearDownTestCase()
    {
        EXPECT_EQ(GmcDisconnect(g_subChanGroup), GMERR_OK);
        st_disconnect();
        StopServer();
    }
    virtual void SetUp()
    {
        // 导入创建vertexlabel的权限
        Status ret = system("gmrule -c import_policy -f "
                            "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
        ret = system("gmrule -c revoke_policy -f "
                     "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        ret = system("gmrule -c import_policy -f "
                     "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all_kv.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson);
        EXPECT_EQ(GMERR_OK, ret);
        ret = system("gmrule -c revoke_policy -f "
                     "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all_kv.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
    }

    virtual void TearDown()
    {
        Status ret = system("gmrule -c import_policy -f "
                            "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcDropVertexLabel(g_stmt, g_labelName);
        EXPECT_EQ(ret, GMERR_OK);

        ret = system("gmrule -c revoke_policy -f "
                     "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        ret = system("gmrule -c import_policy -f "
                     "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all_kv.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcKvDropTable(g_stmt, g_kvTableName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = system("gmrule -c revoke_policy -f "
                     "./001_Privilege_Management/group_policy/system_privilegel_policy_base_all_kv.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
    }
};

TEST_F(StQueryPrivilegelGroup, testGrantGroupSysPrivView)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT");
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT");
}

TEST_F(StQueryPrivilegelGroup, testGrantGroupObjPrivView)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f PROCESS_NAME=st_010_security");
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f PROCESS_NAME=st_010_security");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all_kv.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f PROCESS_NAME=st_010_security");
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all_kv.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f PROCESS_NAME=st_010_security");
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/group_policy/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f PROCESS_NAME=st_010_security");
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f PROCESS_NAME=st_010_security");
}
// 异常场景 isAtomic
TEST_F(StQueryPrivilegelGroup, testGrantGroupObjPrivAtomicError)
{
    Status ret = system("gmrule atomic -c import_policy -f "
                        "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 重复赋权 atomic会失败
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // 重复赋权，非atomic，直接成功覆盖
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 重复撤销
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup, testGrantGroupSysPrivAtomicError)
{
    Status ret = system("gmrule atomic -c import_policy -f "
                        "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 重复赋权 atomic会失败
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // 重复赋权，非atomic，直接成功覆盖
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 重复撤销
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup, testGrantGroupSysPrivNoUser)
{
    // 用户不存在，atomic，失败
    Status ret = system("gmrule atomic -c import_policy -f "
                        "./001_Privilege_Management/group_policy/system_privilegel_policy_all_nouser.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // 用户不存在，非atomic，warning
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all_nouser.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_policy_all_nouser.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup, testGrantGroupObjPrivNoUser)
{
    // 用户不存在，atomic，失败
    Status ret = system("gmrule atomic -c import_policy -f "
                        "./001_Privilege_Management/group_policy/object_privilegel_policy_all_nouser.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // 用户不存在，非atomic，warning
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all_nouser.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_policy_all_nouser.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup, testGrantGroupSysPrivWildCard)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./001_Privilege_Management/group_policy/multi_group.gmuser");
    Status ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // import sys policy: group:* process:p1 RESOURCE FAIL
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_wildcard_error.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // import sys policy: group:* process:* RESOURCE SUC
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_wildcard.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f IS_USER=0");
    // import sys policy: group:* process:p1 RESOURCE WARN
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_wildcard_error.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // import sys policy: group:* process:* RESOURCE SUC
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_wildcard.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f IS_USER=0");
    // 删除用户
    args = string("gmrule -c remove_allowlist -f ./001_Privilege_Management/group_policy/multi_group.gmuser");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup, testGrantGroupObjPrivWildCard)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./001_Privilege_Management/group_policy/multi_group.gmuser");
    Status ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // import obj policy: group:* process:* "UPDATE", "DELETE", "INSERT" FAIL
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard_error.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // import obj policy: group:* process:* "SELECT" SUC
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f IS_USER=0");

    // import obj policy: group:g1 process:* "UPDATE", "DELETE", "INSERT" FAIL
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard_error1.gmpolicy");
    EXPECT_NE(ret, GMERR_OK);
    // import obj policy: group:* process:p1 "UPDATE", "DELETE", "INSERT" SUC
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f IS_USER=0");

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard_error.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard_error1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_wildcard1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT -f IS_USER=0");
    // 删除用户
    args = string("gmrule -c remove_allowlist -f ./001_Privilege_Management/group_policy/multi_group.gmuser");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup, testGrantUnsupportPriv)
{
    Status ret = system("gmrule atomic -c import_policy -f "
                        "./001_Privilege_Management/group_policy/system_privilegel_nosupport.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_nosupport.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/system_privilegel_nosupport.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/group_policy/object_privilegel_nosupport.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}
