/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: group priv st.
 * Author: wangjingyi
 * Create: 2024-01-11
 */

#include "st_common.h"

using namespace std;
extern GmcStmtT *g_stmt;

namespace {
const char *g_labelName = "label1";
const char *g_labelJson = R"(
    [{
        "type": "record",
        "name" : "label1",
        "fields" :
            [
                {"name": "F0", "type": "int32", "nullable": false},
                {"name": "F1", "type": "int32", "nullable": false},
                {"name": "F2", "type": "int32", "nullable": false}
            ],
        "keys":
            [
                {
                    "node": "label1",
                    "name": "label_K0",
                    "fields": ["F0"],
                    "index": {"type": "primary"},
                    "constraints": {"unique": true}
                }
            ]
    }]
)";

const char *g_stmgLabelName = "label2";
const char *g_stmgLabelJson = R"(
    [{
        "type": "record",
        "name" : "label2",
        "subs_type":"status_merge",
        "fields" :
            [
                {"name": "F0", "type": "int32", "nullable": false},
                {"name": "F1", "type": "int32", "nullable": false},
                {"name": "F2", "type": "int32", "nullable": false}
            ],
        "keys":
            [
                {
                    "node": "label2",
                    "name": "label_K0",
                    "fields": ["F0"],
                    "index": {"type": "primary"},
                    "constraints": {"unique": true}
                }
            ]
    }]
)";

const char *g_subsName = "subVertexLabel1";
const char *g_subsJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "label1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
)";

const char *g_stmgSubsName = "subVertexLabel2";
const char *g_stmgSubsJson = R"(
    {
        "label_name":"label2",
        "comment":"VertexLabel2 subscription",
        "subs_type":"status_merge",
        "events": [{ "type": "modify", "msgTypes":["new object", "old object"]}]
    }
)";

const char *g_cfgJson = R"({"max_record_count":100})";
const char *g_kvTableName = "kv_table";
const char *g_labelName1 = "label1";

const char *g_cfgJsonNoLite = R"({"max_record_count":200000, "isFastReadUncommitted":false})";
const char *g_stmgCfgJson = R"({
        "max_record_count":100,
        "push_age_record_batch":20,
        "isFastReadUncommitted":false,
        "defragmentation":false,
        "status_merge_sub":true
        })";

const char *g_labelJson1 =
    R"([
        {"name":"label1",
         "type":"record",
         "fields":[
            { "name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32", "nullable":false},
            { "name":"F3", "type":"string","size":8, "nullable":true}
            ],
        "keys":[
            { "node":"label1", "name":"K0", "fields":["F0"],"index":{"type":"primary"},"constraints":{"unique":true}},
            { "node":"label1", "name":"K1", "fields":["F0"],"index":{"type":"local"},"constraints":{"unique":true}},
            { "node":"label1", "name":"K2", "fields":["F1"],"index":{"type":"local"},"constraints":{"unique":false}},
            { "node":"label1", "name":"K3", "fields":["F0"],"index":{"type":"hashcluster"},"constraints":{"unique":true}},
            { "node":"label1", "name":"K4", "fields":["F1"],"index":{"type":"hashcluster"},"constraints":{"unique":false}},
            { "node":"label1", "name":"K5", "fields":["F0"],"index":{"type":"localhash"},"constraints":{"unique":true}},
            { "node":"label1", "name":"K6", "fields":["F1"],"index":{"type":"localhash"},"constraints":{"unique":false}}
            ]
        }])";
const char *g_labelJson2 =
    R"([
        {"name":"label1",
         "type":"record",
         "fields":[
            { "name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32", "nullable":false},
            { "name":"F3", "type":"string","size":8, "nullable":true}
            ],
        "keys":[
            { "node":"label1", "name":"K0", "fields":["F0"],"index":{"type":"primary"},"constraints":{"unique":true}},
            { "node":"label1", "name":"K1", "fields":["F1"],"index":{"type":"local"},"constraints":{"unique":false}},
            { "node":"label1", "name":"K2", "fields":["F1"],"index":{"type":"hashcluster"},"constraints":{"unique":false}},
            { "node":"label1", "name":"K3", "fields":["F1"],"index":{"type":"localhash"},"constraints":{"unique":false}}
            ]
        }])";

}  // namespace

static Status TestInsertVertex(GmcStmtT *stmt, int F0Value, int F1Value, int F2Value)
{
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static Status TestDeleteVertex(GmcStmtT *stmt, DmVertexLabelT *vertexLabel, int keyValue, const char *keyName)
{
    Status ret = GmcSetIndexKeyName(stmt, keyName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static Status TestUpdateVertex(GmcStmtT *stmt, int F0Value, int F1Value, int F2Value, const char *keyName)
{
    Status ret = GmcSetIndexKeyName(stmt, keyName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static Status TestUpdateVertexByCond(GmcStmtT *stmt, int F2Value, const char *cond)
{
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, cond));
    Status ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static Status TestReplaceVertex(GmcStmtT *stmt, int value)
{
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static Status TestMergeVertex(GmcStmtT *stmt, int value)
{
    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    value = 3;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static Status TestSetKv(GmcStmtT *stmt, const char *key, int32_t value)
{
    return GmcKvSet(stmt, key, strlen(key) + 1, &value, sizeof(int32_t));
}

static Status TestRemoveKv(GmcStmtT *stmt, const char *key)
{
    return GmcKvRemove(stmt, key, strlen(key) + 1);
}

static void TestVertexLabelDml(GmcStmtT *stmt, const char *configJson, bool isInDs)
{
    Status ret;
    if (isInDs) {
        ret = GmcCreateVertexLabel(stmt, g_labelJson2, configJson);
    } else {
        ret = GmcCreateVertexLabel(stmt, g_labelJson1, configJson);
    }
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));
    int i;
    // 成功插入数据5条{{0,1,2},{1,1,2},{2,1,2},{3,1,2},{4,1,2}}
    for (i = 0; i < 5; i++) {
        ret = TestInsertVertex(stmt, i, 1, 2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 失败插入数据1条
    ret = TestInsertVertex(stmt, 0, 1, 2);
    EXPECT_NE(GMERR_OK, ret);
    // 更新5条成功 {{0,3,0},{1,3,1},{2,3,2},{3,3,3},{4,3,4}}
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_UPDATE));
    for (i = 0; i < 5; i++) {
        ret = TestUpdateVertex(stmt, i, 3, i, "K0");
        EXPECT_EQ(GMERR_OK, ret);
    }
    i = 0;
    // 更新1条失败(客户端失败，不统计)
    ret = TestUpdateVertex(stmt, 1, 2, 3, "F_K0");
    EXPECT_NE(GMERR_OK, ret);
    if (!isInDs) {
        // 条件更新1条失败(服务端失败，统计)
        GmcResetStmt(stmt);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_UPDATE));
        ret = TestUpdateVertexByCond(stmt, 1, "T0.F1 = \"aaa\"");
        EXPECT_NE(GMERR_OK, ret);
        // replace 5 条成功 {{0,3,0},{1,4,1},{2,4,2},{3,4,3},{4,4,4}, {5,4,5}}
        for (i = 0; i < 5; i++) {
            EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_REPLACE));
            ret = TestReplaceVertex(stmt, i + 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // merge 5 条成功 {{0,3,0},{1,4,1},{2,3,2},{3,3,3},{4,3,4}, {5,3,5}}, {6,3,6}}
        for (i = 0; i < 5; i++) {
            EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_MERGE));
            ret = TestMergeVertex(stmt, i + 2);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // replace和merge 1 条失败
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_NE(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_NE(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_REPLACE));
        ret = GmcExecute(stmt);
        EXPECT_NE(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_MERGE));
        uint32_t f0 = 100;  // 赋值为100
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_NE(GMERR_OK, ret);
    } else {
        // replace 1 条成功，DS 不支持merge操作，且DS有失败重试机制
        GmcResetStmt(stmt);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (isInDs) {
        DbSleep(1000);  // sleep 1000 us，等待ds merge完成
    }
    // delete
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_DELETE));
    if (!isInDs) {
        ret = TestDeleteVertex(stmt, NULL, 3, "K2");  // 修改值为3的数据
        EXPECT_EQ(GMERR_OK, ret);
        // 预期删除数据6条
        uint32_t affectedRows;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectedRows, sizeof(affectedRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(6u, affectedRows);
        // 删除失败1条
        EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "T0.F1 = \"aaa\""));
        ret = GmcExecute(stmt);
        EXPECT_NE(GMERR_OK, ret);
        // truncate,不纳入统计, DS不支持truncate
        ret = GmcDeleteAllFast(stmt, g_labelName1);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = TestDeleteVertex(stmt, NULL, 3, "K0");  // 删除值为3的数据。
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void TestKvDml(GmcStmtT *stmt, const char *configJson, bool isInDs)
{
    Status ret = GmcKvCreateTable(stmt, g_kvTableName, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    const char *key = "zhangshan";
    ret = TestSetKv(stmt, key, 10);  // 设置key值为10
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestSetKv(stmt, key, 20);  // 更新key值为20
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestSetKv(stmt, key, 30);  // 更新key值为30
    EXPECT_EQ(GMERR_OK, ret);
    if (isInDs) {
        DbSleep(1000);  // sleep 1000 待ds merge完成。
    }
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(1u, count);
    EXPECT_EQ(GMERR_OK, ret);

    ret = TestRemoveKv(stmt, key);
    EXPECT_EQ(GMERR_OK, ret);
    // 重复删除
    ret = TestRemoveKv(stmt, key);
    EXPECT_EQ(GMERR_OK, ret);
}

GmcConnT *g_subChanGroup1 = NULL;

// 权限兼容V3：1、对账权限修改为检测DELETE 2、订阅改为校验SELECT 3、GET改为校验是否拥有增删改查权限之一
//            4、GmcDeleteAllFast改为校验DELETE权限
class StQueryPrivilegelGroup1 : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
#ifdef EXPERIMENTAL_GUANGQI
        SetConfigItems("\"DBA=root:gmrule\" \"userPolicyMode=2\" \"persistentMode=0\"");
#else
        SetConfigItems("DBA=root:gmrule");
#endif
        SetDefaultModConf(false);
        SetImportList(false);
        StartServerByCfgItems();
        string args = string("gmrule -c import_allowlist -f ./001_Privilege_Management/group_policy/user.gmuser");
        Status ret = system(args.c_str());
        EXPECT_EQ(ret, GMERR_OK);

        st_connect();

        const char *subConnName = "subConnName1";
        ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &g_subChanGroup1);
        ASSERT_EQ(ret, GMERR_OK);
    }

    static void TearDownTestCase()
    {
        EXPECT_EQ(GmcDisconnect(g_subChanGroup1), GMERR_OK);
        st_disconnect();
        StopServer();
    }
    virtual void SetUp()
    {
        // 导入创建vertexlabel的权限
        Status ret =
            system("gmrule -c import_policy -f "
                   "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT));

        ret = system("gmrule -c revoke_policy -f "
                     "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
        ret = system("gmrule -c import_policy -f "
                     "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_kv_base.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
    }

    virtual void TearDown()
    {
        Status ret =
            system("gmrule -c import_policy -f "
                   "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcDropVertexLabel(g_stmt, g_labelName);
        EXPECT_EQ(ret, GMERR_OK);

        ret = system("gmrule -c revoke_policy -f "
                     "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
        EXPECT_EQ(ret, GMERR_OK);
    }
};

TEST_F(StQueryPrivilegelGroup1, testSubSysPriv)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    EXPECT_EQ(GmcCreateNamespace(g_stmt, "nsp1", NULL), GMERR_OK);
    // 赋予nsp1 use权限后，use nsp1成功
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GmcUseNamespace(g_stmt, "nsp1"), GMERR_OK);
    // 在nsp1下create label1
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
    GmcSubConfigT config;
    config.subsName = g_subsName;
    config.configJson = g_subsJson;
    // 此处没有导入任何权限，预期失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label1.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有select_any权限，预期失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label1.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 这里导入了nsp select权限，预期订阅成功。
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_subsName);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_subsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_subsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropNamespace(g_stmt, "nsp1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret =
        system("gmrule -c revoke_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
}

TEST_F(StQueryPrivilegelGroup1, testStmgSubObjPriv)
{
    GmcSignalRegisterNotify();
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    EXPECT_EQ(GmcCreateNamespace(g_stmt, "nsp1", NULL), GMERR_OK);
    // 赋予nsp1 use权限后，use nsp1成功
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GmcUseNamespace(g_stmt, "nsp1"), GMERR_OK);
    // 在nsp1下create label1
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_stmgLabelJson, g_stmgCfgJson));
    GmcSubConfigT config;
    config.subsName = g_stmgSubsName;
    config.configJson = g_stmgSubsJson;
    // 此处没有导入任何权限，预期失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label2.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有select权限，预期失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label2.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 这里导入了nsp select权限，预期订阅成功。
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_stmgSubsName);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_stmgSubsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_stmgSubsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, g_stmgLabelName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropNamespace(g_stmt, "nsp1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret =
        system("gmrule -c revoke_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_select_policy.gmpolicy");
}

TEST_F(StQueryPrivilegelGroup1, testStmgSubSysPriv)
{
    GmcSignalRegisterNotify();
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    EXPECT_EQ(GmcCreateNamespace(g_stmt, "nsp1", NULL), GMERR_OK);
    // 赋予nsp1 use权限后，use nsp1成功
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GmcUseNamespace(g_stmt, "nsp1"), GMERR_OK);
    // 在nsp1下create label1
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_stmgLabelJson, g_stmgCfgJson));
    GmcSubConfigT config;
    config.subsName = g_stmgSubsName;
    config.configJson = g_stmgSubsJson;
    // 此处没有导入任何权限，预期失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label2.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有select_any权限，预期失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label2.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 这里导入了nsp select权限，预期订阅成功。
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_stmgSubsName);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_stmgSubsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_stmgSubsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, g_stmgLabelName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropNamespace(g_stmt, "nsp1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret =
        system("gmrule -c revoke_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
}

TEST_F(StQueryPrivilegelGroup1, testGrantAndRevokeNspPriv)
{
    Status ret = GmcCreateNamespace(g_stmt, "nsp1", NULL);
    EXPECT_NE(ret, GMERR_OK);
    // 赋予创建nsp权限
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 赋予视图权限
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_gmsysview.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    ret = GmcCreateNamespace(g_stmt, "nsp1", NULL);
    EXPECT_EQ(ret, GMERR_OK);

    // 赋予nsp上的全部权限，成功
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview  -q V\\$PRIVILEGE_USER_STAT");
    // 撤销nsp上的全部权限，成功
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview  -q V\\$PRIVILEGE_ROLE_STAT");
    // 赋予创建nsp权限
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropNamespace(g_stmt, "nsp1");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

// 没有delete权限的时候开启和结束对账（报错）
// 拥有delete_any权限的时候开启和结束对账（成功）
#ifndef EXPERIMENTAL_GUANGQI
TEST_F(StQueryPrivilegelGroup1, testAccountSysPriv)
{
    // 此处在SetUp中没有导入delete权限，预期check失败，但导入了get权限，预期getcheckinfo成功
    GmcCheckInfoT *checkInfo = NULL;
    GmcCheckStatusE checkStatus;
    Status ret = GmcBeginCheck(g_stmt, g_labelName, 0XFF);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcEndCheck(g_stmt, g_labelName, 0xff, true);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 26, tableName = label1.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_delete.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有delete权限，预期check失败
    ret = GmcBeginCheck(g_stmt, g_labelName, 0XFF);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_labelName, 0xff, true);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 26, tableName = label1.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 这里导入了delete权限，预期check成功。
    ret = GmcBeginCheck(g_stmt, g_labelName, 0XFF);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_CHECKING);
    ret = GmcEndCheck(g_stmt, g_labelName, 0XFF, true);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
}
#endif

TEST_F(StQueryPrivilegelGroup1, testObjPrivTime)
{
    struct timeval start;
    struct timeval end;
    Status ret = system("gmrule -c import_allowlist -f ./001_Privilege_Management/conf/user_import.gmuser");
    ret = system("gmrule -c import_policy -f ./001_Privilege_Management/conf/gm_import_policy.gmpolicy");
    ret = system("gmrule -c import_allowlist -f ./001_Privilege_Management/conf/v5.gmuser");
    ret = system("gmimport -c vschema -f ./gmjson/");
    gettimeofday(&start, NULL);
    ret = system("gmrule -c import_policy -f ./001_Privilege_Management/conf/v5_perf.gmpolicy");
    gettimeofday(&end, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    double timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    (void)printf("time used: %f sec\n", timeUsed);
#ifdef ASAN
    EXPECT_GE(20, timeUsed);
#else
    EXPECT_GE(5, timeUsed);
#endif
}

// 没有delete权限的时候开启和结束对账（报错）
// 拥有delete_any权限的时候开启和结束对账（成功）
#ifndef EXPERIMENTAL_GUANGQI
TEST_F(StQueryPrivilegelGroup1, testAccountObjPriv)
{
    // 此处在SetUp中没有导入delete权限，预期check失败，但导入了get权限，预期getcheckinfo成功
    GmcCheckInfoT *checkInfo = NULL;
    GmcCheckStatusE checkStatus;
    Status ret = GmcBeginCheck(g_stmt, g_labelName, 0XFF);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_labelName, 0xff, true);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 26, tableName = label1.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all_without_delete.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有delete权限，预期check失败
    ret = GmcBeginCheck(g_stmt, g_labelName, 0XFF);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_labelName, 0xff, true);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 26, tableName = label1.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 这里导入了delete权限，预期check成功。
    ret = GmcBeginCheck(g_stmt, g_labelName, 0XFF);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_CHECKING);
    ret = GmcEndCheck(g_stmt, g_labelName, 0XFF, true);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcGetCheckInfo(g_stmt, g_labelName, 0XFF, &checkInfo);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
}
#endif

// 没有SELECT权限直接进行订阅（报错）
// 拥有所有其他系统权限，但是没有select权限，直接进行订阅操作（预期报错）
// 拥有select_any权限，进行订阅操作（成功）

// 没有SELECT权限直接进行订阅（报错）
// 拥有所有其他对象权限，但是没有select权限，直接进行订阅操作（预期报错）
// 拥有select权限，进行订阅操作（成功）
TEST_F(StQueryPrivilegelGroup1, testSubObjPriv)
{
    GmcSubConfigT config;
    config.subsName = g_subsName;
    config.configJson = g_subsJson;
    // 此处没有导入任何权限，预期失败
    Status ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label1.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all_without_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有select权限，预期订阅失败
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 8, tableName = label1.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 这里导入了select权限，预期订阅成功。
    ret = GmcSubscribe(
        g_stmt, &config, g_subChanGroup1, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_subsName);
    EXPECT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_subsName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, g_subsName);
    EXPECT_EQ(ret, GMERR_OK);
}

// 没有DELETE_ANY权限直接进行delete all（报错）
// 拥有所有其他系统权限，但是没有delete_any权限，直接进行delete all（预期报错）
// 拥有delete_any权限，进行delete all（成功）
TEST_F(StQueryPrivilegelGroup1, testDeleteAllSysPriv)
{
    Status ret = GmcDeleteAllFast(g_stmt, g_labelName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 11, tableName = label1.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_without_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有delete_any权限，预期失败
    ret = GmcDeleteAllFast(g_stmt, g_labelName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 11, tableName = label1.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    // 这里导入了delete_any权限，预期订阅成功。
    ret = GmcDeleteAllFast(g_stmt, g_labelName);
    ASSERT_EQ(ret, GMERR_OK);
}

// 没有DELETE权限直接进行订阅（报错）
// 拥有所有其他对象权限，但是没有DELETE权限，直接进行订阅操作（预期报错）
// 拥有DELETE权限，进行订阅操作（成功）
TEST_F(StQueryPrivilegelGroup1, testDeleteAllObjPriv)
{
    Status ret = GmcDeleteAllFast(g_stmt, g_labelName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 11, tableName = label1.");

    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all_without_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    // 此处导入所有权限，但是没有DELETE权限，预期delete all失败
    ret = GmcDeleteAllFast(g_stmt, g_labelName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Have no privilege. The user (root : root : st_010_security) operation lack "
                               "permissions, qryType = 11, tableName = label1.");

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    // 这里导入了DELETE权限，预期delete all成功。
    ret = GmcDeleteAllFast(g_stmt, g_labelName);
    ASSERT_EQ(ret, GMERR_OK);
}

// INSERT_ANY UPDATE_ANY REPLACE_ANY DELETE_ANY SELECT_ANY MERGE_ANY GET
// 以上7种权限，分别具备其中一种、多种，检测是否能进行prepare操作。
TEST_F(StQueryPrivilegelGroup1, testGetVertexLabelSysPrivWithGet)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

// REPLACE_ANY DELETE_ANY SELECT_ANY GET
// 以上7种权限，分别具备其中一种、多种，检测是否能进行prepare操作。
TEST_F(StQueryPrivilegelGroup1, testGetKvTableSysPrivWithGet)
{
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcKvDropTable(g_stmt, g_kvTableName), GMERR_OK);
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcKvDropTable(g_stmt, g_kvTableName), GMERR_OK);
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcKvDropTable(g_stmt, g_kvTableName), GMERR_OK);
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcKvDropTable(g_stmt, g_kvTableName), GMERR_OK);
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GmcKvDropTable(g_stmt, g_kvTableName), GMERR_OK);
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelInsertObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelDeleteObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelMergeObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_merge.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelSelectObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelReplaceObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelUpdateObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantVertexlabelAllObjPriv)
{
    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    ASSERT_EQ(GmcCreateVertexLabel(g_stmt, g_labelJson, NULL), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_labelName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);
}

#ifndef EXPERIMENTAL_GUANGQI
TEST_F(StQueryPrivilegelGroup1, testGetOperCountSysPriv)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    TestVertexLabelDml(g_stmt, g_cfgJsonNoLite, false);
    TestKvDml(g_stmt, g_cfgJsonNoLite, false);
    int countNum = 2;
    uint64_t count[countNum];
    const char *batch[2] = {};
    batch[0] = g_labelName1;
    batch[1] = g_kvTableName;

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, count[0]);
    EXPECT_EQ(0u, count[1]);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_merge.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_merge.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
}
#endif

#ifndef EXPERIMENTAL_GUANGQI
TEST_F(StQueryPrivilegelGroup1, testGetOperCountObjPriv)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);

    ASSERT_EQ(GmcDropVertexLabel(g_stmt, g_labelName), GMERR_OK);
    TestVertexLabelDml(g_stmt, g_cfgJsonNoLite, false);
    TestKvDml(g_stmt, g_cfgJsonNoLite, false);
    int countNum = 2;
    uint64_t count[countNum];
    const char *batch[2] = {};
    batch[0] = g_labelName1;
    batch[1] = g_kvTableName;

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, count[0]);
    EXPECT_EQ(0u, count[1]);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_insert.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_update.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_replace.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_merge.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_merge.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, count, countNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
}
#endif

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantKvTableDeleteObjPriv)
{
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);

// 回收Get权限
#ifdef EXPERIMENTAL_GUANGQI
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv_aa.gmpolicy");
#else
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
#endif
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_delete_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantKvTableReplaceObjPriv)
{
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_replace_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantKvTableSelectObjPriv)
{
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_select_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testHasGetPrivWhenGrantKvTableAllObjPriv)
{
    ASSERT_EQ(GmcKvCreateTable(g_stmt, g_kvTableName, g_cfgJson), GMERR_OK);

    // 回收Get权限
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_get_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    const char *lastErrorStr = GmcGetLastError();
    const char *sub = "Have no privilege";
    EXPECT_NE(strstr(lastErrorStr, sub), nullptr);
    uint32_t labelType = 0;
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_INSUFFICIENT_PRIVILEGE);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/object_privilegel_policy_all_kv.gmpolicy");
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcGetLabelTypeByName(g_stmt, g_kvTableName, &labelType);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testRevokeAllowlistWithAtomic)
{
    // 删除测试套setup时导入的系统权限
    Status ret = system("gmrule atomic -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    EXPECT_EQ(ret, 63488);
}

TEST_F(StQueryPrivilegelGroup1, testRepeatSysPriv1)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create_dup.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    EXPECT_EQ(GmcCreateNamespace(g_stmt, "nsp1", NULL), GMERR_OK);
    // 删除nsp1失败
    EXPECT_EQ(GmcDropNamespace(g_stmt, "nsp1"), GMERR_INSUFFICIENT_PRIVILEGE);
    // 回滚模式下，导入重复的系统权限需要报错+回滚
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_delete_dup.gmpolicy");
    EXPECT_EQ(ret, 37376);
    // 删除nsp1失败
    EXPECT_EQ(GmcDropNamespace(g_stmt, "nsp1"), GMERR_INSUFFICIENT_PRIVILEGE);
    // 回滚模式下，导入重复的系统权限不报错+跳过重复
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_delete_dup.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 删除nsp1成功
    EXPECT_EQ(GmcDropNamespace(g_stmt, "nsp1"), GMERR_OK);
    // 撤销drop和create的权限
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_delete_dup.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testRepeatSysPriv2)
{
    Status ret = system("gmrule -c revoke_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_kv_base.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/gmpolicy_file_group/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    EXPECT_EQ(GmcCreateNamespace(g_stmt, "nsp1", NULL), GMERR_OK);
    // 赋予nsp1 use权限后，use nsp1成功
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GmcUseNamespace(g_stmt, "nsp1"), GMERR_OK);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
    // 重复导入vertexLabel的所有权限
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(g_stmt, g_labelName));
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    EXPECT_EQ(ret, 37376);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(g_stmt, g_labelName));
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(g_stmt, g_labelName));
    ret = system("gmrule atomic -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    EXPECT_EQ(ret, 63488);
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(g_stmt, "nsp1"));
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/gmpolicy_file_group/system_privilegel_policy_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testRepeatObjPriv)
{
    Status ret;
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_kv_base.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    // 创建nsp1成功
    EXPECT_EQ(GmcCreateNamespace(g_stmt, "nsp1", NULL), GMERR_OK);
    // 赋予nsp1 use权限后，use nsp1成功
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_use_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GmcUseNamespace(g_stmt, "nsp1"), GMERR_OK);
    // 删除vertexLabel的所有权限
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/system_privilegel_policy_base_all.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_create_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(g_stmt, g_labelJson, NULL));
    ASSERT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, GmcDropVertexLabel(g_stmt, g_labelName));
    ret = system("gmrule atomic -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, 37376);
    ASSERT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, GmcDropVertexLabel(g_stmt, g_labelName));
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(g_stmt, g_labelName));
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/nsp_all_policy.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(g_stmt, "nsp1"));
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_nsp_create.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testBinaryFilePriv)
{
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/nsp_policy_file_group/system_privilegel_gmsysview.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/gmpolicy_file_group/system_privilegel_policy_binaryfile.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/gmpolicy_file_group/system_privilegel_policy_binaryfile.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT -f PROCESS_NAME=\'st_010_security\'");

    printf("----------revoke binary file open and close priv---------------\n");
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/gmpolicy_file_group/system_privilegel_policy_binaryfile.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT -f PROCESS_NAME=\'st_010_security\'");
    printf("----------grant open and close priv to wrong type---------------\n");
    ret =
        system("gmrule -c import_policy -f "
               "./001_Privilege_Management/gmpolicy_file_group/system_privilegel_policy_binaryfile_wrongtype.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT -f PROCESS_NAME=\'st_010_security\'");
}

// 测试非atomic模式下，导入/撤销权限时，用户不存在不报错
TEST_F(StQueryPrivilegelGroup1, testUserNotExist_obj)
{
    // 导入对象权限
    // 单个用户的情况，预期不会报错
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj/"
                        "object_privilegel_policy_without_user.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 多个用户的情况: 1.不存在用户在首位 2.不存在用户在末尾，预期不会报错
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj/"
                 "object_privilegel_policy_without_part_user1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj/"
                 "object_privilegel_policy_without_part_user2.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 导入目录的情况，预期不会报错
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj");
    EXPECT_EQ(ret, GMERR_OK);

    // 撤销权限，撤销操作不支持目录操作
    // 单个用户的情况，预期不会报错
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj/"
                 "object_privilegel_policy_without_user.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 多个用户的情况: 1.不存在用户在首位 2.不存在用户在末尾，预期不会报错
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj/"
                 "object_privilegel_policy_without_part_user1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_obj/"
                 "object_privilegel_policy_without_part_user2.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StQueryPrivilegelGroup1, testUserNotExist_sys)
{
    // 导入系统权限
    // 单个用户的情况，预期不会报错
    Status ret = system("gmrule -c import_policy -f "
                        "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys/"
                        "system_privilegel_without_user.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 多个用户的情况: 1.不存在用户在首位 2.不存在用户在末尾，预期不会报错
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys/"
                 "system_privilegel_without_part_user1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys/"
                 "system_privilegel_without_part_user2.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 导入目录的情况，预期不会报错
    ret = system("gmrule -c import_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys");
    EXPECT_EQ(ret, GMERR_OK);

    // 撤销权限，撤销操作不支持目录操作
    // 单个用户的情况，预期不会报错
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys/"
                 "system_privilegel_without_user.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);

    // 多个用户的情况: 1.不存在用户在首位 2.不存在用户在末尾，预期不会报错
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys/"
                 "system_privilegel_without_part_user1.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmrule -c revoke_policy -f "
                 "./001_Privilege_Management/policy_v3_file_group/policy_without_part_user_sys/"
                 "system_privilegel_without_part_user2.gmpolicy");
    EXPECT_EQ(ret, GMERR_OK);
}
