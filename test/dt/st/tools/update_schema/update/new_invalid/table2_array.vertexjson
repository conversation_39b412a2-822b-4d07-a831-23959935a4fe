[{"version": "2.0", "type": "record", "name": "table2", "fields": [{"name": "a0", "type": "uint32"}, {"name": "a1", "type": "record", "fields": [{"name": "b1", "type": "uint32"}, {"name": "b2", "type": "uint32"}]}, {"name": "a2", "type": "record", "fixed_array": true, "size": 1, "fields": [{"name": "b3", "type": "uint32"}, {"name": "b4", "type": "uint32"}]}, {"name": "a3", "type": "record", "vector": true, "size": 128, "fields": [{"name": "b5", "type": "uint32"}, {"name": "b6", "type": "uint32"}]}], "keys": [{"name": "table2_pk", "index": {"type": "primary"}, "node": "table2", "fields": ["a0"], "constraints": {"unique": true}}]}]