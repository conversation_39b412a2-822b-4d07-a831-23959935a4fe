%function init()
%function uninit()

// 1. 全量选项
%table inp1(a:int4, b:int4, c:int4)
%table out1(a:int4, b:int4, c:int4) {
    index(0(a)),
    tbm(
        tbm_tableName(out1),
        tbm_id(out1),
        base_struct(out1),
        c_map(
            type(key(uint32_t), value(FwdIf)),
            on_delete(reset),
            field(a, proja),
            field(ignore(b)),
            field(c, "projc", reset_value("0")),
            field_type(int(a, b, c))
        ),
        pre_invoke("pre test"),
        post_invoke("post test")
    )
}
out1(a, b, c) :- inp1(a, b, c).

// 2. 缺省
%table inp2(a:int4, b:int4, c:int4)
%table out2(a:int4, b:int4, c:int4) {
    index(0(a)),
    tbm(
        tbm_tableName(out1),
        tbm_id(out2)
    )
}
out2(a, b, c) :- inp2(a, b, c).

// 2. derived表
%table inp3(a:int4, b:int4, c:int4)
%table out3(a:int4, b:int4, c:int4) {
    index(0(a)),
    tbm(
        tbm_tableName(out1),
        tbm_id(out3)
    )
}
out3(a, b, c) :- inp3(a, b, c).

// 2. field缺省
%table inp4(a:int4, b:int4, c:int4)
%table out4(a:int4, b:int4, c:int4) {
    index(0(a)),
    tbm(
        tbm_tableName(out4),
        tbm_id(out4),
        c_map(
            type(key(uint32_t), value(FwdIf)),
            on_delete(reset),
            field(a, proja),
            field(ignore(b)),
            field_type(int(a, b, c))
        )
    )
}
out4(a, b, c) :- inp4(a, b, c).

namespace hpf {
    %table inpA(a:int4, b:int4, c:int4)
    %table outB(a:int4, b:int4, c:int4) {
        index(0(a)),
        batch_msg_size(2),
        msg_notify(
            invoke("test")
        )
    }
    outB(a, b, c) :- inpA(a, b, c).
}
