// 混合用例 1

%table table0(a:int4, b:int8){
    index(0(a)),
    index(1(b)),
    timeout(field(b)),
    update
}
%table table1(a:int4, b:int8){
    transient(field(a))
}
%table out0(a:int4, b:int8)
table1(a, b) :- table0(a, b).
out0(a, b) :- table1(a, b).



// 混合用例 2

%resource rsc0(a:str, b:int8 -> c:int4){
    sequential(max_size(1000000))
}
%table table2(a:str, b:int8)
%table table3(a:str, b:int8, c:int4)
rsc0(a, b, -) :- table2(a, b).
table3(a, b, c) :- rsc0(a, b, c).

%precedence rsc0, table1

// 混合用例 4

namespace ns0{
    %table table6(a:int4, b:str)
    %function func1(a:int4 -> b:str)
    %readonly table6, func1
}

namespace ns1{
    %resource rsc2(a:int4, b:str -> c:byte32, d:int4){
        pending_id(32, 32)
    }
    %table table7(a:int4, b:str, c:byte32, d:int4)
    %readonly table7
    using namespace ns0
    rsc2(a, b, -, -) :- table6(a, b), func1(a, b).
    table7(a, b, c, d) :- rsc2(a, b, c, d).
}
