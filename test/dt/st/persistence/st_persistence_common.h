/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#ifndef ST_PERSISTENCE_COMMON_H
#define ST_PERSISTENCE_COMMON_H
#include <poll.h>
#include <zlib.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include "storage_st_common.h"
#include "st_common.h"
#include "file_op.h"
#include "gms.h"
#include "gmc_persist.h"
#include "adpt_socket_base.h"

#define ST_NO_DATA 1000
#define ST_EXIT_SUCCESS 0
#define ST_EXIT_FAIL 1
#define MAX_RECORD_NUM 100
#define DEFAULT_MAX_RECORD_NUM 9999999

// 输出文字的颜色
#define KNRM "\x1B[0m"   // 默认
#define KRED "\x1B[31m"  // 红
#define KGRN "\x1B[32m"  // 绿
#define KBLU "\x1B[34m"  // 蓝
#define KCYN "\x1B[36m"  // 青

extern const char *g_labelName;
extern const char *g_configJson;
extern const char *g_liteTrxConfigJson;
extern const char *g_labelJson;
extern const char *g_varVlJsonFormat;
extern const char *g_varMemory10FieldsFormat;
extern const char *g_fixLabelJson;
extern const char *g_fixedVlJsonFormat;

extern const char *g_cfgOfIncrePersist;

extern const char *g_shortString;
extern const char *g_midString;
extern const char *g_longString;
extern const char *g_memoryString;
// 简单表
extern const char *g_normalVertexLabelJson;
extern const char *g_normalLabelConfig;
extern const char *g_normalLabelName;
#define ST_NO_DATA 1000
// 出现错误时跳出循环
#define BREAK_IFERR(ret)                                                                                      \
    {                                                                                                         \
        if ((ret) != GMERR_OK) {                                                                              \
            fprintf(stdout, "Error: %s:%d func:%s " #ret " = %d\n", __FILE__, __LINE__, __FUNCTION__, (ret)); \
            break;                                                                                            \
        }                                                                                                     \
    }
#define WITH_DEFAULT_TRX(conn, op, isCommit)        \
    do {                                            \
        GmcTxConfigT config = {0};                  \
        config.transMode = GMC_TRANS_USED_IN_CS;    \
        config.type = GMC_TX_ISOLATION_COMMITTED;   \
        config.readOnly = false;                    \
        config.trxType = GMC_DEFAULT_TRX;           \
        int32_t ret = GmcTransStart(conn, &config); \
        EXPECT_EQ(GMERR_OK, ret);                   \
        RETURN_IFERR(ret);                          \
        ret = op;                                   \
        RETURN_IFERR(ret);                          \
        if ((isCommit) == 1) {                      \
            ret = GmcTransCommit(conn);             \
        } else if ((isCommit) == 0) {               \
            ret = GmcTransRollBack(conn);           \
        } else {                                    \
        }                                           \
        EXPECT_EQ(GMERR_OK, ret);                   \
    } while (0)

#define WITH_OPTIS_RR_TRX(conn, op, isCommit)       \
    do {                                            \
        GmcTxConfigT config = {0};                  \
        config.transMode = GMC_TRANS_USED_IN_CS;    \
        config.type = GMC_TX_ISOLATION_REPEATABLE;  \
        config.readOnly = false;                    \
        config.trxType = GMC_OPTIMISTIC_TRX;        \
        int32_t ret = GmcTransStart(conn, &config); \
        EXPECT_EQ(GMERR_OK, ret);                   \
        RETURN_IFERR(ret);                          \
        ret = op;                                   \
        RETURN_IFERR(ret);                          \
        if ((isCommit) == 1) {                      \
            ret = GmcTransCommit(conn);             \
        } else if ((isCommit) == 0) {               \
            ret = GmcTransRollBack(conn);           \
        } else {                                    \
        }                                           \
        EXPECT_EQ(GMERR_OK, ret);                   \
    } while (0)

#define WITH_PESSI_RR_TRX(conn, op, isCommit)       \
    do {                                            \
        GmcTxConfigT config = {0};                  \
        config.transMode = GMC_TRANS_USED_IN_CS;    \
        config.type = GMC_TX_ISOLATION_REPEATABLE;  \
        config.readOnly = false;                    \
        config.trxType = GMC_PESSIMISITIC_TRX;      \
        int32_t ret = GmcTransStart(conn, &config); \
        EXPECT_EQ(GMERR_OK, ret);                   \
        RETURN_IFERR(ret);                          \
        ret = op;                                   \
        RETURN_IFERR(ret);                          \
        if ((isCommit) == 1) {                      \
            ret = GmcTransCommit(conn);             \
        } else if ((isCommit) == 0) {               \
            ret = GmcTransRollBack(conn);           \
        } else {                                    \
        }                                           \
        EXPECT_EQ(GMERR_OK, ret);                   \
    } while (0)

#define WITH_TRX(mode, conn, op, isCommit)         \
    do {                                           \
        if ((mode) == PESSI_RC) {                  \
            WITH_DEFAULT_TRX(conn, op, isCommit);  \
        } else if ((mode) == OPTIS_RR) {           \
            WITH_OPTIS_RR_TRX(conn, op, isCommit); \
        } else if ((mode) == PESSI_RR) {           \
            WITH_PESSI_RR_TRX(conn, op, isCommit); \
        }                                          \
    } while (0)

typedef struct {
    bool isExist;
    uint32_t size;
} FileInfoT;
typedef struct PresistentFileInfo {
    FileInfoT ctrlFile;
    FileInfoT sysDataFile;
    FileInfoT userDataFile;
    FileInfoT undoDataFile;
} PersistentFileInfoT;

void TrxBeginWithType(GmcConnT *conn, GmcIsolationTypeE type, GmcTrxTypeE trxType);
void TrxEnd(GmcConnT *conn, uint32_t isCommit);
void GetPersistentFileInfo(const char *path, PersistentFileInfoT *persFile);
bool PersistentFileCompare(PersistentFileInfoT *persFile1, PersistentFileInfoT *persFile2);

int StartDbServerWithRecovery(char *configPath, const char *recoveryPath, bool isBg,
    const char *serverLctr = "usocket:/run/verona/unix_emserver");

int32_t InsertVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t startIndex = 0);
int32_t UpdateVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t startIndex = 0);
int32_t UpdateVertexDataLob(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t startIndex = 0);
int32_t DeleteVertexDataWithPk(GmcStmtT *stmt, uint32_t startIdx, uint32_t recordCount, const char *labelName);
int32_t GetAndCheckProperty(GmcStmtT *stmt, uint32_t f1Value);
int32_t ReadByPk(GmcStmtT *stmt, int32_t recordCount, const char *labelName, int32_t step);
int32_t ReadByScan(GmcStmtT *stmt, uint32_t startVal, int32_t expectCnt, const char *labelName);

void VerifyRecordCount(GmcStmtT *stmt, uint32_t recordCount, const char *labelName);
int32_t GetVertexData(GmcStmtT *stmt, uint32_t startVal, uint32_t recordCount, const char *labelName);
int32_t GetVertexDataLob(
    GmcStmtT *stmt, uint32_t startVal, uint32_t recordCount, const char *labelName, bool isUpdate = false);

void StPersistenceShutdownServer(void);
void ShutdownServer(GmcConnT *conn);
void ShutdownServerNormal(GmcConnT *conn);

int32_t GetInt32CfgCurValue(GmcStmtT *syncStmt, const char *cfgName);
int32_t DbPersistCompressFunc(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize);
int32_t DbPersistDecompressFunc(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize);

namespace fixed_label {
int32_t InsertFixed(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName);
int32_t ReadByPk(GmcStmtT *stmt, uint32_t startIdx, int32_t recordCount, const char *name, bool isUpdate);
int32_t UpdateFixed(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName);
}  // namespace fixed_label

namespace var_label {
int32_t InsertVar(GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName,
    const char *strData);
int32_t InsertBigObj(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName);
int32_t InsertBigObjTwoMegaBytes(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName);
int32_t BatchInsert(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount,
    const char *labelName, const char *strData);
int32_t BatchInsertBigObj(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount,
    const char *labelName);
int32_t ReadUpdatedDataByPk(GmcStmtT *stmt, uint32_t startIdx, uint32_t recordCount, const char *name, const char *str);
int32_t UpdateData(GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName,
    const char *newStr);
int32_t UpdateToBigObj(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName);
int32_t BatchUpdateData(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount,
    const char *labelName, const char *newStr);
int32_t BatchUpdateToBigObj(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx,
    uint32_t recordCount, const char *labelName);
int32_t DeleteByPk(GmcStmtT *stmt, uint32_t pk, const char *labelName);
int32_t ReadByPk(GmcStmtT *stmt, uint32_t startIdx, uint32_t recordCount, const char *labelName);
}  // namespace var_label

inline int32_t DeleteByPk(GmcStmtT *stmt, uint32_t pk, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    RETURN_IFERR(ret);
    return GmcExecute(stmt);
}

inline string GetLabelName(const char *prefix, int idx)
{
    char labelName[32] = {0};
    int len = snprintf_s(labelName, sizeof(labelName), sizeof(labelName) - 1, "%s_%03d", prefix, idx);
    if (len <= 0) {
        EXPECT_TRUE(0);
        return "";
    }
    return string(labelName);
}

int32_t GetUint32ConfigValByName(const char *cfgName, const char *cfgFile, uint32_t *cfgVal);

uint32_t TruncateFile(char *fileName, float percentage, char direction);

int PersistGetPid(const char *processName);

int PersistGetProcessUsedMemSize(const int pid, const char *memType);

int PersistGetFileSize(const char *filePath, const char *fileName);

int PersistGetProcessIoInfo(const int pid, const char *memType);

void StPrintServerMemInfo(const int stServerPid, const char *desc);

void StPrintFileMemInfo(const char *filePath, const char *desc);

void StGetSystemIoInfo(const int stServerPid, int *value);

void StPrintSystemIoInfo(int oldWriteBytes, int newWriteBytes, const char *desc);

bool IsDbServerExit();

int32_t GetRedoTruncateBlockId(uint64_t *fieldVal);

void DestoryDataFileContent(char *filePath, uint32_t pageIndex, uint32_t offset);

void DestoryDataFileHead(char *filePath, uint32_t offset);

void DestoryCtrlFileContent(char *filePath, uint32_t pageIndex, uint32_t offset);

void WriteCtrlFileContent(char *filePath, uint32_t pageIndex, uint32_t offset, uint8_t *content, uint32_t contentLen);

void DestoryRedoFileContent(char *filePath, uint32_t blockId, uint32_t offset);

void DestorySafeFileContent(char *filePath, uint32_t offset);

uint32_t GetSafeFilePageCnt(char *filePath, uint32_t pageCountOffset);

void SwapDataFilePage(char *filePath, uint32_t pageIndex1, uint32_t pageIndex2);

#if (EXPERIMENTAL_GUANGQI)
// 光启共进程部署，持久化压缩，解压，文件名过滤
void StPersistenceGmsRegAdaptFuncs(Status expectRet = GMERR_OK);
#endif

#endif
