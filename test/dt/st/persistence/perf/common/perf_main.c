/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 持久化性能测试框架
 * Author: guopanpan
 * Create: 2021-11-30
 * History:
 * Note:
 *  1. 模块命名前缀 Pt -> Performance Test
 */
#include "perf_common.h"
#include "perf_gmdbv5.h"

int main(int argc, char *argv[])
{
    // 解析命令行参数及初始化运行配置
    PtRunCtxInit();
    PtRunCtxT *ctx = PtRunCtxGet();
    PtParseArgu(argc, argv, ctx);
    PtPrintRunCtxMsg(*ctx);

    int ret = PtByteBuffInit(1024 * 1024);  // 1MB
    RETURN_IFERR(ret);

    // 性能日志文件表头
    const char *opName[] = {
        "write",
        "overwrite",
        "merge",
        "update",
        "delete",
        "read",
        "scan",
        "batch write",
        "batch overwrite",
        "batch merge",
        "batch update",
        "batch delete",
        "read by localhash",
        "read by local",
    };
    PT_ASSERT(sizeof(opName) / sizeof(char *) == PT_OPER_INVALID);

    ret = PtWriteToFile(NULL, "%-16s,", "database");
    RETURN_IFERR(ret);
    for (int32_t i = 0; i < sizeof(opName) / sizeof(char *); i++) {
        ret = PtWriteToFile(NULL, "%-16s,", opName[i]);
        RETURN_IFERR(ret);
    }
    ret = PtWriteToFile(NULL, "\n");
    RETURN_IFERR(ret);

    // GMDBV5数据库性能测试入口
    ret = GdRun();
    RETURN_IFERR(ret);

    PtRunCtxUnint();
    PtByteBuffFree();
    printf("Info: All Opration Are Complete.\n");
    return PT_OK;
}
