
cmake_minimum_required(VERSION 3.10)
project(perf_test LANGUAGES C CXX)
ADD_DEFINITIONS("-DEXPERIMENTAL_GUANGQI")
ADD_DEFINITIONS("-DFEATURE_CLT_SERVER_SAME_PROCESS")
ADD_DEFINITIONS("-DFEATURE_SERVER_FUNC_REG")
ADD_DEFINITIONS("-DFEATURE_PERSISTENCE")

#set(CMAKE_VERBOSE_MAKEFILE ON)
# 设置编译标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_CXX_STANDARD 11)

# 包含头文件路径
include_directories(src)
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/gmdbv5)

set(GMDB_ROOT_PERF ${CMAKE_CURRENT_SOURCE_DIR}/../../../../..)
set(LIBRARY_BUILD_PATH ${GMDB_ROOT_PERF}/build/lib)
link_directories(${LIBRARY_BUILD_PATH})

set(OS $ENV{PERF_OS})
set(ARCH $ENV{PERF_ARCH})
set(GMDB_BUILD_PATH ${GMDB_ROOT_PERF}/output/${OS}/${ARCH})

if(ARCH STREQUAL "x86")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m32")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -m32")
endif()

include_directories(${GMDB_BUILD_PATH}/include)
include_directories(${GMDB_BUILD_PATH}/third_party/include)
include_directories(${GMDB_ROOT_PERF}/test/stub)
include_directories(${GMDB_ROOT_PERF}/test/gtest/include)
include_directories(${GMDB_ROOT_PERF}/test/dt/st/000_st_common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/include/7zlzma)

include (${GMDB_ROOT_PERF}/cmake/function.cmake)
include_sub_directories_recursively(${GMDB_ROOT_PERF}/src)

# 添加 C 源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/gmdbv5 V5_MAIN_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/common V5_COMMON_LIST)

set(GMDB_DT_ST_COMMON_PATH 
    ${GMDB_ROOT_PERF}/test/dt/st/000_st_common/StartDbServer.cc
    ${GMDB_ROOT_PERF}/test/dt/st/000_st_common/InitClt.cc
)

# 共进程部署场景才会需要用到lzma压缩解压接口
set(SRC_ST_LZMA_SRC_LIST
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/LzFindMt.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/Alloc.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/LzFind.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/LzFindOpt.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/LzmaDec.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/LzmaEnc.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/LzmaLib.c
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/Threads.c
    #${CMAKE_CURRENT_SOURCE_DIR}/../lib7zlzma/src/C/CpuArch.c
)
set(SRC_ST_PERSIST_COMMON
    ${GMDB_ROOT_PERF}/test/dt/st/persistence/st_persistence_common.cc
)

set(SRC_ST_PERSIST_COMMON
    ${SRC_ST_PERSIST_COMMON} ${SRC_ST_LZMA_SRC_LIST}
)

set_source_files_properties(${V5_MAIN_LIST} PROPERTIES LANGUAGE CXX)
set_source_files_properties(${V5_COMMON_LIST} PROPERTIES LANGUAGE CXX)

link_directories(${GMDB_BUILD_PATH}/lib)
link_directories(${GMDB_BUILD_PATH}/third_party/lib)

if(ARCH STREQUAL "x86")
link_directories(${GMDB_ROOT_PERF}/test/sdv/exec/third_party/lib/x86_32)
endif()

include_directories(${GMDB_ROOT_PERF}/test/tools/testutil)
if(ARCH STREQUAL "x86")
    link_libraries(${GMDB_ROOT_PERF}/test/gtest/make/gtest_main_euler_x86_64.a)
else()
    link_libraries(${GMDB_ROOT_PERF}/test/gtest/make/gtest_main.a)
endif()

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})

# 添加 C++ 可执行文件
add_executable(perf_test
    ${V5_MAIN_LIST}
    ${V5_COMMON_LIST}
    ${GMDB_DT_ST_COMMON_PATH}
    ${SRC_ST_PERSIST_COMMON}
)

# 链接 C 库到 C++ 程序
if(ARCH STREQUAL "x86")
target_link_libraries(perf_test PRIVATE gmdb gmserver_embed jansson securec pthread gmtools gmadapter m rt z)
elseif(ARM32)
target_link_libraries(perf_test PRIVATE gmdb gmserver_embed jansson securec pthread gmtools atomic gmadapter m rt z)
else()
target_link_libraries(perf_test PRIVATE gmdb gmserver_embed jansson securec pthread gmtools gmadapter m rt z)
endif()
