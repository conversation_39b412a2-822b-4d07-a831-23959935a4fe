#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# used for build db entry
#
set -e

CUR_DIR=$(cd "$(dirname $0)";pwd)
GMDB_DIR="$CUR_DIR"
TMP_DIR="$GMDB_DIR"/build

function clean_build_history() {
    [ -d "${TMP_DIR}" ] && rm -rf "${TMP_DIR}"
    echo "clean $1 all finish"
}

if [ "X${PERF_ARCH}" == "Xx86" ]; then
    defMacro="${defMacro} -m32"
fi

if [ "X${PERF_ARCH}" == "Xarm32a15le" ]; then
    source_toolchain_env arm32a15le
fi

# Argument parser
function parse_arguments() {
    #默认编译debug版本
    debug="-DDEBUG=1"
    release="-DRELEASE=0"
    keysize=""
    allowprint=""
    usage="Usage:
        sh $0 [-h|-c|-t <debug|release>|-k <keySize>|-p]"
    while getopts "t:chk:p" arg; do
        case ${arg} in
            t)
                if [ "X${OPTARG}" = "Xrelease" ] || [ "X${OPTARG}" = "XRELEASE" ]; then
                    release="-DRELEASE=1"
                    debug="-DDEBUG=0"
                elif [ "X${OPTARG}" != "Xdebug" -a "X${OPTARG}" != "XDEBUG" ]; then
                    echo -e "error build type \"${OPTARG}\""
                    echo -e "${usage}"
                    exit 1
                fi
                ;;
            c)
                clean_build_history "build"
                echo "clean binary file success."
                exit 0
                ;;
            k)
                keysize="-DPT_KEY_SIZE=${OPTARG}"
                ;;
            p)
                allowprint="-DPT_ALLOW_PRINT_READ_RESULT=1"
                ;;
            h)
                echo -e "${usage}"
                exit 0
                ;;
            ?)
                echo -e "${usage}"
                exit 0
                ;;
        esac
    done
}

function perf_build_main() {
    GMDB_ROOT="$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/../../..
    parse_arguments "$@"
    cd "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"
    cmake . ${release} ${debug} ${keysize} ${allowprint} -B ${TMP_DIR}
    cd "${TMP_DIR}"/
    make -j
}

function main() {
    if [ "$1" == "clean" ]; then
        clean_build_history "build"
        exit 0
    fi
    perf_build_main "$@"
}

main "$@"

