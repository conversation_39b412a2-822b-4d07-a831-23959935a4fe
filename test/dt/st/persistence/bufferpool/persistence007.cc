/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 持久化原型验证----复杂DML
 * Author: wangyixuan
 * Create: 2023/10/9
 */
#include "crash_debug.h"
#include "st_persistence_common.h"
#include "vl_simple.h"
const int32_t g_repeatNum = 6;
class Persistence007 : public StStorage {
public:
    Persistence007()
    {}
    virtual ~Persistence007()
    {}
    static void SetUpTestCase()
    {
        system("kill -9 $(pidof gmserver)");
        DbSleep(500);
        system("rm -rf /data/gmdb");
        StartDbServer((char *)"gmserver_incre_persist.ini", "usocket:/run/verona/unix_emserver");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        CreateCSModeSyncConnectionAndStmt(&g_conn, &g_stmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }
    static void TearDownTestCase()
    {
        DestroyConnectionAndStmt(g_conn, g_stmt);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {
        Status ret = GmcCreateVertexLabel(g_stmt, g_normalVertexLabelJson, g_normalLabelConfig);
        ASSERT_EQ(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        Status ret = GmcDropVertexLabel(g_stmt, g_normalLabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
};

// 全表扫描数据
TEST_F(Persistence007, testFullScan)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, 1000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// 主键读数据
TEST_F(Persistence007, testPrimaryKeyReading)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, 0, 1000, 0, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, 1000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}
// 更新数据
TEST_F(Persistence007, testUpdate)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC);
    ASSERT_EQ(GMERR_OK, ret);

    // 顺序更新
    ret = VlSimpleUpdateByIndex(g_stmt, 0, 1000, 1, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    ret = VlSimpleScanByIndex(g_stmt, 0, 1000, 1, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, 1000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// delete删除数据
TEST_F(Persistence007, testDelete001)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC);
    ASSERT_EQ(GMERR_OK, ret);

    // 删除数据
    ret = VlSimpleDeleteByIndex(g_stmt, 0, 1000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t count;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &count));
    ASSERT_EQ((uint64_t)0, count);
}

TEST_F(Persistence007, testDelete002)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDeleteAllFast(g_stmt, g_normalLabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// truncate删除数据
TEST_F(Persistence007, testTruncate)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC);
    ASSERT_EQ(GMERR_OK, ret);

    // truncate 数据
    ret = GmcTruncateVertexLabel(g_stmt, g_normalLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t count;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &count));
    ASSERT_EQ((uint64_t)0, count);
}
// drop表
TEST_F(Persistence007, testDrop)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC);
    ASSERT_EQ(GMERR_OK, ret);

    // drop 表
    ret = GmcDropVertexLabel(g_stmt, g_normalLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    // 建个表供teardown去drop
    ret = GmcCreateVertexLabel(g_stmt, g_normalVertexLabelJson, g_normalLabelConfig);
    ASSERT_EQ(GMERR_OK, ret);
}
// 插入数据
TEST_F(Persistence007, testInsert)
{
    int ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = VlSimpleScanByIndex(g_stmt, 0, 1000, 0, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, 1000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// 简单表替换数据
TEST_F(Persistence007, testReplace)
{
    int32_t start = 0, count = 1000, coefficient = 0;
    int ret = VlSimpleReplace(g_stmt, start, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    ret = VlSimpleReplace(g_stmt, start, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// 简单表合并数据
TEST_F(Persistence007, testMerge)
{
    int32_t start = 0, count = 1000, coefficient = 0;
    int ret = VlSimpleMerge(g_stmt, start, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    ret = VlSimpleMerge(g_stmt, start, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

const int32_t repeatCount = 500;
// 重复执行truncate操作
TEST_F(Persistence007, testRepeatTruncate)
{
    int ret;
    for (int32_t i = 0; i < g_repeatNum; i++) {
        ret = VlSimpleInsert(g_stmt, 10, repeatCount, 10);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcTruncateVertexLabel(g_stmt, g_normalLabelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 重复执行drop操作
TEST_F(Persistence007, testRepeatDrop)
{
    int ret;
    for (int32_t i = 0; i < g_repeatNum; i++) {
        ret = GmcDropVertexLabel(g_stmt, g_normalLabelName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(g_stmt, g_normalVertexLabelJson, g_normalLabelConfig);
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleInsert(g_stmt, 10, repeatCount, 10);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 重复执行delete操作
TEST_F(Persistence007, testRepeatDelete)
{
    int ret;
    for (int32_t i = 0; i < g_repeatNum; i++) {
        ret = VlSimpleInsert(g_stmt, 10, repeatCount, 10);
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleDeleteByIndex(g_stmt, 10, repeatCount, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 循环执行增删改查操作
TEST_F(Persistence007, testLoopCreateInsertUpdateScanDeletetTruncateDrop)
{
    int ret;
    //  先删表再开始 因为setup时创建了表
    ret = GmcDropVertexLabel(g_stmt, g_normalLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < g_repeatNum; i++) {
        ret = GmcCreateVertexLabel(g_stmt, g_normalVertexLabelJson, g_normalLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        ASSERT_EQ(GMERR_OK, ret);

        ret = VlSimpleInsert(g_stmt, 10, repeatCount, 10);
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleUpdateByIndex(g_stmt, 10, repeatCount, 11, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleScanByIndex(g_stmt, 10, repeatCount, 11, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleDeleteByIndex(g_stmt, 10, repeatCount, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcTruncateVertexLabel(g_stmt, g_normalLabelName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, g_normalLabelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    //  结束后建个表防止teardown报错
    ret = GmcCreateVertexLabel(g_stmt, g_normalVertexLabelJson, g_normalLabelConfig);
    ASSERT_EQ(GMERR_OK, ret);
}
