/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: head file of degrade concurrency test
 */

#ifndef DUC_UT_BASE_H
#define DUC_UT_BASE_H

#include "client_common_st.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct LabelOpCfg {
    uint32_t start;             // 主键或其他非成员索引的起始值
    uint32_t end;               // 主键或其他非成员索引的结束值（不包含该值）
    GmcOperationTypeE optType;  // vertex操作类型
    uint32_t deltaData;         // update的时候，增加的大小
    bool compareDefault;        // 读取数据时是否允许比较默认值
    bool isDegradeFinish;       // 降级是否已结束
} LabelOpCfgT;

bool CheckCommand(const char *command, const char *expectedStr);
Status PrepareSimpleLabelForDegrade(GmcStmtT *stmt, char *labelConfig = NULL);
void GeneralLabelWriteData(GmcStmtT *stmt, const char *vertexLabelName, uint32_t version, LabelOpCfgT *cfg);

Status GeneralLabelReadData(
    GmcStmtT *stmt, const char *vertexLabelName, uint32_t version, uint32_t comparedVersion, LabelOpCfgT *cfg);
Status DeleteDataForPubsubGC(GmcStmtT *stmt, const char *vertexLabelName, uint32_t version, LabelOpCfgT *cfg);

Status WaitDegradeEnd(GmcStmtT *stmt, const char *vertexLabelName);
bool CheckDataNumOfVertexLabel(const char *vertexLabelName, uint32_t checkNum);
Status CheckSchemaVersion(const char *vertexLabelName, uint32_t expectedVersion);

#ifdef __cplusplus
}
#endif

#endif
