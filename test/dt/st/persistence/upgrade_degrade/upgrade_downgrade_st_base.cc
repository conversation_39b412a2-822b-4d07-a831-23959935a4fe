/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: degrade concurrency test base
 */
#include "upgrade_downgrade__st_base.h"

static char *g_simpleName = (char *)"simpleLabel";
static char *g_labelConfig =
    (char *)R"({"max_record_count":400000, "isFastReadUncommitted":true, "defragmentation":true})";

static Status TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

bool CheckCommand(const char *command, const char *expectedStr)
{
    char buf[10000] = {};
    FILE *file = popen(command, "r");
    fread(buf, sizeof(buf) - 1, 1, file);
    pclose(file);
    if (expectedStr == NULL) {
        printf("Actual:\n%s\n", buf);
        return true;
    }
    if (!strstr(buf, expectedStr)) {
        printf("Actual:\n%s\n", buf);
        return false;
    }
    return true;
}

static void SetPropertyByVersion(GmcStmtT *stmt, uint32_t version, uint32_t value, uint32_t opCode)
{
    Status ret;
    if (opCode == GMC_OPERATION_INSERT || opCode == GMC_OPERATION_REPLACE) {
        uint32_t f0Value = value;
        ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t f1Value = value;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f2Value = value;
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (version >= 2) {
        uint64_t f3Value = (uint64_t)value;
        Status ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT64, &f3Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (version == 3) {
        uint64_t f4Value = (uint64_t)value;
        Status ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_UINT64, &f4Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

Status GeneralLabelWriteData(GmcStmtT *stmt, const char *vertexLabelName, uint32_t version, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    Status ret;
    for (uint32_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, version, cfg->optType);
        if (ret != GMERR_OK) {
            return ret;
        }
        GmcResetVertex(stmt, false);
        // 设置主键（主键值和属性值保持一致，方便精准删除）
        uint32_t keyValue = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 为每个属性设置值
        SetPropertyByVersion(stmt, version, i, cfg->optType);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return GMERR_OK;
}

Status DeleteDataForPubsubGC(GmcStmtT *stmt, const char *vertexLabelName, uint32_t version, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    uint32_t expAffectRows = 1;
    uint32_t retryCount = 0;
    Status ret;
    for (uint32_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, version, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(stmt, false);
        // 设置主键
        uint32_t keyValue = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 为每个属性设置值
        SetProPertyByVersion(stmt, version, i, GMC_OPERATION_DELETE);
        ret = GmcExecute(stmt);
        // 状态合并表与降级有锁竞争,允许重试
        if (ret != GMERR_OK) {
            if (retryCount < 10) {
                retryCount++;
                i--;
                continue;
            }
            return ret;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return GMERR_OK;
}

static Status ComPareVersionProp(
    GmcStmtT *stmt, uint64_t compareValue, char *propName, LabelOpCfgT *cfg, bool isCompareLaterVersion)
{
    Status ret = GMERR_OK;
    uint64_t defaultValue = 6;
    bool flag = false;
    bool isNull = true;
    uint64_t getValue;
    ret = GmcGetVertexPropertyByName(stmt, propName, &getValue, sizeof(uint64_t), &isNull);
    // 降级结束后低版本读高版本数据该字段必定非法
    if (cfg->isDegradeFinish && isCompareLaterVersion) {
        return ret == GMERR_INVALID_PROPERTY ? GMERR_OK : GMERR_DATA_EXCEPTION;
    }
    // 高版本字段值不一定有值,为空需跳过
    if (isNull) {
        return GMERR_OK;
    }
    // 若不允许比较默认值则说明当前数据必定为高版本
    if (!cfg->compareDefault) {
        flag = compareValue == getValue;
    } else {
        flag = defaultValue == getValue || compareValue == getValue;
    }
    return flag ? GMERR_OK : GMERR_DATA_EXCEPTION;
}

static Status ReadDataAndCompare(
    GmcStmtT *stmt, uint32_t version, uint32_t comparedVersion, uint32_t value, LabelOpCfgT *cfg)
{
    Status ret;
    bool isNull = true;
    uint32_t f0Value = value;
    uint32_t f0GetValue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0GetValue, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK || f0Value != f0GetValue) {
        return ret;
    }

    uint32_t f1Value = value;
    uint32_t f1GetValue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1GetValue, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK || f1Value != f1GetValue) {
        return ret;
    }

    uint32_t f2Value = value;
    uint32_t f2GetValue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2GetValue, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK || f2Value != f2GetValue) {
        return ret;
    }

    if (comparedVersion >= 2) {
        ret = ComPareVersionProp(stmt, value, (char *)"F3", cfg, version < comparedVersion);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (comparedVersion == 3) {
        ret = ComPareVersionProp(stmt, value, (char *)"F4", cfg, version < comparedVersion);
    }
    return ret;
}

Status GeneralLabelReadData(
    GmcStmtT *stmt, const char *vertexLabelName, uint32_t version, uint32_t comparedVersion, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    bool isFinish = false;
    Status ret;
    for (uint32_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, version, cfg->optType);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(stmt, false);
        // 设置主键
        uint32_t keyValue = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // 取值然后进行比较
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = ReadDataAndCompare(stmt, version, comparedVersion, i + cfg->deltaData, cfg);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

Status WaitDegradeEnd(GmcStmtT *stmt, const char *vertexLabelName)
{
    Status ret = GMERR_OK;
    // 等待表降级执行结束
    uint32_t degradeProcess = 0;
    while (true) {
        ret = GmcGetVertexLabelDegradeProgress(stmt, vertexLabelName, &degradeProcess);
        if (ret != GMERR_OK) {
            break;
        }
        sleep(1);
    }
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    return ret;
}

Status PrepareSimpleLabelForDegrade(GmcStmtT *stmt, char *labelConfig)
{
    // 建表 version=1：插入5000条数据
    string labelJson = GetFileContext("./st_data/simple_v1.json");
    labelConfig = labelConfig == NULL ? g_labelConfig : labelConfig;
    Status ret = GmcCreateVertexLabel(stmt, labelJson.c_str(), labelConfig);
    if (ret != GMERR_OK) {
        return ret;
    }
    LabelOpCfgT cfg;
    cfg.start = 0;
    cfg.end = 5000;
    cfg.optType = GMC_OPERATION_INSERT;
    cfg.deltaData = 0;
    GeneralLabelWriteData(stmt, g_simpleName, 1, &cfg);

    // 升级 version=2：继续插入5000条数据
    string labelJson2 = GetFileContext("./st_data/simple_v2.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJson2.c_str(), true, g_simpleName);
    if (ret != GMERR_OK) {
        return ret;
    }
    LabelOpCfgT cfg2;
    cfg2.start = 5000;
    cfg2.end = 10000;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.deltaData = 0;
    GeneralLabelWriteData(stmt, g_simpleName, 2, &cfg2);
    return ret;
}

bool CheckDataNumOfVertexLabel(const char *vertexLabelName, uint32_t checkNum)
{
    char buf[10000] = {};
    uint32_t maxLen = 100;
    char command[maxLen] = {};
    char result[maxLen] = {};
    sprintf_s(command, maxLen, "gmsysview count %s", vertexLabelName);
    sprintf_s(result, maxLen, "|   %" PRIu32, checkNum);
    FILE *file = popen(command, "r");
    fread(buf, sizeof(buf) - 1, 1, file);
    pclose(file);
    bool ret = strstr(buf, result);
    if (!ret) {
        printf("Actual:\n%s\n", buf);
        printf("Expected num:\n%" PRIu32 "\n", checkNum);
    }
    return ret;
}

Status CheckSchemaVersion(const char *vertexLabelName, uint32_t expectedVersion)
{
    char command[300] = {};
    char grepCommand[400] = {};
    char expectedStr[100] = {};

    // 构造gmsysview命令，输出到临时文件
    sprintf_s(command, 300,
        "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME='%s' > schema_version_test.log", vertexLabelName);
    sprintf_s(expectedStr, 100, "VERSION: %" PRIu32, expectedVersion);

    // 执行gmsysview命令
    int ret = system(command);
    if (ret != 0) {
        printf("Failed to execute gmsysview command: %s\n", command);
        return GMERR_SYSTEM_ERROR;
    }

    // 使用grep检查版本信息
    sprintf_s(grepCommand, 400, "grep \"%s\" schema_version_test.log", expectedStr);
    ret = system(grepCommand);

    // 清理临时文件
    system("rm -f schema_version_test.log");

    if (ret != 0) {
        printf("Schema version check failed for table: %s\n", vertexLabelName);
        printf("Expected: %s\n", expectedStr);
        printf("Grep command: %s\n", grepCommand);
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_OK;
}

Status CreateTestConnection(GmcConnT **conn, GmcStmtT **stmt)
{
    const char *userName = "XXuser";
    ConnOptionT options = {};
    options.serverLocator = g_gmServerLocator;
    options.userName = userName;
    options.msgReadTimeout = CLIENT_CONNECT_DEFAULT_TIMEOUT;
    options.connName = NULL;

    Status ret = CreateConnection(GMC_CONN_TYPE_SYNC, &options, conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmcAllocStmt(*conn, stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}
