{"type": "record", "name": "simpleLabel", "schema_version": 3, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint64", "nullable": true, "default": 6}, {"name": "F4", "type": "uint64", "nullable": true, "default": 6}], "keys": [{"node": "simpleLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}