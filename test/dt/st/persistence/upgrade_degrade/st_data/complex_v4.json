{"type": "record", "name": "complexLabel", "schema_version": 2, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "nullable": true}, {"name": "SubRecord1", "type": {"type": "record", "name": "SubRecord1", "fields": [{"name": "SR1_F1", "type": "uint64", "nullable": true}, {"name": "SR1_F2", "type": "string", "nullable": true}]}, "nullable": true}, {"name": "F3", "type": "uint64", "nullable": true, "default": 100}, {"name": "F4", "type": "uint64", "nullable": true, "default": 100}], "keys": [{"node": "complexLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}