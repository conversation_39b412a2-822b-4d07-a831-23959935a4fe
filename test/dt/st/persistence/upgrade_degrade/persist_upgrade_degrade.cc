/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#include "crash_debug.h"
#include "storage_st_common.h"
#include "st_persistence_common.h"
#include "upgrade_downgrade__st_base.h"

class StUpgradeDowngradePersistConcurrency : public StStorage {
public:
    StUpgradeDowngradePersistConcurrency()
    {}
    virtual ~StUpgradeDowngradePersistConcurrency()
    {}
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    virtual void SetUp()
    {
        system("kill -9 $(pidof gmserver)");
        DbSleep(500);
        system("rm -rf /data/gmdb");
        system("ipcrm -a");
        system("rm -rf temp_gmserver_upgrade_downgrade_persist.ini");
        StModifyConfig("gmserver_upgrade_downgrade_persist.ini", "temp_gmserver_upgrade_downgrade_persist.ini",
            "\"featureNames=MEMDATA,DURABLEMEMDATA,FASTPATH,TRM,PERSISTENCE\"");
        StartDbServer((char *)"temp_gmserver_upgrade_downgrade_persist.ini", "usocket:/run/verona/unix_emserver", true);
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#endif
        st_clt_init();
        CreateSyncConnectionAndStmt(&StUpgradeDowngradePersistConcurrency::syncConn,
            &StUpgradeDowngradePersistConcurrency::syncStmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }

    void Disconnect()
    {
        DestroyConnectionAndStmt(
            StUpgradeDowngradePersistConcurrency::syncConn, StUpgradeDowngradePersistConcurrency::syncStmt);
        st_clt_uninit();
    }

    virtual void TearDown()
    {
        Disconnect();
        ShutDownDbServer();
        system("rm -rf temp_gmserver_upgrade_downgrade_persist.ini");
    }

protected:
    void RestartAndConnect()
    {
        ShutdownServer(syncConn);
        StartDbServer((char *)"temp_gmserver_upgrade_downgrade_persist.ini", "usocket:/run/verona/unix_emserver");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        // 保证DB启动成功
        while (system("gmsysview -q V\\$DB_SERVER > /dev/null")) {
            DbSleep(200);
        }
        Connect();
#endif
    }
    void Connect()
    {
        st_clt_init();
        CreateSyncConnectionAndStmt(&syncConn, &syncStmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }
    static GmcConnT *syncConn;
    static GmcStmtT *syncStmt;
};

// 批删并发
TEST_F(StUpgradeDowngradePersistConcurrency, 001_BatchDelete)
{
    const char *userName = "XXuser";
    ConnOptionT options = {};
    options.serverLocator = g_gmServerLocator;
    options.userName = userName;
    options.msgReadTimeout = CLIENT_CONNECT_DEFAULT_TIMEOUT;
    options.connName = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    EXPECT_EQ(GMERR_OK, CreateConnection(GMC_CONN_TYPE_SYNC, &options, &conn));
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));
    // 1. 建表，并升级一次
    ASSERT_EQ(GMERR_OK, PrepareSimpleLabelForDegrade(stmt));

    // 【升级/批删 并发】
    Status ret = GMERR_OK;
    LabelOpCfgT cfg;
    // 2. 再插入10000个数据
    cfg.optType = GMC_OPERATION_INSERT;
    cfg.start = 10000;
    cfg.end = 20000;
    cfg.deltaData = 0;
    GeneralLabelWriteData(stmt, g_simpleName, 2, &cfg);
    ret = CheckDataNumOfVertexLabel(g_simpleName, 20000);
    EXPECT_EQ(true, ret);

    // 3. 批量接口删除操作
    ret = GmcPrepareStmtByLabelName(stmt, g_simpleName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t i = 0;
    // 准备先删除0-999数据
    for (i = 0; i < 1000; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 升级
    string labelJson = GetFileContext("./st_data/simple_v3.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, g_simpleName));

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 等待1s确保批删执行结束
    sleep(1);
    ret = CheckDataNumOfVertexLabel(g_simpleName, 19000);
    EXPECT_EQ(true, ret);

    // 准备再删除1000-1999数据
    for (i = 1000; i < 2000; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 在降级之前进行重启操作
    RestartAndConnect();

    // 降级到version=1
    ret = GmcDegradeVertexLabel(stmt, g_simpleName, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);

    // 此时降级不一定结束,需等待
    ret = WaitDegradeEnd(stmt, g_simpleName);
    EXPECT_EQ(GMERR_OK, ret);
    // 预期剩余18000条版本1数据
    ret = CheckDataNumOfVertexLabel(g_simpleName, 18000);
    EXPECT_EQ(true, ret);
    // 版本1读版本3字段应无数据,此处不会报错
    cfg.optType = GMC_OPERATION_SCAN;
    cfg.start = 2000;
    cfg.end = 20000;
    cfg.compareDefault = false;
    cfg.isDegradeFinish = true;
    ret = GeneralLabelReadData(stmt, g_simpleName, 1, 3, &cfg);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除
    GmcDropVertexLabel(stmt, g_simpleName);
    (void)GmcBatchDestroy(batch);
    // 释放连接资源
    DestroyConnectionAndStmt(conn, stmt);
}

// 测试schema_version未定义的表升级降级错误处理
TEST_F(StUpgradeDowngradePersistConcurrency, 002_SchemaVersionNotDefined)
{
    const char *userName = "XXuser";
    ConnOptionT options = {};
    options.serverLocator = g_gmServerLocator;
    options.userName = userName;
    options.msgReadTimeout = CLIENT_CONNECT_DEFAULT_TIMEOUT;
    options.connName = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    EXPECT_EQ(GMERR_OK, CreateConnection(GMC_CONN_TYPE_SYNC, &options, &conn));
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));

    const char *testLabelName = "testSchemaVersionLabel";

    // 1. 根据simple_not_define.json创建表（schema_version未定义，默认为0）
    GmcDropVertexLabel(stmt, testLabelName);
    string labelJsonNotDefined = GetFileContext("./st_data/simple_not_define.json");
    Status ret = GmcCreateVertexLabel(stmt, labelJsonNotDefined.c_str(), NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 2. 查询表schema_version，验证为0
    ret = CheckSchemaVersion(testLabelName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 3. 尝试升级到simple_v1.json对应的表，预期报错
    string labelJsonV1 = GetFileContext("./st_data/simple_v1.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonV1.c_str(), true, testLabelName);
    EXPECT_NE(GMERR_OK, ret);  // 预期升级失败

    // 4. 尝试降级到simple_v1.json对应的表，预期报错
    ret = GmcDegradeVertexLabel(stmt, testLabelName, 1);
    EXPECT_NE(GMERR_OK, ret);  // 预期降级失败

    // 5. 验证表的schema_version仍然为0（升级降级都失败，版本不变）
    ret = CheckSchemaVersion(testLabelName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 清理资源
    GmcDropVertexLabel(stmt, testLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

// 综合测试schema版本升级降级场景
TEST_F(StUpgradeDowngradePersistConcurrency, 003_ComprehensiveSchemaVersionTest)
{
    const char *userName = "XXuser";
    ConnOptionT options = {};
    options.serverLocator = g_gmServerLocator;
    options.userName = userName;
    options.msgReadTimeout = CLIENT_CONNECT_DEFAULT_TIMEOUT;
    options.connName = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    EXPECT_EQ(GMERR_OK, CreateConnection(GMC_CONN_TYPE_SYNC, &options, &conn));
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));

    const char *testLabelName = "comprehensiveTestLabel";
    Status ret;

    // ST1: 创建简单表，不设置schema_version
    printf("ST1: 创建简单表，不设置schema_version\n");
    GmcDropVertexLabel(stmt, testLabelName);
    string labelJsonNotDefined = GetFileContext("./st_data/simple_not_define.json");
    ret = GmcCreateVertexLabel(stmt, labelJsonNotDefined.c_str(), NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // EX1: 查询VL表schema_version正确为0
    printf("EX1: 查询VL表schema_version正确为0\n");
    ret = CheckSchemaVersion(testLabelName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // ST2: 升级到最大version值
    printf("ST2: 升级到最大version值\n");
    string labelJsonVMax = GetFileContext("./st_data/simple_vMax.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonVMax.c_str(), true, testLabelName);

    // EX2: 查询VL表schema_version正确
    printf("EX2: 查询VL表schema_version正确\n");
    if (ret == GMERR_OK) {
        ret = CheckSchemaVersion(testLabelName, 4294967294);  // 0xffffffff-1
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        printf("升级到最大版本失败，这是预期的\n");
    }

    // 重新创建表用于后续测试
    GmcDropVertexLabel(stmt, testLabelName);
    ret = GmcCreateVertexLabel(stmt, labelJsonNotDefined.c_str(), NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // ST3: 持久化重启
    printf("ST3: 持久化重启\n");
    // 这里模拟持久化重启，实际测试中可能需要特殊处理

    // EX3: 查询VL表schema_version正确
    printf("EX3: 查询VL表schema_version正确\n");
    ret = CheckSchemaVersion(testLabelName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 清理资源
    GmcDropVertexLabel(stmt, testLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}