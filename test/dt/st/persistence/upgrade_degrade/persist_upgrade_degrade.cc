/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#include "crash_debug.h"
#include "storage_st_common.h"
#include "st_persistence_common.h"
#include "upgrade_downgrade__st_base.h"

class StUpgradeDowngradePersistConcurrency : public StStorage {
public:
    StUpgradeDowngradePersistConcurrency()
    {}
    virtual ~StUpgradeDowngradePersistConcurrency()
    {}
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    virtual void SetUp()
    {
        system("kill -9 $(pidof gmserver)");
        DbSleep(500);
        system("rm -rf /data/gmdb");
        system("ipcrm -a");
        system("rm -rf temp_gmserver_upgrade_downgrade_persist.ini");
        StModifyConfig("gmserver_upgrade_downgrade_persist.ini", "temp_gmserver_upgrade_downgrade_persist.ini",
            "\"featureNames=MEMDATA,DURABLEMEMDATA,FASTPATH,TRM,PERSISTENCE\"");
        StartDbServer((char *)"temp_gmserver_upgrade_downgrade_persist.ini", "usocket:/run/verona/unix_emserver", true);
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#endif
        st_clt_init();
        CreateSyncConnectionAndStmt(&StUpgradeDowngradePersistConcurrency::syncConn,
            &StUpgradeDowngradePersistConcurrency::syncStmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }

    void Disconnect()
    {
        DestroyConnectionAndStmt(
            StUpgradeDowngradePersistConcurrency::syncConn, StUpgradeDowngradePersistConcurrency::syncStmt);
        st_clt_uninit();
    }

    virtual void TearDown()
    {
        Disconnect();
        ShutDownDbServer();
        system("rm -rf temp_gmserver_upgrade_downgrade_persist.ini");
    }

protected:
    void RestartAndConnect()
    {
        ShutdownServer(syncConn);
        StartDbServer((char *)"temp_gmserver_upgrade_downgrade_persist.ini", "usocket:/run/verona/unix_emserver");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        // 保证DB启动成功
        while (system("gmsysview -q V\\$DB_SERVER > /dev/null")) {
            DbSleep(200);
        }
        Connect();
#endif
    }
    void Connect()
    {
        st_clt_init();
        CreateSyncConnectionAndStmt(&syncConn, &syncStmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }
    static GmcConnT *syncConn;
    static GmcStmtT *syncStmt;
};

// 批删并发
TEST_F(StUpgradeDowngradePersistConcurrency, 001_BatchDelete)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    EXPECT_EQ(GMERR_OK, CreateTestConnection(&conn, &stmt));
    // 1. 建表，并升级一次
    ASSERT_EQ(GMERR_OK, PrepareSimpleLabelForDegrade(stmt));

    // 【升级/批删 并发】
    Status ret = GMERR_OK;
    LabelOpCfgT cfg;
    // 2. 再插入10000个数据
    cfg.optType = GMC_OPERATION_INSERT;
    cfg.start = 10000;
    cfg.end = 20000;
    cfg.deltaData = 0;
    GeneralLabelWriteData(stmt, g_simpleName, 2, &cfg);
    ret = CheckDataNumOfVertexLabel(g_simpleName, 20000);
    EXPECT_EQ(true, ret);

    // 3. 批量接口删除操作
    ret = GmcPrepareStmtByLabelName(stmt, g_simpleName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t i = 0;
    // 准备先删除0-999数据
    for (i = 0; i < 1000; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 升级
    string labelJson = GetFileContext("./st_data/simple_v3.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, g_simpleName));

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 等待1s确保批删执行结束
    sleep(1);
    ret = CheckDataNumOfVertexLabel(g_simpleName, 19000);
    EXPECT_EQ(true, ret);

    // 准备再删除1000-1999数据
    for (i = 1000; i < 2000; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 在降级之前进行重启操作
    RestartAndConnect();

    // 降级到version=1
    ret = GmcDegradeVertexLabel(stmt, g_simpleName, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);

    // 此时降级不一定结束,需等待
    ret = WaitDegradeEnd(stmt, g_simpleName);
    EXPECT_EQ(GMERR_OK, ret);
    // 预期剩余18000条版本1数据
    ret = CheckDataNumOfVertexLabel(g_simpleName, 18000);
    EXPECT_EQ(true, ret);
    // 版本1读版本3字段应无数据,此处不会报错
    cfg.optType = GMC_OPERATION_SCAN;
    cfg.start = 2000;
    cfg.end = 20000;
    cfg.compareDefault = false;
    cfg.isDegradeFinish = true;
    ret = GeneralLabelReadData(stmt, g_simpleName, 1, 3, &cfg);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除
    GmcDropVertexLabel(stmt, g_simpleName);
    (void)GmcBatchDestroy(batch);
    // 释放连接资源
    DestroyConnectionAndStmt(conn, stmt);
}

// 测试schema_version未定义的表升级降级错误处理
TEST_F(StUpgradeDowngradePersistConcurrency, 002_SchemaVersionNotDefined)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    EXPECT_EQ(GMERR_OK, CreateTestConnection(&conn, &stmt));

    // 1. 根据simple_not_define.json创建表（schema_version未定义，默认为0）
    GmcDropVertexLabel(stmt, g_simpleName);
    string labelJsonNotDefined = GetFileContext("./st_data/simple_not_define.json");
    Status ret = GmcCreateVertexLabel(stmt, labelJsonNotDefined.c_str(), NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 2. 查询表schema_version，验证为0
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 3. 尝试升级到simple_v1.json对应的表，预期报错
    string labelJsonV1 = GetFileContext("./st_data/simple_v1.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonV1.c_str(), true, g_simpleName);
    EXPECT_NE(GMERR_OK, ret);  // 预期升级失败

    // 4. 尝试降级到simple_v1.json对应的表，预期报错
    ret = GmcDegradeVertexLabel(stmt, g_simpleName, 1);
    EXPECT_NE(GMERR_OK, ret);  // 预期降级失败

    // 5. 验证表的schema_version仍然为0（升级降级都失败，版本不变）
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 清理资源
    GmcDropVertexLabel(stmt, g_simpleName);
    DestroyConnectionAndStmt(conn, stmt);
}

// 综合测试schema版本升级降级场景
TEST_F(StUpgradeDowngradePersistConcurrency, 003_ComprehensiveSchemaVersionTest)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    EXPECT_EQ(GMERR_OK, CreateTestConnection(&conn, &stmt));

    Status ret;

    // 创建简单表，不设置schema_version
    GmcDropVertexLabel(stmt, g_simpleName);
    string labelJsonNotDefined = GetFileContext("./st_data/simple_not_define.json");
    ret = GmcCreateVertexLabel(stmt, labelJsonNotDefined.c_str(), NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询VL表schema_version正确为0
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 升级到最大version值
    string labelJsonVMax = GetFileContext("./st_data/simple_vMax.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonVMax.c_str(), true, g_simpleName);
    EXPECT_EQ(GMERR_OK, ret);
    // 查询VL表schema_version正确
    ret = CheckSchemaVersion(g_simpleName, 4294967294);  // 0xffffffff-1
    EXPECT_EQ(GMERR_OK, ret);

    // 对最大版本进行插入、查询操作
    LabelOpCfgT maxVersionCfg;
    maxVersionCfg.optType = GMC_OPERATION_INSERT;
    maxVersionCfg.start = 0;
    maxVersionCfg.end = 3;
    maxVersionCfg.deltaData = 0;

    // 插入测试数据到最大版本
    for (uint32_t i = maxVersionCfg.start; i < maxVersionCfg.end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_simpleName, 4294967294, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(stmt, false);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置属性值
        uint32_t f1Value = i + 1000;
        uint32_t f2Value = i + 2000;
        ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 验证最大版本数据数量
    bool maxVersionDataCheck = CheckDataNumOfVertexLabel(g_simpleName, maxVersionCfg.end - maxVersionCfg.start);
    EXPECT_EQ(true, maxVersionDataCheck);

    // 降级到版本0
    ret = GmcDegradeVertexLabel(stmt, g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 验证降级后的版本
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 持久化重启
    RestartAndConnect();

    // 查询VL表schema_version正确
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 对重启后版本0进行插入、查询操作
    LabelOpCfgT restartCfg;
    restartCfg.optType = GMC_OPERATION_INSERT;
    restartCfg.start = 10;
    restartCfg.end = 13;
    restartCfg.deltaData = 0;

    // 插入测试数据到重启后的版本0
    for (uint32_t i = restartCfg.start; i < restartCfg.end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_simpleName, 0, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(stmt, false);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置属性值
        uint32_t f1Value = i + 3000;
        uint32_t f2Value = i + 4000;
        ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 验证重启后版本0数据数量
    bool restartDataCheck = CheckDataNumOfVertexLabel(g_simpleName, restartCfg.end - restartCfg.start);
    EXPECT_EQ(true, restartDataCheck);

    // 对新版本插入、查询操作
    // 先升级到V1版本
    string labelJsonV1 = GetFileContext("./st_data/simple_v1.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonV1.c_str(), true, g_simpleName);
    EXPECT_EQ(GMERR_OK, ret);

    // 预期成功
    ret = CheckSchemaVersion(g_simpleName, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 降级到版本0
    ret = GmcDegradeVertexLabel(stmt, g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询VL表schema_version正确为0
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 对新版本插入、查询操作
    // 重新升级到V2版本进行测试
    string labelJsonV2 = GetFileContext("./st_data/simple_v2.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonV2.c_str(), true, g_simpleName);

    // 预期失败
    EXPECT_NE(GMERR_OK, ret);

    // 对版本0插入、查询操作
    // 在当前版本进行数据操作测试
    LabelOpCfgT cfg;
    cfg.optType = GMC_OPERATION_INSERT;
    cfg.start = 0;
    cfg.end = 5;
    cfg.deltaData = 0;

    // 插入测试数据
    for (uint32_t i = cfg.start; i < cfg.end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_simpleName, 0, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(stmt, false);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置其他属性值
        uint32_t f1Value = i + 100;
        uint32_t f2Value = i + 200;
        ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 验证数据数量
    bool dataCheck = CheckDataNumOfVertexLabel(g_simpleName, cfg.end - cfg.start);
    EXPECT_EQ(true, dataCheck);

    // 持久化重启
    RestartAndConnect();

    // 查询VL表schema_version正确为0
    ret = CheckSchemaVersion(g_simpleName, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 对新版本插入、查询操作
    // 尝试升级到V3版本
    string labelJsonV3 = GetFileContext("./st_data/simple_v3.json");
    ret = GmcAlterVertexLabelWithName(stmt, labelJsonV3.c_str(), true, g_simpleName);

    // 预期失败
    EXPECT_NE(GMERR_OK, ret);

    // 对版本0插入、查询操作
    // 在版本0进行数据操作
    LabelOpCfgT cfg;
    cfg.optType = GMC_OPERATION_INSERT;
    cfg.start = 0;
    cfg.end = 10;
    cfg.deltaData = 0;

    // 插入一些测试数据
    for (uint32_t i = cfg.start; i < cfg.end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_simpleName, 0, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(stmt, false);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置其他属性值
        uint32_t f1Value = i + 100;
        uint32_t f2Value = i + 200;
        ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 验证数据数量
    bool dataCheck = CheckDataNumOfVertexLabel(g_simpleName, cfg.end - cfg.start);
    EXPECT_EQ(true, dataCheck);

    // 清理资源
    GmcDropVertexLabel(stmt, g_simpleName);
    DestroyConnectionAndStmt(conn, stmt);
}