/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: aged in on demand mode
 * Author:
 * Create:
 */
#include "storage_st_common.h"
#include "db_dyn_load.h"
#include "st_persistence_common.h"
#include "st_common.h"
#include "gmc_errno.h"
#include "incre_persist_table.h"

class StPersistCheckAge : public StStorage {
public:
    StPersistCheckAge()
    {}
    virtual ~StPersistCheckAge()
    {}
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    virtual void SetUp()
    {
        ShutdownServer(NULL);
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
        system("rm -rf temp_gmserver_flush_on_demand.ini");
        StModifyConfig("gmserver_flush_on_demand.ini", "temp_gmserver_flush_on_demand.ini",
            "\"featureNames=MEMDATA,DURABLEMEMDATA,FASTPATH,TRM,PERSISTENCE\"");
        StartDbServer((char *)"temp_gmserver_flush_on_demand.ini", "usocket:/run/verona/unix_emserver", true);
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#endif
        st_clt_init();
        CreateSyncConnectionAndStmt(
            &StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);

        StPersistCheckAge::needTearDown = true;
    }
    virtual void TearDown()
    {
        if (!StPersistCheckAge::needTearDown) {
            return;
        }
        DestroyConnectionAndStmt(StPersistCheckAge::syncConn, StPersistCheckAge::syncStmt);
        st_clt_uninit();
        ShutdownServer(StPersistCheckAge::syncConn);
        system("ipcrm -a");
        system("rm -rf temp_gmserver_flush_on_demand.ini");
    }

protected:
    static GmcConnT *syncConn;
    static GmcStmtT *syncStmt;
    bool needTearDown;
};

GmcConnT *StPersistCheckAge::syncConn = nullptr;
GmcStmtT *StPersistCheckAge::syncStmt = nullptr;

static const char *g_labelConfig = R"({"max_record_count":100000})";
// 非分区表
static const char *g_nonPartitionLabel = "non_partition_label";
static const char *g_nonPartitionSchema =
    R"([{
        "type":"record",
        "name":"non_partition_label",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"non_partition_label",
                    "name":"K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

// 分区表
static const char *g_partitionLabel = "partition_label";
static const char *g_partitionSchema =
    R"([{
        "type":"record",
        "name":"partition_label",
        "fields":
        [
            {"name":"F0", "type":"int32", "nullable":false},
            {"name":"F1", "type":"int32", "nullable":false},
            {"name":"F2", "type":"partition", "nullable":false}
        ],
        "keys":
        [
            {
                "node":"partition_label",
                "name":"K0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
        }])";

static void ScanVertex(GmcStmtT *stmt, int32_t begin, int32_t end)
{
    Status ret = GMERR_OK;
    for (int32_t val = begin; val <= end; val++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcSetIndexKeyName(stmt, "K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);

        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(eof);

        int32_t f1;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(val, f1);
    }

    ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    bool isFinish = false;
    uint32_t count = 0;
    do {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(ret, GMERR_OK);
        count += !isFinish;
    } while (!isFinish);
    EXPECT_EQ(count, (uint32_t)(end - begin + 1));
}

static void InsertVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition, uint8_t partitionId)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partitionLabel, GMC_OPERATION_INSERT);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_INSERT);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (isPartition) {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        } else {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void ReplaceVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition, uint8_t partitionId)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partitionLabel, GMC_OPERATION_REPLACE);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_REPLACE);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (isPartition) {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        } else {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        do {
            ret = GmcExecute(stmt);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void UpdateVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partitionLabel, GMC_OPERATION_UPDATE);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_UPDATE);
        }
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        ret = GmcSetIndexKeyName(stmt, "K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 更新字段F1
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void DeleteVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partitionLabel, GMC_OPERATION_DELETE);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_DELETE);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void UpdateVertexCheckVersion(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partitionLabel, GMC_OPERATION_UPDATE_VERSION);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_nonPartitionLabel, GMC_OPERATION_UPDATE_VERSION);
        }
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        ret = GmcSetIndexKeyName(stmt, "K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static const uint32_t g_commandLen = 1024;

void CheckHeapInfoView(const char *vlName, const char *expectedResults[], uint32_t resultsNum)
{
    char command[g_commandLen] = {};
    (void)snprintf_s(command, g_commandLen, g_commandLen - 1,
        "gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=%s -s usocket:/run/verona/unix_emserver", vlName);
    Status ret = StExecuteCommandWithMatch(command, expectedResults, resultsNum);
    EXPECT_EQ(GMERR_OK, ret);
}

void CheckCheckInfoView(const char *vlName, const char *expectedResults[], uint32_t resultsNum)
{
    char command[g_commandLen] = {};
    (void)snprintf_s(command, g_commandLen, g_commandLen - 1,
        "gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -f VERTEX_LABEL_NAME=%s", vlName);
    Status ret = StExecuteCommandWithMatch(command, expectedResults, resultsNum);
    EXPECT_EQ(GMERR_OK, ret);
}

void CheckSysTableView(const char *expectedResults[], uint32_t resultsNum)
{
    char command[g_commandLen] = {};
    (void)snprintf_s(command, g_commandLen, g_commandLen - 1, "gmsysview record GM_SYS_VL -ns system");
    Status ret = StExecuteCommandWithMatch(command, expectedResults, resultsNum);
    EXPECT_EQ(GMERR_OK, ret);
}

void CheckRecordView(const char *vlName, const char *expectedResults[], uint32_t resultsNum)
{
    char command[g_commandLen] = {};
    (void)snprintf_s(command, g_commandLen, g_commandLen - 1, "gmsysview record %s", vlName);
    Status ret = StExecuteCommandWithMatch(command, expectedResults, resultsNum);
    EXPECT_EQ(GMERR_OK, ret);
}

void CheckCountView(const char *vlName, const char *expectedResults[], uint32_t resultsNum)
{
    char command[g_commandLen] = {};
    (void)snprintf_s(command, g_commandLen, g_commandLen - 1, "gmsysview count %s", vlName);
    Status ret = StExecuteCommandWithMatch(command, expectedResults, resultsNum);
    EXPECT_EQ(GMERR_OK, ret);
}

void DbReboot(GmcConnT **conn, GmcStmtT **stmt)
{
    EXPECT_EQ(GMERR_OK, GmcFlushData(*stmt, NULL, false));
    system("ipcrm -a");
    DestroyConnectionAndStmt(*conn, *stmt);
    // reboot server
    ShutDownDbServer();
    st_clt_uninit();
    StartDbServer((char *)"temp_gmserver_flush_on_demand.ini", "usocket:/run/verona/unix_emserver", false);
    // client init
    st_clt_init();
    // connect
    CreateSyncConnectionAndStmt(conn, stmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
}

/** 1-1、对账老化：dml + BeginCheck + dml + EndCheck(老化) **/
void AccCheckTest1_1(GmcConnT **conn, GmcStmtT **stmt, bool needReboot)
{
    Status ret = GmcCreateVertexLabel(*stmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 1、对账前：预置10条数据并校验记录数
    int32_t begin = 0;
    int32_t end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);
    CheckVertexCount(*stmt, g_nonPartitionLabel, originalCount);

    if (needReboot) {
        DbReboot(conn, stmt);
    }

    // 2、开启对账（全表对账）
    ret = GmcBeginCheck(*stmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，插入数据
    begin = 10, end = 14;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);

    // 校验 vertexLabel 系统表记录（证明开启对账会更新系统表）
    const char *vlSysTblResults1[] = {
        "\"VL_CHECK_STATUS\": 1,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    CheckSysTableView(vlSysTblResults1, sizeof(vlSysTblResults1) / sizeof(vlSysTblResults1[0]));

    // 结束对账
    ret = GmcEndCheck(*stmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // 3、校验对账信息和表记录数
    CheckAccountInfo(*stmt, g_nonPartitionLabel, 0xff, originalCount, 0);
    CheckVertexCount(*stmt, g_nonPartitionLabel, addedCount);
    ScanVertex(*stmt, begin, end);
    // 校验相关视图结果
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 5\n",
        "PHY_ITEM_NUM: 5\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 10\n",
        "REAL_AGED_CNT: 10\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 5\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   5                   |\n",
    };
    const char *recordViewResult[] = {
        "\"F0\": 10,\n",
        "\"F0\": 11,\n",
        "\"F0\": 12,\n",
        "\"F0\": 13,\n",
        "\"F0\": 14,\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(*stmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 1-2、对账老化：dml + BeginCheck + dml + EndCheck(老化) **/
void AccCheckTest1_2(GmcConnT **conn, GmcStmtT **stmt, bool needReboot)
{
    Status ret = GmcCreateVertexLabel(*stmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);
    CheckVertexCount(*stmt, g_nonPartitionLabel, originalCount);

    if (needReboot) {
        DbReboot(conn, stmt);
    }

    ret = GmcBeginCheck(*stmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // replace 20条数据，不会被老化（90~99 update、100~109 insert）
    begin = 90, end = 109;
    uint64_t replacedCount = end - begin + 1;
    ReplaceVertex(*stmt, begin, end, false, 0);
    // 新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);
    // 更新10条数据，不会被老化
    begin = 80, end = 89;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(*stmt, begin, end, false);
    // 删除10条数据
    begin = 0, end = 9;
    DeleteVertex(*stmt, begin, end, false);
    ret = GmcEndCheck(*stmt, g_nonPartitionLabel, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(*stmt, g_nonPartitionLabel, 0xff, 70, 0);  // 100-10(replace)-10(update)-10(delete)
    CheckVertexCount(*stmt, g_nonPartitionLabel, replacedCount + addedCount + updatedCount);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 40\n",
        "PHY_ITEM_NUM: 40\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 70\n",
        "REAL_AGED_CNT: 70\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 40\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   40                  |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 1\n",
        "index = 39, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(*stmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 2-1、对账恢复：dml + BeginCheck + dml + EndCheck(恢复) **/
void AccCheckTest2_1(GmcConnT **conn, GmcStmtT **stmt, bool needReboot)
{
    Status ret = GmcCreateVertexLabel(*stmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0;
    int32_t end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);
    CheckVertexCount(*stmt, g_nonPartitionLabel, originalCount);

    if (needReboot) {
        DbReboot(conn, stmt);
    }

    ret = GmcBeginCheck(*stmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = 10, end = 14;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);
    ret = GmcEndCheck(*stmt, g_nonPartitionLabel, 0xff, true);  // true 表示恢复
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(*stmt, g_nonPartitionLabel, 0xff, 0, originalCount);
    CheckVertexCount(*stmt, g_nonPartitionLabel, originalCount + addedCount);
    ScanVertex(*stmt, 0, end);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 15\n",
        "PHY_ITEM_NUM: 15\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 10\n",
        "REAL_RECOVERY_CNT: 10\n",
        "RECORD_CNT: 15\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   15                  |\n",
    };
    const char *recordViewResult[] = {
        "index = 14, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(*stmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 2-2、对账恢复：dml + BeginCheck + dml + EndCheck(恢复) **/
void AccCheckTest2_2(GmcConnT **conn, GmcStmtT **stmt, bool needReboot)
{
    Status ret = GmcCreateVertexLabel(*stmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, false, 0);
    CheckVertexCount(*stmt, g_nonPartitionLabel, originalCount);

    if (needReboot) {
        DbReboot(conn, stmt);
    }

    ret = GmcBeginCheck(*stmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // replace 20条数据，不会被老化
    begin = 90, end = 109;
    ReplaceVertex(*stmt, begin, end, false, 0);
    // 新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    InsertVertex(*stmt, begin, end, false, 0);
    // 更新10条数据，不会被老化
    begin = 80, end = 89;
    UpdateVertex(*stmt, begin, end, false);
    // 删除10条数据
    begin = 0, end = 9;
    DeleteVertex(*stmt, begin, end, false);
    ret = GmcEndCheck(*stmt, g_nonPartitionLabel, 0xff, true);  // true 表示开启恢复任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // 校验对账信息
    // 原来共有100条数据(0~99)
    // replace: 更新了10条(90~99)，并新插入了10条(100~109)；
    // insert: 新插入了10条(200~209)；
    // update: 更新了10条(80~89)
    // delete: 删除了10条(0~9)
    // 需要恢复（即刷新记录的老化版本号）的数据：100-10(90~99)-10(80~89)-10(0~9)
    CheckAccountInfo(*stmt, g_nonPartitionLabel, 0xff, 0, 70);
    CheckVertexCount(*stmt, g_nonPartitionLabel, originalCount + 10);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 110\n",
        "PHY_ITEM_NUM: 110\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 70\n",
        "REAL_RECOVERY_CNT: 70\n",
        "RECORD_CNT: 110\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   110                 |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 1\n",
        "index = 109, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(*stmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StPersistCheckAge, acc_check_1_1)
{
    AccCheckTest1_1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, false);
    AccCheckTest1_1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, true);
}

TEST_F(StPersistCheckAge, acc_check_1_2)
{
    AccCheckTest1_2(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, false);
    AccCheckTest1_2(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, true);
}

TEST_F(StPersistCheckAge, acc_check_2_1)
{
    AccCheckTest2_1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, false);
    AccCheckTest2_1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, true);
}

TEST_F(StPersistCheckAge, acc_check_2_2)
{
    AccCheckTest2_2(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, false);
    AccCheckTest2_2(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, true);
}

/** 3、后台 Truncate：dml + GmcDeleteAllFast **/
TEST_F(StPersistCheckAge, acc_check_3)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0;
    int32_t end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcDeleteAllFast(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);

    begin = end + 1, end = begin + 4;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, addedCount);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, addedCount);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 5\n",
        "PHY_ITEM_NUM: 5\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 10\n",
        "REAL_TRUNCATED_CNT: 10\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 5\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   5                   |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 1\n",
        "index = 4, check_version = 1\n",
    };

    sleep(2);

    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 4、对账老化 + 后台 Truncate：dml + BeginCheck + dml + GmcDeleteAllFast + dml + EndCheck(老化) **/
TEST_F(StPersistCheckAge, acc_check_4)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0;
    int32_t end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = begin + 4;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcDeleteAllFast(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);

    const char *vlSysTblResults1[] = {
        "\"VL_CHECK_STATUS\": 1,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": true,\n",
        "\"VL_CHECK_LAST_VERSION\": 2,\n",
        "\"VL_CHECKING_VERSION\": 1,\n",
        "\"VL_MIN_RECOVERY\": 2,\n",
        "\"VL_MAX_RECOVERY\": 2\n",
    };
    CheckSysTableView(vlSysTblResults1, sizeof(vlSysTblResults1) / sizeof(vlSysTblResults1[0]));

    begin = 20, end = 29;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10\n",
        "PHY_ITEM_NUM: 10\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 2\n",
        "OLD_CHECK_VERSION: 1\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 15\n",
        "REAL_TRUNCATED_CNT: 15\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 10\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 2,\n",
        "\"VL_CHECKING_VERSION\": 1,\n",
        "\"VL_MIN_RECOVERY\": 2,\n",
        "\"VL_MAX_RECOVERY\": 2\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   10                  |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 2\n",
        "index = 9, check_version = 2\n",
    };

    sleep(2);

    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 5、对账恢复 + 后台 Truncate：dml + BeginCheck + dml + GmcDeleteAllFast + dml + EndCheck(恢复) **/
TEST_F(StPersistCheckAge, acc_check_5)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0;
    int32_t end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = begin + 4;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcDeleteAllFast(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);

    const char *vlSysTblResults1[] = {
        "\"VL_CHECK_STATUS\": 1,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": true,\n",
        "\"VL_CHECK_LAST_VERSION\": 2,\n",
        "\"VL_CHECKING_VERSION\": 1,\n",
        "\"VL_MIN_RECOVERY\": 2,\n",
        "\"VL_MAX_RECOVERY\": 2\n",
    };
    CheckSysTableView(vlSysTblResults1, sizeof(vlSysTblResults1) / sizeof(vlSysTblResults1[0]));

    begin = 20, end = 29;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10\n",
        "PHY_ITEM_NUM: 10\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 2\n",
        "OLD_CHECK_VERSION: 1\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 15\n",
        "REAL_TRUNCATED_CNT: 15\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 10\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 2,\n",
        "\"VL_CHECKING_VERSION\": 1,\n",
        "\"VL_MIN_RECOVERY\": 2,\n",
        "\"VL_MAX_RECOVERY\": 2\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   10                  |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 2\n",
        "index = 9, check_version = 2\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 6、对账老化 + UPDATE_VERSION：dml + BeginCheck + dml + UPDATE_VERSION + dml + EndCheck(老化) **/
TEST_F(StPersistCheckAge, acc_check_6)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，更新记录的对账版本号
    begin = 0, end = 3;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertexCheckVersion(StPersistCheckAge::syncStmt, begin, end, false);
    begin = 20, end = 29;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, originalCount - updatedCount, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, updatedCount + addedCount);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 14\n",
        "PHY_ITEM_NUM: 14\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 6\n",
        "REAL_AGED_CNT: 6\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 14\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   14                  |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 1\n",
        "index = 13, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 7、对账恢复 + UPDATE_VERSION：dml + BeginCheck + dml + UPDATE_VERSION + dml + EndCheck(恢复) **/
TEST_F(StPersistCheckAge, acc_check_7)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，更新记录的对账版本号
    begin = 0, end = 3;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertexCheckVersion(StPersistCheckAge::syncStmt, begin, end, false);
    begin = 20, end = 29;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, originalCount - updatedCount);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount + addedCount);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 20\n",
        "PHY_ITEM_NUM: 20\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 6\n",
        "REAL_RECOVERY_CNT: 6\n",
        "RECORD_CNT: 20\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   20                  |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 1\n",
        "index = 19, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 分区表 1、对账老化：dml + BeginCheck + dml + EndCheck(老化) **/
void PartitionAccCheckTest1(GmcConnT **conn, GmcStmtT **stmt, bool needReboot)
{
    Status ret = GmcCreateVertexLabel(*stmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据（分区2：20条；分区3：30条；分区5：50条）
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(*stmt, 0, 19, true, 2);
    InsertVertex(*stmt, 20, 49, true, 3);
    InsertVertex(*stmt, 50, 99, true, 5);

    CheckVertexCount(*stmt, g_partitionLabel, originalCount);
    if (needReboot) {
        DbReboot(conn, stmt);
    }

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(*stmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间
    // replace 20条数据(更新10条，插入10条)
    begin = 90, end = 109;
    uint64_t replacedCount = end - begin + 1;
    ReplaceVertex(*stmt, begin, end, true, partitionId);
    // 新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(*stmt, begin, end, true, partitionId);
    // 更新10条数据，不会被老化
    begin = 80, end = 89;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(*stmt, begin, end, true);
    // 删除10条数据（删除的是分区2中的数据，因此不影响分区5的统计）
    begin = 0, end = 9;
    DeleteVertex(*stmt, begin, end, true);
    const char *vlSysTblResults1[] = {
        "\"VL_CHECK_STATUS\": 1,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    CheckSysTableView(vlSysTblResults1, sizeof(vlSysTblResults1) / sizeof(vlSysTblResults1[0]));

    // 结束对账
    ret = GmcEndCheck(*stmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(*stmt, g_partitionLabel, partitionId, 30, 0);  // 50-10(replace)-10(update)
    uint64_t remainingCount = 10 + 30;                              // 分区2剩下的10条，分区3剩下的30条
    CheckVertexCount(*stmt, g_partitionLabel, replacedCount + addedCount + updatedCount + remainingCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 80\n",
        "PHY_ITEM_NUM: 80\n",
    };
    const char *checkInfoViewResults[] = {
        "PARTITION_ID: 2\n",
        "RECORD_CNT: 10\n",
        "PARTITION_ID: 3\n",
        "RECORD_CNT: 30\n",
        "PARTITION_ID: 4\n",
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 30\n",
        "REAL_AGED_CNT: 30\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 40\n",
        "PARTITION_ID: 6\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   80                  |\n",
    };
    // todo 分区表记录的 check_version 有问题
    const char *recordViewResult[] = {
        "index = 0, check_version = 0\n",
        "\"F0\": 10,\n",
        "index = 79, check_version = 1\n",
        "\"F0\": 209,\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_partitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_partitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(*stmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 分区表2、对账恢复：dml + BeginCheck + dml + EndCheck(恢复) **/
void PartitionAccCheckTest2(GmcConnT **conn, GmcStmtT **stmt, bool needReboot)
{
    Status ret = GmcCreateVertexLabel(*stmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据（分区2：20条；分区3：30条；分区5：50条）
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(*stmt, 0, 19, true, 2);
    InsertVertex(*stmt, 20, 49, true, 3);
    InsertVertex(*stmt, 50, 99, true, 5);
    CheckVertexCount(*stmt, g_partitionLabel, originalCount);
    if (needReboot) {
        DbReboot(conn, stmt);
    }

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(*stmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // replace 20条数据(更新10条，插入10条)
    begin = 90, end = 109;
    ReplaceVertex(*stmt, begin, end, true, partitionId);
    // 新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    InsertVertex(*stmt, begin, end, true, partitionId);
    // 更新10条数据，不会被老化
    begin = 80, end = 89;
    UpdateVertex(*stmt, begin, end, true);
    // 删除10条数据（删除的是分区2中的数据，因此不影响分区5的统计）
    begin = 0, end = 9;
    DeleteVertex(*stmt, begin, end, true);

    // 结束对账
    ret = GmcEndCheck(*stmt, g_partitionLabel, partitionId, true);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // 校验对账信息
    // 50-10-10
    CheckAccountInfo(*stmt, g_partitionLabel, partitionId, 0, 30);
    // 对账后：查询表中的记录数
    CheckVertexCount(*stmt, g_partitionLabel, originalCount + 10);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 110\n",
        "PHY_ITEM_NUM: 110\n",
    };
    const char *checkInfoViewResults[] = {
        "PARTITION_ID: 2\n",
        "RECORD_CNT: 10\n",
        "PARTITION_ID: 3\n",
        "RECORD_CNT: 30\n",
        "PARTITION_ID: 4\n",
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 30\n",
        "REAL_RECOVERY_CNT: 30\n",
        "RECORD_CNT: 70\n",
        "PARTITION_ID: 6\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   110                 |\n",
    };
    const char *recordViewResult[] = {
        "index = 0, check_version = 0\n",
        "index = 109, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_partitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_partitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(*stmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StPersistCheckAge, partition_acc_check_1)
{
    PartitionAccCheckTest1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, false);
    PartitionAccCheckTest1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, true);
}

TEST_F(StPersistCheckAge, partition_acc_check_2)
{
    PartitionAccCheckTest2(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, false);
    PartitionAccCheckTest1(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt, true);
}

/** 分区表4、对账老化 + 后台 Truncate：dml + BeginCheck + dml + GmcDeleteAllFast + dml + EndCheck(老化) **/
TEST_F(StPersistCheckAge, partition_acc_check_4)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, 0, 19, true, 2);
    InsertVertex(StPersistCheckAge::syncStmt, 20, 49, true, 3);
    InsertVertex(StPersistCheckAge::syncStmt, 50, 99, true, 5);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, originalCount);

    uint8_t partitionId = 5;
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = begin + 4;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, true, 6);
    ret = GmcDeleteAllFast(StPersistCheckAge::syncStmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);

    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, true, 4);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10\n",
        "PHY_ITEM_NUM: 10\n",
    };
    const char *checkInfoViewResults[] = {
        "PARTITION_ID: 2\n",
        "SHOULD_TRUNCATE_CNT: 20\n",
        "REAL_TRUNCATED_CNT: 20\n",
        "RECORD_CNT: 0\n",
        "PARTITION_ID: 3\n",
        "SHOULD_TRUNCATE_CNT: 30\n",
        "REAL_TRUNCATED_CNT: 30\n",
        "RECORD_CNT: 0\n",
        "PARTITION_ID: 4\n",
        "RECORD_CNT: 10\n",
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 2\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 50\n",
        "REAL_TRUNCATED_CNT: 50\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 0\n",
        "PARTITION_ID: 6\n",
        "SHOULD_TRUNCATE_CNT: 5\n",
        "REAL_TRUNCATED_CNT: 5\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 2,\n",
        "\"VL_CHECKING_VERSION\": 1,\n",
        "\"VL_MIN_RECOVERY\": 2,\n",
        "\"VL_MAX_RECOVERY\": 2\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   10                  |\n",
    };
    // todo
    const char *recordViewResult[] = {
        "index = 9, check_version = 1\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_partitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_partitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 分区表7、对账恢复 + UPDATE_VERSION：dml + BeginCheck + dml + UPDATE_VERSION + dml + EndCheck(恢复) **/
TEST_F(StPersistCheckAge, partition_acc_check_7)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, 0, 19, true, 2);
    InsertVertex(StPersistCheckAge::syncStmt, 20, 49, true, 3);
    InsertVertex(StPersistCheckAge::syncStmt, 50, 99, true, 5);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, originalCount);

    uint8_t partitionId = 5;
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    begin = 50, end = 59;
    uint64_t updatedCount2 = end - begin + 1;
    UpdateVertexCheckVersion(StPersistCheckAge::syncStmt, begin, end, true);

    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, true, 4);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, 40, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, 50 + updatedCount2 + addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 70\n",
        "PHY_ITEM_NUM: 70\n",
    };
    const char *checkInfoViewResults[] = {
        "PARTITION_ID: 2\n",
        "RECORD_CNT: 20\n",
        "PARTITION_ID: 3\n",
        "RECORD_CNT: 30\n",
        "PARTITION_ID: 4\n",
        "RECORD_CNT: 10\n",
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "SHOULD_AGED_CNT: 40\n",
        "REAL_AGED_CNT: 40\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 10\n",
        "PARTITION_ID: 6\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   70                  |\n",
    };
    // todo
    const char *recordViewResult[] = {
        "index = 9, check_version = 0\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_partitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_partitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

// 验证场景：反复开启、结束对账，操作次数超过255次。看护用例DML_088_021发现的问题
TEST_F(StPersistCheckAge, check_multi_check)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1, changedCount = 0;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    int cnt = 0, repeatTimes = 300;
    while (cnt < repeatTimes) {
        // 开启对账（全表对账）
        ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
        EXPECT_EQ(GMERR_OK, ret);
        // 对账期间，执行直连写replace操作。更新50条数据
        begin = 0, end = 49;
        changedCount = end - begin + 1;
        ReplaceVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
        // 结束对账
        ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }

    sleep(2);

    // 校验对账信息
    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, originalCount - changedCount, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, changedCount);

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

typedef void *(*ThreadFunc)(void *);
static void ThreadsCreateAndJoin(ThreadFunc *func, uint32_t funcNum)
{
    pthread_t threads[funcNum];
    for (uint32_t i = 0; i < funcNum; i++) {
        EXPECT_EQ(0, pthread_create(&threads[i], NULL, func[i], NULL));
    }

    for (uint32_t i = 0; i < funcNum; i++) {
        EXPECT_EQ(0, pthread_join(threads[i], NULL));
    }
}

void *ReplaceVertexThreadFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    time_t time1, time2;
    time(&time1);
    time(&time2);
    while (difftime(time2, time1) <= 5.0) {
        ReplaceVertex(stmt, 0, 10000, false, 0);
        time(&time2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *UpdateVertexThreadFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    time_t time1, time2;
    time(&time1);
    time(&time2);
    while (difftime(time2, time1) <= 5.0) {
        UpdateVertex(stmt, 0, 10000, false);
        time(&time2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *DeleteVertexThreadFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    time_t time1, time2;
    time(&time1);
    time(&time2);
    while (difftime(time2, time1) <= 5.0) {
        for (uint32_t i = 0; i < 3; i++) {
            DeleteVertex(stmt, 1000 * (i + 1), 2000 * (i + 1), false);
        }
        time(&time2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *BackTruncateThreadFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    time_t time1, time2;
    time(&time1);
    time(&time2);
    while (difftime(time2, time1) <= 5.0) {
        Status ret = GmcDeleteAllFast(stmt, g_nonPartitionLabel);
        EXPECT_EQ(ret, GMERR_OK);
        sleep(0.5);
        time(&time2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *AccCheckThreadFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    time_t time1, time2;
    time(&time1);
    time(&time2);
    while (difftime(time2, time1) <= 5.0) {
        Status ret = GmcBeginCheck(stmt, g_nonPartitionLabel, 0xff);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(0.5);
        ret = GmcEndCheck(stmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
        EXPECT_EQ(GMERR_OK, ret);
        time(&time2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

// dml + 对账 + 后台 truncate 并发用例
TEST_F(StPersistCheckAge, acc_check_concurrency)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ThreadFunc func[] = {ReplaceVertexThreadFunc, UpdateVertexThreadFunc, BackTruncateThreadFunc,
        DeleteVertexThreadFunc, AccCheckThreadFunc};
    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    sleep(3);
    char cmdOutput[64] = {0};
    const char *filter = "VERTEX_LABEL_NAME=\'non_partition_label\' | grep -A 11 \'PARTITION_ID\'";
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "RECORD_CNT", filter, cmdOutput, 64);
    uint32_t recordCnt = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter2 = "LABEL_NAME=\'non_partition_label\' | grep -A 2 \'CUR_ITEM_NUM\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM", filter2, cmdOutput, 64);
    uint32_t curItemNum = atoi(cmdOutput);
    EXPECT_EQ(curItemNum, recordCnt);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PHY_ITEM_NUM", filter2, cmdOutput, 64);
    uint32_t phyItemNum = atoi(cmdOutput);
    EXPECT_EQ(phyItemNum, recordCnt);

    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, recordCnt);

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 全表对账1、对账中重启 DB，数据保留： **/
TEST_F(StPersistCheckAge, check_and_reboot_1)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 1、对账前：预置 10000 条数据
    int32_t begin = 0;
    int32_t end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = end + 10;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);

    sleep(2);

    // 重启
    DbReboot(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount + addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10010\n",
        "PHY_ITEM_NUM: 10010\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 10010\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": false,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 0,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   10010               |\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // todo 补充视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 全表对账2、对账老化，重启 DB，对账开启前的数据被删除，对账中的数据保留 **/
TEST_F(StPersistCheckAge, check_and_reboot_2)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 1、对账前：预置 10000 条数据
    int32_t begin = 0;
    int32_t end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = end + 100;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    DbReboot(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 100\n",
        "PHY_ITEM_NUM: 100\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 100\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": false,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 0,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   100                 |\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // todo 补充视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 全表对账3、对账恢复，重启 DB，所有数据均保留 **/
TEST_F(StPersistCheckAge, check_and_reboot_3)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 1、对账前：预置 10000 条数据
    int32_t begin = 0;
    int32_t end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = end + 100;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // 重启
    DbReboot(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount + addedCount);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10100\n",
        "PHY_ITEM_NUM: 10100\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 10100\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": false,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 0,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   10100               |\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // todo 补充视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 分区对账1、对账中重启 DB，数据保留： **/
TEST_F(StPersistCheckAge, partition_check_and_reboot_1)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10000条数据（分区2：2000条；分区3：3000条；分区5：5000条）
    int32_t begin = 0, end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, 0, 1999, true, 2);
    InsertVertex(StPersistCheckAge::syncStmt, 2000, 4999, true, 3);
    InsertVertex(StPersistCheckAge::syncStmt, 5000, 9999, true, 5);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, originalCount);

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    UpdateVertex(StPersistCheckAge::syncStmt, 6000, 6999, true);
    InsertVertex(StPersistCheckAge::syncStmt, 10000, 10099, true, 5);

    // 重启
    DbReboot(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, originalCount + 100);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10100\n",
        "PHY_ITEM_NUM: 10100\n",
    };
    const char *checkInfoPartition2ViewResults[] = {
        "PARTITION_ID: 2\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 2000\n",
    };
    const char *checkInfoPartition3ViewResults[] = {
        "PARTITION_ID: 3\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 3000\n",
    };
    const char *checkInfoPartition5ViewResults[] = {
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 5100\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": false,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 0,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   10100               |\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition2ViewResults) / sizeof(checkInfoPartition2ViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition3ViewResults) / sizeof(checkInfoPartition3ViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition5ViewResults) / sizeof(checkInfoPartition5ViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // todo 补充视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 分区对账2、对账老化，重启 DB，对账开启前的数据被删除，对账中的数据保留 **/
TEST_F(StPersistCheckAge, partition_check_and_reboot_2)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10000条数据（分区2：2000条；分区3：3000条；分区5：5000条）
    int32_t begin = 0, end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, 0, 1999, true, 2);
    InsertVertex(StPersistCheckAge::syncStmt, 2000, 4999, true, 3);
    InsertVertex(StPersistCheckAge::syncStmt, 5000, 9999, true, 5);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, originalCount);

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    UpdateVertex(StPersistCheckAge::syncStmt, 6000, 6999, true);
    InsertVertex(StPersistCheckAge::syncStmt, 10000, 10099, true, 5);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // 重启
    DbReboot(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, 6100);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 6100\n",
        "PHY_ITEM_NUM: 6100\n",
    };
    const char *checkInfoPartition2ViewResults[] = {
        "PARTITION_ID: 2\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 2000\n",
    };
    const char *checkInfoPartition3ViewResults[] = {
        "PARTITION_ID: 3\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 3000\n",
    };
    const char *checkInfoPartition5ViewResults[] = {
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 1100\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": false,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 0,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   6100                |\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition2ViewResults) / sizeof(checkInfoPartition2ViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition3ViewResults) / sizeof(checkInfoPartition3ViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition5ViewResults) / sizeof(checkInfoPartition5ViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // todo 补充视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

/** 分区对账3、对账恢复，重启 DB，所有数据均保留 **/
TEST_F(StPersistCheckAge, partition_check_and_reboot_3)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_partitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10000条数据（分区2：2000条；分区3：3000条；分区5：5000条）
    int32_t begin = 0, end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, 0, 1999, true, 2);
    InsertVertex(StPersistCheckAge::syncStmt, 2000, 4999, true, 3);
    InsertVertex(StPersistCheckAge::syncStmt, 5000, 9999, true, 5);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, originalCount);

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    UpdateVertex(StPersistCheckAge::syncStmt, 6000, 6999, true);
    InsertVertex(StPersistCheckAge::syncStmt, 10000, 10099, true, 5);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, true);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // 重启
    DbReboot(&StPersistCheckAge::syncConn, &StPersistCheckAge::syncStmt);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, 0, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_partitionLabel, 10100);

    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 10100\n",
        "PHY_ITEM_NUM: 10100\n",
    };
    const char *checkInfoPartition2ViewResults[] = {
        "PARTITION_ID: 2\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 2000\n",
    };
    const char *checkInfoPartition3ViewResults[] = {
        "PARTITION_ID: 3\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 3000\n",
    };
    const char *checkInfoPartition5ViewResults[] = {
        "PARTITION_ID: 5\n",
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 0\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 0\n",
        "REAL_AGED_CNT: 0\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 5100\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": false,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 0,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 0,\n",
        "\"VL_MAX_RECOVERY\": 0\n",
    };
    const char *countViewResult[] = {
        "|1       |   partition_label                 |   10100               |\n",
    };
    CheckHeapInfoView(
        g_partitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition2ViewResults) / sizeof(checkInfoPartition2ViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition3ViewResults) / sizeof(checkInfoPartition3ViewResults[0]));
    CheckCheckInfoView(g_partitionLabel, checkInfoPartition5ViewResults,
        sizeof(checkInfoPartition5ViewResults) / sizeof(checkInfoPartition5ViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_partitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_partitionLabel, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // todo 补充视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_partitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StPersistCheckAge, check_abnormal_continuously)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 1、对账前：预置10条数据并校验记录数
    int32_t begin = 0;
    int32_t end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    // 2、开启对账（全表对账）
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，插入数据
    begin = 10, end = 14;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    // 结束对账
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 连续对账恢复
    for (uint32_t i = 0; i < 10; i++) {
        ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(2);

    // 3、校验对账信息和表记录数
    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, originalCount, 50);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, addedCount);
    // 校验相关视图结果
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 5\n",
        "PHY_ITEM_NUM: 5\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 11\n",
        "OLD_CHECK_VERSION: 10\n",
        "SHOULD_AGED_CNT: 10\n",
        "REAL_AGED_CNT: 10\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 50\n",
        "REAL_RECOVERY_CNT: 50\n",
        "RECORD_CNT: 5\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 11,\n",
        "\"VL_CHECKING_VERSION\": 10,\n",
        "\"VL_MIN_RECOVERY\": 11,\n",
        "\"VL_MAX_RECOVERY\": 11\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   5                   |\n",
    };
    const char *recordViewResult[] = {
        "\"F0\": 10,\n",
        "\"F0\": 11,\n",
        "\"F0\": 12,\n",
        "\"F0\": 13,\n",
        "\"F0\": 14,\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));
    CheckRecordView(g_nonPartitionLabel, recordViewResult, sizeof(recordViewResult) / sizeof(recordViewResult[0]));

    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StPersistCheckAge, check_age_and_replace)
{
    Status ret = GmcCreateVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionSchema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 1、对账前：预置 10000 条数据
    int32_t begin = 0;
    int32_t end = 9999;
    uint64_t originalCount = end - begin + 1;
    int32_t replaceBegin = begin + originalCount / 2;
    int32_t replaceEnd = end;
    uint64_t replaceCount = replaceEnd - replaceBegin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, originalCount);

    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    begin = end + 1, end = end + 10;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(StPersistCheckAge::syncStmt, begin, end, false, 0);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    sleep(2);

    // replace 和要老化数据相同的部分数据
    ReplaceVertex(StPersistCheckAge::syncStmt, replaceBegin, replaceEnd, false, 0);

    CheckAccountInfo(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, originalCount, 0);
    CheckVertexCount(StPersistCheckAge::syncStmt, g_nonPartitionLabel, replaceCount + addedCount);
    const char *heapInfoViewResults[] = {
        "CUR_ITEM_NUM: 5010\n",
        "PHY_ITEM_NUM: 5010\n",
    };
    const char *checkInfoViewResults[] = {
        "CHECK_STATUS: 0\n",
        "CHECK_VERSION: 1\n",
        "OLD_CHECK_VERSION: 0\n",
        "SHOULD_AGED_CNT: 10000\n",
        "REAL_AGED_CNT: 10000\n",
        "SHOULD_TRUNCATE_CNT: 0\n",
        "REAL_TRUNCATED_CNT: 0\n",
        "SHOULD_RECOVERY_CNT: 0\n",
        "REAL_RECOVERY_CNT: 0\n",
        "RECORD_CNT: 5010\n",
    };
    const char *vlSysTblResults2[] = {
        "\"VL_CHECK_STATUS\": 0,\n",
        "\"VL_HAS_BEEN_CHECKED\": true,\n",
        "\"VL_TRUNCATED_IN_CHECKING\": false,\n",
        "\"VL_CHECK_LAST_VERSION\": 1,\n",
        "\"VL_CHECKING_VERSION\": 0,\n",
        "\"VL_MIN_RECOVERY\": 1,\n",
        "\"VL_MAX_RECOVERY\": 1\n",
    };
    const char *countViewResult[] = {
        "|1       |   non_partition_label             |   5010                |\n",
    };
    CheckHeapInfoView(
        g_nonPartitionLabel, heapInfoViewResults, sizeof(heapInfoViewResults) / sizeof(heapInfoViewResults[0]));
    CheckCheckInfoView(
        g_nonPartitionLabel, checkInfoViewResults, sizeof(checkInfoViewResults) / sizeof(checkInfoViewResults[0]));
    CheckSysTableView(vlSysTblResults2, sizeof(vlSysTblResults2) / sizeof(vlSysTblResults2[0]));
    CheckCountView(g_nonPartitionLabel, countViewResult, sizeof(countViewResult) / sizeof(countViewResult[0]));

    // 再次开启对账
    ret = GmcBeginCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(StPersistCheckAge::syncStmt, g_nonPartitionLabel, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);
    // replace 和要恢复数据相同的部分数据
    ReplaceVertex(StPersistCheckAge::syncStmt, replaceBegin, replaceEnd, false, 0);

    // todo 视图校验
    ret = GmcDropVertexLabel(StPersistCheckAge::syncStmt, g_nonPartitionLabel);
    EXPECT_EQ(GMERR_OK, ret);
}

// 交互式事务校验最大记录数，在提交的时候报错而不是在Execute的时候报错
TEST_F(StPersistCheckAge, max_record_count)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
             [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
                    "keys":
                    [
                        {
                            "node":"XT60",
                            "name":"XT60_K0",
                            "fields":["F0"],
                            "index":{"type":"primary"},
                            "constraints":{"unique":true}
                        }
                    ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(StPersistCheckAge::syncStmt, labelJson, configJson));

    ASSERT_EQ(GMERR_OK, GmcTransStart(StPersistCheckAge::syncConn, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(StPersistCheckAge::syncStmt, labelName, GMC_OPERATION_REPLACE));
    int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(StPersistCheckAge::syncStmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(StPersistCheckAge::syncStmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(StPersistCheckAge::syncStmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(StPersistCheckAge::syncStmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(StPersistCheckAge::syncStmt));
    f0 = 100000;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(StPersistCheckAge::syncStmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcExecute(StPersistCheckAge::syncStmt));
    ASSERT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, GmcTransCommit(StPersistCheckAge::syncConn));
    // DTS2023042101462修改后，commit失败不abort，导致这里删表会返回GMERR_RECORD_COUNT_LIMIT_EXCEEDED
    ASSERT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, GmcDropVertexLabel(StPersistCheckAge::syncStmt, labelName));
    ASSERT_EQ(GMERR_OK, GmcTransRollBack(StPersistCheckAge::syncConn));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(StPersistCheckAge::syncStmt, labelName));
}
