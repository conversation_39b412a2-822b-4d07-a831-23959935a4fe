{"op": "merge", "ds": [{"name": "candidate", "huawei-snmp": [{"E0": [null], "U0": "union test value0", "huawei-snmp:snmp": {"usm-users": {"huawei-snmp:snmp::usm-users::usm-user": [{"user-name": "user1", "E1": [null], "U1": 10, "auth-protocol": "sha2-256", "auth-key": "%+%##!!!!!!!!!\"!!!!$!!!!*!!!!S6TpP\\N8Z)Jc{|$P3WdKC|W&OMPW~*U`PY0!!!!!2jp5!!!!!!9!!!!&{3_3tBx1%khW>MO@h+38C+'8XA4{!!!!!!!!!!!%+%#", "priv-key": "common"}]}}}]}]}