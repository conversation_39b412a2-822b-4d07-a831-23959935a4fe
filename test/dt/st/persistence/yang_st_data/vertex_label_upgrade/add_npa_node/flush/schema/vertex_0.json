[{"name": "yang", "alias": "yang", "type": "container", "rfc7951_invisible": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "default": 1, "clause": [{"type": "when", "formula": "current() > 1"}]}, {"name": "F1", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "current() > 1"}]}, {"name": "F2", "type": "uint32"}, {"name": "C0", "type": "container", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32", "default": 1, "clause": [{"type": "when", "formula": "current() > 1"}]}]}, {"name": "C1", "type": "container", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "identity", "enumerate_identity": "IdenTest", "enumerate": [{"name": "a", "value": -55555, "derived-paths": [{"derived-path": "a"}]}, {"name": "b", "value": 0, "derived-paths": [{"derived-path": "a/b"}]}, {"name": "d", "value": 1, "derived-paths": [{"derived-path": "d"}]}, {"name": "e", "value": 15, "derived-paths": [{"derived-path": "e"}]}]}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "list1", "type": "list", "min-elements": 1, "max-elements": 5, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "leaflist1", "type": "leaf-list", "clause": [{"type": "when", "formula": "F0 = 2"}], "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "default": [1, 2, 3], "nullable": false}], "keys": [{"name": "k0", "index": {"type": "primary"}, "fields": [":pid", "F0"], "constraints": {"unique": true}}]}, {"name": "leaflist2", "type": "leaf-list", "clause": [{"type": "when", "formula": "F0 = 2"}], "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "default": [1, 2, 3], "nullable": false}], "keys": [{"name": "k0", "index": {"type": "primary"}, "fields": [":pid", "F0"], "constraints": {"unique": true}}]}]