{"interfaces": {"if:interface.1": [{"name": "ethernetCsmacd.0.9.0", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.9.0"}, {"port-layer-if": "port.0.9.1"}, {"port-layer-if": "port.0.9.2"}, {"port-layer-if": "port.0.9.3"}]}, {"name": "ethernetCsmacd.0.9.1", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.9.1"}, {"port-layer-if": "port.0.9.2"}, {"port-layer-if": "port.0.9.3"}, {"port-layer-if": "port.0.9.4"}, {"port-layer-if": "port.0.9.5"}]}, {"name": "ethernetCsmacd.0.9.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.9.2"}]}, {"name": "ethernetCsmacd.0.9.3", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}}, {"name": "ethernetCsmacd.0.10.0", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "ethernetCsmacd.0.10.0"}, {"port-layer-if": "ethernetCsmacd.0.10.1"}]}, {"name": "ethernetCsmacd.0.10.1", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.10.0"}]}, {"name": "ethernetCsmacd.0.10.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.10.0"}]}, {"name": "ethernetCsmacd.0.10.3", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.10.0"}, {"port-layer-if": "port.0.10.1"}, {"port-layer-if": "port.0.10.2"}]}]}}