{"op": "merge", "interfaces": {"op": "merge", "if:interface.1": [{"name": "ethernetCsmacd.0.10.5", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.10.0"}]}, {"name": "ethernetCsmacd.0.10.6", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "ipv4": {"ip:address.1": [{"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}, {"ip": "***************"}]}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.10.0"}]}]}}