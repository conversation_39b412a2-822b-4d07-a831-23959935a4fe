[{"name": "root", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "root_F0", "type": "uint32"}, {"name": "root_F1", "type": "uint32"}, {"name": "root_C0", "type": "container", "fields": [{"name": "root_C0_F0", "type": "uint32"}, {"name": "root_C0_F1", "type": "uint32"}, {"name": "root_C0_C0", "type": "container", "fields": [{"name": "root_C0_C0_F0", "type": "uint32"}]}, {"name": "root_C0_C1", "type": "container", "fields": []}, {"name": "root_C0_C2", "type": "container", "fields": []}]}, {"name": "root_C1", "type": "container", "fields": [{"name": "root_C1_F0", "type": "uint32"}, {"name": "root_C1_F1", "type": "uint32"}, {"name": "root_C1_C0", "type": "container", "fields": [{"name": "root_C1_C0_F0", "type": "uint32"}]}, {"name": "root_C1_C1", "type": "container", "fields": [{"name": "root_C1_C1_F0", "type": "uint32"}]}]}, {"name": "root_Choice", "type": "choice", "fields": [{"name": "root_Choice_Case", "type": "case", "fields": [{"name": "root_Choice_Case_F0", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root_L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "root_L0_F0", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "root_L0_F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]