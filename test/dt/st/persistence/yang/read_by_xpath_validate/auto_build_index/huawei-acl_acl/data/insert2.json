{"type": "recovery_user", "groups": {"huawei-acl:acl::groups::group": [{"identity": "recovery_test001", "type": "recovery_user", "step": 1, "description": "recovery_test when", "rule-advances": {"huawei-acl:acl::groups::group::rule-advances::rule-advance": [{"name": "recovery_test001-01", "action": "recovery_validate data", "active-status": "recovery_on", "protocol-type": {"single": {"protocol": 1}}, "ttl-expired": false, "icmp-type": 1, "icmp-code": 1}, {"name": "recovery_test001-02", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 6}}, "ttl-expired": false, "tcp-flag": {"mask": {"tcp-flag-value": 2}}}, {"name": "recovery_test001-03", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 2}}, "ttl-expired": false, "igmp-type": 3}]}}, {"identity": "recovery_test002", "type": "recovery_user", "step": 2, "description": "recovery_test when", "rule-advances": {"huawei-acl:acl::groups::group::rule-advances::rule-advance": [{"name": "recovery_test002-01", "action": "recovery_validate data", "active-status": "recovery_on", "protocol-type": {"single": {"protocol": 1}}, "ttl-expired": false, "tcp-flag": {"mask": {"tcp-flag-value": 2}}, "igmp-type": 1, "icmp-code": 1}, {"name": "recovery_test002-02", "action": "recovery_validate data", "active-status": "recovery_on", "protocol-type": {"single": {"protocol": 6}}, "ttl-expired": false, "igmp-type": 2, "icmp-type": 2, "icmp-code": 2}, {"name": "recovery_test002-03", "action": "recovery_validate data", "active-status": "recovery_on", "protocol-type": {"single": {"protocol": 2}}, "ttl-expired": false, "tcp-flag": {"mask": {"tcp-flag-value": 3}}, "igmp-type": 3, "icmp-type": 2, "icmp-code": 2}]}}]}}