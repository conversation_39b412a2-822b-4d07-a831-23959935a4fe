{"groups": {"huawei-acl:acl::groups::group": [{"identity": "test001", "type": "user", "step": 1, "description": "test when", "rule-advances": {"huawei-acl:acl::groups::group::rule-advances::rule-advance": [{"name": "test001-01", "action": "validate data", "active-status": "on", "ttl-expired": false, "icmp-type": 1, "icmp-code": 1, "protocol-type": {"single": {"protocol": 1}}}, {"name": "test001-02", "@id": 6, "action": "validate data", "active-status": "on", "ttl-expired": false, "protocol-type": {"single": {"protocol": 6}}}, {"name": "test001-03", "action": "validate data", "active-status": "on", "ttl-expired": false, "igmp-type": 3, "protocol-type": {"single": {"protocol": 2}}}]}}, {"identity": "test002", "type": "user", "step": 2, "description": "test when", "rule-advances": {"huawei-acl:acl::groups::group::rule-advances::rule-advance": [{"name": "test002-01", "action": "validate data", "active-status": "on", "ttl-expired": false, "protocol-type": {"single": {"protocol": 1}}}, {"name": "test002-02", "@id": 6, "action": "validate data", "active-status": "on", "ttl-expired": false, "protocol-type": {"single": {"protocol": 6}}}, {"name": "test002-03", "action": "validate data", "active-status": "on", "ttl-expired": false, "igmp-type": 3, "protocol-type": {"single": {"protocol": 2}}}]}}]}}