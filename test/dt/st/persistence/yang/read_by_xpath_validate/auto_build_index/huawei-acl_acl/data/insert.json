{"type": "user", "groups": {"huawei-acl:acl::groups::group": [{"identity": "test001", "type": "user", "step": 1, "description": "test when", "rule-advances": {"huawei-acl:acl::groups::group::rule-advances::rule-advance": [{"name": "test001-01", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 1}}, "ttl-expired": false, "icmp-type": 1, "icmp-code": 1}, {"name": "test001-02", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 6}}, "ttl-expired": false, "tcp-flag": {"mask": {"tcp-flag-value": 2}}}, {"name": "test001-03", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 2}}, "ttl-expired": false, "igmp-type": 3}]}}, {"identity": "test002", "type": "user", "step": 2, "description": "test when", "rule-advances": {"huawei-acl:acl::groups::group::rule-advances::rule-advance": [{"name": "test002-01", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 1}}, "ttl-expired": false, "tcp-flag": {"mask": {"tcp-flag-value": 2}}, "igmp-type": 1, "icmp-code": 1}, {"name": "test002-02", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 6}}, "ttl-expired": false, "igmp-type": 2, "icmp-type": 2, "icmp-code": 2}, {"name": "test002-03", "action": "validate data", "active-status": "on", "protocol-type": {"single": {"protocol": 2}}, "ttl-expired": false, "tcp-flag": {"mask": {"tcp-flag-value": 3}}, "igmp-type": 3, "icmp-type": 2, "icmp-code": 2}]}}]}}