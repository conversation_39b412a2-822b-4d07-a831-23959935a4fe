yang持久化下的模板用例
通过在yangtemplate目录添加文件夹的方式加入用例，参考005_dml下yang_st_template_service.cc模板化用例执行
文件夹名：表示根节点名字
schema目录：点和边元数据定义，用于建表，并进行模型校验
    vertex_*.json:点表定义json
    edge_*.json:边表定义json
    ValidateRes.json:模型校验结果

trans_*：事务操作目录，包含DML批操作，subtree查询，数据校验

exeCmd.json:执行流程json:Trans是一个事务list，每个元素代表一个事务内的操作，可以添加多个，exeCmd表示执行具体命令和操作步骤
该json先执行事务tx_begin，开始，接着进行insert操作，subtree_1查询，validate校验，最后tx_commit提交事务
{
    "Trans": [
        {
            "index": 0,
            "exeCmd": "tx_begin,insert,subtree_1,validate,tx_commit",
            "insert": "trans_0/insert",
            "subtree_1": "trans_0/subtree_1",
            "validate": "trans_0/validate"
        }
    ]
}

