[{"name": "root:leaflist", "type": "container", "presence": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root2", "type": "container", "presence": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "clause": [{"type": "must", "formula": "/root:leaflist"}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "LF1", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]