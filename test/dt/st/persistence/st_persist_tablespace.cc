/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#include "storage_st_common.h"
#include "st_persistence_common.h"
#include "gmc_tablespace.h"

class StPersistTableSpace : public StStorage {
public:
    StPersistTableSpace()
    {}
    virtual ~StPersistTableSpace()
    {}
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    virtual void SetUp()
    {
        ShutdownServer(NULL);
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
        system("rm -rf temp_gmserver_autoinc.ini");
        // 配置和autoinc保持一致，都属于计算场景
        StModifyConfig("gmserver_autoinc.ini", "temp_gmserver_autoinc.ini",
            "\"featureNames=MEMDATA,DURABLEMEMDATA,FASTPATH,TRM,PERSISTENCE\"");
        StartDbServer((char *)"temp_gmserver_autoinc.ini", "usocket:/run/verona/unix_emserver", true);
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#endif
        st_clt_init();
        CreateSyncConnectionAndStmt(&conn, &stmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }
    virtual void TearDown()
    {
        ShutdownServer(conn);
        DestroyConnectionAndStmt(conn, stmt);
        st_clt_uninit();
        system("ipcrm -a");
        system("rm -rf temp_gmserver_autoinc.ini");
    }

protected:
    GmcConnT *conn;
    GmcStmtT *stmt;
};

TEST_F(StPersistTableSpace, tsp1)
{
    const char *tspName = "tspPersist01";

    GmcPersistentTspCfgT tspCfg = {
        .tablespaceName = tspName,
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
        .isVolatile = false,
        .customFilePath = {0},
    };
    Status ret = GmcCreatePersistentTableSpace(stmt, &tspCfg);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除tablespace
    EXPECT_EQ(GMERR_OK, GmcDropTablespace(stmt, tspCfg.tablespaceName));
}
