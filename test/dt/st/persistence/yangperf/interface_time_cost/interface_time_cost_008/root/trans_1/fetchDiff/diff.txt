root:update[(p<PERSON><PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
root.ietf-interfaces:interfaces:update
ietf-interfaces:interfaces.if:interface.1:create[(pri<PERSON><PERSON>(PID:1,name:v-ani.0.13.1.2), preKey(PID:1,name:port.0.13.1.2.eth.1)),(NULL)]
if:interface.1.ID:create(978)
if:interface.1.type:create(bbf-xpon-if-type:v-ani)
if:interface.1.enabled:create(true)
if:interface.1.ethernet:create
ethernet.duplex:create(full)
ethernet.auto-negotiation:create
auto-negotiation.enable:create(true)
ethernet.flow-control:create
flow-control.force-flow-control:create(false)
ethernet.logical:create
logical.tx-auto-off:create
tx-auto-off.enabled:create(false)
tx-auto-off.detect-time:create(10)
tx-auto-off.resume-detect-mode:create(manual)
tx-auto-off.resume-detect-interval:create(100)
tx-auto-off.resume-detect-duration:create(2000)
ethernet.ethernet-frame:create
ethernet-frame.jumbo-frame:create(false)
ethernet-frame.mtu:create(2052)
if:interface.1.ani:create
ani.upstream-fec:create(false)
ani.management-gemport-aes-indicator:create(true)
ani.tr069-mngt-enable:create(false)
ani.tr069-ip-index:create(0)
ani.transparent-enable:create(false)
ani.multicast-mode:create(unconcern)
ani.mac-learning-enable:create(true)
ani.ring-check:create
ring-check.ring-check:create(false)
ring-check.auto-shutdown:create(false)
ring-check.detect-frequency:create(8)
ring-check.resume-interval:create(300)
ring-check.detect-period:create(0)
if:interface.1.channel-group:create
channel-group.polling-period:create(100)
if:interface.1.channel-partition:create
channel-partition.downstream-fec:create(true)
channel-partition.closest-onu-distance:create(0)
channel-partition.maximum-differential-xpon-distance:create(20)
channel-partition.multicast-aes-indicator:create(false)
if:interface.1.channel-termination:create
channel-termination.laser-switch:create(true)
if:interface.1.v-ani:create
v-ani.expected-serial-number:create(HWTC0995C56E)
v-ani.ont-mngt-mode:create(omci)
v-ani.qos-mode:create(priority-queue)
v-ani.mapping-mode:create(vlan-priority)
v-ani.gem-encrypt-mode:create(aes128)
if:interface.1.xpon-port:create
xpon-port.ont-auto-find-switch:create(false)
xpon-port.ont-password-renew-interval:create(1440)
xpon-port.multicast-encrypt-mode:create(disable)
xpon-port.dba-surplus-assign:create(disable)
xpon-port.multicast-encrypt-algorithm:create(aes128)
if:interface.1.aggregator:create
aggregator.work-mode:create(static)
aggregator.fast-period:create(1)
aggregator.slow-period:create(30)
aggregator.max-link-number:create(no-limit)
aggregator.least-link-number:create(no-limit)
aggregator.forward-mode:create(ingress)
aggregator.preempt-enabled:create(false)
aggregator.preempt-delay:create(0)
if:interface.1.mac-learning:create
mac-learning.max-number-mac-addresses:create(4294967295)
mac-learning.number-committed-mac-addresses:create(1)
mac-learning.mac-learning-enable:create(true)
mac-learning.mac-learning-failure-action:create(forward)
if:interface.1.qos-policies:create
qos-policies.policing:create
policing.statistics:create
statistics.enabled:create(false)
if:interface.1.dhcpv6-ldra:create
dhcpv6-ldra.enable:create(false)
dhcpv6-ldra.trusted-port:create(false)
if:interface.1.vsi-profile:create
vsi-profile.egress-rewrite-tag-0:create
egress-rewrite-tag-0.vlan-id:create(vlan-id-from-match)
vsi-profile.egress-rewrite-tag-1:create
egress-rewrite-tag-1.vlan-id:create(vlan-id-from-match)
if:interface.1.bridge-port:create
bridge-port.pvid:create(1)
bridge-port.default-priority:create(0)
bridge-port.pcp-selection:create(8P0D)
bridge-port.use-dei:create(false)
bridge-port.drop-encoding:create(false)
bridge-port.service-access-priority-selection:create(false)
bridge-port.acceptable-frame:create(admit-all-frames)
bridge-port.enable-ingress-filtering:create(false)
bridge-port.enable-restricted-vlan-registration:create(false)
bridge-port.enable-vid-translation-table:create(false)
bridge-port.enable-egress-vid-translation-table:create(false)
bridge-port.priority-regeneration:create
priority-regeneration.priority0:create(0)
priority-regeneration.priority1:create(1)
priority-regeneration.priority2:create(2)
priority-regeneration.priority3:create(3)
priority-regeneration.priority4:create(4)
priority-regeneration.priority5:create(5)
priority-regeneration.priority6:create(6)
priority-regeneration.priority7:create(7)
bridge-port.service-access-priority:create
service-access-priority.priority0:create(0)
service-access-priority.priority1:create(1)
service-access-priority.priority2:create(2)
service-access-priority.priority3:create(3)
service-access-priority.priority4:create(4)
service-access-priority.priority5:create(5)
service-access-priority.priority6:create(6)
service-access-priority.priority7:create(7)
if:interface.1.pppoe:create
pppoe.enable:create(true)
if:interface.1.l2-dhcpv4-relay:create
l2-dhcpv4-relay.enable:create(false)
l2-dhcpv4-relay.trusted-port:create(false)
