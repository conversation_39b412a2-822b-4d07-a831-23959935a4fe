[{"name": "root_hw:component.1", "source_vertex_label": "root", "dest_vertex_label": "hw:component.1", "source_node_path": "/ietf-hardware:hardware", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw:component.1_hw:uri.1", "source_vertex_label": "hw:component.1", "dest_vertex_label": "hw:uri.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_if:interface.1", "source_vertex_label": "root", "dest_vertex_label": "if:interface.1", "source_node_path": "/ietf-interfaces:interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_board:board.1", "source_vertex_label": "root", "dest_vertex_label": "board:board.1", "source_node_path": "/huawei-board:boards", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_ip:address.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "ip:address.1", "source_node_path": "/ipv4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_rt:control-plane-protocol.2", "source_vertex_label": "root", "dest_vertex_label": "rt:control-plane-protocol.2", "source_node_path": "/ietf-routing:routing/control-plane-protocols", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xponani-pwr:power-management-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-xponani-pwr:power-management-profile.1", "source_node_path": "/bbf-xponani-power-management:xponani-power-management-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xponani-pwr:power-management-profile.1_bbf-xponani-pwr:mode.1", "source_vertex_label": "bbf-xponani-pwr:power-management-profile.1", "dest_vertex_label": "bbf-xponani-pwr:mode.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_dev:agile-controller.1", "source_vertex_label": "root", "dest_vertex_label": "dev:agile-controller.1", "source_node_path": "/huawei-device:device/agile-controllers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_dev:agile-controller-whitelist.1", "source_vertex_label": "root", "dest_vertex_label": "dev:agile-controller-whitelist.1", "source_node_path": "/huawei-device:device/agile-controller-whitelists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_dev:item-list.2", "source_vertex_label": "root", "dest_vertex_label": "dev:item-list.2", "source_node_path": "/huawei-device:config-virtual-license", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_dev:alarm-threshold.1", "source_vertex_label": "root", "dest_vertex_label": "dev:alarm-threshold.1", "source_node_path": "/huawei-device:set-alarm-threshold", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xpongemtcont:traffic-descriptor-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-xpongemtcont:traffic-descriptor-profile.1", "source_node_path": "/bbf-xpongemtcont:xpongemtcont/traffic-descriptor-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xpongemtcont:tcont.2", "source_vertex_label": "root", "dest_vertex_label": "bbf-xpongemtcont:tcont.2", "source_node_path": "/bbf-xpongemtcont:xpongemtcont/tconts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xpongemtcont:gemport.2", "source_vertex_label": "root", "dest_vertex_label": "bbf-xpongemtcont:gemport.2", "source_node_path": "/bbf-xpongemtcont:xpongemtcont/gemports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1", "source_node_path": "/bbf-qos-traffic-mngt:tm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1_bbf-qos-tm:mapping-entry.1", "source_vertex_label": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1", "dest_vertex_label": "bbf-qos-tm:mapping-entry.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-tm:bac-entry.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-tm:bac-entry.1", "source_node_path": "/bbf-qos-traffic-mngt:tm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-qos-tm:queue.2", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-qos-tm:queue.2", "source_node_path": "/tm-root/children-type/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_ps:protection-group.1", "source_vertex_label": "root", "dest_vertex_label": "ps:protection-group.1", "source_node_path": "/huawei-protection-group:protection-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ps:protection-group.1_ps:member.1", "source_vertex_label": "ps:protection-group.1", "dest_vertex_label": "ps:member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_al:shelf.1", "source_vertex_label": "root", "dest_vertex_label": "al:shelf.1", "source_node_path": "/ietf-alarms:alarms/control/alarm-shelving", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "al:shelf.1_al:resource.1", "source_vertex_label": "al:shelf.1", "dest_vertex_label": "al:resource.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "al:shelf.1_al:alarm-type.1", "source_vertex_label": "al:shelf.1", "dest_vertex_label": "al:alarm-type.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_al:alarm-profile.1", "source_vertex_label": "root", "dest_vertex_label": "al:alarm-profile.1", "source_node_path": "/ietf-alarms:alarms", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "al:alarm-profile.1_al:severity-level.2", "source_vertex_label": "al:alarm-profile.1", "dest_vertex_label": "al:severity-level.2", "source_node_path": "/alarm-severity-assignment-profile", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:listen.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:listen.1", "source_node_path": "/ietf-snmp:snmp/engine", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:target.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:target.1", "source_node_path": "/ietf-snmp:snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:target.1_snmp:tag.1", "source_vertex_label": "snmp:target.1", "dest_vertex_label": "snmp:tag.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:target-params.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:target-params.1", "source_node_path": "/ietf-snmp:snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:notify.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:notify.1", "source_node_path": "/ietf-snmp:snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:notify-filter-profile.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:notify-filter-profile.1", "source_node_path": "/ietf-snmp:snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:notify-filter-profile.1_snmp:include.1", "source_vertex_label": "snmp:notify-filter-profile.1", "dest_vertex_label": "snmp:include.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:notify-filter-profile.1_snmp:exclude.1", "source_vertex_label": "snmp:notify-filter-profile.1", "dest_vertex_label": "snmp:exclude.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:proxy.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:proxy.1", "source_node_path": "/ietf-snmp:snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_nacm:group.1", "source_vertex_label": "root", "dest_vertex_label": "nacm:group.1", "source_node_path": "/ietf-netconf-acm:nacm/groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "nacm:group.1_nacm:user-name.1", "source_vertex_label": "nacm:group.1", "dest_vertex_label": "nacm:user-name.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_nacm:rule-list.1", "source_vertex_label": "root", "dest_vertex_label": "nacm:rule-list.1", "source_node_path": "/ietf-netconf-acm:nacm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "nacm:rule-list.1_nacm:group.2", "source_vertex_label": "nacm:rule-list.1", "dest_vertex_label": "nacm:group.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "nacm:rule-list.1_nacm:rule.1", "source_vertex_label": "nacm:rule-list.1", "dest_vertex_label": "nacm:rule.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:community.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:community.1", "source_node_path": "/ietf-snmp:snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:user.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:user.1", "source_node_path": "/ietf-snmp:snmp/usm/local", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:remote.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:remote.1", "source_node_path": "/ietf-snmp:snmp/usm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:remote.1_snmp:user.2", "source_vertex_label": "snmp:remote.1", "dest_vertex_label": "snmp:user.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:group.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:group.1", "source_node_path": "/ietf-snmp:snmp/vacm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:group.1_snmp:member.1", "source_vertex_label": "snmp:group.1", "dest_vertex_label": "snmp:member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:member.1_snmp:security-model.1", "source_vertex_label": "snmp:member.1", "dest_vertex_label": "snmp:security-model.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:group.1_snmp:access.1", "source_vertex_label": "snmp:group.1", "dest_vertex_label": "snmp:access.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:view.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:view.1", "source_node_path": "/ietf-snmp:snmp/vacm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:view.1_snmp:include.2", "source_vertex_label": "snmp:view.1", "dest_vertex_label": "snmp:include.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:view.1_snmp:exclude.2", "source_vertex_label": "snmp:view.1", "dest_vertex_label": "snmp:exclude.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_snmp:cert-to-name.1", "source_vertex_label": "root", "dest_vertex_label": "snmp:cert-to-name.1", "source_node_path": "/ietf-snmp:snmp/tlstm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_frame:frame.1", "source_vertex_label": "root", "dest_vertex_label": "frame:frame.1", "source_node_path": "/huawei-frame:frames", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_ni:instance.1", "source_vertex_label": "root", "dest_vertex_label": "ni:instance.1", "source_node_path": "/huawei-network-instance:network-instance/instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xpon:proxy.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-xpon:proxy.1", "source_node_path": "/bbf-xpon:xpon/ictp/all-ictp-proxies-all-channel-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon:proxy.1_bbf-xpon:channel-termination.1", "source_vertex_label": "bbf-xpon:proxy.1", "dest_vertex_label": "bbf-xpon:channel-termination.1", "source_node_path": "/all-channel-terminations-proxied-by-this-proxy", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xpon:wavelength-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-xpon:wavelength-profile.1", "source_node_path": "/bbf-xpon:xpon/wavelength-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-xpon:multicast-gemport.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-xpon:multicast-gemport.1", "source_node_path": "/bbf-xpon:xpon/multicast-gemports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_onu-tr069-profile:onu-tr069-server-profile.1", "source_vertex_label": "root", "dest_vertex_label": "onu-tr069-profile:onu-tr069-server-profile.1", "source_node_path": "/bbf-xpon:xpon/onu-tr069-server-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_hw-energy-mgnt-an:energy-saving-mode.1", "source_vertex_label": "root", "dest_vertex_label": "hw-energy-mgnt-an:energy-saving-mode.1", "source_node_path": "/huawei-energy-management-an:energy-management-an/energy-saving-modes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-energy-mgnt-an:energy-saving-mode.1_hw-energy-mgnt-an:method.1", "source_vertex_label": "hw-energy-mgnt-an:energy-saving-mode.1", "dest_vertex_label": "hw-energy-mgnt-an:method.1", "source_node_path": "/methods", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-energy-mgnt-an:method.1_hw-energy-mgnt-an:parameter.1", "source_vertex_label": "hw-energy-mgnt-an:method.1", "dest_vertex_label": "hw-energy-mgnt-an:parameter.1", "source_node_path": "/parameters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_hw-res-pool-an:resource-pool-esn.1", "source_vertex_label": "root", "dest_vertex_label": "hw-res-pool-an:resource-pool-esn.1", "source_node_path": "/huawei-resource-pool-an:resource-pool-an/resource-pool-esns", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-res-pool-an:resource-pool-esn.1_hw-res-pool-an:consumption.1", "source_vertex_label": "hw-res-pool-an:resource-pool-esn.1", "dest_vertex_label": "hw-res-pool-an:consumption.1", "source_node_path": "/consumptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-res-pool-an:resource-pool-esn.1_hw-res-pool-an:rtu-consumption.1", "source_vertex_label": "hw-res-pool-an:resource-pool-esn.1", "dest_vertex_label": "hw-res-pool-an:rtu-consumption.1", "source_node_path": "/rtu-consumptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_hw-res-pool-an:resource-pool.1", "source_vertex_label": "root", "dest_vertex_label": "hw-res-pool-an:resource-pool.1", "source_node_path": "/huawei-resource-pool-an:resource-pool-an/resource-pools", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_traffic-alarm-profile:traffic-alarm-profile.1", "source_vertex_label": "root", "dest_vertex_label": "traffic-alarm-profile:traffic-alarm-profile.1", "source_node_path": "/bbf-xpon:xpon/traffic-alarm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_onu-power-shedding-profile:onu-power-shedding-profile.1", "source_vertex_label": "root", "dest_vertex_label": "onu-power-shedding-profile:onu-power-shedding-profile.1", "source_node_path": "/bbf-xpon:xpon/onu-power-shedding-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_sys:server.1", "source_vertex_label": "root", "dest_vertex_label": "sys:server.1", "source_node_path": "/ietf-system:system/ntp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_sys:search.1", "source_vertex_label": "root", "dest_vertex_label": "sys:search.1", "source_node_path": "/ietf-system:system/dns-resolver", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_sys:server.2", "source_vertex_label": "root", "dest_vertex_label": "sys:server.2", "source_node_path": "/ietf-system:system/dns-resolver", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_sys:server.3", "source_vertex_label": "root", "dest_vertex_label": "sys:server.3", "source_node_path": "/ietf-system:system/radius", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_sys:user-authentication-order.1", "source_vertex_label": "root", "dest_vertex_label": "sys:user-authentication-order.1", "source_node_path": "/ietf-system:system/authentication", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_sys:user.1", "source_vertex_label": "root", "dest_vertex_label": "sys:user.1", "source_node_path": "/ietf-system:system/authentication", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "sys:user.1_sys:authorized-key.1", "source_vertex_label": "sys:user.1", "dest_vertex_label": "sys:authorized-key.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_alm-policy-profile:onu-alm-policy-profile.1", "source_vertex_label": "root", "dest_vertex_label": "alm-policy-profile:onu-alm-policy-profile.1", "source_node_path": "/bbf-xpon:xpon/onu-alm-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "alm-policy-profile:onu-alm-policy-profile.1_alm-policy-profile:alarm-lists.1", "source_vertex_label": "alm-policy-profile:onu-alm-policy-profile.1", "dest_vertex_label": "alm-policy-profile:alarm-lists.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-subif:rule.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-subif:rule.1", "source_node_path": "/frame-processing/inline-frame-processing/inline-frame-processing/ingress-rule", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-subif:rule.1_bbf-subif-tag:tag.1", "source_vertex_label": "bbf-subif:rule.1", "dest_vertex_label": "bbf-subif-tag:tag.1", "source_node_path": "/flexible-match/match-criteria/vlan-tag-match-type/vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-subif:rule.1_bbf-subif-tag:push-tag.1", "source_vertex_label": "bbf-subif:rule.1", "dest_vertex_label": "bbf-subif-tag:push-tag.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_alm-threshold-profile:optic-alm-threshold-profile.1", "source_vertex_label": "root", "dest_vertex_label": "alm-threshold-profile:optic-alm-threshold-profile.1", "source_node_path": "/bbf-xpon:xpon/optic-alm-threshold-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-gpon-gemport:gem-bundle.2", "source_vertex_label": "root", "dest_vertex_label": "bbf-gpon-gemport:gem-bundle.2", "source_node_path": "/bbf-xpongemtcont:xpongemtcont/gem-bundle", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-gpon-gemport:gem-bundle.2_bbf-gpon-gemport:slave-gem.2", "source_vertex_label": "bbf-gpon-gemport:gem-bundle.2", "dest_vertex_label": "bbf-gpon-gemport:slave-gem.2", "source_node_path": "/slave-gem", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-v-ani:tpid.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-v-ani:tpid.1", "source_node_path": "/v-ani", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-if-port-ref:port-layer-if.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-if-port-ref:port-layer-if.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_ontload:ont-select.1", "source_vertex_label": "root", "dest_vertex_label": "ontload:ont-select.1", "source_node_path": "/bbf-xpon-ont-load:xpon-ont-load/ont-selects", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-channel:tcont-group.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-channel:tcont-group.1", "source_node_path": "/channel-pair/tcont-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-ani:onu-snmp-route.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-ani:onu-snmp-route.1", "source_node_path": "/ani/snmp-route", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-ani:eth-bundle.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-ani:eth-bundle.1", "source_node_path": "/ani/ont-port-bundle", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon-ani:eth-bundle.1_bbf-xpon-ani:port.1", "source_vertex_label": "bbf-xpon-ani:eth-bundle.1", "dest_vertex_label": "bbf-xpon-ani:port.1", "source_node_path": "/port-list", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_an-pg:protection-group.1", "source_vertex_label": "root", "dest_vertex_label": "an-pg:protection-group.1", "source_node_path": "/an-protection-group:protection-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "an-pg:protection-group.1_an-pg:member.1", "source_vertex_label": "an-pg:protection-group.1", "dest_vertex_label": "an-pg:member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_an-pon-pg:local-node-ports.1", "source_vertex_label": "root", "dest_vertex_label": "an-pon-pg:local-node-ports.1", "source_node_path": "/an-protection-group:protection-groups/dual-parenting-sync", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_an-pon-pg:dual-parenting-peer-node.1", "source_vertex_label": "root", "dest_vertex_label": "an-pon-pg:dual-parenting-peer-node.1", "source_node_path": "/an-protection-group:protection-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-ldra-ext:dhcpv6-option-permit-forwarding.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-ldra-ext:dhcpv6-option-permit-forwarding.1", "source_node_path": "/ietf-system:system/dhcpv6-global", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-subprof:subscriber-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-subprof:subscriber-profile.1", "source_node_path": "/bbf-subscriber-profiles:subscriber-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_dot1ax:aggregating-system.1", "source_vertex_label": "root", "dest_vertex_label": "dot1ax:aggregating-system.1", "source_node_path": "/ieee802-dot1ax:lag-system", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_an-dot1ax:port.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "an-dot1ax:port.1", "source_node_path": "/aggregator/ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-l2-fwd:forwarder.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-l2-fwd:forwarder.1", "source_node_path": "/bbf-l2-forwarding:forwarding/forwarders", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarder.1_bbf-l2-fwd:port.1", "source_vertex_label": "bbf-l2-fwd:forwarder.1", "dest_vertex_label": "bbf-l2-fwd:port.1", "source_node_path": "/ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarder.1_bbf-l2-fwd:port-group.1", "source_vertex_label": "bbf-l2-fwd:forwarder.1", "dest_vertex_label": "bbf-l2-fwd:port-group.1", "source_node_path": "/port-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:port-group.1_bbf-l2-fwd:port.2", "source_vertex_label": "bbf-l2-fwd:port-group.1", "dest_vertex_label": "bbf-l2-fwd:port.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-l2-fwd:flooding-policies-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-l2-fwd:flooding-policies-profile.1", "source_node_path": "/bbf-l2-forwarding:forwarding/flooding-policies-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:flooding-policies-profile.1_bbf-l2-fwd:flooding-policy.1", "source_vertex_label": "bbf-l2-fwd:flooding-policies-profile.1", "dest_vertex_label": "bbf-l2-fwd:flooding-policy.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:flooding-policy.1_bbf-l2-fwd:interface-usages.1", "source_vertex_label": "bbf-l2-fwd:flooding-policy.1", "dest_vertex_label": "bbf-l2-fwd:interface-usages.1", "source_node_path": "/in-interface-usages", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:flooding-policy.1_bbf-l2-fwd:interface-usages.2", "source_vertex_label": "bbf-l2-fwd:flooding-policy.1", "dest_vertex_label": "bbf-l2-fwd:interface-usages.2", "source_node_path": "/frame-forwarding/forward/out-interface-usages", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarder.1_bbf-l2-fwd:flooding-policy.2", "source_vertex_label": "bbf-l2-fwd:forwarder.1", "dest_vertex_label": "bbf-l2-fwd:flooding-policy.2", "source_node_path": "/flooding-policies/flooding-policy-type/forwarder-specific", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-l2-fwd:forwarding-database.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-l2-fwd:forwarding-database.1", "source_node_path": "/bbf-l2-forwarding:forwarding/forwarding-databases", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarding-database.1_bbf-l2-fwd:static-mac-address.1", "source_vertex_label": "bbf-l2-fwd:forwarding-database.1", "dest_vertex_label": "bbf-l2-fwd:static-mac-address.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-l2-fwd:split-horizon-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-l2-fwd:split-horizon-profile.1", "source_node_path": "/bbf-l2-forwarding:forwarding/split-horizon-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:split-horizon-profile.1_bbf-l2-fwd:split-horizon.1", "source_vertex_label": "bbf-l2-fwd:split-horizon-profile.1", "dest_vertex_label": "bbf-l2-fwd:split-horizon.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:split-horizon.1_bbf-l2-fwd:out-interface-usage.1", "source_vertex_label": "bbf-l2-fwd:split-horizon.1", "dest_vertex_label": "bbf-l2-fwd:out-interface-usage.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-l2-fwd:mac-learning-control-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-l2-fwd:mac-learning-control-profile.1", "source_node_path": "/bbf-l2-forwarding:forwarding/mac-learning-control-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:mac-learning-control-profile.1_bbf-l2-fwd:mac-learning-rule.1", "source_vertex_label": "bbf-l2-fwd:mac-learning-control-profile.1", "dest_vertex_label": "bbf-l2-fwd:mac-learning-rule.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:mac-learning-rule.1_bbf-l2-fwd:mac-can-not-move-to.1", "source_vertex_label": "bbf-l2-fwd:mac-learning-rule.1", "dest_vertex_label": "bbf-l2-fwd:mac-can-not-move-to.1", "source_node_path": "/mac-learning-action/learn-but-do-not-move", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pri-prof:cos-group-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pri-prof:cos-group-profile.1", "source_node_path": "/bbf-qos-priority-profiles:cos-group-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:cos-group-profile.1_bbf-qos-pri-prof:cos-group.1", "source_vertex_label": "bbf-qos-pri-prof:cos-group-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:cos-group.1", "source_node_path": "/cos-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1", "source_node_path": "/bbf-qos-priority-profiles:pbit-to-pbit-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1_bbf-qos-pri-prof:pbit.1", "source_vertex_label": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:pbit.1", "source_node_path": "/pbits", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:pbit.1_bbf-qos-pri-prof:color.1", "source_vertex_label": "bbf-qos-pri-prof:pbit.1", "dest_vertex_label": "bbf-qos-pri-prof:color.1", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1", "source_node_path": "/bbf-qos-priority-profiles:ipprec-to-pbit-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1_bbf-qos-pri-prof:ipprec.1", "source_vertex_label": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:ipprec.1", "source_node_path": "/ipprecs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:ipprec.1_bbf-qos-pri-prof:color.2", "source_vertex_label": "bbf-qos-pri-prof:ipprec.1", "dest_vertex_label": "bbf-qos-pri-prof:color.2", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1", "source_node_path": "/bbf-qos-priority-profiles:dscp-to-pbit-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1_bbf-qos-pri-prof:dscp.1", "source_vertex_label": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:dscp.1", "source_node_path": "/dscps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp.1_bbf-qos-pri-prof:color.3", "source_vertex_label": "bbf-qos-pri-prof:dscp.1", "dest_vertex_label": "bbf-qos-pri-prof:color.3", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1", "source_node_path": "/bbf-qos-priority-profiles:dscp-to-dscp-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1_bbf-qos-pri-prof:dscp.2", "source_vertex_label": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:dscp.2", "source_node_path": "/dscps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp.2_bbf-qos-pri-prof:color.4", "source_vertex_label": "bbf-qos-pri-prof:dscp.2", "dest_vertex_label": "bbf-qos-pri-prof:color.4", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pri-prof:queue-mapping-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pri-prof:queue-mapping-profile.1", "source_node_path": "/bbf-qos-priority-profiles:queue-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-cls:classifier-entry.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-cls:classifier-entry.1", "source_node_path": "/bbf-qos-classifiers:classifiers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cls:classifier-entry.1_bbf-qos-cls:classifier-action-entry-cfg.1", "source_vertex_label": "bbf-qos-cls:classifier-entry.1", "dest_vertex_label": "bbf-qos-cls:classifier-action-entry-cfg.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cls:classifier-action-entry-cfg.1_bbf-qos-cls:pbit-marking-list.1", "source_vertex_label": "bbf-qos-cls:classifier-action-entry-cfg.1", "dest_vertex_label": "bbf-qos-cls:pbit-marking-list.1", "source_node_path": "/action-cfg-params/pbit-marking/pbit-marking-cfg", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pol:policy.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pol:policy.1", "source_node_path": "/bbf-qos-policies:policies", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pol:policy.1_bbf-qos-pol:classifiers.1", "source_vertex_label": "bbf-qos-pol:policy.1", "dest_vertex_label": "bbf-qos-pol:classifiers.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-pol:policy-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-pol:policy-profile.1", "source_node_path": "/bbf-qos-policies:qos-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pol:policy-profile.1_bbf-qos-pol:policy-list.1", "source_vertex_label": "bbf-qos-pol:policy-profile.1", "dest_vertex_label": "bbf-qos-pol:policy-list.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-plc:policing-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-plc:policing-profile.1", "source_node_path": "/bbf-qos-policing:policing-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-plc-prof:priority-group-policing-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-plc-prof:priority-group-policing-profile.1", "source_node_path": "/bbf-qos-policing-profiles:priority-group-policing-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-plc-prof:priority-group-policing-profile.1_bbf-qos-plc-prof:priority-group.1", "source_vertex_label": "bbf-qos-plc-prof:priority-group-policing-profile.1", "dest_vertex_label": "bbf-qos-plc-prof:priority-group.1", "source_node_path": "/priority-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-plc-prof:car-threshold-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-plc-prof:car-threshold-profile.1", "source_node_path": "/bbf-qos-policing-profiles:car-threshold-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-tm-prof:queue.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-tm-prof:queue.1", "source_node_path": "/bbf-qos-traffic-mngt-profiles:queue-wred/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-tm-prof:queue-policy-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-tm-prof:queue-policy-profile.1", "source_node_path": "/bbf-qos-traffic-mngt-profiles:queue-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-tm-prof:queue-policy-profile.1_bbf-qos-tm-prof:queue.2", "source_vertex_label": "bbf-qos-tm-prof:queue-policy-profile.1", "dest_vertex_label": "bbf-qos-tm-prof:queue.2", "source_node_path": "/queue-wred/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-tm-prof:queue-policy-profile.1_bbf-qos-tm-prof:queue.3", "source_vertex_label": "bbf-qos-tm-prof:queue-policy-profile.1", "dest_vertex_label": "bbf-qos-tm-prof:queue.3", "source_node_path": "/queue-buffer-max-size/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-fwd-prof:forwarder-policy-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-fwd-prof:forwarder-policy-profile.1", "source_node_path": "/bbf-l2-forwarding:forwarding/forwarder-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-fwd-prof:forwarder-policy.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-fwd-prof:forwarder-policy.1", "source_node_path": "/bbf-l2-forwarding:forwarding/forwarders", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-mgmd:preview-parameters-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-mgmd:preview-parameters-profile.1", "source_node_path": "/bbf-mgmd:multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-mgmd:multicast-snoop-transparent-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-mgmd:multicast-snoop-transparent-profile.1", "source_node_path": "/bbf-mgmd:multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-mgmd:multicast-snoop-with-proxy-reporting-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-mgmd:multicast-snoop-with-proxy-reporting-profile.1", "source_node_path": "/bbf-mgmd:multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-mgmd:multicast-proxy-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-mgmd:multicast-proxy-profile.1", "source_node_path": "/bbf-mgmd:multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-mgmd:multicast-vpn.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-mgmd:multicast-vpn.1", "source_node_path": "/bbf-mgmd:multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-interface-to-host.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-interface-to-host.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-interface-to-host.1_bbf-mgmd:multicast-package.1", "source_vertex_label": "bbf-mgmd:multicast-interface-to-host.1", "dest_vertex_label": "bbf-mgmd:multicast-package.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-network-interface.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-network-interface.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-channel.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-channel.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-channel.1_bbf-mgmd:interface-to-host.1", "source_vertex_label": "bbf-mgmd:multicast-channel.1", "dest_vertex_label": "bbf-mgmd:interface-to-host.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-package.2", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-package.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-package.2_bbf-mgmd:multicast-channel-admission-control.1", "source_vertex_label": "bbf-mgmd:multicast-package.2", "dest_vertex_label": "bbf-mgmd:multicast-channel-admission-control.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd-channel:channel-match-range.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd-channel:channel-match-range.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-mgmd-if:interface-to-cascade.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-mgmd-if:interface-to-cascade.1", "source_node_path": "/bbf-mgmd:multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-channel.1_bbf-mgmd-if:interface-to-cascades.1", "source_vertex_label": "bbf-mgmd:multicast-channel.1", "dest_vertex_label": "bbf-mgmd-if:interface-to-cascades.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_ta:pinned-certificates.1", "source_vertex_label": "root", "dest_vertex_label": "ta:pinned-certificates.1", "source_node_path": "/ietf-trust-anchors:trust-anchors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ta:pinned-certificates.1_ta:pinned-certificate.1", "source_vertex_label": "ta:pinned-certificates.1", "dest_vertex_label": "ta:pinned-certificate.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_ta:pinned-host-keys.1", "source_vertex_label": "root", "dest_vertex_label": "ta:pinned-host-keys.1", "source_node_path": "/ietf-trust-anchors:trust-anchors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ta:pinned-host-keys.1_ta:pinned-host-key.1", "source_vertex_label": "ta:pinned-host-keys.1", "dest_vertex_label": "ta:pinned-host-key.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_syslog:facility-list.1", "source_vertex_label": "root", "dest_vertex_label": "syslog:facility-list.1", "source_node_path": "/ietf-syslog:syslog/actions/console/facility-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_syslog:log-file.1", "source_vertex_label": "root", "dest_vertex_label": "syslog:log-file.1", "source_node_path": "/ietf-syslog:syslog/actions/file", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:log-file.1_syslog:facility-list.2", "source_vertex_label": "syslog:log-file.1", "dest_vertex_label": "syslog:facility-list.2", "source_node_path": "/facility-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_syslog:destination.1", "source_vertex_label": "root", "dest_vertex_label": "syslog:destination.1", "source_node_path": "/ietf-syslog:syslog/actions/remote", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:tls-version.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:tls-version.1", "source_node_path": "/transport/tls/tls/hello-params/tls-versions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:cipher-suite.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:cipher-suite.1", "source_node_path": "/transport/tls/tls/hello-params/cipher-suites", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:facility-list.3", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:facility-list.3", "source_node_path": "/facility-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:cert-signer.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:cert-signer.1", "source_node_path": "/signing/cert-signers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-ldra:dhcpv6-ldra-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-ldra:dhcpv6-ldra-profile.1", "source_node_path": "/bbf-ldra:dhcpv6-ldra-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ldra:dhcpv6-ldra-profile.1_bbf-ldra:option.1", "source_vertex_label": "bbf-ldra:dhcpv6-ldra-profile.1", "dest_vertex_label": "bbf-ldra:option.1", "source_node_path": "/options", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-vsi-prof:vsi-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-vsi-prof:vsi-profile.1", "source_node_path": "/bbf-vlan-sub-interface-profiles:vsi-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof:vsi-profile.1_bbf-vsi-prof-fp:rule.1", "source_vertex_label": "bbf-vsi-prof:vsi-profile.1", "dest_vertex_label": "bbf-vsi-prof-fp:rule.1", "source_node_path": "/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/ingress-rule", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-mac-address.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-mac-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv4-address.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv4-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv6-address.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv6-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:tag.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:tag.1", "source_node_path": "/match-criteria/vlans/vlan-tag-match-type/vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:ethernet-frame-type.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:ethernet-frame-type.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:protocol.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:protocol.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-mac-address.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-mac-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv4-address.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv4-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv6-address.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv6-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:ethernet-frame-type.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:ethernet-frame-type.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:protocol.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:protocol.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:push-tag.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:push-tag.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof:vsi-profile.1_bbf-vsi-prof-fp:push-tag.2", "source_vertex_label": "bbf-vsi-prof:vsi-profile.1", "dest_vertex_label": "bbf-vsi-prof-fp:push-tag.2", "source_node_path": "/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/egress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-filt:filter.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-filt:filter.1", "source_node_path": "/bbf-qos-filters:filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-shap:shaper-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-shap:shaper-profile.1", "source_node_path": "/bbf-qos-traffic-mngt:tm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_x733:x733-mapping.1", "source_vertex_label": "root", "dest_vertex_label": "x733:x733-mapping.1", "source_node_path": "/ietf-alarms:alarms/control", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-lt:link-table.2", "source_vertex_label": "root", "dest_vertex_label": "bbf-lt:link-table.2", "source_node_path": "/bbf-link-table:link-table", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-lt:link-table.2_bbf-lt:to-interface.1", "source_vertex_label": "bbf-lt:link-table.2", "dest_vertex_label": "bbf-lt:to-interface.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-qos-cpsfilt:composite-filter.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-qos-cpsfilt:composite-filter.1", "source_node_path": "/bbf-qos-composite-filters:composite-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:composite-filter.1_bbf-qos-cpsfilt:filter.1", "source_vertex_label": "bbf-qos-cpsfilt:composite-filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:filter.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:tag.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:tag.1", "source_node_path": "/filter-method/inline/vlans/vlan-match/match-vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:ethernet-frame-type.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:ethernet-frame-type.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:protocol.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:protocol.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:pbit-marking-list.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:pbit-marking-list.1_bbf-qos-cpsfilt:pbit-value.1", "source_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.1", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-value.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:dei-marking-list.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:dei-marking-list.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:flow-color.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:flow-color.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cls:classifier-entry.1_bbf-qos-cpsfilt:pbit-marking-list.2", "source_vertex_label": "bbf-qos-cls:classifier-entry.1", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.2", "source_node_path": "/filter-method/enhanced-classifier", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:pbit-marking-list.2_bbf-qos-cpsfilt:pbit-value.2", "source_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.2", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-value.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_dot1q:bridge.1", "source_vertex_label": "root", "dest_vertex_label": "dot1q:bridge.1", "source_node_path": "/ieee802-dot1q-bridge:bridges", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:bridge.1_dot1q:component.1", "source_vertex_label": "dot1q:bridge.1", "dest_vertex_label": "dot1q:component.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:filtering-entry.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:filtering-entry.1", "source_node_path": "/filtering-database", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:filtering-entry.1_dot1q:port-map.1", "source_vertex_label": "dot1q:filtering-entry.1", "dest_vertex_label": "dot1q:port-map.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vlan-registration-entry.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vlan-registration-entry.1", "source_node_path": "/filtering-database", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:vlan-registration-entry.1_dot1q:port-map.2", "source_vertex_label": "dot1q:vlan-registration-entry.1", "dest_vertex_label": "dot1q:port-map.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:filtering-entry.2", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:filtering-entry.2", "source_node_path": "/permanent-database", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:filtering-entry.2_dot1q:port-map.3", "source_vertex_label": "dot1q:filtering-entry.2", "dest_vertex_label": "dot1q:port-map.3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vlan.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vlan.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:protocol-group-database.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:protocol-group-database.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vid-to-fid-allocation.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vid-to-fid-allocation.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:fid-to-vid-allocation.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:fid-to-vid-allocation.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vid-to-fid.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vid-to-fid.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:mstid.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:mstid.1", "source_node_path": "/bridge-mst", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:fid-to-mstid.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:fid-to-mstid.1", "source_node_path": "/bridge-mst", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:fid-to-mstid-allocation.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:fid-to-mstid-allocation.1", "source_node_path": "/bridge-mst", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:pcp-decoding-map.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:pcp-decoding-map.1", "source_node_path": "/bridge-port/pcp-decoding-table", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:pcp-decoding-map.1_dot1q:priority-map.1", "source_vertex_label": "dot1q:pcp-decoding-map.1", "dest_vertex_label": "dot1q:priority-map.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:pcp-encoding-map.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:pcp-encoding-map.1", "source_node_path": "/bridge-port/pcp-encoding-table", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:pcp-encoding-map.1_dot1q:priority-map.2", "source_vertex_label": "dot1q:pcp-encoding-map.1", "dest_vertex_label": "dot1q:priority-map.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:traffic-class-map.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:traffic-class-map.1", "source_node_path": "/bridge-port/traffic-class", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:traffic-class-map.1_dot1q:available-traffic-class.1", "source_vertex_label": "dot1q:traffic-class-map.1", "dest_vertex_label": "dot1q:available-traffic-class.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:protocol-group-vid-set.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:protocol-group-vid-set.1", "source_node_path": "/bridge-port", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:protocol-group-vid-set.1_dot1q:vid.2", "source_vertex_label": "dot1q:protocol-group-vid-set.1", "dest_vertex_label": "dot1q:vid.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:vid-translations.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:vid-translations.1", "source_node_path": "/bridge-port", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:egress-vid-translations.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:egress-vid-translations.1", "source_node_path": "/bridge-port", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_lldp:port.1", "source_vertex_label": "root", "dest_vertex_label": "lldp:port.1", "source_node_path": "/ieee802-dot1ab-lldp:lldp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "lldp:port.1_lldp:management-address-tx-port.1", "source_vertex_label": "lldp:port.1", "dest_vertex_label": "lldp:management-address-tx-port.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rt:control-plane-protocol.2_v4ur:route.1", "source_vertex_label": "rt:control-plane-protocol.2", "dest_vertex_label": "v4ur:route.1", "source_node_path": "/static-routes/ipv4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "v4ur:route.1_v4ur:next-hop.2", "source_vertex_label": "v4ur:route.1", "dest_vertex_label": "v4ur:next-hop.2", "source_node_path": "/next-hop/next-hop-options/next-hop-list/next-hop-list", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-pppoe-ia:pppoe-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-pppoe-ia:pppoe-profile.1", "source_node_path": "/bbf-pppoe-intermediate-agent:pppoe-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-pppoe-ia:pppoe-profile.1_bbf-pppoe-ia:subtag.1", "source_vertex_label": "bbf-pppoe-ia:pppoe-profile.1", "dest_vertex_label": "bbf-pppoe-ia:subtag.1", "source_node_path": "/pppoe-vendor-specific-tag", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-pppoe-ia:pppoe-profile.1_bbf-pppoe-ia:padding.1", "source_vertex_label": "bbf-pppoe-ia:pppoe-profile.1", "dest_vertex_label": "bbf-pppoe-ia:padding.1", "source_node_path": "/pppoe-vendor-specific-tag", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-fpprof:frame-processing-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-fpprof:frame-processing-profile.1", "source_node_path": "/bbf-frame-processing-profiles:frame-processing-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:tag.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:tag.1", "source_node_path": "/match-criteria/vlans/vlan-tag-match-type/vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-mac-address.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-mac-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv4-address.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv4-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv6-address.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv6-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:ethernet-frame-type.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:ethernet-frame-type.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:protocol.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:protocol.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-mac-address.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-mac-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv4-address.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv4-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv6-address.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv6-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:ethernet-frame-type.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:ethernet-frame-type.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:protocol.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:protocol.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:copy-from-tags-to-marking-list.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:copy-from-tags-to-marking-list.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:push-tag.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:push-tag.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:push-tag.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:push-tag.2", "source_node_path": "/egress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_bbf-l2-d4r:l2-dhcpv4-relay-profile.1", "source_vertex_label": "root", "dest_vertex_label": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1", "source_node_path": "/bbf-l2-dhcpv4-relay:l2-dhcpv4-relay-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1_bbf-l2-d4r:suboptions.1", "source_vertex_label": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1", "dest_vertex_label": "bbf-l2-d4r:suboptions.1", "source_node_path": "/option82-format", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_oc-telemetry:sensor-group.1", "source_vertex_label": "root", "dest_vertex_label": "oc-telemetry:sensor-group.1", "source_node_path": "/openconfig-telemetry:telemetry-system/sensor-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:sensor-group.1_oc-telemetry:sensor-path.1", "source_vertex_label": "oc-telemetry:sensor-group.1", "dest_vertex_label": "oc-telemetry:sensor-path.1", "source_node_path": "/sensor-paths", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_oc-telemetry:destination-group.1", "source_vertex_label": "root", "dest_vertex_label": "oc-telemetry:destination-group.1", "source_node_path": "/openconfig-telemetry:telemetry-system/destination-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:destination-group.1_oc-telemetry:destination.1", "source_vertex_label": "oc-telemetry:destination-group.1", "dest_vertex_label": "oc-telemetry:destination.1", "source_node_path": "/destinations", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_oc-telemetry:subscription.1", "source_vertex_label": "root", "dest_vertex_label": "oc-telemetry:subscription.1", "source_node_path": "/openconfig-telemetry:telemetry-system/subscriptions/persistent", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:subscription.1_oc-telemetry:sensor-profile.1", "source_vertex_label": "oc-telemetry:subscription.1", "dest_vertex_label": "oc-telemetry:sensor-profile.1", "source_node_path": "/sensor-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:subscription.1_oc-telemetry:destination-group.2", "source_vertex_label": "oc-telemetry:subscription.1", "dest_vertex_label": "oc-telemetry:destination-group.2", "source_node_path": "/destination-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_ks:asymmetric-key.1", "source_vertex_label": "root", "dest_vertex_label": "ks:asymmetric-key.1", "source_node_path": "/ietf-keystore:keystore/asymmetric-keys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ks:asymmetric-key.1_ks:certificate.1", "source_vertex_label": "ks:asymmetric-key.1", "dest_vertex_label": "ks:certificate.1", "source_node_path": "/certificates", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]