{"op": "merge", "if:interface.1": [{"name": "channel.0.168.6_1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}, "channel-partition": {"channel-group-ref": "channel.0.168.6_2"}}, {"name": "channel.0.168.6_2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel.0.168.6.pair", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel.0.168.6_2", "channel-partition-ref": "channel.0.168.6_1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"op": "delete_graph", "key_name": "if:interface.1.PK", "key_value": ["NULL", "ethernetCsmacd.0.9.0"]}, {"name": "GE0/0/1", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "GE0/0/1", "channel-partition-ref": "GE0/0/1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "GE0/0/2", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "GE0/0/2", "channel-partition-ref": "GE0/0/2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "GE0/0/3", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "GE0/0/3", "channel-partition-ref": "GE0/0/3", "channel-pair-type": "bbf-xpon-types:gpon"}}]}