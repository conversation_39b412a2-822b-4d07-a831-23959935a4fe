ietf-interfaces:interfaces:update[(p<PERSON><PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
ietf-interfaces:interfaces.if:interface.1:create[(pri<PERSON><PERSON>(PID:1,name:channel-group.0.168.1), pre<PERSON>ey(PID:1,name:channel-pair.0.16.15.gpon)),(NULL)]
if:interface.1.ID:create(6)
if:interface.1.type:create(bbf-xpon-if-type:channel-group)
if:interface.1.enabled:create(true)
if:interface.1.channel-group:create
channel-group.polling-period:create(100)
ietf-interfaces:interfaces.if:interface.1:create[(pri<PERSON><PERSON>(PID:1,name:channel-pair.0.168.1.gpon), preKey(PID:1,name:channel-group.0.168.1)),(NULL)]
if:interface.1.channel-pair:create
channel-pair.channel-group-ref:create(channel-group.0.168.1)
channel-pair.channel-pair-type:create(bbf-xpon-types:gpon)
