ietf-interfaces:interfaces:update[(p<PERSON><PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
ietf-interfaces:interfaces.if:interface.1:create[(pri<PERSON><PERSON>(PID:1,name:channel-group.0.168.1), pre<PERSON>ey(PID:1,name:channel-pair.0.16.15.gpon)),(NULL)]
if:interface.1.ID:create(6)
if:interface.1.type:create(bbf-xpon-if-type:ani)
if:interface.1.enabled:create(true)
if:interface.1.ani:create
ani.upstream-fec:create(false)
ani.management-gemport-aes-indicator:create(true)
ani.tr069-mngt-enable:create(false)
ani.tr069-ip-index:create(0)
ani.transparent-enable:create(false)
ani.multicast-mode:create(unconcern)
ani.mac-learning-enable:create(true)
ietf-interfaces:interfaces.if:interface.1:create[(priKey(PID:1,name:channel-pair.0.168.1.gpon), pre<PERSON><PERSON>(PID:1,name:channel-group.0.168.1)),(NULL)]
if:interface.1.ID:create(7)
if:interface.1.type:create(bbf-xpon-if-type:channel-pair)
if:interface.1.enabled:create(true)
if:interface.1.channel-pair:create
channel-pair.channel-group-ref:create(channel-group.0.168.1)
channel-pair.channel-pair-type:create(bbf-xpon-types:gpon)
