/*  LzmaEnc.h -- LZMA Encoder
2023-04-13 : <PERSON> : Public domain */

#ifndef ZIP7_INC_LZMA_ENC_H
#define ZIP7_INC_LZMA_ENC_H

#include "7zTypes.h"

EXTERN_C_BEGIN

#define LZMA_PROPS_SIZE 5

typedef struct {
    int level;               /* 0 <= level <= 9 */
    UInt32 dictSize;         /* (1 << 12) <= dictSize <= (1 << 27) for 32-bit version
                                (1 << 12) <= dictSize <= (3 << 29) for 64-bit version
                                default = (1 << 24) */
    int lc;                  /* 0 <= lc <= 8, default = 3 */
    int lp;                  /* 0 <= lp <= 4, default = 0 */
    int pb;                  /* 0 <= pb <= 4, default = 2 */
    int algo;                /* 0 - fast, 1 - normal, default = 1 */
    int fb;                  /* 5 <= fb <= 273, default = 32 */
    int btMode;              /* 0 - hashChain Mode, 1 - binTree mode - normal, default = 1 */
    int numHashBytes;        /* 2, 3 or 4, default = 4 */
    unsigned numHashOutBits; /* default = ? */
    UInt32 mc;               /* 1 <= mc <= (1 << 30), default = 32 */
    unsigned writeEndMark;   /* 0 - do not write EOPM, 1 - write EOPM, default = 0 */
    int numThreads;          /* 1 or 2, default = 2 */

    // int _pad;

    UInt64 reduceSize; /* estimated size of data that will be compressed. default = (UInt64)(Int64)-1.
                          Encoder uses this value to reduce dictionary size */

    UInt64 affinity;
} CLzmaEncProps;

void LzmaEncProps_Init(CLzmaEncProps *p);
void LzmaEncProps_Normalize(CLzmaEncProps *p);
UInt32 LzmaEncProps_GetDictSize(const CLzmaEncProps *props2);

/* ---------- CLzmaEncHandle Interface ---------- */

/* LzmaEnc* functions can return the following exit codes:
SRes:
  SZ_OK           - OK
  SZ_ERROR_MEM    - Memory allocation error
  SZ_ERROR_PARAM  - Incorrect paramater in props
  SZ_ERROR_WRITE  - ISeqOutStream write callback error
  SZ_ERROR_OUTPUT_EOF - output buffer overflow - version with (Byte *) output
  SZ_ERROR_PROGRESS - some break from progress callback
  SZ_ERROR_THREAD - error in multithreading functions (only for Mt version)
*/

typedef struct CLzmaEnc CLzmaEnc;
typedef CLzmaEnc *CLzmaEncHandle;
// Z7_DECLARE_HANDLE(CLzmaEncHandle)

CLzmaEncHandle LzmaEnc_Create(ISzAllocPtr alloc);
void LzmaEnc_Destroy(CLzmaEncHandle p, ISzAllocPtr alloc, ISzAllocPtr allocBig);

SRes LzmaEnc_SetProps(CLzmaEncHandle p, const CLzmaEncProps *props);
void LzmaEnc_SetDataSize(CLzmaEncHandle p, UInt64 expectedDataSiize);
SRes LzmaEnc_WriteProperties(CLzmaEncHandle p, Byte *properties, SizeT *size);
unsigned LzmaEnc_IsWriteEndMark(CLzmaEncHandle p);

SRes LzmaEnc_Encode(CLzmaEncHandle p, ISeqOutStreamPtr outStream, ISeqInStreamPtr inStream,
    ICompressProgressPtr progress, ISzAllocPtr alloc, ISzAllocPtr allocBig);
SRes LzmaEnc_MemEncode(CLzmaEncHandle p, Byte *dest, SizeT *destLen, const Byte *src, SizeT srcLen, int writeEndMark,
    ICompressProgressPtr progress, ISzAllocPtr alloc, ISzAllocPtr allocBig);

/* ---------- One Call Interface ---------- */

SRes LzmaEncode(Byte *dest, SizeT *destLen, const Byte *src, SizeT srcLen, const CLzmaEncProps *props,
    Byte *propsEncoded, SizeT *propsSize, int writeEndMark, ICompressProgressPtr progress, ISzAllocPtr alloc,
    ISzAllocPtr allocBig);

EXTERN_C_END

#endif
