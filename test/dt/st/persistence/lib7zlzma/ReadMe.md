



# 版本

23.01

https://ostms.rnd.huawei.com/osmanageweb/#/software-detail?versionCode=0583482175&domain=1&releaseTrain=CleanSource+V8.0&keyword=lzma&newSearch=All&serviceCode=0af6a80e-a81a-11ed-b577-02550c0b34ef



https://www.7-zip.org/a/lzma2301.7z



实体包内网下载地址：

https://cmc.cloudartifact.szv.dragon.tools.huawei.com/artifactory/opensource_general/7-Zip---LZMA-SDK/23.01/package/lzma2301.7z





# 背景

光共进程部署场景，业务注册接口中会注册压缩解压接口，用来对持久化文件做压缩以及解压使用

业务24A是ZLIB压缩，24B之后是LZMA压缩

lzma当前业务提供的有arm32以及suse x86环境下的lzma库，euler环境需要特殊编译。故当前考虑统一在特定方式使用源码编译lzma库。


# 使用

当前仅考虑给到部分共进程用例以及perf用例（后边会切换为共进程部署模式）

可以自行编译
rm -rf build && mkdir build && cd build

当前将C目录下的所有h文件都移动到了inlcude/7zlzma目录下

注：当前上库之后，相关第三方文件做了调整，执行了sh scripts/format.sh做了部分格式化，故相对原始文件有部分差异

