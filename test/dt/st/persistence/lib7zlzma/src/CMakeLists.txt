cmake_minimum_required(VERSION 3.10)
project(lzma23)

set(CMAKE_CXX_STANDARD 11)

include_directories(C)
include_directories(CPP)
include_directories(CPP/7zip/Compress)
include_directories(CPP/7zip/Common)
include_directories(CPP/Windows)

# 安装头文件到 include 目录
install(FILES
    ./C/LzFindMt.h
    ./C/LzmaLib.h
    ./C/7zCrc.h
    ./C/MtCoder.h
    ./C/XzEnc.h
    ./C/Bcj2.h
    ./C/LzmaEnc.h
    ./C/LzmaDec.h
    ./C/Compiler.h
    ./C/Lzma2DecMt.h
    ./C/Sha256.h
    ./C/Ppmd7.h
    ./C/Xz.h
    ./C/RotateDefs.h
    ./C/7zTypes.h
    ./C/CpuArch.h
    ./C/Lzma2Dec.h
    ./C/Lzma2Enc.h
    ./C/Alloc.h
    ./C/Precomp.h
    ./C/SwapBytes.h
    ./C/DllSecur.h
    ./C/MtDec.h
    ./C/Ppmd.h
    ./C/7zFile.h
    ./C/LzFind.h
    ./C/Util/SfxSetup/Precomp.h
    ./C/Util/LzmaLib/Precomp.h
    ./C/Util/Lzma/Precomp.h
    ./C/Util/7z/Precomp.h
    ./C/Threads.h
    ./C/7z.h
    ./C/7zWindows.h
    ./C/Delta.h
    ./C/Bra.h
    ./C/Lzma86.h
    ./C/Aes.h
    ./C/XzCrc64.h
    ./C/Sort.h
    ./C/LzHash.h
    ./C/7zVersion.h
    ./C/7zAlloc.h
    ./C/7zBuf.h
    DESTINATION include
)

add_library(7zlzma SHARED
    C/LzFindMt.c
    C/Alloc.c
    C/LzFind.c
    C/LzFindOpt.c
    C/LzmaDec.c
    C/LzmaEnc.c
    C/LzmaLib.c
    C/Threads.c
)

add_executable(lzma_tool
    C/Util/Lzma/LzmaUtil.c
    C/LzFindMt.c
    C/Alloc.c
    C/LzFind.c
    C/LzFindOpt.c
    C/LzmaDec.c
    C/LzmaEnc.c
    C/LzmaLib.c
    C/Threads.c
    C/7zFile.c
    C/7zStream.c
)
target_link_libraries(lzma_tool PRIVATE pthread)

install(TARGETS 7zlzma DESTINATION lib)
install(DIRECTORY include/ DESTINATION include)

# rm -rf build && mkdir build && cd build
# cmake -DCMAKE_INSTALL_PREFIX=./install/ ..
# make -j8
