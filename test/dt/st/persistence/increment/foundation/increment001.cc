/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-1-6
 */
#include "crash_debug.h"
#include "st_persistence_common.h"
#include "vl_simple.h"
int32_t g_dataNum = 2000;
class Persistence011 : public StStorage {
public:
    Persistence011()
    {}
    virtual ~Persistence011()
    {}
    static void SetUpTestCase()
    {
        system("kill -9 $(pidof gmserver)");
        DbSleep(500);
        system("rm -rf /data/gmdb");
        StartDbServer((char *)"gmserver_incre_persist.ini", "usocket:/run/verona/unix_emserver");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        CreateCSModeSyncConnectionAndStmt(&g_conn, &g_stmt, CLIENT_CONNECT_DEFAULT_TIMEOUT);
    }
    static void TearDownTestCase()
    {
        DestroyConnectionAndStmt(g_conn, g_stmt);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {
        Status ret = GmcCreateVertexLabel(g_stmt, g_normalVertexLabelJson, g_normalLabelConfig);
        ASSERT_EQ(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        Status ret = GmcDropVertexLabel(g_stmt, g_normalLabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
};
// insert不存在的vertex数据
TEST_F(Persistence011, testInsertNonExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, 2000, 0);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)2000, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)2000, scanCount);
}
// insert已存在的vertex数据
TEST_F(Persistence011, testInsertExistData)
{
    int32_t startIndex = 0;
    int32_t count = 2000;
    int32_t coefficient = 0;

    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        int ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
        ret = CheckAffectRows(g_stmt, 0);
        ASSERT_EQ(GMERR_OK, ret);
    }

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)count, scanCount);
}
// update不存在的vertex数据
TEST_F(Persistence011, testUpdateNonExistData)
{
    int ret = VlSimpleUpdateByIndex(g_stmt, 0, 2000, 1, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}
// update已存在的vertex数据
TEST_F(Persistence011, testUpdateExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, 2000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleUpdateByIndex(g_stmt, 0, 2000, 1, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)2000, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)2000, scanCount);
}
// delete不存在的vertex数据
TEST_F(Persistence011, testDeleteNonExistData)
{
    int ret = VlSimpleDeleteByIndex(g_stmt, 0, 2000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}
// delete已存在的vertex数据
TEST_F(Persistence011, testDeleteExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, 2000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, 2000, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}
// replace不存在的vertex数据
TEST_F(Persistence011, testReplaceNonExistData)
{
    int ret = VlSimpleReplace(g_stmt, 0, 2000, 1);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)2000, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)2000, scanCount);
}
// replace已存在的vertex数据
TEST_F(Persistence011, testReplaceExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, 2000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 0~1000: replace完全一样的数据
    ret = VlSimpleReplace(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 1000~2000: replace主键一样但其它属性不同的数据
    ret = VlSimpleReplace(g_stmt, 1000, 1000, 1);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 1000, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)1000, scanCount);
    ret = VlSimpleScanByIndex(g_stmt, 1000, 1000, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)1000, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)2000, scanCount);
}
// merge不存在的vertex数据
TEST_F(Persistence011, testMergeNonExistData)
{
    int ret = VlSimpleMerge(g_stmt, 0, 2000, 1);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 2000, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)2000, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)2000, scanCount);
}
// merge已存在的vertex数据
TEST_F(Persistence011, testMergeExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, 2000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 0~1000: replace完全一样的数据
    ret = VlSimpleMerge(g_stmt, 0, 1000, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 1000~2000: replace主键一样但其它属性不同的数据
    ret = VlSimpleMerge(g_stmt, 1000, 1000, 1);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, 1000, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)1000, scanCount);
    ret = VlSimpleScanByIndex(g_stmt, 1000, 1000, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)1000, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)2000, scanCount);
}
// 批量insert不存在的vertex数据
TEST_F(Persistence011, testBatchInsertNonExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    int ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)count, scanCount);
}
// 批量insert已存在的vertex数据
TEST_F(Persistence011, testBatchInsertExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;
    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)0, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, g_dataNum, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}
// 批量update不存在的vertex数据
TEST_F(Persistence011, testBatchUpdateNonExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetIndexKey(g_stmt, i, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleSetDeltaProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}

// 批量update已存在的vertex数据
TEST_F(Persistence011, testBatchUpdateExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;
    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetIndexKey(g_stmt, i, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleSetDeltaProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}

// 批量delete不存在的vertex数据
TEST_F(Persistence011, testBatchDeleteNonExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetIndexKey(g_stmt, i, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, g_dataNum, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}

// 批量delete已存在的vertex数据
TEST_F(Persistence011, testBatchDeleteExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;
    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetIndexKey(g_stmt, i, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, g_dataNum, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)(g_dataNum - count), scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)(g_dataNum - count), scanCount);
}

// 批量replace不存在的vertex数据
TEST_F(Persistence011, testBatchReplaceNonExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)count, scanCount);
}

// 批量replace已存在的vertex数据
TEST_F(Persistence011, testBatchReplaceExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;
    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}

// 批量merge不存在的vertex数据
TEST_F(Persistence011, testBatchMergeNonExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetIndexKey(g_stmt, i, "PrimaryKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleSetDeltaProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)count, scanCount);
}

// 批量merge已存在的vertex数据
TEST_F(Persistence011, testBatchMergeExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;
    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    coefficient = 1;
    for (int32_t i = startIndex; i < startIndex + count; i++) {
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            count = i - startIndex;
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint32_t)count, totalNum);
    ASSERT_EQ((uint32_t)count, successNum);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, startIndex, count, coefficient, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)count, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}
// 开启事务，insert不存在的vertex数据
TEST_F(Persistence011, testTrxInsertNonExistData)
{
    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    int ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, g_dataNum, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}
// 开启事务，insert已存在的vertex数据
TEST_F(Persistence011, testTrxInsertExistData)
{
    int32_t startIndex = 0;
    int32_t count = g_dataNum;
    int32_t coefficient = 0;

    int ret = VlSimpleInsert(g_stmt, startIndex, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t i = startIndex; i < startIndex + count; i++) {
        int ret = GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = VlSimpleSetAllProperty(g_stmt, i, coefficient);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (i == startIndex) {
            ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
        } else {
            ASSERT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
        }
        ret = CheckAffectRows(g_stmt, 0);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = GmcTransRollBack(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}

// 开启事务，update不存在的vertex数据
TEST_F(Persistence011, testTrxUpdateNonExistData)
{
    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    int ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleUpdateByIndex(g_stmt, 0, g_dataNum, 1, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}

// 开启事务，update已存在的vertex数据
TEST_F(Persistence011, testTrxUpdateExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, g_dataNum, 0);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleUpdateByIndex(g_stmt, 0, g_dataNum, 1, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}

// 开启事务，delete不存在的vertex数据
TEST_F(Persistence011, testTrxDeleteNonExistData)
{
    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    int ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, g_dataNum, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}

// 开启事务，delete已存在的vertex数据
TEST_F(Persistence011, testTrxDeleteExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, g_dataNum, 0);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleDeleteByIndex(g_stmt, 0, g_dataNum, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)0, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)0, scanCount);
}

// 开启事务，replace不存在的vertex数据
TEST_F(Persistence011, testTrxReplaceNonExistData)
{
    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    int ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleReplace(g_stmt, 0, g_dataNum, 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}

// 开启事务，replace已存在的vertex数据
TEST_F(Persistence011, testTrxReplaceExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, g_dataNum, 0);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);

    // 0~1000: replace完全一样的数据
    ret = VlSimpleReplace(g_stmt, 0, g_dataNum / 2, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 1000~g_dataNum: replace主键一样但其它属性不同的数据
    ret = VlSimpleReplace(g_stmt, g_dataNum / 2, g_dataNum / 2, 1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum / 2, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)(g_dataNum / 2), scanCount);
    ret = VlSimpleScanByIndex(g_stmt, g_dataNum / 2, g_dataNum / 2, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)(g_dataNum / 2), scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)(g_dataNum), scanCount);
}

// 开启事务，merge不存在的vertex数据
TEST_F(Persistence011, testTrxMergeNonExistData)
{
    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    int ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleMerge(g_stmt, 0, g_dataNum, 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}

// 开启事务，merge已存在的vertex数据
TEST_F(Persistence011, testTrxMergeExistData)
{
    int ret = VlSimpleInsert(g_stmt, 0, g_dataNum, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 0~1000: replace完全一样的数据

    GmcTxConfigT txCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_COMMITTED,
        .trxType = GMC_DEFAULT_TRX,
    };
    ret = GmcTransStart(g_conn, &txCfg);
    ASSERT_EQ(GMERR_OK, ret);

    ret = VlSimpleMerge(g_stmt, 0, g_dataNum / 2, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 1000~g_dataNum: replace主键一样但其它属性不同的数据
    ret = VlSimpleMerge(g_stmt, g_dataNum / 2, g_dataNum / 2, 1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, 0, g_dataNum / 2, 0, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)(g_dataNum / 2), scanCount);
    ret = VlSimpleScanByIndex(g_stmt, g_dataNum / 2, g_dataNum / 2, 1, "PrimaryKey", &scanCount);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((uint64_t)(g_dataNum / 2), scanCount);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt, g_normalLabelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(g_stmt, &scanCount));
    ASSERT_EQ((uint64_t)g_dataNum, scanCount);
}
