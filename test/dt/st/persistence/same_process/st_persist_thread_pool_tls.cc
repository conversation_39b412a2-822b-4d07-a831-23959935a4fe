/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: persist thread pool test
 * Create: 2023-12-4
 */

#include <sys/socket.h>
#include <sys/epoll.h>
#include "storage_st_common.h"
#include "st_persistence_common.h"
#include "st_common.h"
#include "gmc_errno.h"
#include "incre_persist_table.h"
#include "db_thread_pool_tls.h"
#include "ee_pubsub_merge.h"
#include "dm_meta_namespace.h"
#include "dm_meta_log.h"
#include "dm_meta_basic.h"
#include "dm_meta_basic_in.h"

class StPersistThreadPoolTls : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    virtual void SetUp()
    {
        system("rm -rf temp_gmserver_incre_persist_thread_pool.ini");
        StModifyConfig("gmserver_incre_persist_thread_pool.ini", "temp_gmserver_incre_persist_thread_pool.ini",
            "\"featureNames=MEMDATA,DURABLEMEMDATA,FASTPATH,TRM,PERSISTENCE,YANG\"");
    }
    virtual void TearDown()
    {
        system("rm -rf temp_gmserver_incre_persist_thread_pool.ini");
    }
};

void TmpSwapFunc(void *tmpTlsValue)
{
    DbSetReserverTls(tmpTlsValue);
}

const uint32_t tmpReserve = 1;
Status QryInitStMgSubDataSetStubC(QryStMgSubDataSetT *subDataSet, DbMemCtxT *memCtx)
{
    DB_POINTER2(subDataSet, memCtx);
    // 模拟资源申请时注册线程变量
    DbRegisterTlsSwapFunc(TLS_TYPE_RESERVE, (void *)&tmpReserve, TmpSwapFunc);

    DbCreateList(&subDataSet->subDataList, sizeof(QryStMgSubDataT *), memCtx);
    DbCreateList(&subDataSet->subData.insertedPubSubNodeList, sizeof(ShmemPtrT), memCtx);
    DbCreateList(&subDataSet->subData.updatedPubSubNodeList, sizeof(ShmemPtrT), memCtx);
    DbCreateList(&subDataSet->labelIds, sizeof(uint32_t), memCtx);
    subDataSet->subData.vertexLabel = NULL;
    subDataSet->subData.statusMergeListEntry = DB_INVALID_SHMPTR;
    subDataSet->memCtx = memCtx;
    return GMERR_OK;
}

static const char *g_nweLabelJson = R"([{
    "type": "record",
    "name": "dlr_label",
    "fields": [
        { "name": "F1", "type": "uint32", "nullable": false },
        { "name": "F2", "type": "string", "nullable": false, "size": 32 }
    ],
    "keys": [
        {
            "node": "dlr_label",
            "name": "PK",
            "fields": ["F1"],
            "index": { "type": "primary" },
            "constraints": { "unique": true }
        }
    ]
}])";

void *g_tlsReserver = NULL;
Status BasicCheckLabelForCreateStubC(DbInstanceHdT dbInstance, const DmMetaCommonT *metaCommon)
{
    // 读取当前gmserver的线程变量g_threadReserveTls
    void *currReserver = DbGetReserveTls();
    g_tlsReserver = currReserver;

    // namespace是否存在
    if (SECUREC_UNLIKELY(!IsNamespaceIdExist(dbInstance, metaCommon->namespaceId))) {
        CATA_LASTERR(GMERR_UNDEFINED_OBJECT, "Unable to create label because the namespace is not exist.",
            "NamespaceId is: %" PRIu32 ". Label is %s.", metaCommon->namespaceId, metaCommon->metaName);
        return GMERR_UNDEFINED_OBJECT;
    }
    // tablespace是否存在
    if (SECUREC_UNLIKELY(!IsTablespaceIdExistNoLock(dbInstance, metaCommon->tablespaceId))) {
        CATA_LASTERR(GMERR_UNDEFINED_OBJECT, "Unable to create label because the tablespace is not exist.",
            "Label is %s.", metaCommon->metaName);
        return GMERR_UNDEFINED_OBJECT;
    }
    return GMERR_OK;
}

void TestTlsRegisterAndSwapFunc()
{
    init();
    SetClientServerSameProcess();
    (void)setStubC((void *)QryInitStMgSubDataSet, (void *)QryInitStMgSubDataSetStubC);
    (void)setStubC((void *)BasicCheckLabelForCreate, (void *)BasicCheckLabelForCreateStubC);
    system("rm -rf /data/gmdb/*");
    StartDbServer(
        (char *)"temp_gmserver_incre_persist_thread_pool.ini", "usocket:/run/verona/unix_emserver", true, false);
    st_clt_init();

    // 建连接，在建连时通过对函数QryInitStMgSubDataSet打桩，将变量tmpReserve的地址注册给了线程变量g_threadReserveTls
    GmcConnT *conn = NULL;
    GmcConnOptionsT *connOptions;
    Status ret = GmcConnOptionsCreate(&connOptions);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    ASSERT_EQ(GMERR_OK, ret);
    GmcStmtT *stmt = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 对BasicCheckLabelForCreate函数打桩，预期g_tlsReserver取到的还是线程变量的初始空值
    ASSERT_EQ(NULL, g_tlsReserver);

    // 建表，这时会走到 SmScheProcWrapper，对线程变量进行切换
    const char *configJsons = R"({"isFastReadUncommitted":false})";
    ret = GmcCreateVertexLabel(stmt, g_nweLabelJson, configJsons);
    ASSERT_EQ(GMERR_OK, ret);

    // 此时预期已经走过线程变量的注册与切换，所以这里g_tlsReserver预期取到的是tmpReserve的地址
    ASSERT_EQ(&tmpReserve, g_tlsReserver);

    st_clt_uninit();
    clearAllStub();
    exit(ST_EXIT_SUCCESS);
}

// 验证线程池模式下是不是能够执行线程变量切换
TEST_F(StPersistThreadPoolTls, TestTlsRegisterAndSwap)
{
    ASSERT_EXIT(TestTlsRegisterAndSwapFunc(), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}
