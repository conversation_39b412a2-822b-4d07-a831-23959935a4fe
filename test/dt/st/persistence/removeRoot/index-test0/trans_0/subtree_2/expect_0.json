{"if:interface.1": [{"name": "ethernetCsmacd.0.9.0", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "default-vlan": 1, "ethernet": {"duplex": "full", "speed": 10.0, "auto-negotiation": {"enable": false}, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}, "interface-usage": {"interface-usage": "network-port"}, "mac-learning": {"@max-number-mac-addresses": 4294967295, "@number-committed-mac-addresses": 1, "@mac-learning-enable": true, "@mac-learning-failure-action": "forward"}, "qos-policies": {"policing": {"statistics": {"@enabled": false}}}, "bridge-port": {"default-priority": 0, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}}, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.********.eth.1"}]}, {"name": "channel-group.0.16.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "port.********.eth.1", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "ethernet": {"@duplex": "full", "auto-negotiation": {"@enable": true}, "flow-control": {"@force-flow-control": false}, "logical": {"tx-auto-off": {"@enabled": false}}, "ethernet-frame": {"@jumbo-frame": false, "@mtu": 2052}}, "mac-learning": {"@max-number-mac-addresses": 4294967295, "@number-committed-mac-addresses": 1, "@mac-learning-enable": true, "@mac-learning-failure-action": "forward"}, "qos-policies": {"policing": {"statistics": {"@enabled": false}}}, "bridge-port": {"@default-priority": 0, "@pcp-selection": "8P0D", "@use-dei": false, "@drop-encoding": false, "@service-access-priority-selection": false, "priority-regeneration": {"@priority0": 0, "@priority1": 1, "@priority2": 2, "@priority3": 3, "@priority4": 4, "@priority5": 5, "@priority6": 6, "@priority7": 7}, "service-access-priority": {"@priority0": 0, "@priority1": 1, "@priority2": 2, "@priority3": 3, "@priority4": 4, "@priority5": 5, "@priority6": 6, "@priority7": 7}}, "aggregator": {"@work-mode": "static", "@fast-period": 1, "@slow-period": 30, "@max-link-number": "no-limit", "@least-link-number": "no-limit", "@forward-mode": "ingress", "@preempt-enabled": false, "@preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"@actor-port-priority": 16384}}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.********.eth.1"}]}, {"name": "channel-pair.0.16.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.15", "channel-partition-ref": "channel-partition.0.16.15", "channel-pair-type": "bbf-xpon-types:gpon"}}]}