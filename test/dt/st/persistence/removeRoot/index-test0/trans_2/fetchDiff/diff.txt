ietf-interfaces:interfaces:update[(p<PERSON><PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
ietf-interfaces:interfaces.if:interface.1:remove[(NULL),(pri<PERSON><PERSON>(PID:1,name:channel-group.0.168.1), pre<PERSON>ey(PID:1,name:channel-pair.0.16.15.gpon))]
if:interface.1.ID:remove(6)
if:interface.1.type:remove(bbf-xpon-if-type:channel-group)
if:interface.1.enabled:remove(true)
if:interface.1.channel-group:remove
channel-group.polling-period:remove(100)
ietf-interfaces:interfaces.if:interface.1:update[(pri<PERSON><PERSON>(PID:1,name:channel-pair.0.168.1.gpon), pre<PERSON>ey(PID:1,name:channel-pair.0.16.15.gpon)),(pri<PERSON><PERSON>(PID:1,name:channel-pair.0.168.1.gpon), pre<PERSON><PERSON>(PID:1,name:channel-group.0.168.1))]
