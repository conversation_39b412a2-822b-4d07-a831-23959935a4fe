{"op": "merge", "interfaces": {"op": "merge", "if:interface.1": [{"name": "ethernetCsmacd.0.10.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "ethernet": {"duplex": "full", "speed": 10.0, "route-switch": "unconcern", "auto-negotiation": {"enable": false}, "flow-control": {"force-flow-control": false}, "physical": {"mdi": "not-supported", "port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}, "qos-policies": {"policing": {"statistics": {"enabled": false}}}, "interface-usage": {"interface-usage": "network-port"}, "mac-learning": {"max-number-mac-addresses": 4294967295, "number-committed-mac-addresses": 1, "mac-learning-enable": true, "mac-learning-failure-action": "forward"}, "bridge-port": {"default-priority": 0, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}}, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}}]}}