[{"type": "record", "name": "T_DST", "config": {"persistent": true}, "fields": [{"name": "F0", "type": "uint64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "uint64", "nullable": false}, {"name": "F3", "type": "uint64", "nullable": true}], "keys": [{"node": "T_DST", "name": "T_DST_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]