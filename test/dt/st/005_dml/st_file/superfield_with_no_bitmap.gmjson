[{"type": "record", "name": "T40Super", "fields": [{"name": "F0", "type": "char", "default": "a", "nullable": false}, {"name": "F1", "type": "uchar"}, {"name": "F2", "type": "uchar", "default": "f", "nullable": false}, {"name": "F3", "type": "bitmap", "size": 1024}], "super_fields": [{"name": "superfield0", "comment": "test", "fields": {"begin": "F1", "end": "F2"}}], "keys": [{"node": "T40Super", "name": "T40_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]