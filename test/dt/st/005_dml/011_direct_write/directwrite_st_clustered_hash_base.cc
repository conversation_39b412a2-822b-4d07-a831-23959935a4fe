/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: directwrite_st_clustered_hash_base.cc
 * Description: testcase for direct write clustered hash
 * Author: zhangjinglong
 * Create: 2023-04-26
 */
#include "gmc_graph.h"
#include "gmc_types.h"
#include "adpt_define.h"
#include "storage_st_common.h"
#include "db_perf_stat.h"
#include "tools_st_common.h"
#include "storage_st_util.h"
#include "client_common_st.h"
#include "gmc_batch_check.h"
#include "gmc_graph_check.h"
#include "clt_da_write.h"
#include "direct_write_common.h"

static DbMemCtxT *alterMemCtx = NULL;

#define PERF_SAMPLE_STAT 1
// 测试记录数为 100000 时测试也已通过
static const uint32_t MAX_RECORD_COUNT = 5000;
static const uint32_t MAX_CYCLE_COUNT = 100;
static const char *g_labelName = "ip4forward";
static const char *g_labelName2 = "ip4forward_test";
static const char *g_testLabelName = "testT";
static const char *g_generalComplexLabel = "ip4forward_general_complex";
static const char *g_generalComplexLabel2 = "ip4forward_general_complex_test";
static const int MAX_CMD_SIZE = 1024u;
static const uint32_t CONCURRENCY_CYCLE_NUM = 6;
static const uint32_t CONCURRENCY_MAX_RECORD_COUNT = 10000;
static const uint32_t CONCURRENCY_SLEEP_US_NUM = 100;

#pragma pack(1)
typedef struct TagIp4forwardDataT {
    uint32_t f1;
    uint32_t f2;
    int32_t f3;
    uint32_t f4;
    char a1[32];
    char a2[32];
    char a3[32];
    char a4[32];
    char a5[32];
    char a6[32];
} Ip4forwardDataT;
#pragma pack()

class StClientDwClusteredHash : public StStorage {
public:
    StClientDwClusteredHash()
    {
        DbMemCtxArgsT args = {0};
        // client st 会初始化g_gmdbCltInstance.cltCommCtx，这里直接使用
        alterMemCtx =
            DbCreateDynMemCtx(g_gmdbCltInstance.cltCommCtx, false, "StClientStructDirectWriteSpecial st", &args);
    }

    ~StClientDwClusteredHash() override
    {
        // 统一回收中间申请的内存
        DbDeleteDynMemCtx(alterMemCtx);
    }

protected:
    static void SetUpTestCase()
    {
        DbInitCPUFrequency();
        DbRdtscInit();
        StServerClientPrepare("\"enableClusterHash=1\" \"directWrite=1\"", &epollThreadId);
        GmcSignalRegisterNotify();
    }
    static void TearDownTestCase()
    {
        StServerClientExit(&epollThreadId);
    }
    void SetUp()
    {
        CreateSyncConnectionAndStmt(&dwConn, &dwStmt);
        system("gmimport -c vschema -t ip4forward -f ./st_file/ip4forward.gmjson");
        system("gmimport -c vschema -t testT -f ./st_file/test.gmjson");
    }
    void TearDown()
    {
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_labelName));
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, "testT"));
        DestroyConnectionAndStmt(dwConn, dwStmt);
        SaveLogAfterFailed();
    }
    GmcConnT *dwConn = NULL;
    GmcStmtT *dwStmt = NULL;
};

static int32_t ChInsertVertexDataRange(GmcStmtT *stmt, uint32_t startVal, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = startVal; i < startVal + recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    return ChInsertVertexDataRange(stmt, 0, recordCount, labelName);
}

static int32_t ChInsertVertexDataFull(GmcStmtT *stmt, const char *labelName, uint32_t *maxTupleNum)
{
    int ret;
    uint32_t maxTupleCount = 0;
    for (uint32_t i = 0; i < DB_MAX_UINT32; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            *maxTupleNum = maxTupleCount;
            return ret == GMERR_OUT_OF_MEMORY ? GMERR_OK : ret;
        }
        maxTupleCount++;
    }
    *maxTupleNum = maxTupleCount;
    return GMERR_OK;
}

// 给label2写数据
static int32_t ChInsertSimpleVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertAndRollbackVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, int *cnt)
{
    int ret;
    int violationCnt = 0;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i % (MAX_CYCLE_COUNT * 2);
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {  // 主键冲突后回滚
            violationCnt++;
            continue;
        } else if (ret != GMERR_OK) {
            return ret;
        }
    }
    *cnt = violationCnt;
    return GMERR_OK;
}

static int32_t ChReplaceVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue)
{
    for (uint32_t i = 0; i < recordCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChReplaceVertexDataWithPerfFiledsCheck(
    GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue)
{
    for (uint32_t i = 0; i < recordCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);

        EXPECT_TRUE(stmt->lastOpStatus.lastOpDirectWrite);
        if (i > 0) {
            EXPECT_TRUE(stmt->vlUseDirectWrite);
        }
    }
    return GMERR_OK;
}

static int32_t ChReplaceVertexData4Cs(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue)
{
    for (uint32_t i = 0; i < recordCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChReplaceVertexDataV2(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i + 1;
        int32_t f3 = i + 2;
        uint32_t f4 = i + 3;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static void DirectReadCompareResultV2(GmcStmtT *stmt, uint32_t f1Value)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f1 = f1Value;
    uint32_t f2 = f1Value + 1;
    int32_t f3 = f1Value + 2;
    uint32_t f4 = f1Value + 3;
    uint32_t f1Res;
    uint32_t f2Res;
    int32_t f3Res;
    uint32_t f4Res;

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Res, propSize, &isNull));
    EXPECT_EQ(f1, f1Res);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F2", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &f2Res, propSize, &isNull));
    EXPECT_EQ(f2, f2Res);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F3", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F3", &f3Res, propSize, &isNull));
    EXPECT_EQ(f3, f3Res);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F4", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F4", &f4Res, propSize, &isNull));
    EXPECT_EQ(f4, f4Res);
}

static int32_t ChPkReadVertexDataV2(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordCount; i++) {
        uint32_t pKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pKValue, sizeof(pKValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        EXPECT_EQ(GMERR_OK, ret);
        DirectReadCompareResultV2(stmt, pKValue);
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexData(GmcStmtT *stmt, uint32_t recordCount, uint32_t step, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i += step) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2) + 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexData2(
    GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue, int32_t delta)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - delta;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexDataBySecIdx(
    GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue, int32_t delta)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(stmt->openDirectWrite);
        uint32_t f2 = keyValue;
        int32_t f3 = (int32_t)i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_vrfid"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexDataBySecIdx(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f3 = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_vrfid"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexData24Cs(
    GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue, int32_t delta)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - delta;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(DELETE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(DELETE_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexData4Cs(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(DELETE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(DELETE_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static void DirectReadCompareResult(GmcStmtT *stmt, uint32_t f1Value)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f1 = f1Value;
    uint32_t f2 = f1Value % MAX_CYCLE_COUNT;
    int32_t f3 = f1Value - 2;
    uint32_t f4 = f1Value % (MAX_CYCLE_COUNT * 2);
    uint32_t f1Res;
    uint32_t f2Res;
    int32_t f3Res;
    uint32_t f4Res;

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Res, propSize, &isNull));
    EXPECT_EQ(f1, f1Res);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F2", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &f2Res, propSize, &isNull));
    EXPECT_EQ(f2, f2Res);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F3", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F3", &f3Res, propSize, &isNull));
    EXPECT_EQ(f3, f3Res);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F4", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F4", &f4Res, propSize, &isNull));
    EXPECT_EQ(f4, f4Res);
}

static void DirectScanCompareResult(GmcStmtT *stmt)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f2Expect;
    int32_t f3Expect;
    uint32_t f4Expect;

    uint32_t f1Result;
    uint32_t f2Result;
    int32_t f3Result;
    uint32_t f4Result;

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Result, propSize, &isNull));

    f2Expect = f1Result % MAX_CYCLE_COUNT;
    f3Expect = f1Result - 2;
    f4Expect = f1Result % (MAX_CYCLE_COUNT * 2);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F2", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &f2Result, propSize, &isNull));
    EXPECT_EQ(f2Expect, f2Result);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F3", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F3", &f3Result, propSize, &isNull));
    EXPECT_EQ(f3Expect, f3Result);

    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F4", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F4", &f4Result, propSize, &isNull));
    EXPECT_EQ(f4Expect, f4Result);
}

static int32_t ChReadVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordCount; i++) {
        uint32_t pKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pKValue, sizeof(pKValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(PK_READ_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(PK_READ_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        EXPECT_EQ(GMERR_OK, ret);
        DirectReadCompareResult(stmt, i);
    }
    return GMERR_OK;
}

static int32_t DirectReadByFullScan(GmcStmtT *stmt, int32_t expectCnt, const char *labelName)
{
    bool eof;
    int32_t recordCnt = 0;
    int32_t ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        DB_START_TEST_CPU_CYCLES(DIRECT_SCAN);
        ret = GmcFetch(stmt, &eof);
        DB_STOP_TEST_CPU_CYCLES(DIRECT_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof) {
            break;
        }
        DirectScanCompareResult(stmt);
        recordCnt++;
    }
    EXPECT_EQ(expectCnt, recordCnt);
    return GMERR_OK;
}

static int32_t ExtractPrimaryKeyAndDelete(GmcStmtT *stmt, const char *labelName)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f1Result;

    int32_t ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Result, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1Result, sizeof(propSize)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
    return GmcExecute(stmt);
}

static int32_t DirectReadScanAndDeleteByKey(GmcStmtT *stmt, int32_t expectCnt, const char *labelName)
{
    bool eof;
    int32_t recordCnt = 0;
    while (true) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ExtractPrimaryKeyAndDelete(stmt, labelName));
        recordCnt++;
    }
    EXPECT_EQ(expectCnt, recordCnt);
    return GMERR_OK;
}

static Status ChSeriPrimaryKey(void *seri, uint8_t *keyBuf, GmcStructureResvT *reservedSize)
{
    uint8_t *bits = keyBuf;
    *bits = 0xff;  // 空值标识符 参考代码SetKeyBufByFixedPropeValues
    keyBuf += 1;
    errno_t err = memcpy_s(keyBuf, sizeof(uint32_t), (uint8_t *)((GmcSeriT *)seri)->obj, sizeof(uint32_t));
    DB_ASSERT(err == EOK);
    return GMERR_OK;
}

static int32_t ChReadStructVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    Ip4forwardDataT obj;
    Ip4forwardDataT objRes;
    GmcSeriT serStr;
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    serStr.seriFunc = ChSeriPrimaryKey;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.bufSize = sizeof(uint32_t) + 1;
    serStr.userData = stmt;

    uint32_t fixedLen = sizeof(Ip4forwardDataT);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);

    GmcStructBufferT inputBufInfo = {(uint8_t *)&objRes, fixedLen, 1 + newSize};
    for (uint32_t i = 0; i < recordCount; ++i) {
        obj.f1 = i;
        DB_START_TEST_CPU_CYCLES(STRUCT_READ_VERTEX);
        ret = GmcGetVertexBuf(stmt, 0, &serStr, &inputBufInfo);
        DB_STOP_TEST_CPU_CYCLES(STRUCT_READ_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(objRes.f1, i);
        EXPECT_EQ(objRes.f2, i % MAX_CYCLE_COUNT);
        EXPECT_EQ(objRes.f3, (int32_t)(i - 2));
        EXPECT_EQ(objRes.f4, (uint32_t)(i % (MAX_CYCLE_COUNT * 2)));
    }
    return GMERR_OK;
}

static int32_t ChSecondIndexDelete(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_hashcluster"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUniqueSecondIndexCheck(GmcStmtT *stmt, uint32_t primKeyValue, int32_t secIdxValue)
{
    int32_t f3 = secIdxValue;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_vrfid"));
    int32_t ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isFinished;
    ret = GmcFetch(stmt, &isFinished);
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull;
    uint32_t propSize;
    uint32_t f1Res;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Res, propSize, &isNull));
    EXPECT_EQ(primKeyValue, f1Res);
    return GMERR_OK;
}

static uint32_t ChSecondIndexScanDataCheck(GmcStmtT *stmt)
{
    Status ret;
    uint32_t fetchCount = 0;
    while (fetchCount < MAX_RECORD_COUNT) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        fetchCount++;
    }
    return fetchCount;
}

static int32_t ChSecondIndexScan(GmcStmtT *stmt, const char *labelName, uint32_t keyValue, uint32_t count)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f2 = keyValue;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_hashcluster"));
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t scanCount = ChSecondIndexScanDataCheck(stmt);
    EXPECT_EQ(count, scanCount);
    return GMERR_OK;
}

static int32_t ChUniqueSecondIndexScan(GmcStmtT *stmt, uint32_t count, const char *labelName, int32_t delta)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < count; i++) {
        EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexCheck(stmt, i, i - delta));
    }
    return GMERR_OK;
}

static void StCheckHcWriteBytesAndDeleteBytes(uint32_t writeRowCnt, uint32_t deleteRowCnt, const char *filter)
{
    // 获取rowSize，用来校验
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter, cmdOutput1, 64);
    uint32_t rowSize = atoi(cmdOutput1);

    // 校验writeBytes
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "WRITE_BYTES", filter, cmdOutput2, 64);
    uint32_t writeBytes = atoi(cmdOutput2);
    uint32_t writeTotalCheck = rowSize * writeRowCnt;
    EXPECT_EQ(writeBytes, writeTotalCheck);

    // 校验deleteBytes
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResultFilter("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "DELETE_BYTES", filter, cmdOutput3, 64);
    uint32_t deleteBytes = atoi(cmdOutput3);
    uint32_t deleteTotalCheck = rowSize * deleteRowCnt;
    EXPECT_EQ(deleteBytes, deleteTotalCheck);
}

// 聚簇容器适配视图SERVER_MEMORY_OVERHEAD
static void StCheckHcOriginalDataSize(uint32_t writeRowCnt, uint32_t deleteRowCnt, const char *filter)
{
    // 1.获取rowSize，用来校验
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter, cmdOutput1, 64);
    uint32_t rowSize = atoi(cmdOutput1);

    // 2.校验视图SERVER_MEMORY_OVERHEAD中的ORIGINAL_DATA_SIZE字段（初始的数据大小，大小为deleteBytes-writeBytes）
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize = atoi(cmdOutput2);
    uint64_t checkSize = (writeRowCnt - deleteRowCnt) * rowSize;
    EXPECT_EQ(originalDataSize, checkSize);
}

// 创多个表，聚簇容器适配视图SERVER_MEMORY_OVERHEAD
static void StCheckHcOriginalDataSizeWithTwoTables(
    uint32_t writeRowCnt, uint32_t deleteRowCnt, const char *filter1, const char *filter2)
{
    // 1.获取rowSize1，用来校验
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter1, cmdOutput1, 64);
    uint32_t rowSize1 = atoi(cmdOutput1);

    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter2, cmdOutput2, 64);
    uint32_t rowSize2 = atoi(cmdOutput2);

    // 2.校验视图SERVER_MEMORY_OVERHEAD中的ORIGINAL_DATA_SIZE字段
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput3, 64);
    uint64_t originalDataSize = atoi(cmdOutput3);
    uint64_t checkSize = (writeRowCnt - deleteRowCnt) * (rowSize1 + rowSize2);
    EXPECT_EQ(originalDataSize, checkSize);
}

TEST_F(StClientDwClusteredHash, testGetKeyCount)
{
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));

    // 一条记录大小219，一共插入500条
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0

    // 获取全表记录数
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
}

TEST_F(StClientDwClusteredHash, testInsert)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));

    system("gmsysview -q V\\$STORAGE_MEMDATA_STAT |grep -A 15 INSTANCE_ID");
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
}

TEST_F(StClientDwClusteredHash, testInsertFull)
{
    system("gmimport -c vschema -t ip4forward_big -f ./st_file/ip4forward_big.gmjson");
    const char *labelName = "ip4forward_big";
    uint32_t firstMaxTupleNum = 0;
    EXPECT_EQ(GMERR_OK, ChInsertVertexDataFull(dwStmt, labelName, &firstMaxTupleNum));

    GmcConnT *trunConn = NULL;
    GmcStmtT *trunStmt = NULL;
    AllocConnAndStmt(&trunConn, &trunStmt);
    EXPECT_EQ(GMERR_OK, GmcTruncateVertexLabel(trunStmt, labelName));
    FreeConnAndStmt(trunConn, trunStmt);

    GmcConnT *csConn = NULL;
    GmcStmtT *csStmt = NULL;
    AllocConnAndStmt(&csConn, &csStmt);
    uint32_t csMaxTupleNum = 0;
    EXPECT_EQ(GMERR_OK, ChInsertVertexDataFull(csStmt, labelName, &csMaxTupleNum));
    FreeConnAndStmt(csConn, csStmt);

    // 第一次写满存储内存的 tuple 数量少于或等于后续写满存储的 tuple 数量
    EXPECT_LE(firstMaxTupleNum, csMaxTupleNum);

    AllocConnAndStmt(&trunConn, &trunStmt);
    EXPECT_EQ(GMERR_OK, GmcTruncateVertexLabel(trunStmt, labelName));
    FreeConnAndStmt(trunConn, trunStmt);

    uint32_t dwMaxTupleNum = 0;
    EXPECT_EQ(GMERR_OK, ChInsertVertexDataFull(dwStmt, labelName, &dwMaxTupleNum));
    EXPECT_EQ(dwMaxTupleNum, csMaxTupleNum);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, labelName));
}

TEST_F(StClientDwClusteredHash, testReplace)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    EXPECT_EQ(GMERR_OK, ChReplaceVertexDataWithPerfFiledsCheck(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT / 2, g_labelName, 1));
    EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));
    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));
}

TEST_F(StClientDwClusteredHash, testInsertOrReplaceErrorNoPrimaryKey)
{
    int ret = GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f2 = 66 % MAX_CYCLE_COUNT;
    int32_t f3 = 66 - 2;
    uint32_t f4 = 66 % (MAX_CYCLE_COUNT * 2);
    ret = GmcSetVertexProperty(dwStmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(dwStmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(dwStmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(dwStmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    ret = GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(dwStmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
}

TEST_F(StClientDwClusteredHash, testUpdate)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    int32_t delta = 3;
    EXPECT_EQ(GMERR_OK, ChUpdateVertexData2(dwStmt, MAX_RECORD_COUNT / 2, g_labelName, 1, delta));
    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, (uint32_t)(MAX_RECORD_COUNT / 2), g_labelName, delta));
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));
}

TEST_F(StClientDwClusteredHash, testPKDirectRead)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 直连读读取数据并进行比较
    EXPECT_EQ(GMERR_OK, ChReadVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
}

TEST_F(StClientDwClusteredHash, testDelete)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ((uint64_t)MAX_RECORD_COUNT, count);
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 删除数据
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ((uint64_t)0, count);
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, MAX_RECORD_COUNT, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, MAX_RECORD_COUNT, filter);
}

TEST_F(StClientDwClusteredHash, testStructRead)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 结构化直连读数据
    EXPECT_EQ(GMERR_OK, ChReadStructVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput1, 64);
    uint64_t expansionRatio1 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio1, 0u);  // 校验expansionRatio>0
}

TEST_F(StClientDwClusteredHash, TestFullScan)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 全表扫描记录
    EXPECT_EQ(GMERR_OK, DirectReadByFullScan(dwStmt, MAX_RECORD_COUNT, g_labelName));
}

// 直连写暂不支持基于二级索引 delete
// 修改后，直连写不支持的场景会自动通过C/S路径写入，不再报错，先DISABLE掉
TEST_F(StClientDwClusteredHash, testSecondIndexDelete)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0

    // 根据二级索引删除，不支持
    EXPECT_EQ(GMERR_OK, ChSecondIndexDelete(dwStmt, MAX_RECORD_COUNT, g_labelName));
}

TEST_F(StClientDwClusteredHash, testSecondIndexScan)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT / MAX_CYCLE_COUNT)));
}

TEST_F(StClientDwClusteredHash, testInsertAndRollback)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入数并回滚
    int cnt = -1;
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, ChInsertAndRollbackVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, &cnt));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT - cnt, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT - cnt, 0, filter);
    GmcResetStmt(dwStmt);
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ((uint64_t)MAX_CYCLE_COUNT * 2, count);

    // 校验膨胀率
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput1, 64);
    uint64_t expansionRatio1 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio1, 0u);  // 校验expansionRatio>0
    // 删5条数据
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(dwStmt, 5, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT - cnt, 5, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT - cnt, 5, filter);
    GmcResetStmt(dwStmt);
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ((uint64_t)MAX_CYCLE_COUNT * 2 - 5, count);
    // 校验膨胀率
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput2, 64);
    uint64_t expansionRatio2 = atoi(cmdOutput2);
    EXPECT_GT(expansionRatio2, 0u);  // 校验expansionRatio>0
    // 清空全表
    EXPECT_EQ(GMERR_OK, GmcTruncateVertexLabel(dwStmt, g_labelName));
    // truncate后会直接清零
    StCheckHcWriteBytesAndDeleteBytes(0, 0, filter);
    StCheckHcOriginalDataSize(0, 0, filter);
    GmcResetStmt(dwStmt);
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ((uint64_t)0, count);
    // 校验膨胀率=0
    char command[MAX_CMD_SIZE] = {};
    (void)snprintf_s(command, MAX_CMD_SIZE, MAX_CMD_SIZE - 1, "gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    Status ret = executeCommand(command, "EXPANSION_RATIO: 0.000000");
    EXPECT_EQ(GMERR_OK, ret);
    // 再次插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    GmcResetStmt(dwStmt);
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ((uint64_t)MAX_RECORD_COUNT, count);
    // 校验膨胀率
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput3, 64);
    uint64_t expansionRatio3 = atoi(cmdOutput3);
    EXPECT_GT(expansionRatio3, 0u);  // 校验expansionRatio>0
}

TEST_F(StClientDwClusteredHash, testMutipleLabel)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    const char *filter2 = "LABEL_NAME=\'testT\'";
    // 先向ip4forward表插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput1, 64);
    uint64_t expansionRatio1 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio1, 0u);  // 校验expansionRatio>0
    // 再向testT表插入若干数据
    EXPECT_EQ(GMERR_OK, ChReplaceVertexDataV2(dwStmt, MAX_RECORD_COUNT, g_testLabelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    // 这里有两个表在这，ori应该是两个表的size相加
    StCheckHcOriginalDataSizeWithTwoTables(MAX_RECORD_COUNT, 0, filter, filter2);

    // 校验膨胀率
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput2, 64);
    uint64_t expansionRatio2 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio2, 0u);  // 校验expansionRatio>0

    // 视图查出来是 0字节
    uint32_t cyclesCount = 5;
    for (uint32_t i = 0; i < cyclesCount; ++i) {
        // 先全表扫描ipforward4表
        EXPECT_EQ(GMERR_OK, DirectReadByFullScan(dwStmt, MAX_RECORD_COUNT, g_labelName));

        // 主键直连读test表
        EXPECT_EQ(GMERR_OK, ChPkReadVertexDataV2(dwStmt, MAX_RECORD_COUNT, g_testLabelName));

        // 结构化直连读ipforward4表
        EXPECT_EQ(GMERR_OK, ChReadStructVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    }
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));

    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT / 2, g_labelName, 1));
    EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));
    // 根据二级索引扫描
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));

    // 再向testT表插入若干数据
    EXPECT_EQ(GMERR_OK, ChReplaceVertexDataV2(dwStmt, MAX_RECORD_COUNT * 2, g_testLabelName));
    for (uint32_t i = 0; i < cyclesCount; ++i) {
        // 主键直连读test表
        EXPECT_EQ(GMERR_OK, ChPkReadVertexDataV2(dwStmt, MAX_RECORD_COUNT * 2, g_testLabelName));
    }
}

TEST_F(StClientDwClusteredHash, testScanAndDelete)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先向ip4forward表插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput1, 64);
    uint64_t expansionRatio1 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio1, 0u);  // 校验expansionRatio>0

    // 全表扫描，每扫描一条记录就提取主键去删除
    EXPECT_EQ(GMERR_OK, DirectReadScanAndDeleteByKey(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, MAX_RECORD_COUNT, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, MAX_RECORD_COUNT, filter);
    // 校验膨胀率=0
    char command[MAX_CMD_SIZE] = {};
    (void)snprintf_s(command, MAX_CMD_SIZE, MAX_CMD_SIZE - 1, "gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    Status ret = executeCommand(command, "EXPANSION_RATIO: 0.000000");
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 多个直连写进程并发测试
 * 1.多个直连写 Insert
 * 2.多个直连写 Replace
 * 3.多个直连写 Update
 * 4.多个直连写 Delete
 * 5.多个直连写 Insert、Replace、Update、Delete
 */
static int32_t ChInsertVertexData4Concurrency(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        // 多个线程同时插入，出现主键冲突是预期之中的
        if (ret != GMERR_OK && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertVertexData4Concurrency4Cs(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        // 多个线程同时插入，出现主键冲突是预期之中的
        if (ret != GMERR_OK && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChReplaceVertexData4ConcurrencyV2(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i + 1;
        int32_t f3 = i + 2;
        uint32_t f4 = i + 3;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static void *DwChInsertVertexDataFunc4Concurrency(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChInsertVertexData4Concurrency(stmt, MAX_RECORD_COUNT, g_labelName));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChReplaceVertexDataFunc4ConcurrencyV2(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData4ConcurrencyV2(stmt, MAX_RECORD_COUNT, g_testLabelName));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChReplaceVertexDataFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(stmt, MAX_RECORD_COUNT, g_labelName, 0));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChUpdateVertexDataFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChUpdateVertexData2(stmt, MAX_RECORD_COUNT / 2, g_labelName, 1, 666));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChDeleteBykeyFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, MAX_RECORD_COUNT, g_labelName));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *ChLabelPrimaryKeyScan(void *args)
{
    // 根据hashclusert二级索引扫描获取keyCount
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *ChLabelSecondKeyScan(void *args)
{
    // 根据hashclusert二级索引扫描获取keyCount
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_hashcluster"));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        bool isFinish = false;
        while (!isFinish) {
            int ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            if (ret != GMERR_OK || isFinish) {
                break;
            }
        }
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *ChLabelUniqueSecondKeyScan(void *args)
{
    // 根据hashclusert二级索引扫描获取keyCount
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        uint32_t f3 = i - 2;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_vrfid"));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *ChLabelSecondKeyScanWithCheck(void *args)
{
    // 根据hashclusert二级索引扫描获取keyCount
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);

    uint32_t maxScanCnt = MAX_RECORD_COUNT / MAX_CYCLE_COUNT;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = 0; i < MAX_RECORD_COUNT; ++i) {
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_hashcluster"));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

        uint32_t scanCount = ChSecondIndexScanDataCheck(stmt);
        EXPECT_GE(maxScanCnt, scanCount);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

void CheckVertexCountView(const char *filter, uint32_t count)
{
    char cmdOutput[64] = {0};
    GetViewFieldResultFilter("V\\$STORAGE_VERTEX_COUNT", "record count", filter, cmdOutput, 64);
    uint32_t recordCnt = atoi(cmdOutput);
    EXPECT_EQ(count, recordCnt);
}

static void MultiDwInsertConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, DwChInsertVertexDataFunc4Concurrency,
        DwChInsertVertexDataFunc4Concurrency, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Insert 结束后，校验记录数，并读取数据进行比较
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChReadVertexData(stmt, MAX_RECORD_COUNT, g_labelName));
    // 验证视图
    const char *filter = "table=\'ip4forward\'";
    CheckVertexCountView(filter, MAX_RECORD_COUNT);
}

static void MultiDwInsertMultiLabelConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, DwChInsertVertexDataFunc4Concurrency,
        DwChInsertVertexDataFunc4Concurrency, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan,
        DwChReplaceVertexDataFunc4ConcurrencyV2, DwChReplaceVertexDataFunc4ConcurrencyV2,
        DwChReplaceVertexDataFunc4ConcurrencyV2};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Insert 结束后，校验记录数，并读取数据进行比较
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChReadVertexData(stmt, MAX_RECORD_COUNT, g_labelName));

    // 主键直连读test表
    EXPECT_EQ(GMERR_OK, ChPkReadVertexDataV2(stmt, MAX_RECORD_COUNT, g_testLabelName));
    // 验证视图
    const char *filter = "table=\'ip4forward\'";
    CheckVertexCountView(filter, MAX_RECORD_COUNT);
}

static void MultiDwReplaceConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, MAX_RECORD_COUNT, g_labelName));

    ThreadFunc func[] = {DwChReplaceVertexDataFunc, DwChReplaceVertexDataFunc, DwChReplaceVertexDataFunc,
        ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Replace 结束后，校验记录数，以及根据二级索引读取数据并校验值是否设置为 0
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(stmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    // 验证视图
    const char *filter = "table=\'ip4forward\'";
    CheckVertexCountView(filter, MAX_RECORD_COUNT);
}

static void MultiDwUpdateConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(stmt, MAX_RECORD_COUNT, g_labelName, 0));

    ThreadFunc func[] = {DwChUpdateVertexDataFunc, DwChUpdateVertexDataFunc, DwChUpdateVertexDataFunc,
        ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Update 结束后，校验记录数，以及根据一半记录的二级索引值为 0，一半为 1
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(stmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT / 2)));
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(stmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));
    // 验证视图
    const char *filter = "table=\'ip4forward\'";
    CheckVertexCountView(filter, MAX_RECORD_COUNT);
}

static void MultiDwInsertReplaceUpdateConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(stmt, MAX_RECORD_COUNT, g_labelName, 0));

    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, DwChReplaceVertexDataFunc, DwChUpdateVertexDataFunc,
        ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan, DwChInsertVertexDataFunc4Concurrency,
        DwChReplaceVertexDataFunc, DwChUpdateVertexDataFunc};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发结束后，校验记录数
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    // 验证视图
    const char *filter = "table=\'ip4forward\'";
    CheckVertexCountView(filter, MAX_RECORD_COUNT);
}

static void MultiDwDeleteConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, MAX_RECORD_COUNT, g_labelName));

    ThreadFunc func[] = {DwChDeleteBykeyFunc, DwChDeleteBykeyFunc, DwChDeleteBykeyFunc, ChLabelPrimaryKeyScan,
        ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Delete 结束后，校验记录数为 0
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ((uint64_t)0, count);
}

static void MultiDwAllDmlConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, DwChInsertVertexDataFunc4Concurrency,
        DwChReplaceVertexDataFunc, DwChReplaceVertexDataFunc, DwChUpdateVertexDataFunc, DwChUpdateVertexDataFunc,
        DwChDeleteBykeyFunc, DwChDeleteBykeyFunc, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan,
        ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_GE(MAX_RECORD_COUNT, count);
}

// 测试多个直连写同时 Insert 记录到聚簇容器
TEST_F(StClientDwClusteredHash, testMultiDwInsertConcurrency)
{
    MultiDwInsertConcurrency(dwStmt);
}

// 测试多个直连写并发操作多个表
TEST_F(StClientDwClusteredHash, testMultiDwInsertMultiLabelConcurrency)
{
    MultiDwInsertMultiLabelConcurrency(dwStmt);
}

// 测试多个直连写同时 Replace 聚簇容器中的记录
TEST_F(StClientDwClusteredHash, testMultiDwReplaceConcurrency)
{
    MultiDwReplaceConcurrency(dwStmt);
}

// 测试多个直连写同时 Update 聚簇容器中的记录
TEST_F(StClientDwClusteredHash, testMultiDwUpdateConcurrency)
{
    MultiDwUpdateConcurrency(dwStmt);
}

// 测试多个直连写同时 Insert Replace Update 聚簇容器中的记录
TEST_F(StClientDwClusteredHash, testMultiDwInsertReplaceUpdateConcurrency)
{
    MultiDwInsertReplaceUpdateConcurrency(dwStmt);
}

// 测试多个直连写同时 Delete 聚簇容器中的记录
TEST_F(StClientDwClusteredHash, testMultiDwDeleteConcurrency)
{
    MultiDwDeleteConcurrency(dwStmt);
}

// 测试多个直连写各种 dml 操作聚簇容器中的记录
TEST_F(StClientDwClusteredHash, testMultiDwAllDmlConcurrency)
{
    MultiDwAllDmlConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testConcurrency)
{
    // 先向ip4forward表插入若干数据, 其中F2字段创建hashcluster索引
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    pthread_t deleteThread;
    pthread_t scanThread;

    EXPECT_EQ(0, pthread_create(&deleteThread, NULL, DwChDeleteBykeyFunc, NULL));
    EXPECT_EQ(0, pthread_create(&scanThread, NULL, ChLabelSecondKeyScanWithCheck, NULL));

    EXPECT_EQ(0, pthread_join(deleteThread, NULL));
    EXPECT_EQ(0, pthread_join(scanThread, NULL));
}

/**
 * 直连写与CS线程并发测试
 * 1 直连写与 CS 并发 Insert
 * 2 直连写与 CS 并发 Replace
 * 3 直连写与 CS 并发 Update
 * 4 直连写与 CS 并发 Delete
 * 5 直连写与 CS 并发 Insert Replace Update Delete
 */
static void *CsChInsertVertexDataFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChInsertVertexData4Concurrency4Cs(stmt, MAX_RECORD_COUNT, g_labelName));
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChReplaceVertexDataFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData4Cs(stmt, MAX_RECORD_COUNT, g_labelName, 0));
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChUpdateVertexDataFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChUpdateVertexData24Cs(stmt, MAX_RECORD_COUNT / 2, g_labelName, 1, 666));
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChDeleteBykeyFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData4Cs(stmt, MAX_RECORD_COUNT, g_labelName));
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void DwAndCsInsertConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, DwChInsertVertexDataFunc4Concurrency,
        CsChInsertVertexDataFunc, CsChInsertVertexDataFunc, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan,
        ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Insert 结束后，校验记录数，并读取数据进行比较
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChReadVertexData(stmt, MAX_RECORD_COUNT, g_labelName));
}

static void DwAndCsReplaceConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, MAX_RECORD_COUNT, g_labelName));

    ThreadFunc func[] = {DwChReplaceVertexDataFunc, DwChReplaceVertexDataFunc, CsChReplaceVertexDataFunc,
        CsChReplaceVertexDataFunc, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Replace 结束后，校验记录数，以及根据二级索引读取数据并校验值是否设置为 0
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(stmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
}

static void DwAndCsUpdateConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(stmt, MAX_RECORD_COUNT, g_labelName, 0));

    ThreadFunc func[] = {DwChUpdateVertexDataFunc, DwChUpdateVertexDataFunc, CsChUpdateVertexDataFunc,
        CsChUpdateVertexDataFunc, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Update 结束后，校验记录数，以及根据一半记录的二级索引值为 0，一半为 1
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(stmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT / 2)));
    EXPECT_EQ(GMERR_OK, ChSecondIndexScan(stmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));
}

static void DwAndCsDeleteConcurrency(GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(stmt, MAX_RECORD_COUNT, g_labelName, 0));

    ThreadFunc func[] = {DwChDeleteBykeyFunc, DwChDeleteBykeyFunc, CsChDeleteBykeyFunc, CsChDeleteBykeyFunc,
        ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发 Delete 结束后，校验记录数为 0
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ((uint64_t)0, count);
}

static void DwAndCsInsertReplaceUpdateConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, CsChInsertVertexDataFunc, DwChReplaceVertexDataFunc,
        CsChReplaceVertexDataFunc, DwChUpdateVertexDataFunc, CsChUpdateVertexDataFunc, ChLabelPrimaryKeyScan,
        ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    // 并发结束后，校验记录数
    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
    // 验证视图
    const char *filter = "table=\'ip4forward\'";
    CheckVertexCountView(filter, MAX_RECORD_COUNT);
}

static void DwAndCsAllDmlConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataFunc4Concurrency, CsChInsertVertexDataFunc, DwChReplaceVertexDataFunc,
        CsChReplaceVertexDataFunc, DwChUpdateVertexDataFunc, CsChUpdateVertexDataFunc, DwChDeleteBykeyFunc,
        CsChDeleteBykeyFunc, ChLabelPrimaryKeyScan, ChLabelSecondKeyScan, ChLabelUniqueSecondKeyScan};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_GE(MAX_RECORD_COUNT, count);
}

TEST_F(StClientDwClusteredHash, testDwAndCsInsertConcurrency)
{
    DwAndCsInsertConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testDwAndCsReplaceConcurrency)
{
    DwAndCsReplaceConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testDwAndCsUpdateConcurrency)
{
    DwAndCsUpdateConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testDwAndCsDeleteConcurrency)
{
    DwAndCsDeleteConcurrency(dwStmt);
}

// 测试直连写与 CS 同时 Insert Replace Update 聚簇容器中的记录
TEST_F(StClientDwClusteredHash, testDwAndCsInsertReplaceUpdateConcurrency)
{
    DwAndCsInsertReplaceUpdateConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testDwAndCsAllDmlConcurrency)
{
    DwAndCsAllDmlConcurrency(dwStmt);
}

static void *CsChCheckRecoverFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        Status ret = GmcBeginCheck(stmt, g_labelName, 0xff);
        if (ret == GMERR_OK) {
            ret = GmcEndCheck(stmt, g_labelName, 0xff, true);
            while (true) {
                GmcCheckInfoT *checkInfo;
                GmcCheckStatusE checkStatus;
                ret = GmcGetCheckInfo(stmt, g_labelName, 0xff, &checkInfo);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetCheckStatus(checkInfo, &checkStatus);
                EXPECT_EQ(GMERR_OK, ret);
                if (checkStatus == GMC_CHECK_STATUS_NORMAL) {
                    break;
                }
            }
        } else if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
                   ret != GMERR_TABLE_IN_CHECKING) {
            printf("labelName:%s GmcBeginCheck fail.\n", g_labelName);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChCheckFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        Status ret = GmcBeginCheck(stmt, g_labelName, 0xff);
        if (ret == GMERR_OK) {
            do {
                ret = GmcEndCheck(stmt, g_labelName, 0xff, false);
                if (ret == GMERR_OK) {
                    usleep(CONCURRENCY_SLEEP_US_NUM);
                    break;
                } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                    usleep(CONCURRENCY_SLEEP_US_NUM);
                } else {
                    EXPECT_EQ(GMERR_OK, ret);
                }
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        } else if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE &&
                   ret != GMERR_TABLE_IN_CHECKING) {
            printf("labelName:%s GmcBeginCheck fail.\n", g_labelName);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsTruncateFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM);
        Status ret = GmcTruncateVertexLabel(stmt, g_labelName);
        EXPECT_EQ(ret, GMERR_OK);
        usleep(CONCURRENCY_SLEEP_US_NUM);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChDeleteAllFastMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM);
        Status ret = GmcDeleteAllFast(stmt, g_labelName);
        EXPECT_EQ(ret, GMERR_OK);
        usleep(CONCURRENCY_SLEEP_US_NUM);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *DwChInsertVertexDataMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        EXPECT_EQ(GMERR_OK, ChInsertVertexData4Concurrency(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName));
        usleep(CONCURRENCY_SLEEP_US_NUM);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChReplaceVertexDataMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM / 4);
        EXPECT_EQ(GMERR_OK, ChReplaceVertexData(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName, 0));
        usleep(CONCURRENCY_SLEEP_US_NUM * 3 / 4);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChUpdateVertexDataMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
        EXPECT_EQ(GMERR_OK, ChUpdateVertexData2(stmt, CONCURRENCY_MAX_RECORD_COUNT / 2, g_labelName, 1, 666));
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChUpdateVertexDataMultiTimesBySecIdxFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
        EXPECT_EQ(GMERR_OK, ChUpdateVertexDataBySecIdx(stmt, CONCURRENCY_MAX_RECORD_COUNT / 2, g_labelName, 1, 666));
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChDeleteVertexDataMultiTimesBySecIdxFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
        EXPECT_EQ(GMERR_OK, ChDeleteVertexDataBySecIdx(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName));
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *DwChDeleteBykeyMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM * 3 / 4);
        EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName));
        usleep(CONCURRENCY_SLEEP_US_NUM / 4);
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *CsChInsertVertexDataMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        EXPECT_EQ(GMERR_OK, ChInsertVertexData4Concurrency4Cs(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName));
        usleep(CONCURRENCY_SLEEP_US_NUM);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChReplaceVertexDataMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM / 4);
        EXPECT_EQ(GMERR_OK, ChReplaceVertexData4Cs(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName, 0));
        usleep(CONCURRENCY_SLEEP_US_NUM * 3 / 4);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChUpdateVertexDataMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
        EXPECT_EQ(GMERR_OK, ChUpdateVertexData24Cs(stmt, CONCURRENCY_MAX_RECORD_COUNT / 2, g_labelName, 1, 666));
        usleep(CONCURRENCY_SLEEP_US_NUM / 2);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void *CsChDeleteBykeyMultiTimesFunc(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AllocConnAndStmt(&conn, &stmt);
    for (uint32_t i = 0; i < CONCURRENCY_CYCLE_NUM; i++) {
        usleep(CONCURRENCY_SLEEP_US_NUM * 3 / 4);
        EXPECT_EQ(GMERR_OK, ChDeleteVertexData4Cs(stmt, CONCURRENCY_MAX_RECORD_COUNT, g_labelName));
        usleep(CONCURRENCY_SLEEP_US_NUM / 4);
    }
    FreeConnAndStmt(conn, stmt);
    return NULL;
}

static void DwCsDmlAndTruncAndCheckConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataMultiTimesFunc, CsChInsertVertexDataMultiTimesFunc,
        DwChReplaceVertexDataMultiTimesFunc, CsChReplaceVertexDataMultiTimesFunc, CsChDeleteAllFastMultiTimesFunc,
        CsChCheckFunc, CsTruncateFunc, DwChUpdateVertexDataMultiTimesBySecIdxFunc, DwChUpdateVertexDataMultiTimesFunc,
        CsChUpdateVertexDataMultiTimesFunc, DwChDeleteVertexDataMultiTimesBySecIdxFunc, DwChDeleteBykeyMultiTimesFunc,
        CsChDeleteBykeyMultiTimesFunc};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_GE(CONCURRENCY_MAX_RECORD_COUNT, count);
}

static void DwDmlAndTruncAndCheckConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {DwChInsertVertexDataMultiTimesFunc, DwChReplaceVertexDataMultiTimesFunc,
        CsChDeleteAllFastMultiTimesFunc, CsChCheckFunc, DwChUpdateVertexDataMultiTimesFunc,
        DwChUpdateVertexDataMultiTimesBySecIdxFunc, DwChUpdateVertexDataMultiTimesBySecIdxFunc,
        DwChDeleteVertexDataMultiTimesBySecIdxFunc, DwChDeleteVertexDataMultiTimesBySecIdxFunc,
        DwChDeleteBykeyMultiTimesFunc, CsTruncateFunc};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_GE(CONCURRENCY_MAX_RECORD_COUNT, count);
}

static void DwReplaceCsUpdateAndTruncConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {
        DwChReplaceVertexDataMultiTimesFunc, CsChDeleteAllFastMultiTimesFunc, CsChUpdateVertexDataMultiTimesFunc};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_GE(CONCURRENCY_MAX_RECORD_COUNT, count);
}

static void CsDmlAndTruncAndCheckConcurrency(GmcStmtT *stmt)
{
    ThreadFunc func[] = {CsChInsertVertexDataMultiTimesFunc, CsChReplaceVertexDataMultiTimesFunc,
        CsChDeleteAllFastMultiTimesFunc, CsChCheckFunc, CsTruncateFunc, CsChUpdateVertexDataMultiTimesFunc,
        CsChDeleteBykeyMultiTimesFunc};

    ThreadsCreateAndJoin(func, sizeof(func) / sizeof(func[0]));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt, &count));
    EXPECT_GE(CONCURRENCY_MAX_RECORD_COUNT, count);
}

TEST_F(StClientDwClusteredHash, testDwDmlAndTruncAndCheckConcurrency)
{
    DwDmlAndTruncAndCheckConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testDwDmlAndBgTruncAndCheckreCoverConcurrency)
{
    pthread_t dwInsertThread;
    pthread_t dwReplaceThread;
    pthread_t deleteAllFastThread;
    pthread_t checkThread;
    pthread_t dwUpdateThread;
    pthread_t dwDeleteThread;

    EXPECT_EQ(0, pthread_create(&dwInsertThread, NULL, DwChInsertVertexDataMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&dwReplaceThread, NULL, DwChReplaceVertexDataMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&deleteAllFastThread, NULL, CsChDeleteAllFastMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&checkThread, NULL, CsChCheckRecoverFunc, NULL));
    EXPECT_EQ(0, pthread_create(&dwUpdateThread, NULL, DwChUpdateVertexDataMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&dwDeleteThread, NULL, DwChDeleteBykeyMultiTimesFunc, NULL));

    EXPECT_EQ(0, pthread_join(dwInsertThread, NULL));
    EXPECT_EQ(0, pthread_join(dwReplaceThread, NULL));
    EXPECT_EQ(0, pthread_join(deleteAllFastThread, NULL));
    EXPECT_EQ(0, pthread_join(checkThread, NULL));
    EXPECT_EQ(0, pthread_join(dwUpdateThread, NULL));
    EXPECT_EQ(0, pthread_join(dwDeleteThread, NULL));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_GE(CONCURRENCY_MAX_RECORD_COUNT, count);
}

TEST_F(StClientDwClusteredHash, testDwCsDmlAndTruncAndCheckConcurrency)
{
    DwCsDmlAndTruncAndCheckConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testDwReplaceCsUpdateAndTruncConcurrency)
{
    DwReplaceCsUpdateAndTruncConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testCsDmlAndTruncAndCheckConcurrency)
{
    CsDmlAndTruncAndCheckConcurrency(dwStmt);
}

TEST_F(StClientDwClusteredHash, testView)
{
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    const uint32_t maxStrSize = 128;
    char command[maxStrSize] = "gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    char matchValue[maxStrSize] = {0};
    (void)snprintf_s(matchValue, maxStrSize, maxStrSize - 1, "ENTRY_USED: %u", MAX_RECORD_COUNT);
    Status ret = executeCommand(command, matchValue);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    ret = executeCommand(command, "ENTRY_USED: 0");
    EXPECT_EQ(GMERR_OK, ret);
}

// 门槛用例1
// 简单定长表，单写，查视图
// 1.建表 预期ORIGINAL_DATA_SIZE为0，EXPANSION_RATIO为0.000000
// 2.单写一条数据（N字节）
// 3.查视图，预期ORIGINAL_DATA_SIZE为N，EXPANSION_RATIO>0
TEST_F(StClientDwClusteredHash, testView_simple_single_insert)
{
    // 建表，不写入数据，校验视图字段ORIGINAL_DATA_SIZE为0，EXPANSION_RATIO为0.000000
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize = atoi(cmdOutput1);
    EXPECT_EQ(originalDataSize, 0u);  // 校验originDataSize值为0

    // 校验EXPANSION_RATIO为0.000000
    char command[MAX_CMD_SIZE] = {};
    (void)snprintf_s(command, MAX_CMD_SIZE, MAX_CMD_SIZE - 1, "gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    Status ret = executeCommand(command, "EXPANSION_RATIO: 0.000000");
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据,大小221
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, 1, g_labelName));

    // 校验视图字段ORIGINAL_DATA_SIZE为219
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize2 = atoi(cmdOutput2);
    EXPECT_EQ(originalDataSize2, 221u);  // 校验originDataSize值

    // 校验膨胀率>0
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput3, 64);
    uint64_t expansionRatio = atoi(cmdOutput3);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
}

// 门槛用例2
// 简单定长表，单写，查视图
// 1.建表
// 2.单写10条数据（N*10字节），删除1条数据
// 3.查视图，预期ORIGINAL_DATA_SIZE为N*9，EXPANSION_RATIO>0
TEST_F(StClientDwClusteredHash, testView_simple_insert_delete)
{
    // 插入10条数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, 10, g_labelName));

    // 查视图SERVER_MEMORY_OVERHEAD，预期originDataSize为219*10
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize1 = atoi(cmdOutput1);
    EXPECT_EQ(originalDataSize1, 2210u);

    // 删除1条数据，oriDataSize会减小
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(dwStmt, 1, g_labelName));

    // 查视图 预期ORIGINAL_DATA_SIZE为N*9
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize2 = atoi(cmdOutput2);
    EXPECT_EQ(originalDataSize2, 1989u);  // 221*9

    // 校验膨胀率
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput3, 64);
    uint64_t expansionRatio = atoi(cmdOutput3);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
}

// 简单定长表 两张表
// 1.建两张vertex表
// 2.表1插入500条数据,大小为n
// 3.查视图，预期ORIGINAL_DATA_SIZE为500*n，EXPANSION_RATIO>0
// 4.表2插入数据500条数据,大小为m
// 5.查视图，预期ORIGINAL_DATA_SIZE为(500*m + 500*n)，EXPANSION_RATIO>0
// 6.drop表2，
// 7.查视图，预期ORIGINAL_DATA_SIZE为500*n
TEST_F(StClientDwClusteredHash, testview_insert_muti_label)
{
    // 建表2(表1在setup时建立了)
    system("gmimport -c vschema -t ip4forward_test -f ./st_file/ip4forward_test.gmjson");
    // 表1插入500条数据，大小219
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    // 查视图，预期ORIGINAL_DATA_SIZE为219*500
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize1 = atoi(cmdOutput1);
    EXPECT_EQ(originalDataSize1, MAX_RECORD_COUNT * 221);  // 221*500

    // 表2插入数据500条数据,大小为215
    EXPECT_EQ(GMERR_OK, ChInsertSimpleVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName2));
    // 查视图,预期ORIGINAL_DATA_SIZE为219*500+215*500
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize2 = atoi(cmdOutput2);
    EXPECT_EQ(originalDataSize2, MAX_RECORD_COUNT * 221 + MAX_RECORD_COUNT * 217);  // 219*500+215*500

    // drop表2
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_labelName2));
    // 查视图,预期ORIGINAL_DATA_SIZE为219*500
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput3, 64);
    uint64_t originalDataSize3 = atoi(cmdOutput1);
    EXPECT_EQ(originalDataSize3, MAX_RECORD_COUNT * 221);  // 219*500
}

// 简单定长表
// 1.建表
// 2.replace
// 3.查视图，预期ORIGINAL_DATA_SIZE>0
TEST_F(StClientDwClusteredHash, testView_simple_replace)
{
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    // 查视图
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize1 = atoi(cmdOutput1);
    EXPECT_GT(originalDataSize1, 0u);
}

// update,预期oriDataSize应该不变
// 简单定长表
// 1.建表
// 2.insert，再update
// 3.查视图，预期ORIGINAL_DATA_SIZE不变
TEST_F(StClientDwClusteredHash, testView_simple_update)
{
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    // 查视图
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize1 = atoi(cmdOutput1);
    EXPECT_GT(originalDataSize1, 0u);

    // 更新其中一部分数据
    EXPECT_EQ(GMERR_OK, ChUpdateVertexData(dwStmt, MAX_RECORD_COUNT, 2, g_labelName));
    // 查视图
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize2 = atoi(cmdOutput2);
    EXPECT_EQ(originalDataSize1, originalDataSize2);
}

// truncate,预期oriDataSize = 0
// 简单定长表
// 1.建表
// 2.insert，再truncate
// 3.查视图，预期ORIGINAL_DATA_SIZE为0
TEST_F(StClientDwClusteredHash, testView_simple_truncate)
{
    // 先插入若干数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    // 查视图
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize1 = atoi(cmdOutput1);
    EXPECT_GT(originalDataSize1, 0u);
    // 全表清空
    EXPECT_EQ(GMERR_OK, GmcTruncateVertexLabel(dwStmt, g_labelName));
    // 查视图
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize2 = atoi(cmdOutput2);
    EXPECT_EQ(originalDataSize2, 0u);
}

// 测试general_complex
DB_DEF_PERF_STAT_POINT(INSERT_VERTEX);
static int32_t ChInsertGeneralComplexLabelData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        uint32_t f3 = i - 2;
        uint8_t f4 = i % (MAX_CYCLE_COUNT * 2);
        uint32_t f5 = i - 1;
        uint32_t f6 = i - 3;
        // fixed
        uint8_t addFixed[16];
        for (int j = 0; j < 16; j++) {
            addFixed[j] = i + 16;
        }
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &addFixed, sizeof(addFixed));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &f5, sizeof(f5));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &f6, sizeof(f6));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertGeneralComplexLabel2Data(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        uint32_t f3 = i - 2;
        uint8_t f4 = i % (MAX_CYCLE_COUNT * 2);
        // fixed
        uint8_t addFixed[16];
        for (int j = 0; j < 16; j++) {
            addFixed[j] = i + 16;
        }
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &addFixed, sizeof(addFixed));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteGeneralComplexLabelData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        uint32_t f3 = i - 2;
        uint8_t f4 = i % (MAX_CYCLE_COUNT * 2);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &f4, sizeof(f4)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_key"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// 单层一般复杂定长表，单写+单删，查视图
// 1.建表单层一般复杂定长表1,单层一般复杂定长表2
// 2.分别写入1条数据，表1的记录大小为133，表2记录大小为123
// 3.校验视图，预期ORIGINAL_DATA_SIZE = 133+123
// 4.删除表1的数据
// 5.校验视图，预期ORIGINAL_DATA_SIZE = 123
// 6.校验膨胀率
TEST_F(StClientDwClusteredHash, testView_general_comlex_insert_delete)
{
    // 1.建表单层一般复杂定长表1,单层一般复杂定长表2
    system("gmimport -c vschema -t ip4forward_general_complex -f ./st_file/ip4forward_general_complex.gmjson");
    system(
        "gmimport -c vschema -t ip4forward_general_complex_test -f ./st_file/ip4forward_general_complex_test.gmjson");
    // 2.分别写入1条数据，表1的记录大小为133，表2记录大小为123
    EXPECT_EQ(GMERR_OK, ChInsertGeneralComplexLabelData(dwStmt, 1, g_generalComplexLabel));
    EXPECT_EQ(GMERR_OK, ChInsertGeneralComplexLabel2Data(dwStmt, 1, g_generalComplexLabel2));
    // 3.校验视图，预期ORIGINAL_DATA_SIZE = 133+123
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput1, 64);
    uint64_t originalDataSize1 = atoi(cmdOutput1);
    EXPECT_EQ(originalDataSize1, 260u);  // 133+123
    // 4.删除表1的数据
    EXPECT_EQ(GMERR_OK, ChDeleteGeneralComplexLabelData(dwStmt, 1, g_generalComplexLabel));
    // 5.校验视图，预期ORIGINAL_DATA_SIZE = 123
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize2 = atoi(cmdOutput2);
    EXPECT_EQ(originalDataSize2, 125u);

    // 6.校验膨胀率
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput3, 64);
    uint64_t expansionRatio = atoi(cmdOutput3);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0

    // 删表
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_generalComplexLabel));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_generalComplexLabel2));
}

static const uint32_t SSC_INSERT_COUNT = 100000;
static const uint32_t SSC_DELETE_COUNT = 80000;
static uint32_t g_sscScanGe = SSC_DELETE_COUNT;
static uint32_t g_sccScanLt = SSC_INSERT_COUNT;
static uint32_t g_sccSleepTime = 3000;  // us
static uint32_t g_perfect = 0;

static const char *g_sscLabelName = "sscTestTb";
static const char *g_sscLabelConfig = R"({"defragmentation":true})";
static const char *g_sscLabel = R"([{
    "version":"2.0",
    "max_record_count" : 9999999,
    "config": {
        "check_validity": true,
        "direct_write": true
    },
    "type":"record",
    "name":"sscTestTb",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"int32" },
        { "name":"F4", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"sscTestTb",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        }
    ]
}])";

static int32_t DirectReadByFullScanWithRange(
    GmcStmtT *stmt, int32_t ge, int32_t lt, int32_t expectCnt, const char *labelName, uint32_t sleepUs)
{
    bool eof;
    int32_t recordCnt = 0;
    int32_t ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFilterStructT filterGe = {
        .fieldId = 0, .nodeName = NULL, .compOp = GMC_OP_LARGE_EQUAL, .value = &ge, .valueLen = sizeof(int32_t)};
    ret = GmcSetFilterStructure(stmt, &filterGe);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFilterStructT filterLt = {
        .fieldId = 0, .nodeName = NULL, .compOp = GMC_OP_SMALL, .value = &lt, .valueLen = sizeof(int32_t)};
    ret = GmcSetFilterStructure(stmt, &filterLt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof) {
            break;
        }
        DirectScanCompareResult(stmt);
        recordCnt++;
        usleep(sleepUs);
    }
    EXPECT_EQ(expectCnt, recordCnt);
    return GMERR_OK;
}

static void *SSCDeleteBykeyFunc(void *args)
{
    // 删除数据
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, SSC_DELETE_COUNT, g_sscLabelName));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *SSCRangeScan(void *args)
{
    // 删除数据
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    if (g_perfect != 0) {
        EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &g_perfect, sizeof(g_perfect)));
    }
    // 缩容周期是60秒，确保在扫描期间触发缩容，扫描20000条数据，每条休眠3ms，至少休眠60s
    Status ret = DirectReadByFullScanWithRange(
        stmt, g_sscScanGe, g_sccScanLt, g_sccScanLt - g_sscScanGe, g_sscLabelName, g_sccSleepTime);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *SSCInsertFunc(void *args)
{
    // 删除数据
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ChInsertVertexDataRange(stmt, SSC_INSERT_COUNT, SSC_INSERT_COUNT, g_sscLabelName));
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StClientDwClusteredHash, testScanOneRowAndExpandConcurrency)
{
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dwStmt, g_sscLabel, g_sscLabelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, SSC_INSERT_COUNT, g_sscLabelName));
    pthread_t insertThread;
    pthread_t scanThread1;
    pthread_t scanThread2;

    g_sscScanGe = 0;
    g_sccSleepTime = 100;
    g_perfect = 1;
    EXPECT_EQ(0, pthread_create(&scanThread1, NULL, SSCRangeScan, NULL));
    EXPECT_EQ(0, pthread_create(&scanThread2, NULL, SSCRangeScan, NULL));
    EXPECT_EQ(0, pthread_create(&insertThread, NULL, SSCInsertFunc, NULL));

    EXPECT_EQ(0, pthread_join(insertThread, NULL));
    EXPECT_EQ(0, pthread_join(scanThread1, NULL));
    EXPECT_EQ(0, pthread_join(scanThread2, NULL));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_sscLabelName));
}

TEST_F(StClientDwClusteredHash, testScanOverPageAndExpandConcurrency)
{
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dwStmt, g_sscLabel, g_sscLabelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, SSC_INSERT_COUNT, g_sscLabelName));
    pthread_t insertThread;
    pthread_t scanThread;

    g_sscScanGe = 0;
    g_sccSleepTime = 100;
    g_perfect = 1000;  // 741 rows one page
    EXPECT_EQ(0, pthread_create(&scanThread, NULL, SSCRangeScan, NULL));
    EXPECT_EQ(0, pthread_create(&insertThread, NULL, SSCInsertFunc, NULL));

    EXPECT_EQ(0, pthread_join(insertThread, NULL));
    EXPECT_EQ(0, pthread_join(scanThread, NULL));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_sscLabelName));
}

TEST_F(StClientDwClusteredHash, dw_testView_simple_truncate_scan)
{
    g_perfect = 10;
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dwStmt, g_sscLabel, g_sscLabelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, 1000, g_sscLabelName));

    // 开始扫描
    GmcConnT *scanConn = NULL;
    GmcStmtT *scanStmt = NULL;
    AllocConnAndStmt(&scanConn, &scanStmt);
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(scanStmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &g_perfect, sizeof(g_perfect)));
    int32_t ret = GmcPrepareStmtByLabelName(scanStmt, g_sscLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t filterGeV = 0;
    int32_t filterLtV = 999;
    GmcFilterStructT filterGe = {
        .fieldId = 0, .nodeName = NULL, .compOp = GMC_OP_LARGE_EQUAL, .value = &filterGeV, .valueLen = sizeof(int32_t)};
    ret = GmcSetFilterStructure(scanStmt, &filterGe);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFilterStructT filterLt = {
        .fieldId = 0, .nodeName = NULL, .compOp = GMC_OP_SMALL, .value = &filterLtV, .valueLen = sizeof(int32_t)};
    ret = GmcSetFilterStructure(scanStmt, &filterLt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(scanStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表清空
    EXPECT_EQ(GMERR_OK, GmcTruncateVertexLabel(dwStmt, g_sscLabelName));
    // 再次插入
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, 1000, g_sscLabelName));

    bool eof = false;
    uint32_t recordCnt = 0;
    while (!eof) {
        ret = GmcFetch(scanStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof) {
            break;
        }
        DirectScanCompareResult(scanStmt);
        recordCnt++;
    }
    // 应该只扫描到刚开始预期的g_perfect条
    EXPECT_EQ(g_perfect, recordCnt);

    FreeConnAndStmt(scanConn, scanStmt);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_sscLabelName));
}

class StClientDwHeap : public StStorage {
public:
    StClientDwHeap()
    {
        DbMemCtxArgsT args = {0};
        // client st 会初始化g_gmdbCltInstance.cltCommCtx，这里直接使用
        alterMemCtx =
            DbCreateDynMemCtx(g_gmdbCltInstance.cltCommCtx, false, "StClientStructDirectWriteSpecial st", &args);
    }

    ~StClientDwHeap() override
    {
        // 统一回收中间申请的内存
        DbDeleteDynMemCtx(alterMemCtx);
    }

protected:
    static void SetUpTestCase()
    {
        DbInitCPUFrequency();
        DbRdtscInit();
        StServerClientPrepare("\"enableClusterHash=0\" \"directWrite=1\" ", &epollThreadId);
        GmcSignalRegisterNotify();
    }
    static void TearDownTestCase()
    {
        StServerClientExit(&epollThreadId);
    }
    void SetUp()
    {
        CreateSyncConnectionAndStmt(&dwConn, &dwStmt);
        system("gmimport -c vschema -t ip4forward -f ./st_file/ip4forward.gmjson");
        system("gmimport -c vschema -t testT -f ./st_file/test.gmjson");
    }
    void TearDown()
    {
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_labelName));
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, "testT"));
        DestroyConnectionAndStmt(dwConn, dwStmt);
        SaveLogAfterFailed();
    }
    GmcConnT *dwConn = NULL;
    GmcStmtT *dwStmt = NULL;
};

TEST_F(StClientDwHeap, testDwDmlAndTruncAndCheckConcurrency)
{
    DwDmlAndTruncAndCheckConcurrency(dwStmt);
}

TEST_F(StClientDwHeap, testDwDmlAndBgTruncAndCheckreCoverConcurrency)
{
    pthread_t dwInsertThread;
    pthread_t dwReplaceThread;
    pthread_t deleteAllFastThread;
    pthread_t checkThread;
    pthread_t dwUpdateThread;
    pthread_t dwDeleteThread;

    EXPECT_EQ(0, pthread_create(&dwInsertThread, NULL, DwChInsertVertexDataMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&dwReplaceThread, NULL, DwChReplaceVertexDataMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&deleteAllFastThread, NULL, CsChDeleteAllFastMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&checkThread, NULL, CsChCheckRecoverFunc, NULL));
    EXPECT_EQ(0, pthread_create(&dwUpdateThread, NULL, DwChUpdateVertexDataMultiTimesFunc, NULL));
    EXPECT_EQ(0, pthread_create(&dwDeleteThread, NULL, DwChDeleteBykeyMultiTimesFunc, NULL));

    EXPECT_EQ(0, pthread_join(dwInsertThread, NULL));
    EXPECT_EQ(0, pthread_join(dwReplaceThread, NULL));
    EXPECT_EQ(0, pthread_join(deleteAllFastThread, NULL));
    EXPECT_EQ(0, pthread_join(checkThread, NULL));
    EXPECT_EQ(0, pthread_join(dwUpdateThread, NULL));
    EXPECT_EQ(0, pthread_join(dwDeleteThread, NULL));

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_GE(CONCURRENCY_MAX_RECORD_COUNT, count);
}

TEST_F(StClientDwHeap, testDwCsDmlAndTruncAndCheckConcurrency)
{
    DwCsDmlAndTruncAndCheckConcurrency(dwStmt);
}

TEST_F(StClientDwHeap, testDwReplaceCsUpdateAndTruncConcurrency)
{
    DwReplaceCsUpdateAndTruncConcurrency(dwStmt);
}

TEST_F(StClientDwHeap, testCsDmlAndTruncAndCheckConcurrency)
{
    CsDmlAndTruncAndCheckConcurrency(dwStmt);
}

class StClientDwLL : public StClientDwHeap {
protected:
    static void SetUpTestCase()
    {
        DbInitCPUFrequency();
        DbRdtscInit();
        StServerClientPrepare(
            "\"enableClusterHash=1\" \"enableTableLock=1\" \"isFastReadUncommitted=1\" \"directWrite=1\" ",
            &epollThreadId);
        GmcSignalRegisterNotify();
    }
};

TEST_F(StClientDwLL, testDwCsDmlAndTruncAndCheckConcurrency)
{
    DwCsDmlAndTruncAndCheckConcurrency(dwStmt);
}
