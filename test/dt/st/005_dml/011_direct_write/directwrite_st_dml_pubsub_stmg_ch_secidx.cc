/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for direct write pubsub of dml operations based on unique secIdx
 * Author: zhangjinglong
 * Create: 2023-12-22
 */

#include <functional>
#include "query_subs_st_base.h"
#include "client_common_st.h"
#include "tools_st_common.h"
#include "direct_write_common.h"

class StClientDwPubSubStMgSecIdxCh : public StClient {
public:
    static void SetUpTestCase();
    void CreateVertexLabel(const char *labelJson, const char *g_labelCfgJson);
    void InsertRecord(GmcStmtT *stmt, const char *vertexLabelName, uint32_t count, uint32_t startIndex);
    void DeleteRecord(GmcStmtT *stmt, const char *vertexLabelName, uint32_t count);
};
void StClientDwPubSubStMgSecIdxCh::SetUpTestCase()
{
    // 确保打开enableDmlOperStat和enableDmlPerfStat，用于测试dml stat视图
    // 最新代码默认打开聚簇容器，此处将其设置为关闭状态，聚簇容器用例在其他文件中维护
    StartDbServerWithConfig("\"enableDmlOperStat=1\" \"enableDmlPerfStat=1\" \"enableClusterHash=1\" "
                            "\"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" "
                            "\"maxSysDynSize=1024\" \"auditLogEnableDML=0\" \"directWrite=1\" "
                            "\"userPolicyMode=0\" \"compatibleV3=0\" \"subsChannelGlobalShareMemSizeMax=32\" "
                            "\"subsChannelGlobalDynamicMemSizeMax=32\"");
    st_clt_init();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    GmcSignalRegisterNotify();
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

static const char *g_subsLabelStMerge = "g_subsLabelStMerge";
static const char *g_subsLabelStMergeJson =
    R"([{
    "type":"record",
    "name":"g_subsLabelStMerge",
    "subs_type":"status_merge",
    "config": {
        "direct_write": true
    },
    "fields":
        [
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"uint32", "nullable":false},
            {"name":"F2", "type":"uint32", "nullable":false}
        ],
        "keys":
        [
            {
                "node":"g_subsLabelStMerge",
                "name":"subLabel1_K0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"g_subsLabelStMerge",
                "name":"subLabel1_K1",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    }])";

static const char *g_labelCfgJson = R"({
        "max_record_count":10000,
        "push_age_record_batch":20,
        "isFastReadUncommitted":true,
        "defragmentation":false,
        "status_merge_sub":true
    })";

void StClientDwPubSubStMgSecIdxCh::CreateVertexLabel(const char *labelJson, const char *labelCfgJson)
{
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, labelJson, labelCfgJson));
}

void StClientDwPubSubStMgSecIdxCh::InsertRecord(
    GmcStmtT *stmt, const char *vertexLabelName, uint32_t start, uint32_t count)
{
    Status ret;
    uint32_t end = start + count;
    for (uint32_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0 = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void StClientDwPubSubStMgSecIdxCh::DeleteRecord(GmcStmtT *stmt, const char *vertexLabelName, uint32_t count)
{
    Status ret;
    constexpr auto indexName = "subLabel1_K0";
    uint32_t total = 0;
    while (total < count) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t i = total;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }
}

static void SetVertex(GmcStmtT *stmt, uint32_t val)
{
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

static void PubsubModifyCallBackFunc(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    EXPECT_EQ(GMC_SUB_EVENT_MODIFY, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    for (bool eof, &isNull = eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
    }
}

static void PubsubDeleteCallBackFunc(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    for (bool eof, &isNull = eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
    }
}

TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_modify_insert)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    CreateVertexLabel(g_subsLabelStMergeJson, g_labelCfgJson);
    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
        "label_name":"g_subsLabelStMerge",
        "comment":"VertexLabel1 subscription",
        "subs_type":"status_merge",
        "events":
            [
                {
                "type":"modify",
                "msgTypes":["new object"]
                }
            ]
    })";
    auto callback = PubsubModifyCallBackFunc;
    uint32_t received = 0;

    Status ret = GmcSubscribe(syncStmt, &subConfig, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    uint32_t total = 5;
    InsertRecord(syncStmt, g_subsLabelStMerge, 0, total);

    WAIT_WHILE(received != total);

    ret = GmcUnSubscribe(syncStmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record g_subsLabelStMerge");

    ret = GmcDropVertexLabel(syncStmt, g_subsLabelStMerge);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 只校验update更新操作的值
static void UpdateSubsCallBack(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    EXPECT_EQ(GMC_SUB_EVENT_MODIFY, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    bool eof = false;
    while (!eof) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint32_t f0V, f1V, f2V;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        // 主键未发生更改
        EXPECT_EQ(1u, f0V);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        // 只对终态update数据进行校验，不检查insert数据时的中间状态
        if (f1V != 2u) {
            continue;
        }
        received++;

        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2u, f2V);
    }
};

static void ReplaceSubsCallBack(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    EXPECT_EQ(GMC_SUB_EVENT_MODIFY, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    for (bool eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        bool isNull;

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0V, f1V, f2V;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        // 主键未发生更改
        EXPECT_EQ(1u, f0V);

        // 通信开销比两次replace操作开销大，故只能读到最终状态，为(f0, f1, f2): (1, 2, 2);
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(2u, f1V);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2u, f2V);
    }
}

TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_modify_replace)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    Status ret = GmcCreateVertexLabel(syncStmt, g_subsLabelStMergeJson, g_labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
        "label_name":"g_subsLabelStMerge",
        "comment":"VertexLabel1 subscription",
        "subs_type":"status_merge",
        "events":
        [
            {
                "type":"modify",
                "msgTypes":["new object"]
            }
        ]
    })";
    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &subConfig, subConn, ReplaceSubsCallBack, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行replace insert, 插入数据
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 1;
    SetVertex(syncStmt, val);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行replace update，更改插入数据
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新F1, F2字段的值
    uint32_t tmp = val + 1;
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    WAIT_WHILE(received == 0u);
    ret = GmcUnSubscribe(syncStmt, subConfig.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record g_subsLabelStMerge");

    ret = GmcDropVertexLabel(syncStmt, g_subsLabelStMerge);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

static void ReplaceInsertCallBack(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    EXPECT_EQ(GMC_SUB_EVENT_MODIFY, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    for (bool eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        bool isNull;

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0V, f1V, f2V;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1u, f0V);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1u, f1V);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, f2V);
    }
}

TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_modify_update)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    Status ret = GmcCreateVertexLabel(syncStmt, g_subsLabelStMergeJson, g_labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
        "label_name":"g_subsLabelStMerge",
        "comment":"VertexLabel1 subscription",
        "subs_type":"status_merge",
        "events":
        [
            {
                "type":"modify",
                "msgTypes":["new object"]
            }
        ]
    })";
    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &subConfig, subConn, UpdateSubsCallBack, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 1;
    SetVertex(syncStmt, val);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    DbSleep(1);

    // 执行update，更改插入数据
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新F1, F2字段的值
    uint32_t tmp = val + 1;
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    WAIT_WHILE(received != 1u);
    ret = GmcUnSubscribe(syncStmt, subConfig.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record g_subsLabelStMerge");

    ret = GmcDropVertexLabel(syncStmt, g_subsLabelStMerge);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_mq_full_dml)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "config": {
            "direct_write": true
        },
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            },
            {
                "node": "subLabel1",
                "name": "subLabel1_K1",
                "fields": ["F1"],
                "index": { "type": "localhash" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    Status ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */
    constexpr uint32_t total = 1000;
    /* full sync seq scan */
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f2 = i;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            },
            {
                "type":"insert",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"delete",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"update",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"replace update",
                "msgTypes":["new object", "old object"]
            }
        ]
    })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 20, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(syncStmt, &config, subConn, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total);

    /* update */
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i + 1;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f2 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE] != total);

    /* delete */
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i + 1;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] != total);

    /* insert */
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2 = i;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_INSERT] != total);

    /* replace */
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] != total);

    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// mark delete标记删除数据，然后update更新数据，预期执行成功，affectRows = 0。
TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_modify_delete_update)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    CreateVertexLabel(g_subsLabelStMergeJson, g_labelCfgJson);
    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
        "label_name":"g_subsLabelStMerge",
        "comment":"VertexLabel1 subscription",
        "subs_type":"status_merge",
        "events":
        [
            {
                "type":"delete",
                "msgTypes":["old object"]
            }
        ]
    })";
    auto callback = PubsubDeleteCallBackFunc;
    uint32_t received = 0;

    Status ret = GmcSubscribe(syncStmt, &subConfig, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 1;

    // 插入数据，然后删除数据
    InsertRecord(syncStmt, g_subsLabelStMerge, 0, total);
    DeleteRecord(syncStmt, g_subsLabelStMerge, total);

    // 再次删除，确认返回no_data
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    constexpr auto indexName = "subLabel1_K1";
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 0;
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行update，更改插入数据
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新F1, F2字段的值
    uint32_t tmp = val + 1;
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectedRows;
    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectedRows, sizeof(affectedRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectedRows);  // 主键存在，则正常更新，值为1；若主键不存在，则什么都不做，值为0

    ret = GmcUnSubscribe(syncStmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview record g_subsLabelStMerge");

    ret = GmcDropVertexLabel(syncStmt, g_subsLabelStMerge);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// mark delete标记删除数据，然后insert数据，再update数据，预期执行成功。
TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_modify_delete_insert_update)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    CreateVertexLabel(g_subsLabelStMergeJson, g_labelCfgJson);

    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
        "label_name":"g_subsLabelStMerge",
        "comment":"VertexLabel1 subscription",
        "subs_type":"status_merge",
        "events":
        [
            {
                "type":"modify",
                "msgTypes":["new object"]
            }
        ]
    })";

    uint32_t received = 0;
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_MODIFY, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
        }
    };
    Status ret = GmcSubscribe(syncStmt, &subConfig, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 1;
    // 插入数据
    InsertRecord(syncStmt, g_subsLabelStMerge, 0, total);

    WAIT_WHILE(received == 0u);

    // 删除数据
    DeleteRecord(syncStmt, g_subsLabelStMerge, total);
    // 再插入数据
    InsertRecord(syncStmt, g_subsLabelStMerge, 0, total);

    int affectedRows;
    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectedRows, sizeof(affectedRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectedRows);

    // 执行update，更改插入数据
    uint32_t val = 0;
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新F1, F2字段的值
    uint32_t tmp = val + 1;
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 再插入数据，会报主键冲突
    ret = GmcPrepareStmtByLabelName(syncStmt, g_subsLabelStMerge, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0 = 0;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1 = f0 + 1;
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f2 = f1 + 2;
    ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);

    uint64_t num = 1;
    const char *labels[num] = {g_subsLabelStMerge};
    uint64_t count[num] = {0};
    ret = GmcGetOperStatsCnt(syncStmt, labels, GMC_STATISTICS_TYPE_INSERT, count, num);
    EXPECT_EQ(2u, count[0]);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$QRY_DML_OPER_STATIS");

    ret = GmcUnSubscribe(syncStmt, subConfig.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(syncStmt, g_subsLabelStMerge);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(StClientDwPubSubStMgSecIdxCh, dw_pubsub_stmg_age_and_update_and_delete_null_old_msg)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
        "type":"record",
        "config": {
            "direct_write": true
        },
        "name":"subLabel1",
        "fields":
        [
            {"name":"F0", "type":"int32", "nullable":false},
            {"name":"F1", "type":"int32", "nullable":false},
            {"name":"F2", "type":"int32", "nullable":false}
        ],
        "subs_type":"status_merge",
        "keys":
        [
            {
                "node":"subLabel1",
                "name":"subLabel1_K0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"subLabel1",
                "name":"subLabel1_K1",
                "fields":["F1"],
                "index":{"type":"hashcluster"},
                "constraints":{"unique":true}
            }
        ]
    }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, subLabelJson, g_labelCfgJson));
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
        "label_name":"subLabel1",
        "comment":"VertexLabel1 subscription",
        "events":
        [
            {"type":"insert", "msgTypes":["new object", "old object"]},
            {"type":"replace insert", "msgTypes":["new object", "old object"]},
            {"type":"replace update"},
            {"type":"update"},
            {"type":"delete"},
            {"type":"age"}
        ],
        "retry":true
    })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    Status ret = GmcSubscribe(syncStmt, &config, subConn, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t total = 100;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t f0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_INT32, &f0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f1Value = i;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &f1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f2Value = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_INT32, &f2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账操作
    ret = GmcBeginCheck(syncStmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(syncStmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = total - 1; i >= 0; i--) {
        int32_t f0Value = i;
        GmcOperationTypeE opertionType = i % 2 == 0 ? GMC_OPERATION_UPDATE : GMC_OPERATION_DELETE;
        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, "subLabel1", opertionType));
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K1");
        EXPECT_EQ(GMERR_OK, ret);
        if (opertionType == GMC_OPERATION_UPDATE) {
            int32_t f2Value = i + 2;
            ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_INT32, &f2Value, 4);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(syncStmt);
        uint32_t retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(syncStmt);
            printf("update");
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(100);
    /* wait until all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_AGED] != total) {
        DbSleep(100);
        printf("subRowStatis->typeNum[GMC_SUB_EVENT_AGED]:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    }
    EXPECT_EQ((uint32_t)0, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ((uint32_t)0, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    EXPECT_EQ((uint32_t)total, subRowStatis->typeNum[GMC_SUB_EVENT_INSERT]);
    EXPECT_EQ((uint32_t)0, subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE]);
    EXPECT_EQ((uint32_t)0, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ((uint32_t)0, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("updateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("insertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_INSERT]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s channel:");
#else
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s "
           "usocket:/run/verona/unix_emserver");
#endif

    uint64_t count = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, "subLabel1", GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcGetVertexRecordCount(syncStmt, &count));
    EXPECT_EQ((uint64_t)0, count);

    ret = GmcUnSubscribe(syncStmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(syncStmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, syncStmt);
}
