[{"version": "2.0", "max_record_count": 9999999, "config": {"check_validity": true, "direct_write": false}, "type": "record", "name": "testT", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "int32"}, {"name": "F4", "type": "uint32"}], "keys": [{"node": "testT", "name": "pk", "fields": ["F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]