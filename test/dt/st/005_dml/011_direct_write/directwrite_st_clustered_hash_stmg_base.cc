/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: directwrite_st_clustered_hash_base.cc
 * Description: testcase for direct write clustered hash
 * Author: zhangjinglong
 * Create: 2023-04-26
 */
#include "gmc_graph.h"
#include "gmc_types.h"
#include "adpt_define.h"
#include "storage_st_common.h"
#include "db_perf_stat.h"
#include "tools_st_common.h"
#include "storage_st_util.h"
#include "client_common_st.h"
#include "gmc_batch_check.h"
#include "gmc_graph_check.h"
#include "clt_da_write.h"

static DbMemCtxT *alterMemCtx = NULL;

#define PERF_SAMPLE_STAT 1
// 测试记录数为 100000 时测试也已通过
static const uint32_t MAX_RECORD_COUNT = 5000;
static const uint32_t MAX_CYCLE_COUNT = 100;
static const char *g_labelName = "ip4forward";
static const char *g_testLabelName = "testT";
static const int MAX_CMD_SIZE = 1024u;
static const char *g_label_config = R"({"max_record_count":1000000, "status_merge_sub":true})";
static const char *g_ip4forward_schema_json =
    R"([{
        "version":"2.0",
        "max_record_count" : 9999999,
        "config": {
            "check_validity": true,
            "direct_write": true
        },
        "type":"record",
        "name":"ip4forward",
        "fields":[
            { "name":"F1", "type":"uint32" },
            { "name":"F2", "type":"uint32" },
            { "name":"F3", "type":"int32" },
            { "name":"F4", "type":"uint32" },
            { "name": "A1", "type": "fixed", "size": 32},
            { "name": "A2", "type": "fixed", "size": 32},
            { "name": "A3", "type": "fixed", "size": 32},
            { "name": "A4", "type": "fixed", "size": 32},
            { "name": "A5", "type": "fixed", "size": 32},
            { "name": "A6", "type": "fixed", "size": 32}
        ],
        "keys":[
            {
                "node":"ip4forward",
                "name":"pk",
                "fields":["F1"],
                "index":{ "type":"primary" },
                "constraints": {"unique": true}
            },
            {
                "name": "ip4_hashcluster",
                "index": { "type": "hashcluster" },
                "node": "ip4forward",
                "fields": [ "F2" ],
                "constraints": { "unique": false }
            },
            {
                "name": "ip4_vrfid",
                "index": { "type": "localhash" },
                "node": "ip4forward",
                "fields": [ "F3" ],
                "config": { "init_hash_capacity": 200000 },
                "constraints": { "unique": true }
            },
            {
                "name": "ip4_hashcluster1",
                "index": { "type": "hashcluster" },
                "node": "ip4forward",
                "fields": [ "F4" ],
                "constraints": { "unique": false }
            }
        ]
    }])";

static const char *g_test_schema_json =
    R"([{
        "version":"2.0",
        "max_record_count" : 9999999,
        "config": {
            "check_validity": true,
            "direct_write": true
        },
        "type":"record",
        "name":"testT",
        "fields":[
            { "name":"F1", "type":"uint32" },
            { "name":"F2", "type":"uint32" },
            { "name":"F3", "type":"int32" },
            { "name":"F4", "type":"uint32" }
        ],
        "keys":[
            {
                "node":"testT",
                "name":"pk",
                "fields":["F1"],
                "index":{ "type":"primary" },
                "constraints": {"unique": true}
            }
        ]
    }])";

#pragma pack(1)
typedef struct TagIp4forwardDataT {
    uint32_t f1;
    uint32_t f2;
    int32_t f3;
    uint32_t f4;
    char a1[32];
    char a2[32];
    char a3[32];
    char a4[32];
    char a5[32];
    char a6[32];
} Ip4forwardDataT;
#pragma pack()

class StClientDirectWriteClusteredHashStmg : public StStorage {
public:
    StClientDirectWriteClusteredHashStmg()
    {
        DbMemCtxArgsT args = {0};
        // client st 会初始化g_gmdbCltInstance.cltCommCtx，这里直接使用
        alterMemCtx =
            DbCreateDynMemCtx(g_gmdbCltInstance.cltCommCtx, false, "StClientStructDirectWriteSpecial st", &args);
    }

    ~StClientDirectWriteClusteredHashStmg() override
    {
        // 统一回收中间申请的内存
        DbDeleteDynMemCtx(alterMemCtx);
    }

protected:
    static void SetUpTestCase()
    {
        DbInitCPUFrequency();
        DbRdtscInit();
        StServerClientPrepare("\"enableClusterHash=1\" \"compatibleV3=0\" \"directWrite=1\"", &epollThreadId);
        GmcSignalRegisterNotify();
    }
    static void TearDownTestCase()
    {
        StServerClientExit(&epollThreadId);
    }
    void SetUp()
    {
        CreateSyncConnectionAndStmt(&dwConn, &dwStmt);
        Status ret = GmcCreateVertexLabel(dwStmt, g_ip4forward_schema_json, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(dwStmt, g_test_schema_json, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
    void TearDown()
    {
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, g_labelName));
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, "testT"));
        DestroyConnectionAndStmt(dwConn, dwStmt);
        SaveLogAfterFailed();
    }
    GmcConnT *dwConn = NULL;
    GmcStmtT *dwStmt = NULL;
};

static int32_t ChInsertVertexDataRange(GmcStmtT *stmt, uint32_t startVal, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = startVal; i < startVal + recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    return ChInsertVertexDataRange(stmt, 0, recordCount, labelName);
}

static int32_t ChInsertVertexDataFull(GmcStmtT *stmt, const char *labelName, uint32_t *maxTupleNum)
{
    int ret;
    uint32_t maxTupleCount = 0;
    for (uint32_t i = 0; i < DB_MAX_UINT32; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            *maxTupleNum = maxTupleCount;
            return ret == GMERR_OUT_OF_MEMORY ? GMERR_OK : ret;
        }
        maxTupleCount++;
    }
    *maxTupleNum = maxTupleCount;
    return GMERR_OK;
}

// 给label2写数据
static int32_t ChInsertSimpleVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(INSERT_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(INSERT_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertAndRollbackVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, int *cnt)
{
    int ret;
    int violationCnt = 0;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i % (MAX_CYCLE_COUNT * 2);
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {  // 主键冲突后回滚
            violationCnt++;
            continue;
        } else if (ret != GMERR_OK) {
            return ret;
        }
    }
    *cnt = violationCnt;
    return GMERR_OK;
}

static int32_t ChReplaceVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue)
{
    for (uint32_t i = 0; i < recordCount; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChReplaceVertexData4Cs(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue)
{
    for (uint32_t i = 0; i < recordCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChReplaceVertexDataV2(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i + 1;
        int32_t f3 = i + 2;
        uint32_t f4 = i + 3;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static void DirectReadCompareResultV2(GmcStmtT *stmt, uint32_t f1Value)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f1 = f1Value;
    uint32_t f2 = f1Value + 1;
    int32_t f3 = f1Value + 2;
    uint32_t f4 = f1Value + 3;
    uint32_t f1Res;
    uint32_t f2Res;
    int32_t f3Res;
    uint32_t f4Res;

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Res, propSize, &isNull));
    ASSERT_EQ(f1, f1Res);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F2", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &f2Res, propSize, &isNull));
    ASSERT_EQ(f2, f2Res);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F3", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F3", &f3Res, propSize, &isNull));
    ASSERT_EQ(f3, f3Res);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F4", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F4", &f4Res, propSize, &isNull));
    ASSERT_EQ(f4, f4Res);
}

static int32_t ChPkReadVertexDataV2(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordCount; i++) {
        uint32_t pKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pKValue, sizeof(pKValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        EXPECT_EQ(GMERR_OK, ret);
        DirectReadCompareResultV2(stmt, pKValue);
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexData(GmcStmtT *stmt, uint32_t recordCount, uint32_t step, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i += step) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        int32_t f3 = i - 2;
        uint32_t f4 = i % (MAX_CYCLE_COUNT * 2) + 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexData2(
    GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue, int32_t delta)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - delta;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUpdateVertexData24Cs(
    GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t keyValue, int32_t delta)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        uint32_t f2 = keyValue;
        int32_t f3 = i - delta;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(REPLACE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(REPLACE_VERTEX);
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(DELETE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(DELETE_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexData4Cs(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        DB_START_TEST_CPU_CYCLES(DELETE_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(DELETE_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static void DirectReadCompareResult(GmcStmtT *stmt, uint32_t f1Value)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f1 = f1Value;
    uint32_t f2 = f1Value % MAX_CYCLE_COUNT;
    int32_t f3 = f1Value - 2;
    uint32_t f4 = f1Value % (MAX_CYCLE_COUNT * 2);
    uint32_t f1Res;
    uint32_t f2Res;
    int32_t f3Res;
    uint32_t f4Res;

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Res, propSize, &isNull));
    ASSERT_EQ(f1, f1Res);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F2", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &f2Res, propSize, &isNull));
    ASSERT_EQ(f2, f2Res);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F3", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F3", &f3Res, propSize, &isNull));
    ASSERT_EQ(f3, f3Res);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F4", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F4", &f4Res, propSize, &isNull));
    ASSERT_EQ(f4, f4Res);
}

static void DirectScanCompareResult(GmcStmtT *stmt)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f2Expect;
    int32_t f3Expect;
    uint32_t f4Expect;

    uint32_t f1Result;
    uint32_t f2Result;
    int32_t f3Result;
    uint32_t f4Result;

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Result, propSize, &isNull));

    f2Expect = f1Result % MAX_CYCLE_COUNT;
    f3Expect = f1Result - 2;
    f4Expect = f1Result % (MAX_CYCLE_COUNT * 2);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F2", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &f2Result, propSize, &isNull));
    ASSERT_EQ(f2Expect, f2Result);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F3", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F3", &f3Result, propSize, &isNull));
    ASSERT_EQ(f3Expect, f3Result);

    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F4", &propSize));
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F4", &f4Result, propSize, &isNull));
    ASSERT_EQ(f4Expect, f4Result);
}

static int32_t ChReadVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordCount; i++) {
        uint32_t pKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pKValue, sizeof(pKValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        DB_START_TEST_CPU_CYCLES(PK_READ_VERTEX);
        ret = GmcExecute(stmt);
        DB_STOP_TEST_CPU_CYCLES(PK_READ_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        EXPECT_EQ(GMERR_OK, ret);
        DirectReadCompareResult(stmt, i);
    }
    return GMERR_OK;
}

static int32_t DirectReadByFullScan(GmcStmtT *stmt, int32_t expectCnt, const char *labelName)
{
    bool eof;
    int32_t recordCnt = 0;
    int32_t ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        DB_START_TEST_CPU_CYCLES(DIRECT_SCAN);
        ret = GmcFetch(stmt, &eof);
        DB_STOP_TEST_CPU_CYCLES(DIRECT_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof) {
            break;
        }
        DirectScanCompareResult(stmt);
        recordCnt++;
    }
    EXPECT_EQ(expectCnt, recordCnt);
    return GMERR_OK;
}

static int32_t ExtractPrimaryKeyAndDelete(GmcStmtT *stmt, const char *labelName)
{
    bool isNull;
    uint32_t propSize;
    uint32_t f1Result;

    int32_t ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Result, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1Result, sizeof(propSize)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
    return GmcExecute(stmt);
}

static int32_t DirectReadScanAndDeleteByKey(GmcStmtT *stmt, int32_t expectCnt, const char *labelName)
{
    bool eof;
    int32_t recordCnt = 0;
    while (true) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ExtractPrimaryKeyAndDelete(stmt, labelName));
        recordCnt++;
    }
    EXPECT_EQ(expectCnt, recordCnt);
    return GMERR_OK;
}

static Status ChSeriPrimaryKey(void *seri, uint8_t *keyBuf, GmcStructureResvT *reservedSize)
{
    uint8_t *bits = keyBuf;
    *bits = 0xff;  // 空值标识符 参考代码SetKeyBufByFixedPropeValues
    keyBuf += 1;
    errno_t err = memcpy_s(keyBuf, sizeof(uint32_t), (uint8_t *)((GmcSeriT *)seri)->obj, sizeof(uint32_t));
    DB_ASSERT(err == EOK);
    return GMERR_OK;
}

static int32_t ChReadStructVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    Ip4forwardDataT obj;
    Ip4forwardDataT objRes;
    GmcSeriT serStr;
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    serStr.seriFunc = ChSeriPrimaryKey;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.bufSize = sizeof(uint32_t) + 1;
    serStr.userData = stmt;

    uint32_t fixedLen = sizeof(Ip4forwardDataT);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);

    GmcStructBufferT inputBufInfo = {(uint8_t *)&objRes, fixedLen, 1 + newSize};
    for (uint32_t i = 0; i < recordCount; ++i) {
        obj.f1 = i;
        DB_START_TEST_CPU_CYCLES(STRUCT_READ_VERTEX);
        ret = GmcGetVertexBuf(stmt, 0, &serStr, &inputBufInfo);
        DB_STOP_TEST_CPU_CYCLES(STRUCT_READ_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(objRes.f1, i);
        EXPECT_EQ(objRes.f2, i % MAX_CYCLE_COUNT);
        EXPECT_EQ(objRes.f3, (int32_t)(i - 2));
        EXPECT_EQ(objRes.f4, (uint32_t)(i % (MAX_CYCLE_COUNT * 2)));
    }
    return GMERR_OK;
}

static int32_t ChSecondIndexDelete(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret;
    for (uint32_t i = 0; i < recordCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f2 = i % MAX_CYCLE_COUNT;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_hashcluster"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChUniqueSecondIndexCheck(GmcStmtT *stmt, uint32_t primKeyValue, int32_t secIdxValue)
{
    int32_t f3 = secIdxValue;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_vrfid"));
    int32_t ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isFinished;
    ret = GmcFetch(stmt, &isFinished);
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull;
    uint32_t propSize;
    uint32_t f1Res;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "F1", &propSize));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1Res, propSize, &isNull));
    EXPECT_EQ(primKeyValue, f1Res);
    return GMERR_OK;
}

static uint32_t ChSecondIndexScanDataCheck(GmcStmtT *stmt)
{
    Status ret;
    uint32_t fetchCount = 0;
    while (fetchCount < MAX_RECORD_COUNT) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        fetchCount++;
    }
    return fetchCount;
}

static int32_t ChSecondIndexScan(GmcStmtT *stmt, const char *labelName, uint32_t keyValue, uint32_t count)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f2 = keyValue;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4_hashcluster"));
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t scanCount = ChSecondIndexScanDataCheck(stmt);
    EXPECT_EQ(count, scanCount);
    return GMERR_OK;
}

static int32_t ChUniqueSecondIndexScan(GmcStmtT *stmt, uint32_t count, const char *labelName, int32_t delta)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < count; i++) {
        EXPECT_EQ(GMERR_OK, ChUniqueSecondIndexCheck(stmt, i, i - delta));
    }
    return GMERR_OK;
}

static void StCheckHcWriteBytesAndDeleteBytes(uint32_t writeRowCnt, uint32_t deleteRowCnt, const char *filter)
{
    // 获取rowSize，用来校验
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter, cmdOutput1, 64);
    uint32_t rowSize = atoi(cmdOutput1);

    // 校验writeBytes
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "WRITE_BYTES", filter, cmdOutput2, 64);
    uint32_t writeBytes = atoi(cmdOutput2);
    uint32_t writeTotalCheck = rowSize * writeRowCnt;
    EXPECT_EQ(writeBytes, writeTotalCheck);

    // 校验deleteBytes
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResultFilter("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "DELETE_BYTES", filter, cmdOutput3, 64);
    uint32_t deleteBytes = atoi(cmdOutput3);
    uint32_t deleteTotalCheck = rowSize * deleteRowCnt;
    EXPECT_EQ(deleteBytes, deleteTotalCheck);
}

// 聚簇容器适配视图SERVER_MEMORY_OVERHEAD
static void StCheckHcOriginalDataSize(uint32_t writeRowCnt, uint32_t deleteRowCnt, const char *filter)
{
    // 1.获取rowSize，用来校验
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter, cmdOutput1, 64);
    uint32_t rowSize = atoi(cmdOutput1);

    // 2.校验视图SERVER_MEMORY_OVERHEAD中的ORIGINAL_DATA_SIZE字段（初始的数据大小，大小为deleteBytes-writeBytes）
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput2, 64);
    uint64_t originalDataSize = atoi(cmdOutput2);
    uint64_t checkSize = (writeRowCnt - deleteRowCnt) * rowSize;
    EXPECT_EQ(originalDataSize, checkSize);
}

// 创多个表，聚簇容器适配视图SERVER_MEMORY_OVERHEAD
static void StCheckHcOriginalDataSizeWithTwoTables(
    uint32_t writeRowCnt, uint32_t deleteRowCnt, const char *filter1, const char *filter2)
{
    // 1.获取rowSize1，用来校验
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter1, cmdOutput1, 64);
    uint32_t rowSize1 = atoi(cmdOutput1);

    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter(
        "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "FIX_ROW_DATA_SIZE", filter2, cmdOutput2, 64);
    uint32_t rowSize2 = atoi(cmdOutput2);

    // 2.校验视图SERVER_MEMORY_OVERHEAD中的ORIGINAL_DATA_SIZE字段
    char cmdOutput3[64] = {0};
    (void)memset_s(cmdOutput3, sizeof(cmdOutput3), 0, sizeof(cmdOutput3));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "ORIGINAL_DATA_SIZE", cmdOutput3, 64);
    uint64_t originalDataSize = atoi(cmdOutput3);
    uint64_t checkSize = (writeRowCnt - deleteRowCnt) * (rowSize1 + rowSize2);
    EXPECT_EQ(originalDataSize, checkSize);
}

TEST_F(StClientDirectWriteClusteredHashStmg, testGetKeyCount)
{
    // 先插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));

    // 一条记录大小219，一共插入500条
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0

    // 获取全表记录数
    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(dwStmt, g_labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(dwStmt, &count));
    EXPECT_EQ(MAX_RECORD_COUNT, count);
}

TEST_F(StClientDirectWriteClusteredHashStmg, testInsert)
{
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    ASSERT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));

    system("gmsysview -q V\\$STORAGE_MEMDATA_STAT |grep -A 15 INSTANCE_ID");
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
}

TEST_F(StClientDirectWriteClusteredHashStmg, testInsertFull)
{
    system("gmimport -c vschema -t ip4forward_big -f ./st_file/ip4forward_big.gmjson");
    const char *labelName = "ip4forward_big";
    uint32_t firstMaxTupleNum = 0;
    ASSERT_EQ(GMERR_OK, ChInsertVertexDataFull(dwStmt, labelName, &firstMaxTupleNum));

    GmcConnT *trunConn = NULL;
    GmcStmtT *trunStmt = NULL;
    AllocConnAndStmt(&trunConn, &trunStmt);
    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(trunStmt, labelName));
    FreeConnAndStmt(trunConn, trunStmt);

    GmcConnT *csConn = NULL;
    GmcStmtT *csStmt = NULL;
    AllocConnAndStmt(&csConn, &csStmt);
    uint32_t csMaxTupleNum = 0;
    ASSERT_EQ(GMERR_OK, ChInsertVertexDataFull(csStmt, labelName, &csMaxTupleNum));
    FreeConnAndStmt(csConn, csStmt);

    // 第一次写满存储内存的 tuple 数量少于或等于后续写满存储的 tuple 数量
    EXPECT_LE(firstMaxTupleNum, csMaxTupleNum);

    AllocConnAndStmt(&trunConn, &trunStmt);
    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(trunStmt, labelName));
    FreeConnAndStmt(trunConn, trunStmt);

    uint32_t dwMaxTupleNum = 0;
    ASSERT_EQ(GMERR_OK, ChInsertVertexDataFull(dwStmt, labelName, &dwMaxTupleNum));
    EXPECT_EQ(dwMaxTupleNum, csMaxTupleNum);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dwStmt, labelName));
}

TEST_F(StClientDirectWriteClusteredHashStmg, testReplace)
{
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    ASSERT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    ASSERT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    ASSERT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT / 2, g_labelName, 1));
    ASSERT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));
    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));
}

TEST_F(StClientDirectWriteClusteredHashStmg, testInsertOrReplaceErrorNoPrimaryKey)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(dwStmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f2 = 66 % MAX_CYCLE_COUNT;
    int32_t f3 = 66 - 2;
    uint32_t f4 = 66 % (MAX_CYCLE_COUNT * 2);
    ret = GmcSetVertexProperty(dwStmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(dwStmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(dwStmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(dwStmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(dwStmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(dwStmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
}

TEST_F(StClientDirectWriteClusteredHashStmg, testUpdate)
{
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    ASSERT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    int32_t delta = 3;
    ASSERT_EQ(GMERR_OK, ChUpdateVertexData2(dwStmt, MAX_RECORD_COUNT / 2, g_labelName, 1, delta));
    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, (uint32_t)(MAX_RECORD_COUNT / 2), g_labelName, delta));
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));
}

TEST_F(StClientDirectWriteClusteredHashStmg, testPKDirectRead)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 直连读读取数据并进行比较
    ASSERT_EQ(GMERR_OK, ChReadVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
}

TEST_F(StClientDirectWriteClusteredHashStmg, testStructRead)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 结构化直连读数据
    ASSERT_EQ(GMERR_OK, ChReadStructVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput1, 64);
    uint64_t expansionRatio1 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio1, 0u);  // 校验expansionRatio>0
}

TEST_F(StClientDirectWriteClusteredHashStmg, TestFullScan)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 全表扫描记录
    ASSERT_EQ(GMERR_OK, DirectReadByFullScan(dwStmt, MAX_RECORD_COUNT, g_labelName));
}

// 直连写暂不支持基于二级索引 delete
// 修改后，直连写不支持的场景会自动通过C/S路径写入，不再报错，先DISABLE掉
TEST_F(StClientDirectWriteClusteredHashStmg, testSecondIndexDelete)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0

    // 根据二级索引删除，不支持
    ASSERT_EQ(GMERR_OK, ChSecondIndexDelete(dwStmt, MAX_RECORD_COUNT, g_labelName));
}

TEST_F(StClientDirectWriteClusteredHashStmg, testSecondIndexScan)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    // 先插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput, 64);
    uint64_t expansionRatio = atoi(cmdOutput);
    EXPECT_GT(expansionRatio, 0u);  // 校验expansionRatio>0
    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT / MAX_CYCLE_COUNT)));
}

TEST_F(StClientDirectWriteClusteredHashStmg, testMutipleLabel)
{
    const char *filter = "LABEL_NAME=\'ip4forward\'";
    const char *filter2 = "LABEL_NAME=\'testT\'";
    // 先向ip4forward表插入若干数据
    ASSERT_EQ(GMERR_OK, ChInsertVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    StCheckHcOriginalDataSize(MAX_RECORD_COUNT, 0, filter);
    // 校验膨胀率
    char cmdOutput1[64] = {0};
    (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput1, 64);
    uint64_t expansionRatio1 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio1, 0u);  // 校验expansionRatio>0
    // 再向testT表插入若干数据
    ASSERT_EQ(GMERR_OK, ChReplaceVertexDataV2(dwStmt, MAX_RECORD_COUNT, g_testLabelName));
    StCheckHcWriteBytesAndDeleteBytes(MAX_RECORD_COUNT, 0, filter);
    // 这里有两个表在这，ori应该是两个表的size相加
    StCheckHcOriginalDataSizeWithTwoTables(MAX_RECORD_COUNT, 0, filter, filter2);

    // 校验膨胀率
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$SERVER_MEMORY_OVERHEAD", "EXPANSION_RATIO", cmdOutput2, 64);
    uint64_t expansionRatio2 = atoi(cmdOutput1);
    EXPECT_GT(expansionRatio2, 0u);  // 校验expansionRatio>0

    // 视图查出来是 0字节
    uint32_t cyclesCount = 5;
    for (uint32_t i = 0; i < cyclesCount; ++i) {
        // 先全表扫描ipforward4表
        ASSERT_EQ(GMERR_OK, DirectReadByFullScan(dwStmt, MAX_RECORD_COUNT, g_labelName));

        // 主键直连读test表
        ASSERT_EQ(GMERR_OK, ChPkReadVertexDataV2(dwStmt, MAX_RECORD_COUNT, g_testLabelName));

        // 结构化直连读ipforward4表
        ASSERT_EQ(GMERR_OK, ChReadStructVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName));
    }
    ASSERT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT, g_labelName, 0));
    ASSERT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));

    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 0, (uint32_t)(MAX_RECORD_COUNT)));
    ASSERT_EQ(GMERR_OK, ChReplaceVertexData(dwStmt, MAX_RECORD_COUNT / 2, g_labelName, 1));
    ASSERT_EQ(GMERR_OK, ChUniqueSecondIndexScan(dwStmt, MAX_RECORD_COUNT, g_labelName, 2));
    // 根据二级索引扫描
    ASSERT_EQ(GMERR_OK, ChSecondIndexScan(dwStmt, g_labelName, 1, (uint32_t)(MAX_RECORD_COUNT / 2)));

    // 再向testT表插入若干数据
    ASSERT_EQ(GMERR_OK, ChReplaceVertexDataV2(dwStmt, MAX_RECORD_COUNT * 2, g_testLabelName));
    for (uint32_t i = 0; i < cyclesCount; ++i) {
        // 主键直连读test表
        ASSERT_EQ(GMERR_OK, ChPkReadVertexDataV2(dwStmt, MAX_RECORD_COUNT * 2, g_testLabelName));
    }
}
