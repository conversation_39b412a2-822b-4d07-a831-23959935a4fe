/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for account check of direct write dml operations
 * Author: maojinyong
 * Create: 2023-04-11
 */

#include <string.h>
#include <math.h>
#include "client_common_st.h"
#include "storage_st_common.h"

class StDirectWriteAccountCheck : public StClient {
public:
    static void SetUpTestCase();
};
void StDirectWriteAccountCheck::SetUpTestCase()
{
    StartDbServerWithConfig("\"enableClusterHash=0\" \"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" "
                            "\"maxSysDynSize=1024\" \"auditLogEnableDML=0\" \"userPolicyMode=1\" "
                            "\"compatibleV3=0\" \"directWrite=1\"");
    st_clt_init();
    GmcSignalRegisterNotify();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

static const char *g_label_config = R"({"max_record_count":100000})";
static const char *g_stmg_label_config = R"({"max_record_count":1000000, "status_merge_sub":true})";
// 非分区表
static const char *g_non_partition_label = "non_partition_label";
static const char *g_non_partition_schema =
    R"([{
        "type":"record",
        "name":"non_partition_label",
        "config": {
            "direct_write": true
        },
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"non_partition_label",
                    "name":"K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

// 分区表
static const char *g_partition_label = "partition_label";
static const char *g_partition_schema =
    R"([{
        "type":"record",
        "name":"partition_label",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"partition", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"partition_label",
                    "name":"K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
    }])";

static void InsertVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition, uint8_t partitionId)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partition_label, GMC_OPERATION_INSERT);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_non_partition_label, GMC_OPERATION_INSERT);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (isPartition) {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        } else {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void ReplaceVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition, uint8_t partitionId)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partition_label, GMC_OPERATION_REPLACE);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_non_partition_label, GMC_OPERATION_REPLACE);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (isPartition) {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        } else {
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void UpdateVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partition_label, GMC_OPERATION_UPDATE);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_non_partition_label, GMC_OPERATION_UPDATE);
        }
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        ret = GmcSetIndexKeyName(stmt, "K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 更新字段F1
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void DeleteVertex(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partition_label, GMC_OPERATION_DELETE);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_non_partition_label, GMC_OPERATION_DELETE);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void UpdateVertexCheckVersion(GmcStmtT *stmt, int32_t begin, int32_t end, bool isPartition)
{
    Status ret;
    for (int32_t val = begin; val <= end; val++) {
        if (isPartition) {
            ret = GmcPrepareStmtByLabelName(stmt, g_partition_label, GMC_OPERATION_UPDATE_VERSION);
        } else {
            ret = GmcPrepareStmtByLabelName(stmt, g_non_partition_label, GMC_OPERATION_UPDATE_VERSION);
        }
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        ret = GmcSetIndexKeyName(stmt, "K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 验证场景：全表对账，对账期间不执行DML操作，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, do_nothing_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);  // false表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, originalCount, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, 0);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间不执行DML操作，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, do_nothing_during_checking_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, true);  // true表示开启恢复任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 0, originalCount);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写insert，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_insert_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写插入操作，数据正常插入
    begin = 10, end = 14;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, originalCount, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, addedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写insert，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, dw_insert_during_checking_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写插入操作，数据正常插入
    begin = 10, end = 14;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 0, originalCount);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount + addedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写update，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_update_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写更新操作
    begin = 0, end = 3;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(stmt, begin, end, false);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, originalCount - updatedCount, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, updatedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写update，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, dw_update_during_checking_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写更新操作
    begin = 0, end = 3;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(stmt, begin, end, false);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 0, originalCount - updatedCount);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写replace，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_replace_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写replace操作。更新5条老数据，插入5条新数据。
    begin = 5, end = 14;  // [5, 9]数据存在，对其全量替换；[10, 14]数据不存在，新插入
    uint64_t changedCount = end - begin + 1;
    ReplaceVertex(stmt, begin, end, false, 0);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 5, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, changedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写replace，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, dw_replace_during_checking_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写replace操作。更新5条老数据，插入5条新数据。
    begin = 5, end = 14;  // [5, 9]数据存在，对其全量替换；[10, 14]数据不存在，新插入
    ReplaceVertex(stmt, begin, end, false, 0);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 0, 5);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount + 5);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写delete，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_delete_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写删除
    begin = 0, end = 3;
    uint64_t deletedCount = end - begin + 1;
    DeleteVertex(stmt, begin, end, false);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, originalCount - deletedCount, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, 0);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：状态合并表对账，对账期间执行直连写update, delete，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_stmg_dml_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_stmg_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    CheckVertexCount(stmt, g_non_partition_label, originalCount);

    // 开启全表对账
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写删除和更新
    begin = 0, end = 3;
    uint64_t deletedCount = end - begin + 1;
    DeleteVertex(stmt, begin, end, false);
    begin = 4, end = 7;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(stmt, begin, end, false);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验对账信息和表中记录数
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, originalCount - deletedCount - updatedCount, 0);
    CheckVertexCount(stmt, g_non_partition_label, updatedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行直连写delete，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, dw_delete_during_checking_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，执行直连写删除
    begin = 0, end = 3;
    uint64_t deletedCount = end - begin + 1;
    DeleteVertex(stmt, begin, end, false);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 0, originalCount - deletedCount);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount - deletedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行多种直连写DML操作，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_mixops_during_checking_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // 执行直连写replace，replace 20条数据，不会被老化
    begin = 90, end = 109;
    uint64_t replacedCount = end - begin + 1;
    ReplaceVertex(stmt, begin, end, false, 0);
    // 执行直连写insert，新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 执行直连写update，更新10条数据，不会被老化
    begin = 80, end = 89;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(stmt, begin, end, false);
    // 执行直连写delete，删除10条数据
    begin = 0, end = 9;
    DeleteVertex(stmt, begin, end, false);

    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 70, 0);  // 100-10(replace)-10(update)-10(delete)
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, replacedCount + addedCount + updatedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：全表对账，对账期间执行多种直连写DML操作，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, dw_mixops_during_checking_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // 执行直连写replace，replace 20条数据，不会被老化
    begin = 90, end = 109;
    ReplaceVertex(stmt, begin, end, false, 0);
    // 执行直连写insert，新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    InsertVertex(stmt, begin, end, false, 0);
    // 执行直连写update，更新10条数据，不会被老化
    begin = 80, end = 89;
    UpdateVertex(stmt, begin, end, false);
    // 执行直连写delete，删除10条数据
    begin = 0, end = 9;
    DeleteVertex(stmt, begin, end, false);

    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, true);  // true 表示开启恢复任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    // 原来共有100条数据(0~99)
    // replace: 更新了10条(90~99)，并新插入了10条(100~109)；
    // insert: 新插入了10条(200~209)；
    // update: 更新了10条(80~89)
    // delete: 删除了10条(0~9)
    // 需要恢复的数据：100-10(90~99)-10(80~89)-10(0~9)
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, 0, 70);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount + 10);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：分区对账，对账期间执行多种直连写DML操作，对账结束后开启老化任务
TEST_F(StDirectWriteAccountCheck, dw_mixops_during_checking_partition_aged)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据（分区2：20条；分区3：30条；分区5：50条）
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, 0, 19, true, 2);
    InsertVertex(stmt, 20, 49, true, 3);
    InsertVertex(stmt, 50, 99, true, 5);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_partition_label, originalCount);

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(stmt, g_partition_label, partitionId);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // 执行直连写replace，20条数据(更新10条，插入10条)
    begin = 90, end = 109;
    uint64_t replacedCount = end - begin + 1;
    ReplaceVertex(stmt, begin, end, true, partitionId);
    // 执行直连写insert，新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(stmt, begin, end, true, partitionId);
    // 执行直连写update，更新10条数据，不会被老化
    begin = 80, end = 89;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(stmt, begin, end, true);
    // 执行直连写delete，删除10条数据（删除的是分区2中的数据，因此不影响分区5的统计）
    begin = 0, end = 9;
    DeleteVertex(stmt, begin, end, true);

    // 结束对账
    ret = GmcEndCheck(stmt, g_partition_label, partitionId, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    CheckAccountInfo(stmt, g_partition_label, partitionId, 30, 0);  // 50-10(replace)-10(update)
    // 对账后：查询表中的记录数
    uint64_t remainingCount = 10 + 30;  // 分区2剩下的10条，分区3剩下的30条
    CheckVertexCount(stmt, g_partition_label, replacedCount + addedCount + updatedCount + remainingCount);

    ret = GmcDropVertexLabel(stmt, g_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：分区对账，对账期间执行多种直连写DML操作，对账结束后开启恢复任务
TEST_F(StDirectWriteAccountCheck, dw_mixops_during_checking_partition_recovery)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据（分区2：20条；分区3：30条；分区5：50条）
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, 0, 19, true, 2);
    InsertVertex(stmt, 20, 49, true, 3);
    InsertVertex(stmt, 50, 99, true, 5);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_partition_label, originalCount);

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(stmt, g_partition_label, partitionId);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // 执行直连写replace，20条数据(更新10条，插入10条)
    begin = 90, end = 109;
    ReplaceVertex(stmt, begin, end, true, partitionId);
    // 执行直连写insert，新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    InsertVertex(stmt, begin, end, true, partitionId);
    // 执行直连写update，更新10条数据，不会被老化
    begin = 80, end = 89;
    UpdateVertex(stmt, begin, end, true);
    // 执行直连写delete，删除10条数据（删除的是分区2中的数据，因此不影响分区5的统计）
    begin = 0, end = 9;
    DeleteVertex(stmt, begin, end, true);

    // 结束对账
    ret = GmcEndCheck(stmt, g_partition_label, partitionId, true);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    // 50-10-10
    CheckAccountInfo(stmt, g_partition_label, partitionId, 0, 30);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_partition_label, originalCount + 10);

    ret = GmcDropVertexLabel(stmt, g_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 直连写不支持 GMC_OPERATION_UPDATE_VERSION 操作，该操作仍然走CS模式
TEST_F(StDirectWriteAccountCheck, dw_update_version_during_checking)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10条数据
    int32_t begin = 0, end = 9;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账期间，更新记录的对账版本号
    begin = 0, end = 3;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertexCheckVersion(stmt, begin, end, false);
    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, g_non_partition_label, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_NORMAL, checkInfo->checkStatus);
    EXPECT_EQ(originalCount - updatedCount, checkInfo->shouldAgedCnt);  // 未更新的数据会被老化
    EXPECT_EQ(0u, checkInfo->shouldRecoveryCnt);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, updatedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
case 1：如果在对账开始之前就执行完以下所有dml操作，则：
应该老化的数据 = 10000+100(replace新增)+100(insert)-100(delete)=10100
表最终的记录数 = 0

case 2: 如果在对账期间执行完以下所有dml操作，则：
应该老化的数据 = 10000-100(replace更新)-100(update)-100(delete)=9700
表最终的记录数 = 200(replace)+100(insert)+100(update)=400

case 3：如果在对账结束之后才开始执行以下dml操作，则：
应该老化的数据 = 10000。但因为dml和老化线程并发，实际老化的数据可能小于10000
表最终的记录数 = 100(replace新增)+100(insert)+X=200+X (X is unknow)

case 4：如果在对账期间执行了部分dml操作，则数据介于上述情况之间，是不确定的
*/
void *DoDirectWriteDml(void *args)
{
    GmcConnT *dwConn;
    GmcStmtT *dwStmt;
    CreateSyncConnectionAndStmt(&dwConn, &dwStmt);

    // 直连写replace200条数据(9900~9999更新, 10000~10099插入)
    int32_t begin = 9900, end = 10099;
    ReplaceVertex(dwStmt, begin, end, false, 0);

    // 直连写insert，插入100条数据
    begin = 20000, end = 20099;
    InsertVertex(dwStmt, begin, end, false, 0);

    // 直连写update，更新100条数据
    begin = 0, end = 99;
    UpdateVertex(dwStmt, begin, end, false);

    // 直连写delete，删除100条数据
    begin = 100, end = 199;
    DeleteVertex(dwStmt, begin, end, false);

    DestroyConnectionAndStmt(dwConn, dwStmt);
    return NULL;
}

// 验证场景：线程1开启对账，线程2执行直连写DML，观察checkInfo信息
TEST_F(StDirectWriteAccountCheck, dw_multi_thread_test)
{
    GmcConnT *csConn;
    GmcStmtT *csStmt;
    CreateSyncConnectionAndStmt(&csConn, &csStmt);

    Status ret = GmcCreateVertexLabel(csStmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置10000条数据
    int32_t begin = 0, end = 9999;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(csStmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(csStmt, g_non_partition_label, originalCount);

    pthread_t tid;
    pthread_create(&tid, NULL, DoDirectWriteDml, NULL);

    // 开启对账
    ret = GmcBeginCheck(csStmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 结束对账
    ret = GmcEndCheck(csStmt, g_non_partition_label, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid, NULL);

    // 校验对账信息
    // 在并发场景下，shouldAgedCnt和realAgedCnt可能不相等，最多检查10次后退出
    GmcCheckInfoT *checkInfo = NULL;
    GmcCheckStatusE checkStatus;
    int cnt = 0;
    do {
        ret = GmcGetCheckInfo(csStmt, g_non_partition_label, 0xff, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCheckStatus(checkInfo, &checkStatus);
        EXPECT_EQ(GMERR_OK, ret);
        printf("The check status is %d.\n", checkStatus);
        printf("shouldAgedCnt: %lu, realAgedCnt: %lu\n", checkInfo->shouldAgedCnt, checkInfo->realAgedCnt);
        DbSleep(100);
        cnt++;
    } while (checkInfo->shouldAgedCnt != checkInfo->realAgedCnt && cnt < 10);

    // 对账后：查询表中的记录数
    ret = GmcPrepareStmtByLabelName(csStmt, g_non_partition_label, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t actualCount = 0;
    ret = GmcGetVertexRecordCount(csStmt, &actualCount);
    EXPECT_EQ(GMERR_OK, ret);
    printf("After account checking, record count: %lu\n", actualCount);

    ret = GmcDropVertexLabel(csStmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(csConn, csStmt);
}

// 验证场景：通过对账视图验证统计信息
TEST_F(StDirectWriteAccountCheck, dw_checkinfo_dfx_sysview)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // 执行直连写replace，replace 20条数据，不会被老化
    begin = 90, end = 109;
    uint64_t replacedCount = end - begin + 1;
    ReplaceVertex(stmt, begin, end, false, 0);
    // 执行直连写insert，新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    uint64_t addedCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 执行直连写update，更新10条数据，不会被老化
    begin = 80, end = 89;
    uint64_t updatedCount = end - begin + 1;
    UpdateVertex(stmt, begin, end, false);
    // 执行直连写delete，删除10条数据
    begin = 0, end = 9;
    DeleteVertex(stmt, begin, end, false);

    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    // 通过视图校验对账信息
    DbUsleep(1000);  // 等待一段时间，确保后台老化任务执行完成
    char cmdOutput[64] = {0};
    const char *filter = "VERTEX_LABEL_NAME=\'non_partition_label\' | grep -A 11 \'PARTITION_ID\'";
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "SHOULD_AGED_CNT", filter, cmdOutput, 64);
    uint32_t shouldAgedCnt = atoi(cmdOutput);
    EXPECT_EQ(70u, shouldAgedCnt);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "REAL_AGED_CNT", filter, cmdOutput, 64);
    uint32_t realAgedCnt = atoi(cmdOutput);
    EXPECT_EQ(shouldAgedCnt, realAgedCnt);

    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, replacedCount + addedCount + updatedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteAccountCheck, dw_checkinfo_dfx_sysview_2)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);
    // 开启对账（全表对账）
    ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间，执行truncate操作
    GmcDeleteAllFast(stmt, g_non_partition_label);

    // 结束对账
    ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);  // false 表示开启老化任务
    EXPECT_EQ(GMERR_OK, ret);

    // 通过视图校验对账信息
    DbUsleep(1000);  // 等待一段时间，确保后台老化任务执行完成
    char cmdOutput[64] = {0};
    const char *filter = "VERTEX_LABEL_NAME=\'non_partition_label\' | grep -A 11 \'PARTITION_ID\'";
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "SHOULD_TRUNCATE_CNT", filter, cmdOutput, 64);
    uint32_t shouldAgedCnt = atoi(cmdOutput);
    EXPECT_EQ(100u, shouldAgedCnt);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "REAL_TRUNCATED_CNT", filter, cmdOutput, 64);
    uint32_t realAgedCnt = atoi(cmdOutput);
    EXPECT_EQ(shouldAgedCnt, realAgedCnt);

    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, 0);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：分区对账，对账期间执行多种直连写DML操作，对账结束后开启恢复任务，通过视图进行校验
TEST_F(StDirectWriteAccountCheck, dw_checkinfo_dfx_sysview_3)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据（分区2：20条；分区3：30条；分区5：50条）
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1;
    InsertVertex(stmt, 0, 19, true, 2);
    InsertVertex(stmt, 20, 49, true, 3);
    InsertVertex(stmt, 50, 99, true, 5);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_partition_label, originalCount);

    // 开启对账（分区对账）
    uint8_t partitionId = 5;
    ret = GmcBeginCheck(stmt, g_partition_label, partitionId);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账期间
    // 执行直连写replace，20条数据(更新10条，插入10条)
    begin = 90, end = 109;
    ReplaceVertex(stmt, begin, end, true, partitionId);
    // 执行直连写insert，新插入10条数据，不会被老化，不影响统计
    begin = 200, end = 209;
    InsertVertex(stmt, begin, end, true, partitionId);
    // 执行直连写update，更新10条数据，不会被老化
    begin = 80, end = 89;
    UpdateVertex(stmt, begin, end, true);
    // 执行直连写delete，删除10条数据（删除的是分区2中的数据，因此不影响分区5的统计）
    begin = 0, end = 9;
    DeleteVertex(stmt, begin, end, true);

    // 结束对账
    ret = GmcEndCheck(stmt, g_partition_label, partitionId, true);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验对账信息
    // 50-10-10
    CheckAccountInfo(stmt, g_partition_label, partitionId, 0, 30);
    // 通过视图校验对账信息
    DbUsleep(1000);  // 等待一段时间，确保后台老化任务执行完成
    char cmdOutput[64] = {0};
    const char *filter = "VERTEX_LABEL_NAME=\'partition_label\' | grep -A 11 \'PARTITION_ID: 5\'";
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "SHOULD_RECOVERY_CNT", filter, cmdOutput, 64);
    uint32_t shouldRecoveryCnt = atoi(cmdOutput);
    EXPECT_EQ(30u, shouldRecoveryCnt);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_CHECK_INFO", "REAL_RECOVERY_CNT", filter, cmdOutput, 64);
    uint32_t realRecoveryCnt = atoi(cmdOutput);
    EXPECT_EQ(shouldRecoveryCnt, realRecoveryCnt);

    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_partition_label, originalCount + 10);

    ret = GmcDropVertexLabel(stmt, g_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：反复开启、结束对账，操作次数超过255次。看护用例DML_088_021发现的问题
TEST_F(StDirectWriteAccountCheck, dw_multi_check)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_non_partition_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账前：预置100条数据
    int32_t begin = 0, end = 99;
    uint64_t originalCount = end - begin + 1, changedCount = 0;
    InsertVertex(stmt, begin, end, false, 0);
    // 对账前：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, originalCount);

    int cnt = 0, repeatTimes = 300;
    while (cnt < repeatTimes) {
        // 开启对账（全表对账）
        ret = GmcBeginCheck(stmt, g_non_partition_label, 0xff);
        EXPECT_EQ(GMERR_OK, ret);
        // 对账期间，执行直连写replace操作。更新50条数据
        begin = 0, end = 49;
        changedCount = end - begin + 1;
        ReplaceVertex(stmt, begin, end, false, 0);
        // 结束对账
        ret = GmcEndCheck(stmt, g_non_partition_label, 0xff, false);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    // 校验对账信息
    CheckAccountInfo(stmt, g_non_partition_label, 0xff, originalCount - changedCount, 0);
    // 对账后：查询表中的记录数
    CheckVertexCount(stmt, g_non_partition_label, changedCount);

    ret = GmcDropVertexLabel(stmt, g_non_partition_label);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}
