/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Datalog ST .
 * Author: GMDBv5 EE Team
 * Create: 2022-9-7
 */

#include <iostream>
#include <climits>
#include "tools_st_common.h"
#include "client_common_st.h"
#include "ee_plan_state.h"
#include "gmc_batch_check.h"
#include "gmc_graph_check.h"
#include "client_st_exec_datalog.h"
#include "st_common.h"
#define BUF_LEN 1024

using namespace std;

#define STRUCT_VAR_SIZE (sizeof(uint16_t) + sizeof(uint8_t *))

class ClientStExecAggDatalog : public ClientStExecDatalog {
protected:
    // %table ComplexTable(a: str, b: str, c: int4)
    Status InsertComplexTable(
        GmcConnT *syncConn, GmcStmtT *syncStmt, const char *labelName, int32_t records[][5], int num);
    void ScanOutComplexTable(GmcStmtT *syncStmt, const char *labelName);
};
INSTANTIATE_TEST_CASE_P(ExecAggDatalog, ClientStExecAggDatalog,
    ::testing::Values(StClientTestData{.isAsync = false}, StClientTestData{.isAsync = true}));

Status ClientStExecAggDatalog::InsertComplexTable(
    GmcConnT *syncConn, GmcStmtT *syncStmt, const char *labelName, int32_t records[][5], int num)
{
    Status ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < num; ++i) {
        unsigned int stringLen = 10;
        if (stringLen == 0) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        uint8_t *d = (uint8_t *)DbDynMemCtxAlloc(syncStmt->memCtx, stringLen + 1);
        if (d == NULL) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        memset_s(d, stringLen + 1, 0, stringLen + 1);
        (void)snprintf_s((char *)d, stringLen + 1, stringLen + 1, "d%08d", records[i][0]);
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(syncStmt, "a", GMC_DATATYPE_STRING, d, (strlen((char *)d))));

        memset_s(d, stringLen + 1, 0, stringLen + 1);
        (void)snprintf_s((char *)d, stringLen + 1, stringLen + 1, "d%08d", records[i][1]);
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(syncStmt, "b", GMC_DATATYPE_STRING, d, (strlen((char *)d))));

        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(syncStmt, "c", GMC_DATATYPE_INT32, &records[i][2], sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK,
            GmcSetVertexProperty(syncStmt, "dtlReservedCount", GMC_DATATYPE_INT32, &records[i][3], sizeof(int32_t)));

        EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, syncStmt));
    }
    EXPECT_EQ(GMERR_OK, DtlBatchExecute(syncConn, batch, true, num));

    return GmcBatchDestroy(batch);
}

void ClientStExecAggDatalog::ScanOutComplexTable(GmcStmtT *syncStmt, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return;
    }

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return;
    }
    bool isFinish = false, isNull;
    while (!isFinish) {
        ret = GmcFetch(syncStmt, &isFinish);  // 获取扫描的顶点
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish || ret != GMERR_OK) {
            break;
        }

        unsigned int strASize = 10;
        GmcGetVertexPropertySizeByName(syncStmt, "a", &strASize);
        if (strASize == 0) {
            return;
        }
        char *a = (char *)DbDynMemCtxAlloc(syncStmt->memCtx, strASize + 1);
        if (a == NULL) {
            return;
        }
        memset_s(a, strASize + 1, 0, strASize + 1);
        GmcGetVertexPropertyByName(syncStmt, "a", a, strASize, &isNull);

        unsigned int strBSize = 10;
        GmcGetVertexPropertySizeByName(syncStmt, "b", &strBSize);
        if (strBSize == 0) {
            return;
        }
        char *b = (char *)DbDynMemCtxAlloc(syncStmt->memCtx, strBSize + 1);
        if (b == NULL) {
            return;
        }
        memset_s(b, strBSize + 1, 0, strBSize + 1);
        GmcGetVertexPropertyByName(syncStmt, "b", b, strBSize, &isNull);

        int32_t c, count;
        GmcGetVertexPropertyByName(syncStmt, "c", &c, sizeof(int32_t), &isNull);
        GmcGetVertexPropertyByName(syncStmt, "dtlReservedCount", &count, sizeof(int32_t), &isNull);

        cout << a << ", " << b << ", " << c << ", " << count << endl;
    }
}

/*
 * ################################## Many to One 聚合 ################################
 * %table A(k: int4, p: int4, v: int4)
 * %table B(max: int4, min: int4, k: int4, p: int4)
 * %aggregate min_max(v: int4 -> min: int4, max: int4){
      ordered
 * }

 * B(max, min, k, p) :- A(k, p, v) GROUP-BY (k, p) min_max(v, min, max)
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne1)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    Status ret = CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);
    ASSERT_EQ(GMERR_OK, ret);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 1");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 4}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    int32_t records3[][5] = {{-2, 0, 1, 1, 1}, {-1, 0, 1, 1, 2}, {+1, 0, 1, 1, 3}, {-1, 0, 1, 1, 4}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to Many 聚合 ################################
 * %table A(k: int4, a: int4, b: int4)
 * %table B(k: int4, i: int4, v: int4)
 * %aggregate expand(a: int4, b: int4 -> i: int4, c: int4){
        many_to_many
        ordered
 * }

 * B(k, i, v) :- A(k, a, b) GROUP-BY (k) expand(a, b, i, v).
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToMany)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_many_to_many";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 4}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    int32_t records3[][5] = {{-2, 0, 1, 1, 1}, {-1, 0, 1, 1, 2}, {+1, 0, 1, 1, 3}, {-1, 0, 1, 1, 4}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Agg  定长变长综合字段 ################################
 * %table A(a: str, b: str, c: int4)
 * %table (a: str, b: str, c: int4)
 * %aggregate getSum(c: int4 -> sum: int4)

 * B(a, b, sum) :- A(a, b, c) GROUP-BY (a, b) getSum(c, sum).
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggStrAndNor)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_str_and_normal_prop";
    char *functionFileName = (char *)"client_st_agg_getSum";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, functionFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertComplexTable(syncConn, syncStmt, labelNameInp, records1, sizeof(records1) / sizeof(records1[0]));
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutComplexTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutComplexTable(g_stmt, labelNameOut);

    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 4}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertComplexTable(syncConn, syncStmt, labelNameInp, records2, sizeof(records2) / sizeof(records2[0]));
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutComplexTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutComplexTable(g_stmt, labelNameOut);

    int32_t records3[][5] = {{-2, 0, 1, 1, 1}, {-1, 0, 1, 1, 2}, {+1, 0, 1, 1, 3}, {-1, 0, 1, 1, 4}};
    ret = InsertComplexTable(syncConn, syncStmt, labelNameInp, records3, sizeof(records3) / sizeof(records3[0]));
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutComplexTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutComplexTable(g_stmt, labelNameOut);

    const char *cmd = "gmsysview -q V\\$CATA_NAMESPACE_INFO -f NAMESPACE_NAME=\"public\" | grep NAMESPACE_ID";
    ASSERT_EQ(1, CompareCmdOutput("./agg_getSum.log", cmd));
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Agg  修改其他表 (读A_org 写入 C)################################
 * %table A(k: int4, p: int4, v: int4)
 * %table C(k: int4, p: int4, v: int4)
 * %table B(k: int4, p: int4, sum: int4)
 * %aggregate getSum(v: int4 -> sum: int4){
      access_delta (C),
      access_current (A)
 * }

 * B(k, p, sum) :- A(k, p, v) GROUP-BY (k, p) getSum(v, sum)
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggWriteTable)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    const char *labelNameOther = "C";
    char *fileName = (char *)"client_st_agg_write_table";
    char *functionFileName = (char *)"client_st_agg_read_org_write_to_delta";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, functionFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);
    cout << "====================== " << labelNameOther << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOther);

    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 2}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);
    cout << "====================== " << labelNameOther << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOther);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## 聚合表自定义索引 ################################
 * %table A(k: int4, p: int4, v: int4){index(1(k,p)), index(2(p,v))}
 * %table B(max: int4, min: int4, k: int4, p: int4){index(1(min,p))}
 * %aggregate min_max(v: int4 -> min: int4, max: int4){
      ordered
 * }

 * B(max, min, k, p) :- A(k, p, v) GROUP-BY (k, p) min_max(v, min, max)
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggWithOtherIndex)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_with_other_index";
    char *functionFileName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, functionFileName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 4}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);

    int32_t records3[][5] = {{-2, 0, 1, 1, 1}, {-1, 0, 1, 1, 2}, {+1, 0, 1, 1, 3}, {-1, 0, 1, 1, 4}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## 输入表带忽略字段 ################################
 * %table A(a: int4, b: int4, c: int4)
 * %table B(max: int4, min: int4, k: int4)
 * %aggregate min_max(v: int4 -> min: int4, max: int4){
      ordered
 * }

 * B(max, min, k) :- A(k, v, -) GROUP-BY (k) min_max(v, min, max)
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggIgnorPropInInpTable)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_ingnor_prop_in_inpTable";
    char *udfFileName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][5] = {
        {-2, 0, 1, 1, 1}, {-1, 0, 1, 1, 2}, {+1, 0, 1, 1, 3}, {-1, 0, 2, 1, 2}, {-1, 0, 2, 2, 2}, {-1, 0, 2, 3, 2}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    cout << "====================== " << labelNameInp << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameInp);
    cout << "====================== " << labelNameOut << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## 使用agg函数，查看UDF内存视图 ################################
 * %table A(k: int4, p: int4, v: int4)
 * %table B(max: int4, min: int4, k: int4, p: int4)
 * %table C(a: int4, b: int4, c: int4)
 * %aggregate getMinMax(v: int4 -> min: int4, max: int4){
      ordered
 * }
 * %aggregate getSum(c: int4 -> sum: int4)
 *
 * B(max, min, k, p) :- A(k, p, v) GROUP-BY (k, p) getMinMax(v, min, max)
 * C(a, b, sum) :- A(a, b, c) GROUP-BY (a, b) getSum(c, sum).
 */

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggMemoryView)
{
    const char *labelNameInp = "A";
    char *fileName = (char *)"client_st_agg_memory_view";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][5] = {
        {1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1}, {-2, 0, 2, 2, 3}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_UnorderedAggView)
{
    char *fileName = (char *)"client_st_agg_memory_view";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));

    int ret = executeCommand((char *)"gmsysview -q V\\$CATA_UDF_INFO", "CMP_FUNC_NAME: null");
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
}

/*
 * ################################## agg常量约束校验 ################################
 */

// grpBy不支持常量
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyHasHalfConst)
{
    char *fileName = (char *)"client_st_agg_grpby_has_half_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyHasStrConst)
{
    char *fileName = (char *)"client_st_agg_grpby_has_str_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyHasFixConst)
{
    char *fileName = (char *)"client_st_agg_grpby_has_fix_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyHasNumConst)
{
    char *fileName = (char *)"client_st_agg_grpby_has_num_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// grpBy中在输入表中的字段不能是常量
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotExitInpNum)
{
    char *fileName = (char *)"client_st_agg_grpby_not_exit_inp_num";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotExitInpStr)
{
    char *fileName = (char *)"client_st_agg_grpby_not_exit_inp_str";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotExitInpByte)
{
    char *fileName = (char *)"client_st_agg_grpby_not_exit_inp_byte";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// grpBy在输出表中的字段不能是常量
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotExitOutNum)
{
    char *fileName = (char *)"client_st_agg_grpby_not_exit_out_num";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotExitOutStr)
{
    char *fileName = (char *)"client_st_agg_grpby_not_exit_out_str";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotExitOutByte)
{
    char *fileName = (char *)"client_st_agg_grpby_not_exit_out_byte";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// grpBy中的字段必须都出现在输出表中
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotAllOut)
{
    char *fileName = (char *)"client_st_agg_grpby_not_all_exit_out";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// grpBy中的字段必须在输入表中出现
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggGrpbyNotALLExitInp)
{
    char *fileName = (char *)"client_st_agg_grpby_not_all_exit_inp";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// 输入表常量string类型变长字段不能超出最大长度要求
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggInpConstStrMaxLen)
{
    char *fileName = (char *)"client_st_agg_inp_const_str_max_len";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// 输出表常量string类型变长字段不能超出最大长度要求
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggOutConstStrMaxLen)
{
    char *fileName = (char *)"client_st_agg_out_const_str_max_len";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// 输入表常量fix类型变长字段不能超出最大长度要求
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggInpConstFixMaxLen)
{
    char *fileName = (char *)"client_st_agg_inp_const_fix_max_len";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// 输出表常量fix类型变长字段不能超出最大长度要求
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggOutConstFixMaxLen)
{
    char *fileName = (char *)"client_st_agg_out_const_fix_max_len";
    char *udfName = (char *)"client_st_agg_many_to_one";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

// 输出表不支持重复投影
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggOutGrpByNotReapeatPrj)
{
    char *fileName = (char *)"client_st_agg_out_grpby_repeat_project";
    char *udfName = (char *)"client_st_agg_many_to_many";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggOutPropNotReapeatPrj)
{
    char *fileName = (char *)"client_st_agg_out_prop_repeat_project";
    char *udfName = (char *)"client_st_agg_many_to_many";
    // 编译失败
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfName));
}

/*
 * ################################## 变长字段、定长字段最大长度视图打印################################
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_UnorderAggViewMaxLen)
{
    char *fileName = (char *)"client_st_agg_unorder_view_maxlen";
    char *udfName = (char *)"client_st_agg_getSum";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    Status ret = system("gmsysview -q  V\\$DATALOG_PLAN_EXPLAIN_INFO");
    EXPECT_EQ(GMERR_OK, ret);

    ret = system("gmsysview -q  V\\$CATA_UDF_INFO");
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
}

/*
 * ################################## Many to One 聚合 输入表包含常量################################
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpNumConst)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 1");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 1},
        {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 3, 1, 1}, {1, 0, 2, 1, 2}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 2, 4}, {+2, 0, 2, 1, 4}, {1, 0, 2, 2, 3}, {+1, 0, 4, 1, 1},
        {-2, 0, 4, 1, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{-1, 0, 1, 1, 2}, {-1, 0, 2, 2, 3}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 1},
        {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {1, 0, 4, 1, 1}, {1, 0, 1, 2, 4}, {-1, 0, 1, 1, 5}, {1, 0, 4, 4, 3},
        {2, 0, 2, 1, 4}, {-2, 0, 4, 1, 2}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 5, 1, 1}, {1, 0, 4, 1, 2}, {1, 0, 2, 1, 4}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{1, 0, 1, 1, 5}, {3, 0, 1, 2, 5}, {-2, 0, 2, 1, 4}, {2, 0, 2, 2, 4}, {-1, 0, 4, 1, 1},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{-1, 0, 1, 1, 2}, {-1, 0, 2, 2, 3}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 1},
        {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {1, 0, 1, 2, 4}, {1, 0, 4, 4, 3}, {-2, 0, 4, 1, 2}, {-1, 0, 4, 2, 1},
        {2, 0, 2, 2, 4}, {3, 0, 1, 2, 5}, {1, 0, 3, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][5] = {{1, 0, 3, 1, 1}, {1, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {1, 0, 2, 2, 4}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpStrConst)
{
    const char *labelNameInp = "C";
    const char *labelNameOut = "D";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{1, 0, 3, 3, 1}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 1, 1, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][4] = {{1, 0, 1, 6}, {1, 0, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 2, 4}, {+2, 0, 2, 1, 4}, {1, 0, 2, 2, 3}, {+1, 0, 4, 1, 1},
        {-2, 0, 4, 1, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{1, 0, 3, 3, 1}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 1, 1, 3}, {1, 0, 1, 2, 4}, {-2, 0, 4, 1, 2}, {1, 0, 4, 1, 1}, {1, 0, 4, 4, 3},
        {-1, 0, 1, 1, 5}, {2, 0, 2, 1, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][4] = {{1, 0, 4, 3}, {1, 0, 2, 7}, {1, 0, 1, 11}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{1, 0, 1, 1, 5}, {3, 0, 1, 2, 5}, {-2, 0, 2, 1, 4}, {2, 0, 2, 2, 4}, {-1, 0, 4, 1, 1},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{1, 0, 3, 3, 1}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 1, 1, 3}, {1, 0, 1, 2, 4}, {-2, 0, 4, 1, 2}, {1, 0, 4, 4, 3}, {2, 0, 2, 2, 4},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}, {3, 0, 1, 2, 5}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][4] = {{1, 0, 1, 6}, {1, 0, 2, 3}, {1, 0, 3, 1}, {1, 0, 4, 2}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpFixConst)
{
    const char *labelNameInp = "E";
    const char *labelNameOut = "F";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{-1, 0, 1, 1, 2}, {1, 0, 1, 1, 1}, {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][4] = {{1, 0, 1, 6}, {1, 0, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 2, 4}, {+2, 0, 2, 1, 4}, {1, 0, 2, 2, 3}, {+1, 0, 4, 1, 1},
        {-2, 0, 4, 1, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{-1, 0, 1, 1, 2}, {1, 0, 1, 1, 1}, {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 2, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 3, 3, 1}, {1, 0, 4, 1, 1}, {1, 0, 1, 2, 4}, {2, 0, 2, 1, 4}, {-1, 0, 1, 1, 5},
        {-2, 0, 4, 1, 2}, {1, 0, 4, 4, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][4] = {{1, 0, 4, 3}, {1, 0, 2, 7}, {1, 0, 1, 11}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{1, 0, 1, 1, 5}, {3, 0, 1, 2, 5}, {-2, 0, 2, 1, 4}, {2, 0, 2, 2, 4}, {-1, 0, 4, 1, 1},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{-1, 0, 1, 1, 2}, {1, 0, 1, 1, 1}, {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 2, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 3, 3, 1}, {1, 0, 1, 2, 4}, {-2, 0, 4, 1, 2}, {1, 0, 4, 4, 3}, {-1, 0, 4, 2, 1},
        {1, 0, 3, 1, 1}, {3, 0, 1, 2, 5}, {2, 0, 2, 2, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][4] = {{1, 0, 1, 6}, {1, 0, 2, 3}, {1, 0, 3, 1}, {1, 0, 4, 2}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpTwoConst)
{
    const char *labelNameInp = "G";
    const char *labelNameOut = "H";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][6] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {-1, 0, 1, 1, 2, -1}, {2, 0, 2, 1, 2, 3},
        {1, 0, 1, 1, 1, 2}, {-1, 0, 2, 2, 3, -2}, {3, 0, 3, 3, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][6] = {{3, 0, 3, 3, 1, 1}, {1, 0, 1, 1, 1, 2}, {-1, 0, 2, 2, 3, -2}, {1, 0, 1, 1, 1, 1},
        {2, 0, 2, 1, 2, 3}, {1, 0, 1, 1, 3, 1}, {-1, 0, 1, 1, 2, -1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][4] = {{1, 0, 1, 3}, {1, 0, 3, 1}, {1, 0, 2, -1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][6] = {{1, 0, 1, 1, 1, -1}, {-2, 0, 2, 1, 2, 3}, {2, 0, 1, 1, 2, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][6] = {{3, 0, 3, 3, 1, 1}, {1, 0, 1, 1, 1, 2}, {-1, 0, 2, 2, 3, -2}, {1, 0, 1, 1, 1, 1},
        {1, 0, 1, 1, 3, 1}, {-1, 0, 1, 1, 2, -1}, {1, 0, 1, 1, 1, -1}, {2, 0, 1, 1, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][4] = {{1, 0, 3, 1}, {1, 0, 1, 2}, {1, 0, 2, 2}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][6] = {{1, 0, 1, 1, 1, 1}, {-1, 0, 1, 1, 2, -1}, {-1, 0, 1, 1, 3, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][6] = {{3, 0, 3, 3, 1, 1}, {1, 0, 1, 1, 1, 2}, {-1, 0, 2, 2, 3, -2}, {2, 0, 1, 1, 1, 1},
        {-2, 0, 1, 1, 2, -1}, {1, 0, 1, 1, 1, -1}, {2, 0, 1, 1, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][4] = {{1, 0, 1, 2}, {1, 0, 2, 2}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpThreeConst)
{
    const char *labelNameInp = "I";
    const char *labelNameOut = "J";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][7] = {{1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 1, 3, 1}, {1, 0, 2, 1, 1, -1, 1},
        {1, 0, 1, 2, 1, -1, 1}, {1, 0, 1, 1, 1, -1, 2}, {1, 0, 1, 1, 2, 1, 1}, {1, 0, 1, 1, 3, 3, 1},
        {1, 0, 2, 1, 2, 2, 1}, {1, 0, 1, 2, 2, 2, 1}, {1, 0, 1, 1, 2, -1, 2}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][7] = {{1, 0, 1, 1, 2, 1, 1}, {1, 0, 1, 1, 2, -1, 2}, {1, 0, 1, 2, 2, 2, 1},
        {1, 0, 2, 1, 1, -1, 1}, {1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 1, 3, 1}, {1, 0, 1, 1, 3, 3, 1},
        {1, 0, 1, 1, 1, -1, 2}, {1, 0, 2, 1, 2, 2, 1}, {1, 0, 1, 2, 1, -1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 3, 3, 3}, {1, 0, 3, 1, 1}, {1, 0, 1, 1, 2}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][7] = {{-1, 0, 1, 1, 2, 1, 1}, {-1, 0, 1, 1, 1, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][7] = {{1, 0, 1, 1, 2, -1, 2}, {1, 0, 1, 2, 2, 2, 1}, {1, 0, 2, 1, 1, -1, 1},
        {1, 0, 1, 1, 1, 3, 1}, {1, 0, 1, 1, 3, 3, 1}, {1, 0, 1, 1, 1, -1, 2}, {1, 0, 2, 1, 2, 2, 1},
        {1, 0, 1, 2, 1, -1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 3, 3, 3}, {1, 0, 3, 3, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][7] = {{1, 0, 1, 1, 1, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][7] = {{1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 2, -1, 2}, {1, 0, 1, 2, 2, 2, 1},
        {1, 0, 2, 1, 1, -1, 1}, {1, 0, 1, 1, 1, 3, 1}, {1, 0, 1, 1, 3, 3, 1}, {1, 0, 1, 1, 1, -1, 2},
        {1, 0, 2, 1, 2, 2, 1}, {1, 0, 1, 2, 1, -1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][5] = {{1, 0, 3, 3, 3}, {1, 0, 3, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to One 聚合 输出表包含常量################################
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_OutNumConst)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_many_to_one_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 1},
        {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][6] = {{1, 0, 3, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 3, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 2, 4}, {+2, 0, 2, 1, 4}, {1, 0, 2, 2, 3}, {+1, 0, 4, 1, 1},
        {-2, 0, 4, 1, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{-1, 0, 1, 1, 2}, {-1, 0, 2, 2, 3}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 1},
        {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {1, 0, 4, 1, 1}, {1, 0, 1, 2, 4}, {-1, 0, 1, 1, 5}, {1, 0, 4, 4, 3},
        {2, 0, 2, 1, 4}, {-2, 0, 4, 1, 2}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][6] = {{1, 0, 1, 1, 3, 1}, {1, 0, 4, 1, 2, 1}, {1, 0, 5, 1, 1, 1}, {1, 0, 3, 1, 4, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{1, 0, 1, 1, 5}, {3, 0, 1, 2, 5}, {-2, 0, 2, 1, 4}, {2, 0, 2, 2, 4}, {-1, 0, 4, 1, 1},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{-1, 0, 1, 1, 2}, {-1, 0, 2, 2, 3}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 1},
        {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {1, 0, 1, 2, 4}, {1, 0, 4, 4, 3}, {-2, 0, 4, 1, 2}, {-1, 0, 4, 2, 1},
        {2, 0, 2, 2, 4}, {3, 0, 1, 2, 5}, {1, 0, 3, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][6] = {{1, 0, 1, 1, 3, 1}, {1, 0, 4, 1, 2, 1}, {1, 0, 5, 1, 1, 1}, {1, 0, 3, 1, 4, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_OutStrConst)
{
    const char *labelNameInp = "C";
    const char *labelNameOut = "D";
    char *fileName = (char *)"client_st_agg_many_to_one_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{1, 0, 3, 3, 1}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 1, 1, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 1, 6, 9}, {1, 0, 2, 6, 9}, {1, 0, 3, 1, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 2, 4}, {+2, 0, 2, 1, 4}, {1, 0, 2, 2, 3}, {+1, 0, 4, 1, 1},
        {-2, 0, 4, 1, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{1, 0, 3, 3, 1}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 1, 1, 3}, {1, 0, 1, 2, 4}, {-2, 0, 4, 1, 2}, {1, 0, 4, 1, 1}, {1, 0, 4, 4, 3},
        {-1, 0, 1, 1, 5}, {2, 0, 2, 1, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 3, 1, 9}, {1, 0, 2, 10, 9}, {1, 0, 1, 15, 9}, {1, 0, 4, 6, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{1, 0, 1, 1, 5}, {3, 0, 1, 2, 5}, {-2, 0, 2, 1, 4}, {2, 0, 2, 2, 4}, {-1, 0, 4, 1, 1},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{1, 0, 3, 3, 1}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1}, {1, 0, 1, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 1, 1, 3}, {1, 0, 1, 2, 4}, {-2, 0, 4, 1, 2}, {1, 0, 4, 4, 3}, {2, 0, 2, 2, 4},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}, {3, 0, 1, 2, 5}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][5] = {{1, 0, 3, 2, 9}, {1, 0, 2, 10, 9}, {1, 0, 1, 15, 9}, {1, 0, 4, 6, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_OutFixConst)
{
    const char *labelNameInp = "E";
    const char *labelNameOut = "F";
    char *fileName = (char *)"client_st_agg_many_to_one_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 1, 2}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{-1, 0, 1, 1, 2}, {1, 0, 1, 1, 1}, {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 2, 1, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 2, 6, 1}, {1, 0, 1, 6, 1}, {1, 0, 3, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {{-1, 0, 1, 1, 5}, {+1, 0, 1, 2, 4}, {+2, 0, 2, 1, 4}, {1, 0, 2, 2, 3}, {+1, 0, 4, 1, 1},
        {-2, 0, 4, 1, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{-1, 0, 1, 1, 2}, {1, 0, 1, 1, 1}, {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 2, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 3, 3, 1}, {1, 0, 4, 1, 1}, {1, 0, 1, 2, 4}, {2, 0, 2, 1, 4}, {-1, 0, 1, 1, 5},
        {-2, 0, 4, 1, 2}, {1, 0, 4, 4, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 3, 1, 1}, {1, 0, 2, 10, 1}, {1, 0, 1, 15, 1}, {1, 0, 4, 6, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{1, 0, 1, 1, 5}, {3, 0, 1, 2, 5}, {-2, 0, 2, 1, 4}, {2, 0, 2, 2, 4}, {-1, 0, 4, 1, 1},
        {-1, 0, 4, 2, 1}, {1, 0, 3, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{-1, 0, 1, 1, 2}, {1, 0, 1, 1, 1}, {3, 0, 2, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 2, 1, 1},
        {-1, 0, 2, 2, 3}, {1, 0, 3, 3, 1}, {1, 0, 1, 2, 4}, {-2, 0, 4, 1, 2}, {1, 0, 4, 4, 3}, {-1, 0, 4, 2, 1},
        {1, 0, 3, 1, 1}, {3, 0, 1, 2, 5}, {2, 0, 2, 2, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][5] = {{1, 0, 3, 2, 1}, {1, 0, 2, 10, 1}, {1, 0, 1, 15, 1}, {1, 0, 4, 6, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_OutTwoConst)
{
    const char *labelNameInp = "G";
    const char *labelNameOut = "H";
    char *fileName = (char *)"client_st_agg_many_to_one_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][6] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 1, 3}, {-1, 0, 1, 1, 1, 2}, {3, 0, 2, 1, 2, 2},
        {2, 0, 2, 1, 2, 1}, {-2, 0, 2, 2, 2, 3}, {1, 0, 3, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][6] = {{1, 0, 3, 3, 3, 1}, {-1, 0, 1, 1, 1, 2}, {3, 0, 2, 1, 2, 2}, {1, 0, 1, 1, 1, 3},
        {2, 0, 2, 1, 2, 1}, {1, 0, 1, 1, 1, 1}, {-2, 0, 2, 2, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][6] = {{1, 0, 1, 6, 9, 1}, {1, 0, 2, 6, 9, 1}, {1, 0, 3, 1, 9, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][6] = {{-1, 0, 1, 1, 1, 5}, {+1, 0, 1, 2, 1, 4}, {+2, 0, 2, 1, 2, 4}, {1, 0, 2, 2, 2, 3},
        {+1, 0, 4, 1, 4, 1}, {-2, 0, 4, 1, 4, 2}, {+1, 0, 4, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][6] = {{1, 0, 3, 3, 3, 1}, {-1, 0, 1, 1, 1, 2}, {3, 0, 2, 1, 2, 2}, {1, 0, 1, 1, 1, 3},
        {2, 0, 2, 1, 2, 1}, {1, 0, 1, 1, 1, 1}, {-1, 0, 2, 2, 2, 3}, {-1, 0, 1, 1, 1, 5}, {-2, 0, 4, 1, 4, 2},
        {1, 0, 4, 4, 4, 3}, {2, 0, 2, 1, 2, 4}, {1, 0, 4, 1, 4, 1}, {1, 0, 1, 2, 1, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][6] = {{1, 0, 3, 1, 9, 1}, {1, 0, 2, 10, 9, 1}, {1, 0, 4, 6, 9, 1}, {1, 0, 1, 15, 9, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][6] = {{1, 0, 1, 1, 1, 5}, {3, 0, 1, 2, 1, 5}, {-2, 0, 2, 1, 2, 4}, {2, 0, 2, 2, 2, 4},
        {-1, 0, 4, 1, 4, 1}, {-1, 0, 4, 2, 4, 1}, {1, 0, 3, 1, 3, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][6] = {{1, 0, 3, 3, 3, 1}, {-1, 0, 1, 1, 1, 2}, {3, 0, 2, 1, 2, 2}, {1, 0, 1, 1, 1, 3},
        {2, 0, 2, 1, 2, 1}, {1, 0, 1, 1, 1, 1}, {-1, 0, 2, 2, 2, 3}, {-2, 0, 4, 1, 4, 2}, {1, 0, 4, 4, 4, 3},
        {1, 0, 1, 2, 1, 4}, {1, 0, 3, 1, 3, 1}, {-1, 0, 4, 2, 4, 1}, {2, 0, 2, 2, 2, 4}, {3, 0, 1, 2, 1, 5}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][6] = {{1, 0, 3, 2, 9, 1}, {1, 0, 2, 10, 9, 1}, {1, 0, 4, 6, 9, 1}, {1, 0, 1, 15, 9, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_OutThreeConst)
{
    const char *labelNameInp = "I";
    const char *labelNameOut = "J";
    char *fileName = (char *)"client_st_agg_many_to_one_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][7] = {{1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 1}, {-1, 0, 1, 1, 1, 2, 1},
        {3, 0, 2, 1, 2, 2, 2}, {2, 0, 2, 1, 2, 1, 2}, {-2, 0, 2, 2, 2, 3, 2}, {1, 0, 3, 3, 3, 1, 3}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][7] = {{2, 0, 2, 1, 2, 1, 2}, {-1, 0, 1, 1, 1, 2, 1}, {-2, 0, 2, 2, 2, 3, 2},
        {1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 1}, {1, 0, 3, 3, 3, 1, 3}, {3, 0, 2, 1, 2, 2, 2}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][9] = {{1, 0, 2, 1, 2, 1, 9, 1, 1}, {1, 0, 2, 1, 1, 1, 9, 1, 1}, {1, 0, 1, 1, 3, 3, 9, 1, 1},
        {1, 0, 3, 3, 2, 2, 9, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][7] = {{-1, 0, 1, 1, 1, 5, 1}, {+1, 0, 1, 2, 1, 4, 1}, {+2, 0, 2, 1, 2, 4, 2},
        {1, 0, 2, 2, 2, 3, 2}, {+1, 0, 4, 1, 4, 1, 4}, {-2, 0, 4, 1, 4, 2, 4}, {+1, 0, 4, 4, 4, 3, 4}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][7] = {{2, 0, 2, 1, 2, 1, 2}, {-1, 0, 1, 1, 1, 2, 1}, {-1, 0, 2, 2, 2, 3, 2},
        {1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 1}, {1, 0, 3, 3, 3, 1, 3}, {3, 0, 2, 1, 2, 2, 2},
        {2, 0, 2, 1, 2, 4, 2}, {1, 0, 4, 1, 4, 1, 4}, {1, 0, 1, 2, 1, 4, 1}, {-2, 0, 4, 1, 4, 2, 4},
        {-1, 0, 1, 1, 1, 5, 1}, {1, 0, 4, 4, 4, 3, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][9] = {{1, 0, 1, 1, 3, 3, 9, 1, 1}, {1, 0, 3, 3, 2, 2, 9, 1, 1}, {1, 0, 2, 1, 4, 1, 9, 1, 1},
        {1, 0, 5, 1, 1, 1, 9, 1, 1}, {1, 0, 4, 4, 1, 2, 9, 1, 1}, {1, 0, 3, 3, 4, 4, 9, 1, 1},
        {1, 0, 4, 1, 2, 1, 9, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][7] = {{1, 0, 1, 1, 1, 5, 1}, {3, 0, 1, 2, 1, 5, 1}, {-2, 0, 2, 1, 2, 4, 2},
        {2, 0, 2, 2, 2, 4, 2}, {-1, 0, 4, 1, 4, 1, 4}, {-1, 0, 4, 2, 4, 1, 4}, {1, 0, 3, 1, 3, 1, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][7] = {{2, 0, 2, 1, 2, 1, 2}, {-1, 0, 1, 1, 1, 2, 1}, {-1, 0, 2, 2, 2, 3, 2},
        {1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 1}, {1, 0, 3, 3, 3, 1, 3}, {3, 0, 2, 1, 2, 2, 2},
        {1, 0, 1, 2, 1, 4, 1}, {-2, 0, 4, 1, 4, 2, 4}, {1, 0, 4, 4, 4, 3, 4}, {3, 0, 1, 2, 1, 5, 1},
        {1, 0, 3, 1, 3, 1, 3}, {2, 0, 2, 2, 2, 4, 2}, {-1, 0, 4, 2, 4, 1, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][9] = {{1, 0, 1, 1, 4, 2, 9, 1, 1}, {1, 0, 1, 1, 3, 1, 9, 1, 1}, {1, 0, 1, 1, 3, 3, 9, 1, 1},
        {1, 0, 3, 3, 4, 4, 9, 1, 1}, {1, 0, 4, 3, 2, 2, 9, 1, 1}, {1, 0, 2, 1, 1, 1, 9, 1, 1},
        {1, 0, 5, 4, 1, 2, 9, 1, 1}, {1, 0, 2, 1, 2, 1, 9, 1, 1}, {1, 0, 2, 2, 4, 1, 9, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to One 聚合 输入表和输出表均包含常量################################
 */
TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpOutOneConst1)
{
    const char *labelNameInp = "A";
    const char *labelNameOut = "B";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][6] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 3}, {-1, 0, 1, 1, 2, 2}, {3, 0, 2, 1, 2, 2},
        {2, 0, 2, 1, 1, 1}, {-2, 0, 2, 2, 3, 3}, {1, 0, 3, 3, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][6] = {{-2, 0, 2, 2, 3, 3}, {2, 0, 2, 1, 1, 1}, {3, 0, 2, 1, 2, 2}, {1, 0, 1, 1, 3, 3},
        {-1, 0, 1, 1, 2, 2}, {1, 0, 1, 1, 1, 1}, {1, 0, 3, 3, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][7] = {{1, 0, 1, 1, 1, 9, 1}, {1, 0, 2, 2, 2, 9, 2}, {1, 0, 2, 2, 1, 9, 2},
        {1, 0, 3, 3, 1, 9, 3}, {1, 0, 1, 1, 2, 9, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][6] = {{-1, 0, 1, 1, 5, 5}, {+1, 0, 1, 2, 4, 4}, {+2, 0, 2, 1, 4, 4}, {1, 0, 2, 2, 3, 3},
        {1, 0, 4, 1, 1, 1}, {-2, 0, 4, 1, 2, 2}, {1, 0, 4, 4, 3, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][6] = {{-1, 0, 2, 2, 3, 3}, {2, 0, 2, 1, 1, 1}, {3, 0, 2, 1, 2, 2}, {1, 0, 1, 1, 3, 3},
        {-1, 0, 1, 1, 2, 2}, {1, 0, 1, 1, 1, 1}, {1, 0, 3, 3, 1, 1}, {1, 0, 4, 1, 1, 1}, {2, 0, 2, 1, 4, 4},
        {1, 0, 4, 4, 3, 3}, {-1, 0, 1, 1, 5, 5}, {-2, 0, 4, 1, 2, 2}, {1, 0, 1, 2, 4, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][7] = {{1, 0, 1, 1, 1, 9, 1}, {1, 0, 2, 2, 2, 9, 2}, {1, 0, 2, 2, 1, 9, 2},
        {1, 0, 3, 3, 1, 9, 3}, {1, 0, 1, 1, 2, 9, 1}, {1, 0, 2, 2, 4, 9, 2}, {1, 0, 5, 5, 1, 9, 5},
        {1, 0, 4, 4, 2, 9, 4}, {1, 0, 1, 1, 4, 9, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][6] = {{1, 0, 1, 1, 5, 5}, {3, 0, 1, 2, 5, 5}, {-2, 0, 2, 1, 4, 4}, {2, 0, 2, 2, 4, 4},
        {-1, 0, 4, 1, 1, 1}, {-1, 0, 4, 2, 1, 1}, {1, 0, 3, 1, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][6] = {{-1, 0, 2, 2, 3, 3}, {2, 0, 2, 1, 1, 1}, {3, 0, 2, 1, 2, 2}, {1, 0, 1, 1, 3, 3},
        {-1, 0, 1, 1, 2, 2}, {1, 0, 1, 1, 1, 1}, {1, 0, 3, 3, 1, 1}, {1, 0, 4, 4, 3, 3}, {-2, 0, 4, 1, 2, 2},
        {1, 0, 1, 2, 4, 4}, {3, 0, 1, 2, 5, 5}, {2, 0, 2, 2, 4, 4}, {-1, 0, 4, 2, 1, 1}, {1, 0, 3, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][7] = {{1, 0, 1, 1, 1, 9, 1}, {1, 0, 2, 2, 2, 9, 2}, {1, 0, 2, 2, 1, 9, 2},
        {1, 0, 3, 3, 1, 9, 3}, {1, 0, 1, 1, 2, 9, 1}, {1, 0, 2, 2, 4, 9, 2}, {1, 0, 1, 1, 3, 9, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpOutOneConst2)
{
    const char *labelNameInp = "C";
    const char *labelNameOut = "D";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][6] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 3}, {-1, 0, 1, 1, 2, 2}, {3, 0, 2, 1, 2, 2},
        {2, 0, 2, 1, 1, 1}, {-2, 0, 2, 2, 3, 3}, {1, 0, 3, 3, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][6] = {{1, 0, 3, 3, 1, 1}, {-2, 0, 2, 2, 3, 3}, {-1, 0, 1, 1, 2, 2}, {3, 0, 2, 1, 2, 2},
        {1, 0, 1, 1, 3, 3}, {2, 0, 2, 1, 1, 1}, {1, 0, 1, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 1, 6, 1}, {1, 0, 2, 3, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][6] = {{-1, 0, 1, 1, 5, 5}, {+1, 0, 1, 2, 4, 4}, {+2, 0, 2, 1, 4, 4}, {1, 0, 2, 2, 3, 3},
        {1, 0, 4, 1, 1, 1}, {-2, 0, 4, 1, 2, 2}, {1, 0, 4, 4, 3, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][6] = {{1, 0, 3, 3, 1, 1}, {-1, 0, 2, 2, 3, 3}, {-1, 0, 1, 1, 2, 2}, {3, 0, 2, 1, 2, 2},
        {1, 0, 1, 1, 3, 3}, {2, 0, 2, 1, 1, 1}, {1, 0, 1, 1, 1, 1}, {-1, 0, 1, 1, 5, 5}, {1, 0, 4, 1, 1, 1},
        {1, 0, 4, 4, 3, 3}, {2, 0, 2, 1, 4, 4}, {-2, 0, 4, 1, 2, 2}, {1, 0, 1, 2, 4, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 2, 7, 1}, {1, 0, 4, 3, 1}, {1, 0, 1, 11, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][6] = {{1, 0, 1, 1, 5, 5}, {3, 0, 1, 2, 5, 5}, {-2, 0, 2, 1, 4, 4}, {2, 0, 2, 2, 4, 4},
        {-1, 0, 4, 1, 1, 1}, {-1, 0, 4, 2, 1, 1}, {1, 0, 3, 1, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][6] = {{1, 0, 3, 3, 1, 1}, {-1, 0, 2, 2, 3, 3}, {-1, 0, 1, 1, 2, 2}, {3, 0, 2, 1, 2, 2},
        {1, 0, 1, 1, 3, 3}, {2, 0, 2, 1, 1, 1}, {1, 0, 1, 1, 1, 1}, {1, 0, 4, 4, 3, 3}, {-2, 0, 4, 1, 2, 2},
        {1, 0, 1, 2, 4, 4}, {2, 0, 2, 2, 4, 4}, {1, 0, 3, 1, 1, 1}, {3, 0, 1, 2, 5, 5}, {-1, 0, 4, 2, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][5] = {{1, 0, 1, 6, 1}, {1, 0, 4, 2, 1}, {1, 0, 2, 3, 1}, {1, 0, 3, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpOutOneConst3)
{
    const char *labelNameInp = "E";
    const char *labelNameOut = "F";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][6] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 3}, {-1, 0, 1, 1, 2, 2}, {3, 0, 2, 1, 2, 2},
        {2, 0, 2, 1, 1, 1}, {-2, 0, 2, 2, 3, 3}, {1, 0, 3, 3, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][6] = {{1, 0, 1, 1, 3, 3}, {1, 0, 1, 1, 1, 1}, {-2, 0, 2, 2, 3, 3}, {3, 0, 2, 1, 2, 2},
        {1, 0, 3, 3, 1, 1}, {2, 0, 2, 1, 1, 1}, {-1, 0, 1, 1, 2, 2}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 1, 6, 9}, {1, 0, 2, 3, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][6] = {{-1, 0, 1, 1, 5, 5}, {+1, 0, 1, 2, 4, 4}, {+2, 0, 2, 1, 4, 4}, {1, 0, 2, 2, 3, 3},
        {1, 0, 4, 1, 1, 1}, {-2, 0, 4, 1, 2, 2}, {1, 0, 4, 4, 3, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][6] = {{1, 0, 1, 1, 3, 3}, {1, 0, 1, 1, 1, 1}, {-1, 0, 2, 2, 3, 3}, {3, 0, 2, 1, 2, 2},
        {1, 0, 3, 3, 1, 1}, {2, 0, 2, 1, 1, 1}, {-1, 0, 1, 1, 2, 2}, {-2, 0, 4, 1, 2, 2}, {-1, 0, 1, 1, 5, 5},
        {1, 0, 4, 4, 3, 3}, {1, 0, 1, 2, 4, 4}, {2, 0, 2, 1, 4, 4}, {1, 0, 4, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 1, 11, 9}, {1, 0, 4, 3, 9}, {1, 0, 2, 7, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][6] = {{1, 0, 1, 1, 5, 5}, {3, 0, 1, 2, 5, 5}, {-2, 0, 2, 1, 4, 4}, {2, 0, 2, 2, 4, 4},
        {-1, 0, 4, 1, 1, 1}, {-1, 0, 4, 2, 1, 1}, {1, 0, 3, 1, 1, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][6] = {{1, 0, 1, 1, 3, 3}, {1, 0, 1, 1, 1, 1}, {-1, 0, 2, 2, 3, 3}, {3, 0, 2, 1, 2, 2},
        {1, 0, 3, 3, 1, 1}, {2, 0, 2, 1, 1, 1}, {-1, 0, 1, 1, 2, 2}, {-2, 0, 4, 1, 2, 2}, {1, 0, 4, 4, 3, 3},
        {1, 0, 1, 2, 4, 4}, {1, 0, 3, 1, 1, 1}, {3, 0, 1, 2, 5, 5}, {-1, 0, 4, 2, 1, 1}, {2, 0, 2, 2, 4, 4}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][5] = {{1, 0, 1, 6, 9}, {1, 0, 3, 1, 9}, {1, 0, 2, 3, 9}, {1, 0, 4, 2, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_InpOutThreeConst)
{
    const char *labelNameInp = "I";
    const char *labelNameOut = "J";
    char *fileName = (char *)"client_st_agg_many_to_one_inp_out_const";
    char *udfName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][9] = {{1, 0, 1, 1, 1, 1, 1, 1, 1}, {1, 0, 2, 1, 1, -1, 1, 1, 1}, {1, 0, 1, 2, 1, -1, 1, 1, 1},
        {1, 0, 1, 1, 1, -1, 2, 1, 1}, {1, 0, 1, 1, 1, 5, 1, 1, 1}, {1, 0, 1, 1, 2, 1, 1, 2, 1},
        {1, 0, 2, 1, 2, 3, 1, 2, 1}, {1, 0, 1, 2, 2, 3, 1, 2, 1}, {1, 0, 1, 1, 2, 3, 2, 2, 1},
        {1, 0, 1, 1, 3, 1, 1, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][9] = {{1, 0, 1, 1, 1, 5, 1, 1, 1}, {1, 0, 2, 1, 1, -1, 1, 1, 1}, {1, 0, 1, 1, 1, 1, 1, 1, 1},
        {1, 0, 1, 1, 1, -1, 2, 1, 1}, {1, 0, 1, 1, 3, 1, 1, 3, 1}, {1, 0, 1, 1, 2, 3, 2, 2, 1},
        {1, 0, 1, 2, 1, -1, 1, 1, 1}, {1, 0, 2, 1, 2, 3, 1, 2, 1}, {1, 0, 1, 1, 2, 1, 1, 2, 1},
        {1, 0, 1, 2, 2, 3, 1, 2, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][9] = {{1, 0, 1, 1, 2, 2, 9, 2, 9}, {1, 0, 5, 1, 1, 1, 9, 2, 9}, {1, 0, 1, 1, 3, 3, 9, 2, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][9] = {{-2, 0, 1, 1, 1, 1, 1, 1, 1}, {-1, 0, 1, 1, 3, 1, 1, 3, 1}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][9] = {{1, 0, 1, 1, 1, 5, 1, 1, 1}, {1, 0, 2, 1, 1, -1, 1, 1, 1}, {-1, 0, 1, 1, 1, 1, 1, 1, 1},
        {1, 0, 1, 1, 1, -1, 2, 1, 1}, {1, 0, 1, 1, 2, 3, 2, 2, 1}, {1, 0, 1, 2, 1, -1, 1, 1, 1},
        {1, 0, 2, 1, 2, 3, 1, 2, 1}, {1, 0, 1, 1, 2, 1, 1, 2, 1}, {1, 0, 1, 2, 2, 3, 1, 2, 1}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][9] = {{1, 0, 1, 1, 2, 2, 9, 2, 9}, {1, 0, 5, 1, 1, 1, 9, 2, 9}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## 其他常量场景 ################################
 */

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggWriteTableWithConst)
{
    const char *labelNameInp = "D";
    const char *labelNameOut = "E";
    const char *labelNameOther = "F";
    char *fileName = (char *)"client_st_agg_write_table_const";
    char *functionFileName = (char *)"client_st_agg_read_org_write_to_delta";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, functionFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{1, 0, 1, 1, 1}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 3}, {1, 0, 3, 3, 1}, {3, 0, 2, 2, 2},
        {-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][5] = {{1, 0, 1, 1, 6}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));
    int32_t records1Other[][5] = {{1, 0, 1, 1, 1}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 3}, {1, 0, 3, 3, 1}, {3, 0, 2, 2, 2},
        {-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}};
    ScanAndCheckTable(
        g_stmt, labelNameOther, (int32_t *)records1Other, sizeof(records1Other) / sizeof(records1Other[0]));

    // 第二次插入
    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 2}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{1, 0, 1, 1, 1}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 3}, {1, 0, 3, 3, 1}, {5, 0, 2, 2, 2},
        {-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}, {-2, 0, 4, 4, 2}, {1, 0, 1, 1, 4}, {1, 0, 4, 4, 3}, {1, 0, 4, 4, 1},
        {-1, 0, 1, 1, 5}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][5] = {{1, 0, 1, 1, 15}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));
    int32_t records2Other[][5] = {{2, 0, 1, 1, 1}, {4, 0, 2, 2, 1}, {2, 0, 1, 1, 3}, {2, 0, 3, 3, 1}, {8, 0, 2, 2, 2},
        {-2, 0, 1, 1, 2}, {-4, 0, 2, 2, 3}, {1, 0, 4, 4, 3}, {1, 0, 4, 4, 1}, {-1, 0, 1, 1, 5}, {-2, 0, 4, 4, 2},
        {1, 0, 1, 1, 4}};
    ScanAndCheckTable(
        g_stmt, labelNameOther, (int32_t *)records2Other, sizeof(records2Other) / sizeof(records2Other[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

TEST_P(ClientStExecAggDatalog, structureDmlTestExample_AggWithOtherIndexWithConst)
{
    const char *labelNameInp = "C";
    const char *labelNameOut = "D";
    char *fileName = (char *)"client_st_agg_with_other_index";
    char *functionFileName = (char *)"client_st_agg_many_to_one";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, functionFileName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 第一次插入
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {-1, 0, 1, 1, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次插入后校验输入表和输出表的数据
    int32_t records1Inp[][5] = {{1, 0, 1, 1, 1}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 3}, {1, 0, 3, 3, 1}, {3, 0, 2, 2, 2},
        {-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records1Inp, sizeof(records1Inp) / sizeof(records1Inp[0]));
    int32_t records1Out[][6] = {{1, 0, 3, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records1Out, sizeof(records1Out) / sizeof(records1Out[0]));

    // 第二次插入
    int32_t records2[][5] = {
        {-1, 0, 1, 1, 5}, {+1, 0, 1, 1, 4}, {+2, 0, 2, 2, 4}, {+1, 0, 4, 4, 1}, {-2, 0, 4, 4, 2}, {+1, 0, 4, 4, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第二次插入后校验输入表和输出表的数据
    int32_t records2Inp[][5] = {{1, 0, 1, 1, 1}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 3}, {1, 0, 3, 3, 1}, {3, 0, 2, 2, 2},
        {-1, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}, {-2, 0, 4, 4, 2}, {1, 0, 1, 1, 4}, {2, 0, 2, 2, 4}, {1, 0, 4, 4, 1},
        {1, 0, 4, 4, 3}, {-1, 0, 1, 1, 5}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records2Inp, sizeof(records2Inp) / sizeof(records2Inp[0]));
    int32_t records2Out[][6] = {{1, 0, 5, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records2Out, sizeof(records2Out) / sizeof(records2Out[0]));

    // 第三次插入
    int32_t records3[][5] = {{-2, 0, 1, 1, 1}, {-1, 0, 1, 1, 2}, {+1, 0, 1, 1, 3}, {-1, 0, 1, 1, 4}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInp, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), false);
    EXPECT_EQ(GMERR_OK, ret);

    // 第三次插入后校验输入表和输出表的数据
    int32_t records3Inp[][5] = {{-1, 0, 1, 1, 1}, {2, 0, 2, 2, 1}, {2, 0, 1, 1, 3}, {1, 0, 3, 3, 1}, {3, 0, 2, 2, 2},
        {-2, 0, 1, 1, 2}, {-2, 0, 2, 2, 3}, {-2, 0, 4, 4, 2}, {2, 0, 2, 2, 4}, {1, 0, 4, 4, 1}, {1, 0, 4, 4, 3},
        {-1, 0, 1, 1, 5}};
    ScanAndCheckTable(g_stmt, labelNameInp, (int32_t *)records3Inp, sizeof(records3Inp) / sizeof(records3Inp[0]));
    int32_t records3Out[][6] = {{1, 0, 5, 1, 1, 1}};
    ScanAndCheckTable(g_stmt, labelNameOut, (int32_t *)records3Out, sizeof(records3Out) / sizeof(records3Out[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to Many 聚合int4类型带常量 ################################
// ==== int4类型常量 =====
%table A1(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B1(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B2(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B3(a: int4, b: int4, c: int4, d: int4, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered
}

%aggregate sum(a: int4, b: int4 -> c: int4){
    many_to_many
}

// 左右表均只有一个常量
B1(a, d, e, 1, a) :- A1(a, b, c, 1, -) GROUP-BY (a) expand(b, c, d, e).
null(0) :- B1(a, b, c, d, e).

// 左表无常量，右表有两个常量
B2(a, d, e, a, a) :- A1(a, b, c, 1, 2) GROUP-BY (a) expand(b, c, d, e).
null(0) :- B2(a, b, c, d, e).

// 左表有两个常量，右表无常量
B3(a, d, e, 1, 2) :- A1(a, b, c, -, -) GROUP-BY (a) expand(b, c, d, e).
null(0) :- B3(a, b, c, d, e).

// group-by两字段，无ordered
B4(a, a2, d, 1, 2) :- A1(a, b, c, a2, 2) GROUP-BY (a, a2) sum(b, c, d).
null(0) :- B4(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_intConst)
{
    const char *labelNameA = "A1";
    const char *labelNameB1 = "B1";
    const char *labelNameB2 = "B2";
    const char *labelNameB3 = "B3";
    const char *labelNameB4 = "B4";
    char *fileName = (char *)"client_st_agg_many_to_many_const";
    char *udfFileName = (char *)"client_st_agg_many_to_many_const";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][7] = {{1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2}, {-1, 0, 1, 1, 2, 2, 2},
        {3, 0, 2, 2, 2, 1, 1}, {2, 0, 2, 2, 1, 1, 2}, {-2, 0, 2, 2, 3, 2, 2}, {1, 0, 3, 3, 1, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result1A1[][7] = {{-2, 0, 2, 2, 3, 2, 2}, {3, 0, 2, 2, 2, 1, 1}, {2, 0, 2, 2, 1, 1, 2},
        {1, 0, 1, 1, 1, 1, 1}, {1, 0, 3, 3, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2}, {-1, 0, 1, 1, 2, 2, 2}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result1A1, sizeof(result1A1) / sizeof(result1A1[0]));
    int32_t result1B1[][6] = {
        {1, 0, 1, 1, 3, 1}, {1, 0, 2, 2, 2, 1}, {1, 0, 1, 1, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 2, 2, 1, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result1B1, sizeof(result1B1) / sizeof(result1B1[0]));
    int32_t result1B2[][5] = {{2, 0, 1, 1, 3}, {2, 0, 2, 2, 1}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result1B2, sizeof(result1B2) / sizeof(result1B2[0]));
    int32_t result1B3[][7] = {{1, 0, 2, 2, 3, 1, 2}, {1, 0, 1, 1, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2},
        {1, 0, 1, 1, 3, 1, 2}, {1, 0, 2, 2, 1, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result1B3, sizeof(result1B3) / sizeof(result1B3[0]));
    int32_t result1B4[][7] = {{1, 0, 2, 2, 2, 1, 2}, {1, 0, 1, 1, 1, 1, 2}, {1, 0, 2, 1, 1, 1, 2},
        {1, 0, 1, 3, 1, 1, 2}, {1, 0, 2, 2, 1, 1, 2}, {1, 0, 1, 1, 2, 1, 2}, {1, 0, 2, 3, 2, 1, 2},
        {1, 0, 1, 2, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result1B4, sizeof(result1B4) / sizeof(result1B4[0]));

    int32_t records2[][7] = {{-1, 0, 1, 1, 5, 1, 1}, {+1, 0, 1, 1, 4, 1, 2}, {+2, 0, 2, 2, 4, 1, 1},
        {+1, 0, 4, 4, 1, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}, {+1, 0, 4, 4, 3, 2, 2}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result2A1[][7] = {{-2, 0, 2, 2, 3, 2, 2}, {3, 0, 2, 2, 2, 1, 1}, {2, 0, 2, 2, 1, 1, 2},
        {1, 0, 1, 1, 1, 1, 1}, {1, 0, 3, 3, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2}, {-1, 0, 1, 1, 2, 2, 2},
        {-1, 0, 1, 1, 5, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}, {2, 0, 2, 2, 4, 1, 1}, {1, 0, 4, 4, 1, 1, 1},
        {1, 0, 4, 4, 3, 2, 2}, {1, 0, 1, 1, 4, 1, 2}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result2A1, sizeof(result2A1) / sizeof(result2A1[0]));
    int32_t result2B1[][6] = {{1, 0, 1, 1, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 2, 2, 1, 1}, {1, 0, 4, 4, 1, 1},
        {1, 0, 1, 1, 5, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 4, 4, 2, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result2B1, sizeof(result2B1) / sizeof(result2B1[0]));
    int32_t result2B2[][5] = {{1, 0, 1, 1, 3}, {2, 0, 2, 2, 1}, {2, 0, 4, 4, 2}, {1, 0, 1, 1, 4}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result2B2, sizeof(result2B2) / sizeof(result2B2[0]));
    int32_t result2B3[][7] = {{1, 0, 1, 1, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2}, {1, 0, 2, 2, 1, 1, 2},
        {1, 0, 1, 1, 5, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 4, 3, 1, 2}, {1, 0, 2, 2, 4, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result2B3, sizeof(result2B3) / sizeof(result2B3[0]));
    int32_t result2B4[][7] = {{1, 0, 2, 2, 2, 1, 2}, {1, 0, 2, 1, 1, 1, 2}, {1, 0, 2, 2, 1, 1, 2},
        {1, 0, 1, 1, 2, 1, 2}, {1, 0, 2, 3, 2, 1, 2}, {1, 0, 1, 2, 2, 1, 2}, {1, 0, 1, 2, 1, 1, 2},
        {1, 0, 1, 7, 1, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 2, 1, 1, 2}, {1, 0, 4, 3, 2, 1, 2},
        {1, 0, 4, 4, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result2B4, sizeof(result2B4) / sizeof(result2B4[0]));

    int32_t records3[][7] = {
        {-2, 0, 1, 1, 1, 1, 1}, {-1, 0, 1, 1, 2, 1, 2}, {+1, 0, 1, 1, 3, 2, 2}, {-1, 0, 1, 1, 4, 1, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result3A1[][7] = {{-2, 0, 2, 2, 3, 2, 2}, {3, 0, 2, 2, 2, 1, 1}, {2, 0, 2, 2, 1, 1, 2},
        {-1, 0, 1, 1, 1, 1, 1}, {1, 0, 3, 3, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2}, {-1, 0, 1, 1, 2, 2, 2},
        {-1, 0, 1, 1, 5, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}, {2, 0, 2, 2, 4, 1, 1}, {1, 0, 4, 4, 1, 1, 1},
        {1, 0, 4, 4, 3, 2, 2}, {1, 0, 1, 1, 4, 1, 2}, {-1, 0, 1, 1, 2, 1, 2}, {-1, 0, 1, 1, 4, 1, 3},
        {1, 0, 1, 1, 3, 2, 2}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result3A1, sizeof(result3A1) / sizeof(result3A1[0]));
    int32_t result3B1[][6] = {{1, 0, 1, 1, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 2, 2, 1, 1}, {1, 0, 4, 4, 1, 1},
        {1, 0, 1, 1, 5, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 4, 4, 2, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result3B1, sizeof(result3B1) / sizeof(result3B1[0]));
    int32_t result3B2[][5] = {{2, 0, 2, 2, 1}, {2, 0, 4, 4, 2}, {1, 0, 1, 1, 4}, {1, 0, 1, 1, 2}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result3B2, sizeof(result3B2) / sizeof(result3B2[0]));
    int32_t result3B3[][7] = {{1, 0, 1, 1, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2}, {1, 0, 2, 2, 1, 1, 2},
        {1, 0, 1, 1, 5, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 4, 3, 1, 2}, {1, 0, 2, 2, 4, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result3B3, sizeof(result3B3) / sizeof(result3B3[0]));
    int32_t result3B4[][7] = {{1, 0, 2, 2, 2, 1, 2}, {1, 0, 1, 3, 1, 1, 2}, {1, 0, 2, 1, 1, 1, 2},
        {1, 0, 1, 9, 1, 1, 2}, {1, 0, 2, 2, 1, 1, 2}, {1, 0, 2, 3, 2, 1, 2}, {1, 0, 1, 2, 2, 1, 2},
        {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 2, 1, 1, 2}, {1, 0, 4, 3, 2, 1, 2}, {1, 0, 4, 4, 2, 1, 2},
        {1, 0, 1, 5, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result3B4, sizeof(result3B4) / sizeof(result3B4[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to Many 聚合str类型带常量 ################################
// ==== str类型常量 =====
%table AStr1(a: int4, b: int4, c: str, d: str, e: byte8)
%table BStr1(a: int4, b: int4, c: str, d: str, e: byte8)
%table BStr2(a: int4, b: int4, c: str, d: str, e: byte8)
%table BStr3(a: int4, b: int4, c: str, d: str, e: byte8)

%aggregate expandstr(a: int4, b: str -> c: int4, d: str){
    many_to_many,
    ordered
}

%aggregate sumstr(a: int4 -> b: int4){
    many_to_many
}

// 左右表均只有一个常量
BStr1(a, d, e, e, 0x111) :- AStr1(a, b, c, "Atest", -) GROUP-BY (a) expandstr(b, c, d, e).
null(0) :- BStr1(a, b, c, d, e).

// 左表无常量，右表有两个常量
BStr2(a, d, e, e, 0x222) :- AStr1(a, b, c, "Atest", 1) GROUP-BY (a) expandstr(b, c, d, e).
null(0) :- BStr2(a, b, c, d, e).

// 左表有两个常量，右表无常量
BStr3(a, d, e, "Btest", 0x333) :- AStr1(a, b, c, -, -) GROUP-BY (a) expandstr(b, c, d, e).
null(0) :- BStr3(a, b, c, d, e).

// group-by两字段，无ordered
BStr4(a, d, c, "Btest", 0x222) :- AStr1(a, b, c, "Atest", 1) GROUP-BY (a, c) sumstr(b, d).
null(0) :- BStr4(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_strConst)
{
    const char *labelNameA = "AStr1";
    const char *labelNameB1 = "BStr1";
    const char *labelNameB2 = "BStr2";
    const char *labelNameB3 = "BStr3";
    const char *labelNameB4 = "BStr4";
    char *fileName = (char *)"client_st_agg_many_to_many_const";
    char *udfFileName = (char *)"client_st_agg_many_to_many_const";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][7] = {{1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 333, 1, 2}, {-1, 0, 1, 1, 22, 2, 2},
        {3, 0, 2, 2, 22, 1, 1}, {2, 0, 2, 2, 1, 1, 2}, {-2, 0, 2, 2, 333, 2, 2}, {1, 0, 3, 3, 1, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t result1A1[][7] = {{1, 0, 3, 3, 1, 1, 1}, {-1, 0, 1, 1, 22, 2, 2}, {1, 0, 1, 1, 333, 1, 2},
        {-2, 0, 2, 2, 333, 2, 2}, {1, 0, 1, 1, 1, 1, 1}, {2, 0, 2, 2, 1, 1, 2}, {3, 0, 2, 2, 22, 1, 1}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result1A1, sizeof(result1A1) / sizeof(result1A1[0]));

    int32_t result1B1[][6] = {
        {1, 0, 2, 2, 22, 1}, {1, 0, 1, 1, 333, 1}, {1, 0, 1, 1, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 2, 2, 1, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result1B1, sizeof(result1B1) / sizeof(result1B1[0]));

    int32_t result1B2[][5] = {{2, 0, 1, 1, 333}, {2, 0, 2, 2, 1}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result1B2, sizeof(result1B2) / sizeof(result1B2[0]));

    int32_t result1B3[][7] = {{1, 0, 1, 1, 1, 1, 2}, {1, 0, 2, 2, 333, 1, 2}, {1, 0, 1, 1, 333, 1, 2},
        {1, 0, 2, 2, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result1B3, sizeof(result1B3) / sizeof(result1B3[0]));

    int32_t result1B4[][7] = {{1, 0, 2, 2, 2, 1, 2}, {1, 0, 2, 10, 2, 1, 2}, {1, 0, 1, 1, 1, 1, 2},
        {1, 0, 1, 9, 2, 1, 2}, {1, 0, 2, 2, 1, 1, 2}, {1, 0, 1, 10, 1, 1, 2}, {1, 0, 2, 8, 1, 1, 2},
        {1, 0, 1, 1, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result1B4, sizeof(result1B4) / sizeof(result1B4[0]));

    int32_t records2[][7] = {{-1, 0, 1, 1, 55555, 1, 1}, {+1, 0, 1, 1, 4444, 1, 2}, {+2, 0, 2, 2, 4, 1, 1},
        {+1, 0, 4, 4, 1, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}, {+1, 0, 4, 4, 3, 2, 2}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result2A1[][7] = {{1, 0, 3, 3, 1, 1, 1}, {-1, 0, 1, 1, 22, 2, 2}, {1, 0, 1, 1, 333, 1, 2},
        {-2, 0, 2, 2, 333, 2, 2}, {1, 0, 1, 1, 1, 1, 1}, {2, 0, 2, 2, 1, 1, 2}, {3, 0, 2, 2, 22, 1, 1},
        {-1, 0, 1, 1, 55555, 1, 1}, {+1, 0, 1, 1, 4444, 1, 2}, {+2, 0, 2, 2, 4, 1, 1}, {+1, 0, 4, 4, 1, 1, 1},
        {-2, 0, 4, 4, 2, 1, 2}, {+1, 0, 4, 4, 3, 2, 2}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result2A1, sizeof(result2A1) / sizeof(result2A1[0]));

    int32_t result2B1[][6] = {{1, 0, 1, 1, 55555, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 2, 2, 1, 1}, {1, 0, 1, 1, 1, 1},
        {1, 0, 4, 4, 1, 1}, {1, 0, 4, 4, 2, 1}, {1, 0, 2, 2, 22, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result2B1, sizeof(result2B1) / sizeof(result2B1[0]));

    int32_t result2B2[][5] = {{1, 0, 1, 1, 333}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 4444}, {2, 0, 4, 4, 2}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result2B2, sizeof(result2B2) / sizeof(result2B2[0]));

    int32_t result2B3[][7] = {{1, 0, 2, 2, 333, 1, 2}, {1, 0, 1, 1, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2},
        {1, 0, 2, 2, 1, 1, 2}, {1, 0, 4, 4, 3, 1, 2}, {1, 0, 1, 1, 55555, 1, 2}, {1, 0, 4, 4, 1, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result2B3, sizeof(result2B3) / sizeof(result2B3[0]));

    int32_t result2B4[][7] = {{1, 0, 2, 2, 2, 1, 2}, {1, 0, 2, 10, 2, 1, 2}, {1, 0, 1, 9, 2, 1, 2},
        {1, 0, 2, 2, 1, 1, 2}, {1, 0, 2, 8, 1, 1, 2}, {1, 0, 1, 1, 2, 1, 2}, {1, 0, 4, 8, 2, 1, 2},
        {1, 0, 4, 8, 1, 1, 2}, {1, 0, 1, 21, 1, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 1, 2, 1, 1, 2},
        {1, 0, 4, 4, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result2B4, sizeof(result2B4) / sizeof(result2B4[0]));

    int32_t records3[][7] = {
        {-2, 0, 1, 1, 1, 1, 1}, {-1, 0, 1, 1, 2, 1, 2}, {+1, 0, 1, 1, 3, 2, 2}, {-1, 0, 1, 1, 4, 1, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result3A1[][7] = {{1, 0, 3, 3, 1, 1, 1}, {-1, 0, 1, 1, 22, 2, 2}, {1, 0, 1, 1, 333, 1, 2},
        {-2, 0, 2, 2, 333, 2, 2}, {-1, 0, 1, 1, 1, 1, 1}, {2, 0, 2, 2, 1, 1, 2}, {3, 0, 2, 2, 22, 1, 1},
        {-1, 0, 1, 1, 55555, 1, 1}, {+1, 0, 1, 1, 4444, 1, 2}, {+2, 0, 2, 2, 4, 1, 1}, {+1, 0, 4, 4, 1, 1, 1},
        {-2, 0, 4, 4, 2, 1, 2}, {+1, 0, 4, 4, 3, 2, 2}, {-1, 0, 1, 1, 2, 1, 2}, {+1, 0, 1, 1, 3, 2, 2},
        {-1, 0, 1, 1, 4, 1, 3}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result3A1, sizeof(result3A1) / sizeof(result3A1[0]));

    int32_t result3B1[][6] = {{1, 0, 1, 1, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 2, 2, 1, 1}, {1, 0, 1, 1, 55555, 1},
        {1, 0, 4, 4, 1, 1}, {1, 0, 4, 4, 2, 1}, {1, 0, 2, 2, 22, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result3B1, sizeof(result3B1) / sizeof(result3B1[0]));

    int32_t result3B2[][5] = {{1, 0, 1, 1, 4444}, {2, 0, 2, 2, 1}, {2, 0, 4, 4, 2}, {1, 0, 1, 1, 2}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result3B2, sizeof(result3B2) / sizeof(result3B2[0]));

    int32_t result3B3[][7] = {{1, 0, 2, 2, 333, 1, 2}, {1, 0, 1, 1, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2},
        {1, 0, 2, 2, 1, 1, 2}, {1, 0, 4, 4, 3, 1, 2}, {1, 0, 1, 1, 55555, 1, 2}, {1, 0, 4, 4, 1, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result3B3, sizeof(result3B3) / sizeof(result3B3[0]));

    int32_t result3B4[][7] = {{1, 0, 2, 8, 1, 1, 2}, {1, 0, 2, 10, 2, 1, 2}, {1, 0, 2, 2, 2, 1, 2},
        {1, 0, 2, 2, 1, 1, 2}, {1, 0, 1, 17, 2, 1, 2}, {1, 0, 1, 2, 2, 1, 2}, {1, 0, 4, 8, 1, 1, 2},
        {1, 0, 4, 8, 2, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 4, 2, 1, 2}, {1, 0, 1, 29, 1, 1, 2},
        {1, 0, 1, 3, 1, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result3B4, sizeof(result3B4) / sizeof(result3B4[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to Many 聚合byte类型带常量 ################################
// ==== byte类型常量 =====
%table AFixed1(a: int4, b: int4, c: byte8, d: byte8, e: str)
%table BFixed1(a: int4, b: int4, c: byte8, d: byte8, e: str)
%table BFixed2(a: int4, b: int4, c: byte8, d: byte8, e: str)
%table BFixed3(a: int4, b: int4, c: byte8, d: byte8, e: str)

%aggregate expandfixed(a: int4, b: byte8 -> c: int4, d: byte8){
    many_to_many,
    ordered
}

%aggregate sumbyte(a: int4 -> b: int4){
    many_to_many
}

// 左右表均只有一个常量
BFixed1(a, d, e, e, "Btest") :- AFixed1(a, b, c, 1, -) GROUP-BY (a) expandfixed(b, c, d, e).
null(0) :- BFixed1(a, b, c, d, e).

// 左表无常量，右表有两个常量
BFixed2(a, d, e, e, "Btest") :- AFixed1(a, b, c, 1, "Atest") GROUP-BY (a) expandfixed(b, c, d, e).
null(0) :- BFixed2(a, b, c, d, e).

// 左表有两个常量，右表无常量
BFixed3(a, d, e, 0x333, "Btest") :- AFixed1(a, b, c, -, -) GROUP-BY (a) expandfixed(b, c, d, e).
null(0) :- BFixed3(a, b, c, d, e).

// group-by两字段，无ordered
BStr4(a, d, c, 0x333, "Btest") :- AStr1(a, b, c, 1, "Atest") GROUP-BY (a, c) sumbyte(b, d).
null(0) :- BStr4(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_byteConst)
{
    const char *labelNameA = "AFixed1";
    const char *labelNameB1 = "BFixed1";
    const char *labelNameB2 = "BFixed2";
    const char *labelNameB3 = "BFixed3";
    const char *labelNameB4 = "BFixed4";
    char *fileName = (char *)"client_st_agg_many_to_many_const";
    char *udfFileName = (char *)"client_st_agg_many_to_many_const";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][7] = {{1, 0, 1, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2}, {-1, 0, 1, 1, 2, 2, 2},
        {3, 0, 2, 2, 2, 1, 1}, {2, 0, 2, 2, 1, 1, 2}, {-2, 0, 2, 2, 3, 2, 2}, {1, 0, 3, 3, 1, 1, 1}};
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result1A1[][7] = {{2, 0, 2, 2, 1, 1, 2}, {1, 0, 3, 3, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2},
        {-1, 0, 1, 1, 2, 2, 2}, {-2, 0, 2, 2, 3, 2, 2}, {1, 0, 1, 1, 1, 1, 1}, {3, 0, 2, 2, 2, 1, 1}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result1A1, sizeof(result1A1) / sizeof(result1A1[0]));
    int32_t result1B1[][6] = {
        {1, 0, 2, 2, 2, 1}, {1, 0, 2, 2, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result1B1, sizeof(result1B1) / sizeof(result1B1[0]));
    int32_t result1B2[][5] = {{2, 0, 1, 1, 3}, {2, 0, 2, 2, 1}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result1B2, sizeof(result1B2) / sizeof(result1B2[0]));
    int32_t result1B3[][7] = {{1, 0, 2, 2, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2}, {1, 0, 1, 1, 1, 1, 2},
        {1, 0, 2, 2, 3, 1, 2}, {1, 0, 1, 1, 3, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result1B3, sizeof(result1B3) / sizeof(result1B3[0]));
    int32_t result1B4[][7] = {{1, 0, 2, 2, 1, 1, 2}, {1, 0, 1, 2, 2, 1, 2}, {1, 0, 2, 2, 2, 1, 2},
        {1, 0, 2, 3, 2, 1, 2}, {1, 0, 1, 1, 1, 1, 2}, {1, 0, 2, 1, 1, 1, 2}, {1, 0, 1, 3, 1, 1, 2},
        {1, 0, 1, 1, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result1B4, sizeof(result1B4) / sizeof(result1B4[0]));

    int32_t records2[][7] = {{-1, 0, 1, 1, 5, 1, 1}, {+1, 0, 1, 1, 4, 1, 2}, {+2, 0, 2, 2, 4, 1, 1},
        {+1, 0, 4, 4, 1, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}, {+1, 0, 4, 4, 3, 2, 2}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records2, sizeof(records2) / sizeof(records2[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result2A1[][7] = {{2, 0, 2, 2, 1, 1, 2}, {1, 0, 3, 3, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2},
        {-1, 0, 1, 1, 2, 2, 2}, {-2, 0, 2, 2, 3, 2, 2}, {1, 0, 1, 1, 1, 1, 1}, {3, 0, 2, 2, 2, 1, 1},
        {-1, 0, 1, 1, 5, 1, 1}, {1, 0, 1, 1, 4, 1, 2}, {2, 0, 2, 2, 4, 1, 1}, {1, 0, 4, 4, 3, 2, 2},
        {1, 0, 4, 4, 1, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result2A1, sizeof(result2A1) / sizeof(result2A1[0]));
    int32_t result2B1[][6] = {{1, 0, 2, 2, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 1, 1, 1, 1}, {1, 0, 4, 4, 1, 1},
        {1, 0, 4, 4, 2, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 1, 1, 5, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result2B1, sizeof(result2B1) / sizeof(result2B1[0]));
    int32_t result2B2[][5] = {{1, 0, 1, 1, 3}, {2, 0, 2, 2, 1}, {1, 0, 1, 1, 4}, {2, 0, 4, 4, 2}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result2B2, sizeof(result2B2) / sizeof(result2B2[0]));
    int32_t result2B3[][7] = {{1, 0, 2, 2, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2}, {1, 0, 1, 1, 1, 1, 2},
        {1, 0, 4, 4, 3, 1, 2}, {1, 0, 2, 2, 4, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 1, 1, 5, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result2B3, sizeof(result2B3) / sizeof(result2B3[0]));
    int32_t result2B4[][7] = {{1, 0, 2, 2, 1, 1, 2}, {1, 0, 1, 2, 2, 1, 2}, {1, 0, 2, 2, 2, 1, 2},
        {1, 0, 2, 3, 2, 1, 2}, {1, 0, 2, 1, 1, 1, 2}, {1, 0, 1, 1, 2, 1, 2}, {1, 0, 1, 7, 1, 1, 2},
        {1, 0, 4, 2, 1, 1, 2}, {1, 0, 1, 2, 1, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 3, 2, 1, 2},
        {1, 0, 4, 4, 2, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result2B4, sizeof(result2B4) / sizeof(result2B4[0]));

    int32_t records3[][7] = {
        {-2, 0, 1, 1, 1, 1, 1}, {-1, 0, 1, 1, 2, 1, 2}, {+1, 0, 1, 1, 3, 2, 2}, {-1, 0, 1, 1, 4, 1, 3}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records3, sizeof(records3) / sizeof(records3[0]), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result3A1[][7] = {{2, 0, 2, 2, 1, 1, 2}, {1, 0, 3, 3, 1, 1, 1}, {1, 0, 1, 1, 3, 1, 2},
        {-1, 0, 1, 1, 2, 2, 2}, {-2, 0, 2, 2, 3, 2, 2}, {-1, 0, 1, 1, 1, 1, 1}, {3, 0, 2, 2, 2, 1, 1},
        {-1, 0, 1, 1, 5, 1, 1}, {1, 0, 1, 1, 4, 1, 2}, {2, 0, 2, 2, 4, 1, 1}, {1, 0, 4, 4, 3, 2, 2},
        {1, 0, 4, 4, 1, 1, 1}, {-2, 0, 4, 4, 2, 1, 2}, {-1, 0, 1, 1, 4, 1, 3}, {1, 0, 1, 1, 3, 2, 2},
        {-1, 0, 1, 1, 2, 1, 2}};
    cout << "====================== " << labelNameA << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameA, (int32_t *)result3A1, sizeof(result3A1) / sizeof(result3A1[0]));
    int32_t result3B1[][6] = {{1, 0, 2, 2, 1, 1}, {2, 0, 3, 3, 1, 1}, {1, 0, 1, 1, 1, 1}, {1, 0, 4, 4, 1, 1},
        {1, 0, 4, 4, 2, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 1, 1, 5, 1}};
    cout << "====================== " << labelNameB1 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB1, (int32_t *)result3B1, sizeof(result3B1) / sizeof(result3B1[0]));
    int32_t result3B2[][5] = {{2, 0, 2, 2, 1}, {1, 0, 1, 1, 4}, {2, 0, 4, 4, 2}, {1, 0, 1, 1, 2}};
    cout << "====================== " << labelNameB2 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB2, (int32_t *)result3B2, sizeof(result3B2) / sizeof(result3B2[0]));
    int32_t result3B3[][7] = {{1, 0, 2, 2, 1, 1, 2}, {2, 0, 3, 3, 1, 1, 2}, {1, 0, 1, 1, 1, 1, 2},
        {1, 0, 4, 4, 3, 1, 2}, {1, 0, 2, 2, 4, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 1, 1, 5, 1, 2}};
    cout << "====================== " << labelNameB3 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB3, (int32_t *)result3B3, sizeof(result3B3) / sizeof(result3B3[0]));
    int32_t result3B4[][7] = {{1, 0, 2, 2, 1, 1, 2}, {1, 0, 1, 2, 2, 1, 2}, {1, 0, 2, 2, 2, 1, 2},
        {1, 0, 2, 3, 2, 1, 2}, {1, 0, 1, 9, 1, 1, 2}, {1, 0, 2, 1, 1, 1, 2}, {1, 0, 1, 5, 2, 1, 2},
        {1, 0, 4, 2, 1, 1, 2}, {1, 0, 4, 4, 1, 1, 2}, {1, 0, 4, 3, 2, 1, 2}, {1, 0, 4, 4, 2, 1, 2},
        {1, 0, 1, 3, 1, 1, 2}};
    cout << "====================== " << labelNameB4 << " =========================" << endl;
    ScanAndCheckTable(g_stmt, labelNameB4, (int32_t *)result3B4, sizeof(result3B4) / sizeof(result3B4[0]));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to Many 聚合带常量违反约束 ################################
// GROUP-BY在输出表中的字段不能是常量
%table A1(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B1(a: int4, b: int4, c: int4, d: int4, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered
}
B1(1, d, e, 1, 2) :- A1(a, b, c, -, -) GROUP-BY (a) expand(b, c, d, e).
null(0) :- B1(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_exceptionNoOutput)
{
    char *fileName = (char *)"client_st_agg_many_to_many_const_exception_no_output";
    char *udfFileName = (char *)"client_st_agg_many_to_many";
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfFileName));
}

/*
 * ################################## Many to Many 聚合带常量违反约束 ################################
// GROUP-BY中的字段不支持常量
%table A1(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B1(a: int4, b: int4, c: int4, d: int4, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered
}
B1(a, d, e, 1, a) :- A1(a, b, c, 1, -) GROUP-BY (1) expand(b, c, d, e).
null(0) :- B1(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_exceptionNoGroupBy)
{
    char *fileName = (char *)"client_st_agg_many_to_many_const_exception_groupby";
    char *udfFileName = (char *)"client_st_agg_many_to_many";
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfFileName));
}

/*
 * ################################## Many to Many 聚合带常量违反约束 ################################
// 输入表常量长度超出最大长度限制 129的字符串
%table A1(a: int4, b: int4, c: int4, d: str, e: int4)
%table B1(a: int4, b: int4, c: int4, d: int4, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered
}

B1(a, d, e, "Btest", a) :- A1(a, b, c,
"11111111111111111
111111111111111111
1111111111111111111
11111111111111111111111111111
11111111111111111111111111111111111
11111111111",
-) GROUP-BY (a) expand(b, c, d, e). null(0) :- B1(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_exceptionLongInput)
{
    char *fileName = (char *)"client_st_agg_many_to_many_const_exception_long_input";
    char *udfFileName = (char *)"client_st_agg_many_to_many";
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfFileName));
}

/*
 * ################################## Many to Many 聚合带常量违反约束 ################################
// 输出表常量长度超出最大长度限制 129的字符串
%table A1(a: int4, b: int4, c: int4, d: str, e: int4)
%table B1(a: int4, b: int4, c: int4, d: int4, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered
}

B1(a, d, e,
"1111111111111111111111111111
11111111111111111111111111111
111111111111111111111111111111
111111111111111111111111111111111111111111",
a) :- A1(a, b, c, -, -) GROUP-BY (a) expand(b, c, d, e). null(0) :- B1(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_exceptionLongOutput)
{
    char *fileName = (char *)"client_st_agg_many_to_many_const_exception_long_output";
    char *udfFileName = (char *)"client_st_agg_many_to_many";
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfFileName));
}

/*
 * ################################## Many to Many 聚合带常量违反约束 ################################
// GROUP-BY在输入表中的字段不能是常量
%table A1(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B1(a: int4, b: int4, c: int4, d: int4, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered
}
B1(a, d, e, a, a) :- A1(1, b, c, 1, 2) GROUP-BY (a) expand(b, c, d, e).
null(0) :- B1(a, b, c, d, e).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_exceptionNoInput)
{
    char *fileName = (char *)"client_st_agg_many_to_many_const_exception_no_input";
    char *udfFileName = (char *)"client_st_agg_many_to_many";
    EXPECT_NE(GMERR_OK, execCmdCompileUdf(fileName, udfFileName));
}

/*
 * ################################## Agg ManyToOne和Ordered函数中访问KV表 ################################
// ManyToOne和Ordered函数中访问KV表
%table A(k: int4, p: int4, v: int4)
%table B(max: int4, min: int4, k: int4, p: int4)
%aggregate min_max(v: int4 -> min: int4, max: int4){
      ordered,
      many_to_one,
      access_kv(capset)
}

B(max, min, k, p) :- A(k, p, v) GROUP-BY (k, p) min_max(v, min, max).
null(0) :- B(max, min, k, p).
 */
TEST_P(ClientStExecAggDatalog, AggManyToOne_AggOrdered_ReadKV)
{
    const char *labelNameInpA = "A";
    const char *capsetName = "capset";
    const char *capset_configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":false}";
    char *fileName = (char *)"client_st_agg_many2one_read_kv";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 初始化capset kv表
    Status ret = GmcKvCreateTable(g_stmt, capsetName, capset_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, capsetName);
    EXPECT_EQ(GMERR_OK, ret);
    // kv表中插入数据:uint32_t
    char key1[10] = "para1";
    uint32_t value1 = 30;
    ret = GmcKvSet(g_stmt, key1, strlen(key1) + 1, &value1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 2}, {1, 0, 2, 2, 2}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInpA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, capsetName);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Agg ManyToOne和Ordered函数中访问KV表 ################################
// ManyToMany和Ordered函数中访问KV表
%table A(k: int4, a: int4, b: int4)
%table B(k: int4, i: int4, v: int4)
%aggregate expand(a: int4, b: int4 -> i: int4, c: int4){
    many_to_many,
    ordered,
    access_kv(capset)
}

B(k, i, v) :- A(k, a, b) GROUP-BY (k) expand(a, b, i, v).
null(0) :- B(k, i, v).
 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_AggOrdered_ReadKV)
{
    const char *labelNameInpA = "A";
    const char *capsetName = "capset";
    const char *capset_configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":false}";
    char *fileName = (char *)"client_st_agg_many2many_read_kv";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    // 初始化capset kv表
    Status ret = GmcKvCreateTable(g_stmt, capsetName, capset_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, capsetName);
    EXPECT_EQ(GMERR_OK, ret);
    // kv表中插入数据:uint32_t
    char key1[10] = "para1";
    uint32_t value1 = 30;
    ret = GmcKvSet(g_stmt, key1, strlen(key1) + 1, &value1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}};
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameInpA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, capsetName);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
}

/*
 * ################################## Many to Many 复合类型带RunLinkLog ################################

 */
TEST_P(ClientStExecAggDatalog, AggManyToMany_strConst_withFullRunLinkLog)
{
    EXPECT_EQ(GMERR_OK, system("gmadmin -cfgName datalogRunLinkLog -cfgVal 255"));
    const char *labelNameA = "AStr1";
    char *fileName = (char *)"client_st_agg_many_to_many_const";
    char *udfFileName = (char *)"client_st_agg_many_to_many_const";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[100][7];
    for (uint32_t i = 0; i < 100; ++i) {
        records1[i][0] = 1;
        records1[i][1] = 0;
        records1[i][2] = i;
        records1[i][3] = i;
        records1[i][4] = i;
        records1[i][5] = i;
        records1[i][6] = i;
    }
    Status ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; ++i) {
        records1[i][0] = -1;
    }
    ret = InsertInpTableBatch(
        syncConn, syncStmt, labelNameA, (int32_t *)records1, sizeof(records1) / sizeof(records1[0]), true);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
    EXPECT_EQ(GMERR_OK, system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0"));
}

#ifndef ASAN
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t reqId;
    int32_t feId;
    uint8_t data1[1];
    uint8_t data2[2];
    uint8_t data3[4];
    uint8_t data4[8];
    uint8_t data5[16];
    uint8_t data6[32];
    uint8_t data7[64];
    uint8_t data8[128];
    uint8_t data9[256];
    uint8_t *data10;
    uint8_t data11[512];
} AggVarByteInputT;

TEST_F(ClientStExecAggDatalog, structureDmlTestExample_AggManyToOne_SupportBytes)
{
    const char *labelNameInp = "FwdCmdResp";
    char *fileName = (char *)"client_st_agg_many_to_one_support_byte";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 1 > /dev/null");
    system("gmadmin -cfgName enableLogFold -cfgVal 0 > /dev/null");
    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(syncConn, &batchOption, &batch));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, labelNameInp, GMC_OPERATION_INSERT));
    AggVarByteInputT record[20] = {0};
    for (uint32_t i = 0; i < 20; i++) {
        AggVarByteInputT *obj = &record[i];
        obj->dtlReservedCount = 1;
        obj->upgradeVersion = 1;
        obj->reqId = 10086;
        obj->feId = i;
        obj->data1[0] = i;

        uint8_t *bytes = (uint8_t *)DbDynMemCtxAlloc(syncStmt->memCtx, 1024);
        for (uint32_t j = 0; j < 1024; ++j) {
            bytes[j] = j & 0xFF;
        }
        obj->data10 = bytes;

        Status ret = GmcSetVertexProperty(
            syncStmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "reqId", GMC_DATATYPE_INT32, &obj->reqId, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "feId", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data1", GMC_DATATYPE_FIXED, obj->data1, sizeof(obj->data1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data2", GMC_DATATYPE_FIXED, obj->data2, sizeof(obj->data2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data3", GMC_DATATYPE_FIXED, obj->data3, sizeof(obj->data3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data4", GMC_DATATYPE_FIXED, obj->data4, sizeof(obj->data4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data5", GMC_DATATYPE_FIXED, obj->data5, sizeof(obj->data5));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data6", GMC_DATATYPE_FIXED, obj->data6, sizeof(obj->data6));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data7", GMC_DATATYPE_FIXED, obj->data7, sizeof(obj->data7));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data8", GMC_DATATYPE_FIXED, obj->data8, sizeof(obj->data8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data9", GMC_DATATYPE_FIXED, obj->data9, sizeof(obj->data9));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data10", GMC_DATATYPE_BYTES, obj->data10, 1024);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "data11", GMC_DATATYPE_FIXED, obj->data11, sizeof(obj->data11));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet = {};
    EXPECT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));

    system("gmsysview record FwdResp");

    GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}
#endif
