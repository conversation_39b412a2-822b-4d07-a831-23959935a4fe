/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2022-9-19
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} A;

#pragma pack(0)

int32_t dtl_ext_func_func_read_b(void *tuple, GmUdfCtxT *ctx)
{
    GmUdfWriterT *writer = NULL;
    int32_t ret = GmUdfGetDeltaWriter(ctx, 0, &writer);  // 写inpC
    if (ret != GMERR_OK) {
        return ret;
    }

    A key = {.a = 2, .b = 2};
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReaderByIndex(ctx, sizeof(A), &key, 0, 0, &reader);  // 读inpB
    if (ret != GMERR_OK) {
        return ret;
    }

    GmUdfReaderT *reader1 = NULL;
    A key1 = {.a = 1, .b = 2};
    ret = GmUdfCreateCurrentReaderByIndex(ctx, sizeof(A), &key1, 0, 1, &reader1);  // 读inpB
    if (ret != GMERR_OK) {
        return ret;
    }

    A *out1;
    while (ret = GmUdfGetNext(reader, (void **)&out1), ret == GMERR_OK) {
        A temp = {};
        temp.a = out1->b;
        temp.b = out1->a;
        temp.dtlReservedCount = out1->dtlReservedCount;
        ret = GmUdfAppend(writer, sizeof(A), &temp);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    while (ret = GmUdfGetNext(reader1, (void **)&out1), ret == GMERR_OK) {
        A temp = {};
        temp.a = out1->b;
        temp.b = out1->a;
        temp.dtlReservedCount = out1->dtlReservedCount;
        temp.upgradeVersion = out1->upgradeVersion;
        ret = GmUdfAppend(writer, sizeof(A), &temp);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    GmUdfDestroyReader(ctx, reader);
    GmUdfDestroyReader(ctx, reader1);
    return GMERR_OK;
}
