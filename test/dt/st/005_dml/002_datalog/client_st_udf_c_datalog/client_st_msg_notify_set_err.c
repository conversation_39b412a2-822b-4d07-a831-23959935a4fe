/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-03-09
 */

#include "gm_udf.h"
#include "stdio.h"
#include "assert.h"
#define DELETE 1
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} TupleA;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;

int32_t dtl_msg_notify_msgNotifyTbl(Batch *batches, uint32_t arrLen)
{
    printf("batch start.\n");
    for (uint32_t i = 0; i < arrLen; ++i) {
        TupleA *newTuple = (TupleA *)batches[i].newTup;
        uint32_t op = batches[i].op;
        printf("op: %u new tuple a: %d, b: %d, count: %d, upgradeVersion: %d\n", op, newTuple->a, newTuple->b,
            newTuple->dtlReservedCount, newTuple->upgradeVersion);
    }
    printf("batch end set error code 1000.\n");
    return 1000;
}
