#include <stdio.h>
#include <string.h>
#include "gm_udf.h"

#pragma pack(1)
typedef struct B {
    int32_t dtlReservedCount;
    int32_t a;
} B;

typedef struct C {
    int32_t a;
    int32_t b;
} C;

#pragma pack(0)

int32_t dtl_agg_compare_min_max(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    B *inp1 = (B *)tuple1;
    B *inp2 = (B *)tuple2;

    char *key = "para1";
    uint32_t keyLen = strlen(key) + 1;
    uint32_t value = 0;
    uint32_t valueLen = sizeof(uint32_t);
    int32_t ret = GmUdfGetAccessKV(ctx, key, keyLen, &value, &valueLen);
    if (ret != GMERR_OK) {
        printf("==============================>order compare udf kv value fail %u\n", ret);
        return ret;
    }
    if (valueLen != sizeof(uint32_t)) {
        // 不匹配的数据类型
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    printf("=================================================> compare kv value is %u\n", value);
    // 业务逻辑
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_agg_func_min_max(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    B *inpStruct;
    C *outStruct = GmUdfMemAlloc(ctx, sizeof(C));

    char *key = "para1";
    uint32_t keyLen = strlen(key) + 1;
    uint32_t value = 0;
    uint32_t valueLen = sizeof(uint32_t);
    int32_t ret = GmUdfGetAccessKV(ctx, key, keyLen, &value, &valueLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (valueLen != sizeof(uint32_t)) {
        // 不匹配的数据类型
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    printf("=====>kv value is %u\n", value);
    // 业务逻辑
    ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(C);
    *output = (void *)outStruct;
    return GMERR_OK;
}

int32_t dtl_agg_func_getSum(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    B *inpStruct = NULL;
    B *outStruct = GmUdfMemAlloc(ctx, sizeof(B));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->a = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->a += inpStruct->a;
    }
    *outputLen = sizeof(B);
    *output = (void *)outStruct;
    return GMERR_OK;
}
