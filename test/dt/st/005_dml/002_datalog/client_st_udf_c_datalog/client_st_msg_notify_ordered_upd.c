/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2024-03-09
 */

#include "gm_udf.h"
#include "stdio.h"
#include "assert.h"
#define UPDATE 2
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TupleA;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;

// 测试更新
int32_t dtl_msg_notify_compare_outB(void *tup1, void *tup2)
{
    TupleA *ltup = (TupleA *)tup1;
    TupleA *rtup = (TupleA *)tup2;

    if (ltup->a == rtup->a) {
        if (ltup->b == rtup->b) {
            return ltup->c - rtup->c;
        } else {
            return ltup->b - rtup->b;
        }
    }

    return ltup->a - rtup->a;
}
#define BUF_LEN 1024
static char *g_filename = "./msgNotifyTest.log";
// 测试更新
int32_t dtl_msg_notify_outB(Batch *batches, uint32_t arrLen)
{
    FILE *fp = fopen(g_filename, "a+");
    if (fp == NULL) {
        printf("open %s error\n", g_filename);
        return GMERR_DATA_EXCEPTION;
    }
    printf("batch start.\n");
    char buf[BUF_LEN] = {0};
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = batches[i].op;
        TupleA *oldTup = (TupleA *)(batches[i].oldTup);
        TupleA *newTup = (TupleA *)(batches[i].newTup);
        if (op != UPDATE) {
            printf("operation type error!\n");
            (void)fclose(fp);
            return GMERR_DATA_EXCEPTION;
        }
        if (oldTup == NULL || newTup == NULL) {
            printf("data error! NULL value occur\n");
            (void)fclose(fp);
            return GMERR_DATA_EXCEPTION;
        }

        if (oldTup != NULL) {
            (void)sprintf_s(buf, BUF_LEN, "%s %d %d %d %d %d\n", buf, oldTup->dtlReservedCount, oldTup->upgradeVersion,
                oldTup->a, oldTup->b, oldTup->c);
        }
        if (newTup != NULL) {
            (void)sprintf_s(buf, BUF_LEN, "%s %d %d %d %d %d\n", buf, newTup->dtlReservedCount, newTup->upgradeVersion,
                newTup->a, newTup->b, newTup->c);
        }

        printf("a: change %d->%d, b: change %d->%d, c: change: %d->%d, count: %d, upgradeVersion: %d\n", oldTup->a,
            newTup->a, oldTup->b, newTup->b, oldTup->c, newTup->c, newTup->dtlReservedCount, newTup->upgradeVersion);
    }
    (void)fprintf(fp, "%s", buf);
    printf("batch end.\n");
    (void)fclose(fp);

    return GMERR_OK;
}
