/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-2-1
 */

#include "gm_udf.h"
#include "stdio.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
} A;

#pragma pack(0)

int32_t dtl_ext_func_func4(void *tuple, GmUdfCtxT *ctx)
{
    A *inp = (A *)tuple;
    inp->b = inp->a;
    return GMERR_OK;
}
