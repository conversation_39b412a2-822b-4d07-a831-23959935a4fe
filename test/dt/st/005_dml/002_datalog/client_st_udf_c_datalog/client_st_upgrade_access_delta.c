/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2024-4-14
 */

#include "gm_udf.h"
#include "stdio.h"
// 加载调用 init 初始化
#include "gm_udf.h"

#pragma pack(1)

typedef struct Output {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} OutputT;

typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
} A;

int32_t dtl_ext_func_func1(void *tuple, GmUdfCtxT *ctx)
{
    A *input = (A *)tuple;
    input->c = input->a;
    input->d = input->d;

    return GMERR_OK;
}
