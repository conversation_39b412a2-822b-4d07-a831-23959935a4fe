/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2024-1-8
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct Input {
    int32_t dtlReservedCount;
    int32_t b;
    int32_t c;
    int32_t d;
} InputT;

typedef struct Output {
    int32_t count;
} OutputT;
#pragma pack(0)

#define BLOCK_TIME_US (5 * 1000)

int32_t dtl_agg_func_aggCount(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    InputT *inpTup = NULL;
    OutputT *tmpOutput = GmUdfMemAlloc(ctx, sizeof(OutputT));
    *tmpOutput = (OutputT){0};

    int32_t ret = GMERR_OK;
    while ((ret = GmUdfGetNext(input, (void **)&inpTup)) == GMERR_OK) {
        tmpOutput->count++;
    }

    usleep(BLOCK_TIME_US);
    if (ret == GMERR_NO_DATA) {
        *outputLen = sizeof(OutputT);
        *output = tmpOutput;
        return GMERR_OK;
    }

    return ret;
}
