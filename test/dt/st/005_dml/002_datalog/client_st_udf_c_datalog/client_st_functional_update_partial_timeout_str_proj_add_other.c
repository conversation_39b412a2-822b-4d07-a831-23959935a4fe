/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2022-10-18
 */

#include <string.h>
#include <securec.h>
#include "gm_udf.h"

#define NULL_INFO_ZERO_INDEX 0
#define NULL_INFO_ONE_INDEX 1
#define NULL_INFO_TWO_INDEX 2
#define NULL_INFO_THREE_INDEX 3
#define NULL_INFO_FOUR_INDEX 4
#define NULL_INFO_FIVE_INDEX 5
#define NULL_INFO_SIVE_INDEX 6
#define NULL_INFO_SEVEN_INDEX 7

#define ONE_DAY_MILLISECOND (24 * 60 * 60 * 1000)

#define EXPAND_COUNT 2

#pragma pack(1)

typedef struct AStrUpdatePartial6 {
    uint8_t propeNum;
    uint8_t *nullInfo;

    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t t;

    uint32_t aLen;
    char *aStr;
    uint32_t bLen;
    char *bStr;
    uint32_t cLen;
    char *cStr;
    uint32_t dLen;
    char *dStr;
    uint32_t eLen;
    char *eStr;
} AStrUpdatePartial6;

#pragma pack(0)

int32_t dtl_timeout_callback_AStrUpdatePartial6(
    const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    AStrUpdatePartial6 *srcTuple = (AStrUpdatePartial6 *)timeoutTuple;
    AStrUpdatePartial6 *dstTuple = GmUdfMemAlloc(ctx, sizeof(AStrUpdatePartial6));
    if (dstTuple == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dstTuple->propeNum = srcTuple->propeNum;
    dstTuple->nullInfo = GmUdfMemAlloc(ctx, srcTuple->propeNum * sizeof(uint8_t));
    if (dstTuple->nullInfo == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dstTuple->nullInfo[NULL_INFO_ZERO_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_ONE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_TWO_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_THREE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_FOUR_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_FIVE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_SIVE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_SEVEN_INDEX] = 1;

    dstTuple->t = srcTuple->t + ONE_DAY_MILLISECOND;
    dstTuple->dtlReservedCount = -EXPAND_COUNT * srcTuple->dtlReservedCount;
    dstTuple->aLen = srcTuple->aLen;
    dstTuple->aStr = GmUdfMemAlloc(ctx, srcTuple->aLen);
    if (dstTuple->aStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->aStr, srcTuple->aLen, srcTuple->aStr, srcTuple->aLen);

    char *bstr = "bstr";
    dstTuple->bLen = strlen(bstr);
    ;
    dstTuple->bStr = GmUdfMemAlloc(ctx, strlen(bstr) + 1);
    if (dstTuple->bStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->bStr, dstTuple->bLen, bstr, dstTuple->bLen);

    dstTuple->cLen = srcTuple->cLen;
    dstTuple->cStr = GmUdfMemAlloc(ctx, srcTuple->cLen);
    if (dstTuple->cStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->cStr, srcTuple->cLen, srcTuple->cStr, srcTuple->cLen);

    dstTuple->dLen = srcTuple->dLen;
    dstTuple->dStr = GmUdfMemAlloc(ctx, srcTuple->dLen);
    if (dstTuple->dStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->dStr, srcTuple->dLen, srcTuple->dStr, srcTuple->dLen);

    dstTuple->eLen = srcTuple->eLen;
    dstTuple->eStr = GmUdfMemAlloc(ctx, srcTuple->eLen);
    if (dstTuple->eStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->eStr, srcTuple->eLen, srcTuple->eStr, srcTuple->eLen);

    *extraTupleLen = sizeof(AStrUpdatePartial6);
    *extraTuple = dstTuple;
    return GMERR_OK;
}
