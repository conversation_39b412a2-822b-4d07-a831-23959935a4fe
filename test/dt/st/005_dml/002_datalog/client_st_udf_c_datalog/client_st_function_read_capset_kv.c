#include <stdio.h>
#include <string.h>
#include "gm_udf.h"

struct Tuple {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
};
int32_t dtl_ext_func_readCapsetKV(void *tuple, GmUdfCtxT *ctx)
{
    struct Tuple *transTuple = (struct Tuple *)tuple;
    int32_t ret = 0;

    // 正确查询能力集kv表
    char *key = "para1";
    uint32_t keyLen = strlen(key) + 1;

    // 查询的value类型不匹配
    if (transTuple->a == 10) {
        key = "para2";
        keyLen = strlen(key) + 1;
    }

    // 查询不存在的key
    if (transTuple->a > 10) {
        key = "para3";
        keyLen = strlen(key) + 1;
    }
    uint32_t value = 0;
    uint32_t valueLen = sizeof(uint32_t);

    ret = GmUdfGetAccessKV(ctx, key, keyLen, &value, &valueLen);
    if (ret == GMERR_NO_DATA) {
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    if (valueLen != sizeof(uint32_t)) {
        // 不匹配的数据类型
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    // 业务逻辑
    printf("====>value is:%d\n", value);
    return ret;
}
