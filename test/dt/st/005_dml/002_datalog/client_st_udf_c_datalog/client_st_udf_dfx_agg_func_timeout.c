#include "gm_udf.h"
#include <unistd.h>

#pragma pack(1)
typedef struct B {
    int32_t a;
} B;

#pragma pack(0)

int32_t dtl_agg_compare_maxTimeout(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    B *inp1 = (B *)tuple1;
    B *inp2 = (B *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_agg_func_maxTimeout(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    B *inpStruct;
    B *outStruct = GmUdfMemAlloc(ctx, sizeof(B));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    usleep(101000);
    return GMERR_OK;
}
