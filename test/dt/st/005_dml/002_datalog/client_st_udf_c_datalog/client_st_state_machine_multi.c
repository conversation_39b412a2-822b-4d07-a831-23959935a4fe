/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2023-2-18
 */

#include "gm_udf.h"
#define TWO 2
#define THREE 3
#define FOUR 4
#define EIGHT 8
#define EIGHTEIGHT 88

#pragma pack(1)

typedef struct TupleA_ {
    int32_t count;
    int32_t c;
    int32_t d;
    int32_t e;
    int32_t f;
} TupleA;

typedef struct TupleB_ {
    int32_t count;
    int32_t c;
    int32_t e;
    int32_t f;
} TupleB;

#pragma pack(0)

int32_t dtl_ext_func_transferA(void *tuple, GmUdfCtxT *ctx)
{
    TupleA *inp = tuple;
    if (inp->count == 0) {
        if (inp->d == 0) {
            inp->e = EIGHT;
            inp->count = 1;
            return GMERR_OK;
        }
    } else if (inp->count == 1) {
        if (inp->c == 0) {
            inp->e = EIGHT;
            inp->f = EIGHT;
            return GMERR_OK;
        }

        if (inp->c == TWO) {
            inp->e = EIGHTEIGHT;
            inp->f = EIGHTEIGHT;
            return GMERR_OK;
        }

        if (inp->d == THREE) {
            inp->count = -1;
            return GMERR_OK;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_NO_DATA;
}

int32_t dtl_ext_func_transferB(void *tuple, GmUdfCtxT *ctx)
{
    TupleB *inp = tuple;
    if (inp->count == 0) {
        if (inp->c == 1) {
            inp->e = 1;
            inp->count = 1;
            return GMERR_OK;
        }
    } else if (inp->count == 1) {
        if (inp->c == TWO) {
            inp->f = TWO;
            return GMERR_OK;
        }

        if (inp->c == THREE) {
            inp->f = THREE;
            return GMERR_OK;
        }

        if (inp->c == FOUR) {
            inp->count = -1;
            return GMERR_OK;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_NO_DATA;
}
