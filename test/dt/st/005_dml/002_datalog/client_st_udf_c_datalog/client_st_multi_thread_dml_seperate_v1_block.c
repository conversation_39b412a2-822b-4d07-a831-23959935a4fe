/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-10-12
 */

#include <unistd.h>
#include "gm_udf.h"
#include "stdio.h"

#define SLEEP_INTERVAL_USECONDS 50000

// 加载调用 init 初始化
int32_t dtl_ext_func_init()
{
    printf("init multi thread dml successfully.\n");
    return GMERR_OK;
}

// 卸载/加载失败调用 uninit 去初始化
int32_t dtl_ext_func_uninit()
{
    printf("uninit multi thread dml successfully.\n");
    return GMERR_OK;
}

struct Tuple {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
};

int32_t dtl_tbm_tbl_outF(int op, void *tuple)
{
    struct Tuple *result = (struct Tuple *)(tuple);
    if (result->dtlReservedCount < 0) {
        usleep(SLEEP_INTERVAL_USECONDS);  // sleep 50ms
    }
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_outG(int op, void *tuple)
{
    struct Tuple *result = (struct Tuple *)(tuple);
    if (result->dtlReservedCount < 0) {
        usleep(SLEEP_INTERVAL_USECONDS);  // sleep 50ms
    }
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_outH(int op, void *tuple)
{
    struct Tuple *result = (struct Tuple *)(tuple);
    if (result->dtlReservedCount < 0) {
        usleep(SLEEP_INTERVAL_USECONDS);  // sleep 50ms
    }
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_outI(int op, void *tuple)
{
    struct Tuple *result = (struct Tuple *)(tuple);
    if (result->dtlReservedCount < 0) {
        usleep(SLEEP_INTERVAL_USECONDS);  // sleep 50ms
    }
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_outJ(int op, void *tuple)
{
    struct Tuple *result = (struct Tuple *)(tuple);
    if (result->dtlReservedCount < 0) {
        usleep(SLEEP_INTERVAL_USECONDS);  // sleep 50ms
    }
    return GMERR_OK;
}
