/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2022-10-18
 */

#include <string.h>
#include <securec.h>
#include "gm_udf.h"
#define EXPAND_COUNT 2

#pragma pack(1)

typedef struct AStrUpdate2 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t t;

    uint32_t aLen;
    char *aStr;
    uint32_t bLen;
    char *bStr;
    uint32_t cLen;
    char *cStr;
    uint32_t dLen;
    char *dStr;
    uint32_t eLen;
    char *eStr;
} AStrUpdate2;

#pragma pack(0)

int32_t dtl_timeout_callback_AStrUpdate2(
    const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    AStrUpdate2 *srcTuple = (AStrUpdate2 *)timeoutTuple;
    AStrUpdate2 *dstTuple = GmUdfMemAlloc(ctx, sizeof(AStrUpdate2));
    if (dstTuple == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    dstTuple->t = srcTuple->t + 1;
    dstTuple->dtlReservedCount = -EXPAND_COUNT * srcTuple->dtlReservedCount;
    dstTuple->aLen = srcTuple->aLen;
    dstTuple->aStr = GmUdfMemAlloc(ctx, srcTuple->aLen);
    if (dstTuple->aStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->aStr, srcTuple->aLen, srcTuple->aStr, srcTuple->aLen);

    dstTuple->bLen = srcTuple->bLen;
    dstTuple->bStr = GmUdfMemAlloc(ctx, srcTuple->bLen);
    if (dstTuple->bStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->bStr, srcTuple->bLen, srcTuple->bStr, srcTuple->bLen);

    char *str = "communication";
    dstTuple->cLen = strlen(str) + 1;
    dstTuple->cStr = GmUdfMemAlloc(ctx, dstTuple->cLen);
    if (dstTuple->cStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->cStr, dstTuple->cLen, str, dstTuple->cLen);

    dstTuple->dLen = srcTuple->dLen;
    dstTuple->dStr = GmUdfMemAlloc(ctx, srcTuple->dLen);
    if (dstTuple->dStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->dStr, srcTuple->dLen, srcTuple->dStr, srcTuple->dLen);

    dstTuple->eLen = srcTuple->eLen;
    dstTuple->eStr = GmUdfMemAlloc(ctx, srcTuple->eLen);
    if (dstTuple->eStr == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memcpy_s(dstTuple->eStr, srcTuple->eLen, srcTuple->eStr, srcTuple->eLen);
    dstTuple->upgradeVersion = srcTuple->upgradeVersion;

    *extraTupleLen = sizeof(AStrUpdate2);
    *extraTuple = dstTuple;
    return GMERR_OK;
}
