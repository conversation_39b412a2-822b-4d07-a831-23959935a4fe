/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2023-05-05
 */

#include <string.h>
#include <stdio.h>
#include <securec.h>
#include "gm_udf.h"

#define NULL_INFO_ZERO_INDEX 0
#define NULL_INFO_ONE_INDEX 1
#define NULL_INFO_TWO_INDEX 2
#define NULL_INFO_THREE_INDEX 3
#define NULL_INFO_FOUR_INDEX 4
#define NULL_INFO_FIVE_INDEX 5
#define NULL_INFO_SIX_INDEX 6
#define ONE_DAY_MILLISECOND (24 * 60 * 60 * 1000)
#define INSERT 0
#define DELETE 1
#define UPDATE 2

#define TEN_CONST 10

#pragma pack(1)

typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t t;
} A;

typedef struct B {
    uint8_t propeNum;
    uint8_t *nullInfo;
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t t;
} B;

typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TupleA;

typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} TupleTbm;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;

#pragma pack(0)

int32_t dtl_timeout_callback_B(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    A *srcTuple = (A *)timeoutTuple;

    A *dstTuple = GmUdfMemAlloc(ctx, sizeof(A));
    if (dstTuple == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    dstTuple->a = srcTuple->a + TEN_CONST;
    dstTuple->b = srcTuple->b + TEN_CONST;

    // timeout one day after
    dstTuple->t = srcTuple->t + ONE_DAY_MILLISECOND;
    dstTuple->dtlReservedCount = 1;

    *extraTupleLen = sizeof(A);
    *extraTuple = dstTuple;

    // 读写其他表
    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *out1;
    while (ret = GmUdfGetNext(reader, (void **)&out1), ret == GMERR_OK) {
        A temp = {};
        temp.a = out1->b;
        temp.b = out1->a;
        temp.t = out1->t;
        temp.dtlReservedCount = out1->dtlReservedCount;
        temp.upgradeVersion = out1->upgradeVersion;
        printf("a: %ld, b: %ld, t: %ld, count: %d\n", temp.a, temp.b, temp.t, temp.dtlReservedCount);
    }
    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_timeout_callback_F(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    B *srcTuple = (B *)timeoutTuple;

    B *dstTuple = GmUdfMemAlloc(ctx, sizeof(B));
    if (dstTuple == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    dstTuple->a = srcTuple->a + TEN_CONST;
    dstTuple->b = srcTuple->b + TEN_CONST;

    // timeout one day after
    dstTuple->t = srcTuple->t + ONE_DAY_MILLISECOND;
    dstTuple->dtlReservedCount = 1;

    dstTuple->propeNum = srcTuple->propeNum;
    dstTuple->nullInfo = GmUdfMemAlloc(ctx, srcTuple->propeNum * sizeof(uint8_t));
    if (dstTuple->nullInfo == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dstTuple->nullInfo[NULL_INFO_ZERO_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_ONE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_TWO_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_THREE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_FOUR_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_FIVE_INDEX] = 1;
    dstTuple->nullInfo[NULL_INFO_SIX_INDEX] = 1;

    *extraTupleLen = sizeof(B);
    *extraTuple = dstTuple;

    // 读写其他表
    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *out1;
    while (ret = GmUdfGetNext(reader, (void **)&out1), ret == GMERR_OK) {
        A temp = {};
        temp.a = out1->b;
        temp.b = out1->a;
        temp.t = out1->t;
        temp.dtlReservedCount = out1->dtlReservedCount;
        temp.upgradeVersion = out1->upgradeVersion;
        printf("a: %ld, b: %ld, t: %ld, count: %d\n", temp.a, temp.b, temp.t, temp.dtlReservedCount);
    }
    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 加载调用 init 初始化
int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    printf("init successfully.\n");
    return GMERR_OK;
}

// 卸载/加载失败调用 uninit 去初始化
int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    printf("uninit successfully.\n");
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_outD(uint32_t op, void *tuple)
{
    TupleTbm *result = (TupleTbm *)(tuple);
    printf("outD operation: %s, tuple a: %ld, b: %ld, c: %ld, dtlReservedCount: %d\n",
        op == 0u ? "insert" : (op == 1u ? "delete" : "update"), result->a, result->b, result->c,
        result->dtlReservedCount);
    if (result->dtlReservedCount < 0) {
        printf("failed\n");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

// 测试插入
int32_t dtl_msg_notify_outE(Batch *batches, uint32_t arrLen)
{
    printf("batch start.\n");
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = batches[i].op;
        TupleA *oldTup = (TupleA *)(batches[i].oldTup);
        TupleA *newTup = (TupleA *)(batches[i].newTup);
        if (op == INSERT) {
            printf(
                "insert a: %ld, b: %ld, c:%ld, count: %d\n", newTup->a, newTup->b, newTup->c, newTup->dtlReservedCount);
        } else if (op == DELETE) {
            printf(
                "delete a: %ld, b: %ld, c:%ld, count: %d\n", oldTup->a, oldTup->b, oldTup->c, oldTup->dtlReservedCount);
        } else if (op == UPDATE) {
            printf("update a: change %ld->%ld, b: change %ld->%ld, c: change: %ld->%ld, count: %d\n", oldTup->a,
                newTup->a, oldTup->b, newTup->b, oldTup->c, newTup->c, newTup->dtlReservedCount);
        }
    }
    printf("batch end.\n");
    return GMERR_OK;
}
