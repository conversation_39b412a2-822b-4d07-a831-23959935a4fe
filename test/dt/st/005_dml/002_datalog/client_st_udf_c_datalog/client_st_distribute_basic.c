/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-2-1
 */

#include "gm_udf.h"
#include "stdio.h"
// 加载调用 init 初始化
int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    printf("init successfully.\n");
    return GMERR_OK;
}

// 卸载/加载失败调用 uninit 去初始化
int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    printf("uninit successfully.\n");
    return GMERR_OK;
}

struct TupleA {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
};

int32_t dtl_tbm_tbl_outB(uint32_t op, void *tuple)
{
    struct TupleA *result = (struct TupleA *)(tuple);
    printf("operation: %s, tuple a: %d, b: %d, c: %d, dtlReservedCount: %d, upgradeVersion: %d\n",
        op == 0 ? "insert" : "delete", result->a, result->b, result->c, result->dtlReservedCount,
        result->upgradeVersion);
    return GMERR_OK;
}
