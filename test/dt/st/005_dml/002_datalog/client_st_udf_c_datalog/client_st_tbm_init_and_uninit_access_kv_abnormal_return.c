/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-2-1
 */

#include <string.h>
#include <stdio.h>
#include "gm_udf.h"

// 加载调用 init 初始化
int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    // 正确查询能力集kv表
    char *key = "para1";
    uint32_t keyLen = strlen(key) + 1;

    uint32_t value = 0;
    uint32_t valueLen = sizeof(uint32_t);

    int32_t ret = GmUdfGetAccessKV(ctx, key, keyLen, &value, &valueLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (valueLen != sizeof(uint32_t)) {
        // 不匹配的数据类型
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }

    uint32_t *test = GmUdfMemAlloc(ctx, sizeof(uint32_t));
    if (test == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    printf("init successfully====>kv value is:%d\n", value);
    return GMERR_OK;
}

// 卸载/加载失败调用 uninit 去初始化
int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    // 正确查询能力集kv表
    char *key = "para1";
    uint32_t keyLen = strlen(key) + 1;

    uint32_t value = 0;
    uint32_t valueLen = sizeof(uint32_t);

    int32_t ret = GmUdfGetAccessKV(ctx, key, keyLen, &value, &valueLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (valueLen != sizeof(uint32_t)) {
        // 不匹配的数据类型
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }

    uint32_t *test = GmUdfMemAlloc(ctx, sizeof(uint32_t));
    if (test == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    printf("uninit successfully====>value is:%d\n", value);
    return GMERR_OK;
}

struct TupleA {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
};

int32_t dtl_tbm_tbl_outB(uint32_t op, void *tuple)
{
    return GMERR_OK;
}
