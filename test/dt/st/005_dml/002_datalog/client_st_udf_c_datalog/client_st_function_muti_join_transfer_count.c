/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Datalog udf .
 * Author: GMDBv5 EE Team
 * Create: 2023-8-18
 */

#include <stdio.h>
#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t count;
    int32_t a;
    int32_t b;
} A;

#pragma pack(0)

int32_t dtl_ext_func_tupleFilter(void *tuple, GmUdfCtxT *ctx)
{
    A *inp = (A *)tuple;
    inp->b = inp->a + 1;

    printf("in udf input a:%d, b:%d, count:%d\n", inp->a, inp->b, inp->count);

    return GMERR_OK;
}
