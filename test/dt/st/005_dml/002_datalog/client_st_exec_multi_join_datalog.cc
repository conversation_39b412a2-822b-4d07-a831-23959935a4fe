/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Datalog ST .
 * Author: GMDBv5 EE Team
 * Create: 2022-10-10
 */

#include <iostream>
#include <climits>
#include "client_common_st.h"
#include "client_st_exec_datalog.h"

using namespace std;

class ClientStExecMultiJoinDatalog : public ClientStExecDatalog {
protected:
    const char *labelNameA = "A";
    const char *labelNameB = "B";
    const char *labelNameOut1 = "out1";
    const char *labelNameOut2 = "out2";
    const char *labelNameOut3 = "out3";
    const char *udfFileName = "client_st_multi_join";

    const char *labelNameFunc1 = "func1";
    const char *labelNameFunc2 = "func2";
    const char *labelNameFunc3 = "func3";
    const char *labelNameFunc4 = "func4";
    const char *labelNameFunc5 = "func5";
    const char *labelNameFunc6 = "func6";
    const char *labelNameFunc7 = "func7";
    const char *labelNameFunc8 = "func8";
    const char *labelNameFunc9 = "func9";
    const char *labelNameFunc10 = "func10";
    const char *labelNameFunc11 = "func11";
    const char *labelNameFunc12 = "func12";
    const char *labelNameFunc13 = "func13";
    const char *labelNameFunc14 = "func14";
    const char *labelNameFunc15 = "func15";

protected:
    void SequentialScanOut(GmcStmtT *syncStmt)
    {
        // 屏蔽打印 cout << "====================== " << labelNameOut1 << " =========================" << endl;
        ScanOutTable(g_stmt, labelNameOut1);
        // 屏蔽打印 cout << "====================== " << labelNameOut2 << " =========================" << endl;
        ScanOutTable(g_stmt, labelNameOut2);
        // 屏蔽打印 cout << "====================== " << labelNameOut3 << " =========================" << endl;
        ScanOutTable(g_stmt, labelNameOut3);
    }
};
INSTANTIATE_TEST_CASE_P(ExecMultiJoinDatalog, ClientStExecMultiJoinDatalog,
    ::testing::Values(StClientTestData{.isAsync = false}, StClientTestData{.isAsync = true}));

/*
 * * ################################## 【多表join测试】单表与多function join ################################
 * %table A(a: int4, b: int4)
 * %table out1(a:int4 , b:int4 , c:int4)
 * %table out2(a:int4 , b:int4 , c:int4)
 * %table out3(a:int4 , b:int4 , c:int4)
 * %function propAppend(a: int4, b: int4 -> c: int4)
 * %function tupleFilter(a:int4 -> b:int4)
 *
 * out1(a,b,c) :- A(a, b), propAppend(a,b,c) , tupleFilter(a,b)
 * out2(a,b,c) :- A(a, b), propAppend(1,b,c) , tupleFilter(a,b)
 * out3(a,b,c) :- A(a, b), propAppend(1,2,c) , tupleFilter(a,b)
 */
TEST_F(ClientStExecMultiJoinDatalog, multiJoinSingleTableWithFunc)
{
    char *fileName = (char *)"client_st_multi_join_single_table";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    // 插入数据
    int32_t record1[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameA, (int32_t *)record1,
                            sizeof(record1) / sizeof(record1[0]), true));

    // todo 标明预期
    SequentialScanOut(syncStmt);

    // 释放资源
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * * ################################## 【多表join测试】交替join ################################
 * %table A(a: int4, b: int4)
 * %table B(a: int4, b: int4)
 * %table out1(a:int4 , b:int4 , c:int4)
 * %table out2(a:int4 , b:int4 , c:int4)
 * %table out3(a:int4 , b:int4 , c:int4)
 * %function propAppend(a: int4, b: int4 -> c: int4)
 * %function tupleFilter(a:int4 -> b:int4)
 *
 * out1(a,b,c) :- A(a, b), propAppend(a,b,c) , B(a, b), tupleFilter(a,b)
 * out2(a,b,c) :- A(a, b), propAppend(1,b,c) , B(a, b), tupleFilter(a,b)
 * out3(a,b,c) :- A(a, b), propAppend(1,2,c) , B(a, b), tupleFilter(a,b)
 */
TEST_F(ClientStExecMultiJoinDatalog, multiJoinMultiTableWithFunc1)
{
    char *fileName = (char *)"client_st_multi_join_multi_table1";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    // 向A表插入数据
    int32_t record1[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameA, (int32_t *)record1,
                            sizeof(record1) / sizeof(record1[0]), true));
    SequentialScanOut(syncStmt);

    // 向B表插入数据
    int32_t record2[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameB, (int32_t *)record2,
                            sizeof(record2) / sizeof(record2[0]), true));
    SequentialScanOut(syncStmt);

    // 释放资源
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * * ################################## 【多表join测试】交替join ################################
 * %table A(a: int4, b: int4)
 * %table B(a: int4, b: int4)
 * %table out1(a:int4 , b:int4 , c:int4)
 * %table out2(a:int4 , b:int4 , c:int4)
 * %table out3(a:int4 , b:int4 , c:int4)
 * %function propAppend(a: int4, b: int4 -> c: int4)
 * %function tupleFilter(a:int4 -> b:int4)
 *
 * out1(a,b,c) :- A(a, b) , B(a, b), propAppend(a,b,c), tupleFilter(a,b)
 * out2(a,b,c) :- A(a, b) , B(a, b), propAppend(1,b,c), tupleFilter(a,b)
 * out3(a,b,c) :- A(a, b) , B(a, b), propAppend(1,2,c), tupleFilter(a,b)
 */
TEST_F(ClientStExecMultiJoinDatalog, multiJoinMultiTableWithFunc2)
{
    char *fileName = (char *)"client_st_multi_join_multi_table2";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    // 向A表插入数据
    int32_t record1[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameA, (int32_t *)record1,
                            sizeof(record1) / sizeof(record1[0]), true));
    SequentialScanOut(syncStmt);

    // 向B表插入数据
    int32_t record2[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameB, (int32_t *)record2,
                            sizeof(record2) / sizeof(record2[0]), true));
    SequentialScanOut(syncStmt);

    // 释放资源
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * * ################################## 【多表join测试】15join ################################
 */
TEST_F(ClientStExecMultiJoinDatalog, multiJoinMultiTable15Table)
{
    char *fileName = (char *)"client_st_multi_join_multi_table_15table";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    int32_t record1[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameA, (int32_t *)record1,
                            sizeof(record1) / sizeof(record1[0]), true));
    // 屏蔽打印 cout << "====================== " << labelNameOut1 << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut1);

    // 释放资源
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * * ################################## 【多表join测试】15join开启RunLinkLog ################################
 */
TEST_P(ClientStExecMultiJoinDatalog, multiJoinMultiTable15Table_withFullRunLinkLog)
{
    EXPECT_EQ(GMERR_OK, system("gmadmin -cfgName datalogRunLinkLog -cfgVal 255"));
    char *fileName = (char *)"client_st_multi_join_multi_table_15table";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t record1[][4] = {{1, 0, 1, 1}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, labelNameA, (int32_t *)record1,
                            sizeof(record1) / sizeof(record1[0]), true));
    // 屏蔽打印 cout << "====================== " << labelNameOut1 << " =========================" << endl;
    ScanOutTable(g_stmt, labelNameOut1);

    // 释放资源
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
    EXPECT_EQ(GMERR_OK, system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0"));
}

TEST_F(ClientStExecMultiJoinDatalog, multiJoinMultiTable16Table)
{
    char *fileName = (char *)"client_st_multi_join_multi_table_16table_63field";
    EXPECT_EQ(GMERR_OK, execCmd(fileName));  // inputAll

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    int32_t record1[][65] = {{2, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1}};
    EXPECT_EQ(GMERR_OK,
        InsertInpTableBatch(syncConn, syncStmt, "inputAll", (int32_t *)record1, ELEMENT_COUNT(record1), true));
    ScanAndCheckTable(syncStmt, "A001", (int32_t *)record1, ELEMENT_COUNT(record1));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO |wc -l");

    // 释放资源
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}
