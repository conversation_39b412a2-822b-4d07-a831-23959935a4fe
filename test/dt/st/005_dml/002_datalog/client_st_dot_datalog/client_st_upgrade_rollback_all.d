%version v0.0.0

%table inpA(a:int4, b:int4, c:int4)
%table outB(a:int4, b:int4, c:int4){
    tbm,
    index(0(a, b))
}

%table inpC(a:int4, b:int4, c:int4)
%table outD(a:int4, b:int4, c:int4)
%aggregate agg(a:int4 -> b:int4) {
    many_to_many
}

%table mid(a:int4, b:int4, c:int4, d:int4) {index(0(a, b, c))}

%function init()
%function uninit()
outB(a, b, c) :- inpA(a, b, c). // r0

mid(a, b, d, 1) :- inpC(a, b, c) GROUP-BY(a, b) agg(c, d). // r1
outD(a, b, c) :- mid(a, b, c, -).   // r2
