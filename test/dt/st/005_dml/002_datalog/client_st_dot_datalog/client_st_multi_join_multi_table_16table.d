%table A(a: int4, b: int4)
%table out1(a:int4 , b:int4 , c:int4)
%function func1(a:int4 -> b:int4)
%function func2(a:int4 -> b:int4)
%function func3(a:int4 -> b:int4)
%function func4(a:int4 -> b:int4)
%function func5(a:int4 -> b:int4)
%function func6(a:int4 -> b:int4)
%function func7(a:int4 -> b:int4)
%function func8(a:int4 -> b:int4)
%function func9(a:int4 -> b:int4)
%function func10(a:int4 -> b:int4)
%function func11(a:int4 -> b:int4)
%function func12(a:int4 -> b:int4)
%function func13(a:int4 -> b:int4)
%function func14(a:int4 -> b:int4)
%function func15(a:int4 -> b:int4)
%function func16(a:int4 -> b:int4)

out1(a,b,c) :- A(a, b), func1(a,c), func2(a,b), func3(a,b), func4(a,b), func5(a,b), func6(a,b), func7(a,b), func8(a,b), func9(a,b), func10(a,b), func11(a,b), func12(a,b), func13(a,b), func14(a,b), func15(a,b), func16(a,b).
