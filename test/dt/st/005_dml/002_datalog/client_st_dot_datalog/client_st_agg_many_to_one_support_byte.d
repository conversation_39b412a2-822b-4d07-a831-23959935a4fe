%table FwdCmdResp(reqId:int4, feId:int4, data1:byte1, data2:byte2, data3:byte4, data4:byte8, data5:byte16, data6:byte32, data7:byte64, data8:byte128, data9:byte256, data10:byte, data11:byte512){
      index(0(reqId, feId))
}

%aggregate FwdCmdRespAggSum(feId:int4, data1:byte1, data2:byte2, data3:byte4, data4:byte8, data5:byte16, data6:byte32, data7:byte64, data8:byte128, data9:byte256, data10:byte, data11:byte512 -> sum:int4, data:byte){
      many_to_one
}

%table FwdResp(reqId:int4, sum:int4, data:byte){
      index(0(reqId))
}

FwdResp(reqId, sum, data) :- FwdCmdResp(reqId, feId, data1, data2, data3, data4, data5, data6, data7, data8, data9, data10, data11) 
                              GROUP-BY(reqId) FwdCmdRespAggSum(feId, data1, data2, data3, data4, data5, data6, data7, data8, data9, data10, data11, sum, data).
null(0) :- FwdResp(reqId, sum, data).
