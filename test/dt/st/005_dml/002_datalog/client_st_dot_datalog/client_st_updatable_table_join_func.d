%table A(a: int4, b: int4) {index(0(a)), update}
%table out1(a:int4 , b:int4 , c:int4)
%table out2(a:int4 , b:int4 , c:int4)
%table out3(a:int4 , b:int4 , c:int4)
%function propAppend(a: int4, b: int4 -> c: int4)
%function tupleFilter(a:int4 -> b:int4)

out1(a,b,c) :- A(a, b), propAppend(a,b,c) , tupleFilter(a,b).
out2(a,b,c) :- A(a, b), propAppend(1,b,c) , tupleFilter(a,b).
out3(a,b,c) :- A(a, b), propAppend(1,2,c) , tupleFilter(a,b).
