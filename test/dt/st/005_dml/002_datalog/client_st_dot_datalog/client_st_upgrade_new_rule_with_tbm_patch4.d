%version v3.0.0 -> v4.0.0

%function func4(a:int4 -> b:int4)
%table NewTable2(a:int4, b:int4, c:int4){update,index(0(a))}
%table NewTable3(a:int4, b:int4, c:int4){update,index(0(a))}
%table NewTable4(a:int4, b:int4, c:int4){update,index(0(a))}
%rule rx3 outB(a, b, c) :- midC(a, b, c),inpA(a, b, c),NewTable2(a,b,c),func4(a,c).
%rule rxx3 outB(a, b, c) :- NewTable3(a,b,c).
%rule rxx4 null(0) :- NewTable4(a,b,c).

%redo REDO_OFF
%table NewTable5(a:int4, b:int4, c:int4){update,index(0(a))}
%alter rule rx2 outB(a, b, c) :- midC(a, b, c),inpA(a, b, c),NewTable5(a,b,c).
