%function funcWriteDelta(a:int4 , b:int4 -> c:int4) {
        access_delta(nsp10.C)
}
nsp10.out1(a, b,c) :- nsp10.A(a,b),funcWriteDelta(a,b,c).

namespace nsp10{
%table A(a:int4 , b:int4)
%table out1(a:int4 , b:int4, c:int4)
%table C(a:int4 , b:int4){index(0(a)), update, union_delete(D)} // C关联A，B，D均会成环
%table D(a:int4 , b:int8){index(0(a)), update}
%readwrite A, out1, C

null(0) :- C(-,-).
null(0) :- D(-,-).
}
