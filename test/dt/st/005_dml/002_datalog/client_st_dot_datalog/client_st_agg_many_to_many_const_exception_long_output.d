// 输出表常量长度超出最大长度限制 129的字符串
%table A1(a: int4, b: int4, c: int4, d: int4, e: int4)
%table B1(a: int4, b: int4, c: int4, d: str, e: int4)

%aggregate expand(a: int4, b: int4 -> c: int4, d: int4){
    many_to_many,
    ordered  
}

B1(a, d, e, "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111", a) :- A1(a, b, c, -, -) GROUP-BY (a) expand(b, c, d, e).
null(0) :- B1(a, b, c, d, e).
