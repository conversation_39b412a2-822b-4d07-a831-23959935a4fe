%version v0.0.1
%table inpA(a:int4, b:int4, c:int4)
%table outB(a:int4, b:int4, c:int4){
    tbm,
    index(0(a, b))
}

%table outC(a:int4, b:int4, c:int4){
    notify
}

%table External(a:int4, b:int4, c:int4){
    external,
    index(0(a))
}

%table External_Copy(a:int4, b:int4, c:int4){
    external,
    index(0(a))
}

%table transient_finish(a:int4, b:int4, c:int4){
    index(0(a)),
    transient(finish)
}

%function init()
%function uninit()

transient_finish(a, b, c) :- inpA(a, b, c).
outB(a, b, c) :- transient_finish(a, b, c).
outC(a, b, c) :- transient_finish(a, b, c).
External(a, b, c) :- transient_finish(a, b, c).
External_Copy(a, b, c) :- transient_finish(a, b, c).
