// 输入表有一个整型常量
%table A(k: str, p: int4, v: int4)
%table B(max: int4, min: int4, k: str)
%aggregate min_max(v: int4 -> min: int4, max: int4){
      ordered
}
B(max, min, k) :- A(k, 1, v) GROUP-BY (k) min_max(v, min, max).
null(0) :- B(max, min, k).


// 输入表有一个字符串常量
%table C(a: str, b: str, c: int4)
%table D(a: str, c: int4)
%aggregate getSum(c: int4 -> sum: int4)
D(a, sum) :- C(a, "string1", c) GROUP-BY (a) getSum(c, sum).
null(0) :- D(a, sum).


// 输入表有一个fix常量
%table E(a: str, b: byte4, c: int4)
%table F(a: str, c: int4)
F(a, sum) :- E(a, "0x01010101", c) GROUP-BY (a) getSum(c, sum).
null(0) :- F(a, sum).


// 输入表有2个常量，字符串和fix组合
%table G(a: str, b: byte4, c: byte4, d: int4)
%table H(a: byte4, c: int4)
H(c, sum) :- G("string1", "0x01010101", c, d) GROUP-BY (c) getSum(d, sum).
null(0) :- H(a, sum).


// 输入表有3个常量，字符串，fix，整型组合
%table I(a: str, b: byte4, c: int4, d: int4, e: int4)
%table J(max: int4, min: int4, c: int4)
J(max, min, c) :- I("string1", "0x01010101", c, d, 1) GROUP-BY (c) min_max(d, min, max).
null(0) :- J(max, min, c).
