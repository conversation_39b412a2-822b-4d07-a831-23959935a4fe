%table A(a: str, b:byte2 , c:str , d:byte128)
%table B(a: str, b:byte2 , c:str , d:byte128) { transient(tuple), variant }
%table C(a: str, b:byte2 , c:str , d:byte128) { transient(tuple), variant }
%table D(a: str, b:byte2 , c:str , d:byte128)
%table E(a: str, b:byte2 , c:str , d:byte128) { transient(tuple), variant }
%table F(a: str, b:byte2 , c:str , d:byte128)


B("nanjing","FFFF","wuhan",d) :- A("beijing1",-,"xian1",d).
C(a,b,c,d) :- B(a,b,c,d).
D(a,b,c,d) :- C(a,b,c,d).
F("shanghai","FFFF",c,d) :- E("beijing0","FFFF",c,d).
