#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

#pragma pack(0)

int32_t dtl_agg_compare_expand(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        if (inp1->b < inp2->b) {
            return -1;
        } else if (inp1->b > inp2->b) {
            return 1;
        }
        return 0;
    }
}

int32_t dtl_agg_func_expand(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    A *inpStruct;
    A *outStruct = GmUdfMemAlloc(ctx, sizeof(A));
    // 取第一条记录
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->a = inpStruct->a;
    outStruct->b = inpStruct->b;
    ret = GmUdfAppend(output, sizeof(A), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 取最后一条记录
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->a = inpStruct->a;
        outStruct->b = inpStruct->b;
    }
    ret = GmUdfAppend(output, sizeof(A), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
