%table B(a:uint1_3, b:uint1_3, c:uint1_3, d:uint2, e:uint1_3, f:uint4_3, g:uint4_3, h:uint2_3){ index(0(d)) }
%table A(a:uint1_3, b:uint1_3, c:uint1_3, d:uint2, e:uint1_3, f:uint4_3, g:uint4_3, h:uint2_3){ index(0(d)) }
%table C(a:uint1_3, b:uint1_3, d:uint2, c:uint1_3, e:uint1_3, f:uint4_3, g:uint4_3, h:uint2_3){ index(0(d)) }
%function func(a:uint1_3, b:uint1_3, c:uint1_3 -> e:uint1_3, f:uint4_3, g:uint4_3, h:uint2_3) {access_delta(C)}

B(a, b, c, d, e, f, g, h) :- A(a, b, c, d, e, f, g, h), func(a, b, c, e, f, g, h).
null(0) :- C(-,-,-,-,-,-,-,-).
