%version v0.0.0 -> v0.0.1
%block 1
namespace nsp51 {
    %readwrite outD2
    %table midA(a:int4, b:int4, c:int4){transient(finish),index(0(a,c))}
    %table outD2(a:int4, b:int4, c:int4){tbm,index(0(a,c))}
    %rule rx13 midA(a, b, c) :- inpA(a, b, c).
    %rule rx14 nsp3.outD(a, b, 1) :- midA(a, b, -).
    %rule rx16 nsp51.outD2(a, b, c) :- nsp522.outB(a, b, c).
}

namespace nsp522 {
    %readwrite outB
}

%precedence nsp3.outD,  nsp51.outD2
%alter rule r11 nsp3.outD(a, b, 111111111) :- nsp3.outB(a, b, 111111111),nsp3.outC(a, b, 111111111).
