%version v0.0.0
%table inpA(a:int4, b:int4, c:int4){
    index(0(a, b))
}
%table inpB(a:int4, b:int4, c:int4){
    index(0(a, b))
}
%table inpC(a:int4, b:int4, c:int4){
    index(0(a, b))
}
%table inpD(a:int4, b:int4, c:int4){
    index(0(a, b))
}

%table outA(a:int4, b:int4, c:int4){
    index(0(a, b))
}
%table outB(a:int4, b:int4, c:int4){
    index(0(a, b))
}
%table outC(a:int4, b:int4, c:int4){
    index(0(a, b))
}

outA(a, b, c) :- inpA(a, b, -), inpC(-, b, c).  // r0 topo1
outB(a, b, c) :- inpB(a, b, -), inpC(-, b, c).  // r1 topo1
outC(a, b, c) :- inpD(a, b, c).                 // r3 topo2
