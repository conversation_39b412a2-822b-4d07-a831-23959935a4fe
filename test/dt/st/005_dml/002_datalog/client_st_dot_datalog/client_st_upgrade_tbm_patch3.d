%version v0.0.0 -> v0.0.1
%block 1
namespace nsp51 {
    %readwrite outD
    %table inpA(a:int4, b:int4, c:int4){upgrade_version(5),update,index(0(a,c))}
    %table midA(a:int4, b:int4, c:int4){upgrade_version(5),transient(finish),index(0(a,c))}
    %table outD(a:int4, b:int4, c:int4){upgrade_version(5),tbm, index(0(a,c))}
    %rule rx13 midA(a, b, c) :- nsp51.inpA(a, b, c).
    %rule rx14 nsp51.outD(a, b, c) :- midA(a, b, c),nsp51.inpA(a, b, c).
}
%precedence nsp3.outD, nsp51.outD

