// 输入表和资源表需要通过DLR从ylog1同步到ylog2
%table inp(a:int4, b:int4) {
    index(0(a)),
    update
}

%table inpTransientFinish(a:int4, b:int4){
    index(0(a)),
    transient(finish)
}

%table inpTransientTuple(a:int4, b:int4){
    index(0(a)),
    transient(tuple)
}

%table NotifyTblUpdate(a:int4, b:int4){
    update,
    index(0(a)),
    notify,
    data_sync_label(true)
}

%table NotifyTblNormal(a:int4, b:int4){
    index(0(a)),
    notify,
    data_sync_label(true)
}

%table NotifyTblNoSync(a:int4, b:int4){
    index(0(a)),
    notify,
    data_sync_label(false)
}

%table inpStmg(a:int4, b:int4) {
    index(0(a)),
    update,
    status_merge_sub(true)
}
 
NotifyTblUpdate(a, b) :- inp(a, b).
NotifyTblNormal(a, b) :- inp(a, b).
NotifyTblNoSync(a, b) :- inp(a, b).

NotifyTblNormal(a, b) :- inpTransientFinish(a, b).
NotifyTblNormal(a, b) :- inpTransientTuple(a, b).
NotifyTblNormal(a, b) :- inpStmg(a, b).

namespace Ylog2 {
    %table inp(a:int4, b:int4) { update, index(0(a)) }
    %table out(a:int4, b:int4)
    %table mid(a:int4, b:int4)
    %table rsc(a1:int4, b1:int4) { update, index(0(a1, b1)) }
    
    Ylog2.out(a, b) :- Ylog2.rsc(a, b), Ylog2.mid(a, b).
    Ylog2.mid(a, b) :- Ylog2.inp(a, b).

    %table inp2(a:int4, b:int4, c:int4) { update_partial, index(0(a)) }
    %table out2(a:int4, b:int4, c:int4) { index(0(a)) }
    out2(a, b, c) :- inp2(a, b ,c).

    %table inp3(a:int4, b:int4) { transient(finish) }
    %table out3(a:int4, b:int4) { index(0(a)), update }
    out3(a, b) :- inp3(a, b).

    %table inp4(a:int4, b:int4) { transient(tuple) }
    %table out4(a:int4, b:int4) { index(0(a, b)) }
    out4(a, b) :- inp4(a, b).
}
