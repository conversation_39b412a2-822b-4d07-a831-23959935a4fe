/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: 009_graph_dml_index_get_key_count.cc
 * Description: Implementation of getting record count by index
 * Author: chenjunyu
 * Create: 2022/5/27
 */

#include "storage_st_common.h"
#include "st_common.h"

using namespace std;

const char *g_idxTestGetKeyCountConfigJson = R"({"max_record_count":400000})";
const char *g_idxTestGeyKeyCountLabelName = "T0";
const char *g_idxTestGetKeyCountPartitionLabelJson =
    R"([
        {"name":"T0",
         "type":"record",
         "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type": "partition", "nullable":false}
            ],
        "keys":[
            {"node":"T0", "name":"K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"T0", "name":"K1", "fields":["F0"], "index":{"type":"local"}, "constraints":{"unique":true}},
            {"node":"T0", "name":"K2", "fields":["F1"], "index":{"type":"local"}, "constraints":{"unique":false}},
            {"node":"T0", "name":"K3", "fields":["F0"], "index":{"type":"hashcluster"}, "constraints":{"unique":true}},
            {"node":"T0", "name":"K4", "fields":["F1"], "index":{"type":"hashcluster"}, "constraints":{"unique":false}},
            {"node":"T0", "name":"K5", "fields":["F0"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"T0", "name":"K6", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
            ]
        }])";

const int32_t IDX_GET_KEY_CNT_DATA_NUM = 1000;
const int32_t IDX_CHECK_DATA_NUM = 200;
const uint8_t IDX_GET_KEY_CNT_PARTITION_NUM = 16;

static Status IdxGetUniqueKeyCntStBeforeCheck(GmcStmtT *stmt, int32_t keyVal)
{
    uint64_t result;
    int indexValue = keyVal;
    // check PK
    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K0", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, result);
    GmcFreeIndexKey(stmt);

    // check local unique local
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K1", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, result);
    GmcFreeIndexKey(stmt);

    // hashcluster unique
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K3", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, result);
    GmcFreeIndexKey(stmt);

    // localhash unique
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K5", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, result);
    GmcFreeIndexKey(stmt);
    return ret;
}

static Status IdxGetNonUniqueKeyCntStBeforeCheck(GmcStmtT *stmt, int32_t keyVal)
{
    uint64_t result;
    int indexValue = keyVal;
    // check local non-unique
    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K2", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint64_t)IDX_GET_KEY_CNT_DATA_NUM, result);
    GmcFreeIndexKey(stmt);

    // hashcluster non-unique
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K4", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint64_t)IDX_GET_KEY_CNT_DATA_NUM, result);
    GmcFreeIndexKey(stmt);

    // localhash non-unique
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K6", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint64_t)IDX_GET_KEY_CNT_DATA_NUM, result);
    GmcFreeIndexKey(stmt);
    return ret;
}

static Status IdxGetKeyCntStAfterCheck(GmcStmtT *stmt, int32_t keyVal)
{
    uint64_t result;
    int indexValue = keyVal;
    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K0", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);
    GmcFreeIndexKey(stmt);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K1", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);
    GmcFreeIndexKey(stmt);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K2", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);
    GmcFreeIndexKey(stmt);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K3", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K4", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K5", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K6", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, result);
    return ret;
}

static Status StIdxInsert(GmcStmtT *stmt, uint8_t partition, int32_t i)
{
    int32_t pk = i + partition * IDX_GET_KEY_CNT_DATA_NUM;
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &pk, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = partition;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

static void IdxGetKeyCountPrepare(GmcStmtT *stmt)
{
    (void)GmcDropVertexLabel(stmt, g_idxTestGeyKeyCountLabelName);
    Status ret = GmcCreateVertexLabel(stmt, g_idxTestGetKeyCountPartitionLabelJson, g_idxTestGetKeyCountConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_idxTestGeyKeyCountLabelName, GMC_OPERATION_INSERT));

    // 数据分散在不同的分区中，每个分区IDX_GET_KEY_CNT_DATA_NUM条数据
    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        for (int32_t i = 0; i < IDX_GET_KEY_CNT_DATA_NUM; i++) {
            ret = StIdxInsert(stmt, partition, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 检查唯一索引数据
    ret = IdxGetUniqueKeyCntStBeforeCheck(stmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 检查非唯一索引在每个分区的数量
    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        ret = IdxGetNonUniqueKeyCntStBeforeCheck(stmt, (int32_t)partition);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

TEST_F(StStorage, index_graph_get_key_001)
{
    PERSISTENCE_NOT_SUPPORT;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    IdxGetKeyCountPrepare(stmt);
    Status ret;

    // 对每个分区进行对账
    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        ret = GmcBeginCheck(stmt, g_idxTestGeyKeyCountLabelName, partition);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, g_idxTestGeyKeyCountLabelName, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
        // 每对账完一个分区，检查当前分区数据量应为0，下一个分区数据量不变
        if (partition != IDX_GET_KEY_CNT_PARTITION_NUM - 1) {
            ret = IdxGetNonUniqueKeyCntStBeforeCheck(stmt, (int32_t)(partition + 1));
            ASSERT_EQ(GMERR_OK, ret);
            ret = IdxGetKeyCntStAfterCheck(stmt, (int32_t)partition);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    // 对账完全结束，数据均为0
    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        ret = IdxGetKeyCntStAfterCheck(stmt, (int32_t)partition);
        if (ret != GMERR_OK) {
            printf("%uth after check failed.\n", partition);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcResetStmt(stmt);
    ret = GmcDropVertexLabel(stmt, g_idxTestGeyKeyCountLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static Status IdxGetNonKeyCntStAfterCheck(GmcStmtT *stmt, int32_t keyVal, uint64_t expectCnt)
{
    uint64_t result;
    int indexValue = keyVal;

    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K2", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expectCnt, result);
    GmcFreeIndexKey(stmt);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K4", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expectCnt, result);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_idxTestGeyKeyCountLabelName, "K6", &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expectCnt, result);
    return ret;
}

TEST_F(StStorage, index_graph_get_key_002)
{
    PERSISTENCE_NOT_SUPPORT;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    IdxGetKeyCountPrepare(stmt);
    Status ret;

    // 对每个分区进行对账
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_idxTestGeyKeyCountLabelName, GMC_OPERATION_REPLACE));
    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        ret = GmcBeginCheck(stmt, g_idxTestGeyKeyCountLabelName, partition);
        ASSERT_EQ(GMERR_OK, ret);
        // replace aged data
        for (int32_t i = 0; i < IDX_CHECK_DATA_NUM; i++) {
            ret = StIdxInsert(stmt, partition, i);
            ASSERT_EQ(GMERR_OK, ret);
        }

        ret = GmcEndCheck(stmt, g_idxTestGeyKeyCountLabelName, partition, false);
        ASSERT_EQ(GMERR_OK, ret);

        // 每对账完一个分区，检查当前分区数据量应为0，下一个分区数据量不变
        if (partition != IDX_GET_KEY_CNT_PARTITION_NUM - 1) {
            ret = IdxGetNonUniqueKeyCntStBeforeCheck(stmt, (int32_t)(partition + 1));
            ASSERT_EQ(GMERR_OK, ret);
            ret = IdxGetNonKeyCntStAfterCheck(stmt, (int32_t)partition, IDX_CHECK_DATA_NUM);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        ret = GmcBeginCheck(stmt, g_idxTestGeyKeyCountLabelName, partition);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, g_idxTestGeyKeyCountLabelName, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 对账完全结束，数据均为0
    for (uint8_t partition = 0; partition < IDX_GET_KEY_CNT_PARTITION_NUM; partition++) {
        ret = IdxGetKeyCntStAfterCheck(stmt, (int32_t)partition);
        if (ret != GMERR_OK) {
            printf("%uth after check failed.\n", partition);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcResetStmt(stmt);
    ret = GmcDropVertexLabel(stmt, g_idxTestGeyKeyCountLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}
