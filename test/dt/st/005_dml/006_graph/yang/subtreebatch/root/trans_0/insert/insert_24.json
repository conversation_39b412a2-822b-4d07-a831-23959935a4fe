{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4018", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4018000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4018001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4018002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4018003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4018004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4018005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4018006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4018007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4018008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4018009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4018010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4018011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4018012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4018013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4018014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4018015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4018016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4018017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4018018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4018019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4018020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4018021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4018022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4018023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4018024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4018025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4018026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4018027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4018028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4018029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4018030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4018031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4018032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4018033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4018034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4018035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4018036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4018037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4018038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4018039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4018040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4018041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4018042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4018043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4018044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4018045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4018046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4018047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4018048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4018049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4018050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4018051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4018052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4018053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4018054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4018055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4018056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4018057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4018058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4018059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4018060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4018061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4018062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4018063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4018064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4018065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4018066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4018067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4018068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4018069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4018070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4018071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4018072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4018073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4018074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4018075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4018076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4018077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4018078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4018079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4018080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4018081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4018082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4018083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4018084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4018085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4018086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4018087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4018088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4018089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4018090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4018091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4018092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4018093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4018094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4018095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4018096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4018097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4018098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4018099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4018100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4018101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4018102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4018103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4018104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4018105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4018106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4018107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4018108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4018109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4018110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4018111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4018112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4018113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4018114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4018115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4018116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4018117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4018118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4018119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4018120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4018121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4018122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4018123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4018124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4018125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4018126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4018127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6018", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6018000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6018001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6018002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6018003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6018004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6018005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6018006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6018007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6018008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6018009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6018010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6018011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6018012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6018013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6018014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6018015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6018016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6018017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6018018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6018019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6018020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6018021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6018022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6018023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6018024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6018025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6018026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6018027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6018028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6018029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6018030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6018031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6018032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6018033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6018034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6018035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6018036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6018037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6018038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6018039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6018040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6018041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6018042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6018043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6018044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6018045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6018046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6018047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6018048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6018049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6018050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6018051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6018052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6018053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6018054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6018055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6018056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6018057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6018058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6018059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6018060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6018061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6018062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6018063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6018064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6018065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6018066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6018067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6018068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6018069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6018070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6018071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6018072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6018073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6018074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6018075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6018076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6018077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6018078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6018079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6018080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6018081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6018082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6018083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6018084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6018085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6018086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6018087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6018088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6018089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6018090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6018091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6018092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6018093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6018094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6018095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6018096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6018097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6018098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6018099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6018100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6018101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6018102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6018103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6018104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6018105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6018106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6018107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6018108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6018109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6018110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6018111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6018112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6018113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6018114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6018115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6018116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6018117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6018118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6018119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6018120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6018121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6018122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6018123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6018124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6018125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6018126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6018127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3019", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3019000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3019001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3019002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3019003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3019004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3019005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3019006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3019007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3019008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3019009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3019010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3019011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3019012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3019013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3019014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3019015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3019016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3019017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3019018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3019019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3019020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3019021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3019022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3019023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3019024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3019025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3019026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3019027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3019028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3019029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3019030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3019031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3019032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3019033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3019034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3019035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3019036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3019037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3019038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3019039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3019040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3019041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3019042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3019043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3019044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3019045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3019046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3019047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3019048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3019049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3019050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3019051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3019052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3019053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3019054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3019055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3019056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3019057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3019058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3019059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3019060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3019061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3019062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3019063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3019064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3019065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3019066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3019067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3019068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3019069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3019070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3019071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3019072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3019073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3019074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3019075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3019076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3019077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3019078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3019079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3019080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3019081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3019082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3019083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3019084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3019085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3019086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3019087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3019088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3019089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3019090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3019091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3019092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3019093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3019094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3019095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3019096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3019097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3019098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3019099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3019100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3019101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3019102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3019103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3019104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3019105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3019106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3019107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3019108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3019109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3019110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3019111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3019112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3019113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3019114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3019115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3019116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3019117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3019118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3019119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3019120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3019121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3019122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3019123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3019124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3019125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3019126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3019127, "action": "permit", "protocol": 0}]}}]}}}]}]}