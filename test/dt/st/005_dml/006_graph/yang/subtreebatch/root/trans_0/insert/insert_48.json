{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4036", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4036000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4036001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4036002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4036003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4036004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4036005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4036006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4036007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4036008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4036009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4036010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4036011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4036012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4036013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4036014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4036015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4036016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4036017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4036018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4036019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4036020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4036021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4036022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4036023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4036024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4036025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4036026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4036027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4036028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4036029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4036030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4036031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4036032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4036033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4036034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4036035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4036036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4036037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4036038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4036039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4036040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4036041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4036042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4036043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4036044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4036045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4036046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4036047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4036048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4036049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4036050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4036051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4036052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4036053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4036054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4036055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4036056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4036057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4036058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4036059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4036060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4036061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4036062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4036063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4036064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4036065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4036066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4036067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4036068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4036069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4036070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4036071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4036072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4036073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4036074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4036075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4036076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4036077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4036078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4036079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4036080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4036081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4036082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4036083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4036084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4036085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4036086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4036087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4036088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4036089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4036090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4036091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4036092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4036093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4036094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4036095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4036096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4036097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4036098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4036099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4036100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4036101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4036102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4036103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4036104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4036105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4036106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4036107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4036108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4036109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4036110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4036111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4036112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4036113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4036114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4036115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4036116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4036117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4036118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4036119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4036120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4036121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4036122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4036123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4036124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4036125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4036126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4036127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6036", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6036000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6036001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6036002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6036003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6036004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6036005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6036006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6036007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6036008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6036009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6036010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6036011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6036012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6036013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6036014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6036015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6036016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6036017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6036018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6036019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6036020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6036021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6036022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6036023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6036024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6036025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6036026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6036027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6036028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6036029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6036030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6036031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6036032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6036033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6036034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6036035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6036036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6036037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6036038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6036039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6036040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6036041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6036042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6036043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6036044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6036045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6036046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6036047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6036048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6036049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6036050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6036051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6036052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6036053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6036054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6036055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6036056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6036057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6036058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6036059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6036060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6036061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6036062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6036063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6036064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6036065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6036066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6036067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6036068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6036069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6036070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6036071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6036072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6036073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6036074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6036075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6036076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6036077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6036078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6036079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6036080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6036081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6036082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6036083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6036084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6036085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6036086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6036087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6036088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6036089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6036090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6036091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6036092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6036093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6036094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6036095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6036096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6036097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6036098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6036099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6036100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6036101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6036102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6036103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6036104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6036105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6036106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6036107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6036108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6036109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6036110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6036111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6036112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6036113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6036114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6036115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6036116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6036117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6036118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6036119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6036120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6036121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6036122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6036123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6036124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6036125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6036126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6036127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3037", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3037000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3037001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3037002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3037003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3037004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3037005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3037006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3037007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3037008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3037009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3037010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3037011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3037012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3037013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3037014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3037015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3037016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3037017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3037018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3037019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3037020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3037021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3037022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3037023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3037024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3037025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3037026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3037027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3037028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3037029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3037030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3037031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3037032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3037033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3037034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3037035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3037036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3037037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3037038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3037039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3037040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3037041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3037042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3037043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3037044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3037045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3037046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3037047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3037048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3037049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3037050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3037051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3037052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3037053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3037054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3037055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3037056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3037057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3037058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3037059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3037060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3037061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3037062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3037063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3037064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3037065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3037066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3037067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3037068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3037069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3037070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3037071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3037072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3037073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3037074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3037075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3037076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3037077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3037078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3037079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3037080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3037081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3037082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3037083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3037084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3037085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3037086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3037087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3037088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3037089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3037090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3037091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3037092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3037093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3037094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3037095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3037096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3037097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3037098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3037099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3037100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3037101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3037102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3037103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3037104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3037105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3037106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3037107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3037108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3037109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3037110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3037111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3037112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3037113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3037114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3037115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3037116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3037117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3037118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3037119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3037120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3037121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3037122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3037123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3037124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3037125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3037126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3037127, "action": "permit", "protocol": 0}]}}]}}}]}]}