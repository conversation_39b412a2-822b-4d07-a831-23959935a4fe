[{"name": "ds", "source_vertex_label": "yang", "dest_vertex_label": "ds", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-haca", "source_vertex_label": "ds", "dest_vertex_label": "huawei-aaa-haca", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-haca:aaa-haca::server-templates::server-template", "source_vertex_label": "huawei-aaa-haca", "dest_vertex_label": "huawei-aaa-haca:aaa-haca::server-templates::server-template", "source_node_path": "/huawei-aaa-haca:aaa-haca/server-templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-haca:aaa-haca::server-templates::server-template::server::server-type::ip-type::server-ips::server-ip", "source_vertex_label": "huawei-aaa-haca:aaa-haca::server-templates::server-template", "dest_vertex_label": "huawei-aaa-haca:aaa-haca::server-templates::server-template::server::server-type::ip-type::server-ips::server-ip", "source_node_path": "/server/server-type/ip-type/server-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-haca:aaa-haca::act-stop-pkt-qrys::act-stop-pkt-qry", "source_vertex_label": "huawei-aaa-haca", "dest_vertex_label": "huawei-aaa-haca:aaa-haca::act-stop-pkt-qrys::act-stop-pkt-qry", "source_node_path": "/huawei-aaa-haca:aaa-haca/act-stop-pkt-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa", "source_vertex_label": "ds", "dest_vertex_label": "huawei-aaa", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::access-codes::access-code", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::access-codes::access-code", "source_node_path": "/huawei-aaa:aaa/access-codes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::authentication-schemes::authentication-scheme", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::authentication-schemes::authentication-scheme", "source_node_path": "/huawei-aaa:aaa/authentication-schemes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::authentication-schemes::authentication-scheme::authen-mode", "source_vertex_label": "huawei-aaa:aaa::authentication-schemes::authentication-scheme", "dest_vertex_label": "huawei-aaa:aaa::authentication-schemes::authentication-scheme::authen-mode", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::authentication-schemes::authentication-scheme::radius-chap::access-type", "source_vertex_label": "huawei-aaa:aaa::authentication-schemes::authentication-scheme", "dest_vertex_label": "huawei-aaa:aaa::authentication-schemes::authentication-scheme::radius-chap::access-type", "source_node_path": "/radius-chap", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::authorization-schemes::authorization-scheme", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::authorization-schemes::authorization-scheme", "source_node_path": "/huawei-aaa:aaa/authorization-schemes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::authorization-schemes::authorization-scheme::author-mode", "source_vertex_label": "huawei-aaa:aaa::authorization-schemes::authorization-scheme", "dest_vertex_label": "huawei-aaa:aaa::authorization-schemes::authorization-scheme::author-mode", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::accounting-schemes::accounting-scheme", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::accounting-schemes::accounting-scheme", "source_node_path": "/huawei-aaa:aaa/accounting-schemes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::domains::domain", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::domains::domain", "source_node_path": "/huawei-aaa:aaa/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_node_path": "/huawei-aaa:aaa/alive-user-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::management-user-qrys::management-user-qry", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::management-user-qrys::management-user-qry", "source_node_path": "/huawei-aaa:aaa/management-user-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::management-user-qrys::management-user-qry::user-infos::user-info", "source_vertex_label": "huawei-aaa:aaa::management-user-qrys::management-user-qry", "dest_vertex_label": "huawei-aaa:aaa::management-user-qrys::management-user-qry::user-infos::user-info", "source_node_path": "/user-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::management-user-qrys::management-user-qry::last-login-infos::last-login-info", "source_vertex_label": "huawei-aaa:aaa::management-user-qrys::management-user-qry", "dest_vertex_label": "huawei-aaa:aaa::management-user-qrys::management-user-qry::last-login-infos::last-login-info", "source_node_path": "/last-login-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::lam::users::user", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::lam::users::user", "source_node_path": "/huawei-aaa:aaa/lam/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::lam::access-users::access-user", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::lam::access-users::access-user", "source_node_path": "/huawei-aaa:aaa/lam/access-users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::access-user-qrys::access-user-qry", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::access-user-qrys::access-user-qry", "source_node_path": "/huawei-aaa:aaa/access-user-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::access-user-records::access-user-record", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::access-user-records::access-user-record", "source_node_path": "/huawei-aaa:aaa/access-user-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-service", "source_vertex_label": "ds", "dest_vertex_label": "huawei-aaa-service", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-service:aaa-service::service-schemes::service-scheme", "source_vertex_label": "huawei-aaa-service", "dest_vertex_label": "huawei-aaa-service:aaa-service::service-schemes::service-scheme", "source_node_path": "/huawei-aaa-service:aaa-service/service-schemes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-service:aaa-service::service-schemes::service-scheme::acl", "source_vertex_label": "huawei-aaa-service:aaa-service::service-schemes::service-scheme", "dest_vertex_label": "huawei-aaa-service:aaa-service::service-schemes::service-scheme::acl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa-service:aaa-service::service-schemes::service-scheme::acl-ipv6", "source_vertex_label": "huawei-aaa-service:aaa-service::service-schemes::service-scheme", "dest_vertex_label": "huawei-aaa-service:aaa-service::service-schemes::service-scheme::acl-ipv6", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-hwtacacs", "source_vertex_label": "ds", "dest_vertex_label": "huawei-hwtacacs", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-hwtacacs:hwtacacs::templates::template", "source_vertex_label": "huawei-hwtacacs", "dest_vertex_label": "huawei-hwtacacs:hwtacacs::templates::template", "source_node_path": "/huawei-hwtacacs:hwtacacs/templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-hwtacacs:hwtacacs::templates::template::server-hosts::server-host", "source_vertex_label": "huawei-hwtacacs:hwtacacs::templates::template", "dest_vertex_label": "huawei-hwtacacs:hwtacacs::templates::template::server-hosts::server-host", "source_node_path": "/server-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-hwtacacs:hwtacacs::templates::template::server-ipv6-hosts::server-ipv6-host", "source_vertex_label": "huawei-hwtacacs:hwtacacs::templates::template", "dest_vertex_label": "huawei-hwtacacs:hwtacacs::templates::template::server-ipv6-hosts::server-ipv6-host", "source_node_path": "/server-ipv6-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance", "source_vertex_label": "ds", "dest_vertex_label": "huawei-network-instance", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance", "source_vertex_label": "huawei-network-instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance", "source_node_path": "/huawei-network-instance:network-instance/instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "source_node_path": "/huawei-l3vpn:afs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "source_node_path": "/huawei-routing:routing/routing-manage/topologys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_node_path": "/routes/ipv4-unicast-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv6-unicast-routes::ipv6-unicast-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv6-unicast-routes::ipv6-unicast-route", "source_node_path": "/routes/ipv6-unicast-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_node_path": "/routes/ipv4-route-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv6-route-statistics::ipv6-route-statistic", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv6-route-statistics::ipv6-route-statistic", "source_node_path": "/routes/ipv6-route-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_node_path": "/huawei-routing:routing/static-routing/unicast-route2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_node_path": "/nexthop-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_node_path": "/nexthop-interface-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_node_path": "/nexthop-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_node_path": "/huawei-routing:routing/static-routing/ipv4-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv6-routes::ipv6-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv6-routes::ipv6-route", "source_node_path": "/huawei-routing:routing/static-routing/ipv6-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-routing", "source_vertex_label": "ds", "dest_vertex_label": "huawei-routing", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ifm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface", "source_vertex_label": "huawei-ifm", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface", "source_node_path": "/huawei-ifm:ifm/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:dhcp-client-if::request-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:dhcp-client-if::request-option", "source_node_path": "/huawei-dhcp:dhcp-client-if", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/dns-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/excluded-ip-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/static-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_node_path": "/option-format/ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_node_path": "/option-format/sub-options-format/sub-options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_node_path": "/option-format/sub-ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-if::request-options::request-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-if::request-options::request-option", "source_node_path": "/huawei-dhcpv6:dhcpv6-client-if/request-options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::address", "source_node_path": "/huawei-dhcpv6:dhcpv6-client-query", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::dns-server-ip", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::dns-server-ip", "source_node_path": "/huawei-dhcpv6:dhcpv6-client-query", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::request-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::request-option", "source_node_path": "/huawei-dhcpv6:dhcpv6-client-query", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::dns-domains", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcpv6:dhcpv6-client-query::dns-domains", "source_node_path": "/huawei-dhcpv6:dhcpv6-client-query", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_node_path": "/huawei-ethernet:ethernet/main-interface/port-isolate-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ifm-trunk:trunk::members::member", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ifm-trunk:trunk::members::member", "source_node_path": "/huawei-ifm-trunk:trunk/members", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "source_node_path": "/huawei-ip:ipv4/address/common-address/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "source_node_path": "/huawei-ip:ipv4/state/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "source_node_path": "/huawei-ip:ipv4/huawei-arp:static-arps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::address::common-address::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::address::common-address::addresses::address", "source_node_path": "/huawei-ip:ipv6/address/common-address/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::state::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::state::addresses::address", "source_node_path": "/huawei-ip:ipv6/state/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::huawei-ipv6-nd:nd-collection::static-neighbor::common-ifs::common-if", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::huawei-ipv6-nd:nd-collection::static-neighbor::common-ifs::common-if", "source_node_path": "/huawei-ip:ipv6/huawei-ipv6-nd:nd-collection/static-neighbor/common-ifs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::huawei-ipv6-nd:nd-collection::static-neighbor::vlan-ifs::vlan-if", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv6::huawei-ipv6-nd:nd-collection::static-neighbor::vlan-ifs::vlan-if", "source_node_path": "/huawei-ip:ipv6/huawei-ipv6-nd:nd-collection/static-neighbor/vlan-ifs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "source_node_path": "/huawei-lldp:lldp/session/neighbors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_node_path": "/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_node_path": "/protocol-vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_node_path": "/vlan-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_node_path": "/unknown-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_node_path": "/unknown-organizationally-defined-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_node_path": "/med-tlv/capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_node_path": "/med-tlv/network-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_node_path": "/legacy-power-capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-clients::pppoe-client", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-clients::pppoe-client", "source_node_path": "/huawei-pppoe-client:pppoe-clients", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "source_node_path": "/huawei-pppoe-client:pppoe-client-session-summarys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-qos:qos::car-templates::car-template", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-qos:qos::car-templates::car-template", "source_node_path": "/huawei-qos:qos/car-templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_node_path": "/huawei-sacl:traffic-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply", "source_node_path": "/huawei-sacl:traffic-ipv6-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-filter-applys::traffic-ipv6-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_node_path": "/huawei-sacl:traffic-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply", "source_node_path": "/huawei-sacl:traffic-ipv6-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-ipv6-remark-applys::traffic-ipv6-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-arp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::query-entries::query-entry", "source_vertex_label": "huawei-arp", "dest_vertex_label": "huawei-arp:arp::query-entries::query-entry", "source_node_path": "/huawei-arp:arp/query-entries", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::statistics::statistic", "source_vertex_label": "huawei-arp", "dest_vertex_label": "huawei-arp:arp::statistics::statistic", "source_node_path": "/huawei-arp:arp/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dhcp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "source_node_path": "/huawei-dhcp:dhcp/server/global-ip-pools", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::logging", "source_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "dest_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::logging", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::nbns-list", "source_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "dest_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::nbns-list", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::gateway-lists::gateway-list", "source_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "dest_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::gateway-lists::gateway-list", "source_node_path": "/gateway-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::dns-lists::dns-list", "source_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "dest_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::dns-lists::dns-list", "source_node_path": "/dns-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::static-binds::static-bind", "source_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool", "dest_vertex_label": "huawei-dhcp:dhcp::server::global-ip-pools::global-ip-pool::static-binds::static-bind", "source_node_path": "/static-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_node_path": "/huawei-dhcp:dhcp/server/ip-pool-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::snooping::dynamic-binds::dynamic-bind", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::snooping::dynamic-binds::dynamic-bind", "source_node_path": "/huawei-dhcp:dhcp/snooping/dynamic-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::client::client-querys::client-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::client::client-querys::client-query", "source_node_path": "/huawei-dhcp:dhcp/client/client-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::client::client-dns-server-querys::client-dns-server-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::client::client-dns-server-querys::client-dns-server-query", "source_node_path": "/huawei-dhcp:dhcp/client/client-dns-server-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcpv6", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dhcpv6", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcpv6:dhcpv6::snooping::dynamic-binds::dynamic-bind", "source_vertex_label": "huawei-dhcpv6", "dest_vertex_label": "huawei-dhcpv6:dhcpv6::snooping::dynamic-binds::dynamic-bind", "source_node_path": "/huawei-dhcpv6:dhcpv6/snooping/dynamic-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-igmp-mld-snooping", "source_vertex_label": "ds", "dest_vertex_label": "huawei-igmp-mld-snooping", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-igmp-mld-snooping:igmp-mld-snooping::igmp-snooping::group-bandwidths::group-bandwidth", "source_vertex_label": "huawei-igmp-mld-snooping", "dest_vertex_label": "huawei-igmp-mld-snooping:igmp-mld-snooping::igmp-snooping::group-bandwidths::group-bandwidth", "source_node_path": "/huawei-igmp-mld-snooping:igmp-mld-snooping/igmp-snooping/group-bandwidths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-mstp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "source_vertex_label": "huawei-mstp", "dest_vertex_label": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "source_node_path": "/huawei-mstp:mstp/default-process/cist-info/cist-port-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "source_vertex_label": "huawei-mstp", "dest_vertex_label": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "source_node_path": "/huawei-mstp:mstp/default-process/interface-bpdu-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm-trunk", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ifm-trunk", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ipv6-nd", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ipv6-nd", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ipv6-nd:ipv6-nd::ipv6-neighbors::ipv6-neighbor", "source_vertex_label": "huawei-ipv6-nd", "dest_vertex_label": "huawei-ipv6-nd:ipv6-nd::ipv6-neighbors::ipv6-neighbor", "source_node_path": "/huawei-ipv6-nd:ipv6-nd/ipv6-neighbors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-lldp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_node_path": "/huawei-lldp:lldp/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_node_path": "/huawei-lldp:lldp/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_node_path": "/huawei-lldp:lldp/local-info/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nd-snooping", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nd-snooping", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pppoe-client", "source_vertex_label": "ds", "dest_vertex_label": "huawei-pppoe-client", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos", "source_vertex_label": "ds", "dest_vertex_label": "huawei-qos", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::car-template::templates::template", "source_vertex_label": "huawei-qos", "dest_vertex_label": "huawei-qos:qos::car-template::templates::template", "source_node_path": "/huawei-qos:qos/car-template/templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::integration-template::profiles::profile", "source_vertex_label": "huawei-qos", "dest_vertex_label": "huawei-qos:qos::integration-template::profiles::profile", "source_node_path": "/huawei-qos:qos/integration-template/profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::integration-template::profiles::profile::cars::car", "source_vertex_label": "huawei-qos:qos::integration-template::profiles::profile", "dest_vertex_label": "huawei-qos:qos::integration-template::profiles::profile::cars::car", "source_node_path": "/cars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::integration-template::profiles::profile::remark-dscps::remark-dscp", "source_vertex_label": "huawei-qos:qos::integration-template::profiles::profile", "dest_vertex_label": "huawei-qos:qos::integration-template::profiles::profile::remark-dscps::remark-dscp", "source_node_path": "/remark-dscps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-qos:qos::integration-template::profiles::profile::remark-8021ps::remark-8021p", "source_vertex_label": "huawei-qos:qos::integration-template::profiles::profile", "dest_vertex_label": "huawei-qos:qos::integration-template::profiles::profile::remark-8021ps::remark-8021p", "source_node_path": "/remark-8021ps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sacl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius", "source_vertex_label": "ds", "dest_vertex_label": "huawei-radius", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group", "source_vertex_label": "huawei-radius", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "source_node_path": "/huawei-radius:radius/radius-server-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::radius-server-ipv4s::radius-server-ipv4", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::radius-server-ipv4s::radius-server-ipv4", "source_node_path": "/radius-server-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::radius-server-ipv6s::radius-server-ipv6", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::radius-server-ipv6s::radius-server-ipv6", "source_node_path": "/radius-server-ipv6s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute", "source_node_path": "/standard-attributes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute::translate-attribute::translate-packet::type", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute::translate-attribute::translate-packet::type", "source_node_path": "/translate-attribute/translate-packet", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute::translate-attribute::translate-direction::type", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute::translate-attribute::translate-direction::type", "source_node_path": "/translate-attribute/translate-direction", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute::disable-attribute::disable-direction::type", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::standard-attributes::standard-attribute::disable-attribute::disable-direction::type", "source_node_path": "/disable-attribute/disable-direction", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::modify-attributes::modify-attribute", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::modify-attributes::modify-attribute", "source_node_path": "/modify-attributes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extends::translate-extend", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extends::translate-extend", "source_node_path": "/translate-extends", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extends::translate-extend::packet-type", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extends::translate-extend", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extends::translate-extend::packet-type", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extend-vendors::translate-extend-vendor", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extend-vendors::translate-extend-vendor", "source_node_path": "/translate-extend-vendors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extend-vendors::translate-extend-vendor::packet-type", "source_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extend-vendors::translate-extend-vendor", "dest_vertex_label": "huawei-radius:radius::radius-server-groups::radius-server-group::translate-extend-vendors::translate-extend-vendor::packet-type", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-local-ips::radius-local-ip", "source_vertex_label": "huawei-radius", "dest_vertex_label": "huawei-radius:radius::radius-local-ips::radius-local-ip", "source_node_path": "/huawei-radius:radius/radius-local-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::radius-local-ipv6s::radius-local-ipv6", "source_vertex_label": "huawei-radius", "dest_vertex_label": "huawei-radius:radius::radius-local-ipv6s::radius-local-ipv6", "source_node_path": "/huawei-radius:radius/radius-local-ipv6s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::dynamic-authorization-servers::dynamic-authorization-server", "source_vertex_label": "huawei-radius", "dest_vertex_label": "huawei-radius:radius::dynamic-authorization-servers::dynamic-authorization-server", "source_node_path": "/huawei-radius:radius/dynamic-authorization-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-radius:radius::dynamic-authorization-ipv6-servers::dynamic-authorization-ipv6-server", "source_vertex_label": "huawei-radius", "dest_vertex_label": "huawei-radius:radius::dynamic-authorization-ipv6-servers::dynamic-authorization-ipv6-server", "source_node_path": "/huawei-radius:radius/dynamic-authorization-ipv6-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ssl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_vertex_label": "huawei-ssl", "dest_vertex_label": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_node_path": "/huawei-ssl:ssl/ssl-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::ssl-cipher-suites::ssl-cipher-suite", "source_vertex_label": "huawei-ssl", "dest_vertex_label": "huawei-ssl:ssl::ssl-cipher-suites::ssl-cipher-suite", "source_node_path": "/huawei-ssl:ssl/ssl-cipher-suites", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::ssl-cipher-suites::ssl-cipher-suite::ssl-ciphers", "source_vertex_label": "huawei-ssl:ssl::ssl-cipher-suites::ssl-cipher-suite", "dest_vertex_label": "huawei-ssl:ssl::ssl-cipher-suites::ssl-cipher-suite::ssl-ciphers", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::dtls-policys::dtls-policy", "source_vertex_label": "huawei-ssl", "dest_vertex_label": "huawei-ssl:ssl::dtls-policys::dtls-policy", "source_node_path": "/huawei-ssl:ssl/dtls-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::dtls-policys::dtls-policy::feature-name", "source_vertex_label": "huawei-ssl:ssl::dtls-policys::dtls-policy", "dest_vertex_label": "huawei-ssl:ssl::dtls-policys::dtls-policy::feature-name", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::dtls-policys::dtls-policy::signature-algorithms", "source_vertex_label": "huawei-ssl:ssl::dtls-policys::dtls-policy", "dest_vertex_label": "huawei-ssl:ssl::dtls-policys::dtls-policy::signature-algorithms", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-acl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group", "source_vertex_label": "huawei-acl", "dest_vertex_label": "huawei-acl:acl::groups::group", "source_node_path": "/huawei-acl:acl/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_node_path": "/rule-basics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_node_path": "/rule-advances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_node_path": "/rule-ethernets", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::huawei-acl-ucl:rule-ucls::rule-ucl", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::huawei-acl-ucl:rule-ucls::rule-ucl", "source_node_path": "/huawei-acl-ucl:rule-ucls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::group6s::group6", "source_vertex_label": "huawei-acl", "dest_vertex_label": "huawei-acl:acl::group6s::group6", "source_node_path": "/huawei-acl:acl/group6s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::group6s::group6::rule-advances::rule-advance", "source_vertex_label": "huawei-acl:acl::group6s::group6", "dest_vertex_label": "huawei-acl:acl::group6s::group6::rule-advances::rule-advance", "source_node_path": "/rule-advances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::group6s::group6::huawei-acl-ucl:rule-ucls::rule-ucl", "source_vertex_label": "huawei-acl:acl::group6s::group6", "dest_vertex_label": "huawei-acl:acl::group6s::group6::huawei-acl-ucl:rule-ucls::rule-ucl", "source_node_path": "/huawei-acl-ucl:rule-ucls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range", "source_vertex_label": "ds", "dest_vertex_label": "huawei-time-range", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_vertex_label": "huawei-time-range", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_node_path": "/huawei-time-range:time-range/time-range-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_node_path": "/absolute-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_node_path": "/period-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-group", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nac-group", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-group:nac-group::ucl-groups::ucl-group", "source_vertex_label": "huawei-nac-group", "dest_vertex_label": "huawei-nac-group:nac-group::ucl-groups::ucl-group", "source_node_path": "/huawei-nac-group:nac-group/ucl-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-group:nac-group::ucl-groups::ucl-group::ips::ip", "source_vertex_label": "huawei-nac-group:nac-group::ucl-groups::ucl-group", "dest_vertex_label": "huawei-nac-group:nac-group::ucl-groups::ucl-group::ips::ip", "source_node_path": "/ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-vlan", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-vlan", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan", "source_node_path": "/huawei-vlan:vlan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_node_path": "/member-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/interface-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group", "source_node_path": "/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group::sources::source", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::interface-groups::interface-group::groups::group::sources::source", "source_node_path": "/sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user", "source_node_path": "/huawei-igmp-mld-snooping:igmp-snooping/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user::groups::group", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user::groups::group", "source_node_path": "/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user::groups::group::sources::source", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user::groups::group", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-igmp-mld-snooping:igmp-snooping::users::user::groups::group::sources::source", "source_node_path": "/sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "source_node_path": "/huawei-mac:mac-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::huawei-vlan-pool:vlan-pools::vlan-pool", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "huawei-vlan:vlan::huawei-vlan-pool:vlan-pools::vlan-pool", "source_node_path": "/huawei-vlan:vlan/huawei-vlan-pool:vlan-pools", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-mac", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_node_path": "/huawei-mac:mac/vlan-dynamic-macs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::mac-statistics::mac-statistic", "source_node_path": "/huawei-mac:mac/mac-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-wlan-defence-profile", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-wlan-defence-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-defence-profile:wlan-defence-profile::profiles::profile", "source_vertex_label": "hua<PERSON>-wlan-defence-profile", "dest_vertex_label": "huawei-wlan-defence-profile:wlan-defence-profile::profiles::profile", "source_node_path": "/huawei-wlan-defence-profile:wlan-defence-profile/profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-vap-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-vap-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "source_vertex_label": "huawei-wlan-vap-profile", "dest_vertex_label": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "source_node_path": "/huawei-wlan-vap-profile:wlan-vap-profile/vap-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile::ap-zone::radio", "source_vertex_label": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "dest_vertex_label": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile::ap-zone::radio", "source_node_path": "/ap-zone", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-hotspot", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "source_vertex_label": "huawei-wlan-hotspot", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "source_node_path": "/huawei-wlan-hotspot:wlan-hotspot/hotspot2-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::domain-names::domain-name", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::domain-names::domain-name", "source_node_path": "/domain-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::nai-realms::nai-realm", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::nai-realms::nai-realm", "source_node_path": "/nai-realms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::operating-class-indications::operating-class-indication", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::operating-class-indications::operating-class-indication", "source_node_path": "/operating-class-indications", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::operator-friendly-names::operator-friendly-name", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::operator-friendly-names::operator-friendly-name", "source_node_path": "/operator-friendly-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::plmn-ids::plmn-id", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::plmn-ids::plmn-id", "source_node_path": "/plmn-ids", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::roaming-consortium-ois::roaming-consortium-oi", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::roaming-consortium-ois::roaming-consortium-oi", "source_node_path": "/roaming-consortium-ois", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::venue-names::venue-name", "source_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile", "dest_vertex_label": "huawei-wlan-hotspot:wlan-hotspot::hotspot2-profiles::hotspot2-profile::venue-names::venue-name", "source_node_path": "/venue-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sac", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-sac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sac:wlan-sac::sac-profiles::sac-profile", "source_vertex_label": "huawei-wlan-sac", "dest_vertex_label": "huawei-wlan-sac:wlan-sac::sac-profiles::sac-profile", "source_node_path": "/huawei-wlan-sac:wlan-sac/sac-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sac:wlan-sac::sac-profiles::sac-profile::policys::policy", "source_vertex_label": "huawei-wlan-sac:wlan-sac::sac-profiles::sac-profile", "dest_vertex_label": "huawei-wlan-sac:wlan-sac::sac-profiles::sac-profile::policys::policy", "source_node_path": "/policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-wlan-security-profile", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-wlan-security-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile", "source_vertex_label": "hua<PERSON>-wlan-security-profile", "dest_vertex_label": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile", "source_node_path": "/huawei-wlan-security-profile:wlan-security-profile/security-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ssid-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ssid-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile", "source_vertex_label": "huawei-wlan-ssid-profile", "dest_vertex_label": "huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile", "source_node_path": "/huawei-wlan-ssid-profile:wlan-ssid-profile/ssid-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-sta-access", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list", "source_vertex_label": "huawei-wlan-sta-access", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list", "source_node_path": "/huawei-wlan-sta-access:wlan-sta-access/sta-whitelist-profiles/profile-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list::sta-mac-lists::sta-mac-list", "source_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list::sta-mac-lists::sta-mac-list", "source_node_path": "/sta-mac-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list::sta-oui-lists::sta-oui-list", "source_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list::sta-oui-lists::sta-oui-list", "source_node_path": "/sta-oui-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list::sta-device-id-lists::sta-device-id-list", "source_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-whitelist-profiles::profile-lists::profile-list::sta-device-id-lists::sta-device-id-list", "source_node_path": "/sta-device-id-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list", "source_vertex_label": "huawei-wlan-sta-access", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list", "source_node_path": "/huawei-wlan-sta-access:wlan-sta-access/sta-blacklist-profiles/profile-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list::sta-mac-lists::sta-mac-list", "source_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list::sta-mac-lists::sta-mac-list", "source_node_path": "/sta-mac-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list::sta-device-id-lists::sta-device-id-list", "source_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::sta-blacklist-profiles::profile-lists::profile-list::sta-device-id-lists::sta-device-id-list", "source_node_path": "/sta-device-id-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta-access:wlan-sta-access::dynamic-blacklists::dynamic-blacklist", "source_vertex_label": "huawei-wlan-sta-access", "dest_vertex_label": "huawei-wlan-sta-access:wlan-sta-access::dynamic-blacklists::dynamic-blacklist", "source_node_path": "/huawei-wlan-sta-access:wlan-sta-access/dynamic-blacklists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-traffic-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "source_vertex_label": "huawei-wlan-traffic-profile", "dest_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "source_node_path": "/huawei-wlan-traffic-profile:wlan-traffic-profile/traffic-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-up-rate-limits::time-range-up-rate-limit", "source_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "dest_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-up-rate-limits::time-range-up-rate-limit", "source_node_path": "/time-range-up-rate-limits", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-down-rate-limits::time-range-down-rate-limit", "source_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "dest_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-down-rate-limits::time-range-down-rate-limit", "source_node_path": "/time-range-down-rate-limits", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-remarks::traffic-remark", "source_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "dest_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-remarks::traffic-remark", "source_node_path": "/traffic-remarks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-filters::traffic-filter", "source_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "dest_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-filters::traffic-filter", "source_node_path": "/traffic-filters", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-url-filter", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sec-url-filter", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-url-filter:sec-url-filter::profiles::profile", "source_vertex_label": "huawei-sec-url-filter", "dest_vertex_label": "huawei-sec-url-filter:sec-url-filter::profiles::profile", "source_node_path": "/huawei-sec-url-filter:sec-url-filter/profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-url-filter:sec-url-filter::profiles::profile::blacklist::match-rules::match-rule", "source_vertex_label": "huawei-sec-url-filter:sec-url-filter::profiles::profile", "dest_vertex_label": "huawei-sec-url-filter:sec-url-filter::profiles::profile::blacklist::match-rules::match-rule", "source_node_path": "/blacklist/match-rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-url-filter:sec-url-filter::profiles::profile::whitelist::match-rules::match-rule", "source_vertex_label": "huawei-sec-url-filter:sec-url-filter::profiles::profile", "dest_vertex_label": "huawei-sec-url-filter:sec-url-filter::profiles::profile::whitelist::match-rules::match-rule", "source_node_path": "/whitelist/match-rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-aspf", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-aspf", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aspf:aspf::protocol", "source_vertex_label": "hua<PERSON>-aspf", "dest_vertex_label": "huawei-aspf:aspf::protocol", "source_node_path": "/huawei-aspf:aspf", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-capture", "source_vertex_label": "ds", "dest_vertex_label": "huawei-capture", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-capture:capture-packet::interface-name", "source_vertex_label": "huawei-capture", "dest_vertex_label": "huawei-capture:capture-packet::interface-name", "source_node_path": "/huawei-capture:capture-packet", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cfg", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::startup-infos::startup-info", "source_vertex_label": "huawei-cfg", "dest_vertex_label": "huawei-cfg:cfg::startup-infos::startup-info", "source_node_path": "/huawei-cfg:cfg/startup-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::cfg-files::cfg-file", "source_vertex_label": "huawei-cfg", "dest_vertex_label": "huawei-cfg:cfg::cfg-files::cfg-file", "source_node_path": "/huawei-cfg:cfg/cfg-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cli", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cli", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign", "source_vertex_label": "ds", "dest_vertex_label": "huawei-codesign", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::software-crls::software-crl", "source_vertex_label": "huawei-codesign", "dest_vertex_label": "huawei-codesign:codesign::software-crls::software-crl", "source_node_path": "/huawei-codesign:codesign/software-crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::crl-names::crl-name", "source_vertex_label": "huawei-codesign", "dest_vertex_label": "huawei-codesign:codesign::crl-names::crl-name", "source_node_path": "/huawei-codesign:codesign/crl-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cpu-memory", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-cpu-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-memory-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-storage-partition-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-top-infos::board-memory-top-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-memory-top-infos::board-memory-top-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-memory-top-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-top-infos::board-cpu-top-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-cpu-top-infos::board-cpu-top-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-cpu-top-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe", "source_vertex_label": "ds", "dest_vertex_label": "huawei-devm-poe", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe", "source_vertex_label": "huawei-devm-poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "source_node_path": "/huawei-devm-poe:devm-poe/poes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-devm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::physical-entitys::physical-entity", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::physical-entitys::physical-entity", "source_node_path": "/huawei-devm:devm/physical-entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::mpu-boards::mpu-board", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::mpu-boards::mpu-board", "source_node_path": "/huawei-devm:devm/mpu-boards", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::ports::port", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::ports::port", "source_node_path": "/huawei-devm:devm/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::ports::port::huawei-pic:ethernet::speed-autos", "source_vertex_label": "huawei-devm:devm::ports::port", "dest_vertex_label": "huawei-devm:devm::ports::port::huawei-pic:ethernet::speed-autos", "source_node_path": "/huawei-pic:ethernet", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-dns:dns-snooping::domains::domain", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-dns:dns-snooping::domains::domain", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-dns:dns-snooping/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-dns:dns-snooping::caches::cache", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-dns:dns-snooping::caches::cache", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-dns:dns-snooping/caches", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-ip:ipv4/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:base-info::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:base-info::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-radio:base-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:radio::config::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:radio::config::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-radio:radio/config", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:radio-rx-statistics::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:radio-rx-statistics::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-radio:radio-rx-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:radio-tx-statistics::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:radio-tx-statistics::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-radio:radio-tx-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:selfhealing-statistics::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:selfhealing-statistics::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-radio:selfhealing-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:tx-power::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-radio:tx-power::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-radio:tx-power", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-lists::sta-list", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-lists::sta-list", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-sta:sta-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-lists::sta-list::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-lists::sta-list", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-lists::sta-list::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-statistics::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-statistics::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-sta:sta-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-statistics::sta-mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-sta:sta-statistics::sta-mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-sta:sta-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-vap:vap-statisticses::vap-statistics", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-vap:vap-statisticses::vap-statistics", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lmac:lmac/huawei-diagnose-lmac-vap:vap-statisticses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-vap:vap-statisticses::vap-statistics::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-vap:vap-statisticses::vap-statistics", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lmac:lmac::huawei-diagnose-lmac-vap:vap-statisticses::vap-statistics::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/mac-learnings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/vlan-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/car-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/acl-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_node_path": "/acl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-nctl:nctl/innertables", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-nfc:nfc::processes::process", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-nfc:nfc::processes::process", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-nfc:nfc/processes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::syslog-servers::syslog-server", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::syslog-servers::syslog-server", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/syslog-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_node_path": "/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_node_path": "/indicators", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "source_node_path": "/collect-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/collect-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/clients", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client::subscribe-paths::subscribe-path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::clients::client::subscribe-paths::subscribe-path", "source_node_path": "/subscribe-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/notification-subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-patch:patch::patch-unit-infos::patch-unit-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-patch:patch::patch-unit-infos::patch-unit-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-patch:patch/patch-unit-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-pd:power-supplys::power-supply", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-pd:power-supplys::power-supply", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-pd:power-supplys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::config-statistics::config-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::config-statistics::config-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/config-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::vap-config-statistics::vap-config-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::vap-config-statistics::vap-config-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/vap-config-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::vlan-config-statistics::vlan-config-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::vlan-config-statistics::vlan-config-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/vlan-config-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::https-redirect-statistics::https-redirect-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::https-redirect-statistics::https-redirect-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/https-redirect-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::session-statistics::session-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::session-statistics::session-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/session-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::statistics::statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::statistics::statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::user-table-statistics::user-table-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::user-table-statistics::user-table-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/user-table-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::variable-statistics::variable-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::variable-statistics::variable-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/variable-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::url-template-statistics::url-template-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::url-template-statistics::url-template-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/url-template-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::url-template-statistics::url-template-statistic::url", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::url-template-statistics::url-template-statistic", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::url-template-statistics::url-template-statistic::url", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::web-server-agent-statistics::web-server-agent-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::web-server-agent-statistics::web-server-agent-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/web-server-agent-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::http-table-qrys::http-table-qry", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-portal:portal::http-table-qrys::http-table-qry", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-portal:portal/http-table-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-radius:radius::dtls-config-statistics::dtls-config-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-radius:radius::dtls-config-statistics::dtls-config-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-radius:radius/dtls-config-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/routing-manage/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/direct-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/static-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/user-network-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-system:sysdiag::service-infos::service-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-system:sysdiag::service-infos::service-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-system:sysdiag/service-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::terminals::terminal", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::terminals::terminal", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/terminals", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::terminals::terminal::fingerprints::fingerprint", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::terminals::terminal", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::terminals::terminal::fingerprints::fingerprint", "source_node_path": "/fingerprints", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::statistics::statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::statistics::statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::running-status::modules::module", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::running-status::modules::module", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/running-status/modules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/configuration-statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status::infos::info", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-tid:tid::configuration-statuses::configuration-status::infos::info", "source_node_path": "/infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::radio-obj::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::radio-obj::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/dot11/radio-obj", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::sta-list::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::sta-list::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/dot11/sta-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::vap-objs::vap-obj", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::vap-objs::vap-obj", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/dot11/vap-objs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::vap-objs::vap-obj::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::vap-objs::vap-obj", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::dot11::vap-objs::vap-obj::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::radios::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::radios::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/wcfg/radios", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::vaps::vap", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::vaps::vap", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/wcfg/vaps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::vaps::vap::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::vaps::vap", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::vaps::vap::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::history", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::wcfg::history", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/wcfg", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:channel-list::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:channel-list::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:channel-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:scan-status::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:scan-status::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:scan-status", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:calibrate::neighbor-infos::neighbor-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:calibrate::neighbor-infos::neighbor-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:calibrate/neighbor-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:calibrate::neighbor-infos::neighbor-info::mac", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:calibrate::neighbor-infos::neighbor-info", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:calibrate::neighbor-infos::neighbor-info::mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:meas-3d::report::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:meas-3d::report::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:meas-3d/report", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:location::device-info::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:location::device-info::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:location/device-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::attack-detects::attack-detect", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::attack-detects::attack-detect", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:wids/attack-detects", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::attack-detects::attack-detect::mac", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::attack-detects::attack-detect", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::attack-detects::attack-detect::mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::counter-measure-infos::counter-measure-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::counter-measure-infos::counter-measure-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:wids/counter-measure-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::counter-measure-infos::counter-measure-info::mac", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::counter-measure-infos::counter-measure-info", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::counter-measure-infos::counter-measure-info::mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::rogue-detects::rogue-detect", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::rogue-detects::rogue-detect", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:wids/rogue-detects", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::rogue-detects::rogue-detect::mac", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::rogue-detects::rogue-detect", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::rogue-detects::rogue-detect::mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::self-manage-bssids::self-manage-bssid", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::self-manage-bssids::self-manage-bssid", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:wids/self-manage-bssids", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::self-manage-bssids::self-manage-bssid::mac", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::self-manage-bssids::self-manage-bssid", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:wids::self-manage-bssids::self-manage-bssid::mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::link-info::peer-mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::link-info::peer-mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:mesh/link-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::neighbors::neighbor", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::neighbors::neighbor", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:mesh/neighbors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::neighbors::neighbor::peer-mac", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::neighbors::neighbor", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::neighbors::neighbor::peer-mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::blacklist::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-radio:mesh::blacklist::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-radio:mesh/blacklist", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-capability-info::sta-mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-capability-info::sta-mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:sta-capability-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::fsm-trace-info::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::fsm-trace-info::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station/fsm-trace-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::reject-record::sta-mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::reject-record::sta-mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station/reject-record", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::pmk-cache::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::pmk-cache::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station/pmk-cache", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::neighbor-ap::measure-info::sta-mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::neighbor-ap::measure-info::sta-mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station/neighbor-ap/measure-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::sticky-info::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:station::sticky-info::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:station/sticky-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:ft-roam::vap-statistics::vap-statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:ft-roam::vap-statistics::vap-statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:ft-roam/vap-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:ft-roam::vap-statistics::vap-statistic::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:ft-roam::vap-statistics::vap-statistic", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:ft-roam::vap-statistics::vap-statistic::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::ap-list::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::ap-list::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:neighbor-ap/ap-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::link-list::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::link-list::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:neighbor-ap/link-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::vap-lists::vap-list", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::vap-lists::vap-list", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:neighbor-ap/vap-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::vap-lists::vap-list::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::vap-lists::vap-list", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:neighbor-ap::vap-lists::vap-list::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::comeaslists::comeaslist", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::comeaslists::comeaslist", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:sta-meas/comeaslists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::comeaslists::comeaslist::channel", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::comeaslists::comeaslist", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::comeaslists::comeaslist::channel", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::measlist::radio", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sta-meas::measlist::radio", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:sta-meas/measlist", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:steer-history::index", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:steer-history::index", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:steer-history", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sew::neighbor-list::mac", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-sta:sew::neighbor-list::mac", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-sta:sew/neighbor-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap::configs::config", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap::configs::config", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-vap:vap/configs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap::configs::config::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap::configs::config", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap::configs::config::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap-propertys::vap-property", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap-propertys::vap-property", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-vap:vap-propertys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap-propertys::vap-property::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap-propertys::vap-property", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:vap-propertys::vap-property::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:wmm-parameters::wmm-parameter", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:wmm-parameters::wmm-parameter", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-umac:umac/huawei-diagnose-umac-vap:wmm-parameters", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:wmm-parameters::wmm-parameter::vap", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:wmm-parameters::wmm-parameter", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-umac:umac::huawei-diagnose-umac-vap:wmm-parameters::wmm-parameter::vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose-device-info", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose-device-info", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose-omu", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose-omu", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose-portal", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose-portal", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose-wmp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose-wmp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki", "source_vertex_label": "ds", "dest_vertex_label": "huawei-pki", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::entitys::entity", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::entitys::entity", "source_node_path": "/huawei-pki:pki/entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cmp-sessions::cmp-session", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::cmp-sessions::cmp-session", "source_node_path": "/huawei-pki:pki/cmp-sessions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::domains::domain", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::domains::domain", "source_node_path": "/huawei-pki:pki/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "source_node_path": "/huawei-pki:pki/certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_node_path": "/huawei-pki:pki/preset-certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::crl-infos::crl-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::crl-infos::crl-info", "source_node_path": "/huawei-pki:pki/crl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_node_path": "/huawei-pki:pki/key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_node_path": "/huawei-pki:pki/cert-key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_node_path": "/cert-key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-create::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-create::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-create/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-destroy/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-import::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-import::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-import/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-import::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-import::certificates::certificate", "source_node_path": "/huawei-pki:certificate-import/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-delete::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-delete::certificates::certificate", "source_node_path": "/huawei-pki:certificate-delete/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "source_node_path": "/huawei-pki:certificate-delete-by-domain/domain-certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-replace::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-replace::certificates::certificate", "source_node_path": "/huawei-pki:certificate-replace/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:csr-generate::csrs::csr", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:csr-generate::csrs::csr", "source_node_path": "/huawei-pki:csr-generate/csrs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-import::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-import::crls::crl", "source_node_path": "/huawei-pki:crl-import/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-delete::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-delete::crls::crl", "source_node_path": "/huawei-pki:crl-delete/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-delete-by-domain::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-delete-by-domain::crls::crl", "source_node_path": "/huawei-pki:crl-delete-by-domain/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify", "source_vertex_label": "ds", "dest_vertex_label": "huawei-terminal-identify", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::fingerprints::fingerprint", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::fingerprints::fingerprint", "source_node_path": "/huawei-terminal-identify:terminal-identify/fingerprints", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::fingerprints::fingerprint::features::feature", "source_vertex_label": "huawei-terminal-identify:terminal-identify::fingerprints::fingerprint", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::fingerprints::fingerprint::features::feature", "source_node_path": "/features", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnostic-tools", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv4/ping-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv4/trace-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv6::ping-results::ping-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv6::ping-results::ping-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv6/ping-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv6::ping-results::ping-result::ping-result-details::ping-result-detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv6::ping-results::ping-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv6::ping-results::ping-result::ping-result-details::ping-result-detail", "source_node_path": "/ping-result-details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv6::trace-results::trace-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv6::trace-results::trace-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv6/trace-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv6::trace-results::trace-result::trace-result-details::trace-result-detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv6::trace-results::trace-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv6::trace-results::trace-result::trace-result-details::trace-result-detail", "source_node_path": "/trace-result-details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dns", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::domains::domain", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::domains::domain", "source_node_path": "/huawei-dns:dns/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_node_path": "/huawei-dns:dns/ipv4-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv6-hosts::ipv6-host", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv6-hosts::ipv6-host", "source_node_path": "/huawei-dns:dns/ipv6-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_node_path": "/huawei-dns:dns/ipv4-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv6-servers::ipv6-server", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv6-servers::ipv6-server", "source_node_path": "/huawei-dns:dns/ipv6-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::dns-relay::local-ips::local-ip", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::dns-relay::local-ips::local-ip", "source_node_path": "/huawei-dns:dns/dns-relay/local-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::dns-spoofing::ipv4-address", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::dns-spoofing::ipv4-address", "source_node_path": "/huawei-dns:dns/dns-spoofing", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::dns-spoofing::ipv6-address", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::dns-spoofing::ipv6-address", "source_node_path": "/huawei-dns:dns/dns-spoofing", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::query-host-ips::query-host-ip", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::query-host-ips::query-host-ip", "source_node_path": "/huawei-dns:dns/query-host-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::query-host-ipv6s::query-host-ipv6", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::query-host-ipv6s::query-host-ipv6", "source_node_path": "/huawei-dns:dns/query-host-ipv6s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::query-host-ipv6s::query-host-ipv6::ipv6-address", "source_vertex_label": "huawei-dns:dns::query-host-ipv6s::query-host-ipv6", "dest_vertex_label": "huawei-dns:dns::query-host-ipv6s::query-host-ipv6::ipv6-address", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::passthrough-domains::passthrough-domain", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::passthrough-domains::passthrough-domain", "source_node_path": "/huawei-dns:dns/passthrough-domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "<PERSON><PERSON><PERSON>-driver", "source_vertex_label": "ds", "dest_vertex_label": "<PERSON><PERSON><PERSON>-driver", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::temperature2s::temperature2", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::temperature2s::temperature2", "source_node_path": "/huawei-driver:driver/temperature2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::device-health-checks::device-health-check", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::device-health-checks::device-health-check", "source_node_path": "/huawei-driver:driver/device-health-checks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::electronic-labels::electronic-label", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::electronic-labels::electronic-label", "source_node_path": "/huawei-driver:driver/electronic-labels", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt", "source_vertex_label": "ds", "dest_vertex_label": "huawei-easyweb-netmgmt", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-infos::device-info", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-infos::device-info", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-upgrade-infos::device-upgrade-info", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-upgrade-infos::device-upgrade-info", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-upgrade-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:upgrade::devices::device", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:upgrade::devices::device", "source_node_path": "/huawei-easyweb-netmgmt:upgrade/devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-tm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-tm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-fm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::alarms::alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::alarms::alarm", "source_node_path": "/huawei-fm:fm/alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::active-alarms::active-alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::active-alarms::active-alarm", "source_node_path": "/huawei-fm:fm/active-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::history-alarms::history-alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::history-alarms::history-alarm", "source_node_path": "/huawei-fm:fm/history-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ecc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ecc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ecc:ecc::peer-keys::peer-key", "source_vertex_label": "huawei-ecc", "dest_vertex_label": "huawei-ecc:ecc::peer-keys::peer-key", "source_node_path": "/huawei-ecc:ecc/peer-keys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation", "source_vertex_label": "ds", "dest_vertex_label": "huawei-file-operation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation:file-operation::dirs::dir", "source_vertex_label": "huawei-file-operation", "dest_vertex_label": "huawei-file-operation:file-operation::dirs::dir", "source_node_path": "/huawei-file-operation:file-operation/dirs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ftpc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-ftpc", "dest_vertex_label": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_node_path": "/huawei-ftpc:ftpc/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-host-security", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-host-security", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_node_path": "/huawei-host-security:host-security/anti-attacks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_node_path": "/huawei-host-security:host-security/packet-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::top-packet-statistics::top-packet-statistic", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::top-packet-statistics::top-packet-statistic", "source_node_path": "/huawei-host-security:host-security/top-packet-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_node_path": "/huawei-host-security:host-security/adjust-car", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::policys::policy", "source_node_path": "/huawei-host-security:host-security/policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::filters::filter", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::filters::filter", "source_node_path": "/filters", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_node_path": "/cpcars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_node_path": "/auto-defend", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_node_path": "/applied-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http", "source_vertex_label": "ds", "dest_vertex_label": "huawei-http", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "source_vertex_label": "huawei-http", "dest_vertex_label": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "source_node_path": "/huawei-http:http/httpc-transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::transfer-tasks::transfer-task", "source_vertex_label": "huawei-http", "dest_vertex_label": "huawei-http:http::transfer-tasks::transfer-task", "source_node_path": "/huawei-http:http/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-multicast", "source_vertex_label": "ds", "dest_vertex_label": "huawei-multicast", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-loadbalance", "source_vertex_label": "ds", "dest_vertex_label": "huawei-loadbalance", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-masterkey", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-masterkey", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management", "source_vertex_label": "ds", "dest_vertex_label": "huawei-module-management", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::module-infos::module-info", "source_vertex_label": "huawei-module-management", "dest_vertex_label": "huawei-module-management:module-management::module-infos::module-info", "source_node_path": "/huawei-module-management:module-management/module-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::operation-schedules::operation-schedule", "source_vertex_label": "huawei-module-management", "dest_vertex_label": "huawei-module-management:module-management::operation-schedules::operation-schedule", "source_node_path": "/huawei-module-management:module-management/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-dot1x", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nac-dot1x", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-dot1x:nac-dot1x::dot1x-access-profiles::dot1x-access-profile", "source_vertex_label": "huawei-nac-dot1x", "dest_vertex_label": "huawei-nac-dot1x:nac-dot1x::dot1x-access-profiles::dot1x-access-profile", "source_node_path": "/huawei-nac-dot1x:nac-dot1x/dot1x-access-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-dot1x:nac-dot1x::dot1x-access-profiles::dot1x-access-profile::authorize-of-authentication-events::authorize-of-authentication-event", "source_vertex_label": "huawei-nac-dot1x:nac-dot1x::dot1x-access-profiles::dot1x-access-profile", "dest_vertex_label": "huawei-nac-dot1x:nac-dot1x::dot1x-access-profiles::dot1x-access-profile::authorize-of-authentication-events::authorize-of-authentication-event", "source_node_path": "/authorize-of-authentication-events", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-mac", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nac-mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-mac:nac-mac::mac-access-profiles::mac-access-profile", "source_vertex_label": "huawei-nac-mac", "dest_vertex_label": "huawei-nac-mac:nac-mac::mac-access-profiles::mac-access-profile", "source_node_path": "/huawei-nac-mac:nac-mac/mac-access-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nac-portal", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::global::redirect-global::redirect-http-port", "source_vertex_label": "huawei-nac-portal", "dest_vertex_label": "huawei-nac-portal:nac-portal::global::redirect-global::redirect-http-port", "source_node_path": "/huawei-nac-portal:nac-portal/global/redirect-global", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::global::portal-page-functions::portal-page-function", "source_vertex_label": "huawei-nac-portal", "dest_vertex_label": "huawei-nac-portal:nac-portal::global::portal-page-functions::portal-page-function", "source_node_path": "/huawei-nac-portal:nac-portal/global/portal-page-functions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::url-templates::url-template", "source_vertex_label": "huawei-nac-portal", "dest_vertex_label": "huawei-nac-portal:nac-portal::url-templates::url-template", "source_node_path": "/huawei-nac-portal:nac-portal/url-templates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::url-templates::url-template::urls::url", "source_vertex_label": "huawei-nac-portal:nac-portal::url-templates::url-template", "dest_vertex_label": "huawei-nac-portal:nac-portal::url-templates::url-template::urls::url", "source_node_path": "/urls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::url-templates::url-template::url-ssids::url-ssid", "source_vertex_label": "huawei-nac-portal:nac-portal::url-templates::url-template", "dest_vertex_label": "huawei-nac-portal:nac-portal::url-templates::url-template::url-ssids::url-ssid", "source_node_path": "/url-ssids", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::portal-servers::portal-server", "source_vertex_label": "huawei-nac-portal", "dest_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server", "source_node_path": "/huawei-nac-portal:nac-portal/portal-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::portal-servers::portal-server::portal-server-ip", "source_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server", "dest_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server::portal-server-ip", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::portal-servers::portal-server::portal-server-ipv6", "source_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server", "dest_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server::portal-server-ipv6", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::portal-servers::portal-server::server-detect-function::action", "source_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server", "dest_vertex_label": "huawei-nac-portal:nac-portal::portal-servers::portal-server::server-detect-function::action", "source_node_path": "/server-detect-function", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::portal-access-profiles::portal-access-profile", "source_vertex_label": "huawei-nac-portal", "dest_vertex_label": "huawei-nac-portal:nac-portal::portal-access-profiles::portal-access-profile", "source_node_path": "/huawei-nac-portal:nac-portal/portal-access-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac-portal:nac-portal::portal-access-profiles::portal-access-profile::authentication-networks::authentication-network", "source_vertex_label": "huawei-nac-portal:nac-portal::portal-access-profiles::portal-access-profile", "dest_vertex_label": "huawei-nac-portal:nac-portal::portal-access-profiles::portal-access-profile::authentication-networks::authentication-network", "source_node_path": "/authentication-networks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-profiles::authentication-profile", "source_vertex_label": "huawei-nac", "dest_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile", "source_node_path": "/huawei-nac:nac/authentication-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-profiles::authentication-profile::access-force-domains::access-force-domain", "source_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile", "dest_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile::access-force-domains::access-force-domain", "source_node_path": "/access-force-domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-profiles::authentication-profile::access-default-domains::access-default-domain", "source_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile", "dest_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile::access-default-domains::access-default-domain", "source_node_path": "/access-default-domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-profiles::authentication-profile::authorize-of-authentication-events::authorize-of-authentication-event", "source_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile", "dest_vertex_label": "huawei-nac:nac::authentication-profiles::authentication-profile::authorize-of-authentication-events::authorize-of-authentication-event", "source_node_path": "/authorize-of-authentication-events", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile", "source_vertex_label": "huawei-nac", "dest_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile", "source_node_path": "/huawei-nac:nac/authentication-free-rule-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile::free-acl::ipv6-acl", "source_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile", "dest_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile::free-acl::ipv6-acl", "source_node_path": "/free-acl", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile::free-acl::ipv4-acl", "source_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile", "dest_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile::free-acl::ipv4-acl", "source_node_path": "/free-acl", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile::free-rules::free-rule", "source_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile", "dest_vertex_label": "huawei-nac:nac::authentication-free-rule-profiles::authentication-free-rule-profile::free-rules::free-rule", "source_node_path": "/free-rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nac:nac::user-roams::user-roam", "source_vertex_label": "huawei-nac", "dest_vertex_label": "huawei-nac:nac::user-roams::user-roam", "source_node_path": "/huawei-nac:nac/user-roams", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nat-address-group", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "source_vertex_label": "huawei-nat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "source_node_path": "/huawei-nat-address-group:nat-address-group/snat-address-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "source_node_path": "/sections", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "source_node_path": "/exclude-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "source_node_path": "/exclude-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nat-policy", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule", "source_vertex_label": "huawei-nat-policy", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "source_node_path": "/huawei-nat-policy:nat-policy/rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "source_node_path": "/egress/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "source_node_path": "/source-address/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "source_node_path": "/source-address/address-ipv4-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "source_node_path": "/source-address/address-ipv4-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_node_path": "/source-address/address-ipv4-range-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "source_node_path": "/destination-address/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "source_node_path": "/destination-address/address-ipv4-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "source_node_path": "/destination-address/address-ipv4-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_node_path": "/destination-address/address-ipv4-range-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "source_node_path": "/service/service-items/icmpv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "source_node_path": "/service/service-items/protocol", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items-exclude/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "source_node_path": "/service/service-items-exclude/icmpv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "source_node_path": "/service/service-items-exclude/protocol", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-server", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nat-server", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-server:nat-server::server-mappings::server-mapping", "source_vertex_label": "huawei-nat-server", "dest_vertex_label": "huawei-nat-server:nat-server::server-mappings::server-mapping", "source_node_path": "/huawei-nat-server:nat-server/server-mappings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ntp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::unicasts::unicast", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::unicasts::unicast", "source_node_path": "/huawei-ntp:ntp/unicasts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::authentications::authentication", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::authentications::authentication", "source_node_path": "/huawei-ntp:ntp/authentications", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::full-sessions::full-session", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::full-sessions::full-session", "source_node_path": "/huawei-ntp:ntp/full-sessions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-open-app-system", "source_vertex_label": "ds", "dest_vertex_label": "huawei-open-app-system", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-open-app-system:open-app-system::applications::application", "source_vertex_label": "huawei-open-app-system", "dest_vertex_label": "huawei-open-app-system:open-app-system::applications::application", "source_node_path": "/huawei-open-app-system:open-app-system/applications", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-open-app-system:open-app-system::applications::application::instances::instance", "source_vertex_label": "huawei-open-app-system:open-app-system::applications::application", "dest_vertex_label": "huawei-open-app-system:open-app-system::applications::application::instances::instance", "source_node_path": "/instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-open-app-system:open-app-system::softwares::software", "source_vertex_label": "huawei-open-app-system", "dest_vertex_label": "huawei-open-app-system:open-app-system::softwares::software", "source_node_path": "/huawei-open-app-system:open-app-system/softwares", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-open-app-system:open-app-system::public-keys::public-key", "source_vertex_label": "huawei-open-app-system", "dest_vertex_label": "huawei-open-app-system:open-app-system::public-keys::public-key", "source_node_path": "/huawei-open-app-system:open-app-system/public-keys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry", "source_vertex_label": "ds", "dest_vertex_label": "openconfig-telemetry", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_node_path": "/openconfig-telemetry:telemetry-system/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_node_path": "/openconfig-telemetry:telemetry-system/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_node_path": "/openconfig-telemetry:telemetry-system/subscriptions/persistent", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_node_path": "/sensor-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-openconfig-telemetry-ext", "source_vertex_label": "ds", "dest_vertex_label": "huawei-openconfig-telemetry-ext", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-openconfig-telemetry-ext:resync-subscription::sensor-profile", "source_vertex_label": "huawei-openconfig-telemetry-ext", "dest_vertex_label": "huawei-openconfig-telemetry-ext:resync-subscription::sensor-profile", "source_node_path": "/huawei-openconfig-telemetry-ext:resync-subscription", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch", "source_vertex_label": "ds", "dest_vertex_label": "huawei-patch", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "source_node_path": "/huawei-patch:patch/patch-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_node_path": "/operations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_node_path": "/huawei-patch:patch/next-startup-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::operation-schedules::operation-schedule", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::operation-schedules::operation-schedule", "source_node_path": "/huawei-patch:patch/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-pp6", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-pp6", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pp6:pp6::ipv6-pmtus::ipv6-pmtu-statics::ipv6-pmtu-static", "source_vertex_label": "hua<PERSON>-pp6", "dest_vertex_label": "huawei-pp6:pp6::ipv6-pmtus::ipv6-pmtu-statics::ipv6-pmtu-static", "source_node_path": "/huawei-pp6:pp6/ipv6-pmtus/ipv6-pmtu-statics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pp6:pp6::ipv6-pmtus::ipv6-pmtu-states::ipv6-pmtu-state", "source_vertex_label": "hua<PERSON>-pp6", "dest_vertex_label": "huawei-pp6:pp6::ipv6-pmtus::ipv6-pmtu-states::ipv6-pmtu-state", "source_node_path": "/huawei-pp6:pp6/ipv6-pmtus/ipv6-pmtu-states", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-rsa", "source_vertex_label": "ds", "dest_vertex_label": "huawei-rsa", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-rsa:rsa::peer-keys::peer-key", "source_vertex_label": "huawei-rsa", "dest_vertex_label": "huawei-rsa:rsa::peer-keys::peer-key", "source_node_path": "/huawei-rsa:rsa/peer-keys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-savi", "source_vertex_label": "ds", "dest_vertex_label": "huawei-savi", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sec-session-mgmt", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_vertex_label": "huawei-sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_node_path": "/huawei-sec-session-mgmt:sec-session-mgmt/protocol-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_vertex_label": "huawei-sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_node_path": "/huawei-sec-session-mgmt:sec-session-mgmt/application-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade", "source_vertex_label": "ds", "dest_vertex_label": "huawei-smart-upgrade", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_vertex_label": "huawei-smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_node_path": "/huawei-smart-upgrade:smart-upgrade/smart-upgrade-info/download-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_node_path": "/download-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "source_vertex_label": "huawei-smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "source_node_path": "/huawei-smart-upgrade:download-software/type/specify-software/version-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software", "source_vertex_label": "ds", "dest_vertex_label": "huawei-software", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::versions::version", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::versions::version", "source_node_path": "/huawei-software:software/versions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::startup-packages::startup-package", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::startup-packages::startup-package", "source_node_path": "/huawei-software:software/startup-packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::packages::package", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::packages::package", "source_node_path": "/huawei-software:software/packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::operation-schedules::operation-schedule", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::operation-schedules::operation-schedule", "source_node_path": "/huawei-software:software/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-snmp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-snmp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-snmp:snmp::source-interfaces::source-interface", "source_vertex_label": "huawei-snmp", "dest_vertex_label": "huawei-snmp:snmp::source-interfaces::source-interface", "source_node_path": "/huawei-snmp:snmp/source-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-snmp:snmp::mib-views::mib-view", "source_vertex_label": "huawei-snmp", "dest_vertex_label": "huawei-snmp:snmp::mib-views::mib-view", "source_node_path": "/huawei-snmp:snmp/mib-views", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-snmp:snmp::v3-groups::v3-group", "source_vertex_label": "huawei-snmp", "dest_vertex_label": "huawei-snmp:snmp::v3-groups::v3-group", "source_node_path": "/huawei-snmp:snmp/v3-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-snmp:snmp::usm-users::usm-user", "source_vertex_label": "huawei-snmp", "dest_vertex_label": "huawei-snmp:snmp::usm-users::usm-user", "source_node_path": "/huawei-snmp:snmp/usm-users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-snmp:snmp::standard-communitys::standard-community", "source_vertex_label": "huawei-snmp", "dest_vertex_label": "huawei-snmp:snmp::standard-communitys::standard-community", "source_node_path": "/huawei-snmp:snmp/standard-communitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-socket", "source_vertex_label": "ds", "dest_vertex_label": "huawei-socket", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software-fwd", "source_vertex_label": "ds", "dest_vertex_label": "huawei-software-fwd", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software-fwd:software-fwd::fwd-statistics-infos::fwd-statistics-info", "source_vertex_label": "huawei-software-fwd", "dest_vertex_label": "huawei-software-fwd:software-fwd::fwd-statistics-infos::fwd-statistics-info", "source_node_path": "/huawei-software-fwd:software-fwd/fwd-statistics-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sshc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-results::transfer-result", "source_vertex_label": "huawei-sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-results::transfer-result", "source_node_path": "/huawei-sshc:sshc/transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_node_path": "/huawei-sshc:sshc/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sshs", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::users::user", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::users::user", "source_node_path": "/huawei-sshs:sshs/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_node_path": "/huawei-sshs:sshs/ipv4-server-sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "source_node_path": "/huawei-sshs:sshs/call-homes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_node_path": "/end-points", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog", "source_vertex_label": "ds", "dest_vertex_label": "huawei-syslog", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::servers::server", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::servers::server", "source_node_path": "/huawei-syslog:syslog/servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::log-switch-list::log-switch", "source_node_path": "/huawei-syslog:syslog/log-switch-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "source_node_path": "/huawei-syslog:syslog/logfiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_node_path": "/latest-logs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::loginfos::loginfo", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::loginfos::loginfo", "source_node_path": "/huawei-syslog:syslog/loginfos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller", "source_vertex_label": "ds", "dest_vertex_label": "huawei-system-controller", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "source_vertex_label": "huawei-system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::offline-records::offline-record", "source_node_path": "/huawei-system-controller:system-controller/offline-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_vertex_label": "huawei-system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_node_path": "/huawei-system-controller:system-controller/register-fail-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system", "source_vertex_label": "ds", "dest_vertex_label": "huawei-system", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "source_node_path": "/huawei-system:system/system-info/huawei-system-controller:upstream-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::security-risks::security-risk", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::security-risks::security-risk", "source_node_path": "/huawei-system:system/security-risks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::weak-passwords::weak-password", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::weak-passwords::weak-password", "source_node_path": "/huawei-system:system/weak-passwords", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wifi", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi:wifi::power-supply-switchs::power-supply-switch", "source_vertex_label": "huawei-wifi", "dest_vertex_label": "huawei-wifi:wifi::power-supply-switchs::power-supply-switch", "source_node_path": "/huawei-wifi:wifi/power-supply-switchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi:wifi::ap-cards::ap-card", "source_vertex_label": "huawei-wifi", "dest_vertex_label": "huawei-wifi:wifi::ap-cards::ap-card", "source_node_path": "/huawei-wifi:wifi/ap-cards", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "<PERSON><PERSON><PERSON>-web-manager", "source_vertex_label": "ds", "dest_vertex_label": "<PERSON><PERSON><PERSON>-web-manager", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-web-manager:web-manager::source-interface::interfaces::interface", "source_vertex_label": "<PERSON><PERSON><PERSON>-web-manager", "dest_vertex_label": "huawei-web-manager:web-manager::source-interface::interfaces::interface", "source_node_path": "/huawei-web-manager:web-manager/source-interface/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-web-manager:web-manager::security::http-forward-host::addresses::address", "source_vertex_label": "<PERSON><PERSON><PERSON>-web-manager", "dest_vertex_label": "huawei-web-manager:web-manager::security::http-forward-host::addresses::address", "source_node_path": "/huawei-web-manager:web-manager/security/http-forward-host/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-radio", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wifi-radio", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-radio:wifi-radio::wifi-radio-statistics::wifi-radio-statistic", "source_vertex_label": "huawei-wifi-radio", "dest_vertex_label": "huawei-wifi-radio:wifi-radio::wifi-radio-statistics::wifi-radio-statistic", "source_node_path": "/huawei-wifi-radio:wifi-radio/wifi-radio-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-radio:wifi-radio::wifi-neighbor-statistics::wifi-neighbor-statistic", "source_vertex_label": "huawei-wifi-radio", "dest_vertex_label": "huawei-wifi-radio:wifi-radio::wifi-neighbor-statistics::wifi-neighbor-statistic", "source_node_path": "/huawei-wifi-radio:wifi-radio/wifi-neighbor-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-radio:wifi-radio::csi-datas::csi-data", "source_vertex_label": "huawei-wifi-radio", "dest_vertex_label": "huawei-wifi-radio:wifi-radio::csi-datas::csi-data", "source_node_path": "/huawei-wifi-radio:wifi-radio/csi-datas", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-radio:wifi-radio::spectrum-datas::spectrum-data", "source_vertex_label": "huawei-wifi-radio", "dest_vertex_label": "huawei-wifi-radio:wifi-radio::spectrum-datas::spectrum-data", "source_node_path": "/huawei-wifi-radio:wifi-radio/spectrum-datas", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-sta", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wifi-sta", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-sta:wifi-sta::wifi-sta-statistics::wifi-sta-statistic", "source_vertex_label": "huawei-wifi-sta", "dest_vertex_label": "huawei-wifi-sta:wifi-sta::wifi-sta-statistics::wifi-sta-statistic", "source_node_path": "/huawei-wifi-sta:wifi-sta/wifi-sta-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-sta:wifi-sta::location-datas::location-data", "source_vertex_label": "huawei-wifi-sta", "dest_vertex_label": "huawei-wifi-sta:wifi-sta::location-datas::location-data", "source_node_path": "/huawei-wifi-sta:wifi-sta/location-datas", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-vap", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wifi-vap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wifi-vap:wifi-vap::wifi-vap-statistics::wifi-vap-statistic", "source_vertex_label": "huawei-wifi-vap", "dest_vertex_label": "huawei-wifi-vap:wifi-vap::wifi-vap-statistics::wifi-vap-statistic", "source_node_path": "/huawei-wifi-vap:wifi-vap/wifi-vap-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-airscan-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-airscan-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-airscan-profile:wlan-airscan-profile::airscan-profiles::airscan-profile", "source_vertex_label": "huawei-wlan-airscan-profile", "dest_vertex_label": "huawei-wlan-airscan-profile:wlan-airscan-profile::airscan-profiles::airscan-profile", "source_node_path": "/huawei-wlan-airscan-profile:wlan-airscan-profile/airscan-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-radio-2g-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "source_vertex_label": "huawei-wlan-radio-2g-profile", "dest_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "source_node_path": "/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile/radio-2g-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-basic-rate-lists::dot11bg-basic-rate-list", "source_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "dest_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-basic-rate-lists::dot11bg-basic-rate-list", "source_node_path": "/dot11bg-basic-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-support-rate-lists::dot11bg-support-rate-list", "source_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "dest_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-support-rate-lists::dot11bg-support-rate-list", "source_node_path": "/dot11bg-support-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-rrm-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-rrm-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-rrm-profile:wlan-rrm-profile::rrm-profiles::rrm-profile", "source_vertex_label": "huawei-wlan-rrm-profile", "dest_vertex_label": "huawei-wlan-rrm-profile:wlan-rrm-profile::rrm-profiles::rrm-profile", "source_node_path": "/huawei-wlan-rrm-profile:wlan-rrm-profile/rrm-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-radio", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ap-radio", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-mac-whitelists::ap-mac-whitelist", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-mac-whitelists::ap-mac-whitelist", "source_node_path": "/huawei-wlan-ap:wlan-ap/ap-mac-whitelists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-types::ap-type", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-types::ap-type", "source_node_path": "/huawei-wlan-ap:wlan-ap/ap-types", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "source_node_path": "/huawei-wlan-ap:wlan-ap/ap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-card:ap-card-instances::ap-card-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-card:ap-card-instances::ap-card-instance", "source_node_path": "/huawei-wlan-ap-card:ap-card-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance", "source_node_path": "/huawei-wlan-ap-radio:radio-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance::huawei-wlan-vap-inst:vap-instances::vap-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-ap-radio:radio-instances::radio-instance::huawei-wlan-vap-inst:vap-instances::vap-instance", "source_node_path": "/huawei-wlan-vap-inst:vap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:eth-trunk-instances::eth-trunk-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:eth-trunk-instances::eth-trunk-instance", "source_node_path": "/huawei-wlan-wired-port-profile:eth-trunk-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:ethernet-instances::ethernet-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:ethernet-instances::ethernet-instance", "source_node_path": "/huawei-wlan-wired-port-profile:ethernet-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_node_path": "/huawei-wlan-wired-port-profile:ge-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:multige-instances::multige-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:multige-instances::multige-instance", "source_node_path": "/huawei-wlan-wired-port-profile:multige-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:xge-instances::xge-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance::huawei-wlan-wired-port-profile:xge-instances::xge-instance", "source_node_path": "/huawei-wlan-wired-port-profile:xge-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info", "source_node_path": "/huawei-wlan-ap:wlan-ap/wmi-server-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info::log-report-modules::log-report-module", "source_vertex_label": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::wmi-server-infos::wmi-server-info::log-report-modules::log-report-module", "source_node_path": "/log-report-modules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:get-ap-connect-fail-reason::ap-instances::ap-instance", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:get-ap-connect-fail-reason::ap-instances::ap-instance", "source_node_path": "/huawei-wlan-ap:get-ap-connect-fail-reason/ap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:set-remote-ap-mode::ap-instances::ap-instance", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:set-remote-ap-mode::ap-instances::ap-instance", "source_node_path": "/huawei-wlan-ap:set-remote-ap-mode/ap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-card", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ap-card", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-iot-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-iot-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-iot-profile:wlan-iot-profile::iot-profiles::iot-profile", "source_vertex_label": "huawei-wlan-iot-profile", "dest_vertex_label": "huawei-wlan-iot-profile:wlan-iot-profile::iot-profiles::iot-profile", "source_node_path": "/huawei-wlan-iot-profile:wlan-iot-profile/iot-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-iot-profile:wlan-iot-profile::iot-profiles::iot-profile::management-servers::management-server", "source_vertex_label": "huawei-wlan-iot-profile:wlan-iot-profile::iot-profiles::iot-profile", "dest_vertex_label": "huawei-wlan-iot-profile:wlan-iot-profile::iot-profiles::iot-profile::management-servers::management-server", "source_node_path": "/management-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ap-group-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "source_vertex_label": "huawei-wlan-ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "source_node_path": "/huawei-wlan-ap-group-profile:wlan-ap-group-profile/ap-group-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance", "source_node_path": "/huawei-wlan-group-radio:radio-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance::huawei-wlan-vap-inst:vap-instances::vap-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-group-radio:radio-instances::radio-instance::huawei-wlan-vap-inst:vap-instances::vap-instance", "source_node_path": "/huawei-wlan-vap-inst:vap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:eth-trunk-instances::eth-trunk-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:eth-trunk-instances::eth-trunk-instance", "source_node_path": "/huawei-wlan-wired-port-profile:eth-trunk-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:ethernet-instances::ethernet-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:ethernet-instances::ethernet-instance", "source_node_path": "/huawei-wlan-wired-port-profile:ethernet-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_node_path": "/huawei-wlan-wired-port-profile:ge-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:multige-instances::multige-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:multige-instances::multige-instance", "source_node_path": "/huawei-wlan-wired-port-profile:multige-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:xge-instances::xge-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile::huawei-wlan-wired-port-profile:xge-instances::xge-instance", "source_node_path": "/huawei-wlan-wired-port-profile:xge-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-location", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-location:wlan-location::location-profiles::location-profile", "source_vertex_label": "huawei-wlan-location", "dest_vertex_label": "huawei-wlan-location:wlan-location::location-profiles::location-profile", "source_node_path": "/huawei-wlan-location:wlan-location/location-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mesh", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-mesh", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mesh:wlan-mesh::mesh-profiles::mesh-profile", "source_vertex_label": "huawei-wlan-mesh", "dest_vertex_label": "huawei-wlan-mesh:wlan-mesh::mesh-profiles::mesh-profile", "source_node_path": "/huawei-wlan-mesh:wlan-mesh/mesh-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mesh:wlan-mesh::mesh-whitelist-profiles::mesh-whitelist-profile", "source_vertex_label": "huawei-wlan-mesh", "dest_vertex_label": "huawei-wlan-mesh:wlan-mesh::mesh-whitelist-profiles::mesh-whitelist-profile", "source_node_path": "/huawei-wlan-mesh:wlan-mesh/mesh-whitelist-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mesh:wlan-mesh::mesh-whitelist-profiles::mesh-whitelist-profile::peer-ap-macs::peer-ap-mac", "source_vertex_label": "huawei-wlan-mesh:wlan-mesh::mesh-whitelist-profiles::mesh-whitelist-profile", "dest_vertex_label": "huawei-wlan-mesh:wlan-mesh::mesh-whitelist-profiles::mesh-whitelist-profile::peer-ap-macs::peer-ap-mac", "source_node_path": "/peer-ap-macs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mesh:wlan-mesh::mesh-link-infos::mesh-link-info", "source_vertex_label": "huawei-wlan-mesh", "dest_vertex_label": "huawei-wlan-mesh:wlan-mesh::mesh-link-infos::mesh-link-info", "source_node_path": "/huawei-wlan-mesh:wlan-mesh/mesh-link-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-radio-5g-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "source_vertex_label": "huawei-wlan-radio-5g-profile", "dest_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "source_node_path": "/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile/radio-5g-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-basic-rate-lists::dot11a-basic-rate-list", "source_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "dest_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-basic-rate-lists::dot11a-basic-rate-list", "source_node_path": "/dot11a-basic-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-support-rate-lists::dot11a-support-rate-list", "source_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "dest_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-support-rate-lists::dot11a-support-rate-list", "source_node_path": "/dot11a-support-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-wids", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::device-detect::permit-ap::ap-mac", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::device-detect::permit-ap::ap-mac", "source_node_path": "/huawei-wlan-wids:wlan-wids/device-detect/permit-ap", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::device-detect::permit-ap::oui", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::device-detect::permit-ap::oui", "source_node_path": "/huawei-wlan-wids:wlan-wids/device-detect/permit-ap", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::device-detect::permit-ap::ssid", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::device-detect::permit-ap::ssid", "source_node_path": "/huawei-wlan-wids:wlan-wids/device-detect/permit-ap", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::device-detect::spoof-ssid-fuzzy-match-regex", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::device-detect::spoof-ssid-fuzzy-match-regex", "source_node_path": "/huawei-wlan-wids:wlan-wids/device-detect", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::contain::manual-contain", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::contain::manual-contain", "source_node_path": "/huawei-wlan-wids:wlan-wids/contain", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::security-data::wids-detect-datas::wids-detect-data", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::security-data::wids-detect-datas::wids-detect-data", "source_node_path": "/huawei-wlan-wids:wlan-wids/security-data/wids-detect-datas", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::security-data::wids-attack-datas::wids-attack-data", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::security-data::wids-attack-datas::wids-attack-data", "source_node_path": "/huawei-wlan-wids:wlan-wids/security-data/wids-attack-datas", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wids:wlan-wids::security-data::wids-dynamic-blacklists::wids-dynamic-blacklist", "source_vertex_label": "huawei-wlan-wids", "dest_vertex_label": "huawei-wlan-wids:wlan-wids::security-data::wids-dynamic-blacklists::wids-dynamic-blacklist", "source_node_path": "/huawei-wlan-wids:wlan-wids/security-data/wids-dynamic-blacklists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-reg-dom-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-reg-dom-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile", "source_vertex_label": "huawei-wlan-reg-dom-profile", "dest_vertex_label": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile", "source_node_path": "/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile/regulatory-domain-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-system-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile:wlan-system-profile::tunnel-devices::tunnel-device", "source_vertex_label": "huawei-wlan-system-profile", "dest_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::tunnel-devices::tunnel-device", "source_node_path": "/huawei-wlan-system-profile:wlan-system-profile/tunnel-devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile:wlan-system-profile::service-tunnel-profiles::service-tunnel-profile", "source_vertex_label": "huawei-wlan-system-profile", "dest_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::service-tunnel-profiles::service-tunnel-profile", "source_node_path": "/huawei-wlan-system-profile:wlan-system-profile/service-tunnel-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "source_vertex_label": "huawei-wlan-system-profile", "dest_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "source_node_path": "/huawei-wlan-system-profile:wlan-system-profile/system-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::wmi-servers::wmi-server", "source_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "dest_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::wmi-servers::wmi-server", "source_node_path": "/wmi-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::service-tunnel-profile-lists::service-tunnel-profile-list", "source_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "dest_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile::service-tunnel-profile-lists::service-tunnel-profile-list", "source_node_path": "/service-tunnel-profile-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wired-port-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-wired-port-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile", "source_vertex_label": "huawei-wlan-wired-port-profile", "dest_vertex_label": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile", "source_node_path": "/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-portlink-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-portlink-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-portlink-profile:wlan-portlink-profile::portlink-profiles::portlink-profile", "source_vertex_label": "huawei-wlan-portlink-profile", "dest_vertex_label": "huawei-wlan-portlink-profile:wlan-portlink-profile::portlink-profiles::portlink-profile", "source_node_path": "/huawei-wlan-portlink-profile:wlan-portlink-profile/portlink-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-capture", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-capture", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-capture:wireless-capture::addresses::address", "source_vertex_label": "huawei-wlan-capture", "dest_vertex_label": "huawei-wlan-capture:wireless-capture::addresses::address", "source_node_path": "/huawei-wlan-capture:wireless-capture/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-capture:wireless-capture::protocol-lists::protocol-list", "source_vertex_label": "huawei-wlan-capture", "dest_vertex_label": "huawei-wlan-capture:wireless-capture::protocol-lists::protocol-list", "source_node_path": "/huawei-wlan-capture:wireless-capture/protocol-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-capwap", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-capwap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-capwap:wlan-capwap::capwap-source::source-interfaces::source-interface", "source_vertex_label": "huawei-wlan-capwap", "dest_vertex_label": "huawei-wlan-capwap:wlan-capwap::capwap-source::source-interfaces::source-interface", "source_node_path": "/huawei-wlan-capwap:wlan-capwap/capwap-source/source-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-wlan-management", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-wlan-management", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-files::ap-type-update-file", "source_vertex_label": "hua<PERSON>-wlan-management", "dest_vertex_label": "huawei-wlan-management:wlan-management::ap-type-update-files::ap-type-update-file", "source_node_path": "/huawei-wlan-management:wlan-management/ap-type-update-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-patchs::ap-type-update-patch", "source_vertex_label": "hua<PERSON>-wlan-management", "dest_vertex_label": "huawei-wlan-management:wlan-management::ap-type-update-patchs::ap-type-update-patch", "source_node_path": "/huawei-wlan-management:wlan-management/ap-type-update-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mobility-group", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-mobility-group", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-mobility-group:wlan-mobility-group::dynamic-mobility-members::dynamic-mobility-member", "source_vertex_label": "huawei-wlan-mobility-group", "dest_vertex_label": "huawei-wlan-mobility-group:wlan-mobility-group::dynamic-mobility-members::dynamic-mobility-member", "source_node_path": "/huawei-wlan-mobility-group:wlan-mobility-group/dynamic-mobility-members", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ppsk", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ppsk", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ppsk:wlan-ppsk::wlan-ppsk-accounts::wlan-ppsk-account", "source_vertex_label": "huawei-wlan-ppsk", "dest_vertex_label": "huawei-wlan-ppsk:wlan-ppsk::wlan-ppsk-accounts::wlan-ppsk-account", "source_node_path": "/huawei-wlan-ppsk:wlan-ppsk/wlan-ppsk-accounts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ppsk:wlan-ppsk::wlan-ppsk-accounts::wlan-ppsk-account::ssid-lists::ssid-list", "source_vertex_label": "huawei-wlan-ppsk:wlan-ppsk::wlan-ppsk-accounts::wlan-ppsk-account", "dest_vertex_label": "huawei-wlan-ppsk:wlan-ppsk::wlan-ppsk-accounts::wlan-ppsk-account::ssid-lists::ssid-list", "source_node_path": "/ssid-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-radio-calibrate", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate:wlan-radio-calibrate::calibrate::sensitivitys::sensitivity", "source_vertex_label": "huawei-wlan-radio-calibrate", "dest_vertex_label": "huawei-wlan-radio-calibrate:wlan-radio-calibrate::calibrate::sensitivitys::sensitivity", "source_node_path": "/huawei-wlan-radio-calibrate:wlan-radio-calibrate/calibrate/sensitivitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-list::ap-lists::ap-list", "source_vertex_label": "huawei-wlan-radio-calibrate", "dest_vertex_label": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-list::ap-lists::ap-list", "source_node_path": "/huawei-wlan-radio-calibrate:calibrate-manual/type/ap-list/ap-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-group-list::ap-group-lists::ap-group-list", "source_vertex_label": "huawei-wlan-radio-calibrate", "dest_vertex_label": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-group-list::ap-group-lists::ap-group-list", "source_node_path": "/huawei-wlan-radio-calibrate:calibrate-manual/type/ap-group-list/ap-group-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-sta", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta:wlan-sta::user-sac-statistics::user-sac-statistic", "source_vertex_label": "huawei-wlan-sta", "dest_vertex_label": "huawei-wlan-sta:wlan-sta::user-sac-statistics::user-sac-statistic", "source_node_path": "/huawei-wlan-sta:wlan-sta/user-sac-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-sta:wlan-sta::user-sac-statistics::user-sac-statistic::app-traffic-statistics::app-traffic-statistic", "source_vertex_label": "huawei-wlan-sta:wlan-sta::user-sac-statistics::user-sac-statistic", "dest_vertex_label": "huawei-wlan-sta:wlan-sta::user-sac-statistics::user-sac-statistic::app-traffic-statistics::app-traffic-statistic", "source_node_path": "/app-traffic-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ztp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ztp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library", "source_vertex_label": "ds", "dest_vertex_label": "ietf-yang-library", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::module-set", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::feature", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::deviation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::schema", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema::module-set", "source_vertex_label": "ietf-yang-library:yang-library::schema", "dest_vertex_label": "ietf-yang-library:yang-library::schema::module-set", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::datastore", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::datastore", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications", "source_vertex_label": "ds", "dest_vertex_label": "nc-notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications:netconf::streams::stream", "source_vertex_label": "nc-notifications", "dest_vertex_label": "nc-notifications:netconf::streams::stream", "source_node_path": "/nc-notifications:netconf/streams", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "notifications", "source_vertex_label": "ds", "dest_vertex_label": "notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]