[{"name": "derivedfromotherscope", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "clause": [{"type": "when", "formula": "derived-from(../np1/F2, 'outA')"}]}, {"name": "F1", "type": "string", "clause": [{"type": "when", "formula": "derived-from(../np1/F2, 'outB')"}]}, {"name": "F2", "type": "string", "clause": [{"type": "when", "formula": "derived-from(../np1/F2, 'outZ')"}]}, {"name": "F3", "type": "string", "clause": [{"type": "when", "formula": "derived-from(../np1/F2, 'outNotExist')"}]}, {"name": "F4", "type": "string", "clause": [{"type": "when", "formula": "derived-from(../np1/F2, 'c')"}]}, {"name": "F5", "type": "string", "clause": [{"type": "when", "formula": "derived-from-or-self(../np1/F2, 'c')"}]}, {"name": "F6", "type": "string", "clause": [{"type": "when", "formula": "derived-from-or-self(../np1/F2, 'outTop')"}]}, {"name": "F7", "type": "string", "clause": [{"type": "when", "formula": "derived-from-or-self(../np1/F2, 'outNotExist')"}]}, {"name": "np1", "type": "container", "fields": [{"name": "F0", "type": "string", "default": "1"}, {"name": "F1", "type": "string"}, {"name": "F2", "type": "identity", "enumerate_identity": "IdenTest2", "enumerate": [{"name": "A", "value": -2, "derived-paths": [{"derived-path": "A"}]}, {"name": "a1", "value": 0, "derived-paths": [{"derived-path": "A/a1"}]}, {"name": "a2", "value": 6, "derived-paths": [{"derived-path": "A/a2"}]}, {"name": "b", "value": 8, "derived-paths": [{"derived-path": "A/a1/b"}]}, {"name": "c", "value": 9, "derived-paths": [{"derived-path": "A/a2/c"}, {"derived-path": "outZ/c"}, {"derived-path": "outTop/outA/outB/a1/c"}]}], "default": "c"}]}, {"name": "p1", "type": "container", "presence": true, "fields": [{"name": "F0", "type": "string", "default": "1"}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": false}, {"name": "np2", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]