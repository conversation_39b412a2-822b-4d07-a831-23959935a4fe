[{"name": "rangetest", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string"}, {"name": "F1", "type": "string", "clause": [{"type": "when", "formula": "derived-from(../F2, 'l1')"}, {"type": "when", "formula": "derived-from(../F2, 'l2')"}, {"type": "when", "formula": "derived-from(../F2, 'l3')"}]}, {"name": "F2", "type": "identity", "enumerate_identity": "F2_Identity", "enumerate": [{"name": "l1", "value": -100, "derived-paths": [{"derived-path": "l1"}]}, {"name": "l2", "value": 100, "derived-paths": [{"derived-path": "l1/l2"}]}, {"name": "l3", "value": 1000, "derived-paths": [{"derived-path": "l1/l2/l3"}]}, {"name": "l4", "value": 10000, "derived-paths": [{"derived-path": "l1/l2/l3/l4"}]}], "default": "l3"}, {"name": "F3", "type": "string", "clause": [{"type": "when", "formula": "../LL1/EnumInList = 'BB'"}]}, {"name": "np1", "type": "container", "fields": [{"name": "F0", "type": "string", "default": "1"}, {"name": "F1", "type": "string"}]}, {"name": "p1", "type": "container", "presence": true, "fields": [{"name": "F0", "type": "string", "default": "1"}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "LL1", "type": "leaf-list", "clause": [{"type": "when", "formula": "/rangetest/LL1[EnumInList = 'CC']/EnumInList = 'CC'"}], "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "EnumInList", "type": "enum", "enumerate_identity": "EnumListTest", "enumerate": [{"name": "AA", "value": -100}, {"name": "BB", "value": 100}, {"name": "CC", "value": 1000}], "default": ["AA", 100]}], "keys": [{"name": "k0", "fields": [":pid", "EnumInList"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": false}, {"name": "np2", "type": "container", "fields": []}, {"name": "EnumInList", "type": "enum", "enumerate_identity": "EnumListTest", "enumerate": [{"name": "AA", "value": -100}, {"name": "BB", "value": 100}], "default": "AA"}], "keys": [{"name": "k0", "fields": [":pid", "EnumInList"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L2", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]