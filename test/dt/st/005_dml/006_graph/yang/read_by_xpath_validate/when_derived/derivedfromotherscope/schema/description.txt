    └── np-leaflist [NP]
        ├── F0 [string]
        ├── np1 [NP]
        │   ├── F0 [string] default: 1 nullable: False
        │   └── F1 [string] nullable: False
        ├── p1 [P]
        │   └── F0 [string] default: 1 nullable: False
        ├── L1 [list]
        │   ├── F0 [string] nullable: False
        │   └── np2 [NP]
        │       └── L2 [list]
        │           ├── F0 [string] nullable: False
        │           ├── ch1 [choice]
        │           │   └── ca1 [case] default: True
        │           │       ├── F0 [string] default: 1
        │           │       └── F1 [string]
        │           └── ch2 [choice]
        │               ├── ca1 [case] default: True
        │               │   └── F0 [string] default: 1
        │               └── ca2 [case]
        │                   └── F0 [string]
        └── LL1 [leaf-list] [when ../F0 = '3']
            └── F0 [uint32] default: [1, 2, 3]
