[{"name": "np", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "p1", "type": "container", "presence": true, "fields": []}, {"name": "np1", "type": "container", "fields": []}, {"name": "F1", "type": "string"}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "np::np1::list1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": false}, {"name": "F1", "type": "string", "nullable": false}, {"name": "p2", "type": "container", "presence": true, "clause": [{"type": "when", "formula": "../F1 = '599'"}], "fields": [{"name": "ch0", "type": "choice", "nullable": false, "fields": [{"name": "ca1", "type": "case", "fields": [{"name": "F2", "type": "string"}]}, {"name": "ca2", "type": "case", "fields": [{"name": "F3", "type": "string"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]