[{"name": "huawei-aaa:aaa", "type": "container", "presence": true, "clause": [{"type": "when", "formula": "/huawei-aaa:aaa/lam/users/huawei-aaa:aaa::lam::users::user/password = 'hw123' or /huawei-aaa:aaa/alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry/user-id > 10"}, {"type": "must", "formula": "./alive-user-qrys and ./lam and ./lam/users"}, {"type": "must", "formula": "./alive-user-qrys"}], "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alive-user-qrys", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "lam", "type": "container", "presence": true, "fields": [{"name": "users", "type": "container", "presence": true, "fields": []}]}], "keys": [{"node": "huawei-aaa:aaa", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "user-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "access-time", "type": "string", "is_config": false}], "keys": [{"node": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "name": "k0", "fields": [":pid", "user-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::lam::users::user", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "group-name", "type": "string", "default": "admin"}, {"name": "password", "type": "string"}, {"name": "level", "type": "uint32"}, {"name": "service-terminal", "type": "boolean", "default": false}, {"name": "service-api", "type": "uint32", "default": 1}, {"name": "password-force-change", "type": "boolean", "default": true}], "keys": [{"node": "huawei-aaa:aaa::lam::users::user", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]