[{"name": "list-leaflist-expand-upward", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "default": 1}], "keys": [{"node": "list-leaflist-expand-upward", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L1", "type": "list", "clause": [{"type": "when", "formula": "./L2[F2=3]/L3/../../L2/F2 = 2"}], "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "default": 3}], "keys": [{"node": "L1", "name": "k0", "fields": [":pid", "F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L2", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "default": 2}], "keys": [{"node": "L2", "name": "k0", "fields": [":pid", "F2"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L3", "type": "leaf-list", "clause": [{"type": "when", "formula": "../../L2[F2=3]/L3[F3=3]/../F2"}], "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "default": [3, 6, 9]}], "keys": [{"node": "L3", "name": "k0", "fields": [":pid", "F3"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]