[{"name": "npcontainer-list-leaflist-no-data_L1", "source_vertex_label": "npcontainer-list-leaflist-no-data", "dest_vertex_label": "L1", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "L1_L2", "source_vertex_label": "L1", "dest_vertex_label": "L2", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "npcontainer-list-leaflist-no-data_L3", "source_vertex_label": "npcontainer-list-leaflist-no-data", "dest_vertex_label": "L3", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]