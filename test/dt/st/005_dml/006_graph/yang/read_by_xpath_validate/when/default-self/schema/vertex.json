[{"name": "default-self", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}], "keys": [{"node": "default-self", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32", "default": 997, "clause": [{"type": "when", "formula": "count(/default-self/L1/F1)=4"}]}], "keys": [{"node": "L1", "name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]