[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"type": "container", "presence": true, "name": "root::company", "fields": [{"name": "F0", "type": "int32", "nullable": true, "default": 0}]}, {"type": "container", "presence": true, "name": "root::branch2", "fields": [{"name": "F0", "type": "int32"}]}], "keys": [{"node": "root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "root::team", "min-elements": 2, "max-elements": 4, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "members", "presence": true, "type": "container", "fields": []}], "keys": [{"node": "root::team", "name": "team.PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "root::company::employee", "min-elements": 0, "max-elements": 5, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}], "keys": [{"node": "root::company::employee", "name": "employee.PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "root::branch2::desktop", "min-elements": 0, "max-elements": 3, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}], "keys": [{"node": "root::branch2::desktop", "name": "desktop.PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "root::team::members::member", "min-elements": 0, "max-elements": 5, "clause": [{"type": "when", "formula": "./F0>2"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false, "default": [1, 2, 3]}], "keys": [{"node": "root::team::members::member", "name": "member.PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]