[{"name": "huawei-aaa:aaa", "type": "container", "presence": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alive-user-qrys", "type": "container", "is_config": false, "presence": true, "fields": [], "clause": [{"type": "must", "formula": "not(/huawei-aaa:aaa)"}, {"type": "must", "formula": "count(/huawei-aaa:aaa) = 1"}, {"type": "must", "formula": "string(/huawei-aaa:aaa) = 'aaa'"}, {"type": "must", "formula": "string() = 'aaa'"}, {"type": "must", "formula": "number(/huawei-aaa:aaa) = 1"}, {"type": "must", "formula": "number() = 1"}, {"type": "must", "formula": "translate('aaa', 'a', 'b') = 'bbb'"}, {"type": "must", "formula": "starts-with('aaa', 'a')"}, {"type": "when", "formula": "not(current())"}, {"type": "when", "formula": "not(true())"}, {"type": "when", "formula": "not(false())"}]}, {"name": "lam", "type": "container", "presence": true, "fields": [{"name": "users", "type": "container", "presence": true, "fields": []}]}, {"name": "user-string", "type": "string", "nullable": true}, {"name": "user-number", "type": "uint32", "nullable": true}, {"name": "user-str-startwith-1", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "starts-with(../user-string, '1')"}]}, {"name": "user-str-number-111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "number(../user-string) = 111"}]}, {"name": "user-str-number-string-111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "string(number(../user-string)) = '111'"}]}, {"name": "user-str-number-string-stwith-111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "starts-with(string(number(../user-string)), '1')"}]}, {"name": "user-str-translate-222", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "translate((../user-string), '1', '2') = '222'"}]}], "keys": [{"node": "huawei-aaa:aaa", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "user-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "access-time", "type": "string", "is_config": false}], "keys": [{"node": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "name": "k0", "fields": [":pid", "user-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::lam::users::user", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "group-name", "type": "string", "default": "admin"}, {"name": "password", "type": "string"}, {"name": "level", "type": "uint32"}, {"name": "service-terminal", "type": "boolean", "default": false}, {"name": "service-api", "type": "boolean", "default": false}, {"name": "password-force-change", "type": "boolean", "default": true}], "keys": [{"node": "huawei-aaa:aaa::lam::users::user", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]