└── huawei-acl:acl [P]
    └── groups [P]
        └── huawei-acl:acl::groups::group(group) [list]
            ├── identity [string] nullable: False
            ├── type [string]
            ├── step [uint32] default: 5
            ├── description [string]
            ├── number [uint32]
            ├── rule-basics [P]
            │   └── huawei-acl:acl::groups::group::rule-basics::rule-basic(rule-basic) [list]
            │       ├── name [string] nullable: False
            │       ├── id [uint32]
            │       ├── action [string] nullable: False
            │       ├── active-status [string] default: not-ready
            │       ├── source-ipaddr [string] [must (../source-ipaddr and ../source-wild) or (not(../source-ipaddr) and not(../source-wild))]
            │       ├── source-wild [string] [must (../source-ipaddr and ../source-wild) or (not(../source-ipaddr) and not(../source-wild))]
            │       ├── fragment-type [string]
            │       ├── time-range-name [string]
            │       ├── description [string]
            │       └── priority [uint32]
            ├── rule-advances [P]
            └── rule-ethernets [P]
