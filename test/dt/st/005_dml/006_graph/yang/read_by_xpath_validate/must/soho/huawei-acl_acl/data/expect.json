{":id": 1, "groups": {"group": [{":id": 1, ":pid": 1, "identity": "2000", "@step": 5, "rule-basics": {"rule-basic": [{":id": 1, ":pid": 1, "name": "rule1", "id": 5, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 2, ":pid": 1, "name": "rule2", "id": 10, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 3, ":pid": 1, "name": "rule3", "id": 15, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 4, ":pid": 1, "name": "rule4", "id": 20, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {":id": 2, ":pid": 1, "identity": "2001", "@step": 5, "rule-basics": {"rule-basic": [{":id": 5, ":pid": 2, "name": "rule1", "id": 5, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 6, ":pid": 2, "name": "rule2", "id": 10, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 7, ":pid": 2, "name": "rule3", "id": 15, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 8, ":pid": 2, "name": "rule4", "id": 20, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {":id": 3, ":pid": 1, "identity": "2002", "@step": 5, "rule-basics": {"rule-basic": [{":id": 9, ":pid": 3, "name": "rule1", "id": 5, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 10, ":pid": 3, "name": "rule2", "id": 10, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 11, ":pid": 3, "name": "rule3", "id": 15, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {":id": 12, ":pid": 3, "name": "rule4", "id": 20, "action": "permit", "@active-status": "not-ready", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}]}}