{":id": 1, "sensor-groups": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group": [{":id": 1, ":pid": 1, "sensor-group-id": "name3", "config": {"sensor-group-id": "name3"}}]}, "subscriptions": {"persistent": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription": [{":id": 1, ":pid": 1, "subscription-name": "notImportant", "config": {"subscription-name": "notImportant", "local-source-address": "notImportant"}, "sensor-profiles": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile": [{":id": 1, ":pid": 1, "sensor-group": "name2", "config": {"sensor-group": "name1", "@sample-interval": "60000"}}]}}]}}}