[{"name": "root_root::L1", "source_vertex_label": "root", "dest_vertex_label": "root::L1", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "root_root::L2", "source_vertex_label": "root", "dest_vertex_label": "root::L2", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "root_root::L3", "source_vertex_label": "root", "dest_vertex_label": "root::L3", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "root_root::L4", "source_vertex_label": "root", "dest_vertex_label": "root::L4", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]