[{"name": "T0_T1", "source_vertex_label": "T0", "dest_vertex_label": "T0::T1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T0_T2Choice", "source_vertex_label": "T0", "dest_vertex_label": "T0::T2Choice", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T0_T3", "source_vertex_label": "T0", "dest_vertex_label": "T0::T3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T2Choice_T21Case", "source_vertex_label": "T0::T2Choice", "dest_vertex_label": "T0::T2Choice::T21Case", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T2Choice_T22Case", "source_vertex_label": "T0::T2Choice", "dest_vertex_label": "T0::T2Choice::T22Case", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T2Choice_T23Case", "source_vertex_label": "T0::T2Choice", "dest_vertex_label": "T0::T2Choice::T23Case", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T3_T31", "source_vertex_label": "T0::T3", "dest_vertex_label": "T0::T3::T31", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T3_T32", "source_vertex_label": "T0::T3", "dest_vertex_label": "T0::T3::T32", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T3_T33Choice", "source_vertex_label": "T0::T3", "dest_vertex_label": "T0::T3::T33Choice", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T33Choice_T331Case", "source_vertex_label": "T0::T3::T33Choice", "dest_vertex_label": "T0::T3::T33Choice::T331Case", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T21Case_T211", "source_vertex_label": "T0::T2Choice::T21Case", "dest_vertex_label": "T0::T2Choice::T21Case::T211", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T21Case_T212", "source_vertex_label": "T0::T2Choice::T21Case", "dest_vertex_label": "T0::T2Choice::T21Case::T212", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T21Case_T213Choice", "source_vertex_label": "T0::T2Choice::T21Case", "dest_vertex_label": "T0::T2Choice::T21Case::T213Choice", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T213Choice_T2131Case", "source_vertex_label": "T0::T2Choice::T21Case::T213Choice", "dest_vertex_label": "T0::T2Choice::T21Case::T213Choice::T2131Case", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]