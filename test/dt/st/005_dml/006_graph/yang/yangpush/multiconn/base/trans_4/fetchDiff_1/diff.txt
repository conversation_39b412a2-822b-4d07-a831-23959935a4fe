ietf-interfaces:interfaces:update[(<PERSON><PERSON><PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
ietf-interfaces:interfaces.if:interface.1:update[(pri<PERSON><PERSON>(PID:1,name:port.0.13.1.2.eth.1), pre<PERSON><PERSON>(PID:1,name:channel-partition.0.16.15)),(pri<PERSON><PERSON>(PID:1,name:port.0.13.1.2.eth.1), pre<PERSON><PERSON>(PID:1,name:channel-partition.0.16.15))]
if:interface.1.enabled:update(false,true)
ietf-interfaces:interfaces.if:interface.1:create[(pri<PERSON><PERSON>(PID:1,name:channel-group.0.168.3), preKey(PID:1,name:channel-group.0.168.2)),(NULL)]
if:interface.1.ID:create(9)
if:interface.1.type:create(bbf-xpon-if-type:channel-group)
if:interface.1.enabled:create(true)
if:interface.1.channel-group:create
channel-group.polling-period:create(100)
ietf-interfaces:interfaces.if:interface.1:create[(p<PERSON><PERSON><PERSON>(PID:1,name:channel-partition.0.168.3.gpon), pre<PERSON><PERSON>(PID:1,name:channel-group.0.168.3)),(NULL)]
if:interface.1.ID:create(10)
if:interface.1.type:create(bbf-xpon-if-type:channel-partition)
if:interface.1.enabled:create(true)
if:interface.1.channel-partition:create
channel-partition.channel-group-ref:create(channel-group.0.168.3)
channel-partition.downstream-fec:create(true)
channel-partition.closest-onu-distance:create(0)
channel-partition.maximum-differential-xpon-distance:create(20)
channel-partition.multicast-aes-indicator:create(false)
