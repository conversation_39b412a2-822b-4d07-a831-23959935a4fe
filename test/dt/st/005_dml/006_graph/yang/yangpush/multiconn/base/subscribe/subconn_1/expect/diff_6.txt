ietf-interfaces:interfaces:update[(p<PERSON><PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
ietf-interfaces:interfaces.if:interface.1:update[(pri<PERSON><PERSON>(PID:1,name:channel-pair.0.16.15.gpon), pre<PERSON>ey(PID:1,name:channel-partition.0.16.15)),(pri<PERSON><PERSON>(PID:1,name:channel-pair.0.16.15.gpon), pre<PERSON>ey(PID:1,name:port.0.13.1.2.eth.1))]
ietf-interfaces:interfaces.if:interface.1:remove[(NULL),(pri<PERSON><PERSON>(PID:1,name:port.0.13.1.2.eth.1), pre<PERSON>ey(PID:1,name:channel-partition.0.16.15))]
if:interface.1.ID:remove(4)
if:interface.1.type:remove(iana-if-type:ethernetCsmacd)
if:interface.1.enabled:remove(false)
if:interface.1.ethernet:remove
ethernet.duplex:remove(full)
ethernet.auto-negotiation:remove
auto-negotiation.enable:remove(true)
ethernet.flow-control:remove
flow-control.force-flow-control:remove(false)
ethernet.logical:remove
logical.tx-auto-off:remove
tx-auto-off.enabled:remove(false)
ethernet.ethernet-frame:remove
ethernet-frame.jumbo-frame:remove(false)
ethernet-frame.mtu:remove(2052)
if:interface.1.mac-learning:remove
mac-learning.max-number-mac-addresses:remove(4294967295)
mac-learning.number-committed-mac-addresses:remove(1)
mac-learning.mac-learning-enable:remove(true)
mac-learning.mac-learning-failure-action:remove(forward)
if:interface.1.qos-policies:remove
qos-policies.policing:remove
policing.statistics:remove
statistics.enabled:remove(false)
if:interface.1.bridge-port:remove
bridge-port.default-priority:remove(0)
bridge-port.pcp-selection:remove(8P0D)
bridge-port.use-dei:remove(false)
bridge-port.drop-encoding:remove(false)
bridge-port.service-access-priority-selection:remove(false)
bridge-port.priority-regeneration:remove
priority-regeneration.priority0:remove(0)
priority-regeneration.priority1:remove(1)
priority-regeneration.priority2:remove(2)
priority-regeneration.priority3:remove(3)
priority-regeneration.priority4:remove(4)
priority-regeneration.priority5:remove(5)
priority-regeneration.priority6:remove(6)
priority-regeneration.priority7:remove(7)
bridge-port.service-access-priority:remove
service-access-priority.priority0:remove(0)
service-access-priority.priority1:remove(1)
service-access-priority.priority2:remove(2)
service-access-priority.priority3:remove(3)
service-access-priority.priority4:remove(4)
service-access-priority.priority5:remove(5)
service-access-priority.priority6:remove(6)
service-access-priority.priority7:remove(7)
if:interface.1.aggregator:remove
aggregator.work-mode:remove(static)
aggregator.fast-period:remove(1)
aggregator.slow-period:remove(30)
aggregator.max-link-number:remove(no-limit)
aggregator.least-link-number:remove(no-limit)
aggregator.forward-mode:remove(ingress)
aggregator.preempt-enabled:remove(false)
aggregator.preempt-delay:remove(0)
if:interface.1.aggregation-port:remove
aggregation-port.aggregation-port-lacp:remove
aggregation-port-lacp.actor-port-priority:remove(16384)
if:interface.1.bbf-if-port-ref:port-layer-if.1:remove[(NULL),(priKey(PID:4,port-layer-if:port.0.13.1.2.eth.1))]
