{"label_name": "root0", "comment": "root0 subscription", "subs_path": [{"xpath": "L1[empty0=\"\"]/empty0", "states": [{"type": "create"}]}, {"xpath": "L1[empty0='']", "states": [{"type": "create"}]}, {"xpath": "L1[union0='']/union0", "states": [{"type": "create"}]}, {"xpath": "L1[union0=123]/union0", "states": [{"type": "create"}, {"type": "remove"}]}, {"xpath": "L1[union0=true]", "states": [{"type": "create"}, {"type": "remove"}]}, {"xpath": "L1[union0=\"union0\"]", "states": [{"type": "create"}, {"type": "remove"}]}], "events": [{"type": "diff"}]}