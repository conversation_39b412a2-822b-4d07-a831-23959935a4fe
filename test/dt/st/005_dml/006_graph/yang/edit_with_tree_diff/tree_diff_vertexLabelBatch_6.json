[{"type": "container", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"type": "choice", "name": "T1Choice", "fields": [{"type": "case", "name": "T11Case", "fields": [{"name": "F0", "type": "uint32", "nullable": false}]}]}], "keys": [{"node": "T0", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"type": "container", "name": "P1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}]}], "keys": [{"node": "T2", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]