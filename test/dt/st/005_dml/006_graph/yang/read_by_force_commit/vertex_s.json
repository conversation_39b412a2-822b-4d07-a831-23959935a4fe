[{"type": "container", "name": "root0", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "empty0", "type": "empty", "nullable": true}, {"name": "union0", "type": "union", "union_types": ["uint32", "int32", "string", "empty"]}, {"type": "container", "name": "C1", "fields": [{"name": "empty1", "type": "empty", "nullable": true}, {"name": "union1", "type": "union", "union_types": ["uint64", "int32", "string", "empty"]}]}], "keys": [{"name": "PK", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "empty0", "type": "empty", "nullable": false}, {"name": "union0", "type": "union", "union_types": ["uint32", "int32", "string", "boolean"]}], "keys": [{"name": "k0", "fields": [":pid", "empty0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "empty0", "type": "empty", "nullable": false}, {"name": "union0", "type": "union", "union_types": ["uint32", "int32", "string", "boolean", "empty"]}, {"name": "union1", "type": "union", "union_types": ["uint32", "int32"]}, {"name": "choice", "type": "choice", "fields": [{"name": "caseA", "type": "case", "fields": [{"name": "empty1", "type": "empty", "nullable": false}, {"name": "union1", "type": "union", "union_types": ["uint32", "string", "boolean", "empty"]}]}, {"name": "caseB", "type": "case", "fields": [{"name": "empty2", "type": "empty", "nullable": false}, {"name": "union2", "type": "union", "union_types": ["uint32", "string", "boolean", "empty"]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]