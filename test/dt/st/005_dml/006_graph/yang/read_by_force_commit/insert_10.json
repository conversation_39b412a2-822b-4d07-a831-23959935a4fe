{"interfaces": {"if:interface.1": [{"name": "ethernetCsmacd.0.10.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "statistics-accumulation": "system-conditional", "default-vlan": 1, "clock-port-config": {"esmc-send-disable": false}, "ethernet": {"duplex": "full", "speed": 10.0, "route-switch": "unconcern", "auto-negotiation": {"enable": false}, "flow-control": {"force-flow-control": false}, "physical": {"mdi": "not-supported", "port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}, "qos-policies": {"policing": {"statistics": {"enabled": false}}}, "interface-usage": {"interface-usage": "network-port"}, "mac-learning": {"max-number-mac-addresses": 4294967295, "number-committed-mac-addresses": 1, "mac-learning-enable": true, "mac-learning-failure-action": "forward"}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.10.2"}]}]}}