{"interfaces": {"if:interface.1": [{"name": "gpon.0.1.0", "type": "iana-if-type:gpon", "enabled": true, "statistics-accumulation": "system-conditional", "clock-port-config": {"esmc-send-disable": false}, "xpon-port": {"ont-auto-find-switch": true, "ont-password-renew-interval": "1446", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "max-guaranteed-bandwidth": "unlimited", "dba-low-latency": "disable", "ont-online-power-threshold": "unlimited", "regeneration-switch": false}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.1.0"}]}, {"name": "channel-group.0.1.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "statistics-accumulation": "system-conditional", "clock-port-config": {"esmc-send-disable": false}, "channel-group": {"polling-period": 100}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.1.0"}]}, {"name": "channel-partition.0.1.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "statistics-accumulation": "system-conditional", "clock-port-config": {"esmc-send-disable": false}, "channel-partition": {"channel-group-ref": "channel-group.0.1.0", "channel-partition-index": 0, "downstream-fec": false, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.1.0"}]}, {"name": "channel-termination.0.1.0.xgs", "type": "bbf-xpon-if-type:channel-termination", "enabled": false, "statistics-accumulation": "system-conditional", "clock-port-config": {"esmc-send-disable": false}, "channel-termination": {"channel-termination-type": "bbf-xpon-types:xgs", "laser-switch": true, "multicast-encrypt-mode": "disable"}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.1.0"}]}, {"name": "gpon.0.1.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "max-guaranteed-bandwidth": "unlimited", "dba-low-latency": "disable", "ont-online-power-threshold": "unlimited", "regeneration-switch": false}, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.1.1"}]}, {"name": "channel-group.0.1.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "statistics-accumulation": "system-conditional", "clock-port-config": {"esmc-send-disable": false}, "channel-group": {"polling-period": 100}}, {"name": "ethernetCsmacd.0.9.0", "type": "iana-if-type:fastdsl", "enabled": false, "bbf-if-port-ref:port-layer-if.1": [{"port-layer-if": "port.0.1.0"}]}]}}