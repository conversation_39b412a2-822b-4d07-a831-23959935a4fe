[{"type": "container", "name": "root", "is_config": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "is_config": false, "type": "int32"}], "keys": [{"node": "root", "name": "root.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}], "keys": [{"node": "C1", "name": "C1.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C2", "is_config": false, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}], "keys": [{"node": "C2", "name": "C2.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "mk", "type": "int32", "nullable": false}, {"name": "F0", "type": "int32"}], "keys": [{"node": "L1", "name": "L1.PK", "fields": ["PID", "mk"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "is_config": false, "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}], "keys": [{"node": "C3", "name": "C3.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]