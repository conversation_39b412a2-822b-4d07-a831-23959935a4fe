[{"version": "2.0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}], "type": "container", "schema_version": 0, "keys": [{"name": "PK", "node": "root", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "name": "root"}, {"name": "root::sec-policy", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::sec-policy::vsys::default-policy", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "action", "type": "string"}, {"name": "policylog", "type": "string"}, {"name": "sessionlog", "type": "string"}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::default-policy", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::default-policy::global-ip", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "destination", "type": "string"}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::default-policy::global-ip", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "desc", "type": "string"}, {"name": "parent-group", "type": "string"}, {"name": "time-range", "type": "string"}, {"name": "acl-number", "type": "string"}, {"name": "vlan-id", "type": "string"}, {"name": "policy-log", "type": "string"}, {"name": "session-log", "type": "string"}, {"name": "session-aging-time", "type": "string"}, {"name": "enable", "type": "string"}, {"name": "action", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-zone", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-zone", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-zone", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-zone", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::source-ip", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-set", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-set", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4-range", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string"}, {"name": "end-ipv4", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4-range", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv4", "end-ipv4"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6-range", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv6", "type": "string"}, {"name": "end-ipv6", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6-range", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv6", "end-ipv6"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-set-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-set-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4-range-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string"}, {"name": "end-ipv4", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv4-range-exclude", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv4", "end-ipv4"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6-range-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv6", "type": "string"}, {"name": "end-ipv6", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-ipv6-range-exclude", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv6", "end-ipv6"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::address-mac", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::address-mac", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::region", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::region", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::region-group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::region-group", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-ip::domain-set", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-ip::domain-set", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::destination-ip", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-set", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-set", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4-range", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string"}, {"name": "end-ipv4", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4-range", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv4", "end-ipv4"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6-range", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv6", "type": "string"}, {"name": "end-ipv6", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6-range", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv6", "end-ipv6"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-set-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-set-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4-range-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string"}, {"name": "end-ipv4", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv4-range-exclude", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv4", "end-ipv4"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6-range-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-ipv6", "type": "string"}, {"name": "end-ipv6", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-ipv6-range-exclude", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "start-ipv6", "end-ipv6"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-mac", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::address-mac", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::region", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::region", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::region-group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::region-group", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::destination-ip::domain-set", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::destination-ip::domain-set", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-object", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-object", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::tcp", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "source-port", "type": "string"}, {"name": "dest-port", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::tcp", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "source-port", "dest-port"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::udp", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "source-port", "type": "string"}, {"name": "dest-port", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::udp", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "source-port", "dest-port"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::sctp", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "source-port", "type": "string"}, {"name": "dest-port", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::sctp", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "source-port", "dest-port"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp-item", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp-item", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp-item::icmp-name", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp-item::icmp-name", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp-item::icmp-type-code", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "icmp-type-number", "type": "string"}, {"name": "icmp-code-number", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp-item::icmp-type-code", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "icmp-type-number", "icmp-code-number"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp6-item", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp6-item", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp6-item::icmp6-name", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp6-item::icmp6-name", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp6-item::icmp6-type-code", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "icmp-type-number", "type": "string"}, {"name": "icmp-code-number", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::icmp6-item::icmp6-type-code", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "icmp-type-number", "icmp-code-number"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::protocol", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items::protocol", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items::protocol::protocol-id", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items::protocol::protocol-id", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-object-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-object-exclude", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::tcp", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "source-port", "type": "string"}, {"name": "dest-port", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::tcp", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "source-port", "dest-port"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::udp", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "source-port", "type": "string"}, {"name": "dest-port", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::udp", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "source-port", "dest-port"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::sctp", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "source-port", "type": "string"}, {"name": "dest-port", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::sctp", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "source-port", "dest-port"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp-item", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp-item", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp-item::icmp-name", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp-item::icmp-name", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp-item::icmp-type-code", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "icmp-type-number", "type": "string"}, {"name": "icmp-code-number", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp-item::icmp-type-code", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "icmp-type-number", "icmp-code-number"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp6-item", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp6-item", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp6-item::icmp6-name", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp6-item::icmp6-name", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp6-item::icmp6-type-code", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "icmp-type-number", "type": "string"}, {"name": "icmp-code-number", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::icmp6-item::icmp6-type-code", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "icmp-type-number", "icmp-code-number"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::protocol", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::protocol", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::protocol::protocol-id", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::service::service-items-exclude::protocol::protocol-id", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::application", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application::application-object", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::application::application-object", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application::application-group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::application::application-group", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application::application-label", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::application::application-label", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application::category", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::application::category", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application::category::application-category", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "application-subcategory", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::application::category::application-category", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name", "application-subcategory"]}]}, {"name": "root::sec-policy::vsys::static-policy::rule::application::application-software", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::application::application-software", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-user", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::source-user", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-user::users", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-user::users", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-user::user-group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-user::user-group", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::source-user::security-group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::source-user::security-group", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::url-category", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::url-category", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::url-category::pre-defined-category", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::url-category::pre-defined-category", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::url-category::user-defined-category", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::url-category::user-defined-category", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::profile", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "av-profile", "type": "string"}, {"name": "ips-profile", "type": "string"}, {"name": "url-profile", "type": "string"}, {"name": "fileblock-profile", "type": "string"}, {"name": "datafilter-profile", "type": "string"}, {"name": "appctrl-profile", "type": "string"}, {"name": "mailfilter-profile", "type": "string"}, {"name": "anti-apt-profile", "type": "string"}, {"name": "dnsfilter-profile", "type": "string"}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::profile", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::long-connection", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "enable-flag", "type": "string"}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy::rule::long-connection", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy::rule::send-deny-packet", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string", "nullable": false}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy::rule::send-deny-packet", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy-group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy-group", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::sec-policy::vsys::static-policy-group::group", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "enable", "type": "string"}, {"name": "description", "type": "string"}], "type": "list", "keys": [{"name": "PK", "node": "root::sec-policy::vsys::static-policy-group::group", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::sec-policy::vsys::static-policy-group::group::rule-range", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "start-rule", "type": "string"}, {"name": "end-rule", "type": "string"}], "type": "container", "keys": [{"node": "root::sec-policy::vsys::static-policy-group::group::rule-range", "name": "PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]