[{"name": "T0_T1", "source_vertex_label": "T0", "comment": "T0 is root", "dest_vertex_label": "T0::T1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T0_T2", "source_vertex_label": "T0", "dest_vertex_label": "T0::T2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T2_T3", "source_vertex_label": "T0::T2", "dest_vertex_label": "T0::T2::T3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T2_T4", "source_vertex_label": "T0::T2", "dest_vertex_label": "T0::T2::T4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T3_ChoiceA", "source_vertex_label": "T0::T2::T3", "dest_vertex_label": "T0::T2::T3::choiceA", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "choiceA_caseA", "source_vertex_label": "T0::T2::T3::choiceA", "dest_vertex_label": "T0::T2::T3::choiceA::caseA", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "choiceA_caseB", "source_vertex_label": "T0::T2::T3::choiceA", "dest_vertex_label": "T0::T2::T3::choiceA::caseB", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "caseB_T9", "source_vertex_label": "T0::T2::T3::choiceA::caseB", "dest_vertex_label": "T0::T2::T3::choiceA::caseB::T9", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "caseB_choiceB", "source_vertex_label": "T0::T2::T3::choiceA::caseB", "dest_vertex_label": "T0::T2::T3::choiceA::caseB::choiceB", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "choiceB_caseC", "source_vertex_label": "T0::T2::T3::choiceA::caseB::choiceB", "dest_vertex_label": "T0::T2::T3::choiceA::caseB::choiceB::caseC", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "choiceB_caseD", "source_vertex_label": "T0::T2::T3::choiceA::caseB::choiceB", "dest_vertex_label": "T0::T2::T3::choiceA::caseB::choiceB::caseD", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T4_T5", "source_vertex_label": "T0::T2::T4", "dest_vertex_label": "T0::T2::T4::T5", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T4_T6", "source_vertex_label": "T0::T2::T4", "dest_vertex_label": "T0::T2::T4::T6", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T1_T7", "source_vertex_label": "T0::T1", "dest_vertex_label": "T0::T1::T7", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "T5_T8", "source_vertex_label": "T0::T2::T4::T5", "dest_vertex_label": "T0::T2::T4::T5::T8", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]