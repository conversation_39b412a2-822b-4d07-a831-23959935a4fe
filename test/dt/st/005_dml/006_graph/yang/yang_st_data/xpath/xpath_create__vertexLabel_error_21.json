[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "C1", "type": "container", "fields": [{"name": "F0", "type": "int32"}, {"name": "name", "type": "string", "clause": [{"type": "must", "formula": "F0 >= ../../xxx/F0"}, {"type": "when", "formula": "F0 >= ../../xxx/F0"}]}], "clause": [{"type": "must", "formula": "F0 >= ../../xxx/F0"}, {"type": "when", "formula": "F0 >= ../../xxx/F0"}]}], "clause": [{"type": "must", "formula": "F0 >= ../../xxx/F0"}, {"type": "when", "formula": "F0 >= ../../xxx/F0"}], "keys": [{"node": "root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "listChoice1", "type": "choice", "fields": [{"name": "listCase1A", "type": "case", "default": true, "fields": [{"name": "F0", "type": "int32"}, {"name": "name", "type": "string"}], "clause": [{"type": "when", "formula": "F0 >= ../../xxx/F0"}, {"type": "leafref", "formula": "F0 >= ../../xxx/F0"}]}], "clause": [{"type": "when", "formula": "F0 >= ../../xxx/F0"}]}], "clause": [{"type": "must", "formula": "F0 >= ../../xxx/F0"}, {"type": "mandatory", "formula": "F0 >= ../../xxx/F0"}], "keys": [{"node": "L1", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]