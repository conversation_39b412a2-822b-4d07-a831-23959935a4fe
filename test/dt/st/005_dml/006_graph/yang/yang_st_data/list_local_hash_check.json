[{"type": "record", "name": "T0::T2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32"}], "keys": [{"node": "T0::T2", "name": "T2.F1", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T0::T2", "name": "T2.F2", "fields": ["PID", "F2"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}]