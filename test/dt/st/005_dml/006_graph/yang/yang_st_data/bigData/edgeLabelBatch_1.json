[{"name": "root_company_employee", "source_vertex_label": "root", "dest_vertex_label": "root::company::employee", "source_node_path": "/company", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "employee_project", "source_vertex_label": "root::company::employee", "dest_vertex_label": "root::company::employee::project", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_validate", "source_vertex_label": "root", "dest_vertex_label": "root::validate", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "validate_validate1", "source_vertex_label": "root::validate", "dest_vertex_label": "root::validate::validate1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_netconf", "source_vertex_label": "root", "dest_vertex_label": "root::netconf", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "netconf_employee", "source_vertex_label": "root::netconf", "dest_vertex_label": "root::netconf::employee", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "employee_food", "source_vertex_label": "root::netconf::employee", "dest_vertex_label": "root::netconf::employee::food", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]