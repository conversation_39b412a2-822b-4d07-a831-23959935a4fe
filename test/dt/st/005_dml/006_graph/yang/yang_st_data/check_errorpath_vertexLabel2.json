[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "root", "name": "root.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L2", "name": "L2.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L3", "name": "L3.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L4", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L4", "name": "L4.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L5", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L5", "name": "L5.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L6", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L6", "name": "L6.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L7", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L7", "name": "L7.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L8", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L8", "name": "L8.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L9", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L9", "name": "L9.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L10", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L10", "name": "L10.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]