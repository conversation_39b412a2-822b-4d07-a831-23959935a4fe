[{"type": "container", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}], "keys": [{"node": "T0", "name": "T0.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "T0::T1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}], "keys": [{"node": "T0::T1", "name": "T1.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "T0::T2", "min-elements": 3, "max-elements": 8, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}], "keys": [{"node": "T0::T2", "name": "T2.PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]