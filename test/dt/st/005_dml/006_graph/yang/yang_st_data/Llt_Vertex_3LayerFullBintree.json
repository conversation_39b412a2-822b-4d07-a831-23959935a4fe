[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"node": "root", "name": "root.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L1", "name": "L1.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}], "keys": [{"node": "C1", "name": "C1.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L2", "name": "L2.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}], "keys": [{"node": "C2", "name": "C2.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}], "keys": [{"node": "L3", "name": "L3.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}], "keys": [{"node": "C3", "name": "C3.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L1_noMk", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}], "keys": [{"node": "L1_noMk", "name": "L1_noMk.PK", "fields": ["ID", "PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "container", "name": "C2_parent_noMk", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}], "keys": [{"node": "C2_parent_noMk", "name": "C2_parent_noMk.PK", "fields": ["PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L2_noMk", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}], "keys": [{"node": "L2_noMk", "name": "L2_noMk.PK", "fields": ["ID", "PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]