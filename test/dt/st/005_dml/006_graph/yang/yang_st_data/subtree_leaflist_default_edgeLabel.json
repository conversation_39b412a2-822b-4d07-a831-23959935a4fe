[{"name": "root_LF1", "source_vertex_label": "root", "dest_vertex_label": "LF1", "source_node_path": "/NP1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_LF2", "source_vertex_label": "root", "dest_vertex_label": "LF2", "source_node_path": "/P1", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_L1", "source_vertex_label": "root", "dest_vertex_label": "L1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "L1_LF3", "source_vertex_label": "L1", "dest_vertex_label": "LF3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_LF4", "source_vertex_label": "root", "dest_vertex_label": "LF4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_LF5", "source_vertex_label": "root", "dest_vertex_label": "LF5", "source_node_path": "/NP1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]