[{"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "alias": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "index-test-leafref-0", "type": "container", "config": {"check_validity": true}, "is_root": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "ietf-hardware:hardware", "type": "container", "fields": [{"name": "leafref1", "type": "string", "clause": [{"type": "leafref", "formula": "/index-test-leafref-0/list1/listContainer1/name"}]}, {"name": "leafref2", "type": "string", "clause": [{"type": "leafref", "formula": "/index-test-leafref-0/list1/listContainer1/name"}]}]}], "keys": [{"name": "root.PK", "index": {"type": "primary"}, "node": "index-test-leafref-0", "fields": ["ID"], "constraints": {"unique": true}}]}, {"name": "list1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "listContainer1", "type": "container", "fields": [{"name": "name", "type": "string", "nullable": false}]}], "keys": [{"name": "list1.PK", "index": {"type": "primary"}, "node": "list1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}]