{"sensor-groups": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group": [{"sensor-group-id": "1", "config": {"sensor-group-id": "1"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "2", "config": {"sensor-group-id": "2"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "3", "config": {"sensor-group-id": "3"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "4", "config": {"sensor-group-id": "4"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "5", "config": {"sensor-group-id": "5"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "6", "config": {"sensor-group-id": "6"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "7", "config": {"sensor-group-id": "7"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}, {"sensor-group-id": "8", "config": {"sensor-group-id": "8"}, "sensor-paths": {"openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path": [{"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-cpu-infos/board-cpu-info/system-cpu-usage"}}, {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage", "config": {"path": "huawei-cpu-memory:cpu-memory/board-memory-infos/board-memory-info/os-memory-usage"}}, {"path": "huawei-ifm:ifm/interfaces/interface/name", "config": {"path": "huawei-ifm:ifm/interfaces/interface/name"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-bit-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-byte"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-packet-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-multicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-broad-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-unicast-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/send-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/in-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/out-use-rate"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-error-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet", "config": {"path": "huawei-ifm:ifm/interfaces/interface/common-statistics/receive-drop-packet"}}, {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc", "config": {"path": "huawei-ifm:ifm/interfaces/interface/mib-statistics/huawei-pic:eth-port-err-sts/rx-crc"}}]}}]}, "destination-groups": {"openconfig-telemetry:telemetry-system::destination-groups::destination-group": [{"group-id": "1", "config": {"group-id": "1"}, "destinations": {"openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination": [{"destination-address": "*************", "destination-port": 777, "config": {"destination-address": "*************", "destination-port": 777}}]}}, {"group-id": "2", "config": {"group-id": "2"}, "destinations": {"openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination": [{"destination-address": "*************", "destination-port": 888, "config": {"destination-address": "*************", "destination-port": 888}}]}}]}, "subscriptions": {"persistent": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription": [{"subscription-name": "sub1", "config": {"subscription-name": "sub1"}, "sensor-profiles": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile": [{"sensor-group": "1", "config": {"sensor-group": "1", "sample-interval": 60000}}, {"sensor-group": "2", "config": {"sensor-group": "2", "sample-interval": 60000}}, {"sensor-group": "3", "config": {"sensor-group": "3", "sample-interval": 60000}}, {"sensor-group": "4", "config": {"sensor-group": "4", "sample-interval": 60000}}, {"sensor-group": "5", "config": {"sensor-group": "5", "sample-interval": 60000}}, {"sensor-group": "6", "config": {"sensor-group": "6", "sample-interval": 60000}}, {"sensor-group": "7", "config": {"sensor-group": "7", "sample-interval": 60000}}, {"sensor-group": "8", "config": {"sensor-group": "8", "sample-interval": 60000}}]}, "destination-groups": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group": [{"group-id": "1", "config": {"group-id": "1"}}]}}, {"subscription-name": "sub2", "config": {"subscription-name": "sub2"}, "sensor-profiles": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile": [{"sensor-group": "1", "config": {"sensor-group": "1", "sample-interval": 60000}}, {"sensor-group": "2", "config": {"sensor-group": "2", "sample-interval": 60000}}, {"sensor-group": "3", "config": {"sensor-group": "3", "sample-interval": 60000}}, {"sensor-group": "4", "config": {"sensor-group": "4", "sample-interval": 60000}}, {"sensor-group": "5", "config": {"sensor-group": "5", "sample-interval": 60000}}, {"sensor-group": "6", "config": {"sensor-group": "6", "sample-interval": 60000}}, {"sensor-group": "7", "config": {"sensor-group": "7", "sample-interval": 60000}}, {"sensor-group": "8", "config": {"sensor-group": "8", "sample-interval": 60000}}]}, "destination-groups": {"openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group": [{"group-id": "2", "config": {"group-id": "2"}}]}}]}}}