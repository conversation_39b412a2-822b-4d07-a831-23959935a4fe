[{"type": "container", "name": "C1", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "C2", "type": "container", "fields": [{"name": "F0", "type": "uint32"}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}]}], "keys": [{"node": "C1", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "comment", "type": "string"}, {"name": "C3", "type": "container", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "comment", "type": "string"}, {"name": "CHOICE1", "type": "choice", "fields": [{"name": "CASE1", "type": "case", "fields": [{"name": "C4", "type": "container", "presence": true, "fields": [{"name": "leaf1", "type": "uint16", "default": 0}, {"name": "leaf2", "type": "uint8", "default": 0}, {"name": "leaf3", "type": "uint8", "default": 0}]}]}]}]}], "keys": [{"node": "L1", "name": "L1.PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]