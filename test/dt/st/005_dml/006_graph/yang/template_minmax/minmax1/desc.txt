 {
        "type": "leaflist",
        "name": "LF1",
        "min-element": 1,
        "clause": [
            {
                "type": "when",
                "formula": "../name"
            }
        ],
        "fields": [
            {
                "name": ":id",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": ":pid",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "F1",
                "type": "uint32",
                "nullable": false
            }
        ],
}

leaflist LF1 上定义了when校验与min-element，
事务一when校验结果为true，预期min-elements校验报错
事务二预期when校验结果为false时，不对min-element进行校验。
