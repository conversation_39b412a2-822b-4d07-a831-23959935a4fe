{"ds": [{"name": "running", "huawei-wlan-wired-port-profile": [{"huawei-wlan-wired-port-profile:wlan-wired-port-profile": {"wiredport-profiles": {"wiredport-profile": [{"profile-name": "default", "@user-isolate": "disable", "@untagged-vlan": "1", "huawei-wlan-portlink-profile:binding-portlink-profile": {"@binding-portlink-profile": "default"}}]}}}], "huawei-wlan-portlink-profile": [{"huawei-wlan-portlink-profile:wlan-portlink-profile": {"portlink-profiles": {"portlink-profile": [{"profile-name": "default", "poe": {"@disable": false, "@force-power": false, "@legacy": false}}]}}}], "huawei-vlan": [{"huawei-vlan:vlan": {"vlans": {"vlan": [{"id": 1, "@type": "common", "huawei-arp:arp-security": {"@l2proxy-enable": false}}]}}}], "huawei-mac": [{"huawei-mac:mac": {"global-attribute": {"aging-time": 300}, "global-mac-usage": {"mac-threshold": 1}}}], "huawei-ifm": [{"huawei-ifm:ifm": {"interfaces": {"interface": [{"name": "Vlanif1", "class": "main-interface", "type": "<PERSON><PERSON><PERSON>", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ip:ipv4": {"address": {"common-address": {"addresses": {"address": [{"ip": "*************", "mask": "*************", "type": "main"}]}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/1", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "huawei-mstp:mstp-attribute": {"enable": false, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/2", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "huawei-mstp:mstp-attribute": {"enable": false, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/3", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "trunk", "huawei-mstp:mstp-attribute": {"enable": false, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/4", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/5", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/6", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/7", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/8", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/9", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ip:ipv4": {"address": {"common-address": {"addresses": {"address": [{"ip": "***********", "mask": "***********", "type": "main"}]}}}}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/10", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/11", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "down", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "NULL", "class": "main-interface", "type": "NULL", "@admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "lo", "class": "main-interface", "type": "LoopBack", "@admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"@nat-enable": false}}]}}}], "huawei-dhcp": [{"huawei-dhcp:dhcp": {"common": {"global": {"enable": true}}, "server": {"common": {"@ping-packet-nub": 2, "@ping-packet-timeout": 500, "@bootp-enable": true, "@bootp-auto-enable": true}}}}], "huawei-mstp": [{"huawei-mstp:mstp": {"global": {"@bpdu-filter": false, "@edge-port": false}, "default-process": {"enable": false, "@mode": "rstp", "@path-cost-standard": "dot1t", "@timer-factor": 3, "@forward-delay": 1500, "@hello-time": 200, "@max-age": 2000, "default-instance": {"@priority": 32768, "@root-type": "normal"}}}}], "huawei-network-instance": [{"huawei-network-instance:network-instance": {"instances": {"instance": [{"name": "_public_", "huawei-l3vpn:afs": {"af": [{"type": "ipv4-unicast", "huawei-routing:routing": {"routing-manage": {"topologys": {"topology": [{"name": "base"}]}}}}]}}]}}}], "huawei-wlan-ap": [{"huawei-wlan-ap:wlan-ap": {"@ap-auth-mode": "mac", "ap-types": {"ap-type": [{"type-name": "AP163", "type-id": 180}, {"type-name": "AP165", "type-id": 181}, {"type-name": "AP263", "type-id": 182}, {"type-name": "AP363", "type-id": 183}, {"type-name": "AP365", "type-id": 184}, {"type-name": "AP161", "type-id": 190}, {"type-name": "AP162", "type-id": 191}, {"type-name": "AP362", "type-id": 192}, {"type-name": "AP210", "type-id": 125}, {"type-name": "AP230", "type-id": 126}, {"type-name": "AP310", "type-id": 127}, {"type-name": "AP330", "type-id": 128}, {"type-name": "AP350", "type-id": 129}, {"type-name": "AirEngine5761RS-11", "type-id": 162}, {"type-name": "AP361", "type-id": 205}, {"type-name": "AP160", "type-id": 206}, {"type-name": "AP661", "type-id": 207}, {"type-name": "AP761", "type-id": 208}, {"type-name": "AP371", "type-id": 232}, {"type-name": "AP673", "type-id": 238}]}}}], "huawei-wlan-ap-group-profile": [{"huawei-wlan-ap-group-profile:wlan-ap-group-profile": {"ap-group-profiles": {"ap-group-profile": [{"ap-group-name": "default", "huawei-wlan-reg-dom-profile:grp-binding-regular-domain-profile": {"@binding-regular-domain-profile": "default"}, "huawei-wlan-system-profile:binding-system-profile": {"@binding-system-profile": "default"}}]}}}], "huawei-wlan-radio-2g-profile": [{"huawei-wlan-radio-2g-profile:wlan-radio-2g-profile": {"radio-2g-profiles": {"radio-2g-profile": [{"profile-name": "default", "@radio-type": "dot11ax", "@guard-interval-mode": "short", "@rts-cts-mode": "rts-cts", "huawei-wlan-rrm-profile:radio-2g-binding-rrm": {"@binding-rrm-profile": "default"}}]}}}], "huawei-wlan-radio-5g-profile": [{"huawei-wlan-radio-5g-profile:wlan-radio-5g-profile": {"radio-5g-profiles": {"radio-5g-profile": [{"profile-name": "default", "@radio-type": "dot11ax", "@guard-interval-mode": "short", "@rts-cts-mode": "rts-cts", "huawei-wlan-rrm-profile:radio-5g-binding-rrm": {"@binding-rrm-profile": "default"}}]}}}], "huawei-wlan-reg-dom-profile": [{"huawei-wlan-reg-dom-profile:wlan-reg-dom-profile": {"regulatory-domain-profiles": {"regulatory-domain-profile": [{"profile-name": "default", "@country-code": "CN"}]}}}], "huawei-wlan-system-profile": [{"huawei-wlan-system-profile:wlan-system-profile": {"system-profiles": {"system-profile": [{"profile-name": "default", "@stelnet-server": true, "@telnet": false, "@usb": "disable", "led": {"@led-switch": "turn-on"}, "traffic-optimize": {"@arp-threshold": 256, "@igmp-threshold": 256, "@nd-threshold": 256, "@dhcp-threshold": 256, "@dhcpv6-threshold": 256, "@mdns-threshold": 256, "@other-broadcast-threshold": 16, "@other-multicast-threshold": 16, "@arp": true, "@igmp": true, "@nd": true, "@dhcp": true, "@dhcpv6": true, "@mdns": true, "@other-broadcast": true, "@other-multicast": true}}]}}}], "huawei-wlan-vap-profile": [{"huawei-wlan-vap-profile:wlan-vap-profile": {"vap-profiles": {"vap-profile": [{"profile-name": "default", "@ip-source-check-user-bind-switch": false, "@arp-anti-attack-check-user-bind-switch": false, "@band-steer": true, "vlan-mode": {"id": {"@service-vlan": 1}}, "dhcp-option82": {"@insert-switch": false, "circuit-id": {"@dhcp-option82-format-type": "ap-mac", "@option82-pattern-semicolon": "disable"}, "remote-id": {"@dhcp-option82-format-type": "ap-mac", "@option82-pattern-semicolon": "disable"}}, "learn-client-address": {"learn-client-ipv4-address": {"@switch": true, "@conflict-check": true, "@dhcp-strict": false}, "learn-client-ipv6-address": {"@switch": true, "@conflict-check": true, "@strict": "disable"}}, "huawei-wlan-security-profile:binding-security-profile": {"@binding-security-profile": "default"}, "huawei-wlan-ssid-profile:binding-ssid-profile": {"@binding-ssid-profile": "default"}, "huawei-wlan-traffic-profile:binding-traffic-profile": {"@binding-traffic-profile": "default"}}]}}}], "huawei-wlan-security-profile": [{"huawei-wlan-security-profile:wlan-security-profile": {"security-profiles": {"security-profile": [{"profile-name": "default"}]}}}], "huawei-wlan-ssid-profile": [{"huawei-wlan-ssid-profile:wlan-ssid-profile": {"ssid-profiles": {"ssid-profile": [{"profile-name": "default", "@ssid": "HUAWEI-WLAN", "@max-sta-number": 64, "@mimo-switch": true, "@beamforming-switch": true, "ofdma-policy": {"@ofdma-downlink-switch": true, "@ofdma-uplink-switch": true}}]}}}], "huawei-wlan-traffic-profile": [{"huawei-wlan-traffic-profile:wlan-traffic-profile": {"traffic-profiles": {"traffic-profile": [{"profile-name": "default", "rate-limit": {"@client-up": 4294967295, "@client-down": 4294967295}}]}}}], "huawei-wlan-rrm-profile": [{"huawei-wlan-rrm-profile:wlan-rrm-profile": {"rrm-profiles": {"rrm-profile": [{"profile-name": "default", "dynamic-edca": {"@dynamic-edca": "disable"}, "smart-roam": {"@smart-roam-switch": "enable", "@snr-smart-roam-threshold": 20, "@quick-kickoff-threshold": "enable", "@snr-quick-kickoff-threshold": 15}, "uac": {"@uac-client-snr": "disable", "@uac-client-number": "disable", "@snr-threshold": 15, "number-threshold": {"@access": 64, "@roam": 64}}, "load-balance": {"@load-balance-switch": "enable", "@deauth-fail-times": 0}, "calibrate-para": {"@min-tx-power-2g": 9, "@max-tx-power-2g": 127, "@min-tx-power-5g": 12, "@max-tx-power-5g": 127, "@min-tx-power-6g": 12, "@max-tx-power-6g": 127, "@clb-noise-flr-thrd": -75}, "band-steer": {"@start-thrd": 100}}]}}}], "huawei-web-manager": [{"huawei-web-manager:web-manager": {"source-interface": {"@all-interface-enable": false, "interfaces": {"interface": [{"interface-name": "Vlanif1"}]}}}}], "huawei-tm": [{"huawei-tm:tm": {"timezone-configuration": {"@timezone-name": "DefaultZoneName", "@option": "add", "timezone-offset": "00:00:00"}}}], "huawei-system": [{"huawei-system:system": {"system-info": {"sys-name": "HUAWEI", "@sys-contact": "R&D Beijing, Huawei Technologies co.,Ltd.", "@sys-location": "Beijing China"}}}], "huawei-ssl": [{"huawei-ssl:ssl": {"ssl-policys": {"ssl-policy": [{"policy-name": "default", "pki-realm": "default"}]}}}], "huawei-sshs": [{"huawei-sshs:sshs": {"server": {"pki-domain": "default"}, "users": {"user": [{"name": "hua<PERSON>", "key-name": "default", "pub-key-type": "PKI"}]}, "server-enable": {"stelnet-ipv4-enable": "enable"}, "server-port": {"@ipv4-port-number": 22}, "ipv4-server-sources": {"ipv4-server-source": [{"src-interface": "Vlanif1"}, {"src-interface": "GE0/0/9"}]}, "call-homes": {"call-home": [{"call-home-name": "QiankunCloudService", "end-points": {"end-point": [{"end-point-name": "DefaultEndPoint", "enabled": false}]}}]}}}], "huawei-aaa": [{"huawei-aaa:aaa": {"lam": {"password-policy": {"complexity-check-three": true}, "users": {"user": [{"name": "hua<PERSON>", "group-name": "admin", "password": "$6$J1RIgcdlkLl4aqBZ$LG6OpvMhUSIUCMJRnIwXCRlROiagx7NCLBgLVIqAA.moUrzBvc95atu9TccdH/wyKjemU8ex6Xc2Ia1D/IPub.", "service-terminal": true, "service-api": true, "password-force-change": false}]}}}}], "huawei-pki": [{"huawei-pki:pki": {"global": {"certificate-check": {"@validate-method": "crl-none"}}, "domains": {"domain": [{"name": "default", "@digest-algorithm": "sha-256"}]}}}], "huawei-nat-policy": [{"huawei-nat-policy:nat-policy": {"rules": {"rule": [{"name": "default", "action": {"action": {"do-nat": {"source-nat": {"mode": {"easy-ip": {"easy-ip": ""}}}}}}, "egress": {"interfaces": {"egress-interface": [{"egress-interface": "GE0/0/9"}, {"egress-interface": "GE0/0/10"}]}}}]}}}], "huawei-masterkey": [{"huawei-masterkey:masterkey": {"auto-update": {"interval": 1825}}}], "huawei-host-security": [{"huawei-host-security:host-security": {"anti-attacks": {"anti-attack": [{"anti-attack-type": "abnormal", "enable": false}]}, "host-cars": {"@enable": false}, "adjust-car": {"@enable": false}}}], "huawei-ftpc": [{"huawei-ftpc:ftpc": {"client": {"enabled": true}}}], "huawei-easyweb-netmgmt": [{"huawei-easyweb-netmgmt:easyweb-netmgmt": {"network": {"@online-upgrade-enable": false, "device-granteds": {"device-granted": [{"mac-address": "58be-72d1-d220"}]}}}}], "huawei-cli": [{"huawei-cli:cli": {"huawei-cli-lite:terminal": {"@history-cmd-size": 10, "idle-timeout": 86400, "split-screen": false}}}]}]}