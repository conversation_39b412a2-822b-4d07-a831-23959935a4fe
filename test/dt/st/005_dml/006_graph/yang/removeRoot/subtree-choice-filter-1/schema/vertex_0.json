[{"type": "container", "name": "ContainerOne", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"type": "container", "name": "ContainerTwo", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}]}, {"type": "choice", "name": "Choice", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "fields": [{"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}]}]}]}, {"type": "choice", "name": "ChoiceTwo", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "fields": [{"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}]}]}]}], "keys": [{"name": "PK", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListOne", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}, {"name": "F4", "type": "int32"}, {"type": "choice", "name": "ListChoice", "fields": [{"type": "case", "name": "ListChoiceCase", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"name": "F4", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "fields": [{"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}, {"name": "F4", "type": "int32"}]}]}]}], "keys": [{"name": "PK", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]