[{"name": "root", "alias": "root", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "product_pattern1", "type": "string", "pattern": ["infinite|\\d+"]}, {"name": "product_pattern2", "type": "string", "pattern": ["infinite|\\d+"]}, {"name": "product_pattern3", "type": "string", "pattern": ["infinite|\\d+"]}, {"name": "product_pattern4", "type": "string", "pattern": ["(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?"]}, {"name": "product_pattern5", "type": "string", "pattern": ["(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?"]}, {"name": "product_pattern6", "type": "string", "pattern": ["((([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.)*([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.?)|\\."]}, {"name": "product_pattern7", "type": "string", "pattern": ["((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?)|."]}, {"name": "product_pattern8", "type": "string", "pattern": ["\\d{1,2}:\\d{1,2}:\\d{1,2}"]}, {"name": "product_pattern9", "type": "string", "pattern": ["[ -~]*"]}, {"name": "product_pattern10", "type": "string", "pattern": ["(([0-1](\\.[1-3]?[0-9]))|(2\\.(0|([1-9]\\d*))))(\\.(0|([1-9]\\d*)))*"]}, {"name": "product_pattern11", "type": "string", "pattern": ["(([0-1](\\.[1-3]?[0-9]))|(2\\.(0|([1-9]\\d*))))(\\.(0|([1-9]\\d*)))*"]}, {"name": "product_pattern12", "type": "string", "pattern": ["$0$.*|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}|$5$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{43}|$6$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}"]}, {"name": "product_pattern13", "type": "string", "pattern": ["$0$.*|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}|$5$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{43}|$6$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}"]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]