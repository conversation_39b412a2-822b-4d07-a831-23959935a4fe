[{"name": "root", "alias": "root", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "pattern_range", "type": "string", "pattern": ["[0-9a-fA-F]"]}, {"name": "pattern_i_range", "type": "string", "pattern": ["[^0-9a-fA-F]"]}, {"name": "pattern_d", "type": "string", "pattern": ["[\\d]+"]}, {"name": "pattern_D", "type": "string", "pattern": ["[\\D]+"]}, {"name": "pattern_s", "type": "string", "pattern": ["[\\s]+"]}, {"name": "pattern_S", "type": "string", "pattern": ["[\\S]+"]}, {"name": "pattern_dot", "type": "string", "pattern": [".+"]}, {"name": "pattern_dot_in_bracket", "type": "string", "pattern": ["[.]+"]}, {"name": "pattern_i", "type": "string", "pattern": ["[\\i]+"]}, {"name": "pattern_I", "type": "string", "pattern": ["[\\I]+"]}, {"name": "pattern_c", "type": "string", "pattern": ["[\\c]+"]}, {"name": "pattern_C", "type": "string", "pattern": ["[\\C]+"]}, {"name": "pattern_w", "type": "string", "pattern": ["[\\w]+"]}, {"name": "pattern_W", "type": "string", "pattern": ["[\\W]+"]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]