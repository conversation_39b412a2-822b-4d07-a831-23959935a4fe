[{"name": "root", "alias": "root", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "pattern_left_bracket1", "type": "string", "pattern": ["[[]+"]}, {"name": "pattern_left_bracket2", "type": "string", "pattern": ["[abc\\[]+"]}, {"name": "pattern_left_bracket3", "type": "string", "pattern": ["[abc\\[]+"]}, {"name": "pattern_left_bracket4", "type": "string", "pattern": ["[^abc\\[]+"]}, {"name": "pattern_left_bracket5", "type": "string", "pattern": ["[abc\\]]+"]}, {"name": "pattern_left_bracket6", "type": "string", "pattern": ["[^abc\\]]+"]}, {"name": "pattern_escape_in_bracket1", "type": "string", "pattern": ["a[\\n\\*\\.]+"]}, {"name": "pattern_escape_in_bracket2", "type": "string", "pattern": ["a[\\n\\*\\.]+"]}, {"name": "pattern_escape_in_bracket3", "type": "string", "pattern": ["[a\\-z]+"]}, {"name": "pattern_escape_in_bracket4", "type": "string", "pattern": ["[a\\-z]+"]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]