[{"type": "container", "name": "root", "is_config": false, "fields": [{"name": "ID", "is_config": false, "type": "uint32", "nullable": false}, {"name": "F0", "is_config": false, "type": "int32", "nullable": false}, {"name": "F1", "is_config": false, "type": "int32"}, {"type": "container", "name": "C1", "is_config": false, "fields": [{"name": "F0", "is_config": false, "type": "int32"}, {"name": "F1", "is_config": false, "type": "int32"}, {"type": "container", "name": "C2", "is_config": false, "fields": [{"name": "F0", "is_config": false, "type": "int32"}, {"name": "F1", "is_config": false, "type": "int32"}]}]}], "keys": [{"node": "root", "name": "root.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "L1", "is_config": false, "fields": [{"name": "ID", "is_config": false, "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "is_config": false, "type": "uint32", "nullable": false}, {"name": "mk", "is_config": false, "type": "int32", "nullable": false}, {"name": "F0", "is_config": false, "type": "int32"}, {"type": "container", "name": "C3", "is_config": false, "fields": [{"name": "F0", "is_config": false, "type": "int32", "nullable": false}, {"name": "F1", "is_config": false, "type": "int32", "nullable": false}]}], "keys": [{"node": "L1", "name": "L1.PK", "fields": ["PID", "mk"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]