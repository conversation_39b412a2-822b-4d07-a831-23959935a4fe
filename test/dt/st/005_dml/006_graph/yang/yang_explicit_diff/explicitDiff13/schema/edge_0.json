[{"name": "explicitDiff13", "source_vertex_label": "explicitDiff13", "dest_vertex_label": "list_1", "source_node_path": "/C1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "explicitDiff13_2", "source_vertex_label": "explicitDiff13", "dest_vertex_label": "list_2", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]