[{"type": "container", "name": "explicitDiff7", "presence": false, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F1", "type": "string", "nullable": true}, {"name": "F11", "type": "string", "nullable": true}, {"name": "F0", "type": "uint32", "nullable": true, "default": 1}, {"name": "C1", "type": "container", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "C2", "type": "container", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 1}]}]}], "keys": [{"name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "list_1:NP", "type": "container", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 1}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false, "default": ["1", "2", "4"]}], "keys": [{"node": "leaflist_1", "name": "k0", "fields": ["PID", "F1"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}, {"name": "yang_npa", "type": "list", "np_access": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": ["ID", "PID", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]