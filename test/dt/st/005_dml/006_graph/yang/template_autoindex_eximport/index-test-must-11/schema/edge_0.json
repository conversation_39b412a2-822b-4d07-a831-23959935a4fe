[{"name": "root_to_list_1", "source_vertex_label": "index-test-must-11", "dest_vertex_label": "list_1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "list_1_to_list_2", "source_vertex_label": "list_1", "dest_vertex_label": "list_2", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]