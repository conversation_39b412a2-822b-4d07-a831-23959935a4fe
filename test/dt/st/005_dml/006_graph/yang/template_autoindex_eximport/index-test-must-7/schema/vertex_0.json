[{"type": "container", "name": "index-test-must-7", "presence": false, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}], "keys": [{"name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "ch1", "type": "choice", "fields": [{"name": "ca1", "type": "case", "default": true, "fields": [{"name": "F1", "type": "uint32", "default": 2, "clause": [{"type": "must", "formula": "/index-test-must-7/list_1[PK = current()]/F2 <= 1"}]}]}, {"name": "ca2", "type": "case", "clause": [{"type": "when", "formula": "current()/F2 <= 1"}], "fields": [{"name": "F1", "type": "uint32", "default": 1, "clause": [{"type": "must", "formula": "/index-test-must-7/list_1[PK = current()]/F2 <= 1"}]}, {"name": "F2", "type": "uint32", "default": 1}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "yang_npa", "type": "list", "np_access": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": ["ID", "PID", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]