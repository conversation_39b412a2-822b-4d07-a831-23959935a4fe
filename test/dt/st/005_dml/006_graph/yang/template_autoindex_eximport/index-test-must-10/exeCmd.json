{"Trans": [{"index": 0, "exeCmd": "export,createNsp,useNsp,import,tx_begin,sp_begin,insert,subtree_1,validate,tx_commit", "export": "trans_0/export", "createNsp": "common/nsp1", "useNsp": "common/nsp1", "import": "trans_0/import", "insert": "trans_0/insert", "subtree_1": "trans_0/subtree_1", "validate": "trans_0/validate"}, {"index": 1, "exeCmd": "tx_begin,sp_begin,insert,subtree_1,validate,subtree_2,tx_commit", "insert": "trans_1/insert", "subtree_1": "trans_1/subtree_1", "validate": "trans_1/validate", "subtree_2": "trans_1/subtree_2"}, {"index": 2, "exeCmd": "useNsp,clrNsp,dropNsp,useNsp_2", "useNsp": "common/nsp1", "clrNsp": "common/nsp1", "dropNsp": "common/nsp1", "useNsp_2": "common/nspDefault"}]}