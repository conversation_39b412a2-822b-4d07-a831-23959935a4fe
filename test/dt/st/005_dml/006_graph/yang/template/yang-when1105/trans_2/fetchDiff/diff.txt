yang:update[(pri<PERSON><PERSON>(:id:1)),(pri<PERSON><PERSON>(:id:1))]
yang.ds:update[(pri<PERSON><PERSON>(:pid:1,name:running)),(pri<PERSON><PERSON>(:pid:1,name:running))]
ds.huawei-snmp:update[(pri<PERSON><PERSON>(:pid:1)),(pri<PERSON>ey(:pid:1))]
huawei-snmp.huawei-snmp:snmp:update
huawei-snmp:snmp.usm-users:update
usm-users.usm-user:create[(pri<PERSON>ey(:pid:1,user-name:user2), preKey(:pid:1,user-name:user1)),(NULL)]
usm-user.:id:create(2)
usm-user.auth-protocol:create(sha2-256)
usm-user.auth-key:create(%+%##!!!!!!!!!"!!!!$!!!!*!!!!S6TpP\N8Z)Jc{|$P3WdKC|W&OMPW~*U`PY0!!!!!2jp5!!!!!!9!!!!&{3_3tBx1%khW>MO@h+38C+'8XA4{!!!!!!!!!!!%+%#)
usm-user.priv-protocol:create(aes128)
