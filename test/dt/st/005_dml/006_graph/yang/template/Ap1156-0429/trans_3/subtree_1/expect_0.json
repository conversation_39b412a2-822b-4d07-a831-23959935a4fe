{":id": 1, "ds": [{":id": 1, ":pid": 1, "name": "running", "huawei-ifm": [{":id": 1, ":pid": 1, "huawei-ifm:ifm": {"interfaces": {"interface": [{":id": 3, ":pid": 1, "name": "MultiGE0/0/0", "class": "main-interface", "type": "MultiGE", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "trunk-vlans": "230", "untag-vlans": "1", "huawei-igmp-mld-snooping:igmp-snooping": {"@enable": false, "@report-suppress": false, "@max-bandwidth": 0, "@max-user": 0}, "huawei-mstp:mstp-attribute": {"@enable": false, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}}]}}}]}]}