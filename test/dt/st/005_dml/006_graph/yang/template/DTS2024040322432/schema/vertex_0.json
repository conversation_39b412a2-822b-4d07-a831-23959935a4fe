[{"name": "DTS2024040322432", "alias": "DTS2024040322432", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ds", "alias": "ds", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-omu:test", "alias": "huawei-omu:test", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-omu-test:c1", "type": "container", "fields": [{"name": "validation-con", "type": "container", "presence": true, "fields": [{"name": "when-leaf", "type": "int8"}, {"type": "container", "name": "when-con", "clause": [{"type": "when", "formula": "../when-leaf = 1"}], "fields": [{"name": "man-leaf", "type": "string", "nullable": false}]}, {"name": "ca1", "type": "container", "fields": [{"name": "ca1-v1", "type": "string"}]}, {"type": "choice", "name": "man-ch", "fields": [{"name": "ca1", "type": "case", "fields": [{"name": "ca1-v1", "type": "string", "nullable": false}, {"name": "ca1-v2", "type": "string"}, {"name": "ca1-v3", "type": "string"}]}, {"name": "ca2", "type": "case", "fields": [{"name": "ca2-v1", "type": "string", "nullable": false}]}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]