{"op": "merge", "bbf-xpon:xpon": {"op": "merge", "uni-profiles": {"op": "merge", "bbf-uni-profile:uni-profile.1": [{"name": "b", "mac-learning": true, "transparent-enable": true, "isolate-enable": "enable", "monitor-link-enable": "enable", "multicast-mode": "olt-control", "mtu": "1580", "ring-check": {"op": "merge", "switch": true, "auto-shutdown": true, "detect-frequency": "10", "resume-interval": "500", "detect-period": "10"}, "traffic-suppress": {"op": "merge", "broadcast": "10000000", "multicast": "unlimited", "unicast": "unlimited"}, "color-policy": {"op": "merge", "upstream": "dei", "downstream": "dei"}, "ont-ports": {"op": "merge", "bbf-uni-profile:ont-port.1": [{"name": "ethernetCsmacd.1", "type": "iana-if-type:ethernetCsmacd", "frame-processing": {"op": "merge", "bbf-uni-profile:rule.1": [{"name": "ontportvlan.singletag.101.any", "match-criteria": {"op": "merge", "tag": {"op": "merge", "vlan-id": "101", "pbit": "any"}}, "ingress-rewrite": {"op": "merge", "pop-tags": 1, "vlan-id": "1"}}]}}, {"name": "ethernetCsmacd.2", "type": "iana-if-type:ethernetCsmacd", "frame-processing": {"op": "merge", "bbf-uni-profile:rule.1": [{"name": "ontportvlan.transparent", "match-criteria": {"op": "merge", "tag": {"op": "merge", "vlan-id": "any"}}}]}}]}}]}}}