[{"name": "root_to_list0", "source_vertex_label": "nested-predicate", "dest_vertex_label": "list0", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_test", "source_vertex_label": "nested-predicate", "dest_vertex_label": "list_test", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list1", "source_vertex_label": "nested-predicate", "dest_vertex_label": "list1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]