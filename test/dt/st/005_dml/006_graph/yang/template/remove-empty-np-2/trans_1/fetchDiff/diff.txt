remove-empty-np-2:update[(pri<PERSON><PERSON>(:id:1)),(pri<PERSON><PERSON>(:id:1))]
remove-empty-np-2.test-2:update
test-2.L0:remove[(NULL),(priKey(:pid:1,F0:1))]
L0.:id:remove(1)
L0.L0-NP:remove
L0-NP.LL1:remove[(NULL),(priKey(:pid:1,F0:7))]
L0-NP.LL1:remove[(NULL),(priKey(:pid:1,F0:8), preKey(:pid:1,F0:7))]
L0-NP.LL1:remove[(NULL),(priKey(:pid:1,F0:9), preKey(:pid:1,F0:8))]
test-2.L0:remove[(NULL),(priKey(:pid:1,F0:6), preKey(:pid:1,F0:1))]
L0.:id:remove(2)
L0.L0-NP:remove
L0-NP.LL1:remove[(NULL),(pri<PERSON>ey(:pid:2,F0:7))]
L0-NP.LL1:remove[(NULL),(priKey(:pid:2,F0:8), preKey(:pid:2,F0:7))]
L0-NP.LL1:remove[(NULL),(priKey(:pid:2,F0:9), preKey(:pid:2,F0:8))]
