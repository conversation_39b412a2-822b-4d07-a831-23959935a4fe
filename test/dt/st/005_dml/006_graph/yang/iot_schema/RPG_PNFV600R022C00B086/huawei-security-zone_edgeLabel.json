[{"name": "root_zone-instance", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-security-zone:security-zone::zone-instances::zone-instance", "sourceNodeName": "root::huawei-security-zone:security-zone::zone-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "zone-instance_assign-interface", "source_vertex_label": "root::huawei-security-zone:security-zone::zone-instances::zone-instance", "dest_vertex_label": "root::huawei-security-zone:security-zone::zone-instances::zone-instance::assign-interface", "sourceNodeName": "root::huawei-security-zone:security-zone::zone-instances::zone-instance", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]