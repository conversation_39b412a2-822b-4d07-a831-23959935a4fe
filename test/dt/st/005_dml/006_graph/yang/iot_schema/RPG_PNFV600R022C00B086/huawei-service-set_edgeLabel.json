[{"name": "root_service-group", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-service-set:service-set::service-groups::service-group", "sourceNodeName": "root::huawei-service-set:service-set::service-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "service-group_item", "source_vertex_label": "root::huawei-service-set:service-set::service-groups::service-group", "dest_vertex_label": "root::huawei-service-set:service-set::service-groups::service-group::items::item", "sourceNodeName": "root::huawei-service-set:service-set::service-groups::service-group::items", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_service-object", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-service-set:service-set::service-objects::service-object", "sourceNodeName": "root::huawei-service-set:service-set::service-objects", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "service-object_item", "source_vertex_label": "root::huawei-service-set:service-set::service-objects::service-object", "dest_vertex_label": "root::huawei-service-set:service-set::service-objects::service-object::items::item", "sourceNodeName": "root::huawei-service-set:service-set::service-objects::service-object::items", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]