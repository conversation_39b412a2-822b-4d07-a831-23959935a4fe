[{"name": "root_route", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_route_2", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_route_3", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_acl-range", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl-range_acl-info", "source_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_car-info", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_vlan-info", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "vlan-info_vlan-id", "source_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_mac-learning", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_address", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_storage", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-edgem:edgem::vm-storages::storage", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-edgem:edgem::vm-storages", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_oper-state", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-diagnose:diagnose::huawei-diagnose-cellular:cellular::oper-states::oper-state", "sourceNodeName": "root::huawei-diagnose:diagnose::huawei-diagnose-cellular:cellular::oper-states", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]