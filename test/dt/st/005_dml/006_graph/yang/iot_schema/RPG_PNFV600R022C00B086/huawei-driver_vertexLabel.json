[{"name": "root::huawei-driver:driver::electronic-labels::electronic-label", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "entity-class", "type": "string", "is_config": false}, {"name": "position", "type": "string", "is_config": false}, {"name": "entity-serial-number", "type": "uint32", "is_config": false}, {"name": "manufacturer-name", "type": "string", "is_config": false}, {"name": "manufacturer-date", "type": "string", "is_config": false}, {"name": "manufacturer-code", "type": "string", "is_config": false}, {"name": "board-type", "type": "string", "is_config": false}], "is_config": false, "keys": [{"name": "electronic-label_PK", "node": "root::huawei-driver:driver::electronic-labels::electronic-label", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "entity-class", "position", "entity-serial-number"]}]}, {"name": "root::huawei-driver:driver::device-health-checks::device-health-check", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "item", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}], "is_config": false, "keys": [{"name": "device-health-check_PK", "node": "root::huawei-driver:driver::device-health-checks::device-health-check", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "item"]}]}, {"name": "root::hua<PERSON>-driver:driver::temperature2s::temperature2", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "position", "type": "string"}, {"name": "sensor-id", "type": "uint32"}, {"name": "sensor-name", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "current-temperature", "type": "int32", "is_config": false}, {"name": "minor-threshold", "type": "int32", "is_config": false}, {"name": "major-threshold", "type": "int32", "is_config": false}, {"name": "fatal-threshold", "type": "int32", "is_config": false}, {"name": "low-threshold", "type": "int32", "is_config": false}], "keys": [{"name": "temperature2_PK", "node": "root::hua<PERSON>-driver:driver::temperature2s::temperature2", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "position", "sensor-id"]}]}]