[{"name": "alias-test:root::L1", "source_vertex_label": "alias-test:root", "dest_vertex_label": "alias-test:root::L1", "source_node_path": "/C1/Ca", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "alias-test:root::L1::L2", "source_vertex_label": "alias-test:root::L1", "dest_vertex_label": "alias-test:root::L1::L2", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "alias-test:root::L3", "source_vertex_label": "alias-test:root", "dest_vertex_label": "alias-test:root::L3", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "alias-test:root2::L1", "source_vertex_label": "alias-test:root2", "dest_vertex_label": "alias-test:root2::L1", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "alias-test:root::L1::L4", "source_vertex_label": "alias-test:root::L1", "dest_vertex_label": "alias-test:root::L1::L4", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]