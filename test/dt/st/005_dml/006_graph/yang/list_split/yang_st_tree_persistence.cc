/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: test cases for yang diff feature
 * Author: f00633892
 * Create: 2022-9-19
 */

#include "yang/yang_common_st.h"

static const char *g_diffCfgJsonTree = R"({"max_record_count":1000, "auto_increment":101, "yang_model":1})";

static const char *g_lltJsonTree = R"({"yang_model":1})";

static void InitPersistenceBasicLabelAsync(
    GmcStmtT *root_stmt, const char *tblName, const char *nspName, const char *vertexLabel, const char *edgeLabel)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(root_stmt, 64, tblName, nspName));
    if (edgeLabel == NULL) {
        LltCreateVertexLabelAsync(root_stmt, vertexLabel, g_diffCfgJsonTree);
    } else {
        LltCreateVertexAndEdgeLabelAsync(root_stmt, vertexLabel, edgeLabel, g_diffCfgJsonTree);
    }
}

static void InitPersistenceBasicDataAsync(
    GmcConnT *conn, GmcStmtT *root_stmt, const char *nspName, const char *jsonFile)
{
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn, &config, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(root_stmt, nspName));
    LltBatchExecuteByJsonAsync(conn, root_stmt, "T0", (GetFileContext(jsonFile)).c_str(), g_lltJsonTree);
    ASSERT_EQ(GMERR_OK, GmcTransCommitAsync(conn, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
}

static void ExportDataAsync(
    GmcConnT *conn, GmcStmtT *root_stmt, const char *nspName, GmcPersistenceConfigT *exportConfig)
{
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn, &config, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(root_stmt, nspName));
    ASSERT_EQ(GMERR_OK, GmcYangExportDataAsync(root_stmt, exportConfig, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_EQ(GMERR_OK, GmcTransCommitAsync(conn, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
}

static void ImportDataAsync(GmcConnT *conn, GmcStmtT *root_stmt, const char *nspName, GmcPersistenceConfigT *config)
{
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(root_stmt, nspName));
    ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(root_stmt, config, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
}

GmcPersistenceConfigT GetPersistenceConfig(
    GmcPersistenceModeE mode, GmcDigestAlgorithmE algorithm, const char *dir, const char *password)
{
    GmcPersistenceConfigT config;
    config.algorithm = algorithm;
    config.mode = mode;
    config.dir = dir;
    config.password = password;
    return config;
}

// 测试点：部分表有数据，导入导出成功
TEST_F(StYang, ConfigDataPartitionPersistence)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：全部表有数据，导入导出成功
TEST_F(StYang, ConfigDataPersistence)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result1.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：全部表有数据，带CRC算法导入导出成功
TEST_F(StYang, ConfigDataPersistenceWithCrc)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_CRC, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_CRC, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result1.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：全部表有数据，带HMAC算法导入导出成功
TEST_F(StYang, ConfigDataPersistenceWithHmac)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "keyPassword");
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "keyPassword");
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result1.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：全部表有数据，HMAC算法password不一致导入失败
TEST_F(StYang, ConfigDataPersistenceWithInvalidKeyHmac)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "keyPassword");
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "InvalidPassword");

    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb<GMERR_DATA_EXCEPTION>, &step));
    EpollWaitAndCheck(step, ++expectStep);

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：数据导入成功后执行diff成功
TEST_F(StYang, ConfigDataPersistenceWithDiff)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    const char *editStr = "006_graph/yang/read_by_persistence/operation/tree_create_diff.json";
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
                                     "T0::T2.ID:create(7)\n"
                                     "T0::T2.F1:create(9)\n"
                                     "T0::T2.F2:create(11)\n"
                                     "T0::T2.nodeName:create(T2-06)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:7,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(10)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:7,F0:2), preKey(PID:7,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(11)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:7,F0:3), preKey(PID:7,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(12)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
                                     "T0::T2.ID:create(8)\n"
                                     "T0::T2.F1:create(10)\n"
                                     "T0::T2.F2:create(12)\n"
                                     "T0::T2.nodeName:create(T2-07)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:8,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(13)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:8,F0:2), preKey(PID:8,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(14)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:8,F0:3), preKey(PID:8,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(15)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
                                     "T0::T2.ID:create(9)\n"
                                     "T0::T2.F1:create(11)\n"
                                     "T0::T2.F2:create(13)\n"
                                     "T0::T2.nodeName:create(T2-08)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:9,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(16)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:9,F0:2), preKey(PID:9,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(17)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:9,F0:3), preKey(PID:9,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(18)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:10), preKey(PID:1,F0:9)),(NULL)]\n"
                                     "T0::T2.ID:create(10)\n"
                                     "T0::T2.F1:create(12)\n"
                                     "T0::T2.F2:create(14)\n"
                                     "T0::T2.nodeName:create(T2-09)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:10,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(19)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:10,F0:2), preKey(PID:10,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(20)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:10,F0:3), preKey(PID:10,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(21)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:11), preKey(PID:1,F0:10)),(NULL)]\n"
                                     "T0::T2.ID:create(11)\n"
                                     "T0::T2.F1:create(13)\n"
                                     "T0::T2.F2:create(15)\n"
                                     "T0::T2.nodeName:create(T2-10)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:11,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(22)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:11,F0:2), preKey(PID:11,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(23)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:11,F0:3), preKey(PID:11,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(24)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：导出接口异常检测
TEST_F(StYang, ConfigDataExportInterfaceCheck)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(asyncConn, &config, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));

    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangExportDataAsync(asyncStmt, NULL, AsyncOperationCb, &step));

    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangExportDataAsync(NULL, &exportConfig, AsyncOperationCb, &step));

    exportConfig = GetPersistenceConfig(GMC_PERSISTENCE_MODE_BUTT, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncOperationCb, &step));

    exportConfig = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, NULL, NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncOperationCb, &step));

    exportConfig = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncOperationCb, &step));

    exportConfig = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "key");
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncOperationCb, &step));

    exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "keykeykey");
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncOperationCb, &step));

    exportConfig = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/invalid_path/", NULL);
    ASSERT_EQ(
        GMERR_OK, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncOperationCb<GMERR_DATA_EXCEPTION>, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_EQ(GMERR_OK, GmcTransRollBackAsync(asyncConn, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");
}

// 测试点：导入接口异常检测
TEST_F(StYang, ConfigDataImportInterfaceCheck)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));

    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangImportDataAsync(asyncStmt, NULL, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangImportDataAsync(NULL, &config, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_BUTT, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, NULL, NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "key");
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "keykeykey");
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb, &step));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/invalid_path/", NULL);
    ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb<GMERR_DATA_EXCEPTION>, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：完整性算法不一致导入失败
TEST_F(StYang, ConfigDataDigestAlgorithmInconsistent)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_CRC, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_HMAC, "/config_data/", "keyPassword");

    ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb<GMERR_DATA_EXCEPTION>, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：namespace非空导入失败
TEST_F(StYang, ConfigDataAbnormalNamespace)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangDst", "dst", vertexLabelFileTree, edgeLabelFileTree));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);

    ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb<GMERR_DATA_EXCEPTION>, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：导入文件不存在导入失败
TEST_F(StYang, ConfigDataImportFileNotExist)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    system("rm -rf /config_data/*");
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);

    ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(asyncStmt, &config, AsyncOperationCb<GMERR_DATA_EXCEPTION>, &step));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

typedef struct Result {
    int createLabelStatus;
    int importDataStatus;
    int exportDataStatus;
    std::atomic_uint32_t step;
    const char *nspName;
} ResultT;

void AsyncImportCb(void *userData, Status status, const char *errMsg)
{
    ResultT *result = (ResultT *)userData;
    result->importDataStatus = status;
    result->step++;
}

static Status CreateConnOptions(GmcConnOptionsT **connOpt)
{
    static const char *serverLocator = "usocket:/run/verona/unix_emserver";
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    if (ret != GMERR_OK) {
        GmcConnOptionsDestroy(connOptions);
        return ret;
    }
    *connOpt = connOptions;
    return ret;
}

Status ConfigDataCreateSyncConnAndStmt(GmcConnT **connSync, GmcStmtT **stmtSync)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnOptionsT *connOptions;
    Status ret = CreateConnOptions(&connOptions);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcConnOptionsDestroy(connOptions);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        GmcDisconnect(conn);
        return ret;
    }
    *connSync = conn;
    *stmtSync = stmt;
    return ret;
}

// 销毁连接
void ConfigDataDestroyConnectionAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    if (stmt != NULL) {
        GmcFreeStmt(stmt);
    }
    if (conn != NULL) {
        GmcDisconnect(conn);
    }
    return;
}

void *CreateLabelMultiTime(void *args)
{
    ResultT *result = (ResultT *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ConfigDataCreateSyncConnAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, result->nspName));
    const char *vertexLabelFile = "006_graph/yang/read_by_persistence/vertexLabel.json";
    result->createLabelStatus =
        GmcCreateVertexLabel(stmt, (GetFileContext(vertexLabelFile)).c_str(), g_diffCfgJsonTree);
    ConfigDataDestroyConnectionAndStmt(conn, stmt);
    return (void *)0;
}

// 测试点：导入中带DDL并发
TEST_F(StYang, ConfigDataImportAndDDL)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &exportConfig));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));

    for (uint32_t i = 0; i < 100; i++) {
        pthread_t threadId = 0;
        ResultT result = {0};
        result.nspName = "dst";
        ASSERT_EQ(GMERR_OK, pthread_create(&threadId, NULL, CreateLabelMultiTime, &result));

        uint32_t expectStep = 0;
        ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
        GmcPersistenceConfigT config =
            GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
        ASSERT_EQ(GMERR_OK, GmcYangImportDataAsync(asyncStmt, &config, AsyncImportCb, &result));
        EpollWaitAndCheck(result.step, ++expectStep);
        ASSERT_EQ(GMERR_OK, pthread_join(threadId, NULL));
        bool isValid = (result.importDataStatus == GMERR_DATA_EXCEPTION && result.createLabelStatus == GMERR_OK) ||
                       (result.importDataStatus == GMERR_OK && result.createLabelStatus == GMERR_OK);
        if (result.importDataStatus != GMERR_OK) {
            printf("import state:%u\n", result.importDataStatus);
        }
        if (result.createLabelStatus != GMERR_OK) {
            printf("create state:%u\n", result.createLabelStatus);
        }
        ASSERT_TRUE(isValid);
        ASSERT_NO_FATAL_FAILURE(GmcClearNamespaceAsync(asyncStmt, "dst", AsyncOperationCb, &result.step));
        EpollWaitAndCheck(result.step, ++expectStep);
    }

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

void AsyncExportCb(void *userData, Status status, const char *errMsg)
{
    ResultT *result = (ResultT *)userData;
    result->exportDataStatus = status;
    result->step++;
}

// 测试点：导出中带DDL并发
TEST_F(StYang, ConfigDataExportAndDDL)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    bool isRollBack = false;
    for (uint32_t i = 0; i < 100; i++) {
        pthread_t threadId = 0;
        ResultT result = {0};
        result.nspName = "src";
        ASSERT_EQ(GMERR_OK, pthread_create(&threadId, NULL, CreateLabelMultiTime, &result));

        uint32_t expectStep = 0;
        ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
        GmcPersistenceConfigT exportConfig =
            GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
        ASSERT_EQ(GMERR_OK, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncExportCb, &result));
        EpollWaitAndCheck(result.step, ++expectStep);

        ASSERT_EQ(GMERR_OK, pthread_join(threadId, NULL));
        bool isValid = (result.exportDataStatus == GMERR_DATA_EXCEPTION && result.createLabelStatus == GMERR_OK) ||
                       (result.exportDataStatus == GMERR_OK && result.createLabelStatus == GMERR_OK);
        ASSERT_TRUE(isValid);
        if (result.exportDataStatus != GMERR_OK) {
            isRollBack = true;
            ASSERT_EQ(GMERR_OK, GmcTransRollBackAsync(asyncConn, AsyncOperationCb, &result.step));
            EpollWaitAndCheck(result.step, ++expectStep);
            break;
        }
        if (result.createLabelStatus == GMERR_OK) {
            ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(asyncStmt, "T10", AsyncOperationCb, &result.step));
            EpollWaitAndCheck(result.step, ++expectStep);
        }
    }
    if (!isRollBack) {
        ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    }

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(GmcClearNamespaceAsync(asyncStmt, "src", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");
}

constexpr uint32_t COMMENT_NUM = 32;
constexpr uint32_t HUGE_STRING_SIZE = 15360;
constexpr uint32_t HUGE_VERTEX_SIZE_BYTE = 1024 * 1024 * 30;
static char *g_hugeString = NULL;
static char *g_hugeDmlData = NULL;
static void BigObjStrInit(uint32_t strSize)
{
    g_hugeString = (char *)malloc(strSize);
    (void)memset_s(g_hugeString, strSize, 65, strSize);  // 全部写‘A’
    g_hugeString[10240] = '\0';
    g_hugeDmlData = (char *)malloc(HUGE_VERTEX_SIZE_BYTE);
}

static void BigObjStrFree()
{
    free(g_hugeDmlData);
    g_hugeDmlData = NULL;
    free(g_hugeString);
    g_hugeString = NULL;
}

static uint32_t GenerateDml(
    char *data, uint32_t size, uint32_t editStart, uint32_t editEnd, const char *opChar, uint32_t index = 0)
{
    std::string indexStr;
    std::string dmlStr;
    std::string nodeStr;
    std::string opString = opChar;
    std::string commentStr = g_hugeString;

    dmlStr = R"({"op": ")" + opString + "\", ";
    if (index == 1 && (opString == "merge" || opString == "remove_graph" || opString == "delete_graph")) {
        dmlStr += R"("key_name": "hugeL1.PK", "key_value": ["NULL",1], )";
    }
    dmlStr += R"("index": )" + to_string(index) + ", ";
    if (opString == "replace_graph") {
        opString = "merge";
    }
    for (uint32_t loop = 0; loop < 8; loop++) {
        // 处理第一层CX节点，插入32条10kb数据
        nodeStr = "C" + to_string(loop + 1);
        dmlStr += "\"" + nodeStr + R"(": {"op": ")" + opString + "\", ";
        for (uint32_t i = editStart; i < editEnd; i++) {
            indexStr = to_string(i + 1);
            dmlStr += " \"comment" + indexStr + "\": \"" + commentStr + "\", ";
        }
        // 处理第二层CX1节点，插入32条10kb数据
        dmlStr += "\"" + nodeStr + R"(1": {"op": ")" + opString + "\", ";
        for (uint32_t i = editStart; i < editEnd; i++) {
            indexStr = to_string(i + 1);
            dmlStr += " \"comment" + indexStr + "\": \"" + commentStr + "\", ";
        }
        // 处理第三层CX11, CX12节点， 各插入32条10kb数据
        dmlStr += "\"" + nodeStr + R"(11": {"op": ")" + opString + "\", ";
        for (uint32_t i = editStart; i < editEnd; i++) {
            indexStr = to_string(i + 1);
            dmlStr += " \"comment" + indexStr + "\": \"" + commentStr + "\"";
            if (i != editEnd - 1) {
                dmlStr += ", ";
            }
        }
        dmlStr += "}, ";
        dmlStr += "\"" + nodeStr + R"(12": {"op": ")" + opString + "\", ";
        for (uint32_t i = editStart; i < editEnd; i++) {
            indexStr = to_string(i + 1);
            dmlStr += " \"comment" + indexStr + "\": \"" + commentStr + "\"";
            if (i != editEnd - 1) {
                dmlStr += ", ";
            }
        }
        dmlStr += "}}}";
        if (loop != 7) {
            dmlStr += ", ";
        }
    }
    dmlStr += "}";

    memset_s(data, size, 0, size);
    memcpy_s(data, size, dmlStr.c_str(), dmlStr.size());

    return dmlStr.size();
}

static void GenerateL1Dml(
    char *data, uint32_t size, uint32_t editStart, uint32_t editEnd, const char *opChar, uint32_t index)
{
    std::string dmlStr;
    uint32_t offset = 0;

    memset_s(data, size, 0, size);
    printf("%s", data);

    dmlStr = R"({"op": "merge", "hugeL1": [)";
    memcpy_s(data, size, dmlStr.c_str(), dmlStr.size());
    offset += dmlStr.size();
    offset += GenerateDml(data + offset, size - offset, editStart, editEnd, opChar, index);
    dmlStr = R"(]})";
    memcpy_s(data + offset, size - offset, dmlStr.c_str(), dmlStr.size());
}

static void ExecuteHugeL1Batch(
    GmcConnT *conn, GmcStmtT *stmt, const char *execJson, const char *lltJson, const char *op, int32_t segNum)
{
    ASSERT_NE(segNum, 0);
    uint32_t offset = COMMENT_NUM / segNum;
    for (int32_t i = 0; i < segNum; i++) {
        if (op != NULL) {
            GenerateL1Dml(g_hugeDmlData, HUGE_VERTEX_SIZE_BYTE, i * offset, (i + 1) * offset, op, i + 1);
        }
        ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(conn, stmt, "hugeRoot", execJson, lltJson));
    }
}

// 测试点：大对象场景导入导出成功
TEST_F(StYang, ConfigDataPersistenceWithHugeObj)
{
    GmcConnT *asyncConn = Bigconn;
    GmcStmtT *asyncStmt = Bigstmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/Llt_Yang_Tree_10m_LargeVertex.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/Llt_Yang_Tree_10m_LargeEdge.json";

    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    BigObjStrInit(HUGE_STRING_SIZE);
    g_hugeString[2048] = '\0';
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    ASSERT_NO_FATAL_FAILURE(ExecuteHugeL1Batch(asyncConn, asyncStmt, g_hugeDmlData, g_lltJsonTree, "insert", 1));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));

    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result_huge_obj.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");

    BigObjStrFree();
}

// 测试点：大对象场景导入导出成功
TEST_F(StYang, ConfigDataPersistenceWithMultiHugeObj)
{
    GmcConnT *asyncConn = Bigconn;
    GmcStmtT *asyncStmt = Bigstmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/Llt_Yang_Tree_10m_LargeVertex.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/Llt_Yang_Tree_10m_LargeEdge.json";

    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    BigObjStrInit(HUGE_STRING_SIZE);
    g_hugeString[2048] = '\0';
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    ASSERT_NO_FATAL_FAILURE(ExecuteHugeL1Batch(asyncConn, asyncStmt, g_hugeDmlData, g_lltJsonTree, "insert", 2));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));

    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");

    BigObjStrFree();
}

// 测试点：导入成功后做模型检验和语义检验成功
TEST_F(StYang, ConfigDataPersistenceWithDoubleValidation)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch2.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch2.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData2.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &exportConfig));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result2.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcNodeT *rootNode;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(asyncStmt, "T0", GMC_OPERATION_SUBTREE_FILTER));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(asyncStmt, &rootNode));

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.json = NULL},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_FULL_JSON,
        .filter = &filter,
    };

    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(asyncStmt, res));
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateAsync(asyncStmt, GMC_YANG_VALIDATION_WHEN, res, NULL));
    string subreeJson2 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result3.json");
    std::vector<std::string> expectReplys1 = {subreeJson2};
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys1));
    // 校验结束，提交事务
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：导入成功后做语义检验成功
TEST_F(StYang, ConfigDataPersistenceWithValidation)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch2.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch2.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData2.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);

    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(asyncStmt, res));

    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &exportConfig));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result2.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcNodeT *rootNode;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(asyncStmt, "T0", GMC_OPERATION_SUBTREE_FILTER));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(asyncStmt, &rootNode));

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.json = NULL},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_FULL_JSON,
        .filter = &filter,
    };

    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    // 数据校验
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateAsync(asyncStmt, GMC_YANG_VALIDATION_WHEN, res, NULL));
    string subreeJson2 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result3.json");
    std::vector<std::string> expectReplys1 = {subreeJson2};
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys1));
    // 校验结束，提交事务
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

void *ModelCheckMultiTime(void *args)
{
    ResultT *result = (ResultT *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    StYang::CreateAsyncConnAndStmt(&conn, &stmt, false);

    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    // use namespace
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(stmt, result->nspName, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);

    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    uint64_t startTime = DbGetNsec();
    ValidateParam param = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = res,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL,
        .startTime = startTime,
        .printTime = true,
        .printSize = true};
    EXPECT_EQ(GMERR_OK, GmcYangValidateModelAsync(stmt, AsyncValidateCb, &param));
    EpollWaitAndCheck(step, ++expectStep);

    StYang::DestroyConnAndStmt(conn, stmt);
    return (void *)0;
}

#ifndef ASAN
// 测试点：导出中和模型检验并发
TEST_F(StYang, ConfigDataExportAndModeValidate)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch2.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch2.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData2.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(asyncStmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    bool isRollBack = false;
    for (uint32_t i = 0; i < 100; i++) {
        pthread_t threadId = 0;
        ResultT result = {0};
        result.nspName = "src";
        ASSERT_EQ(GMERR_OK, pthread_create(&threadId, NULL, ModelCheckMultiTime, &result));

        uint32_t expectStep = 0;
        ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
        GmcPersistenceConfigT exportConfig =
            GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
        ASSERT_EQ(GMERR_OK, GmcYangExportDataAsync(asyncStmt, &exportConfig, AsyncExportCb, &result));
        EpollWaitAndCheck(result.step, ++expectStep);

        ASSERT_EQ(GMERR_OK, pthread_join(threadId, NULL));
        bool isValid = (result.exportDataStatus == GMERR_DATA_EXCEPTION || result.exportDataStatus == GMERR_OK);
        ASSERT_TRUE(isValid);
        if (result.exportDataStatus != GMERR_OK) {
            isRollBack = true;
            ASSERT_EQ(GMERR_OK, GmcTransRollBackAsync(asyncConn, AsyncOperationCb, &result.step));
            EpollWaitAndCheck(result.step, ++expectStep);
            break;
        }
    }
    if (!isRollBack) {
        ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    }

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(GmcClearNamespaceAsync(asyncStmt, "src", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");
}
#endif

// 测试点：开启事务后导入数据，导入会提交当前事务再做DCL
TEST_F(StYang, ConfigDataImportDataWithTranscation)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    const char *newDataJsonTree = "006_graph/yang/read_by_persistence/operation/tree_add_list.json";
    LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0", (GetFileContext(newDataJsonTree)).c_str(), g_lltJsonTree);

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn, GMERR_DATA_EXCEPTION));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result4.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：带enum定义表导入导出
TEST_F(StYang, ConfigDataPersistenceWithEnum)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch3.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch3.json";

    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));

    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：带alias定义表导入导出
TEST_F(StYang, ConfigDataPersistenceWithAlias)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch4.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch4.json";

    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));

    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点:namespace清空后导入成功
TEST_F(StYang, ConfigDataImportCleaningNamespace)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));

    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ASSERT_NO_FATAL_FAILURE(GmcClearNamespaceAsync(asyncStmt, "src", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "src", &config));

    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));

    ASSERT_NO_FATAL_FAILURE(GmcClearNamespaceAsync(asyncStmt, "dst", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);

    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result1.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：导出前做模型检验，导入后查询不到默认值不可见的节点
TEST_F(StYang, ConfigDataExportAfterDataValidate)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch2.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch2.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData2.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    // 模型校验
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(asyncStmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateAsync(asyncStmt, GMC_YANG_VALIDATION_WHEN, res, NULL));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &exportConfig));
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result3.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcNodeT *rootNode;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(asyncStmt, "T0", GMC_OPERATION_SUBTREE_FILTER));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(asyncStmt, &rootNode));

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.json = NULL},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_FULL_JSON,
        .filter = &filter,
    };

    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "src"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangSrc", "src");

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

// 测试点：全部表有数据，重启server导入导出成功
TEST_F(StYang, ConfigDataPersistenceRestart)
{
    GmcConnT *asyncConn = StYang::NormalConn;
    GmcStmtT *asyncStmt = StYang::NormalStmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch1.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    StYang::RestartDbServerWithAdditionConfig(DefaultConfigWithOutDirectWrite);
    asyncConn = StYang::NormalConn;
    asyncStmt = StYang::NormalStmt;
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result1.json");
    std::vector<std::string> expectReplys = {subreeJson1};
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_JSON;
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}

TEST_F(StYang, ConfigDataPersistenceRestart2)
{
    GmcConnT *asyncConn = StYang::NormalConn;
    GmcStmtT *asyncStmt = StYang::NormalStmt;
    const char *vertexLabelFileTree = "006_graph/yang/read_by_persistence/vertexLabelBatch5.json";
    const char *edgeLabelFileTree = "006_graph/yang/read_by_persistence/edgeLabelBatch1.json";

    const char *baseDataJsonTree = "006_graph/yang/read_by_persistence/operation/basicPartialData5.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPersistenceBasicLabelAsync(asyncStmt, "YangSrc", "src", vertexLabelFileTree, edgeLabelFileTree));
    ASSERT_NO_FATAL_FAILURE(InitPersistenceBasicDataAsync(asyncConn, asyncStmt, "src", baseDataJsonTree));
    GmcPersistenceConfigT config =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ExportDataAsync(asyncConn, asyncStmt, "src", &config));
    StYang::RestartDbServerWithAdditionConfig(DefaultConfigWithOutDirectWrite);
    asyncConn = StYang::NormalConn;
    asyncStmt = StYang::NormalStmt;
    ASSERT_NO_FATAL_FAILURE(LltCreateTablespaceAndNamespaceAsync(asyncStmt, 300, "YangDst", "dst"));
    config = GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, "/config_data/", NULL);
    ASSERT_NO_FATAL_FAILURE(ImportDataAsync(asyncConn, asyncStmt, "dst", &config));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    string subreeJson1 = GetFileContext("006_graph/yang/read_by_persistence/subtree/subtree_result_union.json");
    std::vector<std::string> expectReplys = {subreeJson1};

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = "{}";
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.jsonFlag = GMC_JSON_COMPACT;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };

    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeBatchReplyAsync(asyncStmt, filters, expectReplys));

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt, "dst"));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropTablespaceAndNamespaceAsync(asyncStmt, "YangDst", "dst");
}
