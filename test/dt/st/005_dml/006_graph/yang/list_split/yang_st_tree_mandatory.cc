/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: yang_st_tree_mandatory.cc
 * Description: yang tree mandatory validate testcases
 * Author: yuxinxin
 * Create: 2022-9-12
 */

#include "yang/yang_common_st.h"

static const char *g_lltJson = R"({"yang_model":1})";
static const char *g_vertexFile = "006_graph/yang/yang_st_data/mandatory_vertexLabelBatch.json";
static const char *g_edgeFile = "006_graph/yang/yang_st_data/mandatory_edgeLabelBatch.json";
static const char *g_configJson = R"({"max_record_count":1000, "auto_increment": 1, "yang_model":1})";

static const char *g_insertJson1 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":14,
            "T0Choice": {
                "CaseA": {
                    "F0": 5
                },
                "CaseB": {
                    "F0": 7
                }
            }
        }
    }
)";
static const char *g_replyJson1 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":14,
            "T0Choice": {
                "CaseB": {
                    "F0": 7
                }
            }
        }
    }
)";
static const char *g_insertJson2 = R"(
    {
        "op": "none",
        "T0Container":{
            "op": "none",
            "T0Choice": {
                "op": "none",
                "CaseB": {
                    "op": "none",
                    "NestChoice": {
                        "NestCase": {"F0": 1}
                    }
                }
            }
        }
    }
)";
static const char *g_replyJson2 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":14,
            "T0Choice": {
                "CaseB": {
                    "F0": 7,
                    "NestChoice": {
                        "NestCase": {
                            "F0": 1
                        }
                    }
                }
            }
        }
    }
)";
// 测试场景：嵌套choice-case下，验证caseA caseB互斥删除, 未插入mandatory NestChoice，预计校验失败
TEST_F(StYang, MandatoryWithContainer)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 执行dml操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson1, GMERR_OK, g_lltJson));

    // 验证结果
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MANDATORY_CHOICE,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "mandatory verify no choice",
        .expectedErrPath = "/T0/T0Container/T0Choice/CaseB/NestChoice"};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_UNDEFINED, .cfgJson = NULL};
    ASSERT_EQ(GMERR_INVALID_VALUE, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    cfg.type = GMC_YANG_VALIDATION_MANDATORY;
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);
    GmcSubtreeFilterItemT filter = {.rootName = "T0", .subtree = {.json = "{}"}};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_replyJson1));

    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson2, GMERR_OK, g_lltJson));

    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_replyJson2));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

static const char *g_insertJson3 = R"(
    {
        "F0": 1,
        "T0Container": {
            "F0": 1,
            "T0Choice": {
                "CaseA": {
                    "F0": 1
                }
            }
        },
        "T0::T1": [{
            "F0": 7,
            "T1Choice": {
                "CaseB": {
                    "F0": 12,
                    "NestChoice": {
                        "NestCaseA": {
                            "F0": 5
                        }
                    }
                }
            }
        }]
    }
)";
static const char *g_replyJson3 = R"(
    {
        "F0": 1,
        "T0Container": {
            "F0": 1,
            "T0Choice": {
                "CaseA": {
                    "F0": 1
                }
            }
        },
        "T0::T1": [{
            "F0": 7,
            "T1Choice": {
                "CaseB": {
                    "F0": 12,
                    "NestChoice": {
                        "NestCaseA": {
                            "F0": 5
                        }
                    }
                }
            }
        }]
    }
)";
static const char *g_insertJson4 = R"(
    {
        "op": "none",
        "T0::T1": [{
            "op": "none",
            "key_value": ["NULL", 7],
            "T1Choice": {
                "op": "none",
                "CaseB": {
                    "op": "none",
                    "NestChoice": {
                        "op": "none",
                        "NestCaseA": {
                            "op": "delete_graph"
                        }
                    }
                }
            }
        }]
    }
)";
static const char *g_replyJson4 = R"(
    {
        "F0": 1,
        "T0Container": {
            "F0": 1,
            "T0Choice": {
                "CaseA": {
                    "F0": 1
                }
            }
        },
        "T0::T1": [{
            "F0": 7,
            "T1Choice": {
                "CaseB": {
                    "F0": 12
                }
            }
        }]
    }
)";
// 测试场景：嵌套choice-case下，删除mandatory choice下的case，预计校验返回失败
TEST_F(StYang, MandatoryWithList)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 执行dml操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson3, GMERR_OK, g_lltJson));

    // 验证结果
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);
    GmcSubtreeFilterItemT filter = {.rootName = "T0", .subtree = {.json = "{}"}};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_replyJson3));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    // 执行dml操作
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson4, GMERR_OK, g_lltJson));

    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MANDATORY_CHOICE,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "mandatory verify no choice",
        .expectedErrPath = "/T0/T0::T1[F0=7]/T1Choice/CaseB/NestChoice"};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_replyJson4));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

static const char *g_insertJson5 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":33,
            "T0Choice": {
                "CaseA": {
                    "F0": 7,
                    "T0::T3": [{
                        "F0": 10,
                        "T3Container": {
                            "F0": 1,
                            "F1": 1
                        }
                    }]
                }
            }
        }
    }
)";
static const char *g_replyJson5 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":33,
            "T0Choice": {
                "CaseA": {
                    "F0": 7,
                    "T0::T3": [{
                        "F0": 10,
                        "T3Container": {
                            "F0": 1,
                            "F1": 1
                        }
                    }]
                }
            }
        }
    }
)";
static const char *g_insertJson6 = R"(
    {
        "op": "none",
        "T0Container":{
            "op": "none",
            "T0Choice": {
                "op": "none",
                "CaseB": {
                    "F0": 7,
                    "NestChoice": {
                        "NestCase": {
                            "F0": 21
                        }
                    }
                }
            }
        }
    }
)";
static const char *g_replyJson6 = R"(
    {
        "F0": 1,
        "T0Container": {
            "F0": 33,
            "T0Choice": {
                "CaseB": {
                    "F0": 7,
                    "NestChoice": {
                        "NestCase": {
                            "F0": 21
                        }
                    }
                }
            }
        }
    }
)";
// 测试场景：验证list下的choice case互斥删除，预计mandatory校验成功
TEST_F(StYang, MandatoryWithNestList)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 执行dml操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson5, GMERR_OK, g_lltJson));

    // 验证结果
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);
    GmcSubtreeFilterItemT filter = {.rootName = "T0", .subtree = {.json = "{}"}};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_replyJson5));

    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson6, GMERR_OK, g_lltJson));
    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_replyJson6));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

static const char *g_insertJson7 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":314,
            "T0Choice": {
                "CaseA": {
                    "F0": 7,
                    "T0::T3": [{
                        "F0": 10,
                        "T3Container":{
                            "F0": 12
                        }
                    }]
                }
            }
        }
    }
)";
static const char *g_insertJson8 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":314,
            "T0Choice": {
                "CaseA": {
                    "F0": 7,
                    "T0::T3": [{
                        "F0": 10,
                        "T3Container":{
                            "F0": 12,
                            "F1": 48
                        }
                    }]
                }
            }
        }
    }
)";
// 测试场景：验证复杂模型下的字段mandatory校验，未插入F1，预计校验返回失败
TEST_F(StYang, MandatoryWithBasicField)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 执行dml操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson7, GMERR_OK, g_lltJson));

    // 验证结果
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "mandatory verify no field",
        .expectedErrPath = "/T0/T0Container/T0Choice/CaseA/T0::T3[F0=10]/T3Container/F1"};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, GMERR_TRANSACTION_ROLLBACK));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson8, GMERR_OK, g_lltJson));
    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    GmcSubtreeFilterItemT filter = {.rootName = "T0", .subtree = {.json = "{}"}};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, g_insertJson8));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

static const char *g_insertJson9 = R"(
    {
        "F0": 1,
        "T0Container": {
            "F0": 1,
            "T0Choice": {
                "CaseA": {
                    "F0": 1
                }
            }
        },
        "T0::T2": [{
            "F0": 14,
            "T2Container": {
                "F0":21,
                "T2Choice":{
                    "CaseB": {
                        "F0": 15,
                        "NestChoice": {
                            "NestCase": {
                                "F0": 5
                            }
                        }
                    }
                }
            }
        }]
    }
)";
static const char *g_insertJson10 = R"(
    {
        "op": "none",
        "T0::T2": [{
            "op": "none",
            "key_value": ["NULL", 14],
            "T2Container":{
                "op": "none",
                "T2Choice": {
                    "op": "none",
                    "CaseB": {
                        "op": "none",
                        "NestChoice": {
                            "op": "delete_graph"
                        }
                    }
                }
            }
        }]
    }
)";
// 测试场景：复合list的嵌套choice-case下，删除mandatory choice，预计校验返回失败
TEST_F(StYang, MandatoryWithDeleteNest)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 执行dml操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson9, GMERR_OK, g_lltJson));

    // 验证结果
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);

    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson10, GMERR_OK, g_lltJson));
    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MANDATORY_CHOICE,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "mandatory verify no choice",
        .expectedErrPath = "/T0/T0::T2[F0=14]/T2Container/T2Choice/CaseB/NestChoice"};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, GMERR_TRANSACTION_ROLLBACK));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

static const char *g_insertJson11 = R"(
    {
        "F0": 1,
        "T0Container": {
            "F0": 1,
            "T0Choice": {
                "CaseA": {
                    "F0": 1
                }
            }
        }
    }
)";
static const char *g_insertJson12 = R"(
    {
        "op": "merge",
        "F0": 1
    }
)";
static const char *g_deleteJson1 = R"(
    {
        "yang_model":1,
        "F0": {"propOp":"delete"}
    }
)";
// 测试场景：删除vertex中nullabel=false字段，成功删除，mandatory校验预期失败
TEST_F(StYang, MandatoryWithVertexMandatoryField)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 初始化数据
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson11, GMERR_OK, g_lltJson));

    // 预计校验返回成功
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);

    // 删除nullable=false字段，预计校验返回失败
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson12, GMERR_OK, g_deleteJson1));
    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "mandatory verify no field",
        .expectedErrPath = "/T0/F0"};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, GMERR_TRANSACTION_ROLLBACK));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

static const char *g_insertJson13 = R"(
    {
        "F0": 1,
        "T0Container":{
            "F0":14,
            "T0Choice": {
                "CaseA": {
                    "F0": 5
                }
            }
        }
    }
)";
static const char *g_insertJson14 = R"(
    {
        "op": "none",
        "T0Container": {
            "op":"merge",
            "F0": 1
        }
    }
)";
static const char *g_deleteJson2 = R"(
    {
        "yang_model":1,
        "T0Container": {
            "F0": {"propOp":"delete"}
        }
    }
)";
// 测试场景：删除node中nullabel=false字段，成功删除，mandatory校验预期失败
TEST_F(StYang, MandatoryWithNodeMandatoryField)
{
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile, g_configJson));
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 初始化数据
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson13, GMERR_OK, g_lltJson));

    // 预计校验返回成功
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    EpollWaitAndCheck(step, ++expectStep);

    // 删除nullable=false字段，预计校验返回失败
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0", g_insertJson14, GMERR_OK, g_deleteJson2));
    ValidateParam param2 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "mandatory verify no field",
        .expectedErrPath = "/T0/T0Container/F0"};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param2));
    EpollWaitAndCheck(step, ++expectStep);
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, GMERR_TRANSACTION_ROLLBACK));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, g_vertexFile, g_edgeFile));
}

#ifdef YANG_SUPPORT_GRAPH
const char *g_mandatoryVertexSchema = R"([{
        "type":"container",
        "name":"T0Vertex",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"int32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T0Vertex",
                "name":"T0.F0",
                "fields":["ID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    },
    {
        "name": "TABLE_YANG_REMOVED_DEFAULT_RECORD",
        "type": "list",
        "np_access": true,
        "fields": [
            {
                "name": ":id",
                "type": "uint32",
                "nullable": false,
                "auto_increment": true
            },
            {
                "name": ":pid",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "labelId",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "nodeId",
                "type": "uint16",
                "nullable": false
            },
            {
                "name": "propeId",
                "type": "uint32",
                "nullable": false
            }
        ],
        "keys": [
            {
                "node": "TABLE_YANG_REMOVED_DEFAULT_RECORD",
                "name": "k0",
                "fields": [
                    ":id",
                    ":pid",
                    "labelId",
                    "nodeId",
                    "propeId"
                ],
                "index": {
                    "type": "primary"
                },
                "constraints": {
                    "unique": true
                }
            }
        ]
    }])";

static const char *g_insertJson15 = R"(
    {
        "F0": 1
    }
)";

// 测试场景：mandatory和删除场景并发，不会死锁，校验预期成功
TEST_F(StYang, MandatoryWithDrop)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ASSERT_NO_FATAL_FAILURE(CreateAsyncConnAndStmt(&conn, &stmt));
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_mandatoryVertexSchema, cfgJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
    // 模型校验
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(stmt, res));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 初始化数据
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(
        LltMultiBatchExecuteByJsonWithDiffAsync(conn, stmt, batch, "T0Vertex", g_insertJson15, GMERR_OK, g_lltJson));
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ASSERT_NO_FATAL_FAILURE(CreateAsyncConnAndStmt(&conn1, &stmt1));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt1, "T0Vertex", AsyncOperationCb, &step));

    // 预计校验返回成功
    ValidateParam param1 = {.step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = true, .failCount = 0},
        .isValidateErrorPath = false,
        .expectedErrCode = GMC_VIOLATES_BUTT,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = NULL,
        .expectedErrPath = NULL};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ASSERT_EQ(GMERR_OK, GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param1));
    ASSERT_EQ(GMERR_OK, GmcTransCommitAsync(conn, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 4);

    ASSERT_NO_FATAL_FAILURE(DestroyConnectionAndStmt(conn, stmt));
    ASSERT_NO_FATAL_FAILURE(DestroyConnectionAndStmt(conn1, stmt1));
}
#endif
