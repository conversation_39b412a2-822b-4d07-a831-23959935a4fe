[{"type": "record", "name": "simpleLabel", "schema_version": 7, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "uint8", "nullable": false}, {"name": "F7", "type": "time", "nullable": true}, {"name": "F8", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F9", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F10", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F11", "type": "uint32", "nullable": false}, {"name": "F12", "type": "uint32", "nullable": false}, {"name": "F13", "type": "uint8: 4", "nullable": true}, {"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "int32", "nullable": true}, {"name": "F17", "type": "uint32", "nullable": true}, {"name": "F18", "type": "int16", "nullable": true}, {"name": "F19", "type": "uint16", "nullable": true}, {"name": "F20", "type": "int8", "nullable": true}, {"name": "F21", "type": "uint8", "nullable": true}, {"name": "F22", "type": "time", "nullable": true}, {"name": "F23", "type": "fixed", "nullable": true, "size": 8}, {"name": "F24", "type": "uint8: 5", "nullable": true}, {"name": "F25", "type": "uint16: 10", "nullable": true}, {"name": "F26", "type": "uint32: 17", "nullable": true}, {"name": "F27", "type": "uint64: 33", "nullable": true}, {"name": "F28", "type": "boolean", "nullable": true}, {"name": "F29", "type": "float", "nullable": true}, {"name": "F30", "type": "double", "nullable": true}, {"name": "F31", "type": "bitmap", "nullable": true, "size": 8}], "keys": [{"node": "simpleLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "simpleLabel", "name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"node": "simpleLabel", "name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "simpleLabel", "name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "simpleLabel", "name": "lpm4_key", "fields": ["F3", "F11", "F12", "F6"], "index": {"type": "lpm4_tree_bitmap"}, "constraints": {"unique": true}}]}]