/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for client and server in same process mode
 * Author: shenjunhui
 * Create: 2023-07-16
 */
#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include "stdio.h"
#include "stdlib.h"
#include "client_common_st.h"
#include "storage_st_common.h"
#include "tools_st_common.h"

class StClientUm : public StClient {
public:
    StClientUm(){};
    virtual ~StClientUm(){};
    static void SetUpTestCase();
};

void StClientUm::SetUpTestCase()
{
    SetClientServerSameProcess();
    StClient::SetUpTestCase();
}

uint32_t g_testFlag = 0;

void *ClientOperation(void *para)
{
    st_clt_init();
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (g_testFlag == 0) {
        sleep(1);
    }
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    st_clt_uninit();
    return NULL;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
class ClientStCSSP : public testing::TestWithParam<StClientTestData> {};

#ifdef DIRECT_WRITE
TEST_F(ClientStCSSP, sameProcessWithDirectWrite)
{
    SetClientServerSameProcess();
    EXPECT_EQ(GMERR_CONFIG_ERROR,
        StartDbServerWithConfigWithRet(" \"userPolicyMode=0\" \"DBA=root:st_005_dml;gmrule;gmimport\" "
                                       "\"udfEnable=1\" \"isFastReadUncommitted=0\" \"directWrite=1\""));
    UnSetClientServerSameProcess();
    EXPECT_EQ(
        GMERR_OK, StartDbServerWithConfigWithRet(" \"userPolicyMode=0\" \"DBA=root:st_005_dml;gmrule;gmimport\" "
                                                 "\"udfEnable=1\" \"isFastReadUncommitted=0\" \"directWrite=1\""));
    ShutDownDbServer();
}
#endif

TEST_F(ClientStCSSP, multiServer)
{
    SetClientServerSameProcess();
    EXPECT_EQ(
        GMERR_OK, StartDbServerWithConfigWithRet(" \"userPolicyMode=0\" \"DBA=root:st_005_dml;gmrule;gmimport\" "
                                                 "\"udfEnable=1\" \"isFastReadUncommitted=0\" \"directWrite=0\""));
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT,
        StartDbServerWithConfigWithRet(" \"userPolicyMode=0\" \"DBA=root:st_005_dml;gmrule;gmimport\" "
                                       "\"udfEnable=1\" \"isFastReadUncommitted=0\" \"directWrite=0\""));
    EXPECT_EQ(GMERR_OK, GmsShmemClear());
    system("kill -9 `pidof gmserver`");
}

#endif
