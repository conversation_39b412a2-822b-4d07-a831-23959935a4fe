/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2022-8-1
 */

#include "runtime_st_common.h"
#include "gmc_internal.h"

using namespace std;

class StLongOperationLogWithThresholdDefault : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
    static void TearDownTestCase()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
    }
};

class StLongOperationLogWithThreshold0 : public StLongOperationLogWithThresholdDefault {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig(
            "\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\" \"longProcTimeThreshold=0\" \"logLengthMax=1024\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
};

class StLongOperationLogWithThresholdClose : public StLongOperationLogWithThresholdDefault {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\" \"longProcTimeThreshold=-1\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
};

class StLongOperationLogWithThreshold1 : public StLongOperationLogWithThresholdDefault {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\" \"longProcTimeThreshold=1\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
};

class StLongOperationLogWithThreshold5 : public StLongOperationLogWithThresholdDefault {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\" \"longProcTimeThreshold=5\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
};

class StLongOperationLogWithThreshold20 : public StLongOperationLogWithThresholdDefault {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\" \"longProcTimeThreshold=20\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
};

class StLongOperationLogWithThreshold500 : public StLongOperationLogWithThresholdDefault {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"isFastReadUncommitted=0\" \"auditLogEnableDML=0\" \"longProcTimeThreshold=500\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
};

#if !(defined RTOSV2 || defined RTOSV2X || defined HPE || defined ASAN)
static const char *g_labelName = "VertexLabel";
static const char *g_indexName = "PrimaryKey";
static const char *g_cfgJson = "{\"max_record_count\":10000}";
static const char *g_labelJson =
    R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PrimaryKey",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalHashKey",
                    "fields":["F1"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"HashClusterKey",
                    "fields":["F2"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalKey",
                    "fields":["F3"],
                    "index":{"type":"local"},
                    "constraints":{"unique":false}
                }
            ]
        }])";

static const uint32_t g_maxLogLen = 1025;
static const uint32_t g_defExeNum = 1000;
static const uint32_t g_timeOut = 600000;
static const char *g_truncated = "truncated log";
static const string g_invalidStr("invalid");

static Status SetConfigure(const char *config = NULL)
{
    string name = string("gmadmin ") + string(config);
    return system(name.c_str());
}

static void RtClearRunLog()
{
    system("rm -rf ./log");
}

class RtLongLogChecker {
public:
    explicit RtLongLogChecker(int32_t timeThreshold) : threshold(timeThreshold)
    {}
    Status RtInitLogResult();
    Status RtCheckLogExist() const;
    Status RtCheckLongLog(uint32_t logCount, uint32_t expectedOpCount, const string &expectedOpCode,
        const string &expectedLabelName, const string &expectedIndexName = "");
    Status RtCheckLogFinish() const;

private:
    Status RtExecSystemCmd(const char *format, ...);
    void RtInitLogData();
    void RtSkipElements(int32_t strEnd, uint32_t skipCount = 1);
    uint64_t RtGetNextNum(int32_t strEnd);
    string RtGetNextStr(int32_t strEnd);
    Status RtGetNextRecord();
    bool RtCheckValid(uint64_t actualTime);
    Status RtCheckUsedTime();
    Status RtCheckLogCommon(uint32_t expectedOpCount, const string &expectedOpCode, const string &expectedLabelName,
        const string &expectedIndexName);
    Status RtCheckFullLog(uint32_t expectedOpCount, const string &expectedOpCode, const string &expectedLabelName,
        const string &expectedIndexName);
    Status RtCheckPartLog(uint32_t expectedOpCount, const string &expectedOpCode, const string &expectedLabelName,
        const string &expectedIndexName);

private:
    int32_t threshold;
    string record;
    string truncatedStr = g_truncated;
    int32_t position = 0;
    int32_t lastStrEnd = -1;
    int32_t strEnd = -1;

private:
    string tableName;
    string indexName;
    string opCode;
    uint64_t thresholdTime;
    uint64_t totalTime;
    uint64_t reqRecvTime;
    uint64_t scheduleTime;
    uint64_t prepareTime;
    uint64_t executeTime;
    uint32_t operationCount;
    uint64_t longestPrepareTime;
    uint64_t longestExecuteTime;
    bool truncFound;
};

Status RtLongLogChecker::RtExecSystemCmd(const char *format, ...)
{
    va_list args;
    va_start(args, format);
    char cmd[g_maxLogLen] = {0};
    int32_t ret = vsnprintf_s(cmd, sizeof(cmd), sizeof(cmd) - 1, format, args);
    if (ret < 0) {
        return GMERR_INTERNAL_ERROR;
    }
    va_end(args);

    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        return GMERR_FILE_OPERATE_FAILED;
    }

    char buf[g_maxLogLen] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        record.append(buf);
    }
    ret = pclose(fd);
    if (ret < 0) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

void RtLongLogChecker::RtInitLogData()
{
    tableName = g_invalidStr;
    indexName = g_invalidStr;
    opCode = g_invalidStr;
    thresholdTime = DB_INVALID_UINT64;
    totalTime = DB_INVALID_UINT64;
    reqRecvTime = DB_INVALID_UINT64;
    scheduleTime = DB_INVALID_UINT64;
    prepareTime = DB_INVALID_UINT64;
    executeTime = DB_INVALID_UINT64;
    operationCount = DB_INVALID_UINT32;
    longestPrepareTime = DB_INVALID_UINT64;
    longestExecuteTime = DB_INVALID_UINT64;
    truncFound = false;
}

Status RtLongLogChecker::RtInitLogResult()
{
    string cmdStr = string("grep -Er \"long op log: time_threshold: ") + to_string(threshold) +
                    string("\" ./log/run/rgmserver/rgmserver*log | sed \"s/.*long "
                           "op log: \\(.*\\)/\\1/\"");
    return RtExecSystemCmd(cmdStr.c_str());
}

inline Status RtLongLogChecker::RtCheckLogExist() const
{
    if (threshold < 0 && !record.empty()) {
        cout << "RtCheckLogExist1" << endl;
        cout << "threshold:" << threshold << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (threshold >= 0 && record.empty()) {
        cout << "RtCheckLogExist2" << endl;
        cout << "threshold:" << threshold << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

void RtLongLogChecker::RtSkipElements(int32_t strEnd, uint32_t skipCount)
{
    for (uint32_t i = 0; i < skipCount; ++i) {
        while (position < strEnd && record[position] != ':') {
            ++position;
        }
        if (position > strEnd) {
            throw out_of_range(g_truncated);
        }
        ++position;
    }
}

uint64_t RtLongLogChecker::RtGetNextNum(int32_t strEnd)
{
    while (position < strEnd && record[position] != ':') {
        ++position;
    }
    position += 2;
    if (position >= strEnd) {
        throw out_of_range(g_truncated);
    }
    while (position < strEnd && !isdigit(record[position])) {
        ++position;
    }
    if (position > strEnd) {
        throw out_of_range(g_truncated);
    }
    uint32_t start = position;
    while (position < strEnd && isdigit(record[position])) {
        ++position;
    }
    return (uint64_t)stoull(record.substr(start, position - start));
}

string RtLongLogChecker::RtGetNextStr(int32_t strEnd)
{
    while (position < strEnd && record[position] != ':') {
        ++position;
    }
    position += 2;
    if (position >= strEnd) {
        throw out_of_range(g_truncated);
    }
    uint32_t start = position;
    while (position < strEnd && record[position] != ',') {
        ++position;
    }
    return record.substr(start, position - start);
}

Status RtLongLogChecker::RtGetNextRecord()
{
    RtInitLogData();
    uint32_t size = (uint32_t)record.size();
    lastStrEnd = strEnd;
    position = strEnd + 1;
    try {
        auto pos = record.find('\n', position);
        strEnd = (pos == string::npos) ? size : (int32_t)pos;
        thresholdTime = RtGetNextNum(strEnd) * USECONDS_IN_MSECOND;
        RtSkipElements(strEnd, 2);  // connType, namespaceId暂不检测
        tableName = RtGetNextStr(strEnd);
        tableName = tableName.substr(1, tableName.size() - 2);
        indexName = RtGetNextStr(strEnd);
        indexName = indexName.substr(1, indexName.size() - 2);
        opCode = RtGetNextStr(strEnd);
        RtSkipElements(strEnd, 1);  // connId暂不检测
        totalTime = RtGetNextNum(strEnd);
        reqRecvTime = RtGetNextNum(strEnd);
        scheduleTime = RtGetNextNum(strEnd);
        prepareTime = RtGetNextNum(strEnd);
        executeTime = RtGetNextNum(strEnd);
        operationCount = (uint32_t)RtGetNextNum(strEnd);
        RtSkipElements(strEnd, 1);
        longestPrepareTime = RtGetNextNum(strEnd);
        longestExecuteTime = RtGetNextNum(strEnd);
    } catch (const exception &exp) {
        if (truncatedStr != string(exp.what())) {
            cout << exp.what() << endl;
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

inline bool RtLongLogChecker::RtCheckValid(uint64_t actualTime)
{
    if (actualTime == DB_INVALID_UINT64) {
        truncFound = true;
        return false;
    }
    return true;
}

Status RtLongLogChecker::RtCheckUsedTime()
{
    reqRecvTime = RtCheckValid(reqRecvTime) ? reqRecvTime : 0;
    scheduleTime = RtCheckValid(scheduleTime) ? scheduleTime : 0;
    prepareTime = RtCheckValid(prepareTime) ? prepareTime : 0;
    executeTime = RtCheckValid(executeTime) ? executeTime : 0;
    longestPrepareTime = RtCheckValid(longestPrepareTime) ? longestPrepareTime : 0;
    longestExecuteTime = RtCheckValid(longestExecuteTime) ? longestExecuteTime : 0;
    if (reqRecvTime > totalTime || scheduleTime > totalTime || prepareTime > totalTime || executeTime > totalTime ||
        longestPrepareTime > prepareTime || longestExecuteTime > executeTime ||
        reqRecvTime + scheduleTime + prepareTime + executeTime > totalTime ||
        longestPrepareTime + longestExecuteTime > prepareTime + executeTime) {
        cout << "RtCheckLogCommon1" << endl;
        cout << "reqRecvTime: " << reqRecvTime << ", ";
        cout << "scheduleTime: " << scheduleTime << ", ";
        cout << "prepareTime: " << prepareTime << ", ";
        cout << "executeTime: " << executeTime << ", ";
        cout << "longestPrepareTime: " << longestPrepareTime << ", ";
        cout << "longestExecuteTime: " << longestExecuteTime << "." << endl;
    }
    if (!truncFound &&
        (uint64_t)operationCount * (longestPrepareTime + longestExecuteTime) < prepareTime + executeTime) {
        cout << "RtCheckLogCommon2" << endl;
        cout << "reqRecvTime: " << reqRecvTime << ", ";
        cout << "scheduleTime: " << scheduleTime << ", ";
        cout << "prepareTime: " << prepareTime << ", ";
        cout << "executeTime: " << executeTime << ", ";
        cout << "longestPrepareTime: " << longestPrepareTime << ", ";
        cout << "longestExecuteTime: " << longestExecuteTime << "." << endl;
    }
    return GMERR_OK;
}

Status RtLongLogChecker::RtCheckLogCommon(uint32_t expectedOpCount, const string &expectedOpCode,
    const string &expectedLabelName, const string &expectedIndexName)
{
    if (tableName != g_invalidStr && tableName != expectedLabelName) {
        cout << "RtCheckLogCommon1" << endl;
        cout << "tableName:" << tableName << endl;
        cout << "g_invalidStr:" << g_invalidStr << endl;
        cout << "expectedLabelName:" << expectedLabelName << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (!indexName.empty() && indexName != g_invalidStr && indexName != expectedIndexName) {
        cout << "RtCheckLogCommon2" << endl;
        cout << "indexName:" << indexName << endl;
        cout << "g_invalidStr:" << g_invalidStr << endl;
        cout << "expectedIndexName:" << expectedIndexName << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    operationCount = (operationCount == DB_INVALID_UINT32) ? expectedOpCount : operationCount;
    if (operationCount != expectedOpCount) {
        cout << "RtCheckLogCommon3" << endl;
        cout << "operationCount:" << operationCount << endl;
        cout << "expectedOpCount:" << expectedOpCount << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint64_t expectedTime = (uint64_t)threshold * USECONDS_IN_MSECOND;
    if (RtCheckValid(thresholdTime) && thresholdTime != expectedTime) {
        cout << "RtCheckLogCommon4" << endl;
        cout << "thresholdTime:" << thresholdTime << endl;
        cout << "expectedTime:" << expectedTime << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (RtCheckValid(totalTime) && totalTime < expectedTime) {
        cout << "RtCheckLogCommon5" << endl;
        cout << "totalTime:" << totalTime << endl;
        cout << "expectedTime:" << expectedTime << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (truncFound) {
        return GMERR_OK;
    }
    return RtCheckUsedTime();
}

Status RtLongLogChecker::RtCheckFullLog(uint32_t expectedOpCount, const string &expectedOpCode,
    const string &expectedLabelName, const string &expectedIndexName)
{
    if (opCode != g_invalidStr && opCode != expectedOpCode) {
        cout << "RtCheckFullLog" << endl;
        cout << "opCode:" << opCode << endl;
        cout << "g_invalidStr:" << g_invalidStr << endl;
        cout << "expectedOpCode:" << expectedOpCode << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return RtCheckLogCommon(expectedOpCount, expectedOpCode, expectedLabelName, expectedIndexName);
}

Status RtLongLogChecker::RtCheckPartLog(uint32_t expectedOpCount, const string &expectedOpCode,
    const string &expectedLabelName, const string &expectedIndexName)
{
    if (opCode != g_invalidStr && opCode != expectedOpCode) {
        strEnd = lastStrEnd;
        return GMERR_OK;
    }
    return RtCheckLogCommon(expectedOpCount, expectedOpCode, expectedLabelName, expectedIndexName);
}

Status RtLongLogChecker::RtCheckLongLog(uint32_t logCount, uint32_t expectedOpCount, const string &expectedOpCode,
    const string &expectedLabelName, const string &expectedIndexName)
{
    if (threshold < 0) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < logCount; ++i) {
        ret = RtGetNextRecord();
        if (ret != GMERR_OK) {
            return ret;
        }
        if (threshold == 0) {
            ret = RtCheckFullLog(expectedOpCount, expectedOpCode, expectedLabelName, expectedIndexName);
        } else {
            ret = RtCheckPartLog(expectedOpCount, expectedOpCode, expectedLabelName, expectedIndexName);
            if (strEnd == lastStrEnd) {
                break;
            }
        }
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

inline Status RtLongLogChecker::RtCheckLogFinish() const
{
    if (strEnd + 1 < (int32_t)record.size()) {
        cout << "RtCheckLogFinish" << endl;
        cout << "strEnd:" << strEnd << endl;
        cout << "record.size:" << (int32_t)record.size() << endl;
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status RtSetNormalVertexProperty(GmcStmtT *stmt, uint32_t F1Val, uint32_t F2Val, uint32_t F3Val, uint32_t F4Val)
{
    Status ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(F1Val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Val, sizeof(F2Val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Val, sizeof(F4Val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &F4Val, sizeof(F4Val));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

Status RtSetInsertVertexProperty(GmcStmtT *stmt, GmcOperationTypeE operationType, uint32_t F0Val, uint32_t F1Val,
    uint32_t F2Val, uint32_t F3Val, uint32_t F4Val)
{
    if (operationType != GMC_OPERATION_MERGE) {
        Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(F0Val));
        EXPECT_EQ(GMERR_OK, ret);
    }
    return RtSetNormalVertexProperty(stmt, F1Val, F2Val, F3Val, F4Val);
}

Status RtSetUpdateVertexProperyty(GmcStmtT *stmt, uint32_t F1Val, uint32_t F2Val, uint32_t F3Val, uint32_t F4Val)
{
    return RtSetNormalVertexProperty(stmt, F1Val, F2Val, F3Val, F4Val);
}

static void RtSingleOpBase(int32_t threshold, uint32_t executeNum)
{
    RtClearRunLog();
    Status ret = SetConfigure("-cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt, g_timeOut);
    ret = GmcCreateVertexLabel(stmt, g_labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // Insert Vertex
    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = RtSetInsertVertexProperty(stmt, GMC_OPERATION_INSERT, (int32_t)i, 8, 8, 8, 8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // Merge Vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t primaryKey = 8;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryKey, sizeof(primaryKey));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = RtSetInsertVertexProperty(stmt, GMC_OPERATION_MERGE, 9999, 2, 2, 2, 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // Update Vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    primaryKey = 50;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryKey, sizeof(primaryKey));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = RtSetUpdateVertexProperyty(stmt, 3, 3, 3, 3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // Replace Vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = RtSetInsertVertexProperty(stmt, GMC_OPERATION_REPLACE, 8888, 5, 5, 5, 5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // Delete Vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    primaryKey = 8888;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryKey, sizeof(primaryKey));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    RtLongLogChecker checker(threshold);
    ret = checker.RtInitLogResult();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogExist();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(executeNum, 1, DbGetOpCodeMsg(MSG_OP_RPC_INSERT_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_MERGE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_UPDATE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_REPLACE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_DELETE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogFinish();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StLongOperationLogWithThresholdClose, longop_vertex_no_log_006)
{
    PERSISTENCE_NOT_SUPPORT;
    RtSingleOpBase(-1, 1000);
}

TEST_F(StLongOperationLogWithThreshold0, longop_vertex_all_log_007)
{
    PERSISTENCE_NOT_SUPPORT;
    RtSingleOpBase(0, 200);
}

TEST_F(StLongOperationLogWithThreshold0, longop_kv_operation_008)
{
    RtClearRunLog();
    Status ret = SetConfigure("-cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, ret);

    const char *kvCfgJson = R"({"max_record_count":100})";
    const char *kvTableName = "student";
    GmcConnT *kvConn = NULL;
    GmcStmtT *kvStmt = NULL;
    CreateSyncConnectionAndStmt(&kvConn, &kvStmt, g_timeOut);
    ret = GmcKvCreateTable(kvStmt, kvTableName, kvCfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(kvStmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // Set Kv
    char kvKey[] = "zhangsan";
    uint32_t kvKeyLen = sizeof(kvKey);
    int32_t kvValue = 30;
    ret = GmcKvSet(kvStmt, kvKey, kvKeyLen, &kvValue, sizeof(kvValue));
    EXPECT_EQ(GMERR_OK, ret);

    // Remove Kv
    ret = GmcKvRemove(kvStmt, kvKey, kvKeyLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(kvStmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(kvConn, kvStmt);

    RtLongLogChecker checker(0);
    ret = checker.RtInitLogResult();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogExist();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_SET_KV), kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_DELETE_KV), kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogFinish();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StLongOperationLogWithThreshold0, longop_vertex_split_single_operation_010)
{
    const char *labelName = "runtimeLabel";
    const char *indexName = "runtimeLabel_PK";
    const char *cfgJson = "{\"max_record_count\":10000}";
    const char *labelJson =
        R"([{
        "type":"record",
        "name":"runtimeLabel",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"}
        ],
        "keys":[
            {"node":"runtimeLabel",
             "name":"runtimeLabel_PK",
             "fields":["F0"],
             "index":{"type":"primary"}
            },
            {"node":"runtimeLabel",
             "name":"runtimeLabel_hash",
             "fields":["F1"],
             "index":{"type":"localhash"},
             "constraints":{"unique":false}
            }
        ]
    }])";

    RtClearRunLog();
    Status ret = SetConfigure("-cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt, g_timeOut);
    ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 0;
    uint32_t opCount = 2000;
    for (uint32_t i = 0; i < opCount; i++) {
        ++value;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ++value;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 按照条件更新
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    value = 100;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "F0 < 10000");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //  全表删除
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    RtLongLogChecker checker(0);
    ret = checker.RtInitLogResult();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogExist();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(opCount, 1, DbGetOpCodeMsg(MSG_OP_RPC_INSERT_VERTEX), labelName, indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_UPDATE_VERTEX), labelName, indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_DELETE_VERTEX), labelName, indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogFinish();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StLongOperationLogWithThreshold0, longop_vertex_extreme_longlabel_011)
{
    const char *batchLabelName = "RuntimeBatchLabel";
    const char *batchLabelIndexName = "RuntimeBatchExtremelyExtremelyExtremelyExtremelyExtremelyLongLabel_K0";
    const char *batchLabelConfigJson = R"({"max_record_count":10000})";
    const char *batchLabelSchemaJson =
        R"([{
            "type":"record",
            "name":"RuntimeBatchLabel",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":true}],
            "keys":
                [
                    {
                        "node":"RuntimeBatchLabel",
                        "name":"RuntimeBatchExtremelyExtremelyExtremelyExtremelyExtremelyLongLabel_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    RtClearRunLog();
    Status ret = SetConfigure("-cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt, g_timeOut);
    ret = GmcCreateVertexLabel(stmt, batchLabelSchemaJson, batchLabelConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, batchLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t opCount = 1000;
    for (int32_t i = 0; i < opCount; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    ret = GmcPrepareStmtByLabelName(stmt, batchLabelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value = 0;
    for (int32_t i = 0; i < opCount; i++) {
        value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, batchLabelIndexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, totalNum);
    EXPECT_EQ(1u, successNum);

    // delete all vertex
    ret = GmcPrepareStmtByLabelName(stmt, batchLabelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < opCount; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, batchLabelIndexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, totalNum);
    EXPECT_EQ(1u, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, batchLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    RtLongLogChecker checker(0);
    ret = checker.RtInitLogResult();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogExist();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_INSERT_VERTEX), batchLabelName, batchLabelIndexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_UPDATE_VERTEX), batchLabelName, batchLabelIndexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, 1, DbGetOpCodeMsg(MSG_OP_RPC_DELETE_VERTEX), batchLabelName, batchLabelIndexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogFinish();
    EXPECT_EQ(GMERR_OK, ret);
}

static void RtBatchOpBase(int32_t threshold, uint32_t executeNum)
{
    RtClearRunLog();
    Status ret = SetConfigure("-cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    CreateSyncConnectionAndStmt(&conn, &stmt, g_timeOut);
    ret = GmcCreateVertexLabel(stmt, g_labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改batch buff size属性
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t primaryKey = i;
        ret = RtSetInsertVertexProperty(stmt, GMC_OPERATION_INSERT, primaryKey, 8, 8, 8, 8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = RtSetUpdateVertexProperyty(stmt, 6, 6, 6, 6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t primaryKey = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryKey, sizeof(primaryKey));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = RtSetInsertVertexProperty(stmt, GMC_OPERATION_MERGE, primaryKey, 9, 9, 9, 9);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t primaryKey = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryKey, sizeof(primaryKey));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = RtSetInsertVertexProperty(stmt, GMC_OPERATION_REPLACE, primaryKey, 3, 3, 3, 3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE_VERSION);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t primaryKey = i + 10000;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryKey, sizeof(primaryKey));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < executeNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    RtLongLogChecker checker(threshold);
    ret = checker.RtInitLogResult();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogExist();
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, executeNum, DbGetOpCodeMsg(MSG_OP_RPC_INSERT_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, executeNum, DbGetOpCodeMsg(MSG_OP_RPC_UPDATE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, executeNum, DbGetOpCodeMsg(MSG_OP_RPC_MERGE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, executeNum, DbGetOpCodeMsg(MSG_OP_RPC_REPLACE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(
        1, executeNum, DbGetOpCodeMsg(MSG_OP_RPC_UPDATE_CHECK_VERSION), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLongLog(1, executeNum, DbGetOpCodeMsg(MSG_OP_RPC_DELETE_VERTEX), g_labelName, g_indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checker.RtCheckLogFinish();
    EXPECT_EQ(GMERR_OK, ret);
}

#ifndef FEATURE_PERSISTENCE
TEST_F(StLongOperationLogWithThreshold0, DISABLED_longop_vertex_batch_operation_012)
{
    RtBatchOpBase(0, 100);
}

// 以上阈值均为0ms

TEST_F(StLongOperationLogWithThreshold1, DISABLED_longop_vertex_operate_threshold_013)
{
    RtBatchOpBase(1, 10);
}

TEST_F(StLongOperationLogWithThreshold5, longop_vertex_operate_threshold_014)
{
    RtBatchOpBase(5, 30);
}

TEST_F(StLongOperationLogWithThreshold20, longop_vertex_operate_threshold_015)
{
    RtBatchOpBase(20, 60);
}

TEST_F(StLongOperationLogWithThresholdDefault, DISABLED_longop_vertex_operate_threshold_016)
{
    RtBatchOpBase(100, 100);
}

TEST_F(StLongOperationLogWithThreshold500, DISABLED_longop_vertex_operate_threshold_017)
{
    RtBatchOpBase(500, 150);
}
#endif
#endif
