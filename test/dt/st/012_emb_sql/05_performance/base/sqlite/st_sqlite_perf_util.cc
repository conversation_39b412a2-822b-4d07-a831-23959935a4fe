/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: util perf test for sqlite
 * Author: SQL
 * Create: 2024-03-12
 */
#include "st_sqlite_perf_util.h"

void StEmbSqliteStartTrans(sqlite3 *conn)
{
    if (!g_isAutoTrans) {
        char *errMsg = NULL;
        const char *sql = "BEGIN;";
        int ret = StEmbSqliteBlockingExec(conn, sql, NULL, 0, &errMsg);
        EXPECT_EQ(ret, SQLITE_OK);
    }
}

void StEmbSqliteEndTrans(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    if (!g_isAutoTrans) {
        StEmbSqlSetTransTimestamp(worker);
        char *errMsg = NULL;
        const char *sql = "COMMIT;";
        int ret = StEmbSqliteBlockingExec(conn, sql, NULL, 0, &errMsg);
        EXPECT_EQ(ret, SQLITE_OK);
        StEmbSqlCalcTransExecuteTimeCost(worker);
    }
}

static void StEmbSqliteInsertOneBatch(
    StEmbSqlWorkerT *worker, sqlite3 *conn, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    Status ret;
    char *errMsg = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        char *sql = StEmbSqlBuildInsertTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, index_val);
        ret = StEmbSqliteBlockingExec(conn, sql, NULL, 0, &errMsg);
        EXPECT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

static void StEmbSqliteInsertData(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteInsertOneBatch(worker, conn, index, vals);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d insert data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetInsertCostTime(worker);
}

static void StEmbSqliteInsertOneBatchByStmt(
    StEmbSqlWorkerT *worker, sqlite3 *conn, sqlite3_stmt *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    const uint32_t count = 10;
    char thread[count] = {0};
    (void)snprintf_s(thread, count, count - 1, "w%u", worker->workerId);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        Status ret = sqlite3_bind_int64(stmt, 1, index_val);
        ASSERT_EQ(ret, SQLITE_OK);
        char buf[count] = {0};
        (void)snprintf_s(buf, count, count - 1, "t%u", index_val);
        ret = sqlite3_bind_text(stmt, 2, buf, (uint32_t)strlen(buf), NULL);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = sqlite3_bind_int64(stmt, 3, index_val);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = sqlite3_bind_text(stmt, 4, thread, (uint32_t)strlen(thread), NULL);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = sqlite3_bind_double(stmt, 5, 0.2);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = sqlite3_bind_text(stmt, 6, "?", 1, NULL);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = StEmbSqliteBlockingStep(stmt);
        ASSERT_EQ(ret, SQLITE_DONE);
        ret = sqlite3_reset(stmt);
        ASSERT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

void StEmbSqliteInsertDataByStmt(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    // 统计时间之前预置数据, 离开当前接口时数据占用内存自动销毁
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);

    sqlite3_stmt *stmt = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    char *sql = StEmbSqlBuildInsertTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);

    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = StEmbSqliteBlockingPrepare(conn, sql, len, &stmt, NULL);
    ASSERT_EQ(ret, SQLITE_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteInsertOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(sqlite3_finalize(stmt), SQLITE_OK);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d insert data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetInsertCostTime(worker);
}

static void StEmbSqliteUpdateOneBatch(
    StEmbSqlWorkerT *worker, sqlite3 *conn, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    Status ret;
    char *errMsg = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        int32_t total = g_maxDmlNum + worker->workerId * g_maxDmlNum - index_val;
        char *sql = StEmbSqlBuildUpdateTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "weight1 =", total, index_val);
        ret = StEmbSqliteBlockingExec(conn, sql, NULL, 0, &errMsg);
        EXPECT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

static void StEmbSqliteUpdateData(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteUpdateOneBatch(worker, conn, index, vals);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d update all data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetUpdateCostTime(worker);
}

static void StEmbSqliteUpdateOneBatchByStmt(
    StEmbSqlWorkerT *worker, sqlite3 *conn, sqlite3_stmt *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        int32_t total = g_maxDmlNum + worker->workerId * g_maxDmlNum - index_val;
        Status ret = sqlite3_bind_int64(stmt, 2, index_val);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = sqlite3_bind_int64(stmt, 1, total);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = StEmbSqliteBlockingStep(stmt);
        ASSERT_EQ(ret, SQLITE_DONE);
        ret = sqlite3_reset(stmt);
        ASSERT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

void StEmbSqliteUpdateDataByStmt(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    sqlite3_stmt *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    char *sql = StEmbSqlBuildUpdateTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = StEmbSqliteBlockingPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, SQLITE_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteUpdateOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(sqlite3_finalize(stmt), SQLITE_OK);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d update data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetUpdateCostTime(worker);
}

static void StEmbSqliteRangeUpdateBatch(StEmbSqlWorkerT *worker, sqlite3 *conn, uint32_t index)
{
    StEmbSqliteStartTrans(conn);
    char *errMsg = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    char *sql = StEmbSqlBuildRangeUpdateTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "weight2 =", begin, end);
    Status ret = StEmbSqliteBlockingExec(conn, sql, NULL, NULL, &errMsg);
    EXPECT_EQ(ret, SQLITE_OK);
    StEmbSqliteEndTrans(conn, worker);
}

static void StEmbSqliteRangeUpdateData(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlSetBatchTimestamp(worker);
    StEmbSqlClearTransExecTime(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteRangeUpdateBatch(worker, conn, index);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range update data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeUpdateCostTime(worker);
}

// 这里假定更新的范围，和事务提交条数保持一致，隐示事务默认10条数据为一个范围
static void StEmbSqliteRangeUpdateOneBatchByStmt(
    StEmbSqlWorkerT *worker, sqlite3 *conn, sqlite3_stmt *stmt, uint32_t index)
{
    StEmbSqliteStartTrans(conn);
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    Status ret = sqlite3_bind_double(stmt, 1, begin);
    ASSERT_EQ(ret, SQLITE_OK);
    ret = sqlite3_bind_int64(stmt, 2, begin);
    ASSERT_EQ(ret, SQLITE_OK);
    ret = sqlite3_bind_int64(stmt, 3, end);
    ASSERT_EQ(ret, SQLITE_OK);
    ret = StEmbSqliteBlockingStep(stmt);
    ASSERT_EQ(ret, SQLITE_DONE);
    ret = sqlite3_reset(stmt);
    ASSERT_EQ(ret, SQLITE_OK);
    StEmbSqliteEndTrans(conn, worker);
}

void StEmbSqliteRangeUpdateDataByStmt(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    char *sql = StEmbSqlBuildRangeUpdateTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    sqlite3_stmt *stmt = NULL;
    const char *unused = NULL;
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = StEmbSqliteBlockingPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, SQLITE_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteRangeUpdateOneBatchByStmt(worker, conn, stmt, index);
    }
    ASSERT_EQ(sqlite3_finalize(stmt), SQLITE_OK);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range update data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeUpdateCostTime(worker);
}

static void StEmbSqliteDeleteBatch(StEmbSqlWorkerT *worker, sqlite3 *conn, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    Status ret;
    char *errMsg = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        char *sql = StEmbSqlBuildDeleteTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "id =", index_val);
        ret = StEmbSqliteBlockingExec(conn, sql, NULL, 0, &errMsg);
        EXPECT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

static void StEmbSqliteDeleteData(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteDeleteBatch(worker, conn, index, vals);
    }
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d delete all data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetDeleteCostTime(worker);
}

static void StEmbSqliteDeleteOneBatchByStmt(
    StEmbSqlWorkerT *worker, sqlite3 *conn, sqlite3_stmt *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        Status ret = sqlite3_bind_int64(stmt, 1, index_val);
        ASSERT_EQ(ret, SQLITE_OK);
        ret = StEmbSqliteBlockingStep(stmt);
        ASSERT_EQ(ret, SQLITE_DONE);
        ret = sqlite3_reset(stmt);
        ASSERT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

void StEmbSqliteDeleteDataByStmt(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    sqlite3_stmt *stmt = NULL;
    const char *unused = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    char *sql = StEmbSqlBuildDeleteTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = StEmbSqliteBlockingPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, SQLITE_OK);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteDeleteOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(sqlite3_finalize(stmt), SQLITE_OK);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d delete data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetDeleteCostTime(worker);
}

static int StEmbSqliteReadDataCallback(void *data, int argc, char **argv, char **azColName)
{
    uint32_t threadId = 0;
    if (data != NULL) {
        threadId = ((StEmbSqlUserDataT *)data)->threadId;
    }
    StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[threadId];
    StEmbSqlDqlCallback(worker, argc, argv, azColName);
    return 0;
}

static void StEmbSqlitePointQueryBatch(
    StEmbSqlWorkerT *worker, sqlite3 *conn, uint32_t index, StEmbSqlUserDataT *data, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    char *errMsg = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index * ST_SQL_MAX_BATCH_NUM + op);
        char *sql = StEmbSqlBuildSelectTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, "id =", index_val);
        Status ret = StEmbSqliteBlockingExec(conn, sql, StEmbSqliteReadDataCallback, data, &errMsg);
        EXPECT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

static void StEmbSqlitePointQueryData(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);

    // 设置回调参数
    StEmbSqlUserDataT *data = (StEmbSqlUserDataT *)malloc(sizeof(StEmbSqlUserDataT));
    EXPECT_EQ((data != NULL), true);
    data->threadId = worker->workerId;
    // 读取之前先将统计计数清0
    StEmbSqlResetCount(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlitePointQueryBatch(worker, conn, index, data, vals);
    }
    free(data);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Point read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetSelectCostTime(worker);
}

static void StEmbSqlitePrintDataByStmt(sqlite3_stmt *stmt)
{
    if (!g_isPrintData) {
        return;
    }
    int32_t id = (int32_t)sqlite3_column_int64(stmt, 0);
    const char *name = (const char *)sqlite3_column_text(stmt, 1);
    int32_t weight1 = (int32_t)sqlite3_column_int64(stmt, 2);
    const char *note1 = (const char *)sqlite3_column_text(stmt, 3);
    double real = sqlite3_column_double(stmt, 4);
    const char *note2 = (const char *)sqlite3_column_text(stmt, 5);
    printf("%-15d %-15s %-15d %-15s %-15f %-15s\n", id, name, weight1, note1, real, note2);
}

static void StEmbSqlitePointQueryOneBatchByStmt(
    StEmbSqlWorkerT *worker, sqlite3 *conn, sqlite3_stmt *stmt, uint32_t index, std::vector<uint32_t> &vals)
{
    StEmbSqliteStartTrans(conn);
    for (uint32_t op = 0; op < ST_SQL_MAX_BATCH_NUM; op++) {
        uint32_t index_val = StEmbSqlObtainData(vals, index);
        Status ret = sqlite3_bind_int64(stmt, 1, index_val);
        ASSERT_EQ(ret, SQLITE_OK);
        while (ret = StEmbSqliteBlockingStep(stmt), ret == SQLITE_ROW) {
            StEmbSqlIncreaseCount(worker);
            uint32_t colCount = sqlite3_column_count(stmt);
            ASSERT_EQ(colCount, 6u);
            StEmbSqlitePrintDataByStmt(stmt);
        }
        ASSERT_EQ(ret, SQLITE_DONE);
        ret = sqlite3_reset(stmt);
        ASSERT_EQ(ret, SQLITE_OK);
    }
    StEmbSqliteEndTrans(conn, worker);
}

void StEmbSqlitePointQueryDataByStmt(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    std::vector<uint32_t> vals;
    StEmbSqlGenerateData(vals, worker->workerId);
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    char *sql = StEmbSqlBuildSelectTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);
    sqlite3_stmt *stmt = NULL;
    const char *unused = NULL;
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = StEmbSqliteBlockingPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, SQLITE_OK);
    StEmbSqlResetCount(worker);
    StEmbSqlPrintTable();
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqlitePointQueryOneBatchByStmt(worker, conn, stmt, index, vals);
    }
    ASSERT_EQ(sqlite3_finalize(stmt), SQLITE_OK);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Point read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetSelectCostTime(worker);
}

static void StEmbSqliteRangeReadBatch(StEmbSqlWorkerT *worker, sqlite3 *conn, uint32_t index, StEmbSqlUserDataT *data)
{
    StEmbSqliteStartTrans(conn);
    char *errMsg = NULL;
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    char *sql = StEmbSqlBuildRangeSelectTableSql(ST_EMB_SQL_PERF_TABLE1, workerId, begin, end);
    Status ret = StEmbSqliteBlockingExec(conn, sql, StEmbSqliteReadDataCallback, (void *)data, &errMsg);
    EXPECT_EQ(ret, GMERR_OK);
    StEmbSqliteEndTrans(conn, worker);
}

static void StEmbSqliteRangeQueryData(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    // 设置回调参数
    StEmbSqlUserDataT *data = (StEmbSqlUserDataT *)malloc(sizeof(StEmbSqlUserDataT));
    EXPECT_EQ((data != NULL), true);
    data->threadId = worker->workerId;
    // 读取之前先将统计计数清0
    StEmbSqlResetCount(worker);
    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteRangeReadBatch(worker, conn, index, data);
    }
    free(data);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeSelectCostTime(worker);
}

// 这里假定查询的范围，和事务提交条数保持一致，隐示事务默认10条数据为一个范围
static void StEmbSqliteRangeQueryOneBatchByStmt(
    StEmbSqlWorkerT *worker, sqlite3 *conn, sqlite3_stmt *stmt, uint32_t index)
{
    StEmbSqliteStartTrans(conn);
    uint32_t begin = index * ST_SQL_MAX_BATCH_NUM + worker->workerId * g_maxDmlNum;
    uint32_t end = begin + ST_SQL_MAX_BATCH_NUM;
    Status ret = sqlite3_bind_int64(stmt, 1, begin);
    ASSERT_EQ(ret, GMERR_OK);
    ret = sqlite3_bind_int64(stmt, 2, end);
    ASSERT_EQ(ret, GMERR_OK);
    while (ret = StEmbSqliteBlockingStep(stmt), ret == SQLITE_ROW) {
        StEmbSqlIncreaseCount(worker);
        uint32_t colCount = sqlite3_column_count(stmt);
        ASSERT_EQ(colCount, 6u);
        StEmbSqlitePrintDataByStmt(stmt);
    }
    ASSERT_EQ(ret, SQLITE_DONE);
    ret = sqlite3_reset(stmt);
    ASSERT_EQ(ret, SQLITE_OK);
    StEmbSqliteEndTrans(conn, worker);
}

void StEmbSqliteRangeQueryDataByStmt(sqlite3 *conn, StEmbSqlWorkerT *worker)
{
    StEmbSqlGetCurrentMemorySize(worker);
    StEmbSqlClearTransExecTime(worker);
    StEmbSqlSetBatchTimestamp(worker);
    uint32_t workerId = g_isSameTable ? 0 : worker->workerId;
    char *sql = StEmbSqlBuildRangeSelectTableSqlByStmt(ST_EMB_SQL_PERF_TABLE1, workerId);

    StEmbSqlResetCount(worker);
    StEmbSqlPrintTable();

    sqlite3_stmt *stmt = NULL;
    const char *unused = NULL;
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    Status ret = StEmbSqliteBlockingPrepare(conn, sql, len, &stmt, &unused);
    ASSERT_EQ(ret, SQLITE_OK);

    uint32_t batchNum = g_maxDmlNum / ST_SQL_MAX_BATCH_NUM;
    for (uint32_t index = 0; index < batchNum; ++index) {
        StEmbSqliteRangeQueryOneBatchByStmt(worker, conn, stmt, index);
    }

    ASSERT_EQ(sqlite3_finalize(stmt), SQLITE_OK);
    EXPECT_GE(worker->count, g_maxDmlNum);
    EXPECT_LE(worker->count, worker->workerNum * g_maxDmlNum);
    char dmlInfo[ST_MAX_SQL_TEXT_LEN] = {0};
    (void)sprintf_s(dmlInfo, ST_MAX_SQL_TEXT_LEN, "Thread_%d Range read data finished", worker->workerId);
    StEmbSqlCalcBatchExecuteTimeCost(worker);
    StEmbSqlMinusTransExecTime(worker);
    StEmbSqlOutputPerfInfo(worker, dmlInfo, g_maxDmlNum);
    StEmbSqlSetRangeSelectCostTime(worker);
}

static void *StEmbSqlitePerfThreadEntry(void *args)
{
    StEmbSqlUserDataT *data = (StEmbSqlUserDataT *)args;
    uint32_t workerId = data->threadId;
    uint32_t workerNum = data->threadNum;
    uint32_t mask = data->mask;

    StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[workerId];
    worker->workerNum = workerNum;

    sqlite3 *conn = NULL;
    int ret = StEmbSqliteOpen(&conn);
    EXPECT_EQ(ret, SQLITE_OK);

    StEmbSqliteSetJournalMode(conn);
    StEmbSqliteSetBusyTimeout(conn);

    // 根据操作码进行不同的数据操作，实现不组合
    if ((mask & ST_SQL_TEST_INSERT) != 0) {
        StEmbSqliteInsertData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_UPDATE) != 0) {
        StEmbSqliteUpdateData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_UPDATE) != 0) {
        StEmbSqliteRangeUpdateData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_READ) != 0) {
        StEmbSqlitePointQueryData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_READ) != 0) {
        StEmbSqliteRangeQueryData(conn, worker);
    }
    if ((mask & ST_SQL_TEST_DELETE) != 0) {
        StEmbSqliteDeleteData(conn, worker);
    }
    StEmbSqliteClose(conn);
    return NULL;
}

void *StEmbSqlitePerfByStmtEntry(void *args)
{
    StEmbSqlUserDataT *data = (StEmbSqlUserDataT *)args;
    uint32_t workerId = data->threadId;
    uint32_t workerNum = data->threadNum;
    uint32_t mask = data->mask;

    StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[workerId];
    worker->workerNum = workerNum;

    sqlite3 *conn = NULL;
    int ret = StEmbSqliteOpen(&conn);
    EXPECT_EQ(ret, SQLITE_OK);

    StEmbSqliteSetJournalMode(conn);
    StEmbSqliteSetBusyTimeout(conn);

    // 根据操作码进行不同的数据操作，实现不组合
    if ((mask & ST_SQL_TEST_INSERT) != 0) {
        StEmbSqliteInsertDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_UPDATE) != 0) {
        StEmbSqliteUpdateDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_UPDATE) != 0) {
        StEmbSqliteRangeUpdateDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_READ) != 0) {
        StEmbSqlitePointQueryDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_RANGE_READ) != 0) {
        StEmbSqliteRangeQueryDataByStmt(conn, worker);
    }
    if ((mask & ST_SQL_TEST_DELETE) != 0) {
        StEmbSqliteDeleteDataByStmt(conn, worker);
    }
    StEmbSqliteClose(conn);
    return NULL;
}

void StEmbSqliteMultiPerfByStmtWithType(StEmbSqlUserDataT *datas, uint32_t type)
{
    pthread_t pthread[ST_SQL_MAX_THREAD_NUM];
    for (uint32_t i = 0; i < ST_SQL_MAX_THREAD_NUM; i++) {
        datas[i].threadId = i;
        datas[i].threadNum = ST_SQL_MAX_THREAD_NUM;
        datas[i].mask = type;
        pthread_create(&pthread[i], NULL, StEmbSqlitePerfByStmtEntry, &datas[i]);
    }
    for (uint32_t i = 0; i < ST_SQL_MAX_THREAD_NUM; i++) {
        pthread_join(pthread[i], NULL);
    }
}
