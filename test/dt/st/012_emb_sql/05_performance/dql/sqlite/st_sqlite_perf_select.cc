/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: select perf test for sqlite
 * Author: SQL
 * Create: 2024-03-12
 */
#include "st_emb_sql_frame.h"

TEST_F(StEmbSqlitePerfFrame, TestRead_1Thread_10W_Scan)
{
    Status ret;
    char *cfgFilePath = StEmbSqlGetConfigPath();
    GmeConnT *conn = NULL;
    ret = GmeOpen(cfgFilePath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
