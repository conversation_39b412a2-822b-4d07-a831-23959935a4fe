/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_frame.cc
 * Description: sql test framework source file
 * Author: SQL
 * Create: 2024-3-12
 */

#include "st_emb_sql_frame.h"

bool g_isPerfTest = false;   // 是否做性能测试开销，打开后数据会增加1000倍
bool g_isAutoTrans = false;  // 是否使用自动提交事务
bool g_isPrintData = false;  // Sql查询的时候，是否在屏幕输出信息
bool g_isSameTable = true;   // 并发测试时是否是对同一张表进行操作
uint32_t g_maxDmlNum = 0;
StEmbSqlPerfInsT *g_stSqlPerfIns = NULL;

void StEmbSqlCreatePerfIns(bool isEmbedded)
{
    g_stSqlPerfIns = (StEmbSqlPerfInsT *)malloc(sizeof(StEmbSqlPerfInsT));
    EXPECT_EQ((g_stSqlPerfIns != NULL), true);
    DbSpinInit(&g_stSqlPerfIns->lock);
    g_stSqlPerfIns->workerNum = ST_SQL_MAX_THREAD_NUM;
    for (uint32_t i = 0; i < g_stSqlPerfIns->workerNum; i++) {
        StEmbSqlWorkerT *worker = &g_stSqlPerfIns->worker[i];
        (void)memset_s(worker, sizeof(StEmbSqlWorkerT), 0, sizeof(StEmbSqlWorkerT));
        worker->workerId = i;
        worker->workerNum = ST_SQL_MAX_THREAD_NUM;  // 默认是1个客户端操作数据
        worker->printTime = true;
        worker->printMemory = true;
        worker->isEmbedded = isEmbedded;
    }
}

void StEmbSqlDestroyPerfIns()
{
    free(g_stSqlPerfIns);
    g_stSqlPerfIns = NULL;
}

static int StEmbSqlGetPid(const char *processName)
{
    char result[32];
    std::string cmd = std::string("pidof ") + processName;
    FILE *fp = popen(cmd.c_str(), "r");
    if (fp == NULL) {
        return -1;
    }

    char *p = fgets(result, sizeof(result), fp);
    fclose(fp);
    if ((p == NULL) || (strlen(p) == 0)) {
        // server has stopped
        return -2;
    }

    return atoi(p);
}

void StEmbSqlPrintCPUTimeInfo(StEmbSqlWorkerT *worker, const char *str, uint32_t totalNum)
{
    if (!worker->printTime) {
        return;
    }
    printf("%s%-50s%s Count: %s%-6u%s batch execute time : %-7.3lf ms, trans commit time : %-10.3lf ms, "
           "total time : %s%-10.3lf%s ms\n",
        KNRM, str, KNRM, KCYN, totalNum, KNRM, worker->perf.time.batchExecuteTime, worker->perf.time.transExecuteTime,
        KRED, worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime, KNRM);
}

static int StEmbSqlGetMemSize(const int pid, const char *memType)
{
    FILE *fp;
    constexpr int cmdResultBufLen = 2048;
    char file[ST_MAX_FILE_NAME_SIZE] = {0};
    char lineBuff[cmdResultBufLen] = {0};
    (void)snprintf_s(file, ST_MAX_FILE_NAME_SIZE, ST_MAX_FILE_NAME_SIZE - 1, "/proc/%d/status", pid);
    fp = fopen(file, "r");
    if (fp == NULL) {
        fprintf(stderr, "Process %d not exist.\n", pid);
        return 0;
    }
    char name[ST_MAX_FILE_NAME_SIZE] = {0};
    int vmrss;
    const char *vmrssStr = memType;
    for (;;) {
        char *ret = fgets(lineBuff, sizeof(lineBuff), fp);
        if (ret == NULL) {
            fclose(fp);
            fprintf(stderr, "Get meminfo by pid %d failed.\n", pid);
            return 0;
        }
        if (strncasecmp(lineBuff, vmrssStr, strlen(vmrssStr)) == 0) {
            break;
        }
    }
    fclose(fp);
    sscanf_s(lineBuff, "%s %d", name, sizeof(name), &vmrss);
    return vmrss;
}

// 输出的是增量的内存信息
void StEmbSqlPrintMemoryInfo(StEmbSqlWorkerT *worker, const char *str, uint32_t totalNum)
{
    if (!worker->printMemory) {
        return;
    }
    double lastVmPeak = worker->perf.memory.vmPeak;
    double lastVmSize = worker->perf.memory.vmSize;
    double lastVmRss = worker->perf.memory.vmRss;
    // 获取当前的内存使用情况
    StEmbSqlGetCurrentMemorySize(worker);
    double incVmPeak = worker->perf.memory.vmPeak - lastVmPeak;
    double incVmSize = worker->perf.memory.vmSize - lastVmSize;
    double incVmRss = worker->perf.memory.vmRss - lastVmRss;
    if (incVmRss >= 1.0) {
        printf(
            "%s%-50s%s Count: %s%-6u%s IncVmPeak : %-7.3lf MB, IncVmSize : %-10.3lf MB, IncVmRss : %s%-10.3lf%s MB\n",
            KNRM, str, KNRM, KCYN, totalNum, KNRM, incVmPeak, incVmSize, KNRM, incVmRss, KNRM);
    } else {
        printf(
            "%s%-50s%s Count: %s%-6u%s IncVmPeak : %-7.3lf KB, IncVmSize : %-10.3lf KB, IncVmRss : %s%-10.3lf%s KB\n",
            KNRM, str, KNRM, KCYN, totalNum, KNRM, incVmRss * DB_KIBI, incVmRss * DB_KIBI, KNRM, incVmRss * DB_KIBI,
            KNRM);
    }
}

void StEmbSqlGetCurrentMemorySize(StEmbSqlWorkerT *worker)
{
    if (!worker->printMemory) {
        return;
    }
    // 获取自身进程pid，或者服务端进程的pid值
    int pid = worker->isEmbedded ? getpid() : StEmbSqlGetPid("gmserver");
    StEmbSqlMemoryT *memory = &worker->perf.memory;
    memory->vmPeak = StEmbSqlGetMemSize(pid, "VmPeak") / (double)DB_KIBI;
    memory->vmSize = StEmbSqlGetMemSize(pid, "VmSize") / (double)DB_KIBI;
    memory->vmRss = StEmbSqlGetMemSize(pid, "VmRSS") / (double)DB_KIBI;
}

void StEmbSqlPrintTotalMemoryDirectly(StEmbSqlWorkerT *worker, const char *str, uint32_t totalNum)
{
    worker->printMemory = true;
    StEmbSqlGetCurrentMemorySize(worker);
    double vmPeak = worker->perf.memory.vmPeak;
    double vmSize = worker->perf.memory.vmSize;
    double vmRss = worker->perf.memory.vmRss;
    if (vmRss >= 1.0) {
        printf("%s%-50s%s Count: %s%-6u%s VmPeak : %-7.3lf MB, VmSize : %-10.3lf MB, VmRss : %s%-10.3lf%s MB\n", KNRM,
            str, KNRM, KCYN, totalNum, KNRM, vmPeak, vmSize, KNRM, vmRss, KNRM);
    } else {
        printf("%s%-50s%s Count: %s%-6u%s VmPeak : %-7.3lf KB, VmSize : %-10.3lf KB, VmRss : %s%-10.3lf%s KB\n", KNRM,
            str, KNRM, KCYN, totalNum, KNRM, vmPeak * DB_KIBI, vmPeak * DB_KIBI, KNRM, vmPeak * DB_KIBI, KNRM);
    }
}

void StEmbSqlPrintTotalTimeCostDirectly(StEmbSqlWorkerT *worker, const char *str, uint32_t totalNum)
{
    StEmbSqlTimeT *time = &worker->perf.time;
    printf("%s%-50s%s Count: %s%-6u%s batch execute time : %-7.3lf ms, trans commit time : %-10.3lf ms, "
           "total time : %s%-10.3lf%s ms\n",
        KNRM, str, KNRM, KCYN, totalNum, KNRM, time->batchExecuteTime, time->transExecuteTime, KRED,
        time->batchExecuteTime + time->transExecuteTime, KNRM);
}

void StEmbSqlOutputPerfInfo(StEmbSqlWorkerT *worker, const char *info, uint32_t totalNum)
{
    StEmbSqlPrintCPUTimeInfo(worker, info, totalNum);
    StEmbSqlPrintMemoryInfo(worker, info, totalNum);
}

void StEmbSqlSetInsertCostTime(StEmbSqlWorkerT *worker)
{
    double insertCost = worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime;
    DbSpinLock(&g_stSqlPerfIns->lock);
    if (insertCost > g_stSqlPerfIns->costInfo.insertCost.time) {
        g_stSqlPerfIns->costInfo.insertCost.time = insertCost;
        g_stSqlPerfIns->costInfo.insertCost.id = worker->workerId;
    }
    DbSpinUnlock(&g_stSqlPerfIns->lock);
}

void StEmbSqlSetUpdateCostTime(StEmbSqlWorkerT *worker)
{
    double updateCost = worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime;
    DbSpinLock(&g_stSqlPerfIns->lock);
    if (updateCost > g_stSqlPerfIns->costInfo.updateCost.time) {
        g_stSqlPerfIns->costInfo.updateCost.time = updateCost;
        g_stSqlPerfIns->costInfo.updateCost.id = worker->workerId;
    }
    DbSpinUnlock(&g_stSqlPerfIns->lock);
}

void StEmbSqlSetRangeUpdateCostTime(StEmbSqlWorkerT *worker)
{
    double rangeUpdateCost = worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime;
    DbSpinLock(&g_stSqlPerfIns->lock);
    if (rangeUpdateCost > g_stSqlPerfIns->costInfo.rangeUpdateCost.time) {
        g_stSqlPerfIns->costInfo.rangeUpdateCost.time = rangeUpdateCost;
        g_stSqlPerfIns->costInfo.rangeUpdateCost.id = worker->workerId;
    }
    DbSpinUnlock(&g_stSqlPerfIns->lock);
}

void StEmbSqlSetSelectCostTime(StEmbSqlWorkerT *worker)
{
    double selectCost = worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime;
    DbSpinLock(&g_stSqlPerfIns->lock);
    if (selectCost > g_stSqlPerfIns->costInfo.selectCost.time) {
        g_stSqlPerfIns->costInfo.selectCost.time = selectCost;
        g_stSqlPerfIns->costInfo.selectCost.id = worker->workerId;
    }
    DbSpinUnlock(&g_stSqlPerfIns->lock);
}

void StEmbSqlSetRangeSelectCostTime(StEmbSqlWorkerT *worker)
{
    double rangeSelectCost = worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime;
    DbSpinLock(&g_stSqlPerfIns->lock);
    if (rangeSelectCost > g_stSqlPerfIns->costInfo.rangeSelectCost.time) {
        g_stSqlPerfIns->costInfo.rangeSelectCost.time = rangeSelectCost;
        g_stSqlPerfIns->costInfo.rangeSelectCost.id = worker->workerId;
    }
    DbSpinUnlock(&g_stSqlPerfIns->lock);
}

void StEmbSqlSetDeleteCostTime(StEmbSqlWorkerT *worker)
{
    double deleteCost = worker->perf.time.batchExecuteTime + worker->perf.time.transExecuteTime;
    DbSpinLock(&g_stSqlPerfIns->lock);
    if (deleteCost > g_stSqlPerfIns->costInfo.deleteCost.time) {
        g_stSqlPerfIns->costInfo.deleteCost.time = deleteCost;
        g_stSqlPerfIns->costInfo.deleteCost.id = worker->workerId;
    }
    DbSpinUnlock(&g_stSqlPerfIns->lock);
}

void StEmbSqlOutputCostTime()
{
    StEmbSqlCostInfoT *cost = &g_stSqlPerfIns->costInfo;
    uint32_t totalNum = g_maxDmlNum * ST_SQL_MAX_THREAD_NUM;
    printf("Insert Cost Time: %-7.3lf ms, Ops: %-7.3lf Kops/s, Worker Id: %u\n", cost->insertCost.time,
        totalNum * 1.0 / cost->insertCost.time, cost->insertCost.id);
    printf("Update Cost Time: %-7.3lf ms, Ops: %-7.3lf Kops/s, Worker Id: %u\n", cost->updateCost.time,
        totalNum * 1.0 / cost->updateCost.time, cost->updateCost.id);
    printf("Range Update Cost Time: %-7.3lf ms, Ops: %-7.3lf Kops/s, Worker Id: %u\n", cost->rangeUpdateCost.time,
        totalNum * 1.0 / cost->rangeUpdateCost.time, cost->rangeUpdateCost.id);
    printf("Point Read Cost Time: %-7.3lf ms, Ops: %-7.3lf Kops/s, Worker Id: %u\n", cost->selectCost.time,
        totalNum * 1.0 / cost->selectCost.time, cost->selectCost.id);
    printf("Range Read Cost Time: %-7.3lf ms, Ops: %-7.3lf Kops/s, Worker Id: %u\n", cost->rangeSelectCost.time,
        totalNum * 1.0 / cost->rangeSelectCost.time, cost->rangeSelectCost.id);
    printf("Delete Cost Time: %-7.3lf ms, Ops: %-7.3lf Kops/s, Worker Id: %u\n", cost->deleteCost.time,
        totalNum * 1.0 / cost->deleteCost.time, cost->deleteCost.id);
}
