/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_update_expr.cc
 * Description:
 * Create: 2024-03-12
 */
#include "gme_sql_api.h"
#include "st_emb_sql_com.h"
#include "st_emb_sql_util.h"

using namespace std;

class StEmbSqlUpdateExpr : public StEmbSqlTestSuitExtend {};

typedef struct {
    const char *sql;                            // sql语句
    Status expectRet = GMERR_OK;                // 预期返回结果
    StEmbSqlQryResultSetExtendT exptQryResult;  // 预期查询结果
} EmbSqlWhereExprCaseT;

// In表达式常规条件查询
TEST_F(StEmbSqlUpdateExpr, TestUpdateInCondition)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tunc(a int , b text);",
        },
        {
            .sql = "insert into tunc values ( 1, 'aaa'),( 2, 'bbb'),( 3, 'ccc');",
        },
        {
            .sql = "update tunc set b='zzz' where a in (1, 2);",
        },
        {
            .sql = "select * from tunc;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "zzz"}, {"2", "zzz"}, {"3", "ccc"}}},
        },
        {
            .sql = "update tunc set b='www' where a not in (1, 2);",
        },
        {
            .sql = "select * from tunc;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "zzz"}, {"2", "zzz"}, {"3", "www"}}},
        },
        {
            .sql = "drop table tunc;",
        },
    };
    for (auto testCase : cases) {
        ret = GmeSqlExecute(conn, (char *)testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(testCase.exptQryResult);
            StEmbSqlClearActQryResultSet();
        }
    }
}

// Between表达式常规条件查询
TEST_F(StEmbSqlUpdateExpr, TestUpdateBetweenCondition)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tubc(id int, pid int, name text);",
        },
        {
            .sql = "insert into tubc(id, pid, name) values(1, 1, 'xx1'),(1, 2, 'xx2'),(1, 4, 'xx4');",
        },
        {
            .sql = "update tubc set name='zzz' where pid between 2 and 3;",
        },
        {
            .sql = "select * from tubc;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "pid", "name"}, {{"1", "1", "xx1"}, {"1", "2", "zzz"}, {"1", "4", "xx4"}}},
        },
        {
            .sql = "update tubc set name='ccc' where pid not between 2 and 3;",
        },
        {
            .sql = "select * from tubc;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "pid", "name"}, {{"1", "1", "ccc"}, {"1", "2", "zzz"}, {"1", "4", "ccc"}}},
        },
        {
            .sql = "drop table tubc;",
        },
    };
    for (auto testCase : cases) {
        ret = GmeSqlExecute(conn, (char *)testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(testCase.exptQryResult);
            StEmbSqlClearActQryResultSet();
        }
    }
}

// is表达式常规条件查询
TEST_F(StEmbSqlUpdateExpr, TestUpdateIsCondition)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tuis(id int, cid int, name text);",
        },
        {
            .sql = "insert into tuis(id, cid, name) values(1, 1, 'xx1'),(2, 2, 'xx2'),(3, 4, 'xx4'),(4, 8, null),(5, "
                   "null, 'xx16');",
        },
        {
            .sql = "update tuis set name='zzz' where cid is 2;",
        },
        {
            .sql = "select * from tuis where cid is 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"}, {{"2", "2", "zzz"}}},
        },
        {
            .sql = "update tuis set name='ccc' where cid IS NOT distinct from 2;",
        },
        {
            .sql = "select * from tuis where cid IS NOT distinct from 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"}, {{"2", "2", "ccc"}}},
        },
        {
            .sql = "update tuis set name='www' where cid is not 2;",
        },
        {
            .sql = "select * from tuis where cid is not 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"},
                {{"1", "1", "www"}, {"3", "4", "www"}, {"4", "8", "www"}, {"5", "null", "www"}}},
        },
        {
            .sql = "update tuis set name='yyy' where cid is distinct from 2;",
        },
        {
            .sql = "select * from tuis where cid is distinct from 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"},
                {{"1", "1", "yyy"}, {"3", "4", "yyy"}, {"4", "8", "yyy"}, {"5", "null", "yyy"}}},
        },
        {
            .sql = "drop table tuis;",
        },
    };
    for (auto testCase : cases) {
        ret = GmeSqlExecute(conn, (char *)testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(testCase.exptQryResult);
            StEmbSqlClearActQryResultSet();
        }
    }
}

// 更新值为函数表达式
TEST_F(StEmbSqlUpdateExpr, TestUpdateFunctionCondition)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tufunc(id int, pid int, name text);",
        },
        {
            .sql = "insert into tufunc(id, pid, name) values(1, -1, '111'),(1, 2, '222'),(1, 4, '333');",
        },
        {
            .sql = "update tufunc set pid=abs(-99), name=upper('Aaa') where pid=2;",
        },
        {
            .sql = "select abs(2),abs(1.32) from tufunc;",
        },
        {
            .sql = "select * from tufunc;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "pid", "name"}, {{"1", "-1", "111"}, {"1", "99", "AAA"}, {"1", "4", "333"}}},
        },
        {
            .sql = "update tufunc set pid='222' like '222' where pid<0;",
        },
        {
            .sql = "select * from tufunc;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "pid", "name"}, {{"1", "1", "111"}, {"1", "99", "AAA"}, {"1", "4", "333"}}},
        },
    };
    for (auto testCase : cases) {
        ret = GmeSqlExecute(conn, (char *)testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, NULL, NULL);
        ASSERT_EQ(testCase.expectRet, ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(testCase.exptQryResult);
            StEmbSqlClearActQryResultSet();
        }
    }
}
