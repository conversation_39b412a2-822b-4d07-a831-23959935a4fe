/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: testsuite for buffer pool online resize bufferPoolSize
 * Author: yangyongji
 * Create: 2025-2-26
 */
#include "st_emb_sql_util.h"
#include "st_emb_sql_persistence_base.h"
#include "srv_emb_sql_com.h"
#include "st_emb_sql_com.h"
#include "db_instance.h"
#include "se_buffer_pool_inner.h"
#include "se_buffer_pool_resize.h"

using namespace std;

#ifdef ST_SQL_DEBUG
#define ST_SQL_PRINTF(format, ...) printf(format, ##__VA_ARGS__)
#else
#define ST_SQL_PRINTF(format, ...)
#endif

class StBufferpoolResize : public StEmbSqlTestSuitExtend {
protected:
    static void SetUpTestCase()
    {
        StEmbSqlTestSuitBase::SetUpTestCase();
        init();
    }
    static void TearDownTestCase()
    {
        // 清除打桩
        clearAllStub();
        StEmbSqlTestSuitBase::TearDownTestCase();
    }

    virtual void SetUp()
    {
        StEmbSqlSuitCloseConn();  // 没有成功
        StEmbSqlSuitOpenConn();
    }
};

static void QueryGetViewCapacityAndHwm(GmeConnT *conn, uint32_t &bufferpoolCapacity, uint32_t &viewHwm)
{
    string sqlStr = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    auto ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);

    const int capacityColIdx = 3;  // capacity
    ST_SQL_PRINTF("check to get capacity name:%s values:%d\n", resultSet.colNames[capacityColIdx].c_str(),
        stoi(resultSet.colValues[0][capacityColIdx]));
    bufferpoolCapacity = stoi(resultSet.colValues[0][capacityColIdx]);

    const int hwmColIdx = 4;  // HWM
    ST_SQL_PRINTF("check to get HWM name:%s values:%d\n", resultSet.colNames[hwmColIdx].c_str(),
        stoi(resultSet.colValues[0][hwmColIdx]));
    viewHwm = stoi(resultSet.colValues[0][hwmColIdx]);
}

// 仅构建，不上门禁
TEST_F(StBufferpoolResize, queryStatusWithoutChunk)
{
    // bufferpoolChunkSize不配置的情况下，能否取默认值0, 表示不开启chunkMgr
    GmeConnT *conn = StEmbSqlGetConn();
    DbMemCtxT *connMemCtx = conn->conMemCtx;  // release disorienting for conn->embSession->session->memCtx
    DbInstanceT *dbInstance = (DbInstanceT *)DbGetInstanceByMemCtx(connMemCtx);
    SeInstanceHdT seInsPtr = (SeInstanceT *)SeGetInstance(DbGetInstanceId((DbInstanceHdT)dbInstance));
    EXPECT_TRUE(seInsPtr != nullptr);
    EXPECT_EQ(seInsPtr->seConfig.bpChunkSize, 0);
    BufpoolMgrT *pageMgr = (BufpoolMgrT *)seInsPtr->pageMgr;
    EXPECT_NE(pageMgr, nullptr);
    EXPECT_TRUE(pageMgr->resizeMgr == NULL);
    EXPECT_FALSE(pageMgr->enableSplitChunk);

    // 测试接口能否调用，预期会返回不支持
    uint32_t oldCapacity = pageMgr->bufPool->capacity;
    uint32_t newBufferPoolSize = SIZE_M(10) / SIZE_K(1);  // 保证小于maxSysDynSize (512M)
    auto ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    // 报错之后，且容量不变
    EXPECT_EQ(oldCapacity, pageMgr->bufPool->capacity);
}

Status TryOpenDefaultConfig(GmeConnT **outConn)
{
    // 读取修改后的配置项启动
    char bpResizeConfigPath[DB_MAX_PATH] = {0};
    char *gmdbHome = getenv("GMDB_HOME");

    // 修改配置文件之后生成临时配置文件
    char *baseModPath = (char *)"/test/dt/st/012_emb_sql/00_config/st_emb_sql_persistence_tmp.ini";
    memset_s(bpResizeConfigPath, sizeof(bpResizeConfigPath), 0, sizeof(bpResizeConfigPath));
    strcat_s(bpResizeConfigPath, sizeof(bpResizeConfigPath), gmdbHome);
    strcat_s(bpResizeConfigPath, sizeof(bpResizeConfigPath), baseModPath);
    return GmeOpen(bpResizeConfigPath, GME_OPEN_CREATE, outConn);
}

static Status BpResizeOpenConnWithConfig(GmeConnT **outConn, const char *modifyItem)
{
    // 原始配置项文件，确认modifyItem修改的配置项在这个文件中都存在 --> st_emb_sql_persistence_tmp.ini
    const char *originIni = "00_config/st_emb_sql_bufferpool_resize.ini";
    StSqlModifyIniConfigFile(originIni, modifyItem);

    return TryOpenDefaultConfig(outConn);
}

class StBufferpoolResizeConfig : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // clean data for persistence scenarios
        system("rm -rf /data/gmdb");
        system("ipcrm -a");
        init();
    }
    static void TearDownTestCase()
    {
        // 清除打桩
        clearAllStub();
    }
    virtual void SetUp()
    {
        system("rm -rf /data/gmdb/*");
        system("ipcrm -a");
    }
    virtual void TearDown()
    {
        system("rm -rf /data/gmdb/*");
        system("ipcrm -a");
    }
};

static void StartWithChunkSizeErr(uint32_t chunkSize, Status expectRet = GMERR_CONFIG_ERROR)
{
    string modifyConfigItemStr = "\"bufferPoolChunkSize=" + to_string(chunkSize) +
                                 "\" "
                                 "\"maxTotalDynSize=2048\" \"maxSysDynSize=512\" \"bufferPoolSize=40960\"";
    GmeConnT *conn = NULL;
    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    // int32 value is not in the proper range
    EXPECT_EQ(ret, expectRet) << "invalid chunkSize:" << chunkSize;
    if (conn) {
        ret = GmeClose(conn);
        EXPECT_EQ(ret, GMERR_OK);
    }
}
/**
 * @tc.name: configErrChunkSize_001
 * @tc.desc: test err config for chunkSize.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, configErrChunkSize_001)
{
    // bufferpoolChunkSize配置不合理
    // 不是[0, 4G] 超过最大值
    uint32_t chunkSize = SIZE_G(5) / SIZE_K(1);  // 单位KB
    // int32 value is not in the proper range
    StartWithChunkSizeErr(chunkSize, GMERR_DATA_EXCEPTION);

    // 0-128M之间是不合理的, 127M
    chunkSize = SIZE_M(127) / SIZE_K(1);
    // Configuration unsucc. bp chunk size:131KB
    StartWithChunkSizeErr(chunkSize);

    // [128M, 4G], 不是1M倍数不合理
    chunkSize = (SIZE_M(128) + SIZE_K(50)) / SIZE_K(1);
    // Configuration unsucc. bp chunk size:131122KB
    StartWithChunkSizeErr(chunkSize);
}

// chunkSize非0，bufferpoolSize配置为比chunkSize小，报错
/**
 * @tc.name: configErrBufferpoolLessChunkSize_002
 * @tc.desc: test bufferpool size less than chunk size, expect return err.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, configErrBufferpoolLessChunkSize_002)
{
    GmeConnT *conn = nullptr;
    // bufferpoolChunkSize配置不合理，bufferpoolSize 40M 比chunkSize 130m小
    auto chunkSize = SIZE_M(130) / SIZE_K(1);
    auto bufferPoolSize = SIZE_M(40) / SIZE_K(1);
    string modifyConfigItemStr = "\"bufferPoolChunkSize=" + to_string(chunkSize) +
                                 "\" "
                                 "\"maxTotalDynSize=2048\" \"maxSysDynSize=1024\" \"bufferPoolSize=" +
                                 to_string(bufferPoolSize) + "\"";

    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    // Configuration unsucc. bp size:40960KB, chunk:133120KB
    EXPECT_EQ(ret, GMERR_CONFIG_ERROR);

    // 配置一样大，是允许的
    chunkSize = SIZE_M(130) / SIZE_K(1);
    bufferPoolSize = SIZE_M(130) / SIZE_K(1);
    auto maxTotalDynSize = SIZE_M(2000) / SIZE_M(1);
    auto maxSysDynSize = SIZE_M(1000) / SIZE_M(1);
    modifyConfigItemStr =
        "\"bufferPoolChunkSize=" + to_string(chunkSize) + "\" \"maxTotalDynSize=" + to_string(maxTotalDynSize) +
        "\" \"maxSysDynSize=" + to_string(maxSysDynSize) + "\" \"bufferPoolSize=" + to_string(bufferPoolSize) + "\"";
    ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

void CheckBufferpoolChunkSizeWithView(uint32_t bufferPoolSize, uint32_t chunkSize, uint32_t pageSize)
{
    GmeConnT *conn = nullptr;
    printf("try config buferpoosize:%d chunkSize:%d pagesize:%d\n", bufferPoolSize, chunkSize, pageSize);
    string modifyConfigItemStr = "\"bufferPoolChunkSize=" + to_string(chunkSize) +
                                 "\" "
                                 "\"pageSize=" +
                                 to_string(pageSize) +
                                 "\" "
                                 "\"bufferPoolPolicy=" +
                                 to_string(3) +
                                 "\" "
                                 "\"maxTotalDynSize=20480\" \"maxSysDynSize=10240" +
                                 "\" "
                                 "\"bufferPoolSize=" +
                                 to_string(bufferPoolSize) + "\"";
    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 启动成功，查询视图CONFIG_PARAMETERS配置项的值bufferPoolSize没用，只能查询bufferpool视图，查看capacity
    string sqlStr = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    const int capacityColIdx = 3;  // 第3个状态为capacity
    // 配置校验是否会自动取整。bufferpoolize，查询视图。capacity = bufferPoolSize / pageSize
    auto expectCapacity = bufferPoolSize / chunkSize * chunkSize / pageSize;
    EXPECT_EQ(stoi(resultSet.colValues[0][capacityColIdx]), expectCapacity);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 配置校验是否会自动取整。bufferpoolize，查询视图。capacity = bufferPoolSize / pageSize
/**
 * @tc.name: configBufferpoolAutoRoundingChunkSize_003
 * @tc.desc: test bufferpool size should be auto rounding chunk size.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, configBufferpoolAutoRoundingChunkSize_003)
{
    // 配置bufferpool稍微大于2倍chunkSize
    uint32_t bufferPoolSize = SIZE_G(4) / SIZE_K(1);
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);

    CheckBufferpoolChunkSizeWithView(bufferPoolSize, chunkSize, pageSize);

    // 配置bufferpool等于chunkSize
    chunkSize = SIZE_M(130) / SIZE_K(1);
    bufferPoolSize = chunkSize;
    CheckBufferpoolChunkSizeWithView(bufferPoolSize, chunkSize, pageSize);
}

// 按照配置启动db，预期ok，返回conn。外部close
static Status ResizeOpenConfig(
    uint32_t bufferPoolSize, uint32_t bufferPoolChunkSize, uint32_t pageSize, GmeConnT **outConn)
{
    GmeConnT *conn = nullptr;
    printf("try open config buferpoosize:%d chunkSize:%d pagesize:%d\n", bufferPoolSize, bufferPoolChunkSize, pageSize);
    string modifyConfigItemStr = "\"bufferPoolSize=" + to_string(bufferPoolSize) +
                                 "\" "
                                 "\"bufferPoolChunkSize=" +
                                 to_string(bufferPoolChunkSize) +
                                 "\" "
                                 "\"pageSize=" +
                                 to_string(pageSize) +
                                 "\" "
                                 "\"maxTotalDynSize=20480\" \"maxSysDynSize=10240" +
                                 "\" "
                                 "\"bufferPoolPolicy=3" +
                                 "\"";
    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    *outConn = conn;
    return ret;
}

static Status ResizeOpenConfigPreFetch(
    uint32_t bufferPoolSize, uint32_t bufferPoolChunkSize, uint32_t pageSize, GmeConnT **outConn)
{
    GmeConnT *conn = nullptr;
    printf("try open config buferpoosize:%d chunkSize:%d pagesize:%d\n", bufferPoolSize, bufferPoolChunkSize, pageSize);
    string modifyConfigItemStr = "\"bufferPoolSize=" + to_string(bufferPoolSize) +
                                 "\" "
                                 "\"bufferPoolChunkSize=" +
                                 to_string(bufferPoolChunkSize) +
                                 "\" "
                                 "\"pageSize=" +
                                 to_string(pageSize) +
                                 "\" "
                                 "\"maxTotalDynSize=20480\" \"maxSysDynSize=10240" +
                                 "\" "
                                 "\"bufferPoolPolicy=3" +
                                 "\" "
                                 "\"preFetchPagesEnable=1 \"";
    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    *outConn = conn;
    return ret;
}

// 查询没有配置chunk情况下的视图字段
/**
 * @tc.name: queryBufferPoolViewStatusNoChunk_004
 * @tc.desc: chunk view filed in STORAGE_BUFFERPOOL_STAT. RESIZE_STATUS
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, queryBufferPoolViewStatusNoChunk_004)
{
    GmeConnT *conn = nullptr;
    auto chunkSize = 0;  // 默认为0
    auto bufferPoolSize = SIZE_M(40) / SIZE_K(1);
    auto pageSize = SIZE_K(32) / SIZE_K(1);
    auto ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    string sqlStr = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    // AR.SR.IR20241018001387.017.001 增加resize_status字段之后，变成11个字段。
    EXPECT_EQ(resultSet.colNames.size(), 12);
    // 一行数据
    EXPECT_EQ(resultSet.colValues.size(), 1);

    const int resizeColIdx = 11;  // 第11个状态为resize status
    ST_SQL_PRINTF("check to get name:%s values:%s %d\n", resultSet.colNames[resizeColIdx].c_str(),
        resultSet.colValues[0][resizeColIdx].c_str(), stoi(resultSet.colValues[0][resizeColIdx]));
    string resizeStatusString = "RESIZE_STATUS";
    EXPECT_EQ(resultSet.colNames[resizeColIdx], "RESIZE_STATUS");
    // 0表示离线 RESIZE_STATUS
    EXPECT_EQ(stoi(resultSet.colValues[0][resizeColIdx]), 0);
    EXPECT_EQ(resultSet.colNames[resizeColIdx], resizeStatusString);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 按照配置启动db，预期ok，返回conn。外部close
static Status ResizeOpenConfig_005(uint32_t bufferPoolSize, uint32_t chunkSize, uint32_t pageSize, GmeConnT **outConn)
{
    GmeConnT *conn = nullptr;
    printf("try open config buferpoosize:%d chunkSize:%d pagesize:%d\n", bufferPoolSize, chunkSize, pageSize);
    string modifyConfigItemStr = "\"bufferPoolChunkSize=" + to_string(chunkSize) +
                                 "\" "
                                 "\"pageSize=" +
                                 to_string(pageSize) +
                                 "\" "
                                 "\"maxTotalDynSize=2048\" \"maxSysDynSize=512" +
                                 "\" "
                                 "\"bufferPoolSize=" +
                                 to_string(bufferPoolSize) + "\"";
    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    *outConn = conn;
    return ret;
}

// 测试接口功能 conn
/**
 * @tc.name: bufferpoolResizeCheckConn_005
 * @tc.desc: test bufferpool resize feature.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeCheckConn_005)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    // conn参数不合理（空指针，或者已经close的connection）

    // 空指针conn
    Status ret = GMERR_OK;
    ret = GmeBufferpoolResize(NULL, 0);
    EXPECT_EQ(ret, GMERR_UNEXPECTED_NULL_VALUE);

    GmeConnT *conn = nullptr;
    ret = ResizeOpenConfig_005(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newBufferPoolSize = SIZE_M(130) / SIZE_K(1);
    // 已经释放掉的conn，无法保证行为，可能出现野指针访问

    // 使用新的conn
    GmeConnT *newConn = nullptr;
    ret = ResizeOpenConfig_005(bufferPoolSize, chunkSize, pageSize, &newConn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeBufferpoolResize(newConn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);  // 设置的和当前一致

    GmeConnT *newConn2 = nullptr;
    ret = ResizeOpenConfig_005(bufferPoolSize, chunkSize, pageSize, &newConn2);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeBufferpoolResize(newConn2, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    // 并发调用resize接口
    // 预期同一时刻只会响应一个,其他的接口应该报错返回

    if (newConn) {
        Status ret = GmeClose(newConn);
        EXPECT_EQ(ret, GMERR_OK);
    }

    if (newConn2) {
        Status ret = GmeClose(newConn2);
        EXPECT_EQ(ret, GMERR_OK);
    }
}

// 测试接口功能 newBufferPoolSize
/**
 * @tc.name: bufferpoolResizeCheck_006
 * @tc.desc: test bufferpool resize feature.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeCheck_006)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * 2.3);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // resize 超过bufferpool最大理论阈值，要报错 GMERR_PROGRAM_LIMIT_EXCEEDED
    uint32_t newBufferPoolSize = (SIZE_T(1) + SIZE_G(1)) / SIZE_K(1);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 之后尝试resize超过阈值，然后报错330992431 323234KB 315M 7296265
    newBufferPoolSize = 7296266;  // (SIZE_M(317)) / SIZE_K(1);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);
    newBufferPoolSize = 7296265;  // (SIZE_M(317)) / SIZE_K(1);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_OK);

    // 正常的数值
    // 执行太块，会报错正在执行
    newBufferPoolSize = SIZE_M(140) / SIZE_K(1);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_TRUE(ret == GMERR_BUFFERPOOL_ALREADY_RESIZING || ret == GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试接口功能 newBufferpoolSize，测试重启能够恢复
/**
 * @tc.name: bufferpoolResizeCheck_007
 * @tc.desc: test bufferpool resize feature.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeCheck_007)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * 2.3);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t newBufferPoolSize = SIZE_M(140) / SIZE_K(1);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);

    // 重启
    conn = nullptr;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    if (conn) {
        Status ret = GmeClose(conn);
        EXPECT_EQ(ret, GMERR_OK);
    }
}

// 按照配置启动db，预期ok，返回conn。外部close
static Status ResizeOpenConfigWithSysDynSize(
    uint32_t bufferPoolSize, uint32_t sysDynSize, uint32_t chunkSize, uint32_t pageSize, GmeConnT **outConn)
{
    printf("try open config buferpoolsize:%d KB, sysDsynSize:%d MB chunkSize:%d KB pagesize:%d KB\n", sysDynSize,
        bufferPoolSize, chunkSize, pageSize);
    // 配置一样大，是允许的
    auto maxTotalDynSize = SIZE_M(4000) / SIZE_M(1);
    string modifyConfigItemStr =
        "\"bufferPoolChunkSize=" + to_string(chunkSize) + "\" \"maxTotalDynSize=" + to_string(maxTotalDynSize) +
        "\" \"maxSysDynSize=" + to_string(sysDynSize) + "\" \"bufferPoolSize=" + to_string(bufferPoolSize) + "\"";
    return BpResizeOpenConnWithConfig(outConn, modifyConfigItemStr.c_str());
}

// 门槛1 resize正常参数，执行正常（可以正常开库，正常resize就行）
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_008)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * 4.2);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t sysDynSize = SIZE_M(1000) / SIZE_M(1);

    // maxTotalDynSize 2T sysDynSize 1T
    // chunk 130M，pagesize 32K，bufferpoolsize = 550M 4个chunk，520M
    // 准备调小3个chunk，400M.调小之后，从视图查看capacity hwm变小了(按照390计算)
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfigWithSysDynSize(bufferPoolSize, sysDynSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 1.0f * 3.4);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询视图状态
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

void QueryGetViewResizeStatus(
    GmeConnT *conn, uint32_t &resizeStatus, uint32_t &bufferpoolCapacity, uint32_t &bufferPoolSizeFromView)
{
    string sqlStr = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    auto ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    const int resizeColIdx = 11;  // 第10个状态为resize status
    // we could print status like resultSet.colNames[resizeColIdx].c_str(), stoi(resultSet.colValues[0][resizeColIdx])
    ST_SQL_PRINTF("check to get resize st name:%s values:%d\n", resultSet.colNames[resizeColIdx].c_str(),
        stoi(resultSet.colValues[0][resizeColIdx]));
    resizeStatus = stoi(resultSet.colValues[0][resizeColIdx]);

    const int capacityColIdx = 3;  // capacity
    ST_SQL_PRINTF("check to get capacity name:%s values:%d\n", resultSet.colNames[capacityColIdx].c_str(),
        stoi(resultSet.colValues[0][capacityColIdx]));
    bufferpoolCapacity = stoi(resultSet.colValues[0][capacityColIdx]);

    const int bufferpoolSizeColIdx = 1;  // BUFFERPOOL_SIZE
    ST_SQL_PRINTF("check to get bufferPoolSize name:%s values:%d\n", resultSet.colNames[bufferpoolSizeColIdx].c_str(),
        stoi(resultSet.colValues[0][bufferpoolSizeColIdx]));
    bufferPoolSizeFromView = stoi(resultSet.colValues[0][bufferpoolSizeColIdx]);
}

static void WaitResizeExit(GmeConnT *conn, uint32_t resizeBufferpoolSize, uint32_t chunkSize, uint32_t pageSize)
{
    uint32_t beforeResizeStatus = BP_RESIZE_STATUS_BUTT;
    uint32_t beforeBufferpoolCapacity = 0;
    uint32_t beforeBufferpoolSize = 0;
    QueryGetViewResizeStatus(conn, beforeResizeStatus, beforeBufferpoolCapacity, beforeBufferpoolSize);
    printf("resize before status:%d bufferpool size:%d capacity:%d\n", beforeResizeStatus, beforeBufferpoolSize,
        beforeBufferpoolCapacity);

    auto ret = GmeBufferpoolResize(conn, resizeBufferpoolSize);
    EXPECT_EQ(ret, GMERR_OK);

    // 可能resize过程很快结束。状态不能看出来表示结束。最多循环100次，1s
    uint32_t resizeStatus = BP_RESIZE_STATUS_BUTT;  // --> BP_RESIZE_STATUS_OFFLINE
    uint32_t bufferpoolCapacity = 0;
    uint32_t bufferPoolSize = 0;
    uint32_t sleepCnt = 0;
    while (sleepCnt++ < 1500000) {  // 默认15 * 1000 ms 控制等待时间 15s
        if (sleepCnt % 200) {       // 2s 查询一次
            QueryGetViewResizeStatus(conn, resizeStatus, bufferpoolCapacity, bufferPoolSize);
        }
        // 最多等10s
        DbSleep(10);
        // 是否已经修正 bufferPoolSize != beforeBufferpoolSize
        if (resizeStatus == BP_RESIZE_STATUS_OFFLINE) {
            // 查询到数值变化则退出
            break;
        }
    }

    printf("resize after status:%d bufferpool size:%d capacity:%d\n", resizeStatus, bufferPoolSize, bufferpoolCapacity);
    EXPECT_EQ(resizeStatus, BP_RESIZE_STATUS_OFFLINE);
    // 需要为chunkSize倍数
    uint32_t expectBufferpoolSize = resizeBufferpoolSize / chunkSize * chunkSize;
    // 按照expectBufferpoolSize计算多少个页
    uint32_t expectBufferpoolCapacity = expectBufferpoolSize / pageSize;
    EXPECT_EQ(bufferPoolSize, expectBufferpoolSize);
    EXPECT_EQ(bufferpoolCapacity, expectBufferpoolCapacity);
}

// bufferpoolSize配置需要大于chunkSize，否则会报错.
// resize时候也需要大于bufferpoolSize.
static void CheckStatusAfterNormalStart(
    uint32_t chunkSize, float bufferPoolSizeRatioRelatedChunkSize = 4.2f, float resizeRatioRelatedChunkSize = 3.4f)
{
    // 必须为pageSize倍数
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * bufferPoolSizeRatioRelatedChunkSize) / pageSize * pageSize;
    uint32_t sysDynSize = SIZE_M(3000) / SIZE_M(1);

    // maxTotalDynSize 2T sysDynSize 1T
    // chunk 130M，pagesize 32K，bufferpoolsize = 550M 4个chunk，520M
    // 准备调小3个chunk，400M.调小之后，从视图查看capacity hwm变小了(按照390计算)
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfigWithSysDynSize(bufferPoolSize, sysDynSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 1.0f * resizeRatioRelatedChunkSize);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 不上门禁，仅构建
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_009)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    // resize before status:0 bufferpool size:532480 capacity:16640
    // resize after status:0 bufferpool size:452608 capacity:14144
    CheckStatusAfterNormalStart(chunkSize);
}

// 门槛2 bufferpoolTrunkSize = 0，bufferpoolSize尝试配置，预期不可配。且配置不变
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_010)
{
    uint32_t chunkSize = 0;
    // maxTotalDynSize 2T sysDynSize 1T
    // chunk 0，pagesize 32K，bufferpoolsize = 40/2000M 功能未开启。
    // 准备调小150M.
    uint32_t bufferPoolSize = SIZE_M(200) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t sysDynSize = SIZE_M(1000) / SIZE_M(1);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfigWithSysDynSize(bufferPoolSize, sysDynSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t beforeResizeStatus = BP_RESIZE_STATUS_BUTT;
    uint32_t beforeBufferpoolCapacity = 0;
    uint32_t beforeBufferpoolSize = 0;
    QueryGetViewResizeStatus(conn, beforeResizeStatus, beforeBufferpoolCapacity, beforeBufferpoolSize);
    // resize before status:0 bufferpool size:204800 capacity:6400
    printf("resize before status:%d bufferpool size:%d capacity:%d\n", beforeResizeStatus, beforeBufferpoolSize,
        beforeBufferpoolCapacity);

    uint32_t newBufferPoolSize = SIZE_M(150) / SIZE_K(1);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    // Feature is not supported. config bufferPoolChunkSize
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);  // 不支持

    // 查询数据应该不变
    // 可能resize过程很快结束。状态不能看出来表示结束。最多循环100次，1s
    uint32_t resizeStatus = BP_RESIZE_STATUS_BUTT;  // --> BP_RESIZE_STATUS_OFFLINE
    uint32_t bufferpoolCapacity = 0;
    uint32_t bufferPoolSizeFromView = 0;
    QueryGetViewResizeStatus(conn, resizeStatus, bufferpoolCapacity, bufferPoolSizeFromView);
    // resize err after status:0 bufferpool size:204800 capacity:6400
    printf("resize err after status:%d bufferpool size:%d capacity:%d\n", resizeStatus, bufferPoolSizeFromView,
        bufferpoolCapacity);

    EXPECT_EQ(resizeStatus, BP_RESIZE_STATUS_OFFLINE);
    EXPECT_EQ(bufferPoolSize, beforeBufferpoolSize);
    EXPECT_EQ(bufferpoolCapacity, beforeBufferpoolCapacity);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 门槛3 bufferpoolTrunkSize = 128M，预期开库执行正常。查询符合预期配置
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_011)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    // resize before status:0 bufferpool size:524288 capacity:16384
    // resize after status:0 bufferpool size:445644 capacity:13926
    CheckStatusAfterNormalStart(chunkSize);
}

// 测试rehash调小的情况，3倍数
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_012)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    CheckStatusAfterNormalStart(chunkSize, 4.5f, 1.3f);
}

// 测试调大不rehash的情况
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_013)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    CheckStatusAfterNormalStart(chunkSize, 2.7f, 3.7f);

    chunkSize = SIZE_M(135) / SIZE_K(1);
    CheckStatusAfterNormalStart(chunkSize, 2.5f, 3.8f);
}

// 测试调大rehash的情况，增长了4倍
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_014)
{
    // Not normal parameter value. limit ctx:689185994 2.7f, 10.3f
    // Not normal parameter value. limit ctx:689185994 2.7f, 6.5f
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    CheckStatusAfterNormalStart(chunkSize, 2.7f, 6.5f);
}

static void ResizeInsertTableData(GmeConnT *conn, uint32_t maxColNum, uint32_t insertTimes, uint32_t beginId = 0)
{
    for (int m = 0; m < insertTimes; ++m) {
        // 插入 1 ~ 1024 列 不超过 32KB的数据
        string insertSql = "INSERT INTO resize_employee VALUES(";
        for (uint32_t i = 1; i <= maxColNum; ++i) {
            insertSql += "?,";
        }
        insertSql[insertSql.length() - 1] = ')';
        insertSql += ";";

        GmeSqlStmtT *stmt = NULL;
        auto ret = GmeSqlPrepare(conn, insertSql.c_str(), insertSql.length(), &stmt, NULL);
        ASSERT_EQ(GMERR_OK, ret);

        char remark[900] = {0};  // GMERR_PROGRAM_LIMIT_EXCEEDED
        for (uint32_t i = 1; i <= maxColNum; ++i) {
            // 字符串长度等于1KB, 注意，DB内部会自动计算结尾符，用户传入通过strlen获取的长度
            // 序列化后 runningBuf 实际会超过 31KB 但小于 32KB
            (void)memset_s(remark, sizeof(remark), 'a' + m + beginId, sizeof(remark) - 1);
            ret = GmeSqlBindText(stmt, i, remark, strlen(remark), NULL);
            ASSERT_EQ(GMERR_OK, ret);
        }

        ret = GmeSqlStep(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
static void ResizeInsertBigData(GmeConnT *conn, uint32_t maxColNum, uint32_t insertTimes)
{
    string createSql = "CREATE TABLE resize_employee (";
    for (uint32_t i = 1; i <= maxColNum; ++i) {
        string colStr = "n";
        colStr += to_string(i);
        colStr += " text,";
        createSql += colStr;
    }
    createSql[createSql.length() - 1] = ')';
    createSql += ";";

    Status ret = GmeSqlExecute(conn, (char *)createSql.c_str(), NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    ResizeInsertTableData(conn, maxColNum, insertTimes);
}

// 测试rehash调小的情况，3倍数(有数据，触发回收chunk); 执行时间较长，暂不放到门禁 43s
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeBase_015)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    auto bufferPoolSizeRatioRelatedChunkSize = 4.5f;
    // 必须为pageSize倍数
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * bufferPoolSizeRatioRelatedChunkSize) / pageSize * pageSize;
    uint32_t sysDynSize = SIZE_M(1000) / SIZE_M(1);

    // maxTotalDynSize 2T sysDynSize 1T
    // chunk 130M，pagesize 32K，bufferpoolsize = 550M 4个chunk，520M
    // 准备调小3个chunk，400M.调小之后，从视图查看capacity hwm变小了(按照390计算)
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfigWithSysDynSize(bufferPoolSize, sysDynSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 800;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    // 查询视图，看看数据是否满了此时的hwm
    uint32_t viewCapacity = 0;
    uint32_t viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);
    auto resizeRatioRelatedChunkSize = 1.3f;
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 1.0f * resizeRatioRelatedChunkSize);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);

    viewCapacity = 0;
    viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);
    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

void CheckBufferpoolChunkSizePolicyWithView(
    uint32_t bufferPoolSize, uint32_t chunkSize, uint32_t pageSize, uint32_t bufferPoolPolicy)
{
    GmeConnT *conn = nullptr;
    printf("try config buferpoosize:%d chunkSize:%d pagesize:%d\n", bufferPoolSize, chunkSize, pageSize);
    string modifyConfigItemStr = "\"bufferPoolChunkSize=" + to_string(chunkSize) +
                                 "\" "
                                 "\"pageSize=" +
                                 to_string(pageSize) +
                                 "\" "
                                 "\"bufferPoolPolicy=" +
                                 to_string(bufferPoolPolicy) +
                                 "\" "
                                 "\"maxTotalDynSize=20480\" \"maxSysDynSize=10240" +
                                 "\" "
                                 "\"bufferPoolSize=" +
                                 to_string(bufferPoolSize) + "\"";
    // 3145728
    auto ret = BpResizeOpenConnWithConfig(&conn, modifyConfigItemStr.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试使用policy BUF_RECYCLE_VECTOR_INDEX 3
// 配置4G 出现翻转 DTS2025030405195 DTS2025030424820
TEST_F(StBufferpoolResizeConfig, configVectorIndexPolicy_016)
{
    uint32_t bufferPoolSize = SIZE_G(4) / SIZE_K(1);
    uint32_t chunkSize = SIZE_G(4) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolPolicy = BUF_RECYCLE_VECTOR_INDEX;

    CheckBufferpoolChunkSizePolicyWithView(bufferPoolSize, chunkSize, pageSize, bufferPoolPolicy);

    bufferPoolSize = (SIZE_G(4) + SIZE_M(256)) / SIZE_K(1);
    CheckBufferpoolChunkSizePolicyWithView(bufferPoolSize, chunkSize, pageSize, bufferPoolPolicy);
}

void QueryGetViewCapacity(GmeConnT *conn, uint32_t &bufferpoolCapacity)
{
    string sqlStr = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    auto ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);

    const int capacityColIdx = 3;  // capacity
    ST_SQL_PRINTF("check to get capacity name:%s values:%d\n", resultSet.colNames[capacityColIdx].c_str(),
        stoi(resultSet.colValues[0][capacityColIdx]));
    bufferpoolCapacity = stoi(resultSet.colValues[0][capacityColIdx]);
}

// 测试接口功能 newBufferPoolSize
/**
 * @tc.name: bufferpoolResizeCheck_016
 * @tc.desc: test bufferpool resize feature. resize min value
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250210001703.005.001 TD AR.SR.IR20241018001387.017.001
 * @tc.author: yangyongji
 */
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeCheckMinValue_017)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * 2.3);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 1;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    uint32_t newBufferPoolSize = 0;
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    newBufferPoolSize = chunkSize - 1;
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    newBufferPoolSize = chunkSize;
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);  // 2倍变成1倍
    EXPECT_EQ(ret, GMERR_OK);

    newBufferPoolSize = chunkSize + 1;
    // 和当前值一致
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    // 可能resize过程很快结束。状态不能看出来表示结束。最多循环100次，1s
    uint32_t viewBufferpoolCapacity = 0;
    uint32_t viewBufferPoolSize = 0;
    uint32_t viewResizeStatus = BP_RESIZE_STATUS_BUTT;  // --> BP_RESIZE_STATUS_OFFLINE
    uint32_t sleepCnt = 0;
    while (sleepCnt++ < 500) {  // 默认5 * 1000 ms 控制等待时间 5s
        if (sleepCnt % 100) {   // 1s 查询一次
            QueryGetViewResizeStatus(conn, viewResizeStatus, viewBufferpoolCapacity, viewBufferPoolSize);
        }
        DbSleep(10);
        if (viewResizeStatus == BP_RESIZE_STATUS_OFFLINE) {
            // 查询到数值变化则退出
            break;
        }
    }
    ST_SQL_PRINTF("resize after status:%d bufferpool size:%d capacity:%d\n", viewResizeStatus, viewBufferPoolSize,
        viewBufferpoolCapacity);

    // 启动成功，查看capacity
    auto expectCapacity = newBufferPoolSize / chunkSize * chunkSize / pageSize;
    uint32_t viewCapacity = 0;
    QueryGetViewCapacity(conn, viewCapacity);
    EXPECT_EQ(viewCapacity, expectCapacity);

    char *selectSql = (char *)"select * from resize_employee;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 设置bufferpool size 10M，maxTotalDynSize maxSysDynSize配置合理。当其他memctx尝试申请内存能否触发申请内存不足
// bufferpool插入一定数据量，如5M；其他memCtx不断申请内存能够触发报错内存不足
// 此时bufferpool还能够申请内存，如能够从5M涨到10M。
void QueryBucketNumAfterResize(GmeConnT *conn, uint32_t bufferPoolSize, uint32_t chunkSize, uint32_t pageSize)
{
    string sqlStr = "select * from 'V$STORAGE_BUFFERPOOL_STAT'";
    ViewResultSet resultSet;
    auto ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);

    const int bucketNumsColIdx = 5;  // bucketNum
    ST_SQL_PRINTF("check to get name:%s values:%s %d\n", resultSet.colNames[bucketNumsColIdx].c_str(),
        resultSet.colValues[0][bucketNumsColIdx].c_str(), stoi(resultSet.colValues[0][bucketNumsColIdx]));
    auto expectBucketNums = bufferPoolSize / chunkSize * chunkSize / pageSize * BUCKET_TIMES;
    EXPECT_EQ(stoi(resultSet.colValues[0][bucketNumsColIdx]), expectBucketNums);
}
// resize 2倍，还原
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeLargerAndBack_018)
{
    uint32_t chunkSize = SIZE_M(130) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 1.0f * 1.1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 1;
    ResizeInsertBigData(conn, maxColNum, insertTimes);
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 1.0f * 2.3);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    // 未发生变化，根据第一次bufferPoolSize来计算
    QueryBucketNumAfterResize(conn, bufferPoolSize, chunkSize, pageSize);

    // rehash，根据当前bufferPoolSize来计算
    newBufferPoolSize = (uint32_t)(chunkSize * 1.0f * 3.3);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    QueryBucketNumAfterResize(conn, newBufferPoolSize, chunkSize, pageSize);

    newBufferPoolSize = (uint32_t)(chunkSize * 1.0f * 1.3);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    // 发生变化，根据第一次bufferPoolSize来计算
    QueryBucketNumAfterResize(conn, newBufferPoolSize, chunkSize, pageSize);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 插入数据，resize 3倍，drop DTS2025030627704。触发rehash
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeLargerAndBack_019)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 1;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 3);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    // rehash，根据当前bufferPoolSize来计算
    QueryBucketNumAfterResize(conn, newBufferPoolSize, chunkSize, pageSize);

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertTableData(conn, maxColNum, insertTimes, insertTimes);
    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

static void *InsertDataWhileResize(void *args)
{
    GmeConnT *conn = (GmeConnT *)args;
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 3;
    ResizeInsertTableData(conn, maxColNum, insertTimes);
    return NULL;
}

const uint32_t ST_SQL_RESIZE_INS_THREAD_NUM = 10;
// 并发插入数据，resize +3倍。rehash过程有其他页的操作，是否存在问题
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeLargerWhileConInsert_020)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize);

    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 1;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    pthread_t threads[ST_SQL_RESIZE_INS_THREAD_NUM] = {0};
    // 创建多线程并发插入数据
    for (uint32_t i = 0; i < ST_SQL_RESIZE_INS_THREAD_NUM; i++) {
        ret = pthread_create(&threads[i], NULL, InsertDataWhileResize, conn);
        EXPECT_EQ(ret, 0);
    }

    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 3);
    ret = GmeBufferpoolResize(conn, newBufferPoolSize);
    EXPECT_EQ(ret, GMERR_OK);
    // 等待线程结束
    for (uint32_t i = 0; i < ST_SQL_RESIZE_INS_THREAD_NUM; i++) {
        ret = pthread_join(threads[i], NULL);
        EXPECT_EQ(ret, 0);
        threads[i] = 0;
    }

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertTableData(conn, maxColNum, insertTimes, insertTimes);
    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试 配置 256，插满数据，至少满一个 chunk。然后触发回收。 执行时间较长18s
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeLargerAndRecycleLruListCount_021)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 2);
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 200;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    // 查询视图，看看数据是否满了此时的hwm
    uint32_t viewCapacity = 0;
    uint32_t viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);
    // insertTimes需要满足至少插满一个chunk
    EXPECT_GT(viewHwm, chunkSize / pageSize);

    // resize 一个chunk
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 1.2f);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    DbMemCtxT *connMemCtx = conn->conMemCtx;  // release disorienting for conn->embSession->session->memCtx
    DbInstanceT *dbInstance = (DbInstanceT *)DbGetInstanceByMemCtx(connMemCtx);
    SeInstanceHdT seInsPtr = (SeInstanceT *)SeGetInstance(DbGetInstanceId((DbInstanceHdT)dbInstance));
    EXPECT_TRUE(seInsPtr != nullptr);
    BufpoolMgrT *pageMgr = (BufpoolMgrT *)seInsPtr->pageMgr;
    EXPECT_NE(pageMgr, nullptr);
    // 只有一个list，此时count == capacity
    EXPECT_EQ(pageMgr->bufPool->list[LRU_LIST_NORMAL].count, viewCapacity);

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertTableData(conn, maxColNum, 1, insertTimes);  // 插入一轮即可
    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

static int RandomInt(int start, int end)
{
    random_device randDevice;
    mt19937 gen(randDevice());
    uniform_int_distribution<> dis(start, end);
    int randomNum = dis(gen);
    return randomNum;
}

typedef struct DeleteThreadArgs {
    GmeConnT *threadConn;
    bool isRunning;
    uint32_t insertTimes;
} DeleteThreadArgsT;

static void DeleteOneItemFromTable(GmeConnT *conn, uint32_t indexId)
{
    string deleteSql = (string) "delete from resize_normal_employee where id = " + to_string(indexId) + ";";
    ST_SQL_PRINTF("try deleteSql:%d %s\n", indexId, deleteSql.c_str());
    GmeSqlStmtT *stmt = NULL;
    auto ret = GmeSqlPrepare(conn, deleteSql.c_str(), deleteSql.length(), &stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static void *DeleteSqlText(void *args)
{
    DeleteThreadArgsT *threadArgs = (DeleteThreadArgsT *)args;
    while (threadArgs->isRunning) {
        auto deleteIndexId = RandomInt(1, threadArgs->insertTimes);
        DeleteOneItemFromTable(threadArgs->threadConn, deleteIndexId);
        DbSleep(20);
    }
    return NULL;
}

static string GetNormalTableTxt(void)
{
    // resize_normal_employee
    // 5 6 7 d e f
    return "567567567567567567567567567567567567567defdefdefdefdefdefdefdefdefdefdefdefdefdefdefdefdefdefdef";
}

static void ResizeInsertNormalTableData(GmeConnT *conn, uint32_t insertTimes, uint32_t beginId = 0)
{
    for (int m = 0; m < insertTimes; ++m) {
        // 插入 1 ~ 1024 列 不超过 32KB的数据
        string insertSql = "INSERT INTO resize_normal_employee VALUES(";
        // id + name
        insertSql += to_string(m + beginId);
        insertSql += ",'" + GetNormalTableTxt() + "'";
        insertSql += ");";
        GmeSqlStmtT *stmt = NULL;
        auto ret = GmeSqlPrepare(conn, insertSql.c_str(), insertSql.length(), &stmt, NULL);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmeSqlStep(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmeSqlReset(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void ResizeInsertNormalData(GmeConnT *conn, uint32_t insertTimes)
{
    string createSql = "CREATE TABLE resize_normal_employee (id int, name text);";
    Status ret = GmeSqlExecute(conn, (char *)createSql.c_str(), NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ResizeInsertNormalTableData(conn, insertTimes);
}

const int CONCURRENCY_THREAD_NUM = 10;
// 触发新增rehash时候，尝试delete并发读写页，执行时间较长11s
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeRehashWhileDeleteNormal_022)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 2);
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t insertTimes = 10000;
    ResizeInsertNormalData(conn, insertTimes);

    // 查询视图，看看数据是否满了此时的hwm
    uint32_t viewCapacity = 0;
    uint32_t viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    pthread_t threads[CONCURRENCY_THREAD_NUM];
    DeleteThreadArgsT args[CONCURRENCY_THREAD_NUM];
    // 多线程删数据
    for (uint32_t i = 0; i < CONCURRENCY_THREAD_NUM; i++) {
        args[i].threadConn = conn;
        args[i].isRunning = true;
        args[i].insertTimes = insertTimes;
        ret = pthread_create(&threads[i], NULL, DeleteSqlText, &args[i]);
        EXPECT_EQ(0, ret);
    }

    // resize 5个chunk
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 5.1f);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    // 等待线程结束
    for (uint32_t i = 0; i < CONCURRENCY_THREAD_NUM; i++) {
        args[i].isRunning = false;
        EXPECT_EQ(0, pthread_join(threads[i], NULL));
    }

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertNormalTableData(conn, 1, insertTimes);  // 插入一轮即可
    char *dropSql = (char *)"DROP TABLE resize_normal_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}

#if !(defined ASAN || defined UBSAN || defined TSAN || defined COVERAGE)
static void DeleteOneItemFromBigTable(GmeConnT *conn, uint32_t indexId)
{
    char remark[900] = {0};
    (void)memset_s(remark, sizeof(remark), 'a' + indexId, sizeof(remark) - 1);
    string deleteSql = (string) "DELETE FROM resize_employee where n1 = '" + string(remark) + "';";
    GmeSqlStmtT *stmt = NULL;
    auto ret = GmeSqlPrepare(conn, deleteSql.c_str(), deleteSql.length(), &stmt, NULL);
    if (GMERR_OK != ret) {
        // Sql unable to parse origin sql string: DELETE FROM resize_employee where n1 = ''''''''''
        printf("nok try deleteSql:%d %s\n", indexId, deleteSql.c_str());
        return;
    }
    ret = GmeSqlStep(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static void *DeleteSqlTextBigData(void *args)
{
    DeleteThreadArgsT *threadArgs = (DeleteThreadArgsT *)args;
    while (threadArgs->isRunning) {
        auto deleteIndexId = RandomInt(1, threadArgs->insertTimes);
        DeleteOneItemFromBigTable(threadArgs->threadConn, deleteIndexId);
        DbSleep(20);
    }
    return NULL;
}

// 触发新增rehash时候，尝试delete并发读写页，执行时间较长97s
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeRehashWhileDeleteBigData_023)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 2);
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t insertTimes = 700;
    const uint32_t maxColNum = 1024;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    // 查询视图，看看数据是否满了此时的hwm
    uint32_t viewCapacity = 0;
    uint32_t viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    pthread_t threads[CONCURRENCY_THREAD_NUM];
    DeleteThreadArgsT args[CONCURRENCY_THREAD_NUM];
    // 多线程删数据
    for (uint32_t i = 0; i < CONCURRENCY_THREAD_NUM; i++) {
        args[i].threadConn = conn;
        args[i].isRunning = true;
        args[i].insertTimes = insertTimes;
        ret = pthread_create(&threads[i], NULL, DeleteSqlTextBigData, &args[i]);
        EXPECT_EQ(0, ret);
    }

    GmeConnT *queryConn = nullptr;
    ret = TryOpenDefaultConfig(&queryConn);
    ASSERT_EQ(ret, GMERR_OK);
    // resize 5个chunk
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 5.1f);
    WaitResizeExit(queryConn, newBufferPoolSize, chunkSize, pageSize);
    QueryGetViewCapacityAndHwm(queryConn, viewCapacity, viewHwm);

    // 等待线程结束
    for (uint32_t i = 0; i < CONCURRENCY_THREAD_NUM; i++) {
        args[i].isRunning = false;
        EXPECT_EQ(0, pthread_join(threads[i], NULL));
    }

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertTableData(queryConn, maxColNum, 1, insertTimes);  // 插入一轮即可
    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(queryConn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(queryConn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
#endif

typedef struct InsertThreadArgs {
    GmeConnT *threadConn;
    bool isRunning;
    uint32_t minLimit;
    uint32_t maxLimit;
} InsertThreadArgsT;

static void InsertOneItemFromTable(GmeConnT *conn, uint32_t indexId)
{
    string insertSql = (string) "insert into resize_normal_employee values (" + to_string(indexId) + ",'" +
                       GetNormalTableTxt() + "');";
    ST_SQL_PRINTF("try insertSql:%d %s\n", indexId, insertSql.c_str());
    GmeSqlStmtT *stmt = NULL;
    auto ret = GmeSqlPrepare(conn, insertSql.c_str(), insertSql.length(), &stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static void *InsertSqlText(void *args)
{
    InsertThreadArgsT *threadArgs = (InsertThreadArgsT *)args;
    while (threadArgs->isRunning) {
        auto insertIndexId = RandomInt(threadArgs->minLimit, threadArgs->maxLimit);
        InsertOneItemFromTable(threadArgs->threadConn, insertIndexId);
        DbSleep(20);
    }
    return NULL;
}

typedef struct SelectThreadArgs {
    GmeConnT *threadConn;
    bool isRunning;
    uint32_t maxLimit;
} SelectThreadArgsT;

static void SelectOneItemFromTable(GmeConnT *conn, uint32_t indexId)
{
    string selectSql = (string) "select * from resize_normal_employee where id = " + to_string(indexId) + ";";
    ST_SQL_PRINTF("try selectSql:%d %s\n", indexId, selectSql.c_str());
    auto ret = GmeSqlExecute(conn, selectSql.c_str(), StEmbSqlGetQryResultSet, NULL, NULL);
    StEmbSqlCleanQryResultSet(StEmbSqlGetResultSet());
    ASSERT_EQ(GMERR_OK, ret);
}

static void *SelectSqlText(void *args)
{
    SelectThreadArgsT *threadArgs = (SelectThreadArgsT *)args;
    while (threadArgs->isRunning) {
        auto selectIndexId = RandomInt(1, threadArgs->maxLimit);
        SelectOneItemFromTable(threadArgs->threadConn, selectIndexId);
        DbSleep(20);
    }
    return NULL;
}

// 触发新增rehash时候，尝试insert select并发读写页，执行时间较长 9s
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeRehashWhileInsertSelect_024)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(32) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 2);
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);

    // insert data
    const uint32_t insertTimes = 10000;
    ResizeInsertNormalData(conn, insertTimes);

    // 查询视图，看看数据是否满了此时的hwm
    uint32_t viewCapacity = 0;
    uint32_t viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    // 5个insert 5个select
    int insertThreadNum = CONCURRENCY_THREAD_NUM / 2;
    pthread_t insertThreads[insertThreadNum];
    InsertThreadArgsT insertArgs[insertThreadNum];
    // 多线程insert数据
    for (uint32_t i = 0; i < insertThreadNum; i++) {
        insertArgs[i].threadConn = conn;
        insertArgs[i].isRunning = true;
        insertArgs[i].minLimit = insertTimes;
        insertArgs[i].maxLimit = insertTimes * 4;
        ret = pthread_create(&insertThreads[i], NULL, InsertSqlText, &insertArgs[i]);
        EXPECT_EQ(0, ret);
    }

    int selectThreadNum = CONCURRENCY_THREAD_NUM / 2;
    SelectThreadArgsT selectArgs[selectThreadNum];
    pthread_t selectThreads[selectThreadNum] = {0};
    // 多线程insert数据
    for (uint32_t i = 0; i < selectThreadNum; i++) {
        selectArgs[i].threadConn = conn;
        selectArgs[i].isRunning = true;
        selectArgs[i].maxLimit = insertTimes * 4;
        ret = pthread_create(&selectThreads[i], NULL, SelectSqlText, &selectArgs[i]);
        EXPECT_EQ(0, ret);
    }

    // resize 5个chunk
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 5.1f);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    // 等待线程结束
    for (uint32_t i = 0; i < insertThreadNum; i++) {
        insertArgs[i].isRunning = false;
        EXPECT_EQ(0, pthread_join(insertThreads[i], NULL));
    }
    for (uint32_t i = 0; i < selectThreadNum; i++) {
        selectArgs[i].isRunning = false;
        EXPECT_EQ(0, pthread_join(selectThreads[i], NULL));
    }

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertNormalTableData(conn, 1, insertTimes);  // 插入一轮即可
    char *dropSql = (char *)"DROP TABLE resize_normal_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
// leave blank line at the file end

// 测试 配置 512，插满2 chunk数据，刷盘后重启，打开预取，然后触发回收为1 chunk
TEST_F(StBufferpoolResizeConfig, bufferpoolResizeLargerAndRecycleLruListCount_025)
{
    uint32_t chunkSize = SIZE_M(128) / SIZE_K(1);
    uint32_t pageSize = SIZE_K(64) / SIZE_K(1);
    uint32_t bufferPoolSize = (uint32_t)(chunkSize * 4);
    GmeConnT *conn = nullptr;
    Status ret = GMERR_OK;
    ret = ResizeOpenConfig(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // insert data
    const uint32_t maxColNum = 1024;
    const uint32_t insertTimes = 400;
    ResizeInsertBigData(conn, maxColNum, insertTimes);

    // 查询视图，看看数据是否满了此时的hwm
    uint32_t viewCapacity = 0;
    uint32_t viewHwm = 0;
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);
    // insertTimes需要满足至少插满一个chunk
    EXPECT_GT(viewHwm, 2 * chunkSize / pageSize);
    GmeFlushData(conn, 0);
    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = ResizeOpenConfigPreFetch(bufferPoolSize, chunkSize, pageSize, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(5);
    // resize 一个chunk
    uint32_t newBufferPoolSize = (uint32_t)(chunkSize * 1.2f);
    WaitResizeExit(conn, newBufferPoolSize, chunkSize, pageSize);
    QueryGetViewCapacityAndHwm(conn, viewCapacity, viewHwm);

    DbMemCtxT *connMemCtx = conn->conMemCtx;  // release disorienting for conn->embSession->session->memCtx
    DbInstanceT *dbInstance = (DbInstanceT *)DbGetInstanceByMemCtx(connMemCtx);
    SeInstanceHdT seInsPtr = (SeInstanceT *)SeGetInstance(DbGetInstanceId((DbInstanceHdT)dbInstance));
    EXPECT_TRUE(seInsPtr != nullptr);
    BufpoolMgrT *pageMgr = (BufpoolMgrT *)seInsPtr->pageMgr;
    EXPECT_NE(pageMgr, nullptr);
    // 只有一个list，此时count == capacity
    EXPECT_EQ(pageMgr->bufPool->list[LRU_LIST_NORMAL].count, viewCapacity);

    // 结束之后，尝试插入数据，然后drop表
    ResizeInsertTableData(conn, maxColNum, 1, insertTimes);  // 插入一轮即可
    char *dropSql = (char *)"DROP TABLE resize_employee;";
    ret = GmeSqlExecute(conn, dropSql, NULL, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = GmeClose(conn);
    EXPECT_EQ(ret, GMERR_OK);
}
