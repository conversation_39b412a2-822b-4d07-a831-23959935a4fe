/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_persistence_com.cc
 * Description:
 * Create: 2024-11-08
 */

#include "st_emb_sql_persistence_base.h"

static char g_sConfigPath[DB_MAX_PATH] = {0};
static char g_sShPath[DB_MAX_PATH] = {0};

char *GetConfigPath(char *basePath)
{
    char *gmdbHome = getenv("GMDB_HOME");
    memset_s(g_sConfigPath, DB_MAX_PATH, 0, DB_MAX_PATH);
    strcat_s(g_sConfigPath, DB_MAX_PATH, gmdbHome);
    strcat_s(g_sConfigPath, DB_MAX_PATH, basePath);
    return g_sConfigPath;
}

char *GetShPath(char *baseShPath)
{
    char *gmdbHome = getenv("GMDB_HOME");
    memset_s(g_sShPath, DB_MAX_PATH, 0, DB_MAX_PATH);
    strcat_s(g_sShPath, DB_MAX_PATH, gmdbHome);
    strcat_s(g_sShPath, DB_MAX_PATH, baseShPath);
    return g_sShPath;
}

void CreateAndInsertTable(const std::string &tableName, GmeConnT *conn, uint32_t count)
{
    int dim = 1024;
    const int n = count;
    std::string sqlCreateTable =
        "CREATE TABLE " + tableName + "(id int primary key, repr floatvector(" + std::to_string(dim) + "));";
    Status ret = GmeSqlExecute(conn, sqlCreateTable.c_str(), NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < n; i++) {
        std::string sqlInsert =
            "INSERT INTO " + tableName + " VALUES(" + std::to_string(i) + ", '" + GetRandVector(n, dim) + "');";
        ret = GmeSqlExecute(conn, const_cast<char *>(sqlInsert.c_str()), NULL, NULL, NULL);
        ASSERT_EQ(ret, GMERR_OK) << i << " t1 insert failed";
    }
}

Status CreateAndInsertFull(const std::string &tableName, GmeConnT *conn, uint32_t *count)
{
    int dim = 1024;
    std::string sqlCreateTable =
        "CREATE TABLE " + tableName + "(id int primary key, repr floatvector(" + std::to_string(dim) + "));";
    Status ret = GmeSqlExecute(conn, sqlCreateTable.c_str(), NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t totalCount = 0;
    while (true) {
        std::string sqlInsert = "INSERT INTO " + tableName + " VALUES(" + std::to_string(totalCount) + ", '" +
                                GetRandVector(totalCount, dim) + "');";
        ret = GmeSqlExecute(conn, const_cast<char *>(sqlInsert.c_str()), NULL, NULL, NULL);
        if (ret != GMERR_OK) {
            if (ret == GMERR_INSUFFICIENT_RESOURCES) {  // 文件满错误码
                *count = totalCount;
            } else {  // 其他错误均为非预期错
                std::cout << "insert failed, count = %d, ret = %d" << totalCount << (uint32_t)ret << endl;
            }
            return ret;
        }
        totalCount++;
    }

    return ret;
}

void CommonCfgChange(const char *cfgFilePath, const char *cfgShPath)
{
    CfgItemT cfgList[] = {{"maxTotalDynSize", "948576"}, {"dbFilesMaxCnt", "100"}, {"maxSysDynSize", "848576"},
        {"userSpaceSizeLimited", "0"},
        {"dbFileSize", "3145728"},  // 考虑测试场景最大64G，单个文件大小先调整为3G，后续可根据需求调整
        {"userSpaceSizeLimited", "0"}};
    std::string chmodCmd = "chmod +x " + std::string(cfgShPath);
    system(chmodCmd.c_str());

    for (uint32_t i = 0; i < sizeof(cfgList) / sizeof(CfgItemT); ++i) {
        std::string sedCommand = std::string(cfgShPath) + " " + cfgList[i].configItemName + " " +
                                 cfgList[i].configValue + " " + std::string(cfgFilePath);
        system(sedCommand.c_str());
    }
}

void CfgChange(CfgItemT *cfgList, uint32_t cfgListSize, const char *cfgShPath, const char *cfgFilePath)
{
    for (uint32_t i = 0; i < cfgListSize; ++i) {
        std::string sedCommand = std::string(cfgShPath) + " " + cfgList[i].configItemName + " " +
                                 cfgList[i].configValue + " " + std::string(cfgFilePath);
        system(sedCommand.c_str());
    }
}
