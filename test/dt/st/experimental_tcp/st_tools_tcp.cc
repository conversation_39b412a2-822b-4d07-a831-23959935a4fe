/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2025-3-6
 */

#include <sys/epoll.h>
#include <sys/prctl.h>
#include <thread>
#include <atomic>
#include <cstring>
#include <poll.h>
#include <cerrno>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "client_common_st.h"
#include "st_sock_adpt_base.h"
#include "st_tcp_adpt_base.h"
#include "st_persistence_common.h"
#include "gm_adpt.h"
#include "gms.h"
#include "gmc_test.h"

#define MAX_LINE_LENGTH 1024
static int32_t DbUsockOpenListenStub(char *unixDomain, GmsSockPipeT *lsrnFd)
{
    DB_POINTER(unixDomain);
    lsrnFd->ctx = (void *)(lsrnFd + 1);
    struct sockaddr_un *sockAddr = (struct sockaddr_un *)(void *)lsrnFd->ctx;
    // 按需创建父文件夹。因为会临时修改unixDomain，所以请保证它不是一个只读addr且没有多线程共用
    char *slash = strrchr(unixDomain, '/');
    if (slash != NULL) {
        *slash = '\0';
        Status res = DbMakeDirectory(unixDomain, S_IRWXU);
        *slash = '/';
        if (res != GMERR_OK) {
            DB_LOG_ERROR(res, "Make directory %s for usocket domain worthless.", unixDomain);
            return SOCKET_INTERNAL_ERROR;
        }
    }
    // 创建用于通信的套接字,通信域为UNIX通信域
    int32_t ret = DbSockSetLsnrStub(lsrnFd, AF_UNIX, SOCK_STREAM);
    if (ret != SOCKET_SUCCESS) {
        return ret;
    }
    // 设置服务器addr参数
    sockAddr->sun_family = AF_UNIX;
    uint32_t nameLen = (uint32_t)strlen(unixDomain);
    uint32_t sunPathLen = sizeof(sockAddr->sun_path);
    if (strncpy_s(sockAddr->sun_path, sunPathLen, unixDomain, nameLen) != EOK) {
        DbSockClosePipeStub(lsrnFd);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "str copy %s sockAddr len %" PRIu32 ", name len %" PRIu32 ".", unixDomain,
            sunPathLen, nameLen);
        return SOCKET_INTERNAL_ERROR;
    }

    (void)unlink(unixDomain);

    // 绑定套接字与服务器addr信息
    uint32_t sunAddrLen = (uint32_t)sizeof(sockAddr->sun_family) + nameLen;
    if (bind(lsrnFd->fd, (struct sockaddr *)(void *)(sockAddr), sunAddrLen) == -1) {
        DbSockClosePipeStub(lsrnFd);
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Bind worthless where usocket open listen");
        return SOCKET_INTERNAL_ERROR;
    }

    // 对套接字进行监听,判断是否有连接请求
    if (listen(lsrnFd->fd, SOMAXCONN) == -1) {
        DbSockClosePipeStub(lsrnFd);
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Listen worthless where usocket open listen");
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

#define CS_OSERR_CAN_IGNORE (errno == EINTR || errno == EWOULDBLOCK || errno == EAGAIN)

static int32_t DbUsockAcceptStub(
    const GmsSockPipeT *lsrnPipe, GmsSockPipeT *acceptPipe, int32_t timeoutMs, uint32_t bufSize, bool nonBlock)
{
    acceptPipe->ctx = (void *)(acceptPipe + 1);
    SockAcceptParaT para = {.timeoutMs = timeoutMs, .bufSize = bufSize, .nonBlock = nonBlock};
    SockAddrT addr = {
        .addr = (struct sockaddr *)(void *)acceptPipe->ctx, .addrLen = (uint32_t)sizeof(struct sockaddr_un)};
    int32_t ret = DbSockAcceptStub(lsrnPipe, acceptPipe, &addr, &para);
    if (ret != SOCKET_SUCCESS) {
        return ret;
    }
    int32_t option = 1;
    int32_t res = setsockopt(acceptPipe->fd, SOL_SOCKET, SO_KEEPALIVE, (void *)&option, sizeof(int32_t));
    if (res == -1) {
        DbSockClosePipeStub(acceptPipe);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

static int32_t DbUsockGetCredStub(const GmsSockPipeT *pipe, uint32_t *pid, uint32_t *uid, uint32_t *gid)
{
    printf("==== User defined DbUsockGetCredStub.\n");
    DB_POINTER3(pid, uid, gid);
    struct ucred cred;
    uint32_t len = sizeof(cred);
    int32_t ret = getsockopt(pipe->fd, SOL_SOCKET, SO_PEERCRED, &cred, &len);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Getsockopt SO_PEERCRED %" PRId32 " worthless, osno %" PRId32 " ret %" PRId32 "", pipe->fd, errno, ret);
        return SOCKET_INTERNAL_ERROR;
    }
    *pid = (uint32_t)cred.pid;
    *uid = (uint32_t)cred.uid;
    *gid = (uint32_t)cred.gid;
    return SOCKET_SUCCESS;
}

static int32_t DbUsockBindClientPath(DbSocket fd, const char *clientPath)
{
    struct sockaddr_un usAddr;
    usAddr.sun_family = AF_UNIX;

    int ret = strcpy_s(usAddr.sun_path, DOMAIN_NAME_MAX_LEN, clientPath);
    if (ret != EOK) {
        return SOCKET_INTERNAL_ERROR;
    }
    (void)unlink(clientPath);
    socklen_t len = (socklen_t)(sizeof(usAddr.sun_family) + strlen(clientPath));
    if (bind(fd, (struct sockaddr *)&usAddr, len) < 0) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Bind worthless where usocket bind client path");
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

#define DB_TV_USEC_START 800

static int32_t DbUsockConnect2Sever(int32_t connectFd, const struct sockaddr *srvAddr, socklen_t addrLen)
{
    DB_POINTER(srvAddr);
    int32_t ret = connect(connectFd, srvAddr, addrLen);
    int mistake = errno;
    if (ret == 0) {
        return SOCKET_SUCCESS;
    }

    if (mistake != EINPROGRESS && mistake != EAGAIN) {
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "osno %d where usocket connect to server", mistake);
        return SOCKET_INTERNAL_ERROR;
    }

    struct pollfd pollFd;
    pollFd.fd = connectFd;
    pollFd.events = POLLOUT;
    pollFd.revents = 0;
    if (poll(&pollFd, 1, DB_TV_USEC_START) > 0) {
        errno = 0;
        ret = connect(connectFd, srvAddr, addrLen);
        mistake = errno;
        if (errno == EISCONN || ret == EOK) {
            return SOCKET_SUCCESS;
        }
    }

    DB_LOG_ERROR(
        GMERR_CONNECTION_FAILURE, "Connection suffer a defeat osno %d when usocket connect to server", mistake);
    return SOCKET_INTERNAL_ERROR;
}

static int32_t DbUsockConnectStub(
    const char *unixDomain, GmsSockPipeT *pipe, const char *clientPath, uint32_t bufSize, bool nonBlock)
{
    ++g_gmdbConnectTimes;
    pipe->fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (pipe->fd < 0) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION,
            "Create socket connection worthless where usocket connection, os ret no: %" PRId32 ".", errno);
        return SOCKET_INTERNAL_ERROR;
    }

    int32_t ret = (clientPath != NULL) ? DbUsockBindClientPath(pipe->fd, clientPath) : SOCKET_SUCCESS;
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipeStub(pipe);
        return ret;
    }

    struct sockaddr_un srvAddr;
    srvAddr.sun_family = AF_UNIX;

    size_t nameLen = strlen(unixDomain);
    ret = strncpy_s(srvAddr.sun_path, sizeof(srvAddr.sun_path), unixDomain, nameLen);
    if (ret != EOK) {
        DbSockClosePipeStub(pipe);
        return SOCKET_INTERNAL_ERROR;
    }

    socklen_t addrLen = (socklen_t)(sizeof(srvAddr.sun_family) + nameLen);
    ret = DbUsockConnect2Sever(pipe->fd, (struct sockaddr *)(void *)&srvAddr, addrLen);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipeStub(pipe);
        return ret;
    }

    ret = DbSockSetNoBlockStub(pipe, nonBlock);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipeStub(pipe);
        DB_LOG_ERROR(ret, "set socket nonblock where usocket connect to server");
        return ret;
    }
    ret = DbSockSetBufferSizeStub(pipe->fd, bufSize, bufSize);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipeStub(pipe);
        return ret;
    }
    DB_LOG_DEBUG("Usocket connect success, sock: %d", pipe->fd);
    return SOCKET_SUCCESS;
}

static Status DbCheckLog(const char *logFilePath, const char *searchStr)
{
    if (strlen(logFilePath) == 0) {
        printf("Located file %s is empty\n", logFilePath);
        return GMERR_FILE_OPERATE_FAILED;
    }
    printf("Log file: %s\n", logFilePath);

    FILE *logFile = fopen(logFilePath, "r");
    if (!logFile) {
        perror("Failed to open log file");
        return GMERR_FILE_OPERATE_FAILED;
    }

    // 定位到文件末尾
    fseek(logFile, 0, SEEK_END);
    long pos = ftell(logFile);  // 获取文件大小
    char buffer[MAX_LINE_LENGTH];
    int lineCount = 0;

    // 从文件末尾开始向前读取
    while (pos >= 0) {
        fseek(logFile, pos, SEEK_SET);                          // 定位到当前位置
        if (fgetc(logFile) == '\n' || pos == 0) {               // 检测换行符或文件开头
            fseek(logFile, pos + (pos > 0 ? 1 : 0), SEEK_SET);  // 调整到行的开头
            if (fgets(buffer, sizeof(buffer), logFile)) {
                lineCount++;
                if (strstr(buffer, searchStr)) {
                    printf("Found target string: \"%s\" in line: %s\n", searchStr, buffer);
                    fclose(logFile);
                    return GMERR_OK;
                }
                if (lineCount >= MAX_LINE_LENGTH)
                    break;  // 只处理最后100行
            }
        }
        pos--;  // 逐字符向前移动
    }

    fclose(logFile);
    return GMERR_UNEXPECTED_NULL_VALUE;
}

static void RegAdaptFuncsProc(Status expectRet)
{
    GmAdptFuncsHandle handle = NULL;
    int32_t ret = GmCreateAdptFuncsHandle(&handle);
    ASSERT_EQ(ret, GMERR_OK);
    // USOCK
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_OPEN_LISTEN, (GmAdptFunc)DbUsockOpenListenStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_ACCEPT, (GmAdptFunc)DbUsockAcceptStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_CONNECT, (GmAdptFunc)DbUsockConnectStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_GET_CRED, (GmAdptFunc)DbUsockGetCredStub);
    // TCP
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_OPEN_LISTEN, (GmAdptFunc)DbTcpOpenListenStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_ACCEPT, (GmAdptFunc)DbTcpAcceptStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_CONNECT, (GmAdptFunc)DbTcpConnectStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_PEER_NAME, (GmAdptFunc)DbTcpGetPeerNameStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_CREDIT, (GmAdptFunc)DbTcpGetCreditStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_LOGIN_RULE, (GmAdptFunc)DbTcpLoginVerifyStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_IP_CONTROL, (GmAdptFunc)DbTcpIpControlStub);
    // SOCK
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_CLOSE, (GmAdptFunc)DbSockClosePipeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_NONBLOCK, (GmAdptFunc)DbSockSetNoBlockStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_RECV_TIMEOUT, (GmAdptFunc)DbBlockSockSetRecvTimeoutStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_SEND_TIMEOUT, (GmAdptFunc)DbBlockSockSetSendTimeoutStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_BOLCK_RECV, (GmAdptFunc)DbBlockSockRecvStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_BOLCK_SEND, (GmAdptFunc)DbBlockSockSendStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_NONBOLCK_RECV, (GmAdptFunc)DbNonBlockSockRecvStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_NONBOLCK_SEND, (GmAdptFunc)DbNonBlockSockSendStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_RECV_BUFF_SIZE, (GmAdptFunc)DbSockGetRecvBuffSizeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_SEND_BUFF_SIZE, (GmAdptFunc)DbSockGetSendBuffSizeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_REMAIN_RECV_BYTES, (GmAdptFunc)DbSockGetUnRecvBytesStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_REMAIN_SEND_BYTES, (GmAdptFunc)DbSockGetUnSendBytesStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_PEER_CLOSE, (GmAdptFunc)DbSockPeerClosedStub);
    ret |= GmRegAdaptFuncs(handle);
    GmDestroyAdptFuncsHandle(&handle);
    if (ret != expectRet) {
        exit(ST_EXIT_FAIL);
    }
}

static bool g_isStartEpoll;
static pthread_t g_epollThreadId;

static void InitEpollFd()
{
    if (!g_isStartEpoll) {
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
        printf("base call GmcStartEpoll done\n");
        g_isStartEpoll = true;
    }
}

static void UnInitEpollFd()
{
    if (g_isStartEpoll) {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        g_epollThreadId = -1;
        printf("base call GmcStopEpoll done\n");
        g_isStartEpoll = false;
    }
}

static pid_t g_gmserverPid = 0;

static void StartServer(char *cfgPath)
{
    // 子进程
    DbEnableRegAdaptFuncs();
    g_gmserverPid = fork();
    if (g_gmserverPid == 0) {
        SetClientServerSameProcess();
        RegAdaptFuncsProc(GMERR_OK);
        char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)cfgPath};
        GmServerMain(3, cmdString, true);
        exit(0);
    } else {
        UnSetClientServerSameProcess();
        RegAdaptFuncsProc(GMERR_OK);
        sleep(2);
        printf("start server success pid:%d\n", g_gmserverPid);
    }
}

extern "C" bool DbProcessInstanceIsExist(void);

static void StopServer(void)
{
    if (g_gmserverPid != 0) {
        printf("kill server pid %d\n", g_gmserverPid);
        kill(g_gmserverPid, SIGTERM);
        waitpid(g_gmserverPid, NULL, 0);
        g_gmserverPid = 0;
        system("rm -rf /run/verona/unix_emserver;ipcrm -a");
    }
    // 等待上一个DB退出
    while (!IsDbServerExit()) {
        DbSleep(1000);
    }
    system("kill -15 $(pidof gmserver)");
    system("ipcrm -a");
}

typedef Status (*StGmToolsTcpFunc)(int32_t argc, char *argv[]);
class StGmToolsTcp : public testing::Test {
public:
    static void Start()
    {
        StopServer();
        UnSetClientServerSameProcess();
        StartServer((char *)"gmserver_guangqi_tcp.ini");
        st_clt_init();
        StSwitchTcpLocator();
        InitEpollFd();
        StConnectWithCSMode();
    }

    static void End()
    {
        st_clt_uninit();
        StopServer();
    }

    void GmToolsTest(StGmToolsTcpFunc func, int32_t argc, char *argv[])
    {
        StGmToolsTcp::Start();
        Status ret = (Status)st_connect();
        CreateSubConnectionAndStmt(&connSub, &stmtSub, "subConnection");
        if (ret != GMERR_OK) {
            DestroyConnectionAndStmt(connSub, stmtSub);
            st_disconnect();
            StGmToolsTcp::End();
            exit(ST_EXIT_FAIL);
        }
        ret = func(argc, argv);
        DestroyConnectionAndStmt(connSub, stmtSub);
        st_disconnect();
        StGmToolsTcp::End();
        exit((ret == GMERR_OK) ? ST_EXIT_SUCCESS : ST_EXIT_FAIL);
    }

protected:
    GmcConnT *connSub;
    GmcStmtT *stmtSub;

    static void SetUpTestCase()
    {
        system("rm -rf /data/gmdb/* -rf");
        StopServer();
        UnSetClientServerSameProcess();
    }
    static void TearDownTestCase()
    {
        StopServer();
        UnSetClientServerSameProcess();
    }
    virtual void SetUp()
    {
        StSwitchTcpLocator();
    }
    virtual void TearDown()
    {
        StSwitchDefaultLocator();
    }
};

TEST_F(StGmToolsTcp, gmsysviewDRT_CONN_STAT)
{
    char *cmd[] = {(char *)"sysview", (char *)"-q", (char *)"V$DRT_CONN_STAT", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmsysviewDRT_CONN_SUBS_STAT)
{
    char *cmd[] = {
        (char *)"sysview", (char *)"-q", (char *)"V$DRT_CONN_SUBS_STAT", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmsysviewCLT_PROCESS_CONN)
{
    char *cmd[] = {(char *)"sysview", (char *)"-q", (char *)"V$CLT_PROCESS_CONN", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmsysviewCLT_PROCESS_FLOWCTRL_INFO_LABEL)
{
    char *cmd[] = {(char *)"sysview", (char *)"-q", (char *)"V$CLT_PROCESS_FLOWCTRL_INFO_LABEL", (char *)"-s",
        (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmsysviewCLT_PROCESS_INFO)
{
    char *cmd[] = {(char *)"sysview", (char *)"-q", (char *)"V$CLT_PROCESS_INFO", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmsysviewCLT_PROCESS_LABEL)
{
    char *cmd[] = {(char *)"sysview", (char *)"-q", (char *)"V$CLT_PROCESS_LABEL", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmsysviewCLT_PROCESS_TIME_CONSUMPTION)
{
    char *cmd[] = {
        (char *)"sysview", (char *)"-q", (char *)"V$CLT_PROCESS_TIME_CONSUMPTION", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsSysviewMain, 5, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmcmd)
{
    char *cmd[] = {
        (char *)"gmcmd", (char *)"-c", (char *)"drop", (char *)"database", (char *)"-s", (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsCmdMain, 6, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, gmlogSetClient)
{
    char *cmd[] = {(char *)"gmlog", (char *)"-p", (char *)"127.0.0.1", (char *)"-l", (char *)"4", (char *)"-s",
        (char *)serverLocator};
    ASSERT_EXIT(GmToolsTest(GmsLogMain, 7, cmd), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

/*
测试内容：1、DBA创建；2、白名单导入；3、授权：对象权限和系统权限；4、撤权：对象权限和系统权限；5、权限视图；6、赋对象权限后建表删表
*/
int32_t privilege_test_001(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 导入白名单
    char *cmd1[] = {(char *)"gmrule", (char *)"-c", (char *)"import_allowlist", (char *)"-f",
        (char *)"./nergc_files/user.gmuser", (char *)"-s", (char *)serverLocator, (char *)"-u", (char *)"root_001"};
    int32_t ret = GmsRuleMain(9, cmd1);
    EXPECT_EQ(ret, 0);

    const char *configJson = R"({"max_record_count":50000})";
    const char *labelJson1 =
        R"([{
            "type":"record",
            "name":"vertexLabel001",
            "fields":
                [
                    {"name":"id", "type":"int32", "nullable":false}
                ],
            "keys":
                [{
                    "node":"vertexLabel001",
                    "name":"pkIndex",
                    "fields":["id"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";
    const char *labelJson2 =
        R"([{
            "type":"record",
            "name":"vertexLabel002",
            "fields":
                [
                    {"name":"id", "type":"int32", "nullable":false}
                ],
            "keys":
                [{
                    "node":"vertexLabel002",
                    "name":"pkIndex",
                    "fields":["id"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";
    ret = GmcCreateVertexLabel(stmt, labelJson1, configJson);
    EXPECT_EQ(ret, 0);
    ret = GmcCreateVertexLabel(stmt, labelJson2, configJson);
    EXPECT_EQ(ret, 0);

    // 导入对象权限
    char *cmd2[] = {(char *)"gmrule", (char *)"-c", (char *)"import_policy", (char *)"-f",
        (char *)"./nergc_files/user_obj_priv.gmpolicy", (char *)"-s", (char *)serverLocator, (char *)"-u",
        (char *)"root_001"};
    ret = GmsRuleMain(9, cmd2);
    EXPECT_EQ(ret, 0);
    // 导入系统权限
    char *cmd3[] = {(char *)"gmrule", (char *)"-c", (char *)"import_policy", (char *)"-f",
        (char *)"./nergc_files/user_sys_priv.gmpolicy", (char *)"-s", (char *)serverLocator, (char *)"-u",
        (char *)"root_002"};
    ret = GmsRuleMain(9, cmd3);
    EXPECT_EQ(ret, 0);

    // 查视图
    char *cmd4[] = {(char *)"gmsysview", (char *)"-q", (char *)"V$PRIVILEGE_USER_STAT", (char *)"-s",
        (char *)serverLocator, (char *)"-u", (char *)"root_002"};
    ret = GmsSysviewMain(7, cmd4);
    EXPECT_EQ(ret, 0);
    // 查视图
    char *cmd5[] = {(char *)"gmsysview", (char *)"-q", (char *)"V$PRIVILEGE_ROLE_STAT", (char *)"-s",
        (char *)serverLocator, (char *)"-u", (char *)"root_002"};
    ret = GmsSysviewMain(7, cmd5);
    EXPECT_EQ(ret, 0);

    // 撤销对象权限
    char *cmd6[] = {(char *)"gmrule", (char *)"-c", (char *)"revoke_policy", (char *)"-f",
        (char *)"./nergc_files/user_obj_priv.gmpolicy", (char *)"-s", (char *)serverLocator, (char *)"-u",
        (char *)"root_001"};
    ret = GmsRuleMain(9, cmd6);
    EXPECT_EQ(ret, 0);
    // 撤销系统权限
    char *cmd7[] = {(char *)"gmrule", (char *)"-c", (char *)"revoke_policy", (char *)"-f",
        (char *)"./nergc_files/user_sys_priv.gmpolicy", (char *)"-s", (char *)serverLocator, (char *)"-u",
        (char *)"root_002"};
    ret = GmsRuleMain(9, cmd7);
    EXPECT_EQ(ret, 0);

    ret = GmsSysviewMain(7, cmd4);
    EXPECT_EQ(ret, 0);
    ret = GmsSysviewMain(7, cmd5);
    EXPECT_EQ(ret, 0);
    // 删表
    GmcDropVertexLabel(stmt, "vertexLabel001");
    GmcDropVertexLabel(stmt, "vertexLabel002");
    DestroyConnectionAndStmt(conn, stmt);

    return ret;
}

TEST_F(StGmToolsTcp, privilege_test_001)
{
    ASSERT_EXIT(GmToolsTest(privilege_test_001, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_with_status_merge(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson = R"({"max_record_count":50000, "status_merge_sub":0})";
    const char *configJson1 = R"({"max_record_count":50000, "status_merge_sub":1})";
    const char *configJson2 = R"({"max_record_count":50000, "status_merge_sub":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"int32", "nullable":false}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(ret, 0);
    ret = GmcCreateVertexLabel(stmt, labelJson, configJson1);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcCreateVertexLabel(stmt, labelJson, configJson2);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_status_merge)
{
    ASSERT_EXIT(GmToolsTest(create_with_status_merge, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_with_isFastReadUncommitted(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson1 = R"({"max_record_count":50000, "isFastReadUncommitted":true})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":1024},
                    {"name":"F2", "type":"fixed", "size":1024},
                    {"name":"F3", "type":"fixed", "size":1024}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson1);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_config_isFastReadUncommitted)
{
    ASSERT_EXIT(GmToolsTest(create_with_isFastReadUncommitted, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_data_sync_label(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson2 = R"({"max_record_count":50000, "data_sync_label":true})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":1024},
                    {"name":"F2", "type":"fixed", "size":1024},
                    {"name":"F3", "type":"fixed", "size":1024}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson2);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_config_data_sync_label)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_data_sync_label, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_direct_write(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson3 = R"({"max_record_count":50000, "direct_write":true})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":1024},
                    {"name":"F2", "type":"fixed", "size":1024},
                    {"name":"F3", "type":"fixed", "size":1024}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson3);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_config_direct_write)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_direct_write, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_with_auto_incre(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson7 = R"({"max_record_count":50000, "auto_increment":1})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":1024},
                    {"name":"F2", "type":"fixed", "size":1024},
                    {"name":"F3", "type":"uint32", "auto_increment":true}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson7);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_config_auto_increment)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_with_auto_incre, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_with_partition(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson = R"({"max_record_count":50000})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"uint32", "nullable":false},
                    {"name":"c1", "type":"partition", "nullable":false}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_partition)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_with_partition, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_auto_increment(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson = R"({"max_record_count":50000, "auto_increment":0})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"uint32", "nullable":false, "auto_increment":true}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_auto_increment)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_auto_increment, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_with_rsmNsp(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson = R"({"max_record_count":50000, "rsm_tablespace_name":"test"})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"uint32", "nullable":false}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_rsmNsp)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_with_rsmNsp, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

const char *vertexLabelTest4Schema = (char *)R"([{
    "version": "2.0", "type": "record", "name": "vertexLabelTest1",
    "fields": [
    { "name": "c0", "type": "uint32" },
    { "name": "c1", "type": "string", "size":20, "nullable":true },
    { "name": "c2", "type": "record",
        "vector": true, "size": 1024,
        "fields": [
        { "name": "b1", "type": "uint32", "nullable":true },
        { "name": "b2", "type": "uint32", "nullable":true }
        ]
    },
    { "name": "c3", "type": "bytes", "size": 128, "nullable":true},
    { "name": "c4", "type": "record",
        "fixed_array": true, "size": 512,
        "fields": [
        { "name": "t1", "type": "uint32", "nullable":true},
        { "name": "t2", "type": "string", "size":8, "nullable":true}
        ]
    },
    { "name": "c5", "type": "record",
        "vector": true, "size": 1024,
        "fields": [
        { "name": "b1", "type": "uint32", "nullable":true },
        { "name": "b2", "type": "uint32", "nullable":true }
        ]
    }
    ],
    "keys": [
    { "name": "table_pk", "index": { "type": "primary" },
        "node": "vertexLabelTest1",
        "fields": [ "c0" ],
        "constraints": { "unique": true }
    },
    { "name": "table_secondary", "index": { "type": "localhash" },
        "node": "vertexLabelTest1",
        "fields": [ "c1" ],
        "constraints": { "unique": false }
    },
    { "name": "member_key1", "index": { "type": "none" },
        "node": "c2",
        "fields": [ "b2" ],
        "constraints": { "unique": false }
    },
    { "name": "member_key_c5_1", "index": { "type": "none" },
        "node": "c5",
        "fields": [ "b1" ],
        "constraints": { "unique": false }
    },
    { "name": "member_key", "index": { "type": "none" },
        "node": "c2",
        "fields": [ "b1" ],
        "constraints": { "unique": false }
    },
    { "name": "member_key_c5_2", "index": { "type": "none" },
        "node": "c5",
        "fields": [ "b2" ],
        "constraints": { "unique": false }
    }
    ]
}])";

const char *vertexLabelTest4Schema1 = (char *)R"([{
    "type":"record",
    "name":"OP_T0",
    "fields":[
        {"name":"F0", "type":"int64", "nullable":false},
        {"name":"F1", "type":"uint64", "nullable":true},
        {"name":"F2", "type":"int32", "nullable":true},
        {"name":"F3", "type":"uint32", "nullable":true},
        {"name":"F4", "type":"int16", "nullable":true},
        {"name":"F5", "type":"uint16", "nullable":true},
        {"name":"F6", "type":"int8", "nullable":true},
        {"name":"F7", "type":"uint8", "nullable":true},
        {"name":"F8", "type":"boolean", "nullable":true},
        {"name":"F9", "type":"float", "nullable":true},
        {"name":"F10", "type":"double", "nullable":true},
        {"name":"F11", "type":"time", "nullable":true},
        {"name":"F12", "type":"char", "nullable":true},
	    {"name":"F13", "type":"uchar", "nullable":true},
        {"name":"F14", "type":"string", "size":100, "nullable":true},
        {"name":"F15", "type":"bytes", "size":7, "nullable":true},
        {"name":"F16", "type":"fixed", "size":7, "nullable":true},
        {"name":"T1", "type": "record",
	    "fields": [
	          {"name":"P0", "type":"int64", "nullable":true},
              {"name":"P1", "type":"uint64", "nullable":true},
              {"name":"P2", "type":"int32", "nullable":true},
              {"name":"P3", "type":"uint32", "nullable":true},
              {"name":"P4", "type":"int16", "nullable":true},
              {"name":"P5", "type":"uint16", "nullable":true},
              {"name":"P6", "type":"int8", "nullable":true},
              {"name":"P7", "type":"uint8", "nullable":true},
              {"name":"P8", "type":"boolean", "nullable":true},
              {"name":"P9", "type":"float", "nullable":true},
              {"name":"P10", "type":"double", "nullable":true},
              {"name":"P11", "type":"time", "nullable":true},
              {"name":"P12", "type":"char", "nullable":true},
	          {"name":"P13", "type":"uchar", "nullable":true},
              {"name":"P14", "type":"string", "size":100, "nullable":true},
              {"name":"P15", "type":"bytes", "size":7, "nullable":true},
              {"name":"P16", "type":"fixed", "size":7, "nullable":true},
	  		  {"name":"T2", "type":"record", "fixed_array": true, "size": 3,
	  		  "fields": [
	  		  	     {"name":"A0", "type":"int64", "nullable":true},
                     {"name":"A1", "type":"uint64", "nullable":true},
                     {"name":"A2", "type":"int32", "nullable":true},
                     {"name":"A3", "type":"uint32", "nullable":true},
                     {"name":"A4", "type":"int16", "nullable":true},
                     {"name":"A5", "type":"uint16", "nullable":true},
                     {"name":"A6", "type":"int8", "nullable":true},
                     {"name":"A7", "type":"uint8", "nullable":true},
                     {"name":"A8", "type":"boolean", "nullable":true},
                     {"name":"A9", "type":"float", "nullable":true},
                     {"name":"A10", "type":"double", "nullable":true},
                     {"name":"A11", "type":"time", "nullable":true},
                     {"name":"A12", "type":"char", "nullable":true},
	                 {"name":"A13", "type":"uchar", "nullable":true},
                     {"name":"A14", "type":"string", "size":100, "nullable":true},
                     {"name":"A15", "type":"bytes", "size":7, "nullable":true},
                     {"name":"A16", "type":"fixed", "size":7, "nullable":true}
	  		  	]
	  		  }]
	    }
    ],
       
    "keys":[
       {
            "node":"OP_T0",
            "name":"OP_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        },
       {
           "node": "OP_T0",
           "name": "localhash_unique_key",
           "index": { "type": "localhash"},
           "fields": [ "F1" ],
	       "constraints": { "unique": true }
        },
        {
            "node" : "OP_T0",
            "name" : "localhash_key",
            "fields" : [ "F3" ],
            "index" : {"type":"localhash"},
            "constraints" : { "unique" : false }
        }
    ]
}
])";

int32_t create_yang_vertexLabel_with_none(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson = R"({"max_record_count":50000})";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, vertexLabelTest4Schema, configJson);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

int32_t create_yang_vertexLabel_with_none1(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson = R"({"max_record_count":50000})";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, vertexLabelTest4Schema1, configJson);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_none)
{
    ASSERT_EXIT(GmToolsTest(create_yang_vertexLabel_with_none, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_none1)
{
    ASSERT_EXIT(GmToolsTest(create_yang_vertexLabel_with_none1, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_with_filter(int argc, char **argv)
{
    const char *tabelConfig = R"({"max_record_count":100, "isFastReadUncommitted":false})";
    const char *tableJson =
        R"([
            {
                "type":"record",
                "name":"Table49",
                "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":true},
                    {"name":"F2", "type":"uint32", "nullable":true}
                ],
                "keys":
                [
                    {
                        "name": "F0_PK", "index": { "type": "primary" },
                        "fields": [ "F0"],
                        "constraints": { "unique": true }
                    },
                    {
                        "name": "F2_LOCAL", "index": { "type": "hashcluster" },
                        "fields": [ "F2"],
                        "constraints": { "unique": true },
                        "filter":
                        {
                        "operator_type": "and",
                        "conditions":
                            [
                            {"property": "F2", "compare_type": "unequal", "value": 1}
                            ]
                        }
                    }
                ]
            }
        ])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, tableJson, tabelConfig);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_filter)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_with_filter, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t create_vertexLabel_auto_incre1(int argc, char **argv)
{
    (void)argc;
    (void)argv;
    const char *configJson7 = R"({"max_record_count":50000, "auto_increment":1})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"testOrder",
            "fields":
                [
                    {"name":"c", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":1024},
                    {"name":"F2", "type":"fixed", "size":1024},
                    {"name":"F3", "type":"uint32", "auto_increment":true}
                ],
            "keys":
                [{
                    "node":"testOrder",
                    "name":"pkIndex",
                    "fields":["c"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson7);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, create_yang_vertexLabel_with_config_auto_increment1)
{
    ASSERT_EXIT(GmToolsTest(create_vertexLabel_auto_incre1, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t beginCheck(int argc, char **argv)
{
    (void)argc;
    (void)argv;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcBeginCheck(stmt, "Test", 0);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, beginCheck)
{
    ASSERT_EXIT(GmToolsTest(beginCheck, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

int32_t endCheck(int argc, char **argv)
{
    (void)argc;
    (void)argv;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcEndCheck(stmt, "Test", 0, false);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, endCheck)
{
    ASSERT_EXIT(GmToolsTest(endCheck, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

void createDropCallback(void *userData, int32_t status, const char *errMsg)
{
    return;
}

int32_t createKv(int argc, char **argv)
{
    (void)argc;
    (void)argv;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char kvLabel_name[] = "KVLabel";
    const char *kVconfigJson = R"({"max_record_count":100000})";
    char data[10] = {0};
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcKvCreateTable(stmt, kvLabel_name, kVconfigJson);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcKvCreateTableAsync(stmt, kvLabel_name, kVconfigJson, createDropCallback, &data);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcKvDropTable(stmt, kvLabel_name);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcKvDropTableAsync(stmt, kvLabel_name, createDropCallback, &data);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
    return GMERR_OK;
}

TEST_F(StGmToolsTcp, createKv)
{
    ASSERT_EXIT(GmToolsTest(createKv, 0, NULL), testing::ExitedWithCode(ST_EXIT_SUCCESS), "");
}

TEST_F(StGmToolsTcp, SubConnCb)
{
    StGmToolsTcp::Start();
    ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, "XXuser", "subConn", &connSub));
    EXPECT_NE(nullptr, connSub);
    StopServer();
    StGmToolsTcp::End();
}

TEST_F(StGmToolsTcp, AsyncConnCb)
{
    StGmToolsTcp::Start();
    ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_ASYNC, serverLocator, "XXuser", "asyncConn", &connSub));
    EXPECT_NE(nullptr, connSub);
    StopServer();
    StGmToolsTcp::End();
}
