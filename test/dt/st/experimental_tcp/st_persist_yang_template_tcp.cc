/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: template of OMU edit-config
 * Author: guo<PERSON>wu
 * Create: 2024-03-23
 */
#include <dirent.h>
#include <vector>
#include "storage_st_common.h"
#include "st_persistence_common.h"
#include "st_common.h"
#include "yang/yang_common_st.h"
#include "yang/yang_testframe.h"
#include "yang/yang_template.h"
#include "gmc_yang_types.h"
#include "gmc_test.h"
#include "InitClt.h"

static pid_t g_gmserverPid = 0;

extern "C" bool DbProcessInstanceIsExist(void);

static void StartServer(char *cfgPath)
{
    // 子进程
    g_gmserverPid = fork();
    if (g_gmserverPid == 0) {
        SetClientServerSameProcess();
        StPersistenceGmsRegAdaptFuncs();
        char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)cfgPath};
        GmServerMain(3, cmdString, true);
        exit(0);
    } else {
        UnSetClientServerSameProcess();
        sleep(2);
        printf("start server success pid:%d\n", g_gmserverPid);
    }
}

static void StopServer(void)
{
    ShutDownDbServer();
    if (g_gmserverPid != 0) {
        printf("kill server pid %d\n", g_gmserverPid);
        kill(g_gmserverPid, SIGTERM);
        waitpid(g_gmserverPid, NULL, 0);
        g_gmserverPid = 0;
        system("rm -rf /run/verona/unix_emserver;ipcrm -a");
    }
    // 等待上一个DB退出
    while (!IsDbServerExit()) {
        DbSleep(1000);
    }
    system("kill -15 $(pidof gmserver)");
    system("ipcrm -a");
}

class StPersistTcpYangTemplate : public StYang {
public:
    StPersistTcpYangTemplate()
    {}
    virtual ~StPersistTcpYangTemplate()
    {}
    static void SetUpTestCase()
    {
        StopServer();
        SetClientServerSameProcess();
        StPersistenceGmsRegAdaptFuncs();
    }
    static void TearDownTestCase()
    {
        UnSetClientServerSameProcess();
        StopServer();
    }
    virtual void SetUp()
    {
        cleanPresisit = true;
    }
    virtual void TearDown()
    {
        StopServer();
    }
    virtual void Start()
    {
        system("kill -15 $(pidof gmserver)");
        system("ipcrm -a");
        // 等待上一个DB退出
        while (!IsDbServerExit()) {
            DbSleep(1000);
        }
        if (cleanPresisit) {
            system("rm -rf /data/gmdb;mkdir -p /data/gmdb");
        }

        StartDbServer((char *)"./gmserver_guangqi.ini", "usocket:/run/verona/unix_emserver", cleanPresisit);
        DbSleep(1000);
        st_clt_init();
        StYang::InitEpollFd();
        // 设置CS模式
        StConnectWithCSMode();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxForCommon =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        dyAlgoCtxForCommonOld = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxForCommon);
        ASSERT_NO_FATAL_FAILURE(CreateConn(&conn, GMC_CONN_TYPE_ASYNC, "subConn", false, 1024, 300000));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmt));
        ASSERT_NO_FATAL_FAILURE(CreateSyncConnectionAndStmt(&syncConn, &syncStmt));
    }

    virtual void End()
    {
        ASSERT_NO_FATAL_FAILURE(DestroyConnAndStmt(conn, stmt));
        stmt = NULL;
        conn = NULL;
        ASSERT_NO_FATAL_FAILURE(DestroyConnectionAndStmt(syncConn, syncStmt));
        syncConn = NULL;
        syncStmt = NULL;
        dyAlgoCtxForCommonOld == NULL ? DbSetCurrMemCtxToNull() : (void)DbMemCtxSwitchTo(dyAlgoCtxForCommonOld);
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxForCommon);
        dyAlgoCtxForCommon = NULL;
        st_disconnect();
        st_clt_uninit();
    }

    virtual void StartByTcp()
    {
        serverLocator = "tcp:host=127.0.0.1,port=8000";
        system("kill -15 $(pidof gmserver)");
        system("ipcrm -a");
        // 等待上一个DB退出
        while (!IsDbServerExit()) {
            DbSleep(1000);
        }
        if (cleanPresisit) {
            system("rm -rf /data/gmdb;mkdir -p /data/gmdb");
        }
        UnSetClientServerSameProcess();
        StartServer((char *)"gmserver_guangqi_tcp.ini");
        DbSleep(1000);

        st_clt_init();
        StSwitchTcpLocator();
        StYang::InitEpollFd();
        // 设置CS模式
        StConnectWithCSMode();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxForCommon =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        dyAlgoCtxForCommonOld = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxForCommon);
        ASSERT_NO_FATAL_FAILURE(StYang::CreateAsyncConnAndStmt(&conn, &stmt));
        ASSERT_NO_FATAL_FAILURE(CreateCSModeSyncConnectionAndStmt(&syncConn, &syncStmt));
    }

    static void EndByTcp()
    {
        DestroyConnectionAndStmt(StPersistTcpYangTemplate::syncConn, StPersistTcpYangTemplate::syncStmt);
        StPersistTcpYangTemplate::syncConn = NULL;
        StPersistTcpYangTemplate::syncStmt = NULL;
        st_clt_uninit();
        StopServer();
    }

public:
    static GmcConnT *syncConn;
    static GmcStmtT *syncStmt;
    static bool cleanPresisit;
};

GmcConnT *StPersistTcpYangTemplate::syncConn = nullptr;
GmcStmtT *StPersistTcpYangTemplate::syncStmt = nullptr;
bool StPersistTcpYangTemplate::cleanPresisit = true;

static void HandleOneTemplateTestCase(GmcConnT *conn, GmcStmtT *stmt, const char *dirPath, const char *rootName)
{
    // 建表
    if (StPersistTcpYangTemplate::cleanPresisit) {
        ASSERT_NO_FATAL_FAILURE(LltCreateNamespaceAsync(stmt, "running"));

        ASSERT_NO_FATAL_FAILURE(TemplateCreateLabelsNormal(stmt, dirPath));
        // 共进程模式下建表模型校验后触发落盘
        EXPECT_EQ(GMERR_OK, GmcFlushData(StPersistTcpYangTemplate::syncStmt, NULL, false));
        ASSERT_NO_FATAL_FAILURE(TemplatePrefetchLabels(stmt, dirPath));
    }

    // 执行事务
    ASSERT_NO_FATAL_FAILURE(TemplateExecuteTrans(conn, stmt, dirPath, rootName));

    if (StPersistTcpYangTemplate::cleanPresisit) {
        // 清空 namespace
        ASSERT_NO_FATAL_FAILURE(TemplateClearNamespaceNormal(stmt));
        ASSERT_NO_FATAL_FAILURE(LltDropNamespaceAsync(stmt, "running"));
    }
}

static void HandleOneTemplateTestCaseForSub(GmcConnT *conn, GmcStmtT *stmt, const char *dirPath, const char *rootName)
{
    // 建表
    if (StPersistTcpYangTemplate::cleanPresisit) {
        ASSERT_NO_FATAL_FAILURE(LltCreateNamespaceAsync(stmt, "running"));

        ASSERT_NO_FATAL_FAILURE(TemplateCreateLabelsNormal(stmt, dirPath));
        // 共进程模式下建表模型校验后触发落盘
        EXPECT_EQ(GMERR_OK, GmcFlushData(StPersistTcpYangTemplate::syncStmt, NULL, false));
    }
    ASSERT_NO_FATAL_FAILURE(TemplateCreateSubsNormal("running", dirPath));

    ASSERT_NO_FATAL_FAILURE(TemplatePrefetchLabels(stmt, dirPath));

    // 执行事务
    ASSERT_NO_FATAL_FAILURE(TemplateExecuteTrans(conn, stmt, dirPath, rootName));

    ASSERT_NO_FATAL_FAILURE(TemplateDestroySubsNormal("running", dirPath));
    if (StPersistTcpYangTemplate::cleanPresisit) {
        // 清空 namespace
        ASSERT_NO_FATAL_FAILURE(TemplateClearNamespaceNormal(stmt));
        ASSERT_NO_FATAL_FAILURE(LltDropNamespaceAsync(stmt, "running"));
    }
}

TEST_F(StPersistTcpYangTemplate, DDLNormalWithTcpTest1)
{
    StPersistTcpYangTemplate::StartByTcp();
    static const char *cfgJson = R"({"max_record_count":1000, "auto_increment": 1, "yang_model": 1})";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ASSERT_NO_FATAL_FAILURE(StYang::CreateAsyncConnAndStmt(&conn, &stmt));

    const char *vertexLabelJson = "./yang_st_data/normal/vertex_test.json";
    const char *edgeLabelJson = "./yang_st_data/normal/edge_test.json";
    // 2.建点表和边表
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexLabelJson, edgeLabelJson, cfgJson));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // 3.用视图查询并将当前表模型结构写入文件，用于重启后校验
    int32_t ret = system("rm -rf model_result_expect.txt;rm -rf model_result_recovery.txt");
    ASSERT_EQ(GtParseSystemStatus(ret), 0);
    ret = system(
        "gmsysview subtree -filterMode MODEL -ns public -s tcp:host=127.0.0.1,port=8000 >> model_result_expect.txt");
    ASSERT_EQ(GtParseSystemStatus(ret), 0);
    ASSERT_NO_FATAL_FAILURE(StYang::DestroyConnAndStmt(conn, stmt));

    // 4.触发落盘
    EXPECT_EQ(GMERR_OK, GmcFlushData(StPersistTcpYangTemplate::syncStmt, NULL, false));

    // 5.重启gmserver
    StPersistTcpYangTemplate::EndByTcp();
    DestroyConnectionAndStmt(StPersistTcpYangTemplate::syncConn, StPersistTcpYangTemplate::syncStmt);
    st_disconnect();
    st_clt_uninit();
    StPersistTcpYangTemplate::cleanPresisit = false;
    StPersistTcpYangTemplate::StartByTcp();
    st_clt_init();
    StSwitchTcpLocator();
    StConnectWithCSMode();

    // 6.校验恢复后表是否和落盘后模型数据保持一致
    ret = system(
        "gmsysview subtree -filterMode MODEL -ns public -s tcp:host=127.0.0.1,port=8000 >>model_result_recovery.txt");
    ASSERT_EQ(GtParseSystemStatus(ret), 0);
    ASSERT_NO_FATAL_FAILURE(StYang::CreateAsyncConnAndStmt(&conn, &stmt));
    GmcStmtT *interfacesStmt = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &interfacesStmt));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));
    GmcNodeT *rootNode = NULL;
    GmcNodeT *interfaces = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "ietf-interfaces:interfaces", GMC_OPERATION_MERGE, &interfaces));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(interfacesStmt, "if:interface.1", GMC_OPERATION_MERGE));

    GmcNodeT *interfaces1 = NULL;
    GmcPropValueT propValue = {0};
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "11", strlen("11"));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, stmt, interfacesStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(interfacesStmt, 1, GMC_DATATYPE_STRING, "11", strlen("11")));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(interfacesStmt, &interfaces1));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(interfaces1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, interfacesStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(interfacesStmt));

    // 6.查询数据
    ret = system("gmsysview subtree -rn root -ns public -s tcp:host=127.0.0.1,port=8000 >> subtree_result.txt");
    ASSERT_EQ(GtParseSystemStatus(ret), 0);

    ASSERT_NO_FATAL_FAILURE(StYang::DestroyConnAndStmt(conn, stmt));
    StPersistTcpYangTemplate::EndByTcp();
}

// 测试场景: Yang订阅推送，多个订阅连接订阅多棵yang树
TEST_F(StPersistTcpYangTemplate, EditConfigSubPubMultiSubConnByTcp)
{
    StartByTcp();
    system("gmadmin -cfgName yangTraceLog -cfgVal 127 -s tcp:host=127.0.0.1,port=8000");
    const char *testDir = "./yangpush/multiconn";
    ASSERT_NO_FATAL_FAILURE(HandleValidateRoutine(conn, stmt, testDir, HandleOneTemplateTestCaseForSub));
    system("gmadmin -cfgName yangTraceLog -cfgVal 0 -s tcp:host=127.0.0.1,port=8000");
    End();
}

// 测试场景: Yang订阅推送，单个订阅连接订阅多棵yang树
TEST_F(StPersistTcpYangTemplate, EditConfigSubPubMultiTreeByTcp)
{
    StartByTcp();
    system("gmadmin -cfgName yangTraceLog -cfgVal 127 -s tcp:host=127.0.0.1,port=8000");
    const char *testDir = "./yangpush/multitree";
    ASSERT_NO_FATAL_FAILURE(HandleValidateRoutine(conn, stmt, testDir, HandleOneTemplateTestCaseForSub));
    system("gmadmin -cfgName yangTraceLog -cfgVal 0 -s tcp:host=127.0.0.1,port=8000");
    End();
}
