/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#include <dirent.h>
#include "adpt_file.h"
#include "adpt_process_lock.h"
#include "st_persistence_common.h"

static const char DB_CTRL_FILE_NAME[] = "dbCtrlFile";
static const char DB_SYSTEM_SPACE_NAME[] = "dbSystemSpace";
static const char DB_USER_SPACE_NAME[] = "dbUserSpace";
static const char DB_UNDO_SPACE_NAME[] = "dbUndoSpace";

const char *g_cfgOfIncrePersist = "gmserver_incre_persist.ini";
const char *g_shortString = "abc123";
const char *g_midString = "gmdbv5xshjcbhcsjhcjascvsdvsd7uyj.,..vdzxvbtb--=-=hbstg1224540cdjo csajnc4536sc4sz3445r32r";
const char *g_longString = "abc123dddddddddddddddddddddddddddddeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeffffffffffffff"
                           "ffffffffgggggggggggggggggggggggghhhhhhhhhhhhhhhhhhhhhhhhhiiiiiiiiiiiiiiiiiiiiijjjjjjjjj";
// 内存摸底，字符串长度为50
const char *g_memoryString = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";

const char *g_labelName = "ip4forward";
const char *g_configJson = R"({"max_record_count":9999999})";
const char *g_liteTrxConfigJson = R"({"isFastReadUncommitted":true})";
const char *g_labelJson =
    R"([{
    "version":"2.0",
    "max_record_count" : 9999999,
    "config": {
        "check_validity": true
    },
    "type":"record",
    "name":"ip4forward",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"int16" },
        { "name":"F4", "type":"string", "size":512, "nullable":true },
        { "name":"F5", "type":"string", "size":512, "nullable":true },
        { "name":"F6", "type":"string", "size":512, "nullable":true }
    ],
    "keys":[
        {
            "node":"ip4forward",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        }
    ]
}])";

const char *g_varVlJsonFormat =
    R"([{
    "version":"2.0",
    "max_record_count" : %u,
    "config": {
        "check_validity": true
    },
    "type":"record",
    "name":"%s",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"int16" },
        { "name":"F4", "type":"string", "nullable":true },
        { "name":"F5", "type":"string", "nullable":true },
        { "name":"F6", "type":"string", "nullable":true },
        { "name":"BigField", "type":"string", "nullable":true  }
    ],
    "keys":[
        {
            "node":"%s",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        }
    ]
}])";

const char *g_varMemory10FieldsFormat =
    R"([{
    "version":"2.0",
    "max_record_count" : 9999999,
    "config": {
        "check_validity": true
    },
    "type":"record",
    "name":"%s",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"int16" },
        { "name":"F4", "type":"string", "size":512, "nullable":true },
        { "name":"F5", "type":"string", "size":512, "nullable":true },
        { "name":"F6", "type":"string", "size":512, "nullable":true },
        { "name":"F7", "type":"string", "size":512, "nullable":true },
        { "name":"F8", "type":"string", "size":512, "nullable":true },
        { "name":"F9", "type":"string", "size":512, "nullable":true },
        { "name":"F10", "type":"string", "size":512, "nullable":true }
    ],
    "keys":[
        {
            "node":"%s",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        }
    ]
}])";

const char *g_fixLabelJson =
    R"([{
    "version":"2.0",
    "max_record_count" : %u,
    "config": {
        "check_validity": true
    },
    "type":"record",
    "name":"%s",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"int16" }
    ],
    "keys":[
        {
            "node":"%s",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        }
    ]
}])";

const char *g_fixedVlJsonFormat =
    R"([{
    "version":"2.0",
    "max_record_count" : 9999999,
    "config": {
        "check_validity": true
    },
    "type":"record",
    "name":"%s",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"int16" }
    ],
    "keys":[
        {
            "node":"%s",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        }
    ]
}])";
const char *g_normalVertexLabelJson = R"(
[{
    "comment":"典型的vertex label, 覆盖常见字段和索引, 适用于通用测试场景",
    "version":"2.0",
    "type":"record",
    "name":"vl_simple",
    "fields":
        [
            {"name":"A0", "type":"int32", "nullable":false},
            {"name":"A1", "type":"int64", "nullable":false},
            {"name":"A2", "type":"uint32", "nullable":false},
            {"name":"A3", "type":"uint64", "nullable":false},
            {"name":"A4", "type":"float", "nullable":true},
            {"name":"A5", "type":"double", "nullable":true}
        ],
    "keys":
        [
            {
                "node":"vl_simple",
                "name":"PrimaryKey",
                "index":{"type":"primary"},
                "fields":["A0"],
                "constraints":{"unique":true},
                "comment": "主键索引"
            }
        ]
}])";
const char *g_normalLabelConfig = R"({"max_record_num":1000000, "auto_increment":10})";
const char *g_normalLabelName = "vl_simple";
const int g_bigStrLen = 63 * 1024;
char g_longBigString[g_bigStrLen] = {0};

bool IsDbServerExit()
{
    int count = 0;
    int32_t cmdRet = 0;
    while (true) {
        DbSleep(500);
        cout << "......shutdown wait times:" << count << endl;
        bool isExist = DbProcessInstanceIsExist();

        cmdRet = system("pidof gmserver > /dev/null");  // 找不到gmserver进程返回值非0
        if (!isExist && cmdRet != 0) {
            cout << "gmserver exits" << endl;
            return true;
        }
        count++;
        if (count > 30) {
            cout << "shutdown more than 15 seconds, wait times:" << count << endl;
            return false;
        }
    }
}

void SetInitBigStr()
{
    memset_s(g_longBigString, g_bigStrLen, 'A', g_bigStrLen - 1);
    g_longBigString[g_bigStrLen - 1] = '\0';
}

void SetUpdateBigStr()
{
    memset_s(g_longBigString, g_bigStrLen, 'B', g_bigStrLen - 1);
    g_longBigString[g_bigStrLen - 1] = '\0';
}

void TrxBeginWithType(GmcConnT *conn, GmcIsolationTypeE type, GmcTrxTypeE trxType)
{
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = type;
    config.readOnly = false;
    config.trxType = trxType;
    int32_t ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
}

void TrxEnd(GmcConnT *conn, uint32_t isCommit)
{
    int32_t ret = GMERR_OK;
    if (isCommit == 1) {
        ret = GmcTransCommit(conn);
    } else if (isCommit == 0) {
        ret = GmcTransRollBack(conn);
    } else {
    }
    EXPECT_EQ(GMERR_OK, ret);
}

char g_stringData[1024] = "gmdbv5xshjcbhcsjhcjascvsdvsd7uyj.,..vdzxvbtb--=-=hbstg1224540cdjo csajnc4536sc4sz3445r32r";

int32_t InsertVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t startIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startIndex; i < startIndex + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 25;
        int16_t F3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

int32_t UpdateVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t startIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startIndex; i < startIndex + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 25;
        int16_t F3 = i - 2;
        // 更新记录和插入记录完全一致
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            return ret;
        }
    }
    return ret;
}

int32_t UpdateVertexDataLob(GmcStmtT *stmt, uint32_t recordCount, const char *labelName, uint32_t startIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startIndex; i < startIndex + recordCount; i++) {
        uint32_t F1 = i;
        SetUpdateBigStr();
        // 更新大对象数据
        ret = GmcSetVertexProperty(stmt, "BigField", GMC_DATATYPE_STRING, g_longBigString, strlen(g_longBigString));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            return ret;
        }
    }
    return ret;
}

int32_t DeleteVertexDataWithPk(GmcStmtT *stmt, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t f1 = i;
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

int32_t GetAndCheckProperty(GmcStmtT *stmt, uint32_t f1Value)
{
    bool isNull;
    uint32_t propSize;
    void *propVal;
    uint32_t F1 = f1Value;
    uint32_t F2 = f1Value + 25;
    int16_t F3 = f1Value - 2;

    int32_t ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (uint32_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F1", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F1, *((uint32_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (uint32_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F2", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F2, *((uint32_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F3", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (int16_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F3", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F3, *((int16_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F4", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (char *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F4", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_STREQ(g_stringData, (char *)propVal);
    free(propVal);
    return GMERR_OK;
}

int32_t ReadByPk(GmcStmtT *stmt, int32_t recordCount, const char *labelName, int32_t step)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (int i = 0; i < recordCount; i = i + step) {
        uint32_t PKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &PKValue, sizeof(PKValue));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        RETURN_IFERR(ret);
        EXPECT_FALSE(isFinished) << "FAILED ! pk: " << i;
        ret = GetAndCheckProperty(stmt, i);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int32_t ReadByScan(GmcStmtT *stmt, uint32_t startVal, int32_t expectCnt, const char *labelName)
{
    bool eof;
    int32_t recordCnt = 0;
    uint32_t F1Value = startVal;
    int32_t ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    while (true) {
        ret = GmcFetch(stmt, &eof);
        RETURN_IFERR(ret);
        if (ret != GMERR_OK || eof) {
            break;
        }
        ret = GetAndCheckProperty(stmt, F1Value);
        RETURN_IFERR(ret);
        recordCnt++;
        F1Value++;
    }
    EXPECT_EQ(expectCnt, recordCnt);
    return expectCnt != recordCnt ? GMERR_DATA_EXCEPTION : GMERR_OK;
}

void VerifyRecordCount(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret) << "labelName: " << labelName;
    uint64_t count;
    ret = GmcGetVertexRecordCount(stmt, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(recordCount, count);
}

int32_t GetVertexData(GmcStmtT *stmt, uint32_t startVal, uint32_t recordCount, const char *labelName)
{
    VerifyRecordCount(stmt, recordCount, labelName);
    ReadByScan(stmt, startVal, recordCount, labelName);
    return GMERR_OK;
}

int32_t GetVertexDataLob(GmcStmtT *stmt, uint32_t startVal, uint32_t recordCount, const char *labelName, bool isUpdate)
{
    // check other fields except lob and the total insert count as well.
    int ret = GetVertexData(stmt, startVal, recordCount, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // decided whether to use updated version of lob to check.
    if (isUpdate) {
        SetUpdateBigStr();
    } else {
        SetInitBigStr();
    }
    uint32_t propSize = sizeof(g_longBigString);
    bool isNull = false;
    uint32_t PKValue = 0;
    ret = GmcPrepareStmtByLabelName(stmt, "var_vl_000", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &PKValue, sizeof(PKValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinished;
    ret = GmcFetch(stmt, &isFinished);
    EXPECT_EQ(GMERR_OK, ret);
    void *propVal = (char *)malloc(propSize);
    ret = GmcGetVertexPropertySizeByName(stmt, "BigField", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "BigField", propVal, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (strcmp((const char *)propVal, g_longBigString) != 0) {
        EXPECT_EQ(GMERR_OK, -1);
    }
    free(propVal);
    return GMERR_OK;
}

static void GetFileInfo(const char *path, const char *name, FileInfoT *fileInfo)
{
    char filePath[DB_MAX_PATH] = {0};
    int ret = snprintf_s(filePath, DB_MAX_PATH, DB_MAX_PATH - 1, "%s/%s", path, name);
    if (ret < 0) {
        fileInfo->isExist = false;
        return;
    }
    struct stat statBuf;
    if (DbAdptStat(filePath, &statBuf) != GMERR_OK) {
        fileInfo->isExist = false;
        return;
    }
    fileInfo->isExist = true;
    fileInfo->size = (uint32_t)statBuf.st_size;
}
void GetPersistentFileInfo(const char *path, PersistentFileInfoT *persFile)
{
    (void)memset_s(persFile, sizeof(PersistentFileInfoT), 0, sizeof(PersistentFileInfoT));
    if (!DbDirExist(path)) {
        return;
    }
    GetFileInfo(path, DB_CTRL_FILE_NAME, &persFile->ctrlFile);
    GetFileInfo(path, DB_SYSTEM_SPACE_NAME, &persFile->sysDataFile);
    GetFileInfo(path, DB_USER_SPACE_NAME, &persFile->userDataFile);
    GetFileInfo(path, DB_UNDO_SPACE_NAME, &persFile->undoDataFile);
}
bool PersistentFileCompare(PersistentFileInfoT *persFile1, PersistentFileInfoT *persFile2)
{
    int ret = memcmp(persFile1, persFile2, sizeof(PersistentFileInfoT));
    return ret == 0;
}

int StartDbServerWithRecoveryInEuler(char *configPath, const char *recoveryPath, bool isBg, const char *serverLctr)
{
    DbLctrT srvLctr;
    int32_t ret;
    const uint32_t buf_size = 256;
    char cmdBuf[buf_size] = {0};
    ret = DbLctrParse(&srvLctr, serverLctr);
    if (ret != GMERR_OK) {
        printf("parse server locator failed: %s. \n", serverLctr);
        return -1;
    }
    char *bgOp = (char *)"";
    if (isBg) {
        bgOp = (char *)"-b";
    }
    if (recoveryPath == NULL) {
        ret = snprintf_s(cmdBuf, buf_size, buf_size - 1, "gmserver %s -p %s", bgOp, configPath);
    } else {
        ret = snprintf_s(cmdBuf, buf_size, buf_size - 1, "gmserver %s -p %s -r %s", bgOp, configPath, recoveryPath);
    }
    if (ret < 0) {
        printf("format start db server command failed. \n");
        return -1;
    }
    ret = system(cmdBuf);
    if (ret != 0) {
        printf("system(gmserver) in euler failed. \n");
        if (WIFEXITED(ret)) {
            return WEXITSTATUS(ret);
        } else if (WIFSIGNALED(ret)) {
            return WTERMSIG(ret);
        } else {
            return -1;
        }
    } else {
        while (system("gmsysview -q V\\$DB_SERVER")) {
            DbSleep(500);
        }
        printf("start gmserver in euler successfully! \n");
    }
    return 0;
}

int StartDbServerWithRecovery(char *configPath, const char *recoveryPath, bool isBg, const char *serverLctr)
{
#if (defined RTOSV2 || defined RTOSV2X)
    return -1;  // 暂不适配HPE环境
#else
    return StartDbServerWithRecoveryInEuler(configPath, recoveryPath, isBg, serverLctr);
#endif
}

extern "C" bool DbProcessInstanceIsExist(void);
void ShutdownServer(GmcConnT *conn)
{
    DB_UNUSED(conn);
    system("kill -9 $(pidof gmserver)");
    int count = 0;
    DbSetInstanceId(GET_INSTANCE_ID);
    while (DbProcessInstanceIsExist()) {
        DbSleep(50);
        count++;
        if (count > 60) {
            cout << "gmserver still exists for more than " << (count / 20) << "seconds" << endl;
        }
    }
    cout << "gmserver exits by kill -9" << endl;
}

void StPersistenceShutdownServer(void)
{
    ShutdownServer(NULL);
}

void ShutdownServerNormal(GmcConnT *conn)
{
    DB_UNUSED(conn);
    system("kill -15 $(pidof gmserver)");
    int count = 0;
    DbSetInstanceId(GET_INSTANCE_ID);
    while (DbProcessInstanceIsExist()) {
        DbSleep(50);
        count++;
        if (count > 60) {
            cout << "gmserver still exists for more than " << (count / 20) << "seconds" << endl;
        }
    }
    cout << "gmserver exits  by kill -15" << endl;
}

namespace var_label {
int32_t ReadByPk(GmcStmtT *stmt, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t PKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &PKValue, sizeof(PKValue));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        RETURN_IFERR(ret);
        // 不能这个，EXPECT_FALSE(isFinished) ; 因为可能会校验不存在的key
        if (isFinished) {
            return ST_NO_DATA;
        }
        ret = GetAndCheckProperty(stmt, i);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int32_t InsertVar(GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName,
    const char *strData)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    RETURN_IFERR(ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 25;
        int16_t F3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, strData, strlen(strData));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, strData, strlen(strData));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, strData, strlen(strData));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return GMERR_OK;
}

int32_t InsertBigObj(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    RETURN_IFERR(ret);

    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 25;
        int16_t F3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, g_stringData, strlen(g_stringData));
        RETURN_IFERR(ret);
        SetInitBigStr();
        ret = GmcSetVertexProperty(stmt, "BigField", GMC_DATATYPE_STRING, g_longBigString, strlen(g_longBigString));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return GMERR_OK;
}

int32_t InsertBigObjTwoMegaBytes(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    RETURN_IFERR(ret);

    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 25;
        int16_t F3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        RETURN_IFERR(ret);
        SetInitBigStr();
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, g_longBigString, strlen(g_longBigString));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, g_longBigString, strlen(g_longBigString));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, g_longBigString, strlen(g_longBigString));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "BigField", GMC_DATATYPE_STRING, g_longBigString, strlen(g_longBigString));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return GMERR_OK;
}

int32_t BatchInsert(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount,
    const char *labelName, const char *strData)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    RETURN_IFERR(ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    uint32_t batchNum = 2048;
    for (uint32_t i = startIdx; i < startIdx + recordCount;) {
        for (uint32_t j = 0; j < batchNum && i < startIdx + recordCount; j++, i++) {
            uint32_t F1 = i;
            uint32_t F2 = i + 25;
            int16_t F3 = i - 2;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
            RETURN_IFERR(ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
            RETURN_IFERR(ret);
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
            RETURN_IFERR(ret);
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, strData, strlen(strData));
            RETURN_IFERR(ret);
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, strData, strlen(strData));
            RETURN_IFERR(ret);
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, strData, strlen(strData));
            RETURN_IFERR(ret);
            GmcBatchAddDML(batch, stmt);
            RETURN_IFERR(ret);
        }
        GmcBatchRetT batchRet = {};
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
    }
    ret = GmcBatchDestroy(batch);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int32_t BatchInsertBigObj(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount,
    const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    RETURN_IFERR(ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    batchOption.batchLimitSize = 64;
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    const int bigStrLen = 32 * 1024;
    char longString[bigStrLen] = {0};
    memset_s(longString, bigStrLen, 'A', bigStrLen - 1);
    longString[bigStrLen - 1] = '\0';

    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 25;
        int16_t F3 = i - 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "BigField", GMC_DATATYPE_STRING, longString, strlen(longString));
        RETURN_IFERR(ret);
        GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    GmcBatchRetT batchRet = {};
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int32_t CheckUpdatedData(GmcStmtT *stmt, uint32_t F1Value, const char *str)
{
    bool isNull;
    uint32_t propSize;
    void *propVal;
    uint32_t F1 = F1Value;
    uint32_t F2 = F1Value + 25 + F1Value;  // 与Update 对应
    int16_t F3 = F1Value - 2 + F1Value;

    int32_t ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (uint32_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F1", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F1, *((uint32_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (uint32_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F2", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F2, *((uint32_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F3", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (int16_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F3", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F3, *((int16_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F4", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (char *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F4", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_STREQ(str, (char *)propVal);
    free(propVal);
    return GMERR_OK;
}

int32_t ReadUpdatedDataByPk(GmcStmtT *stmt, uint32_t startIdx, uint32_t recordCount, const char *name, const char *str)
{
    int ret = GmcPrepareStmtByLabelName(stmt, name, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t PKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &PKValue, sizeof(PKValue));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        RETURN_IFERR(ret);
        EXPECT_FALSE(isFinished);
        ret = CheckUpdatedData(stmt, i, str);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

// 改变insert 的值的update版本, 更易测出页压缩等场景
int32_t UpdateData(GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName,
    const char *newStr)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        // 在insert 的值基础上变更
        uint32_t F1 = i;
        uint32_t F2 = i + 25 + i;
        int16_t F3 = i - 2 + i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, newStr, strlen(newStr));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, newStr, strlen(newStr));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, newStr, strlen(newStr));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            return ret;
        }
    }
    return ret;
}

int32_t UpdateToBigObj(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    EXPECT_EQ(GMERR_OK, ret);

    const int bigStrLen = 61 * 1024;
    char longString[bigStrLen] = {0};
    memset_s(longString, bigStrLen, 'B', bigStrLen - 1);
    longString[bigStrLen - 1] = '\0';

    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        // 在insert 的值基础上变更
        uint32_t F1 = i;
        uint32_t F2 = i + 25 + i;
        int16_t F3 = i - 2 + i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, g_longString, strlen(g_longString));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "BigField", GMC_DATATYPE_STRING, longString, strlen(longString));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

int32_t BatchUpdateData(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount,
    const char *labelName, const char *newStr)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    batchOption.batchLimitSize = 64;
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    const int bigStrLen = 32 * 1024;
    char longString[bigStrLen] = {0};
    memset_s(longString, bigStrLen, 'A', bigStrLen - 1);
    longString[bigStrLen - 1] = '\0';

    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        // 在insert 的值基础上变更
        uint32_t F1 = i;
        uint32_t F2 = i + 25 + i;
        int16_t F3 = i - 2 + i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, newStr, strlen(newStr));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, newStr, strlen(newStr));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, newStr, strlen(newStr));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    GmcBatchRetT batchRet = {};
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

int32_t BatchUpdateToBigObj(GmcStmtT *stmt, GmcConnT *conn, GmcOperationTypeE op, uint32_t startIdx,
    uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    batchOption.batchLimitSize = 64;
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    const int bigStrLen = 32 * 1024;
    char longString[bigStrLen] = {0};
    memset_s(longString, bigStrLen, 'A', bigStrLen - 1);
    longString[bigStrLen - 1] = '\0';

    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        // 在insert 的值基础上变更
        uint32_t F1 = i;
        uint32_t F2 = i + 25 + i;
        int16_t F3 = i - 2 + i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "BigField", GMC_DATATYPE_STRING, longString, strlen(longString));
        EXPECT_EQ(GMERR_OK, ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            break;
        }
    }
    GmcBatchRetT batchRet = {};
    ret = GmcBatchExecute(batch, &batchRet);
    (void)GmcBatchDestroy(batch);
    return ret;
}
}  // namespace var_label

namespace fixed_label {

int32_t InsertFixed(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t F1 = i;
        uint32_t F2 = i + 1;
        int16_t F3 = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return GMERR_OK;
}

int32_t UpdateFixed(
    GmcStmtT *stmt, GmcOperationTypeE op, uint32_t startIdx, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, op);
    RETURN_IFERR(ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        // 在insert 的值基础上变更
        uint32_t F1 = i;
        uint32_t F2 = i + 1 + i;
        int16_t F3 = i + 2 + i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2, sizeof(F2));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        RETURN_IFERR(ret);

        // 设置待更新的主键值
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    return ret;
}

int32_t GetAndCheckFixedProperty(GmcStmtT *stmt, uint32_t F1Value, bool isUpdate)
{
    bool isNull;
    uint32_t propSize;
    void *propVal;
    uint32_t F1 = F1Value;
    uint32_t F2 = !isUpdate ? F1Value + 1 : F1Value + 1 + F1;
    int16_t F3 = !isUpdate ? F1Value + 2 : F1Value + 2 + F1;

    int32_t ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (uint32_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F1", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F1, *((uint32_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (uint32_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F2", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F2, *((uint32_t *)propVal));
    free(propVal);

    ret = GmcGetVertexPropertySizeByName(stmt, "F3", &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    propVal = (int16_t *)malloc(propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F3", propVal, propSize, &isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    EXPECT_EQ(F3, *((int16_t *)propVal));
    free(propVal);
    return GMERR_OK;
}

int32_t ReadByPk(GmcStmtT *stmt, uint32_t startIdx, int32_t recordCount, const char *name, bool isUpdate)
{
    int ret = GmcPrepareStmtByLabelName(stmt, name, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (uint32_t i = startIdx; i < startIdx + recordCount; i++) {
        uint32_t PKValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &PKValue, sizeof(PKValue));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinished;
        ret = GmcFetch(stmt, &isFinished);
        RETURN_IFERR(ret);
        if (isFinished) {
            return ST_NO_DATA;
        }
        ret = GetAndCheckFixedProperty(stmt, i, isUpdate);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}
}  // namespace fixed_label

uint32_t TruncateFile(char *fileName, float percentage, char direction)
{
    FILE *fp = fopen(fileName, "rb+");
    if (fp == NULL) {
        printf("Failed to open file %s", fileName);
        return -1;
    }

    // 获取文件大小
    (void)fseek(fp, 0L, SEEK_END);
    long int fileSize = ftell(fp);
    if (fileSize == 0) {
        return -1;
    }
    // 计算截断位置
    long int sizeCount;
    char *buffer = NULL;
    long int truncatePos = fileSize * percentage;

    if (direction == 'b') {
        (void)fseek(fp, truncatePos, SEEK_SET);
        sizeCount = fileSize - truncatePos;
        buffer = (char *)malloc(fileSize - truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return -1;
        }
        (void)fread(buffer, sizeof(char), fileSize - truncatePos, fp);
    } else if (direction == 'a') {
        (void)fseek(fp, 0, SEEK_SET);
        sizeCount = truncatePos;
        buffer = (char *)malloc(truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return -1;
        }
        (void)fread(buffer, sizeof(char), truncatePos, fp);
    } else {
        printf("Invalid direction %c", direction);
        (void)fclose(fp);
        return -1;
    }
    (void)fseek(fp, 0, SEEK_SET);
    (void)ftruncate(fileno(fp), 0);
    (void)fwrite(buffer, sizeof(char), sizeCount, fp);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t DbPersistCompressFunc(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uLongf dSize = srcSize;
    uLong sSize = srcSize;
    int ret = compress(dest, &dSize, src, sSize);
    *destSize = (uint32_t)dSize;
    return ret;
}

int32_t DbPersistDecompressFunc(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uLongf dSize = (uLongf)*destSize;
    uLong sSize = srcSize;
    int ret = uncompress(dest, &dSize, src, sSize);
    *destSize = (uint32_t)dSize;
    return ret;
}

int32_t GetUint32ConfigValByName(const char *cfgName, const char *cfgFile, uint32_t *cfgVal)
{
    char command[BUFF_MAX_LEN];
    int ret = snprintf_s(
        command, BUFF_MAX_LEN, BUFF_MAX_LEN - 1, "grep %s %s | grep %s |awk '{printf $3}'", cfgName, cfgFile, "=");
    EXPECT_GT(ret, 0);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        return DB_ERROR;
    }
    EXPECT_NE((void *)NULL, pf);

    char result[BUFF_MAX_LEN] = {0};
    while (NULL != fgets(result, BUFF_MAX_LEN, pf)) {
    };

    fclose(pf);

    *cfgVal = atoi(result);
    return DB_SUCCESS;
}

int32_t GetInt32CfgCurValue(GmcStmtT *syncStmt, const char *cfgName)
{
    EXPECT_EQ(GMERR_OK, GmcGetCfg(syncStmt, cfgName));
    void *value = NULL;
    uint32_t valueSize = 0;
    GmcDataTypeE type = GMC_DATATYPE_NULL;

    EXPECT_EQ(GMERR_OK, GmcGetCfgInfoByType(syncStmt, GMC_CFG_INFO_TYPE_CUR_VALUE, &type, &value, &valueSize));
    EXPECT_EQ(GMC_DATATYPE_INT32, type);
    EXPECT_EQ(sizeof(uint32_t), valueSize);
    return *(int32_t *)value;
}

// 文件名长度
constexpr int MAX_FILE_NAME_SIZE = 128;

int PersistGetProcessUsedMemSize(const int pid, const char *memType)
{
    FILE *fp;
    constexpr int cmdResultBufLen = 2048;
    char file[MAX_FILE_NAME_SIZE] = {0};
    char lineBuff[cmdResultBufLen] = {0};
    sprintf_s(file, sizeof(file), "/proc/%d/status", pid);
    fp = fopen(file, "r");
    if (fp == NULL) {
        fprintf(stderr, "Process %d not exist.\n", pid);
        return 0;
    }
    char name[MAX_FILE_NAME_SIZE];
    int vmrss;

    const char *vmrssStr = memType;
    for (;;) {
        char *ret = fgets(lineBuff, sizeof(lineBuff), fp);
        if (ret == NULL) {
            fclose(fp);
            fprintf(stderr, "Get meminfo by pid %d failed.\n", pid);
            return 0;
        }
        if (strncasecmp(lineBuff, vmrssStr, strlen(vmrssStr)) == 0) {
            break;
        }
    }
    fclose(fp);
    sscanf_s(lineBuff, "%s %d", name, sizeof(name), &vmrss);
    return vmrss;
}

int PersistGetPid(const char *processName)
{
    char result[32];
    std::string cmd = std::string("pidof ") + processName;
    FILE *fp = popen(cmd.c_str(), "r");
    if (fp == NULL) {
        return -1;
    }

    char *p = fgets(result, sizeof(result), fp);
    pclose(fp);
    if ((p == NULL) || (strlen(p) == 0)) {
        // server has stopped
        return -2;
    }

    return atoi(p);
}

int PersistGetFileSize(const char *filePath, const char *fileName)
{
    char result[32];
    std::string cmd = std::string("du -s ") + filePath + std::string("/") + fileName;
    FILE *fp = popen(cmd.c_str(), "r");
    if (fp == NULL) {
        return -1;
    }

    char *p = fgets(result, sizeof(result), fp);
    pclose(fp);
    if ((p == NULL) || (strlen(p) == 0)) {
        // server has stopped
        return -2;
    }

    return atoi(p);
}

int PersistGetProcessIoInfo(const int pid, const char *memType)
{
    FILE *fp;
    constexpr int cmdResultBufLen = 2048;
    char file[MAX_FILE_NAME_SIZE] = {0};
    char lineBuff[cmdResultBufLen] = {0};
    sprintf_s(file, sizeof(file), "/proc/%d/io", pid);
    fp = fopen(file, "r");
    if (fp == NULL) {
        fprintf(stderr, "Process %d not exist.\n", pid);
        return 0;
    }
    char name[MAX_FILE_NAME_SIZE];
    int vmrss;

    const char *vmrssStr = memType;
    for (;;) {
        char *ret = fgets(lineBuff, sizeof(lineBuff), fp);
        if (ret == NULL) {
            fclose(fp);
            fprintf(stderr, "Get ioInfo by pid %d failed.\n", pid);
            return 0;
        }
        if (strncasecmp(lineBuff, vmrssStr, strlen(vmrssStr)) == 0) {
            break;
        }
    }
    fclose(fp);
    sscanf_s(lineBuff, "%s %d", name, sizeof(name), &vmrss);
    return vmrss;
}

void StPrintServerMemInfo(const int stServerPid, const char *desc)
{
    double serverVmrss = PersistGetProcessUsedMemSize(stServerPid, "VmRSS") / (double)DB_KIBI;
    ASSERT_GE(serverVmrss, 0);
    double serverVmsize = PersistGetProcessUsedMemSize(stServerPid, "VmSize") / (double)DB_KIBI;
    ASSERT_GE(serverVmsize, 0);
    double serverVmpeak = PersistGetProcessUsedMemSize(stServerPid, "VmPeak") / (double)DB_KIBI;
    ASSERT_GE(serverVmpeak, 0);

    cout << "\n" << desc << endl;
    cout << "server vmrss   :" << KRED << serverVmrss << KNRM << "(MB)" << endl;
    cout << "server vmsize  :" << KRED << serverVmsize << KNRM << "(MB)" << endl;
    cout << "server vmpeak  :" << KRED << serverVmpeak << KNRM << "(MB)" << endl;
}

void StPrintFileMemInfo(const char *filePath, const char *desc)
{
    DIR *d = opendir(filePath);
    struct dirent *entry;
    cout << "\n" << desc << endl;
    while ((entry = readdir(d)) != NULL) {
        if (entry->d_type == DT_REG) {
            double fileSize = PersistGetFileSize(filePath, entry->d_name) / (double)DB_KIBI;
            ASSERT_GE(fileSize, 0);
            cout << entry->d_name << " size      :" << KRED << fileSize << KNRM << "(MB)" << endl;
        }
    }
    closedir(d);
}

void StGetSystemIoInfo(const int stServerPid, int *value)
{
    double writeBytes = PersistGetProcessIoInfo(stServerPid, "write_bytes") / (double)DB_KIBI;
    ASSERT_GE(writeBytes, 0);

    *value = (int)writeBytes;
}

void StPrintSystemIoInfo(int oldWriteBytes, int newWriteBytes, const char *desc)
{
    cout << desc << endl;
    cout << "basic_bytes : " << KRED << oldWriteBytes << KNRM << "KB" << endl;
    cout << "write_bytes : " << KRED << newWriteBytes - oldWriteBytes << KNRM << "KB" << endl;
}

int32_t GetRedoTruncateBlockId(uint64_t *fieldVal)
{
    char command[BUFF_MAX_LEN];
    int ret = snprintf_s(command, BUFF_MAX_LEN, BUFF_MAX_LEN - 1,
        "gmsysview -q V\\$STORAGE_PERSISTENT_STAT | grep TRUNC_POINT | cut -d ',' -f 2");
    EXPECT_GT(ret, 0);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        return DB_ERROR;
    }
    EXPECT_NE((void *)NULL, pf);
    char result[BUFF_MAX_LEN] = {0};
    while (NULL != fgets(result, BUFF_MAX_LEN, pf)) {
    };
    fclose(pf);

    *fieldVal = atoi(result);
    return DB_SUCCESS;
}

static const uint32_t pageSize = 32 * 1024;  // 当前配置项 pageSize/ctrlPageSize 默认为32KB
void DestoryDataFileContent(char *filePath, uint32_t pageIndex, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // 文件头，以4KB对齐，实际的数据内容从4KB偏移开始
    // 页头20B的DbLatchT不会纳入CRC的计算
    // PersFileHeadT 结构体4KB对齐
    static const uint32_t dataFileHeadSize = 4 * 1024;
    uint32_t fileOffset = dataFileHeadSize + pageIndex * pageSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryDataFileHead(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    int ret = fseek(file, offset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void SwapDataFilePage(char *filePath, uint32_t pageIndex1, uint32_t pageIndex2)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    static const uint32_t dataFileHeadSize = 4 * 1024;
    uint32_t page1Offset = dataFileHeadSize + pageIndex1 * pageSize;
    int ret = fseek(file, page1Offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    uint8_t *page1 = (uint8_t *)malloc(pageSize);
    ret = fread(page1, 1, pageSize, file);
    ASSERT_GT(ret, 0);

    uint32_t page2Offset = dataFileHeadSize + pageIndex2 * pageSize;
    ret = fseek(file, page2Offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    uint8_t *page2 = (uint8_t *)malloc(pageSize);
    ret = fread(page2, 1, pageSize, file);
    ASSERT_GT(ret, 0);
    // 把page1写到page2的位置
    ret = fwrite(page1, 1, pageSize, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, page1Offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    // 把page2写到page1的位置
    ret = fwrite(page2, 1, pageSize, file);
    ASSERT_GT(ret, 0);

    free(page1);
    free(page2);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryCtrlFileContent(char *filePath, uint32_t pageIndex, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // ctrl文件头大小（固定）
    static const uint32_t ctrlFileHeadSize = 8;  // PersFileHeadT 的大小
    // ctrl file 第一个页为预留页
    uint32_t fileOffset = ctrlFileHeadSize + pageIndex * pageSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void WriteCtrlFileContent(char *filePath, uint32_t pageIndex, uint32_t offset, uint8_t *content, uint32_t contentLen)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // ctrl文件头大小（固定）
    static const uint32_t ctrlFileHeadSize = 40;  // sizeof(PersCtrlFileHeadT)
    // ctrl file 第一个页为预留页
    uint32_t fileOffset = ctrlFileHeadSize + pageIndex * pageSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite(content, sizeof(uint8_t), contentLen, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryRedoFileContent(char *filePath, uint32_t blockId, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // redo 文件是以512字节对齐 每一个RedoLogBatchT的大小不定
    // 第一个512字节存放的是RedoLogFileHeadT
    // RedoLogFileHeadT 512字节对齐
    static const uint32_t redoDefaultBlockSize = 512;
    uint32_t fileOffset = blockId * redoDefaultBlockSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestorySafeFileContent(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // safeFile 文件头预留4KB
    static const uint32_t safeFileReserveSize = 4 * 1024;
    uint32_t fileOffset = safeFileReserveSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

uint32_t GetSafeFilePageCnt(char *filePath, uint32_t pageCountOffset)
{
    FILE *file = fopen(filePath, "r+");
    if (file == NULL) {
        return 0;
    }

    // safeFile 文件头预留4KB
    static const uint32_t safeFileReserveSize = 4 * 1024;
    uint32_t fileOffset = safeFileReserveSize + pageCountOffset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    if (ret != 0) {
        return 0;
    }

    // 破坏文件的内容
    uint32_t pageCount = 0;
    ret = fread(&pageCount, sizeof(uint32_t), 1, file);
    if (ret != 1) {
        return 0;
    }

    // 关闭文件
    ret = fclose(file);
    if (ret != 0) {
        return 0;
    }
    return pageCount;
}

#if (EXPERIMENTAL_GUANGQI)
static int32_t GmdbPersistCompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    std::vector<uint8_t> buf(compressBound(srcSize), 0);
    unsigned long outLen = buf.size();
    auto ret = compress2(buf.data(), &outLen, src, srcSize, Z_BEST_SPEED);
    if (outLen > *destSize) {
        return -1;
    }

    (void)memcpy_s(dest, *destSize, buf.data(), outLen);
    *destSize = outLen;
    return ret;
}

bool g_stRegisterGmsAdpter = false;
static int32_t GmdbPersistDecompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    unsigned long outLen = *destSize;
    auto ret = uncompress(dest, &outLen, src, srcSize);
    *destSize = outLen;
    return ret;
}

static int32_t GmdbPersistFileNameFilterCallBack(const char *src, char *dest, uint32_t destSize)
{
    uint32_t i = 0;
    for (i = 0; src[i] != '\0'; i++) {
        dest[i] = tolower(src[i]);
    }
    dest[i] = '\0';
    return GMERR_OK;
}

void StPersistenceGmsRegAdaptFuncs(Status expectRet)
{
    if (g_stRegisterGmsAdpter) {
        // 重复注册，adapter会报错。即gmserver主线程已经启动的情况下，不能再次注册接口
        return;
    }
    GmsAdptFuncsT adpt = {0};
    g_stRegisterGmsAdpter = true;
    adpt.persistCompressFunc = (GmsPersistCompressFuncT)GmdbPersistCompressCallBack;
    adpt.persistDecompressFunc = (GmsPersistDecompressFuncT)GmdbPersistDecompressCallBack;
    adpt.persistFileNameFilterFunc = (GmsPersistFileNameFilterFuncT)GmdbPersistFileNameFilterCallBack;
    Status ret = GmsRegAdaptFuncs(&adpt);
    EXPECT_EQ(expectRet, ret);
}
#endif
