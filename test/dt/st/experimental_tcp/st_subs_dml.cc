#include <sys/epoll.h>
#include "query_subs_st_base.h"
#include "tools_st_common.h"
#include "query_subs_util.h"

using namespace std;

extern const char *serverLocator;
extern const char *userName;
extern const char *pwd;

#define WAIT_WHILE(condition) \
    do {                      \
        usleep(5000);         \
    } while (condition);

static const char *subLabelJson =
    R"([{
        "type":"record",
        "name":"subLabel1",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"subLabel1",
                    "name":"subLabel1_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const auto emptyCallback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {};
static bool g_isStartEpoll;
static pthread_t g_epollThreadId;

static void InitEpollFd()
{
    if (!g_isStartEpoll) {
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
        printf("base call GmcStartEpoll done\n");
        g_isStartEpoll = true;
    }
}

static void UnInitEpollFd()
{
    if (g_isStartEpoll) {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        g_epollThreadId = -1;
        printf("base call GmcStopEpoll done\n");
        g_isStartEpoll = false;
    }
}

class StQuerySubsTcp : public StTestSuitBase {
public:
protected:
    static void SetUpTestCase()
    {
        StSwitchTcpLocator();
        StartDbServer((char *)"gmserver_guangqi_tcp.ini", serverLocator, false);
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        StSwitchTcpLocator();
        InitEpollFd();
    }
    static void TearDownTestCase()
    {
        UnInitEpollFd();
        StopServer();
    }
};

TEST_F(StQuerySubsTcp, testSubPushVertexOnReplace_Update)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateCSModeSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, NULL));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            },
                            {
                                "property":"F2",
                                "value":4
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 3;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait until all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] < 1) {
        DbSleep(100);
    }
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}
