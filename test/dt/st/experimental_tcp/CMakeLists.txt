project(GMDB_ST_EXPERIMENTAL_TCP)
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

add_definitions("-DFEATURE_SERVER_FUNC_REG")
add_definitions("-DEXPERIMENTAL_GUANGQI")
add_definitions("-DEXPERIMENTAL_NERGC")

include_directories(.)
include_directories(${GMDB_ROOT_DIR}/pub/include)
include_directories(${GMDB_ROOT_DIR}/test/dt/st/000_st_common)

aux_source_directory(. SRC_ST_EXPERIMENTAL_TCP_LIST)

list(APPEND DT_LINK_LIB_LIST_EXPERIMENTAL_TCP stub)

add_executable(st_experimental_tcp ${SRC_ST_EXPERIMENTAL_TCP_LIST})

target_link_libraries(st_experimental_tcp StartDbServer gmcmd_embed gmadmin_embed gmerror_embed gmexport_embed gmimport_embed gmlog_embed gmrule_embed gmips_embed gmids_embed gmasst_embed gmddl_embed gmtools gmserver_embed z)

target_link_libraries(st_experimental_tcp ${DEPS_GMDB} ${DT_LINK_LIB_LIST_EXPERIMENTAL_TCP})
