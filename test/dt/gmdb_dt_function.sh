#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# manage public test fucntion
#
CUR_DIR=$(cd "$(dirname $0)";pwd)
GMDB_HOME="$CUR_DIR"/../../../../scripts
function helpInfo() {
    echo "[Error]: Unknown parameters"
    echo "[Usage]:"
    echo "[-a host | arm32 | aarch64 | x86_64 | x86] [-f release | debug | cov | asan | ubsan | tsan] [-e RTOS_SERVER | RTOS_CLIENT | HPE | rtosv2x | rtosv2 | rtos | suse] [-r TEST | ALL] [-P AR502H]"
}

function exec_cmd() {
    echo "${@:1}"
    "${@:1}"
}

function clean_build_hisotry() {
    rm -rf $1
    [ -d "${TMP_DIR}" ] && rm -rf "${TMP_DIR}"
    rm -rf *.xml
    rm -rf data/
    rm -rf log/
    GMDB_ROOT="$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/../..
    cd ${GMDB_ROOT}/test/dt/ut/common_test_include/
    rm -rf "EnvConfig.h"
    rm -rf lib*.so
    rm -rf lib*.a
    cd -
    echo "clean $1 all finish"
}

function check_serverpath()
{
    serverPath="/run/verona";
    if [ ! -d $serverPath ]; then
        mkdir $serverPath
    fi
}

function build_smoke() {
    build_test_opt=""
    if [[ "$test" =~ "ut" ]]; then
        build_test_opt="${build_test_opt} -DBUILD_UT=1"
    fi
    if [[ "$test" =~ "st" ]]; then
        build_test_opt="${build_test_opt} -DBUILD_ST=1"
    fi
    if [[ "$test" =~ "benchmark" ]]; then
        build_test_opt="${build_test_opt} -DBUILD_BENCHMARK=1"
    fi

    module_opt="-DMODULE=${module}"

    if [ "X${arch}" == "X" ]; then
        arch="aarch64"
    fi

    if [ "${arch}" = "x86" ]; then
        arch_opt="-DX86=1"
    elif [ "${arch}" = "x86_64" ]; then
        arch_opt="-DX86_64=1"
    fi

    if [ "${arch}" = "arm32" ] || [ "${arch}" = "arm32a15le" ]; then
        arch_opt="-DARM32=1"
    fi

    if [ "X$env" = "Xharmony" ] || [ "X$env" = "Xrtosv2x" ]; then
        build_os="rtosv2x"
    elif [ "X$env" = "Xhpe" ]; then
        build_os="hpe"
    elif [ "X$env" = "Xrtosv2" ]; then
        build_os="rtosv2"
    else
        if [ "${arch}" = "x86" ] || [ "${arch}" = "x86_64" ]; then
            build_os="suse"
        elif [ "${arch}" = "arm32a15le" ]; then
            build_os="rtos"
        else
            build_os="euler"
        fi
    fi

    if [[ "${arch}" == 'x86' ]];then
        export CROSS_COMPILE_FLAG='-m32'
    fi
    if [ "${asan}" = "-DASAN=1" ] && [ "X$env" = "Xrtosv2x" ]; then
        "${GMDB_HOME}"/ASAN_init.sh off && "${GMDB_HOME}"/ASAN_init.sh on "${build_os}"
    fi

    toolchain_name="toolchain-${arch}-${build_os}.cmake"
    CPU_COUNT=8   # cpu太多部分机器容易崩溃。
    . ${GMDB_ROOT}/toolchain_env.sh
    if [ "X$build_os" = "Xrtosv2x" ]; then
        source_toolchain_env aarch64-hm
        exec_cmd cmake . ${coverage} ${asan} ${ubsan} ${tsan} ${experimental} ${experimental_guangqi} ${perf_only} ${durable_memdata} ${bufferpool} ${single_so} ${gcc_only} ${feature_minikv} ${feature_persistence}\
            -DMODE="${mode}" ${build_test_opt} ${arch_opt} ${module_opt} ${direct_write} ${yang_validation} ${experimental_tsdb} ${experimental_nergc}\
            ${warm_reboot} ${feature_sql} ${feature_stream} ${feature_rsmem} ${feature_hac} ${feature_daf} ${feature_gql} ${feature_simplerel} ${clt_server_same_process} ${artcontainer} ${ts_multi_inst}  ${feature_annvector} -DRTOSV2X=1 -DCMAKE_TOOLCHAIN_FILE="${GMDB_ROOT}"/cmake/${toolchain_name} -B ${TMP_DIR}
    elif [ "X$build_os" = "Xrtosv2" ]; then
        source_toolchain_env ${arch} ${product}
        exec_cmd cmake . ${coverage} ${asan} ${ubsan} ${tsan} ${experimental} ${experimental_guangqi} ${perf_only} ${durable_memdata} ${bufferpool} ${single_so} ${gcc_only} ${feature_minikv} ${feature_persistence}\
            ${enablecp} ${perfsample} ${release} ${debug} ${build_test_opt} ${arch_opt} ${module_opt} ${direct_write} ${warm_reboot} ${experimental_nergc}\
            ${yang_validation} ${experimental_tsdb} ${feature_sql} ${feature_stream} ${feature_rsmem} ${feature_hac} ${feature_daf} ${feature_gql} ${feature_simplerel} ${clt_server_same_process} ${artcontainer} ${ts_multi_inst} ${feature_annvector} -DRTOSV2=1 -DMODE="${mode}" -DCMAKE_TOOLCHAIN_FILE="${GMDB_ROOT}"/cmake/${toolchain_name} \
            -B ${TMP_DIR}
    elif [ "X$build_os" = "Xhpe" ]; then
        source_toolchain_env aarch64-hpe ${product}
        export GCCLIB_PATH=${DL_SDK_HOME}/../gnu64/lib64/gcc/aarch64-target-linux-gnu/7.3.0
        export STDCXXLIB_PATH=${DL_SDK_HOME}/../gnu64/aarch64-target-linux-gnu/lib64
        exec_cmd cmake -DCMAKE_INSTALL_PREFIX="$OUTPUT_DIR" ${experimental} ${experimental_guangqi} ${perf_only} ${durable_memdata} ${bufferpool} ${single_so} ${gcc_only} ${feature_minikv} ${feature_persistence}\
            ${build_test_opt} ${arch_opt} ${module_opt} ${direct_write} ${warm_reboot} ${yang_validation} ${experimental_tsdb} ${experimental_nergc}\
            ${feature_sql}$ {feature_stream} ${feature_rsmem} ${feature_hac} ${feature_daf} ${feature_gql} ${feature_simplerel} ${clt_server_same_process} ${artcontainer} ${ts_multi_inst} ${feature_annvector} -DHPE="1" -DCMAKE_TOOLCHAIN_FILE="${GMDB_ROOT}"/cmake/${toolchain_name} -B ${TMP_DIR}
    elif [ "X$build_os" = "Xeuler" ] || [ "X$build_os" = "Xsuse" ]; then
        exec_cmd cmake . ${coverage} ${asan} ${ubsan} ${tsan} ${experimental} ${experimental_guangqi} ${durable_memdata} ${bufferpool} ${single_so} ${gcc_only} ${feature_minikv} ${feature_persistence}\
            ${enablecp} ${perfsample} ${release} ${debug} ${build_test_opt} ${arch_opt} ${module_opt} ${direct_write} ${experimental_nergc} ${merge_queue}\
            ${yang_validation} ${experimental_tsdb} ${warm_reboot} ${feature_sql} ${feature_stream} ${feature_rsmem} ${yang_only} ${perf_only} ${feature_hac} ${feature_daf} ${feature_gql} ${feature_simplerel} ${clt_server_same_process} ${artcontainer} ${ts_multi_inst} ${feature_annvector} -DRTOS_SERVER=1 -DMODE="${mode}" \
            -DCMAKE_TOOLCHAIN_FILE="${GMDB_ROOT}"/cmake/${toolchain_name} -B ${TMP_DIR}
    elif [ "X$build_os" = "Xrtos" ]; then
        source_toolchain_env arm32a15le
        exec_cmd cmake . ${coverage} ${asan} -DMODE="${mode}" ${build_test_opt} ${arch_opt} ${module_opt} ${single_so} -DRTOS=1 \
            ${experimental_guangqi} ${experimental_nergc} ${perf_only} ${durable_memdata} ${bufferpool} ${experimental_tsdb} ${feature_sql} ${feature_stream} ${feature_rsmem} ${feature_hac} ${feature_daf} ${feature_gql} ${feature_simplerel} ${clt_server_same_process} ${artcontainer} ${ts_multi_inst} ${feature_annvector} \
            -DCMAKE_TOOLCHAIN_FILE="${GMDB_ROOT}"/cmake/${toolchain_name} -B ${TMP_DIR}
    fi
    if [ "${arch}" = "arm32" ] || [ "${arch}" = "arm32a15le" ]; then
        # arm32需要编译对应的gtest库
        pushd ${GMDB_ROOT}/test/gtest/make/
        make
        popd
    fi

    cd "${TMP_DIR}"/
    make -j"${CPU_COUNT}"
    if [ "${asan}" = "-DASAN=1" ] && [ "X$env" = "Xrtosv2x" ]; then
        "${GMDB_HOME}"/ASAN_init.sh off
    fi
}

# Argument parser
function parse_arguments() {
    arch=''
    env='RTOS_SERVER'
    #benchmark运行模式，默认跑全量
    mode='ALL'
    single_so="-DSINGLE_SO=1"
    #默认编译release版本
    release="-DRELEASE=1"
    debug="-DDEBUG=0"
    coverage="-DCOVERAGE=0"
    asan="-DASAN=0"
    ubsan="-DUBSAN=0"
    tsan="-DTSAN=0"
    test=""
    module=""
    enablecp="-DENABLE_CRASHPOINT=0"
    perfsample="-DPERF_SAMPLE_STAT=0"
    experimental="-DEXPERIMENTAL=1"
    experimental_guangqi=""
    gcc_only="-DGCC_ONLY=0"
    feature_minikv=""
    feature_persistence=""
    product=""
    direct_write="-DDIRECT_WRITE=0"
    warm_reboot="-DWARM_REBOOT=0"
    yang_validation="-DYANG_VALIDATION=0"
    experimental_tsdb="-DEXPERIMENTAL_TSDB=0"
    feature_sql="-DFEATURE_SQL=0"
    feature_stream="-DFEATURE_STREAM=0"
    feature_gql="-DFEATURE_GQL=0"
    feature_simplerel="-DFEATURE_SIMPLEREL=0"
    yang_only="-DYANG_ONLY=0"
    feature_rsmem="-DFEATURE_RSMEM=0"
    durable_memdata="-DDURABLE_MEMDATA=0"
    bufferpool="-DBUFFERPOOL=0"
    feature_hac="-DFEATURE_HAC=0"
    feature_daf="-DFEATURE_DAF=0"
    perf_only="-DPERF_ONLY=0"
    clt_server_same_process=""
    artcontainer="-DART_CONTAINER=0"
    ts_multi_inst="-DTS_MULTI_INST=0"
    feature_annvector="-DFEATURE_ANNVECTOR=0"
    experimental_nergc=""
    merge_queue="-DMERGE_QUEUE=0"

    TEMP=`getopt -o a:t:f:e:c:r:R:o:P: \
--long arch:,buildtype:,env:,os:,cov,enablecp,perfsample,asan,ubsan,tsan,hpe,test:,module:,Product:,\
disable-experimental,experimental-guangqi,single_so,gcconly,feature_minikv,feature_persistence,direct_write,yang_validation,experimental_tsdb,\
warmreboot,feature_sql,feature_stream,feature_rsmem,yang_only,durable_memdata,bufferpool,feature_hac,feature_daf,perf_only,feature_gql,\
feature_simplerel,clt_server_same_process,artcontainer,ts_multi_inst,feature_annvector,ids_haotian,experimental-nergc,merge_queue -- "$@"`
	if [ $? != 0 ] ; then echo "Terminating..." >&2 ; exit 1 ; fi
	eval set -- "$TEMP"


	while true ; do
		case "$1" in
            -a|--arch) arch="$2"; shift 2 ;;
            -r) mode="$2"; shift 2 ;;
            -e|--env) env="$2"; shift 2 ;;
            -R|-o|--os) env="$2"; shift 2 ;;
            -P--Product) product="$2"; shift 2 ;;
            -t|--buildtype)
            if [ "X$2" = "Xdebug" ] || [ "X$2" = "XDEBUG" ]; then
                release="-DRELEASE=0"
                debug="-DDEBUG=1"
            fi
            shift 2 ;;
            -f)
            if [ "X$2" = "Xcov" ] || [ "X$2" = "XCOV" ]; then
                coverage="-DCOVERAGE=1"
            elif [ "X$2" = "Xasan" ] || [ "X$2" = "XASAN" ]; then
                asan="-DASAN=1"
            elif [ "X$2" = "Xubsan" ] || [ "X$2" = "XUBSAN" ]; then
                ubsan="-DUBSAN=1"
            elif [ "X$2" = "Xtsan" ] || [ "X$2" = "XTSAN" ]; then
                tsan="-DTSAN=1"
            elif [ "X$2" = "Xdebug" ] || [ "X$2" = "XDEBUG" ]; then
                release="-DRELEASE=0"
                debug="-DDEBUG=1"
            elif [ "X$2" = "Xrelease" ] || [ "X$2" = "XRELEASE" ]; then
                release="-DRELEASE=1"
                debug="-DDEBUG=0"
            fi
            shift 2 ;;
            --cov) coverage="-DCOVERAGE=1"; shift ;;
            --enablecp) enablecp="-DENABLE_CRASHPOINT=1"; shift ;;
            --perfsample) perfsample="--DPERF_SAMPLE_STAT=1"; shift ;;
            --asan) asan="-DASAN=1"; shift ;;
			--ubsan) ubsan="-DUBSAN=1"; shift ;;
			--tsan) tsan="-DTSAN=1"; shift ;;
            --test) test="$2"; shift 2 ;;
            --module) module="$2"; shift 2 ;;
            --disable-experimental) experimental="-DEXPERIMENTAL=0"; shift ;;
            --experimental-guangqi) experimental_guangqi="-DEXPERIMENTAL_GUANGQI=1 -DFEATURE_PERSISTENCE=1"; shift ;;
            --feature_minikv) feature_minikv="-DFEATURE_MINIKV=1"; shift ;;
            --feature_persistence) feature_persistence="-DFEATURE_PERSISTENCE=1"; shift ;;
            --direct_write) direct_write="-DDIRECT_WRITE=1"; shift ;;
            --yang_validation) yang_validation="-DYANG_VALIDATION=1"; shift ;;
            --single_so) single_so="-DSINGLE_SO=1"; shift ;;
            --ids_haotian) single_so="-DSINGLE_SO=0 -DIDS_HAOTIAN=1"; shift ;;
            --gcconly) gcc_only="-DGCC_ONLY=1"; shift ;;
            --experimental_tsdb) experimental_tsdb="-DEXPERIMENTAL_TSDB=1"; shift ;;
            --feature_sql) feature_sql="-DFEATURE_SQL=1"; shift ;;
            --feature_stream) feature_stream="-DFEATURE_STREAM=1"; shift ;;
            --feature_gql) feature_gql="-DFEATURE_GQL=1"; shift ;;
            --feature_rsmem) feature_rsmem="-DFEATURE_RSMEM=1"; shift ;;
            --warmreboot) warm_reboot="-DWARM_REBOOT=1"; shift ;;
            --yang_only) yang_only="-DYANG_ONLY=1"; shift ;;
            --durable_memdata) durable_memdata="-DDURABLE_MEMDATA=1"; shift ;;
            --bufferpool) bufferpool="-DBUFFERPOOL=1"; shift ;;
            --feature_hac) feature_hac="-DFEATURE_HAC=1"; shift ;;
            --feature_daf) feature_daf="-DFEATURE_DAF=1"; shift ;;
            --perf_only) perf_only="-DPERF_ONLY=1"; shift ;;
            --feature_simplerel) feature_simplerel="-DFEATURE_SIMPLEREL=1"; shift ;;
            --clt_server_same_process) clt_server_same_process="-DFEATURE_CLT_SERVER_SAME_PROCESS=1"; shift ;;
            --artcontainer) artcontainer="-DART_CONTAINER=1"; shift ;;
            --ts_multi_inst) ts_multi_inst="-DTS_MULTI_INST=1"; shift ;;
            --feature_annvector) feature_annvector="-DFEATURE_ANNVECTOR=1"; shift ;;
            --experimental-nergc) experimental_nergc="-DEXPERIMENTAL_NERGC=1 -DEXPERIMENTAL_GUANGQI=1 -DFEATURE_PERSISTENCE=1";experimental_guangqi="-DEXPERIMENTAL_GUANGQI=1 -DFEATURE_PERSISTENCE=1"; shift ;;
            --merge_queue) merge_queue="-DMERGE_QUEUE=1"; shift ;;
            --) shift ; break ;;
			*) helpInfo; shift ;;
		esac
	done
}

# Argument checker
function check_arguments() {
    if [ "$env" != 'RTOS_SERVER' ] && [ "$env" != 'RTOS_CLIENT' ] && [ "$env" != 'hpe' ] && [ "$env" != "rtosv2x" ] && [ "$env" != "rtosv2" ] && [ "$env" != "rtos" ] && [ "$env" != "suse" ]; then
        helpInfo
        exit 1
    fi
    if [ "X$arch" = "X" ] || [ "X$arch" = "Xhost" ]; then
        arch=$(uname -m)
    fi
    if [ "$arch" != 'aarch64' ] && [ "$arch" != 'x86_64' ] && [ "$arch" != 'x86' ] && [ "$arch" != "harmony" ] && [ "$arch" != "arm32" ] && [ "$arch" != "arm32a15le" ]; then
        helpInfo
        exit 1
    fi
}

function dt_build_main() {
    GMDB_ROOT="$(dirname "$(realpath "${BASH_SOURCE[0]}")")"/../..

    parse_arguments "$@"
    check_arguments
    cd "$(dirname "$(realpath "${BASH_SOURCE[0]}")")"
    slimit_bak=$(ulimit -s)
    ulimit -s 65535
    build_smoke
    ulimit -s ${slimit_bak}
}
