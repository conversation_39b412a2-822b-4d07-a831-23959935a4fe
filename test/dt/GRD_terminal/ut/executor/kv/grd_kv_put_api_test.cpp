/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "grd_kv_api.h"

#include <fstream>
#include <thread>
#include <ctime>
#include "gtest/gtest.h"

#include "db_test_tool.h"
#include "grd_db_api.h"
#include "grd_document_api.h"
#include "grd_error.h"
#include "store_limits.h"

#define TEST_DB "./data"
#define TEST_DB_FILE "./data/kvputapitestFile"
#define CONFIG_STR "{}"
#define COLLECTION_NAME "KVCollectionName"
#define COLLECTION_NAME2 "KVCollectionName2"
#define OPTION_STR "{ \"mode\" : \"kv\"}"
#define SHARED_MODE_DB "./data/shared_mode/sharedb"
#define SHARED_MODE_DB_DIR "./data/shared_mode"
#define LEN_512K (512 * 1024)

using namespace std;
using namespace testing::ext;

static GRD_DB *g_db = nullptr;
static const char *g_syncMapFile = "./data/shared_mode/sharedb_ut.map";
static const int SLEEP_MICROSECONDS = 10000;
#ifdef HARMONY_OS
// 鸿蒙自测试框架目前没有超时系统，因此设置为默认最多等待100s，防止中间步骤失败导致用例卡死
static const int MAX_SLEEP_TIME = (100 * 100);
#else
static const int MAX_SLEEP_TIME = (1200 * 100);  // 为方便写用例调试，设置超时时间1200s，即20min
#endif

static void CompareValue(const GRD_KVItemT value, const GRD_KVItemT targetValue)
{
    EXPECT_EQ(value.dataLen, targetValue.dataLen);
    EXPECT_EQ(memcmp(value.data, targetValue.data, value.dataLen), 0);
}

static void WaitCondition(const int *variable, int expect, int maxSleepTime = MAX_SLEEP_TIME)
{
    int time = 0;
    while (*variable != expect) {
        usleep(SLEEP_MICROSECONDS);
        time++;
        if (time >= maxSleepTime) {
            ASSERT_TRUE(false) << "WaitCondition 等待时间过久";
        }
    }
}

const size_t MAP_SIZE = sizeof(int);  // 申请1个控制位

static int *GetProcessSyncMemory(int *fileDesc)
{
    *fileDesc = open(g_syncMapFile, O_RDWR | O_CREAT | O_TRUNC, S_IRUSR | S_IWUSR);
    if (*fileDesc < 0) {
        return nullptr;
    }
    if (ftruncate(*fileDesc, MAP_SIZE) < 0) {
        return nullptr;
    }
    int *step = (int *)mmap(nullptr, MAP_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, *fileDesc, 0);
    if (step == MAP_FAILED) {
        return nullptr;
    }
    *step = 0;
    return step;
}

static void ClearProcessSyncMemory(int fd, int *buffer)
{
    ASSERT_GE(munmap(buffer, MAP_SIZE), 0);
    ASSERT_EQ(close(fd), 0);
    ASSERT_GE(unlink(g_syncMapFile), 0);
}

class KVPutApiTest : public testing::Test {
public:
    static void SetUpTestCase(void);
    static void TearDownTestCase(void);
    void SetUp();
    void TearDown();
};

void KVPutApiTest::SetUpTestCase(void)
{}

void KVPutApiTest::TearDownTestCase(void)
{}

void KVPutApiTest::SetUp(void)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen to create a database and create collection
     * @tc.expected: step1. GRD_OK
     */
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB), 0);
    EXPECT_EQ(DbTestTool::MakeDir(TEST_DB), 0);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE, CONFIG_STR, GRD_DB_OPEN_CREATE, &g_db), GRD_OK);
    ASSERT_EQ(GRD_CreateCollection(g_db, COLLECTION_NAME, OPTION_STR, 0), GRD_OK);
}

void KVPutApiTest::TearDown(void)
{
    /**
     * @tc.steps: step1. call GRD_DropCollection to drop the collection and close the database
     * @tc.expected: step1. GRD_OK
     */
    ASSERT_EQ(GRD_DropCollection(g_db, COLLECTION_NAME, 0), GRD_OK);
    ASSERT_EQ(GRD_DBClose(g_db, GRD_DB_CLOSE), GRD_OK);
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB), 0);
    g_db = nullptr;
}

/**
 * @tc.name: KVPutApiTest001
 * @tc.desc: Put key-value into collection with null parameter. parameter db is nullptr.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put key-value into collection with null db
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(nullptr, COLLECTION_NAME, &key, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: KVPutApiTest002
 * @tc.desc: Put key-value into collection with null parameter. parameter collectionName is nullptr.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put key-value into collection with null collectionName
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, nullptr, &key, &value), GRD_INVALID_ARGS);
    /**
     * @tc.steps: step2. parameter collectionName is empty string
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_KVPut(g_db, "", &key, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: KVPutApiTest003
 * @tc.desc: Put key-value into collection which does not exist
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put key-value into collection which does not exist
     * @tc.expected: step1. GRD_UNDEFINED_TABLE
     */
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME "not_existed", &key, &value), GRD_UNDEFINED_TABLE);
}

/**
 * @tc.name: KVPutApiTest004
 * @tc.desc: Put legal key-value into collection
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint8_t firstKey = 55;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    /**
     * @tc.steps: step2. Get key-value of collection to promise key-value put successful
     * @tc.expected: step2. GRD_OK
     */
    GRD_KVItemT tmpValue = {nullptr, 0};
    ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_OK);
    CompareValue(value, tmpValue);
    ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
}

/**
 * @tc.name: KVPutApiTest005
 * @tc.desc: Put legal key-value into collection and then put same key again
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest005, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint8_t firstKey = 111;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    /**
     * @tc.steps: step2. Put same key and another value into collection
     * @tc.expected: step2. GRD_OK
     */
    uint16_t secondVal = 543;
    GRD_KVItemT newVal = {&secondVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &newVal), GRD_OK);
    /**
     * @tc.steps: step3. Get key-value of collection to promise key-value put successful
     * @tc.expected: step3. GRD_OK
     */
    GRD_KVItemT tmpValue = {nullptr, 0};
    ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_OK);
    CompareValue(newVal, tmpValue);
    ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
}

/**
 * @tc.name: KVPutApiTest006
 * @tc.desc: Put key-value into collection with with null parameter. parameter key is nullptr.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest006, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put key-value into collection with Null key
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, nullptr, &value), GRD_INVALID_ARGS);
    /**
     * @tc.steps: step2. Put key-value into collection and data of key is nullptr
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    GRD_KVItemT key = {nullptr, sizeof(uint8_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_INVALID_ARGS);
    /**
     * @tc.steps: step3. Put key-value into collection and dataLen of key is zero
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_KVItemT zeroKey = {&firstKey, 0};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &zeroKey, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: KVPutApiTest007
 * @tc.desc: put kv item.data null and item.dataLen != 0
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest007, TestSize.Level0)
{
    /**
     * @tc.steps: step1. put kv item.data null and item.dataLen != 0
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    uint8_t firstKey = 123;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {nullptr, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: KVPutApiTest008
 * @tc.desc: Put key-value into collection and dataLen of key is over 1024 bytes
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest008, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put key-value into collection and dataLen of key is over 1024 bytes
     * @tc.expected: step1. GRD_OVER_LIMIT
     */
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, (uint32_t)MAX_GRD_KEY_LEN + 1};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OVER_LIMIT);
}

/**
 * @tc.name: KVPutApiTest009
 * @tc.desc: Put key-value into collection and dataLen of value is over 1M bytes
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest009, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put key-value into collection and dataLen of value is over 1M bytes
     * @tc.expected: step1. GRD_OVER_LIMIT
     */
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, (uint32_t)MAX_GRD_VALUE_LEN + 1};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OVER_LIMIT);
}

/**
 * @tc.name: KVPutApiTest010
 * @tc.desc: Put key-value into collection and put same key-value 1023 times
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest010, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint8_t firstKey = 1;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    for (int i = 1; i <= 1023; i++) {
        /**
         * @tc.steps: step2. Put same key-value into collection 1023 times
         * @tc.expected: step2. GRD_OK
         */
        ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK) << ", i:" << i;
        /**
         * @tc.steps: step3. Get key-value of collection to promise key-value put successful
         * @tc.expected: step3. GRD_OK
         */
        GRD_KVItemT tmpValue = {nullptr, 0};
        ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_OK);
        CompareValue(value, tmpValue);
        ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
    }
}

/**
 * @tc.name: KVPutApiTest011
 * @tc.desc: Put key-value into collection and put different key 1023 times
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest011, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint16_t firstKey = 0;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint16_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    GRD_KVItemT tmpValue = {nullptr, 0};
    for (int i = 1; i <= 1023; i++) {
        /**
         * @tc.steps: step2. Put different key into collection 1023 times
         * @tc.expected: step2. GRD_OK
         */
        firstKey++;
        key = {&firstKey, sizeof(uint16_t)};
        ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
        /**
         * @tc.steps: step3. Get key-value of collection to promise key-value put successful
         * @tc.expected: step3. GRD_OK
         */
        ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_OK);
        CompareValue(value, tmpValue);
        ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
    }
}

/**
 * @tc.name: KVPutApiTest012
 * @tc.desc: Put key-value into collection and put different value 1023 times
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest012, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint16_t firstKey = 0;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint16_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    GRD_KVItemT tmpValue = {nullptr, 0};
    for (int i = 1; i <= 1023; i++) {
        /**
         * @tc.steps: step2. Put different value into collection 1023 times
         * @tc.expected: step2. GRD_OK
         */
        firstVal++;
        value = {&firstVal, sizeof(uint16_t)};
        ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
        /**
         * @tc.steps: step3. Get key-value of collection to promise key-value put successful
         * @tc.expected: step3. GRD_OK
         */
        ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_OK);
        CompareValue(value, tmpValue);
        ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
    }
}

/**
 * @tc.name: KVPutApiTest013
 * @tc.desc: Put legal key-value into collection
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest013, TestSize.Level3)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint8_t firstKey = 55;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    /**
     * @tc.steps: step2. Delete key-value of collection
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_KVDel(g_db, COLLECTION_NAME, &key), GRD_OK);
    /**
     * @tc.steps: step3. Get key-value of collection to promise key-value del successful
     * @tc.expected: step3. GRD_NO_DATA
     */
    GRD_KVItemT tmpValue = {nullptr, 0};
    ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_NO_DATA);
}

/**
 * @tc.name: KVPutApiTest014
 * @tc.desc: Put legal key-value into collection
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.010.003
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest014, TestSize.Level3)
{
    uint8_t firstKey = 55;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    /**
     * @tc.steps: step1. Get key-value which is not of collection
     * @tc.expected: step1. GRD_NO_DATA
     */
    ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &value), GRD_NO_DATA);
    /**
     * @tc.steps: step2. Put legal key-value into collection
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    /**
     * @tc.steps: step3. Get key-value of collection to promise key-value put successful
     * @tc.expected: step3. GRD_OK
     */
    GRD_KVItemT tmpValue = {nullptr, 0};
    ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, &tmpValue), GRD_OK);
    CompareValue(value, tmpValue);
    ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
}

/**
 * @tc.name: KVPutApiTest015
 * @tc.desc: Put key-value into collection which is document collection
 * @tc.type: FUNC
 * @tc.require: DTS2023081201382
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest015, TestSize.Level0)
{
    /**
     * @tc.steps: step1. Create collection which is document collection
     * @tc.expected: step1. GRD_OK
     */
    uint8_t firstKey = 10;
    uint16_t firstVal = 678;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    const char *collName = "document_name";
    const char *optionStr = "{ \"maxdoc\" : 5}";
    ASSERT_EQ(GRD_CreateCollection(g_db, collName, optionStr, 0), GRD_OK);
    /**
     * @tc.steps: step2. Put key-value into collection which is document collection
     * @tc.expected: step2. GRD_NOT_SUPPORT
     */
    ASSERT_EQ(GRD_KVPut(g_db, collName, &key, &value), GRD_NOT_SUPPORT);
}

/**
 * @tc.name: KVPutApiTest016
 * @tc.desc: Put key-value into kv collection after reopen DB
 * @tc.type: FUNC
 * @tc.require: DTS2023081510070
 * @tc.author: caihaoting
 */
HWTEST_F(KVPutApiTest, KVPutApiTest016, TestSize.Level3)
{
    /**
     * @tc.steps: step1. Create collection which is kv collection
     * @tc.expected: step1. GRD_OK
     */
    const char *collName = "kv_collection_name";
    const char *optionStr = "{ \"mode\" : \"kv\"}";
    ASSERT_EQ(GRD_CreateCollection(g_db, collName, optionStr, 0), GRD_OK);
    /**
     * @tc.steps: step2. Close DB and then open DB
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_DBClose(g_db, GRD_DB_CLOSE), GRD_OK);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE, CONFIG_STR, GRD_DB_OPEN_CREATE, &g_db), GRD_OK);
    /**
     * @tc.steps: step3. Put key-value into kv collection
     * @tc.expected: step3. GRD_OK
     */
    uint8_t firstKey = 10;
    uint16_t firstVal = 678;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, collName, &key, &value), GRD_OK);
    /**
     * @tc.steps: step4. Drop collection
     * @tc.expected: step4. GRD_OK
     */
    ASSERT_EQ(GRD_DropCollection(g_db, collName, 0), GRD_OK);
}

/**
 * @tc.name: KVPutApiTest017
 * @tc.desc: Get key-value when the value input param is nullptr
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: butaoyu
 */
HWTEST_F(KVPutApiTest, KVPutApiTest017, TestSize.Level3)
{
    /**
     * @tc.steps: step1. Put legal key-value into collection
     * @tc.expected: step1. GRD_OK
     */
    uint8_t firstKey = 5;
    uint16_t firstVal = 45;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ASSERT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);

    /**
     * @tc.steps: step2. Get key-value but the input value is nullptr
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_KVGet(g_db, COLLECTION_NAME, &key, nullptr), GRD_INVALID_ARGS);
}

static void KVPutApiCommon(GRD_KVItemT *keyItem, GRD_KVItemT *outputValueItem)
{
    int valueOut = 1;
    outputValueItem->data = &valueOut;
    outputValueItem->dataLen = 1;
    int ret = GRD_KVGet(g_db, COLLECTION_NAME, keyItem, outputValueItem);
    ASSERT_EQ(GRD_OK, ret);
    ASSERT_EQ(outputValueItem->data, nullptr);
    ASSERT_EQ(outputValueItem->dataLen, 0);
    ASSERT_EQ(GRD_KVFreeItem(outputValueItem), GRD_OK);

    ret = GRD_KVDel(g_db, COLLECTION_NAME, keyItem);
    ASSERT_EQ(GRD_OK, ret);
    ret = GRD_KVGet(g_db, COLLECTION_NAME, keyItem, outputValueItem);
    ASSERT_EQ(GRD_NO_DATA, ret);
}

/**
 * @tc.name: KVPutApiTest020
 * @tc.desc: put kv data with valueLength = 0B.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.016.001
 * @tc.author: wanghaishuo
 */
HWTEST_F(KVPutApiTest, KVPutApiTest020, TestSize.Level0)
{
    int val = 3;
    int key = 8;
    GRD_KVItemT keyItem = {&key, (int)sizeof(key)};
    GRD_KVItemT valueItem = {&val, 0};
    GRD_KVItemT outputValueItem;
    int ret = GRD_KVPut(g_db, COLLECTION_NAME, &keyItem, &valueItem);
    ASSERT_EQ(GRD_OK, ret);
    KVPutApiCommon(&keyItem, &outputValueItem);
    ret = GRD_KVPut(g_db, COLLECTION_NAME, &keyItem, &outputValueItem);
    ASSERT_EQ(GRD_OK, ret);
}

/**
 * @tc.name: KVPutApiTest021
 * @tc.desc: put kv data null.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230714002092.016.001
 * @tc.author: wanghaishuo
 */
HWTEST_F(KVPutApiTest, KVPutApiTest021, TestSize.Level0)
{
    int key = 8;
    GRD_KVItemT keyItem = {&key, (int)sizeof(key)};
    int ret = GRD_KVPut(g_db, COLLECTION_NAME, &keyItem, nullptr);
    ASSERT_EQ(GRD_OK, ret);

    GRD_KVItemT outputValueItem;
    KVPutApiCommon(&keyItem, &outputValueItem);
}

/**
 * @tc.name: KVPutApiTest022
 * @tc.desc: Test to put data over 4G, using object's size less than one page
 * @tc.type: FUNC
 * @tc.require: DTS2023120413032
 * @tc.author: zhujinlin
 */
HWTEST_F(KVPutApiTest, KVPutApiTest022, TestSize.Level4)
{
    /**
     * @tc.steps: step1. Open DB using bufferPoolSize:4G - 1 * pageSize, aka 4194240K, pageSize:64K
     * @tc.expected: step1. Execute successfully
     */
#define UT_SINGLE_PROCESS_64K_CONFIG_STR \
    R"({"pageSize":64, "redoFlushByTrx":0, "redoPubBufSize":4096, "bufferPoolSize":4194240 })"
    GRD_DB *db = nullptr;
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE "022", UT_SINGLE_PROCESS_64K_CONFIG_STR, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    ASSERT_EQ(GRD_CreateCollection(db, COLLECTION_NAME, OPTION_STR, 0), GRD_OK);

    /**
     * @tc.steps: step2. Continuously put data, 60K, till over 4G
     * @tc.expected: step2. GRD_OK
     */
    for (int i = 1; i <= 100000; i++) {
        string keyStr("key");
        keyStr += to_string(i);
        string valueStr(60 * 1024, 'a');
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        ASSERT_EQ(GRD_KVPut(db, COLLECTION_NAME, &key, &value), GRD_OK);
    }
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
#undef UT_SINGLE_PROCESS_64K_CONFIG_STR
}

/**
 * @tc.name: KVSetConfigTest001
 * @tc.desc: Test to call GRD_SetConfig with GRD_CONFIG_DATA_VERSION
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVSetConfigTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_SetConfig with GRD_CONFIG_DATA_VERSION
     * @tc.expected: step1. return GRD_NOT_SUPPORT
     */
    GRD_DbValueT dataVersion = {};
    EXPECT_EQ(GRD_SetConfig(g_db, GRD_CONFIG_DATA_VERSION, dataVersion), GRD_NOT_SUPPORT);
}

int KVGetConfigTest(GRD_DB *db)
{
    /**
     * @tc.steps: step1. call GRD_GetConfig whitout dml
     * @tc.expected: step1. return dataVersion = 0
     */
    GRD_DbValueT dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 0L);

    /**
     * @tc.steps: step2. put kv(value len < 512k), call GRD_GetConfig
     * @tc.expected: step2. return dataVersion = 0
     */
    string keyStr("k1");
    string valueStr(LEN_512K - 1, 'a');
    GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
    GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
    EXPECT_EQ(GRD_KVPut(db, COLLECTION_NAME, &key, &value), GRD_OK);
    dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 0L);

    /**
     * @tc.steps: step3. delete kv(value len < 512k), call GRD_GetConfig
     * @tc.expected: step3. return dataVersion = 1(delete will increase data version, regardless of the data length)
     */
    EXPECT_EQ(GRD_KVDel(db, COLLECTION_NAME, &key), GRD_OK);
    GRD_KVItemT getValue = {nullptr, 0};
    EXPECT_EQ(GRD_KVGet(db, COLLECTION_NAME, &key, &getValue), GRD_NO_DATA);
    dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 1L);

    /**
     * @tc.steps: step4. put kv(value len = 512k), call GRD_GetConfig
     * @tc.expected: step4. return dataVersion = 2
     */
    string valueStr512K(LEN_512K, 'a');
    value = {(void *)valueStr512K.c_str(), (uint32_t)valueStr512K.size()};
    EXPECT_EQ(GRD_KVPut(db, COLLECTION_NAME, &key, &value), GRD_OK);
    dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 2L);

    /**
     * @tc.steps: step5. delete kv(value len = 512k), call GRD_GetConfig
     * @tc.expected: step5. return dataVersion = 3(delete will increase data version, regardless of the data length)
     */
    EXPECT_EQ(GRD_KVDel(db, COLLECTION_NAME, &key), GRD_OK);
    EXPECT_EQ(GRD_KVGet(db, COLLECTION_NAME, &key, &getValue), GRD_NO_DATA);
    dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 3L);
    return GRD_OK;
}

/**
 * @tc.name: KVGetConfigTest001
 * @tc.desc: Test to call GRD_GetConfig in non-shared mode
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest001, TestSize.Level0)
{
    EXPECT_EQ(KVGetConfigTest(g_db), GRD_OK);
}

/**
 * @tc.name: KVGetConfigTest002
 * @tc.desc: Test to call GRD_GetConfig in shared mode
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest002, TestSize.Level0)
{
    const char *spConfig =
        "{\"pageSize\": 4, \"redoFlushByTrx\": 1, \"redoPubBufSize\": 256, \"maxConnNum\": 100, "
        "\"bufferPoolSize\": 1024, \"crcCheckEnable\": 0, \"bufferPoolPolicy\" : \"BUF_PRIORITY_INDEX\", "
        "\"sharedModeEnable\" : 1}";
    GRD_DB *db = nullptr;
    EXPECT_EQ(DbTestTool::RemoveDir(SHARED_MODE_DB_DIR), 0);
    EXPECT_EQ(DbTestTool::MakeDir(SHARED_MODE_DB_DIR), 0);
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    EXPECT_EQ(GRD_CreateCollection(db, COLLECTION_NAME, OPTION_STR, 0), GRD_OK);
    EXPECT_EQ(KVGetConfigTest(db), GRD_OK);
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
    EXPECT_EQ(DbTestTool::RemoveDir(SHARED_MODE_DB_DIR), 0);
}

/**
 * @tc.name: KVGetConfigTest003
 * @tc.desc: Test close the last connection will set data version = 0 in shared mode
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. put kv(value len = 512k), call GRD_GetConfig
     * @tc.expected: step1. return dataVersion = 1
     */
    string keyStr("k1");
    string valueStr512K(LEN_512K, 'a');
    GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
    GRD_KVItemT value = {(void *)valueStr512K.c_str(), (uint32_t)valueStr512K.size()};
    EXPECT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    GRD_DbValueT dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 1L);

    /**
     * @tc.steps: step2. close db, reopen, call GRD_GetConfig
     * @tc.expected: step2. return dataVersion = 0
     */
    EXPECT_EQ(GRD_DBClose(g_db, GRD_DB_CLOSE), GRD_OK);
    EXPECT_EQ(GRD_DBOpen(TEST_DB_FILE, CONFIG_STR, GRD_DB_OPEN_CREATE, &g_db), GRD_OK);
    dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 0);
}

/**
 * @tc.name: KVGetConfigTest004
 * @tc.desc: Test drop table will increase data version
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. create collection, call GRD_GetConfig
     * @tc.expected: step1. return dataVersion = 0
     */
    std::string tableName = "temp_table";
    EXPECT_EQ(GRD_CreateCollection(g_db, tableName.c_str(), OPTION_STR, 0), GRD_OK);
    GRD_DbValueT dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 0);

    /**
     * @tc.steps: step2. drop collection, call GRD_GetConfig
     * @tc.expected: step2. return dataVersion = 1
     */
    EXPECT_EQ(GRD_DropCollection(g_db, tableName.c_str(), 0), GRD_OK);
    dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 1L);
}

/**
 * @tc.name: KVGetConfigTest005
 * @tc.desc: Test insert, delete different size data
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest005, TestSize.Level2)
{
    /**
     * @tc.steps: step1. put kv and delete kv, call GRD_GetConfig
     * @tc.expected: step1. return expected dataVersion
     */
    int expectedVersion = 0;
    int sizeK = 1024;
    int maxValueLen = 4 * sizeK;  // max size 4M
    srand((unsigned)time(nullptr));
    for (int i = 1; i <= 50; i++) {  // 50 is loop count
        string keyStr("key");
        keyStr += to_string(i);
        int valueLen = (rand() % maxValueLen + 1) * sizeK;
        string valueStr(valueLen, 'a');
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        EXPECT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
        if (valueLen >= LEN_512K) {
            expectedVersion++;
        }
        GRD_DbValueT dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
        EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
        EXPECT_EQ(dataVersion.value.longValue, expectedVersion);

        // delete kv
        EXPECT_EQ(GRD_KVDel(g_db, COLLECTION_NAME, &key), GRD_OK);
        expectedVersion++;
        dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
        EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
        EXPECT_EQ(dataVersion.value.longValue, expectedVersion);
    }
}

void ExecuteDml(int index)
{
    GRD_DB *db = nullptr;
    EXPECT_EQ(GRD_DBOpen(TEST_DB_FILE, CONFIG_STR, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    string keyStr("key");
    keyStr += to_string(index);
    int valueLen = LEN_512K + (index % 2 ? index : -index);  // 2 is mod
    string valueStr(valueLen, 'a');
    GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
    GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
    EXPECT_EQ(GRD_KVPut(g_db, COLLECTION_NAME, &key, &value), GRD_OK);
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
}

/**
 * @tc.name: KVGetConfigTest006
 * @tc.desc: Test put data concurrently will increase data version correctly
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest006, TestSize.Level2)
{
    /**
     * @tc.steps:step1. create 50 threads, execute dml
     * @tc.expected: step1. ok
     */
    std::vector<std::thread> threadVec;
    int threadCnt = 50;
    for (int i = 0; i < threadCnt; i++) {
        threadVec.push_back(std::thread(ExecuteDml, i));
    }
    for (auto &t : threadVec) {
        t.join();
    }

    /**
     * @tc.steps: step2. create collection, call GRD_GetConfig
     * @tc.expected: step2. return dataVersion = 26
     */
    GRD_DbValueT dataVersion = GRD_GetConfig(g_db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 26L);
}

void OperationInParent(const char *spConfig, pid_t pid, int *step)
{
    /**
     * @tc.steps: step3. put big obj in parent process, call GRD_GetConfig
     * @tc.expected: step3. return dataVersion = 2
     */
    WaitCondition(step, 1);
    GRD_DB *db = nullptr;
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    string keyStr("k1");
    string valueStr(LEN_512K, 'a');
    GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
    GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
    EXPECT_EQ(GRD_KVPut(db, COLLECTION_NAME, &key, &value), GRD_OK);
    GRD_DbValueT dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 2L);

    /**
     * @tc.steps: step4. reopen db, call GRD_GetConfig
     * @tc.expected: step4. return dataVersion = 2, because child process still open
     */
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
    db = nullptr;
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 2L);
    *step = 2;  // 2 means parent process already get data version

    waitpid(pid, nullptr, 0);
    /**
     * @tc.steps: step5. reopen db, call GRD_GetConfig
     * @tc.expected: step5. return dataVersion = 0, because child process already close
     */
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
    db = nullptr;
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 0);

    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
    db = nullptr;
}

/**
 * @tc.name: KVGetConfigTest007
 * @tc.desc: Test insert data in different process will increase data version
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest007, TestSize.Level2)
{
    /**
     * @tc.steps: step1. open db in shared mode
     * @tc.expected: step1. ok
     */
    const char *spConfig =
        "{\"pageSize\": 4, \"redoFlushByTrx\": 1, \"redoPubBufSize\": 256, \"maxConnNum\": 100, "
        "\"bufferPoolSize\": 1024, \"crcCheckEnable\": 0, \"bufferPoolPolicy\" : \"BUF_PRIORITY_INDEX\", "
        "\"sharedModeEnable\" : 1}";
    EXPECT_EQ(DbTestTool::RemoveDir(SHARED_MODE_DB_DIR), 0);
    EXPECT_EQ(DbTestTool::MakeDir(SHARED_MODE_DB_DIR), 0);
    int fd = 0;
    int *step = GetProcessSyncMemory(&fd);
    *step = 0;

    /**
     * @tc.steps: step2. put big obj in child process, call GRD_GetConfig
     * @tc.expected: step2. return dataVersion = 1
     */
    pid_t pid = fork();
    if (pid == 0) {
        // child process
        GRD_DB *db = nullptr;
        EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
        EXPECT_EQ(GRD_CreateCollection(db, COLLECTION_NAME, OPTION_STR, 0), GRD_OK);
        *step = 1;
        string keyStr("k1");
        string valueStr(LEN_512K, 'a');
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        EXPECT_EQ(GRD_KVPut(db, COLLECTION_NAME, &key, &value), GRD_OK);
        GRD_DbValueT dataVersion2 = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
        EXPECT_EQ(dataVersion2.type, GRD_DB_DATATYPE_INTEGER);
        EXPECT_EQ(dataVersion2.value.longValue, 1L);
        WaitCondition(step, 2);  // 2 means parent process already get data version
        EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
        exit(0);
    }

    OperationInParent(spConfig, pid, step);
    ClearProcessSyncMemory(fd, step);
    EXPECT_EQ(DbTestTool::RemoveDir(SHARED_MODE_DB_DIR), 0);
}

void ExecuteDmlInChildProcess(const char *spConfig)
{
    int dataCnt = 50;
    GRD_DB *db = nullptr;
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    for (int j = 0; j < dataCnt; j++) {
        string keyStr("key");
        keyStr += to_string(j);
        int valueLen = LEN_512K + (j % 2 ? j : -j);  // 2 is mod
        string valueStr(valueLen, 'a');
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        EXPECT_EQ(GRD_KVPut(db, COLLECTION_NAME, &key, &value), GRD_OK);
    }
    for (int j = 0; j < dataCnt; j++) {
        string keyStr("key");
        keyStr += to_string(j);
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        EXPECT_EQ(GRD_KVDel(db, COLLECTION_NAME, &key), GRD_OK);
    }
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
}

/**
 * @tc.name: KVGetConfigTest008
 * @tc.desc: Test put/delete data concurrently from different process
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.004.001
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, KVGetConfigTest008, TestSize.Level3)
{
    /**
     * @tc.steps: step1. create db, collection
     * @tc.expected: step1. ok
     */
    EXPECT_EQ(DbTestTool::RemoveDir(SHARED_MODE_DB_DIR), 0);
    EXPECT_EQ(DbTestTool::MakeDir(SHARED_MODE_DB_DIR), 0);
    const char *spConfig = "{\"pageSize\": 4, \"redoFlushByTrx\": 1, \"sharedModeEnable\" : 1}";
    GRD_DB *db = nullptr;
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    EXPECT_EQ(GRD_CreateCollection(db, COLLECTION_NAME, OPTION_STR, 0), GRD_OK);
    EXPECT_EQ(GRD_CreateCollection(db, COLLECTION_NAME2, OPTION_STR, 0), GRD_OK);
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);

    /**
     * @tc.steps: step2. fork 7 process, excute dml
     * @tc.expected: step2. ok
     */
    int childCnt = 7;
    std::vector<pid_t> pidVec;
    for (int i = 0; i < childCnt; i++) {
        pid_t pid = fork();
        if (pid == 0) {
            // child process
            ExecuteDmlInChildProcess(spConfig);
            exit(0);
        }
        // parent
        pidVec.push_back(pid);
    }

    for (auto pid : pidVec) {
        waitpid(pid, nullptr, 0);
    }
    /**
     * @tc.steps: step3. excute dml in parent
     * @tc.expected: step3. ok
     */
    EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
    int dataCnt = 50;
    for (int j = 0; j < dataCnt; j++) {
        string keyStr("key");
        keyStr += to_string(j);
        int valueLen = LEN_512K + (j % 2 ? j : -j);  // 2 is mod
        string valueStr(valueLen, 'a');
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        EXPECT_EQ(GRD_KVPut(db, COLLECTION_NAME2, &key, &value), GRD_OK);
    }
    for (int j = 0; j < dataCnt; j++) {
        string keyStr("key");
        keyStr += to_string(j);
        GRD_KVItemT key = {(void *)keyStr.c_str(), (uint32_t)keyStr.size()};
        EXPECT_EQ(GRD_KVDel(db, COLLECTION_NAME2, &key), GRD_OK);
    }

    /**
     * @tc.steps: step3. call GRD_GetConfig
     * @tc.expected: step3. return dataVersion = 16 (26 put + 50 delete)
     */
    GRD_DbValueT dataVersion = GRD_GetConfig(db, GRD_CONFIG_DATA_VERSION);
    EXPECT_EQ(dataVersion.type, GRD_DB_DATATYPE_INTEGER);
    EXPECT_EQ(dataVersion.value.longValue, 76L);
    EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
}

/**
 * @tc.name: SharedMemoryOverlimitTest001
 * @tc.desc: Test file map shared memory alloc size wouldn't over limit in shared mode(one process keep open, another
 * process open and close many times)
 * @tc.type: FUNC
 * @tc.require: DTS2024072722521
 * @tc.author: zhangshijie
 */
HWTEST_F(KVPutApiTest, SharedMemoryOverlimitTest001, TestSize.Level2)
{
    EXPECT_EQ(DbTestTool::RemoveDir(SHARED_MODE_DB_DIR), 0);
    EXPECT_EQ(DbTestTool::MakeDir(SHARED_MODE_DB_DIR), 0);
    const char *spConfig = "{\"pageSize\": 4, \"redoFlushByTrx\": 1, \"sharedModeEnable\" : 1}";
    int fd = 0;
    int *step = GetProcessSyncMemory(&fd);
    *step = 0;

    /**
     * @tc.steps: step1. create db in child process
     * @tc.expected: step1. ok
     */
    pid_t pid = fork();
    if (pid == 0) {
        // child process
        GRD_DB *db = nullptr;
        std::cout << "open in child" << std::endl;
        EXPECT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK);
        (*step)++;
        WaitCondition(step, 2);  // 2 means parent already call open and close mamny times
        std::cout << "close in child" << std::endl;
        EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
        exit(0);
    }

    WaitCondition(step, 1);
    /**
     * @tc.steps: step2. open and close 1000 times in parent
     * @tc.expected: step2. ok
     */
    GRD_DB *db = nullptr;
    int loopCnt = 1000;
    for (int i = 0; i < loopCnt; i++) {
        ASSERT_EQ(GRD_DBOpen(SHARED_MODE_DB, spConfig, GRD_DB_OPEN_CREATE, &db), GRD_OK) << " loop1 index = " << i;
        EXPECT_EQ(GRD_DBClose(db, GRD_DB_CLOSE), GRD_OK);
    }

    *step = 2;  // 2 means parent already call open and close mamny times
    waitpid(pid, nullptr, 0);
    ClearProcessSyncMemory(fd, step);
}
