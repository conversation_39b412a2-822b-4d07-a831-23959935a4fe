/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "grd_fast_vec_test_common.h"

using namespace std;

// Params start
Params::Params() : strategy(GsPD_FAST_VEC_STRATEGY_KV_CACHE)
{}

Params::Params(const char *inputName) : strategy(GsPD_FAST_VEC_STRATEGY_SINGLE)
{
    if (inputName != NULL) {
        char *newStr = new char[strlen(inputName) + 1];
        (void)strcpy_s(newStr, strlen(inputName) + 1, inputName);
        name = newStr;
    } else {
        name = NULL;
    }
}

Params::Params(vector<const char *> inputNames) : strategy(GsPD_FAST_VEC_STRATEGY_BATCH)
{
    names = new const char *[MAX_TMP_TABLE_NUM];
    for (size_t i = 0; i < inputNames.size(); ++i) {
        names[i] = inputNames[i];
    }
    num = inputNames.size();
}
Params::~Params()
{
    if (name) {
        delete[] name;
    }
    if (names) {
        delete[] names;
    }
}
// Params end

// Create start
CreateParams::CreateParams(const char *inputName, const GsPD_FastVecVectorTypeE inputType, const uint16_t inputDim)
    : Params(inputName), vecType(inputType), vecDim(inputDim)
{
    Initialize();
}
CreateParams::CreateParams(
    vector<const char *> inputNames, const GsPD_FastVecVectorTypeE inputType, const uint16_t inputDim)
    : Params(inputNames), vecType(inputType), vecDim(inputDim)
{
    Initialize();
}
CreateParams::CreateParams(const uint16_t inputGroups, const uint16_t inputLayers,
    const GsPD_FastVecVectorTypeE inputType, const uint16_t inputDim)
    : Params(), groups(inputGroups), layers(inputLayers), vecType(inputType), vecDim(inputDim)
{
    Initialize();
}
GsPD_FastVecCreateParamsT &CreateParams::getParams()
{
    return params;
}

void CreateParams::Initialize()
{
    params.strategy = strategy;
    params.type = GsPD_FAST_VEC_TABLE_KEY;
    params.indexGroupSize = GROUP_SIZE4;
    params.lvqBit = LVQ_BIT4;
    params.indexGroupStrategy = GsPD_FAST_VEC_GROUP_STRATEGY_AVG;
    params.vecType = vecType;
    params.vecDim = vecDim;
    params.tableName = name;
    params.tableNames = names;
    params.tableNamesNum = num;
    params.groups = groups;
    params.layers = layers;
}
// Create end

// Drop start
DropParams::DropParams(const char *name) : Params(name)
{
    Initialize();
}
DropParams::DropParams(vector<const char *> names) : Params(names)
{
    Initialize();
}
DropParams::DropParams() : Params()
{
    Initialize();
}
GsPD_FastVecDropParamsT &DropParams::getParams()
{
    return params;
}
void DropParams::Initialize()
{
    params.strategy = strategy;
    params.tableName = name;
    params.tableNames = names;
    params.tableNamesNum = num;
}
// Drop end

// Insert start
InsertParams::InsertParams(const char *inputName, const uint16_t inputVecDim, const uint16_t inputVecNum,
    const GsPD_FastVecVectorTypeE inputVecType)
    : Params(inputName), vecDim(inputVecDim), vecNum(inputVecNum), vecType(inputVecType)
{
    InitializeBuffer();
    Initialize();
}

InsertParams::InsertParams(const uint16_t layer, const uint16_t group, const GsPD_FastVecTableTypeE type,
    const uint16_t inputVecDim, const uint16_t inputVecNum, const GsPD_FastVecVectorTypeE inputVecType)
    : Params(),
      group(group),
      layer(layer),
      tableType(type),
      vecDim(inputVecDim),
      vecNum(inputVecNum),
      vecType(inputVecType)
{
    InitializeBuffer();
    Initialize();
}
InsertParams::~InsertParams()
{
    if (vecType == GsPD_FAST_VEC_FLOAT_32) {
        delete[] static_cast<float *>(params.vecBuffer);
#if defined(__aarch64__) && defined(__ARM_NEON) && (__ARM_ARCH >= 7)
    } else if (vecType == GsPD_FAST_VEC_FLOAT_16) {
        delete[] static_cast<float16_t *>(params.vecBuffer);
#endif
    }
}
GsPD_FastVecBatchInsertParamsT &InsertParams::getParams()
{
    return params;
}

void InsertParams::InitializeBuffer()
{
    if (vecType == GsPD_FAST_VEC_FLOAT_32) {
        float *vecBuffer = new float[vecNum * vecDim];
        for (uint32_t i = 0; i < vecNum; ++i) {
            for (uint32_t j = 0; j < vecDim; ++j) {
                vecBuffer[i * vecDim + j] = float(i);
            }
        }
        buffer = (void *)vecBuffer;
        vecSize = sizeof(float);
#if defined(__aarch64__) && defined(__ARM_NEON) && (__ARM_ARCH >= 7)
    } else if (vecType == GsPD_FAST_VEC_FLOAT_16) {
        float16_t *vecBuffer = new float16_t[vecNum * vecDim];
        for (uint32_t i = 0; i < vecNum; ++i) {
            for (uint32_t j = 0; j < vecDim; ++j) {
                vecBuffer[i * vecDim + j] = float16_t(i);
            }
        }
        buffer = (void *)vecBuffer;
        vecSize = sizeof(float16_t);
#endif
    } else {
        buffer = nullptr;
        vecSize = 0;
    }
}
void InsertParams::Initialize()
{
    params.tableName = name;
    params.group = group;
    params.layer = layer;
    params.type = tableType;
    params.vecBuffer = buffer;
    params.bufLen = vecNum * vecDim * vecSize;
    params.vecNum = vecNum;
}
// Insert end

// Search start
SearchParams::SearchParams(const char *inputName, const uint16_t inputVecDim, uint16_t inputHeadLen,
    uint16_t inputTailLen, uint16_t inputTotalLen, void *inputQueryVec)
    : Params(inputName),
      vecDim(inputVecDim),
      headLen(inputHeadLen),
      tailLen(inputTailLen),
      totalLen(inputTotalLen),
      queryVec(inputQueryVec)
{
    Initialize();
}
SearchParams::SearchParams(vector<const char *> names) : Params(names)
{
    Initialize();
}
SearchParams::SearchParams() : Params()
{
    Initialize();
}
SearchParams::~SearchParams()
{
    if (needFree) {
        free(params.queryVector);
    }
}
GsPD_FastVecSearchParamsT &SearchParams::getParams()
{
    return params;
}

void SearchParams::Initialize()
{
    void *vecBuffer = nullptr;
    if (queryVec == nullptr) {
        float *tmpBuf = (float *)malloc(sizeof(float) * vecDim);
        for (uint32_t i = 0; i < vecDim; ++i) {
            tmpBuf[i] = float(i);
        }
        vecBuffer = tmpBuf;
        needFree = true;
    } else {
        vecBuffer = queryVec;
        needFree = false;
    }
    params.group = 0;
    params.layer = 0;
    params.tableName = name;
    params.queryVector = vecBuffer;
    params.headLen = headLen;
    params.tailLen = tailLen;
    params.totalLen = totalLen;
    params.searchStrategy = GsPD_FAST_VEC_SEARCH_FILTER_KEY_TOKEN;
}
// Search end
