/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cmath>
#include <ctime>
#include <string>
#include <sys/mman.h>
#include <sys/sem.h>
#include <sys/stat.h>
#include <thread>
#include <mutex>
#include "gtest/gtest.h"

#include "db_file.h"
#include "db_mem_context.h"
#include "db_test_tool.h"
#include "db_process_mgr.h"
#include "grd_db_api.h"
#include "grd_document_api.h"
#include "grd_error.h"
#include "grd_kv_api.h"
#include "grd_resultset.h"
#include "operation_context.h"
#include "os_posix.h"
#include "parser.h"
#include "json_result_set.h"
#include "store_limits.h"
#include "store_type.h"
#include "stub.h"

using namespace std;
using namespace testing::ext;

#define TEST_DB_SHARED_MODE_CONFIG_STR "{\"sharedModeEnable\" : 1, \"redoFlushByTrx\" : 1}"

extern "C" {
Status SeCheckAndUnsetBackUpMark(uint32_t dbInstanceId);
}

const char *g_dbPathBackup = "./data";
static const char *g_mapFile = "./data/g_mapFile";
constexpr int BUFF_SIZE = 512 - 1;

static std::mutex g_mtx;

namespace {}  // namespace

static int g_busyThreadCnt = 0;
static int g_backupThreadCnt = 0;

class GRDDbBackupApiTest : public testing::Test {
public:
    static void SetUpTestCase(void)
    {}
    static void TearDownTestCase(void)
    {}
    void SetUp(void);
    void TearDown(void);
    int ModifyDataInPage(int modifyPos, char newVal, const char *modifyFile);
};

void GRDDbBackupApiTest::SetUp(void)
{
    InitStub();
    EXPECT_EQ(DbTestTool::RemoveDir(g_dbPathBackup), 0);
    EXPECT_EQ(DbTestTool::MakeDir(g_dbPathBackup), 0);
}

void GRDDbBackupApiTest::TearDown(void)
{
    ClearAllStub();
    EXPECT_EQ(DbTestTool::RemoveDir(g_dbPathBackup), 0);
}

static void UtOperateInsert(
    GRD_DB *db, const char *collectionName, const uint32_t insertKvNum, const char tag, uint32_t keyLen)
{
    for (uint32_t i = 0; i < insertKvNum; ++i) {
        string keyStr(keyLen, tag);
        keyStr += to_string(i);
        GRD_KVItemT key = {(void *)keyStr.c_str(), keyLen};
        uint32_t valueLen = 20 * keyLen;
        string valueStr(valueLen, tag);
        GRD_KVItemT value = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        ASSERT_EQ(GRD_KVPut(db, collectionName, &key, &value), GRD_OK);
    }
}

static void CheckDocResWithFilter(GRD_DB *db, const char *collectionName, const char *filter, int num)
{
    GRD_ResultSet *resultSet = nullptr;
    Query query = {filter, "{}"};
    ASSERT_EQ(GRD_FindDoc(db, collectionName, query, 0, &resultSet), GRD_OK);
    for (int i = 0; i < num; i++) {
        ASSERT_EQ(GRD_Next(resultSet), GRD_OK);
    }
    ASSERT_EQ(GRD_Next(resultSet), GRD_NO_DATA);
    ASSERT_EQ(GRD_FreeResultSet(resultSet), GRD_OK);
}

static void UtOperateInsertDocument(GRD_DB *db, const char *collectionName, const uint32_t insertKvNum)
{
    for (uint32_t i = 0; i < insertKvNum; ++i) {
        string doc("{\"_id\":\"");
        doc += to_string(insertKvNum + i);
        doc += "\",\"name\":\"document";
        doc += to_string(i);
        doc += "\",\"address\":\"shenzhen\",\"age\":18,\"friend\":{\"name\":\"David\",\"sex\":\"female\",\"age\":90}, \
            \"subject\":[\"math\", \"English\", \"music\"]}";
        ASSERT_EQ(GRD_OK, GRD_InsertDoc(db, collectionName, doc.c_str(), 0));
    }
}

static void ChecKvResWithKey(
    GRD_DB *db, const char *collectionName, const GRD_KVItemT *key, const GRD_KVItemT *expectValue, bool isExist = true)
{
    GRD_KVItemT tmpValue = {nullptr, 0};
    ASSERT_EQ(GRD_KVGet(db, collectionName, key, &tmpValue), isExist ? GRD_OK : GRD_NO_DATA);
    if (!isExist) {
        return;
    }
    ASSERT_EQ(expectValue->dataLen, tmpValue.dataLen);
    EXPECT_EQ(memcmp(expectValue->data, tmpValue.data, tmpValue.dataLen), GRD_OK);
    ASSERT_EQ(GRD_KVFreeItem(&tmpValue), GRD_OK);
}

static void UtOperateGet(GRD_DB *db, const char *collectionName, uint32_t count, const char tag, uint32_t keyLen)
{
    uint8_t firstKey = 123;
    uint16_t firstVal = 456;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    GRD_KVItemT value = {&firstVal, sizeof(uint16_t)};
    ChecKvResWithKey(db, collectionName, &key, &value, false);
    for (uint32_t i = 0; i < count; ++i) {
        string keyStr(keyLen, tag);
        keyStr += to_string(i);
        GRD_KVItemT key1 = {(void *)keyStr.c_str(), keyLen};
        uint32_t valueLen = 20 * keyLen;
        string valueStr(valueLen, tag);
        GRD_KVItemT value1 = {(void *)valueStr.c_str(), (uint32_t)valueStr.size()};
        ChecKvResWithKey(db, collectionName, &key1, &value1, true);
    }
}

static void UtOperateGetDocument(GRD_DB *db, const char *collectionName, uint32_t count)
{
    for (uint32_t i = 0; i < count; ++i) {
        string filter("{\"name\":\"document");
        filter += to_string(i);
        filter += "\"}";
        CheckDocResWithFilter(db, collectionName, filter.c_str(), 1);
    }
}

static void DbBackupRestore(const char *optionStr, const char *dbFilePath, const char *backupDbPath)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "table_KV_btree";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));

    const char *collectionName1 = "table_KV_hash";
    const uint32_t insertNum = 2;
    UtOperateInsert(db1, collectionName, insertNum, 'a', 100);

    const char *kvOptionHash = "{\"mode\":\"KV\", \"indexType\":\"hash\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName1, kvOptionHash, 0));
    UtOperateInsert(db1, collectionName1, insertNum, 'b', 100);
    UtOperateGet(db1, collectionName1, insertNum, 'b', 100);

    const char *collectionName3 = "table_DOCUMENT_hash";
    const char *documentOptionHash = "{\"mode\":\"DOCUMENT\", \"indexType\":\"hash\"}";
    ASSERT_EQ(GRD_NOT_SUPPORT, GRD_CreateCollection(db1, collectionName3, documentOptionHash, 0));

    const char *collectionName2 = "table_DOCUMENT_btree";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName2, "{}", 0));

    UtOperateInsertDocument(db1, collectionName2, insertNum);
    UtOperateGetDocument(db1, collectionName2, insertNum);
    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, backupDbPath, nullptr));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    /**
     * @tc.steps: step4. call GRD_DBRestore with db1
     * @tc.expected: step4. return GRD_OK.
     */
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, backupDbPath, nullptr));

    /**
     * @tc.steps: step5. call GRD_DBOpen with flags = GRD_DB_OPEN_ONLY
     * @tc.expected: step5. return GRD_OK.
     */
    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db2));

    /**
     * @tc.steps: step6. call GRD_KVPut and GRD_InsertDoc to check the db health
     * @tc.expected: step6. return GRD_OK.
     */
    UtOperateGet(db2, collectionName, insertNum, 'a', 100);
    UtOperateGet(db2, collectionName1, insertNum, 'b', 100);
    UtOperateGetDocument(db2, collectionName2, insertNum);

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup01
 * @tc.desc: Test backup old db kv table
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup01, TestSize.Level0)
{
    const char *dbFilePath = "./data/testFileDb01";
    const char *optionStr = "{\"mode\":\"KV\", \"indexType\":\"btree\"}";
    const char *dbFileBakBackup = "./data/testFileDbBackup01";
    DbBackupRestore(optionStr, dbFilePath, dbFileBakBackup);
}

int GRDDbBackupApiTest::ModifyDataInPage(int modifyPos, char newVal, const char *modifyFile)
{
    FILE *fp = fopen(modifyFile, "rb+");
    if (fp == nullptr) {
        printf("Failed to open file");
        return 1;
    }
    fseek(fp, modifyPos, SEEK_SET);
    fwrite(&newVal, sizeof(char), 1, fp);
    fclose(fp);
    return 0;
}

/**
 * @tc.name: DbBackup02
 * @tc.desc: Test backup old db and damage the backup's file
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup02, TestSize.Level0)
{
    const char *dbFilePath = "./data/testFileDbBackup02";
    const char *optionStr = "{\"mode\":\"KV\", \"indexType\":\"btree\"}";
    const char *dbFileBakBackup = "./data/testFileDbBackup02BAK";
    DbBackupRestore(optionStr, dbFilePath, dbFileBakBackup);

    /**
     * @tc.steps: step4. call GRD_DBRestore with db1
     * @tc.expected: step4. return GRD_OK.
     */
    ModifyDataInPage(50086, '0', dbFileBakBackup);
    ASSERT_EQ(GRD_DATA_CORRUPTED, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    /**
     * @tc.steps: step4. remove the backup db's file and try to restore db
     * @tc.expected: step4. return GRD_INVALID_ARGS.
     */
    ASSERT_EQ(GRD_OK, DbRemoveFile(dbFileBakBackup));
    ASSERT_EQ(GRD_FAILED_FILE_OPERATION, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));
}

/**
 * @tc.name: DbBackup03
 * @tc.desc: Test backup old db and try to restore db before open db.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup03, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    const char *dbFilePath = "./data/testFileDbBackup03";
    const char *dbFileBakBackup = "./data/testFileDbBackup03BAK";
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));

    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    const char *nonExistDbFile = "./datasssd/nonExistDbFile/sssasd";
    EXPECT_EQ(GRD_FAILED_FILE_OPERATION, GRD_DBBackup(db1, nonExistDbFile, nullptr));
    EXPECT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));

    /**
     * @tc.steps: step4. call GRD_DBRestore with db1, but didnot close db1
     * @tc.expected: step4. return GRD_OK.
     */
    ASSERT_EQ(GRD_DB_BUSY, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));
    ASSERT_EQ(GRD_FAILED_FILE_OPERATION, GRD_DBRestore(nonExistDbFile, dbFileBakBackup, nullptr));
}

typedef struct TagDtThreadCtxT {
    bool *isRunning;
    GRD_DB *db;
    const char *tableName;
    const char *dbFilePath;
    std::string backupDbFile;
    uint32_t index;
} DtThreadCtxT;

static Status SeCheckAndUnsetBackUpMarkMock(uint32_t dbInstanceId)
{
    (void)dbInstanceId;
    return GMERR_OK;
}

void *ThreadBackupDataMainWithSameConn(void *argVoid)
{
    DtThreadCtxT *arg = (DtThreadCtxT *)argVoid;
    while (!*arg->isRunning) {
    }
    printf("Start Thread: [%lu]\n", pthread_self());
    int32_t ret = GRD_DBBackup(arg->db, arg->backupDbFile.c_str(), nullptr);
    EXPECT_TRUE(ret == GRD_OK || ret == GRD_DB_BUSY);
    g_mtx.lock();
    if (ret == GRD_DB_BUSY) {
        g_busyThreadCnt++;
    } else if (ret == GRD_OK) {
        g_backupThreadCnt++;
    }
    g_mtx.unlock();
    return nullptr;
}

std::string ReplaceCharacter(const std::string &originalStr, char fromChar, char toChar)
{
    std::string result = originalStr;
    for (size_t i = 0; i < result.size(); ++i) {
        if (result[i] == fromChar) {
            result[i] = toChar;
        }
    }
    return result;
}

void *ThreadBackupDataMain(void *argVoid)
{
    DtThreadCtxT *arg = (DtThreadCtxT *)argVoid;
    while (!*arg->isRunning) {
    }
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    std::string dbFilePathStr = ".*data*testFileDbBackup04";
    const char *dbFileBakBackupStr = ".*data*testFileDbBackup04BAK";
    char toReplace = '*';
    char replacement = '/';

    std::string dbFilePath = ReplaceCharacter(dbFilePathStr, toReplace, replacement);
    std::string dbFileBakBackup = ReplaceCharacter(dbFileBakBackupStr, toReplace, replacement);

    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    int32_t ret = GRD_DBOpen(dbFilePath.c_str(), nullptr, flags, &db1);
    EXPECT_TRUE(ret == GRD_OK);
    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    (void)GRD_CreateCollection(db1, collectionName, optionStr, 0);
    ret = GRD_DBBackup(db1, dbFileBakBackup.c_str(), nullptr);
    EXPECT_TRUE(ret == GRD_OK || ret == GRD_DB_BUSY);
    g_mtx.lock();
    if (ret == GRD_DB_BUSY) {
        g_busyThreadCnt++;
    } else if (ret == GRD_OK) {
        g_backupThreadCnt++;
    }
    g_mtx.unlock();
    (void)GRD_DBClose(db1, GRD_DB_CLOSE);
    return nullptr;
}

void *ThreadDbOpenAndBackupDataMain(void *argVoid)
{
    DtThreadCtxT *arg = (DtThreadCtxT *)argVoid;
    while (!*arg->isRunning) {
    }
    std::string dbFilePathStr = ".*data*testFileDbBackup10";
    const char *dbFileBakBackupStr = ".*data*testFileDbBackup10BAK";
    char toReplace = '*';
    char replacement = '/';

    std::string dbFilePath = ReplaceCharacter(dbFilePathStr, toReplace, replacement);
    std::string dbFileBakBackup = ReplaceCharacter(dbFileBakBackupStr, toReplace, replacement);
    GRD_DB *db1 = nullptr;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath.c_str(), nullptr, GRD_DB_OPEN_CREATE, &db1));

    const char *optionStr = "{\"mode\":\"KV\"}";
    EXPECT_EQ(GRD_OK, GRD_CreateCollection(db1, arg->tableName, optionStr, 0));

    uint32_t keyLen = 512;
    uint32_t dataCount = 100;
    UtOperateInsert(db1, arg->tableName, dataCount, 'x', keyLen);
    int32_t ret = GRD_DBBackup(db1, dbFileBakBackup.c_str(), nullptr);
    EXPECT_TRUE(ret == GRD_DB_BUSY || ret == GRD_OK);
    g_mtx.lock();
    if (ret == GRD_DB_BUSY) {
        g_busyThreadCnt++;
    } else if (ret == GRD_OK) {
        g_backupThreadCnt++;
    }
    g_mtx.unlock();
    EXPECT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    return nullptr;
}

/**
 * @tc.name: DbBackup04
 * @tc.desc: Test backup old db and try to backup in multi thread.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup04, TestSize.Level0)
{
    g_busyThreadCnt = 0;
    g_backupThreadCnt = 0;
    uint32_t i = 0;
    pthread_t tid[10];
    DtThreadCtxT args[10];
    bool isRunning = false;
    uint32_t threadCnt = 10;
    for (i = 0; i < threadCnt; i++) {
        args[i].isRunning = &isRunning;
        pthread_create(tid + i, nullptr, ThreadBackupDataMain, (void *)(args + i));
    }
    isRunning = true;
    for (i = 0; i < threadCnt; i++) {
        pthread_join(tid[i], nullptr);
    }
    ASSERT_GE(g_busyThreadCnt, 1);
    ASSERT_GE(g_backupThreadCnt, 1);
    ASSERT_EQ(threadCnt, g_busyThreadCnt + g_backupThreadCnt);
    g_busyThreadCnt = 0;
    g_backupThreadCnt = 0;
}

/**
 * @tc.name: DbBackup05
 * @tc.desc: Test backup old db and try to backup in multi thread with same conn
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: sunhan
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup05, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    std::string dbFilePathStr = ".*data*testFileDbBackup05";
    const char *dbFileBakBackupStr = ".*data*testFileDbBackup05BAK";
    char toReplace = '*';
    char replacement = '/';

    std::string dbFilePath = ReplaceCharacter(dbFilePathStr, toReplace, replacement);
    std::string dbFileBakBackup = ReplaceCharacter(dbFileBakBackupStr, toReplace, replacement);
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath.c_str(), nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    g_busyThreadCnt = 0;
    g_backupThreadCnt = 0;
    uint32_t i = 0;
    pthread_t tid[10];
    DtThreadCtxT args[10];
    bool isRunning = false;
    uint32_t threadCnt = 10;
    for (i = 0; i < threadCnt; i++) {
        args[i].isRunning = &isRunning;
        args[i].db = db1;
        args[i].tableName = collectionName;
        args[i].backupDbFile = dbFileBakBackup;
        pthread_create(tid + i, nullptr, ThreadBackupDataMainWithSameConn, (void *)(args + i));
    }
    isRunning = true;
    for (i = 0; i < threadCnt; i++) {
        pthread_join(tid[i], nullptr);
    }
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    EXPECT_EQ(g_busyThreadCnt, 0);
    EXPECT_EQ(g_backupThreadCnt, threadCnt);
}

Status DbOpenFileStub(const char *fullPath, int32_t flag, uint32_t permission, int32_t *fd)
{
    return GMERR_MEMORY_OPERATE_FAILED;
}

/**
 * @tc.name: DbBackup06
 * @tc.desc: Test backup old db but file system is error.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup06, TestSize.Level0)
{
    (void)InitStub();
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    const char *dbFilePath = "./data/testFileDbBackup06ddd";
    const char *dbFileBakBackup = "./data/testFileDbBackup06BAK";
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));

    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    int stubIndex = SetStubC((void *)DbOpenFile, (void *)DbOpenFileStub);
    EXPECT_GE(stubIndex, 0);

    ASSERT_EQ(GRD_FAILED_MEMORY_ALLOCATE, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    (void)ClearAllStub();
}

/**
 * @tc.name: DbBackup07
 * @tc.desc: Test backup old db after open db as GRD_DB_OPEN_ONLY flags
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup07, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    const char *dbFilePath = "./data/testFileDbBackup07";
    const char *dbFileBakBackup = "./data/testFileDbBackup07BAK";
    const char *configStr = R"({"sharedModeEnable" : 1, "redoFlushByTrx" : 1})";
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr, flags, &db2));

    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    EXPECT_EQ(GRD_PERMISSION_DENIED, GRD_DBBackup(db2, dbFileBakBackup, nullptr));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));

    GRD_DB *db3 = nullptr;
    flags = GRD_DB_OPEN_SHARED_READ_ONLY;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr, flags, &db3));

    EXPECT_EQ(GRD_PERMISSION_DENIED, GRD_DBBackup(db3, dbFileBakBackup, nullptr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db3, GRD_DB_CLOSE));

    GRD_DB *db4 = nullptr;
    flags = GRD_DB_OPEN_CREATE;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr, flags, &db4));
    EXPECT_EQ(GRD_OK, GRD_DBBackup(db4, dbFileBakBackup, nullptr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db4, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup08
 * @tc.desc: Test backup old db with preset a data and restore it then check a data.(small data)
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup08, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup08";
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    uint32_t keyLen = 2;
    UtOperateInsert(db1, collectionName, 100, 'a', keyLen);
    UtOperateGet(db1, collectionName, 100, 'a', keyLen);

    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    const char *dbFileBakBackup = "./data/testFileDbBackup08BAK";
    EXPECT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    /**
     * @tc.steps: step4. call GRD_DBRestore with db1
     * @tc.expected: step4. return GRD_OK.
     */
    EXPECT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    /**
     * @tc.steps: step5. call GRD_DBOpen with flags = GRD_DB_OPEN_ONLY
     * @tc.expected: step5. return GRD_OK.
     */
    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db2));

    /**
     * @tc.steps: step6. call GRD_KVPut and GRD_InsertDoc to check the db health
     * @tc.expected: step6. return GRD_OK.
     */
    UtOperateInsert(db2, collectionName, 100, 'b', keyLen);

    UtOperateGet(db2, collectionName, 100, 'a', keyLen);
    UtOperateGet(db2, collectionName, 100, 'b', keyLen);

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup09
 * @tc.desc: Test backup old db with preset a data and restore it then check a data.(big key and big value)
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup09, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup09";
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    uint32_t keyLen = 512;
    uint32_t dataCount = 100;
    UtOperateInsert(db1, collectionName, dataCount, 'c', keyLen);
    UtOperateGet(db1, collectionName, dataCount, 'c', keyLen);

    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    const char *dbFileBakBackup = "./data/testFileDbBackup09BAK";
    EXPECT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    /**
     * @tc.steps: step4. call GRD_DBRestore with db1
     * @tc.expected: step4. return GRD_OK.
     */
    EXPECT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    /**
     * @tc.steps: step5. call GRD_DBOpen with flags = GRD_DB_OPEN_ONLY
     * @tc.expected: step5. return GRD_OK.
     */
    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db2));

    /**
     * @tc.steps: step6. call GRD_KVPut and GRD_InsertDoc to check the db health
     * @tc.expected: step6. return GRD_OK.
     */
    UtOperateInsert(db2, collectionName, dataCount, 'd', keyLen);

    UtOperateGet(db2, collectionName, dataCount, 'c', keyLen);
    UtOperateGet(db2, collectionName, dataCount, 'd', keyLen);

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup11
 * @tc.desc: Test dbOpen and backup/restore with some invalid param
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup11, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup11";
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step3. call GRD_DBBackup with wrong backupPath
     * @tc.expected: step3. return GRD_INVALID_ARGS.
     */
    EXPECT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, dbFilePath, nullptr));
    EXPECT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(nullptr, "./data/backup", nullptr));
    EXPECT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, nullptr, nullptr));
    EXPECT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, "", nullptr));
    const char *noExistDbFilePath = "./noExistFolder/testFileDbBackup11";
    EXPECT_EQ(GRD_FAILED_FILE_OPERATION, GRD_DBBackup(db1, noExistDbFilePath, nullptr));

    const char *dbFileBakBackup = "./data/testdbBackup011BAK";
    EXPECT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));

    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    EXPECT_EQ(GRD_INVALID_ARGS, GRD_DBRestore(dbFilePath, dbFilePath, nullptr));
    EXPECT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db2));

    GRD_KVItemT tmpValue = {nullptr, 0};
    uint8_t firstKey = 123;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    ASSERT_EQ(GRD_UNDEFINED_TABLE, GRD_KVGet(db2, collectionName, &key, &tmpValue));
    ASSERT_EQ(GRD_OK, GRD_KVFreeItem(&tmpValue));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup12
 * @tc.desc: Test insert 1G data and delete 0.9G, and calculate db's fileSize, and backup db, and check db's fileSize
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup12, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup12";
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step2. call GRD_CreateCollection with db1
     * @tc.expected: step2. return GRD_OK.
     */
    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    uint32_t keyLen = 512;
    uint32_t dataCount = 100;
    UtOperateInsert(db1, collectionName, dataCount, 'c', keyLen);
    UtOperateGet(db1, collectionName, dataCount, 'c', keyLen);

    /**
     * @tc.steps: step3. call GRD_DBBackup with db1
     * @tc.expected: step3. return GRD_OK.
     */
    const char *dbFileBakBackup = "./data/testFileDbBackup09BAK";
    EXPECT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    /**
     * @tc.steps: step4. call GRD_DBRestore with db1
     * @tc.expected: step4. return GRD_OK.
     */
    EXPECT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    /**
     * @tc.steps: step5. call GRD_DBOpen with flags = GRD_DB_OPEN_ONLY
     * @tc.expected: step5. return GRD_OK.
     */
    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db2));

    /**
     * @tc.steps: step6. call GRD_KVPut and GRD_InsertDoc to check the db health
     * @tc.expected: step6. return GRD_OK.
     */
    UtOperateInsert(db2, collectionName, dataCount, 'd', keyLen);

    UtOperateGet(db2, collectionName, dataCount, 'c', keyLen);
    UtOperateGet(db2, collectionName, dataCount, 'd', keyLen);

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup13
 * @tc.desc: Test concurrent for multi process
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: sunhan
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup13, TestSize.Level0)
{
    const char *dbFilePath = "./data/testFileDbBackup13";
    GRD_DB *db = nullptr;
    EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, TEST_DB_SHARED_MODE_CONFIG_STR, GRD_DB_OPEN_CREATE, &db));

    const char *optionStr = "{\"mode\":\"KV\"}";
    EXPECT_EQ(GRD_OK, GRD_CreateCollection(db, "test_table_for_back_up", optionStr, 0));

    uint32_t keyLen = 512;
    uint32_t dataCount = 100;
    UtOperateInsert(db, "test_table_for_back_up", dataCount, 'x', keyLen);
    EXPECT_EQ(GRD_OK, GRD_DBClose(db, GRD_DB_CLOSE));
    db = nullptr;
    int fd = open(g_mapFile, O_RDWR | O_CREAT, 0666);
    ASSERT_GT(fd, -1);
    ASSERT_GT(ftruncate(fd, sizeof(int) * 2), -1);
    int *num = (int *)mmap(nullptr, sizeof(int) * 2, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    ASSERT_NE(num, MAP_FAILED);
    for (int i = 0; i < 2; i++) {
        num[i] = 0;
    }
    std::vector<pid_t> pids;
    for (int i = 0; i < 8; i++) {
        pid_t pid = fork();
        ASSERT_GE(pid, 0);
        if (pid == 0) {
            int stubIndex = SetStubC((void *)SeCheckAndUnsetBackUpMark, (void *)SeCheckAndUnsetBackUpMarkMock);
            EXPECT_GE(stubIndex, 0);
            GRD_DB *dbBackup = nullptr;
            EXPECT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, TEST_DB_SHARED_MODE_CONFIG_STR, GRD_DB_OPEN_CREATE, &dbBackup));
            const char *dbFileBakBackup = "./data/testFileDbBackup13BAK";
            int32_t ret = GRD_DBBackup(dbBackup, dbFileBakBackup, nullptr);
            EXPECT_TRUE(ret == GRD_DB_BUSY || ret == GRD_OK);
            if (ret == GRD_OK) {
                num[0]++;
            } else if (ret == GRD_DB_BUSY) {
                num[1]++;
            }
            EXPECT_EQ(GRD_OK, GRD_DBClose(dbBackup, GRD_DB_CLOSE));
            dbBackup = nullptr;
            ClearStub(stubIndex);
            exit(0);
        }
        pids.push_back(pid);
    }
    for (auto pid : pids) {
        waitpid(pid, nullptr, 0);
    }
    EXPECT_TRUE(num[0] >= 1);
    ASSERT_GT(munmap(num, sizeof(int) * 2), -1);
    close(fd);
}

/**
 * @tc.name: DbBackup14
 * @tc.desc: Test dbOpen and backup/restore with some invalid hexPassword1
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup14, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup14";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    /**
     * @tc.steps: step3. call GRD_DBBackup with wrong backupPath
     * @tc.expected: step3. return GRD_INVALID_ARGS.
     */
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, dbFilePath, nullptr));
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(nullptr, "./data/backup", nullptr));
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, nullptr, nullptr));
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, "", nullptr));
    const char *noExistDbFilePath = "./noExistFolder/testFileDbBackup14";
    ASSERT_EQ(GRD_FAILED_FILE_OPERATION, GRD_DBBackup(db1, noExistDbFilePath, nullptr));

    const char *dbFileBakBackup = "./data/testdbBackup014BAK";
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));
    GRD_CipherInfoT cipherInfo = {.hexPassword = "abc123"};
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));

    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBRestore(dbFilePath, dbFilePath, nullptr));
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));
    GRD_CipherInfoT cipherInfo1 = {.hexPassword = "abc123"};
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, &cipherInfo1));

    GRD_CipherInfoT wrongCipherInfo = {.hexPassword = "abc1234"};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackup, &wrongCipherInfo));

    wrongCipherInfo = {.hexPassword = "Abc123"};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackup, &wrongCipherInfo));

    wrongCipherInfo = {.hexPassword = "abc12"};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackup, &wrongCipherInfo));

    GRD_DB *db2 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    const char *configStr2 = "{\"pageSize\": 4, \"redoFlushByTrx\":1, \"isEncrypted\":1, \"hexPassword\":\"abc123\"}";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr2, flags, &db2));

    GRD_KVItemT tmpValue = {nullptr, 0};
    uint8_t firstKey = 123;
    GRD_KVItemT key = {&firstKey, sizeof(uint8_t)};
    ASSERT_EQ(GRD_UNDEFINED_TABLE, GRD_KVGet(db2, collectionName, &key, &tmpValue));
    ASSERT_EQ(GRD_OK, GRD_KVFreeItem(&tmpValue));

    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup15
 * @tc.desc: Test dbOpen and backup/restore with some invalid hexPassword2
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup15, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup15";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    const char *dbFileBakBackup = "./data/testdbDbBackup15BAK";
    char hexPwd[BUFF_SIZE] = {0};
    EXPECT_EQ(EOK, strcat_s(hexPwd, BUFF_SIZE, "abc123"));
    GRD_CipherInfoT cipherInfo = {.hexPassword = hexPwd};
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));

    const char *dbFileBakBackupWithoutPwd = "./data/testdbDbBackup15BAKWithoutPwd";
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackupWithoutPwd, nullptr));

    const char *collectionName = "collectionMem";
    const char *optionStr = "{\"mode\":\"KV\"}";
    ASSERT_EQ(GRD_OK, GRD_CreateCollection(db1, collectionName, optionStr, 0));
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackupWithoutPwd, nullptr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    char hexPwd1[BUFF_SIZE] = {0};
    EXPECT_EQ(EOK, strcat_s(hexPwd1, BUFF_SIZE, "abxc123"));
    GRD_CipherInfoT cipherInfo1 = {.hexPassword = hexPwd1};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackupWithoutPwd, &cipherInfo1));

    const char *dbFileBakBackupWithoutPwdxx = "./data/testdbDbBackup15BAKWithoutPwdxx";
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFileBakBackupWithoutPwdxx, dbFileBakBackupWithoutPwd, nullptr));

    GRD_DB *db3 = nullptr;
    flags = GRD_DB_OPEN_ONLY;
    const char *configStr3 = "{\"pageSize\": 4, \"redoFlushByTrx\":1, \"isEncrypted\":0}";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFileBakBackupWithoutPwdxx, configStr3, flags, &db3));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db3, GRD_DB_CLOSE));

    EXPECT_EQ(EOK, strcat_s(hexPwd1, BUFF_SIZE, "abc1234"));
    GRD_CipherInfoT wrongCipherInfo = {.hexPassword = hexPwd1};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackupWithoutPwd, &wrongCipherInfo));

    EXPECT_EQ(EOK, strcat_s(hexPwd1, BUFF_SIZE, "Abc123"));
    wrongCipherInfo = {.hexPassword = hexPwd1};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackupWithoutPwd, &wrongCipherInfo));

    EXPECT_EQ(EOK, strcat_s(hexPwd1, BUFF_SIZE, "abc12"));
    wrongCipherInfo = {.hexPassword = hexPwd1};
    ASSERT_EQ(GRD_PASSWORD_UNMATCHED, GRD_DBRestore(dbFilePath, dbFileBakBackupWithoutPwd, &wrongCipherInfo));

    char hexPwd2[BUFF_SIZE] = {0};
    EXPECT_EQ(EOK, strcat_s(hexPwd2, BUFF_SIZE, "\0"));
    wrongCipherInfo = {.hexPassword = hexPwd2};
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBRestore(dbFilePath, dbFileBakBackupWithoutPwd, &wrongCipherInfo));

    cipherInfo = {.hexPassword = "abc123"};
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, &cipherInfo));
}

/**
 * @tc.name: DbBackup16
 * @tc.desc: Test dbOpen and backup/restore with overlimit hexPassword
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup16, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbDbBackup16";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, nullptr, flags, &db1));

    string overlimitHexPwd = "";
    for (int i = 0; i < BUFF_SIZE + 1; i++) {
        overlimitHexPwd += "q";
    }
    const char *dbFileBakBackup = "./data/testdbDbDbBackup16BAK";
    GRD_CipherInfoT cipherInfo = {.hexPassword = overlimitHexPwd.c_str()};
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));
    cipherInfo.hexPassword = "abc12433";
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));

    string maxLenHexPwd = "";
    for (int i = 0; i < BUFF_SIZE; i++) {
        maxLenHexPwd += "x";
    }
    cipherInfo.hexPassword = maxLenHexPwd.c_str();
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));

    const char *dbFileBakBackup2 = "./data/testdbDbDbBackupxxx16BAK2";
    cipherInfo.hexPassword = nullptr;
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup2, &cipherInfo));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    cipherInfo = {.hexPassword = overlimitHexPwd.c_str()};
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBRestore(dbFilePath, dbFileBakBackup, &cipherInfo));

    cipherInfo.hexPassword = maxLenHexPwd.c_str();
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, &cipherInfo));
}

/**
 * @tc.name: DbBackup17
 * @tc.desc: Test dbOpen and backup/restore with some hexPassword
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: wuyichen
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup17, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *dbFilePath = "./data/testFileDbBackup17";
    const char *configStr1 = "{\"pageSize\": 8, \"redoFlushByTrx\":1, \"isEncrypted\":1, \"hexPassword\":\"abc123\"}";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr1, flags, &db1));

    const char *dbFileBakBackup = "./data/testdbDbBackup17BAK";
    char hexPwd[BUFF_SIZE] = {0};
    EXPECT_EQ(EOK, strcat_s(hexPwd, BUFF_SIZE, "abc123"));
    GRD_CipherInfoT cipherInfo = {.hexPassword = hexPwd};
    /**
     * @tc.steps: step2. call GRD_DBBackup with cipherInfo
     * @tc.expected: step2. return GRD_OK.
     */
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));

    /**
     * @tc.steps: step3. call GRD_DBBackup with cipherInfo = nullptr
     * @tc.expected: step3. return GRD_OK.
     */
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, nullptr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));

    /**
     * @tc.steps: step4. call GRD_DBRestore with cipherInfo = nullptr
     * @tc.expected: step4. return GRD_OK.
     */
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, nullptr));

    /**
     * @tc.steps: step5. call GRD_DBOpen with isEncrypted = false
     * @tc.expected: step5. return GRD_OK.
     */
    GRD_DB *db2 = nullptr;
    const char *configStr2 = "{\"pageSize\": 8, \"redoFlushByTrx\":1, \"isEncrypted\":0}";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr2, flags, &db2));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}

/**
 * @tc.name: DbBackup18
 * @tc.desc: Test dbOpen and backup/restore with overlimit hexPassword
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.009.001
 * @tc.author: pengweijun
 */
HWTEST_F(GRDDbBackupApiTest, DbBackup18, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_DBOpen with configStr = nullptr
     * @tc.expected: step1. return GRD_OK.
     */
    GRD_DB *db1 = nullptr;
    int flags = GRD_DB_OPEN_CREATE;
    const char *configStr = "{\"pageSize\": 8, \"redoFlushByTrx\":1, \"isEncrypted\":0}";
    const char *dbFilePath = "./data/testFileDbDbBackuiii18";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr, flags, &db1));

    const char *dbFileBakBackup = "./data/testFileDbDbBackuiii18BAK";
    GRD_CipherInfoT cipherInfo = {.hexPassword = "xxxxxxxx"};
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, &cipherInfo));

    GRD_CipherInfoT *cipherPtr = (GRD_CipherInfoT *)malloc(sizeof(GRD_CipherInfoT));
    cipherPtr->hexPassword = "xxxxxxxx";
    ASSERT_EQ(GRD_OK, GRD_DBBackup(db1, dbFileBakBackup, cipherPtr));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db1, GRD_DB_CLOSE));
    cipherPtr->hexPassword = "xxxxxxxx";
    ASSERT_EQ(GRD_OK, GRD_DBRestore(dbFilePath, dbFileBakBackup, cipherPtr));
    free(cipherPtr);
    GRD_DB *db2 = nullptr;
    ASSERT_EQ(GRD_INVALID_ARGS, GRD_DBOpen(dbFilePath, configStr, flags, &db2));
    const char *configStr2 = "{\"pageSize\": 8, \"redoFlushByTrx\":1, \"isEncrypted\":1, \"hexPassword\":\"xxxxxxxx\"}";
    ASSERT_EQ(GRD_OK, GRD_DBOpen(dbFilePath, configStr2, flags, &db2));
    ASSERT_EQ(GRD_OK, GRD_DBClose(db2, GRD_DB_CLOSE));
}
