/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <iostream>
#include <string>
#include <thread>
#include "gtest/gtest.h"
#include "grd_gql_com.h"
#include "grd_gql_api.h"
#include "db_test_tool.h"

using namespace std;
using namespace testing::ext;

class GrdGqlDelete : public GrdGqlCommon {
    void SetUp() override
    {
        GrdGqlCommon::SetUp();
    }

    void TearDown() override
    {
        GrdGqlCommon::TearDown();
    }

protected:
    void PrepareGraphData();
    void CheckDeleteResult(string checkGql, uint32_t exptColCnt, uint32_t exptRowCnt, string deleteGql);
};

void GrdGqlDelete::PrepareGraphData()
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 10;
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

        gqlStr = GetInsertEventGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    }
    string gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_2'}) "
                    "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_2'}), (b: Person {name: 'name_3'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_3'}), (b: Person {name: 'name_4'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_4'}), (b: Person {name: 'name_5'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_5'}), (b: Person {name: 'name_6'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_6'}), (b: Person {name: 'name_7'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_7'}), (b: Person {name: 'name_8'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_8'}), (b: Person {name: 'name_7'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_8'}), (b: Person {name: 'name_9'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_10'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_3'}), (b: Person {name: 'name_9'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_9'}), (b: Person {name: 'name_1'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_9'}), (b: Person {name: 'name_10'})"
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
}

void GrdGqlDelete::CheckDeleteResult(string checkGql, uint32_t exptColCnt, uint32_t exptRowCnt, string deleteGql)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    uint32_t len = (uint32_t)(checkGql.size() + 1);
    GRD_StmtT *stmt = NULL;
    int32_t ret = GRD_GqlPrepare(conn, checkGql.c_str(), len, &stmt, NULL);
    ASSERT_EQ(GRD_OK, ret);
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlCheckActQryResultSetSize(data, exptColCnt, exptRowCnt, deleteGql);
    UtGrdGqlClearActQryResultSet(data);
    ret = GRD_GqlFinalize(stmt);
    ASSERT_EQ(GRD_OK, ret);
}

/**
 * usecase design:
 * 1. delete node
 * 2. delete edge
 * 3. delete edge and node
 * 4. match delete with where
 */
HWTEST_F(GrdGqlDelete, TestGqlNormalDelete, TestSize.Level0)
{
    PrepareGraphData();

    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    // single node
    string gqlStr = "MATCH (a: Person {name: 'name_1'}) DETACH DELETE a;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    string checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);

    // node not exist
    gqlStr = "MATCH (a: Person {name: 'name_11'}) DETACH DELETE a;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);

    gqlStr = GetInsertPersonGql(1, true);
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_2'}) "
             "INSERT (a)-[:直系亲属 {roletype: 'dad'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 11, gqlStr);

    gqlStr = "MATCH (a: Person {name: 'name_1'})-[r]->(b: Person {name: 'name_123'}) DETACH DELETE a, b;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 11, gqlStr);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) DETACH DELETE a;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);

    // single edge
    gqlStr = "Match (a: Person {name: 'name_2'})-[r:直系亲属 {roletype:'dad'}]->(b:Person {name: 'name_3'})"
             "DETACH DELETE r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);

    // edge not exist
    gqlStr = "Match (a: Person {name: 'name_2'})-[r:直系亲属]->(b:Person {name: 'name_1'}) DETACH DELETE r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_3'})-[r:直系亲属]->(b:Person)-[s]->(c:Person {name: 'name_5'}) \
            DETACH DELETE a, r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 8, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 7, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_7'})<-[r:直系亲属]-(b:Person)-[s:直系亲属]->(c:Person {name:'name_9'}) \
            DETACH DELETE b;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 7, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 4, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_5'})-[r]->(b:Person)-[s:直系亲属]->(c:Person {name: 'name_7'}) \
        DETACH DELETE a, s;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 6, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 1, gqlStr);

    gqlStr = "Match (a:Person) Where a.name = 'name_6' \
    DETACH DELETE a;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 5, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 1, gqlStr);

    gqlStr = "Match (a:Person)-[r:直系亲属]->(b:Person) Where a.name = 'name_9' and b.name = 'name_10' \
        DETACH DELETE a, r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 4, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);

    gqlStr = "Match (a:Person)-[r:直系亲属]->(b:Person) Where a.name > 'name_3' \
        DETACH DELETE a, r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 4, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);

    gqlStr = "Match (a:Person) Where a.name > 'name_3' \
        DETACH DELETE a;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 2, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);

    gqlStr = "Match (a:Person) DETACH DELETE a";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);

    PrepareGraphData();
    gqlStr = "Match (a: Person {name: 'name_4'})-[]->{0,2}(c:Person) DETACH DELETE c";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 7, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_1'})-[]->{1,2}(c:Person) where c.name > 'name_2' DETACH DELETE c";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 6, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 7, gqlStr);

    // duplicate delete
    gqlStr = "Match (a: Person {name: 'name_1'})-[]->{1,2}(c:Person) where c.name > 'name_2' DETACH DELETE c";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 6, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 7, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_7'})-[]->{1,2}(c:Person) DETACH DELETE a, c, a";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 3, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 2, gqlStr);

    gqlStr = "Match (a:Person) DETACH DELETE a";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);

    PrepareGraphData();
    gqlStr = "Match (a: Person {name: 'name_4'})-[]->{0,2}(c:Person) set a.name = 'name_111'";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 13, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_4'})-[]->{0,2}(c:Person) DETACH DELETE c";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 13, gqlStr);

    gqlStr = "Match (a: Person {name: 'name_111'})-[]->{0,2}(c:Person) DETACH DELETE c";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 7, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 1, 9, gqlStr);

    gqlStr = "Match (a:Person) DETACH DELETE a";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);
    checkStr = "MATCH (a: Person)-[r:直系亲属]->(b:Person) RETURN r;";
    CheckDeleteResult(checkStr, 0, 0, gqlStr);

    PrepareGraphData();
    gqlStr = "Match (a: Person {name: 'name_1'})-[r:直系亲属]->(b:Person) WHERE r.roletype = 'dad'"
             "DETACH DELETE r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);

    gqlStr = "Match (a: Person)-[r:直系亲属]->(b:Person) WHERE r.roletype = 'dad'"
             "DETACH DELETE r;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    checkStr = "MATCH (a: Person) RETURN a;";
    CheckDeleteResult(checkStr, 1, 10, gqlStr);
}

/**
 * usecase design:
 * 1. variable not exist
 * 2. delete path
 * 3. delete other expression
 */
HWTEST_F(GrdGqlDelete, TestGqlErrorDelete, TestSize.Level0)
{
    PrepareGraphData();

    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_UNDEFINED_OBJECT, GRD_INVALID_ARGS};
    string gqlStr = "Match (a: Person {name: 'name_2'})-[r]->(b:Person {name: 'name_3'}) DETACH DELETE c;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    ctx = {GRD_FEATURE_NOT_SUPPORTED, GRD_INVALID_ARGS};
    gqlStr = "Match p = (a: Person {name: 'name_2'})-[r]->(b:Person {name: 'name_3'}) DETACH DELETE p;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    ctx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    gqlStr = "Match p = (a: Person {name: 'name_2'})-[r]->(b:Person {name: 'name_3'}) DETACH DELETE 1 + 1;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    ctx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    gqlStr = "Match (a: Person {name: 'name_5'})-[r:直系亲属]->{0,2}(c:Person) DETACH DELETE r";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    ctx = {GRD_FEATURE_NOT_SUPPORTED, GRD_INVALID_ARGS};
    gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_123'}) DETACH DELETE a, b;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
}

HWTEST_F(GrdGqlDelete, TestGqlNoLabelDelete_001, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};

    vector<string> gqlStrs = {
        "MATCH (a) DETACH DELETE a",                      // Person + Event + edges = 10 + 10 + 13 = 33
        "MATCH (a)-[e]->(b) DETACH DELETE a",             // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a)-[e]->(b) DETACH DELETE a,e",           // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a)-[e1]->(b)-[e2]->(c) DETACH DELETE a",  // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a)-[e]->{1,2}(b) DETACH DELETE a",        // 匹配不上Event，出点没有10，点共9个，边13个
        "MATCH (a) WHERE a.title IS NULL DETACH DELETE a",        // 所有Person + 边 = 10 + 13 = 23
        "MATCH (a) WHERE a.title IS NOT NULL DETACH DELETE a",    // 所有EVENT
        "MATCH (a where a.name > 'name_1') DETACH DELETE a",      // 9个person + 13条边
        "MATCH (a) where a.name > 'name_1' DETACH DELETE a",      // 9个person + 13条边
        "MATCH (a) where a.invalid IS NULL DETACH DELETE a",      // 10个person + 13条边 + 10个Event
        "MATCH (a) where a.invalid IS NOT NULL DETACH DELETE a",  // 0
        "MATCH (a)-[e1]->(b)-[e2]->(c) DETACH DELETE a, b, c",  // 7，8为自环，测试同时删除。所有Person + 边 = 23
        "MATCH (a)-[e]->(b) DETACH DELETE a, e, b",             // 多条相同边。所有Person + 边 + 1 = 24
    };
    vector<vector<uint32_t>> resultNums = {{0u, 0u}, {11u, 0u}, {11u, 0u}, {11u, 0u}, {11u, 0u}, {10u, 0u}, {10u, 13u},
        {11u, 0u}, {11u, 0u}, {0u, 0u}, {20u, 13u}, {10u, 0u}, {10u, 0u}};
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        PrepareGraphData();

        if (i == gqlStrs.size() - 1) {
            string gqlStr =
                "MATCH (a: Person {name: 'name_9'}), (b: Person {name: 'name_10'}) INSERT (a)-[:直系亲属]->(b);";
            GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
        }

        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

        string checkStr = "MATCH (a) RETURN a;";
        if (resultNums[i][0] == 0) {
            CheckDeleteResult(checkStr, 0, 0, gqlStr);
        } else {
            CheckDeleteResult(checkStr, 1, resultNums[i][0], gqlStr);
        }
        checkStr = "MATCH (a)-[r:直系亲属]->(b) RETURN r;";
        if (resultNums[i][1] == 0) {
            CheckDeleteResult(checkStr, 0, 0, gqlStr);
        } else {
            CheckDeleteResult(checkStr, 1, resultNums[i][1], gqlStr);
        }

        const char *gql = "MATCH (a) DETACH DELETE a";
        GrdGqlExecCmdWithStatus(conn, gql, ctx);
        i++;
    }
}

HWTEST_F(GrdGqlDelete, TestGqlNoLabelDelete_002, TestSize.Level0)
{
    UtGrdGqlClearActQryResultSet(NULL);
    const char *gql = "DROP GRAPH IF EXISTS myGraph;";
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    GrdGqlExecCmdWithStatus(conn, gql, ctx);

    vector<string> gqlStrs = {
        "MATCH (a) DETACH DELETE a",
        "MATCH (a)-[e]->(b) DETACH DELETE a",
        "MATCH (a)-[e]->(b) DETACH DELETE a,e",
        "MATCH (a)-[e1]->(b)-[e2]->(c) DETACH DELETE a",
        "MATCH (a)-[e]->{1,2}(b) DETACH DELETE a",
    };

    vector<GrdGqlExecCmdCtxT> expectedRes = {
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectedRes[i]);
        i++;
    }
}

HWTEST_F(GrdGqlDelete, SpecialCase, TestSize.Level0)
{
    string gql = "Drop Graph myGraph";
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);
    gql = "CREATE GRAPH myGraph {       \
        (a:Staff {name STRING, age INT, salary DOUBLE}),       \
        (a)-[:FRIENDS ]->(a)}";
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);

    gql = "DETACH DELETE a";
    ctx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);

    gql = "MATCH () DETACH DELETE a";
    ctx = {GRD_UNDEFINED_OBJECT, GRD_INVALID_ARGS};
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);

    gql = "MATCH ()-[a]->() DETACH DELETE a";
    ctx = {GRD_OK, GRD_OK};
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);

    gql = "MATCH ()-[]->(a) DETACH DELETE a";
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);

    gql = "MATCH (a)-[]->() DETACH DELETE a";
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);
}

static void GrdGqlDeletePrepareDataInSpecialCase(GRD_DB *conn)
{
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    string gql = "Drop Graph myGraph";
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);
    gql = "CREATE GRAPH myGraph {(a:Staff {name STRING, age INT, salary DOUBLE}), (a)-[:FRIENDS ]->(a)}";
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);

    gql = "INSERT (:Staff {name: 'a', age: 10, salary: 1000.0})";
    GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);
    uint32_t edgeCount = 100;
    for (uint32_t i = 0; i < edgeCount; i++) {
        gql = "INSERT (:Staff {name: 'b_" + to_string(i) + "', age: 10, salary: 1000.0})";
        GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);
        gql = "MATCH (a:Staff {name: 'a'}), (b:Staff {name: 'b_" + to_string(i) + "'}) INSERT (a)-[e:FRIENDS]->(b)";
        GrdGqlExecCmdWithStatus(conn, gql.c_str(), ctx);
    }
}

HWTEST_F(GrdGqlDelete, SpecialCaseWithDMLAndDQL, TestSize.Level3)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlDeletePrepareDataInSpecialCase(conn);
    bool isRunning = false;
    auto queryTask = [&isRunning](GRD_DB *outConn) {
        while (!isRunning) {
        };
        string queryGql = "MATCH (a:Staff)-[r:FRIENDS]->(b:Staff) RETURN r";
        GRD_StmtT *stmt = nullptr;
        int32_t ret = GRD_GqlPrepare(outConn, queryGql.c_str(), queryGql.size() + 1, &stmt, NULL);
        ASSERT_EQ(GRD_OK, ret);
        ret = GRD_GqlStep(stmt);
        ASSERT_EQ(GRD_OK, ret);
        uint32_t sleepTime = 5u;
        sleep(sleepTime);
        while (ret = GRD_GqlStep(stmt), ret == GRD_OK) {
        }
        ASSERT_EQ(GRD_NO_DATA, ret);
        ret = GRD_GqlFinalize(stmt);
        ASSERT_EQ(GRD_OK, ret);
    };
    auto deleteTask = [&isRunning](GRD_DB *outConn) {
        while (!isRunning) {
        };
        uint32_t sleepTime = 1u;
        sleep(sleepTime);
        string deleteGql = "MATCH (a:Staff {name: 'a'})-[r]->(b) DETACH DELETE r;";
        GrdGqlExecCmdWithStatus(outConn, deleteGql.c_str(), {GRD_OK, GRD_RESOURCE_BUSY});
    };
    vector<thread> threads;
    threads.emplace_back(thread(deleteTask, conn));
    GRD_DB *newConn = nullptr;
    UtGrdGqlOpenConn(&newConn);
    threads.emplace_back(thread(queryTask, newConn));

    isRunning = true;
    for (thread &t : threads) {
        t.join();
    };
    UtGrdGqlCloseConn(newConn);
    // DQL做完后尝试删除
    string matchDeleteGql = "MATCH (a:Staff {name: 'a'})-[r]->(b) DETACH DELETE r;";
    GrdGqlExecCmdWithStatus(conn, matchDeleteGql.c_str(), {GRD_OK, GRD_OK});
}
