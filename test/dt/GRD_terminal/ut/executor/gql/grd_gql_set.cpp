/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <iostream>
#include <string>
#include "gtest/gtest.h"
#include "grd_gql_api.h"
#include "grd_gql_com.h"
#include "grd_db_api.h"

using namespace std;
using namespace testing::ext;

typedef struct GrdGqlSetCtx {
    string gqlStr;
    uint32_t expectColNum;
    uint32_t expectRowNum;
    int32_t expectPrepareRet;
    int32_t expectStepRet;
} GrdGqlSetCtxT;

class GrdGqlSet : public GrdGqlCommon {
    void SetUp() override
    {
        GrdGqlCommon::SetUp();
    }

    void TearDown() override
    {
        GrdGqlCommon::TearDown();
    }
};

// 用例描述: 更新操作不合法场景
// 预期结果: 返回错误
HWTEST_F(GrdGqlSet, TestGqlUpdateInvalidCases, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }

    // 更新不存在的变量，返回错误 GRD_UNDEFINED_OBJECT
    cmdCtx = {GRD_UNDEFINED_OBJECT, GRD_INVALID_ARGS};
    string gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.gender='g', b.name='b';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新不存在的属性, 返回错误 GRD_UNDEFINE_COLUMN
    cmdCtx = {GRD_UNDEFINE_COLUMN, GRD_INVALID_ARGS};
    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.dad='Dad';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a={dad:'aaa'};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新不存在的标签，返回错误 GRD_UNDEFINED_TABLE
    cmdCtx = {GRD_UNDEFINED_TABLE, GRD_INVALID_ARGS};
    gqlStr = "MATCH (a: Company {name: 'name_1'}) SET a.name='aaa';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新值为不存在的属性值，返回错误 GRD_UNDEFINED_OBJECT
    cmdCtx = {GRD_UNDEFINED_OBJECT, GRD_INVALID_ARGS};
    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.name=aaa;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    cmdCtx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.name=aaa';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
}

// 用例描述: 更新操作不合法场景
// 预期结果: 返回错误
HWTEST_F(GrdGqlSet, TestGqlUpdateInvalidCases2, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }

    // SET 子句后语法错误，返回 GRD_SYNTAX_ERROR
    cmdCtx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    string gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a..name='aaa';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.name='aaa;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.name='aa'a';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.name == 'aaa';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.name.='aaa';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a={};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a={1};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a={name='aaa'};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a={name:'aaa};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a={name:aaa'};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新值的类型不匹配，返回错误 GRD_DATATYPE_MISMATCH
    cmdCtx = {GRD_OK, GRD_DATATYPE_MISMATCH};
    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.gender=100;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新值为点变量，返回错误 GRD_SEMANTIC_ERROR
    cmdCtx = {GRD_SEMANTIC_ERROR, GRD_INVALID_ARGS};
    gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.gender=a;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
}

// 用例描述: 暂不支持的场景，支持后放开
// 预期结果: 返回错误
HWTEST_F(GrdGqlSet, TestGqlUpdateNotSupported, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    string gqlStr = "MATCH (a: Person {name: 'name_1'}), (b: Person {name: 'name_2'}) SET a.name='aaa';";
    cmdCtx = {GRD_FEATURE_NOT_SUPPORTED, GRD_INVALID_ARGS};
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
}

// 用例描述: 更新点的部分属性
// 预期结果: 更新成功，被修改的点属性符合预期
HWTEST_F(GrdGqlSet, TestGqlUpdateVertex1, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    string gqlStr = "MATCH (a: Person {name: 'name_1'}) SET a.gender='g', a.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 结果校验
    gqlStr = "MATCH (a: Person {name: 'name_1'}) RETURN a.gender;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult{{"a.gender"}, {{"female"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新点的所有属性
// 预期结果: 更新成功，被修改的点属性符合预期
HWTEST_F(GrdGqlSet, TestGqlUpdateVertex2, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    string gqlStr =
        "MATCH (a: Person {name: 'name_1'}) SET a={position:'SZ', gender:'female', name:'aaa'}, a.position='SH';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 结果校验
    gqlStr = "MATCH (a: Person {name: 'aaa'}) RETURN a.name, a.gender, a.position, a.birthdate;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult{
        {"a.name", "a.gender", "a.position", "a.birthdate"}, {{"aaa", "female", "SH", "NULL"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新表里所有点的部分属性
// 预期结果: 更新成功，被修改的点属性符合预期
HWTEST_F(GrdGqlSet, TestGqlUpdateVertex3, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    string gqlStr = "MATCH (a: Person) SET a.name='yyy', a.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 结果校验
    gqlStr = "MATCH (a: Person) RETURN a.name, a.gender;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult{{"a.name", "a.gender"}, {{"yyy", "female"}, {"yyy", "female"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新没有匹配到任何点
// 预期结果: 更新0条数据，返回OK
HWTEST_F(GrdGqlSet, TestGqlUpdateVertex4, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    string gqlStr = "MATCH (a: Person {name: 'aaa'}) SET a.name='yyy', a.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
}

// 用例描述: 更新超过一个批次数据量的点
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateVertex5, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    // 插入数据
    const int vertexNum = 50;
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 更新数据
    string gqlStr = "MATCH (a: Person) WHERE a.gender='male' SET a.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 结果校验
    gqlStr = "MATCH (a: Person) WHERE a.gender='female' RETURN a.gender;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlCheckActQryResultSetSize(data, 1, 50, gqlStr);  // 50 定义为result->colValues.size大小
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新定长N跳匹配到的点
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateVertex6, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系
    gqlStr =
        "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2') INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新操作
    gqlStr = "MATCH (src: Person)-[e]->(dest: Person) SET dest.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    // 结果校验
    gqlStr = "MATCH (a: Person) RETURN a.name, a.gender;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult{
        {"a.name", "a.gender"}, {{"name_1", "female"}, {"name_2", "male"}, {"name_3", "female"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));

    // 更新操作
    gqlStr = "MATCH (src: Person)-[e1]->(mid: Person)-[e2]->(dest: Person) SET mid.position='BJ', dest.position='SZ';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    // 结果校验
    gqlStr = "MATCH (a: Person) RETURN a.name, a.position;";
    stmt = nullptr;
    len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult1{
        {"a.name", "a.position"}, {{"name_1", "BJ"}, {"name_2", "WanChuang"}, {"name_3", "SZ"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult1);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新简单不带标签
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateNoLabel_001, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
        gqlStr = GetInsertEventGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系 1->1, 1->3, 2->1, 2->2, 2->3, 3->1, 3->2, 3->3
    gqlStr =
        "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2') INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    gqlStr = "MATCH (a: Person WHERE a.name != 'name_1'), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    vector<string> gqlStrs = {
        "MATCH (a) SET a.name = 'a'",
        "MATCH (a) SET a = {name: 'b'}",
        "MATCH (a)-[b]->(c) SET c.name = 'c'",
        "MATCH (a)-[e1]->(b)-[e2]->(c) SET b.name = 'd'",
        "MATCH (a)-[e]->{0, 1}(b) SET b.name = 'e'",
    };
    vector<vector<vector<string>>> resNames = {
        {{"a"}, {"a"}, {"a"}},
        {{"b"}, {"b"}, {"b"}},
        {{"c"}, {"c"}, {"c"}},
        {{"d"}, {"d"}, {"d"}},
        {{"e"}, {"e"}, {"e"}},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

        gqlStr = "MATCH (a: Person) RETURN a.name;";
        GRD_StmtT *stmt = nullptr;
        uint32_t len = (uint32_t)(gqlStr.size() + 1);
        EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
        void *data = nullptr;
        UtGrdGqlGetActQryResultSetEx(data, stmt);
        UtGrdGqlQryResultSetExtendT exptQryResult{{"a.name"}, {resNames[i]}};
        UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
        UtGrdGqlClearActQryResultSet(NULL);
        EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt)) << "Mismatch name: " << gqlStr;
        i++;
    }
}

// 用例描述: 更新复杂不带标签
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateNoLabel_002, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
        gqlStr = GetInsertEventGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系 1->1, 1->3, 2->1, 2->2, 2->3, 3->1, 3->2, 3->3
    gqlStr =
        "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2') INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    gqlStr = "MATCH (a: Person WHERE a.name != 'name_1'), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    vector<string> gqlStrs = {
        "MATCH (a) SET a = {name: 'x', title: 'x'}",  // Person无title，全部更新失败
        "MATCH (a WHERE a.name = 'name_1') SET a.name = 'a'",
        "MATCH (a) WHERE a.name = 'name_2' SET a = {name: 'b'}",
        "MATCH (a)-[b]->(c) WHERE a.name = 'a' SET c.name = 'c'",
        "MATCH (a)-[e1]->(b WHERE b.name = 'b')-[e2]->(c) SET c.name = 'd'",
        "MATCH (a)-[e1]->(b WHERE b.name = 'd')-[e2]->(c) SET c.name = 'e'",
    };
    vector<vector<vector<string>>> resNames = {
        {{"name_1"}, {"name_2"}, {"name_3"}},
        {{"a"}, {"name_2"}, {"name_3"}},
        {{"a"}, {"b"}, {"name_3"}},
        {{"c"}, {"b"}, {"c"}},
        {{"d"}, {"d"}, {"d"}},
        {{"e"}, {"e"}, {"e"}},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

        if (resNames[i].empty()) {
            continue;
        }

        gqlStr = "MATCH (a: Person) RETURN a.name;";
        GRD_StmtT *stmt = nullptr;
        uint32_t len = (uint32_t)(gqlStr.size() + 1);
        EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
        void *data = nullptr;
        UtGrdGqlGetActQryResultSetEx(data, stmt);
        UtGrdGqlQryResultSetExtendT exptQryResult{{"a.name"}, {resNames[i++]}};
        UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
        UtGrdGqlClearActQryResultSet(NULL);
        EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt)) << "Mismatch name: " << gqlStr;
    }
}

// 用例描述: 更新复杂不带标签
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateNoLabel_003, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
        gqlStr = GetInsertEventGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系 1->1, 1->3, 2->1, 2->2, 2->3, 3->1, 3->2, 3->3
    gqlStr =
        "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2') INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    gqlStr = "MATCH (a: Person WHERE a.name != 'name_1'), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    vector<string> gqlStrs = {
        "MATCH (a)-[e]->{0, 1}(b) WHERE b.invalid IS NOT NULL SET b.name = 'f'",
        "MATCH (a)-[e]->{0, 1}(b) WHERE b.invalid IS NULL SET b.name = 'g'",
        "MATCH (a)-[e]->(b) SET a.name='h1', a.name='h2'",
        "MATCH (a)-[e]->(b) SET a.name='j1', b.name='j2'",  // 随机顺序
    };
    vector<vector<vector<string>>> resNames = {
        {{"name_1"}, {"name_2"}, {"name_3"}},
        {{"g"}, {"g"}, {"g"}},
        {{"h2"}, {"h2"}, {"h2"}},
        {},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

        if (resNames[i].empty()) {
            continue;
        }

        gqlStr = "MATCH (a: Person) RETURN a.name;";
        GRD_StmtT *stmt = nullptr;
        uint32_t len = (uint32_t)(gqlStr.size() + 1);
        EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
        void *data = nullptr;
        UtGrdGqlGetActQryResultSetEx(data, stmt);
        UtGrdGqlQryResultSetExtendT exptQryResult{{"a.name"}, {resNames[i]}};
        UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
        UtGrdGqlClearActQryResultSet(NULL);
        EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt)) << "Mismatch name: " << gqlStr;
        i++;
    }
}

// 用例描述: 更新复杂不带标签负向用例
// 预期结果: 更新失败，无死锁
HWTEST_F(GrdGqlSet, TestGqlUpdateNoLabel_004, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    gqlStr =
        "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2') INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    gqlStr = "MATCH (a: Person WHERE a.name != 'name_1'), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    vector<string> gqlStrs = {
        "MATCH (a)-[e]->(b) SET a.name = 'f1' AND b.name = 'f2'",  // GMERR_OK
        "MATCH (a)-[e]->(b) SET a.name = 'f1' RETURN a.name",      // GMERR_SYNTAX_ERROR
    };
    vector<GrdGqlExecCmdCtxT> expectedRes = {
        {GRD_OK, GRD_OK},
        {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectedRes[i]);
        i++;
    }
}

// 用例描述: 更新复杂无图
// 预期结果: 更新失败，无死锁
HWTEST_F(GrdGqlSet, TestGqlUpdateNoLabel_005, TestSize.Level0)
{
    UtGrdGqlClearActQryResultSet(NULL);
    const char *gql = "DROP GRAPH IF EXISTS myGraph;";
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    GrdGqlExecCmdWithStatus(conn, gql, ctx);

    vector<string> gqlStrs = {
        "MATCH (a) SET a.name = 'a'",
        "MATCH (a) SET a = {name: 'b'}",
        "MATCH (a)-[b]->(c) SET c.name = 'c'",
        "MATCH (a)-[e1]->(b)-[e2]->(c) SET b.name = 'd'",
        "MATCH (a)-[e]->{0, 1}(b) SET b.name = 'e'",
    };

    vector<GrdGqlExecCmdCtxT> expectedRes = {
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
        {GRD_OK, GRD_NO_DATA},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectedRes[i]);
        i++;
    }
}

// 用例描述: 更新操作不合法场景
// 预期结果: 返回错误
HWTEST_F(GrdGqlSet, TestGqlUpdateInvalidEdgeCases, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 2;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }

    // 插入关系
    gqlStr = "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name = 'name_2')"
             "INSERT (a)-[:直系亲属{roletype:'dad', age:18}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新不存在的变量，返回错误 GMERR_UNDEFINED_OBJECT
    cmdCtx = {GRD_UNDEFINED_OBJECT, GRD_INVALID_ARGS};
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET b.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新不存在的属性, 返回错误 GMERR_UNDEFINE_COLUMN
    cmdCtx = {GRD_UNDEFINE_COLUMN, GRD_INVALID_ARGS};
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET e.gender='female';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    cmdCtx = {GRD_UNDEFINE_COLUMN, GRD_INVALID_ARGS};
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET e = {unkown_attr1:'dad', age:18};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新值的类型不匹配，返回错误 GMERR_DATATYPE_MISMATCH
    cmdCtx = {GRD_OK, GRD_DATATYPE_MISMATCH};
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET e.age = '2313';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    cmdCtx = {GRD_OK, GRD_DATATYPE_MISMATCH};
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET e = {roletype:18, age:'2313'};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    cmdCtx = {GRD_OK, GRD_OK};
    gqlStr = "MATCH (src: Person)-[e]-(dest) SET e.invalid = 1;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
}

// 用例描述: 更新定长N跳匹配到的点和边的部分属性
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateEdge1, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系
    gqlStr = "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2')"
             "INSERT (a)-[:直系亲属{roletype:'dad', age:18}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 同时更新点和边
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET dest.gender='female', e.roletype='mom';";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 点结果校验
    gqlStr = "MATCH (a: Person) RETURN a.name, a.gender;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult{
        {"a.name", "a.gender"}, {{"name_1", "female"}, {"name_2", "male"}, {"name_3", "female"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));

    // 边结果校验
    gqlStr = "MATCH (a: Person)-[e]->(b: Person) RETURN e.roletype, e.age;";
    stmt = nullptr;
    len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    exptQryResult = {{"e.roletype", "e.age"}, {{"mom", "18"}, {"mom", "18"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新定长N跳匹配到边的整体属性（除identity）
// 预期结果: 更新成功
HWTEST_F(GrdGqlSet, TestGqlUpdateEdge2, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系
    gqlStr = "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2')"
             "INSERT (a)-[:直系亲属{roletype:'dad', age:18}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    // 更新整条边
    gqlStr = "MATCH (src: Person)-[e:直系亲属]->(dest: Person) SET e = {roletype:'mom', age:19};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    // 边结果校验
    gqlStr = "MATCH (a: Person)-[e]->(b: Person) RETURN e.roletype, e.age;";
    GRD_StmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(gqlStr.size() + 1);
    EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, NULL));
    void *data = nullptr;
    UtGrdGqlGetActQryResultSetEx(data, stmt);
    UtGrdGqlQryResultSetExtendT exptQryResult{{"e.roletype", "e.age"}, {{"mom", "19"}, {"mom", "19"}}};
    UtGrdGqlCheckActQryResultSet(NULL, exptQryResult);
    UtGrdGqlClearActQryResultSet(NULL);
    EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
}

// 用例描述: 更新定长N跳匹配边不存在的属性
// 预期结果: 更新不报错
TEST_F(GrdGqlSet, TestGqlUpdateEdge3)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    string gqlStr;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系, 2->1, 2->2, 2->3, 3->1, 3->2, 3->3
    gqlStr = "MATCH (a: Person WHERE a.name != 'name_1'), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    vector<string> gqlStrs = {
        "MATCH (a)-[e]->(b) SET e.roletype = null;",
        "MATCH (a)-[e]->(b) SET e.roletype = a.invalid || b.invalid LIKE e.invalid;",
        "MATCH (a)-[e]->(b) SET e.invalid = a.name || b.name LIKE e.roletype;",
    };

    for (auto str : gqlStrs) {
        // 执行更新语句
        GrdGqlExecCmdWithStatus(conn, str.c_str(), cmdCtx);
        // 边结果校验
        gqlStr = "MATCH (a: Person)-[e]->(b: Person) RETURN e.roletype;";
        GRD_StmtT *stmt = nullptr;
        uint32_t len = (uint32_t)(gqlStr.size() + 1);
        EXPECT_EQ(GRD_OK, GRD_GqlPrepare(conn, gqlStr.c_str(), len, &stmt, nullptr));
        void *data = nullptr;
        UtGrdGqlGetActQryResultSetEx(data, stmt);
        UtGrdGqlQryResultSetExtendT exptQryResult{
            {"e.roletype"}, {{"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}}};
        UtGrdGqlCheckActQryResultSet(nullptr, exptQryResult, str);
        UtGrdGqlClearActQryResultSet(nullptr);
        EXPECT_EQ(GRD_OK, GRD_GqlFinalize(stmt));
    }
}

HWTEST_F(GrdGqlSet, SpecialCase, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 3;
    GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
    string gqlStr;

    // 插入点
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    // 插入关系
    gqlStr = "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2')"
             "INSERT (a)-[:直系亲属{roletype:'dad', age:18}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "SET e = {roletype:'mom', age:19};";
    cmdCtx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH ({}) SET e = {roletype:'mom', age:19};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (src {}) SET src = {isUser: true};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH (src) SET src = {isUser: true};";
    cmdCtx = {GRD_OK, GRD_OK};
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH ()-[e:直系亲属]->() SET e = {roletype:'mom', age:19};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);

    gqlStr = "MATCH ()-[e:直系亲属 {}]->() SET e = {roletype:'mom', age:19};";
    cmdCtx = {GRD_SYNTAX_ERROR, GRD_INVALID_ARGS};
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
}

HWTEST_F(GrdGqlSet, TestSetIdentityCol, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    const int vertexNum = 5;
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    for (int i = 1; i <= vertexNum; ++i) {
        string gqlStr = GetInsertPersonGql(i, true);
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    }
    // 插入关系
    string gqlStr = "MATCH (a: Person WHERE a.name = 'name_1'), (b: Person WHERE b.name != 'name_2')"
                    "INSERT (a)-[:直系亲属{roletype:'dad', age:18}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    vector<string> gqlStrs = {
        "MATCH (a:Person {identity:1}) SET a.identity = 1;",
        "MATCH (a:Person {identity:1}) SET a.rowid = 1;",
        "MATCH (a)-[b]->(c) SET b.identity = 2;",
        "MATCH (a)-[b:直系亲属]->(c) SET b.identity = 2;",
        "MATCH (a)-[b:直系亲属]->(c) SET b.rowid = 2;",
        "MATCH (a)-[b]->(c) SET b.identity = a.identity + 1;",
    };
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdCtxT cmdCtx = {GRD_UNDEFINE_COLUMN, GRD_UNDEFINE_COLUMN};
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
    }
    gqlStrs = {
        "MATCH (a)-[b]->(c) SET b.roletype = a.identity || '1';",  // b.roletype = NULL
        "MATCH (a:Person WHERE element_id(a) = '1') SET a.name = '1' || element_id(a);",
        "MATCH (a)-[b]->(c) SET b.roletype = element_id(a) || '1';",
        "MATCH (a)-[b]->(c) SET a.name = element_id(b) || '1';",
    };
    vector<UtGrdGqlQryResultSetExtendT> results{{{"?column?", "a.NAME", "b.ROLETYPE", "?column?"},
                                                    {{"1", "name_1", "NULL", "92"}, {"1", "name_1", "NULL", "82"},
                                                        {"1", "name_1", "NULL", "72"}, {"1", "name_1", "NULL", "62"}}},
        {{"?column?", "a.NAME", "b.ROLETYPE", "?column?"}, {{"1", "11", "NULL", "92"}, {"1", "11", "NULL", "82"},
                                                               {"1", "11", "NULL", "72"}, {"1", "11", "NULL", "62"}}},
        {{"?column?", "a.NAME", "b.ROLETYPE", "?column?"},
            {{"1", "11", "11", "92"}, {"1", "11", "11", "82"}, {"1", "11", "11", "72"}, {"1", "11", "11", "62"}}},
        {{"?column?", "a.NAME", "b.ROLETYPE", "?column?"},
            {{"1", "61", "11", "92"}, {"1", "61", "11", "82"}, {"1", "61", "11", "72"}, {"1", "61", "11", "62"}}}};
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdCtxT cmdCtx = {GRD_OK, GRD_OK};
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), cmdCtx);
        GrdGqlCheckActQryResultSetWithGql(conn, nullptr, results[i++],
            "MATCH (a)-[b]->(c) RETURN element_id(a), a.name, b.roletype, element_id(b) || '2';");
    }
}

HWTEST_F(GrdGqlSet, TestSetUndefinedColumn001, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    string gqlStr = "DROP GRAPH IF EXISTS myGraph;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "CREATE GRAPH IF NOT EXISTS myGraph{(person:Person), (person)-[:直系亲属]->(person)};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    const int vertexNum = 5;
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = "INSERT (:Person);";
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    }
    gqlStr = "MATCH (a: Person), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    vector<string> gqlStrs = {
        "MATCH (a:Person {identity:1}) SET a.invalid = 1;",
        "MATCH (a)-[b:直系亲属]->(c) SET a.invalid = 1;",
        "MATCH (a)-[b]->(c) SET c.invalid = 1;",
        "MATCH (a)-[b]->(c:Person) SET c.invalid = 1;",
        "MATCH (a)-[]->{0,1}(c:Person) SET c.invalid = 1;",
        "MATCH (a)-[]->{0,1}(c) SET c.invalid = 1;",
        "MATCH (a) SET a.invalid = 1;",
        "MATCH (a)-[b:直系亲属]->(c) SET b.invalid = 2;",
        "MATCH (a)-[b]->(c) SET b.invalid = 2;",
    };
    vector<GrdGqlExecCmdCtxT> expectRes = {
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_OK, GRD_OK},
        {GRD_OK, GRD_OK},
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_OK, GRD_OK},
        {GRD_OK, GRD_OK},
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_OK, GRD_OK},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectRes[i]);
        i++;
    }
}

HWTEST_F(GrdGqlSet, TestSetUndefinedColumn002, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    string gqlStr = "DROP GRAPH IF EXISTS myGraph;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "CREATE GRAPH IF NOT EXISTS myGraph{(person:Person{age int}), (person)-[:直系亲属{score int}]->(person)};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    const int vertexNum = 5;
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = "INSERT (:Person);";
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    }
    gqlStr = "MATCH (a: Person), (b: Person) INSERT (a)-[:直系亲属]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);

    vector<string> gqlStrs = {
        "MATCH (a:Person {age:1}) SET a.age = 1 + a.invalid;",
        "MATCH (a:Person {age:1}) SET a.age = 1 + cast(a.invalid || '2' as int);",
        "MATCH (a:Person {age:1}) SET a.age = 1 + char_length(a.invalid || '2');",
        "MATCH (a:Person {age:1}) SET a.age = 1 + floor(a.invalid);",
        "MATCH (a)-[b]->(c:Person) SET c.age = 2 * c.invalid;",
        "MATCH (a)-[]->{0,1}(c:Person) SET c.age = c.invalid IS NULL;",
        "MATCH (a)-[]->{0,1}(a:Person) SET a.age = a.invalid IS NULL;",
        "MATCH (a)-[b:直系亲属]->(c) SET b.score = b.invalid;",
    };
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdCtxT expectRes = {GRD_UNDEFINE_COLUMN, GRD_UNDEFINE_COLUMN};
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectRes);
    }
    gqlStrs = {
        "MATCH (a)-[b:直系亲属]->(c) SET a.age = 1 + cast(a.invalid || '2' as int);",
        "MATCH (a)-[b:直系亲属]->(c) SET a.age = 1 + char_length(a.invalid || '2');",
        "MATCH (a)-[b]->(c) SET c.age = ceil(c.invalid);",
        "MATCH (a)-[b]->(c) SET c.age = c.invalid;",
        "MATCH (a)-[]->{0,1}(c:Person) SET c.age = a.invalid IS NULL;",
        "MATCH (a)-[]->{0,1}(c) SET c.age = c.invalid;",
        "MATCH (a) SET a.age = a.invalid;",
        "MATCH (a)-[b]->(c) SET b.score = b.invalid;",
        "MATCH (a)-[b:直系亲属]->(c {invalid:1}) SET b.score = 1;",
        "MATCH (a)-[b:直系亲属]->(c WHERE c.invalid = 1 ) SET b.score = 1;",
    };
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdCtxT expectRes = {GRD_OK, GRD_OK};
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectRes);
    }
}

HWTEST_F(GrdGqlSet, TestSetWithoutEdge, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    string gqlStr = "DROP GRAPH IF EXISTS myGraph;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "CREATE GRAPH IF NOT EXISTS myGraph{(person:Person)};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    const int vertexNum = 5;
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = "INSERT (:Person);";
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    }

    vector<string> gqlStrs = {
        "MATCH (a:Person)-[b]->(c) SET a.invalid = 1;",
        "MATCH (a:Person)-[]->{0,1}(c) SET a.invalid = 1;",
        "MATCH (a)-[]->{0,1}(c) SET a.invalid = 1;",
        "MATCH (a)-[b]->(c) SET b.invalid = 1;",
        "MATCH (a)-[b:直系亲属]->(c) SET b.invalid = 2;",
        "MATCH (a)-[b]->(c:Person) SET c.invalid = 1;",
        "MATCH (a)-[]->{0,1}(c:Person) SET c.invalid = 1;",
        "MATCH (a)-[]->{0,1}(c) SET c.invalid = 1;",
    };
    vector<GrdGqlExecCmdCtxT> expectRes = {
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_OK, GRD_OK},
        {GRD_OK, GRD_OK},
        {GRD_UNDEFINED_TABLE, GRD_OK},
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_UNDEFINE_COLUMN, GRD_OK},
        {GRD_OK, GRD_OK},
    };
    uint32_t i = 0;
    for (auto gqlStr : gqlStrs) {
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), expectRes[i]);
        i++;
    }
}

HWTEST_F(GrdGqlSet, TestSetAllProperties, TestSize.Level0)
{
    GRD_DB *conn = UtGrdGqlGetConn();
    GrdGqlExecCmdCtxT ctx = {GRD_OK, GRD_OK};
    string gqlStr = "DROP GRAPH IF EXISTS myGraph;";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "CREATE GRAPH IF NOT EXISTS myGraph{(person:Person{name string}), "
             "(person)-[:直系亲属{type string}]->(person)};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    const int vertexNum = 2;
    for (int i = 1; i <= vertexNum; ++i) {
        gqlStr = "INSERT (:Person {name : 'name_" + to_string(i) + "'});";
        GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    }
    gqlStr = "MATCH (a: Person), (b: Person) INSERT (a)-[:直系亲属 {type : '直系亲属'}]->(b);";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a: Person) SET a = {name : 'new_name'};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a:Person) RETURN a;";
    UtGrdGqlQryResultSetExtendT exptQryResult{
        {"a"}, {{"{\"label\":\"Person\",\"identity\":\"1\",\"properties\":{\"name\":\"new_name\"}}"},
                   {"{\"label\":\"Person\",\"identity\":\"2\",\"properties\":{\"name\":\"new_name\"}}"}}};
    GrdGqlCheckActQryResultSetWithGql(conn, nullptr, exptQryResult, gqlStr);
    gqlStr = "MATCH (a)-[b]->() SET a = {name : 'new_name_2'}, b = {type : 'new_type'};";
    GrdGqlExecCmdWithStatus(conn, gqlStr.c_str(), ctx);
    gqlStr = "MATCH (a)-[b]->() RETURN a, b;";
    exptQryResult = {
        {"a", "b"}, {{"{\"label\":\"Person\",\"identity\":\"1\",\"properties\":{\"name\":\"new_name_2\"}}",
                         "{\"label\":\"直系亲属\",\"identity\":\"4\",\"start\":\"1\",\"end\":\"2\",\"properties\":{"
                         "\"type\":\"new_type\"}}"},
                        {"{\"label\":\"Person\",\"identity\":\"1\",\"properties\":{\"name\":\"new_name_2\"}}",
                            "{\"label\":\"直系亲属\",\"identity\":\"3\",\"start\":\"1\",\"end\":\"1\",\"properties\":{"
                            "\"type\":\"new_type\"}}"},
                        {"{\"label\":\"Person\",\"identity\":\"2\",\"properties\":{\"name\":\"new_name_2\"}}",
                            "{\"label\":\"直系亲属\",\"identity\":\"6\",\"start\":\"2\",\"end\":\"2\",\"properties\":{"
                            "\"type\":\"new_type\"}}"},
                        {"{\"label\":\"Person\",\"identity\":\"2\",\"properties\":{\"name\":\"new_name_2\"}}",
                            "{\"label\":\"直系亲属\",\"identity\":\"5\",\"start\":\"2\",\"end\":\"1\",\"properties\":{"
                            "\"type\":\"new_type\"}}"}}};
    GrdGqlCheckActQryResultSetWithGql(conn, nullptr, exptQryResult, gqlStr);
}
