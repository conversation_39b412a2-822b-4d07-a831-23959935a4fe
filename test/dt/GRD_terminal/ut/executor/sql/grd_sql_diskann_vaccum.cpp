/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <chrono>
#include <future>
#include <string>
#include "gtest/gtest.h"

#include "db_test_tool.h"
#include "grd_sql_diskann_fusion_search.h"
#include "grd_sql_test_tool.h"
#include "grd_error.h"
#include "grd_sql_api.h"
#include "grd_db_api.h"
#include "store_type.h"
#include "grd_type_export.h"
#include "stub.h"

using namespace testing::ext;

constexpr int TEST_GRD_PERSISTENCE_MODE = 1;
static const char *g_sqlDatafilePath = "./data/gmdb/";
static const char *g_sqlDbFile = "./data/gmdb/datafile";
static std::string g_sqlTail = ";";
static std::string g_tableOption = "";

static GRD_DB *g_db = nullptr;

class GrdSqlDiskAnnVaccum : public testing::Test {
public:
    static void SetUpTestCase(void);
    static void TearDownTestCase(void);
    void SetUp(void);
    void TearDown(void);
};

void GrdSqlDiskAnnVaccum::SetUpTestCase(void)
{
    EXPECT_EQ(DbTestTool::RemoveDir(g_sqlDatafilePath), 0);
    EXPECT_EQ(DbTestTool::MakeDir(g_sqlDatafilePath), 0);
    EXPECT_EQ(GMERR_OK, GRD_DBOpen(g_sqlDbFile, FUSION_SEARCH_CFG, GRD_DB_OPEN_CREATE, &g_db));
}

void GrdSqlDiskAnnVaccum::TearDownTestCase(void)
{
    EXPECT_EQ(GRD_OK, GRD_DBClose(g_db, GRD_DB_CLOSE));
    g_db = nullptr;
    EXPECT_EQ(DbTestTool::RemoveDir(g_sqlDatafilePath), 0);
}

void GrdSqlDiskAnnVaccum::SetUp(void)
{
    FusionSearch::fsThreadPool_ = new FsThreadPool();
    EXPECT_EQ(GMERR_OK, GRD_SqlRegistryThreadPool(g_db, &FusionSearch::threadPool_));
}

void GrdSqlDiskAnnVaccum::TearDown(void)
{
    delete FusionSearch::fsThreadPool_;
    FusionSearch::fsThreadPool_ = nullptr;
}

void AnnAsyncTestFinish(GRD_DB *db)
{
    EXPECT_EQ(GRD_OK, GRD_DBClose(db, GRD_DB_CLOSE));
    db = nullptr;
    EXPECT_EQ(DbTestTool::RemoveDir(g_sqlDatafilePath), 0);
    EXPECT_EQ(DbTestTool::MakeDir(g_sqlDatafilePath), 0);
    EXPECT_EQ(GMERR_OK, GRD_DBOpen(g_sqlDbFile, FUSION_SEARCH_CFG, GRD_DB_OPEN_CREATE, &db));
    g_db = db;
}

void AnnAsyncReOpenDB()
{
    TimeStatTool counter("ReOpenDB");
    EXPECT_EQ(GMERR_OK, GRD_DBClose(g_db, 0));
    g_db = nullptr;
    EXPECT_EQ(GMERR_OK, GRD_DBOpen(g_sqlDbFile, FUSION_SEARCH_CFG, GRD_DB_OPEN_CREATE, &g_db));
    EXPECT_EQ(GMERR_OK, GRD_SqlRegistryThreadPool(g_db, &FusionSearch::threadPool_));
    // 需要后，必须更新当前所有的db，注意，如果不更新会有不可预期问题
}

// 测试删除刚大于20%，纯自动整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnAutoVaccum001, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 50;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 0;
    const uint32_t deleteSize0 = 10;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after auto vaccum is %lu \n", size1);

    printf("GRD Do delete to enable gc\n");
    const uint32_t newDeleteStartIndex = 30;
    const uint32_t newDeleteSize = 1;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(newDeleteStartIndex, newDeleteSize));

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(newDeleteStartIndex, newDeleteStartIndex + newDeleteSize));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试删除50%，纯自动整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnAutoVaccum002, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 100;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 0;
    const uint32_t deleteSize0 = 50;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after auto vaccum is %lu \n", size1);

    printf("Do delete to enable gc\n");
    const uint32_t newDeleteSize = 1;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(deleteSize0, newDeleteSize));

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(deleteSize0, deleteSize0 + newDeleteSize));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after OldBatchReInsert is %lu \n", size2);

    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试删除99%，自动整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnAutoVaccum003, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 100;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 0;
    const uint32_t deleteSize0 = 99;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after auto vaccum is %lu \n", size1);

    printf("Do delete to enable gc\n");
    const uint32_t newDeleteSize = 1;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(deleteSize0, newDeleteSize));

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(deleteSize0, deleteSize0 + newDeleteSize));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试临界点不触发自动/手动整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum001, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 50;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 5;
    const uint32_t deleteSize0 = 9;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试删除20%，关库停止自动整理并手动触发整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum002, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 100;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 0;
    const uint32_t deleteSize0 = 20;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    AnnAsyncReOpenDB();
    annT1.UpdateDb(g_db);
    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize() * 2);
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试删除99%，关库停止自动整理并手动触发整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum003, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 50;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 0;
    const uint32_t deleteSize0 = 49;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    AnnAsyncReOpenDB();
    annT1.UpdateDb(g_db);
    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试删除50%，关库停止自动整理并手动触发整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum004, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 50;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    for (int i = 0; i < 10; ++i) {
        // 删除deleteSize0向量
        const uint32_t startIndex = 0;
        const uint32_t deleteSize0 = 15;
        ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

        AnnAsyncReOpenDB();
        annT1.UpdateDb(g_db);
        // 执行全表异步整理
        annT1.DoDiskAnnAsyncCollecting();
        annT1.DoSleepForVaccum(deleteSize0);

        // 再次插入deleteSize0的随机向量
        ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));

        auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
        printf("Dbfile size after round %d is %lu \n", i, size1);
    }

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试多表多索引清理场景
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum005, TestSize.Level4)
{
    // 创建表t1 t2 t3并分别建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");
    AnnAsyncCollector annT2(g_db, g_sqlDbFile, g_sqlTail, "t2", "");
    AnnAsyncCollector annT3(g_db, g_sqlDbFile, g_sqlTail, "t3", "");

    // 为两个表t1 t2 分别创建02索引 diskann_cos_idx."tableName_:.02;
    std::string indexSuffix = "02";
    std::string indexSuffix2 = "03";
    annT1.CreateIndex(indexSuffix);
    annT2.CreateIndex(indexSuffix);
    annT2.CreateIndex(indexSuffix2);

    // 分别插入batchSize的随机向量
    const uint32_t batchSizeT1 = 50;
    const uint32_t batchSizeT2 = 100;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSizeT1));
    ASSERT_EQ(GRD_OK, annT2.BatchRandomInsert(batchSizeT2));

    // 删除deleteSize0向量
    const uint32_t startIndex = 15;
    const uint32_t deleteSize0 = 20;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT2.BatchDelete(startIndex, deleteSize0));
    AnnAsyncReOpenDB();
    annT1.UpdateDb(g_db);
    annT2.UpdateDb(g_db);
    annT3.UpdateDb(g_db);

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0 * 2);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT2.OldBatchReInsert(startIndex, deleteSize0));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize() * 3);  // 多索引放宽约束
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试删除80%，关库停止自动整理并手动触发整理
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum006, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 100;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 50;
    const uint32_t deleteSize0 = 80;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    AnnAsyncReOpenDB();
    annT1.UpdateDb(g_db);
    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试未注册线程池异常场景
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum007, TestSize.Level0)
{
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 重新开关库以取消线程池注册
    EXPECT_EQ(GMERR_OK, GRD_DBClose(g_db, 0));
    g_db = nullptr;
    EXPECT_EQ(GMERR_OK, GRD_DBOpen(g_sqlDbFile, FUSION_SEARCH_CFG, GRD_DB_OPEN_CREATE, &g_db));
    annT1.UpdateDb(g_db);

    // 执行全表异步整理，返回不支持
    std::string sqlRunVaccum = "PRAGMA DISKANN_ASYNC_COLLECTING;";
    GrdSqlTestTool::ExecuteSingleSql(annT1.GetDb(), sqlRunVaccum, GRD_OK, GRD_INVALID_ARGS);

    AnnAsyncTestFinish(g_db);
}

// 测试插入随机向量的场景下的膨胀率
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum008, TestSize.Level4)
{
    // 创建表t1 并建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSize = 200;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSize));

    // 删除deleteSize0向量
    const uint32_t startIndex = 5;
    const uint32_t deleteSize0 = 160;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));

    AnnAsyncReOpenDB();
    annT1.UpdateDb(g_db);
    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(deleteSize0, startIndex));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize());
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}

// 测试多表多索引清理场景 DTS2025051633446
HWTEST_F(GrdSqlDiskAnnVaccum, AnnManualVaccum009, TestSize.Level4)
{
    // 创建表t1 t2 t3并分别建立default diskann索引
    AnnAsyncCollector annT1(g_db, g_sqlDbFile, g_sqlTail, "t1", "");
    AnnAsyncCollector annT2(g_db, g_sqlDbFile, g_sqlTail, "t2", "");
    AnnAsyncCollector annT3(g_db, g_sqlDbFile, g_sqlTail, "t3", "");

    // 分别插入batchSize的随机向量
    const uint32_t batchSizeT1 = 100;
    const uint32_t batchSizeT2 = 100;
    const uint32_t batchSizeT3 = 100;
    ASSERT_EQ(GRD_OK, annT1.BatchRandomInsert(batchSizeT1));
    ASSERT_EQ(GRD_OK, annT2.BatchRandomInsert(batchSizeT2));
    ASSERT_EQ(GRD_OK, annT3.BatchRandomInsert(batchSizeT3));

    // 删除deleteSize0向量
    const uint32_t startIndex = 0;
    const uint32_t deleteSize0 = 25;
    ASSERT_EQ(GRD_OK, annT1.BatchDelete(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT2.BatchDelete(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT3.BatchDelete(startIndex, deleteSize0));
    AnnAsyncReOpenDB();
    annT1.UpdateDb(g_db);
    annT2.UpdateDb(g_db);
    annT3.UpdateDb(g_db);

    auto size0 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after BatchDelete is %lu \n", size0);

    // 执行全表异步整理
    annT1.DoDiskAnnAsyncCollecting();
    annT1.DoSleepForVaccum(deleteSize0 * 5);

    auto size1 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    printf("Dbfile size after DISKANN_ASYNC_COLLECTING is %lu \n", size1);

    // 再次插入deleteSize0的随机向量
    ASSERT_EQ(GRD_OK, annT1.OldBatchReInsert(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT2.OldBatchReInsert(startIndex, deleteSize0));
    ASSERT_EQ(GRD_OK, annT3.OldBatchReInsert(startIndex, deleteSize0));

    auto size2 = annT1.GetFilePhysicalSize(g_sqlDbFile);
    auto expansionSize = size2 - size0;
    EXPECT_LE(expansionSize, annT1.GetLimitExpansionSize() * 3);  // 多索引放宽约束
    printf("Dbfile size after reInsert is %lu, expansionSize is %lu\n", size2, expansionSize);

    AnnAsyncTestFinish(g_db);
}
