/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <string>
#include <random>
#include "grd_sql_test_tool.h"
#include "grd_sql_diskann_fusion_search.h"

using namespace std;
using namespace testing::ext;

static char g_cfgPath[] = R"(
{
    "pageSize": 4
})";

static inline const char *StEmbSqlGetConfig()
{
    return g_cfgPath;
}

#define SQL_UT_TEST_FILE "datafile"
static const char *g_sqlDatafilePath = "./data/gmdb/";
static const char *g_sqlDbFile = "./data/gmdb/" SQL_UT_TEST_FILE;
static GRD_DB *g_db = nullptr;
static const int MAX_DIM_DISKANN = 1024;

class GrdSqlBasicApi : public testing::Test {
public:
    static void SetUpTestCase(void);
    static void TearDownTestCase(void);
    void ReOpenDb(void);
    void SetUp(void);
    void TearDown(void);
};

void GrdSqlBasicApi::SetUpTestCase(void)
{
    EXPECT_EQ(DbTestTool::RemoveDir(g_sqlDatafilePath), 0);
    EXPECT_EQ(DbTestTool::MakeDir(g_sqlDatafilePath), 0);
    int32_t ret = GRD_DBOpen(g_sqlDbFile, StEmbSqlGetConfig(), GRD_DB_OPEN_CREATE, &g_db);
    EXPECT_EQ(ret, GRD_OK);
}

void GrdSqlBasicApi::TearDownTestCase(void)
{
    int32_t ret = GRD_DBClose(g_db, GRD_DB_CLOSE);
    EXPECT_EQ(ret, GRD_OK);
}

void GrdSqlBasicApi::ReOpenDb(void)
{
    int32_t ret = GRD_DBClose(g_db, GRD_DB_CLOSE);
    EXPECT_EQ(ret, GRD_OK);
    ret = GRD_DBOpen(g_sqlDbFile, StEmbSqlGetConfig(), GRD_DB_OPEN_ONLY, &g_db);
    EXPECT_EQ(ret, GRD_OK);
}

void GrdSqlBasicApi::SetUp(void)
{}

void GrdSqlBasicApi::TearDown(void)
{}

/**
 * @tc.name: SqlPrepareAfterBuilding
 * @tc.desc: run this testcase to test if SQL feature combined with GRD_FIELD is correctly built
 * @tc.type: FUNC
 * @tc.require: DTS2023092103546
 * @tc.author: gerui
 */
HWTEST_F(GrdSqlBasicApi, SqlPrepareAfterBuilding, TestSize.Level2)
{
    const char *sql = "create table t1(id int, name text, weight real, photo blob);";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    GRD_StmtT *stmt = nullptr;
    EXPECT_EQ(GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr), GRD_OK);
}

/**
 * @tc.name: SqlBasicQueryForVector_001
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_001, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_DbDataTypeE type;
    GRD_DbValueT val = {};
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select id, repr FROM t1 WHERE id = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    float expectVec[16] = {30, 12, 12, 25, 5, 2, 10, 8, 6, 18, 24, 19, 5, 3, 0, 4};
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get id
        type = GRD_SqlColumnType(stmt, 1);
        ASSERT_EQ(type, GRD_DB_DATATYPE_FLOATVECTOR);
        uint32_t dim = 0;
        const float *vector = GRD_SqlColumnFloatVector(stmt, 1, &dim);
        val = GRD_SqlColumnValue(stmt, 1);
        for (uint32_t i = 0; i < dim; ++i) {
            ASSERT_FLOAT_EQ(vector[i], expectVec[i]);
            ASSERT_FLOAT_EQ((static_cast<const float *>(val.value.strAddr))[i], expectVec[i]);
        }
    }
    type = GRD_SqlColumnType(stmt, 1);
    ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_002
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_002, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "INSERT INTO t1 VALUES(1, 'Bella', ?, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlBindNull(stmt, 1);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlBindNull(stmt, 0);
    ASSERT_EQ(ret, GRD_INVALID_BIND_VALUE);
    ret = GRD_SqlBindNull(stmt, 2);
    ASSERT_EQ(ret, GRD_INVALID_BIND_VALUE);
    ret = GRD_SqlBindNull(stmt, 10000);
    ASSERT_EQ(ret, GRD_INVALID_BIND_VALUE);
    ret = GRD_SqlStep(stmt);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // query data
    GRD_DbDataTypeE type;
    sql = "select weight FROM t1;";
    len = (uint32_t)(strlen(sql) + 1);
    stmt = nullptr;
    ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get id
        type = GRD_SqlColumnType(stmt, 0);
        ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_003
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_003, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select id, repr FROM t1 WHERE id = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get id
        int32_t id = GRD_SqlColumnInt(stmt, 0);
        ASSERT_EQ(id, 1);
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_004
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_004, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select id, repr FROM t1 WHERE id = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get id
        int64_t id = GRD_SqlColumnInt64(stmt, 0);
        ASSERT_EQ(id, 1);
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_005
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_005, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select weight, repr FROM t1 WHERE id = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get weight
        double weight = GRD_SqlColumnDouble(stmt, 0);
        ASSERT_DOUBLE_EQ(weight, 3.0);
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_006
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_006, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select weight, name FROM t1 WHERE id = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get name
        const char *name = GRD_SqlColumnText(stmt, 1);
        ASSERT_STREQ(name, "Bella");
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_007
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_007, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, home blob, repr floatvector(16));");

    // insert data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "INSERT INTO t1 VALUES(1, 'Bella', 3.0, ?, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlBindBlob(stmt, 1, "?!2@$1.235454sdasd84\0", 21u, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlStep(stmt);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // query data
    sql = "select home FROM t1 WHERE id = 1;";
    len = (uint32_t)(strlen(sql) + 1);
    stmt = nullptr;
    ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get home
        const char *home = static_cast<const char *>(GRD_SqlColumnBlob(stmt, 0));
        ASSERT_STREQ(home, "?!2@$1.235454sdasd84\0");
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_008
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_008, TestSize.Level2)
{
    // create table

    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table t1(id int unique, name text, weight real, repr floatvector(16));");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(2, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(3, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select id, repr FROM t1 WHERE id = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    float expectVec[16] = {30, 12, 12, 25, 5, 2, 10, 8, 6, 18, 24, 19, 5, 3, 0, 4};
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get repr
        uint32_t dim = 0;
        const float *vector = GRD_SqlColumnFloatVector(stmt, 1, &dim);
        for (uint32_t i = 0; i < dim; ++i) {
            ASSERT_FLOAT_EQ(vector[i], expectVec[i]);
        }
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);

    ret = GRD_SqlReset(stmt);
    ASSERT_EQ(ret, GRD_OK);

    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get repr
        uint32_t dim = 0;
        const float *vector = GRD_SqlColumnFloatVector(stmt, 1, &dim);
        for (uint32_t i = 0; i < dim; ++i) {
            ASSERT_FLOAT_EQ(vector[i], expectVec[i]);
        }
    }
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);

    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_009
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: liangjialing
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_009, TestSize.Level2)
{
    // prepare stmt1
    GRD_StmtT *stmt1 = nullptr;
    GRD_StmtT *stmt2 = nullptr;
    const char *sql = "CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, repr floatvector(8));";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt1, nullptr);
    ASSERT_EQ(ret, GRD_OK);

    // step execute stmt1
    ret = GRD_SqlStep(stmt1);
    ASSERT_EQ(ret, GRD_OK);

    // prepare stmt2
    sql = "INSERT INTO test VALUES(1, '[1.2, 0.3, 3.2, 1.6, 2.5, 3.1, 0.8, 0.4]');";
    len = (uint32_t)(strlen(sql) + 1);
    ret = GRD_SqlPrepare(g_db, sql, len, &stmt2, nullptr);
    ASSERT_EQ(ret, GRD_OK);

    // finalize stmt1
    ret = GRD_SqlFinalize(stmt1);
    ASSERT_EQ(ret, GRD_OK);

    // step execute stmt2
    ret = GRD_SqlStep(stmt2);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_SqlColumnCount(stmt2);
    ASSERT_EQ(ret, GRD_OK);

    // finalize stmt2
    ret = GRD_SqlFinalize(stmt2);
    ASSERT_EQ(ret, GRD_OK);
}

/**
 * @tc.name: SqlBasicQueryForVector_010
 * @tc.desc: test GRD_SqlColumnType api when result set is null
 * @tc.type: FUNC
 * @tc.require: DTS2024032303644
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_010, TestSize.Level2)
{
    // create table
    GRD_StmtT *stmt = nullptr;
    const char *sql = "create table t1(id int unique, name text, weight real, repr floatvector(16));";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlStep(stmt);
    ASSERT_EQ(ret, GRD_OK);
    GRD_DbDataTypeE type = GRD_SqlColumnType(stmt, 1);
    ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // select empty table
    sql = "select id, repr FROM t1 WHERE id = 1;";
    len = (uint32_t)(strlen(sql) + 1);
    stmt = nullptr;
    ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_SqlStep(stmt);
    ASSERT_EQ(ret, GRD_NO_DATA);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);
    type = GRD_SqlColumnType(stmt, 1);
    ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");
    // query data
    GRD_DbValueT val;
    sql = "select id, repr FROM t1 WHERE id = 1;";
    len = (uint32_t)(strlen(sql) + 1);
    stmt = nullptr;
    ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    type = GRD_SqlColumnType(stmt, 1);
    ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
    float expectVec[16] = {30, 12, 12, 25, 5, 2, 10, 8, 6, 18, 24, 19, 5, 3, 0, 4};
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get id
        type = GRD_SqlColumnType(stmt, 1);
        ASSERT_EQ(type, GRD_DB_DATATYPE_FLOATVECTOR);

        type = GRD_SqlColumnType(stmt, 100);
        ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
        uint32_t dim = 0;
        const float *vector = GRD_SqlColumnFloatVector(stmt, 1, &dim);
        val = GRD_SqlColumnValue(stmt, 1);
        for (uint32_t i = 0; i < dim; ++i) {
            ASSERT_FLOAT_EQ(vector[i], expectVec[i]);
            ASSERT_FLOAT_EQ((static_cast<const float *>(val.value.strAddr))[i], expectVec[i]);
        }
    }
    type = GRD_SqlColumnType(stmt, 1);
    ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table t1;");
}

/**
 * @tc.name: SqlBasicQueryForVector_011
 * @tc.desc: run this testcase to test
 * @tc.type: FUNC
 * @tc.require: AR
 * @tc.author: liyichen18
 */
HWTEST_F(GrdSqlBasicApi, SqlBasicQueryForVector_011, TestSize.Level2)
{
    GRD_ThreadPoolT threadPool = {FusionSearch::ScheduleTest};
    Status threadRet = GRD_SqlRegistryThreadPool(g_db, &threadPool);
    ASSERT_EQ(threadRet, GRD_OK);
    // create table
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "create table table1(time int not null, name text, repr floatvector(16)) with (time_col  = 'time');");

    // insert data
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO table1 VALUES(1145141919, 'Bella', '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO table1 VALUES(2, 'Bella', '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");
    GrdSqlTestTool::ExecuteSingleSql(
        g_db, "INSERT INTO table1 VALUES(3, 'Bella', '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');");

    // query data
    GRD_DbDataTypeE type;
    GRD_DbValueT val = {};
    GRD_StmtT *stmt = nullptr;
    const char *sql = "select time, repr FROM table1 WHERE time = 1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    int32_t ret = GRD_SqlPrepare(g_db, sql, len, &stmt, nullptr);
    ASSERT_EQ(ret, GRD_OK);
    float expectVec[16] = {30, 12, 12, 25, 5, 2, 10, 8, 6, 18, 24, 19, 5, 3, 0, 4};
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        // get id
        type = GRD_SqlColumnType(stmt, 1);
        ASSERT_EQ(type, GRD_DB_DATATYPE_FLOATVECTOR);
        uint32_t dim = 0;
        const float *vector = GRD_SqlColumnFloatVector(stmt, 1, &dim);
        val = GRD_SqlColumnValue(stmt, 1);
        for (uint32_t i = 0; i < dim; ++i) {
            ASSERT_FLOAT_EQ(vector[i], expectVec[i]);
            ASSERT_FLOAT_EQ((static_cast<const float *>(val.value.strAddr))[i], expectVec[i]);
        }
    }
    type = GRD_SqlColumnType(stmt, 1);
    ASSERT_EQ(type, GRD_DB_DATATYPE_NULL);
    ASSERT_TRUE(ret == GRD_NO_DATA || ret == GRD_OK);
    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(ret, GRD_OK);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table table1;");
}

string GenerateLiteralFloatVector(float start, float end, int dim)
{
    string str = "[";
    for (int i = 1; i <= dim; i++) {
        str += "3";
        if (i != dim) {
            str += ",";
        }
    }
    str += "]";
    return str;
}

/**
 * @tc.name: ReopenAndQueryWithIvfflatIndex
 * @tc.desc: test reopen and select
 * @tc.type: FUNC
 * @tc.require: DTS2024031604145
 * @tc.author: yangbingjie
 */
HWTEST_F(GrdSqlBasicApi, ReopenAndQueryWithIvfflatIndex, TestSize.Level2)
{
    // create table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "CREATE TABLE T1(id int unique, label varchar(20), repr floatvector(128));");

    // insert data
    uint32_t count = 50;
    for (uint32_t i = 0; i < count; i++) {
        string str = GenerateLiteralFloatVector(1, 10, 128);
        GrdSqlTestTool::ExecuteSingleSql(g_db, "INSERT INTO T1 VALUES(" + to_string(i) + ", '新闻', '" + str + "');");
    }

    // create ivf index
    GrdSqlTestTool::ExecuteSingleSql(g_db, "CREATE INDEX IF NOT EXISTS index_name ON T1 USING GSIVFFLAT(repr L2);");
    // update data
    for (uint32_t i = 60; i < 70; i++) {
        string updateVecStr = GenerateLiteralFloatVector(1, 10, 128);
        GrdSqlTestTool::ExecuteSingleSql(
            g_db, "UPDATE T1 SET repr = '" + updateVecStr + "' WHERE id = " + to_string(i) + ";");
    }

    ReOpenDb();

    string querySql = "SELECT * FROM T1 WHERE id < 60;";
    GRD_StmtT *stmt = nullptr;
    int32_t ret = GRD_SqlPrepare(g_db, querySql.c_str(), querySql.length() + 1, &stmt, nullptr);
    ASSERT_EQ(GRD_OK, ret);
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
    }
    ASSERT_TRUE(ret == GRD_OK || ret == GRD_NO_DATA);

    ret = GRD_SqlFinalize(stmt);
    ASSERT_EQ(GRD_OK, ret);

    // drop table
    GrdSqlTestTool::ExecuteSingleSql(g_db, "drop table T1;");
}

static string SqlGenDim(int dimension)
{
    float minVal = 0.1;
    float maxVal = 999.9;
    stringstream ss;
    ss << "'[";
    // 设置随机数生成器
    random_device rd;
    mt19937 gen(rd());
    uniform_real_distribution<float> dis(minVal, maxVal);

    // 生成随机向量
    for (int i = 0; i < dimension; ++i) {
        // 生成保留1位小数的随机浮点数，并转换为字符串
        float randomNum = dis(gen);
        ss << fixed << setprecision(1) << randomNum;
        if (i < dimension - 1)
            ss << ",";
    }
    ss << "]'";
    return ss.str();
}

/**
 * @tc.name: VectorOper1to1024
 * @tc.desc: Create Insert Drop Delete
 * @tc.type: FUNC
 * @tc.require: DTS2024041220221
 * @tc.author: wuyichen
 */
HWTEST_F(GrdSqlBasicApi, VectorOper1to1024, TestSize.Level2)
{
    GRD_StmtT *stmt = nullptr;
    for (uint32_t j = 0; j < 2; j++) {
        // 维度过大时，单个节点大小会超过页大小，预期是检测出非法参数
        bool dimOverSizeFlg = false;
        for (uint32_t i = 1; i <= MAX_DIM_DISKANN; ++i) {
            // create table
            string sql = "CREATE TABLE T1(id int unique, repr floatvector(" + to_string(i) + "));";
            GrdSqlTestTool::ExecuteSingleSql(g_db, sql);

            // insert data
            sql = "INSERT INTO t1 VALUES(1, " + SqlGenDim(i) + ");";
            GrdSqlTestTool::ExecuteSingleSql(g_db, sql);
            // create diskAnn index
            if (j == 0) {
                sql = "CREATE INDEX index_for_t1 on t1 using GSDISKANN(repr L2);";
            } else {
                sql = "CREATE INDEX index_for_t1 on t1 using GSIVFFLAT(repr L2);";
            }
            stmt = nullptr;
            int32_t ret = GRD_SqlPrepare(g_db, sql.c_str(), sql.length() + 1, &stmt, nullptr);
            ASSERT_EQ(GRD_OK, ret);
            ret = GRD_SqlStep(stmt);
            // 第一次返回GRD_INVALID_ARGS时视为维度超过阈值，此后都应该返回GRD_INVALID_ARGS
            if (ret == GRD_INVALID_ARGS) {
                dimOverSizeFlg = true;
            }
            if (dimOverSizeFlg) {
                ASSERT_EQ(GRD_INVALID_ARGS, ret);
            } else {
                ASSERT_EQ(GRD_OK, ret);
            }
            ret = GRD_SqlFinalize(stmt);
            ASSERT_EQ(GRD_OK, ret);

            // drop diskAnn index
            sql = "DROP INDEX IF EXISTS t1.index_for_t1;";
            GrdSqlTestTool::ExecuteSingleSql(g_db, sql);

            // delete data
            sql = "DELETE FROM t1;";
            GrdSqlTestTool::ExecuteSingleSql(g_db, sql);

            // drop table
            sql = "DROP TABLE t1;";
            GrdSqlTestTool::ExecuteSingleSql(g_db, sql);
        }
    }
}
