/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <chrono>
#include <random>
#include <string>
#include "grd_sql_test_tool.h"

using namespace testing::ext;
using namespace std;
using namespace chrono;

string GenerateLiteralFloatVector(float start, float end, int dim);

static char g_cfg[] = R"(
{
    "pageSize": 16,
    "bufferPoolSize": 40960,
    "crcCheckEnable": 1,
    "redoFlushByTrx": 1,
    "sharedModeEnable": 1
})";
static char g_encryptedCfg[] = R"(
{
    "pageSize": 16,
    "bufferPoolSize": 40960,
    "crcCheckEnable": 1,
    "redoFlushByTrx": 1,
    "sharedModeEnable": 1,
    "isEncrypted": 1,
    "hexPassword": "abc123"
})";
static const char *g_fileNamePath = "./data/gmdb/datafile";
static const char *g_filePath = "./data/gmdb/";

class GrdSqlRepair : public testing::Test {
public:
    const char *tableName[10] = {"repair_table_01", "repair_table_02", "repair_table_03", "repair_table_04",
        "repair_table_05", "repair_table_06", "repair_table_07", "repair_table_08", "repair_table_09",
        "repair_table_10"};

    static void SetUpTestCase(void)
    {}
    static void TearDownTestCase(void)
    {}
    void SetUp(void)
    {
        EXPECT_EQ(DbTestTool::RemoveDir(g_filePath), 0);
        EXPECT_EQ(DbTestTool::MakeDir(g_filePath), 0);
    }
    void TearDown(void)
    {}
};

void GrdUtQueryTableDataCount(GRD_DB *db, const string &tableName, uint32_t *count)
{
    string sqlStr = "SELECT * FROM " + tableName;
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    EXPECT_EQ(0, GRD_SqlPrepare(db, sqlStr.c_str(), sqlStr.length(), &stmt, &unused));
    int32_t ret;
    *count = 0;
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        (*count)++;
    }
    EXPECT_EQ(GRD_OK, GRD_SqlReset(stmt));
    EXPECT_EQ(GRD_OK, GRD_SqlFinalize(stmt));
}

/**
 * @tc.name: SqlRepair01
 * @tc.desc: insert a simple table and ten items
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair01, TestSize.Level0)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdSqlTestTool::ExecuteSingleSql(db, "CREATE TABLE " + string(tableName[0]) + "(id int)");

    for (uint32_t i = 0; i < 10; i++) {
        GrdSqlTestTool::ExecuteSingleSql(db, "INSERT INTO " + string(tableName[0]) + " VALUES(" + to_string(i) + ")");
    }
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);
    uint32_t count;
    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdUtQueryTableDataCount(db, tableName[0], &count);
    EXPECT_EQ(count, 10);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
}

/**
 * @tc.name: SqlRepair02
 * @tc.desc: insert a simple table and three items with constrain
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair02, TestSize.Level0)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    string sql = "CREATE TABLE " + string(tableName[0]) + " (id INT PRIMARY KEY, \
                                                            name TEXT,\
                                                            age int CHECK (age BETWEEN 16 AND 35));";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(1, 'zhangsan', 17);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(2, 'lisi', 19)";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(3, 'wangwu', 22)";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);
    uint32_t count;
    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdUtQueryTableDataCount(db, tableName[0], &count);
    EXPECT_EQ(count, 3);
    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(4, 'wuyanzu', 44)";
    GrdSqlTestTool::ExecuteSingleSql(db, sql, GRD_OK, GRD_CONSTRAINT_CHECK_VIOLATION);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
}

#ifdef GAUSSPD_DISABLED_TDD
/**
 * @tc.name: SqlRepair03
 * @tc.desc: insert two table and add foreign key
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.003
 * @tc.author: xujun
 */

HWTEST_F(GrdSqlRepair, DISABLED_SqlRepair03, TestSize.Level0)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    string sql = "CREATE TABLE " + string(tableName[0]) + " (id INT PRIMARY KEY, \
                                                            name TEXT,\
                                                            age int);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(1, 'zhangsan', 17);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(2, 'lisi', 19)";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(3, 'wangwu', 22)";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    sql = "CREATE TABLE " + string(tableName[1]) + " (id INT REFERENCES " + string(tableName[0]) + "(id),\
                                                      goal INT,\
                                                      school TEXT\
                                                      );";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[1]) + " VALUES(1, 97, 'qinghua');";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[1]) + " VALUES(2, 98, 'beida');";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[1]) + " VALUES(4, 88,'fudan')";
    GrdSqlTestTool::ExecuteSingleSql(db, sql, GRD_OK, GRD_CONSTRAINT_CHECK_VIOLATION);

    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);
    uint32_t count;
    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdUtQueryTableDataCount(db, tableName[0], &count);
    EXPECT_EQ(count, 3);

    sql = "INSERT INTO " + string(tableName[1]) + " VALUES(3, 99,'zheda')";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    sql = "INSERT INTO " + string(tableName[1]) + " VALUES(4, 88,'fudan')";
    GrdSqlTestTool::ExecuteSingleSql(db, sql, GRD_OK, GRD_CONSTRAINT_CHECK_VIOLATION);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
}
#endif  // GAUSSPD_DISABLED_TDD

/**
 * @tc.name: SqlRepair04
 * @tc.desc: insert a simple table and create index
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair04, TestSize.Level0)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    string sql = "CREATE TABLE " + string(tableName[0]) + " (id INT, \
                                                            name TEXT,\
                                                            age int);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    sql = "CREATE INDEX id_index ON " + string(tableName[0]) + "(id);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(3, 'zhangsan', 17);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[0]) + " (id, age) VALUES(1, 19);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    sql = "INSERT INTO " + string(tableName[0]) + " VALUES(2, 'wangwu', 22)";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);

    string sqlStr = "SELECT * FROM " + string(tableName[0]) + " where id > 0;";
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    EXPECT_EQ(0, GRD_SqlPrepare(db, sqlStr.c_str(), sqlStr.length(), &stmt, &unused));
    uint32_t prevId = 0;
    while ((ret = GRD_SqlStep(stmt)) == GRD_OK) {
        uint32_t id = GRD_SqlColumnInt(stmt, 0);
        EXPECT_GT(id, prevId);
        prevId = id;
    }
    EXPECT_EQ(GRD_OK, GRD_SqlFinalize(stmt));

    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
}

void CorruptFile(const char *fileName, int64_t offset)
{
    int fd = open(fileName, O_RDWR);
    ASSERT_GT(fd, 0);
    uint64_t buf = 0xabcddbca;
    lseek(fd, offset + 1024, SEEK_SET);
    write(fd, &buf, sizeof(uint64_t));
    close(fd);
}
/**
 * @tc.name: SqlRepair05
 * @tc.desc: insert a simple table and ten thousand item, then damage page
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair05, TestSize.Level3)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    string sql = "CREATE TABLE " + string(tableName[0]) + " (id INT);";
    GrdSqlTestTool::ExecuteSingleSql(db, sql);

    const uint32_t itemCount = 10000;
    for (uint32_t i = 0; i < itemCount; i++) {
        sql = "INSERT INTO " + string(tableName[0]) + " VALUES(" + to_string(i) + ");";
        GrdSqlTestTool::ExecuteSingleSql(db, sql);
    }
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    // gdb 调试到i = 5000时，数据插入到page [26, 0] ->跟踪SpaceWriteBlock offset = 380928
    CorruptFile("./data/gmdb/datafile", 380928);

    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);

    uint32_t count;
    GrdUtQueryTableDataCount(db, tableName[0], &count);
    EXPECT_LT(count, itemCount);
    EXPECT_GT(count, itemCount - 4096 / 4);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
}

/**
 * @tc.name: SqlRepair06
 * @tc.desc: insert a simple table and create IVF
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair06, TestSize.Level3)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdSqlTestTool::ExecuteSingleSql(db, "CREATE TABLE T1(id int unique, label varchar(20), repr floatvector(128));");

    // insert data
    string str;
    uint32_t count = 50;
    for (uint32_t i = 0; i < count; i++) {
        str = GenerateLiteralFloatVector(1, 10, 128);
        GrdSqlTestTool::ExecuteSingleSql(db, "INSERT INTO T1 VALUES(" + to_string(i) + ", '新闻', '" + str + "');");
    }

    // create ivf index
    GrdSqlTestTool::ExecuteSingleSql(db, "CREATE INDEX IF NOT EXISTS index_name ON T1 USING GSIVFFLAT(repr L2);");

    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    str = GenerateLiteralFloatVector(1, 10, 128);
    string querySql = "SELECT * FROM T1 ORDER BY repr <-> '" + str + "' LIMIT 10;";

    vector<string> keywords = {"VectorIndexScan"};
    vector<string> exclusiveKeywords = {};
    GrdSqlTestTool::CheckExecPlan(db, querySql, keywords, exclusiveKeywords);

    uint32_t num;
    GrdUtQueryTableDataCount(db, "T1", &num);
    EXPECT_EQ(count, num);
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
}

void *ThreadMain(void *args)
{
    const uint32_t preInsertNum = 1000;
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    EXPECT_EQ(ret == GRD_OK || ret == GRD_DB_BUSY, true) << "ret = " << ret;
    ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_ONLY, &db);
    EXPECT_EQ(ret == GRD_OK || ret == GRD_DB_BUSY, true);
    const uint32_t cnt = 600;
    const uint32_t diff = 500;
    if (ret == GRD_OK) {
        uint32_t count;
        GrdUtQueryTableDataCount(db, "T1", &count);
        EXPECT_GE(count, preInsertNum);
        EXPECT_LE(count, preInsertNum + cnt);
        uint32_t num = 100;
        if (count >= preInsertNum + diff) {
            for (uint32_t i = 0; i < num; i++) {
                GrdSqlTestTool::ExecuteSingleSql(db, "DELETE FROM T1 WHERE id = " + to_string(count - i) + ";");
            }
        } else {
            for (uint32_t i = 0; i < num; i++) {
                GrdSqlTestTool::ExecuteSingleSql(
                    db, "INSERT INTO T1 VALUES(" + to_string(i + count) + "," + to_string(i + 1 + count) + ");");
            }
        }
        ret = GRD_DBClose(db, GRD_DB_CLOSE);
        EXPECT_EQ(ret, GRD_OK);
    }
    return nullptr;
}

/**
 * @tc.name: SqlRepair07
 * @tc.desc: repair in multiprocess
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair07, TestSize.Level3)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdSqlTestTool::ExecuteSingleSql(db, "CREATE TABLE T1(id int unique, grade int);");

    const int32_t preInsertNum = 1000;
    for (int32_t i = 0; i < preInsertNum; i++) {
        GrdSqlTestTool::ExecuteSingleSql(db, "INSERT INTO T1 VALUES(" + to_string(i) + "," + to_string(i + 1) + ");");
    }
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    for (int32_t i = 0; i < 8; i++) {
        pid_t pid = fork();
        if (pid == 0) {
            ThreadMain(nullptr);
            exit(0);
        } else {
            ASSERT_GT(pid, 0);
            ThreadMain(nullptr);
        }
    }

    for (int32_t i = 0; i < 8; i++) {
        wait(nullptr);
    }
}
/**
 * @tc.name: SqlRepair08
 * @tc.desc: repair in multiThread
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20240127001783.011.002
 * @tc.author: xujun
 */
HWTEST_F(GrdSqlRepair, SqlRepair08, TestSize.Level3)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdSqlTestTool::ExecuteSingleSql(db, "CREATE TABLE T1(id int unique, grade int);");

    const int32_t preInsertNum = 1000;
    for (int32_t i = 0; i < preInsertNum; i++) {
        GrdSqlTestTool::ExecuteSingleSql(db, "INSERT INTO T1 VALUES(" + to_string(i) + "," + to_string(i + 1) + ");");
    }
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    pthread_t tid[8];
    for (int32_t i = 0; i < 8; i++) {
        pthread_create(tid + i, nullptr, ThreadMain, nullptr);
    }
    sleep(1);
    for (int32_t i = 0; i < 8; i++) {
        pthread_join(tid[i], nullptr);
    }
}

/**
 * @tc.name: SqlRepair09
 * @tc.desc: insert data with encrypted and repair without encrypted
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078080.010.001
 * @tc.author: pengweijun
 */
HWTEST_F(GrdSqlRepair, SqlRepair09, TestSize.Level0)
{
    GRD_DB *db = nullptr;
    int32_t ret = GRD_DBOpen(g_fileNamePath, g_cfg, GRD_DB_OPEN_CREATE, &db);
    ASSERT_EQ(ret, GRD_OK);
    GrdSqlTestTool::ExecuteSingleSql(db, "CREATE TABLE " + string(tableName[0]) + "(id int)");

    for (uint32_t i = 0; i < 20; i++) {
        GrdSqlTestTool::ExecuteSingleSql(db, "INSERT INTO " + string(tableName[0]) + " VALUES(" + to_string(i) + ")");
    }
    ret = GRD_DBClose(db, GRD_DB_CLOSE);
    ASSERT_EQ(ret, GRD_OK);
    ret = GRD_DBRepair(g_fileNamePath, g_cfg);
    ASSERT_EQ(ret, GRD_OK);

    ret = GRD_DBRepair(g_fileNamePath, g_encryptedCfg);
    ASSERT_EQ(GRD_PASSWORD_NEED_REKEY, ret);
}
