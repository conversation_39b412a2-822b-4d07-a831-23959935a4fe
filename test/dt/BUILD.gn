# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")

module_output_path = "arkdata_database_core/arkdata_database_core/gaussdb_rd_kernel_vector_unittest"
gaussdb_rd_kernel_vector_path = "../../platform/gaussdb_rd_kernel_vector"
grd_path = "../../"

config("module_private_config") {
  visibility = [ ":*" ]

  include_dirs = [
    "$gaussdb_rd_kernel_vector_path/src_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/linux",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/linux/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/linux/ext_linux",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/parser",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/datalog",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/fastpath",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/gql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/ir",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/kv",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/mini",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/optimization",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/public",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/shared_obj",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/sql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/ts",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/include/yang",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/cost_model",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/transformation",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/search_algo",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/search_space",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/fusion",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/parser",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/yang",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/yang/parser/fastpath",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/cu",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/tree",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/tree/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/yang",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/include/mini",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/kv",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/namespace",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/namespace/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/privilege",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/privilege/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/subscription",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/subscription/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/topology",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/udf",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metacache",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metacache/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metadata",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metadata/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/sharedobj",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/sharedobj/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/namespace",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/namespace/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/sql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/sql/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/gql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/binlog",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/equip",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/component_fmw",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/fmw",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/sync",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/sync/cloud",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/aa_am",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/container",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/container/temptable",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/tuple_am",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/check",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/control",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/kv",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/namespace",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/observer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/observer/template",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/observer/timeout",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/privilege",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/public",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/pubsub",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/pubsub/async",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/pubsub/sync",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/resource",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/tablespace",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/topology",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/base/expr",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/base/util",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/extended",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/kv",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/kv/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/property",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/property/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/topology",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/fpexec/topology/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/include/mini",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/include/mini/sharedobj",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/procedure",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/scan",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/sort",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/receiver",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/view",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/abnormity/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/connection/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/flowctrl/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/instance/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/schedule/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/runtime_terminal/worker/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/datalog",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/datalog/dtlservice",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/datalog/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/fastpath",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/kv",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/public",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/shared_obj",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/yang/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/boot",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/sql",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/view",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/bufferpool/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/device/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/durable_memdata/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/memdata/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/rsm_block/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/tablespace/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/temp_file",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/persistence/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/clustered_hash",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fixed_heap",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fsm",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fsm/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fsm/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_quantizer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_cluster",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_support/merge_based_cluster/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_support/merge_based_cluster/src",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/algo",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/algo",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/art_base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/art_lpm",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/art_sorted",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree_common",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree_common",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_base/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_chained",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cluster",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_linklist",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_base",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/fast_update_list",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/page_list",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmadm/gmadm_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmadmin/gmadmin_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmasst/gmasst_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmcmd/gmcmd_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmddl/gmddl_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmerror/gmerror_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmexport/gmexport_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmids/gmids_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmimport/gmimport_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmips/gmips_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmlog/gmlog_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmrule/gmrule_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmserver/gmserver_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/gmsysview/gmsysview_inner",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/tools_terminal/utils",

    # for stub
    "mini_terminal/stub/",

    # for ut_gql
    "mini_terminal/ut/gql",
    "mini_terminal/ut/gql/analyzer",
    "mini_terminal/ut/gql/datamodel",
    "mini_terminal/ut/gql/executor",
    "mini_terminal/ut/gql/parser",

    # for ut_sql
    "mini_terminal/ut/sql",

    # for mini_st_012_emb_sql
    "mini_terminal/st/012_emb_sql/01_common",
    "mini_terminal/st/012_emb_sql/02_frame",
    "mini_terminal/st/012_emb_sql/03_utility",
    "mini_terminal/st/012_emb_sql/04_feature/vector",
    "mini_terminal/st/012_emb_sql/05_performance/base/com",
    "mini_terminal/st/012_emb_sql/05_performance/base/emb_sql",
    "mini_terminal/st/012_emb_sql/05_performance/base/sqlite",

    # for mini_st_014_emb_gql
    "mini_terminal/st/014_emb_gql/01_common",
    "mini_terminal/st/014_emb_gql/01_common/gql_data_transfer",
    "mini_terminal/st/014_emb_gql/02_feature",

    # grd util
    "GRD_terminal/ut/utility",

    # for dt_GRD_ut_executor_sql
    "GRD_terminal/ut/executor/sql",

    # for dt_GRD_ut_executor_gql
    "GRD_terminal/ut/executor/gql",
    "GRD_terminal/ut/executor/gql/gql_data_transfer",

    # mini st util
    "mini_terminal/st/util",

    # for dt_st_hash
    "mini_terminal/st/008_index",

    # for dt_ut_hash
    "mini_terminal/ut/storage",
    "mini_terminal/ut/storage/hash",
    "mini_terminal/ut/common_test_include",

    # for mini_st_008_index
    "mini_terminal/st/008_index/",

    # for mini_ut
    "mini_terminal/ut/common_test_include",

    # for mini_ut_datamodel
    "mini_terminal/ut/datamodel",
    "mini_terminal/ut/datamodel/common",

    # for mini_ut_storage
    "mini_terminal/ut/storage",

    # for mini_ut_storage_diskann
    "mini_terminal/ut/storage/ann_diskann",
    "mini_terminal/ut/storage/ann_common",

    # for mini_ut_storage_ivfcluster
    "mini_terminal/ut/storage/ann_ivfcluster",

    # for mini_ut_storage_btree
    "mini_terminal/ut/storage/btree",

    # for mini_ut_storage_hash
    "mini_terminal/ut/storage/hash",

    # for mini_ut_storage_inverted
    "mini_terminal/ut/storage/inverted_index",

    # for mini_ut_distribution
    "mini_terminal/ut/distribution",
    "mini_terminal/ut/distribution/common",

    # for mini_ut_distribution_cache
    "mini_terminal/ut/distribution/cache",

    # for mini_ut_distribution_oplog
    "mini_terminal/ut/distribution/oplog",

    # for mini_ut_distribution_binlog
    "mini_terminal/ut/distribution/binlog",
  ]

  include_dirs += [
    "$target_out_dir",
  ]

  defines = [
    "HI_SYS_EVENT",
    "HARMONY_LOG",
    "HARMONY_OS",
    "HASH_USE_OPENSSL",
    "FEATURE_MINIKV",
    "FEATURE_SQL",
    "FEATURE_GQL",
    "FEATURE_IVFFLAT",
    "FEATURE_IVFTREE",
    "FEATURE_IVFCLUSTER",
    "FEATURE_DISKANN",
    "FEATURE_LPASMEM",
    "FEATURE_DISTRIBUTION",
    "FEATURE_PERSISTENCE",
    "SINGLE_SO",
    "FEATURE_BUFFERPOOL",
    "FEATURE_BTREE",
    "FEATURE_HASH",
    "FEATURE_FASTVEC",
    "FEATURE_INVERTED",
  ]
  cflags_cc = [
    "-O0",
    "-g",
    "-Wno-missing-braces",
    "-Wno-unused-const-variable",
    "-Wno-unused-variable",
    "-Wno-unused-function",
    "-pthread"
  ]
}

config("grd_v5_kernel_public_config") {
  visibility = [ "*:*" ]
  include_dirs = [
    "$gaussdb_rd_kernel_vector_path/pub_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/alarm",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/buffer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/compression",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/config",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/error",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/flowctrl",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/lock",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/log",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/memory",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/notifychan",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/patch",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/protocol",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/rsmem",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/session",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/signal",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/sysview",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/task",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/utils",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/logger",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/memory",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/include",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/include/config",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/include/process",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/session",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/include",

    # for gme
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/base",

    # for grd
    "$grd_path/include",
    "$grd_path/include/grd_base",
    "$grd_path/include/grd_document",
    "$grd_path/include/grd_fast_vec",
    "$grd_path/include/grd_gql",
    "$grd_path/include/grd_kv",
    "$grd_path/include/grd_shared_obj",
    "$grd_path/include/grd_sql",
    "$grd_path/include/grd_tokenizer",
    "$grd_path/include/grd_sync",
    "$grd_path/src/common_terminal",
    "$grd_path/src/executor_terminal/base",
    "$grd_path/src/executor_terminal/document",
    "$grd_path/src/executor_terminal/kv",
    "$grd_path/src/oh_adapter/posix",
  ]
  cflags = [
    "-Os",
    "-fvisibility=hidden",
    "-Wno-sometimes-uninitialized",
    "-fsigned-char",
    "-Wdate-time",
    "-Wfloat-equal",
    "-Wno-pointer-arith",
    "-Wno-typedef-redefinition",
    "-Wno-unused-variable",
    "-Wno-unused-parameter",
    "-Wno-unused-function",
    "-g",
  ]
}

###############################################################################
action("test_build_action") {
  visibility = [ ":*" ]
  script = "$gaussdb_rd_kernel_vector_path/scripts/build_scripts/build_action.sh"
  args = [
    rebase_path("$target_out_dir"),
  ]
  outputs = [
    "$target_out_dir/lex.sql_.c",
    "$target_out_dir/sql.tab.c",
    "$target_out_dir/lex.gql_.c",
    "$target_out_dir/gql.tab.c",
    "$target_out_dir/lex.sql_invert_.c",
    "$target_out_dir/sql_invert.tab.c",
    "$target_out_dir/lex.gql_invert_.c",
    "$target_out_dir/gql_invert.tab.c",
  ]
}

ohos_source_set("src_file") {
  testonly = true

  sources = [
    # harmony log adapter file, DO NOT EDIT
    "$grd_path/src/oh_adapter/harmony/harmony_log_adpt.c",

    # other files
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_define.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_dfx.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_file.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_function_loader.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_io.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_log.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_log_write.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_memory.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_persist.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_rdtsc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_semaphore.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_spinlock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_startup_time.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_time.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/base/adpt_mini_string.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/adpt_mini_file_linux.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/adpt_mini_io.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/adpt_mini_process_id.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/adpt_mini_process_lock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/adpt_mini_process_name_linux.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/adpt_mini_thread.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/ext_linux/adpt_mini_mem_blockpool_euler_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/ext_linux/adpt_mini_mem_group_euler_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/ext_linux/adpt_mini_mem_os_euler_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/ext_linux/adpt_mini_mem_pagepool_euler_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linux/ext_linux/adpt_mini_mem_segment_euler_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/adapter_terminal/mini/linuxexclusives/adpt_mini_log_linux.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/buffer/db_msg_buffer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/buffer/db_tuple_buffer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/lock/db_inter_process_rwlatch.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/lock/db_label_latch_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/logger/db_last_error.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/memory/db_arena.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/memory/db_extmem.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/alarm/db_mini_alarm.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/bfdict.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/easyList.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/encode.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/hashdict.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/keywordExtractor.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/mmseg.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/cntokenizer/tokenizer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/instance/db_instance.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/instance/db_json_adpt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/lock/db_mini_file_lock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/lock/db_mini_rwlatch.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/logger/db_mini_log.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_dynmem_algo_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_file_map_shmem.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_file_map.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_mem_context_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_share_mem_context_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_shmem_blockpool_algo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_shmem_pagepool_algo_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_top_dynmem_ctx_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/db_mini_top_shmem_ctx.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/memory/mini_simulate_shmem.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/session/db_mini_resource_session.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/sysview/db_mini_sysview_instance.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/task/db_mini_storage_dfgmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/task/db_mini_thread_pool_tls.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_common_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_config.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_crc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_dead_loop_dummy.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_dyn_array.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_dyn_load.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_error.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_galist.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_hash_map.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_hash.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_json.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_namepath_dummy.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_string.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_sysapp_context.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_mini_timer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/mini/utils/db_recycle_stack.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/process/db_process_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/utils/db_list.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/utils/db_priority_queue.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/utils/db_queue.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/utils/db_shm_array.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/utils/db_text.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/common_terminal/utils/db_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/base/cpl_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_alter_graph.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_build_op.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_call_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_context.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_create_graph.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_create_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_delete_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_drop_graph.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_drop_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_exec_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_insert_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_match_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_merge_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_order_op.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_pattern.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_pragma.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_return_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_set_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_transaction_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/analyzer/cpl_gql_analyzer_verify.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/parser/cpl_gql_parser_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/parser/cpl_gql_parser.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/gql/rewriter/cpl_gql_rewriter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_aa_schema.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_func_op_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_item_op_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_leaf_op.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_logical_op_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_op_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_op.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_physical_op_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/cpl_ir_plan_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain/cpl_ir_explain_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain/cpl_ir_explain_func.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain/cpl_ir_explain_item.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain/cpl_ir_explain_logical.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain/cpl_ir_explain_physical.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/ir/explain/cpl_ir_explain.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/minicpl/kv/cpl_parser_ddl_mini_kv.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/minicpl/kv/cpl_parser_dml_mini_kv.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/minicpl/shared_obj/cpl_distribution_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/minicpl/shared_obj/cpl_parser_doc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_config.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_eq_class.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_explain.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_id_converter_g2l.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_id_converter_l2g.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_join_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/base/cpl_opt_optimizer_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/cost_model/cpl_opt_cost_physical.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/cost_model/cpl_opt_statistics.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/cpl_opt_optimizer_cascades.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/cpl_opt_rule_binding.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/cpl_opt_rule_search_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/cpl_opt_rule_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/cpl_opt_rule.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_compound.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_cud.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_distinct.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_expr_project.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_extend_order_by.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_filter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graph_call_proc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graph_merge.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graph_order_by.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graphexpand.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graphmodify.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graphscan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_hash_agg.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_inverted_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_graph_inverted_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_join.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_limit.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_link.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_mixed_vector_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_result.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_subqueryscan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_vector_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_cluster_vector_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/implementation/cpl_opt_rule_indexing_scan_for_select.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/transformation/cpl_opt_rule_limit_push_down_expr_project.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/rule/transformation/cpl_opt_rule_select_push_down.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/search_algo/cpl_opt_searchalgo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/search_space/cpl_opt_group.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/search_space/cpl_opt_memo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/optimizer/search_space/cpl_opt_multi_expression.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/cpl_opt_planner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_builtin.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_compound.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_cud.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_distinct.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_expr_project.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_extend_sort.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_filter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_graphexpand.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_graphmodify.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_graphscan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_graphcallproc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_hashAgg.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_limit.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_link.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_merge.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_nestloop.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_result.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_seq_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_subquery_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_vector_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_cluster_vector.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/cpl_opt_plangenerator_inverted_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/plangenerator/fusion/cpl_plangenerator_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/optimization/planner/cpl_opt_planner_serializer_stub.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/persistence/cpl_persistence.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/analyzer/cpl_public_analyzer_build_expr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/analyzer/cpl_public_analyzer_common_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/analyzer/cpl_public_sql_analyzer_build_op.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_parser_ddl_index_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_parser_ddl_schema_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_parser_ddl_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_parser_ddl_vertex_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_expr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_keywords.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_sql_parser_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_json_parser_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/public/parser/cpl_public_parser_ddl_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_create_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_create_table.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_create_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_create_view.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_drop.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_pragma.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_returning.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_set_parameter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_transaction.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_analyzer_alter_table.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_build_expr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_build_ir_tree.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_build_ir_tree_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_build_ir_tree_update.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_build_op.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_symbol_table.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_verify_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_verify_expr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_verify_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_verify_select.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/analyzer/cpl_sql_verify_update.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/parser/cpl_sql_parser.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/compiler_terminal/sql/rewriter/cpl_sql_rewriter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/base/dm_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/base/dm_systbl.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/base/dm_table_lock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/base/dm_table_lock_basic.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/ann/dm_data_annvector.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base/dm_data_basic.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base/dm_data_math.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base/dm_data_print_json_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base/dm_data_print_to_sb.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/base/dm_data_print_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/kv/dm_data_kv_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_bit_field_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_indexkey_seri.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_indexkey_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_indexkeybuf.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop_buf_print_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop_delta_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop_desc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop_json_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop_print_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop_seri.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_prop.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_record_buf_print_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_record_desc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_record_seri.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/property/dm_data_record.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql/dm_data_constraint_sql_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql/dm_data_expr_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql/dm_data_index_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql/dm_data_math_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql/dm_data_trigger_info_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/sql/dm_data_view_info_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/topology/dm_data_edge_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/topology/dm_data_edge_topo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/topology/dm_data_edge_topo_print.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/tree/dm_data_node_buf_print_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/tree/dm_data_node_desc_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/tree/dm_data_node_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/dataobject/tree/dm_data_node_seri_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache/dm_cache_basic.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache/dm_cache_label_list.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache/dm_cache_label_map.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache/dm_cache_multi_ver_mgr_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metacache/dm_cache_single_ver_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/base/dm_meta_info_shm.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/base/dm_meta_key_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/base/dm_meta_list_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/dm_meta_basic_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/dm_meta_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/kv/dm_meta_kv_index_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/kv/dm_meta_kv_label_cache_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/kv/dm_meta_kv_label_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/kv/dm_meta_kv_label_oper_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/namespace/dm_meta_namespace_cache_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/namespace/dm_meta_namespace_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/dm_meta_common_info_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/dm_meta_index_label.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/dm_meta_prop_label_cache.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/dm_meta_prop_label_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/dm_meta_vertex_label.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/property/dm_meta_sql_prop_label_cache.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/tablespace/dm_meta_tablespace_cache.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/tablespace/dm_meta_tablespace_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/topology/dm_meta_graph_cache.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/topology/dm_meta_topo_label_cache.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/topology/dm_meta_topo_label_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/topology/dm_meta_topo_label_oper.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/metadata/tree/dm_meta_node_schema_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/base/dm_mini_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/dm_data_minikv.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metacache/dm_minicache.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metadata/dm_meta_mini_basic.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/metadata/kv/dm_meta_minikv_label.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/sharedobj/dm_shared_struct_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/sharedobj/dm_shared_struct_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/sharedobj/dm_shared_struct.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable/systable_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable/systable_de_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable/systable_de.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable/systable_kv.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/minidm/systable/systable.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/base/dm_systbl_basic_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/base/dm_systbl_heap_access.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/base/dm_systbl_list_util.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/namespace/dm_systbl_nsp_label_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property/dm_systbl_edge_label_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property/dm_systbl_index_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property/dm_systbl_prop_label_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property/dm_systbl_schema_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/property/dm_systbl_super_field_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/sql/dm_systbl_util_sql_in.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/datamodel_terminal/systable/gql/dm_systbl_gql_util.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/awareness/de_awareness.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/base/de_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/binlog/binlog_instance.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/binlog/binlog_read.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/binlog/binlog_write.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/binlog/binlog.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/equip/de_equip_info.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/component_fmw/de_awareness_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/component_fmw/de_equip_info_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/component_fmw/de_instance_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/component_fmw/de_oplog_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/component_fmw/de_sync_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/de_base_def.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/instance/de_instance.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog_adpt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog_apply.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog_event.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog_file.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog_metadata.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog_persistence.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/oplog/de_oplog.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/sync/cloud/de_schema.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/sync/de_sync_metadata.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/sync/de_sync_task.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/distribution_terminal/sync/de_sync.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/container/filetape/ee_access_file_tape.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_access_method_router.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_heap_label_access_method.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_inverted_access_method.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_heap_topo_label_access_method.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_index_access_method_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_index_access_method.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/access/ee_resource_access_method.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_associative_array_slot_edge.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_associative_array_slot_vertex.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_associative_array_slot.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_associative_array.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_concurrency_control.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_context.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_extern_table.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_feature_import.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_plan_node.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_session.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_sql_session.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_stmt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/ee_dummy.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_associative_array_slot_array_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_associative_array_slot_vertex_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_context_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_cursor_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_session_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_statistic_utils_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/base/mock/ee_vertex_map_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/base/ee_cmd_state_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/control/ee_ddl_trans_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/control/ee_dcl_trx_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/guc/ee_cmd_set_parameter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_cmd_index_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_cmd_normal_vertex_label.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_cmd_table_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_cmd_trigger_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_cmd_view_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_cmd_vertex_label_template_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/property/ee_ddl_vertex_label_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/public/ee_cmd_router_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/public/ee_key_cmp.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/public/ee_user_def_item.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/tablespace/ee_ddl_tablespace_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/cmdexec/topology/ee_cmd_graph_fusion.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/base/ee_mini_cursor.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/base/ee_mini_session.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/cmdexec/ee_mini_dcl.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/cmdexec/ee_mini_ddl.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/cmdexec/ee_mini_dml.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/cmdexec/ee_mini_process.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/dummy/ee_mini_sql_dummy.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/ee_mini_key_cmp.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_array_obj.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_obj_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_obj.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_text_format_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_undo_mgr_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_undo_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_doc_xml_element_obj.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_shared_obj_process.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_shared_struct_log.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_shared_struct_table_process.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_mini_shared_struct_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_shared_obj_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/miniexec/sharedobjexec/ee_shared_obj.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/persistence/ee_persist_flush.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/persistence/ee_persist_systbl.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/persistence/ee_persist.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/ee_path_array.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/ee_plan_state_router.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/expression/ee_expression_dump.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/expression/ee_expression_eval.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/expression/ee_expression_eval_projection.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/expression/ee_expression_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/expression/ee_expression_subquery.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/expression/ee_expression.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/function/ee_function_time.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/function/ee_function.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/procedure/ee_gql_graph.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/procedure/ee_gql_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/procedure/ee_lineage.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/procedure/ee_lineage_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/procedure/ee_procedure.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/base/slotmap/ee_slotmap.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/ee_plan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/inverted/ee_inverted_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_apply.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_argument.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_expand.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_link.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_merge.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_result.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_update.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/gql/ee_graph_call_procedure.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/vector/ee_vector_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/extended/vector/ee_cluster_vector_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/build/ee_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/builtin/ee_builtin.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/distinct/ee_distinct.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/hashagg/ee_hash_aggregate.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/join/ee_nest_loop.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/limit/ee_limit.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/scan/ee_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/scan/ee_scan_template.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/scan/ee_seq_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/scan/ee_subquery_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/sort/ee_sort.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/sort/ee_tuple_sort.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/property/union/ee_union.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public/delete/ee_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public/ee_constraints.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public/ee_returning.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public/insert/ee_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public/trigger/ee_trigger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/operators/public/update/ee_update.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/receiver/ee_receiver.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/executor_terminal/planexec/receiver/ee_table_receiver.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/boot/db_server.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/gql/srv_data_gql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/kv/srv_mini_portal.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/kv/srv_mini_prepare.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/shared_obj/srv_mini_shared_obj_portal.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/shared_obj/srv_mini_shared_obj_prepare.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/sql/srv_data_sql.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/sql/srv_data_sql_util.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/data/srv_data_service.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/base/gme_api.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/base/gme_backup_restore.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/boot/db_multi_process_init.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/boot/db_multi_process_recovery_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/boot/db_mini_server_undo_purger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/dummy/srv_mini_sql_dummy.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/dummy/srv_mini_sql_manager_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/gme_fast_vec_api.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/srv_emb_fast_vec.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/srv_emb_fast_vec_group.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/srv_emb_fast_vec_lvq.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/srv_emb_fast_vec_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/operations/srv_emb_fast_vec_create.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/operations/srv_emb_fast_vec_drop.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/operations/srv_emb_fast_vec_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/fast_vec/operations/srv_emb_fast_vec_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/gql/srv_emb_gql_api.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/kv/gme_kv.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/shared_obj/gme_doc_obj.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/sql/srv_emb_sql_api.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/sql/srv_emb_sql_base_api.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/sql/srv_emb_sql_util.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/sync/gme_awareness.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/service_terminal/embedded/sync/gme_sync.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_ann_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_ann_node_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_btree_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_ckpt_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_diskann_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/empty_function/se_mini_fixed_heap.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_hash_persistence.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_hash_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_heap_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_ivfflat_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_ivftree_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_ivfcluster_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_lfsmgr_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_meta_persist_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_page_list_redo_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_recovery.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/empty_function/se_mini_resource_column.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_space_am.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/component_fmw/se_space_am_split_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/se_instance.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/se_backup_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/instance/se_capacity_def_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/bufferpool/se_buffer_pool_file_map.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/bufferpool/se_buffer_pool_priority_recycle.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/bufferpool/se_buffer_pool_table_recycle.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/bufferpool/se_buffer_pool.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/bufferpool/se_cipher.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/device/se_device_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/page/se_page.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/se_page_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_database.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_database_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_datafile_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_backup_file.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_datafile.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_file.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_space_device.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_space_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_space.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/space/se_space_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/page_manager/tablespace/empty_function/se_mini_spacemgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/checkpoint/se_ckpt_dwr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/checkpoint/se_ckpt.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/checkpoint/se_ckpt_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/persistence_config.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/persistence_config_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/recovery/se_recovery_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/recovery/se_recovery_inner_split_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_buf.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_buf_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_file.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_file_inner_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_phy_log.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_replay_split_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/redo/se_redo_split_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/se_meta_persist.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/se_page_desc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/temp_file/se_temp_file_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/persistence/temp_file/se_temp_file.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock/se_deadlock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock/se_lock_acq_pool.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock/se_lock_dfx.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock/se_lock_notify.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock/se_lock_table.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/lock/se_lock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_label_readview.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_trx_chlabel.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_trx_heap.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_trx_lite.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_trx_optimistic.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_trx_pessimistic.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/transaction_manager/se_trx.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/persistence/se_undo_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/persistence/se_undo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_gc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_lite_mock.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_purger.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_rollback.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_savepoint.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_trx_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_trx_resource.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/transaction/undo/se_undo_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fsm/persistence/se_lfsmgr_redo_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fsm/se_lfsmgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/fsm/se_lfsmgr_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/persistence/se_heap_redo_impl.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/persistence/se_heap_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_access_dm.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_access_mini_dm.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_access.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_batch.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_defrag.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_fetch.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_fixed.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_hc.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_inner.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_page.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_rollback.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_slice_row.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_stats.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_trx.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_heap_dml.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/heap/se_page_latch_sorter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/se_ann_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/se_kmeans.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/se_mergecluster.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/se_vectorarray.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_quantizer/se_ann_quantizer.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr/se_ann_node_manager.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr/persistence/se_ann_node_redo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr/persistence/se_ann_node_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr/persistence/se_ann_node_undo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_node_mgr/persistence/se_ann_node_undo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_cluster/se_ann_cluster.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_support/merge_based_cluster/src/autoq.cpp",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_support/merge_based_cluster/src/merge_based_cluster_adapter.cpp",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_support/merge_based_cluster/src/merge_based_cluster.cpp",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_common/ann_support/merge_based_cluster/src/opoc.cpp",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_algo_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_algo_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_algo_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_algo_robust_prune.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_algo_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_algo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_filter.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_iterator.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/algo/se_diskann_nmq.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/persistence/se_diskann_common_replay_method.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/persistence/se_diskann_redo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/persistence/se_diskann_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/persistence/se_diskann_undo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/persistence/se_diskann_undo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/se_diskann_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/se_diskann_storage.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/se_diskann_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_diskann/se_diskann_vertex_manager.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/persistence/se_ivfflat_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/persistence/se_ivfflat_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfflat/se_ivfflat_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/algo/se_ivftree_algo_delete.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/algo/se_ivftree_algo_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/algo/se_ivftree_algo_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/persistence/se_ivftree_redo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/persistence/se_ivftree_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/persistence/se_ivftree_undo_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/persistence/se_ivftree_undo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/se_ivftree_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/se_ivftree_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivftree/se_ivftree_storage.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/algo/se_ivfcluster_algo_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/algo/se_ivfcluster_algo_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/persistence/se_ivfcluster_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/persistence/se_ivfcluster_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/se_ivfcluster_build.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/se_ivfcluster_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/se_ivfcluster_storage.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/ann_ivfcluster/se_ivfcluster_utils.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/base/se_index_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/base/se_index_page_mgr.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/persistence/se_btree_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/persistence/se_btree_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/se_btree_index_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/se_btree_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/se_btree_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/se_btree_insert_base.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/btree/se_btree_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_base/se_persistence_hash_common.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh/se_hash_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh/se_hash_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh/se_persistence_hash_index_ddl.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh/se_persistence_hash_index_scan.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh/se_persistence_hash_index_stash_page.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/hash_cceh_persistence/hash_cceh/se_persistence_hash_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/fast_update_list/se_easy_vector.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/fast_update_list/se_fast_update_list.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/se_inverted_index.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/se_inverted_index_insert.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/se_inverted_index_search.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/page_list/se_page_list.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/page_list/se_page_list_redo.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/page_list/se_page_list_replay.c",
    "$gaussdb_rd_kernel_vector_path/src_terminal/storage_terminal/trm/index/inverted/se_posting_list.c",

    # for grd
    "$grd_path/src/common_terminal/grd_basic_utils.c",
    "$grd_path/src/common_terminal/grd_utils.c",
    "$grd_path/src/common_terminal/kernel_adapter.c",
    "$grd_path/src/executor_terminal/base/collection_common.c",
    "$grd_path/src/executor_terminal/base/collection_manager.c",
    "$grd_path/src/executor_terminal/base/document_check.c",
    "$grd_path/src/executor_terminal/base/grd_db_api.c",
    "$grd_path/src/executor_terminal/base/grd_resultset_api.c",
    "$grd_path/src/executor_terminal/base/parser.c",
    "$grd_path/src/executor_terminal/base/result_set.c",
    "$grd_path/src/executor_terminal/document/delete.c",
    "$grd_path/src/executor_terminal/document/document_key.c",
    "$grd_path/src/executor_terminal/document/filter.c",
    "$grd_path/src/executor_terminal/document/grd_document_api.c",
    "$grd_path/src/executor_terminal/document/insert.c",
    "$grd_path/src/executor_terminal/document/json_result_set.c",
    "$grd_path/src/executor_terminal/document/update.c",
    "$grd_path/src/executor_terminal/document/upsert.c",
    "$grd_path/src/executor_terminal/fast_vec/grd_fast_vec_api.c",
    "$grd_path/src/executor_terminal/gql/grd_gql_api.c",
    "$grd_path/src/executor_terminal/kv/grd_kv_api.c",
    "$grd_path/src/executor_terminal/kv/kv_result_set.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_doc_api.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_map_api.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_text_api.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_xml_element_api.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_xml_fragment_api.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_array_api.c",
    "$grd_path/src/executor_terminal/shared_obj/grd_doc_undo_manager_api.c",
    "$grd_path/src/executor_terminal/shared_obj/shared_obj_check.c",
    "$grd_path/src/executor_terminal/sql/grd_sql_api.c",
    "$grd_path/src/executor_terminal/sync/grd_awareness_api.c",
    "$grd_path/src/executor_terminal/sync/grd_sync_api.c",
    "$grd_path/src/oh_adapter/posix/os_posix.c",
    "$grd_path/src/tokenizer/grd_tokenizer.c",
  ]
  sources += [
    "$target_out_dir/lex.sql_.c",
    "$target_out_dir/sql.tab.c",
    "$target_out_dir/lex.gql_.c",
    "$target_out_dir/gql.tab.c",
    "$target_out_dir/lex.sql_invert_.c",
    "$target_out_dir/sql_invert.tab.c",
    "$target_out_dir/lex.gql_invert_.c",
    "$target_out_dir/gql_invert.tab.c",
  ]

  configs = [ ":module_private_config" ]
  configs += [ ":grd_v5_kernel_public_config" ]

  deps = [
    ":test_build_action",
  ]

  external_deps = [
    "c_utils:utils",
    "hilog:libhilog",
    "hisysevent:libhisysevent",
    "hitrace:hitrace_meter",
    "zlib:libz_crc",
    "zlib:shared_libz",
    "cJSON:cjson",
    "jsoncpp:jsoncpp",
    "openssl:libcrypto_shared",
    "sqlite:sqlite",
  ]

  cflags_cc = [
    "-O0",
    "-g",
    "-Wno-missing-braces",
    "-Wno-unused-const-variable",
    "-Wno-unused-variable",
    "-Wno-unused-function",
  ]

  subsystem_name = "gaussdb_rd_kernel_vector"
  part_name = "arkdata_database_core"
}

template("gaussdb_rd_kernel_vector_unittest") {
  ohos_unittest(target_name) {
    forward_variables_from(invoker, "*")
    module_out_path = module_output_path
    if (!defined(deps)) {
      deps = []
    }
    if (!defined(external_deps)) {
      external_deps = []
    }
    if (!defined(system_deps)) {
      system_deps = []
    }
    external_deps += [
      "c_utils:utils",
      "hilog:libhilog",
      "zlib:libz_crc",
      "zlib:shared_libz",
      "googletest:gmock_main",
      "googletest:gtest_main",
      "sqlite:sqlite",
      "cJSON:cjson",
    ]
    deps += [
      ":src_file",
    ]
    system_deps += [
      "shared_library:libcrypto",
      "shared_library:libflatbuffers_mini",
      "shared_library:libjsoncpp",
    ]
    configs = [ ":module_private_config" ]
    configs += [ ":grd_v5_kernel_public_config" ]
    cflags_cc = [
      "-O0",
      "-g",
      "-Wno-missing-braces",
      "-Wno-unused-const-variable",
      "-Wno-unused-variable",
      "-Wno-unused-function",
    ]
  }
}

gaussdb_rd_kernel_vector_unittest("ut_gql") {
  sources = [
    # for stub
    "mini_terminal/stub/stub.cpp",

    "mini_terminal/ut/gql/ut_emb_gql_common.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_create_graph.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_create_index_stmt.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_delete_stmt.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_drop_graph.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_drop_index_stmt.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_group_by.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_insert_stmt.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_inverted_index.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_merge_stmt.cpp",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_query_stmt.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_query_expr.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_set_stmt.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_transaction.cc",
    "mini_terminal/ut/gql/analyzer/ut_emb_gql_analyzer_vector_index.cc",
    "mini_terminal/ut/gql/datamodel/ut_emb_gql_datamodel_common.cc",
    "mini_terminal/ut/gql/datamodel/ut_emb_gql_datamodel_dataobject_edge_topo.cc",
    "mini_terminal/ut/gql/datamodel/ut_emb_gql_datamodel_meta_topo_label.cc",
    "mini_terminal/ut/gql/datamodel/ut_emb_gql_datamodel_systable_alter.cc",
    "mini_terminal/ut/gql/datamodel/ut_emb_gql_datamodel_systable_trigger.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_aa.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_access_method.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_alter.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_common.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_ddl.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_delete.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_expand.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_hashagg.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_index.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_insert.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_link.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_merge.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_result.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_scan.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_update.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_func.cc",
    "mini_terminal/ut/gql/executor/ut_emb_gql_executor_orderby.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_create_graph.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_create_index.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_drop_index.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_insert_stmt.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_delete_stmt.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_query_expr.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_list.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_query_stmt.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_set_stmt.cc",
    "mini_terminal/ut/gql/parser/ut_emb_gql_parser_utils.cc",
  ]
}

gaussdb_rd_kernel_vector_unittest("ut_sql") {
  sources = [
    # for stub
    "mini_terminal/stub/stub.cpp",

    "mini_terminal/ut/sql/ut_emb_sql_analyze_create_index.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_create_table.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_delete.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_explain.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_foreignkey.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_insert.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_inverted.cc",
    "mini_terminal/ut/sql/ut_emb_sql_analyze_trigger.cc",
    "mini_terminal/ut/sql/ut_emb_sql_catacache_triginfo.cc",
    "mini_terminal/ut/sql/ut_emb_sql_catcache_constraint.cc",
    "mini_terminal/ut/sql/ut_emb_sql_common.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_common.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_compound.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_dml.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_expr.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_foreignkey.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_index.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_inverted.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_session.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_sort.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_subquery.cc",
    "mini_terminal/ut/sql/ut_emb_sql_executor_trigger.cc",
    "mini_terminal/ut/sql/ut_emb_sql_ir.cc",
    "mini_terminal/ut/sql/ut_emb_sql_optimizer_inverted.cc",
    "mini_terminal/ut/sql/ut_emb_sql_parser.cc",
    "mini_terminal/ut/sql/ut_emb_sql_parser_explain.cc",
    "mini_terminal/ut/sql/ut_emb_sql_parser_foreignkey.cc",
    "mini_terminal/ut/sql/ut_emb_sql_parser_para.cc",
    "mini_terminal/ut/sql/ut_emb_sql_parser_select.cc",
    "mini_terminal/ut/sql/ut_emb_sql_parser_trigger.cc",
    "mini_terminal/ut/sql/ut_emb_sql_rewrite.cc",
    "mini_terminal/ut/sql/vector_ut_analyzer_basic_ddl.cc",
    "mini_terminal/ut/sql/vector_ut_analyzer_basic_dml.cc",
    "mini_terminal/ut/sql/vector_ut_analyzer_basic_dql.cc",
    "mini_terminal/ut/sql/vector_ut_analyzer_not_copy.cc",
    "mini_terminal/ut/sql/vector_ut_executor_dml.cc",
    "mini_terminal/ut/sql/vector_ut_executor_dql.cc",
    "mini_terminal/ut/sql/vector_ut_executor_index.cc",
    "mini_terminal/ut/sql/vector_ut_optimization.cc",
    "mini_terminal/ut/sql/vector_ut_parser_basic_ddl.cc",
    "mini_terminal/ut/sql/vector_ut_parser_basic_dml.cc",
    "mini_terminal/ut/sql/vector_ut_parser_basic_dql.cc",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_012_emb_sql") {
  sources = [
    "mini_terminal/st/012_emb_sql/01_common/st_emb_sql_com.cc",
    "mini_terminal/st/012_emb_sql/02_frame/st_emb_sql_frame.cc",
    "mini_terminal/st/012_emb_sql/03_utility/st_emb_sql_util.cc",
    "mini_terminal/st/012_emb_sql/04_feature/ddl/st_emb_sql_constrain.cc",
    "mini_terminal/st/012_emb_sql/04_feature/ddl/st_emb_sql_ddl.cc",
    "mini_terminal/st/012_emb_sql/04_feature/ddl/st_emb_sql_ddl_alter.cc",
    "mini_terminal/st/012_emb_sql/04_feature/ddl/st_emb_sql_defaultvalue.cc",
    "mini_terminal/st/012_emb_sql/04_feature/ddl/st_emb_sql_ddl_recover.cc",
    "mini_terminal/st/012_emb_sql/04_feature/ddl/st_emb_sql_foreignkey.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_compression.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_conflict.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_delete.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_delete_expr.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_insert.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_insert_ttl.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_insert_sub_query.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_returning.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_trigger.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_update.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_update_expr.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dml/st_emb_sql_update_subquery.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_explain.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_aggregates.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_alias.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_compound.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_distinct.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_expr.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_expr_index.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_func.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_groupby.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_having.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_index.cc",
    "mini_terminal/st/012_emb_sql/04_feature/inverted/st_emb_sql_select_inverted.cc",
    "mini_terminal/st/012_emb_sql/04_feature/inverted/st_emb_sql_select_inverted_bm25.cc",
    "mini_terminal/st/012_emb_sql/04_feature/inverted/st_emb_sql_select_inverted_boolean.cc",
    "mini_terminal/st/012_emb_sql/04_feature/inverted/st_emb_sql_select_inverted_fusion.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_join.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_limit.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_orderby.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_subquery.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_subquery_extend.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_unary.cc",
    "mini_terminal/st/012_emb_sql/04_feature/dql/st_emb_sql_select_view.cc",
    "mini_terminal/st/012_emb_sql/04_feature/fuzz/st_emb_sql_fuzz.cc",
    "mini_terminal/st/012_emb_sql/04_feature/fuzz/st_emb_sql_fuzz_extend_one.cc",
    "mini_terminal/st/012_emb_sql/04_feature/interface/st_emb_sql_interface.cc",
    "mini_terminal/st/012_emb_sql/04_feature/instance/st_emb_sql_instance.cc",
    "mini_terminal/st/012_emb_sql/04_feature/instance/st_emb_sql_instance_expr.cc",
    "mini_terminal/st/012_emb_sql/04_feature/instance/st_emb_sql_instance_orderby.cc",
    "mini_terminal/st/012_emb_sql/04_feature/process/st_emb_sql_process.cc",
    "mini_terminal/st/012_emb_sql/04_feature/process/st_emb_sql_process_rc.cc",
    "mini_terminal/st/012_emb_sql/04_feature/process/st_emb_sql_process_serializable.cc",
    "mini_terminal/st/012_emb_sql/04_feature/tcl/st_emb_sql_tcl_pthread_serializable.cc",
    "mini_terminal/st/012_emb_sql/04_feature/tcl/st_emb_sql_tcl.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_diskann_index.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_explain.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_index.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_insert.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_not_copy.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_parameter.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_project.cc",
    "mini_terminal/st/012_emb_sql/04_feature/vector/st_emb_sql_vector_update.cc",
    "mini_terminal/st/012_emb_sql/05_performance/base/com/st_emb_sql_perf_com.cc",
    "mini_terminal/st/012_emb_sql/05_performance/base/emb_sql/st_emb_sql_perf_util.cc",
    "mini_terminal/st/012_emb_sql/05_performance/dml/emb_sql/st_emb_sql_perf_dml.cc",
    "mini_terminal/st/012_emb_sql/05_performance/ddl/emb_sql/st_emb_sql_perf_ddl.cc",
    "mini_terminal/st/012_emb_sql/05_performance/dql/emb_sql/st_emb_sql_perf_select.cc",
    "mini_terminal/st/012_emb_sql/05_performance/dql/emb_sql/st_emb_sql_perf_sub_query.cc",
    "mini_terminal/st/012_emb_sql/05_performance/instance/emb_sql/st_emb_sql_perf_instance.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_014_emb_gql") {
  sources = [
    "mini_terminal/st/014_emb_gql/01_common/st_emb_gql_com.cc",
    "mini_terminal/st/014_emb_gql/01_common/gql_data_transfer/st_emb_gql_data_transfer.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dcl/st_emb_gql_pragma.cc",
    "mini_terminal/st/014_emb_gql/02_feature/ddl/st_emb_gql_ddl_alter.cc",
    "mini_terminal/st/014_emb_gql/02_feature/ddl/st_emb_gql_ddl.cc",
    "mini_terminal/st/014_emb_gql/02_feature/ddl/st_emb_gql_ddl_index.cc",
    "mini_terminal/st/014_emb_gql/02_feature/ddl/st_emb_gql_ddl_systbl.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dml/st_emb_gql_delete.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dml/st_emb_gql_insert.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dml/st_emb_gql_set.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dml/st_emb_gql_set_expr.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dml/st_emb_gql_set_func_expr.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dml/st_emb_gql_merge.cpp",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_hashagg.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_lineage.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_match.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_match_expr.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_match_func_expr.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_match_multi_hop.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_match_no_label.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_call_procedure.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_transaction.cc",
    "mini_terminal/st/014_emb_gql/02_feature/dql/st_emb_gql_trigger.cc",
    "mini_terminal/st/014_emb_gql/02_feature/hybrid/st_emb_gql_vector_basic.cc",
    "mini_terminal/st/014_emb_gql/02_feature/hybrid/st_emb_gql_vector_index.cc",
    "mini_terminal/st/014_emb_gql/02_feature/hybrid/st_emb_gql_vector_index_query.cc",
    "mini_terminal/st/014_emb_gql/02_feature/hybrid/st_emb_gql_inverted_index.cc",
    "mini_terminal/st/014_emb_gql/02_feature/hybrid/st_emd_gql_vector_inverted_hybrid.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_executor_sql") {
  sources = [
    "GRD_terminal/ut/executor/sql/grd_sql_basic_api.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_db_backup.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_diskann_dml.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_diskann_fusion_search.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_diskann_vaccum.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_inverted_index_base.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_ivfcluster_dml.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_ivftree_dml.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_ivftree_persist.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_metaInfo_bak.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_repair.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_test_config.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_test_compression.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_test_serializable.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_test_tool.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_test_ttl.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_vector_base.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_vector_explain.cpp",
    "GRD_terminal/ut/executor/sql/grd_sql_vector_parameter.cpp",
    "GRD_terminal/ut/executor/sql/grd_db_file_remove.cpp",
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    "GRD_terminal/ut/utility/grd_clouddb_impl.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_executor_gql") {
  sources = [
    "GRD_terminal/ut/executor/gql/grd_gql_cipher.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_com.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_ddl.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_ddl_alter.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_ddl_alter_positive.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_ddl_alter_negative.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_ddl_systbl.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_delete.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_dql.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_fuzz.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_insert.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_lineage.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_lineage_com.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_lineage_delete.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_lineage_query.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_match_expr.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_match_multi_hop.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_match_no_label.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_merge.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_process.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_set.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_set_expr.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_transaction.cpp",
    "GRD_terminal/ut/executor/gql/grd_gql_vector.cpp",
    "GRD_terminal/ut/executor/gql/gql_data_transfer/grd_gql_data_transfer.cpp",
    "GRD_terminal/ut/executor/gql/gql_performance/grd_gql_perf.cpp",
    "GRD_terminal/ut/executor/gql/gql_vector/grd_gql_vector_dml.cpp",
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    "GRD_terminal/ut/utility/grd_clouddb_impl.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_executor_base") {
  sources = [
    "GRD_terminal/ut/executor/base/grd_db_api_test.cpp",
    "GRD_terminal/ut/executor/base/grd_db_dfx_api_test.cpp",
    "GRD_terminal/ut/executor/base/GRDDbBackupApiTest.cpp",
    "GRD_terminal/ut/executor/base/GRDDbCipherApiTest.cpp",
    # for util
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_st_hash") {
  sources = [
    "mini_terminal/st/008_index/cceh_index_base_st.cc",
    "mini_terminal/st/008_index/cceh_index_batch_dml_st.cc",
    "mini_terminal/st/008_index/cceh_index_ddl_st.cc",
    "mini_terminal/st/008_index/cceh_index_dml_st.cc",
    "mini_terminal/st/008_index/st_index_common.cc",
    "mini_terminal/st/util/test_mini_util.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_ut_hash") {
  sources = [
    "mini_terminal/ut/storage/hash/ut_cceh_hash_index_base.cc",
    "mini_terminal/ut/storage/hash/ut_cceh_hash_index_common.cc",
    "mini_terminal/ut/storage/hash/ut_cceh_hash_redo_test.cc",
    "mini_terminal/ut/storage/storage_common.cc",
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_000_base") {
  sources = [
    "mini_terminal/st/000_base/mini_st_basic.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_001_ddl") {
  sources = [
    "mini_terminal/st/001_ddl/001_kv_ddl_st.cc",
    "mini_terminal/st/001_ddl/003_kv_repair.cc",
    "mini_terminal/st/001_ddl/004_kv_metaInfoBak.cc",
    "mini_terminal/st/001_ddl/mini_st_kv_ddl.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_002_dml") {
  sources = [
    "mini_terminal/st/002_dml/001_kv_dml_st.cc",
    "mini_terminal/st/002_dml/002_kv_prefix_scan_st.cc",
    "mini_terminal/st/002_dml/005_kv_dml_undo.cc",
    "mini_terminal/st/002_dml/006_st_kv_batch_dml.cc",
    "mini_terminal/st/002_dml/007_st_kv_dml.cc",
    "mini_terminal/st/002_dml/008_st_kv_space.cc",
    "mini_terminal/st/002_dml/009_kv_dml_hash_undo.cc",
    # Test cases below will create a new executable file and should run by gtest. But HarmonyOs testing framework
    # will try to execute all exes directly, cause test cases to fail, so that these files will not be compiled.
    # "mini_terminal/st/002_dml/003_abnormal_process.cc",
    # "mini_terminal/st/002_dml/003_kv_dml_abnormal.cc",

    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_003_dql") {
  sources = [
    "mini_terminal/st/003_dql/mini_st_index_preload.cc",
    "mini_terminal/st/003_dql/mini_st_kv_dql.cc",
    "mini_terminal/st/003_dql/mini_st_kv_scan.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_004_single_file") {
  sources = [
    "mini_terminal/st/004_single_file/mini_st_single_file.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_005_multi_instance") {
  sources = [
    "mini_terminal/st/005_multi_instance/mini_st_multi_instance.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_006_crc") {
  sources = [
    "mini_terminal/st/006_crc/mini_st_crc.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_007_multi_process") {
  sources = [
    "mini_terminal/st/007_multi_process/multi_process_dfx.cc",
    "mini_terminal/st/007_multi_process/multi_process_dfx_extra.cc",
    "mini_terminal/st/007_multi_process/multi_process_inter_lock.cc",
    "mini_terminal/st/007_multi_process/multi_process_simple.cc",
    "mini_terminal/st/007_multi_process/multi_process_sql.cc",
    "mini_terminal/st/007_multi_process/multi_process_test.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_008_index") {
  sources = [
    "mini_terminal/st/008_index/cceh_index_base_st.cc",
    "mini_terminal/st/008_index/cceh_index_batch_dml_st.cc",
    "mini_terminal/st/008_index/cceh_index_ddl_st.cc",
    "mini_terminal/st/008_index/cceh_index_dml_shmem_st.cc",
    "mini_terminal/st/008_index/cceh_index_dml_st.cc",
    "mini_terminal/st/008_index/mini_index_st_scan.cc",
    "mini_terminal/st/008_index/mini_index_st.cc",
    "mini_terminal/st/008_index/st_index_common.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_009_oh_view") {
  sources = [
    "mini_terminal/st/009_oh_view/009_oh_view.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_st_010_crud_perf") {
  sources = [
    "mini_terminal/st/010_crud_perf/010_crud_perf.cc",
    "mini_terminal/st/010_crud_perf/010_mem.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
    # for util
    "mini_terminal/st/util/test_mini_util.cc",
    "mini_terminal/st/util/db_file_tool.cpp",
    "mini_terminal/st/util/common_init.cc"
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_executor_document") {
  sources = [
    "GRD_terminal/ut/executor/document/grd_collection_manager_test.cpp",
    "GRD_terminal/ut/executor/document/grd_doc_key_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_api_in_sharedmode.cpp",
    "GRD_terminal/ut/executor/document/grd_document_check_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_delete_api_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_find_api_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_insert_api_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_parser_find.cpp",
    "GRD_terminal/ut/executor/document/grd_document_update_api_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_update_test.cpp",
    "GRD_terminal/ut/executor/document/grd_document_upsert_api_test.cpp",
    "GRD_terminal/ut/executor/document/grd_parser_test.cpp",
    "GRD_terminal/ut/executor/document/grd_projection_test.cpp",

    # for util
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_executor_fast_vec") {
  sources = [
    "GRD_terminal/ut/executor/fast_vec/grd_fast_vec_fp16_test.cpp",
    "GRD_terminal/ut/executor/fast_vec/grd_fast_vec_fp32_test.cpp",
    "GRD_terminal/ut/executor/fast_vec/grd_fast_vec_test_common.cpp",

    # for util
    "GRD_terminal/ut/utility/db_test_tool.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_executor_kv") {
  sources = [
    "GRD_terminal/ut/executor/kv/grd_kv_batch_api_test.cpp",
    "GRD_terminal/ut/executor/kv/grd_kv_collection_manager_api_test.cpp",
    "GRD_terminal/ut/executor/kv/grd_kv_del_api_test.cpp",
    "GRD_terminal/ut/executor/kv/grd_kv_get_api_test.cpp",
    "GRD_terminal/ut/executor/kv/grd_kv_put_api_test.cpp",
    "GRD_terminal/ut/executor/kv/grd_kv_scan_api_test.cpp",

    # for util
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_tokenizer") {
  sources = [
    "GRD_terminal/ut/tokenizer/grd_test_tokenizer.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_adapter") {
  sources = [
    "mini_terminal/ut/adapter/ut_adapter_log.cc",
    "mini_terminal/ut/adapter/ut_adpt_dfx.cc",
    "mini_terminal/ut/adapter/ut_adpt_file_linux.cc",
    "mini_terminal/ut/adapter/ut_adpt_thread.cc",
    "mini_terminal/ut/adapter/ut_db_types.cc",
    "mini_terminal/ut/adapter/ut_file_lock.cc",
    "mini_terminal/ut/adapter/ut_file.cc",
    "mini_terminal/ut/adapter/ut_init_adapter.cc",
    "mini_terminal/ut/adapter/ut_spinlock.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_diskann") {
  sources = [
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_adpt_ut.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_corrupt_detect_ut.cpp",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_dml_ut.cpp",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_fault_injection_ut.cpp",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_filter_ut.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_graph.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_index_ut.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_iter_to_fixed_point_ut.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_nmq_ut.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_ut_common.cc",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_lvq_ut.cpp",
    "mini_terminal/ut/storage/ann_diskann/storage_diskann_redo_ut.cpp",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",
    "mini_terminal/ut/storage/ann_common/storage_ann_common_ut.cpp",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_ivfcluster") {
  sources = [
    "mini_terminal/ut/storage/ann_ivfcluster/ivfcluster_index_ut_common.cpp",
    "mini_terminal/ut/storage/ann_ivfcluster/ivfcluster_index_ut.cpp",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",
    "mini_terminal/ut/storage/ann_common/storage_ann_common_ut.cpp",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_ivfflat") {
  sources = [
    "mini_terminal/ut/storage/ann_ivfflat/storage_ann_common_kmeans_ut.cc",
    "mini_terminal/ut/storage/ann_ivfflat/storage_ivf_index_dml_ut.cc",
    "mini_terminal/ut/storage/ann_ivfflat/storage_ivf_index_ut.cc",
    "mini_terminal/ut/storage/ann_ivfflat/storage_ivf_index_utils_ut.cc",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",
    "mini_terminal/ut/storage/ann_common/storage_ann_common_ut.cpp",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_ivftree") {
  sources = [
    "mini_terminal/ut/storage/ann_ivftree/ivftree_ann_cluster_ut.cpp",
    "mini_terminal/ut/storage/ann_ivftree/ivftree_index_ut_common.cpp",
    "mini_terminal/ut/storage/ann_ivftree/ivftree_index_ut.cpp",
    "mini_terminal/ut/storage/ann_ivftree/ivftree_node_manager_ut.cpp",
    "mini_terminal/ut/storage/ann_ivftree/ivftree_storage_ut.cpp",
 
    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",
    "mini_terminal/ut/storage/ann_common/storage_ann_common_ut.cpp",
 
    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_btree") {
  sources = [
    "mini_terminal/ut/storage/btree/ut_btree_index_base.cc",
    "mini_terminal/ut/storage/btree/ut_btree_index_common.cc",
    "mini_terminal/ut/storage/btree/ut_btree_index_direction.cc",
    "mini_terminal/ut/storage/btree/ut_btree_index_getleave.cc",
    "mini_terminal/ut/storage/btree/ut_btree_index_scan.cc",
    "mini_terminal/ut/storage/btree/ut_btree_keyInfo_test.cc",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_hash") {
  sources = [
    "mini_terminal/ut/storage/hash/ut_cceh_hash_index_base.cc",
    "mini_terminal/ut/storage/hash/ut_cceh_hash_index_common.cc",
    "mini_terminal/ut/storage/hash/ut_cceh_hash_redo_test.cc",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_inverted") {
  sources = [
    "mini_terminal/ut/storage/inverted_index/ut_invert_index_common.cc",
    "mini_terminal/ut/storage/inverted_index/ut_inverted_fast_update_list.cc",
    "mini_terminal/ut/storage/inverted_index/ut_inverted_index_base_test.cc",
    "mini_terminal/ut/storage/inverted_index/ut_inverted_index_bm25.cc",
    "mini_terminal/ut/storage/inverted_index/ut_page_list_base.cc",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_redo") {
  sources = [
    "mini_terminal/ut/storage/redo/ut_redo_create.cc",
    "mini_terminal/ut/storage/redo/ut_redo_feature.cc",
    "mini_terminal/ut/storage/redo/ut_redo_function.cc",
    "mini_terminal/ut/storage/redo/ut_redo_log.cc",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_storage_others") {
  sources = [
    "mini_terminal/ut/storage/tempfile/ut_storage_temp_file.cc",
    "mini_terminal/ut/storage/bufferpool/ut_bufferpool_mini_base.cc",
    "mini_terminal/ut/storage/bufferpool/ut_bufferpool.cc",
    "mini_terminal/ut/storage/ut_storage_checkpoint.cc",
    "mini_terminal/ut/storage/ut_storage_instance.cc",
    "mini_terminal/ut/storage/ut_storage_temp.cc",
    "mini_terminal/ut/storage/ut_storage_am.cc",
    "mini_terminal/ut/storage/ut_storage_pagemgr.cc",
    "mini_terminal/ut/storage/ut_storage_index.cc",
    "mini_terminal/ut/storage/ut_storage_backup.cc",
    "mini_terminal/ut/storage/ut_storage_persistence.cc",
    "mini_terminal/ut/storage/ut_storage_space.cc",

    # for storage_common
    "mini_terminal/ut/storage/storage_common.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_shared_obj") {
  sources = [
    "GRD_terminal/ut/executor/shared_obj/grd_doc_array_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_awareness_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_cursor_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_fragment_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_map_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_multi_thread_operation_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_rel_pos_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_text_format_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_text_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_undo_manager_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_undo_manager_api_map_array_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_xml_element_api_test.cpp",
    "GRD_terminal/ut/executor/shared_obj/grd_doc_text_delta_api_test.cpp",
    "GRD_terminal/ut/utility/asset_loader_impl.cpp",
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    "GRD_terminal/ut/utility/grd_clouddb_impl.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("dt_GRD_ut_sync") {
  sources = [
    "GRD_terminal/ut/executor/sync/grd_doc_oplog_garbage_collection_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_sync_api_oplog_apply_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_sync_api_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_sync_asset_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_sync_format_conflict_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_sync_text_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_undo_mgr_sync_test.cpp",
    "GRD_terminal/ut/executor/sync/grd_doc_sync_cursor_api_test.cpp",
    "GRD_terminal/ut/utility/asset_loader_impl.cpp",
    "GRD_terminal/ut/utility/db_test_tool.cpp",
    "GRD_terminal/ut/utility/grd_clouddb_impl.cpp",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_common") {
  sources = [
    "mini_terminal/ut/common/ut_config.cc",
    "mini_terminal/ut/common/ut_error.cc",
    "mini_terminal/ut/common/ut_hash.cc",
    "mini_terminal/ut/common/ut_hashmap.cc",
    "mini_terminal/ut/common/ut_json.cc",
    "mini_terminal/ut/common/ut_log.cc",
    "mini_terminal/ut/common/ut_mini_file_map_memory_context.cc",
    "mini_terminal/ut/common/ut_mini_file_map.cc",
    "mini_terminal/ut/common/ut_string.cc",
    "mini_terminal/ut/common/ut_utils.cc",
    "mini_terminal/ut/common/ut_memory.cc",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_service") {
  sources = [
    "mini_terminal/ut/service/ut_service_corrupt_rebuild.cc",
    "mini_terminal/ut/service/ut_service_doc_obj.cc",
    "mini_terminal/ut/service/ut_service_awareness.cpp",
    "mini_terminal/ut/service/ut_service_sync.cpp",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_compiler") {
  sources = [
    "mini_terminal/ut/compiler/ut_compiler_doc_parser.cpp",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_datamodel") {
  sources = [
    "mini_terminal/ut/datamodel/ut_dm_basic.cc",
    "mini_terminal/ut/datamodel/ut_dm_compression.cc",
    "mini_terminal/ut/datamodel/ut_dm_constructor.cc",
    "mini_terminal/ut/datamodel/ut_dm_edgeTopo.cc",
    "mini_terminal/ut/datamodel/ut_dm_math.cc",
    "mini_terminal/ut/datamodel/ut_dm_print.cc",
    "mini_terminal/ut/datamodel/ut_dm_record.cc",
    "mini_terminal/ut/datamodel/ut_dm_shared_struct.cc",
    "mini_terminal/ut/datamodel/ut_dm_sql.cc",
    "mini_terminal/ut/datamodel/ut_dm_vertex.cc",
    "mini_terminal/ut/datamodel/ut_dm_vertexlabel.cc",
    "mini_terminal/ut/datamodel/ut_dm_data_math.cpp",
    "mini_terminal/ut/datamodel/ut_dm_math_extra.cpp",
    "mini_terminal/ut/datamodel/ut_dm_shared_struct_am.cpp",

    # for common test
    "mini_terminal/ut/datamodel/common/ut_dm_common.c",
    "mini_terminal/ut/common_test_include/common_init.cc",
    "mini_terminal/ut/common_test_include/ut_dm_meta_kv_base_label.cc",
    "mini_terminal/ut/common_test_include/ut_dm_meta_topo_label_prop.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_distribution_binlog") {
  sources = [
    "mini_terminal/ut/distribution/binlog/ut_binlog_open.cpp",
    "mini_terminal/ut/distribution/binlog/ut_binlog_read.cpp",
    "mini_terminal/ut/distribution/binlog/ut_binlog_write.cpp",

    # for common test
    "mini_terminal/ut/distribution/common/distribution_common_ut.cpp",
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_distribution_fwm") {
  sources = [
    "mini_terminal/ut/distribution/ut_distribution_fwm.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_distribution_oplog") {
  sources = [
    "mini_terminal/ut/distribution/oplog/ut_oplog_apply.cpp",

    # for common test
    "mini_terminal/ut/distribution/common/distribution_common_ut.cpp",
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_distribution_sync") {
  sources = [
    "mini_terminal/ut/distribution/sync/ut_sync.cpp",

    # for common test
    "mini_terminal/ut/distribution/common/distribution_common_ut.cpp",
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

gaussdb_rd_kernel_vector_unittest("mini_ut_executor") {
  sources = [
    "mini_terminal/ut/executor/ut_executor_shared_struct_utils.cpp",
    "mini_terminal/ut/executor/ut_executor_doc_am.cpp",
    "mini_terminal/ut/executor/ut_executor_doc_obj.cpp",

    # for common test
    "mini_terminal/ut/common_test_include/common_init.cc",
    # for stub
    "mini_terminal/stub/stub.cpp",
  ]
}

###############################################################################
group("unittest") {
  testonly = true

  deps = [ ":ut_gql" ]
  deps += [ ":ut_sql" ]
  deps += [ ":dt_GRD_ut_executor_sql" ]
  deps += [ ":dt_GRD_ut_executor_gql" ]
  deps += [ ":dt_GRD_ut_executor_base" ]
  deps += [ ":dt_GRD_ut_executor_document" ]
  deps += [ ":dt_GRD_ut_executor_kv" ]
  deps += [ ":dt_GRD_ut_executor_fast_vec" ]
  deps += [ ":dt_GRD_ut_tokenizer" ]
  deps += [ ":dt_st_hash" ]
  deps += [ ":dt_ut_hash" ]
  deps += [ ":mini_st_000_base" ]
  deps += [ ":mini_st_001_ddl" ]
  deps += [ ":mini_st_002_dml" ]
  deps += [ ":mini_st_003_dql" ]
  deps += [ ":mini_st_004_single_file" ]
  deps += [ ":mini_st_005_multi_instance" ]
  deps += [ ":mini_st_006_crc" ]
  deps += [ ":mini_st_007_multi_process" ]
  deps += [ ":mini_st_008_index" ]
  deps += [ ":mini_st_009_oh_view" ]
  deps += [ ":mini_st_010_crud_perf" ]
  deps += [ ":mini_st_012_emb_sql" ]
  deps += [ ":mini_st_014_emb_gql" ]
  deps += [ ":mini_ut_adapter" ]
  deps += [ ":mini_ut_storage_diskann" ]
  deps += [ ":mini_ut_storage_ivfflat" ]
  deps += [ ":mini_ut_storage_ivfcluster"]
  deps += [ ":mini_ut_storage_ivftree" ]
  deps += [ ":mini_ut_storage_btree" ]
  deps += [ ":mini_ut_storage_hash" ]
  deps += [ ":mini_ut_storage_inverted" ]
  deps += [ ":mini_ut_storage_redo" ]
  deps += [ ":mini_ut_storage_others" ]
  deps += [ ":dt_GRD_ut_shared_obj" ]
  deps += [ ":dt_GRD_ut_sync" ]
  deps += [ ":mini_ut_common" ]
  deps += [ ":mini_ut_compiler" ]
  deps += [ ":mini_ut_datamodel" ]
  deps += [ ":mini_ut_distribution_binlog" ]
  deps += [ ":mini_ut_distribution_fwm" ]
  deps += [ ":mini_ut_distribution_oplog" ]
  deps += [ ":mini_ut_distribution_sync" ]
  deps += [ ":mini_ut_executor" ]
  deps += [ ":mini_ut_service" ]
}
###############################################################################
