#include "query_benchmark.h"
#include "drt_instance_inner.h"
using namespace std;

// benchmark打印、计算框架参数
#ifdef ALL
const uint64_t times = 1000;
HeadParamT g_qeHeader = {.name = "Execution Times", .headParamList = {50, 100, 200, 400, 1000}, .size = 5};
#else
const uint64_t times = 100;
HeadParamT g_qeHeader = {.name = "Execution Times", .headParamList = {5, 10, 20, 40, 100}, .size = 5};
#endif

OpParamT g_qeOperation = {.tgtNum = 3,
    .opNum = 4,
    .tgtList = {BM_SUM_CYCLE, BM_AVERAGE_CYCLE, BM_AVERAGE_OPS},
    .opNameList = {
        (char *)"Create VertexLabel", (char *)"Insert Vertex", (char *)"Update Vertex", (char *)"Delete Vertex"}};
DbMemCtxT *dyAlgoCtxVertex;
FixBufferT req = {0};

BenchmarkTaskT queryBenchmarkTask = {.head = g_qeHeader, .operation = g_qeOperation};
MapKeyT g_qeBenchmarkKeys;
map<MapKey, MapValue> g_qeTimeResultMap;

uint64_t g_qeBenchmarkStartCycle = 0;
uint64_t g_qeBenchmarkEndCycle = 0;
uint64_t g_qeBenchmarkCyclesPerSum = 0;
uint64_t g_qeBenchmarkCyclesPer = 0;
#ifndef FEATURE_PERSISTENCE
TEST_F(QueryBenchmark, Query_Benchmark_CreateVertexLabel)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelNameBase1 = (char *)"benchmark";
    char labelName[30];
    char *cfgJson = (char *)R"({
            "max_record_count":100001
        })";
    char *labelJsonBase1 = (char *)R"([{
        "type":"record",
        "name":
        )";
    char *labelJsonBase2 = (char *)R"(
    ,
        "fields":
            [
                {
                    "name":"nhp_group_id2",
                    "type":"int32"
                },
                {
                    "name":"nhp_group_name2",
                    "type":"int32"
                }
            ]
        }])";
    char labelJson[1000];

    uint32_t labelJsonLen;
    uint32_t cfgJsonLen = strlen(cfgJson) + 1;
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    MsgHeaderT *msgHeader;
    TextT putText;
    MapKey TimeKeys;

    for (uint32_t i = 0, j = 0; i < times; i++) {
        sprintf_s(labelName, 30, "%s%d", labelNameBase1, i);
        sprintf_s(labelJson, 1000, "%s\"%s\"%s", labelJsonBase1, labelName, labelJsonBase2);
        labelJsonLen = strlen(labelJson) + 1;

        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;
        msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
        msgHeader->opNum = 1;

        putText.str = labelJson;
        putText.len = labelJsonLen;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        putText.str = cfgJson;
        putText.len = cfgJsonLen;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        // appoint label name
        putText.str = NULL;
        putText.len = 0;
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);

        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_VERTEX_LABEL,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        g_qeBenchmarkStartCycle = DbRdtsc();
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        g_qeBenchmarkEndCycle = DbRdtsc();
        g_qeBenchmarkCyclesPer = g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        g_qeBenchmarkCyclesPerSum += g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        if (queryBenchmarkTask.head.headParamList[j] == i + 1) {
            TimeKeys.headLinePara = i + 1;
            TimeKeys.opName = g_qeOperation.opNameList[0];
            g_qeTimeResultMap[TimeKeys].sumCycles = g_qeBenchmarkCyclesPerSum;
            j++;
        }
    }
    g_qeBenchmarkCyclesPerSum = 0;
    QryTestReleaseSession(conn);
}
#endif
