#include "query_base.h"
#include "db_text.h"
#include "common_init.h"
#include "db_label_latch_mgr.h"
#include "db_sysapp_context.h"
#include "drt_instance_inner.h"
#include "srv_data_service.h"
#include "srv_data_public.h"
#include "ee_init.h"
#include "db_mem_context_pool.h"

#ifdef __cplusplus
extern "C" {
#endif

static uint32_t curStmtID = -1;

void QryTestSetCurStmtID(uint32_t stmtID)
{
    curStmtID = stmtID;
}

uint32_t QryTestGetCurStmtID()
{
    return curStmtID;
}

void DbFillMsgHeader(
    const FixBufferT *fixBuffer, int opCode, uint32_t headerOff, int32_t reqTimeOut, uint32_t stmtId, uint32_t flag)
{
    // fill head
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, serviceId, DRT_SERVICE_STMT);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, flags, flag);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, size, fixBuffer->pos);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, reqTimeOut, reqTimeOut);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, reqStartTime, (uint64_t)DbToUseconds(DbRdtsc()));
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, serialNumber, 1);
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, stmtId, stmtId);
    uint8_t hdrFlag = ((MsgHeaderT *)(fixBuffer->buf + headerOff))->flags;
    FIX_BUF_SET_STRUCT_VALUE(fixBuffer, headerOff, MsgHeaderT, flags, hdrFlag & (~CS_FLAG_SELF_SCHEDULE));
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(fixBuffer), opCode,
        fixBuffer->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

// init storage module
int32_t DbInitStorage(DbMemCtxT *topShmMemCtx)
{
    // use defalut config without config file
    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = SE_DEFAULT_PAGE_SIZE;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
    config.maxTrxNum = MAX_CONN_NUM + MAX_BG_WORKER_NUM;

    SeInstanceHdT se = NULL;
    return SeCreateInstance(NULL, (DbMemCtxT *)topShmMemCtx, &config, &se);
}

int32_t QrySetCfg(const char *configName, const char *configValue)
{
    DbCfgMgrHandleT handle = DbGetCfgHandle(NULL);
    return DbCfgSetByNameInner(handle, configName, configValue, false, true);
}

void DsFreeGloablVariaMemory()
{
    // 依赖上层外围函数DsFreeGloablVariaMemory()清理内存
}

void DrtLongOpLogStub(DrtProcCtxT *procCtx, uint64_t sendTime)
{
    return;
}

static Status DbCfgGetStub(DbCfgMgrHandleT handle, DbCfgEmItemIdE id, DbCfgValueT *value)
{
    if (handle == NULL || value == NULL) {
        return GMERR_NO_DATA;
    }
    DbCfgMgrT *mgr = (DbCfgMgrT *)handle;
    if ((uint32_t)id >= mgr->cfgItemNum) {
        return GMERR_DATA_EXCEPTION;
    }
    DbRWSpinRLock(&mgr->cfgModifyLock);
    Status ret = GMERR_OK;
    if (id == DB_CFG_LOCAL_LOCATOR) {
        char serverLocator[] = "usocket:/run/verona/unix_emserver;channel:Ctrl_channel";
        errno_t err = strcpy_s(value->str, sizeof(value->str), serverLocator);
        ret = err != EOK ? GMERR_MEMORY_OPERATE_FAILED : GMERR_OK;
    } else {
        ret = DbCfgValueCpy(&mgr->cfgItems[id].value, value);
    }
    DbRWSpinRUnlock(&mgr->cfgModifyLock);
    return ret;
}

int32_t BaseInit()
{
    int32_t ret;
    DbSetServerThreadFlag();
    init();
    CommonInit();

    (void)setStubC((void *)DbCfgGet, (void *)DbCfgGetStub);
    DrtInstanceInit(false, NULL);
    (void)clearAllStub();

    ret = CataLabelCacheInitWithOutTimer(NULL);
    if (ret != GMERR_OK) {
        // logging
        printf("catalog cache init failed!\n");
        return ret;
    }

    DbMemCtxT *topShmemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    EXPECT_TRUE(topShmemCtx != NULL);
    ret = DbInitStorage(topShmemCtx);
    if (ret != GMERR_OK) {
        // logging
        printf("storage init failed!\n");
        return ret;
    }

    ret = QryInitSessionPool(NULL);
    if (ret != GMERR_OK) {
        // logging
        printf("qry session pool init failed!\n");
        return ret;
    }

    ret = DbServerInitCltStat();
    if (ret != GMERR_OK) {
        // logging
        printf("qry clt cache init failed!\n");
        return ret;
    }

    QrySetMemCtxPool(NULL, DbCreateDynMemCtxPool(true, (DbMemCtxT *)DbGetTopDynMemCtx(NULL)));
    if (QryGetMemCtxPool(NULL) == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = QrySetCfg("userPolicyMode", "1");
    EXPECT_EQ(ret, GMERR_OK);

    ret = PublicServiceInit(NULL);
    if (ret != GMERR_OK) {
        printf("public init failed!\n");
        return ret;
    }
    ret = FastPathServiceInit();
    if (ret != GMERR_OK) {
        printf("qry init failed!\n");
        return ret;
    }

    ret = ExecutorInit(NULL);
    if (ret != GMERR_OK) {
        printf("executor init failed!\n");
        return ret;
    }

    return GMERR_OK;
}

void BaseUninit()
{
    FastPathServiceUnInit(NULL);
    PublicServiceUnInit(NULL);
    DrtInstanceDestroy(NULL);
    CommonRelease();
    // 在运行结束之后，清理全局变量申请的动态内存。
    DsFreeGloablVariaMemory();
    clearAllStub();
}

uint8_t DrtGetConnFlowCtrlLevelStub(DrtConnectionT *conn)
{
    return 0;
}

void DrtFreeMsgStub(FixBufferT *msg)
{
    return;
}

void DrtFreeProcCtxStub(DrtProcCtxT *procCtx)
{
    return;
}

int32_t DrtConnWritePackStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    SessionT *session = (SessionT *)conn->session;

    FixBufferT *rsp = QrySessionGetRsp(session);
    FixBufSeek(rsp, 0);
    MsgHeaderT *header = RpcPeekMsgHeader(rsp);
    EXPECT_EQ(GMERR_OK, header->opStatus);
    if (header->opStatus == GMERR_OK) {
        QryTestSetCurStmtID(header->stmtId);
    }
    uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
    uint32_t opCode = ProtocolPeekFirstOpHeader(msg)->opCode;
    uint32_t affectRows;

    if (opCode == MSG_OP_RPC_CREATE_VERTEX_LABEL || opCode == MSG_OP_RPC_DROP_VERTEX_LABEL) {
        EXPECT_EQ((uint32_t)0, datalen);
    } else if (opCode == MSG_OP_RPC_INSERT_VERTEX || opCode == MSG_OP_RPC_UPDATE_VERTEX ||
               opCode == MSG_OP_RPC_DELETE_VERTEX || opCode == MSG_OP_RPC_INSERT_EDGE ||
               opCode == MSG_OP_RPC_DELETE_EDGE || opCode == MSG_OP_RPC_REPLACE_VERTEX ||
               opCode == MSG_OP_RPC_MERGE_VERTEX) {
        RpcSeekFirstOpMsg(rsp);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)1, affectRows);
        if (opCode == MSG_OP_RPC_INSERT_VERTEX) {
            EXPECT_EQ((uint32_t)8, datalen);
        } else {
            EXPECT_EQ((uint32_t)4, datalen);
        }
    }

    return GMERR_OK;
}

void UtPutBufDeleteVertex(MsgHeaderT **msgHeader, DeleteVertexMsgT *delMsg)
{
    EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    (*msgHeader)->reqTimeOut = CS_NEVER_TIMEOUT;
    (*msgHeader)->stmtId = 0;
    (*msgHeader)->flags |= CS_FLAG_SPLIT_DISABLED;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, delMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, delMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, delMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, delMsg->indexId));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &delMsg->condBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, delMsg->scanFlag));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &delMsg->leftFilterText));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &delMsg->rightFilterText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, delMsg->autoOperateFlag));
    (*msgHeader)->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_DELETE_VERTEX,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

void UtPutBufUpdateVertex(MsgHeaderT **msgHeader, UpdateVertexMsgT *updMsg)
{
    EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    (*msgHeader)->reqTimeOut = CS_NEVER_TIMEOUT;
    (*msgHeader)->stmtId = 0;
    (*msgHeader)->flags |= CS_FLAG_SPLIT_DISABLED;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, updMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, updMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, updMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, updMsg->indexId));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &updMsg->condBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &updMsg->filterBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &updMsg->vertexBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, updMsg->flag));
    (*msgHeader)->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_UPDATE_VERTEX,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

void UtPutBufInsertVertex(MsgHeaderT **msgHeader, InsertVertexMsgT *insMsg)
{
    EXPECT_EQ(GMERR_OK, FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER));
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    (*msgHeader)->reqTimeOut = CS_NEVER_TIMEOUT;
    (*msgHeader)->stmtId = 0;
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, insMsg->labelId));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, insMsg->version));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, insMsg->uuid));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 1));
    EXPECT_EQ(GMERR_OK, FixBufPutText(&req, &insMsg->vertexBufText));
    EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, insMsg->flag));
    (*msgHeader)->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_INSERT_VERTEX,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
}

Status CataLoginVerify(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isDBA, bool *isGroupLogin, CataRoleT *role)
{
    Status ret;
    // step 1 : 根据userName和processName判断能否登录
    if ((ret = CataLoginVerifyByUser(userNameInfo, login, isDBA, isGroupLogin, role) == GMERR_OK)) {
        return GMERR_OK;
    }
    // step 2 : 根据groupName和processName判断能否登录
    return CataLoginVerifyByGroup(userNameInfo, login, isDBA, isGroupLogin, role);
}

Status QryCheckLoginPrivByGroupListStub(SessionT *session, bool *login, CataRoleT *role)
{
    const SessionParamT param = {0};
    Status ret = QryInitUserAndGroup(session, &param);
    if (ret != GMERR_OK) {
        return ret;
    }
    CataUserNameInfoT user = {
        session->externalUser.dbUserName,
        session->externalUser.dbGroupName,
        session->externalUser.dbProcessName,
    };
    bool isGroupLogin;
    ret = (Status)CataLoginVerify(&user, login, &session->isDBA, &isGroupLogin, role);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Internal error occurs when check login.");
        return ret;
    }
    return GMERR_OK;
}

Status QryInitUserAndGroupStub(SessionT *session, SessionParamT *param)
{
    (void)param;
    errno_t rc = strcpy_s(session->externalUser.dbUserName, sizeof(session->externalUser.dbUserName), "root");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    rc = strcpy_s(session->externalUser.dbProcessName, sizeof(session->externalUser.dbProcessName), "gmrule");
    if (rc != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status QryTestDrtConnWritePackSuccessStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    return GMERR_OK;
}

Status QryTestAllocSession(DrtConnectionT **conn)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    const char *auditUserInfo = "0-qry_benchmark";
    DbCredT cred = {0};
    *conn = DrtAllocConnection(&drtInstance->connMgr, auditUserInfo, &cred);
    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupStub);
    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)QryTestDrtConnWritePackSuccessStub);
    CliConnectResponseT connResp = {0};
    SessionParamT param = {.logThreshold = 0, .rollBackThreshold = 0, .userName = "", .pwd = ""};
    Status ret = QryAllocSessionForDrt(*conn, &connResp, drtInstance, &param);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

static void UtDrtDetachConnection(DrtConnectionT *conn)
{
    DB_POINTER2(conn, conn->connMgr);
    conn->nodeId.nodeId = DB_INVALID_ID16;
    (void)DbAtomicDec(&conn->ref);
    DrtSetConnStatus(conn, CONN_STATUS_CLOSED);
    DrtConnProcessClosedList(conn->connMgr);
}

void QryTestReleaseSession(DrtConnectionT *conn)
{
    UtDrtDetachConnection(conn);
}

int32_t QryTestCreateVertexLabel(char *cfgJson, char *labelJson, FixBufferT **rsp)
{
    uint32_t ret;
    FixBufferT req = {0};
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    if (ret != GMERR_OK) {
        // logging
        printf("alloc session failed!\n");
        return ret;
    }
    DbMemCtxArgsT args = {0};
    DbMemCtxT *dyAlgoCtxVertex =
        DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
    DbMemCtxT *old = (DbMemCtxT *)DbMemCtxSwitchTo(dyAlgoCtxVertex);
    ret = FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t labelJsonLen = 0;
    uint32_t cfgJsonLen = 0;

    if (labelJson != NULL) {
        labelJsonLen = strlen(labelJson) + 1;
    }
    if (cfgJson != NULL) {
        cfgJsonLen = strlen(cfgJson) + 1;
    }

    TextT putText;

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    OpHeaderT *opHdr = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, &opHdr, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;
    msgHeader->flags = msgHeader->flags & (~CS_FLAG_SELF_SCHEDULE);
    msgHeader->opNum = 1;
    if (labelJson != NULL) {
        putText.str = labelJson;
        putText.len = labelJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, labelJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (cfgJson != NULL) {
        putText.str = cfgJson;
        putText.len = cfgJsonLen;
        ret = FixBufPutText(&req, &putText);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = FixBufPutUint32(&req, cfgJsonLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    putText.str = NULL;
    putText.len = 0;
    ret = FixBufPutText(&req, &putText);
    if (ret != GMERR_OK) {
        return ret;
    }

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(opHdr, MSG_OP_RPC_CREATE_VERTEX_LABEL, msgHeader->size - MSG_HEADER_ALIGN_SIZE);
    if (rsp != NULL) {
        SessionT *session = (SessionT *)conn->session;
        (*rsp) = QrySessionGetRsp(session);
        uint32_t len = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
        FixBufInitPut(*rsp, len);
        FixBufSeek(*rsp, len);
        (void)setStubC((void *)DrtFreeMsg, (void *)DrtFreeMsgStub);
    }
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    DrtServiceCtxT serviceCtx = {};
    serviceCtx.procStartTime = DbRdtsc();
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
    FixBufRelease(&req);
    if (old != NULL) {
        DbMemCtxSwitchTo(old);
    }
    DbDeleteDynMemCtx(dyAlgoCtxVertex);
    clearAllStub();
    return ret;
}

#ifdef __cplusplus
}
#endif
