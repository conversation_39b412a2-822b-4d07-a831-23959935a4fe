#include "query_benchmark.h"
#include "drt_instance_inner.h"
#ifndef FEATURE_PERSISTENCE
using namespace std;

TEST_F(QueryBenchmark, Query_Benchmark_InsertVertex)
{
    uint32_t ret;
    char *labelName = (char *)"labelvertex1";
    char *cfgJson = (char *)R"({
            "max_record_count":100001
        })";
    char *labelJson = (char *)R"([{
            "type":"record",
            "name":"labelvertex1",
            "fields":
                [
                    {
                        "name":"F0",
                        "type":"int32"
                    },
                    {
                        "name":"F1",
                        "type":"int32"
                    },
                    {
                        "name":"F2",
                        "type":"int32"
                    },
                    {
                        "name":"F3",
                        "type":"int32"
                    }
                ],
            "keys":
                [
                    {
                        "node":"labelvertex1",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
        }])";
    ret = QryTestCreateVertexLabel(cfg<PERSON><PERSON>, label<PERSON>son, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    // 从Catalog中查询得到相应点标签的元数据
    DmVertexLabelT *vertexLabel = NULL;
    DmVertexT *vertex = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    ret = CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)((Session *)conn->session)->memCtx, vertexLabel, &vertex);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader;
    MapKey TimeKeys;
    InsertVertexMsgT insMsg;
    uint32_t vertexBufLen = 0;
    uint8_t *buf = NULL;
    DmValueT propertyValue;

    for (uint32_t i = 0, j = 0; i < times; i++) {
        // set the porperty of vertex
        propertyValue.type = DB_DATATYPE_INT32;
        propertyValue.value.intValue = i;
        ret = DmVertexSetPropeByName("F0", propertyValue, vertex);
        EXPECT_EQ(GMERR_OK, ret);

        propertyValue.type = DB_DATATYPE_INT32;
        propertyValue.value.intValue = i;
        ret = DmVertexSetPropeByName("F1", propertyValue, vertex);
        EXPECT_EQ(GMERR_OK, ret);

        propertyValue.type = DB_DATATYPE_INT32;
        propertyValue.value.intValue = i;
        ret = DmVertexSetPropeByName("F2", propertyValue, vertex);
        EXPECT_EQ(GMERR_OK, ret);

        propertyValue.type = DB_DATATYPE_INT32;
        propertyValue.value.intValue = i;
        ret = DmVertexSetPropeByName("F3", propertyValue, vertex);
        EXPECT_EQ(GMERR_OK, ret);
        ret = DmSerializeVertex(vertex, &buf, &vertexBufLen);
        EXPECT_EQ(GMERR_OK, ret);

        insMsg.labelId = vertexLabel->metaCommon.metaId;
        insMsg.version = vertexLabel->metaCommon.version;
        insMsg.uuid = vertexLabel->metaVertexLabel->uuid;
        insMsg.vertexBufText.str = (char *)buf;
        insMsg.vertexBufText.len = vertexBufLen;
        insMsg.labelName.str = labelName;
        insMsg.labelName.len = strlen(labelName) + 1;
        insMsg.flag = 0;

        UtPutBufInsertVertex(&msgHeader, &insMsg);

        g_qeBenchmarkStartCycle = DbRdtsc();
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        g_qeBenchmarkEndCycle = DbRdtsc();
        g_qeBenchmarkCyclesPer = g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        g_qeBenchmarkCyclesPerSum += g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        if (queryBenchmarkTask.head.headParamList[j] == i + 1) {
            TimeKeys.headLinePara = i + 1;
            TimeKeys.opName = g_qeOperation.opNameList[1];
            g_qeTimeResultMap[TimeKeys].sumCycles = g_qeBenchmarkCyclesPerSum;
            j++;
        }
    }
    g_qeBenchmarkCyclesPerSum = 0;

    ret = CataReleaseVertexLabel(vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    DmDestroyVertex(vertex);
    QryTestReleaseSession(conn);
}

TEST_F(QueryBenchmark, Query_Benchmark_UpdateVertex)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;

    const char *keyName = "T39_K0";
    TextT labelName = {.len = strlen("labelvertex1") + 1, .str = (char *)"labelvertex1"};

    // 从Catalog中查询得到相应点标签的元数据
    DmVertexLabelT *vertexLabel = NULL;
    DmVertexT *vertex = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName.str);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)((Session *)conn->session)->memCtx, vertexLabel, &vertex);
    MapKey TimeKeys;
    UpdateVertexMsgT UpdMsg;
    DmValueT propertyValue;
    DmIndexKeyT *filter = NULL;
    uint32_t vertexBufLen = 0;
    uint8_t *vertexBuf = NULL;
    uint32_t filterBufLen = 0;
    uint8_t *filterBuf = NULL;

    for (uint32_t i = 0, j = 0; i < times; i++) {
        propertyValue.type = DB_DATATYPE_INT32;
        propertyValue.value.intValue = i + 100000;
        DmVertexSetPropeByName("F3", propertyValue, vertex);

        DmSerializeVertex(vertex, &vertexBuf, &vertexBufLen);
        propertyValue.type = DB_DATATYPE_INT32;
        propertyValue.value.intValue = i;
        EXPECT_EQ(GMERR_OK, DmCreateIndexKeyByNameWithMemCtx((DbMemCtxT *)((Session *)conn->session)->memCtx,
                                vertexLabel, keyName, &propertyValue, 1, &filter));

        filterBufLen = DmIndexKeyGetSeriBufLength(filter);
        filterBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, filterBufLen);
        DmSerializeIndexKey2InvokerBuf(filter, filterBufLen, filterBuf);
        DmDestroyIndexKey(filter);

        UpdMsg.labelId = vertexLabel->metaCommon.metaId;
        UpdMsg.version = vertexLabel->metaCommon.version;
        UpdMsg.uuid = vertexLabel->metaVertexLabel->uuid;
        UpdMsg.vertexBufText = {.len = vertexBufLen, .str = (char *)vertexBuf};
        UpdMsg.filterBufText = {.len = filterBufLen, .str = (char *)filterBuf};
        UpdMsg.condBufText = {.len = 0, .str = NULL};
        UpdMsg.labelName.str = labelName.str;
        UpdMsg.labelName.len = labelName.len;
        UpdMsg.flag = 0;
        UpdMsg.indexId = vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId;

        UtPutBufUpdateVertex(&msgHeader, &UpdMsg);

        g_qeBenchmarkStartCycle = DbRdtsc();
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        g_qeBenchmarkEndCycle = DbRdtsc();
        g_qeBenchmarkCyclesPer = g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        g_qeBenchmarkCyclesPerSum += g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        if (queryBenchmarkTask.head.headParamList[j] == i + 1) {
            TimeKeys.headLinePara = i + 1;
            TimeKeys.opName = g_qeOperation.opNameList[2];
            g_qeTimeResultMap[TimeKeys].sumCycles = g_qeBenchmarkCyclesPerSum;
            j++;
        }
        DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, filterBuf);
    }
    g_qeBenchmarkCyclesPerSum = 0;
    CataReleaseVertexLabel(vertexLabel);
    DmDestroyVertex(vertex);
    QryTestReleaseSession(conn);
}

TEST_F(QueryBenchmark, Query_Benchmark_DeleteVertex)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    MsgHeaderT *msgHeader = NULL;
    const char *keyName = "T39_K0";
    DmIndexKeyT *filter = NULL;
    uint8_t *seriBuf = NULL;
    uint32_t length;
    TextT labelName = {.len = strlen("labelvertex1") + 1, .str = (char *)"labelvertex1"};

    // get vertexLabel
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName.str);
    ret = CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
    // create filter
    DmValueT propertyValues;
    propertyValues.type = DB_DATATYPE_INT32;
    g_qeBenchmarkCyclesPerSum = 0;
    MapKey TimeKeys;
    DeleteVertexMsgT delMsg;
    for (uint32_t i = 0, j = 0; i < times; i++) {
        propertyValues.value.intValue = i + 100000;
        ret = DmCreateIndexKeyByNameWithMemCtx(
            (DbMemCtxT *)((Session *)conn->session)->memCtx, vertexLabel, keyName, &propertyValues, 1, &filter);
        EXPECT_EQ(GMERR_OK, ret);
        length = DmIndexKeyGetSeriBufLength(filter);
        seriBuf = (uint8_t *)DbDynMemCtxAlloc((DbMemCtxT *)((Session *)conn->session)->memCtx, length);
        ret = DmSerializeIndexKey2InvokerBuf(filter, length, seriBuf);
        EXPECT_EQ(GMERR_OK, ret);
        DmDestroyIndexKey(filter);

        delMsg.labelId = vertexLabel->metaCommon.metaId;
        delMsg.version = vertexLabel->metaCommon.version;
        delMsg.uuid = vertexLabel->metaVertexLabel->uuid;
        delMsg.scanFlag = 0;
        delMsg.leftFilterText = {.len = length, .str = (char *)seriBuf};
        delMsg.rightFilterText = {.len = 0, .str = NULL};
        delMsg.condBufText = {.len = 0, .str = NULL};
        delMsg.labelName.str = labelName.str;
        delMsg.labelName.len = labelName.len;
        delMsg.autoOperateFlag = 0;
        delMsg.indexId = vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId;

        UtPutBufDeleteVertex(&msgHeader, &delMsg);

        g_qeBenchmarkStartCycle = DbRdtsc();
        DrtServiceCtxT serviceCtx = {};
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);
        g_qeBenchmarkEndCycle = DbRdtsc();
        g_qeBenchmarkCyclesPer = g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        g_qeBenchmarkCyclesPerSum += g_qeBenchmarkEndCycle - g_qeBenchmarkStartCycle;
        if (queryBenchmarkTask.head.headParamList[j] == i + 1) {
            TimeKeys.headLinePara = i + 1;
            TimeKeys.opName = g_qeOperation.opNameList[3];
            g_qeTimeResultMap[TimeKeys].sumCycles = g_qeBenchmarkCyclesPerSum;
            j++;
        }
        DbDynMemCtxFree((DbMemCtxT *)((Session *)conn->session)->memCtx, seriBuf);
    }
    CataReleaseVertexLabel(vertexLabel);
    g_qeBenchmarkCyclesPerSum = 0;
    QryTestReleaseSession(conn);
    BenchmarkCalculateResult(&queryBenchmarkTask, &g_qeTimeResultMap);
    ret = BenchmarkWriteFile(&g_qeOperation, &g_qeHeader, g_qeTimeResultMap, (char *)QE_BENCHMARK_FILE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    BenchmarkPrintResult(&queryBenchmarkTask, &g_qeTimeResultMap);
}
#endif
