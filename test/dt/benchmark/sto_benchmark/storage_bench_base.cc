/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2020. All rights reserved.
 * File Name: storage_bench_base.cc
 * Description:
 * Author: wangsiyuan
 * Create: 2021/5/31
 */

#include "gtest/gtest.h"
#include "stub.h"
#include "ee_context.h"
#include "storage_bench_base.h"
#include "ee_cmd.h"
#include "drt_instance.h"
#include "db_mem_context.h"
#include "adpt_types.h"
#include "se_instance.h"
#include "cpl_public_parser_ddl.h"
#include "cpl_public_parser.h"
#include "db_dynmem_algo.h"
#include "se_resource_session_pub.h"
#include "db_label_latch_mgr.h"
#include "db_sysapp_context.h"
#include "ee_cltcache.h"
#include "ee_init.h"

// 存储引擎全局变量
void *g_seTopShmCtxTop;
DbTopShmemCtxT *g_topShmMemCtx = nullptr;
SeRunCtxHdT g_seRunCtx;
SessionT *g_session = nullptr;
QryStmtT *g_stmt = nullptr;
DmVertexLabelT *g_vertexLabel = nullptr;

// #7号表ip4forward
const char *g_ip4forward = "ip4forward";
const char *g_ip4forwardConfig = R"({"max_record_count":4000000, "isFastReadUncommitted":false})";
const char *g_ip4forwardSchema =
    R"([{
        "type":"record",
        "name": "ip4forward",
        "comment": "ip4forward",
        "fields": [
            { "name": "vr_id", "type": "uint32",
            "comment": "Vs索引" },
            { "name": "vrf_index", "type": "uint32",
            "comment": "VpnInstace索引" },
            { "name": "dest_ip_addr", "type": "uint32",
            "comment": "目的地址" },
            { "name": "mask_len", "type": "uint8",
            "comment": "掩码长度" },
            { "name": "nhp_group_flag", "type": "uint8",
            "comment": "标识Nhp或NhpG" },
            { "name": "qos_profile_id", "type": "uint16",
            "comment": "QosID" },
            { "name": "primary_label", "type": "uint32",
            "comment": "标签" },
            { "name": "attribute_id", "type": "uint32",
            "comment": "属性ID" },
            { "name": "nhp_group_id", "type": "uint32",
            "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定" },
            { "name": "path_flags", "type": "uint32",
            "comment": "path标记" },
            { "name": "flags", "type": "uint32",
            "comment": "标志 path完备性" },
            { "name": "status_high_prio", "type": "uint8", "default": 0,
            "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"},
            { "name": "status_normal_prio", "type": "uint8", "default": 0,
            "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"},
            { "name": "errcode_high_prio", "type": "uint8", "default": 0,
            "comment": "SERVICE高优先级下发状态错误码"},
            { "name": "errcode_normal_prio", "type": "uint8", "default": 0,
            "comment": "SERVICE普通优先级下发状态错误码"},
            { "name": "svc_ctx_high_prio", "type": "fixed", "size": 16,
            "comment": "高优先级FWM_SERVICE返回的svcCtx" },
            { "name": "svc_ctx_normal_prio", "type": "fixed", "size": 16,
            "comment": "普通优先级FWM_SERVICE返回的svcCtx" },
            { "name": "app_source_id", "type": "uint32",  "default": 0},
            { "name": "table_smooth_id", "type": "uint32" },
            { "name": "app_obj_id", "type": "uint64" },
            { "name": "trace", "type": "uint64" },
            { "name": "app_version", "type": "uint32" },
            { "name": "route_flags", "type": "uint16","comment": "路由标记" },
            { "name": "reserved", "type": "uint16", "nullable": true,"comment": "预留" },
            { "name": "time_stamp_create", "type": "time" },
            { "name": "time_stamp_smooth", "type": "time" }
        ],
        "super_fields": [
            { "name":"sp1", "fields":{ "begin":"vr_id", "end":"time_stamp_smooth" } }
        ],
        "keys": [
            {"name": "primary_key",
             "node": "ip4forward",
             "index": { "type": "primary" },
             "fields": [ "vr_id", "vrf_index", "dest_ip_addr", "mask_len" ]
            },
            { "name": "localhash_key",
            "index": { "type": "hashcluster" },
            "node": "ip4forward",
            "fields": [ "nhp_group_id", "vr_id" ],
            "comment": "根据nhp_group_id vrid索引"
            },
            { "name": "vrfid_hashcluster_key",
              "index": { "type": "hashcluster" },
              "node": "ip4forward",
              "fields": [ "app_source_id", "vr_id", "vrf_index" ],
              "comment": "根据app_source_id   vr_id  vrf_index索引"
            }
        ]
    }])";

static int32_t SeBenchInitSeInstance(DbTopShmemCtxT **dbTopShmemCtx, void **seTopDynaCtxTop, SeRunCtxHdT *seRunCtx)
{
    *dbTopShmemCtx = (DbTopShmemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    EXPECT_TRUE(*dbTopShmemCtx != NULL);

    DbMemCtxArgsT dyargs = {0};
    dyargs.ctxSize = sizeof(DbDynamicMemCtxT);
    dyargs.memType = DB_DYNAMIC_MEMORY;
    dyargs.init = DynamicAlgoInit;
    *seTopDynaCtxTop = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "Se Dynamic MemCtx", &dyargs);
    if (*seTopDynaCtxTop == NULL) {
        return DB_ERROR;
    };

    // set up default dynamic memctx
    DbMemCtxSwitchTo((DbMemCtxT *)*seTopDynaCtxTop);

    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = SE_DEFAULT_PAGE_SIZE;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
    config.maxTrxNum = MAX_CONN_NUM + MAX_BG_WORKER_NUM;
    SeInstanceHdT sePtr = NULL;
    int32_t ret = SeCreateInstance(NULL, (DbMemCtxT *)*dbTopShmemCtx, &config, &sePtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeOpen(GET_INSTANCE_ID, (DbMemCtxT *)*seTopDynaCtxTop, NULL, seRunCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SeOpenResSession(*seRunCtx);
}

static int32_t AllocQryContext(QryContextT **qryCtx)
{
    auto *memCtx = (DbMemCtxT *)g_seTopShmCtxTop;
    auto *ctx = (QryContextT *)DbDynMemCtxAlloc(g_seTopShmCtxTop, sizeof(QryContextT));
    if (ctx == nullptr) {
        return -1;
    }
    memset(ctx, 0, sizeof(QryContextT));
    ctx->memCtx = memCtx;

    DbGaListInit(&ctx->labels, ctx, (GaListAllocFuncT)QryCtxAllocMem, (GaListFreeFuncT)QryCtxFreeMem);
    DbGaListInit(&ctx->edgeLabels, ctx, (GaListAllocFuncT)QryCtxAllocMem, (GaListFreeFuncT)QryCtxFreeMem);

    *qryCtx = ctx;
    return GMERR_OK;
}

static Status DbCfgGetStub(DbCfgMgrHandleT handle, DbCfgEmItemIdE id, DbCfgValueT *value)
{
    if (handle == NULL || value == NULL) {
        return GMERR_NO_DATA;
    }
    DbCfgMgrT *mgr = (DbCfgMgrT *)handle;
    if ((uint32_t)id >= mgr->cfgItemNum) {
        return GMERR_DATA_EXCEPTION;
    }
    DbRWSpinRLock(&mgr->cfgModifyLock);
    Status ret = GMERR_OK;
    if (id == DB_CFG_LOCAL_LOCATOR) {
        char serverLocator[] = "usocket:/run/verona/unix_emserver;channel:ctl_channel";
        errno_t err = strcpy_s(value->str, sizeof(value->str), serverLocator);
        ret = err != EOK ? GMERR_MEMORY_OPERATE_FAILED : GMERR_OK;
    } else {
        ret = DbCfgValueCpy(&mgr->cfgItems[id].value, value);
    }
    DbRWSpinRUnlock(&mgr->cfgModifyLock);
    return ret;
}

void SeBenchSetupEnvironment()
{
    int32_t ret;
    // common初始化
    DbCommonInitCfgT cfg = {
        .env = ADPT_RTOS_SERVER,
        .configFileName = NULL,
        .isBackGround = false,
    };
    DbSetServerThreadFlag();
    ret = DbCommonInit(&cfg, NULL, NULL);
    ASSERT_EQ(ret, GMERR_OK);

    // init label cache
    ret = CataLabelCacheInitWithOutTimer(NULL);
    ASSERT_EQ(ret, GMERR_OK);

    // 存储引擎初始化
    SeBenchInitSeInstance(&g_topShmMemCtx, &g_seTopShmCtxTop, &g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    // runtime instance init
    (void)setStubC((void *)DbCfgGet, (void *)DbCfgGetStub);
    ret = DrtInstanceInit(false, NULL);
    ASSERT_EQ(ret, GMERR_OK);
    (void)clearAllStub();

    // init query engine, because we need to create label via json
    // init session pool
    ret = QryInitSessionPool(NULL);
    ASSERT_EQ(ret, GMERR_OK);

    ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &g_session);
    ASSERT_EQ(ret, GMERR_OK);

    ret = QryAllocStmt(g_session, &g_stmt);
    ASSERT_EQ(ret, GMERR_OK);

    ret = AllocQryContext(&g_stmt->context);
    ASSERT_EQ(ret, GMERR_OK);

    ret = ExecutorInit(NULL);
    ASSERT_EQ(ret, GMERR_OK);

    TextT jsonTxt, configTxt;
    jsonTxt.str = const_cast<char *>(g_ip4forwardSchema);
    jsonTxt.len = strlen(g_ip4forwardSchema);

    configTxt.str = const_cast<char *>(g_ip4forwardConfig);
    configTxt.len = strlen(g_ip4forwardConfig) + 1;
    DbCfgMgrHandleT handle = DbGetCfgHandle(NULL);
    DbServerInitCltStat();
    ret = DbCfgSetByNameInner(handle, "compatibleV3", "0", false, true);
    ASSERT_EQ(ret, GMERR_OK);
    QryCreateVertexLabelDescT desc = {0};
    ret = QryParseVertexLabel(g_stmt, &jsonTxt, &configTxt, &desc);
    ASSERT_EQ(ret, GMERR_OK);

    g_vertexLabel = ((QryCreateSingleVertexLabelDescT *)DbListItem(&desc.vertexLabels, 0))->vertexLabel;
    if (g_vertexLabel->metaCommon.version == DB_INVALID_UINT32) {
        g_vertexLabel->metaCommon.version = 0;
    }
    g_vertexLabel->commonInfo->creator = NULL;
    g_vertexLabel->memCtx = (DbMemCtxT *)g_seTopShmCtxTop;
    ret = CataGetTspIdByName(PUBLIC_TABLESPACE_NAME, &g_vertexLabel->metaCommon.tablespaceId, NULL);
    ASSERT_EQ(ret, GMERR_OK);
    DmVertexLabelT *tempLabel = NULL;
    ret = CmdCreateVertexLabel(g_session->seInstance, g_vertexLabel, &tempLabel);
    ASSERT_EQ(ret, GMERR_OK);
    DmVertexLabelT *vl = NULL;
    ret = CataGetVertexLabelById(NULL, g_vertexLabel->metaCommon.metaId, &vl);
    ASSERT_EQ(ret, GMERR_OK);
    g_vertexLabel = vl;
    g_vertexLabel->memCtx = (DbMemCtxT *)g_seTopShmCtxTop;
}
