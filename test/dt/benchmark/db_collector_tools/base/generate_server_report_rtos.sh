#! /bin/bash

env=$1
resultDir=$2
resultFile=$3
homeDir=$(dirname $0)
source ${homeDir}/../base/common_func.sh
shmemUsageInfoFile=${resultDir}/shmem_usage_info.txt
cfgFile=${resultDir}/configInfo.txt

intermediateFileDir=${resultDir}/intermediateFile
rm -rf ${intermediateFileDir}
mkdir ${intermediateFileDir}

getCtxIdFromMemoryView()
{
  ctxIdList=$(grep 'CTX_ID: ' ${shmemUsageInfoFile} | awk -F": " '{print $2}')
  ctxIdListRes=$(grep -B1 -w 'CTX_LEVEL: 0\|CTX_LEVEL: 1\|CTX_LEVEL: 2' ${shmemUsageInfoFile} | grep 'CTX_ID:'| awk -F": " '{print $2}')
}

getInstanceIdFromConfig()
{
  instanceId=$(grep -A1 "NAME: instanceId" ${cfgFile} | grep "VALUE" | awk -F":" '{print $2}')
}

hpeServerSmapsFile=
processHpeCliSmapsFile()
{
  hpeSmapsLog=/opt/vrpv8/home/<USER>/hpe_smaps.log
  hpeServerSmapsFile=${intermediateFileDir}/hpe_smaps_server.log
  rm -f ${hpeSmapsLog}
  hpecli smaps dump
  sed -n '/ gmserver/,/PID/p' ${hpeSmapsLog} >> ${hpeServerSmapsFile}
}

getIntermediateResult()
{
  hppcli diag debug mem_info 2 0 >> ${intermediateFileDir}/PtName_to_PtNo.txt
  key=0
  serverId=1

  for ctxId in ${ctxIdList}
  do
    eval Size_${ctxId}=0
    eval Rss_${ctxId}=0
    eval Pss_${ctxId}=0
  done

  echo "================================share memory information================================" >> ${intermediateFileDir}/intermediateResult.txt
  printf "%-30s %-15s %-15s %-15s %-15s\n" "SE-MODULE" "CtxId" "Size(KB)" "Rss(KB)" "Pss(KB)" >> ${intermediateFileDir}/intermediateResult.txt
  for ctxId in ${ctxIdList}
  do
    let "number=${key}|${serverId}<<30|${instanceId}<<22|${ctxId}"  #reference: DbConstructShmemKeyById
    local PtName="_${number}"
    local ShmIds=$(grep "${PtName}" ${intermediateFileDir}/PtName_to_PtNo.txt | awk '{print $3}')

    if [ "x" = "x${ShmIds}" ];then
      echo "PtName ${PtName} don't have ShmId !"
      continue
    fi

    local ctxName=$(grep -B1 -w "CTX_ID: ${ctxId}" ${shmemUsageInfoFile} | grep 'CTX_NAME' |awk -F": " '{print $2}')
    echo "Processing ${ctxName} ... "

    for ShmId in ${ShmIds}
    do
      local size=$(grep -B7 "${ShmId}" ${hpeServerSmapsFile} | grep 'Size:' | awk '{print $2}')
      local rss=$(grep -B7 "${ShmId}" ${hpeServerSmapsFile} | grep 'Rss:' | awk '{print $2}')
      local pss=$(grep -B7 "${ShmId}" ${hpeServerSmapsFile} | grep 'Pss:' | awk '{print $2}')

      eval let Size_${ctxId}+=size
      eval let Rss_${ctxId}+=rss
      eval let Pss_${ctxId}+=pss
    done

    eval local Size=\$Size_${ctxId}
    eval local Rss=\$Rss_${ctxId}
    eval local Pss=\$Pss_${ctxId}

    printf "%-30s %-15s %-15s %-15s %-15s\n" "${ctxName}" "${ctxId}" "${Size}" "${Rss}" "${Pss}" >> ${intermediateFileDir}/intermediateResult.txt
  done
  echo "=====================================END=====================================" >> ${intermediateFileDir}/intermediateResult.txt
}

sumChildShmInfo()
{
  echo "Sum all result..."
  echo "================================share memory information================================" >> ${resultFile}
  printf "%-30s %-15s %-15s %-15s\n" "SE-MODULE" "Size(KB)(include child)" "Rss(KB)(include child)" "Pss(KB)(include child)" >> ${resultFile}
  for ctxIdRes in ${ctxIdListRes}
  do
    local allSize=0
    local allRss=0
    local allPss=0
    local childIdList=$(grep -A11 -w "CTX_ID: ${ctxIdRes}" ${shmemUsageInfoFile} |grep -w 'ALL_CHILD_CTXIDS:'| awk -F": " '{print $2}' | tr " " "\n")
    for childId in ${childIdList}
    do
      eval local size=\$Size_${childId}
      eval local rss=\$Rss_${childId}
      eval local pss=\$Pss_${childId}

      let allSize+=size
      let allRss+=rss
      let allPss+=pss
    done

    local ctxName=$(grep -B1 -w "CTX_ID: ${ctxIdRes}" ${shmemUsageInfoFile} | grep 'CTX_NAME' |awk -F": " '{print $2}')

    printf "%-30s %-15s %-15s %-15s\n" "${ctxName}" "${allSize}" "${allRss}" "${allPss}" >> ${resultFile}
  done
}

get_mem_monitor_info()
{
  local pid=`pidof gmserver`
    ./mem_monitor > /dev/null 2<&1 <<EOF
    4
    ${pid}
    0
    9
EOF
  cat ${pid}.csv >> ${resultFile}
}

sumAllTypeMemInfo()
{
  # arr0: 所有总的内存统计 size | rss | pss
  # arr2: 共享内存 size | rss | pss
  # arr3: anonymous动态内存 size | rss | pss
  # arr4: so size | rss | pss
  # arr5: unknown size | rss | pss
  # arr6: gmserver size | rss | pss
  # arr7: 除了前面类别的内存段 size | rss | pss
  local arr01=0 # 设备上不支持数组和map
  local arr02=0
  local arr03=0
  local arr21=0
  local arr22=0
  local arr23=0
  local arr31=0
  local arr41=0
  local arr42=0
  local arr43=0
  local arr51=0
  local arr52=0
  local arr53=0
  local arr61=0
  local arr62=0
  local arr63=0
  local arr71=0
  local arr72=0
  local arr73=0
  arr01=$(cat ${hpeServerSmapsFile} | grep "^Size:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr02=$(cat ${hpeServerSmapsFile} | grep "^Rss:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr03=$(cat ${hpeServerSmapsFile} | grep "^Pss:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr21=$(cat ${hpeServerSmapsFile} | grep -A 5 "shmm\>" | grep "^Size:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr22=$(cat ${hpeServerSmapsFile} | grep -A 5 "shmm\>" | grep "^Rss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr23=$(cat ${hpeServerSmapsFile} | grep -A 5 "shmm\>" | grep "^Pss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr31=$(cat ${hpeServerSmapsFile} | grep -A 5 "anonymous\>" | grep "^Size:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr32=$(cat ${hpeServerSmapsFile} | grep -A 5 "anonymous\>" | grep "^Rss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr33=$(cat ${hpeServerSmapsFile} | grep -A 5 "anonymous\>" | grep "^Pss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr41=$(cat ${hpeServerSmapsFile} | grep -A 5 "\.so" | grep "^Size:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr42=$(cat ${hpeServerSmapsFile} | grep -A 5 "\.so" | grep "^Rss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr43=$(cat ${hpeServerSmapsFile} | grep -A 5 "\.so" | grep "^Pss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr51=$(cat ${hpeServerSmapsFile} | grep -A 5 "unknown\>" | grep "^Size:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr52=$(cat ${hpeServerSmapsFile} | grep -A 5 "unknown\>" | grep "^Rss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr53=$(cat ${hpeServerSmapsFile} | grep -A 5 "unknown\>" | grep "^Pss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr61=$(cat ${hpeServerSmapsFile} | grep -A 5 "/usr/local/hpe/gmserver" | grep "^Size:" | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr62=$(cat ${hpeServerSmapsFile} | grep -A 5 "/usr/local/hpe/gmserver" | grep "^Rss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr63=$(cat ${hpeServerSmapsFile} | grep -A 5 "/usr/local/hpe/gmserver" | grep "^Pss:" |awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
  arr71=`expr ${arr01} - ${arr21} - ${arr31} - ${arr41} - ${arr51} - ${arr61}`
  arr72=`expr ${arr02} - ${arr22} - ${arr32} - ${arr42} - ${arr52} - ${arr62}`
  arr73=`expr ${arr03} - ${arr23} - ${arr33} - ${arr43} - ${arr53} - ${arr63}`
  local subFix="|_"
  printLine "Mem Info From Smaps" ${resultFile}
  printMemInfo "" "Type" "Size(Uint:KB)" "Rss(Uint:KB)" "Pss(Uint:KB)" ${resultFile}
  printMemInfo "" "total" "${arr01}" "${arr02}" "${arr03}" ${resultFile}
  printMemInfo "${subFix}" "shmem" "${arr21}" "${arr22}" "${arr23}" ${resultFile}
  printMemInfo "${subFix}" "anonymous" "${arr31}" "${arr32}" "${arr33}" ${resultFile}
  printMemInfo "${subFix}" "so" "${arr41}" "${arr42}" "${arr43}" ${resultFile}
  printMemInfo "${subFix}" "unkonwn" "${arr51}" "${arr52}" "${arr53}" ${resultFile}
  printMemInfo "${subFix}" "gmserver" "${arr61}" "${arr62}" "${arr63}" ${resultFile}
  printMemInfo "${subFix}" "other" "${arr71}" "${arr72}" "${arr73}" ${resultFile}
}

echo "Begin to collect share memory information, please wait..."
getCtxIdFromMemoryView
getInstanceIdFromConfig
processHpeCliSmapsFile
getIntermediateResult
printSpaceLine ${resultFile}
# 按照ctx模块汇总共享内存使用信息
sumChildShmInfo
# 根据共享内存、动态内存、匿名段内存等统计内存信息
printSpaceLine ${resultFile}
sumAllTypeMemInfo
if [ "xrtosv2" == "x${env}" ]; then
   get_mem_monitor_info
fi
echo "=====================================END=====================================" >> ${resultFile}
cat ${resultFile}
