homeDir=$(dirname $0)
source ${homeDir}/../base/common_func.sh
env=${1}
pId=${2}
pName=${3}
soPath=${4}
socket=${5}

homeDir=$(dirname $0)/..
resultDir=${homeDir}/result_${pName}_${pId}
rm -rf ${resultDir}
mkdir ${resultDir}

resultFile=${resultDir}/result.txt
smapsFile=${resultDir}/smaps.txt
> ${resultFile}
> ${smapsFile}

cp /proc/${pId}/smaps ${smapsFile}

# 汇总smaps中的信息
summary_smaps_info()
{
    printLine "Mem Info From Smaps" ${resultFile}
    local subFix=$1
    # 定义一个数组
    # 0：total size | total rss | total pss
    # 1: stack size | stack rss | stack pss
    # 2: heap size | heap rss | heap pss
    # 3: shmem size | shmem rss | shmem pss
    # 4: server size | server rss | server pss
    # 5: so size | so rss | so pss
    # 6: other size | other rss | other pss
    local arr00=0
    local arr01=0
    local arr02=0
    local arr10=0
    local arr11=0
    local arr12=0
    local arr20=0
    local arr21=0
    local arr22=0
    local arr30=0
    local arr31=0
    local arr32=0
    local arr40=0
    local arr41=0
    local arr42=0
    local arr50=0
    local arr51=0
    local arr52=0
    local arr60=0
    local arr61=0
    local arr62=0
    local arr70=0
    local arr71=0
    local arr72=0
    local arr80=0
    local arr81=0
    local arr82=0
    arr00=$(cat ${smapsFile} | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr01=$(cat ${smapsFile} | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr02=$(cat ${smapsFile} | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr10=$(cat ${smapsFile} | grep -A 5 "stack" | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr11=$(cat ${smapsFile} | grep -A 5 "stack" | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr12=$(cat ${smapsFile} | grep -A 5 "stack" | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr20=$(cat ${smapsFile} | grep -A 5 "heap" | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr21=$(cat ${smapsFile} | grep -A 5 "heap" | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr22=$(cat ${smapsFile} | grep -A 5 "heap" | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr30=$(cat ${smapsFile} | grep -A 5 "/SYSV" | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr31=$(cat ${smapsFile} | grep -A 5 "/SYSV" | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr32=$(cat ${smapsFile} | grep -A 5 "/SYSV" | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr40=$(cat ${smapsFile} | grep -A 5 "/${pName}$" | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr41=$(cat ${smapsFile} | grep -A 5 "/${pName}$" | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr42=$(cat ${smapsFile} | grep -A 5 "/${pName}$" | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr50=$(cat ${smapsFile} | grep -A 5 "\.so" | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr51=$(cat ${smapsFile} | grep -A 5 "\.so" | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr52=$(cat ${smapsFile} | grep -A 5 "\.so" | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr70=$(cat ${smapsFile} | grep -A 5 "shmm" | grep '^Size:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr71=$(cat ${smapsFile} | grep -A 5 "shmm" | grep '^Rss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr72=$(cat ${smapsFile} | grep -A 5 "shmm" | grep '^Pss:' | awk 'BEGIN{sum=0}{sum += $2} END{print sum}')
    arr60=`expr ${arr00} - ${arr10} - ${arr20} - ${arr30} - ${arr40} - ${arr50} - ${arr70}`
    arr61=`expr ${arr01} - ${arr11} - ${arr21} - ${arr31} - ${arr41} - ${arr51} - ${arr71}`
    arr62=`expr ${arr02} - ${arr12} - ${arr22} - ${arr32} - ${arr42} - ${arr52} - ${arr72}`
    arr80=`expr ${arr30} + ${arr70}`
    arr81=`expr ${arr31} + ${arr71}`
    arr82=`expr ${arr32} + ${arr72}`
    printMemInfo "" "Type" "Size(Uint:KB)" "Rss(Uint:KB)" "Pss(Uint:KB)" ${resultFile}
    printMemInfo "" "total" "${arr00}" "${arr01}" "${arr02}" ${resultFile}
    printMemInfo "${subFix}" "stack" "${arr10}" "${arr11}" "${arr12}" ${resultFile}
    printMemInfo "${subFix}" "heap" "${arr20}" "${arr21}" "${arr22}" ${resultFile}
    printMemInfo "${subFix}" "shmem" "${arr81}" "${arr81}" "${arr82}" ${resultFile}
    printMemInfo "${subFix}" ${pName} "${arr40}" "${arr41}" "${arr42}" ${resultFile}
    printMemInfo "${subFix}" "so" "${arr50}" "${arr51}" "${arr52}" ${resultFile}
    printMemInfo "${subFix}" "other" "${arr60}" "${arr61}" "${arr62}" ${resultFile}
}

summary_smaps_info "|_"
printSpaceLine ${resultFile}
summary_so_info ${soPath}
printSpaceLine ${resultFile}
cat ${resultFile}
