#! /bin/bash
# ----------------------------------------------------------------------------
# Description: 全量crud操作并发时先读再写
# Author: wangweida wx994638
# Create: 2021-09
# History: 
# ----------------------------------------------------------------------------
# IOT环境上不能带-ef参数搜索， 全部使用ps 
function check_gmserverId()
{
    gmserverId=`ps | grep gmserver | grep -v grep | awk '{print $1}' | wc -l`
    if [ $gmserverId -le 0 ];then
       echo "----------gmserver is exit!----------"
       # IOT上无法使用pidof xxx形式
       teststaId=`ps | grep teststa |grep -v grep|awk {'print $1'}`
       # IOT上无法使用exit 1 形式， 直接导致脚本无法继续执行
       kill -9 $teststaId
       # exit 1
    fi
}
ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="harmony"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ "X$ENV_TYPE" == "Xharmony" ];then
   echo "do nothing"
else
   stop.sh -f
    sh $TEST_HOME/tools/modifyCfg.sh "workerHungThreshold=6,200,300"
    start.sh -f
fi

../test_execute_all.sh
gmimport -c kvtable -f schema_file/gmimport_Kvtabel_01.gmconfig -t KV600000

localhash_true="primary_key_name"
localhash_false="ifname_pk"
lpm_key="ip4forward_lpm"
hashcluster_key="vrfid_hashcluster_key"
start_id=0
if [ X"$ENV_TYPE" == "Xharmony" ];then
   end_id=4000
   # soho设备，线程过多，设备易重启
   thread_count=1
else
   end_id=8192
   thread_count=10
fi
times=100
function setMax()
{
   ${TEST_HOME}/perf/stability/teststa -v -m replace -n aaa_account_scheme -b 0 -e 8192
   ${TEST_HOME}/perf/stability/teststa -v -m replace -n ip4forward -b 0 -e 2000000
   ${TEST_HOME}/perf/stability/teststa -v -m replace -n nhp_group -b 0 -e 409600
   ${TEST_HOME}/perf/stability/teststa -v -m replace -n if -b 0 -e 128000
   ${TEST_HOME}/perf/stability/teststa -k -m set -n KV600000 -b 0 -e 100000 -K gmdb%d:0:0: -V 10-7168,gmdb%s:2:abcdefg:
}

function loop_set()
{
   for i in $(seq 1 $times)
   do
      for labelName in "aaa_account_scheme" "if" "ip4forward" "nhp_group"
      do
         ${TEST_HOME}/perf/stability/teststa -v -m select -n aaa_account_scheme -b 0 -e $end_id
         echo $labelName
         ${TEST_HOME}/perf/stability/teststa -v -m replace -n $labelName -b 0 -e $end_id &
         ${TEST_HOME}/perf/stability/teststa -v -m merge -n $labelName -b 0 -e $end_id &
         ${TEST_HOME}/perf/stability/teststa -v -m insert -n $labelName -b 0 -e $end_id &
      done
   done
}
for labelName in "aaa_account_scheme" "if" "ip4forward" "nhp_group"
do
   echo $labelName
   echo $labelName
   if [ X"$ENV_TYPE" == "Xharmony" ];then
      ${TEST_HOME}/perf/stability/teststa -v -p 1:$labelName & 
   else
      ${TEST_HOME}/perf/stability/teststa -v -p 10:$labelName & 
   fi
   ${TEST_HOME}/perf/stability/teststa -v -m replace -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -m merge -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -m update -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -m select -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -m delete -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -a -m replace -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -a -m update -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -a -m delete -n $labelName -b $start_id -e $end_id -c $thread_count &
   ${TEST_HOME}/perf/stability/teststa -v -a -m merge -n $labelName -b $start_id -e $end_id -c $thread_count &
   sleep 2
done
loop2=1
while [ ${loop2} -lt 1000 ]
do
    runProcessCount=`ps | grep "replace\|update\|delete" |grep -v grep |wc -l`
    echo "i $i runProcessCount $runProcessCount"
    if [ ${runProcessCount} -lt 1 ];then
        let loop2+=1000
    fi
    sleep 10
done
pkill teststa
sleep 5

schema1=aaa_account_scheme
schema2=if
schema3=if_statistics
schema4=ip4forward
schema5=nhp_group
${TEST_HOME}/perf/stability/teststa -D -n $schema1 
${TEST_HOME}/perf/stability/teststa -D -n $schema2
${TEST_HOME}/perf/stability/teststa -D -n $schema3 
${TEST_HOME}/perf/stability/teststa -D -n $schema4 
${TEST_HOME}/perf/stability/teststa -D -n $schema5 
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
