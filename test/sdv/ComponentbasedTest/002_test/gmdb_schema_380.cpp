/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <semaphore.h>
#include <sys/sem.h>
#include <sys/shm.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
// epoll模式下同步连接池+异步连接+推送连接最大值测试
int main(int argc, char **argv)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh -f");
    int chanRingLen = 64;
    GmcStmtT *stmt[10240];
    ret = testEnvInit();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = create_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcConnT *conn_sync[1024];
    GmcStmtT *stmt_sync[1024];
    for (int i = 0; i < 512; i++) {
        ret = testGmcConnect(&conn_sync[i], &stmt_sync[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcConnT *conn_async[1024];
    GmcStmtT *stmt_async[1024];
    for (int i = 0; i < 128; i++) {
        ret = testGmcConnect(&conn_async[i], &stmt_async[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    GmcConnT *conn_sub[1024];
    GmcStmtT *stmt_sub[1024];
    for (int i = 0; i < 384; i++) {
        char subConnName[50] = {0};
        sprintf(subConnName, "subConnName%d", i);
        ret = testSubConnect(&conn_sub[i], &stmt_sub[i], 1, g_epoll_reg_info, subConnName, &chanRingLen);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = testGmcConnect(&conn_sync[1023], &stmt_sync[1023]);
    EXPECT_EQ(10002, ret);
    for (int i = 0; i < 512; i++) {
        ret = testGmcDisconnect(conn_sync[i], stmt_sync[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int i = 0; i < 128; i++) {
        ret = testGmcDisconnect(conn_async[i], stmt_async[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int i = 0; i < 384; i++) {
        ret = testGmcDisconnect(conn_sub[i], stmt_sub[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = close_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testEnvClean();
    printf("[  PASSED  ] 1 test.\n");
    return 0;
}
