 #!/bin/sh
#定义函数，产生随机年月日
ran_ymd(){
num=$(($RANDOM%3651))
#定义变量ymd,赋值（格式yyyy-mm-dd）
ymd=`date -d "${num} days ago" +%Y-%m-%d`
echo ${ymd}
}

#产生指定范围的小时
ran_hour(){
	x=`echo {00..23}`
	array=($x)
	num=$(($RANDOM%24))
	hour=`echo ${array[${num}]}`
	echo ${hour}
}
#产生指定范围的分钟
ran_min(){
	x=`echo {00..59}`
	array=($x)
	num=$(($RANDOM%60))
	min=`echo ${array[${num}]}`
	echo ${min}
}
#产生指定范围的秒
ran_sec(){
	x=`echo {00..59}`
	array=($x)
	num=$(($RANDOM%60))
	sec=`echo ${array[${num}]}`
	echo ${sec}
}

#产生指定的时间，格式 YYYY-MM-DD HH:MM:SS
ran_time(){
	a=`ran_ymd`
	b=`ran_hour`
	c=`ran_min`
	d=`ran_sec`
#开始拼接
    Ttime=`echo "${b}:${c}:${d}"`
	time1=`echo "${a} ${b}:${c}:${d}"`
	echo ${Ttime}
}

i=0
while [ $i -lt $1 ]
do
    result=`ran_time`
	#echo ${result}
	date -s ${result}
	let i+=1
done
