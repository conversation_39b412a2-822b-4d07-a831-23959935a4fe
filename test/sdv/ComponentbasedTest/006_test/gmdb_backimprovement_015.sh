#!/bin/bash
# ----------------------------------------------------------------------------
# Description:
# Author: yang<PERSON>wen ywx1060383
# Create: 2022-05-25
# History: 
# Note: 
# ----------------------------------------------------------------------------
#测试步骤：
#1.gmsysview连接时敲错server_locator名字，观察是否有断言，错误码是否正确；
#预期结果：
#1.不应该有assert，错误码不应该是14001，而是17003；
# ----------------------------------------------------------------------------

ENV_TYPE=$1
tEnv=$(uname -a|grep <PERSON>|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="rtosv2x"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ -z $ENV_TYPE ] || [ "x$ENV_TYPE" = "xeuler" ]; then
    envType=0
    runMode=0
    environ="RTOS"
    server_locator="usocket:/run/verona/unix_emserver"
elif [ "x$ENV_TYPE" = "xrtosv2" ]; then
    envType=1
    runMode=1
elif [ "x$ENV_TYPE" = "xrtosv2x" ]; then
    envType=2
    runMode=1
else
    echo "usage: $0 <euler/rtosv2/rtosv2x>"
    exit 1
fi

function clean_process()
{
        for processPid in `ps -aux|grep './tool/'|grep -v grep|awk '{print $2}'`
	do
		kill -9 $processPid
	done
}
function check_gmserverId()
{
    gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
    hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
    if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
       echo "----------gmserver is exit!----------"
       kill -9 `pidof teststa` >/dev/null
       exit 1
    fi
}
kill -9 `pidof teststa` >/dev/null
clean_process
start.sh -f

usr="user123"
pwd="password.123"
teststa="${TEST_HOME}/perf/stability/teststa"

#testcaseName: gmdb_backimprovement_015
loop=0
while [ $loop -le 10 ]
do
    gmsysview  -q V\$DRT_CONN_STAT
    let loop+=1
done

clean_process
check_gmserverId
kill -9 `pidof teststa` >/dev/null
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
