cmake_minimum_required(VERSION 3.14.1)

project(testcase)

set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

file(GLOB fileList RELATIVE "${CMAKE_CURRENT_SOURCE_DIR}" "*.cpp")
foreach(sinFile IN LISTS fileList)
    get_filename_component(mainName ${sinFile} NAME_WE)
    add_executable(${mainName} ${sinFile})

endforeach()

list(APPEND compile_list gmdb_backimprovement_073)
list(APPEND compile_list gmdb_selfimprovement_032)
verify_and_add_directory(${compile_list})
