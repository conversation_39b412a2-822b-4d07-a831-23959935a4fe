#!/bin/bash
# ----------------------------------------------------------------------------
# Description:  HRP的vrrp_entry表在不支持的设备形态上不产生异常
# Author: ya<PERSON><PERSON>wen wx1060383
# Create: 2022-05-28
# History: 
# Note: 
# ----------------------------------------------------------------------------
#测试步骤：
# 1.在@docker 上所有线程{{multi_process}}*{{multi_threads}}并发同步写vrrp_entry表的最大记录数，有预期结果1；
# 2.在@docker 上再向vrrp_entry表增加写入1条记录，有预期结果2；
# 3.在@docker 上删除1条记录后，再写入1条的记录，有预期结果3；
# 4.在@docker 上删除掉所有的vrrp_entry表，有预期结果4；
# 5.在@docker 上按照1-4遍历所有vrrp_entry表；
#预期结果：
#1.可在数据库中查询到最大记录数，记录内容正确，且无错误返回码；
#2.无法再写入新的记录，返回正确的错误码{{error_code}}；
#3.写入成功，可以在数据库中查看到该条正确记录；
#4.删除成功，数据库中无记录；
# ----------------------------------------------------------------------------

ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="rtosv2x"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"

if [ -z $ENV_TYPE ] || [ "x$ENV_TYPE" = "xeuler" ]; then
    envType=0
    runMode=0
    environ="RTOS"
    server_locator="usocket:/run/verona/unix_emserver"
elif [ "x$ENV_TYPE" = "xrtosv2" ]; then
    envType=1
    runMode=1
elif [ "x$ENV_TYPE" = "xrtosv2x" ]; then
    envType=2
    runMode=1
else
    echo "usage: $0 <euler/rtosv2/rtosv2x>"
    exit 1
fi

function clean_process()
{
        for processPid in `ps -aux|grep './tool/'|grep -v grep|awk '{print $2}'`
	do
		kill -9 $processPid
	done
}
function check_gmserverId()
{
    gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
    hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
    if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
       echo "----------gmserver is exit!----------"
       kill -9 `pidof teststa` >/dev/null
       exit 1
    fi
}
kill -9 `pidof teststa` >/dev/null
clean_process
start.sh -f

usr="user123"
pwd="password.123"
teststa="${TEST_HOME}/perf/stability/teststa"
toolLog="log_tool"
runLog="log_teststa"
subLog="log_sub"

> ${runLog}
> ${toolLog}
> ${subLog}

kill -9 `pidof teststa` >/dev/null
clean_process
start.sh -f

if [ $envType -eq 0 ]; then
    threadCount=50
    processCount=5
    loopCount=10
    labelCount=10
    startId=0
    endId=10000
elif [ $envType -eq 1 ]; then
    exit 1
elif [ $envType -eq 2 ]; then
    echo "[  PASSED  ] 1 test."
    exit 1
else
    exit 1
fi
#testcaseName: gmdb_selfimprovement_018
function testcase_TESTF
{
    labelName=$1
    labelNameTool="${labelName}00000"

    if [ $envType -eq 0 ]; then
        sh ./tool/createVertex.sh ${labelName}
    elif [ $envType -eq 1 ]; then
        sh ./tool/createVertexIot.sh ${labelName}
    elif [ $envType -eq 2 ]; then
        sh ./tool/createVertexIot.sh ${labelName}
    else
        exit 1
    fi
}

start_id=0
end_id=4096
labelName="vrrp_entry"
testcase_TESTF ${labelName}
${TEST_HOME}/perf/stability/teststa -v -m replace -n $labelName -b $start_id -e $end_id -c ${threadCount}
${TEST_HOME}/perf/stability/teststa -v -m replace -n $labelName -b $start_id -e $end_id -c ${threadCount}
${TEST_HOME}/perf/stability/teststa -v -m replace -n $labelName -b 4096 -e 4097
${TEST_HOME}/perf/stability/teststa -v -m delete -n $labelName -b 4095 -e 4096
${TEST_HOME}/perf/stability/teststa -v -m replace -n $labelName -b 4096 -e 4097

${TEST_HOME}/perf/stability/teststa -D -n $labelName 

clean_process
check_gmserverId
kill -9 `pidof teststa` >/dev/null
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
