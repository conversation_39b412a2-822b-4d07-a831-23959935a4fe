function parse_arguments() {
    if [ "$1" = "clean" ];then
        is_clean_cmd="1"
        return 0
    fi
    while getopts "r:" opt
    do
        case "$opt" in
            r) repo_path="$OPTARG";;
            ?) usage; exit 1;;
        esac
    done
}

function check_arguments()
{
    if [[ -z "$repo_path" ]];then
        echo "Error: Please input parameter '-r'."
        exit 1
    fi
}

function decide_repo(){
    rm -rf /etc/yum.repos.d/*
    [ -f "$repo_path" ] && { echo "Setup local repo file $repo_path"; cp -f "$repo_path" /etc/yum.repos.d/; }
    [ -d "$repo_path" ] && { echo "Setup local repo file path $repo_path"; find "$repo_path" -name *.repo -exec cp -f {} /etc/yum.repos.d/ \;; }
    [ -e "$repo_path" ] || { echo "Setup remote repo file $repo_path"; wget -P /etc/yum.repos.d/ "$repo_path"; }
}

function download_all_deps()
{
    if [ ! -d "$DEPS_DIR" ];then
        local deps_last_dir=$(dirname "$DEPS_DIR")
        mkdir -p "$deps_last_dir"
        echo "###################### build.spec"
        pnftool build-dep -a aarch64 -s $GMDB_PATH/scripts/build.spec -d true -p "$deps_last_dir"
    else
        echo "Using exist deps dir $DEPS_DIR"
    fi
}


function clean()
{
    rm -rf .output
    rm -rf ./testcases/pnf_perf_test/gmdb_benchmark/output
    rm -rf ./testcases/pnf_perf_test/gmdb_benchmark/static_api/schema_file/src

}
