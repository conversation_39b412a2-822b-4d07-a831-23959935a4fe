#ifndef _TOOLS_H
#define _TOOLS_H
#define CMD_RESULT_BUF_LEN 1024
#include "common.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <malloc.h>
#include <string.h>
#include <ctype.h>

#ifdef __cplusplus
extern "C" {
#endif


#define SUCCESS 0
#define FAILED 1
#define DMDB_DEFAULT_FLAGS 0x0

char read_file[100] = {0};
char sn_file[100] = {0};
int  read_write_flag = 0;//0:not write 1:write  added
int  sn_write_flag = 0;//0:not write 1:write  added by l<PERSON><PERSON><PERSON>

/*****************************************************************************
* Description  : compare objects
* *****************************************************************************/
int32_t objects_compare(object_t src_obj, object_t dst_obj, int32_t result)
{
	char * src_txt = 0;
	char * dst_txt = 0;
	status_t ret;
	while (result)
	{
		ret = dmdb_create_object_text(src_obj, DMDB_DEFAULT_FLAGS, &src_txt);
		if (ret != SUCCESS)
		{
			break;
		}

		ret = dmdb_create_object_text(dst_obj, DMDB_DEFAULT_FLAGS, &dst_txt);
		if (ret != SUCCESS)
		{
			break;
		}
		result = strcmp(src_txt, dst_txt);
		if (result==0)
		{
		 break;
		}
		else
		{
		printf("object not match!!!\n");
		}	
	}
		

	if (src_txt != 0)
	{
		ret = dmdb_release_object_text(src_txt);
		if (ret != SUCCESS)
		{
			exit(1);
		}
	}

	if (dst_txt != 0)
	{
		ret = dmdb_release_object_text(dst_txt);
		if (ret != SUCCESS)
		{
			exit(1);
		}
	}

	return SUCCESS;
}

int print_log(char * file_name, char * print_obj)
    {
                FILE *log_file;
                char file_name_new[100] = { 0 };

                sprintf(file_name_new, "./%s", file_name);
                log_file = fopen(file_name_new, "a");
                fprintf(log_file, "%s\n", print_obj);
                fflush(log_file);
                fclose(log_file);

                return 0;
    }
/************************************************************************/
/* print object text                                                    */
/************************************************************************/
status_t print_obj(object_t object)
{
	char * object_txt = 0;
	status_t ret;
	ret = dmdb_create_object_text(object, DMDB_DEFAULT_FLAGS, &object_txt);
	if (SUCCESS != ret)
	{
		printf("[Client]: Create object text failed. status: %d\n", ret);
		return ret;
	}
	if (ret == SUCCESS && object_txt != NULL)
	{
		//printf("[Client]: Object String:\n%s\n", object_txt);
		if (read_write_flag == 1)
		{
			print_log(read_file, object_txt);
		        //printf(object_txt);
		}
		ret = dmdb_release_object_text(object_txt);
		if (ret != SUCCESS)
		{
			printf("[Client]: Release object text failed. status: %d\n", ret);
			return ret;
		}
	}
	return ret;
	
}

/************************************************************************/
/* print sn_object text                                                    */
/************************************************************************/

status_t print_sn_obj(object_t object)
    {
        char * object_txt = 0;
        status_t ret;
        ret = dmdb_create_object_text(object, DMDB_DEFAULT_FLAGS, &object_txt);
        if (SUCCESS != ret)
        {
            printf("[Client]: Create object text failed. status: %d\n", ret);
            return ret;
        }
        if (ret == SUCCESS && object_txt != NULL)
        {
            //printf("[Client]: Object String:\n%s\n", object_txt);
	    if (sn_write_flag == 1)
            {
            print_log(sn_file, object_txt);
	    //printf("\n",object_txt);
	    }
            ret = dmdb_release_object_text(object_txt);
            if (ret != SUCCESS)
            {
                printf("[Client]: Release object text failed. status: %d\n", ret);
                return ret;
            }
        }
        return ret;
    }

/*
* get file size
*/
long fileSize(char const* path)
{
	FILE* fp;
	long   size;
	int rc;

	/* Open the file */
	fp = fopen(path, "rb");

	if (NULL == fp)
	{
		return -1L;
	}

	/* Seek to the end of the file */
	rc = fseek(fp, 0L, SEEK_END);

	if (0 != rc)
	{
		fclose(fp);
		return -1L;
	}

	/* Byte offset to the end of the file (size) */
	if (0 > (size = ftell(fp)))
	{
		fclose(fp);
		return -1L;
	}

	/* Close the file */
	if (EOF == fclose(fp))
	{
		return -1L;
	}

	return size;
}


/*
run linux commands
*/
int runLinuxCommand(char* cmd, char* cmd_prompt)
{
	pid_t ret = 0;
	ret = system(cmd);
	if (WEXITSTATUS(ret) == 0) {
		printf("%s success.\n", cmd_prompt);
		return 0;
	}
	else {
		printf("%s failed.\n", cmd_prompt);
		return 1;
	}
}

//start GMDB cluster
int startCluster()
{
	char *home_path;
	char cmd[256];
	home_path = getenv("GMDB_HOME");
	snprintf(cmd, 1024, "%s/bin/gmserver %s/tools/config/gmserver-x86.ini -b &", home_path, home_path);
	int ret = runLinuxCommand(cmd, "Start Server");
	return ret;
	sleep(1);
}

//stop GMDB Cluster
int stopCluster()
{
	char cmd[1024];
	char *home_path;
	home_path = getenv("GMDB_HOME");
	snprintf(cmd, 1024, "ps -ef | grep gmserver-x86.ini | grep -v grep | cut -c 9-15 | xargs kill -s 9");
	int ret = runLinuxCommand(cmd, "kill Server");
	return ret;
	sleep(1);
}

//restart GMDB cluster
int restartCluster()
{
	int ret = stopCluster();
	if (ret != 0)
	{
		return ret;
	}
	ret = startCluster();
	return ret;
}


	
#ifdef __cplusplus
}
#endif

#endif
