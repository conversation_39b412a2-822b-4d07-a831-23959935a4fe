include ${TEST_HOME}/test_include/common.in

PRJ_NAME=cpu_load

PRJ_INC_DIR_FLAG=$(INC_DIR_FLAGS)
PRJ_LINK_FLAG=$(EXE_LINK_FLAGS)
PRJ_LIB_DIR_FLAG=$(LIB_DIR_FLAGS)

C_TEST_LIBS=$(BASE_LIBS)

C_TEST_SRCS=$(wildcard *.c)
C_TEST_OBJS=$(patsubst %.c, %.o, $(C_TEST_SRCS))

PRJ_OBJS=$(C_TEST_OBJS)
PRJ_TGT=$(PRJ_NAME)

default:$(PRJ_TGT)

$(PRJ_TGT):$(PRJ_OBJS)
	@$(HIDE) $(EXE_LINKER) $(PRJ_LINK_FLAG) -o $@ $^ $(PRJ_LIB_DIR_FLAG) $(C_TEST_LIBS) -O0 -g

$(C_TEST_OBJS):%.o:./%.c
	@$(PRINT) "Compiling file "$<
	@$(HIDE) $(COMPILER) $(PRJ_CFLAG) $(PRJ_INC_DIR_FLAG) -o $@ -c $< -O0 -g

clean:
	$(RM) $(GMDB_OBJ_DIR)/$(PRJ_NAME)/* $(PRJ_TGT)
