
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "localize_func.h"
#include "local_schema_model.h"
#include "tools.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"

status_t local_prepare_connection(local_config_t *conf)
{
    status_t ret = STATUS_OK;
    db_connection_pool_config pool_config;

    db_set_current_server_locator(conf->server_locator);
    db_set_current_user_name("osc");

    /* apply for connection pool */
    pool_config.min_num = 12;
    pool_config.max_num = 50;
    pool_config.is_async_check = false;

    ret = db_create_connection_pool(conf->server_locator, "osc", SYNC_MODE, &pool_config, NULL);

    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "create connection pool");
        return ret;
    } /* apply for connection pool done */
    return ret;
}

status_t local_prepare_connection_new(process_conf_t *conf)
{
    status_t ret = STATUS_OK;
    db_connection_pool_config pool_config;
    char *com_mod[3] = { PERF_USOCKET_MODE, PERF_TCP_MODE, PERF_SHM_MODE };
    if (conf->comm_mode >= COMM_MODE_MAX || conf->username == NULL) {
        return -1;
    }

    db_set_current_server_locator(com_mod[conf->comm_mode]);
    db_set_current_user_name(conf->username);

    /* apply for connection pool */
    sync_conf_t *sync_conf = conf->sync_conf[0];
    pool_config.min_num = sync_conf->min;
    pool_config.max_num = sync_conf->max;
    pool_config.is_async_check = sync_conf->is_async_check;

    ret = db_create_connection_pool(com_mod[conf->comm_mode], conf->username, SYNC_MODE, &pool_config, NULL);

    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "create connection pool");
        return ret;
    } /* apply for connection pool done */
    return ret;
}

status_t table_write_data(uint32_t table_index)
{
    p_local_db_schema_model_entry_t schema_model = NULL;
    p_local_db_schema_model_t model_obj = NULL;

    if (table_index >= LOCAL_DB_IDX_INVALID) {
        LOG_ERROR(-1, "table_index is wrong, index:%u", table_index);
        return -1;
    }

    schema_model = local_get_schema_model_entry(table_index);
    if (schema_model == NULL) {
        LOG_ERROR(-1, "set db object fail, index:%u", table_index);
        return -1;
    }
    model_obj = schema_model->model;
    if (model_obj == NULL) {
        LOG_ERROR(-1, "index:%u, schema %s mode null", table_index, schema_model->schema_json_file_name);
        return -1;
    }
    TESET_LOG_INFO("index:%u, write schema %s, from:%u to:%u", 
        table_index, schema_model->schema_json_file_name, (uint32_t)(model_obj->low), (uint32_t)(model_obj->high));

    status_t ret = STATUS_OK;
    uint32_t i = model_obj->low;

    db_object object = NULL;

    ret = model_obj->obj_create_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);  // 0 sync
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "obj_create_func fail");
        return ret;
    }

    while (1) {
        ret = model_obj->obj_set_func(object, i, 1, 0);  // thr_num
        if (ret) {
            LOG_ERROR(ret, "set db object fail");
            break;
        }

        ret = db_write_obj(object);
        if (ret) {
            LOG_ERROR(ret, "write db object fail, i:%u, schema:%s", i, schema_model->schema_json_file_name);
            break;
        }

        model_obj->obj_reset_func(object, WRITE_OBJ);
        i++;
        if (i > model_obj->high) {
            break;
        }
    }

    model_obj->obj_release_func(object);

    return ret;
}

status_t local_prepare_table_data_new()
{
    status_t ret = STATUS_OK;
    unsigned int loop;
    p_local_db_schema_model_t model;

    TEST_INFO("----------------we begin to prepare table data");
    for (loop = 0; loop < LOCAL_DB_IDX_INVALID; loop++) {
        model = local_db_get_schema_model_by_index(loop);
        if (model == NULL || model->is_use == 0 || model->need_prepare_data == 0) {
            continue;
        }

        ret = table_write_data(loop);
        if (ret != STATUS_OK) {
            TEST_ERROR("table_write_data %d ret:%d", loop, ret);
            return ret;
        }
    }
    TEST_INFO("----------------prepare table data success");
    return 0;
}

status_t local_prepare_table_new()
{
    status_t ret = STATUS_OK;
    char *schema_json;
    unsigned int loop;
    char schema_file_path[1024] = { 0 };
    p_local_db_schema_model_entry_t schema_model;
    p_local_db_schema_model_t model;
    char schema_json_file[100] = { 0 };
    char sz_ht_idx_size[1024] = { 0 };

    TEST_INFO("----------------we begin to prepare table data");
    for (loop = 0; loop < LOCAL_DB_IDX_INVALID; loop++) {
        schema_json = NULL;

        schema_model = local_get_schema_model_entry(loop);
        if (schema_model == NULL || schema_model->schema_json_file_name == NULL) {
            continue;
        }
        model = schema_model->model;
        if (model == NULL || model->is_use == 0) {
            continue;
        }
        snprintf(schema_json_file, 100, schema_model->schema_json_file_name);
        strcat(schema_json_file, ".gmjson");
        snprintf(schema_file_path, 1024, "%s/%s", GMJSON_FILE_PATH_SPEC_LOCAL, schema_json_file);
        com_read_file_content(schema_file_path, &schema_json, true);
        if (schema_json == NULL) {
            TEST_ERROR("read schema file failed %s ret:%d", schema_file_path, -1);
            return ret;
        }
        // printf("schema:%s\n", schema_json);

        if (model->data_ht > 0) {
            snprintf(sz_ht_idx_size, 1024,
                     "{\"ht_idx_size\":%u, \"del_obj_delay\":false}",
                     model->data_ht);
            printf("%s  tab_config:%s\n", schema_model->schema_json_file_name, (char *)sz_ht_idx_size);
        } else {
            snprintf(sz_ht_idx_size, 1024,
                     "{\"del_obj_delay\":false}");
            printf("%s  tab_config:%s\n", schema_model->schema_json_file_name, (char *)sz_ht_idx_size);
        }
        ret = db_create_table(schema_model->schema_json_file_name, schema_json, (char *)sz_ht_idx_size);
        if (ret != STATUS_OK) {
            TEST_ERROR("db_create_table %s ret:%d", schema_model->schema_json_file_name, ret);
            free(schema_json);
            return ret;
        }
        ret = local_db_int_res(loop);
        if (ret != STATUS_OK) {
            TEST_ERROR("local_db_int_res %s ret:%d", schema_model->schema_json_file_name, ret);
            free(schema_json);
            return ret;
        }
        free(schema_json);
    }
    TEST_INFO("----------------prepare table data success");
    return 0;
}

status_t local_truncate_table_data_new(uint32_t table_index)
{
    status_t ret = STATUS_OK;
    char *table_name = local_db_get_schema_name_by_index(table_index);
    if (table_name == NULL) {
        return -1;
    }
    ret = _db_truncate_table_foreground(table_name);
    return ret;
}

status_t local_read_primary(void *model, uint64_t index)
{
    status_t ret = STATUS_OK;
    db_object object;
    p_local_db_schema_model_t model_obj = (p_local_db_schema_model_t)model;
    ret = model_obj->obj_create_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    TEST_ASSERT_EQ(ret, STATUS_OK);
    ret = model_obj->getfield_bykey_func(object, index, 0);
    if (ret) {
        TEST_INFO("out of key_set_func ret=%d, index=%lu", ret, index);
    }
    model_obj->obj_release_func(object);
    return ret;
}

status_t local_batch_write(local_config_t *conf, uint32_t table_index)
{
    status_t ret = STATUS_OK;
    const int wr_loop = 60;  // should be config
    db_object object;
    uint32_t i = 0, j = 0;
    uint64_t start_time = 0, end_time = 0, cost_time = 0;
    uint64_t operation_count = 0;
    p_local_db_schema_model_t model_obj = NULL;
    uint32_t total_num, success_num;

    double ops = 0.0;

    if (table_index >= LOCAL_DB_IDX_INVALID) {
        LOG_ERROR(ret, "table_index is wrong");
        return -1;
    }

    model_obj = local_db_get_schema_model_by_index(table_index);
    if (model_obj == NULL) {
        LOG_ERROR(ret, "model_obj is NULL");
        return -1;
    }

    start_time = com_gettimeofday_usec();
    ret = model_obj->obj_create_func(BATCH_OPERATION, LOCAL_SYNC_FLAG, &object);
    TEST_ASSERT_EQ(ret, STATUS_OK);

    while (1) {
        // j=0;
        while (1) {
            j++;

            ret = model_obj->bobj_reset_func(object, WRITE_OBJ);
            TEST_ASSERT_EQ(ret, STATUS_OK);
            ret = model_obj->obj_set_func(object, i++, 1, 0);
            if (ret) {
                LOG_ERROR(ret, "obj_set_func");
                break;
            }

            ret = db_batch_add_oper(object, NULL, NULL);
            if (ret) {
                LOG_ERROR(ret, "db_batch_add_oper");
                j = 0;
                break;
            }

            if (j >= wr_loop || STATUS_BATCH_BUFFER_FULL == ret || i > conf->data_max) {
                j = 0;
                ret = db_batch_exec(object, &total_num, &success_num);
                if (ret && (ret != STATUS_SN_SUB_PUSH_QUEUE_FULL_DROP_MSG)) {  // sub queue full error. 13044
                    LOG_ERROR(ret, "db_batch_exec fail");
                    break;
                }
                ret = STATUS_OK;
                operation_count += success_num;

                break;
            }
        }

        if (ret) {
            break;
        }

        if (ret || i > conf->data_max) {
            CHECK_OK_RET(ret, "db_lite_reset_batch");
            break;
        }
    }

    model_obj->obj_release_func(object);
    end_time = com_gettimeofday_usec();

    TEST_INFO("#########end_time=%lu, start_time=%lu \n", end_time, start_time);
    TEST_INFO("#########end_time-start_time=%lu \n", end_time - start_time);

    cost_time = (end_time - start_time) / 1000;  // unit ms
    ops = (operation_count * 1.0) / cost_time * 1000;

    TEST_INFO("#########batch write operation_count=%lu, cost_time=%lu \n", operation_count, cost_time);
    TEST_INFO("#########batch write ops=%f", ops);
    return ret;
}

void local_destroy_all_res_pool()
{
    return;
}
