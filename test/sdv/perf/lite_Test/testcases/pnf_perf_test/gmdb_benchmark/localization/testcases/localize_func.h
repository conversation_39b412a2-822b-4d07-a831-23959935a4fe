#ifndef __LOCALIZATION_FUNC_H_
#define __LOCALIZATION_FUNC_H_

#include "localize_common.h"
#include "gm_errno.h"
#include "parse_conf.h"

status_t local_prepare_connection(local_config_t *conf);
status_t local_read_primary(void *, uint64_t);
status_t local_batch_write(local_config_t *conf, uint32_t table_index);
status_t local_prepare_connection_new(process_conf_t *conf);
status_t local_prepare_table_new();
status_t local_prepare_table_data_new();
status_t table_write_data(uint32_t);
status_t local_truncate_table_data_new(uint32_t table_index);

#endif




