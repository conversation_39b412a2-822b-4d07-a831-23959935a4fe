{"version": "2.0", "type": "record", "name": "mac_fdb", "config": {"check_validity": true}, "fields": [{"name": "vrid", "type": "uint32"}, {"name": "bd_type", "type": "uint8", "comment": "1:VLAN, 2:VSI, 3:bridgedomain"}, {"name": "bd_id", "type": "uint32"}, {"name": "mac_address", "type": "fixed", "size": 6}, {"name": "mac_service_type", "type": "uint8", "comment": "1:VLAN, 4:VSI, 6:VSI_VLANIF, 7:VSI_QinQ_Termination, 8:VSI_dot1qTermination, 13:global_blackhole, 15：bridge，16：VSI_PW"}, {"name": "mac_type", "type": "uint8", "comment": "1:dynamic, 2:static, 3:blackhole, 4:oam, 5:mux_vlan, 6:security, 7:stick, 8:mux_vlan_remote, 9:dhcp, 10:vm, 11:trill, 12:static_all, 13:evn, 14:802.1x, 15:nvo3vfi"}, {"name": "valid", "type": "boolean", "comment": "单板拔出，MAC对应的MAC表项失效"}, {"name": "if_index", "type": "uint32", "comment": "出接口索引"}, {"name": "vp_index", "type": "uint32", "comment": "VxLAN隧道"}, {"name": "out_vlanid", "type": "uint16"}, {"name": "ce_vlanid", "type": "uint16", "comment": "用户侧VLANid"}, {"name": "pe_vlanid", "type": "uint16"}, {"name": "lsp_token", "type": "uint32"}, {"name": "vc_label", "type": "uint32"}, {"name": "peer_ip", "type": "uint32"}, {"name": "vc_id", "type": "uint32"}, {"name": "learn_time", "type": "uint32"}, {"name": "app_source_id", "type": "uint32"}, {"name": "app_serial_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "mac_fdb_pk", "index": {"type": "primary"}, "node": "mac_fdb", "fields": ["vrid", "bd_type", "bd_id", "mac_address"], "constraints": {"unique": true}}, {"name": "k_if", "index": {"type": "local"}, "node": "mac_fdb", "fields": ["vrid", "if_index", "bd_type", "bd_id", "mac_address"], "comment": "按照出口查询，树遍历"}, {"name": "k_bd", "index": {"type": "local"}, "node": "mac_fdb", "fields": ["vrid", "bd_type", "bd_id", "mac_address"], "comment": "按照bd查询，树遍历"}, {"name": "k_type_bd", "index": {"type": "local"}, "node": "mac_fdb", "fields": ["vrid", "mac_type", "bd_type", "bd_id", "mac_address"], "comment": "按照类型+bd查询，树遍历"}, {"name": "k_if_type_bd", "index": {"type": "local"}, "node": "mac_fdb", "fields": ["vrid", "bd_type", "bd_id", "mac_address", "mac_type", "if_index"], "comment": "按照if+类型+bd查询"}]}