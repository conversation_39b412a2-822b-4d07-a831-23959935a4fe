{"comment": "eedb表", "version": "2.0", "type": "record", "name": "eedb", "max_record_count": 500000, "fields": [{"name": "eepindex", "type": "uint32", "comment": "eep: eedb point"}, {"name": "target_blade", "type": "uint16", "comment": "target_blade"}, {"name": "is_trunk", "type": "uint8", "comment": "is_trunk"}, {"name": "eedb_type", "type": "uint32", "comment": "ARP ND/SFC/VARP等业务"}, {"name": "mac_addr", "type": "fixed", "size": 6, "comment": "封装DMAC"}, {"name": "unit_bit", "type": "uint8", "comment": "需要下发的芯片的bitmap"}, {"name": "out_vid_valid", "type": "uint8", "comment": "out_vid_valid"}, {"name": "out_vid", "type": "uint32", "comment": "OUTVSI"}, {"name": "ref_count", "type": "uint32", "comment": "引用计数"}, {"name": "user_type", "type": "uint8", "comment": "user_type"}, {"name": "ipormac_flag", "type": "uint8", "comment": "ipormac_flag"}, {"name": "ipormac_info", "type": "fixed", "size": 8, "comment": "ipormac_info"}], "keys": [{"name": "eedb_pk", "index": {"type": "primary"}, "node": "eedb", "fields": ["eepindex", "target_blade", "is_trunk"], "constraints": {"unique": true}}, {"name": "eedb_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "eedb", "fields": ["ipormac_flag", "ipormac_info"], "comment": "eedb_index"}]}