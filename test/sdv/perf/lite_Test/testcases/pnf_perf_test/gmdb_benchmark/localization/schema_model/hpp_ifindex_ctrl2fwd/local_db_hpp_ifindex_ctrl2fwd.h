#ifndef __LOCAL_DB_HPP_IFINDEX_CTRL2FWD_H__
#define __LOCAL_DB_HPP_IFINDEX_CTRL2FWD_H__

#include "gm_errno.h"
#include "type.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"

#define HPP_IFINDEX_CTRL2FWD_RES_POOL_NAME "hpp_ifindex_ctrl2fwd_res_pool"
status_t local_db_hpp_ifindex_ctrl2fwd_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                         db_object *object);
void local_db_hpp_ifindex_ctrl2fwd_release_db_obj_func(db_object object);
status_t local_db_hpp_ifindex_ctrl2fwd_reset_db_obj_func(db_object object, db_object_type obj_type);
status_t local_db_hpp_ifindex_ctrl2fwd_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num);
status_t local_db_hpp_ifindex_ctrl2fwd_create_db_batch_obj_func(db_conn_type, db_object *);
status_t local_db_hpp_ifindex_ctrl2fwd_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void *key_info, void *key_data);
status_t local_db_hpp_ifindex_ctrl2fwd_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num);
status_t local_db_hpp_ifindex_ctrl2fwd_reset_db_bobj_func(db_object object, db_object_type obj_type);
status_t local_db_hpp_ifindex_ctrl2fwd_register_shcema_model(void *schema_model);
uint32_t local_db_hpp_ifindex_ctrl2fwd_init_res();
#endif
