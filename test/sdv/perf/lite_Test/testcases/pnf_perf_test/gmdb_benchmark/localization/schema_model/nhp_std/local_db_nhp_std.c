
#include "local_db_nhp_std.h"
#include "local_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "db_nhp_std.h"
#include "cpu_cycles.h"
p_local_db_schema_model_t g_nhp_std_model = NULL;

status_t local_db_nhp_std_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                             db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    DB_START_TEST_CPU_CYCLES(CreateObj);
    ret = db_create_nhp_std_obj(obj_type, conn_type, &obj);
    DB_STOP_TEST_CPU_CYCLES(CreateObj);
    *object = obj;
    return ret;
}

void local_db_nhp_std_release_db_obj_func(db_object object)
{
    DB_START_TEST_CPU_CYCLES(Release);
    db_release_nhp_std_object(object);
    DB_STOP_TEST_CPU_CYCLES(Release);
}

status_t local_db_nhp_std_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(ResetObj);
    ret = db_reset_nhp_std_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(ResetObj);

    return ret;
}

status_t local_db_nhp_std_create_db_batch_obj_func(db_conn_type type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    DB_START_TEST_CPU_CYCLES(CreateObj);
    ret = db_create_nhp_std_obj(BATCH_OPERATION, type, &obj);
    DB_STOP_TEST_CPU_CYCLES(CreateObj);
    *object = obj;
    return ret;
}

status_t local_db_nhp_std_reset_db_bobj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;
    DB_START_TEST_CPU_CYCLES(ResetObj);
    ret = db_reset_nhp_std_batch_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(ResetObj);

    return ret;
}

status_t local_db_nhp_std_primary_key_malloc_func(void **key, uint32_t *length)
{
    *length = sizeof(db_nhp_std_key_t);
    *key = malloc(sizeof(db_nhp_std_key_t));
    return 0;
}

void local_db_nhp_std_primary_key_free_func(void *key)
{
    free(key);
}

status_t local_db_nhp_std_struct_data_malloc_func(void **data, uint32_t *length)
{
    *length = sizeof(nhp_std_struct_t);
    *data = malloc(*length);
    return 0;
}

void local_db_nhp_std_struct_data_free_func(void *data)
{
    free(data);
}

status_t local_db_nhp_std_get_field_func(db_object object, uint32_t get_field_cnt)
{
    status_t ret = STATUS_OK;
    int8_t *svcCtx = NULL;
    uint32_t nhpIndex;
    uint32_t nextHop;
    uint32_t outIfIndex;
    uint32_t vrId;
    uint32_t vrfIndex;
    uint32_t flags;
    uint16_t fwdIfType;
    uint32_t ifType;
    uint8_t status[2];
    uint8_t errcode[2];
    uint32_t svc_ctx[2][4];
    uint32_t appSourceId;
    uint32_t groupSmoothID;
    uint64_t appObjId;
    uint32_t appVersion;
    (void)db_get_nhp_std_nhp_index(object, &nhpIndex);
    (void)db_get_nhp_std_next_hop(object, &nextHop);
    (void)db_get_nhp_std_out_if_index(object, &outIfIndex);
    (void)db_get_nhp_std_vr_id(object, &vrId);
    (void)db_get_nhp_std_vrf_index(object, &vrfIndex);
    (void)db_get_nhp_std_flags(object, &flags);
    (void)db_get_nhp_std_fwd_if_type(object, &fwdIfType);

    (void)db_get_nhp_std_status_high_prio(object, &status[0]);
    (void)db_get_nhp_std_status_normal_prio(object, &status[1]);
    (void)db_get_nhp_std_errcode_high_prio(object, &errcode[0]);
    (void)db_get_nhp_std_errcode_normal_prio(object, &errcode[1]);

    (void)db_get_nhp_std_svc_ctx_high_prio(object, &svcCtx, NULL);
    (void)memcpy_s(svc_ctx[0], sizeof(uint32_t) * 4, svcCtx,
                   sizeof(uint32_t) * 4);

    (void)db_get_nhp_std_svc_ctx_normal_prio(object, &svcCtx, NULL);
    (void)memcpy_s(svc_ctx[1], sizeof(uint32_t) * 4, svcCtx,
                   sizeof(uint32_t) * 4);

    (void)db_get_nhp_std_app_source_id(object, &appSourceId);
    (void)db_get_nhp_std_group_smooth_id(object, &groupSmoothID);
    (void)db_get_nhp_std_app_obj_id(object, &appObjId);
    (void)db_get_nhp_std_app_version(object, &appVersion);
    (void)db_get_nhp_std_ifType(object, &ifType);
    return ret;
}

status_t local_db_nhp_std_set_field_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    uint64_t conflict_value = uiVrIndex % (g_nhp_std_model->conflict_size);
    /* д���� */
    ret = db_set_nhp_std_nhp_index(object, (conflict_value & ((uint32_t)~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_next_hop(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_out_if_index(object, (conflict_value & ((uint32_t)~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_vr_id(object, 0);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_vrf_index(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_flags(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_fwd_if_type(object, uiVrIndex);

    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_app_source_id(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_group_smooth_id(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_app_obj_id(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_app_version(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    ret = db_set_nhp_std_ifType(object, (conflict_value & ((uint32_t)~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_arp_vsi_index");
    return ret;
}

status_t local_db_nhp_std_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
#ifdef NORMAL_INTF
    ret = local_db_nhp_std_set_field_func(object, uiVrIndex, array, thread_local_num);
#else
    uint64_t conflict_value = uiVrIndex % (g_nhp_std_model->conflict_size);
    nhp_std_struct_t obj_struct;
    memset(&obj_struct, 0, sizeof(obj_struct));
    
    obj_struct.nhp_index = (conflict_value & ((uint32_t)~0));
    obj_struct.next_hop = uiVrIndex;
    obj_struct.out_if_index = (conflict_value & ((uint32_t)~0));
    obj_struct.vr_id = 0;
    obj_struct.vrf_index = uiVrIndex;
    obj_struct.flags = uiVrIndex;
    obj_struct.fwd_if_type = uiVrIndex;

    obj_struct.app_source_id = uiVrIndex;
    obj_struct.group_smooth_id = uiVrIndex;
    obj_struct.app_obj_id = uiVrIndex;
    obj_struct.app_version = uiVrIndex;
    obj_struct.ifType = (conflict_value & ((uint32_t)~0));

set_again:
    ret = db_set_nhp_std_all_fields(object, &obj_struct, sizeof(obj_struct));
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }
#endif
    return ret;
}

status_t local_db_nhp_std_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    uint64_t conflict_value = uiVrIndex % (g_nhp_std_model->conflict_size);
    db_key_info_t key_info = {0};
    db_nhp_std_key_t key_data = {0};
    key_info.index_id = DB_NHP_STD_PRIMARY_KEY_ID;
    key_info.key_len = sizeof(key_data);
    key_data.nhp_index = (conflict_value & ((uint32_t)~0));
    key_data.next_hop = uiVrIndex;
    key_data.out_if_index = (conflict_value & ((uint32_t)~0));
    DB_START_TEST_CPU_CYCLES(Read);
    ret = db_read_obj(object, &key_info, &key_data);
    DB_STOP_TEST_CPU_CYCLES(Read);
    if (ret != STATUS_OK) {
        return ret;
    }
    nhp_std_struct_t obj_struct;
    DB_START_TEST_CPU_CYCLES(GetAllField);
    ret = db_get_nhp_std_all_fields(object, &obj_struct, sizeof(nhp_std_struct_t));
    DB_STOP_TEST_CPU_CYCLES(GetAllField);
    return ret;
}

status_t local_db_nhp_std_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void *key_info, void *key_data)
{
    uint64_t conflict_value = uiVrIndex % (g_nhp_std_model->conflict_size);
    db_nhp_std_key_t *pri_key_data = (db_nhp_std_key_t *)key_data;
    db_key_info_t *pri_key_info = (db_key_info_t *)key_info;
    memset(pri_key_data, 0, sizeof(db_nhp_std_key_t));
    memset(pri_key_info, 0, sizeof(db_key_info_t));
    pri_key_info->index_id = DB_NHP_STD_PRIMARY_KEY_ID;
    pri_key_info->key_len = sizeof(db_nhp_std_key_t);
    pri_key_data->nhp_index = (conflict_value & ((uint32_t)~0));
    pri_key_data->next_hop = uiVrIndex;
    pri_key_data->out_if_index = (conflict_value & ((uint32_t)~0));
    return 0;
}

status_t local_db_nhp_std_register_shcema_model(void *schema_model)
{
    if (g_nhp_std_model == NULL) {
        g_nhp_std_model = (p_local_db_schema_model_t)(schema_model);
        g_nhp_std_model->obj_create_func = local_db_nhp_std_create_db_obj_func;
        g_nhp_std_model->obj_release_func = local_db_nhp_std_release_db_obj_func;
        g_nhp_std_model->obj_reset_func = local_db_nhp_std_reset_db_obj_func;
        g_nhp_std_model->bobj_reset_func = local_db_nhp_std_reset_db_bobj_func;
        g_nhp_std_model->bobj_create_func = local_db_nhp_std_create_db_batch_obj_func;
        g_nhp_std_model->obj_set_func = local_db_nhp_std_set_obj_func;
        g_nhp_std_model->malloc_primary_key_func = local_db_nhp_std_primary_key_malloc_func;
        g_nhp_std_model->free_primary_key_func = local_db_nhp_std_primary_key_free_func;
        g_nhp_std_model->malloc_struct_data_func = local_db_nhp_std_struct_data_malloc_func;
        g_nhp_std_model->free_struct_data_func = local_db_nhp_std_struct_data_free_func;
        g_nhp_std_model->getfield_bykey_func = local_db_nhp_std_get_field_by_key_func;
        g_nhp_std_model->key_set_func = local_db_nhp_std_key_set_func;
        g_nhp_std_model->getfield_func = local_db_nhp_std_get_field_func;
    }
    return 0;
}
