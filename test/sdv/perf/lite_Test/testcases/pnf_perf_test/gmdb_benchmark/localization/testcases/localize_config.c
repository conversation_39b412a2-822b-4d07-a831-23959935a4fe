#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "localize_config.h"

p_local_db_schema_model_t g_model_list[LOCAL_DB_IDX_INVALID];
uint64_t g_model_low[LOCAL_DB_IDX_INVALID];
uint64_t g_model_high[LOCAL_DB_IDX_INVALID];

void local_config_set_model()
{
    unsigned int i;
    for (i = 0; i < LOCAL_DB_IDX_INVALID; i++) {
        g_model_list[i] = local_db_get_schema_model_by_index(i);
        g_model_low[i] = (g_model_list[i])->low;
        g_model_high[i] = (g_model_list[i])->high;
    }
}

int local_config_set_table_low_high(const char *file_name)
{
    FILE *fp;
    unsigned int i, j;
    char temp_name[50];
    unsigned int temp_low;
    unsigned int temp_high;
    unsigned int temp_conflict;
    unsigned int is_need_pre_data;
    p_local_db_schema_model_entry_t temp_model;
    if (file_name == NULL) {
        printf("filename NULL\n");
        return -1;
    }
    if ((fp = fopen(file_name, "r")) == NULL) {
        printf("can not open file\n");
        return -1;
    }
    i = 0;
    while (fscanf(fp, "%s %u %u %u %u", temp_name, &temp_low, &temp_high, &temp_conflict, &is_need_pre_data) == 5) {
        for (j = 0; j < LOCAL_DB_IDX_INVALID; j++) {
            temp_model = NULL;
            temp_model = local_get_schema_model_entry(j);
            if ((temp_model != NULL) && (temp_model->schema_json_file_name != NULL) && (temp_model->model != NULL)) {
                if (strcmp(temp_name, temp_model->schema_json_file_name) == 0) {
                    (temp_model->model)->is_use = 1;
                    (temp_model->model)->low = temp_low;
                    (temp_model->model)->high = temp_high;
                    if (temp_conflict != 0) {
                        (temp_model->model)->conflict_size = temp_conflict;
                    }
                    
                    (temp_model->model)->need_prepare_data = is_need_pre_data;
                    i++;
                    break;
                }
            }
        }
        if (j >= LOCAL_DB_IDX_INVALID) {
            printf("table name unkown:%s\n", temp_name);
        }
    }
    fclose(fp);
    local_config_set_model();
    return 0;
}
