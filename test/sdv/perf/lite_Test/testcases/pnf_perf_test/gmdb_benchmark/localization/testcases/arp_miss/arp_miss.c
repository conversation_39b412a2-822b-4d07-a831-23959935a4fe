#include <unistd.h>
#include <stdio.h>
#include <string.h>

#include "securec.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"
#include "type.h"
#include "localize_common.h"
#include "localize_multi_thread.h"
#include "localize_func_async.h"
#include "localize_func.h"
#include "localize_config.h"
#include "tools.h"
#include "local_schema_model.h"
#include "local_db_arp_re.h"
#include "local_db_arp_nhp.h"
#include "local_db_arp_fake.h"
#include "db_arp.h"
#include "db_arp_re.h"
#include "db_arp_nhp.h"
#include "db_arp_fake.h"
#include "arp_test.h"
#include "arp_miss.h"

static void *arp_miss_thr_fun_01(void *);
static void *arp_miss_thr_fun_02(void *);

typedef enum tag_arp_miss_thread_idx {
    ARP_MISS_THREAD_INDEX_ONE = 0,
    ARP_MISS_THREAD_INDEX_TWO,
    ARP_MISS_THREAD_INDEX_INVALID
} arp_miss_thread_idx_e;

static char *g_arp_miss_thread_string[] = {
    [ARP_MISS_THREAD_INDEX_ONE] = "arp_re delete,arp_re write,arp_nhp read,arp_re update,arp_re write,arp_nhp read,arp_re update,arp_re read",
    [ARP_MISS_THREAD_INDEX_TWO] = "arp_nhp read,arp_nhp delete,arp_fake delete,arp write,aib write,arp update,arp_nhp write,arp_nhp update,\
arp update,arp_nhp write,arp_nhp update,arp update,arp write,arp_fake write,arp read"
};

static void arp_miss_usage(char *basename)
{
    printf("usage:%s [-h] [-c flag] [-p num] [-t runtime] [-0|1] [-f schema_idx]"
           " [-s conf_file]\n",
           basename);

    printf("-p num              : num threads to create\n");
    printf("-c flag             : 0|1 create table data.\n");
    printf("-t runtime          : run time, uint: s\n");
    printf("-n                  : max data\n");
    printf("-f schema_idx       : schema_idx\n");
    printf("-%d sleep_ms        : %s\n", ARP_MISS_THREAD_INDEX_ONE,
           g_arp_miss_thread_string[ARP_MISS_THREAD_INDEX_ONE]);
    printf("-%d sleep_ms        : %s\n", ARP_MISS_THREAD_INDEX_TWO,
           g_arp_miss_thread_string[ARP_MISS_THREAD_INDEX_TWO]);
    printf("-h                  : show usage\n");
}

int arp_miss_get_config(int argc, char **argv, local_config_t *conf)
{
    int ret = STATUS_OK;
    int opt;
    local_p_thr_arg_t p_thr_args;
    unsigned char temp;
    while ((opt = getopt(argc, argv, "hp:b:c:f:n:t:s:0:1:")) != -1) {
        switch (opt) {
            case 'h':
                arp_miss_usage(argv[0]);
                ret = -1;
                break;
            case 'p':
                conf->parallel_count = atoi(optarg);
                break;
            case 'b':
                conf->batch_wr_loop = atoi(optarg);
                break;
            case 'c':
                conf->create_talbe_data = atoi(optarg);  // 1: should create
                break;

            case 'f':
                conf->table_index = atoi(optarg);
                break;

            case 'n':
                conf->data_max = atoi(optarg);  // 100w
                break;

            case 't':
                conf->run_time = atoi(optarg);
                break;

            case 's':
                ret = local_config_set_table_low_high(optarg);
                break;

            case '0':
            case '1':
                temp = (unsigned char)(opt - '0');
                p_thr_args = multi_thread_get_thread_args(temp);
                if (p_thr_args == NULL) {
                    ret = -1;
                    break;
                }
                p_thr_args->is_use = 1;
                p_thr_args->sleep_ms_when_run = atoi(optarg);
                conf->parallel_count += 1;
                break;

            default:
                break;
        }
    }

    if (conf->parallel_count == 0) {
        printf("parallel_count is 0\n");
        ret = -1;
    }
    return ret;
}

int arp_miss_test_task(local_config_t *conf)
{
    int ret = STATUS_OK;

    local_thr_arg_t thr_args;

// 线程1:arp_re delete,arp_re write,arp_nhp read,arp_re update,arp_re write,arp_nhp read,arp_re update,arp_re read
    memset(&thr_args, 0, sizeof(local_thr_arg_t));
    thr_args.thr_num = ARP_MISS_THREAD_INDEX_ONE;
    thr_args.thr_name = "ARP_MISS_00";
    thr_args.thr_description = g_arp_miss_thread_string[ARP_MISS_THREAD_INDEX_ONE];
    thr_args.thr_conf = conf;
    thr_args.restrict_attr = NULL;
    thr_args.thread_func = arp_miss_thr_fun_01;
     
    thr_args.thr_run = DB_TRUE;
    (void)multi_thread_set_thread_args(&thr_args);

// 线程2:arp_nhp read,arp_nhp delete,arp_fake delete,arp write,aib write,arp update,arp_nhp write,arp_nhp update,
// arp update,arp_nhp write,arp_nhp update,arp update,arp write,arp_fake write,arp read
    memset(&thr_args, 0, sizeof(local_thr_arg_t));
    thr_args.thr_num = ARP_MISS_THREAD_INDEX_TWO;
    thr_args.thr_name = "ARP_MISS_00";
    thr_args.thr_description = g_arp_miss_thread_string[ARP_MISS_THREAD_INDEX_TWO];
    thr_args.thr_conf = conf;
    thr_args.restrict_attr = NULL;
    thr_args.thread_func = arp_miss_thr_fun_02;
     
    thr_args.thr_run = DB_TRUE;
    (void)multi_thread_set_thread_args(&thr_args);

    ret = multi_thread_start_all_threads();
    if (ret != 0) {
        TEST_ERROR("threads start error, ret:%d", ret);
    }

    sleep(conf->run_time);
    TEST_INFO("time over, now we will stop all threads");
    // 发送信号终止线程
    local_p_thr_arg_t p_thr_args;
    unsigned int i = 0;
    for (i = 0; i < ARP_MISS_THREAD_INDEX_INVALID; i++) {
        p_thr_args = multi_thread_get_thread_args((unsigned char)i);
        if (p_thr_args == NULL || p_thr_args->is_use == 0) {
            continue;
        }
        p_thr_args->thr_run = 0;
    }
    for (i = 0; i < ARP_MISS_THREAD_INDEX_INVALID; i++) {
        p_thr_args = multi_thread_get_thread_args((unsigned char)i);
        if (p_thr_args == NULL || p_thr_args->is_use == 0) {
            continue;
        }
        TEST_INFO("waiting thread %u to end...", i);
        ret = pthread_join(p_thr_args->thread_id, DB_NULL);
        if (ret != STATUS_OK) {
            TEST_ERROR("Josin benchmark thread failed, id=%u, err_msg=%s.", i, strerror(errno));
        }
        TEST_INFO("thread %u has exited", i);
        thread_print_result_data(i);
    }

    // print result info.
    // ...
    return ret;
}

// 线程1:arp_re delete,arp_re write,arp_nhp read,arp_re update,arp_re write,arp_nhp read,arp_re update,arp_re read
void *arp_miss_thr_fun_01(void *thr_user_args)
{
    status_t ret = STATUS_OK;
    local_p_thr_arg_t p_thr_arg = (local_p_thr_arg_t)thr_user_args;
    unsigned int thr_num = p_thr_arg->thr_num;
    (p_thr_arg->thr_result)->is_finished = 0;

    // set i
    uint64_t i = 0;

    // objects

    thread_start_set_result_data(thr_num);

    while (p_thr_arg->thr_run) {

        // arp_re remove
        ret = arp_re_remove(i);
        if ((ret != 0) && (ret != STATUS_OBJECT_DOESNT_EXIST)) {
            // LOG_ERROR(ret, "arp_re_remove");
            break;
        }

        // arp_re write
        ret = arp_func_write_arp_re(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_read_arp_re");
            break;
        }

        // arp_nhp read
        ret = arp_func_read_arp_nhp(i);
        if ((ret != 0) && (ret != STATUS_OBJECT_DOESNT_EXIST)) {
            // LOG_ERROR(ret, "arp_func_read_arp_nhp");
            break;
        }

        // arp_re update
        ret = arp_func_update_arp_re(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_update_arp_re");
            break;
        }

        // arp_re write
        ret = arp_func_write_arp_re(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_read_arp_re");
            break;
        }

        // arp_nhp read
        ret = arp_func_read_arp_nhp(i);
        if ((ret != 0) && (ret != STATUS_OBJECT_DOESNT_EXIST)) {
            // LOG_ERROR(ret, "arp_func_read_arp_nhp");
            break;
        }

        // arp_re update
        ret = arp_func_update_arp_re(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_update_arp_re");
            break;
        }

        // arp_re read
        ret = arp_func_read_arp_re(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_read_arp_re");
            break;
        }

        i++;
    }

    if (ret == 0) {
        (p_thr_arg->thr_result)->is_finished = 1;
    }
    (p_thr_arg->thr_result)->op_total = i;
    thread_end_set_result_data(thr_num);

    return NULL;
}

// 线程2:arp_nhp read,arp_nhp delete,arp_fake delete,arp write,aib write,arp update,arp_nhp write,arp_nhp update,
// arp update,arp_nhp write,arp_nhp update,arp update,arp write,arp_fake write,arp read
void *arp_miss_thr_fun_02(void *thr_user_args)
{
    status_t ret = STATUS_OK;
    local_p_thr_arg_t p_thr_arg = (local_p_thr_arg_t)thr_user_args;
    unsigned int thr_num = p_thr_arg->thr_num;
    // unsigned int sleep_us = 1000 * (p_thr_arg->sleep_ms_when_run);
    (p_thr_arg->thr_result)->is_finished = 0;

    // set i
    uint64_t i = 0;
    unsigned int if_index;

    // objects

    thread_start_set_result_data(thr_num);

    while (p_thr_arg->thr_run) {
        // arp_nhp read,
        ret = arp_func_read_arp_nhp(i);
        if ((ret != 0) && (ret != STATUS_OBJECT_DOESNT_EXIST)) {
            // LOG_ERROR(ret, "arp_func_read_arp_nhp");
            break;
        }

        // arp_nhp delete,
        ret = arp_nhp_remove(i);
        if ((ret != 0) && (ret != STATUS_OBJECT_DOESNT_EXIST)) {
            // LOG_ERROR(ret, "arp_re_remove");
            break;
        }

        // arp_fake delete,
        ret = arp_fake_remove(i);
        if ((ret != 0) && (ret != STATUS_OBJECT_DOESNT_EXIST)) {
            // LOG_ERROR(ret, "arp_fake_remove");
            break;
        }

        // arp write,
        ret = arp_func_write_arp(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_write_arp");
            break;
        }

        // aib write,
        ret = arp_func_write_aib(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_read_aib");
            break;
        }

        // arp update,
        ret = arp_func_update_arp_service(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_update_arp_service");
            break;
        }

        // arp_nhp write,
        ret = arp_func_write_arp_nhp(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_write_arp_nhp");
            break;
        }

        // arp_nhp update,
        ret = arp_func_update_arp_nhp_context(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_update_arp_nhp_context");
            break;
        }

        // arp update,
        ret = arp_func_update_arp_service(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_update_arp_service");
            break;
        }

        // arp_nhp write,
        ret = arp_func_write_arp_nhp(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_write_arp_nhp");
            break;
        }

        // arp_nhp update,
        ret = arp_func_update_arp_nhp_context(i);
        if (ret) {
            LOG_ERROR(ret, "arp_func_update_arp_nhp_context");
            break;
        }

        // arp update,
        ret = arp_func_update_arp_service(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_update_arp_service");
            break;
        }

        // arp write,
        ret = arp_func_write_arp(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_write_arp");
            break;
        }

        // arp_fake write,
        ret = arp_fake_single_write(i);
        if (ret) {
            // LOG_ERROR(ret, "arp_fake_single_write");
            break;
        }

        // arp read
        ret = arp_func_read_arp(i, &if_index);
        if (ret) {
            // LOG_ERROR(ret, "arp_func_read_arp");
            break;
        }

        i++;
    }

    if (ret == 0) {
        (p_thr_arg->thr_result)->is_finished = 1;
    }
    (p_thr_arg->thr_result)->op_total = i;
    thread_end_set_result_data(thr_num);

    return NULL;

}

uint32_t arp_fake_remove(uint64_t uiVrIndex)
{
    uint32_t ret;
    db_object object = NULL;

    if (uiVrIndex > g_model_high[LOCAL_DB_IDX_ARP_FAKE]) {
        uiVrIndex = g_model_low[LOCAL_DB_IDX_ARP_FAKE];
    }
    ret = local_db_arp_fake_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_arp_fake_create_db_obj_func");

    db_key_info_t key_info;
    db_arp_fake_key_t key_data;
    ret = local_db_arp_fake_key_set_func(object, uiVrIndex, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_arp_fake_key_set_func");

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    local_db_arp_fake_release_db_obj_func(object);

    return ret;
}

uint32_t arp_fake_single_write(uint64_t uiVrIndex)
{
    uint32_t ret;
    db_object object = NULL;

    if (uiVrIndex > g_model_high[LOCAL_DB_IDX_ARP_FAKE]) {
        uiVrIndex = g_model_low[LOCAL_DB_IDX_ARP_FAKE];
    }

    // arp_fake write
    ret = local_db_arp_fake_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_arp_fake_set_obj_func(object, uiVrIndex, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", uiVrIndex);
        local_db_arp_fake_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "db_write_object i=%lu", uiVrIndex);
    }

    local_db_arp_fake_release_db_obj_func(object);

    return ret;
}

uint32_t arp_re_remove(uint64_t uiVrIndex)
{
    uint32_t ret;
    db_object object = NULL;

    if (uiVrIndex > g_model_high[LOCAL_DB_IDX_ARP_RE]) {
        uiVrIndex = g_model_low[LOCAL_DB_IDX_ARP_RE];
    }
    ret = local_db_arp_re_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_arp_re_create_db_obj_func");

    db_key_info_t key_info;
    db_arp_re_key_t key_data;
    ret = local_db_arp_re_key_set_func(object, uiVrIndex, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_arp_re_key_set_func");

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    local_db_arp_re_release_db_obj_func(object);

    return ret;
}

uint32_t arp_nhp_remove(uint64_t uiVrIndex)
{
    uint32_t ret;
    db_object object = NULL;

    if (uiVrIndex > g_model_high[LOCAL_DB_IDX_ARP_NHP]) {
        uiVrIndex = g_model_low[LOCAL_DB_IDX_ARP_NHP];
    }
    ret = local_db_arp_nhp_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_arp_nhp_create_db_obj_func");

    db_key_info_t key_info;
    db_arp_nhp_key_t key_data;
    ret = local_db_arp_nhp_key_set_func(object, uiVrIndex, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_arp_nhp_key_set_func");

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    local_db_arp_nhp_release_db_obj_func(object);

    return ret;
}
