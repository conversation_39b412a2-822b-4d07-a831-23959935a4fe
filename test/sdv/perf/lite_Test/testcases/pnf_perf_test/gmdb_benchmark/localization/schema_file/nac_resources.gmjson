{"version": "2.0", "type": "record", "name": "nac_resources", "special_complex": true, "config": {"check_validity": false}, "fields": [{"name": "instance_id", "type": "uint32"}, {"name": "global_cid", "type": "uint32"}, {"name": "car_entryid", "type": "uint32"}, {"name": "car_profileid", "type": "uint32"}, {"name": "acl_list", "type": "record", "array": true, "size": 8, "fields": [{"name": "acl_id", "type": "uint32"}]}], "keys": [{"name": "nac_resources_pk", "index": {"type": "primary"}, "node": "nac_resources", "fields": ["instance_id", "global_cid"], "constraints": {"unique": true}}]}