
#include "local_db_nhp6_group.h"
#include "db_nhp6_group.h"
#include "local_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "cpu_cycles.h"
p_local_db_schema_model_t g_nhp6_group_model = NULL;

status_t local_db_nhp6_group_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                              db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    DB_START_TEST_CPU_CYCLES(CreateObj);
    ret = db_create_nhp6_group_obj(obj_type, conn_type, &obj);
    DB_STOP_TEST_CPU_CYCLES(CreateObj);
    *object = obj;
    return ret;
}

void local_db_nhp6_group_release_db_obj_func(db_object object)
{
    DB_START_TEST_CPU_CYCLES(Release);
    db_release_nhp6_group_object(object);
    DB_STOP_TEST_CPU_CYCLES(Release);
}

status_t local_db_nhp6_group_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(ResetObj);
    ret = db_reset_nhp6_group_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(ResetObj);

    return ret;
}

status_t local_db_nhp6_group_create_db_batch_obj_func(db_conn_type type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    DB_START_TEST_CPU_CYCLES(CreateObj);
    ret = db_create_nhp6_group_obj(BATCH_OPERATION, type, &obj);
    DB_STOP_TEST_CPU_CYCLES(CreateObj);
    *object = obj;
    return ret;
}

status_t local_db_nhp6_group_reset_db_bobj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;
    DB_START_TEST_CPU_CYCLES(ResetObj);
    ret = db_reset_nhp6_group_batch_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(ResetObj);

    return ret;
}

status_t local_db_nhp6_group_primary_key_malloc_func(void **key, uint32_t *length)
{
    *length = sizeof(db_nhp6_group_key_t);
    *key = malloc(sizeof(db_nhp6_group_key_t));
    return 0;
}

void local_db_nhp6_group_primary_key_free_func(void *key)
{
    free(key);
}

status_t local_db_nhp6_group_struct_data_malloc_func(void **data, uint32_t *length)
{
    *length = sizeof(nhp6_group_struct_t);
    *data = malloc(*length);
    return 0;
}

void local_db_nhp6_group_struct_data_free_func(void *data)
{
    free(data);
}

status_t local_db_nhp6_group_get_field_func(db_object object, uint32_t get_field_cnt)
{
    status_t ret = STATUS_OK;
    uint32_t iidGroupId;               
    uint32_t vrId;                     
    uint32_t vrfIndex;                 
    uint32_t refCount;                 
    uint32_t nhpNodeNum;               
    uint32_t newVrfIndex;              
    uint8_t isNextTable;               
    uint8_t nhpType[2]; 
    uint8_t isMeLocal;                    

    uint32_t flag; 
    
    uint8_t status[2];                         
    uint8_t errcode[2];                        
    uint32_t svcCtx[2][4]; 
    uint32_t appSourceId;                                     
    uint32_t tableSmoothId;                                   
    uint64_t appObjId;                                        
    
    uint32_t appVersion; 
    int8_t *svc_ctx = NULL;
    uint32_t len = 0;

    (void)db_get_nhp6_group_iid_group_id(object, &iidGroupId);
    (void)db_get_nhp6_group_vr_id(object, &vrId);
    (void)db_get_nhp6_group_vrf_index(object, &vrfIndex);
    (void)db_get_nhp6_group_ref_count(object, &refCount);
    (void)db_get_nhp6_group_node_num(object, &nhpNodeNum);
    (void)db_get_nhp6_group_new_vrf_index(object, &newVrfIndex);
    (void)db_get_nhp6_group_is_next_table(object, &isNextTable);
    (void)db_get_nhp6_group_nhp_type_high_prio(object, &nhpType[0]);
    (void)db_get_nhp6_group_nhp_type_normal_prio(object, &nhpType[1]);

    (void)db_get_nhp6_group_flag(object, &flag);
    (void)db_get_nhp6_group_status_high_prio(object, &status[0]);
    (void)db_get_nhp6_group_status_normal_prio(object, &status[1]);
    (void)db_get_nhp6_group_err_code_high_prio(object, &errcode[0]);
    (void)db_get_nhp6_group_err_code_normal_prio(object, &errcode[1]);
    (void)db_get_nhp6_group_svc_ctx_high_prio(object, &svc_ctx, &len);
    (void)memcpy_s(svcCtx[0], 4, svc_ctx, len);
    
    (void)db_get_nhp6_group_svc_ctx_normal_prio(object, &svc_ctx, &len);
    (void)memcpy_s(svcCtx[1], 4, svc_ctx, len);

    (void)db_get_nhp6_group_app_source_id(object, &appSourceId);
    (void)db_get_nhp6_group_table_smooth_id(object, &tableSmoothId);
    (void)db_get_nhp6_group_app_obj_id(object, &appObjId);
    (void)db_get_nhp6_group_app_version(object, &appVersion);
    (void)db_get_nhp6_group_is_meth_local(object, &isMeLocal);
#ifdef TEST_DEBUG
    printf("iid_group_id:%u, vr_id:%d\n", iidGroupId, vrId);
#endif

    return ret;
}

status_t local_db_nhp6_group_set_field_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    (void)db_set_nhp6_group_iid_group_id(object, (uiVrIndex & (uint32_t)(~0)));
    (void)db_set_nhp6_group_vr_id(object, 0);
    (void)db_set_nhp6_group_vrf_index(object, uiVrIndex);
    (void)db_set_nhp6_group_ref_count(object, uiVrIndex);
    (void)db_set_nhp6_group_node_num(object, uiVrIndex);
    (void)db_set_nhp6_group_new_vrf_index(object, uiVrIndex);
    (void)db_set_nhp6_group_is_next_table(object, uiVrIndex);
    (void)db_set_nhp6_group_flag(object, uiVrIndex);
    
    (void)db_set_nhp6_group_status_high_prio(object, uiVrIndex);
    (void)db_set_nhp6_group_status_normal_prio(object, uiVrIndex);
    (void)db_set_nhp6_group_err_code_high_prio(object, uiVrIndex);
    (void)db_set_nhp6_group_err_code_normal_prio(object, uiVrIndex);

    (void)db_set_nhp6_group_svc_ctx_high_prio(object, (int8_t *)ZERO_64_BYTES, DB_NHP6_GROUP_SVC_CTX_HIGH_PRIO_LEN);
    (void)db_set_nhp6_group_svc_ctx_normal_prio(object, (int8_t *)ZERO_64_BYTES, DB_NHP6_GROUP_SVC_CTX_NORMAL_PRIO_LEN);

    (void)db_set_nhp6_group_app_source_id(object, uiVrIndex);
    (void)db_set_nhp6_group_table_smooth_id(object, uiVrIndex);
    (void)db_set_nhp6_group_app_obj_id(object, uiVrIndex);
    (void)db_set_nhp6_group_app_version(object, uiVrIndex);
    (void)db_set_nhp6_group_is_meth_local(object, uiVrIndex);

    return ret;
}

status_t local_db_nhp6_group_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
#ifdef NORMAL_INTF
    ret = local_db_nhp6_group_set_field_func(object, uiVrIndex, array, thread_local_num);
#else
    nhp6_group_struct_t obj_struct;
    memset(&obj_struct, 0, sizeof(obj_struct));
    
    obj_struct.iid_group_id = (uiVrIndex & (uint32_t)(~0));
    obj_struct.vr_id = 0;
    obj_struct.vrf_index = uiVrIndex;
    obj_struct.ref_count = uiVrIndex;
    obj_struct.node_num = uiVrIndex;
    obj_struct.new_vrf_index = uiVrIndex;
    obj_struct.is_next_table = uiVrIndex;
    obj_struct.nhp_type_high_prio = uiVrIndex;
    obj_struct.nhp_type_normal_prio = uiVrIndex;
    obj_struct.is_meth_local = uiVrIndex;
    obj_struct.flag = uiVrIndex;
    obj_struct.status_high_prio = uiVrIndex;
    obj_struct.status_normal_prio = uiVrIndex;
    obj_struct.err_code_high_prio = uiVrIndex;
    obj_struct.err_code_normal_prio = uiVrIndex;
    memset(obj_struct.svc_ctx_high_prio, 0, sizeof(obj_struct.svc_ctx_high_prio));
    memset(obj_struct.svc_ctx_normal_prio, 0, sizeof(obj_struct.svc_ctx_normal_prio));
    obj_struct.app_source_id = uiVrIndex;
    obj_struct.table_smooth_id = uiVrIndex;
    obj_struct.app_obj_id = uiVrIndex;
    obj_struct.app_version = uiVrIndex;
set_again:
    ret = db_set_nhp6_group_all_fields(object, &obj_struct, sizeof(obj_struct));
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }
#endif
    return ret;
}

status_t local_db_nhp6_group_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_nhp6_group_key_t key_data = {0};
    key_info.index_id = DB_NHP6_GROUP_PRIMARY_KEY_ID;
    key_info.key_len = sizeof(key_data);
    key_data.iid_group_id = (uiVrIndex & (uint32_t)(~0));
    key_data.vr_id = 0;
    DB_START_TEST_CPU_CYCLES(Read);
    ret = db_read_obj(object, &key_info, &key_data);
    DB_STOP_TEST_CPU_CYCLES(Read);
    if (ret != STATUS_OK) {
        return ret;
    }
    nhp6_group_struct_t obj_struct;
    DB_START_TEST_CPU_CYCLES(GetAllField);
    ret = db_get_nhp6_group_all_fields(object, &obj_struct, sizeof(nhp6_group_struct_t));
    DB_STOP_TEST_CPU_CYCLES(GetAllField);
    return ret;
}

status_t local_db_nhp6_group_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void *key_info, void *key_data)
{
    db_key_info_t *ptr_key_info = (db_key_info_t *)key_info;
    db_nhp6_group_key_t *ptr_key_data = (db_nhp6_group_key_t *)key_data;
    memset(ptr_key_info, 0, sizeof(db_key_info_t));
    memset(ptr_key_data, 0, sizeof(db_nhp6_group_key_t));
    ptr_key_info->index_id = DB_NHP6_GROUP_PRIMARY_KEY_ID;
    ptr_key_info->key_len = sizeof(db_nhp6_group_key_t);
    ptr_key_data->iid_group_id = (uiVrIndex & (uint32_t)(~0));
    ptr_key_data->vr_id = 0;
    return 0;
}

status_t local_db_nhp6_group_register_shcema_model(void *schema_model)
{
    if (g_nhp6_group_model == NULL) {
        g_nhp6_group_model = (p_local_db_schema_model_t)(schema_model);

        g_nhp6_group_model->obj_create_func = local_db_nhp6_group_create_db_obj_func;
        g_nhp6_group_model->obj_release_func = local_db_nhp6_group_release_db_obj_func;
        g_nhp6_group_model->obj_reset_func = local_db_nhp6_group_reset_db_obj_func;
        g_nhp6_group_model->bobj_reset_func = local_db_nhp6_group_reset_db_bobj_func;
        g_nhp6_group_model->bobj_create_func = local_db_nhp6_group_create_db_batch_obj_func;
        g_nhp6_group_model->obj_set_func = local_db_nhp6_group_set_obj_func;
        g_nhp6_group_model->malloc_primary_key_func = local_db_nhp6_group_primary_key_malloc_func;
        g_nhp6_group_model->free_primary_key_func = local_db_nhp6_group_primary_key_free_func;
        g_nhp6_group_model->malloc_struct_data_func = local_db_nhp6_group_struct_data_malloc_func;
        g_nhp6_group_model->free_struct_data_func = local_db_nhp6_group_struct_data_free_func;
        g_nhp6_group_model->getfield_bykey_func = local_db_nhp6_group_get_field_by_key_func;
        g_nhp6_group_model->key_set_func = local_db_nhp6_group_key_set_func;
        g_nhp6_group_model->getfield_func = local_db_nhp6_group_get_field_func;
    }
    return 0;
}
