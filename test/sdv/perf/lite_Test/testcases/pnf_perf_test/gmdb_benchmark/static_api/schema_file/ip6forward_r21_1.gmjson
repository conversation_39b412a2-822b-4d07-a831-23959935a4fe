{"comment": "IPV6前缀表，对应81#表", "version": "2.0", "type": "record", "name": "ip6forward_r21_1", "config": {"check_validity": true}, "max_record_count": 4000000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "ip_addr", "type": "fixed", "size": 16, "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "目的地址掩码长度"}, {"name": "iid_group_flag", "type": "uint8", "comment": "标识Nhp或Nhp组"}, {"name": "qos_id", "type": "uint16", "comment": "QOS ID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "iid_index", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据iidGroupFlag决定"}, {"name": "route_type", "type": "uint32", "comment": "直连路由，非直连路由"}, {"name": "flag", "type": "uint32", "comment": "path完备性标志"}, {"name": "status_high_prio", "type": "uint8", "default": 0, "comment": "SERVICE高优先级下发状态"}, {"name": "status_normal_prio", "type": "uint8", "default": 0, "comment": "SERVICE普通优先级下发状态"}, {"name": "err_code_high_prio", "type": "uint8", "default": 0, "comment": "SERVICE高优先级返回错误码"}, {"name": "err_code_normal_prio", "type": "uint8", "default": 0, "comment": "SERVICE普通优先级返回错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识，对应VRP8 hSrcPid字段"}, {"name": "table_smooth_id", "type": "uint32", "comment": "记载当前表的全局序列号，该序列号每次对账时递增"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理，即使KEY和data相同，但删除后再添加时这个ID也会不同，具体使用场景不明确，暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号，用于跟踪同一条记录的变化情形，具体使用场景不明确，暂时保留不用"}, {"name": "topology_id", "type": "uint32", "comment": "拓扑ID"}, {"name": "route_flag", "type": "uint16", "comment": "路由标记"}, {"name": "reserved", "type": "uint16", "nullable": true, "comment": "预留"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip6forward_r21_1", "fields": ["vr_id", "vrf_index", "ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "localhash_key", "index": {"type": "hashcluster"}, "node": "ip6forward_r21_1", "fields": ["iid_index", "vr_id"], "constraints": {"unique": false}, "comment": "根据iidIndex索引 + vrid索引"}, {"name": "vrfid_hashcluster_key", "index": {"type": "hashcluster"}, "node": "ip6forward_r21_1", "fields": ["app_source_id", "vr_id", "vrf_index"], "constraints": {"unique": false}, "comment": "根据appSourceId + vrId + vrfIndex索引"}, {"name": "ip6forward_r21_1_lpm", "index": {"type": "lpm6_tree_bitmap"}, "node": "ip6forward_r21_1", "fields": ["vr_id", "vrf_index", "ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "ipv6最长前缀匹配"}, {"name": "local_key", "index": {"type": "local"}, "node": "ip6forward_r21_1", "fields": ["vr_id", "vrf_index", "ip_addr", "mask_len"], "comment": "字典序排序建立索引"}]}