
#include "intricacy_06_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "db_intricacy_06.h"
#include "db_trunk.h"
#include "cpu_cycles.h"
#include <pthread.h>

DB_DEF_CPU_CYCLES(intricacy_06_CreateChild);
DB_DEF_CPU_CYCLES(intricacy_06_CreateObj);

p_static_schema_model_t g_model_intricacy_06;

status_t static_intricacy_06_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateObj);
    ret = db_create_intricacy_06_obj(obj_type, conn_type, &obj);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateObj)
    CHECK_OK_RET(ret, "Create intricacy_06 object.");
    *object = obj;
    return ret;
}

status_t static_intricacy_06_create_db_obj_spec_conn_func(db_object_type obj_type, db_connect_t conn, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_intricacy_06_obj_specific_conn(obj_type, conn, &obj);
    CHECK_OK_RET(ret, "Create intricacy_06 spec conn object.");

    *object = obj;
    return ret;
}

void static_intricacy_06_release_db_obj_func(db_object object)
{
    db_release_intricacy_06_object(object);
}

status_t static_intricacy_06_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_intricacy_06_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_intricacy_06_reset_db_batch_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_intricacy_06_batch_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t setT1Node_intricacy_06(db_object object, void* obj_struct, uint32_t uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T1", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_set_intricacy_06_T1_F91(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F91");
    ret = db_set_intricacy_06_T1_F92(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F92");
    ret = db_set_intricacy_06_T1_F93(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F93");
    ret = db_set_intricacy_06_T1_F94(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F94");
    ret = db_set_intricacy_06_T1_F95(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F95");
    ret = db_set_intricacy_06_T1_F96(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F96");
    ret = db_set_intricacy_06_T1_F97(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F97");
    ret = db_set_intricacy_06_T1_F98(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F98");
    ret = db_set_intricacy_06_T1_F99(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F99");
    ret = db_set_intricacy_06_T1_F100(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T1_F100");

    return ret;
}
status_t setT2Node_intricacy_06(db_object object, void* obj_struct, uint32_t uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_set_intricacy_06_T2_F81(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F81");
    ret = db_set_intricacy_06_T2_F82(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F82");
    ret = db_set_intricacy_06_T2_F83(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F83");
    ret = db_set_intricacy_06_T2_F84(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F84");
    ret = db_set_intricacy_06_T2_F85(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F85");
    ret = db_set_intricacy_06_T2_F86(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F86");
    ret = db_set_intricacy_06_T2_F87(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F87");
    ret = db_set_intricacy_06_T2_F88(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F88");
    ret = db_set_intricacy_06_T2_F89(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F89");
    ret = db_set_intricacy_06_T2_F90(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T2_F90");

    return ret;
}

status_t setT3Node_intricacy_06(db_object object, void* obj_struct, uint32_t uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T3", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_set_intricacy_06_T3_F71(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F71");
    ret = db_set_intricacy_06_T3_F72(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F72");
    ret = db_set_intricacy_06_T3_F73(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F73");
    ret = db_set_intricacy_06_T3_F74(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F74");
    ret = db_set_intricacy_06_T3_F75(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F75");
    ret = db_set_intricacy_06_T3_F76(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F76");
    ret = db_set_intricacy_06_T3_F77(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F77");
    ret = db_set_intricacy_06_T3_F78(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F78");
    ret = db_set_intricacy_06_T3_F79(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F79");
    ret = db_set_intricacy_06_T3_F80(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T3_F80");

    return ret;
}

status_t setT4Node_intricacy_06(db_object object, void* obj_struct, uint32_t uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T4", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_set_intricacy_06_T4_F61(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F61");
    ret = db_set_intricacy_06_T4_F62(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F62");
    ret = db_set_intricacy_06_T4_F63(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F63");
    ret = db_set_intricacy_06_T4_F64(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F64");
    ret = db_set_intricacy_06_T4_F65(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F65");
    ret = db_set_intricacy_06_T4_F66(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F66");
    ret = db_set_intricacy_06_T4_F67(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F67");
    ret = db_set_intricacy_06_T4_F68(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F68");
    ret = db_set_intricacy_06_T4_F69(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F69");
    ret = db_set_intricacy_06_T4_F70(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T4_F70");

    return ret;
}

status_t setT5Node_intricacy_06(db_object object, void* obj_struct, uint32_t uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T5", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_set_intricacy_06_T5_F51(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F51");
    ret = db_set_intricacy_06_T5_F52(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F52");
    ret = db_set_intricacy_06_T5_F53(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F53");
    ret = db_set_intricacy_06_T5_F54(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F54");
    ret = db_set_intricacy_06_T5_F55(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F55");
    ret = db_set_intricacy_06_T5_F56(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F56");
    ret = db_set_intricacy_06_T5_F57(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F57");
    ret = db_set_intricacy_06_T5_F58(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F58");
    ret = db_set_intricacy_06_T5_F59(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F59");
    ret = db_set_intricacy_06_T5_F60(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T5_F60");

    return ret;
}

status_t setT6Node_intricacy_06(db_object object, void* obj_struct, uint32_t uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T6", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_set_intricacy_06_T6_F41(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F41");
    ret = db_set_intricacy_06_T6_F42(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F42");
    ret = db_set_intricacy_06_T6_F43(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F43");
    ret = db_set_intricacy_06_T6_F44(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F44");
    ret = db_set_intricacy_06_T6_F45(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F45");
    ret = db_set_intricacy_06_T6_F46(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F46");
    ret = db_set_intricacy_06_T6_F47(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F47");
    ret = db_set_intricacy_06_T6_F48(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F48");
    ret = db_set_intricacy_06_T6_F49(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F49");
    ret = db_set_intricacy_06_T6_F50(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_intricacy_06_T6_F50");

    return ret;
}

status_t static_intricacy_06_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_special_num)
{
    status_t ret = 0;

    ret = db_set_intricacy_06_cid(object, (uiVrIndex & (uint64_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F1(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F2(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F3(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F4(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F5(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F6(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F7(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F8(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F9(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F10(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F11(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F12(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F13(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F14(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F15(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F16(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F17(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F18(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F19(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F20(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F21(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F22(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F23(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F24(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F25(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F26(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F27(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F28(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F29(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F30(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F31(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F32(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F33(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F34(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F35(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F36(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F37(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F38(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F39(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F40(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");

    ret = setT1Node_intricacy_06(object, NULL, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T1 node");
    
    ret = setT2Node_intricacy_06(object, NULL, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T2 node");

    ret = setT3Node_intricacy_06(object, NULL, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T3 node");

    ret = setT4Node_intricacy_06(object, NULL, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T4 node");

    ret = setT5Node_intricacy_06(object, NULL, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T5 node");

    ret = setT6Node_intricacy_06(object, NULL, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T6 node");

    return ret;
}


status_t getT1Node_intricacy_06(db_object object, uint32_t* uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T1", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_get_intricacy_06_T1_F91(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F92(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F93(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F94(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F95(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F96(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F97(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F98(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F99(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T1_F100(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT1Node_intricacy_06 failed");

    return ret;
}

status_t getT2Node_intricacy_06(db_object object, uint32_t* uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_get_intricacy_06_T2_F81(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F82(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F83(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F84(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F85(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F86(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F87(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F88(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F89(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T2_F90(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");

    return ret;
}

status_t getT3Node_intricacy_06(db_object object, uint32_t* uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T3", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_get_intricacy_06_T3_F71(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F72(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F73(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F74(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F75(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F76(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F77(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F78(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F79(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T3_F80(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");

    return ret;
}

status_t getT4Node_intricacy_06(db_object object, uint32_t* uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T4", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_get_intricacy_06_T4_F61(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F62(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F63(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F64(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F65(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F66(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F67(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F68(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F69(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T4_F70(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");

    return ret;
}

status_t getT5Node_intricacy_06(db_object object, uint32_t* uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T5", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_get_intricacy_06_T5_F51(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F52(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F53(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F54(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F55(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F56(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F57(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F58(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F59(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T5_F60(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");

    return ret;
}

status_t getT6Node_intricacy_06(db_object object, uint32_t* uiVrIndex)
{
    status_t ret = STATUS_OK;

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(intricacy_06_CreateChild);
    ret = db_create_child_node(object, NULL, "T6", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(intricacy_06_CreateChild);

    ret = db_get_intricacy_06_T6_F41(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F42(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F43(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F44(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F45(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F46(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F47(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F48(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F49(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");
    ret = db_get_intricacy_06_T6_F50(child_iterator, uiVrIndex);
    CHECK_OK_RET(ret, "getT2Node_intricacy_06 failed");

    return ret;
}

status_t static_intricacy_06_get_field_func(db_object object, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    uint32_t read_data_u32;
    uint64_t read_data_u64;
    CHECK_OK_RET(ret, "static_devm_get_field_func failed");

    ret = db_get_intricacy_06_cid(object, &read_data_u64);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F1(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F2(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F3(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F4(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F5(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F6(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F7(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F8(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F9(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F10(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F11(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F12(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F13(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F14(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F15(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F16(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F17(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F18(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F19(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F20(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F21(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F22(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F23(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F24(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F25(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F26(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F27(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F28(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F29(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F30(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F31(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F32(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F33(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F34(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F35(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F36(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F37(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F38(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F39(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");
    ret = db_get_intricacy_06_F40(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_06_get_obj_func failed");

    ret = getT1Node_intricacy_06(object, &read_data_u32)
    CHECK_OK_RET_DEBUG(ret, "get  T1 node");

    ret = getT2Node_intricacy_06(object, &read_data_u32)
    CHECK_OK_RET_DEBUG(ret, "get  T2 node");

    ret = getT3Node_intricacy_06(object, &read_data_u32)
    CHECK_OK_RET_DEBUG(ret, "get  T3 node");

    ret = getT4Node_intricacy_06(object, &read_data_u32)
    CHECK_OK_RET_DEBUG(ret, "get  T4 node");

    ret = getT5Node_intricacy_06(object, &read_data_u32)
    CHECK_OK_RET_DEBUG(ret, "get  T5 node");

    ret = getT6Node_intricacy_06(object, &read_data_u32)
    CHECK_OK_RET_DEBUG(ret, "get  T6 node");

    return ret;
    
}

status_t static_intricacy_06_pri_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_special_num, void *key_info, void *key_data)
{
    db_intricacy_06_key_t *p_key_data = (db_intricacy_06_key_t *)key_data;
    db_key_info_t *p_key_info = (db_key_info_t *)key_info;
    memset(p_key_data, 0, sizeof(db_intricacy_06_key_t));
    memset(p_key_info, 0, sizeof(db_key_info_t));
    p_key_info->key_len = sizeof(db_intricacy_06_key_t);
    p_key_info->index_id = DB_INTRICACY_06_INTRICACY_06_PK_ID;
    p_key_data->cid = (uiVrIndex & (uint32_t)(~0));

    return 0;
}

status_t static_intricacy_06_update(db_object object, uint64_t uiVrIndex)
{
    static uint32_t factor = 0;
    factor++;
    status_t ret = STATUS_OK;

    ret = db_set_intricacy_06_F1(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F2(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F3(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F4(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F5(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F6(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F7(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F8(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F9(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F10(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F11(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F12(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F13(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F14(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F15(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F16(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F17(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F18(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F19(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F20(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F21(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F22(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F23(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F24(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F25(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F26(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F27(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F28(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F29(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F30(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F31(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F32(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F33(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F34(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F35(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F36(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F37(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F38(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F39(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");
    ret = db_set_intricacy_06_F40(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_06_set_obj_func failed");

    ret = setT1Node_intricacy_06(object, NULL, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T1 node");
    
    ret = setT2Node_intricacy_06(object, NULL, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T2 node");

    ret = setT3Node_intricacy_06(object, NULL, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T3 node");

    ret = setT4Node_intricacy_06(object, NULL, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T4 node");

    ret = setT5Node_intricacy_06(object, NULL, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T5 node");

    ret = setT6Node_intricacy_06(object, NULL, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set  T6 node");

    return ret;
}

status_t static_intricacy_06_fullscan_func(db_object object, uint64_t *op)
{
    status_t ret = STATUS_OK;
    db_root_iterator root_iterator;
    uint64_t scan_idx = 0;

    ret = db_create_root_iter(object, NULL, NULL, &root_iterator);
    CHECK_OK_RET(ret, "Create root iterator failed.");

    // uint32_t scan_count = g_model_ip4forward_r21_1->conflict_size;
    // ret = db_set_root_iterator_property(root_iterator, DB_ITERATOR_SET_CACHE_COUNT, (uint8_t *)&scan_count, sizeof(uint32_t));
    // CHECK_OK_RET(ret,"db_set_root_iterator_property failed.");

    // printf(">>>>db_set_root_iterator_property:%u\n", scan_count);

    // for (ret = db_root_iterator_next(root_iterator);; ret = db_root_iterator_next(root_iterator)) {
    for (;;) {
        STATIC_TIME_DELAY_START(g_time_delay_data);
        ret = db_root_iterator_next(root_iterator);
        STATIC_TIME_DELAY_STOP(g_time_delay_data);
        if (ret == STATUS_SCAN_TO_END) {
            ret = STATUS_OK;
            break;
        }
        CHECK_OK_RET(ret, "Scan table failed");
        ret = static_intricacy_06_get_field_func(object, FIELD_CNT_ALL);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Get field failed");
            break;
        }
        scan_idx++;
    }
    *op = scan_idx;
    return ret;
}

status_t static_intricacy_06_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_special_num, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_intricacy_06_key_t key_data = {0};
    key_info.index_id = DB_INTRICACY_06_INTRICACY_06_PK_ID;
    key_info.key_len = sizeof(db_intricacy_06_key_t);
    key_data.cid = (uint64_t)(uiVrIndex & ((uint64_t)~0));
    STATIC_TIME_DELAY_START(g_time_delay_data);
    ret = db_read_obj(object, &key_info, &key_data);
    STATIC_TIME_DELAY_STOP(g_time_delay_data);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "db_read_obj");
        return ret;
    }
    ret = static_intricacy_06_get_field_func(object, read_field_cnt);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "static_if_get_field_func");
        return ret;
    }
    return ret;
}

status_t static_intricacy_06_primary_key_malloc_func(void **key)
{
    *key = malloc(sizeof(db_intricacy_06_key_t));
    return 0;
}

void static_intricacy_06_primary_key_free_func(void *key)
{
    free(key);
}


status_t static_intricacy_06_register_schema_model(void *schema_model)
{
    g_model_intricacy_06 = (p_static_schema_model_t)(schema_model);

    if (g_model_intricacy_06->schema_json_file_name == NULL) {
        g_model_intricacy_06->schema_json_file_name = "intricacy_06";
        g_model_intricacy_06->obj_create_func = static_intricacy_06_create_db_obj_func;
        g_model_intricacy_06->obj_create_spec_conn_func = static_intricacy_06_create_db_obj_spec_conn_func;
        g_model_intricacy_06->obj_release_func = static_intricacy_06_release_db_obj_func;
        g_model_intricacy_06->obj_reset_func = static_intricacy_06_reset_db_obj_func;
        g_model_intricacy_06->bobj_reset_func = static_intricacy_06_reset_db_batch_obj_func;
        g_model_intricacy_06->obj_set_func = static_intricacy_06_set_obj_func;
        g_model_intricacy_06->pri_key_set_func = static_intricacy_06_pri_key_set_func;
        g_model_intricacy_06->update_func = static_intricacy_06_update;
        g_model_intricacy_06->fullscan_func = static_intricacy_06_fullscan_func;
        g_model_intricacy_06->getfield_bykey_func = static_intricacy_06_get_field_by_key_func;
        g_model_intricacy_06->malloc_primary_key_func = static_intricacy_06_primary_key_malloc_func;
        g_model_intricacy_06->free_primary_key_func = static_intricacy_06_primary_key_free_func;
        if (g_model_intricacy_06->conflict_size == 0) {
            g_model_intricacy_06->conflict_size = INTRICACY_06_HASH_CONFILICT_SIZE;
        }
    }
    return 0;
}
