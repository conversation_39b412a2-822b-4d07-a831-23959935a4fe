
#include "kpi_mem_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
// #include "cpu_cycles.h"

p_static_schema_model_t model;

status_t static_kpi_mem_create_db_spec_conn_obj_func(db_object_type object_type, db_connect_t conn, db_object* object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    ret = db_create_kpi_mem_obj_specific_conn(object_type, conn, &obj);
    CHECK_OK_RET(ret, "Create kpi spec conn object.");

    *object = obj;
    return ret;
}

status_t static_kpi_mem_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                       db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_kpi_mem_obj(obj_type, conn_type, &obj);
    CHECK_OK_RET(ret, "Create kpi object.");

    *object = obj;
    return ret;
}

void static_kpi_mem_release_db_obj_func(db_object object)
{
    db_release_kpi_mem_object(object);
}

status_t static_kpi_mem_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_kpi_mem_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_kpi_mem_reset_db_batch_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_kpi_mem_batch_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_kpi_mem_set_obj_func(db_object object, uint64_t index, uint32_t array,
                                 uint32_t thread_special_num)
{
    status_t ret = 0;
   
    kpi_mem_struct_t obj_struct = {
        .componentID = ((index) & ((uint16_t)~0)),
        .kpiID = ((index) & ((uint16_t)~0)),
        .uptime = ((index) & ((uint64_t)~0)),
        .objectID = ((index) & ((uint32_t)~0)),
        .subObjectID = ((index) & ((uint16_t)~0)),
        .slot = (index & ((uint16_t)~0)),
        .cpu = (index & ((uint8_t)~0)),
        .valueType = (index & ((uint8_t)~0)),
        .value = (index & ((uint64_t)~0))};

set_again:
    ret = db_set_kpi_mem_all_fields(object, &obj_struct, sizeof(kpi_mem_struct_t));
    if (ret == STATUS_QUEUE_EMPTY)
    {
        usleep(50);
        goto set_again;
    }
    CHECK_OK_RET(ret, "db_set_kpi_all_fields failed.");

    return ret;
}

status_t static_kpi_mem_primary_key_malloc_func(void **key)
{
    *key = malloc(sizeof(db_kpi_mem_key_t));
    return 0;
}

void static_kpi_mem_primary_key_free_func(void *key)
{
    free(key);
}

status_t static_kpi_mem_struct_data_malloc_func(void **data, uint32_t *length)
{
    *length = sizeof(kpi_mem_struct_t);
    *data = malloc(*length);
    return 0;
}

void static_kpi_mem_struct_data_free_func(void *data)
{
    free(data);
}

status_t static_kpi_mem_key_set_func(db_object object, uint64_t index, uint32_t thread_special_num,
                                 void *pri_key)
{
    return 0;
}


status_t static_kpi_mem_get_field_func(db_object object, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    kpi_mem_struct_t read_data;
    ret = db_get_kpi_mem_all_fields(object, (void *)&read_data, sizeof(kpi_mem_struct_t));
    CHECK_OK_RET(ret, "Get object data failed.");
    return ret;
}



status_t static_kpi_mem_register_schema_model(void *schema_model)
{
    model = (p_static_schema_model_t)(schema_model);

    if (model->schema_json_file_name == NULL)
    {
        model->schema_json_file_name = "kpi_mem";
        model->obj_create_spec_conn_func = static_kpi_mem_create_db_spec_conn_obj_func;
        model->obj_create_func = static_kpi_mem_create_db_obj_func;
        model->obj_release_func = static_kpi_mem_release_db_obj_func;
        model->obj_reset_func = static_kpi_mem_reset_db_obj_func;
        model->bobj_reset_func = static_kpi_mem_reset_db_batch_obj_func;
        model->obj_set_func = static_kpi_mem_set_obj_func;
        model->key_set_func = static_kpi_mem_key_set_func;
        model->malloc_primary_key_func = static_kpi_mem_primary_key_malloc_func;
        model->free_primary_key_func = static_kpi_mem_primary_key_free_func;
        model->malloc_struct_data_func = static_kpi_mem_struct_data_malloc_func;
        model->free_struct_data_func = static_kpi_mem_struct_data_free_func;
    }
    return 0;
}
