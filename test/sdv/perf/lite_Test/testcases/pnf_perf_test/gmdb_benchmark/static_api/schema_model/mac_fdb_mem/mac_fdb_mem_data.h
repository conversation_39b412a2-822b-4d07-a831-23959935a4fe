#ifndef __MAC_FDB_MEM_DATA_H__
#define __MAC_FDB_MEM_DATA_H__

#include "db_mac_fdb_mem.h"
#include "gm_errno.h"
#include "type.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"

status_t static_mac_fdb_mem_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                                    db_object *object);
void static_mac_fdb_mem_release_db_obj_func(db_object object);
status_t static_mac_fdb_mem_reset_db_obj_func(db_object object, db_object_type obj_type);
status_t static_mac_fdb_mem_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array,
                                              uint32_t thread_special_num);
status_t static_mac_fdb_mem_reset_db_batch_obj_func(db_object object, db_object_type obj_type);

status_t static_mac_fdb_mem_register_schema_model(void *schema_model);
#endif
