
#include "intricacy_00_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "cpu_cycles.h"
p_static_schema_model_t g_model_intricacy_00;

status_t static_intricacy_00_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_intricacy_00_obj(obj_type, conn_type, &obj);
    CHECK_OK_RET(ret, "Create intricacy_00 object.");

    *object = obj;
    return ret;
}

status_t static_intricacy_00_create_db_obj_spec_conn_func(db_object_type obj_type, db_connect_t conn, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_intricacy_00_obj_specific_conn(obj_type, conn, &obj);
    CHECK_OK_RET(ret, "Create intricacy_00 spec conn object.");

    *object = obj;
    return ret;
}

void static_intricacy_00_release_db_obj_func(db_object object)
{
    db_release_intricacy_00_object(object);
}

status_t static_intricacy_00_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_intricacy_00_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_intricacy_00_reset_db_batch_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_intricacy_00_batch_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_intricacy_00_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_special_num)
{
    status_t ret = 0;

    ret = db_set_intricacy_00_cid(object, (uiVrIndex & (uint64_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F1(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F2(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F3(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F4(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F5(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F6(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F7(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F8(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F9(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F10(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F11(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F12(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F13(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F14(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F15(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F16(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F17(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F18(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F19(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F20(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F21(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F22(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F23(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F24(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F25(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F26(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F27(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F28(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F29(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F30(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F31(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F32(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F33(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F34(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F35(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F36(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F37(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F38(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F39(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F40(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F41(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F42(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F43(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F44(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F45(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F46(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F47(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F48(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F49(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F50(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F51(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F52(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F53(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F54(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F55(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F56(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F57(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F58(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F59(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F60(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F61(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F62(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F63(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F64(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F65(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F66(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F67(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F68(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F69(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F70(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F71(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F72(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F73(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F74(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F75(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F76(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F77(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F78(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F79(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F80(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F81(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F82(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F83(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F84(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F85(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F86(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F87(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F88(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F89(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F90(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F91(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F92(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F93(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F94(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F95(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F96(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F97(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F98(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F99(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F100(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");

    return ret;
}


status_t static_intricacy_00_get_field_func(db_object object, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    uint32_t read_data_u32;
    uint64_t read_data_u64;
    CHECK_OK_RET(ret, "static_devm_get_field_func failed");

    ret = db_get_intricacy_00_cid(object, &read_data_u64);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F1(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F2(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F3(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F4(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F5(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F6(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F7(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F8(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F9(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F10(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F11(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F12(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F13(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F14(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F15(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F16(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F17(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F18(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F19(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F20(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F21(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F22(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F23(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F24(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F25(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F26(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F27(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F28(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F29(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F30(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F31(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F32(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F33(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F34(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F35(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F36(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F37(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F38(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F39(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F40(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F41(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F42(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F43(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F44(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F45(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F46(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F47(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F48(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F49(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F50(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F51(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F52(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F53(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F54(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F55(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F56(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F57(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F58(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F59(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F60(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F61(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F62(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F63(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F64(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F65(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F66(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F67(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F68(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F69(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F70(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F71(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F72(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F73(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F74(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F75(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F76(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F77(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F78(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F79(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F80(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F81(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F82(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F83(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F84(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F85(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F86(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F87(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F88(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F89(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F90(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F91(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F92(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F93(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F94(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F95(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F96(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F97(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F98(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F99(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");
    ret = db_get_intricacy_00_F100(object, &read_data_u32);
    CHECK_OK_RET(ret, "static_intricacy_00_get_obj_func failed");

    return ret;
    
}

status_t static_intricacy_00_pri_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_special_num, void *key_info, void *key_data)
{
    db_key_info_t *p_key_info = (db_key_info_t *)key_info;
    p_key_info->key_len = sizeof(db_intricacy_00_key_t);
    p_key_info->index_id = DB_INTRICACY_00_INTRICACY_00_PK_ID;

    db_intricacy_00_key_t *p_key_data = (db_intricacy_00_key_t *)key_data;
    p_key_data->cid = (uiVrIndex & (uint32_t)(~0));

    return 0;
}

status_t static_intricacy_00_update(db_object object, uint64_t uiVrIndex)
{
    static uint32_t factor = 0;
    factor++;
    status_t ret = STATUS_OK;

    ret = db_set_intricacy_00_F1(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F2(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F3(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F4(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F5(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F6(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F7(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F8(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F9(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F10(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F11(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F12(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F13(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F14(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F15(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F16(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F17(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F18(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F19(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F20(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F21(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F22(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F23(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F24(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F25(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F26(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F27(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F28(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F29(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F30(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F31(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F32(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F33(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F34(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F35(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F36(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F37(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F38(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F39(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F40(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F41(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F42(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F43(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F44(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F45(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F46(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F47(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F48(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F49(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F50(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F51(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F52(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F53(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F54(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F55(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F56(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F57(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F58(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F59(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F60(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F61(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F62(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F63(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F64(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F65(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F66(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F67(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F68(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F69(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F70(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F71(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F72(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F73(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F74(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F75(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F76(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F77(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F78(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F79(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F80(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F81(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F82(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F83(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F84(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F85(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F86(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F87(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F88(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F89(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F90(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F91(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F92(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F93(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F94(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F95(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F96(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F97(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F98(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F99(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");
    ret = db_set_intricacy_00_F100(object, ((uiVrIndex + factor) & (uint32_t)(~0)));
    CHECK_OK_RET(ret, "static_intricacy_00_set_obj_func failed");

    return ret;
}

status_t static_intricacy_00_fullscan_func(db_object object, uint64_t *op)
{
    status_t ret = STATUS_OK;
    db_root_iterator root_iterator;
    uint64_t scan_idx = 0;

    ret = db_create_root_iter(object, NULL, NULL, &root_iterator);
    CHECK_OK_RET(ret, "Create root iterator failed.");

    // uint32_t scan_count = g_model_ip4forward_r21_1->conflict_size;
    // ret = db_set_root_iterator_property(root_iterator, DB_ITERATOR_SET_CACHE_COUNT, (uint8_t *)&scan_count, sizeof(uint32_t));
    // CHECK_OK_RET(ret,"db_set_root_iterator_property failed.");

    // printf(">>>>db_set_root_iterator_property:%u\n", scan_count);

    // for (ret = db_root_iterator_next(root_iterator);; ret = db_root_iterator_next(root_iterator)) {
    for (;;) {
        STATIC_TIME_DELAY_START(g_time_delay_data);
        ret = db_root_iterator_next(root_iterator);
        STATIC_TIME_DELAY_STOP(g_time_delay_data);
        if (ret == STATUS_SCAN_TO_END) {
            ret = STATUS_OK;
            break;
        }
        CHECK_OK_RET(ret, "Scan table failed");
        ret = static_intricacy_00_get_field_func(object, FIELD_CNT_ALL);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Get field failed");
            break;
        }
        scan_idx++;
    }
    *op = scan_idx;
    return ret;
}

status_t static_intricacy_00_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_special_num, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_intricacy_00_key_t key_data = {0};
    key_info.index_id = DB_INTRICACY_00_INTRICACY_00_PK_ID;
    key_info.key_len = sizeof(db_intricacy_00_key_t);
    key_data.cid = (uint64_t)(uiVrIndex & ((uint64_t)~0));
    STATIC_TIME_DELAY_START(g_time_delay_data);
    ret = db_read_obj(object, &key_info, &key_data);
    STATIC_TIME_DELAY_STOP(g_time_delay_data);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "db_read_obj");
        return ret;
    }
    ret = static_intricacy_00_get_field_func(object, read_field_cnt);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "static_if_get_field_func");
        return ret;
    }
    return ret;
}

status_t static_intricacy_00_primary_key_malloc_func(void **key)
{
    *key = malloc(sizeof(db_intricacy_00_key_t));
    return 0;
}

void static_intricacy_00_primary_key_free_func(void *key)
{
    free(key);
}


status_t static_intricacy_00_register_schema_model(void *schema_model)
{
    g_model_intricacy_00 = (p_static_schema_model_t)(schema_model);

    if (g_model_intricacy_00->schema_json_file_name == NULL) {
        g_model_intricacy_00->schema_json_file_name = "intricacy_00";
        g_model_intricacy_00->obj_create_func = static_intricacy_00_create_db_obj_func;
        g_model_intricacy_00->obj_create_spec_conn_func = static_intricacy_00_create_db_obj_spec_conn_func;
        g_model_intricacy_00->obj_release_func = static_intricacy_00_release_db_obj_func;
        g_model_intricacy_00->obj_reset_func = static_intricacy_00_reset_db_obj_func;
        g_model_intricacy_00->bobj_reset_func = static_intricacy_00_reset_db_batch_obj_func;
        g_model_intricacy_00->obj_set_func = static_intricacy_00_set_obj_func;
        g_model_intricacy_00->pri_key_set_func = static_intricacy_00_pri_key_set_func;
        g_model_intricacy_00->update_func = static_intricacy_00_update;
        g_model_intricacy_00->fullscan_func = static_intricacy_00_fullscan_func;
        g_model_intricacy_00->getfield_bykey_func = static_intricacy_00_get_field_by_key_func;
        g_model_intricacy_00->malloc_primary_key_func = static_intricacy_00_primary_key_malloc_func;
        g_model_intricacy_00->free_primary_key_func = static_intricacy_00_primary_key_free_func;
        if (g_model_intricacy_00->conflict_size == 0) {
            g_model_intricacy_00->conflict_size = INTRICACY_00_HASH_CONFILICT_SIZE;
        }
    }
    return 0;
}
