#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "db_wrapper.h"
#include "db_wrapper_private.h"
#include <pthread.h>
#include <errno.h>
#include <getopt.h>

#include "db_ip4foward.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"
#include "gm_errno.h"
#include "securec.h"
#include "tools.h"
#include "static_sub_client.h"
#include "static_schema_model.h"
#include "type.h"
#include "db_if_r21_1.h"

#ifdef EPOLL
#include "static_epoll_sub.h"
db_connect_t g_sub_conn[SC_SUB_CONN_MAX_NUM];
#endif

pthread_t g_sub_wait_thread_id;
static pthread_cond_t g_cond;
static pthread_mutex_t g_mutex;
static db_bool g_sub_should_end = false;
static int32_t g_cond_wait_retcode = 0;
static volatile uint32_t g_callback_count = 0;
static volatile uint32_t g_push_obj_count = 0;
static uint32_t g_event_count[10] = { 0 };
static static_sub_client_config_t g_static_sub_client_conf;
static static_sub_client_ops_t g_static_sub_client_ops;
static static_sub_conf_t g_static_sub_conf;
unsigned int g_sub_id[SC_SUB_MAX_SUB_CNT] = {0};

#ifdef MEM_CALC
static char *g_process_name = "static_sub_clie";
static int g_heap_mem_size = 0;
static int g_heap_mem_size1 = 0;
#endif

#define BEGIN_GET_MEM()                                  \
do  {                                                           \
    g_heap_mem_size = st_mem_opt_get_cur_heap_memory(g_process_name);  \
    printf("[Test][Info] ==== start to calc current operation occupy heap memory:%d B.\n", g_heap_mem_size);   \
}                                                               \
while (0)

#define END_GET_MEM()                                   \
do  {                                                           \
    g_heap_mem_size1 = st_mem_opt_get_cur_heap_memory(g_process_name);  \
    printf("[Test][Info] ==== end to calc current operation occupy heap memory start:%d end:%d cost:%u B.\n",  \
        g_heap_mem_size, g_heap_mem_size1, g_heap_mem_size1 - g_heap_mem_size);   \
}                                                               \
while (0)


static void static_sub_client_usage(char *basename)
{
    printf("\nUsage : %s -s schema_index -m mode -t time -e event_type -p push_op -c trigger_count \
-b batch_notify_flag -L lpm_sub_ip -N lpm_sub_ip_num -d log_level -k conn_min_num -K conn_max_num\n",
           basename);
    printf("\n[Option]:\n");
    printf("\n              -h              show help message\n");
    printf("\n              -s              set subscribe table schema index\n");
    printf("\n              -t              client subscribe run duration, in seconds\n");
    printf("\n              -m              set communication mode, mode:= { 0: default ( usocket ), 2: shm }\n");
    printf("\n              -e              add subscribe event type. event_type:= { 0x1: inserted, 0x2: modified, 0x4: deleted, 0x8: initial loaded, 0x10: drop table, 0x1f: all events }\n");
    printf("\n              -p              add subscribe push operation. push_op:= { 0x100: current data, 0x200: full rollback, 0x400: old data, 0x700: all operation }\n");
    printf("\n              -b              set batch notifiy, batch_notify_flag:= { 0 : false, 1: true }\n");
    printf("\n              -c              subscribe total object count\n");
    printf("\n              -L              add LPM sub ip, ip:= xxx.xxx.xxx.xxx,   (eg, ************)\n");
    printf("\n              -N              auto gen N sub ips for LPM subcribe, will not use ips added by -L option\n");
    printf("\n              -d              log level, default 0, debug 1\n");
    printf("\n              -g              subscribe channel size\n");
    printf("\n              -n              compare type. node_name\n");
    printf("\n              -f              compare type. field_name\n");
    printf("\n              -v              compare type. value(max int64_t)\n");
    printf("\n              -l              compare type. value_len\n");
    printf("\n              -F              compare type file\n");
    printf("\n              -I              set sub client ID for result print\n");
}
#define DB_SUB_SERVER_LOCATOR "shm:unix_emserver"
static void static_sub_client_init_config(p_static_sub_client_config_t conf)
{
    (void)memset_s(conf, sizeof(static_sub_client_config_t), 0, sizeof(static_sub_client_config_t));
    (void)strncpy_s(conf->server_locator, SERVER_LOCATOR_MAX_LEN, DB_SUB_SERVER_LOCATOR,
                    SERVER_LOCATOR_MAX_LEN);
    conf->schema_index = LITE_SCHEMA_IDX_INVALID;
    conf->event_mask = SC_SUB_EVENT_MASK_DEFAULT;
    conf->push_mask = SC_SUB_PUSH_OP_DEFAULT;
    conf->trigger_count = SC_SUB_OBJ_COUNT_DEFAULT;
    conf->time = SC_SUB_TIME_SECONDS_DEFAULT;
    conf->is_batch_notified = SC_SUB_IS_BATCH_NOTIFY_DEFAULT;
    conf->log_level = 0;
    conf->sub_channel_size = 10240;
    conf->conn_min_num = 2;
    conf->conn_max_num = 5;
    memset_s(conf->comp_type_node_name, FIELD_NAME_MAX_LEN, '\0', FIELD_NAME_MAX_LEN);
    memset_s(conf->comp_type_field_name, FIELD_NAME_MAX_LEN, '\0', FIELD_NAME_MAX_LEN);
    memset_s(conf->comp_type_value, FIELD_VALUE_MAX_LEN, '\0', FIELD_VALUE_MAX_LEN);
    conf->comp_type_value_len = 0;
    memset_s(conf->comp_type_file, COMPARE_TYPE_FILE_NAME_MAX_LEN, '\0', COMPARE_TYPE_FILE_NAME_MAX_LEN);
    conf->client_id = 0;
}

uint32_t g_cfgSubConnId = 0;
static int static_sub_client_set_config(int argc, char *argv[], p_static_sub_client_config_t conf)
{
    const char *optstring = "hm:t:e:p:s:c:b:L:N:d:g:k:K:i:n:f:v:l:F:I:";

    static struct option long_options[] = {
        { "help", no_argument,       NULL, 'h' },
        { "comm", required_argument, NULL, 'm' },
        { "time", required_argument, NULL, 't' },
        { "event", required_argument, NULL, 'e' },
        { "push", required_argument, NULL, 'p' },
        { "schema", required_argument, NULL, 's' },
        { "count", required_argument, NULL, 'c' },
        { "batch", required_argument, NULL, 'b' },
        { "LPM", required_argument, NULL, 'L' },
        { "Num", required_argument, NULL, 'N' },
        { "debug", required_argument, NULL, 'd' },
        { "channel", required_argument, NULL, 'g' },
        { "conn_min_num", required_argument, NULL, 'k' },
        { "conn_max_num", required_argument, NULL, 'K' },
        { "subcribe id", required_argument, NULL, 'i' },
        { "comp_node", required_argument, NULL, 'n' },
        { "comp_field", required_argument, NULL, 'f' },
        { "comp_value", required_argument, NULL, 'v' },
        { "comp_value_len", required_argument, NULL, 'l' },
        { "comp_file", required_argument, NULL, 'F' },
        { "client_id", required_argument, NULL, 'I' },
        { NULL,           0,                 NULL, 0 }
    };

    int ret = STATUS_OK;
    int arg_num = 0;
    uint32_t opt;
    int option_index = 0;
    while ((opt = getopt_long(argc, argv, optstring, long_options, &option_index)) != -1) {
        switch (opt) {
            case 'm':
                arg_num = atoi(optarg);
                if (arg_num == 0) {
                    strncpy_s(conf->server_locator, SERVER_LOCATOR_MAX_LEN, DB_SUB_SERVER_LOCATOR,
                              SERVER_LOCATOR_MAX_LEN);
                } else if (arg_num == 2) {
                    strncpy_s(conf->server_locator, SERVER_LOCATOR_MAX_LEN, DMDB_DEFAULT_SHM_SERVER_LOCATOR,
                              SERVER_LOCATOR_MAX_LEN);
                } else {
                    ret = -1;
                    LOG_ERROR(ret, "Sub invalid communication mode %d.", arg_num);
                    return ret;
                }
                break;

            case 'c':
                conf->trigger_count = atoi(optarg);
                break;

            case 'g':
                conf->sub_channel_size = atoi(optarg);
                break;

            case 't':
                conf->time = atoi(optarg);
                break;

            case 'e':
                sscanf(optarg, "%x", &arg_num);
                conf->event_mask = conf->event_mask | ((uint32_t)arg_num);
                break;

            case 'p':
                sscanf(optarg, "%x", &arg_num);
                conf->push_mask = conf->push_mask | ((uint32_t)arg_num);
                break;

            case 's':
                conf->schema_index = atoi(optarg);
                break;

            case 'b':
                conf->is_batch_notified = ((0 == atoi(optarg)) ? 0x0 : 0x1);
                break;

            case 'd':
                conf->log_level = ((0 == atoi(optarg)) ? 0x0 : 0x1);
                break;

            case 'N':
                arg_num = atoi(optarg);
                if (arg_num > SC_SUB_MAX_SUB_CNT) {
                    ret = -1;
                    LOG_ERROR(ret, "Sub ip count %d exceeds maximum %d.", arg_num, SC_SUB_MAX_SUB_CNT);
                    return ret;
                }
                g_static_sub_conf.sub_count = arg_num;
                for (int loop = 0; loop < arg_num; loop++) {
                    g_sub_id[loop] = loop;
                }
                break;

            case 'L':
		g_cfgSubConnId = atoi(optarg);
                break;

            case 'k':
                arg_num = atoi(optarg);
                if (arg_num < 0) {
                    ret = -1;
                    LOG_ERROR(ret, "invalid connection pool min num %s", optarg);
                    return ret;
                }
                conf->conn_min_num = arg_num;
                break;
            
            case 'K':
                arg_num = atoi(optarg);
                if (arg_num < 0 || arg_num < conf->conn_min_num || arg_num > SC_SUB_CONN_MAX_NUM) {
                    ret = -1;
                    LOG_ERROR(ret, "invalid connection pool max num %s", optarg);
                    return ret;
                }
                conf->conn_max_num = arg_num;
                break;
            
            case 'i':
                // for if table
                arg_num = atoi(optarg);
                if (arg_num < 0 || arg_num > SC_SUB_MAX_SUB_ID) {
                    ret = -1;
                    LOG_ERROR(ret, "invalid sub id %s, expect (%u, %u)", optarg, 0, SC_SUB_MAX_SUB_ID);
                    return ret;
                }
                if (g_static_sub_conf.sub_count >= SC_SUB_MAX_SUB_CNT) {
                    ret = -1;
                    LOG_ERROR(ret, "too many sub id, maximum:%u", SC_SUB_MAX_SUB_CNT);
                    return ret;
                }
                g_sub_id[g_static_sub_conf.sub_count] = (unsigned int)arg_num;
                g_static_sub_conf.sub_count += 1;
                break;

            case 'n':
                strcpy_s(conf->comp_type_node_name, FIELD_NAME_MAX_LEN, optarg);
                break;
            case 'f':
                strcpy_s(conf->comp_type_field_name, FIELD_NAME_MAX_LEN, optarg);
                break;
            case 'v':
                strcpy_s(conf->comp_type_value, FIELD_VALUE_MAX_LEN, optarg);
                break;
            case 'l':
                conf->comp_type_value_len = atoi(optarg);
                break;
            case 'F':
                strcpy_s(conf->comp_type_file, COMPARE_TYPE_FILE_NAME_MAX_LEN, optarg);
                break;
            case 'I':
                conf->client_id = atoi(optarg);
                break;

            case 'h':
            default:
                static_sub_client_usage(argv[0]);
                break;
        }
    }
    return STATUS_OK;
}

static void static_sub_client_cal_ops_start(struct timeval *nowtime)
{
    if (g_static_sub_client_ops.is_started) {
        return;
    }
    g_static_sub_client_ops.triggernum = 0;
    g_static_sub_client_ops.start = *nowtime;
    g_static_sub_client_ops.is_started = 1;
}

void static_sub_client_cal_ops_stop(uint32_t callback_count, double time_cost)
{
    g_static_sub_client_ops.triggernum = callback_count;
    g_static_sub_client_ops.tcost = time_cost;
    // TimeFlow(g_static_sub_client_ops.start, g_static_sub_client_ops.stop, g_static_sub_client_ops.tcost);
    if (g_static_sub_client_ops.tcost == 0) {
        LOG_ERROR(-1, "tcost equals to zero");
        return;
    }
    g_static_sub_client_ops.ops = g_static_sub_client_ops.triggernum / g_static_sub_client_ops.tcost;
#ifdef DELAY
    g_static_sub_client_ops.delay_avg = g_static_sub_client_ops.delay_total / g_static_sub_client_ops.delay_count;
#endif
}

static void static_sub_client_reset_data()
{
    g_sub_should_end = false;
    g_callback_count = 0;
    g_push_obj_count = 0;
    g_static_sub_client_ops.is_started = false;
    (void)memset_s(g_event_count, sizeof(g_event_count), 0, sizeof(g_event_count));
    (void)memset_s(&g_static_sub_client_ops, sizeof(g_static_sub_client_ops), 0, sizeof(g_static_sub_client_ops));
}

static void static_sub_client_print_data()
{
    printf("\nTotal push object num     : %u", g_push_obj_count);
    printf("\nOBJ_INSERTED     : %u times.", g_event_count[0]);
    printf("\nOBJ_MODIFIED     : %u times.", g_event_count[1]);
    printf("\nOBJ_DELETED      : %u times.", g_event_count[2]);
    printf("\nOBJ_INITIAL_LOAD : %u times.", g_event_count[3]);
    printf("\nOBJ_DROP_TABLE   : %u times.\r\n", g_event_count[4]);
}

static status_t static_get_filed_timestamp(db_object object, uint32_t schema_index, struct timeval *field_timestamp)
{
    status_t ret = STATUS_OK;
    uint32_t get_field_uint32;
    switch (schema_index) {
    case LITE_SCHEMA_IDX_IF_R21_1:
        ret = db_get_if_r21_1_ipv4mss(object, &get_field_uint32);
        CHECK_OK_RET(ret, "db_get_if_r21_1_ipv4mss");
        field_timestamp->tv_sec = get_field_uint32;
        ret = db_get_if_r21_1_ipv6mss(object, &get_field_uint32);
        CHECK_OK_RET(ret, "db_get_if_r21_1_ipv6mss");
        field_timestamp->tv_usec = get_field_uint32;
        break;
    default:
        LOG_ERROR(1, "not support current schema.");
        break;
    }
    return ret;
}


void static_sub_callback_new(char *table_name, db_sub_obj_info *obj_info,
                         uint32_t obj_info_num, void *user_data)
{
    (void)pthread_mutex_lock(&g_mutex);
    if (g_sub_should_end) {
        (void)pthread_mutex_unlock(&g_mutex);
        return;
    }

    uint32_t i;
    db_sub_event_type event_type;
    struct timeval nowtime;
    gettimeofday(&nowtime, DB_NULL);
    // static_sub_client_cal_ops_start(&nowtime);
    if (g_static_sub_client_ops.is_started == 0) {
        g_static_sub_client_ops.triggernum = 0;
        g_static_sub_client_ops.start = nowtime;
        g_static_sub_client_ops.is_started = 1;
#ifdef DELAY
        g_static_sub_client_ops.delay_min = 10.0;
        g_static_sub_client_ops.delay_max = 0;
        g_static_sub_client_ops.delay_total = 0;
        g_static_sub_client_ops.delay_count = 0;
#endif
    }
    g_static_sub_client_ops.stop = nowtime;

    g_callback_count++;
    for (i = 0; i < obj_info_num; i++) {
        g_push_obj_count++;
        event_type = obj_info[i].event_type;
        if (event_type == OBJ_INSERTED) {
            g_event_count[0]++;
        } else if (event_type == OBJ_MODIFIED) {
            g_event_count[1]++;
        } else if (event_type == OBJ_DELETED) {
            g_event_count[2]++;
        } else if (event_type == OBJ_INITIAL_LOAD) {
            g_event_count[3]++;
        } else {
            g_event_count[4]++;
        }
#ifdef DELAY
        if(obj_info[i].object != NULL) {
            db_object object = obj_info[i].object;
            float tcost;
            status_t ret;
            struct timeval get_timeval;
            if (event_type != OBJ_DELETED && event_type != OBJ_DROP_TABLE && event_type != OBJ_AGED) {
                ret = static_get_filed_timestamp(object, g_static_sub_client_conf.schema_index, &get_timeval);
                if (ret != STATUS_OK) {
                    printf("get pk field failed, ret:%d\n", ret);
                    break;
                }
            } else if (event_type == OBJ_DELETED) {
                object = obj_info[i].old_obj;
                if (object != NULL) {  // ֻ��PUSH_CURR_DATAʱ�򣬸�ֵΪ��
                    ret = static_get_filed_timestamp(object, g_static_sub_client_conf.schema_index, &get_timeval);
                    if (ret != STATUS_OK) {
                        printf("get pk field failed, ret:%d\n", ret);
                        break;
                    }
                }
            }
            TimeFlow(get_timeval, nowtime, tcost);
            // printf("start: %d.%d; end: %d.%d; cost: %.8f\n", get_timeval.tv_sec, get_timeval.tv_usec, nowtime.tv_sec, nowtime.tv_usec, tcost);
            g_static_sub_client_ops.delay_total += tcost;
            g_static_sub_client_ops.delay_count++;
            if (tcost > g_static_sub_client_ops.delay_max) {
                g_static_sub_client_ops.delay_max = tcost;
            }
            if (tcost < g_static_sub_client_ops.delay_min) {
                g_static_sub_client_ops.delay_min = tcost;
            }
        }
#endif
    }

    if ((g_static_sub_client_conf.trigger_count > 0) &&
        (g_static_sub_client_conf.trigger_count <= g_push_obj_count)) {  // g_callback_count
        // printf("\nSub callback reach max trigger count %d.", g_static_sub_client_conf.trigger_count);
        g_sub_should_end = true;
        (void)pthread_cond_signal(&g_cond);
    }
    
    (void)pthread_mutex_unlock(&g_mutex);
    return;
}


void static_sub_callback_new_subMerged(char *table_name, db_object obj, db_sub_event_type event_type, void *user_data)
{
    (void)pthread_mutex_lock(&g_mutex);
    if (g_sub_should_end) {
        (void)pthread_mutex_unlock(&g_mutex);
        return;
    }

    uint32_t i;

    struct timeval nowtime;
    gettimeofday(&nowtime, DB_NULL);
    // static_sub_client_cal_ops_start(&nowtime);
    if (g_static_sub_client_ops.is_started == 0) {
        g_static_sub_client_ops.triggernum = 0;
        g_static_sub_client_ops.start = nowtime;
        g_static_sub_client_ops.is_started = 1;
#ifdef DELAY
        g_static_sub_client_ops.delay_min = 10.0;
        g_static_sub_client_ops.delay_max = 0;
        g_static_sub_client_ops.delay_total = 0;
        g_static_sub_client_ops.delay_count = 0;
#endif
    }
    g_static_sub_client_ops.stop = nowtime;

    g_callback_count++;
    for (i = 0; i < 1; i++) {
        g_push_obj_count++;
        if (event_type == OBJ_INSERTED) {
            g_event_count[0]++;
        } else if (event_type == OBJ_MODIFIED) {
            g_event_count[1]++;
        } else if (event_type == OBJ_DELETED) {
            g_event_count[2]++;
        } else if (event_type == OBJ_INITIAL_LOAD) {
            g_event_count[3]++;
        } else {
            g_event_count[4]++;
        }
#ifdef DELAY
        if (obj != NULL) {
            db_object object = obj;
            float tcost;
            status_t ret;
            struct timeval get_timeval;
            if (event_type != OBJ_DELETED && event_type != OBJ_DROP_TABLE && event_type != OBJ_AGED) {
                ret = static_get_filed_timestamp(object, g_static_sub_client_conf.schema_index, &get_timeval);
                if (ret != STATUS_OK) {
                    printf("get pk field failed, ret:%d\n", ret);
                    break;
                }
            } else if (event_type == OBJ_DELETED) {
                object = obj;  // deleteֻ��old
                if (object != NULL) {  // ֻ��PUSH_CURR_DATAʱ�򣬸�ֵΪ��
                    ret = static_get_filed_timestamp(object, g_static_sub_client_conf.schema_index, &get_timeval);
                    if (ret != STATUS_OK) {
                        printf("get pk field failed, ret:%d\n", ret);
                        break;
                    }
                }
            }
            TimeFlow(get_timeval, nowtime, tcost);
            /* printf("start: %d.%d; end: %d.%d; cost: %.8f\n", get_timeval.tv_sec, get_timeval.tv_usec,
                nowtime.tv_sec, nowtime.tv_usec, tcost); */
            g_static_sub_client_ops.delay_total += tcost;
            g_static_sub_client_ops.delay_count++;
            if (tcost > g_static_sub_client_ops.delay_max) {
                g_static_sub_client_ops.delay_max = tcost;
            }
            if (tcost < g_static_sub_client_ops.delay_min) {
                g_static_sub_client_ops.delay_min = tcost;
            }
        }
#endif
    }

    if ((g_static_sub_client_conf.trigger_count > 0) &&
        (g_static_sub_client_conf.trigger_count <= g_push_obj_count)) {  // g_callback_count
        // printf("\nSub callback reach max trigger count %d.", g_static_sub_client_conf.trigger_count);
        g_sub_should_end = true;
        (void)pthread_cond_signal(&g_cond);
    }
    
    (void)pthread_mutex_unlock(&g_mutex);
    return;
}

static status_t static_sub_client_set_object_by_config(p_static_sub_client_config_t conf, db_object object, uint32_t idx)
{
    int ret;

    // ���ö���ͨ������ͨ����С
    // db_sub_channel_conf channel_conf;
    // channel_conf.count = 1;
    // channel_conf.size = conf->sub_channel_size;
    // ret = db_set_sub_channel_conf(object, &channel_conf);
    // if (ret != STATUS_OK) {
    // LOG_ERROR(ret, "Sub set sub channel conf failed.");
    // return ret;
    // }

    // �����������ı�־
#ifndef EPOLL    
    ret = db_set_batch_sub_conf(object, &(conf->is_batch_notified));
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub set batch sub conf failed.");
        return ret;
    }
#endif    

    ret = db_sub_add_event(object, conf->event_mask);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub add event failed.");
        return ret;
    }

#ifndef SUB_MERGE
    if (conf->push_mask != SC_SUB_PUSH_OP_DEFAULT) {
        ret = db_sub_add_push_op(object, conf->push_mask);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Sub add push op failed.");
            return ret;
        }
    }
#endif

#ifndef EPOLL
    ret = db_sub_set_callback_new(object, static_sub_callback_new, NULL);
#else
#ifndef SUB_MERGE
    ret = db_sub_reg_callback(object, static_sub_callback_new, NULL);
#else  // sub_merge
    ret = db_sub_reg_status_merge_callback(object, static_sub_callback_new_subMerged, NULL);
#endif
#endif 
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set sub callback function failed.");
        return ret;
    }
    // db_sub_add_field from -n -f -v
    if (*conf->comp_type_node_name != '\0') {
        if (strcmp(conf->comp_type_value, "null") == 0 || strcmp(conf->comp_type_value, "NULL") == 0) {  // �ֶ�ȫ����
            ret =
                db_sub_add_field(object, conf->comp_type_node_name, conf->comp_type_field_name, NULL, 0, SUB_CMP_EQUAL);
	    printf("[Test][Info][sub_client] db_sub_add_field node_name: %s, field_name: %s, all_event.\n",
                conf->comp_type_node_name, conf->comp_type_field_name);
        } else {
            uint32_t comp_value = atoi(conf->comp_type_value);
            if (conf->comp_type_value_len == 0) {
                conf->comp_type_value_len = sizeof(uint32_t);   // Compatible with old command(no -l)
            }
            ret = db_sub_add_field(object, conf->comp_type_node_name, conf->comp_type_field_name, (void*)&comp_value,
                conf->comp_type_value_len, SUB_CMP_EQUAL);
        printf("[Test][Info][sub_client] db_sub_add_field node_name: %s, field_name: %s, value: %s.\n",
            conf->comp_type_node_name, conf->comp_type_field_name, conf->comp_type_value);
        }
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "db_sub_add_field function failed.");
            return ret;
        }
    }
    // db_sub_add_field from file -F
    if (*conf->comp_type_file != '\0') {
        FILE* fp;
        unsigned int i;
        char node_name[FIELD_NAME_MAX_LEN], filed_name[FIELD_NAME_MAX_LEN], value[FIELD_VALUE_MAX_LEN];
        char tmp[FIELD_VALUE_MAX_LEN];
        uint32_t value_len;
        if ((fp = fopen(conf->comp_type_file, "r")) == NULL) {
            printf("can not open sub compare type file\n");
            return -1;
        }
        i = 0;
        fscanf(fp, "%s %s %s %s", node_name, filed_name, value, tmp);   // ȥ����һ��
        while (fscanf(fp, "%s %s %s %u", node_name, filed_name, value, &value_len) == 4) {
            if (strcmp(value, "null") == 0 || strcmp(value, "NULL") == 0) {  // �ֶ�ȫ����
                ret = db_sub_add_field(object, node_name, filed_name, NULL, 0, SUB_CMP_EQUAL);
                printf("[Test][Info][sub_client] db_sub_add_field node_name: %s, field_name: %s, all_event.\n",
                    node_name, filed_name);
            } else {
                uint32_t comp_value = atoi(value);
                ret = db_sub_add_field(
                    object, node_name, filed_name, (void*)&comp_value, sizeof(uint32_t), SUB_CMP_EQUAL);
                printf("[Test][Info][sub_client] db_sub_add_field node_name: %s, field_name: %s, value: %s.\n",
                    node_name, filed_name, value);
            }
            if (ret != STATUS_OK) {
                LOG_ERROR(ret, "db_sub_add_field function failed.");
                return ret;
            }
            i++;
        }
        printf("\n[Test][Info][sub_client] db_sub_add_field %u times from -F %s.\n", i, conf->comp_type_file);
    }
    return STATUS_OK;
}

static status_t static_sub_client_create_sub_object(db_object_type obj_type, db_object *object)
{
    if (object == NULL) {
        return STATUS_NULL_POINTER;
    }
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    uint32_t schema_index = g_static_sub_client_conf.schema_index;
    p_static_schema_model_t schema_model = static_get_schema_model_by_index(schema_index);
    if (schema_model == NULL) {
        LOG_ERROR(STATUS_NULL_POINTER, "The schema index %u is NULL", schema_index);
        return STATUS_NULL_POINTER;
    }
    ret = schema_model->obj_create_func(obj_type, SYNC_MODE, &obj);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Create sub object failed");
        return ret;
    }
    *object = obj;
    return ret;
}

static status_t static_sub_client_reset_sub_object(db_object_type obj_type, db_object object)
{
    if (object == NULL) {
        return STATUS_NULL_POINTER;
    }
    status_t ret = STATUS_OK;
    uint32_t schema_index = g_static_sub_client_conf.schema_index;
    p_static_schema_model_t schema_model = static_get_schema_model_by_index(schema_index);
    if (schema_model == NULL) {
        LOG_ERROR(STATUS_NULL_POINTER, "The schema index %u is NULL", schema_index);
        return STATUS_NULL_POINTER;
    }
    ret = schema_model->obj_reset_func(object, obj_type);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "reset sub object failed");
        return ret;
    }
    return ret;
}

static void static_sub_client_release_sub_object(db_object object)
{
    if (object == NULL) {
        return;
    }
    
    uint32_t schema_index = g_static_sub_client_conf.schema_index;
    p_static_schema_model_t schema_model = static_get_schema_model_by_index(schema_index);
    if (schema_model == NULL) {
        LOG_ERROR(STATUS_NULL_POINTER, "The schema index %u is NULL", schema_index);
        return;
    }
    schema_model->obj_release_func(object);
}

static status_t static_sub_client_subscribe(db_object object, db_subid *sub_id, db_connect_t conn)
{
    if (object == NULL) {
        return STATUS_NULL_POINTER;
    }
    status_t ret = STATUS_OK;
    db_subid id = NULL;
#ifndef EPOLL
    id = db_subscribe(object);
#else
    ret = db_sub_with_conn(conn, object, &id);
    if (STATUS_OK != ret) {
        LOG_ERROR(ret, "db_sub_with_conn failed");
    }
    // else{
    //     printf("epoll sub sucess.\n");    
    // }
#endif   
 
    if (id == NULL) {
        ret = -1;
    }
    *sub_id = id;
    return ret;
}


static void static_sub_client_unsubscribe(db_object object, db_subid sub_id)
{
    if (sub_id == NULL) {
        return;
    }
    db_unsubscribe(object, sub_id);
}

static status_t static_sub_client_try_subscribe(db_object object, uint32_t idx, db_connect_t conn)
{
    status_t ret;

    if (g_static_sub_conf.set_sub_object_func == NULL || g_static_sub_conf.subscribe_func == NULL) {
        LOG_ERROR(-1, "Client try subscribe failed. set obj func is NULL.");
        return -1;
    }

    ret = g_static_sub_conf.set_sub_object_func(&g_static_sub_client_conf, object, idx);
    if (ret != STATUS_OK) {
        return ret;
    }

    uint32_t try_sub_cnt = 0;
    db_subid sub_id;
    do {
        if (++try_sub_cnt > SC_SUB_TRY_SUBSCRIBE_MAX_TIME) {
            break;
        }
        // static_sub_client_reset_data();
        ret = g_static_sub_conf.subscribe_func(object, &sub_id, conn);
        if (ret == STATUS_OK) {
            g_static_sub_conf.sub_id[idx] = sub_id;
            return ret;
        }
        usleep(100);
    } while (1);

    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Client try subscribe failed. Try count:%u.", try_sub_cnt);
    }
    return ret;
}

static void *static_sub_client_wait_until_reach_count_or_timeout(void *arg)
{
    struct timeval now;
    struct timespec tsp;
    (void)gettimeofday(&now, NULL);
    tsp.tv_sec = now.tv_sec;
    tsp.tv_nsec = now.tv_usec * 1000;
    tsp.tv_sec += g_static_sub_client_conf.time;

    (void)pthread_mutex_lock(&g_mutex);
    do {
        g_cond_wait_retcode = pthread_cond_timedwait(&g_cond, &g_mutex, &tsp);
    } while ((!g_sub_should_end) && (g_cond_wait_retcode != ETIMEDOUT));
    g_sub_should_end = true;

    double time_cost;
    TimeFlow(g_static_sub_client_ops.start, g_static_sub_client_ops.stop, time_cost);
    if ((g_static_sub_client_conf.sub_model)->cal_ops_func != NULL) {
        (g_static_sub_client_conf.sub_model)->cal_ops_func(((uint32_t *)&g_callback_count), (double *)&time_cost);
    }
    static_sub_client_cal_ops_stop(g_callback_count, time_cost);

    (void)pthread_mutex_unlock(&g_mutex);

    return NULL;
}

inline int SC_SUB_MALLOC(void **mem_ptr, unsigned int mem_size)
{
    if (mem_ptr == NULL) {
        return (int)STATUS_NULL_POINTER;
    }
    void *malloc_ptr;
    malloc_ptr = malloc(mem_size);
    if (malloc_ptr == NULL) {
        return (int)STATUS_NULL_POINTER;
    }
    *mem_ptr = malloc_ptr;
    return (int)0;
}

inline void SC_SUB_FREE(void *mem_ptr)
{
    if (mem_ptr == NULL) {
        return;
    }
    free(mem_ptr);
}

inline int SC_SUB_MEMSET(void *mem_ptr, int ch, unsigned int size)
{
    if (mem_ptr == NULL) {
        return STATUS_NULL_POINTER;
    }
    return (int)memset_s(mem_ptr, size, ch, size);
}

inline int SC_SUB_MEMCPY(void *dst, unsigned int dst_size, void *src, unsigned int src_size)
{
    if (dst == NULL || src == NULL) {
        return STATUS_NULL_POINTER;
    }
    return (int)memcpy_s(dst, dst_size, src, src_size);
}

int main(int argc, char *argv[])
{
    status_t ret = STATUS_OK;
    db_object unsub_obj = NULL;
    db_object sub_obj = NULL;

    g_static_sub_conf.sub_object = NULL;
    g_static_sub_conf.sub_id = NULL;
    g_static_sub_conf.sub_count = 0;
    //g_static_sub_conf.lpm_sub_info.ip_num = 0;
    //g_static_sub_conf.lpm_sub_info.sub_ip_info = NULL;
    uint32_t loop;

    ret = pthread_mutex_init(&g_mutex, NULL);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Mutex initialize failed.");
        return ret;
    }

    ret = pthread_cond_init(&g_cond, NULL);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Condition variable initialize failed.");
        (void)pthread_mutex_destroy(&g_mutex);
        return ret;
    }

    unsigned int size;
    //size = SC_SUB_MAX_SUB_CNT * sizeof(db_lpm_sub_ip_info);
    //db_lpm_sub_ip_info *ips = NULL;
    //ret = SC_SUB_MALLOC((void **)&ips, size);
    //if (ret != STATUS_OK) {
    //    LOG_ERROR(ret, "sub object malloc failed.");
    //    goto EARLY_ERROR;
    //}
    //g_static_sub_conf.lpm_sub_info.sub_ip_info = ips;
    //(void)SC_SUB_MEMSET(g_static_sub_conf.lpm_sub_info.sub_ip_info, 0, size);

    static_reset_global_schema_model();
    ret = static_register_all_schema_model();
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub reg all schema model failed.");
        goto EARLY_ERROR;
    }

    db_object_type sub_obj_type;
    db_object_type unsub_obj_type;
    g_static_sub_conf.create_sub_obj_func = static_sub_client_create_sub_object;
    g_static_sub_conf.release_sub_obj_func = static_sub_client_release_sub_object;
    g_static_sub_conf.try_sub_func = static_sub_client_try_subscribe;
    g_static_sub_conf.reset_sub_obj_func = static_sub_client_reset_sub_object;

    sub_obj_type = SUB_OBJ;
    unsub_obj_type = UNSUB_OBJ;
    g_static_sub_conf.set_sub_object_func = static_sub_client_set_object_by_config;
    g_static_sub_conf.subscribe_func = static_sub_client_subscribe;
    g_static_sub_conf.unsubscribe_func = static_sub_client_unsubscribe;
    g_static_sub_conf.print_conf_func = static_print_sub_conf;
    g_static_sub_conf.print_data_func = static_sub_client_print_data;

    static_sub_client_init_config(&g_static_sub_client_conf);
    ret = static_sub_client_set_config(argc, argv, &g_static_sub_client_conf);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set config failed.");
        goto EARLY_ERROR;
    }
    if (g_static_sub_client_conf.schema_index >= LITE_SCHEMA_IDX_INVALID) {
        LOG_ERROR(ret, "Invalid schema model index.");
        goto EARLY_ERROR;
    }
    if (g_static_sub_conf.sub_count < 1){
        g_static_sub_conf.sub_count = 1;
        g_sub_id[0] = 0;
    }
    p_static_schema_model_t model = static_get_schema_model_by_index(g_static_sub_client_conf.schema_index);
    if (model == NULL) {
        LOG_ERROR(ret, "Invalid schema model index.");
        goto EARLY_ERROR;
    }
    g_static_sub_client_conf.sub_model = model;

    db_subid *sub_ids = NULL;
    size = g_static_sub_conf.sub_count * sizeof(db_subid);
    ret = SC_SUB_MALLOC((void **)&sub_ids, size);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "sub_id malloc failed.");
        goto EARLY_ERROR;
    }
    g_static_sub_conf.sub_id = sub_ids;
    (void)SC_SUB_MEMSET(g_static_sub_conf.sub_id, 0, size);

#ifdef EPOLL
    //epoll ��������
    db_ctrl_event_fd_t reg_info;
	get_heart_db_reg_info(&reg_info);
	ret = db_heartbeat_register(reg_info);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Create heartbeat conn failed.");
        return ret;
    }

#endif

    ret = db_set_current_server_locator(g_static_sub_client_conf.server_locator);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub set server locator failed.");
        goto EARLY_ERROR;
    }

    ret = db_set_current_user_name("osc");
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub set user name failed.");
        goto EARLY_ERROR;
    }

    db_connection_pool_config pool_config;
    pool_config.min_num = g_static_sub_client_conf.conn_min_num;
    pool_config.max_num = g_static_sub_client_conf.conn_max_num;
    pool_config.is_async_check = false;
    ret = db_create_connection_pool(g_static_sub_client_conf.server_locator, "osc", SYNC_MODE, &pool_config,
                                    NULL);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Create connection pool failed");
        goto EARLY_ERROR;
    }

// create sub conn
#ifdef EPOLL    
	db_ctrl_event_fd_t sub_reg_info;

    db_sub_connect_attr_t sub_conn_attr;
    memset(&sub_conn_attr, 0, sizeof(db_sub_connect_attr_t));
    sub_conn_attr.server_locator = "shm:unix_emserver";
    sub_conn_attr.user_name = "osc";
    sub_conn_attr.chn_buf_size = g_static_sub_client_conf.sub_channel_size;
    sub_conn_attr.max_handle_count = 10000;
#ifdef SUB_MERGE
    sub_conn_attr.is_statusmerge_sub = 1; // �ϲ����ı��
#endif
    //sub_conn_attr.is_batch_notified = 1;
    for (loop = 0; loop < g_static_sub_conf.sub_count; loop++) {
        get_conn_db_reg_info(&sub_reg_info);
        ret = db_create_sub_connect(&sub_conn_attr, sub_reg_info, &g_sub_conn[loop]);
        if (ret != STATUS_OK) {
        (void)db_close_connection_pool(g_static_sub_client_conf.server_locator, "osc", SYNC_MODE);
            LOG_ERROR(ret, "Create sub epoll conn failed");
            goto EARLY_ERROR;
        }
        printf("Create sub epoll conn successs.\n");
    }
#endif

#ifdef MEM_CALC
    BEGIN_GET_MEM();
#endif

    ret = g_static_sub_conf.create_sub_obj_func(sub_obj_type, &sub_obj);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Create sub obj failed");
        goto TRY_SUB_ERROR;
    }
    g_static_sub_conf.sub_object = sub_obj;
    model->cal_ops_func = NULL;

    static_sub_client_reset_data();

    for (loop = 0; loop < g_static_sub_conf.sub_count; loop++) {
        ret = g_static_sub_conf.reset_sub_obj_func(sub_obj_type, g_static_sub_conf.sub_object);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "reset sub obj failed");
            goto TRY_SUB_ERROR;
        }
	uint32_t loopCon = loop;
	if (g_cfgSubConnId > 0 ) {
	    loopCon = 0;
	}
        ret = g_static_sub_conf.try_sub_func(g_static_sub_conf.sub_object, g_sub_id[loop], g_sub_conn[loopCon]);
        if (ret != STATUS_OK) {
            goto TRY_SUB_ERROR;
        }
	printf("Client ID: %u, sub conn: %u ...\n", g_static_sub_client_conf.client_id, loopCon);

    }
    printf("Client ID: %u, total %u threads subscribe ...\n", g_static_sub_client_conf.client_id, g_static_sub_conf.sub_count);

    if (g_static_sub_conf.print_conf_func != NULL) {
        g_static_sub_conf.print_conf_func(g_static_sub_conf.sub_object);
    }

    ret = pthread_create(&g_sub_wait_thread_id, DB_NULL, static_sub_client_wait_until_reach_count_or_timeout, NULL);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Sub client create thread failed.");
        goto CREATE_THR_ERROR;
    }

    pthread_join(g_sub_wait_thread_id, NULL);

    printf("\nSub_client %u done.Trigger %u times. cost %.4f seconds. sub_ops : %.4f kops\n",
        g_static_sub_client_conf.client_id, g_callback_count, g_static_sub_client_ops.tcost,
        g_static_sub_client_ops.ops / 1000);
#ifdef DELAY
    printf("Sub_client %u done. sub delay average: %.4f seconds, total calculate times: %lu, delay_max: %.4f seconds, "
           "delay_min: %.4f seconds.\n",
        g_static_sub_client_conf.client_id, g_static_sub_client_ops.delay_avg, g_static_sub_client_ops.delay_count,
        g_static_sub_client_ops.delay_max, g_static_sub_client_ops.delay_min);
#endif
    printf("\n");


TRY_SUB_ERROR:
CREATE_THR_ERROR:

    if (g_static_sub_conf.create_sub_obj_func != NULL) {
        ret = g_static_sub_conf.create_sub_obj_func(unsub_obj_type, &unsub_obj);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Create unsub obj failed");
            g_static_sub_conf.release_sub_obj_func(g_static_sub_conf.sub_object);
            (void)db_close_all_connection_pool();
            goto EARLY_ERROR;
        }
    }
    if (g_static_sub_conf.unsubscribe_func != NULL) {
        for (loop = 0; loop < g_static_sub_conf.sub_count; loop++) {
            g_static_sub_conf.unsubscribe_func(unsub_obj, g_static_sub_conf.sub_id[loop]);
        }
    }
    
#ifdef MEM_CALC   
    END_GET_MEM();
#endif

    if (g_static_sub_conf.release_sub_obj_func != NULL) {
        g_static_sub_conf.release_sub_obj_func(unsub_obj);
        g_static_sub_conf.release_sub_obj_func(g_static_sub_conf.sub_object);
    }

    (void)db_close_all_connection_pool();

EARLY_ERROR:
    //SC_SUB_FREE(g_static_sub_conf.lpm_sub_info.sub_ip_info);
    SC_SUB_FREE(g_static_sub_conf.sub_id);
    (void)pthread_cond_destroy(&g_cond);
    (void)pthread_mutex_destroy(&g_mutex);

#ifdef EPOLL
    for (loop = 0; loop < g_static_sub_conf.sub_count; loop++) {
        (void)db_disconnect(g_sub_conn[loop]);
    }
#endif


    return ret;
}
