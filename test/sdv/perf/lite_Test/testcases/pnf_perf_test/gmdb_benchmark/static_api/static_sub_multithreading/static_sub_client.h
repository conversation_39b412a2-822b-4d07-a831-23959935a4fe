#ifndef __STATIC_SUB_CLIENT_H_
#define __STATIC_SUB_CLIENT_H_

#include "db_wrapper.h"
#include "db_wrapper_private.h"
#include "gm_errno.h"
#include "tools.h"
#include <sys/time.h>
#include "static_schema_model.h"

#define SERVER_LOCATOR_MAX_LEN               128
#define FIELD_NAME_MAX_LEN                   128
#define FIELD_VALUE_MAX_LEN                  128
#define COMPARE_TYPE_FILE_NAME_MAX_LEN       128

#define SC_SUB_EVENT_MASK_DEFAULT      (0)
#define SC_SUB_PUSH_OP_DEFAULT         (0)
#define SC_SUB_OBJ_COUNT_DEFAULT       (0)
#define SC_SUB_TIME_SECONDS_DEFAULT    (360)
#define SC_SUB_IS_BATCH_NOTIFY_DEFAULT (false)
#define SC_SUB_TRY_SUBSCRIBE_MAX_TIME  (10)
#define SC_SUB_MAX_SUB_CNT         (1000)
#define SC_SUB_MAX_SUB_ID         (30)
#define SC_SUB_MAX_LPM_SUB_IP_LEN      (16)  // DB_MAX_LPM_SUB_IP_LEN
#define SC_SUB_CONN_MAX_NUM            (512)

typedef struct tag_static_sub_client_ops_t {
    uint8_t is_started;
    struct timeval start;
    struct timeval stop;
    double tcost;
    double ops;
    uint32_t triggernum;
#ifdef DELAY
    float delay_max;
    float delay_min;
    uint64_t delay_count;
    float delay_total;
    float delay_avg;
#endif
} static_sub_client_ops_t, *p_static_sub_client_ops_t;

typedef struct tag_static_sub_client_config_t {
    uint32_t schema_index;
    char server_locator[SERVER_LOCATOR_MAX_LEN];
    uint32_t time;
    uint32_t event_mask;
    uint32_t push_mask;
    uint32_t trigger_count;
    uint8_t is_batch_notified;
    p_static_schema_model_t sub_model;
    uint8_t log_level;
    uint32_t sub_channel_size;
    uint32_t conn_min_num;
    uint32_t conn_max_num;
    char comp_type_node_name[FIELD_NAME_MAX_LEN];
    char comp_type_field_name[FIELD_NAME_MAX_LEN];
    char comp_type_value[FIELD_VALUE_MAX_LEN];
    uint32_t comp_type_value_len;
    char comp_type_file[COMPARE_TYPE_FILE_NAME_MAX_LEN];
    uint32_t client_id;
} static_sub_client_config_t, *p_static_sub_client_config_t;

typedef struct tag_static_sub_conf_t {
    db_object sub_object;
    uint32_t sub_count;
    db_subid *sub_id;
    status_t (*create_sub_obj_func)(db_object_type, db_object *);
    status_t (*reset_sub_obj_func)(db_object_type, db_object);
    void (*release_sub_obj_func)(db_object);
    status_t (*try_sub_func)(db_object, uint32_t, db_connect_t);
    status_t (*set_sub_object_func)(p_static_sub_client_config_t, db_object, uint32_t);
    status_t (*subscribe_func)(db_object, db_subid *, db_connect_t);
    void (*unsubscribe_func)(db_object, db_subid);
    void (*print_conf_func)(db_object);
    void (*print_data_func)(void);
    // db_sub_info_lpm lpm_sub_info;
} static_sub_conf_t, *p_static_sub_conf_t;

#endif
