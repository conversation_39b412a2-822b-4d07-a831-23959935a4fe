#ifndef __KPI_MEM_DATA_H__
#define __KPI_MEM_DATA_H__

#include "db_kpi_mem.h"
#include "gm_errno.h"
#include "type.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"

status_t static_kpi_mem_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                                db_object *object);
void static_kpi_mem_release_db_obj_func(db_object object);
status_t static_kpi_mem_reset_db_obj_func(db_object object, db_object_type obj_type);
status_t static_kpi_mem_reset_db_batch_obj_func(db_object object, db_object_type obj_type);
status_t static_kpi_mem_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array,
                                          uint32_t thread_special_num);
status_t static_kpi_mem_pri_key_set_func(db_object object, uint64_t ip_address, uint32_t thread_special_num,
                                        void *key_info, void *key_data);
status_t static_kpi_mem_register_schema_model(void *schema_model);
#endif
