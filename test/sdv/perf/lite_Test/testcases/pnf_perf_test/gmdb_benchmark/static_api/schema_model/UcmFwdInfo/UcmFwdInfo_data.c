#include "UcmFwdInfo_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "cpu_cycles.h"

status_t static_UcmFwdInfo_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type, db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    ret = db_create_UcmFwdInfo_obj(obj_type, conn_type, &obj);
    CHECK_OK_RET(ret, "Create object.");

    *object = obj;
    return ret;
}


status_t static_UcmFwdInfo_create_db_spec_conn_obj_func(db_object_type object_type, db_connect_t conn, 
                                                 db_object* object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_UcmFwdInfo_obj_specific_conn(object_type, conn, &obj);
    CHECK_OK_RET(ret, "Create UcmFwdInfo spec conn object.");

    *object = obj;
    return ret;
}


void static_UcmFwdInfo_release_db_obj_func(db_object object)
{
    db_release_UcmFwdInfo_object(object);
}
status_t static_UcmFwdInfo_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;
    ret = db_reset_UcmFwdInfo_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");

    return ret;
}
status_t static_UcmFwdInfo_reset_db_batch_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;
    ret = db_reset_UcmFwdInfo_batch_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for batch object failed.");

    return ret;
}

status_t static_UcmFwdInfo_get_field_func(db_object object, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    uint32_t array = 1;
    /*  uint32_t ulVer, ulInstanceID, ulCidPrefix, ulCID, ulNodeId, ulVACNodeID, ulIpAddr, ulOldIpAddr, ulGateway, ulMask,
             ulIfIndex, ulLogicInterface, ulPortIndex, ulApTunnelId, ulIsPortBased, ulProductIndex, ulSenderIp, ulApId,
             ulModifyTriggerBmp, ulAuthorModifyBitmap, ulMacLimit, ulForwardInterface, ulForwardPdtIndex, ulIdleCutTime,
             ulIdleCutFlow, ulVsysID, ulIPSecAcl, uiAclStringNum, ulMagicNumber, ulVTIfIndex, ulPhyTxIf, ulMss;
   uint8_t ucTableType, ucAccessType, ucAuthType, ucPortalLayerFlag, ucPreAuthFlag, ucIsIpStaticUserEnable,
            ucLogicInterfaceLayer3Flag, ucIsETrunkAccess, ucSlot, ucIsPortBaseAuthenMac, ucIsRemoteUser, ucIsUCRemoteUser,
            ucWlanFlag, ucRadio, ucWorkGroupID, ucIsETrunkPulgIn, ucPriority, ucDownPriority, ucRemarkDscp, ucRemark8021p,
            ucDnRemarkDscp, ucDnRemark8021p, ucQosUserNameFlag, ucBandSharKeyFlag, ucOldPreAuthFlag, ucProcGrpFlag,
            ucInterIsolated, ucIsolated, ucPvcVlanType, ucAuthVlanType, ucHttpToCpuFlag, ucHttpUpFlag, ucPushFlag,
            ucDaaStaticEnable, ucIdleCutSecond, ucIfRDS, ucAuthedPlace, ucHttpsToCpuFlag, ucIdleCutFlowDirection,
            ucUserGroupChanged, ucIPSecFlag, ucProtocolTpye, ucIfL3Roam, ucIfSameCentralAP, ucUserUpFlowStaticsFlag,
            ucUserDownFlowStaticsFlag, ucArpDeny, ucIsGuestAuth, ucHttpFwdFlg, ucAuthVlanSource, ucWlanFwdTunnelFlag,
            ucIpv6ControlFlag, ucDot1xUrlFlag, ucSingleStackControlFlag, ucUserGroupPriority, ucForwardingOpen, ucAclDescNum,
            ucStaticsUpMap, ucStaticsDownMap, ucNeedSendtoFp, ucIfVpdn, ucIpType, ucIsV6User;
    int8_t *szMac, *stIPv6, *stGlobalIPv6, *stOldIPv6, *szASMac, *aucSenderMac, *szSysMac, *szUserName, *szLocalMac,
           **szURL, *szUserProfile, *szUserGroupName, *szDaaQosProfileName, *szVlanPoolName;
    uint16_t usVrfId, usPort, usMtu, usSessionId, usUserGroup, usUclGroupId, usAuthVlan, usDynamicVlan, usDefaultVlan,
             usISPVlan, usOldISPVlan, usRedirectAclId, usPortNum, usGuestVlan;

    ret = db_get_UcmFwdInfo_ulVer(object, &ulVer);
        CHECK_OK_RET(ret, "Get ulVer failed.");
        ret = db_get_UcmFwdInfo_ulInstanceID(object, &ulInstanceID);
        CHECK_OK_RET(ret, "Get ulInstanceID failed.");
        ret = db_get_UcmFwdInfo_ulCidPrefix(object, &ulCidPrefix);
        CHECK_OK_RET(ret, "Get ulCidPrefix failed.");
        ret = db_get_UcmFwdInfo_ulCID(object, &ulCID);
        CHECK_OK_RET(ret, "Get ulCID failed.");
        ret = db_get_UcmFwdInfo_ulNodeId(object, &ulNodeId);
        CHECK_OK_RET(ret, "Get ulNodeId failed.");
        ret = db_get_UcmFwdInfo_ucTableType(object, &ucTableType);
        ret = db_get_UcmFwdInfo_ucAccessType(object, &ucAccessType);
        ret = db_get_UcmFwdInfo_ucAuthType(object, &ucAuthType);
        ret = db_get_UcmFwdInfo_ucPortalLayerFlag(object, &ucPortalLayerFlag);
        ret = db_get_UcmFwdInfo_ucPreAuthFlag(object, &ucPreAuthFlag);
        ret = db_get_UcmFwdInfo_ucIsIpStaticUserEnable(object, &ucIsIpStaticUserEnable);
        uint32_t length;
        ret = db_get_UcmFwdInfo_szMac(object, &szMac, &length);
        ret = db_get_UcmFwdInfo_ulVACNodeID(object, &ulVACNodeID);
        ret = db_get_UcmFwdInfo_ulIpAddr(object, &ulIpAddr);
        ret = db_get_UcmFwdInfo_ulOldIpAddr(object, &ulOldIpAddr);
        ret = db_get_UcmFwdInfo_stIPv6(object, &stIPv6, &length);
        ret = db_get_UcmFwdInfo_stGlobalIPv6(object, &stGlobalIPv6, &length);
        ret = db_get_UcmFwdInfo_stOldIPv6(object, &stOldIPv6, &length);
        ret = db_get_UcmFwdInfo_usVrfId(object, &usVrfId);
        ret = db_get_UcmFwdInfo_ucIpType(object, &ucIpType);
        ret = db_get_UcmFwdInfo_ucIsV6User(object, &ucIsV6User);
        ret = db_get_UcmFwdInfo_ulGateway(object, &ulGateway);
        ret = db_get_UcmFwdInfo_ulMask(object, &ulMask);
        ret = db_get_UcmFwdInfo_ulIfIndex(object, &ulIfIndex);
        ret = db_get_UcmFwdInfo_ulLogicInterface(object, &ulLogicInterface);
        ret = db_get_UcmFwdInfo_ulPortIndex(object, &ulPortIndex);
        ret = db_get_UcmFwdInfo_ulApTunnelId(object, &ulApTunnelId);
        ret = db_get_UcmFwdInfo_ucLogicInterfaceLayer3Flag(object, &ucLogicInterfaceLayer3Flag);
        ret = db_get_UcmFwdInfo_ucIsETrunkAccess(object, &ucIsETrunkAccess);
        ret = db_get_UcmFwdInfo_ucSlot(object, &ucSlot);
        ret = db_get_UcmFwdInfo_ucIsPortBaseAuthenMac(object, &ucIsPortBaseAuthenMac);
        ret = db_get_UcmFwdInfo_usPort(object, &usPort);
        ret = db_get_UcmFwdInfo_usMtu(object, &usMtu);
        ret = db_get_UcmFwdInfo_ulIsPortBased(object, &ulIsPortBased);
        ret = db_get_UcmFwdInfo_ulProductIndex(object, &ulProductIndex);
        ret = db_get_UcmFwdInfo_ucIsRemoteUser(object, &ucIsRemoteUser);
        ret = db_get_UcmFwdInfo_ucIsUCRemoteUser(object, &ucIsUCRemoteUser);
        ret = db_get_UcmFwdInfo_szASMac(object, &szASMac, &length);
        ret = db_get_UcmFwdInfo_ulSenderIp(object, &ulSenderIp);
        ret = db_get_UcmFwdInfo_aucSenderMac(object, &aucSenderMac, &length);
        ret = db_get_UcmFwdInfo_szSysMac(object, &szSysMac, &length);
        ret = db_get_UcmFwdInfo_ucWlanFlag(object, &ucWlanFlag);
        ret = db_get_UcmFwdInfo_ucRadio(object, &ucRadio);
        ret = db_get_UcmFwdInfo_ucWorkGroupID(object, &ucWorkGroupID);
        ret = db_get_UcmFwdInfo_ucIsETrunkPulgIn(object, &ucIsETrunkPulgIn);
        ret = db_get_UcmFwdInfo_ulApId(object, &ulApId);
        ret = db_get_UcmFwdInfo_usSessionId(object, &usSessionId);
        ret = db_get_UcmFwdInfo_ucPriority(object, &ucPriority);
        ret = db_get_UcmFwdInfo_ucDownPriority(object, &ucDownPriority);
        ret = db_get_UcmFwdInfo_ulModifyTriggerBmp(object, &ulModifyTriggerBmp);
        ret = db_get_UcmFwdInfo_ulAuthorModifyBitmap(object, &ulAuthorModifyBitmap);
        ret = db_get_UcmFwdInfo_ucRemarkDscp(object, &ucRemarkDscp);
        ret = db_get_UcmFwdInfo_ucRemark8021p(object, &ucRemark8021p);
        ret = db_get_UcmFwdInfo_ucDnRemarkDscp(object, &ucDnRemarkDscp);
        ret = db_get_UcmFwdInfo_ucDnRemark8021p(object, &ucDnRemark8021p);
        ret = db_get_UcmFwdInfo_ucQosUserNameFlag(object, &ucQosUserNameFlag);
        ret = db_get_UcmFwdInfo_ucBandSharKeyFlag(object, &ucBandSharKeyFlag);
        ret = db_get_UcmFwdInfo_ucOldPreAuthFlag(object, &ucOldPreAuthFlag);
        ret = db_get_UcmFwdInfo_ucProcGrpFlag(object, &ucProcGrpFlag);
        ret = db_get_UcmFwdInfo_usUserGroup(object, &usUserGroup);
        ret = db_get_UcmFwdInfo_usUclGroupId(object, &usUclGroupId);
        ret = db_get_UcmFwdInfo_ucInterIsolated(object, &ucInterIsolated);
        ret = db_get_UcmFwdInfo_ucIsolated(object, &ucIsolated);
        ret = db_get_UcmFwdInfo_ucPvcVlanType(object, &ucPvcVlanType);
        ret = db_get_UcmFwdInfo_ucAuthVlanType(object, &ucAuthVlanType);
        ret = db_get_UcmFwdInfo_usAuthVlan(object, &usAuthVlan);
        ret = db_get_UcmFwdInfo_usDynamicVlan(object, &usDynamicVlan);
        ret = db_get_UcmFwdInfo_usDefaultVlan(object, &usDefaultVlan);
        ret = db_get_UcmFwdInfo_ulMacLimit(object, &ulMacLimit);
        ret = db_get_UcmFwdInfo_ucHttpToCpuFlag(object, &ucHttpToCpuFlag);
        ret = db_get_UcmFwdInfo_ucHttpUpFlag(object, &ucHttpUpFlag);
        ret = db_get_UcmFwdInfo_ucPushFlag(object, &ucPushFlag);
        ret = db_get_UcmFwdInfo_ucDaaStaticEnable(object, &ucDaaStaticEnable);
        ret = db_get_UcmFwdInfo_ulForwardInterface(object, &ulForwardInterface);
        ret = db_get_UcmFwdInfo_ulForwardPdtIndex(object, &ulForwardPdtIndex);
        ret = db_get_UcmFwdInfo_usISPVlan(object, &usISPVlan);
        ret = db_get_UcmFwdInfo_usOldISPVlan(object, &usOldISPVlan);
        ret = db_get_UcmFwdInfo_ulIdleCutTime(object, &ulIdleCutTime);
        ret = db_get_UcmFwdInfo_ulIdleCutFlow(object, &ulIdleCutFlow);
        ret = db_get_UcmFwdInfo_ulVsysID(object, &ulVsysID);
        ret = db_get_UcmFwdInfo_ucIdleCutSecond(object, &ucIdleCutSecond);
        ret = db_get_UcmFwdInfo_ucIfRDS(object, &ucIfRDS);
        ret = db_get_UcmFwdInfo_ucAuthedPlace(object, &ucAuthedPlace);
        ret = db_get_UcmFwdInfo_ucHttpsToCpuFlag(object, &ucHttpsToCpuFlag);
        ret = db_get_UcmFwdInfo_usRedirectAclId(object, &usRedirectAclId);
        ret = db_get_UcmFwdInfo_ucIdleCutFlowDirection(object, &ucIdleCutFlowDirection);
        ret = db_get_UcmFwdInfo_ucUserGroupChanged(object, &ucUserGroupChanged);
        ret = db_get_UcmFwdInfo_usPortNum(object, &usPortNum);
        ret = db_get_UcmFwdInfo_ucProtocolTpye(object, &ucProtocolTpye);
        ret = db_get_UcmFwdInfo_ucIPSecFlag(object, &ucIPSecFlag);
        ret = db_get_UcmFwdInfo_ucIfL3Roam(object, &ucIfL3Roam);
        ret = db_get_UcmFwdInfo_ucIfSameCentralAP(object, &ucIfSameCentralAP);
        ret = db_get_UcmFwdInfo_ucUserUpFlowStaticsFlag(object, &ucUserUpFlowStaticsFlag);
        ret = db_get_UcmFwdInfo_ucUserDownFlowStaticsFlag(object, &ucUserDownFlowStaticsFlag);
        ret = db_get_UcmFwdInfo_ulIPSecAcl(object, &ulIPSecAcl);
        ret = db_get_UcmFwdInfo_ucArpDeny(object, &ucArpDeny);
        ret = db_get_UcmFwdInfo_ucIsGuestAuth(object, &ucIsGuestAuth);
        ret = db_get_UcmFwdInfo_ucHttpFwdFlg(object, &ucHttpFwdFlg);
        ret = db_get_UcmFwdInfo_ucAuthVlanSource(object, &ucAuthVlanSource);
        ret = db_get_UcmFwdInfo_uiAclStringNum(object, &uiAclStringNum);
        ret = db_get_UcmFwdInfo_ucWlanFwdTunnelFlag(object, &ucWlanFwdTunnelFlag);
        ret = db_get_UcmFwdInfo_ucIpv6ControlFlag(object, &ucIpv6ControlFlag);
        ret = db_get_UcmFwdInfo_ucDot1xUrlFlag(object, &ucDot1xUrlFlag);
        ret = db_get_UcmFwdInfo_ucSingleStackControlFlag(object, &ucSingleStackControlFlag);
        ret = db_get_UcmFwdInfo_ucUserGroupPriority(object, &ucUserGroupPriority);
        ret = db_get_UcmFwdInfo_ucForwardingOpen(object, &ucForwardingOpen);
        ret = db_get_UcmFwdInfo_usGuestVlan(object, &usGuestVlan);
        ret = db_get_UcmFwdInfo_ucAclDescNum(object, &ucAclDescNum);
        ret = db_get_UcmFwdInfo_ucStaticsUpMap(object, &ucStaticsUpMap);
        ret = db_get_UcmFwdInfo_ucStaticsDownMap(object, &ucStaticsDownMap);
        ret = db_get_UcmFwdInfo_ucNeedSendtoFp(object, &ucNeedSendtoFp);
        ret = db_get_UcmFwdInfo_ulMagicNumber(object, &ulMagicNumber);
        ret = db_get_UcmFwdInfo_ulVTIfIndex(object, &ulVTIfIndex);
        ret = db_get_UcmFwdInfo_ulPhyTxIf(object, &ulPhyTxIf);
        ret = db_get_UcmFwdInfo_ulMss(object, &ulMss);
        ret = db_get_UcmFwdInfo_ucIfVpdn(object, &ucIfVpdn);
        ret = db_get_UcmFwdInfo_szUserName(object, &szUserName, &length);
        ret = db_get_UcmFwdInfo_szLocalMac(object, &szLocalMac, &length);
        ret = db_get_UcmFwdInfo_szURL(object, szURL, &length);
        ret = db_get_UcmFwdInfo_szUserProfile(object, &szUserProfile, &length);
        ret = db_get_UcmFwdInfo_szUserGroupName(object, &szUserGroupName, &length);
        ret = db_get_UcmFwdInfo_szDaaQosProfileName(object, &szDaaQosProfileName, &length);
        ret = db_get_UcmFwdInfo_szVlanPoolName(object, &szVlanPoolName, &length); */

    sp_UcmFwdInfo_ucm_superfield_t ucm_superfield;
    ret = db_get_UcmFwdInfo_sp_ucm_superfield(object, &ucm_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_ucm_superfield failed.");

    db_child_iterator child_iterator;
    ret = db_create_child_node(object, NULL, "stUserDetect", NULL, NULL, &child_iterator);
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    CHECK_OK_RET(ret, "Get child array iterator failed.");
    /* uint32_t ulDetectTimeLen, ulDetectTimeLenForMacMove, ulDetectDelayTimeLen;
    uint16_t usDetectTimes, usDetectTimesForMacMove;
    uint8_t ucDetectType, ucEapolHandShakeType, ucEapPause, ucResv;
    ret = db_get_UcmFwdInfo_stUserDetect_ulDetectTimeLen(child_iterator, &ulDetectTimeLen);
        CHECK_OK_RET(ret, "Get ulDetectTimeLen failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_usDetectTimes(child_iterator, &usDetectTimes);
        CHECK_OK_RET(ret, "Get usDetectTimes failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_ucDetectType(child_iterator, &ucDetectType);
        CHECK_OK_RET(ret, "Get ucDetectType failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_ucEapolHandShakeType(child_iterator, &ucEapolHandShakeType);
        CHECK_OK_RET(ret, "Get ucEapolHandShakeType failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_ucEapPause(child_iterator, &ucEapPause);
        CHECK_OK_RET(ret, "Get ucEapPause failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_ucResv(child_iterator, &ucResv);
        CHECK_OK_RET(ret, "Get ucResv failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_usDetectTimesForMacMove(child_iterator, &usDetectTimesForMacMove);
        CHECK_OK_RET(ret, "Get usDetectTimesForMacMove failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_ulDetectTimeLenForMacMove(child_iterator, &ulDetectTimeLenForMacMove);
        CHECK_OK_RET(ret, "Get ulDetectTimeLenForMacMove failed.");
        ret = db_get_UcmFwdInfo_stUserDetect_ulDetectDelayTimeLen(child_iterator, &ulDetectDelayTimeLen);
        CHECK_OK_RET(ret, "Get ulDetectDelayTimeLen failed."); */

    sp_UcmFwdInfo_stUserDetect_stUserDetect_superfield_t stUserDetect_superfield;
    ret = db_get_UcmFwdInfo_sp_stUserDetect_stUserDetect_superfield(child_iterator, &stUserDetect_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stUserDetect_stUserDetect_superfield failed.");

    uint32_t array_idx;
    /* Add child array node. */
    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stDestSlotFlag", NULL, NULL, &child_iterator);
        uint32_t ulBitMap;
        if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
            printf("\t[Child node stUserDetect]: Not exist.\n");
            return STATUS_OK;
        }
        CHECK_OK_RET(ret, "Get child array iterator failed.");
        for (array_idx = 0; array_idx < 4; array_idx++) {
            ret = db_child_get_next(child_iterator);
            if (ret == STATUS_SCHEMA_ARRAY_GET_NEXT_END) {
                ret = STATUS_OK;
                break;
            }
            CHECK_OK_RET(ret, "Get next child failed.");
            ret = db_get_UcmFwdInfo_stDestSlotFlag_ulBitMap(child_iterator, &ulBitMap);
            CHECK_OK_RET(ret, "Get child field ulBitMap failed for write.");
        }

        ret = db_create_child_node(object, NULL, "stDestSlotFlagOld", NULL, NULL, &child_iterator);
        if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
            printf("\t[Child node stUserDetect]: Not exist.\n");
            return STATUS_OK;
        }
        CHECK_OK_RET(ret, "Get child array iterator failed.");
        for (array_idx = 0; array_idx < 4; array_idx++) {
            ret = db_child_get_next(child_iterator);
            if (ret == STATUS_SCHEMA_ARRAY_GET_NEXT_END) {
                ret = STATUS_OK;
                break;
            }
            CHECK_OK_RET(ret, "Get next child failed.");
            ret = db_get_UcmFwdInfo_stDestSlotFlagOld_ulBitMap(child_iterator, &ulBitMap);
            CHECK_OK_RET(ret, "Get child field ulBitMap failed for write.");
        }
    }

    ret = db_create_child_node(object, NULL, "stVmCar", NULL, NULL, &child_iterator);
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    CHECK_OK_RET(ret, "Get child array iterator failed.");
    /* uint8_t ucIsUpCar, ucIsDownCar, ucOpCode;
    uint32_t ulDownCir, ulDownPir, ulUpCir, ulUpPir;
    ret = db_get_UcmFwdInfo_stVmCar_ucIsUpCar(child_iterator, &ucIsUpCar);
        ret = db_get_UcmFwdInfo_stVmCar_ucIsDownCar(child_iterator, &ucIsDownCar);
        ret = db_get_UcmFwdInfo_stVmCar_ucOpCode(child_iterator, &ucOpCode);
        ret = db_get_UcmFwdInfo_stVmCar_ucResv(child_iterator, &ucResv);
        ret = db_get_UcmFwdInfo_stVmCar_ulUpCir(child_iterator, &ulUpCir);
        ret = db_get_UcmFwdInfo_stVmCar_ulUpPir(child_iterator, &ulUpPir);
        ret = db_get_UcmFwdInfo_stVmCar_ulDownCir(child_iterator, &ulDownCir);
        ret = db_get_UcmFwdInfo_stVmCar_ulDownPir(child_iterator, &ulDownPir); */

    sp_UcmFwdInfo_stVmCar_stVmCar_superfield_t stVmCar_superfield;
    ret = db_get_UcmFwdInfo_sp_stVmCar_stVmCar_superfield(child_iterator, &stVmCar_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stVmCar_stVmCar_superfield failed.");

    ret = db_create_child_node(object, NULL, "stCarInbound", NULL, NULL, &child_iterator);
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    CHECK_OK_RET(ret, "Get child array iterator failed.");
    /* uint32_t ulCarFlag, ulCir, ulCbs, ulPir, ulPbs;
    ret = db_get_UcmFwdInfo_stCarInbound_ulCarFlag(child_iterator, &ulCarFlag);
        ret = db_get_UcmFwdInfo_stCarInbound_ulCir(child_iterator, &ulCir);
        ret = db_get_UcmFwdInfo_stCarInbound_ulCbs(child_iterator, &ulCbs);
        ret = db_get_UcmFwdInfo_stCarInbound_ulPir(child_iterator, &ulPir);
        ret = db_get_UcmFwdInfo_stCarInbound_ulPbs(child_iterator, &ulPbs); */

    sp_UcmFwdInfo_stCarInbound_stCarInbound_superfield_t stCarInbound_superfield;
    ret = db_get_UcmFwdInfo_sp_stCarInbound_stCarInbound_superfield(child_iterator, &stCarInbound_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stCarInbound_stCarInbound_superfield failed.");

    ret = db_create_child_node(object, NULL, "stCarOutbound", NULL, NULL, &child_iterator);
    // uint32_t ulCarFlag,ulCir,ulCbs,ulPir,ulPbs;
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    CHECK_OK_RET(ret, "Get child array iterator failed.");
    /* ret = db_get_UcmFwdInfo_stCarOutbound_ulCarFlag(child_iterator, &ulCarFlag);
        ret = db_get_UcmFwdInfo_stCarOutbound_ulCir(child_iterator, &ulCir);
        ret = db_get_UcmFwdInfo_stCarOutbound_ulCbs(child_iterator, &ulCbs);
        ret = db_get_UcmFwdInfo_stCarOutbound_ulPir(child_iterator, &ulPir);
        ret = db_get_UcmFwdInfo_stCarOutbound_ulPbs(child_iterator, &ulPbs); */

    sp_UcmFwdInfo_stCarOutbound_stCarOutbound_superfield_t stCarOutbound_superfield;
    ret = db_get_UcmFwdInfo_sp_stCarOutbound_stCarOutbound_superfield(child_iterator, &stCarOutbound_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stCarOutbound_stCarOutbound_superfield failed.");

    ret = db_create_child_node(object, NULL, "stProfileUserQueue", NULL, NULL, &child_iterator);    
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    /*     uint8_t ucValid;
    int8_t *ucResv1;
    uint16_t usFlowQueueProfileIndex, usFlowMappingProfileIndex;
    CHECK_OK_RET(ret, "Get child array iterator failed.");
        ret = db_get_UcmFwdInfo_stProfileUserQueue_ucValid(child_iterator, &ucValid);
        ret = db_get_UcmFwdInfo_stProfileUserQueue_ucResv(child_iterator, &ucResv1, &length);
        ret = db_get_UcmFwdInfo_stProfileUserQueue_ulPir(child_iterator, &ulPir);
        ret = db_get_UcmFwdInfo_stProfileUserQueue_ulCir(child_iterator, &ulCir);
        ret = db_get_UcmFwdInfo_stProfileUserQueue_usFlowQueueProfileIndex(child_iterator, &usFlowQueueProfileIndex);
        ret = db_get_UcmFwdInfo_stProfileUserQueue_usFlowMappingProfileIndex(child_iterator, &usFlowMappingProfileIndex); */

    sp_UcmFwdInfo_stProfileUserQueue_stProfileUserQueue_superfield_t stProfileUserQueue_superfield;
    ret = db_get_UcmFwdInfo_sp_stProfileUserQueue_stProfileUserQueue_superfield(child_iterator,
                                                                                &stProfileUserQueue_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stProfileUserQueue_stProfileUserQueue_superfield failed.");

    ret = db_create_child_node(object, NULL, "stPvcVlan", NULL, NULL, &child_iterator);
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    CHECK_OK_RET(ret, "Get child array iterator failed.");
    /*  uint16_t usVpi, usVci; 
    uint32_t ulVlan;
        ret = db_get_UcmFwdInfo_stPvcVlan_usVpi(child_iterator, &usVpi);
        ret = db_get_UcmFwdInfo_stPvcVlan_usVci(child_iterator, &usVci);
        ret = db_get_UcmFwdInfo_stPvcVlan_ulVlan(child_iterator, &ulVlan); */

    sp_UcmFwdInfo_stPvcVlan_stPvcVlan_superfield_t stPvcVlan_superfield;
    db_get_UcmFwdInfo_sp_stPvcVlan_stPvcVlan_superfield(child_iterator, &stPvcVlan_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stPvcVlan_stPvcVlan_superfield failed.");

    ret = db_create_child_node(object, NULL, "stOriginalPvcVlan", NULL, NULL, &child_iterator);
    if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
        printf("\t[Child node stUserDetect]: Not exist.\n");
        return STATUS_OK;
    }
    CHECK_OK_RET(ret, "Get child array iterator failed.");
    /* ret = db_get_UcmFwdInfo_stOriginalPvcVlan_usVpi(child_iterator, &usVpi);
        ret = db_get_UcmFwdInfo_stOriginalPvcVlan_usVci(child_iterator, &usVci);
        ret = db_get_UcmFwdInfo_stOriginalPvcVlan_ulVlan(child_iterator, &ulVlan); */

    sp_UcmFwdInfo_stOriginalPvcVlan_stOriginalPvcVlan_superfield_t stOriginalPvcVlan_superfield;
    ret = db_get_UcmFwdInfo_sp_stOriginalPvcVlan_stOriginalPvcVlan_superfield(child_iterator,
                                                                              &stOriginalPvcVlan_superfield);
    CHECK_OK_RET(ret, "get_UcmFwdInfo_sp_stOriginalPvcVlan_stOriginalPvcVlan_superfield failed.");

    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stAclID", NULL, NULL, &child_iterator);
        uint16_t usAclID;
        if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
            printf("\t[Child node stUserDetect]: Not exist.\n");
            return STATUS_OK;
        }
        CHECK_OK_RET(ret, "Get child array iterator failed.");
        for (array_idx = 0; array_idx < 8; array_idx++) {
            ret = db_child_get_next(child_iterator);
            if (ret == STATUS_SCHEMA_ARRAY_GET_NEXT_END) {
                ret = STATUS_OK;
                break;
            }
            CHECK_OK_RET(ret, "Get next child failed.");
            ret = db_get_UcmFwdInfo_stAclID_usAclID(child_iterator, &usAclID);
        }
    }

    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stIPv6AclID", NULL, NULL, &child_iterator);
        uint16_t usIPv6AclID;
        if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
            printf("\t[Child node stUserDetect]: Not exist.\n");
            return STATUS_OK;
        }
        CHECK_OK_RET(ret, "Get child array iterator failed.");
        for (array_idx = 0; array_idx < 8; array_idx++) {
            ret = db_child_get_next(child_iterator);
            if (ret == STATUS_SCHEMA_ARRAY_GET_NEXT_END) {
                ret = STATUS_OK;
                break;
            }
            CHECK_OK_RET(ret, "Get next child failed.");
            ret = db_get_UcmFwdInfo_stIPv6AclID_usIPv6AclID(child_iterator, &usIPv6AclID);
        }
    }

    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stGrpAcl", NULL, NULL, &child_iterator);
        uint16_t usGrpAcl;
        if (ret == STATUS_SCHEMA_NODE_NOT_EXIST) {
            printf("\t[Child node stUserDetect]: Not exist.\n");
            return STATUS_OK;
        }
        CHECK_OK_RET(ret, "Get child array iterator failed.");
        for (array_idx = 0; array_idx < 8; array_idx++) {
            ret = db_child_get_next(child_iterator);
            if (ret == STATUS_SCHEMA_ARRAY_GET_NEXT_END) {
                ret = STATUS_OK;
                break;
            }
            CHECK_OK_RET(ret, "Get next child failed.");
            ret = db_get_UcmFwdInfo_stGrpAcl_usGrpAcl(child_iterator, &usGrpAcl);
        }
    }

    return ret;
}

status_t static_UcmFwdInfo_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array,
                                    uint32_t thread_special_num)
{
    status_t ret = 0;
    /* 	ret = db_set_UcmFwdInfo_ulVer(object, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulVer failed.");
	ret = db_set_UcmFwdInfo_ulInstanceID(object, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulInstanceID failed.");
	ret = db_set_UcmFwdInfo_ulCidPrefix(object, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulCidPrefix failed.");
	ret = db_set_UcmFwdInfo_ulCID(object, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulCID failed.");
	ret = db_set_UcmFwdInfo_ulNodeId(object, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulNodeId failed.");
	ret = db_set_UcmFwdInfo_ucTableType(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucAccessType(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucAuthType(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucPortalLayerFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucPreAuthFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsIpStaticUserEnable(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_szMac(object, (int8_t *)"fix", 6);
	ret = db_set_UcmFwdInfo_ulVACNodeID(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulIpAddr(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulOldIpAddr(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stIPv6(object, (int8_t *)"fix", 16);
	ret = db_set_UcmFwdInfo_stGlobalIPv6(object, (int8_t *)"fix", 16);
	ret = db_set_UcmFwdInfo_stOldIPv6(object, (int8_t *)"fix", 16);
	ret = db_set_UcmFwdInfo_usVrfId(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucIpType(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsV6User(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ulGateway(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulMask(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulIfIndex(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulLogicInterface(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulPortIndex(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulApTunnelId(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucLogicInterfaceLayer3Flag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsETrunkAccess(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucSlot(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsPortBaseAuthenMac(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_usPort(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_usMtu(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ulIsPortBased(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulProductIndex(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsRemoteUser(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsUCRemoteUser(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_szASMac(object, (int8_t *)"fix", 6);
	ret = db_set_UcmFwdInfo_ulSenderIp(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_aucSenderMac(object, (int8_t *)"fix", 6);
	ret = db_set_UcmFwdInfo_szSysMac(object, (int8_t *)"fix", 6);
	ret = db_set_UcmFwdInfo_ucWlanFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucRadio(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucWorkGroupID(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsETrunkPulgIn(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ulApId(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_usSessionId(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucPriority(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucDownPriority(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ulModifyTriggerBmp(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulAuthorModifyBitmap(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucRemarkDscp(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucRemark8021p(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucDnRemarkDscp(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucDnRemark8021p(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucQosUserNameFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucBandSharKeyFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucOldPreAuthFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucProcGrpFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_usUserGroup(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_usUclGroupId(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucInterIsolated(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsolated(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucPvcVlanType(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucAuthVlanType(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_usAuthVlan(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_usDynamicVlan(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_usDefaultVlan(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ulMacLimit(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucHttpToCpuFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucHttpUpFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucPushFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucDaaStaticEnable(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ulForwardInterface(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulForwardPdtIndex(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_usISPVlan(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_usOldISPVlan(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ulIdleCutTime(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulIdleCutFlow(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulVsysID(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucIdleCutSecond(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIfRDS(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucAuthedPlace(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucHttpsToCpuFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_usRedirectAclId(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucIdleCutFlowDirection(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucUserGroupChanged(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_usPortNum(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucProtocolTpye(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIPSecFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIfL3Roam(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIfSameCentralAP(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucUserUpFlowStaticsFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucUserDownFlowStaticsFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ulIPSecAcl(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucArpDeny(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIsGuestAuth(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucHttpFwdFlg(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucAuthVlanSource(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_uiAclStringNum(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucWlanFwdTunnelFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucIpv6ControlFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucDot1xUrlFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucSingleStackControlFlag(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucUserGroupPriority(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucForwardingOpen(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_usGuestVlan(object, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_ucAclDescNum(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucStaticsUpMap(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucStaticsDownMap(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ucNeedSendtoFp(object, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_ulMagicNumber(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulVTIfIndex(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulPhyTxIf(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ulMss(object, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_ucIfVpdn(object, uiVrIndex&(uint8_t)(~0));
	 */

    sp_UcmFwdInfo_ucm_superfield_t ucm_superfield = {
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        {0}, //(int8_t *)"fix111",
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        {0}, //(int8_t *)"fix1111111111111",
        {0}, //(int8_t *)"fix1111111111111",
        {0}, //(int8_t *)"fix1111111111111",
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        {0}, //(int8_t *)"fix111",
        uiVrIndex &(uint32_t)(~0),
        {0}, //(int8_t *)"fix111",
        {0}, //(int8_t *)"fix111",
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint8_t)(~0)
    };
    memcpy(ucm_superfield.szMac, (int8_t *)"fix111", sizeof(ucm_superfield.szMac));
    memcpy(ucm_superfield.stIPv6, (int8_t *)"fix1111111111111", sizeof(ucm_superfield.stIPv6));
    memcpy(ucm_superfield.stGlobalIPv6, (int8_t *)"fix1111111111111", sizeof(ucm_superfield.stGlobalIPv6));
    memcpy(ucm_superfield.stOldIPv6, (int8_t *)"fix1111111111111", sizeof(ucm_superfield.stOldIPv6));
    memcpy(ucm_superfield.szASMac, (int8_t *)"fix111", sizeof(ucm_superfield.szASMac));
    memcpy(ucm_superfield.aucSenderMac, (int8_t *)"fix111", sizeof(ucm_superfield.aucSenderMac));
    memcpy(ucm_superfield.szSysMac, (int8_t *)"fix111", sizeof(ucm_superfield.szSysMac));
    ret = db_set_UcmFwdInfo_sp_ucm_superfield(object, &ucm_superfield);

    ret = db_set_UcmFwdInfo_szUserName(object, (int8_t *)"bytes", 254);
    ret = db_set_UcmFwdInfo_szLocalMac(object, (int8_t *)"fix", 6);
    ret = db_set_UcmFwdInfo_szURL(object, (int8_t *)"bytes", 201);
    ret = db_set_UcmFwdInfo_szUserProfile(object, (int8_t *)"bytes", 36);
    ret = db_set_UcmFwdInfo_szUserGroupName(object, (int8_t *)"bytes", 678);
    ret = db_set_UcmFwdInfo_szDaaQosProfileName(object, (int8_t *)"bytes", 256);
    ret = db_set_UcmFwdInfo_szVlanPoolName(object, (int8_t *)"bytes", 33);

    db_child_iterator child_iterator;
    ret = db_create_child_node(object, NULL, "stUserDetect", NULL, NULL, &child_iterator);
    CHECK_OK_RET(ret, "Create child for write object failed.");
    /* 	ret = db_set_UcmFwdInfo_stUserDetect_ulDetectTimeLen(child_iterator, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulDetectTimeLen failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_usDetectTimes(child_iterator, uiVrIndex&(uint16_t)(~0));
	CHECK_OK_RET(ret, "Set usDetectTimes failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_ucDetectType(child_iterator, uiVrIndex&(uint8_t)(~0));
	CHECK_OK_RET(ret, "Set ucDetectType failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_ucEapolHandShakeType(child_iterator, uiVrIndex&(uint8_t)(~0));
	CHECK_OK_RET(ret, "Set ucEapolHandShakeType failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_ucEapPause(child_iterator, uiVrIndex&(uint8_t)(~0));
	CHECK_OK_RET(ret, "Set ucEapPause failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_ucResv(child_iterator, uiVrIndex&(uint8_t)(~0));
	CHECK_OK_RET(ret, "Set ucResv failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_usDetectTimesForMacMove(child_iterator, uiVrIndex&(uint16_t)(~0));
	CHECK_OK_RET(ret, "Set usDetectTimesForMacMove failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_ulDetectTimeLenForMacMove(child_iterator, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulDetectTimeLenForMacMove failed.");
	ret = db_set_UcmFwdInfo_stUserDetect_ulDetectDelayTimeLen(child_iterator, uiVrIndex&(uint32_t)(~0));
	CHECK_OK_RET(ret, "Set ulDetectDelayTimeLen failed."); */

    sp_UcmFwdInfo_stUserDetect_stUserDetect_superfield_t stUserDetect_superfield = {
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0)
    };
    db_set_UcmFwdInfo_sp_stUserDetect_stUserDetect_superfield(child_iterator, &stUserDetect_superfield);

    uint32_t array_idx;
    /* Add child array node. */
    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stDestSlotFlag", NULL, NULL, &child_iterator);
        CHECK_OK_RET(ret, "Create child for write object failed.");
        ret = db_child_push_back(child_iterator);
        CHECK_OK_RET(ret, "Push one child failed.");
        ret = db_set_UcmFwdInfo_stDestSlotFlag_ulBitMap(child_iterator, 1);
        for (array_idx = 1; array_idx < 4; array_idx++) {
            ret = db_child_push_back(child_iterator);
            CHECK_OK_RET(ret, "Push one child failed.");
            ret = db_set_UcmFwdInfo_stDestSlotFlag_ulBitMap(child_iterator, 0);
            CHECK_OK_RET(ret, "Set child field ulBitMap failed for write.");
        }
        ret = db_create_child_node(object, NULL, "stDestSlotFlagOld", NULL, NULL, &child_iterator);
        CHECK_OK_RET(ret, "Create child for write object failed.");
        for (array_idx = 0; array_idx < 4; array_idx++) {
            ret = db_child_push_back(child_iterator);
            CHECK_OK_RET(ret, "Push one child failed.");
            ret = db_set_UcmFwdInfo_stDestSlotFlagOld_ulBitMap(child_iterator, 0);
            CHECK_OK_RET(ret, "Set child field ulBitMap failed for write.");
        }
    }

    ret = db_create_child_node(object, NULL, "stVmCar", NULL, NULL, &child_iterator);
    CHECK_OK_RET(ret, "Create child for write object failed.");
    /* 	ret = db_set_UcmFwdInfo_stVmCar_ucIsUpCar(child_iterator, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ucIsDownCar(child_iterator, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ucOpCode(child_iterator, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ucResv(child_iterator, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ulUpCir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ulUpPir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ulDownCir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stVmCar_ulDownPir(child_iterator, uiVrIndex&(uint32_t)(~0)); */

    sp_UcmFwdInfo_stVmCar_stVmCar_superfield_t stVmCar_superfield = {
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint8_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0)
    };
    ret = db_set_UcmFwdInfo_sp_stVmCar_stVmCar_superfield(child_iterator, &stVmCar_superfield);

    ret = db_create_child_node(object, NULL, "stCarInbound", NULL, NULL, &child_iterator);
    /* 	ret = db_set_UcmFwdInfo_stCarInbound_ulCarFlag(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarInbound_ulCir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarInbound_ulCbs(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarInbound_ulPir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarInbound_ulPbs(child_iterator, uiVrIndex&(uint32_t)(~0)); */

    sp_UcmFwdInfo_stCarInbound_stCarInbound_superfield_t stCarInbound_superfield = {
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0)
    };
    ret = db_set_UcmFwdInfo_sp_stCarInbound_stCarInbound_superfield(child_iterator, &stCarInbound_superfield);

    ret = db_create_child_node(object, NULL, "stCarOutbound", NULL, NULL, &child_iterator);
    /* 	ret = db_set_UcmFwdInfo_stCarOutbound_ulCarFlag(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarOutbound_ulCir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarOutbound_ulCbs(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarOutbound_ulPir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stCarOutbound_ulPbs(child_iterator, uiVrIndex&(uint32_t)(~0));	 */

    sp_UcmFwdInfo_stCarOutbound_stCarOutbound_superfield_t stCarOutbound_superfield = {
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0)
    };
    ret = db_set_UcmFwdInfo_sp_stCarOutbound_stCarOutbound_superfield(child_iterator, &stCarOutbound_superfield);

    ret = db_create_child_node(object, NULL, "stProfileUserQueue", NULL, NULL, &child_iterator);
    /* 	ret = db_set_UcmFwdInfo_stProfileUserQueue_ucValid(child_iterator, uiVrIndex&(uint8_t)(~0));
	ret = db_set_UcmFwdInfo_stProfileUserQueue_ucResv(child_iterator, (int8_t *)"fix", 3);
	ret = db_set_UcmFwdInfo_stProfileUserQueue_ulPir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stProfileUserQueue_ulCir(child_iterator, uiVrIndex&(uint32_t)(~0));
	ret = db_set_UcmFwdInfo_stProfileUserQueue_usFlowQueueProfileIndex(child_iterator, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_stProfileUserQueue_usFlowMappingProfileIndex(child_iterator, uiVrIndex&(uint16_t)(~0)); */

    sp_UcmFwdInfo_stProfileUserQueue_stProfileUserQueue_superfield_t stProfileUserQueue_superfield = {
        uiVrIndex &(uint8_t)(~0),
        {0},
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint32_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0)
    };
    memcpy(stProfileUserQueue_superfield.ucResv, (char *)"fix", sizeof(stProfileUserQueue_superfield.ucResv));
    ret = db_set_UcmFwdInfo_sp_stProfileUserQueue_stProfileUserQueue_superfield(child_iterator,
                                                                                &stProfileUserQueue_superfield);

    ret = db_create_child_node(object, NULL, "stPvcVlan", NULL, NULL, &child_iterator);
    /* 	ret = db_set_UcmFwdInfo_stPvcVlan_usVpi(child_iterator, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_stPvcVlan_usVci(child_iterator, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_stPvcVlan_ulVlan(child_iterator, uiVrIndex&(uint32_t)(~0)); */

    sp_UcmFwdInfo_stPvcVlan_stPvcVlan_superfield_t stPvcVlan_superfield = {
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint32_t)(~0)
    };
    ret = db_set_UcmFwdInfo_sp_stPvcVlan_stPvcVlan_superfield(child_iterator, &stPvcVlan_superfield);

    ret = db_create_child_node(object, NULL, "stOriginalPvcVlan", NULL, NULL, &child_iterator);
    /* 	ret = db_set_UcmFwdInfo_stOriginalPvcVlan_usVpi(child_iterator, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_stOriginalPvcVlan_usVci(child_iterator, uiVrIndex&(uint16_t)(~0));
	ret = db_set_UcmFwdInfo_stOriginalPvcVlan_ulVlan(child_iterator, uiVrIndex&(uint32_t)(~0)); */

    sp_UcmFwdInfo_stOriginalPvcVlan_stOriginalPvcVlan_superfield_t stOriginalPvcVlan_superfield = {
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint16_t)(~0),
        uiVrIndex &(uint32_t)(~0)
    };
    ret = db_set_UcmFwdInfo_sp_stOriginalPvcVlan_stOriginalPvcVlan_superfield(child_iterator,
                                                                              &stOriginalPvcVlan_superfield);

    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stAclID", NULL, NULL, &child_iterator);
        CHECK_OK_RET(ret, "Create child for write object failed.");
        for (array_idx = 0; array_idx < 8; array_idx++) {
            ret = db_child_push_back(child_iterator);
            CHECK_OK_RET(ret, "Push one child failed.");
            ret = db_set_UcmFwdInfo_stAclID_usAclID(child_iterator, 65535);
        }
    }

    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stIPv6AclID", NULL, NULL, &child_iterator);
        CHECK_OK_RET(ret, "Create child for write object failed.");
        for (array_idx = 0; array_idx < 8; array_idx++) {
            ret = db_child_push_back(child_iterator);
            CHECK_OK_RET(ret, "Push one child failed.");
            ret = db_set_UcmFwdInfo_stIPv6AclID_usIPv6AclID(child_iterator, 65535);
        }
    }

    if (array != 0) {
        ret = db_create_child_node(object, NULL, "stGrpAcl", NULL, NULL, &child_iterator);
        CHECK_OK_RET(ret, "Create child for write object failed.");
        for (array_idx = 0; array_idx < 8; array_idx++) {
            ret = db_child_push_back(child_iterator);
            CHECK_OK_RET(ret, "Push one child failed.");
            ret = db_set_UcmFwdInfo_stGrpAcl_usGrpAcl(child_iterator, 65535);
        }
    }

    return ret;
}
status_t static_UcmFwdInfo_pri_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_special_num,
                                    void *key_info, void *key_data)
{
//     status_t ret = 0;

//     ret = db_set_UcmFwdInfo_key_field_ulCidPrefix(object, uiVrIndex & (uint32_t)(~0));
//     CHECK_OK_RET(ret, "Set ulCidPrefix key failed.");
//     ret = db_set_UcmFwdInfo_key_field_ulCID(object, uiVrIndex & (uint32_t)(~0));
//     CHECK_OK_RET(ret, "Set ulCID key failed.");
//     return ret;
    
    db_key_info_t *p_key_info = (db_key_info_t *)key_info;
    p_key_info->key_len = sizeof(db_UcmFwdInfo_key_t);
    p_key_info->index_id = DB_UCMFWDINFO_UCMFWDINFO_KEY_PRIM_ID;

    db_UcmFwdInfo_key_t *p_key_data = (db_UcmFwdInfo_key_t *)key_data;

    p_key_data->ulCidPrefix = uiVrIndex & (uint32_t)(~0);
    p_key_data->ulCID = uiVrIndex & (uint32_t)(~0);
    return 0;
}

status_t static_UcmFwdInfo_update(db_object object, uint64_t index)
{
    status_t ret = STATUS_OK;
    ret = db_set_UcmFwdInfo_ulNodeId(object, (index & (uint32_t)(~0)) + 1);
    CHECK_OK_RET(ret, "set_UcmFwdInfo_ulNodeId failed");
    return ret;
}
status_t static_UcmFwdInfo_fullscan_func(db_object object, uint64_t *op)
{
    status_t ret = STATUS_OK;
    db_root_iterator root_iterator;
    uint64_t scan_idx = 0;
    ret = db_create_root_iter(object, NULL, NULL, &root_iterator);
    CHECK_OK_RET(ret, "Create root iterator failed.");

    for (ret = db_root_iterator_next(root_iterator);; ret = db_root_iterator_next(root_iterator)) {
        if (ret == STATUS_SCAN_TO_END) {
            ret = STATUS_OK;
            break;
        }
        CHECK_OK_RET(ret, "Scan table failed");
        // ret = print_sample_object(scan_idx, object, GET_CHILD_BY_IDX, 0);
        ret = static_UcmFwdInfo_get_field_func(object, FIELD_CNT_ALL);
        CHECK_OK_RET(ret, "get field failed");
        scan_idx++;
    }
    *op = scan_idx;
    return ret;
}
status_t static_UcmFwdInfo_register_schema_model(void *schema_model)
{
    p_static_schema_model_t model = (p_static_schema_model_t)(schema_model);
    if (model->schema_json_file_name == NULL) {
        model->schema_json_file_name = "UcmFwdInfo";
        model->bobj_reset_func = static_UcmFwdInfo_reset_db_batch_obj_func;
        model->obj_create_func = static_UcmFwdInfo_create_db_obj_func;        
        model->obj_create_spec_conn_func = static_UcmFwdInfo_create_db_spec_conn_obj_func;
        model->obj_release_func = static_UcmFwdInfo_release_db_obj_func;
        model->obj_reset_func = static_UcmFwdInfo_reset_db_obj_func;
        model->obj_set_func = static_UcmFwdInfo_set_obj_func;
        model->key_set_func = NULL;
        model->pri_key_set_func = static_UcmFwdInfo_pri_key_set_func;
        model->update_func = static_UcmFwdInfo_update;
        model->getfield_func = static_UcmFwdInfo_get_field_func;
        model->fullscan_func = static_UcmFwdInfo_fullscan_func;
    }
    return 0;
}
