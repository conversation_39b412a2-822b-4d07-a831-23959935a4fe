
#include "mac_fdb_mem_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
// #include "cpu_cycles.h"
mac_fdb_mem_struct_t g_obj_struct;
p_static_schema_model_t g_mac_fdb_mem_model = NULL;

#define LOCAL_INDEX_EXIST 1

status_t static_mac_fdb_mem_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                                    db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_mac_fdb_mem_obj(obj_type, conn_type, &obj);
    CHECK_OK_RET(ret, "Create mac_fdb_mem object.");

    *object = obj;
    return ret;
}


status_t static_mac_fdb_mem_create_db_obj_spec_conn_func(db_object_type obj_type, db_connect_t conn,
                                                  db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_mac_fdb_mem_obj_specific_conn(obj_type, conn, &obj);
    CHECK_OK_RET(ret, "Create mac_fdb_mem spec conn object.");

    *object = obj;
    return ret;
}

void static_mac_fdb_mem_release_db_obj_func(db_object object)
{
    db_release_mac_fdb_mem_object(object);
}

status_t static_mac_fdb_mem_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_mac_fdb_mem_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}
status_t static_mac_fdb_mem_reset_db_batch_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_mac_fdb_mem_batch_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_mac_fdb_mem_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array,
                                              uint32_t thread_special_num)
{
    status_t ret = 0;
    mac_fdb_mem_struct_t obj_struct = {0};
    if (g_mac_fdb_mem_model->data_value_type == 1) {
        uint32_t rand_num32 = rand() % 4294967296;
        uint8_t rand_num8 = rand() % 256;
        obj_struct.vrid = 0;
        obj_struct.bd_type = 2;
        obj_struct.bd_id = rand_num32 % 4096;
        obj_struct.mac_service_type = (uiVrIndex & 0xf);
        obj_struct.mac_type = rand_num8 % 13;
        obj_struct.valid = true;
        obj_struct.if_index = rand_num32 % 4096;
        obj_struct.vp_index = (uiVrIndex & 0xffff);
        obj_struct.out_vlanid = (uiVrIndex & 0xff);
        obj_struct.ce_vlanid = (uiVrIndex & 0xff);
        obj_struct.pe_vlanid = (uiVrIndex & 0xff);
        obj_struct.lsp_token = (uiVrIndex & 0xffff);
        obj_struct.vc_label = (uiVrIndex & 0xffff);
        obj_struct.vc_id = (uiVrIndex & 0xffff);
        obj_struct.learn_time = (uiVrIndex & 0xffff);
        obj_struct.vsi = (uiVrIndex & 0xffff);
        obj_struct.vnid = (uiVrIndex & 0xffff);
        obj_struct.addresstype = (uiVrIndex& 0xf);
        obj_struct.hit = (uiVrIndex & 0xf);  

        for (int i = 0; i < 6; i++) {
            obj_struct.mac_address[i] = rand() % 256;
        }
        for (int i = 0; i < 16; i++) {
            obj_struct.peerip[i] = rand() % 256;
        }
    } else if (g_mac_fdb_mem_model->data_value_type == 2) {
        uint32_t rand_num32 = rand() % 4294967296;
        uint8_t rand_num8 = rand() % 256;
        obj_struct.vrid = 0;
        obj_struct.bd_type = 2;
        obj_struct.bd_id = rand_num32 % 32768;
        obj_struct.mac_service_type = (uiVrIndex & 0xf);
        obj_struct.mac_type = rand_num8 % 13;
        obj_struct.valid = true;
        obj_struct.if_index = rand_num32 % 32768;
        obj_struct.vp_index = (uiVrIndex & 0xffff);
        obj_struct.out_vlanid = (uiVrIndex & 0xff);
        obj_struct.ce_vlanid = (uiVrIndex & 0xff);
        obj_struct.pe_vlanid = (uiVrIndex & 0xff);
        obj_struct.lsp_token = (uiVrIndex & 0xffff);
        obj_struct.vc_label = (uiVrIndex & 0xffff);
        obj_struct.vc_id = (uiVrIndex & 0xffff);
        obj_struct.learn_time = (uiVrIndex & 0xffff);
        obj_struct.vsi = (uiVrIndex & 0xffff);
        obj_struct.vnid = (uiVrIndex & 0xffff);
        obj_struct.addresstype = (uiVrIndex& 0xf);
        obj_struct.hit = (uiVrIndex & 0xf);           

        for (int i = 0; i < 6; i++) {
            obj_struct.mac_address[i] = rand() % 256;
        }
        for (int i = 0; i < 16; i++) {
            obj_struct.peerip[i] = rand() % 256;
        }
    } else {
        obj_struct.vrid = 0;
        obj_struct.bd_type = 2;
        obj_struct.bd_id = ((uiVrIndex + 2) & (uint32_t)(~0)) % 4096;
        obj_struct.mac_service_type = (uiVrIndex & 0xf);
        obj_struct.mac_type = (uiVrIndex & (uint8_t)(~0)) % 13;
        obj_struct.valid = true;
        obj_struct.if_index = (uiVrIndex & (uint32_t)(~0)) % 4096;
        obj_struct.vp_index = (uiVrIndex & 0xffff);
        obj_struct.out_vlanid = (uiVrIndex & 0xff);
        obj_struct.ce_vlanid = (uiVrIndex & 0xff);
        obj_struct.pe_vlanid = (uiVrIndex & 0xff);
        obj_struct.lsp_token = (uiVrIndex & 0xffff);
        obj_struct.vc_label = (uiVrIndex & 0xffff);
        obj_struct.vc_id = (uiVrIndex & 0xffff);
        obj_struct.learn_time = (uiVrIndex & 0xffff);
        obj_struct.vsi = (uiVrIndex & 0xffff);
        obj_struct.vnid = (uiVrIndex & 0xffff);
        obj_struct.addresstype = (uiVrIndex& 0xf);
        obj_struct.hit = (uiVrIndex & 0xf);  

        for (int i = 0; i < 4; i++)  // 32 bit, because of uint32_t
        {
            obj_struct.mac_address[5 - i] = (((uint32_t)uiVrIndex) >> (i * 8)) & 0xFF;
        }
        for (int i = 0; i < 16; i++) {
            obj_struct.peerip[i] = rand() % 256;
        }
    }

    obj_struct.app_source_id = (uiVrIndex & (uint32_t)(~0));
    obj_struct.app_serial_id = (uiVrIndex & (uint32_t)(~0));
    obj_struct.app_obj_id = (uiVrIndex & (uint64_t)(~0));
    obj_struct.app_version = (uiVrIndex & (uint32_t)(~0));
set_again:
    ret = db_set_mac_fdb_mem_all_fields(object, &obj_struct, sizeof(mac_fdb_mem_struct_t));
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }

    return ret;
}

status_t static_mac_fdb_mem_primary_key_malloc_func(void **key)
{
    *key = malloc(sizeof(db_mac_fdb_mem_key_t));
    return 0;
}

void static_mac_fdb_mem_primary_key_free_func(void *key)
{
    free(key);
}

status_t static_mac_fdb_mem_struct_data_malloc_func(void **data, uint32_t *length)
{
    *length = sizeof(mac_fdb_mem_struct_t);
    *data = malloc(*length);
    return 0;
}

void static_mac_fdb_mem_struct_data_free_func(void *data)
{
    free(data);
}

status_t static_mac_fdb_mem_struct_data_local_scan_func(void * left, void * right, void * cnt)
{
#if LOCAL_INDEX_EXIST==1    
    status_t ret = 0;
    //uint32_t t_left = *((uint32_t*)left);
    //uint32_t t_right = *((uint32_t*)right);
    
    db_key_info_t key_info;      
    db_mac_fdb_mem_k_if_t left_key, right_key;
    left_key.vrid = 0;
    left_key.if_index = 0;

    right_key.vrid = 0;
    right_key.if_index = 1000000;
    key_info.key_len = sizeof(left_key);
    key_info.index_id = DB_MAC_FDB_MEM_K_IF_ID;

    db_object object = NULL;
    ret = db_create_mac_fdb_mem_obj(QUERY_OBJ, SYNC_MODE, &object);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "obj_create");
        return -1;
    }

    ret = db_create_stmt(object, STMT_SCAN_RANGE, NULL, DMDB_SORT_ASCENDING_ORDER);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "db_create_stmt.");
         return -1;
    }
	ret = db_append_stmt_condition(object, "local_key", 0, CMP_OP_RANGE_L_SE, NULL, 0, &key_info, &left_key, &right_key);	
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "db_append_stmt_condition.");
         return -1;
    }

    uint32_t scan_idx = 0;
    db_root_iterator root_iterator;
    ret = db_create_root_iterator_by_filter(object, &root_iterator);
    for (ret = db_root_iterator_next(root_iterator);; ret = db_root_iterator_next(root_iterator)) {
        if (ret == STATUS_SCAN_TO_END) {
            ret = STATUS_OK;
            break;
        }

        CHECK_OK_RET(ret, "Scan table by local failed");
        mac_fdb_mem_struct_t read_data;
        ret =  db_get_mac_fdb_mem_all_fields(object, (void *)&read_data, sizeof(mac_fdb_mem_struct_t));
        CHECK_OK_RET(ret, "Get object data failed.");
        scan_idx++;
    }

    *(uint32_t*)cnt = scan_idx;

    return 0;
#else
    return -1;
#endif    
}



status_t static_mac_fdb_mem_pri_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_special_num,
                                         void *key_info, void *key_data)
{
    db_key_info_t *p_key_info = (db_key_info_t *)key_info;
    p_key_info->index_id = DB_MAC_FDB_MEM_MAC_FDB_PK_ID;
    p_key_info->key_len = sizeof(db_mac_fdb_mem_key_t);    
    db_mac_fdb_mem_key_t *p_key_data = (db_mac_fdb_mem_key_t *)key_data;
    if (g_mac_fdb_mem_model->data_value_type == 1) {
        //#if defined(DMAC_FDB_SETLOCAL_RAND)
        uint32_t rand_num32 = rand() % 4294967296;
        p_key_data->vrid = 0;
        p_key_data->bd_type = 2;
        p_key_data->bd_id = rand_num32 % 4096;
        memset(p_key_data->mac_address, 0, sizeof(p_key_data->mac_address));
        for (int i = 0; i < 6; i++) {
            p_key_data->mac_address[i] = rand() % 256;
        }
    } else if (g_mac_fdb_mem_model->data_value_type == 2) {
    //#elif defined(MAC_FDB_SETADDR_RAND)
        uint32_t rand_num32 = rand() % 4294967296;
        p_key_data->vrid = 0;
        p_key_data->bd_type = 2;
        p_key_data->bd_id = rand_num32 % 32768;
        memset(p_key_data->mac_address, 0, sizeof(p_key_data->mac_address));
        for (int i = 0; i < 6; i++) {
            p_key_data->mac_address[i] = rand() % 256;
        }
    } else {
        p_key_data->vrid = 0;
        p_key_data->bd_type = 2;
        p_key_data->bd_id = ((uiVrIndex + 2) & (uint32_t)(~0)) % 4096;
        memset(p_key_data->mac_address, 0, sizeof(p_key_data->mac_address));

        for (int i = 0; i < 4; i++)  // 32 bit, because of uint32_t
        {
            p_key_data->mac_address[5 - i] = (((uint32_t)uiVrIndex) >> (i * 8)) & 0xFF;
        }
    }

    return 0;
}

// only can exec data_value_type = 0;
status_t static_mac_fdb_mem_get_field_by_key_func(db_object object, uint64_t uiVrIndex,
                                                     uint32_t thread_special_num, uint32_t read_field_cnt)
{
    status_t ret;

    db_key_info_t key_info;
    key_info.key_len = sizeof(db_mac_fdb_mem_key_t);
    key_info.index_id = DB_MAC_FDB_MEM_MAC_FDB_PK_ID;

    db_mac_fdb_mem_key_t key_data;
    key_data.vrid = 0;
    key_data.bd_type = 2;
    key_data.bd_id = ((uiVrIndex + 2) & (uint32_t)(~0)) % 4096;

    memset(key_data.mac_address, 0, sizeof(key_data.mac_address));
    for (int i = 0; i < 4; i++)  // 32 bit, because of uint32_t
    {
        key_data.mac_address[5 - i] = (((uint32_t)uiVrIndex) >> (i * 8)) & 0xFF;
    }


    mac_fdb_mem_struct_t read_data;
    //DB_START_TEST_CPU_CYCLES(lite_read_object_data);
    ret = db_read_obj_with_buf(object, &key_info, &key_data, &read_data, sizeof(mac_fdb_mem_struct_t));
    //DB_STOP_TEST_CPU_CYCLES(lite_read_object_data);
    return ret;
}
status_t static_mac_fdb_mem_register_schema_model(void *schema_model)
{ 
    g_mac_fdb_mem_model = (p_static_schema_model_t)(schema_model);
    (void)memset_s(&g_obj_struct, sizeof(mac_fdb_mem_struct_t), 0, sizeof(mac_fdb_mem_struct_t));

    if (g_mac_fdb_mem_model->schema_json_file_name == NULL) {
        g_mac_fdb_mem_model->schema_json_file_name = "mac_fdb_mem";
        g_mac_fdb_mem_model->obj_create_func = static_mac_fdb_mem_create_db_obj_func;
        g_mac_fdb_mem_model->obj_release_func = static_mac_fdb_mem_release_db_obj_func;
        g_mac_fdb_mem_model->obj_reset_func = static_mac_fdb_mem_reset_db_obj_func;
        g_mac_fdb_mem_model->bobj_reset_func = static_mac_fdb_mem_reset_db_batch_obj_func;
        g_mac_fdb_mem_model->pri_key_set_func = static_mac_fdb_mem_pri_key_set_func;
        g_mac_fdb_mem_model->obj_set_func = static_mac_fdb_mem_set_obj_func;
        g_mac_fdb_mem_model->malloc_primary_key_func = static_mac_fdb_mem_primary_key_malloc_func;
        g_mac_fdb_mem_model->free_primary_key_func = static_mac_fdb_mem_primary_key_free_func;
        g_mac_fdb_mem_model->malloc_struct_data_func = static_mac_fdb_mem_struct_data_malloc_func;
        g_mac_fdb_mem_model->free_struct_data_func = static_mac_fdb_mem_struct_data_free_func;
        g_mac_fdb_mem_model->data_value_type = 0;
        
        g_mac_fdb_mem_model->getfield_bykey_func = static_mac_fdb_mem_get_field_by_key_func;
        g_mac_fdb_mem_model->local_scan_func = static_mac_fdb_mem_struct_data_local_scan_func;
        g_mac_fdb_mem_model->obj_create_spec_conn_func = static_mac_fdb_mem_create_db_obj_spec_conn_func;


    }
    return 0;
}
