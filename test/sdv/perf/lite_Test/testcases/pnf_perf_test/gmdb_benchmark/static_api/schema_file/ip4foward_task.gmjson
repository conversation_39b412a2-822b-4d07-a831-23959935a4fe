{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "ip4forward", "config": {"check_validity": true}, "max_record_count": 4000000, "fields": [{"name": "vr_id", "type": "uint32", "nullable": false}, {"name": "vrf_index", "type": "uint32"}, {"name": "dest_ip_addr", "type": "uint32"}, {"name": "mask_len", "type": "uint8"}, {"name": "nhp_group_flag", "type": "uint8"}, {"name": "qos_profile_id", "type": "uint16"}, {"name": "primary_label", "type": "uint32"}, {"name": "attribute_id", "type": "uint32"}, {"name": "nhp_group_id", "type": "uint32"}, {"name": "route_flags", "type": "uint32"}, {"name": "flags", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "nhpgroupid_localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["nhp_group_id"], "constraints": {"unique": false}}, {"name": "primary_label_localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["primary_label"], "constraints": {"unique": true}}, {"name": "local_key", "index": {"type": "local"}, "node": "ip4forward", "fields": ["attribute_id", "primary_label"]}, {"name": "hashcluster_key", "index": {"type": "hashcluster"}, "node": "ip4forward", "fields": ["route_flags"], "constraints": {"unique": false}}, {"name": "flags_hashcluster_key", "index": {"type": "hashcluster"}, "node": "ip4forward", "fields": ["flags"], "constraints": {"unique": true}}, {"name": "lpm_key", "index": {"type": "lpm4_tree_bitmap"}, "node": "ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "根据lpm4索引"}]}