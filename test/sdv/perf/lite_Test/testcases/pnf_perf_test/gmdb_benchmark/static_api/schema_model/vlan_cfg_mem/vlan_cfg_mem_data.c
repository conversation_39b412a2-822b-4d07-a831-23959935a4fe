
#include "vlan_cfg_mem_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
// #include "cpu_cycles.h"
vlan_cfg_mem_struct_t g_obj_struct;
status_t static_vlan_cfg_mem_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                                     db_object *object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_vlan_cfg_mem_obj(obj_type, conn_type, &obj);
    CHECK_OK_RET(ret, "Create vlan_cfg_mem object.");

    *object = obj;
    return ret;
}

void static_vlan_cfg_mem_release_db_obj_func(db_object object)
{
    db_release_vlan_cfg_mem_object(object);
}

status_t static_vlan_cfg_mem_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_vlan_cfg_mem_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_vlan_cfg_mem_reset_db_batch_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    ret = db_reset_vlan_cfg_mem_batch_object(object, obj_type);
    CHECK_OK_RET(ret, "Reset for object failed.");
    return ret;
}

status_t static_vlan_cfg_mem_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array,
                                               uint32_t thread_special_num)
{
    status_t ret = 0;
    g_obj_struct.vrid = (uiVrIndex & (uint32_t)(~0));
    g_obj_struct.vlan_id = (uiVrIndex & (0xffff));
    g_obj_struct.admin_state = (uiVrIndex & (0xff));
    g_obj_struct.static_enable = (uiVrIndex & (0xff));
    char *fix_name_s = "fixed";
    (void)memcpy_s(g_obj_struct.vlan_name, 32, fix_name_s, strlen(fix_name_s) + 1);
    (void)memcpy_s(g_obj_struct.description, 64, fix_name_s, strlen(fix_name_s) + 1);
    g_obj_struct.transparent = (uiVrIndex & (uint32_t)(~0));
    g_obj_struct.vlan_type = (uiVrIndex & (0xff));
    g_obj_struct.app_source_id = (uiVrIndex & (uint32_t)(~0));
    g_obj_struct.app_serial_id = (uiVrIndex & (uint32_t)(~0));
    g_obj_struct.app_obj_id = (uiVrIndex & (uint32_t)(~0));
    g_obj_struct.app_version = (uiVrIndex & (uint32_t)(~0));
set_again:
    ret = db_set_vlan_cfg_mem_all_fields(object, &g_obj_struct, sizeof(vlan_cfg_mem_struct_t));
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }
    CHECK_OK_RET(ret, "db_set_vlan_cfg_mem_all_fields failed.");

    return ret;
}

status_t static_vlan_cfg_mem_primary_key_malloc_func(void **key)
{
    *key = malloc(sizeof(db_vlan_cfg_mem_key_t));
    return 0;
}

void static_vlan_cfg_mem_primary_key_free_func(void *key)
{
    free(key);
}

status_t static_vlan_cfg_mem_struct_data_malloc_func(void **data, uint32_t *length)
{
    *length = sizeof(vlan_cfg_mem_struct_t);
    *data = malloc(*length);
    return 0;
}

void static_vlan_cfg_mem_struct_data_free_func(void *data)
{
    free(data);
}

status_t static_vlan_cfg_mem_register_schema_model(void *schema_model)
{
    p_static_schema_model_t model = (p_static_schema_model_t)(schema_model);
    (void)memset_s(&g_obj_struct, sizeof(vlan_cfg_mem_struct_t), 0, sizeof(vlan_cfg_mem_struct_t));

    if (model->schema_json_file_name == NULL) {
        model->schema_json_file_name = "vlan_cfg_mem";
        model->obj_create_func = static_vlan_cfg_mem_create_db_obj_func;
        model->obj_release_func = static_vlan_cfg_mem_release_db_obj_func;
        model->obj_reset_func = static_vlan_cfg_mem_reset_db_obj_func;
        model->bobj_reset_func = static_vlan_cfg_mem_reset_db_batch_obj_func;
        model->obj_set_func = static_vlan_cfg_mem_set_obj_func;
        model->malloc_primary_key_func = static_vlan_cfg_mem_primary_key_malloc_func;
        model->free_primary_key_func = static_vlan_cfg_mem_primary_key_free_func;
        model->malloc_struct_data_func = static_vlan_cfg_mem_struct_data_malloc_func;
        model->free_struct_data_func = static_vlan_cfg_mem_struct_data_free_func;
    }
    return 0;
}
