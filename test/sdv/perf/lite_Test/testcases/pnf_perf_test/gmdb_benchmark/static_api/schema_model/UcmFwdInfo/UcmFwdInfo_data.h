#ifndef __UCMFWDINFO_DATA_H__
#define __UCMFWDINFO_DATA_H__

#include "tools.h"
#include "db_UcmFwdInfo.h"

status_t static_UcmFwdInfo_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type, db_object *object);
void static_UcmFwdInfo_release_db_obj_func(db_object object);
status_t static_UcmFwdInfo_reset_db_obj_func(db_object object, db_object_type obj_type);
status_t static_UcmFwdInfo_reset_db_batch_obj_func(db_object object, db_object_type obj_type);
status_t static_UcmFwdInfo_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array,
                                    uint32_t thread_special_num);
status_t static_UcmFwdInfo_get_field_func(db_object object, uint32_t read_field_cnt);
status_t static_UcmFwdInfo_update(db_object object, uint64_t index);
status_t static_UcmFwdInfo_fullscan_func(db_object object, uint64_t *op);

status_t static_UcmFwdInfo_register_schema_model(void *);
#endif
