{"comment": "bfd ctrl表", "version": "2.0", "type": "record", "name": "bfd_ctrl", "max_record_count": 8192, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "state", "type": "uint8", "comment": "state"}, {"name": "pos_type", "type": "uint8", "comment": "pos_type"}, {"name": "tos_exp", "type": "uint8", "comment": "tos_exp"}, {"name": "flag", "type": "uint8", "comment": "flag"}, {"name": "node_group_id", "type": "uint32", "comment": "node_group_id"}, {"name": "act_rcv_int", "type": "uint32", "comment": "act_rcv_int"}, {"name": "act_trans_int", "type": "uint32", "comment": "act_trans_int"}, {"name": "act_det_mult", "type": "uint32", "comment": "act_det_mult"}, {"name": "out_ifindex", "type": "uint32", "comment": "out_ifindex"}, {"name": "bfd_diag", "type": "uint8", "comment": "bfd_diag"}, {"name": "sess_mode", "type": "uint8", "comment": "sess_mode"}, {"name": "sess_type", "type": "uint16", "comment": "sess_type"}, {"name": "flag_ext", "type": "uint32", "comment": "flag_ext"}, {"name": "track_ifindex", "type": "uint32", "comment": "track_ifindex"}, {"name": "pdt_sess_index", "type": "uint32", "comment": "pdt_sess_index"}, {"name": "pdt_global_index", "type": "uint32", "comment": "pdt_global_index"}, {"name": "np_id", "type": "uint8", "comment": "np_id"}, {"name": "path_flag", "type": "uint8", "comment": "path_flag"}, {"name": "app_source_id", "type": "uint32", "comment": "app_source_id"}, {"name": "app_serial_id", "type": "uint32", "comment": "app_serial_id"}, {"name": "app_obj_id", "type": "uint64", "comment": "app_obj_id"}, {"name": "app_version", "type": "uint32", "comment": "app_version"}], "keys": [{"name": "bfd_ctrl_pk", "index": {"type": "primary"}, "node": "bfd_ctrl", "fields": ["my_discr"], "constraints": {"unique": true}}]}