{"comment": "mlag peerlink cfg fesId 2175", "version": "2.0", "type": "record", "name": "mlag_peer_link", "config": {"check_validity": false}, "max_record_count": 16, "fields": [{"name": "vrid", "type": "uint32"}, {"name": "if_index", "type": "uint32"}, {"name": "link_id", "type": "uint32"}, {"name": "link_type", "type": "uint16"}, {"name": "tunnel_type", "type": "uint16"}, {"name": "src_ipv4_addr", "type": "uint32"}, {"name": "dst_ipv4_addr", "type": "uint32"}, {"name": "src_ipv6_addr", "type": "fixed", "size": 16}, {"name": "dst_ipv6_addr", "type": "fixed", "size": 16}, {"name": "reserved", "type": "uint32"}], "keys": [{"name": "pk", "index": {"type": "primary"}, "node": "mlag_peer_link", "fields": ["vrid"], "constraints": {"unique": true}}, {"name": "ifk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mlag_peer_link", "fields": ["if_index"], "constraints": {"unique": true}}]}