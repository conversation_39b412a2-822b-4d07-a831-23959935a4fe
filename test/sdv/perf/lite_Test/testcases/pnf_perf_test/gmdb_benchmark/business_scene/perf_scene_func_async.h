#ifndef __MFIB_FUNC_ASYNC_H_
#define __MFIB_FUNC_ASYNC_H_

#include "perf_scene_common.h"
#include "gm_errno.h"

typedef struct tagspec_execute_user_data_t {
    volatile uint64_t expect_total_record_count;
    volatile uint64_t curr_total_record_count;
    struct timeval start_time;
    struct timeval end_time;
} special_batch_execute_user_data_t;

extern db_connect_t g_spec_async_conn;
status_t special_prepare_connection_async_epoll(special_config_t *conf);

status_t special_prepare_connection_async(special_config_t *conf);
status_t special_read_primary_async(special_config_t *conf, int table_index);
status_t special_batch_write_async(special_config_t *conf, int table_index);
status_t special_mix_read_sync_batch_write_async(special_config_t *conf, int table_index, int read_table_index);
//#ifdef EPOLL
status_t special_epoll_clean_async();
//#endif
//void special_create_table_callback(void* callback_argu, uint64_t msg_id, status_t status);
void set_batch_write_stop_flag(db_bool value);
void special_batch_execute_callback(
    uint32_t batch_id, uint32_t total_num, uint32_t success_num, status_t status, void* callback_argu);


void special_batch_execute_callbackFib(
    uint32_t batch_id, uint32_t total_num, uint32_t success_num, status_t status, void* callback_argu);


void wrapped_sem_init();
void wrapped_sem_wait(unsigned int timeout);
void wrapped_sem_post();


#endif
