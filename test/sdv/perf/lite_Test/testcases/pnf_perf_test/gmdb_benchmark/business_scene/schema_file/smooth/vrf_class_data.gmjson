{"comment": "vrf数据", "version": "2.0", "type": "record", "name": "vrf_class_data", "config": {"check_validity": true}, "max_record_count": 1200000, "fields": [{"name": "table_id", "type": "uint32", "comment": "vrpv8中的fes table_id"}, {"name": "producer_pid", "type": "uint32", "comment": "table对应的生产者Pid"}, {"name": "vr_id", "type": "uint32", "comment": "VR ID"}, {"name": "vrf_index", "type": "uint32", "comment": "VRF ID"}, {"name": "topology_id", "type": "uint32", "comment": "拓扑ID"}, {"name": "vrf_state", "type": "uint32", "comment": "表状态"}, {"name": "vrf_data_machine", "type": "uint8", "comment": "此Pid+tableid+vrid+vrfid下的状态机"}, {"name": "vrf_state_version", "type": "uint32", "comment": "此Pid和tableId下的VrfState版本号"}, {"name": "vrf_data_version", "type": "uint32", "comment": "此Pid+tableId+vrid+Vrf下的VrfData版本号"}, {"name": "smooth_flag", "type": "uint32", "comment": "此Pid+tableId+vrid+Vrf下数据是否平滑结束"}, {"name": "time_stamp_verify_begin", "type": "time", "comment": "最近一轮对账的开始时间"}, {"name": "time_stamp_verify_end", "type": "time", "comment": "最近一轮对账的结束时间"}, {"name": "time_stamp_age_begin", "type": "time", "comment": "最近一轮对账中老化的开始时间"}, {"name": "time_stamp_age_end", "type": "time", "comment": "最近一轮对账中老化的结束时间"}], "keys": [{"name": "vrf_class_data_key", "index": {"type": "primary"}, "node": "vrf_class_data", "fields": ["table_id", "producer_pid", "vr_id", "vrf_index"], "constraints": {"unique": true}, "comment": "primary key"}, {"name": "localhash_key", "index": {"type": "hashcluster"}, "node": "vrf_class_data", "fields": ["table_id", "producer_pid"], "constraints": {"unique": false}, "comment": "table_id + producer_pid索引"}]}