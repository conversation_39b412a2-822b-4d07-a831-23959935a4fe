{"comment": "ARP表项定义", "version": "2.0", "type": "record", "name": "arp", "config": {"check_validity": false}, "max_record_count": 512000, "fields": [{"name": "ip_address", "type": "fixed", "size": 4, "comment": "ARP的IPv4目的地址"}, {"name": "if_index", "type": "uint32", "comment": "ARP对应的三层接口索引"}, {"name": "vr_id", "type": "uint32", "comment": "ARP表出接口的所在的VR"}, {"name": "vrf_index", "type": "uint32", "comment": "ARP表出接口的所在的VRF(VNP)"}, {"name": "mac_address", "type": "fixed", "size": 6, "comment": "ARP的MAC地址"}, {"name": "fake_flag", "type": "uint8", "comment": "ARP真表/假表标记, 对应原来的arpFlag属性: 0-假表, 1-真表"}, {"name": "link_type", "type": "uint8", "comment": "链路类型"}, {"name": "work_if_index", "type": "uint32", "comment": "物理出接口"}, {"name": "atm_if_index", "type": "uint32", "comment": "预留, 仅ATM口时使用, 即VE口或者VE加入Vlanif"}, {"name": "target_blade", "type": "uint16", "comment": "物理出接口TB, 对应原来的tb属性"}, {"name": "target_port", "type": "uint16", "comment": "物理出接口TP, 对应原来的tp属性"}, {"name": "pe_vid", "type": "uint16", "comment": "子接口或vlanif的vlanid, QinQ外层VLAN"}, {"name": "ce_vid", "type": "uint16", "comment": "QinQ内层vlan"}, {"name": "vcd", "type": "uint32", "comment": "预留, ATM的Vcd索引"}, {"name": "fwd_if_type", "type": "uint16", "comment": "转发接口类型, LDM使用"}, {"name": "tunnel_type", "type": "uint8", "comment": "隧道类型, 可以表示vxlan或nvgre"}, {"name": "path_flag", "type": "uint8", "comment": "path完备性标记"}, {"name": "if_phy_type", "type": "uint32", "comment": "ARP表出接口的物理接口类型"}, {"name": "main_if_phy_type", "type": "uint32", "comment": "主接口类型"}, {"name": "if_link_type", "type": "uint32", "comment": "ARP表物理出接口的链路类型"}, {"name": "if_encap_type", "type": "uint32", "comment": "ARP表出接口的封装类型"}, {"name": "flow_id", "type": "uint32", "comment": "流索引, FES预留, 当前没用到"}, {"name": "tunnel_vrf_id", "type": "uint32", "comment": "对应evnindex"}, {"name": "tunnel_id", "type": "uint32", "comment": "Tunnel id"}, {"name": "tunnel_encap_id", "type": "uint32", "comment": "如果为VXLAN隧道, 该字段为vniid; 如果为nvgre, 该字段为vsid"}, {"name": "tunnel_sip", "type": "uint32", "comment": "VxLAN隧道上学习到的BDIF ARP, 通知MAC模块时, 要传这个IPv6隧道保存的隧道ID"}, {"name": "tunnel_dip", "type": "uint32", "comment": "VxLAN隧道上学习到的BDIF ARP, 通知MAC模块时, 要传这个; 若为二层子接口上学习到的BDIF"}, {"name": "vsi_index", "type": "uint32", "comment": "vsi索引复用, NHP下发vsi_index 指导封装vlan用"}, {"name": "smooth_id", "type": "uint32", "comment": "记载当前组的全局序列号, 该序列号每次ARP更新时递增, 各种ARP更新场合下使用"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识,对应VRP8 hSrcPid字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理, 即使KEY和data相同, 但删除后再添加时, 这个ID也会不同, 具体使用场景不明确, 暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号, 用于跟踪同一条记录的变化情形, 具体使用场景不明确, 暂时保留不用"}, {"name": "adj_service_status", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE下发状态"}, {"name": "adj_service_errcode", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE返回错误码"}, {"name": "adj_svc_context_high_prio", "type": "fixed", "size": 32, "default": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的ARP上下文"}, {"name": "adj_svc_context_normal_prio", "type": "fixed", "size": 32, "default": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的ARP上下文"}, {"name": "nhp_service_status", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE下发状态"}, {"name": "nhp_service_errcode", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE返回错误码"}, {"name": "nhp_svc_context_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的ARP上下文"}, {"name": "nhp_svc_context_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的ARP上下文"}, {"name": "vlanid", "type": "uint16", "comment": "VLANif绑定的VLAN"}, {"name": "bridge_vlanid", "type": "uint16", "comment": "ARP的桥接vlan, vlanif使用"}, {"name": "is_trunk", "type": "uint16", "comment": "是否为Trunk口, 先预留"}, {"name": "mlag_flag", "type": "uint16", "comment": "mlag标志"}, {"name": "state", "type": "uint8", "comment": "ARP表预留状态标记"}, {"name": "sub_if_index", "type": "uint32", "comment": "subif子接口索引"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "arp_key", "index": {"type": "primary"}, "node": "arp", "fields": ["ip_address", "if_index"], "constraints": {"unique": true}, "comment": "主索引"}, {"name": "arp_mac_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["vr_id", "vlanid", "mac_address"], "comment": "根据MAC+VLAN索引ARP表, MAC漂移场景使用"}, {"name": "arp_mac_vsi_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["vr_id", "vsi_index", "mac_address"], "comment": "根据MAC+VSI索引ARP表, MAC漂移(BDIF)场景使用"}, {"name": "arp_if_index_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["if_index"], "comment": "根据if_index索引ARP表"}, {"name": "arp_work_if_index_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["work_if_index"], "comment": "根据work_if_index索引ARP表"}, {"name": "arp_vpn_ip_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["ip_address", "vr_id", "vrf_index"], "constraints": {"unique": true}, "comment": "VPN+IP的索引"}, {"name": "arp_tunnel_encap_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["tunnel_type", "tunnel_sip", "tunnel_dip"], "constraints": {"unique": false}, "comment": "跟据隧道封装信息索引ARP表"}, {"name": "arp_sub_if_index_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["sub_if_index"], "comment": "根据sub_if_index索引ARP表"}, {"name": "arp_fwd_if_type_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["fwd_if_type"], "comment": "根据fwd_if_type索引ARP表"}, {"name": "arp_tnl6_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["tunnel_type", "tunnel_id"], "comment": "tnl6隧道表key索引ARP表"}, {"name": "arp_if_pevid_index", "index": {"type": "hashcluster"}, "node": "arp", "fields": ["if_index", "pe_vid"], "comment": "根据if_index以及vlanid索引ARP表"}]}