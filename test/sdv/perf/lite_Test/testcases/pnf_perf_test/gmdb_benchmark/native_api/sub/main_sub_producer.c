#include <string.h>
#include <inttypes.h>
#include <sys/time.h>
#include <signal.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdio.h>
#include "sub_common.h"
#include "gm_sub.h"
#include "pthread.h"
#include "tools.h"
#include "type.h"
#define SCHEMA_FILE "ip4addr_status.gmjson"

typedef struct output_t {
    uint64_t write_count;
    double write_ops;
} output_t;
static output_t output;
typedef struct fib_status {
    uint32_t ifindex;
    char family;
    char prefix_length;
    char scope;
    char *local_ip4;
    char *peer_ip4;
    char *broadcast_ip4;
    char *multicast_ip4;
    char *anycast_ip4;
    char *label;
    uint32_t valid_lft;
    uint32_t preferred_lft;
    uint32_t create_time;
    uint32_t last_update_time;
    uint32_t flags;
} fib_status_t;
fib_status_t fib_status = {
    .ifindex = 1,
    .family = 2,
    .prefix_length = 8,
    .scope = 254,
    .local_ip4 = "127.0.0.1",
    .peer_ip4 = "",
    .broadcast_ip4 = "***************",
    .multicast_ip4 = "",
    .anycast_ip4 = "",
    .label = "lo",
    .valid_lft = 4294967295,
    .preferred_lft = 4294967295,
    .create_time = 2914,
    .last_update_time = 2914,
    .flags = 128,
};
typedef struct config_t {
    // uint32_t table_low;
    // uint32_t table_high;
    char server_locator[128];
    uint32_t batch_wr_loop;
    uint32_t record_num;
    uint32_t para;
    uint32_t table_num;
} config_t, *p_config_t;
static config_t g_conf;

typedef struct tag_argument {
    pthread_t thread_id; /* Current thread id. */
    connection_t conn;   /* Connection handle. */
    object_t write_obj;
    schema_t schema;
    char table_name[10]; /* Table name. */
    uint64_t write_count;
    uint32_t batch_wr_loop;
    uint32_t data_min;
    uint32_t data_max;
    double write_ops;
    db_bool is_success;
} thread_arg_t, *p_thread_arg_t;
static p_thread_arg_t g_thread_args;

static db_bool is_single_table = DB_FALSE;
static int create_object(schema_t *lschema, object_t *obj_t, fib_status_t *fib_status)
{
    int ret;
    node_t root_node;
    schema_t schema = *lschema;
    ret = dmdb_create_object(schema, DB_FALSE, obj_t);
    if (ret) {
        return 1;
    }
    object_t full_obj = *obj_t;
    dmdb_get_root_node(full_obj, &root_node);

    dmdb_value_t value;
    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = fib_status->ifindex;
    ret = dmdb_set_field(root_node, "ifindex", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.type = DMDB_DATATYPE_UINT8;
    value.value.byte_val = fib_status->family;
    ret = dmdb_set_field(root_node, "family", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.byte_val = fib_status->prefix_length;
    ret = dmdb_set_field(root_node, "prefix_length", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.byte_val = fib_status->scope;
    dmdb_set_field(root_node, "scope", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.type = DMDB_DATATYPE_STRING;
    value.value.ref = fib_status->local_ip4;
    dmdb_set_field(root_node, "local_ip4", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.ref = fib_status->peer_ip4;
    dmdb_set_field(root_node, "peer_ip4", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.ref = fib_status->broadcast_ip4;
    dmdb_set_field(root_node, "broadcast_ip4", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.ref = fib_status->multicast_ip4;
    dmdb_set_field(root_node, "multicast_ip4", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.ref = fib_status->anycast_ip4;
    dmdb_set_field(root_node, "anycast_ip4", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.ref = fib_status->label;
    dmdb_set_field(root_node, "label", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = fib_status->valid_lft;
    dmdb_set_field(root_node, "valid_lft", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.uint_val = fib_status->preferred_lft;
    dmdb_set_field(root_node, "preferred_lft", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.uint_val = fib_status->create_time;
    dmdb_set_field(root_node, "create_time", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.uint_val = fib_status->last_update_time;
    dmdb_set_field(root_node, "last_update_time", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    value.value.uint_val = fib_status->flags;
    dmdb_set_field(root_node, "flags", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");
    return 0;
}
extern void *dmdb_get_field_data_addr(node_t node_handle, const char *name);
static void *object_argument_func(object_t *obj)
{
    node_t root_node;
    dmdb_get_root_node(*obj, &root_node);
    return dmdb_get_field_data_addr(root_node, "ifindex");
}

static uint32_t threads_arg_init(p_config_t conf)
{
    uint32_t ret;
    // uint32_t threads = conf->table_high - conf->table_low + 1;
    uint32_t threads = conf->para;
    g_thread_args = (p_thread_arg_t)malloc(sizeof(thread_arg_t) * threads);
    if (g_thread_args == NULL) {
        LOG_ERROR(1, ",malloc fail.");
        return 1;
    }
    // uint32_t table_start = conf->table_low;
    uint32_t records = conf->record_num / threads;
    int i;
    for (i = 0; i < threads; i++) {
        g_thread_args[i].batch_wr_loop = conf->batch_wr_loop;
        g_thread_args[i].write_count = 0;
        g_thread_args[i].is_success = DB_TRUE;
        g_thread_args[i].data_min = i * records;
        g_thread_args[i].data_max = (i + 1) * records - 1;
        // snprintf(g_thread_args[i].table_name, 10, "sub_%03d", i + table_start);
        if (is_single_table) {
            snprintf(g_thread_args[i].table_name, 10, "sub_001");
        }
        else {
            snprintf(g_thread_args[i].table_name, 10, "sub_%03d", i + 1);
        }
        printf("thread %d table name %s, data min %d, data max %d\n", i, g_thread_args[i].table_name,
               g_thread_args[i].data_min, g_thread_args[i].data_max);
        ret = dmdb_connect(conf->server_locator, NULL, "osc", &g_thread_args[i].conn);
        if (ret) {
            return 1;
        }
        ret = dmdb_get_schema_by_table_name_sync(g_thread_args[i].conn, g_thread_args[i].table_name, 0,
                                                 &g_thread_args[i].schema);
        if (ret) {
            return 1;
        }
        ret = create_object(&g_thread_args[i].schema, &g_thread_args[i].write_obj, &fib_status);
        if (ret) {
            LOG_ERROR(ret, "create obj fail");
            return 1;
        }
    }
    return 0;
}
static void *batch_write(void *arg)
{
    uint32_t ret;
    struct timeval start, stop;
    double tcost;
    p_thread_arg_t thread_arg = (p_thread_arg_t)arg;
    connection_t conn = thread_arg->conn;
    object_t write_obj = thread_arg->write_obj;
    uint64_t write_count = 0;
    uint32_t wr_loop = thread_arg->batch_wr_loop;
    uint32_t begin = thread_arg->data_min;
    uint32_t end = thread_arg->data_max;
    int i = begin, j;
    batch_t batch_handle;
    uint32_t total_num, success_num;

    uint32_t *obj_addr = (uint32_t *)object_argument_func(&write_obj);
    gettimeofday(&start, DB_NULL);
    while (1) {
        j = 0;
        ret = dmdb_prepare_batch(conn, &batch_handle);
        if (ret) {
            LOG_ERROR(ret, "prepare_batch");
            break;
        }
        while (++j) {
            *obj_addr = i++;
            ret = dmdb_add_write(batch_handle, NULL, write_obj, 0);
            if (ret) {
                LOG_ERROR(ret, "add write");
                break;
            }
            if (j == wr_loop || STATUS_BATCH_BUFFER_FULL == ret || i > end) {
                ret = dmdb_execute_batch(batch_handle, &total_num, &success_num);
                if (ret) {
                    LOG_ERROR(ret, "execute_batch");
                    break;
                }
                write_count += success_num;
                break;
            }
        }
        if (ret || i > end) {
            break;
        }
    }
    gettimeofday(&stop, DB_NULL);
    if (ret) {
        thread_arg->is_success = DB_FALSE;
    }
    TimeFlow(start, stop, tcost);
    thread_arg->write_ops = write_count / tcost;
    thread_arg->write_count = write_count;
    return (void *)NULL;
}

static uint32_t multi_threads_set(p_config_t conf)
{
    uint32_t i, ret;
    p_thread_arg_t thread_arg;
    // uint32_t threads = conf->table_high - conf->table_low + 1;
    uint32_t threads = conf->para;
    for (i = 0; i < threads; i++) {
        thread_arg = &g_thread_args[i];
        ret = pthread_create(&thread_arg->thread_id, DB_NULL, batch_write, (void *)thread_arg);
        if (ret != STATUS_OK) {
            LOG_ERROR(STATUS_CREATE_THREAD_FAILED, "Create benchmark thread failed, id=%u, err_msg=%s.", i, strerror(errno));
        }
    }
    sleep(2);
    for (i = 0; i < threads; i++) {
        thread_arg = &g_thread_args[i];
        ret = pthread_join(thread_arg->thread_id, DB_NULL);
        if (ret != STATUS_OK) {
            LOG_ERROR(STATUS_INTERNAL_ERROR, "Join benchmark thread failed, id=%u, err_msg=%s.", i, strerror(errno));
        }
    }
    return 0;
}
static void print_batchwr_info(p_config_t conf)
{
    int i;
    uint64_t totalwritecount = 0;
    double wr_ops = 0.0;
    // uint32_t threads = conf->table_high - conf->table_low + 1;
    uint32_t threads = conf->para;
    for (i = 0; i < threads; i++) {
        if (!g_thread_args[i].is_success) {
            printf("thread %d write fail.\n", i);
        }
        totalwritecount += g_thread_args[i].write_count;
        wr_ops += g_thread_args[i].write_ops;
    }
    output.write_ops = wr_ops;
    output.write_count = totalwritecount;
    printf("total write count : %" PRId64 ".\n", output.write_count);
    printf("batch_write_ops : %.4f kops.\n", output.write_ops / 1000);
}
static void release(p_config_t conf)
{
    int i;
    // 	char table_name[10] = {0};
    // uint32_t threads = conf->table_high - conf->table_low + 1;
    // 	uint32_t table_start = conf->table_low;
    uint32_t threads = conf->para;
    if (g_thread_args) {
        for (i = 0; i < threads; i++) {
            dmdb_release_object(g_thread_args[i].write_obj);
            dmdb_release_schema(g_thread_args[i].conn, g_thread_args[i].schema);

            // 			snprintf(table_name, 10, "sub_%03d", i+table_start);
            // 			dmdb_drop_table_sync(g_thread_args[i].conn,table_name);
            dmdb_disconnect(g_thread_args[i].conn);
        }
        free(g_thread_args);
    }
}
static void usage(char *basename)
{
    // printf("usage : %s [-h] [-s] -p <parallel num> -c <record num> -t <table start> -T <table end> -m <mode> -b <batch write records once>\n", basename);
    printf("usage : %s [-h] [-s] -p <parallel num> -c <record num> -m <mode> -b <batch write records once>\n",
           basename);
    printf("-h: help\n");
    printf("-c: write records\n");
    // 	printf("-t: lowlimit table to write\n");
    // 	printf("-T: highlimit table to write\n");
    printf("-m: <0|2>. 2 is shm, 0 is usocket\n");
    printf("-b: object counts for one batch\n");
    printf("-p: parallel number\n");
    printf("-s: single table\n");
    exit(1);
}
static void init_config(int argc, char *argv[], p_config_t conf)
{
    uint32_t mode;
    char *com_mod[3] = { PERF_USOCKET_MODE, PERF_TCP_MODE, PERF_SHM_MODE };
    memset(conf, 0, sizeof(config_t));
    strncpy(conf->server_locator, PERF_USOCKET_MODE, 128);
    conf->record_num = 120000;
    uint32_t opt;
    while ((opt = getopt(argc, argv, "hm:c:b:p:s")) != -1) {
        switch (opt) {
            case 'm':
                mode = atoi(optarg);
                if (mode > 2) {
                    usage(argv[0]);
                }
                strncpy(conf->server_locator, com_mod[mode], 128);
                break;
            // case 't':
            // conf->table_low = atoi(optarg);
            // break;
            // case 'T':
            // conf->table_high = atoi(optarg);
            // break;
            case 'b':
                conf->batch_wr_loop = atoi(optarg);
                break;
            case 'c':
                conf->record_num = atoi(optarg);
                break;
            case 'p':
                conf->para = atoi(optarg);
                break;
            case 's':
                is_single_table = DB_TRUE;
                break;
            case 'h':
            default: /* '?' */
                usage(argv[0]);
                break;
        }
    }
#if 0
    if (conf->table_high < conf->table_low)
    {
        printf("table wrong\n");
        usage(argv[0]);
    }
#endif
    conf->table_num = (is_single_table) ? 1 : conf->para;
    return;
}
static void print_config(p_config_t conf)
{
    printf("-----GMDB sub producer config information----\n");
    printf("%-15s:  %s\n", "operation mode", "producer");
    printf("%-15s:  %s\n", "ServerLocator", conf->server_locator);
    printf("%-15s:  %u\n", "record count", conf->record_num);
    printf("%-15s:  %u\n", "threads", conf->para);
    printf("%-15s:  %u\n", "table_num", conf->table_num);
    printf("\n");
    return;
}
static uint32_t read_schema_file(char *file_name, char **p_schema)
{
    FILE *fd = DB_NULL;
    fd = fopen(file_name, "r");
    if (fd == DB_NULL) {
        LOG_ERROR(STATUS_FILE_OPEN, "Open schema file %s failed, err_msg=%s.", file_name, strerror(errno));
        return STATUS_FILE_OPEN;
    }
    fseek(fd, 0, SEEK_END);
    size_t fileLen = (size_t)ftell(fd);
    if (fileLen == 0) {
        LOG_ERROR(STATUS_FILE_NULL, "Schema file '%s' is null, err_msg=%s.", file_name, strerror(errno));
        fclose(fd);
        return STATUS_FILE_NULL;
    }

    size_t size_alloc = sizeof(char) * (fileLen + 1);
    char *schema_str = (char *)malloc(size_alloc);
    if (schema_str == DB_NULL) {
        fclose(fd);
        return STATUS_OUT_OF_MEMORY;
    }
    (void)memset(schema_str, 0, size_alloc);
    fseek(fd, 0, SEEK_SET);

    if (fread(schema_str, fileLen, sizeof(char), fd) == 0) {
        LOG_ERROR(STATUS_FILE_IO_ERR, "Read file failed.");
        fclose(fd);
        free(schema_str);
        return STATUS_FILE_IO_ERR;
    }

    if (schema_str[fileLen - 1] == '\n') {
        schema_str[fileLen - 1] = '\0';
    }
    else {
        schema_str[fileLen] = '\0';
    }
    fclose(fd);
    *p_schema = schema_str;
    return 0;
}

int main(int argc, char *argv[])
{
    init_config(argc, argv, &g_conf);
    print_config(&g_conf);

    int i, ret = 0;
    char *schema_str = DB_NULL;
    ret = read_schema_file(SCHEMA_FILE, &schema_str);
    if (ret) {
        return ret;
    }
    connection_t conn;
    ret = dmdb_connect(g_conf.server_locator, NULL, "osc", &conn);
    if (ret) {
        LOG_ERROR(ret, "connect");
        free(schema_str);
        return ret;
    }
    // 	int tablenum = g_conf.table_high - g_conf.table_low + 1;
    int tablenum = g_conf.table_num;
    char table_name[10] = { 0 };
    // 	uint32_t table_start = g_conf.table_low;
    for (i = 1; i <= tablenum; i++) {
        // snprintf(table_name, 10, "sub_%03d", i+table_start);
        snprintf(table_name, 10, "sub_%03d", i);
        dmdb_create_table_sync(conn, table_name, schema_str, NULL);
    }
    free(schema_str);
    dmdb_disconnect(conn);
    sleep(3);
    system("kill -10 `pidof main_sub_consumer`");

    sleep(6);  // wait consumer for finishing subscribe
    // consumer subscribe done,producer start batch write
    if (threads_arg_init(&g_conf)) {
        release(&g_conf);
        return 1;
    }

    multi_threads_set(&g_conf);

    print_batchwr_info(&g_conf);
    sleep(10);
    // release threads resource
    release(&g_conf);
    return 0;
}
