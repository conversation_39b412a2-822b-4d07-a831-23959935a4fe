#include <stdio.h>

#include "ip4route_fowarding.h"
#include "gm_interface_sync.h"
#include "gm_sub.h"

// create primary key
status_t ip4route_fowarding_create_primary_key(schema_t schema, ip4route_fowarding_key_t *ip4route_fowarding_key,
                                               obj_key_t *primary_key)
{
    CheckNULL(ip4route_fowarding_key);
    CheckNULL(primary_key);

    status_t ret;
    dmdb_value_t value[4];

    value[0].type = DMDB_DATATYPE_UINT32;
    value[0].value.uint_val = ip4route_fowarding_key->uiVrIndex;

    value[1].type = DMDB_DATATYPE_UINT32;
    value[1].value.uint_val = ip4route_fowarding_key->uiVrfIndex;

    value[2].type = DMDB_DATATYPE_UINT32;
    value[2].value.uint_val = ip4route_fowarding_key->uiDestAddr;

    value[3].type = DMDB_DATATYPE_UINT8;
    value[3].value.byte_val = ip4route_fowarding_key->ucMaskLen;

    ret = dmdb_create_key(schema, "ip4route_fowarding_key", 4, value, primary_key);
    CHECK_OK_RET(ret, "dmdb_create_key");

    return STATUS_OK;
}

static status_t insert_data_ip4route_nhp_index(node_t ip4route_nhp_index_node,
                                               ip4route_nhp_index_t *ip4route_nhp_index)
{
    dmdb_value_t value;
    status_t ret;
    node_t node_item;
    while (ip4route_nhp_index != DB_NULL) {
        ret = dmdb_append_array_node(ip4route_nhp_index_node, &node_item);
        CHECK_OK_RET(ret, "dmdb_append_array_node");
        value.type = DMDB_DATATYPE_UINT32;
        value.value.uint_val = ip4route_nhp_index->uiNhpIndex;
        ret = dmdb_set_field(node_item, "uiNhpIndex", &value);
        CHECK_OK_RET(ret, "dmdb_set_field");

        ip4route_nhp_index = ip4route_nhp_index->next;
    }

    return STATUS_OK;
}

// Create object with data
status_t ip4route_fowarding_set_object_with_data(ip4route_fowarding_t *ip4route_fowarding, object_t object)
{
    status_t ret;
    node_t root_node;
    dmdb_value_t value;

    ret = dmdb_get_root_node(object, &root_node);
    CHECK_OK_RET(ret, "dmdb_get_root_node");

    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = ip4route_fowarding->uiVrIndex;
    ret = dmdb_set_field(root_node, "uiVrIndex", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = ip4route_fowarding->uiVrfIndex;
    ret = dmdb_set_field(root_node, "uiVrfIndex", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = ip4route_fowarding->uiDestAddr;
    ret = dmdb_set_field(root_node, "uiDestAddr", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT8;
    value.value.byte_val = ip4route_fowarding->ucMaskLen;
    ret = dmdb_set_field(root_node, "ucMaskLen", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = ip4route_fowarding->uiPrimaryLabel;
    ret = dmdb_set_field(root_node, "uiPrimaryLabel", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = ip4route_fowarding->uiAttributeId;
    ret = dmdb_set_field(root_node, "uiAttributeId", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT16;
    value.value.ushort_val = ip4route_fowarding->usQosid;
    ret = dmdb_set_field(root_node, "usQosid", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    node_t ip4route_nhp_index;
    ret = dmdb_find_child(root_node, "ip4route_nhp_index", DB_NULL, 0, DB_FALSE, &ip4route_nhp_index);
    CHECK_OK_RET(ret, "dmdb_find_child");

    ret = insert_data_ip4route_nhp_index(ip4route_nhp_index, ip4route_fowarding->ip4route_nhp_index);
    CHECK_OK_RET(ret, "insert_data_ip4route_nhp_index");

    return STATUS_OK;
}
