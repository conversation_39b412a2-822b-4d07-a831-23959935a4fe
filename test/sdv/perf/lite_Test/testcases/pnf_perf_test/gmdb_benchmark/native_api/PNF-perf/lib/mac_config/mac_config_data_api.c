#include "mac_config_data_api.h"
#include "schema_model.h"
extern void *dmdb_get_primary_key_data_addr(obj_key_t key);
extern void *dmdb_get_field_data_addr(node_t node_handle, const char *name);

mac_config_t g_mac_config = {
    .vlan_id = 0x0001,
    .port = "12345",
    .if_index = 0x001001,
    .is_static = 0x01,
    .mac_addr = "01:02:03:04:0f:0e",
    .ageing = 25,
};

mac_config_key_t g_mac_config_key = {
    .vlan_id = 0x0001,
    .port = "12345",
    .mac_addr = "01:02:03:04:0f:0e",
};
/* Init array node */
void mac_config_init_arraynode(uint32_t arraycnt)
{
    return;
}

/* Init primary key's argument. */
void *mac_config_get_read_key_init_arg()
{
    return &g_mac_config_key;
}

/* Init primary key's function. */
status_t mac_config_init_read_key_func(schema_t schema, obj_key_t *key, void *arg)
{
    mac_config_key_t *mac_config_pk = (mac_config_key_t *)arg;
    status_t ret = STATUS_OK;
    ret = mac_config_create_primary_key(schema, mac_config_pk, key);
    CHECK_OK_RET(ret, "Create mac_config primary key failed.");
    return STATUS_OK;
}

/* Dynamic change primary key's argument function's argument. */
void *mac_config_get_read_key_change_key_arg()
{
    return DB_NULL;
}

/* Dynamic change primary key's argument function. */
void *mac_config_change_read_key_argument_func(obj_key_t key, void *arg)
{
    return dmdb_get_primary_key_data_addr(key);
}

/* Dynamic change primary key's function. */
void mac_config_change_read_key_func(obj_key_t *key, uint32_t i, void *change_key_arg)
{
    *(uint32_t *)change_key_arg = i;
}

/* Init writed object's function's argument. */
void *mac_config_get_write_obj_init_arg()
{
    return &g_mac_config;
}

/* Init writed object's function. */
status_t mac_config_init_write_obj_func(schema_t schema, object_t *obj, void *arg)
{
    mac_config_t *mac_config = (mac_config_t *)arg;
    status_t ret = dmdb_create_object(schema, DB_FALSE, obj);
    CHECK_OK_RET(ret, "Create object failed.");

    object_t full_obj = *obj;
    ret = mac_config_set_object_with_data(mac_config, full_obj);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "Set object with data failed.");
        dmdb_release_object(full_obj);
        *obj = DB_NULL;
        return ret;
    }

    return STATUS_OK;
}
status_t mac_config_get_field_func(schema_t schema, node_t node, uint32_t array)
{
    return STATUS_OK;
}
status_t mac_config_create_write_obj_func(object_t obj, uint32_t index, uint32_t array)
{
    status_t ret = 0;
    dmdb_value_t value;
    node_t root_node;

    dmdb_get_root_node(obj, &root_node);
    value.type = DMDB_DATATYPE_UINT16;
    value.value.ushort_val = 0x0001;
    ret = dmdb_set_field(root_node, "vlan_id", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_STRING;
    char port[10] = { 0 };
    snprintf(port, 10, "%d", index);
    value.value.ref = port;
    ret = dmdb_set_field(root_node, "port", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT32;
    value.value.uint_val = 0x001001;
    ret = dmdb_set_field(root_node, "if_index", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT8;
    value.value.byte_val = 0x01;
    ret = dmdb_set_field(root_node, "is_static", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_STRING;
    value.value.ref = "01:02:03:04:0f:0e";
    ret = dmdb_set_field(root_node, "mac_addr", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    value.type = DMDB_DATATYPE_UINT8;
    value.value.byte_val = 25;
    ret = dmdb_set_field(root_node, "ageing", &value);
    CHECK_OK_RET(ret, "dmdb_set_field");

    return ret;
}

/* Dynamic change object's argument function's argument. */
void *mac_config_get_write_obj_change_obj_arg()
{
    return DB_NULL;
}

/* Dynamic change object's argument function. */
void *mac_config_change_object_argument_func(object_t obj, void *arg)
{
    node_t root_node;
    dmdb_get_root_node(obj, &root_node);
    return dmdb_get_field_data_addr(root_node, "vlan_id");
}

/* Dynamic change object's function. */
void mac_config_change_object_func(object_t *obj, uint32_t i, void *change_obj_arg)
{
    *(uint16_t *)change_obj_arg = i;
}
status_t mac_config_register_shcema_model(void)
{
    if (g_schema_model[2].schema_json_file_name == NULL) {
        g_schema_model[2].schema_json_file_name = "mac_config";
        g_schema_model[2].array_init_func = mac_config_init_arraynode;
        g_schema_model[2].key_init_arg = mac_config_get_read_key_init_arg();
        g_schema_model[2].key_init_func = mac_config_init_read_key_func;
        g_schema_model[2].obj_init_arg = mac_config_get_write_obj_init_arg();
        g_schema_model[2].obj_init_func = mac_config_init_write_obj_func;
        g_schema_model[2].obj_create_func = mac_config_create_write_obj_func;
        g_schema_model[2].key_info.change_key_arg = mac_config_get_read_key_change_key_arg();
        g_schema_model[2].key_info.change_key_arg_func = mac_config_change_read_key_argument_func;
        g_schema_model[2].key_info.change_key_func = mac_config_change_read_key_func;
        g_schema_model[2].obj_info.change_obj_arg = mac_config_get_write_obj_change_obj_arg();
        g_schema_model[2].obj_info.change_obj_arg_func = mac_config_change_object_argument_func;
        g_schema_model[2].obj_info.change_obj_func = mac_config_change_object_func;
        g_schema_model[2].get_field_func = mac_config_get_field_func;
    }
    return 0;
}
