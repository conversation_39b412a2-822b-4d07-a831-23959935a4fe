#ifndef __IP4ROUTE_FOWARDING_H__
#define __IP4ROUTE_FOWARDING_H__

#include "tools.h"
#include "gm_interface_sync.h"

typedef struct ip4route_nhp_index_t {
    uint32_t uiNhpIndex;
    struct ip4route_nhp_index_t *next;
} ip4route_nhp_index_t;

typedef struct ip4route_fowarding_t {
    uint32_t uiVrIndex;
    uint32_t uiVrfIndex;
    uint32_t uiDestAddr;
    uint8_t ucMaskLen;
    uint32_t uiPrimaryLabel;
    uint32_t uiAttributeId;
    uint16_t usQosid;
    ip4route_nhp_index_t *ip4route_nhp_index;
} ip4route_fowarding_t;

typedef struct ip4route_fowarding_key_t {
    uint32_t uiVrIndex;
    uint32_t uiVrfIndex;
    uint32_t uiDestAddr;
    uint32_t ucMaskLen;
} ip4route_fowarding_key_t;

status_t ip4route_fowarding_create_primary_key(schema_t schema, ip4route_fowarding_key_t *ip4route_fowarding_key,
                                               obj_key_t *primary_key);
status_t ip4route_fowarding_set_object_with_data(ip4route_fowarding_t *ip4route_fowarding, object_t object);

#endif
