#!/bin/bash
if [ $# -ne 3 ];then
    echo "Usage:$0 [x64|arm|arm64|arm64_32] [0-8] [sub channel]"
	echo "0:write only  1-8:1-8 subscribers"
	echo "sub channel: subscribe channel number of every client process"
	exit 1
fi
COMMUNICATION_MODE=(2 0) #0)	# 0-->Usocket  2->Shm.
COMMUNICATION_MODE_NAME=("shm" "usocket")
TEST_OPERATION_NAME=("write-only" "one-subscriber" "two-subscriber" "three-subscriber" "four-subscriber" "five-subscriber")

DB_PACKAGE_DIR=`pwd`/hpprun
GMDB_SERVER_BIN=${DB_PACKAGE_DIR}/bin/gmserver
GMDB_SERVER_CONF=${DB_PACKAGE_DIR}/conf/gmserver.ini
BENCHMARK_BIN=gmdb_benchmark_static
HPPD_BIN=${DB_PACKAGE_DIR}/bin/start_hpp.sh

RUN_COUNT=1 #Per perf test run one times."
RUN_TIME=120 #Per perf run 120s.
ARCH=$1
OP=$2   #operation mode
SUBCHANNEL=$3 #subChannelNumPerCli 
TEST_OPT="$4" 
CONSUMER_EVENT_MASK=3   #  3: DMDB_OBJ_INSERTED | DMDB_OBJ_MODIFIED   31: DMDB_OBJ_ALL
if [ -z ${SUBCHANNEL} ];then
    SUBCHANNEL=1
fi
# 加表锁读性能有优化，默认使用表锁
USE_FREE_LATCH=1
if [ $USE_FREE_LATCH -eq 1 ];then
	TEST_OPT+=" -j 1 "
fi

RESULT_DIR=sub_log
GET_RESOURCE_LOG_FILE=get_resource_${ARCH}.self.log

RESULT_FILE_SUFFIX="txt"
GET_RESOURCE_BIN="get_resource_${ARCH}"
GET_RESOURCE=1 #Don't run get resource while value is 1, othersize run.
GET_RESOURCE_SLEEP_TIME=100

mkdir -p ${RESULT_DIR}
echo > ${GET_RESOURCE_LOG_FILE}
echo "## $0 $@ ##"

case $ARCH in
x64)
	TOTAL_RECORD_COUNT=4000000
	SUB_TRIGGER_COUNT=(3000000 40000)
	TOTAL_SPACE_SIZE=8192
	PER_DEVICE_PAGE_COUNT=1024
	PRI_HASH_BUCKETS_COUNT=6000000
	SUB_CHANNEL_SIZE=102400
	CLIENT_THREAD_COUNT="1 6 8 10"
	WORKER_THREAD_NUM_LIST="5"
	SHMMAX=16805695488
	SHMALL=4102953

	sysctl -w kernel.shmmax=${SHMMAX}
	sysctl -w kernel.shmall=${SHMALL}
	;;
arm)
	TOTAL_RECORD_COUNT=120000
	SUB_TRIGGER_COUNT=(100000 1200)
	TOTAL_SPACE_SIZE=120
	PER_DEVICE_PAGE_COUNT=64
	PRI_HASH_BUCKETS_COUNT=500000
	SUB_CHANNEL_SIZE=2048
	WORKER_THREAD_NUM_LIST="2"
	CLIENT_THREAD_COUNT="1 2 4 6"

	#Prepare env
	kill -19 `pidof security_proxy_bin` 
	kill -19 `pidof acagent`
	;;
arm64)
	TOTAL_RECORD_COUNT=1400000
	SUB_TRIGGER_COUNT=(1200000 14000)
	TOTAL_SPACE_SIZE=2048
	PER_DEVICE_PAGE_COUNT=1024
	PRI_HASH_BUCKETS_COUNT=6000000
	SUB_CHANNEL_SIZE=64
	WORKER_THREAD_NUM_LIST="5"
	CLIENT_THREAD_COUNT="1 4 8 12"
	;;
arm64_32)
	TOTAL_RECORD_COUNT=120000
	TOTAL_SPACE_SIZE=120
	PER_DEVICE_PAGE_COUNT=64
	PRI_HASH_BUCKETS_COUNT=500000
	WORKER_THREAD_NUM_LIST="2 3 4"
	CLIENT_THREAD_COUNT="1 2 4 6 8 10 12"
	;;
*)
	echo "Error arch '$ARCH'."
	echo "Usage:$0 [x64|arm|arm64|arm64_32] [0|1|2] [sub channel]"
	exit
	;;
esac

export LD_LIBRARY_PATH=${DB_PACKAGE_DIR}/lib/

echo "Prepare config file 'gmserver.ini'."
sed -i "s/\(^subChannelSize = \).*$/\1${SUB_CHANNEL_SIZE}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^totalSpaceSize = \).*$/\1${TOTAL_SPACE_SIZE}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^perDevicePageCount = \).*$/\1${PER_DEVICE_PAGE_COUNT}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^priHashBucketsCount = \).*$/\1${PRI_HASH_BUCKETS_COUNT}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^subChannelNumPerCli = \).*$/\1${SUBCHANNEL}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^offloadingWithHpp = \).*$/\11/" ${GMDB_SERVER_CONF}
if [ ! -f "${DB_PACKAGE_DIR}/bin/start_hpp.sh" ];then
	sed -i "s/\(^offloadingWithHpp = \).*$/\10/" ${GMDB_SERVER_CONF}
fi
sed -i "s/\(^EnableShmHugePage = \).*$/\11/" ${GMDB_SERVER_CONF}
# sed -i "s/\(^loggingModel = \).*$/\12/" ${GMDB_SERVER_CONF}

check_hppd()
{
    if [ -f hpp_startinfo.log ];then
        echo "" > hpp_startinfo.log
    fi

	local hppd_pid=$(pidof hppd)
	if [ "X${hppd_pid}" != "X" ];then
		# kill -9 ${hppd_pid}
		sleep 1
        return
	fi
	for i in $(seq 100)
	do
		sleep 1
		hppd_pid=$(pidof hppd)
		if [ "X" = "X${hppd_pid}" ];then
			break
		fi
	done
    sleep 5
    sysctl -p > /dev/null
    rm -rf /mnt/verona/hpp_huge/*;sleep 1;ipcrm -a;sleep 1;cat /proc/meminfo
	hppd_pid=`pidof hppd`
	if [ "X${hppd_pid}" != "X" ];then
	    echo "---------hppd stop failed!---------"
		exit 1
	else
	    (${HPPD_BIN} hsd &)>hpp_startinfo.log 2>&1 &
	    sleep 15
	fi
	
	hppd_pid=`pidof hppd`
	if [ "X${hppd_pid}" != "X" ];then
	    echo "---------hppd started!---------"
	else
	    echo "---------hppd start failed!---------"
		exit 1
	fi
}

stop_restart_gmserver()
{
	local server_pid=$(pidof gmserver)
	if [ "X${server_pid}" != "X" ];then
		kill -9 ${server_pid}
	fi
	for i in $(seq 100)
	do
		sleep 1
		server_pid=$(pidof gmserver)
		if [ "X" = "X${server_pid}" ];then
			break
		fi
	done
	
    # if [ -f "${DB_PACKAGE_DIR}/bin/start_stlmd.sh" ];then
    #     ./restart_stlmd.sh ${DB_PACKAGE_DIR}
    # fi
    	
	if [ -f "${DB_PACKAGE_DIR}/bin/start_hpp.sh" ];then
		check_hppd
	fi

	rm -f /run/verona/GMDB*
	${GMDB_SERVER_BIN} -c ${GMDB_SERVER_CONF} -r -w 1 &
    sleep 5
    for i in $(seq 30)
    do
        sleep 1
        server_pid=$(ps -eT | grep gmdb_listen)
        if [ "X" != "X${server_pid}" ];then
            break
        fi
    done

        ./gt_gmpolicy_import.sh perf
        ${DB_PACKAGE_DIR}/bin/gmrule  permit uid[0] gid[0] connect server unix_emserver
}

wait_sub_process_exit()
{
    for i in $(seq ${RUN_TIME})
    do
    	local sub_pid=$(pidof main_sub_consumer)
    	if [ "X" = "X${sub_pid}" ];then
    		break
    	fi
    	sleep 1
    done
    sleep 1
}

before_get_resource()
{
	local run_times=$1
	local resource_result_file="$2"
	if [ "X${GET_RESOURCE}" = "X0" ];then
		return
	fi
	echo "**********Times:${run_times} Resource Information.**********" >> ${resource_result_file}
	echo "***********Machine lives and load average************" >> ${resource_result_file}
	uptime >> ${resource_result_file}
	echo "***********kernel message************" >> ${resource_result_file}
	dmesg -T |tail >> ${resource_result_file}
	echo "***********CPU compression************" >> ${resource_result_file}
	mpstat -P ALL >> ${resource_result_file}
    echo "*************IO*************" >> ${resource_result_file}
	iostat -z >> ${resource_result_file}
	echo "************memory free***************" >> ${resource_result_file}
	free -m >> ${resource_result_file}
	echo "************TOP******************" >> ${resource_result_file}
	top -n1 >> ${resource_result_file}
	local gmserver_pid=$(pidof gmserver)
	cat /proc/${gmserver_pid}/status | tail -n 2 >> ${resource_result_file}
}

running_get_resource()
{
	if [ "X${GET_RESOURCE}" = "X0" ];then
		return
	fi

	local resource_result_file="$1"

	local total_time=$(expr ${RUN_TIME} - 1)
	local benchmark_pid=$(pidof ${BENCHMARK_BIN})
	local gmserver_pid=$(pidof gmserver)

	local options="-s ${GET_RESOURCE_SLEEP_TIME} -t ${total_time} -p ${gmserver_pid},${benchmark_pid}"

	./${GET_RESOURCE_BIN} ${options}
	echo "+++++Gmserver (pid=${gmserver_pid}) resource information." >> ${resource_result_file}
	echo -e "cpucost\t\tvsize\trss" >> ${resource_result_file}
	cat resource.${gmserver_pid}.log >> ${resource_result_file}
	if [ $? -eq 0 ];then
	    rm resource.${gmserver_pid}.log
	fi
	echo "+++++Testcase (pid=${benchmark_pid}) resource information." >> ${resource_result_file}
	echo -e "cpucost\t\tvsize\trss" >> ${resource_result_file}
	cat resource.${benchmark_pid}.log >> ${resource_result_file}
    if [ $? -eq 0 ];then
	    rm resource.${benchmark_pid}.log
	fi
}

after_get_resource()
{
	local run_times=$1
	local resource_result_file="$2"
	local gmserver_pid=$(pidof gmserver)
	cat /proc/${gmserver_pid}/status | tail -n 2 >> ${resource_result_file}
	echo -e "**********Times:${run_times} Resource Information End.**********\n\n" >> ${resource_result_file}
}

get_time_space()
{
	local begin="$1"
	local end="$2"
	local begin_seconds=$(date --date="$begin" +%s)
	local end_seconds=$(date --date="$end" +%s)
	echo "Used time: "$((end_seconds-begin_seconds))"s"
}

run_test()
{
	local mode_idx=$1
	local op_idx=$2
	local client_thread_num=$3
	local perf_result_file="$4"
	local resource_result_file="$5"
	local total_ops=0
	for j in $(seq ${RUN_COUNT})
	do
	        killall main_sub_consumer
	        wait_sub_process_exit
                rm -rf consume*.log produce.log
	        	        
		stop_restart_gmserver
		./${BENCHMARK_BIN} -r 1 -m${COMMUNICATION_MODE[$mode_idx]} -f2
		#执行用例前获取资源占用
		before_get_resource ${j} ${resource_result_file}

		#Test operation.
		start_time=$(date +'%Y-%m-%d %H:%M:%S')
		echo "Operation:${TEST_OPERATION_NAME[$op_idx]} BeginTime:$start_time"
		if [ "${op_idx}" = "0" ];then
			echo "option:${BENCHMARK_BIN} -a 8 -m ${COMMUNICATION_MODE[$mode_idx]} -b 60 -l0 -L ${TOTAL_RECORD_COUNT} -t ${RUN_TIME} -p${client_thread_num} -f2 ${TEST_OPT}"
			(./${BENCHMARK_BIN} -a 8 -m ${COMMUNICATION_MODE[$mode_idx]} -b 60 -l0 -L ${TOTAL_RECORD_COUNT} -t ${RUN_TIME} -p${client_thread_num} -f2 ${TEST_OPT} | tee produce.log ) &
		fi
		if [ "${op_idx}" != "0" ];then
			echo "option:${BENCHMARK_BIN} -a 8 -m ${COMMUNICATION_MODE[$mode_idx]} -b 60 -l0 -L ${TOTAL_RECORD_COUNT} -t ${RUN_TIME} -p${client_thread_num} -f2 -d ${TEST_OPT}"
			for op in `seq 1 ${op_idx}`
			do
			        echo "./main_sub_consumer -t 0 -T 0 -m${COMMUNICATION_MODE[$mode_idx]} -c${SUB_TRIGGER_COUNT[$mode_idx]} -s ip4foward -e ip4foward -k $CONSUMER_EVENT_MASK"
				(./main_sub_consumer -t 0 -T 0 -m${COMMUNICATION_MODE[$mode_idx]} -c${SUB_TRIGGER_COUNT[$mode_idx]} -s ip4foward -e ip4foward -k $CONSUMER_EVENT_MASK | tee consume${op}.log) &
			done
			(./${BENCHMARK_BIN} -a 8 -m ${COMMUNICATION_MODE[$mode_idx]} -b 60 -l0 -L ${TOTAL_RECORD_COUNT} -t ${RUN_TIME} -p${client_thread_num} -f2 -d ${TEST_OPT} | tee produce.log) &
		fi
		running_get_resource ${resource_result_file}
		wait $!
		stop_time=$(date +'%Y-%m-%d %H:%M:%S')
		echo "${TEST_OPERATION_NAME[$op_idx]} finished. StopTime=${stop_time}"
		
		#用例执行结束后获取资源占用
		after_get_resource ${j} ${resource_result_file}

		get_time_space "$start_time" "$stop_time"
		local write_ops=$(grep "Ops_batch_write" produce.log | awk '{print $3}')
		local sub_ops=$(grep "sub_ops" consume*.log | awk -v sum=0 '{sum+=$3} END{print sum}')
		echo ":${TEST_OPERATION_NAME[$op_idx]}: times=${j} write_ops=${write_ops} sub_ops=${sub_ops}"
		local total_write_ops=$(awk -v x=${total_write_ops} -v y=${write_ops} 'BEGIN{printf ("%.5f",x+y)}')
		local total_sub_ops=$(awk -v x=${total_sub_ops} -v y=${sub_ops} 'BEGIN{printf ("%.5f",x+y)}')

		if [ "${op_idx}" != "0" ];then
                   wait_sub_process_exit    
                   killall main_sub_consumer               
		fi
	done
	local aver_write_ops=$(awk -v x=${total_write_ops} -v y=${RUN_COUNT} 'BEGIN{printf ("%.2f",x/y/1000)}')
	local aver_sub_ops=$(awk -v x=${total_sub_ops} -v y=${RUN_COUNT} 'BEGIN{printf ("%.2f",x/y)}')
	echo -e "${aver_write_ops} ${aver_sub_ops} \c" >> ${perf_result_file}

	echo "${TEST_OPERATION_NAME[$op_idx]}'s average ops is ${aver_write_ops} ${aver_sub_ops}"
}

do_specific_operation_test()
{
	local mode_idx=$1
	local op_idx=${OP}
	result_file_suffix="${RESULT_DIR}/${ARCH}_${COMMUNICATION_MODE_NAME[$mode_idx]}"
	local perf_result_file="${result_file_suffix}_${TEST_OPERATION_NAME[$op_idx]}.${RESULT_FILE_SUFFIX}"
	local resource_result_file="${result_file_suffix}_${TEST_OPERATION_NAME[$op_idx]}_resource.${RESULT_FILE_SUFFIX}"
	echo "ClientThreadCount ${CLIENT_THREAD_COUNT}" > ${perf_result_file}
	echo -e "\c" > ${resource_result_file}
	
	sed -i "s/\(^serverRpcMode = \).*$/\1${COMMUNICATION_MODE[$mode_idx]}/" ${GMDB_SERVER_CONF}
	for worker_num in ${WORKER_THREAD_NUM_LIST}
	do
		if [ "X${worker_num}" = "X0" ];then
			sed -i "s/\(^processMode = \).*$/\10/" ${GMDB_SERVER_CONF}
		else
			sed -i "s/\(^processMode = \).*$/\11/" ${GMDB_SERVER_CONF}
			sed -i "s/\(^workerThreadCount = \).*$/\1${worker_num}/" ${GMDB_SERVER_CONF}
		fi
		echo -e "WorkerCount:${worker_num} \c" >> ${perf_result_file}
		for client_thread_count in ${CLIENT_THREAD_COUNT}
		do
			client_thread_num=${client_thread_count}
			one_thread_max_record_count=$(expr ${TOTAL_RECORD_COUNT} / ${client_thread_num})

			echo "Operation:${TEST_OPERATION_NAME[$op_idx]}  WorkerCount:${worker_num}  ClientThreadCount:${client_thread_count}  OneThreadMaxRecordCount:${one_thread_max_record_count}" >> ${resource_result_file}

			run_test $mode_idx $op_idx $client_thread_num "$perf_result_file" "$resource_result_file"
		done
		echo -e ""  >> ${perf_result_file}
	done	

        echo "---------Logfile is:${perf_result_file}"
        echo "---------ResourceLogfile is:${resource_result_file}"
}

do_benchmark_test()
{
	for((mode_idx=0; mode_idx<${#COMMUNICATION_MODE[@]}; mode_idx++))
	do
		do_specific_operation_test $mode_idx
	done
}

do_benchmark_test

cp $0 ${RESULT_DIR}/
