#!/bin/bash
COMMUNICATION_MODE=(2) #0)	# 0-->Usocket  2->Shm.
COMMUNICATION_MODE_NAME=("shm" "usocket")

ARCH=$1

DB_PACKAGE_DIR=`pwd`/hpprun
GMDB_SERVER_BIN=${DB_PACKAGE_DIR}/bin/gmserver
GMDB_SERVER_CONF=${DB_PACKAGE_DIR}/conf/gmserver.ini
RESULT_DIR=pnftst_log

RESULT_FILE_SUFFIX="txt"
GET_RESOURCE_BIN="get_resource_${ARCH}"
GET_RESOURCE=1 #Don't run get resource while value is 1, othersize run.
GET_RESOURCE_SLEEP_TIME=100

mkdir -p ${RESULT_DIR}

case $ARCH in
x64)
	TOTAL_SPACE_SIZE=8192
	PER_DEVICE_PAGE_COUNT=1024
	PRI_HASH_BUCKETS_COUNT=10000000
	SUB_CHANNEL_SIZE=102400
	WORKER_THREAD_NUM_LIST="6"
	SHMMAX=16805695488
	SHMALL=4102953

	sysctl -w kernel.shmmax=${SHMMAX}
	sysctl -w kernel.shmall=${SHMALL}
	;;
arm)
	TOTAL_SPACE_SIZE=120
	PER_DEVICE_PAGE_COUNT=64
	PRI_HASH_BUCKETS_COUNT=500000
	WORKER_THREAD_NUM_LIST="2"

	#Prepare env
	kill -19 `pidof security_proxy_bin` 
	kill -19 `pidof acagent`
	kill -19 `pidof nm`
	;;
arm64)
	TOTAL_SPACE_SIZE=3072
	PER_DEVICE_PAGE_COUNT=1024
	PRI_HASH_BUCKETS_COUNT=10000000
	SUB_CHANNEL_SIZE=20480
	WORKER_THREAD_NUM_LIST="2"
	;;
arm64_32)
	TOTAL_SPACE_SIZE=120
	PER_DEVICE_PAGE_COUNT=64
	PRI_HASH_BUCKETS_COUNT=500000
	WORKER_THREAD_NUM_LIST="2 3 4"
	;;
*)
	echo "Error arch '$ARCH'."
	echo "Usage:$0 ARCH:[x64|arm|arm64|arm64_32]"
	exit
	;;
esac

export LD_LIBRARY_PATH=${DB_PACKAGE_DIR}/lib/

echo "Prepare config file 'gmserver.ini'."
sed -i "s/\(^totalSpaceSize = \).*$/\1${TOTAL_SPACE_SIZE}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^perDevicePageCount = \).*$/\1${PER_DEVICE_PAGE_COUNT}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^priHashBucketsCount = \).*$/\1${PRI_HASH_BUCKETS_COUNT}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^subChannelSize = \).*$/\1${SUB_CHANNEL_SIZE}/" ${GMDB_SERVER_CONF}
sed -i "s/\(^subChannelNumPerCli = \).*$/\11/" ${GMDB_SERVER_CONF}

stop_restart_gmserver()
{
	local server_pid=$(pidof gmserver)
	if [ "X${server_pid}" != "X" ];then
		kill -9 ${server_pid}
	fi
	for i in $(seq 100)
	do
		sleep 1
		server_pid=$(pidof gmserver)
		if [ "X" = "X${server_pid}" ];then
			break
		fi
	done
    if [ -f "${DB_PACKAGE_DIR}/bin/start_stlmd.sh" ];then
        ./restart_stlmd.sh ${DB_PACKAGE_DIR}
    fi
    
	${GMDB_SERVER_BIN} -c ${GMDB_SERVER_CONF} -r -b
	sleep 1
}

before_get_resource()
{
	local resource_result_file="$1"
	if [ "X${GET_RESOURCE}" = "X0" ];then
		return
	fi
	echo "***********Machine lives and load average************" >> ${resource_result_file}
	uptime >> ${resource_result_file}
	echo "***********kernel message************" >> ${resource_result_file}
	dmesg -T |tail >> ${resource_result_file}
	echo "***********CPU compression************" >> ${resource_result_file}
	mpstat -P ALL >> ${resource_result_file}
    echo "*************IO*************" >> ${resource_result_file}
	iostat -z >> ${resource_result_file}
	echo "************memory free***************" >> ${resource_result_file}
	free -m >> ${resource_result_file}
	echo "************TOP******************" >> ${resource_result_file}
	top -n1 >> ${resource_result_file}
	local gmserver_pid=$(pidof gmserver)
	cat /proc/${gmserver_pid}/status | tail -n 2 >> ${resource_result_file}
}
export AVG_RES=0
get_avg_resource()
{
	local res_file=$1
	local avg=
	# get rid of first and last data
	awk '/^[0-9]/ {if(NR >= 2) print $1}' ${res_file} > tmp_res.log
    sed -i '$d' tmp_res.log
	# get average data
	AVG_RES=$(awk -v max=0 -v min=0 -v total=0 '{total=total+$1;if(NR==1){max=$1;min=$1} else if($1>max){max=$1} else if($1<min){min=$1}}END{printf "%.3f",(total-max-min)/(FNR-2)}' tmp_res.log)
	return $?
}
running_get_resource()
{
	if [ "X${GET_RESOURCE}" = "X0" ];then
		return
	fi

	local resource_result_file="$1"
	local client="$2"
	local total_time="$3"
	
	local client_pid=$(pidof ${client})
	local gmserver_pid=$(pidof gmserver)

	local options="-s ${GET_RESOURCE_SLEEP_TIME} -t ${total_time} -p ${gmserver_pid},${client_pid}"

	./${GET_RESOURCE_BIN} ${options}
	echo "+++++Gmserver (pid=${gmserver_pid}) resource information." >> ${resource_result_file}
	echo -e "cpucost\t\tvsize\trss" >> ${resource_result_file}
	cat resource.${gmserver_pid}.log >> ${resource_result_file}
	
	echo "+++++Testcase (pid=${client_pid}) resource information." >> ${resource_result_file}
	echo -e "cpucost\t\tvsize\trss" >> ${resource_result_file}
	cat resource.${client_pid}.log >> ${resource_result_file}
    
	get_avg_resource resource.${gmserver_pid}.log
	echo "gmserver_cpucons : $AVG_RES" >> ${resource_result_file}
	rm resource.${gmserver_pid}.log 
	
	get_avg_resource resource.${client_pid}.log
	echo "${client}_cpucons : $AVG_RES" >> ${resource_result_file}
	rm  resource.${client_pid}.log
}

after_get_resource()
{
	local resource_result_file="$1"
	local gmserver_pid=$(pidof gmserver)
	cat /proc/${gmserver_pid}/status | tail -n 2 >> ${resource_result_file}
	echo -e "**********Resource Information End.**********\n\n" >> ${resource_result_file}
}

get_time_space()
{
	local begin="$1"
	local end="$2"
	local begin_seconds=$(date --date="$begin" +%s)
	local end_seconds=$(date --date="$end" +%s)
	echo "Used time: "$((end_seconds-begin_seconds))"s"
}

do_specific_operation_test()
{
	local mode_idx=$1
	
	local result_file_suffix="${RESULT_DIR}/${ARCH}_${COMMUNICATION_MODE_NAME[$mode_idx]}"
    local perf_result_file=
	local resource_result_file=
	
	sed -i "s/\(^serverRpcMode = \).*$/\1${COMMUNICATION_MODE[$mode_idx]}/" ${GMDB_SERVER_CONF}
	for worker_num in ${WORKER_THREAD_NUM_LIST}
	do
		if [ "X${worker_num}" = "X0" ];then
			sed -i "s/\(^processMode = \).*$/\10/" ${GMDB_SERVER_CONF}
		else
			sed -i "s/\(^processMode = \).*$/\11/" ${GMDB_SERVER_CONF}
			sed -i "s/\(^workerThreadCount = \).*$/\1${worker_num}/" ${GMDB_SERVER_CONF}
		fi
		local testname=
		local base=
		local cmdline=
		local sub64=
		local sub32=
		local time=
		local init_recs=
		local m_flag=
		while read list
		do
			if [ -z "$list" ];then
				continue
			fi
			#skip #
			m_flag=$(echo $list | awk '{print substr($1,1,1)}')
			if [ "$m_flag" = "#" ];then
				continue
			fi
			testname=$(echo $list | awk '{print $1}')
			perf_result_file="${result_file_suffix}_${testname}_pnf.${RESULT_FILE_SUFFIX}"
            resource_result_file="${result_file_suffix}_${testname}_pnf_resource.${RESULT_FILE_SUFFIX}"
			echo "WorkerCount:${worker_num}  " > ${perf_result_file}
		    echo "WorkerCount:${worker_num}  " > ${resource_result_file}
			
			base=$(echo $list | awk '{print $2}' | sed 's/.\///')
			cmdline=$(echo $list | awk '{for(i=2;i<=NF-5;i++) printf $i OFS}')
			sub64=$(echo $list | awk '{print $(NF-3)}')
			sub32=$(echo $list | awk '{print $(NF-2)}')
			time=$(echo $list | awk '{print $(NF-1)}')
			init_recs=$(echo $list | awk '{print $NF}')
			stop_restart_gmserver
			if [ ${init_recs} -gt 0 ];then
				${cmdline} -C ${init_recs} -${COMMUNICATION_MODE[$mode_idx]}
			fi
			
			echo "$testname; sub64:$sub64 sub32:$sub32"
			echo "$testname; sub64:$sub64 sub32:$sub32" >> ${resource_result_file}
			echo "$testname; sub64:$sub64 sub32:$sub32" >> ${perf_result_file}
			before_get_resource ${resource_result_file}
			for sub64 in `seq 1 $sub64`
			do
				./sub_client_arm64 -t $base -i $time -${COMMUNICATION_MODE[$mode_idx]} &
			done
			for sub32 in `seq 1 $sub32`
			do
				./sub_client_arm32 -t $base -i $time -${COMMUNICATION_MODE[$mode_idx]} &
			done
			$cmdline -${COMMUNICATION_MODE[$mode_idx]} | tee temp.txt &
			running_get_resource ${resource_result_file} $base $time
			wait
			awk '/_ops/ {print $2" : "$4}' temp.txt >> ${perf_result_file}
			after_get_resource ${resource_result_file}
		done < testlists
	done	
}

do_benchmark_test()
{
	for((mode_idx=0; mode_idx<${#COMMUNICATION_MODE[@]}; mode_idx++))
	do
		do_specific_operation_test $mode_idx
	done
}

do_benchmark_test

cp $0 ${RESULT_DIR}/
