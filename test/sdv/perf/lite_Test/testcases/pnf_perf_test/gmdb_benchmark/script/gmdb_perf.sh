#!/bin/bash
ARGNUM=$#
SERVER_NAME=gmserver
ARCH=x86
TOP_DIR=$(pwd)
LOG_FILE="gmdb_bin.txt"
SYSLOG_FILE="/var/log/syslog"

DB_PACKAGE_DIR=`pwd`/hpprun
HPPD_BIN=${DB_PACKAGE_DIR}/bin/start_hpp.sh
GMDB_SERVER_BIN=${DB_PACKAGE_DIR}/bin/gmserver
GMDB_SERVER_CONF_SRC=${DB_PACKAGE_DIR}/conf/gmserver.ini
GMDB_SERVER_CONF=${DB_PACKAGE_DIR}/conf/gmserver_in_use.ini

DIVISION_ACCURACY="04"

usage()
{
    echo "Arguements is error! Please use like $0 arm/x86/arm64"
}
calcu_division()
{
    local a=$1
    local b=$2
    local result=$(awk 'BEGIN{printf "%.04f", ('$a'/'$b')}')
    echo "${result}"
}
gmdb_perf_init()
{
    ################################################################################
    # get the make options
    ################################################################################
    if [ 1 -ne ${ARGNUM} ];then
        usage $@
        exit
    fi
    
    case $1 in
    x86)
        ARCH=x86
        rsyslogd || true
        ;;
    arm)
        ARCH=arm
        SYSLOG_FILE="/var/log/messages"
        syslogd
        ;;
	arm64)
	    ARCH=arm64
		#SYSLOG_FILE="/var/log/syslog"
		SYSLOG_FILE="/opt/vrpv8/var/log/cpdt_journal.log"
		# rsyslogd
		;;
    *)
        echo "error ARCH"
        exit
        ;;
    esac
	
    echo > ${LOG_FILE}
    echo > ${SYSLOG_FILE}
	
	local cpuinfo=
	cpuinfo=$(lscpu | grep CPU)
	echo "CPUINFO:" >> ${LOG_FILE}
	echo "${cpuinfo}" >> ${LOG_FILE}
}

export LD_LIBRARY_PATH=${DB_PACKAGE_DIR}/lib/
rm ${GMDB_SERVER_CONF} -rf
cp ${GMDB_SERVER_CONF_SRC} ${GMDB_SERVER_CONF}
sed -i "s/\(^offloadingWithHpp = \).*$/\11/" ${GMDB_SERVER_CONF}
if [ ! -f "${DB_PACKAGE_DIR}/bin/start_hpp.sh" ];then
	sed -i "s/\(^offloadingWithHpp = \).*$/\10/" ${GMDB_SERVER_CONF}
fi
sed -i "s/\(^EnableShmHugePage = \).*$/\10/" ${GMDB_SERVER_CONF}
echo "config file modify ok, view changes by command \" diff ${GMDB_SERVER_CONF_SRC} ${GMDB_SERVER_CONF} \""

cp ${DB_PACKAGE_DIR}/lib/libgm* /lib 
cp ${DB_PACKAGE_DIR}/lib/libnb_bsp.so /lib/
rm -f ${DB_PACKAGE_DIR}/lib64/libnb_bsp.so
chmod -R 777 /run/verona* /run/dpdk 

#stop server
gmdb_stop()
{
    PID=`ps -aux | grep ${SERVER_NAME} | grep -v grep | awk '{print $2}'`
    if [ x = ${PID}x ];then
        echo "no server in running!!!"
    else
        kill -9 ${PID}
        echo "server stop success!!!"
    fi
}
check_hppd()
{
    if [ -f hpp_startinfo.log ];then
        echo "" > hpp_startinfo.log
    fi
	local hppd_pid=$(pidof hppd)
	if [ "X${hppd_pid}" != "X" ];then
	        echo "-------hppd(pid:${hppd_pid})--------"
		# kill -9 ${hppd_pid}
		sleep 1
		return
	fi
	for i in $(seq 100)
	do
		hppd_pid=$(pidof hppd)
		if [ "X" = "X${hppd_pid}" ];then
			break
		fi
		sleep 1
	done
    sleep 5
    sysctl -p > /dev/null
	if [ "X${hppd_pid}" = "X" ];then
	    (${HPPD_BIN} hsd &)>hpp_startinfo.log 2>&1 &
	    sleep 30
	    echo "---------hppd start!---------"
	fi
}

#start server
gmdb_start()
{
    # if [ -f "${DB_PACKAGE_DIR}/bin/start_stlmd.sh" ];then
    #     ./restart_stlmd.sh ${DB_PACKAGE_DIR}
    # fi

	if [ -f "${DB_PACKAGE_DIR}/bin/start_hpp.sh" ];then
		check_hppd
	fi
    #gmdb_stop
    if [ "arm64X" = "${ARCH}X" ];then
		#PID=`ps | grep ${SERVER_NAME} | grep -v grep | awk '{print $1}'`
		PID=$(pidof gmserver)
	else
		PID=`ps -aux | grep ${SERVER_NAME} | grep -v grep | awk '{print $2}'`
	fi
    if [ x != ${PID}x ];then
        kill -9 ${PID}
		sleep 2
    fi
    for i in $(seq 100)
    do
	server_pid=$(pidof gmserver)
	if [ "X" = "X${server_pid}" ];then
		break
	fi
	sleep 1
    done
    
    rm -f /run/verona/GMDB*
    ${GMDB_SERVER_BIN} -c ${GMDB_SERVER_CONF} -r -w 0 &
    sleep 15
	if [ "arm64X" = "${ARCH}X" ];then
		#PID=`ps | grep ${SERVER_NAME} | grep -v grep | awk '{print $1}'`
		PID=$(pidof gmserver)
	else
		PID=`ps -aux | grep ${SERVER_NAME} | grep -v grep | awk '{print $2}'`
	fi
    if [ x = ${PID}x ];then
        echo "server start failed !!!"
        exit 1
    fi
	if [ "armX" = "${ARCH}X" ];then
		CPUCOST=`top -n 1 | grep gmserver | grep -v grep |awk '{print $8}'`
	elif [ "arm64X" = "${ARCH}X" ];then
	    #CPUCOST=$(top -n 1 | grep gmserver | grep -v grep |awk '{print $8}')
		CPUCOST=$(top -b -n 1| grep gmserver | grep -v grep |awk '{print $9}')
	else
		sleep 8
		CPUCOST=`ps -aux|grep gmserver | grep -v grep | awk '{print $3}'`
	fi

	# if [ "arm64X" = "${ARCH}X" ];then
	# 	VMSIZE=$(ps | grep gmserver | grep -v grep | awk '{print $3}')
	# else
		VMSIZE=`ps -aux | grep gmserver | grep -v grep | awk '{print $5}'`
	# fi

	if [ "armX" = "${ARCH}X" ] || [ "arm64X" = "${ARCH}X" ];then
		VMRSS=$(awk -v rss=0 '/Private/ {rss+=$2} END{print rss}' /proc/`pidof gmserver`/smaps)
	else
		VMRSS=`pmap -x $(pidof gmserver) | grep -v -E  "libjansson|liblz4|libsecurec|libc|libpthread|ld-|total kB" | awk -v rss=0 '{rss+=$3} END{print rss}'`
	fi
    VMPSS=$(awk '/Pss/ {print $0}' /proc/`pidof gmserver`/smaps | awk '{sum+=$2}END{print sum}')
	# no calc the lib not access to db
        lib_nocalc=("libstdc++.so" "libstlm.so" "libnb_bsp.so" "libxml2.so.2.9.9" "libcrypto.so" "libsystemd.so.0"  "libsisp" "libpatch" )   
        awk  '/^[0-9]|^[a-z]|Private/  {print $0}' /proc/`pidof gmserver`/smaps >tmp.smaps
        awk  '/^[0-9]|^[a-z]|^Pss/  {print $0}' /proc/`pidof gmserver`/smaps > pss_tmp.smaps
        rm -f tmp_lib_nocalc.txt
        rm -f pss_tmp_lib_nocalc.txt
        touch tmp_lib_nocalc.txt
        
        for lib in ${lib_nocalc[@]}
        do
            echo  $lib
            cat tmp.smaps | grep $lib  -A 3 >> tmp_lib_nocalc.txt
            cat pss_tmp.smaps | grep $lib  -A 1 >> pss_tmp_lib_nocalc.txt
        done
        
        VMRSS_NOCALC=`awk -v rss=0 '/Private/ {rss+=$2} END{print rss}' tmp_lib_nocalc.txt`
        VMPSS_NOCALC=`awk -v rss=0 '/^Pss/ {rss+=$2} END{print rss}' pss_tmp_lib_nocalc.txt`
        let VMRSS_END=${VMRSS}-${VMRSS_NOCALC}
        let VMPSS_END=${VMPSS}-${VMPSS_NOCALC}
        VMRSS_M=$(calcu_division ${VMRSS} 1024)
        VMPSS_M=$(calcu_division ${VMPSS} 1024)
        VMRSS_NOCALC_M=$(calcu_division ${VMRSS_NOCALC} 1024)
        VMPSS_NOCALC_M=$(calcu_division ${VMPSS_NOCALC} 1024)
        echo "Initial VMRSS: ${VMRSS_M}MB  no_calc VMRSS: ${VMRSS_NOCALC_M}"
        echo "Initial VMPSS: ${VMPSS_M}MB  no_calc VMRSS: ${VMPSS_NOCALC_M}"
        

    	
    #TIME=`cat ${SYSLOG_FILE} | grep "GMDB start" | awk '{print $NF}'`
	TIME=`cat ${SYSLOG_FILE} | grep "GMDB start" | head -n 1|awk '{print $NF}'`
    BIN_SIZE=0 
    VMSIZE=$(calcu_division ${VMSIZE} 1024)
    VMRSS=$(calcu_division ${VMRSS_END} 1024)
    VMPSS=$(calcu_division ${VMPSS_END} 1024)
    echo "ARCH:${ARCH}" >> ${LOG_FILE}
    echo "time cost:${TIME}s" >> ${LOG_FILE}
    echo "cpu cost:${CPUCOST}%" >>${LOG_FILE}
    echo "memory cost" >>${LOG_FILE}
    echo -e "memory cost VmSize:${VMSIZE}MB" >>${LOG_FILE}
    echo -e "memory cost VmRSS:${VMRSS}MB" >>${LOG_FILE}
    echo -e "memory cost VmPSS:${VMPSS}MB" >>${LOG_FILE}
    if [ x86X = "${ARCH}X" ];then
        find ./ -type f | xargs strip -g -S -d --strip-unneeded 2>/dev/null
    fi
    #BIN_SIZE=`ls -lR ./ | grep gm | grep -v -E ".txt|.sh|.h|.ini|.log|.gmjson|gmdb_benchmark" | awk 'BEGIN{sum=0}{sum+=$5}END{print sum}'`
    BIN_SIZE=`ls -lR ./ | grep -E " gm| libgm.*.so"| grep -v -E ".txt|.sh|.h|.ini|.log|.gmjson|gmdb_benchmark|.json|.service"| awk 'BEGIN{sum=0}{sum+=$5}END{print sum}'`
    BIN_SIZE=$(calcu_division ${BIN_SIZE} 1024)
    echo "binary size after trip:${BIN_SIZE}(KB)" >>${LOG_FILE}
    
    echo "time cost:${TIME}s"
    echo "cpu cost:${CPUCOST}%" 
    echo "binary size after trip:${BIN_SIZE}(KB)"
    echo "memory cost VmSize:${VMSIZE}MB"
    echo "memory cost VmRSS:${VMRSS}MB"
    echo "memory cost VmPSS:${VMPSS}MB"
}
#clean cpu-consumer process for perf test
clean_process()
{
	if [ "armX" = "${ARCH}X" ];then
		kill -19 `pidof security_proxy_bin` 
		kill -19 `pidof acagent`
	fi
}
gmdb_perf_init $@
clean_process
gmdb_start
