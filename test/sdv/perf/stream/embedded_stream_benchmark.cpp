/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 嵌入式流计算性能场景
 * Author:
 * Create:
 * Note:
 */
#if defined FEATURE_STREAM_EMB

#include <cstdlib>
#include <string>
#include <fstream>
#include <chrono>
#include <functional>
#include <random>
#include "gtest/gtest.h"
#include "t_rd_stream_emb.h"
#define RD_STREAM_TABLE_T1_NAME_SIZE 50
#define MAX_CMD_SIZE 1024

#pragma pack(1)
typedef struct {
    int64_t time;
    int64_t delay;
    int64_t jitter;
    int64_t send_packet;
    int64_t send_byte;
    int64_t recv_packet;
    int64_t recv_byte;
    double cpu_usage;
    double mem_usage;
    double cpu_temperature;
} SimulationStruct;
#pragma pack()

constexpr int POOL_SIZE = 100;
static SimulationStruct dataPool[POOL_SIZE];

static int GetRamPss()
{
    int32_t ret;
    int pid = getpid();  // 获取当前进程 ID
    int pss = 0;         // 单位为 KB
    char fileName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    char path[RD_STREAM_TABLE_T1_NAME_SIZE] = {'/'};
    (void)snprintf(
        fileName, RD_STREAM_TABLE_T1_NAME_SIZE, "%cproc/%s/smaps", path[0], std::to_string(pid).c_str());
    std::ifstream ifs(fileName);
    std::string line;

    while (getline(ifs, line)) {
        if (line.substr(0, 4) == "Pss:") {     // 查找以 Pss 开头的行
            pss += std::stoi(line.substr(6));  // 计算 Pss 的值
        }
    }
    return pss;
}

// 生成满足约束的高斯分布随机数
template <typename T>
T GenerateGaussianRandom(T min, T max, std::normal_distribution<T> &dist, std::default_random_engine &generator)
{
    T value;
    do {
        value = dist(generator);
    } while (value < min || value > max);
    return value;
}

void construct_data()
{
    // 初始化随机数生成器
    std::srand(static_cast<unsigned int>(std::time(0)));
    std::default_random_engine generator(std::time(0));

    // 高斯分布的随机数生成器
    std::normal_distribution<double> delayDist(50.0, 10.0);     // 均值为 50 ms，标准差为 10 ms
    std::normal_distribution<double> jitterDist(10.0, 5.0);     // 均值为 10 ms，标准差为 5 ms
    std::normal_distribution<double> cpuUsageDist(50.0, 10.0);  // 均值为 50%，标准差为 10%
    std::normal_distribution<double> memUsageDist(50.0, 10.0);  // 均值为 50%，标准差为 10%
    std::normal_distribution<double> cpuTempDist(60.0, 5.0);    // 均值为 60°C，标准差为 5°C

    // 预先生成一组数据
    for (int i = 0; i < POOL_SIZE; ++i) {
        dataPool[i].time = i;  // 模拟时间

        // 确保延迟和抖动为非负数
        dataPool[i].delay = std::max<int64_t>(0, static_cast<int64_t>(delayDist(generator)));
        dataPool[i].jitter = std::max<int64_t>(0, static_cast<int64_t>(jitterDist(generator)));

        // 发包和收包数量
        dataPool[i].send_packet = std::rand() % 10000;                            // 随机发送包数，范围 0-9999
        dataPool[i].recv_packet = dataPool[i].send_packet - std::rand() % 500;    // 随机丢包，最多丢 500 个包
        dataPool[i].recv_packet = std::max<int64_t>(0, dataPool[i].recv_packet);  // 确保收包数量非负

        // 发包和收包字节数
        dataPool[i].send_byte = std::rand() % 1000000;                        // 随机发送字节数，范围 0-999999
        dataPool[i].recv_byte = dataPool[i].send_byte - std::rand() % 50000;  // 随机丢包，最多丢 50000 字节
        dataPool[i].recv_byte = std::max<int64_t>(0, dataPool[i].recv_byte);  // 确保收包字节数非负

        // CPU 和内存使用率
        dataPool[i].cpu_usage = GenerateGaussianRandom<double>(0.0, 100.0, cpuUsageDist, generator);
        dataPool[i].mem_usage = GenerateGaussianRandom<double>(0.0, 100.0, memUsageDist, generator);

        // CPU 温度
        dataPool[i].cpu_temperature = GenerateGaussianRandom<double>(40.0, 80.0, cpuTempDist, generator);
    }
}

class Embedded_stream_benchmark : public testing::Test {
public:
    GmeConnT *conn = NULL;

    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
    virtual void SetUp();
    virtual void TearDown();
};

void Embedded_stream_benchmark::SetUp()
{
    int32_t ret;
    system("stop.sh -f");
    AW_FUN_Log(LOG_INFO, "Begin: %d KB", GetRamPss());
    char configPath[MAX_CMD_SIZE];
    (void)snprintf(configPath, MAX_CMD_SIZE, "%s/gmserver.ini", g_sysGMDBCfgPath);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "Open DB: %d KB", GetRamPss());
}

void Embedded_stream_benchmark::TearDown()
{
    int32_t ret;
    ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 嵌入式流表典型格式流表下的单写性能>5kops
TEST_F(Embedded_stream_benchmark, write001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char *errMsg = NULL;
    construct_data();
    bool exec_int = false;
    // ddl
    ret = GmeSqlExecute(conn,
        "create stream table source("
        "time integer,"
        "delay integer,"
        "jitter integer,"
        "send_packet integer,"
        "send_byte integer,"
        "recv_packet integer,"
        "recv_byte integer,"
        "cpu_usage real,"
        "mem_usage real,"
        "cpu_temperature real,"
        "watermark for time as time);",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM SINK pkt_loss AS "
        "select time, recv_packet, send_packet, "
        "100*(recv_packet-send_packet+1)/(recv_packet+1) "
        "FROM source "
        "INTO embedded_callback with (batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM SINK avg_calc AS "
        "select window_start, window_end, avg(delay), avg(jitter) "
        "FROM TABLE(TUMBLE(TABLE source, time, INTERVAL '10' SECONDS)) "
        "GROUP BY window_start, window_end "
        "INTO embedded_callback with (batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM SINK usage_warning AS "
        "select time, cpu_usage, mem_usage, cpu_temperature "
        "FROM source WHERE cpu_usage>0.8 or mem_usage>0.8 or cpu_temperature>80.0 "
        "INTO embedded_callback with (batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // benckmark, 数据不做处理
    ret = GmeStreamRegisterCallBack(
        conn, "pkt_loss", [](void *data, unsigned int tupleSize, unsigned char *colValues) -> int { return 0; });
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeStreamRegisterCallBack(
        conn, "avg_calc", [](void *data, unsigned int tupleSize, unsigned char *colValues) -> int { return 0; });
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeStreamRegisterCallBack(
        conn, "usage_warning", [](void *data, unsigned int tupleSize, unsigned char *colValues) -> int { return 0; });
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_INFO, "Create stream: %d KB", GetRamPss());
    // 开始计时
    auto start = std::chrono::high_resolution_clock::now();

    constexpr int rowNum = 1e6;
    for (int32_t i = 0; i < rowNum; ++i) {
        // 选择一个数据并进行修改
        SimulationStruct &data = dataPool[i % POOL_SIZE];
        data.time = i;  // 更新时间字段
        ret = GmeStreamStructWrite(conn, "source", &data, sizeof(SimulationStruct), 1, nullptr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 结束计时
    auto end = std::chrono::high_resolution_clock::now();

    // 计算耗时（单位：秒）
    std::chrono::duration<double> duration = end - start;

    // 计算吞吐量速度（单位：条/秒）
    double throughput = (double)rowNum / duration.count();
    AW_FUN_Log(LOG_INFO, "End: %f ops, %d KB", throughput, GetRamPss());
    double expectPut = 50000.0;
    EXPECT_LE(expectPut, throughput);
    AW_FUN_Log(LOG_STEP, "test end.");
}

#endif
