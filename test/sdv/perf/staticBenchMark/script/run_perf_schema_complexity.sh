ARCH=$1
if [ "x" = "x${ARCH}" ] || [ "xh" = "x${ARCH}" ] || [ "X${ARCH}" = "X-h" ] || [ $# -lt 3 ];then
    echo ">> benchMark schema complexity "
    echo ">>useage:arg1 [01 - 10] support: schema depth "
    echo ">>       arg2 [num]"
    echo ">>       arg3 [batch_num] support: actul_num"
    exit 1
fi

SCHEMA_DEPTH=$1
NUM=$2
BATCH_NUM=$3

PERF=0
GET_CPU=1

if [ "x${PERF}" = "x1" ];then
    GET_CPU=0
fi

TABLE_NAME="schema_complexity_benchMark_${SCHEMA_DEPTH}"
SCHEMA_FILE_NAME="schema_complexity_benchMark_${SCHEMA_DEPTH}.gmjson"

cur=`pwd`

./deploy.sh  euler
source ./autotest_env.sh

# 修改配置
echo ">>> modify config:"
sh modifyCfg.sh "enableTableLock=1"
sh modifyCfg.sh "isFastReadUncommitted=1"
sh modifyCfg.sh "isUseHugePage=1"
sh modifyCfg.sh "messageSecurityCheck=0"
sysctl -w vm.nr_hugepages=1024
cat /proc/meminfo |grep HugePages_
echo ""


cd $cur
if [ "x${BATCH_NUM}" = "x" ];then
    BATCH_NUM=0
fi

TEST_SYN_BIN=testperf_fib
TEST_ASYN_BIN=testperf_fib_async

killall_process_of_name()
{
    local perf_tgt_process=$1
    local first_kill_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`

    while true
    do
        if [ "$first_kill_pid"X != ""X ];then
            kill -9 ${first_kill_pid}
            first_kill_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`
            continue
        fi
        return
    done
    sleep 2
}

echo ""
echo ">>> Start to test table: ${TABLE_NAME}  schema depth: ${SCHEMA_DEPTH}  num: ${NUM}  batch_num: ${BATCH_NUM} json: ${SCHEMA_FILE_NAME}"

killall_process_of_name ${TEST_SYN_BIN}
killall_process_of_name ${TEST_ASYN_BIN}

echo ">>> restart server"
stop.sh -f
start.sh -f

sleep 3

echo ">>> Create table: ${TABLE_NAME}  gmjson: benchMark_schema/${SCHEMA_FILE_NAME}"
echo ">>> ./testperf_fib -v -C -n ${TABLE_NAME} -f  benchMark_schema/${SCHEMA_FILE_NAME}"
./testperf_fib -v -C -n ${TABLE_NAME} -f  benchMark_schema/${SCHEMA_FILE_NAME}

echo ">>> Start to test:"

if [ "x${BATCH_NUM}" != "x0" ];then
    batch_oper=" -N ${BATCH_NUM} "
fi

echo ">>>>>> start sync single insert"
echo "./${TEST_SYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM}

echo ">>>>>> start sync single replace"
echo "./${TEST_SYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM}

echo ">>>>>> start select"
echo "./${TEST_SYN_BIN} -v -m select -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m select -n ${TABLE_NAME} -e ${NUM}

echo ">>>>>> start sync single delete"
echo "./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM}

echo ">>>>>> start async batch insert"
echo "./${TEST_ASYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
./${TEST_ASYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM} ${batch_oper}

echo ">>>>>> start async batch replace"
echo "./${TEST_ASYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
./${TEST_ASYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} ${batch_oper}

echo ">>>>>> start sync batch delete"
echo "./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM} ${batch_oper}

exit 0
