# 需要测试的数据量:
testNums=(50000 300000)

PerfTestSub() {
    local subNum=$1
    local subConfigJson=$2

    for nums in ${testNums[@]}; do
        echo "sh run_perf_sub.sh 1${subType} if_product replace ${nums} 220 8 "
        sh run_perf_sub.sh 11 if_product replace ${nums} 220 ${subNum} ${subConfigJson}
    done
}

#测试订阅性能
PerfTestSub "8"

#测试订阅数量对性能影响
PerfTestSub "16"
PerfTestSub "32"
PerfTestSub "256"

#测试订阅条件复杂度对性能影响（目前订阅条件简单无嵌套）
PerfTestSub "8" "if_product_sub_complex_config1.gmjson"
PerfTestSub "8" "if_product_sub_complex_config2.gmjson"

#测试订阅非可靠对性能影响
PerfTestSub "8" "if_product_sub_config_unreliable.gmjson"

exit 0
