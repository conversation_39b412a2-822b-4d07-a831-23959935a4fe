ARCH=$1
if [ "x" = "x${ARCH}" ] || [ "xh" = "x${ARCH}" ] || [ "X${ARCH}" = "X-h" ] || [ $# -lt 4 ];then
    echo ">> support RTOS "
    echo ">>useage:arg1 [1/11] support: 1:sync 11:sync struct"
    echo ">>       arg2 [table_name] support: ip4forward / arp / if_product"
    echo ">>       arg3 [oper] support: replace"
    echo ">>       arg4 [num]"
    echo ">>       arg5 [batch_num] support: null / 0 / actul_num"
    echo ">>       arg6 [sub_num] support: null / 0 / actul_num"
    echo ">>       arg7 [config_json] support: null"
    exit 1
fi

SYNC_MODE=$1
TABLE_NAME=$2
OPER=$3
NUM=$4
BATCH_NUM=$5
SUB_NUM=$6
CONFIG_JSON=$7
SUB_TIME=180
PERF=0
GET_CPU=0

env_9700_rtos=0

if [ "x${PERF}" = "x1" ];then
    GET_CPU=0
fi

SCHEMA_FILE_NAME="${TABLE_NAME}.gmjson"


echo "## $0 $@ ##"

cur=`pwd`
env_type=$(cat /etc/*release|grep -w PRETTY_NAME)
env_rtos=$(echo ${env_type}|grep EulerOS)
env_iot=$(echo ${env_type}|grep "RTOS V2X")
env_9700=$(echo ${env_type}|grep "RTOS"|grep "arm64le-preempt-pro")

if [ "x${env_9700_rtos}" != "x0" ];then
    env_rtos=1
    env_9700=""
    echo ">>> test on 9700_RTOS."
fi

SysType=0
if [ "x${env_rtos}" != "x" ];then
    echo ">>> test on RTOS."
    SysType=0
    cd ../../tools/
    ./deploy.sh  euler
    source ../autotest_env.sh
elif [ "x${env_iot}" != "x" ];then
    echo ">>> test on iot_hm."
    SysType=1
    cd ../../tools/
    ./deploy.sh  rtosv2x
    source ../autotest_env.sh
elif [ "x${env_9700}" != "x" ];then
    echo ">>> test on 9700 ."
    SysType=2
    cd ../../tools/
    ./deploy.sh  rtosv2
    source ../autotest_env.sh
fi

# 修改配置
if [ "x${env_rtos}" != "x" ];then
    echo ">>> modify config:"
		sh $TEST_HOME/tools/modifyCfg.sh "enableTableLock=1"
		sh $TEST_HOME/tools/modifyCfg.sh "isFastReadUncommitted=1"
		sh $TEST_HOME/tools/modifyCfg.sh "isUseHugePage=1"
		sh $TEST_HOME/tools/modifyCfg.sh "messageSecurityCheck=0"
		sysctl -w vm.nr_hugepages=1024
		cat /proc/meminfo |grep HugePages_

		if [ "x${TABLE_NAME}" = "xip4forward00000" ];then
       sh $TEST_HOME/tools/modifyCfg.sh "maxSeMem=1280"
    fi
fi
echo ""


cd $cur
if [ "x${BATCH_NUM}" = "x" ];then
    BATCH_NUM=0
fi
if [ "x${SUB_NUM}" = "x" ];then
    SUB_NUM=0
fi

syncflag=$(echo $SYNC_MODE|awk '{print substr($0, 1, 2)}')
if [ "x${syncflag}" = "x1" ] || [ "x${syncflag}" = "x10" ];then
    TEST_BIN=tf
    TEST_PREPARE_BIN=tf_async
    SYNC_MODE_NAME=sync
    echo ">>> The test bin is: tf ."
elif [ "x${syncflag}" = "x11" ];then
    TEST_BIN=tf_st_scx
    TEST_PREPARE_BIN=tf_async_st_scx
    if [ "x${env_9700}" = "x" ];then
        TEST_PREPARE_BIN=tf_st_scx
    fi
    SYNC_MODE_NAME=sync
    echo ">>> The prepare bin is: ${TEST_PREPARE_BIN} ."
    echo ">>> The test bin is: ${TEST_BIN} ."
fi

TEST_SUB_BIN=tf_sub
if [ "x${env_9700}" != "x" ];then
    TEST_BIN=$(echo $TEST_BIN| sed  's/tf/tf/g')
    TEST_SUB_BIN=$(echo $TEST_SUB_BIN| sed  's/tf/tf/g')
    TEST_PREPARE_BIN=$(echo $TEST_PREPARE_BIN| sed  's/tf/tf/g')

    echo ">>> on 9700, the preare_data bin is: $TEST_PREPARE_BIN ."
    echo ">>> on 9700, the test_sub bin is: $TEST_SUB_BIN ."
    echo ">>> on 9700, the test bin is: $TEST_BIN ."
    echo " "
fi

sub_log_fix="sub"
if [ "x${CONFIG_JSON}" = "x" ];then
    sub_oper=" -f benchMark_schema/if_product_sub_config.gmjson "
    sub_log_fix="sub_file"
    echo ">>> Use conditional sub: benchMark_schema/if_product_sub_config.gmjson "
else
    sub_oper=" -f benchMark_schema/${CONFIG_JSON} "
    sub_log_fix="sub_file_${CONFIG_JSON}"
    echo ">>> Use conditional sub: benchMark_schema/${CONFIG_JSON} "
fi


get_perf_data_file()
{
	local sleep_time=$1
	local perf_tgt_process=$2
	local output_file="$3"
	local first_perf_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`

	if [ "${first_perf_pid}"X = X ];then
		return 0
	fi

	perf record -g -e cpu-clock -p ${first_perf_pid} -- sleep ${sleep_time}
	if [[ $? -eq 0 ]] && [[ -f ./perf.data ]];then
		perf script > "${output_file}"
		echo "[ >>> perf file: ${output_file} ]"
	fi
}

wait_sub_process_exit()
{
	local sub_process_name=$1
	local sub_cli_pid=`pidof ${sub_process_name}`
	if [ "X${sub_cli_pid}" = "X" ];then
		return
	fi

    for i in $(seq ${SUB_TIME})
	do
		if [ ! `pidof -s ${sub_process_name}` ];then
			break
		fi
		sleep 1
	done
}

killall_process_of_name()
{
    local perf_tgt_process=$1
    local first_kill_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`

    while true
    do
        if [ "$first_kill_pid"X != ""X ];then
            kill -9 ${first_kill_pid}
            first_kill_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`
            continue
        fi
        return
    done
    sleep 2
}

running_get_cpu()
{
    rm -f tmp_cpu_client.txt
    rm -f tmp_cpu_gmserver.txt
    if [ "X${GET_CPU}" = "X0" ];then
        return
    fi

   	local perf_tgt_process=$1
    local first_perf_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`

    if [ "X${SysType}" != "X0" ] || [ "X${GET_CPU}" = "X0" ];then
        return
    fi

    top -b -d 0.5 -n 4 -p ${first_perf_pid}  > tmp_cpu_client.txt &
    top -b -d 0.5 -n 4 -p `pidof gmserver` > tmp_cpu_gmserver.txt &
}

get_cpu()
{
    local perf_tgt_process=$1
    if [ "X${GET_CPU}" = "X0" ];then
        return
    fi

    if [ ! -f tmp_cpu_client.txt ];then
        return
    fi

    num_cpu=$(cat tmp_cpu_client.txt| grep testperf_ |wc -l)
    cpu_client=$(cat tmp_cpu_client.txt| grep testperf_ | awk '{num+=1; sum+=$9; print "   num" num ": " $9 }' )
    cpu_client_avg=$(cat tmp_cpu_client.txt| grep testperf_ | awk '{num+=1; sum+=$9;  } END {printf("%.2f", sum/num)}' )
    if [ ! -f tmp_cpu_gmserver.txt ];then
        return
    fi
    cpu_gmserver=$(cat tmp_cpu_gmserver.txt| grep gmserver | awk -v max_num=${num_cpu} '{count+=1; if(count <= max_num){num+=1;  sum+=$9; print "   num" num ": " $9}}' )
    cpu_gmserver_avg=$(cat tmp_cpu_gmserver.txt| grep gmserver | awk -v max_num=${num_cpu} '{count+=1; if(count <= max_num){num+=1;  sum+=$9;}} END {printf("%.2f", sum/num)}' )
}

print_cpu()
{
    if [ "X${GET_CPU}" = "X0" ];then
        return
    fi

    if [ "X${num_cpu}" = "X" ];then
        return
    fi

    echo "  cpu_calc_num: ${num_cpu}"
    echo "  cpu_gmserver_avg: ${cpu_gmserver_avg}"
    echo "${cpu_gmserver}"
    echo "  cpu_client_avg: ${cpu_client_avg}"
    echo "${cpu_client}"

    echo "  cpu_calc_num: ${num_cpu}"  >> $1
    echo "  cpu_gmserver_avg: ${cpu_gmserver_avg}"  >> $1
    echo "${cpu_gmserver}"  >> $1
    echo "  cpu_client_avg: ${cpu_client_avg}" >> $1
    echo "${cpu_client}"  >> $1
}

echo ""
echo ">>> Start to test table: ${TABLE_NAME}  mode: ${SYNC_MODE_NAME}  oper:${OPER}  num: ${NUM}  batch_num: ${BATCH_NUM} sub_num: ${SUB_NUM}  ${SCHEMA_FILE_NAME}"

rm -f temp_sub_*.txt
echo ">>> cp ${TEST_BIN} ${TEST_SUB_BIN}"
rm -f ${TEST_SUB_BIN}
cp ${TEST_BIN}  ${TEST_SUB_BIN}
killall_process_of_name ${TEST_SUB_BIN}
killall_process_of_name ${TEST_BIN}

if [ "x${env_9700_rtos}" != "x0" ];then
    echo ">>> restart server"
    kill -9 `pidof gmserver`
    sleep 2
    ipcrm -a
    curdir=`pwd`
    export LD_LIBRARY_PATH=${curdir}/../../../../output/euler/aarch64/lib:${curdir}/../../../../output/euler/aarch64/third_party/lib
    cd ${curdir}/../../../../output/euler/aarch64/bin
    echo "cd ${curdir}/../../../../output/euler/aarch64/bin"
    echo "taskset 2 ./gmserver -p /usr/local/file/gmserver.ini -b"
    taskset 2 ./gmserver -p /usr/local/file/gmserver.ini -b
    sleep 3
    cd ${curdir}
    echo "cd ${curdir}"
    server_pid=`pidof gmserver`
    if [ "x" = "x${server_pid}" ];then
        echo ">>> error, gmserver start failed."
    else
        echo ">>>> gmserver pid:$server_pid"
    fi
elif [ "x${env_9700}" = "x" ];then
    echo ">>> restart server"
    stop.sh -f
    start.sh -f
fi

sleep 3

op_name_flag=""
if [ "xreplace1" = "x${OPER}" ];then
    OPER=replace
    op_name_flag="1"
fi

if [ "xreplace_check" = "x${OPER}" ];then
    OPER=replace
    op_name_flag="_check"
fi

echo ">>> Create table: ${TABLE_NAME}  gmjson: schema/${SCHEMA_FILE_NAME}"
echo ">>> ./tf -v -C -n ${TABLE_NAME} -f  schema/${SCHEMA_FILE_NAME}"
./tf -v -m drop -n ${TABLE_NAME} > /dev/null  2>&1
./tf -v -C -n ${TABLE_NAME} -f  schema/${SCHEMA_FILE_NAME}
rm -f batch_write.txt

if [ "xreplace" = "x${OPER}" ] || [ "xbatch_replace" = "x${OPER}" ] || [ "xreplace1" = "x${OPER}" ]  || [ "xinsert" = "x${OPER}" ];then
     echo "./${TEST_PREPARE_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} -N 60 -r 2"
    ./${TEST_PREPARE_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} -N 60 -r 2

    if [ "x_check" == "x${op_name_flag}" ];then
        echo "./tf -v -m check -n ${TABLE_NAME}  "
        ./tf -v -m check -n ${TABLE_NAME}
    elif [ "x1" != "x${op_name_flag}" ];then
        echo "./tf -v -m truncate -n ${TABLE_NAME}  "
        ./tf -v -m truncate -n ${TABLE_NAME}
    fi
fi
./tf -v -m count -n ${TABLE_NAME}

echo ">>> Start to test:"

for suber in `seq 1 $SUB_NUM`
do
    echo "sub_${suber}: ./${TEST_SUB_BIN} -v -m sub -n ${TABLE_NAME} -e  ${NUM}  -t  ${SUB_TIME} ${sub_oper} &"
    (./${TEST_SUB_BIN} -v -m sub -n ${TABLE_NAME} -e  ${NUM}  -t  ${SUB_TIME} ${sub_oper} | tee temp_sub_${suber}.txt) &
done


res=$(echo | awk -v a=$SUB_NUM -v b=15 '{if(a>b) print 1; else print 0}')
if [[ $res -eq 1  ]]; then
    echo "sleep 15"
    sleep 15
else
    echo "sleep ${SUB_NUM}"
    sleep ${SUB_NUM}
fi


if [ "x${BATCH_NUM}" != "x0" ];then
    batch_oper=" -N ${BATCH_NUM} "
    if [ "x${subflag}" = "x1" ];then
        batch_oper=" $batch_oper -r 2"
    else
        batch_oper=" $batch_oper -r 2"
    fi

fi

if [ "x${env_9700_rtos}" != "x0" ];then
    echo "taskset 4 ./${TEST_BIN} -v -m ${OPER} -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
    (taskset 4 ./${TEST_BIN} -v -m ${OPER} -n ${TABLE_NAME} -e ${NUM} ${batch_oper} |tee temp.txt)&
else
    echo "./${TEST_BIN} -v -m ${OPER} -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
    (./${TEST_BIN} -v -m ${OPER} -n ${TABLE_NAME} -e ${NUM} ${batch_oper} |tee temp.txt)&
fi

if [ "x${PERF}" = "x1" ];then
    sleep 1
    test_pid=`pidof gmserver`
    echo ">>>perf pid:${test_pid}"
    rm -f temp.perf
    rm -f temp_gmserver.perf
    get_perf_data_file 5 "gmserver" "temp_gmserver.perf"
    get_perf_data_file 5 "${TEST_BIN}" "temp.perf"
fi
running_get_cpu ${TEST_BIN}
wait_sub_process_exit ${TEST_BIN}
#wait $!

wait_sub_process_exit "${TEST_SUB_BIN}"
./${TEST_BIN} -v -m count -n ${TABLE_NAME} |tee tmpCnt.txt

if [ ! -d "$TEST_HOME/perf/testperf/log_sub" ]; then
    mkdir $TEST_HOME/perf/testperf/log_sub
fi
logFolder=$TEST_HOME/perf/testperf/log_sub/
mkdir -p $logFolder

get_cpu ${TEST_BIN}
ops=$(grep Ops_ temp.txt |awk '{print $3}')
op_name=$(grep Opertion temp.txt  |awk '{print $4}')
log_file=${logFolder}sub_log_$3_${TABLE_NAME}_${NUM}_${SUB_NUM}${sub_log_fix}.txt

cat temp.txt > ${log_file}
cat tmpCnt.txt >> ${log_file}

total_sub_ops=0
for suber in `seq 1 ${SUB_NUM}`
do
    this_sub_str=$(grep -Eo "sub_client.*" temp_sub_${suber}.txt)
    this_sub_ops=$(grep -Eo "ops.*" temp_sub_${suber}.txt|awk '{print $2}')
    echo "---sub_${suber}: $this_sub_str "
	echo "---sub_${suber}: $this_sub_str " >> ${log_file}
    total_sub_ops=$(awk -v x=${total_sub_ops} -v y=${this_sub_ops} 'BEGIN{printf ("%.5f",x+y)}')
done

if [ ${SUB_NUM} -gt 0 ];then
    echo ">>> sub_total ops: $total_sub_ops"
	echo ">>> sub_total ops: $total_sub_ops" >> ${log_file}
fi

if [ "x${ops}" != "x0.00000" ] && [ "x${env_rtos}" != "x" ] && [ "x${env_9700_rtos}" == "x0" ];then
    cpuHzInfo=$(lscpu|grep "CPU MHz"|awk '{print $1 $2 " " $3}')
    cpuHz=$(lscpu|grep "CPU MHz"|awk '{print $3}')
    cylce_f=$(printf "%.2f" $(echo "scale=2;1000000*$cpuHz/$ops" | bc))
    echo ">>> ${op_name}: ${ops}    ${cpuHzInfo}    cpu_cycles: $cylce_f"
fi

echo "${op_name}: ${ops}" >> ${log_file}
print_cpu ${log_file}

if [ "x${PERF}" = "x1" ];then
    ops_int=$(grep Ops_ temp.txt |awk '{printf("%.0f", $3)}')
    cycle_int=$(grep -Eo cpu_cycles.* temp.txt |awk -F ':' '{printf("%.0f", $2)}')
    perf_file=log_${TABLE_NAME}_${SYNC_MODE_NAME}_${op_name}${op_name_flag}_ops_${ops_int}_cycle_${cycle_int}
    if [ ${SUB_NUM} -gt 0 ];then
        perf_file=${logFolder}sub_log_${TABLE_NAME}_${op_name_flag}_${SUB_NUM}sub_ops_${ops_int}_cycle_${cycle_int}
    fi
    mv temp.perf ${perf_file}_client.perf
    mv temp_gmserver.perf ${perf_file}_gmserver.perf
	echo "--------Perffile is: ${perf_file}_client.perf"
    echo "--------Perffile is: ${perf_file}_gmserver.perf"
fi

# 9700 不支持重启服务，删表
if [ "x${env_9700}" != "x" ];then
    echo "./tf -v -m drop -n ${TABLE_NAME}"
    ./tf -v -m drop -n ${TABLE_NAME}
fi

echo "---------Logfile is: ${log_file}"
echo ""
exit 0
