{"comment": "索引类型、数量对性能的影响", "version": "2.0", "type": "record", "name": "various_hashcluster_10", "config": {"check_validity": true}, "max_record_count": 40000000, "fields": [{"name": "F00", "type": "uint32"}, {"name": "F01", "type": "uint32"}, {"name": "F02", "type": "uint32"}, {"name": "F03", "type": "uint32"}, {"name": "F04", "type": "uint32"}, {"name": "F05", "type": "uint32"}, {"name": "F06", "type": "uint32"}, {"name": "F07", "type": "uint32"}, {"name": "F08", "type": "uint32"}, {"name": "F09", "type": "uint32"}, {"name": "F10", "type": "uint32"}, {"name": "F11", "type": "uint32"}, {"name": "F12", "type": "uint32"}, {"name": "F13", "type": "uint32"}, {"name": "F14", "type": "uint32"}, {"name": "F15", "type": "uint32"}, {"name": "F16", "type": "uint32"}, {"name": "F17", "type": "uint32"}, {"name": "F18", "type": "uint32"}, {"name": "F19", "type": "uint32"}, {"name": "F20", "type": "uint32"}, {"name": "F21", "type": "uint32"}, {"name": "F22", "type": "uint32"}, {"name": "F23", "type": "uint32"}, {"name": "F24", "type": "uint32"}, {"name": "F25", "type": "uint32"}, {"name": "F26", "type": "uint32"}, {"name": "F27", "type": "uint32"}, {"name": "F28", "type": "uint32"}, {"name": "F29", "type": "uint32"}, {"name": "F30", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "various_hashcluster_10", "fields": ["F00"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "hashcluster_key1", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F01"], "constraints": {"unique": true}}, {"name": "hashcluster_key2", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F02"], "constraints": {"unique": true}}, {"name": "hashcluster_key3", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F03"], "constraints": {"unique": true}}, {"name": "hashcluster_key4", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F04"], "constraints": {"unique": true}}, {"name": "hashcluster_key5", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F05"], "constraints": {"unique": true}}, {"name": "hashcluster_key6", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F06"], "constraints": {"unique": true}}, {"name": "hashcluster_key7", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F07"], "constraints": {"unique": true}}, {"name": "hashcluster_key8", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F08"], "constraints": {"unique": true}}, {"name": "hashcluster_key9", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F09"], "constraints": {"unique": true}}, {"name": "hashcluster_key10", "index": {"type": "hashcluster"}, "node": "various_hashcluster_10", "fields": ["F10"], "constraints": {"unique": true}}]}