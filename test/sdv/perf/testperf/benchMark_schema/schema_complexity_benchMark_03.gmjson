{"version": "2.0", "type": "record", "name": "schema_complexity_benchMark_03", "config": {"check_validity": false}, "max_record_count": 1000000, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32"}, {"name": "F4", "type": "uint32"}, {"name": "F5", "type": "uint32"}, {"name": "F6", "type": "uint32"}, {"name": "F7", "type": "uint32"}, {"name": "F8", "type": "uint32"}, {"name": "F9", "type": "uint32"}, {"name": "F10", "type": "uint32"}, {"name": "F11", "type": "uint32"}, {"name": "F12", "type": "uint32"}, {"name": "F13", "type": "uint32"}, {"name": "F14", "type": "uint32"}, {"name": "F15", "type": "uint32"}, {"name": "F16", "type": "uint32"}, {"name": "F17", "type": "uint32"}, {"name": "F18", "type": "uint32"}, {"name": "F19", "type": "uint32"}, {"name": "F20", "type": "uint32"}, {"name": "F21", "type": "uint32"}, {"name": "F22", "type": "uint32"}, {"name": "F23", "type": "uint32"}, {"name": "F24", "type": "uint32"}, {"name": "F25", "type": "uint32"}, {"name": "F26", "type": "uint32"}, {"name": "F27", "type": "uint32"}, {"name": "F28", "type": "uint32"}, {"name": "F29", "type": "uint32"}, {"name": "F30", "type": "uint32"}, {"name": "F31", "type": "uint32"}, {"name": "F32", "type": "uint32"}, {"name": "F33", "type": "uint32"}, {"name": "F34", "type": "uint32"}, {"name": "F35", "type": "uint32"}, {"name": "F36", "type": "uint32"}, {"name": "F37", "type": "uint32"}, {"name": "F38", "type": "uint32"}, {"name": "F39", "type": "uint32"}, {"name": "F40", "type": "uint32"}, {"name": "F41", "type": "uint32"}, {"name": "F42", "type": "uint32"}, {"name": "F43", "type": "uint32"}, {"name": "F44", "type": "uint32"}, {"name": "F45", "type": "uint32"}, {"name": "F46", "type": "uint32"}, {"name": "F47", "type": "uint32"}, {"name": "F48", "type": "uint32"}, {"name": "F49", "type": "uint32"}, {"name": "F50", "type": "uint32"}, {"name": "F51", "type": "uint32"}, {"name": "F52", "type": "uint32"}, {"name": "F53", "type": "uint32"}, {"name": "F54", "type": "uint32"}, {"name": "F55", "type": "uint32"}, {"name": "F56", "type": "uint32"}, {"name": "F57", "type": "uint32"}, {"name": "F58", "type": "uint32"}, {"name": "F59", "type": "uint32"}, {"name": "F60", "type": "uint32"}, {"name": "F61", "type": "uint32"}, {"name": "F62", "type": "uint32"}, {"name": "F63", "type": "uint32"}, {"name": "F64", "type": "uint32"}, {"name": "F65", "type": "uint32"}, {"name": "F66", "type": "uint32"}, {"name": "F67", "type": "uint32"}, {"name": "F68", "type": "uint32"}, {"name": "F69", "type": "uint32"}, {"name": "F70", "type": "uint32"}, {"name": "F71", "type": "uint32"}, {"name": "F72", "type": "uint32"}, {"name": "F73", "type": "uint32"}, {"name": "F74", "type": "record", "fields": [{"name": "F80", "type": "uint32"}, {"name": "F81", "type": "uint32"}, {"name": "F82", "type": "uint32"}, {"name": "F83", "type": "uint32"}, {"name": "F84", "type": "record", "fields": [{"name": "F90", "type": "uint32"}, {"name": "F91", "type": "uint32"}, {"name": "F92", "type": "uint32"}, {"name": "F93", "type": "uint32"}, {"name": "F94", "type": "uint32"}, {"name": "F95", "type": "uint32"}, {"name": "F96", "type": "uint32"}, {"name": "F97", "type": "uint32"}, {"name": "F98", "type": "uint32"}, {"name": "F99", "type": "uint32"}]}, {"name": "F85", "type": "uint32"}, {"name": "F86", "type": "uint32"}, {"name": "F87", "type": "uint32"}, {"name": "F88", "type": "uint32"}, {"name": "F89", "type": "uint32"}]}, {"name": "F75", "type": "uint32"}, {"name": "F76", "type": "uint32"}, {"name": "F77", "type": "uint32"}, {"name": "F78", "type": "uint32"}, {"name": "F79", "type": "uint32"}], "keys": [{"name": "pk", "index": {"type": "primary"}, "node": "schema_complexity_benchMark_03", "fields": ["F0"], "constraints": {"unique": true}}]}