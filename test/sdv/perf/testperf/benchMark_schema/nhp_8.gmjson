{"comment": "下一跳组表，对应8#表", "version": "2.0", "type": "record", "name": "nhp_benchMark8", "config": {"check_validity": true}, "max_record_count": 4096000, "fields": [{"name": "nhp_group_id", "type": "uint32", "comment": "下一跳组ID"}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "flags", "type": "uint32", "comment": "标志(包括path完备性等)"}, {"name": "ref_count", "type": "uint32", "comment": "引用计数"}, {"name": "new_vrf", "type": "uint32"}, {"name": "nhp_number", "type": "uint16", "comment": "下一跳组成员数量"}, {"name": "nhp_type_high_prio", "type": "uint8", "nullable": true, "comment": "下发高优先级形式,0为单下一跳,1为ecmp,2为frr"}, {"name": "nhp_type_normal_prio", "type": "uint8", "nullable": true, "comment": "下发普通优先级形式,0为单下一跳,1为ecmp,2为frr"}, {"name": "status_high_prio", "type": "uint8", "default": "0", "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"}, {"name": "status_normal_prio", "type": "uint8", "default": "0", "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"}, {"name": "errcode_high_prio", "type": "fixed", "size": 2, "default": "0x0000", "comment": "SERVICE高优先级下发状态错误码"}, {"name": "errcode_normal_prio", "type": "fixed", "size": 2, "default": "0x0000", "comment": "SERVICE普通优先级下发状态错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 32, "default": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的NHP_GROUP上下文"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 32, "default": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的NHP_GROUP上下文"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "time_stamp_update", "type": "time"}, {"name": "oper_bitmap", "type": "uint32"}, {"name": "attr_flag", "type": "uint32", "comment": "公用字段"}, {"name": "time_stamp_app", "type": "time"}, {"name": "time_stamp_svc", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_benchMark8", "fields": ["nhp_group_id", "vr_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "vrfid_hashcluster_key", "index": {"type": "hashcluster"}, "node": "nhp_benchMark8", "fields": ["app_source_id", "vr_id", "vrf_index"], "constraints": {"unique": false}, "comment": "根据app_source_id + vr_id + vrf_index索引"}]}