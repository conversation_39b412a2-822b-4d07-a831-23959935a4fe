[{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "ip4forward_local", "config": {"check_validity": true}, "max_record_count": 4000000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8", "default": "0", "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"}, {"name": "status_normal_prio", "type": "uint8", "default": "0", "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"}, {"name": "errcode_high_prio", "type": "uint8", "default": "0", "comment": "SERVICE高优先级下发状态错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "default": "0", "comment": "SERVICE普通优先级下发状态错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "trace", "type": "uint64"}, {"name": "route_flags", "type": "uint16", "comment": "路由标记"}, {"name": "next_type", "type": "uint8", "nullable": true, "comment": "#7关联的下一跳类型"}, {"name": "reserved", "type": "uint8", "nullable": true, "comment": "预留"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip4forward_local", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "config": {"init_hash_capacity": 10000000}, "comment": "根据主键索引"}, {"name": "local_key1", "index": {"type": "local"}, "node": "ip4forward_local", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "config": {"init_hash_capacity": 10000000}, "comment": "字典序排序建立索引"}, {"name": "local_key2", "index": {"type": "local"}, "node": "ip4forward_local", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "config": {"init_hash_capacity": 10000000}, "comment": "字典序排序建立索引"}]}]