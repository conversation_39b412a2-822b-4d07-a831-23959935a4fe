{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "ip4forward_v3_hashcluster", "config": {"check_validity": true}, "max_record_count": 4000000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8", "default": "0"}, {"name": "status_normal_prio", "type": "uint8", "default": "0"}, {"name": "errcode_high_prio", "type": "uint8", "default": "0"}, {"name": "errcode_normal_prio", "type": "uint8", "default": "0"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "trace", "type": "uint64"}, {"name": "route_flags", "type": "uint16", "comment": "路由标记"}, {"name": "reserved", "type": "uint16", "nullable": true, "comment": "预留"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip4forward_v3_hashcluster", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "config": {"init_hash_capacity": 10}, "comment": "根据主键索引"}, {"name": "vrfid_hashcluster_key", "index": {"type": "hashcluster"}, "node": "ip4forward_v3_hashcluster", "fields": ["app_source_id", "vr_id", "vrf_index"], "config": {"init_hash_capacity": 10}, "comment": "根据app_source_id + vr_id + vrf_index索引"}]}