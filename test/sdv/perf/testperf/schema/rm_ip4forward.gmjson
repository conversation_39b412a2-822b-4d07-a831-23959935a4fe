{"comment": "prefix table, corresponding to table 7", "version": "2.0", "type": "record", "name": "rm_ip4forward", "config": {"check_validity": true}, "max_record_count": 512000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "virtual routing index"}, {"name": "vrf_index", "type": "uint32", "comment": "VPN instance index"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "destination ipv4 address"}, {"name": "mask_len", "type": "uint8", "comment": "mask length"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "identifies nhp or nhpGroup used"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QoS id"}, {"name": "primary_label", "type": "uint32", "comment": "labeling"}, {"name": "attribute_id", "type": "uint32", "comment": "attribute id"}, {"name": "nhp_group_id", "type": "uint32", "comment": "indicates the next hop index or next hop group index, the value is determined by nhp_group_flag"}, {"name": "path_flags", "type": "uint32", "comment": "path flags"}, {"name": "cost", "type": "uint8", "comment": "cost of route"}, {"name": "preference", "type": "uint8", "comment": "preference of route"}, {"name": "route_attr", "type": "uint8", "comment": "route attribute"}, {"name": "route_flags", "type": "uint8", "comment": "route flag"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "rm_ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "index based on primary key"}, {"name": "ip4forward_lpm", "index": {"type": "lpm4_tree_bitmap"}, "node": "rm_ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "ipv4最长前缀匹配"}]}