{"name": "SUB_NAME", "label_name": "if_product", "comment": "if subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "replace", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "and", "conditions": [{"property": "<PERSON><PERSON><PERSON><PERSON>"}]}}