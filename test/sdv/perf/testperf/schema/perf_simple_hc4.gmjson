[{"type": "record", "name": "perf_simple", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uchar", "nullable": false}, {"name": "F2", "type": "int8", "nullable": false}, {"name": "F3", "type": "uint8", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int32", "nullable": false}, {"name": "F7", "type": "uint32", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": false}, {"name": "F9", "type": "int64", "nullable": false}, {"name": "F10", "type": "uint64", "nullable": false}, {"name": "F11", "type": "uint32", "nullable": false}, {"name": "F12", "type": "uchar", "nullable": false}, {"name": "F13", "type": "uint8", "nullable": false}, {"name": "F14", "type": "int16", "nullable": false}, {"name": "F15", "type": "uint16", "nullable": false}, {"name": "F16", "type": "int32", "nullable": false}, {"name": "F17", "type": "uint32", "nullable": false}, {"name": "F18", "type": "boolean", "nullable": false}, {"name": "F19", "type": "int64", "nullable": false}, {"name": "F20", "type": "uint64", "nullable": false}, {"name": "F21", "type": "float", "nullable": false}, {"name": "F22", "type": "double", "nullable": false}, {"name": "F23", "type": "float", "nullable": false}, {"name": "F24", "type": "double", "nullable": false}, {"name": "F25", "type": "time", "nullable": false}, {"name": "F26", "type": "uint32", "nullable": false}, {"name": "F27", "type": "fixed", "nullable": false, "size": 32}, {"name": "F28", "type": "bytes", "nullable": false, "size": 1000}, {"name": "F29", "type": "string", "nullable": false, "size": 10000}], "keys": [{"name": "perf_simple_PK", "node": "perf_simple", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "perf_simple_hc1", "index": {"type": "hashcluster"}, "node": "perf_simple", "fields": ["F6"], "constraints": {"unique": false}}, {"name": "perf_simple_hc2", "index": {"type": "hashcluster"}, "node": "perf_simple", "fields": ["F7"], "constraints": {"unique": false}}, {"name": "perf_simple_hc3", "index": {"type": "hashcluster"}, "node": "perf_simple", "fields": ["F9"], "constraints": {"unique": false}}, {"name": "perf_simple_hc4", "index": {"type": "hashcluster"}, "node": "perf_simple", "fields": ["F10"], "constraints": {"unique": false}}]}]