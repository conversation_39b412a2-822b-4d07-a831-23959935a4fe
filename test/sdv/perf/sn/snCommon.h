/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: xxxx
 * Author: liyiwei
 * Create: 2022-11-27
 */

#ifndef __SN_PERF_COMMON_H__
#define __SN_PERF_COMMON_H__
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <dlfcn.h>

#include "snConnect.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "testutil.h"
#include "gmc_internal.h"
#include "gmc_test.h"
#include "gmc_connection.h"

typedef struct GmcContext {
    GmcConnT *conn;
    GmcStmtT *stmt;
} GmcContextT;

typedef void *(WaitFuncT)(int epollFd, void *args);

typedef struct {
    int flag;
} AsyncEpollFuncArgsT;

typedef struct {
    bool running;
    int id;
} SubEpollFuncArgsT;

typedef struct {
    WaitFuncT *func;
    int epollFd;
    void *args;
} EpollWaitFuncT;

typedef struct {
    EpollWaitFuncT funcStruct;
    pthread_t phandle;
} EpollThreadCallT;


int PerfGmcConnectSync(GmcConnT **conn);
int PerfGmcDisConnect(GmcConnT **conn);
int PerfGmcInitContext(GmcContext *ctx);
int PerfGmcDestoryContext(GmcContext *ctx);

int PerfGmcAsyncEpollRunner(EpollThreadCallT *threadCall);
int PerfGmcJoinAsyncEpollRunner(EpollThreadCallT *threadCall);

int PerfGmcSubEpollRunner(EpollThreadCallT *threadCall);
int PerfGmcJoinSubEpollRunner(EpollThreadCallT *threadCall);

int PerfGmcConnectWithEpollRecv(int* proxyFd, GmcConnT **conn, char *connName, GmcConnTypeE connMode);
// ===========================================================================================
int64_t PerfReadJsonFile(const char *path, char **buf)
{
    FILE *fp;
    fp = fopen(path, "rb");
    if (NULL == fp) {
        printf("[PerfReadJsonFile] open file:%s fail.\n", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        printf("[PerfReadJsonFile] fseek file:%s to end failed.\n", path);
        (void)fclose(fp);
        return -1;
    }

    int64_t size = ftell(fp);
    if (size < 0) {
        printf("[PerfReadJsonFile] read file size:%ld failed.\n", size);
        fclose(fp);
        return -1;
    }

    char *pBuffer = (char *)malloc(size + 4);
    if (pBuffer == NULL) {
        printf("[PerfReadJsonFile] malloc memory:%ld for file:%s failed.\n", size + 4, path);
        fclose(fp);
        return -1;
    }
    (void)fseek(fp, 0L, SEEK_SET);
    int64_t readSize = fread(pBuffer, 1, size, fp);
    if (readSize != size) {
        printf("[PerfReadJsonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.\n", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    (void)fclose(fp);
    pBuffer[size] = '\0';
    *buf = pBuffer;
    return size;
}

int PerfGmcConnectSync(GmcConnT **conn)
{
    int ret;
    GmcConnOptionsT *connOptions = NULL;
    GmcConnT *connLocal = NULL;
    *conn = NULL;
    ret = createConnOptions(&connOptions);
    if (ret != 0) {
        if (connOptions == NULL) {
            goto EXIT;
        }
    }

    ret = connectProxy(GMC_CONN_TYPE_SYNC, connOptions, &connLocal);
    if (ret != 0) {
        goto EXIT;
    }

    *conn = connLocal;
    GmcConnOptionsDestroy(connOptions);
    return GMERR_OK;
EXIT:
    GmcConnOptionsDestroy(connOptions);
    return ret;
}

int PerfGmcDisConnect(GmcConnT **conn)
{
    if (*conn == NULL) {
        return 0;
    }
    int ret;
    ret = GmcDisconnect(*conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    *conn = NULL;
    return ret;
}

int PerfGmcCreateContext(GmcContext *ctx)
{
    int ret = PerfGmcConnectSync(&ctx->conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(ctx->conn, &ctx->stmt);

    return ret;
}

int PerfGmcDestoryContext(GmcContext *ctx)
{
    GmcFreeStmt(ctx->stmt);
    PerfGmcDisConnect(&ctx->conn);
    return 0;
}

void *AsyncMain(int epollFd, void *arg)
{
    AsyncEpollFuncArgsT *args = (AsyncEpollFuncArgsT *)arg;
    int *asyncFlag = &args->flag;

    char tmpBuf[24];
    (void)snprintf(tmpBuf, sizeof(tmpBuf), "epollAsync");
    prctl(PR_SET_NAME, tmpBuf);
    int n = 5;
    struct epoll_event etOut[5];
    int i, nfds;
    while (*asyncFlag) {
        nfds = epoll_wait(epollFd, etOut, n, 0);
        if (nfds == 0) {
            usleep(1000);
            continue;
        } else if (nfds < 0) {
            printf("[AsyncMain] epoll_wait failed, ret = %d, errno = %d.\n", nfds, errno);
            usleep(1000);
            continue;
        }
        for (i = 0; i < nfds; ++i) {
            GmcHandleRWEvent(etOut[i].data.fd, etOut[i].events);
        }
    }
}

void *SubscribeMain(int epollFd, void *arg)
{
    SubEpollFuncArgsT *p = (SubEpollFuncArgsT *)arg;
    char tmpBuf[24];
    (void)snprintf(tmpBuf, sizeof(tmpBuf), "epollSub%04d", p->id);
    prctl(PR_SET_NAME, tmpBuf);
    int n = 64;
    struct epoll_event *etOut = (struct epoll_event *)malloc(sizeof(struct epoll_event) * n);
    if (etOut == NULL) {
        printf("[tid:%d] [SubscribeMain] malloc memory for etOut failed.\n", p->id);
        return NULL;
    }
    memset(etOut, 0, sizeof(struct epoll_event) * n);
    int i, nfds;
    p->running = true;
    while (p->running) {
        nfds = epoll_wait(epollFd, etOut, n, 0);
        if (nfds == 0) {
            usleep(1000);
            continue;
        } else if (nfds < 0) {
            printf("[SubscribeMain] epoll_wait(fd:%d,recv) failed, ret = %d, errno = %d.\n", p->id, nfds, errno);
            usleep(1000);
            continue;
        }
        for (i = 0; i < nfds; ++i) {
            GmcHandleRWEvent(etOut[i].data.fd, etOut[i].events);
        }
    }
    free(etOut);
}

void *FuncProxy(void *args)
{
    EpollWaitFuncT *funcStruct = (EpollWaitFuncT*)args;
    return funcStruct->func(funcStruct->epollFd, funcStruct->args);
}

int CreateThread(pthread_t *pt, EpollWaitFuncT *func)
{
    pthread_create(pt, NULL, FuncProxy, func);
    return 0;
}

int PerfGmcAsyncEpollRunner(EpollThreadCallT *threadCall)
{
    threadCall->funcStruct.epollFd = epoll_create(5);
    if (threadCall->funcStruct.epollFd == 0) {
        return -1;
    }
    threadCall->funcStruct.func = AsyncMain;

    AsyncEpollFuncArgsT *asyncArgs = (AsyncEpollFuncArgsT *)threadCall->funcStruct.args;
    asyncArgs->flag = true;

    int ret = CreateThread(&threadCall->phandle, &threadCall->funcStruct);
    if (ret != 0) {
        printf("error");
        return -1;
    }
    return 0;
}

int PerfGmcSubEpollRunner(EpollThreadCallT *threadCall)
{
    threadCall->funcStruct.epollFd = epoll_create(64);
    if (threadCall->funcStruct.epollFd == 0) {
        return -1;
    }
    threadCall->funcStruct.func = SubscribeMain;

    SubEpollFuncArgsT *subArgs = (SubEpollFuncArgsT *)threadCall->funcStruct.args;
    subArgs->running = true;

    int ret = CreateThread(&threadCall->phandle, &threadCall->funcStruct);
    if (ret != 0) {
        printf("error");
        return -1;
    }
    subArgs->id = threadCall->phandle;
    return 0;
}

int PerfGmcJoinAsyncEpollRunner(EpollThreadCallT *threadCall)
{
    AsyncEpollFuncArgsT* args = (AsyncEpollFuncArgsT*)threadCall->funcStruct.args;
    args->flag = false;
    pthread_join(threadCall->phandle, NULL);
    close(threadCall->funcStruct.epollFd);
    return 0;
}

int PerfGmcJoinSubEpollRunner(EpollThreadCallT *threadCall)
{
    SubEpollFuncArgsT* args = (SubEpollFuncArgsT*)threadCall->funcStruct.args;
    args->running = false;
    pthread_join(threadCall->phandle, NULL);
    close(threadCall->funcStruct.epollFd);
    return 0;
}

int epollEventCommon(int epfd, int fd, GmcEpollCtlTypeE type, uint32_t events = EPOLLIN)
{
    if (epfd == 0) {
        return 0;
    }
    int ret;
    if (type == GMC_EPOLL_DEL) {
        ret = epoll_ctl(epfd, EPOLL_CTL_DEL, fd, NULL);
        if (ret != 0) {
            printf(" EPOLL_CTL_DEL(%d, %d, %d) failed, ret = %d\n", epfd, fd, type, ret);
        }
        return ret;
    }
    struct epoll_event e;
    e.data.fd = fd;
    e.events = events;
    if (type == GMC_EPOLL_ADD) {
        printf("epoll add succ epfd %d fd %d\n", epfd, fd);
        ret = epoll_ctl(epfd, EPOLL_CTL_ADD, fd, &e);
        if (ret != 0) {
            printf(" EPOLL_CTL_ADD(%d, %d, %d) failed, errCode:%d, errNo:%d\n", epfd, fd, type, ret, errno);
            assert(0);
        }
        return ret;
    }
    if (type == GMC_EPOLL_MOD) {
        ret = epoll_ctl(epfd, EPOLL_CTL_MOD, fd, &e);
        if (ret != 0) {
            printf(" EPOLL_CTL_MOD(%d, %d, %d) failed, errCode:%d, errNo:%d\n", epfd, fd, type, ret, errno);
            assert(0);
        }
        return ret;
    }
}

int asyncEpollEvent(int32_t fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    int epfd = *(int *)userData;
    return epollEventCommon(epfd, fd, type, events);
}

int createConnOptionsAsync(GmcConnOptionsT **connOptions, char *connName, int *asyncFd)
{
    GmcConnOptionsT *connOptionsLocal = NULL;
    int ret = createConnOptions(&connOptionsLocal);
    if (ret != GMERR_OK) {
        printf("Create Conn Options Failed, ret = %d.\n", ret);
        return ret;
    }
    if (connName != NULL) {
        ret = GmcConnOptionsSetConnName(connOptionsLocal, connName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsLocal, asyncEpollEvent, (void *)asyncFd);
    if (ret != 0) {
        printf("GmcConnOptionsSetEpollRegFuncWithUserData failed, ret = %d.\n", ret);
        goto EXIT;
    }

    *connOptions = connOptionsLocal;
    return GMERR_OK;
EXIT:
    GmcConnOptionsDestroy(connOptionsLocal);
    return ret;
}

// 创建连接并融合进入epoll fd和其thread
int PerfGmcConnectWithEpollRecv(int* proxyFd, GmcConnT **conn, char *connName, GmcConnTypeE connMode)
{
    int ret;
    *conn = NULL;
    GmcConnOptionsT *connOptions = NULL;
    GmcConnT *connInner;

    ret = createConnOptionsAsync(&connOptions, connName, proxyFd);
    if (ret != 0) {
        goto EXIT;
    }
    ret = connectProxy(connMode, connOptions, &connInner);
    if (ret != 0) {
        goto EXIT;
    }

    *conn = connInner;
    GmcConnOptionsDestroy(connOptions);
    return GMERR_OK;
EXIT:
    GmcConnOptionsDestroy(connOptions);
    return ret;
}

#endif  // #define  __SN_PERF_COMMON_H__
