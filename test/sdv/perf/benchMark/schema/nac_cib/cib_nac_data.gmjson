{"comment": "table of cib_nac_data", "version": "2.0", "type": "record", "name": "cib_nac_data", "config": {"check_validity": false}, "max_record_count": 102400, "fields": [{"name": "ulCID", "type": "uint32", "comment": "cid"}, {"name": "ulCidPrefix", "type": "uint32", "comment": "prefix of cid"}, {"name": "ulMagic", "type": "uint32", "comment": "Magic Number"}, {"name": "ulVsysID", "type": "uint32", "comment": "VsysID"}, {"name": "ulVrID", "type": "uint32", "comment": "VrID"}, {"name": "ulTaskID", "type": "uint32", "comment": "TaskID"}, {"name": "ucState", "type": "uint8", "comment": "State"}, {"name": "ucSubState", "type": "uint8", "comment": "SubState"}, {"name": "ucTableType", "type": "uint8", "comment": "Table Type"}, {"name": "ucAccessType", "type": "uint8", "comment": "Access Type"}, {"name": "ucTriggerType", "type": "uint8", "comment": "Trigger Type"}, {"name": "ucUserType", "type": "uint8", "comment": "User Type"}, {"name": "ucIsV6User", "type": "uint8", "comment": "V6 User Flag"}, {"name": "ucIfModifyAccNum", "type": "uint8", "comment": "Acc Num Modify Flag"}, {"name": "ucSendLogInFlag", "type": "uint8", "comment": "Send LogIn Flag"}, {"name": "ulAccessTime", "type": "uint32", "comment": "Access Time"}, {"name": "lAccessTime", "type": "uint32", "comment": "Last Access Time"}, {"name": "usPort", "type": "uint16", "comment": "Port"}, {"name": "ulPortType", "type": "uint32", "comment": "Port Type"}, {"name": "ulInterface", "type": "uint32", "comment": "Interface"}, {"name": "ulLogicInterface", "type": "uint32", "comment": "Logic Interface"}, {"name": "ulPortIndex", "type": "uint32", "comment": "Port Index"}, {"name": "ulL2Interface", "type": "uint32", "comment": "L2 Interface"}, {"name": "ucSlot", "type": "uint8", "comment": "Slot"}, {"name": "ucPic", "type": "uint8", "comment": "Pic"}, {"name": "usMTU", "type": "uint16", "comment": "MTU"}, {"name": "ulSrcNodeID", "type": "uint32", "comment": "Source NodeID"}, {"name": "ulSrcChnlID", "type": "uint32", "comment": "Source Channel ID"}, {"name": "ulAccessModIndex", "type": "uint32", "comment": "Access Mod Index"}, {"name": "ulAuthModIndex", "type": "uint32", "comment": "Auth Mod Index"}, {"name": "ulIpAddr", "type": "uint32", "comment": "IpAddr"}, {"name": "ulVrfIndex", "type": "uint32", "comment": "VrfIndex"}, {"name": "ulGateWay", "type": "uint32", "comment": "GateWay"}, {"name": "ulMask", "type": "uint32", "comment": "Mask"}, {"name": "ulAuthProfileIndex", "type": "uint32", "comment": "AuthProfile Index"}, {"name": "ucPortalLayerFlag", "type": "uint8", "comment": "Portal Layer Flag"}, {"name": "ucWlanFlag", "type": "uint8", "comment": "<PERSON><PERSON>"}, {"name": "ucIsETrunkAccess", "type": "uint8", "comment": "ETrunk Access Flag"}, {"name": "ucAuthFailAccessType", "type": "uint8", "comment": "Auth Fail Access Type"}, {"name": "waitMsgType", "type": "uint8", "comment": "wait msg type"}, {"name": "ucResponeSuccessToEAPFlag", "type": "uint8", "comment": "Respone Success To EAP Flag"}, {"name": "ucResponeSuccessToWEBFlag", "type": "uint8", "comment": "Respone Success To WEB Flag"}, {"name": "ulRdsAuthenSvrVrf", "type": "uint32", "comment": "Rds Authen Svr Vrf"}, {"name": "usDomainIndex", "type": "uint16", "comment": "Domain Index"}, {"name": "usServiceProfile", "type": "uint16", "comment": "Service Profile"}, {"name": "usRDSTemplateID", "type": "uint16", "comment": "RDS TemplateID"}, {"name": "ucAuthType", "type": "uint8", "comment": "Auth Type"}, {"name": "ucAuthedPlace", "type": "uint8", "comment": "Authed Place"}, {"name": "uiTACTemplateID", "type": "uint32", "comment": "TAC TemplateID"}, {"name": "uiLDAPTemplateID", "type": "uint32", "comment": "LDAP TemplateID"}, {"name": "uiADTemplateID", "type": "uint32", "comment": "AD TemplateID"}, {"name": "ucAuthoredPlace", "type": "uint8", "comment": "Authored Place"}, {"name": "ucAuthenType", "type": "uint8", "comment": "Authen Type"}, {"name": "ucAAASuccFlag", "type": "uint8", "comment": "AAA Success Flag"}, {"name": "ucSubMsgType", "type": "uint8", "comment": "SubMsg Type"}, {"name": "stRdsAuthenSvr", "type": "fixed", "size": 16, "comment": "Rds Authen Server"}, {"name": "ucSubAuthenCode", "type": "uint8", "comment": "Sub Authen Code"}, {"name": "ucPreAuthenCode", "type": "uint8", "comment": "Pre Authen Code"}, {"name": "ulAAAAuthType", "type": "uint32", "comment": "AAA Auth Type"}, {"name": "szAcctSessionID", "type": "fixed", "size": 64, "comment": "Acct SessionID"}, {"name": "ul<PERSON>lan", "type": "uint32", "comment": "VlanID"}, {"name": "usActionFlag", "type": "uint16", "comment": "Action Flag"}, {"name": "ulAttrFlag", "type": "uint32", "comment": "Attribute Flag"}, {"name": "bLeavingFlagAAA", "type": "uint8", "comment": "Leaving Flag of AAA"}, {"name": "bLeavingFlagWEB", "type": "uint8", "comment": "Leaving Flag of WEB"}, {"name": "bLeavingFlagFC", "type": "uint8", "comment": "Leaving Flag of FC"}, {"name": "bLeavingFlagDHCPR", "type": "uint8", "comment": "Leaving Flag of DHCPR"}, {"name": "bLeavingFlagEAPOL", "type": "uint8", "comment": "Leaving Flag of EAPOL"}, {"name": "bLeavingFlagWLAN", "type": "uint8", "comment": "Leaving Flag of WLAN"}, {"name": "bLeavingFlagAdmin", "type": "uint8", "comment": "Leaving Flag of Admin"}, {"name": "bLeavingFlagPPP", "type": "uint8", "comment": "Leaving Flag of PPP"}, {"name": "bLeavingFlagSrcMod", "type": "uint8", "comment": "Leaving Flag of SrcMod"}, {"name": "szUserMac", "type": "fixed", "size": 6, "comment": "User Mac Addr"}, {"name": "ucDelRetryTimes", "type": "uint8", "comment": "Retry Times"}, {"name": "ucGateWayFlag", "type": "uint8", "comment": "GateWay Flag"}, {"name": "ucUpFlag", "type": "uint8", "comment": "Up Flag"}, {"name": "ucPreAcctMethod", "type": "uint8", "comment": "Pre Acct Method"}, {"name": "ucPreAuthPlace", "type": "uint8", "comment": "Pre Auth Place"}, {"name": "ucBatchFlag", "type": "uint8", "comment": "Batch Flag"}, {"name": "ucBakModifyFlog", "type": "uint8", "comment": "Back Modify Flog"}, {"name": "ucBakState", "type": "uint8", "comment": "BakState"}, {"name": "ulVlanifIndex", "type": "uint32", "comment": "Vlanif Index"}, {"name": "is_per_user_hash_added", "type": "uint8", "comment": "per user hash added flag"}, {"name": "ucLocalAuthorType", "type": "uint8", "comment": "Local Author Type"}, {"name": "original_ulVlan", "type": "uint32", "comment": "original Vlan id"}, {"name": "ulMagicNumber", "type": "uint32", "comment": "MagicNumber"}, {"name": "ucUserNameAccountFlag", "type": "uint8", "comment": "User Name Account Flag"}, {"name": "usSqId", "type": "uint16", "comment": "Sequence Id"}, {"name": "ucAccessMode", "type": "uint8", "comment": "Access Mode"}, {"name": "ulModifyTriggerBmp", "type": "uint32", "comment": "Trigger Bit map modify flag"}, {"name": "ulAuthorModifyBitmap", "type": "uint32", "comment": "Author Modify Bit map flag"}, {"name": "ucAuthVlanType", "type": "uint8", "comment": "Auth <PERSON>lan <PERSON>"}, {"name": "usAuthVlan", "type": "uint16", "comment": "Auth Vlan id"}, {"name": "ucUserVlanSource", "type": "uint8", "comment": "User Vlan Source"}, {"name": "ucWebMangerUserFlag", "type": "uint8", "comment": "Web Manger User Flag"}, {"name": "ucIsForceDomainFlag", "type": "uint8", "comment": "Force Domain Flag"}, {"name": "lIdleSec", "type": "int32", "comment": "Idle Second"}, {"name": "ucBakUserFlag", "type": "uint8", "comment": "Backup User Flag"}, {"name": "ucBakNeedAddFlag", "type": "uint8", "comment": "Backup Need Add Flag"}, {"name": "ulBakRandom", "type": "uint32", "comment": "Bak Random"}, {"name": "ulUserNameMaxNum", "type": "uint32", "comment": "User Name <PERSON>"}, {"name": "ucAclOKV6", "type": "uint8", "comment": "Acl OK V6"}, {"name": "ucIfHasProcAdpAuthor", "type": "uint8", "comment": "Proc Adp Author"}, {"name": "ucNotProcFcFlag", "type": "uint8", "comment": "Not Proc Fc Flag"}, {"name": "isVoiceDev", "type": "uint8", "comment": "Is voice device"}, {"name": "isdot1xmacReAuth", "type": "uint8", "comment": "Is dot1x mac reauth"}, {"name": "ulAcctSessionTime", "type": "uint32", "comment": "Acct Session Time"}, {"name": "ucIpConflictCheck", "type": "uint8", "comment": "Ip Conflict Check"}, {"name": "ucPreAuthFlag", "type": "uint8", "comment": "Pre Auth Flag"}, {"name": "ucIsPortBaseAuthenMac", "type": "uint8", "comment": "Port Base Authen Mac"}, {"name": "ucHaveUpdateCounter", "type": "uint8", "comment": "Have Update Counter"}, {"name": "ulReauthTimeLen", "type": "uint32", "comment": "<PERSON><PERSON><PERSON>"}, {"name": "ulHacaServerIp", "type": "uint32", "comment": "Haca Server Ip"}, {"name": "ucOpenUser", "type": "uint8", "comment": "Open User"}, {"name": "ucStaticUserFlag", "type": "uint8", "comment": "Static User Flag"}, {"name": "ucIsVoiceTerminal", "type": "uint8", "comment": "Voice Terminal Flag"}, {"name": "ucISPFlag", "type": "uint8", "comment": "ISP Flag"}, {"name": "ucLeaseFlag", "type": "uint8", "comment": "Lease Flag"}, {"name": "ulDnsIp1", "type": "uint32", "comment": "Dns Ip1"}, {"name": "ulDnsIp2", "type": "uint32", "comment": "Dns Ip2"}, {"name": "ulWinsIP1", "type": "uint32", "comment": "Wins IP1"}, {"name": "ulWinsIP2", "type": "uint32", "comment": "Wins IP2"}, {"name": "ucUserGroupChanged", "type": "uint8", "comment": "User Group Changed"}, {"name": "ucHttpFwdFlg", "type": "uint8", "comment": "Http Fwd Flag"}, {"name": "ulSessionStartTime", "type": "uint32", "comment": "Session Start Time"}, {"name": "ucTermAct", "type": "uint8", "comment": "Term Act"}, {"name": "ucPushFlag", "type": "uint8", "comment": "Push Flag"}, {"name": "ucAuthorizeType", "type": "uint8", "comment": "Authorize Type"}, {"name": "ucUnRestartTimerFlag", "type": "uint8", "comment": "UnRestart Timer Flag"}, {"name": "ulEAPSessionTime", "type": "uint32", "comment": "EAP Session Time"}, {"name": "ucVlanType", "type": "uint8", "comment": "Vlan Type"}, {"name": "usISPvlan", "type": "uint16", "comment": "ISP VlanID"}, {"name": "usVLAN", "type": "uint16", "comment": "VLANID"}, {"name": "usGroupID", "type": "uint16", "comment": "GroupID"}, {"name": "usUclGroupId", "type": "uint16", "comment": "Ucl GroupId"}, {"name": "ucInnerIsolated", "type": "uint8", "comment": "Inner Isolated"}, {"name": "ucInterIsolated", "type": "uint8", "comment": "Inter Isolated"}, {"name": "ulMacLimit", "type": "uint32", "comment": "<PERSON>"}, {"name": "ulSessionTimeOut", "type": "uint32", "comment": "Session TimeOut"}, {"name": "ucPriority", "type": "uint8", "comment": "Priority"}, {"name": "ucDownPriority", "type": "uint8", "comment": "DownPriority"}, {"name": "ucInDscpValue", "type": "uint8", "comment": "In Dscp Value"}, {"name": "ucIn8021pValue", "type": "uint8", "comment": "In 8021p Value"}, {"name": "ucInExpValue", "type": "uint8", "comment": "In Exp Value"}, {"name": "ucInLPValue", "type": "uint8", "comment": "In LP Value"}, {"name": "ucOutDscpValue", "type": "uint8", "comment": "Out Dscp Value"}, {"name": "ucOut8021pValue", "type": "uint8", "comment": "Out 8021p Value"}, {"name": "ucOutExpValue", "type": "uint8", "comment": "Out Exp Value"}, {"name": "ucOutLPValue", "type": "uint8", "comment": "Out LP Value"}, {"name": "ucIsSupportBandWidth", "type": "uint8", "comment": "Support BandWidth"}, {"name": "ucBandWidthSlot", "type": "uint8", "comment": "BandWidth Slot"}, {"name": "ulUpLinkbandwidth", "type": "uint32", "comment": "UpLink bandwidth"}, {"name": "ulDownLinkBWCir", "type": "uint32", "comment": "DownLink bandwidth Cir"}, {"name": "ulDownLinkBWCbs", "type": "uint32", "comment": "DownLink bandwidth Cbs"}, {"name": "ucHttpToCpuFlag", "type": "uint8", "comment": "Http To Cpu Flag"}, {"name": "ucHttpsToCpuFlag", "type": "uint8", "comment": "Https To Cpu Flag"}, {"name": "ucProtocolTpye", "type": "uint8", "comment": "Protocol Tpye"}, {"name": "usPortNum", "type": "uint16", "comment": "Port Num"}, {"name": "ucBandWidthShareMode", "type": "uint8", "comment": "BandWidth Share Mode"}, {"name": "ulForwardInterface", "type": "uint32", "comment": "Forward Interface"}, {"name": "ucDaaStaticEnable", "type": "uint8", "comment": "Daa Static Enable"}, {"name": "ucStaticsUpMap", "type": "uint8", "comment": "Statics Up Map"}, {"name": "ucStaticsDownMap", "type": "uint8", "comment": "Statics Down Map"}, {"name": "ucAcctMap", "type": "uint8", "comment": "Acct Map"}, {"name": "ucUserUpFlowStaticsFlag", "type": "uint8", "comment": "User Up Flow Statics Flag"}, {"name": "ucUserDownFlowStaticsFlag", "type": "uint8", "comment": "User Down Flow Statics Flag"}, {"name": "ucFlowStatEnableFlag", "type": "uint8", "comment": "Flow Stat Enable Flag"}, {"name": "ucDscp", "type": "uint8", "comment": "Dscp"}, {"name": "ucAccoutingSeparate", "type": "uint8", "comment": "Accouting Separate"}, {"name": "ucV6StaticsUpMap", "type": "uint8", "comment": "V6 Statics Up Map"}, {"name": "ucV6StaticsDownMap", "type": "uint8", "comment": "V6 Statics Down Map"}, {"name": "ulLocalAuthorBitMap", "type": "uint32", "comment": "Local Author Bit Map"}, {"name": "ulRemoteAuthorBitMap", "type": "uint32", "comment": "Remote Author Bit Map"}, {"name": "stAuthorizeInfo_ulIpAddr", "type": "uint32", "comment": "Authorize Info Ip Addr"}, {"name": "stAuthorizeInfo_ulGateWay", "type": "uint32", "comment": "Authorize Info GateWay"}, {"name": "stAuthorizeInfo_ulMask", "type": "uint32", "comment": "Authorize Info Mask"}, {"name": "ucIfRdsAcl", "type": "uint8", "comment": "Rds Acl"}, {"name": "ucServiceSchemeVoiceVlan", "type": "uint8", "comment": "Service Scheme Voice Vlan"}, {"name": "userAddrNetwork_ulIP", "type": "uint32", "comment": "user Addr Network IP"}, {"name": "userAddrNetwork_ulMask", "type": "uint32", "comment": "user Addr Network Mask"}, {"name": "duRemainFlow", "type": "uint64", "comment": "Remain Flow"}, {"name": "usRedirectAclId", "type": "uint16", "comment": "Redirect Acl Id"}, {"name": "ucSessionTimeoutSource", "type": "uint8", "comment": "Session Timeout Source"}, {"name": "ucDot1xUrlFlag", "type": "uint8", "comment": "Dot1x Url Flag"}, {"name": "ucUserGroupPriority", "type": "uint8", "comment": "User Group Priority"}, {"name": "ucUpRemarkOK", "type": "uint8", "comment": "Up Remark OK"}, {"name": "ucDnRemarkOK", "type": "uint8", "comment": "Down Remark OK"}, {"name": "usSesssionID", "type": "uint16", "comment": "SesssionID"}, {"name": "ucOptionLen", "type": "uint8", "comment": "OptionLen"}, {"name": "ucDownQosOK", "type": "uint8", "comment": "Down Qos OK"}, {"name": "ucDhcpAckFlag", "type": "uint8", "comment": "Dhcp Ack Flag"}, {"name": "ucIfNeedDHCPAck", "type": "uint8", "comment": "Need DHCP Ack Flag"}, {"name": "ucIsIpStaticUserEnable", "type": "uint8", "comment": "Ip Static User Enable"}, {"name": "szLocalMac", "type": "fixed", "size": 6, "comment": "Local Mac"}, {"name": "ucIfVpdn", "type": "uint8", "comment": "Vpdn"}, {"name": "usUserGroup", "type": "uint16", "comment": "User Group"}, {"name": "usUclGroup", "type": "uint16", "comment": "Ucl Group"}, {"name": "ucIsHacaUserFlag", "type": "uint8", "comment": "Haca User Flag"}, {"name": "ulIPSecAcl", "type": "uint32", "comment": "IP Sec Acl"}, {"name": "ucIPSecFlag", "type": "uint8", "comment": "IP Sec Flag"}, {"name": "ucSetGroupOk", "type": "uint8", "comment": "Set Group Ok"}, {"name": "ucUpDscpOK", "type": "uint8", "comment": "Up Dscp OK"}, {"name": "ucDnDscpOK", "type": "uint8", "comment": "Down Dscp OK"}, {"name": "szSysMac", "type": "fixed", "size": 6, "comment": "System Mac"}, {"name": "ucIsV6Acl", "type": "uint8", "comment": "V6Acl"}, {"name": "ucIsPortBased", "type": "uint8", "comment": "Port Based Flag"}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "cib_nac_data", "fields": ["ulCID", "ulCidPrefix"], "constraints": {"unique": true}, "comment": "key of cib_nac_data"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "cib_nac_data", "fields": ["ulCidPrefix"], "constraints": {"unique": false}, "comment": "local key of cib_nac_data"}]}