{"comment": "认证用户基本信息表", "version": "2.0", "type": "record", "name": "fwm_nac_cib_base_info", "config": {"check_validity": false}, "max_record_count": 100000, "special_complex": false, "fields": [{"name": "global_cid", "type": "uint32", "comment": "用户全局CID"}, {"name": "instance_id", "type": "uint32", "comment": "用户所在业务进程"}, {"name": "vr_id", "type": "uint32", "comment": "用户vr id"}, {"name": "vsys_id", "type": "uint32", "comment": "用户vsys id"}, {"name": "mac", "type": "fixed", "size": 6, "comment": "用户Mac地址"}, {"name": "user_type", "type": "uint32", "default": 4294967295, "comment": "用户类型"}, {"name": "access_type", "type": "uint32", "default": 4294967295, "comment": "接入类型"}, {"name": "auth_code_type", "type": "uint32", "default": 4294967295, "comment": "认证类型"}, {"name": "remote_type", "type": "uint8", "default": 0, "comment": "远端用户类型"}, {"name": "if_index", "type": "uint32", "comment": "接入接口"}, {"name": "act_vlan", "type": "uint16", "comment": "生效的VLAN"}, {"name": "bd_id", "type": "uint32", "default": 0, "comment": "用户属于的BD ID"}, {"name": "vsi_id", "type": "uint32", "default": 0, "comment": "Mac表对应的vsi id"}, {"name": "create_time", "type": "uint64", "default": 0, "comment": "表记录生成时间"}, {"name": "ip_type", "type": "uint32", "default": 4294967295, "comment": "用户地址类型"}, {"name": "ipv4_addr", "type": "uint32", "comment": "用户V4地址"}, {"name": "preauth_flag", "type": "uint8", "default": 0, "comment": "预连接标记"}, {"name": "port_base", "type": "uint8", "default": 0, "comment": "port base用户标记"}, {"name": "auth_flag", "type": "uint32", "default": 0, "comment": "授权业务标记，bitmap"}, {"name": "detect_type", "type": "uint32", "comment": "探测类型"}, {"name": "detect_period", "type": "uint32", "comment": "探测间隔"}, {"name": "detect_timer", "type": "uint32", "comment": "探测定时器"}, {"name": "is_eth_trunk_access", "type": "uint32", "comment": "eth口标记"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "fwm_nac_cib_base_info", "fields": ["global_cid", "instance_id"], "constraints": {"unique": true}, "comment": "基于全局CID的主Key"}]}