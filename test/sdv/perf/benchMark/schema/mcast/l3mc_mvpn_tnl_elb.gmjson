{"comment": "vrp fes 281 L3MC_MVPN4_TNL_ELB", "version": "2.0", "type": "record", "name": "l3mc_mvpn_tnl_elb", "config": {"check_validity": false, "generate_cache_interface": true}, "max_record_count": 262144, "fields": [{"name": "vrId", "type": "uint32", "comment": "VR索引"}, {"name": "vrfId", "type": "uint32", "comment": "VRF索引"}, {"name": "groupAddr", "type": "uint32", "comment": "组播组地址"}, {"name": "sourceAddr", "type": "uint32", "comment": "组播源地址"}, {"name": "mTIIndex", "type": "uint32", "comment": "MTI索引"}, {"name": "tnlVrId", "type": "uint32", "comment": "隧道VR索引"}, {"name": "tnlVrfId", "type": "uint32", "comment": "隧道VRF索引"}, {"name": "vniId", "type": "uint32", "comment": "VXLAN索引"}, {"name": "tnlType", "type": "uint8", "comment": "隧道类型"}, {"name": "resv", "type": "fixed", "size": 3, "comment": "预留字段"}, {"name": "pathFlags", "type": "uint32", "comment": "表项完备性标识"}, {"name": "tnlId", "type": "uint32", "comment": "隧道索引"}, {"name": "mcId", "type": "uint32", "comment": "组播组Id"}, {"name": "mcidVersion", "type": "uint64", "comment": "组播组Id的版本号"}, {"name": "mfibVersion", "type": "uint32", "comment": "组mfib和elb一致性判断的版本号，mfib删除场景仅删除<=version的elb"}, {"name": "tnlGroupAddr", "type": "uint32", "comment": "隧道组播组地址"}, {"name": "tnlSourceAddr", "type": "uint32", "comment": "隧道组播源地址"}, {"name": "appSrcPid", "type": "uint32", "comment": "vrp生产者appid"}, {"name": "fVrfIndex", "type": "uint32", "comment": "FVRF索引"}, {"name": "appVersion", "type": "uint32", "comment": "对账版本号，与APP对账使用"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑对账版本号"}, {"name": "serviceStatus", "type": "fixed", "size": 2, "comment": "mvpn_tnl_elb本身不下svc，此处用于记录表项是否已经下发且完备"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l3mc_mvpn_tnl_elb", "fields": ["vrId", "vrfId", "groupAddr", "sourceAddr"], "constraints": {"unique": true}, "comment": "主键索引, 确定唯一的组播用叶子"}, {"name": "vni_key", "index": {"type": "hashcluster"}, "node": "l3mc_mvpn_tnl_elb", "fields": ["tnlVrId", "tnlVrfId", "vniId"], "constraints": {"unique": false}, "comment": "underlay地址变化反刷"}, {"name": "srcpid_key", "index": {"type": "hashcluster"}, "node": "l3mc_mvpn_tnl_elb", "fields": ["vrId", "vrfId", "appSrcPid"], "constraints": {"unique": false}, "comment": "老化查询索引（vrId, vrfId, appSrcPid）"}, {"name": "tnl_vsg_key", "index": {"type": "hashcluster"}, "node": "l3mc_mvpn_tnl_elb", "fields": ["tnlVrId", "tnlVrfId", "tnlGroupAddr", "tnlSourceAddr"], "constraints": {"unique": false}, "comment": "underlay叶子变化反刷"}]}