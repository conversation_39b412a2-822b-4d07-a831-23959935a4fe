#!/bin/bash


function usage()
{
    echo "usage: $0 -R [ex_st/parse_txt]"
    exit 1
}

if [ $# -eq 0 ]; then
    usage
fi

# 预期值
function Ex_Average() {
    case $1 in
        0) echo "100";;
        1) echo "2";;
        2) echo "1.5";;
        3) echo "1.5";;
        4) echo "13";;
        5) echo "19";;
        6) echo "23";;
        7) echo "100";;
        8) echo "100";;
        9) echo "100";;
        10) echo "100";;
        11) echo "100";;
        12) echo "100";;
        13) echo "100";;
        14) echo "100";;
        15) echo "100";;
        16) echo "100";;
        17) echo "1";;
        18) echo "1";;
        19) echo "10";;
        20) echo "7";;
        21) echo "100";;
        22) echo "100";;
        23) echo "100";;
        24) echo "100";;
        25) echo "2";;
        26) echo "1.5";;
        27) echo "2";;
        28) echo "68";;
        29) echo "46";;
        30) echo "55";;
        31) echo "100";;
        32) echo "100";;
        33) echo "100";;
        34) echo "1.6";;
        35) echo "1.3";;
        36) echo "1.5";;
        37) echo "10";;
        38) echo "6";;
        39) echo "9";;
        40) echo "33";;
        41) echo "460";;
        42) echo "100";;
        43) echo "100";;
        44) echo "100";;
        45) echo "100";;
        46) echo "100";;
        47) echo "100";;
        48) echo "100";;
        49) echo "100";;
        50) echo "100";;
        *) echo "Invalid inpu100";;
    esac
}


function Ex_Validate() {
    case $1 in
        0) echo "100";;
        1) echo "19";;
        2) echo "3.5";;
        3) echo "7";;
        4) echo "490";;
        5) echo "55";;
        6) echo "170";;
        7) echo "100";;
        8) echo "100";;
        9) echo "100";;
        10) echo "100";;
        11) echo "100";;
        12) echo "100";;
        13) echo "100";;
        14) echo "100";;
        15) echo "100";;
        16) echo "100";;
        17) echo "3";;
        18) echo "1.8";;
        19) echo "58";;
        20) echo "17";;
        21) echo "100";;
        22) echo "100";;
        23) echo "100";;
        24) echo "100";;
        25) echo "18";;
        26) echo "3";;
        27) echo "6";;
        28) echo "601";;
        29) echo "40";;
        30) echo "195";;
        31) echo "100";;
        32) echo "100";;
        33) echo "100";;
        34) echo "3";;
        35) echo "2";;
        36) echo "2";;
        37) echo "23";;
        38) echo "11";;
        39) echo "9";;
        40) echo "88";;
        41) echo "668";;
        42) echo "100";;
        43) echo "100";;
        44) echo "100";;
        45) echo "100";;
        46) echo "100";;
        47) echo "100";;
        48) echo "100";;
        49) echo "100";;
        50) echo "100";;
        *) echo "Invalid inpu100";;
    esac
}

function Ex_Fetchdiff() {
    case $1 in
        0) echo "100";;
        1) echo "2";;
        2) echo "2";;
        3) echo "2";;
        4) echo "6";;
        5) echo "10";;
        6) echo "6";;
        7) echo "100";;
        8) echo "100";;
        9) echo "100";;
        10) echo "100";;
        11) echo "100";;
        12) echo "100";;
        13) echo "100";;
        14) echo "100";;
        15) echo "100";;
        16) echo "100";;
        17) echo "1";;
        18) echo "1";;
        19) echo "3";;
        20) echo "4";;
        21) echo "100";;
        22) echo "100";;
        23) echo "100";;
        24) echo "100";;
        25) echo "1.6";;
        26) echo "2";;
        27) echo "1.8";;
        28) echo "13";;
        29) echo "23";;
        30) echo "15";;
        31) echo "100";;
        32) echo "100";;
        33) echo "100";;
        34) echo "1.6";;
        35) echo "0.5";;
        36) echo "1.5";;
        37) echo "3.6";;
        38) echo "100";;
        39) echo "4";;
        40) echo "6";;
        41) echo "168";;
        *) echo "Invalid inpu100";;
    esac
}


function Ex_Querytime() {
    case $1 in
        0) echo "100";;
        1) echo "100";;
        2) echo "100";;
        3) echo "100";;
        4) echo "100";;
        5) echo "100";;
        6) echo "100";;
        7) echo "100";;
        8) echo "100";;
        9) echo "100";;
        10) echo "100";;
        11) echo "100";;
        12) echo "100";;
        13) echo "100";;
        14) echo "100";;
        15) echo "100";;
        16) echo "100";;
        17) echo "100";;
        18) echo "100";;
        19) echo "100";;
        20) echo "100";;
        21) echo "100";;
        22) echo "100";;
        23) echo "100";;
        24) echo "100";;
        25) echo "100";;
        26) echo "100";;
        27) echo "100";;
        28) echo "100";;
        29) echo "100";;
        30) echo "100";;
        31) echo "100";;
        32) echo "100";;
        33) echo "100";;
        34) echo "100";;
        35) echo "100";;
        36) echo "100";;
        37) echo "100";;
        38) echo "100";;
        39) echo "100";;
        40) echo "100";;
        41) echo "100";;
        42) echo "100";;
        43) echo "100";;
        44) echo "100";;
        45) echo "100";;
        46) echo "100";;
        47) echo "100";;
        48) echo "100";;
        49) echo "100";;
        50) echo "100";;
        *) echo "Invalid inpu100";;
    esac
}


function Ex_Parsetime() {
    case $1 in
        0) echo "100";;
        1) echo "100";;
        2) echo "100";;
        3) echo "100";;
        4) echo "100";;
        5) echo "100";;
        6) echo "100";;
        7) echo "100";;
        8) echo "100";;
        9) echo "100";;
        10) echo "100";;
        11) echo "100";;
        12) echo "100";;
        13) echo "100";;
        14) echo "100";;
        15) echo "100";;
        16) echo "100";;
        17) echo "100";;
        18) echo "100";;
        19) echo "100";;
        20) echo "100";;
        21) echo "100";;
        22) echo "100";;
        23) echo "100";;
        24) echo "100";;
        25) echo "100";;
        26) echo "100";;
        27) echo "100";;
        28) echo "100";;
        29) echo "100";;
        30) echo "100";;
        31) echo "100";;
        32) echo "100";;
        33) echo "100";;
        34) echo "100";;
        35) echo "100";;
        36) echo "100";;
        37) echo "100";;
        38) echo "100";;
        39) echo "100";;
        40) echo "100";;
        41) echo "100";;
        42) echo "100";;
        43) echo "100";;
        44) echo "100";;
        45) echo "100";;
        46) echo "100";;
        47) echo "100";;
        48) echo "100";;
        49) echo "100";;
        50) echo "100";;
        *) echo "Invalid inpu100";;
    esac
}

calculate_stats() {
    # 将参数转换为数组
    local numbers=("$@")

    # 初始化最大值和最小值
    local max=${numbers[0]}
    local min=${numbers[0]}

    # 计算最大值和最小值
    for num in "${numbers[@]}"; do
        if (( $(echo "$num >= $max" | bc -l) )); then
            max=$num
        fi
        if (( $(echo "$num <= $min" | bc -l) )); then
            min=$num
        fi
    done
    formatted_max=$(printf "%.6f" $max)
    formatted_min=$(printf "%.6f" $min)

    # 去除最大值和最小值后计算平均值
    local sum=0
    local count=0
    for num in "${numbers[@]}"; do
            sum=$(echo "$sum + $num" | bc -l)
            ((count++))
    done
    other_sum=$(echo "$sum - $max - $min" | bc)

    local average=$(echo "scale=6; $other_sum / ($count - 2)" | bc)
    formatted_average=$(printf "%.6f" $average)

    # 输出结果
    echo "max: $formatted_max min: $formatted_min ave: $formatted_average"
}


exType="nil"
function parse_opts()
{
    ARGS=""
    while [ $# -gt 0 ]
    do
        unset OPTIND
        unset OPTARG
        while getopts "R:m:f:a:g:p:s:" options
        do
        case $options in
            R)
                envVal="$OPTARG"
                if [ "${envVal}" = "ex_st" ]; then
                    exType=0
                elif [ "${envVal}" = "parse_txt" ]; then
                    exType=1
                else
                    usage
                fi
                ;;
        esac
    done
    shift $((OPTIND-1))
    ARGS="${ARGS} $1"
    if [ "${ARGS}" != " " ]; then
        shift
    fi
    done
}

parse_opts $@
echo "exType:${exType}"

skip_lines=(1 8 9 10 11 12 13 14 15 16 17 22 23 24 25)
skip_diff_empty_line=(33 35 38)
need_64=()
average_time=10

if [ ${exType} -eq 0 ]; then
    echo exType=ex_st
    #source 小型化测试 环境变量
    export GMDB_HOME=/opt/vrpv8/home/<USER>
    source ${GMDB_HOME}/scripts/env_arm32.sh rtos arm32a15le
    cd ${GMDB_HOME}/test/dt/st/persistence/
    for i in 0 1 2 3 4 5 6 7 8 9; do
        pkill gmserver
        for k in `ipcs -m | grep -v "Shared" | grep -v "shmid" | awk {'print $2'}`;do ipcrm -m $k;done
        ./st_persistence --gtest_filter=*PerfTestWithPersistData |tee PerfTestWithPersistData$i.txt
    done
    echo -e "拷贝PerfTestWithPersistData*.txt"
elif [ ${exType} -eq 1 ]; then
    echo exType=parse_txt
    #循环9次 同文件个数
    for j_time in 0 1 2 3 4 5 6 7 8 9;do
        #######################################解析文档#############################################################
        cat PerfTestWithPersistData$j_time.txt |grep 'execute total cost:' |awk -F'execute total cost: ' '{print $2}'|awk -F' ' '{print$1}' >trans_total.txt
        cat PerfTestWithPersistData$j_time.txt |grep 'average cost:' |awk -F'average cost: ' '{print $2}'|awk -F' ' '{print$1}' >trans_average.txt
        cat PerfTestWithPersistData$j_time.txt |grep 'Validate' |awk -F' ' '{print$5}' >Validate.txt
        cat PerfTestWithPersistData$j_time.txt |grep 'Fetch' |awk -F' ' '{print$6}' >FetchDiff.txt
        # 给没有数据得事务填充一行 便于后续比较
        sed -i '33a\0' FetchDiff.txt
        sed -i '35a\0' FetchDiff.txt
        sed -i '38a\0' FetchDiff.txt
        
        rm perf_5800T_half_test_result$j_time.txt -rf;
        # 解析文件每一行对应每个事务的值
        for i in 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50; do
            # 使用awk命令来读取指定行的内容
            line_a=`awk "NR==$i" trans_total.txt`
            line_b=`awk "NR==$i" Validate.txt`
            line_c=`awk "NR==$i" FetchDiff.txt`
            let j_nub=i-1
            Ex_Average ${j_nub} >ex_av.txt
            ex_av=`cat ex_av.txt`
            Ex_Validate ${j_nub} >ex_val.txt
            ex_val=`cat ex_val.txt`
            Ex_Fetchdiff ${j_nub} >ex_diff.txt
            ex_fetchdiff=`cat ex_diff.txt`
            # 解析42 subtree的值
            if [[ $i -gt 42 ]]; then
                Ex_Querytime ${j_nub} >ex_query.txt
                ex_qrytm=`cat ex_query.txt`
                Ex_Parsetime ${j_nub} >ex_parse.txt
                ex_pt=`cat ex_parse.txt`
                sed -n "/transaction: $j_nub/,/transaction: $i/p" PerfTestWithPersistData$j_time.txt |grep 'obj subtree' >subtree.txt
                subtree_line=`cat subtree.txt|wc -l`
                # 求和 
                line_d=`cat subtree.txt|awk -F'qryTime: ' '{print $2}'|awk -F';' '{sum += $1} END {printf "%.6g", sum}'`
                line_e=`cat subtree.txt|awk -F'qryTime: ' '{print $2}'|awk -F' ' '{sum += $3} END {printf "%.6g", sum}'`
                echo -e "trans_${j_nub}: $ex_qrytm ${line_d} $ex_pt ${line_e}" >>perf_5800T_half_test_result$j_time.txt
            else
                echo -e "trans_$j_nub: $ex_av ${line_a} $ex_val ${line_b} $ex_fetchdiff ${line_c}" >>perf_5800T_half_test_result$j_time.txt
            fi
        done
        cat perf_5800T_half_test_result$j_time.txt
        echo "查看文件" `pwd perf_5800T_half_test_result$j_time.txt`"/perf_5800T_half_test_result$j_time.txt"
    done
    # subtree

    # 取平均值
    >perf_5800T_half_test_result.txt
    # 定义文件列表
    files=("perf_5800T_half_test_result0.txt" "perf_5800T_half_test_result1.txt" "perf_5800T_half_test_result2.txt" "perf_5800T_half_test_result3.txt" "perf_5800T_half_test_result4.txt" "perf_5800T_half_test_result5.txt" "perf_5800T_half_test_result6.txt" "perf_5800T_half_test_result7.txt" "perf_5800T_half_test_result8.txt" "perf_5800T_half_test_result9.txt")
    
    # 初始化变量
    declare -a columns_col3
    declare -a columns_col5
    declare -a columns_col7
    # 获取第一个文件的行数
    line_count=`cat ${files[0]} | wc -l`
    # 遍历每一行
    for ((i=1; i<=line_count; i++)); do
        # 初始化变量
        columns_col3=()
        columns_col5=()
        columns_col7=()
        # 排除没有数据的列
        col5=`sed -n "${i}p" ${files[0]} | cut -d' ' -f5`
        #  没有数据则退出
        if [ -z "$col5" ]; then
            break
        fi
        # 遍历每一个文件
        for file in "${files[@]}"; do
            # 读取当前行的第三列和第五列
            col3=$(sed -n "${i}p" "$file" | cut -d' ' -f3)
            col5=$(sed -n "${i}p" "$file" | cut -d' ' -f5)
            columns_col3+=("$col3")
            columns_col5+=("$col5")
        done
        col1=$(sed -n "${i}p" ${files[0]} | cut -d' ' -f1)
        col2=$(sed -n "${i}p" ${files[0]} | cut -d' ' -f2)
        col4=$(sed -n "${i}p" ${files[0]} | cut -d' ' -f4)
        calculate_stats "${columns_col3[@]}" >col3.txt
        col3_max=`cat col3.txt |awk -F' ' '{print$2}'`
        col3_min=`cat col3.txt |awk -F' ' '{print$4}'`
        col3_ave=`cat col3.txt |awk -F' ' '{print$6}'`
        calculate_stats "${columns_col5[@]}" >col5.txt
        col5_max=`cat col5.txt |awk -F' ' '{print$2}'`
        col5_min=`cat col5.txt |awk -F' ' '{print$4}'`
        col5_ave=`cat col5.txt |awk -F' ' '{print$6}'`
        
        if [[ $i -lt 43 ]]; then
            # 遍历每一个文件
            for file in "${files[@]}"; do
                col7=$(sed -n "${i}p" "$file" | cut -d' ' -f7)
                columns_col7+=("$col7")
            done
            col6=$(sed -n "${i}p" ${files[0]} | cut -d' ' -f6)
            col7=$(sed -n "${i}p" "$file" | cut -d' ' -f7)
            columns_col7+=("$col7")
            calculate_stats "${columns_col7[@]}" >col7.txt
            col7_max=`cat col7.txt |awk -F' ' '{print$2}'`
            col7_min=`cat col7.txt |awk -F' ' '{print$4}'`
            col7_ave=`cat col7.txt |awk -F' ' '{print$6}'`
            # 打印结果
            echo -e "$col1 $col2 $col3_max $col3_ave $col4 $col5_max $col5_ave $col6 $col7_max $col7_ave" >>perf_5800T_half_test_result.txt
        else
            # 打印结果
            echo -e "$col1 $col2 $col3_max $col3_ave $col4 $col5_max $col5_ave" >>perf_5800T_half_test_result.txt
        fi
       
    done
    cat perf_5800T_half_test_result.txt
    echo "查看文件" `pwd perf_5800T_half_test_result.txt`"/perf_5800T_half_test_result.txt"
else
    echo "unknown envType:${exType}"
    exit 1
fi


