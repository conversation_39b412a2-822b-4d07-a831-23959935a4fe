# C上的DB服务

# 获取ci目录下面最新的hpe_ci.tar.gz文件
#repFiles=`ls -lt $AC_BASE_WORKSPACE/ci/ | grep "hpe_ci_*" | head -n 1 | awk '{print $9}'`

#替换db bin、so
change_gmdb_so_files()
{
    repFiles=`ls -lt $AC_BASE_WORKSPACE/ci/ | grep "hpe_ci_*" | head -n 1 | awk '{print $9}'`
    echo ">>> replace db.  $repFiles "
    if [ ! -f $AC_BASE_WORKSPACE/ci/$repFiles ];then
        echo "未找到可替换文件"
        exit 0
    else
        cd $AC_BASE_WORKSPACE/ci/; tar -zxvf $repFiles -C /
        echo "-------------------------"
        cat /commit*.txt
        echo "-------------------------"
    fi
}

remove_service()
{
    systemctl stop ftp_server.service
    systemctl stop vcmu.service
    systemctl stop vcmu_agent.service
    systemctl stop swxn_main.service
    kill -9 `pidof pssp`
}


replace_ormso()
{
ls -l /usr/local/lib/libgmwrapper.so
ls -l /usr/local/lib/liborm.so
ls -l /usr/local/lib/libdbstart_adapter.so
rm -f /usr/local/lib/liborm.so
rm -f /usr/local/lib/libgmwrapper.so
rm -f /usr/local/lib/libdbstart_adapter.so
cp ./orm_so/lib/libgmwrapper.so /usr/local/lib/
cp ./orm_so/lib/liborm.so /usr/local/lib/
cp ./orm_so/lib/libdbstart_adapter.so /usr/local/lib/
chmod 777 /usr/local/lib/libgmwrapper.so
chmod 777 /usr/local/lib/liborm.so
chmod 777 /usr/local/lib/libdbstart_adapter.so
ls -l /usr/local/lib/libgmwrapper.so
ls -l /usr/local/lib/liborm.so
ls -l /usr/local/lib/libdbstart_adapter.so

#ls -l /usr/local/hpe/libgmwrapper.so
#ls -l /usr/local/hpe/liborm.so
#rm -f /usr/local/hpe/libgmwrapper.so
#rm -f /usr/local/hpe/liborm.so
#cp ./orm_so/hpe/libgmwrapper.so /usr/local/hpe/
#cp ./orm_so/hpe/liborm.so /usr/local/hpe/
#chmod 777 /usr/local/hpe/libgmwrapper.so
#chmod 777 /usr/local/hpe/liborm.so
#ls -l /usr/local/hpe/libgmwrapper.so
#ls -l /usr/local/hpe/liborm.so

}

replace_db()
{
  echo ">>> replace db"
#  echo ">>> replace db hpe_ci_0727_wcy_master.tar.gz"
  cd /opt/vrpv8/home/
#  tar -zxvf hpe_ci_0725_master.tar.gz -C /
#  tar -zxvf hpe_ci_0727_wcy_master.tar.gz -C /
  tar -zxvf hpe_ci.tar.gz -C /
  echo "-------commit info-------"
  cat /commit*.txt
  echo "-------------------------"
  echo "-------cc info ----------"
  ls /tftpboot/
  echo "-------------------------"


}


replace_ini()
{

     echo ">>> replace ini."
     rm -f /usr/local/conf/gmserver.ini
     cp /opt/vrpv8/home/<USER>/gmserver.ini  /usr/local/conf/
     chmod 777 /usr/local/conf/gmserver.ini
}

# 启动DB
start_db()
{
    # 0423版本启动方式
    echo ">>> start db."
    # rm -f /usr/local/conf/gmserver.ini
    # cp $AC_BASE_DIR/ci_630/gmserver.ini  /usr/local/conf/
    # chmod 777 /usr/local/conf/gmserver.ini

    cd /usr/local/etc/gmjson
    ls |grep -v dev|grep -v mirr |xargs rm -rf


    rm -rf /opt/vrpv8/var/ydrv_no_start  # CE上不能执行此断点
    systemctl start gmpolicy_extract.service
    systemctl start gmdb_daemon.service
    systemctl start hdmng.service &
    systemctl start hpe.service
    systemctl start dophi-daemon.service &
    systemctl start capacityset.service &
    systemctl start hctl.service &
    systemctl start hpp_hsd.service &
    sleep 15
    systemctl start litedb_generate_gmconfig.service &
    sleep 15
    systemctl start gmserver.service
    systemctl start schema_loader.service &
    sleep 15
    gmsysview -q V\$DB_SERVER
}

# 影响到性能的一些组件和V3对齐
remove_unnecessary_components()
{
    # 删除pssp进程
    echo ">>> kill pssp process"
    ps -ef|grep pssp
    pssp_id=$(ps -ef|grep pssp|grep -v grep|awk '{print $2}')
    echo ">> pssp id: $pssp_id"
    kill -9 ${pssp_id}
    ps -ef|grep pssp
    sleep 2

    # 删除ftp_server进程
    echo ">>> kill ftp_server process"
    ps -ef|grep ftp_server
    ftp_server_id=$(ps -ef|grep ftp_server|grep -v grep|awk '{print $2}')
    echo ">> ftp_server id: $ftp_server_id"
    kill -9 ${ftp_server_id}
    ps -ef|grep ftp_server
    sleep 2

    # 删除swxn_main进程
    echo ">>> kill swxn_main process"
    ps -ef|grep swxn_main
    swxn_main_id=$(ps -ef|grep swxn_main|grep -v grep|awk '{print $2}')
    echo ">> swxn_main id: $swxn_main_id"
    kill -9 ${swxn_main_id}
    ps -ef|grep swxn_main
    sleep 2
}

# source一下环境
source_env()
{
    export PATH=/sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
    export LD_LIBRARY_PATH=/usr/local/lib
}

# 视图方法判断系统是否启动成功
check_is_start_DB_success()
{
    echo ">>> check_db"
    source_env
    gmsysview -q V\$DB_SERVER
}

check_enable()
{
    echo "-------------------"
    gmadmin -cfgName enableClusterHash | grep -E "name|current"
    gmadmin -cfgName isFastReadUncommitted | grep -E "name|current"
    gmadmin -cfgName scheduleMode | grep -E "name|current"
    gmadmin -cfgName directWrite | grep -E "name|current"
    echo "-------------------"
}

view_look_entry()
{
    echo "view STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT entry"
    gmsysview -q V\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME="ip4forward_r21_1" | grep ENTRY_USED
    gmsysview -q V\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME="if_r21_1" | grep ENTRY_USED
}

# 解压缩用例场景
decompress_test_cases()
{
    if [ -d "$AC_TESTCAST_EXEC_DIR" ];then
        source_env
        cd $AC_TESTCAST_EXEC_DIR
        return
    fi
    cd /opt/vrpv8/home
    tar -xvf gmdb_benchmark_aarch64_v5_0717.tar.gz -C /root/
    cd /root/
    mv gmdb_benchmark_aarch64 gmdb_benchmark_arm64
    cd $AC_TESTCAST_EXEC_DIR
    cp $AC_TESTCAST_EXEC_DIR/test_lib/libbenchmark_schema.so /usr/local/lib/
    source_env
    cd $AC_TESTCAST_EXEC_DIR
    chmod 777 gmdb_benchmark_static*
}

import_priv()
{
    sh /opt/vrpv8/home/<USER>/prepare_policy.sh
    gmrule -c import_allowlist -f /usr/local/conf/gmdb/gmdb_perf.gmuser
    gmrule -c import_policy -f /usr/local/etc/gmdb_sys_priv/gmdb_perf.gmpolicy
}

# 启动DB
#cp /opt/vrpv8/home/<USER>/usr/local/service.d/
#cp /opt/vrpv8/home/<USER>/hctl /usr/local/bin/hctl
#systemctl daemon-reload
#sleep 2
check_is_start_DB_success
if [ $? -eq 0 ];then
    echo "DB Start Successful!"
else
    # change_gmdb_so_files
    remove_unnecessary_components

    # replace_ormso
    replace_db
    replace_ini

    start_db

    import_priv

    check_is_start_DB_success
    check_enable
    remove_service
    # decompress_test_cases
fi
