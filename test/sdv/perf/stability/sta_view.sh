#!/bin/bash

s1=`pidof gmserver`
s2=`pidof vm.elf`
if [ "x" = "x${s1}" -a "x" = "x${s2}" ]; then
    echo "server is down!"
    exit 1
fi
if [ "x" != "x${s1}" -a "x" != "x${s2}" ]; then
    echo "both gmserver(pid=${s1}) and hpk(pid=%{s2}) are available."
    exit 1
fi
if [ "x" != "x${s1}" ]; then
    sid=${s1}
    tools="gmsysview"
else
    sid=${s2}
    tools="/usr/local/bin/gmsysview"
    export TERM=vt100
fi

fileName="$0.tmp"
num=0
rm -f ${fileName}
function setIntoArray
{
    echo "$1" >> ${fileName}
    num=`expr ${num} + 1`
}

setIntoArray  CLT_PROCESS_INFO
setIntoArray  CLT_PROCESS_LABEL
setIntoArray  CLT_PROCESS_FLOWCTRL_INFO_LABEL
setIntoArray  CLT_PROCESS_CONN
setIntoArray  CLT_PROCESS_TIME_CONSUMPTION

setIntoArray  DSTORE_MERGE_STAT
setIntoArray  DSTORE_MERGE_SUMMARY_STAT
setIntoArray  DSTORE_MERGE_SPEC_STAT
setIntoArray  DSTORE_CONFIG_STAT
setIntoArray  DSTORE_MEMORY_STAT
setIntoArray  DSTORE_SUMMARY_STAT

setIntoArray  QRY_PLAN_CACHE
setIntoArray  QRY_AGE_TASK
setIntoArray  QRY_DML_INFO
setIntoArray  QRY_DML_OPER_STATIS
setIntoArray  QRY_SESSION
setIntoArray  QRY_TRX_MONITOR_STAT
setIntoArray  QRY_DYNMEM
setIntoArray  QRY_USER_THREAT_STAT
setIntoArray  QRY_SHOW_SCHEMA_DEGRADE

setIntoArray  CATA_GENERAL_INFO
setIntoArray  CATA_VERTEX_LABEL_INFO
setIntoArray  CATA_HPR_TABLE_INFO
setIntoArray  CATA_TBM_TABLE_INFO
setIntoArray  CATA_MSG_NOTIFY_TABLE_INFO
setIntoArray  CATA_EDGE_LABEL_INFO
setIntoArray  CATA_LABEL_SUBS_INFO
setIntoArray  CATA_PATH_SUBS_INFO
setIntoArray  CATA_NAMESPACE_INFO
setIntoArray  CATA_TABLESPACE_INFO
setIntoArray  PRIVILEGE_USER_STAT
setIntoArray  PRIVILEGE_ROLE_STAT
setIntoArray  CATA_KV_TABLE_INFO
setIntoArray  CATA_RESOURCE_INFO
setIntoArray  CATA_VERTEX_LABEL_CHECK_INFO
setIntoArray  CATA_UDF_INFO

setIntoArray  DB_SERVER
setIntoArray  CONFIG_PARAMETERS
setIntoArray  MEM_COMPACT_TASKS_STAT
setIntoArray  DB_SERVER_KEY_RESOURCE
setIntoArray  DRT_CONN_THREAT_STAT
setIntoArray  PTL_DATALOG_QUEUE
setIntoArray  DB_PROBE_DATA
setIntoArray  DB_SERVICE_DETECTION

setIntoArray  STORAGE_HEAP_STAT
setIntoArray  STORAGE_FSM_STAT
setIntoArray  STORAGE_MEMDATA_STAT
setIntoArray  STORAGE_EDGE_LABEL_STAT
setIntoArray  STORAGE_VERTEX_LABEL_STAT
setIntoArray  STORAGE_KV_STAT
setIntoArray  STORAGE_VERTEX_COUNT
setIntoArray  STORAGE_HPR_COUNT
setIntoArray  STORAGE_KV_COUNT
setIntoArray  STORAGE_UNDO_STAT
setIntoArray  STORAGE_LOCK_OVERVIEW
setIntoArray  STORAGE_HASH_INDEX_STAT
setIntoArray  STORAGE_HASH_LINKLIST_INDEX_STAT
setIntoArray  STORAGE_HASH_CLUSTER_INDEX_STAT
setIntoArray  STORAGE_ART_INDEX_STAT
setIntoArray  STORAGE_HASH_COLLISION_STAT
setIntoArray  STORAGE_INDEX_GLOBAL_STAT
setIntoArray  SERVER_MEMORY_OVERHEAD
setIntoArray  STORAGE_TRX_STAT
setIntoArray  STORAGE_RESOURCE_ALL_POOL_STAT
setIntoArray  STORAGE_RESOURCE_SINGLE_POOL_STAT
setIntoArray  STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT
setIntoArray  STORAGE_RESOURCE_START_INDEX_USED_STAT
setIntoArray  STORAGE_RESOURCE_RESID_STAT
setIntoArray  STORAGE_EXTERN_RESOURCE_POOL_STAT
setIntoArray  STORAGE_RESOURCE_BIND_TO_LABEL_STAT
setIntoArray  STORAGE_TRX_DETAIL
setIntoArray  STORAGE_LOCK_CONFLICT_INFO_STAT
setIntoArray  STORAGE_UNDO_PURGER_INFO
setIntoArray  STORAGE_CLUSTERED_HASH_LABEL_STAT
setIntoArray  STORAGE_RES_SESSION_STAT

setIntoArray  DRT_WORKER_STAT
setIntoArray  DRT_CONN_STAT
setIntoArray  DRT_COM_STAT
setIntoArray  DRT_PIPE_STAT
setIntoArray  DRT_LONG_OPERATION_STAT
setIntoArray  DRT_DATA_PLANE_CHANNEL_STAT
setIntoArray  DRT_CONN_SUBS_STAT
setIntoArray  DRT_SCHEDULE_STAT

setIntoArray  COM_SHMEM_CTX
setIntoArray  COM_DYN_CTX
setIntoArray  SYS_MODULE_MEM_INFO
setIntoArray  COM_DTL_UDF_DYN_CTX
setIntoArray  DYN_MEM_TRACE_INFO
setIntoArray  SHMEM_TRACE_INFO
setIntoArray  STORAGE_LATCH_CONFLICT_INFO_STAT

if [ -d view ]; then
    rm -f view/*
else
    mkdir view
fi
idx=1
repeat=1
while [ ${idx} -le ${num} ]
do
    viewName=`sed -n "${idx}p" ${fileName}`
    i=0
    while [ ${i} -lt ${repeat} ]
    do
        #${tools} -q V\$${viewName}   1>/dev/null 2>/dev/null
	${tools} -q V\$${viewName} > view/${viewName}.txt
        i=`expr ${i} + 1`
    done
    #idx=`echo $(($RANDOM % ${num}))`
    idx=`expr ${idx} + 1`
    #repeat=`echo $(($RANDOM % 10))`
    #repeat=`expr ${repeat} + 1`
done
