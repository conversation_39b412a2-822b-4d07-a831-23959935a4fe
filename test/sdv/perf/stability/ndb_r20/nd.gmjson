[{"comment": "ND表项定义, 对应FES 48#表", "version": "2.0", "type": "record", "name": "nd", "config": {"check_validity": false}, "max_record_count": 512000, "fields": [{"name": "ipv6_address", "type": "fixed", "size": 16, "comment": "ND的IPv6目的地址"}, {"name": "if_index", "type": "uint32", "comment": "ND对应的三层接口索引"}, {"name": "vr_id", "type": "uint32", "comment": "ND表出接口的所在的VR"}, {"name": "vrf_index", "type": "uint32", "comment": "ND表出接口的所在的VRF(VNP)"}, {"name": "mac_address", "type": "fixed", "size": 6, "comment": "ND的MAC地址"}, {"name": "fake_flag", "type": "uint8", "comment": "ND真表/假表标记: 0-假表, 1-真表"}, {"name": "link_type", "type": "uint8", "comment": "链路类型"}, {"name": "work_if_index", "type": "uint32", "comment": "物理出接口"}, {"name": "atm_if_index", "type": "uint32", "comment": "预留, 仅ATM口时使用, 即VE口或者VE加入Vlanif"}, {"name": "target_blade", "type": "uint16", "comment": "物理出接口TB"}, {"name": "target_port", "type": "uint16", "comment": "物理出接口TP"}, {"name": "pe_vid", "type": "uint16", "comment": "子接口或vlanif的vlanid, QinQ外层VLAN"}, {"name": "ce_vid", "type": "uint16", "comment": "QinQ内层vlan"}, {"name": "vcd", "type": "uint32", "comment": "预留, ATM的Vcd索引"}, {"name": "fwd_if_type", "type": "uint16", "comment": "转发接口类型, LDM使用"}, {"name": "tunnel_type", "type": "uint8", "comment": "隧道类型, 可以表示vxlan或nvgre"}, {"name": "path_flag", "type": "uint8", "comment": "path完备性标记"}, {"name": "if_phy_type", "type": "uint32", "comment": "ND表出接口的物理接口类型"}, {"name": "main_if_phy_type", "type": "uint32", "comment": "主接口类型"}, {"name": "if_link_type", "type": "uint32", "comment": "ND表物理出接口的链路类型"}, {"name": "if_encap_type", "type": "uint32", "comment": "ND表出接口的封装类型"}, {"name": "flow_id", "type": "uint32", "comment": "流索引, FES预留, 当前没用到"}, {"name": "tunnel_vrf_id", "type": "uint32", "comment": "对应evnindex"}, {"name": "tunnel_id", "type": "uint32", "comment": "Tunnel id"}, {"name": "tunnel_encap_id", "type": "uint32", "comment": "如果为VXLAN隧道, 该字段为vniid; 如果为nvgre, 该字段为vsid"}, {"name": "tunnel_sip", "type": "uint32", "comment": "VxLAN隧道上学习到的BDIF ND, 通知MAC模块时, 要传这个IPv6隧道保存的隧道ID"}, {"name": "tunnel_dip", "type": "uint32", "comment": "VxLAN隧道上学习到的BDIF ND, 通知MAC模块时, 要传这个; 若为二层子接口上学习到的BDIF"}, {"name": "vsi_index", "type": "uint32", "comment": "vsi索引复用, NHP下发vsi_index 指导封装vlan用"}, {"name": "smooth_id", "type": "uint32", "comment": "记载当前组的全局序列号, 该序列号每次ARP更新时递增, 各种ARP更新场合下使用"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识,对应VRP8 hSrcPid字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理, 即使KEY和data相同, 但删除后再添加时, 这个ID也会不同, 具体使用场景不明确, 暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号, 用于跟踪同一条记录的变化情形, 具体使用场景不明确, 暂时保留不用"}, {"name": "adj_service_status", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE下发状态"}, {"name": "adj_service_errcode", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE返回错误码"}, {"name": "adj_svc_context_high_prio", "type": "fixed", "size": 32, "default": "ffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的ARP上下文"}, {"name": "adj_svc_context_normal_prio", "type": "fixed", "size": 32, "default": "ffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的ARP上下文"}, {"name": "nhp_service_status", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE下发状态"}, {"name": "nhp_service_errcode", "type": "fixed", "nullable": true, "size": 2, "comment": "SERVICE返回错误码"}, {"name": "nhp_svc_context_high_prio", "type": "fixed", "size": 16, "default": "ffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的ARP上下文"}, {"name": "nhp_svc_context_normal_prio", "type": "fixed", "size": 16, "default": "ffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的ARP上下文"}, {"name": "vlanid", "type": "uint16", "comment": "VLANif绑定的VLAN"}, {"name": "bridge_vlanid", "type": "uint16", "comment": "ND的桥接vlan, vlanif使用"}, {"name": "is_trunk", "type": "uint16", "comment": "是否为Trunk口, 先预留"}, {"name": "mlag_flag", "type": "uint16", "comment": "mlag标志"}, {"name": "state", "type": "uint8", "comment": "ND表预留状态标记"}, {"name": "sub_if_index", "type": "uint32", "comment": "subif子接口索引"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "nd_key", "index": {"type": "primary"}, "node": "nd", "fields": ["ipv6_address", "if_index"], "constraints": {"unique": true}, "comment": "主索引"}, {"name": "nd_mac_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["mac_address"], "comment": "根据MAC索引ND表, MAC漂移场景使用"}, {"name": "nd_mac_vsi_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["vsi_index", "mac_address"], "comment": "根据MAC+VSI索引ND表, MAC漂移(BDIF)场景使用"}, {"name": "nd_if_index_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["if_index"], "comment": "根据if_index索引ND表"}, {"name": "nd_work_if_index_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["work_if_index"], "comment": "根据work_if_index索引ND表"}, {"name": "nd_tunnel_encap_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["tunnel_type", "tunnel_sip", "tunnel_dip"], "constraints": {"unique": false}, "comment": "跟据隧道封装信息索引nd表"}, {"name": "nd_sub_if_index_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["sub_if_index"], "comment": "根据sub_if_index索引ND表"}, {"name": "nd_fwd_if_type_index", "index": {"type": "hashcluster"}, "node": "nd", "fields": ["fwd_if_type"], "comment": "根据fwd_if_type索引ND表"}]}]