#!/bin/bash

if [ $# -lt 1 -o $# -gt 3 ]; then
    echo "usage: $0 <seq/random> [init/uninit] [cycle]"
    exit 1
fi
if [ "x$1" = "xseq" ]; then
    isRandom=0
elif [ "x$1" = "xrandom" ]; then
    isRandom=1
else
    echo "usage: $0 <seq/random>"
    exit 1
fi
initFlag=1
if [ $# -ge 2 ]; then
    if [ "x$2" = "xinit" ]; then
        initFlag=1
    elif [ "x$2" = "xuninit" ]; then
        initFlag=0
    else
        echo "usage: $0 <seq/random> [init/uninit] [cycle]"
        exit 1
    fi
fi
cycleNum=0
if [ $# -eq 3 ]; then
    cycleNum=$3
fi

dataCount=100000  # hpe:100000  euler:1000000
opsI=200          # hpe:200    euler:2000
opsU=200          # hpe:200    euler:2000
opsD=200          # hpe:200    euler:2000

if [ ${initFlag} -eq 1 ]; then
    ./teststa -d -C -s ds
    ./teststa -v -C -n ip4forward_perf -l "10000000|ds" -f pnf/ip4forward_perf.gmjson -r "2|3"
fi

fileName="$0.tmp"
num=0
rm -f ${fileName}
function setIntoArray
{
    echo "$1" >> ${fileName}
    num=`expr ${num} + 1`
}
setIntoArray  "./teststa -v -m insert  -c 2 -n ip4forward_perf -s nhp_group_id=0-6000 -e ${dataCount} -o ${opsI}"
setIntoArray  "./teststa -v -m replace -c 1 -n ip4forward_perf -s nhp_group_id=0-3000 -e ${dataCount} -o ${opsI}"
setIntoArray  "./teststa -v -m select  -c 3 -n ip4forward_perf -e ${dataCount}"
setIntoArray  "./teststa -v -m update  -c 1 -n ip4forward_perf -e ${dataCount} -o ${opsU} -t '1005|1'"
setIntoArray  "./teststa -v -m delete  -c 4 -n ip4forward_perf -e ${dataCount} -o ${opsD}"
setIntoArray  "./teststa -v -m insert  -c 2 -n ip4forward_perf00001 -s nhp_group_id=0-6000 -e ${dataCount} -o ${opsI}"
setIntoArray  "./teststa -v -m replace -c 1 -n ip4forward_perf00001 -s nhp_group_id=0-3000 -e ${dataCount} -o ${opsI} -t '1002|1'"
setIntoArray  "./teststa -v -m select  -c 3 -n ip4forward_perf00001 -e ${dataCount}"
setIntoArray  "./teststa -v -m update  -c 1 -n ip4forward_perf00001 -e ${dataCount} -o ${opsU} -t '1004|1'"
setIntoArray  "./teststa -v -m delete  -c 4 -n ip4forward_perf00001 -e ${dataCount} -o ${opsD}"

times=0
idx=1
while [ ${cycleNum} -eq 0 -o ${times} -lt ${cycleNum} ]
do
    cmd=`sed -n "${idx}p" ${fileName}`
    ${cmd}
    if [ ${isRandom} -eq 0 ]; then
        idx=`expr ${idx} + 1`
        if [ ${idx} -gt ${num} ]; then
            idx=1
        fi
    else
        idx=`echo $(($RANDOM % ${num}))`
	idx=`expr ${idx} + 1`
    fi
    if [ ${cycleNum} -gt 0 ]; then
        times=`expr ${times} + 1`
    fi
done

