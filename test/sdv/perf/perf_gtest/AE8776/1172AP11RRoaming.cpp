/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: 1172AP11RRoaming.cpp
 * Description: AP 1172 11r_roaming scene testcase
 * Author:
 * Create: 2025-04-02
 */
#define DB_INVALID_UINT8 0xFF
#define DB_INVALID_UINT16 0xFFFF

#include "gmc.h"
#include "gmc_datalog.h"

#include <sys/time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/epoll.h>
#include "securec.h"

#define DEMO_ERROR(ret, ...)        \
    do {                            \
        printf("[ERROR %d] ", ret); \
        printf(__VA_ARGS__);        \
        putchar('\n');              \
    } while (0)

// 输入表
const char *LabelPublicAuth = "Usf.PublicAuth";
const char *LabelVlan = "BR.Vlan";
const char *LabelVlanMacAttr = "BR.VlanMacAttr";
const char *LabelVlanBitmap = "BR.VlanBitmap";
const char *LabelPortVlanBitmap = "BR.PortVlanBitmap";
const char *LabelVlanTagPort = "BR.VlanTagPortInput";
const char *LabelPort = "BR.Port";
const char *LabelVlanUntagPort = "BR.VlanUntagPortInput";
const char *LabelPublicClearIngPktCache = "Usf.PublicClearIngPktCache";
const char *LabelPublicSta = "Usf.PublicSta";
const char *LabelCfgMacIpv6 = "Wmp.ConfigMacIpv6";
const char *LabelCfgMacIp = "Wmp.ConfigMacIp";
const char *LabelDhcpBind = "Dhcpsnp.DynamicUserBind";
const char *LabelPublicRoam = "Usf.PublicROAM";
const char *LabelSacProfileUsf = "Sac.SacProfileUsf";

/*
 * * ################################## begin:输入表结构体 ################################
 */
#define USF_PUBLICAUTH_STAMAC_LEN 6
#define BR_VLAN_NAME_LEN 32
#define BR_VLAN_DESC_LEN 81
#define BR_MACOPER_MAC_LEN 6
#define USF_PUBLICCLEARINGPKTCACHE_STAMAC_LEN 6
#define BR_VLANTAGPORTINPUT_VLAN_TAG_BITMAP_LEN 512
#define BR_VLANMACBITMAP_VLAN_BITMAP_LEN 512
#define BR_VLANUNTAGPORTINPUT_VLAN_UNTAG_BITMAP_LEN 512
#define USF_PUBLICSTA_STAMAC_LEN 6
#define WMP_CONFIGMACIP_MAC_LEN 6
#define WMP_CONFIGMACIPV6_MAC_LEN 6
#define WMP_CONFIGMACIPV6_IPV6ADDR_LEN 16
#define DHCPSNP_DYNAMICUSERBIND_MACADDR_LEN 6
#define DHCPSNP_DYNAMICUSERBIND_IPV6ADDR_LEN 16
#define USF_PUBLICROAM_MAC_LEN 6
#define USF_PUBLICROAM_IPADDR_LEN 16
#define USF_PUBLICROAM_HAPMAC_LEN 6
#define SAC_SACPROFILEUSF_MAC_LEN 6
#define SAC_SACPROFILEUSF_SACPROFILEDATA_LEN 388

#pragma pack(1)
// %table PublicAuth(
//         staMac:byte6, authStatus:int1, authVlanEn:int1, authVlan:int2, hasPolicy:int1,
//         userVipFlag:int1, ingStatEn:int1, egrStatEn:int1, statId:int4, userGroupId:int2,
//         userGroupEn:int1, monitorMark:int1, userPreRoamIpv4Addr:int4, innerIsolateEn:int1, interIsolateEn:int1,
//         splitEn:int1, splitTnlAclNum:int4, gatewayIsoEn:int1, currAuthStage:int1)
typedef struct UsfPublicAuth {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t staMac[USF_PUBLICAUTH_STAMAC_LEN];
    int8_t authStatus;
    int8_t authVlanEn;
    int16_t authVlan;
    int8_t hasPolicy;
    int8_t userVipFlag;
    int8_t ingStatEn;
    int8_t egrStatEn;
    int32_t statId;
    int16_t userGroupId;
    int8_t userGroupEn;
    int8_t monitorMark;
    int32_t userPreRoamIpv4Addr;
    int8_t innerIsolateEn;
    int8_t interIsolateEn;
    int8_t splitEn;
    int32_t splitTnlAclNum;
    int8_t gatewayIsoEn;
    int8_t currAuthStage;
} UsfPublicAuthT;

// %table Vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81, br_vlan_if: int4,
//              is_ctrl: int1, inst_id: int2)
//              {index(0(ns_id, br_id, vlan_id)),index(1(vlan_id)), index(2(br_id, vlan_id, br_vlan_if)),
//              update_partial, status_merge_sub(true)}
typedef struct Vlan {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t nsId;
    int32_t brId;
    int16_t vlanId;
    int8_t vlanType;
    int8_t name[BR_VLAN_NAME_LEN];
    int8_t desc[BR_VLAN_DESC_LEN];
    int32_t brVlanIf;
    int8_t isCtrl;
    int16_t instId;
} BRVlanT;

// %table VlanMacAttr(ns_id: int4, br_id: int4, vlan_id: int2, learn: int1, limit: int4, limit_act: int1,
//                      limit_alm:int1)
//            {index(0(ns_id, br_id, vlan_id)), index(1(br_id, vlan_id)), update_partial, status_merge_sub(true) }
typedef struct BRVlanMacAttr {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t nsId;
    int32_t brId;
    int16_t vlanId;
    int8_t learn;
    int32_t limit;
    int8_t limitAct;
    int8_t limitAlm;
} BRVlanMacAttrT;

// %table VlanBitmap(brId: int4, vlanBitmap: byte512) {index(0(brId)), update_partial, status_merge_sub(true) }

typedef struct BRVlanBitmap {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t brId;
    int8_t vlanBitmap[BR_VLANMACBITMAP_VLAN_BITMAP_LEN];
} BRVlanBitmapT;

// %table PortVlanBitmap(brId: int4, ifIndex: int4, vlanMemberBitmap: byte512, vlanTagBitmap: byte512, vlanUntagBitmap: byte512) {index(0(ifIndex)), update_partial, status_merge_sub(true) }

typedef struct BRPortVlanBitmap {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t brId;
    int32_t ifIndex;
    int8_t vlanMemberBitmap[BR_VLANMACBITMAP_VLAN_BITMAP_LEN];
    int8_t vlanTagBitmap[BR_VLANTAGPORTINPUT_VLAN_TAG_BITMAP_LEN];
    int8_t vlanUntagBitmap[BR_VLANMACBITMAP_VLAN_BITMAP_LEN];
} BRPortVlanBitmapT;

// %table Port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, pvid: int2, link_type: int1,
//              stp_enable: int1)
//              {index(0(if_index)), index(1(port_index)), update_partial, status_merge_sub(true)}
typedef struct BRPort {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t ifIndex;
    int32_t portIndex;
    int32_t nsId;
    int32_t brId;
    int16_t pvid;
    int8_t linkType;
    int8_t stpEnable;
} BRPortT;

// %table VlanTagPortInput(ns_id: int4, br_id: int4, vlan_tag_bitmap: byte512, if_index: int4)
//                  {index(0(if_index)), update_partial}
typedef struct BRVlanTagPortInput {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t nsId;
    int32_t brId;
    int8_t vlanTagBitmap[BR_VLANTAGPORTINPUT_VLAN_TAG_BITMAP_LEN];
    int32_t ifIndex;
} BRVlanTagPortInputT;

// %table VlanUntagPortInput(ns_id: int4, br_id: int4, vlan_untag_bitmap: byte512, if_index: int4)
//                  {index(0(if_index)), update_partial}
typedef struct BRVlanUntagPortInput {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t nsId;
    int32_t brId;
    int8_t vlanUntagBitmap[BR_VLANUNTAGPORTINPUT_VLAN_UNTAG_BITMAP_LEN];
    int32_t ifIndex;
} BRVlanUntagPortInputT;

// %table PublicClearIngPktCache(staMac:byte6, clearPktType:int1, staServiceVlan:int2, authId:int4)
//                  { index(0(staMac)), update_partial }
typedef struct UsfPublicClearIngPktCache {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t staMac[USF_PUBLICCLEARINGPKTCACHE_STAMAC_LEN];
    int8_t clearPktType;
    int16_t staServiceVlan;
    int32_t authId;
} UsfPublicClearIngPktCacheT;

// %table PublicSta(
//         staMac:byte6, vapIfIndex:int4, globalIdx:int2, staFlag: int1, peerId:int4,
//         pfeVapIdx:int4, sessionInfo:int2, radioId:int1, authId:int4, mainVapIfIndex:int4,
//         startParse:int1, upload:int1, expectAuthStage:int1)
typedef struct UsfPublicSta {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t staMac[USF_PUBLICSTA_STAMAC_LEN];
    int32_t vapIfIndex;
    int16_t globalIdx;
    int8_t staFlag;
    int32_t peerId;
    int32_t pfeVapIdx;
    int16_t sessionInfo;
    int8_t radioId;
    int32_t authId;
    int32_t mainVapIfIndex;
    int8_t startParse;
    int8_t upload;
    int8_t expectAuthStage;
} UsfPublicStaT;


/* All fields struct define as following */
/* Struct definition of Wmp_ConfigMacIp */
typedef struct WmpConfigMacIp {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t mac[WMP_CONFIGMACIP_MAC_LEN];
    int32_t ipAddr;
    int8_t learnType;
} WmpConfigMacIpT;

/* All fields struct define as following */
/* Struct definition of Wmp_ConfigMacIpv6 */
typedef struct WmpConfigMacIpv6 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t mac[WMP_CONFIGMACIPV6_MAC_LEN];
    int8_t ipv6Addr[WMP_CONFIGMACIPV6_IPV6ADDR_LEN];
    int8_t learnType;
} WmpConfigMacIpv6T;

/* All fields struct define as following */
/* Struct definition of Dhcpsnp_DynamicUserBind */
typedef struct DhcpsnpDynamicUserBind {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t macAddr[DHCPSNP_DYNAMICUSERBIND_MACADDR_LEN];
    int32_t ipv4Addr;
    int16_t vlanId;
    int32_t ifIndex;
    int32_t vrfIndex;
    int32_t serverIp;
    int32_t lifeTime;
    int8_t ipv6Addr[DHCPSNP_DYNAMICUSERBIND_IPV6ADDR_LEN];
    int8_t isPrefix;
    int8_t prefixLen;
    int8_t protoType;
    int32_t clientRequestTime;
} DhcpsnpDynamicUserBindT;

/* All fields struct define as following */
/* Struct definition of Usf_PublicROAM */
typedef struct UsfPublicROAM {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t mac[USF_PUBLICROAM_MAC_LEN];
    int8_t roamRole;
    int8_t interIsolate;
    int8_t innerIsolate;
    int8_t tunnelDstRole;
    int16_t usrGroupId;
    int32_t homeVapFwdif;
    int32_t tunnelId;
    int8_t userIsolate;
    int16_t vlanId;
    int8_t ipAddr[USF_PUBLICROAM_IPADDR_LEN];
    int8_t hapMac[USF_PUBLICROAM_HAPMAC_LEN];
    int8_t ipType;
} UsfPublicROAMT;

/* All fields struct define as following */
/* Struct definition of Sac_SacProfileUsf */
typedef struct SacSacProfileUsf {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t mac[SAC_SACPROFILEUSF_MAC_LEN];
    int32_t ifIndex;
    int8_t sacProfileData[SAC_SACPROFILEUSF_SACPROFILEDATA_LEN];
} SacSacProfileUsfT;
#pragma pack()
/*
 * * ################################## end:输入表结构体 ################################
 */

/*
 * * ################################## begin:建连组件 ################################
 */

typedef struct UserData {
    uint32_t received;
    uint32_t affectRows;
    int32_t status;
} UserDataT;

void BatchCb(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    UserDataT *data = (UserDataT *)(userData);
    data->received++;
    data->status = status;
}

void DMLCb(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    UserDataT *data = (UserDataT *)(userData);
    data->received++;
    data->status = status;
}

void *EpollThreadFunc(void *args)
{
    volatile int *epollFd = (volatile int *)args;

    do {
        struct epoll_event events[1024];
        constexpr int epollTimeoutMs = 1000;
        int32_t fdCount = epoll_wait(*epollFd, events, 1024, epollTimeoutMs);
        while (fdCount > 0) {
            --fdCount;
            GmcHandleEvent(events[fdCount].data.fd);
        }
    } while (*epollFd >= 0);

    return NULL;
}

void CreateAndStartEpoll(pthread_t *threadId, int *epollFd)
{
    int fd = *epollFd = epoll_create(1024);
    pthread_create(threadId, NULL, EpollThreadFunc, epollFd);
}

void StopAndDestroyEpoll(pthread_t threadId, int *epollFd)
{
    int fd = *epollFd;
    *epollFd = -1;  // tell EpollThreadFunc to exit
    pthread_join(threadId, NULL);
    close(fd);
}

int EpollRegWithUserData(int fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    int ret;
    struct epoll_event event;
    event.data.fd = fd;
    event.events = events;
    int *epollFd = (int *)(userData);
    switch (type) {
        case GMC_EPOLL_ADD: {
            ret = epoll_ctl(*epollFd, EPOLL_CTL_ADD, fd, &event);
            return ret;
        }
        case GMC_EPOLL_MOD: {
            ret = epoll_ctl(*epollFd, EPOLL_CTL_MOD, fd, &event);
            return ret;
        }
        case GMC_EPOLL_DEL:
            ret = epoll_ctl(*epollFd, EPOLL_CTL_DEL, fd, NULL);
            return ret;
        default:
            return -1;
    }
}

int32_t ConnectWrapper(
    GmcConnTypeE type, const char *serverLactor, const char *username, const char *connName, GmcConnT **conn, int fd)
{
    GmcConnOptionsT *connOptions = NULL;
    int32_t ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    ret = GmcConnOptionsSetServerLocator(connOptions, serverLactor);
    if (ret != GMERR_OK) {
        goto CLEAR;
    }

    if (type == GMC_CONN_TYPE_ASYNC) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptions, EpollRegWithUserData, (void *)&fd);
        if (ret != GMERR_OK) {
            goto CLEAR;
        }
    }

    if (type == GMC_CONN_TYPE_SUB && connName != NULL) {
        ret = GmcConnOptionsSetConnName(connOptions, connName);
        if (ret != GMERR_OK) {
            goto CLEAR;
        }
    }
    ret = GmcConnect(type, connOptions, conn);
CLEAR:
    GmcConnOptionsDestroy(connOptions);
    return ret;
}

int32_t CreateConnectionAndStmt(GmcConnTypeE type, GmcConnT **conn, GmcStmtT **stmt, int fd)
{
    int32_t ret;
    GmcConnT *connPtr = NULL;
    GmcStmtT *stmtPtr = NULL;

#if defined(HPE) || defined(RTOSV2X)
    char *serverLocator = (char *)"channel:";
    char *userName = (char *)"gmdbv5";
    char *passwd = (char *)"passwd";
#else
    char *serverLocator = (char *)"usocket:/run/verona/unix_emserver";
    char *userName = (char *)"XXuser";
#endif
    ret = ConnectWrapper(type, serverLocator, userName, NULL, &connPtr, fd);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcAllocStmt(connPtr, &stmtPtr);
    if (ret != GMERR_OK) {
        (void)GmcDisconnect(connPtr);
        return ret;
    }
    *conn = connPtr;
    *stmt = stmtPtr;
    return ret;
}

void ClientUninit()
{
    (void)GmcUnInit();
}

void DestroyConnAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    if (stmt != NULL) {
        GmcFreeStmt(stmt);
    }

    if (conn != NULL) {
        (void)GmcDisconnect(conn);
    }
}

/*
 * * ################################## end:建连组件 ################################
 */

/*
 * * ################################## begin:输入表插入接口 ################################
 */

typedef int32_t (*InputTableSetFieldsImp)(GmcStmtT *stmt, const char *labelName, void *obj);

int32_t SetUsfPublicAuthOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    UsfPublicAuthT *inp = (UsfPublicAuthT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "staMac", GMC_DATATYPE_FIXED, inp->staMac, sizeof(inp->staMac));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "authStatus", GMC_DATATYPE_INT8, &inp->authStatus, sizeof(inp->authStatus));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "authVlanEn", GMC_DATATYPE_INT8, &inp->authVlanEn, sizeof(inp->authVlanEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "authVlan", GMC_DATATYPE_INT16, &inp->authVlan, sizeof(inp->authVlan));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "hasPolicy", GMC_DATATYPE_INT8, &inp->hasPolicy, sizeof(inp->hasPolicy));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "userVipFlag", GMC_DATATYPE_INT8, &inp->userVipFlag, sizeof(inp->userVipFlag));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ingStatEn", GMC_DATATYPE_INT8, &inp->ingStatEn, sizeof(inp->ingStatEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "egrStatEn", GMC_DATATYPE_INT8, &inp->egrStatEn, sizeof(inp->egrStatEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "statId", GMC_DATATYPE_INT32, &inp->statId, sizeof(inp->statId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "userGroupId", GMC_DATATYPE_INT16, &inp->userGroupId, sizeof(inp->userGroupId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "userGroupEn", GMC_DATATYPE_INT8, &inp->userGroupEn, sizeof(inp->userGroupEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "monitorMark", GMC_DATATYPE_INT8, &inp->monitorMark, sizeof(inp->monitorMark));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "userPreRoamIpv4Addr", GMC_DATATYPE_INT32, &inp->userPreRoamIpv4Addr, sizeof(inp->userPreRoamIpv4Addr));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "innerIsolateEn", GMC_DATATYPE_INT8, &inp->innerIsolateEn, sizeof(inp->innerIsolateEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "interIsolateEn", GMC_DATATYPE_INT8, &inp->interIsolateEn, sizeof(inp->interIsolateEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "splitEn", GMC_DATATYPE_INT8, &inp->splitEn, sizeof(inp->splitEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "splitTnlAclNum", GMC_DATATYPE_INT32, &inp->splitTnlAclNum, sizeof(inp->splitTnlAclNum));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "gatewayIsoEn", GMC_DATATYPE_INT8, &inp->gatewayIsoEn, sizeof(inp->gatewayIsoEn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "currAuthStage", GMC_DATATYPE_INT8, &inp->currAuthStage, sizeof(inp->currAuthStage));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int32_t SetBRVlanOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRVlanT *inp = (BRVlanT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &inp->nsId, sizeof(inp->nsId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlan_id", GMC_DATATYPE_INT16, &inp->vlanId, sizeof(inp->vlanId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlan_type", GMC_DATATYPE_INT8, &inp->vlanType, sizeof(inp->vlanType));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "name", GMC_DATATYPE_FIXED, inp->name, sizeof(inp->name));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "desc", GMC_DATATYPE_FIXED, inp->desc, sizeof(inp->desc));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "br_vlan_if", GMC_DATATYPE_INT32, &inp->brVlanIf, sizeof(inp->brVlanIf));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "is_ctrl", GMC_DATATYPE_INT8, &inp->isCtrl, sizeof(inp->isCtrl));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "inst_id", GMC_DATATYPE_INT16, &inp->instId, sizeof(inp->instId));
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

int32_t SetBRVlanMacAttrOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRVlanMacAttrT *inp = (BRVlanMacAttrT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &inp->nsId, sizeof(inp->nsId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlan_id", GMC_DATATYPE_INT16, &inp->vlanId, sizeof(inp->vlanId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "learn", GMC_DATATYPE_INT8, &inp->learn, sizeof(inp->learn));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "limit", GMC_DATATYPE_INT32, &inp->limit, sizeof(inp->limit));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "limit_act", GMC_DATATYPE_INT8, &inp->limitAct, sizeof(inp->limitAct));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "limit_alm", GMC_DATATYPE_INT8, &inp->limitAlm, sizeof(inp->limitAlm));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}


int32_t SetBRVlanBitmapOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRVlanBitmapT *inp = (BRVlanBitmapT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "brId", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanBitmap", GMC_DATATYPE_FIXED, inp->vlanBitmap, sizeof(inp->vlanBitmap));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}


int32_t SetBRPortVlanBitmapOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRPortVlanBitmapT *inp = (BRPortVlanBitmapT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "brId", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &inp->ifIndex, sizeof(inp->ifIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanMemberBitmap", GMC_DATATYPE_FIXED, inp->vlanMemberBitmap, sizeof(inp->vlanMemberBitmap));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanTagBitmap", GMC_DATATYPE_FIXED, inp->vlanTagBitmap, sizeof(inp->vlanTagBitmap));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanUntagBitmap", GMC_DATATYPE_FIXED, inp->vlanUntagBitmap, sizeof(inp->vlanUntagBitmap));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}


int32_t SetBRPortOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRPortT *inp = (BRPortT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &inp->ifIndex, sizeof(inp->ifIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "port_index", GMC_DATATYPE_INT32, &inp->portIndex, sizeof(inp->portIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &inp->nsId, sizeof(inp->nsId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "pvid", GMC_DATATYPE_INT16, &inp->pvid, sizeof(inp->pvid));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "link_type", GMC_DATATYPE_INT8, &inp->linkType, sizeof(inp->linkType));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "stp_enable", GMC_DATATYPE_INT8, &inp->stpEnable, sizeof(inp->stpEnable));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int32_t SetBRVlanTagPortInput(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRVlanTagPortInputT *inp = (BRVlanTagPortInput *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &inp->nsId, sizeof(inp->nsId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "vlan_tag_bitmap", GMC_DATATYPE_FIXED, inp->vlanTagBitmap, sizeof(inp->vlanTagBitmap));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &inp->ifIndex, sizeof(inp->ifIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int32_t SetBRVlanUntagPortInput(GmcStmtT *stmt, const char *labelName, void *obj)
{
    BRVlanUntagPortInputT *inp = (BRVlanUntagPortInputT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ns_id", GMC_DATATYPE_INT32, &inp->nsId, sizeof(inp->nsId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "br_id", GMC_DATATYPE_INT32, &inp->brId, sizeof(inp->brId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "vlan_untag_bitmap", GMC_DATATYPE_FIXED, inp->vlanUntagBitmap, sizeof(inp->vlanUntagBitmap));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "if_index", GMC_DATATYPE_INT32, &inp->ifIndex, sizeof(inp->ifIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int32_t SetUsfPublicClearIngPktCacheOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    UsfPublicClearIngPktCacheT *inp = (UsfPublicClearIngPktCacheT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "staMac", GMC_DATATYPE_FIXED, inp->staMac, sizeof(inp->staMac));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "clearPktType", GMC_DATATYPE_INT8, &inp->clearPktType, sizeof(inp->clearPktType));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "staServiceVlan", GMC_DATATYPE_INT16, &inp->staServiceVlan, sizeof(inp->staServiceVlan));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "authId", GMC_DATATYPE_INT32, &inp->authId, sizeof(inp->authId));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int32_t SetUsfPublicStaOne(GmcStmtT *stmt, const char *labelName, void *obj)
{
    UsfPublicStaT *inp = (UsfPublicStaT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "staMac", GMC_DATATYPE_FIXED, inp->staMac, sizeof(inp->staMac));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vapIfIndex", GMC_DATATYPE_INT32, &inp->vapIfIndex, sizeof(inp->vapIfIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "globalIdx", GMC_DATATYPE_INT16, &inp->globalIdx, sizeof(inp->globalIdx));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "staFlag", GMC_DATATYPE_INT8, &inp->staFlag, sizeof(inp->staFlag));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "peerId", GMC_DATATYPE_INT32, &inp->peerId, sizeof(inp->peerId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "pfeVapIdx", GMC_DATATYPE_INT32, &inp->pfeVapIdx, sizeof(inp->pfeVapIdx));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "sessionInfo", GMC_DATATYPE_INT16, &inp->sessionInfo, sizeof(inp->sessionInfo));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "radioId", GMC_DATATYPE_INT8, &inp->radioId, sizeof(inp->radioId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "authId", GMC_DATATYPE_INT32, &inp->authId, sizeof(inp->authId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "mainVapIfIndex", GMC_DATATYPE_INT32, &inp->mainVapIfIndex, sizeof(inp->mainVapIfIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "startParse", GMC_DATATYPE_INT8, &inp->startParse, sizeof(inp->startParse));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "upload", GMC_DATATYPE_INT8, &inp->upload, sizeof(inp->upload));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "expectAuthStage", GMC_DATATYPE_INT8, &inp->expectAuthStage, sizeof(inp->expectAuthStage));
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int32_t SetCfgMacIpv6(GmcStmtT *stmt, const char *labelName, void *obj)
{
    WmpConfigMacIpv6T *inp = (WmpConfigMacIpv6T *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, inp->mac, WMP_CONFIGMACIPV6_MAC_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ipv6Addr", GMC_DATATYPE_FIXED, inp->ipv6Addr, WMP_CONFIGMACIPV6_IPV6ADDR_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "learnType", GMC_DATATYPE_INT8, &inp->learnType, sizeof(inp->learnType));
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

int32_t SetCfgMacIp(GmcStmtT *stmt, const char *labelName, void *obj)
{
    WmpConfigMacIpT *inp = (WmpConfigMacIpT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, inp->mac, WMP_CONFIGMACIPV6_MAC_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ipAddr", GMC_DATATYPE_INT32, &inp->ipAddr, sizeof(inp->ipAddr));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "learnType", GMC_DATATYPE_INT8, &inp->learnType, sizeof(inp->learnType));
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

int32_t SetDynamicUserBind(GmcStmtT *stmt, const char *labelName, void *obj)
{
    DhcpsnpDynamicUserBindT *inp = (DhcpsnpDynamicUserBindT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "macAddr", GMC_DATATYPE_FIXED, inp->macAddr, DHCPSNP_DYNAMICUSERBIND_MACADDR_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ipv4Addr", GMC_DATATYPE_INT32, &inp->ipv4Addr, sizeof(inp->ipv4Addr));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanId", GMC_DATATYPE_INT16, &inp->vlanId, sizeof(inp->vlanId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &inp->ifIndex, sizeof(inp->ifIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_INT32, &inp->vrfIndex, sizeof(inp->vrfIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "serverIp", GMC_DATATYPE_INT32, &inp->serverIp, sizeof(inp->serverIp));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "lifeTime", GMC_DATATYPE_INT32, &inp->lifeTime, sizeof(inp->lifeTime));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "ipv6Addr", GMC_DATATYPE_FIXED, inp->ipv6Addr, DHCPSNP_DYNAMICUSERBIND_IPV6ADDR_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "isPrefix", GMC_DATATYPE_INT8, &inp->isPrefix, sizeof(inp->isPrefix));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "prefixLen", GMC_DATATYPE_INT8, &inp->prefixLen, sizeof(inp->prefixLen));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "protoType", GMC_DATATYPE_INT8, &inp->protoType, sizeof(inp->protoType));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "clientRequestTime", GMC_DATATYPE_INT32, &inp->clientRequestTime, sizeof(inp->clientRequestTime));
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

int32_t SetPublicROAM(GmcStmtT *stmt, const char *labelName, void *obj)
{
    UsfPublicROAMT *inp = (UsfPublicROAMT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, inp->mac, USF_PUBLICROAM_MAC_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "roamRole", GMC_DATATYPE_INT8, &inp->roamRole, sizeof(inp->roamRole));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "interIsolate", GMC_DATATYPE_INT8, &inp->interIsolate, sizeof(inp->interIsolate));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "innerIsolate", GMC_DATATYPE_INT8, &inp->innerIsolate, sizeof(inp->innerIsolate));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "tunnelDstRole", GMC_DATATYPE_INT8, &inp->tunnelDstRole, sizeof(inp->tunnelDstRole));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "usrGroupId", GMC_DATATYPE_INT16, &inp->usrGroupId, sizeof(inp->usrGroupId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "homeVapFwdif", GMC_DATATYPE_INT32, &inp->homeVapFwdif, sizeof(inp->homeVapFwdif));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "tunnelId", GMC_DATATYPE_INT32, &inp->tunnelId, sizeof(inp->tunnelId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "userIsolate", GMC_DATATYPE_INT8, &inp->userIsolate, sizeof(inp->userIsolate));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "vlanId", GMC_DATATYPE_INT16, &inp->vlanId, sizeof(inp->vlanId));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ipAddr", GMC_DATATYPE_FIXED, inp->ipAddr, USF_PUBLICROAM_IPADDR_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "hapMac", GMC_DATATYPE_FIXED, inp->hapMac, USF_PUBLICROAM_HAPMAC_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ipType", GMC_DATATYPE_INT8, &inp->ipType, sizeof(inp->ipType));
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

int32_t SetSacProfileUsf(GmcStmtT *stmt, const char *labelName, void *obj)
{
    SacSacProfileUsfT *inp = (SacSacProfileUsfT *)obj;
    int32_t ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &inp->dtlReservedCount, sizeof(inp->dtlReservedCount));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "mac", GMC_DATATYPE_FIXED, inp->mac, SAC_SACPROFILEUSF_MAC_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &inp->ifIndex, sizeof(inp->ifIndex));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "sacProfileData", GMC_DATATYPE_FIXED, inp->sacProfileData, SAC_SACPROFILEUSF_SACPROFILEDATA_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

typedef enum SetLabelStatus {
    SET_USF_PUBLICAUTH,
    SET_BR_VLAN,
    SET_BR_VLAN_MACATTR,
    SET_BR_VLAN_BITMAP,
    SET_BR_PORT_VLAN_BITMAP,
    SET_BR_PORT,
    SET_USF_PUBLICSTA,
    SET_BOTTOM
} SetLabelStatusE;

const InputTableSetFieldsImp g_setFunc[] = {
    [SET_USF_PUBLICAUTH] = SetUsfPublicAuthOne,
    [SET_BR_VLAN] = SetBRVlanOne,
    [SET_BR_VLAN_MACATTR] = SetBRVlanMacAttrOne,
    [SET_BR_VLAN_BITMAP] = SetBRVlanBitmapOne,
    [SET_BR_PORT_VLAN_BITMAP] = SetBRPortVlanBitmapOne,
    [SET_BR_PORT] = SetBRPortOne,
    [SET_USF_PUBLICSTA] = SetUsfPublicStaOne,
};

const char *g_inputLabelName[] = {
    [SET_USF_PUBLICAUTH] = LabelPublicAuth,
    [SET_BR_VLAN] = LabelVlan,
    [SET_BR_VLAN_MACATTR] = LabelVlanMacAttr,
    [SET_BR_VLAN_BITMAP] = LabelVlanBitmap,
    [SET_BR_PORT_VLAN_BITMAP] = LabelPortVlanBitmap,
    [SET_BR_PORT] = LabelPort,
    [SET_USF_PUBLICSTA] = LabelPublicSta,
};

int32_t InputTableSingleSync(GmcStmtT *stmt, SetLabelStatusE insertLabels, void *obj)
{
    int32_t ret = GmcPrepareStmtByLabelName(stmt, g_inputLabelName[insertLabels], GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcPrepareStmtByLabelName: %s", g_inputLabelName[insertLabels]);
        return ret;
    }

    ret = g_setFunc[insertLabels](stmt, g_inputLabelName[insertLabels], obj);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "set fields: %s", g_inputLabelName[insertLabels]);
        return ret;
    }

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcExecute: %s", g_inputLabelName[insertLabels]);
        return ret;
    }

    return ret;
}

int32_t InputTableSingle(GmcStmtT *stmt, SetLabelStatusE insertLabels, void *obj, GmcAsyncRequestDoneContextT *context)
{
    int32_t ret = GmcPrepareStmtByLabelName(stmt, g_inputLabelName[insertLabels], GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcPrepareStmtByLabelName: %s", g_inputLabelName[insertLabels]);
        return ret;
    }

    ret = g_setFunc[insertLabels](stmt, g_inputLabelName[insertLabels], obj);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "set fields: %s", g_inputLabelName[insertLabels]);
        return ret;
    }

    ret = GmcExecuteAsync(stmt, context);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcExecute: %s", g_inputLabelName[insertLabels]);
        return ret;
    }

    return ret;
}

int32_t InputTableBatch(
    GmcConnT *conn, GmcStmtT *stmt, SetLabelStatusE insertLabels[], void *obj[], uint32_t objNum, UserDataT *userData)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    int32_t ret = GmcBatchOptionInit(&batchOption);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);

    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcBatchPrepare");
        return ret;
    }

    ret = GmcBatchBindStmt(batch, stmt);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcBatchBindStmt");
        return ret;
    }

    for (uint32_t i = 0; i < objNum; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, g_inputLabelName[insertLabels[i]], GMC_OPERATION_INSERT);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "GmcPrepareStmtByLabelName: %s", g_inputLabelName[insertLabels[i]]);
            return ret;
        }

        ret = g_setFunc[insertLabels[i]](stmt, g_inputLabelName[insertLabels[i]], obj[i]);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "set fields: %s", g_inputLabelName[insertLabels[i]]);
            return ret;
        }

        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "GmcBatchAddDML: %s", g_inputLabelName[insertLabels[i]]);
            return ret;
        }
    }

    ret = GmcBatchExecuteAsync(batch, BatchCb, userData);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "GmcBatchExecuteAsync");
        return ret;
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

void InitInpObj(UsfPublicStaT *publicSta, UsfPublicStaT *publicSta1, UsfPublicAuthT *pubAuth, BRVlanT *vlan, BRVlanMacAttrT *vlanMacAttr, BRVlanBitmapT *vlanBitmap, BRPortVlanBitmapT *portVlanBitmap,
    BRPortT *port, int count)
{
    // UsfPublicStaT publicSta 1,0,0x50f9587b39af,1627389971,128,10,0,0,0,1,-1976523922,1627389971,1,0,2;
    publicSta->dtlReservedCount = count;
    publicSta->staMac[0] = 0x50;
    publicSta->staMac[1] = 0xf9;
    publicSta->staMac[2] = 0x58;
    publicSta->staMac[3] = 0x7b;
    publicSta->staMac[4] = 0x39;
    publicSta->staMac[5] = 0xaf;
    publicSta->vapIfIndex = 1627389971;
    publicSta->globalIdx = 128;
    publicSta->staFlag = 10;
    publicSta->peerId = 0;
    publicSta->pfeVapIdx = 0;
    publicSta->sessionInfo = 0;
    publicSta->radioId = 1;
    publicSta->authId = -1976523922;
    publicSta->mainVapIfIndex = 1627389971;
    publicSta->startParse = 1;
    publicSta->upload = 0;
    publicSta->expectAuthStage = 2;

    // UsfPublicStaT publicSta1 1,0,0x50f9587b39af,null,null,8,null,null,null,null,null,null,null,null,null;
    publicSta1->dtlReservedCount = count;
    publicSta1->staMac[0] = 0x50;
    publicSta1->staMac[1] = 0xf9;
    publicSta1->staMac[2] = 0x58;
    publicSta1->staMac[3] = 0x7b;
    publicSta1->staMac[4] = 0x39;
    publicSta1->staMac[5] = 0xaf;
    publicSta1->vapIfIndex = 1627389971;
    publicSta1->globalIdx = 128;
    publicSta1->staFlag = 8;
    publicSta1->peerId = 0;
    publicSta1->pfeVapIdx = 0;
    publicSta1->sessionInfo = 0;
    publicSta1->radioId = 1;
    publicSta1->authId = -1976523922;
    publicSta1->mainVapIfIndex = 1627389971;
    publicSta1->startParse = 1;
    publicSta1->upload = 0;
    publicSta1->expectAuthStage = 2;

    // UsfPublicAuthT 1,0,0x50f9587b39af,0,1,170,0,0,1,1,1,0,0,0,0,0,0,0,0,0,0
    pubAuth->dtlReservedCount = count;
    pubAuth->staMac[0] = 0x50;
    pubAuth->staMac[1] = 0xf9;
    pubAuth->staMac[2] = 0x58;
    pubAuth->staMac[3] = 0x7b;
    pubAuth->staMac[4] = 0x39;
    pubAuth->staMac[5] = 0xaf;
    pubAuth->authStatus = 0;
    pubAuth->authVlanEn = 1;
    pubAuth->authVlan = 170;
    pubAuth->hasPolicy = 0;
    pubAuth->userVipFlag = 0;
    pubAuth->ingStatEn = 1;
    pubAuth->egrStatEn = 1;
    pubAuth->statId = 1;
    pubAuth->userGroupId = 0;
    pubAuth->userGroupEn = 0;
    pubAuth->monitorMark = 0;
    pubAuth->userPreRoamIpv4Addr = 0;
    pubAuth->innerIsolateEn = 0;
    pubAuth->interIsolateEn = 0;
    pubAuth->splitEn = 0;
    pubAuth->splitTnlAclNum = 0;
    pubAuth->gatewayIsoEn = 0;
    pubAuth->currAuthStage = 0;

    // BRVlanT 1,0,0,0,170,1,0x00(32),0x00(81),-1,0,0;
    vlan->dtlReservedCount = count;
    vlan->nsId = 0;
    vlan->brId = 0;
    vlan->vlanId = 170;
    vlan->vlanType = 1;
    for (int32_t i = 0; i < 32; i++) {
        vlan->name[i] = 0x00;
    }
    for (int32_t i = 0; i < 81; i++) {
        vlan->desc[i] = 0x00;
    }
    vlan->brVlanIf = -1;
    vlan->isCtrl = 0;
    vlan->instId = 0;

    // BRVlanMacAttrT 1,0,0,0,170,1,512,1,1;
    vlanMacAttr->dtlReservedCount = count;
    vlanMacAttr->nsId = 0;
    vlanMacAttr->brId = 0;
    vlanMacAttr->vlanId = 170;
    vlanMacAttr->learn = 1;
    vlanMacAttr->limit = 512;
    vlanMacAttr->limitAct = 1;
    vlanMacAttr->limitAlm = 1;

    // 表BRVlanBitmapT 1,0,0,0x4000(19)802000(490);
    vlanBitmap->dtlReservedCount = count;
    vlanBitmap->brId = 0;
    vlanBitmap->vlanBitmap[0] = 0x40;
    for (int32_t i = 1; i < 20; i++) {
        vlanBitmap->vlanBitmap[i] = 0x00;
    }
    vlanBitmap->vlanBitmap[20] = 0x80;
    vlanBitmap->vlanBitmap[21] = 0x20;
    for (int32_t i = 22; i < 512; i++){
        vlanBitmap->vlanBitmap[i] = 0x00;
    }

    // 表BRPortVlanBitmapT 1,0,0,1627389971,0x00(20)802000(490),0x00(512),0x00(20)802000(490);
    portVlanBitmap->dtlReservedCount = count;
    portVlanBitmap->brId = 0;
    portVlanBitmap->ifIndex = 1627389971;
    for (int32_t i = 0;  i < 20; i++) {
        portVlanBitmap->vlanMemberBitmap[i] = 0x00;
    }
    portVlanBitmap->vlanMemberBitmap[20] = 0x80;
    portVlanBitmap->vlanMemberBitmap[21] = 0x20;
    for (int32_t i = 22; i < 512; i++){
        portVlanBitmap->vlanMemberBitmap[i] = 0x00;
    }

    for (int32_t i = 0;  i < 512; i++) {
        portVlanBitmap->vlanTagBitmap[i] = 0x00;
    }

    for (int32_t i = 0;  i < 20; i++) {
        portVlanBitmap->vlanUntagBitmap[i] = 0x00;
    }
    portVlanBitmap->vlanUntagBitmap[20] = 0x80;
    portVlanBitmap->vlanUntagBitmap[21] = 0x20;
    for (int32_t i = 22; i < 512; i++){
        portVlanBitmap->vlanUntagBitmap[i] = 0x00;
    }

    // BRPortT 1,0,1627389971,0,0,0,160,0,0;
    port->dtlReservedCount = count;
    port->ifIndex = 1627389971;
    port->portIndex = 0;
    port->nsId = 0;
    port->brId = 0;
    port->pvid = 160;
    port->linkType = 0;
    port->stpEnable = 0;

}

int32_t Test11rRoamingInsert(
    GmcConnT *conn, GmcStmtT *syncStmt, int64_t *itfDoneTimeCost, int64_t *calDoneTimeCost, int count)
{
    UsfPublicStaT publicSta = {0};
    UsfPublicStaT publicSta1 = {0};
    UsfPublicAuthT pubAuth = {0};
    BRVlanT vlan = {0};
    BRVlanMacAttrT vlanMacAttr = {0};
    BRVlanBitmapT vlanBitmap = {0};
    BRPortVlanBitmapT portVlanBitmap = {0};
    BRPortT port = {0};
    WmpConfigMacIpv6T cfgIPv6 = {0};
    WmpConfigMacIpT cfgIP = {0};
    DhcpsnpDynamicUserBindT dynBindUser = {0};
    UsfPublicROAMT publicRoam = {0};
    SacSacProfileUsfT sacProfileUsf = {0};
    UsfPublicClearIngPktCacheT pubClearPkt = {0};

    InitInpObj(&publicSta, &publicSta1, &pubAuth, &vlan, &vlanMacAttr, &vlanBitmap, &portVlanBitmap, &port, count);

    struct timeval tmBegin, tmEnd1, tmEnd2;

    UserDataT data = {0};
    GmcAsyncRequestDoneContextT context = {0};
    context.insertCb = DMLCb;
    context.userData = &data;

    gettimeofday(&tmBegin, NULL);
    int32_t ret = InputTableSingle(syncStmt, SET_USF_PUBLICSTA, (void *)&publicSta, &context);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "SET_USF_PUBLICSTA first InputTableSingle");
        return ret;
    }
    if (count == 1) {
        usleep(15000);
    }
    ret = InputTableSingle(syncStmt, SET_USF_PUBLICSTA, (void *)&publicSta1, &context);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "SET_USF_PUBLICSTA second InputTableSingle");
        return ret;
    }

    ret = InputTableSingle(syncStmt, SET_USF_PUBLICAUTH, (void *)&pubAuth, &context);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "SET_USF_PUBLICAUTH InputTableSingle");
        return ret;
    }

    SetLabelStatusE insertLabels1[5] = {SET_BR_VLAN, SET_BR_VLAN_MACATTR, SET_BR_VLAN_BITMAP, SET_BR_PORT_VLAN_BITMAP, SET_BR_PORT};
    void *obj1[5] = {(void *)&vlan, (void *)&vlanMacAttr, (void *)&vlanBitmap, (void *)&portVlanBitmap, (void *)&port};
    ret = InputTableBatch(conn, syncStmt, insertLabels1, obj1, 5, &data);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "SET_BR_VLAN SET_BR_VLAN_MACATTR InputTableBatch");
        return ret;
    }
    
    gettimeofday(&tmEnd1, NULL);

    while (data.received != 4) {
        // do nothing
    }
    gettimeofday(&tmEnd2, NULL);
    const char *opStr = count == 1 ? "insert" : "delete";
    int64_t timeCost1 = (tmEnd1.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd1.tv_usec - tmBegin.tv_usec);
    int64_t timeCost2 = (tmEnd2.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd2.tv_usec - tmBegin.tv_usec);

    *itfDoneTimeCost = *itfDoneTimeCost + timeCost1;
    *calDoneTimeCost = *calDoneTimeCost + timeCost2;

    printf("%s_interface_cost_time:%lld s|%lld us, %s_calculate_cost_time:%lld s|%lld us\n", opStr, timeCost1 / 1000000,
        timeCost1 % 1000000, opStr, timeCost2 / 1000000, timeCost2 % 1000000);
    return ret;
}

/*
 * * ################################## end:输入表插入接口 ################################
 */

int32_t ExecInstallCmd(const char *fileName)
{
    char installCmd[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(installCmd, DB_INVALID_UINT8, "gmimport -c datalog -f ../datalogFile/%s.so", fileName);
    return system(installCmd);
}

int32_t ExecUninstallCmd(const char *soFile)
{
    char uninstallCmd[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(uninstallCmd, DB_INVALID_UINT8, "gmimport -c datalog -d %s", soFile);
    return system(uninstallCmd);
}

int32_t ExecPrecompileCmd(const char *fileName)
{
    printf("execCmdWithUdf\n");
    char cmdName[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(cmdName, DB_INVALID_UINT8, "../datalogFile/%s", fileName);

    char precompileCmd[DB_INVALID_UINT16];
    int precompileCmdLen = sprintf_s(precompileCmd, DB_INVALID_UINT16,
        "gmprecompiler -f %s.d %s.c && gcc -fPIC -I ../../../../pub/include/ -I "
        "../../../../platform/hwsecurec/include/ "
        "--shared -g %s.c -o %s.so",
        cmdName, cmdName, cmdName, cmdName);
    printf("%s\n", precompileCmd);
    return system(precompileCmd);
}

int main()
{
    int ret = GMERR_OK;
    uint32_t testCount = 1;  // 此处可以控制用例执行的次数
    int64_t avgInsertItfTimeCost = 0, avgInsertCalTimeCost = 0, avgDeleteItfTimeCost = 0, avgDeleteCalTimeCost = 0,
            itfOpsSecond = 0, calOpsSecond = 0;
    const char *filename = (char *)"client_st_11r_roaming_level3";

    ret = GmcInit();
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Failed to init client");
        return ret;
    }

    pthread_t asyncWrite;
    int asyncWriteFd = -1;
    CreateAndStartEpoll(&asyncWrite, &asyncWriteFd);

    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;

    ret = CreateConnectionAndStmt(GMC_CONN_TYPE_SYNC, &syncConn, &syncStmt, -1);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "connect sync failed.");
        goto FAIL;
    }

    ret = CreateConnectionAndStmt(GMC_CONN_TYPE_ASYNC, &asyncConn, &asyncStmt, asyncWriteFd);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "connect async failed.");
        goto FAIL;
    }

#if defined(HPE) || defined(RTOSV2X)  // Dopra环境下
// empty
#else
    ret = ExecPrecompileCmd(filename);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "Failed to ExecPrecompileCmd");
        goto FAIL;
    }

    ret = ExecInstallCmd(filename);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "ExecInstallCmd failed.");
        goto FAIL;
    }
#endif

    for (uint32_t i = 0; i < testCount; i++) {
        DEMO_ERROR(0, "============================== testCount: %u", i);
        // 插入
        ret = Test11rRoamingInsert(asyncConn, asyncStmt, &avgInsertItfTimeCost, &avgInsertCalTimeCost, 1);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "insert failed. index:%u", i);
            goto UNPRE;
        }
        usleep(30000);
        // 删除
        ret = Test11rRoamingInsert(asyncConn, asyncStmt, &avgDeleteItfTimeCost, &avgDeleteCalTimeCost, -1);
        if (ret != GMERR_OK) {
            DEMO_ERROR(ret, "delete failed. index:%u", i);
            goto UNPRE;
        }
        usleep(1000 * 100);
    }
    avgInsertItfTimeCost = avgInsertItfTimeCost / testCount;
    itfOpsSecond = ((double)testCount / avgInsertItfTimeCost) * 1000000;

    avgInsertCalTimeCost = avgInsertCalTimeCost / testCount;
    calOpsSecond = ((double)testCount / avgInsertCalTimeCost) * 1000000;

    // avg_interface_insert_cost_time: 异步接口返回的平均耗时 （*业务关注的耗时）
    // avg_calculate_insert_cost_time：异步接口回包全部收到的平均耗时
    printf("avg_interface_insert_cost_time:%lld s|%lld us, ops: %lld \navg_calculate_insert_cost_time:%lld s|%lld us, "
           "ops: %lld\n",
        avgInsertItfTimeCost / 1000000, avgInsertItfTimeCost % 1000000, itfOpsSecond, avgInsertCalTimeCost / 1000000,
        avgInsertCalTimeCost % 1000000, calOpsSecond);

UNPRE:
    // 删除预置数据
#if defined(HPE) || defined(RTOSV2X)  // Dopra环境下
// empty
#else
    ret = ExecUninstallCmd(filename);
    if (ret != GMERR_OK) {
        DEMO_ERROR(ret, "ExecUninstallCmd failed.");
        goto FAIL;
    }
#endif
FAIL:
    DestroyConnAndStmt(syncConn, syncStmt);
    DestroyConnAndStmt(asyncConn, asyncStmt);
UNINIT:
    StopAndDestroyEpoll(asyncWrite, &asyncWriteFd);
    ClientUninit();
    return ret;
}
