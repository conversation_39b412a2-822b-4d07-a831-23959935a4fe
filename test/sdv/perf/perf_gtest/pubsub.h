/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: pubsub.h
 * Description: datalog pubsub
 * Author: wuxueqi 00495442
 * Create: 2022-09-14
 */

#ifndef __PUBSUB_H__
#define __PUBSUB_H__

#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <stdarg.h>
#include <regex.h>
#include <signal.h>
#include <pthread.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/time.h>
#include <dirent.h>
#include <semaphore.h>

#include "gtest/gtest.h"
#include "struct_common.h"

#define MAX_CMD_SIZE 1024
#define FILE_PATH 512
#define MAX_NAME_LENGTH 512

GmcConnT *g_connSync = NULL, *g_connSub = NULL;
GmcStmtT *g_stmtSync = NULL;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
char g_fileName[FILE_PATH] = "pubsubRsc";

char g_inp3[MAX_NAME_LENGTH] = "ns1.inp3";
char g_mid3[MAX_NAME_LENGTH] = "ns1.mid3";
char g_inp4[MAX_NAME_LENGTH] = "ns1.inp4";

char g_inp0[MAX_NAME_LENGTH] = "ns1.inp0";
char g_rsc0[MAX_NAME_LENGTH] = "ns1.rsc0";
char g_rsc00[MAX_NAME_LENGTH] = "ns1.rsc00";
char g_rsc2[MAX_NAME_LENGTH] = "ns1.rsc2";
char g_inp5[MAX_NAME_LENGTH] = "ns1.inp5";
char g_inp6[MAX_NAME_LENGTH] = "ns1.inp6";
char g_inp7[MAX_NAME_LENGTH] = "ns1.inp7";

int g_isFlowOn = true;

typedef int (*FuncWrite)(GmcStmtT *stmt, int32_t index);
typedef int (*FuncRead)(GmcStmtT *stmt, uint8_t *dataCheckIndexes);

int CompileAndLoad(char *fileName)
{
    // 加载.so
    TestUninstallDatalog(fileName, NULL, false);
    char command[MAX_CMD_SIZE] = {0};
    char outputDir[FILE_PATH] = "datalogFile";
    (void)snprintf(command, FILE_PATH, "./%s/%s.so", outputDir, fileName);
    return TestLoadDatalog(command, NULL);
}

uint32_t GetBatchCallbackTimes(uint32_t vertexCount)
{
    return vertexCount / GMC_SUB_BATCH_MAX + 1;
}

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    FuncRead func;
} SnUserDataWithFuncT;

typedef struct TaglabelCfg {
    int32_t startVal;  // 主键或其他非成员索引的起始值
    uint32_t count;    // 主键或其他非成员索引的数量
    SnUserDataT *userData1;
    SnUserDataT *userData2;
    SnUserDataT *userData3;
    uint32_t userDataIdx;
    bool isBatch;
    bool isBatchStruct;
    int32_t threadId;  // 线程Id
} GtlabelCfgT;

typedef struct TagGtViewDrtConnSubsStat {
    uint32_t subTryCnt;
    uint32_t subAllocConMemFailCnt;
    uint32_t subSendSucCnt;
    uint32_t subSendFailCnt;
} GtViewDrtConnSubsStat;

typedef struct TagGtViewDrtDataPlaneChannelStat {
    char *status;
    uint32_t syncMessageTotalSendCnt;
    uint32_t syncMessageSendSuccessCnt;
    uint32_t syncMessageSendFailureCnt;
    uint32_t syncMessageSendBufferFullCnt;
} GtViewDrtDataPlaneChannelStat;

const char *g_emptyStatus = "SEND_CHANNEL_EMPTY";

// 取出 $DRT_CONN_SUBS_STAT 视图中的部分字段
int PubsubViewDrtConnSubsStat(GtViewDrtConnSubsStat *view, const char *subConnName, int threadId)
{
    char command[MAX_CMD_SIZE] = {0}, filter[MAX_CMD_SIZE] = {0};
    (void)sprintf(filter, "NODE_NAME=\'%s\'", subConnName);
    char const *viewName = "V\\$DRT_CONN_SUBS_STAT";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f %s", g_toolPath, g_connServer, viewName, filter);

    system(command);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_DEBUG, "popen(%s) error.", command);
        return -1;
    }

    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "SUB_TRY_CNT:") == 0) {
            view->subTryCnt = atoi(str[1]);
        }
        if (strcmp(str[0], "SUB_ALLOC_CON_MEM_FAIL_CNT:") == 0) {
            view->subAllocConMemFailCnt = atoi(str[1]);
        }
        if (strcmp(str[0], "SUB_SEND_SUC_CNT:") == 0) {
            view->subSendSucCnt = atoi(str[1]);
        }
        if (strcmp(str[0], "SUB_SEND_FAIL_CNT:") == 0) {
            view->subSendFailCnt = atoi(str[1]);
            break;
        }
    }
    int ret = pclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_DEBUG, "pclose(%d) error.", ret);
        return -1;
    }

    AW_FUN_Log(LOG_DEBUG,
        "[PubsubViewDrtConnSubsStat %d] subTryCnt : %d, subAllocConMemFailCnt : %d, subSendSucCnt : %d, "
        "subSendFailCnt : %d",
        threadId, view->subTryCnt, view->subAllocConMemFailCnt, view->subSendSucCnt, view->subSendFailCnt);
    return GMERR_OK;
}

// 取出 $DRT_DATA_PLANE_CHANNEL_STAT 视图中的部分字段
int PubsubViewDrtDataPlaneChannelStat(GtViewDrtDataPlaneChannelStat *view, int threadId)
{
    char command[MAX_CMD_SIZE] = {0};
    char const *viewName = "V\\$DRT_DATA_PLANE_CHANNEL_STAT";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);

    system(command);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_DEBUG, "popen(%s) error.", command);
        return -1;
    }

    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "STATUS:") == 0) {
            memcpy(view->status, str[1], sizeof(str[1]));
        }
        if (strcmp(str[0], "SYNC_MESSAGE_TOTAL_SEND_COUNT:") == 0) {
            view->syncMessageTotalSendCnt = atoi(str[1]);
        }
        if (strcmp(str[0], "SYNC_MESSAGE_SEND_SUCCESS_COUNT:") == 0) {
            view->syncMessageSendSuccessCnt = atoi(str[1]);
        }
        if (strcmp(str[0], "SYNC_MESSAGE_SEND_FAILURE_COUNT:") == 0) {
            view->syncMessageSendFailureCnt = atoi(str[1]);
        }
        if (strcmp(str[0], "SYNC_MESSAGE_SEND_BUFFER_FULL_COUNT:") == 0) {
            view->syncMessageSendBufferFullCnt = atoi(str[1]);
            break;
        }
    }
    int ret = pclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_DEBUG, "pclose(%d) error.", ret);
        return -1;
    }

    AW_FUN_Log(LOG_DEBUG,
        "[PubsubViewDrtDataPlaneChannelStat %d] status : %s, syncMessageTotalSendCnt : %d, "
        "syncMessageSendSuccessCnt : %d, "
        "syncMessageSendFailureCnt : %d, "
        "syncMessageSendBufferFullCnt : %d",
        threadId, view->status, view->syncMessageTotalSendCnt, view->syncMessageSendSuccessCnt,
        view->syncMessageSendFailureCnt, view->syncMessageSendBufferFullCnt);
    return GMERR_OK;
}

int PubsubView(
    GtViewDrtConnSubsStat expView1, GtViewDrtDataPlaneChannelStat expView2, const char *subConnName, int threadId)
{
    GtViewDrtConnSubsStat actualView1 = {0};
    GtViewDrtDataPlaneChannelStat actualView2 = {0};
    actualView2.status = (char *)malloc(MAX_CMD_SIZE);
    AW_MACRO_EXPECT_NOTNULL(actualView2.status);

    int ret = PubsubViewDrtConnSubsStat(&actualView1, subConnName, threadId);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(expView1.subTryCnt, actualView1.subTryCnt);
    AW_MACRO_EXPECT_EQ_INT(expView1.subAllocConMemFailCnt, actualView1.subAllocConMemFailCnt);
    AW_MACRO_EXPECT_EQ_INT(expView1.subSendSucCnt, actualView1.subSendSucCnt);
    AW_MACRO_EXPECT_EQ_INT(expView1.subSendFailCnt, actualView1.subSendFailCnt);

    ret = PubsubViewDrtDataPlaneChannelStat(&actualView2, threadId);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_STR(expView2.status, actualView2.status);
    AW_MACRO_EXPECT_EQ_INT(expView2.syncMessageTotalSendCnt, actualView2.syncMessageTotalSendCnt);
    AW_MACRO_EXPECT_EQ_INT(expView2.syncMessageSendSuccessCnt, actualView2.syncMessageSendSuccessCnt);
    AW_MACRO_EXPECT_EQ_INT(expView2.syncMessageSendFailureCnt, actualView2.syncMessageSendFailureCnt);
    AW_MACRO_EXPECT_EQ_INT(expView2.syncMessageSendBufferFullCnt, actualView2.syncMessageSendBufferFullCnt);

    free(actualView2.status);
    return GMERR_OK;
}

int createSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, SnUserDataWithFuncT *userData,
    uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb, FuncRead func)
{
    char *subInfo = NULL;
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);

    int ret = testSnMallocUserData(&userData->data, mallocCount, mallocCount);
    RETURN_IFERR(ret);
    userData->func = func;

    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, userData);
    RETURN_IFERR(ret);
    free(subInfo);
    return ret;
}

int cancelSubscription(
    GmcStmtT *stmtSync, const char *subsName, SnUserDataWithFuncT *userData, int32_t dataStart, uint32_t checkNum)
{
    for (uint32_t i = dataStart; i < dataStart + checkNum; i++) {
        if (userData->data->dataCheckIndexes[i] != 1) {
            AW_FUN_Log(LOG_DEBUG, "userData->data->dataCheckIndexes[%d] : %d", i, userData->data->dataCheckIndexes[i]);
            return -1;
        }
    }
    int ret = GmcUnSubscribe(stmtSync, subsName);
    RETURN_IFERR(ret);

    testSnFreeUserData(userData->data);
    return GMERR_OK;
}

int startTrx(GmcConnT *conn)
{
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    return GmcTransStart(conn, &config);
}

int commitTrx(GmcConnT *conn)
{
    return GmcTransCommit(conn);
}

#define STR_LEN 128
#define BYTE_1 1
#define BYTE_4 4
#define BYTE_128 128

int batchPrepare(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    RETURN_IFERR(ret);
    return ret;
}

void SetUserData(GtlabelCfgT vertexCfg, uint32_t *userDataIdx, int32_t v)
{
    SnUserDataT *data1 = vertexCfg.userData1;
    SnUserDataT *data2 = vertexCfg.userData2;
    SnUserDataT *data3 = vertexCfg.userData3;

    if (data1 != NULL) {
        ((int *)(data1->new_value))[*userDataIdx] = v;
    } else {
        AW_FUN_Log(LOG_STEP, "warning : data1 is null");
    }
    if (data2 != NULL) {
        ((int *)(data2->new_value))[*userDataIdx] = v;
    }
    if (data3 != NULL) {
        ((int *)(data3->new_value))[*userDataIdx] = v;
    }
    (*userDataIdx)++;
}

int writeTable(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, GtlabelCfgT vertexCfg, FuncWrite func)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    bool isBatch = vertexCfg.isBatch;
    bool isBatchStruct = vertexCfg.isBatchStruct;
    uint32_t userDataIdx = vertexCfg.userDataIdx;

    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    if (!isBatch && isBatchStruct) {
        RETURN_IFERR((-1));
    }
    if (isBatch) {
        ret = batchPrepare(conn, &batch);
        RETURN_IFERR(ret);
        if (isBatchStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            RETURN_IFERR(ret);
        }
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        SetUserData(vertexCfg, &userDataIdx, i);

        ret = func(stmt, i);
        RETURN_IFERR(ret);

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            RETURN_IFERR(ret);
        } else {
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
            if (!g_isFlowOn) {
                ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
                RETURN_IFERR(ret);
            }
        }
    }

    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
        if (isBatchStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

int readTable(GmcStmtT *stmt, const char *labelName, GtlabelCfgT vertexCfg, FuncRead func, bool needCheck)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t readCnt = startPkVal;
    uint8_t *dataCheckIndexes = (uint8_t *)malloc(sizeof(uint8_t) * (startPkVal + vertexCount));
    if (dataCheckIndexes == NULL) {
        return -1;
    }
    memset(dataCheckIndexes, 0, sizeof(uint8_t) * (startPkVal + vertexCount));

    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (isFinish == true) {
            break;
        }
        ret = func(stmt, dataCheckIndexes);
        RETURN_IFERR(ret);
        readCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(startPkVal + vertexCount, readCnt);
    if (needCheck) {
        for (uint32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
            if (dataCheckIndexes[i] != 1) {
                AW_FUN_Log(LOG_DEBUG, "dataCheckIndexes[%d] : %d", i, dataCheckIndexes[i]);
                return -1;
            }
        }
    }
    free(dataCheckIndexes);
    return ret;
}

int snSendResp(GmcStmtT *stmt, uint16_t failedDataNum, uint16_t failedIndexes[GMC_SUB_BATCH_MAX])
{
    GmcRespT *response;
    int ret = GmcCreateResp(stmt, &response);
    RETURN_IFERR(ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    RETURN_IFERR(ret);
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    RETURN_IFERR(ret);
    ret = GmcSendResp(stmt, response);
    RETURN_IFERR(ret);
    ret = GmcDestroyResp(stmt, response);
    RETURN_IFERR(ret);
    return ret;
}

int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead func = userDefinedData->func;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;

    data->callbackTimes++;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    uint16_t failedDataNum = 1;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
    int ret = snSendResp(subStmt, failedDataNum, failedIndexes);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// %table inp3(F0:int4, F1:int4, F2:str, F3:byte1, F4:byte128, F5:int4, F6:int8)
int inp3Set(GmcStmtT *stmt, int32_t v)
{
    int32_t f0 = v;
    int32_t f1 = v;
    char f2[STR_LEN] = {0};
    uint8_t f3[BYTE_1] = {0};
    uint8_t f4[BYTE_128] = {0};
    int32_t f5 = v;
    int64_t f6 = v;
    int32_t dtlReservedCount = v;

    (void)snprintf((char *)f2, sizeof(f2), "aaaaaaa%0120d", v);
    int ret = memset_s(f3, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    if (v % 2 == 0) {
        ret = memset_s(f4, BYTE_128, 0xff, BYTE_128);
    } else {
        ret = memset_s(f4, BYTE_128, 0x0, BYTE_128);
    }
    RETURN_IFERR(ret);

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, f2, strlen(f2));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3, sizeof(f3));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_FIXED, f4, sizeof(f4));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &f5, sizeof(f5));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &f6, sizeof(f6));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

int inp3GetValue(GmcStmtT *stmt, int32_t v)
{
    int32_t f1 = v;
    char f2[STR_LEN] = {0};
    uint8_t f3[BYTE_1] = {0};
    uint8_t f4[BYTE_128] = {0};
    int32_t f5 = v;
    int64_t f6 = v;

    (void)snprintf((char *)f2, sizeof(f2), "aaaaaaa%0120d", v);
    int ret = memset_s(f3, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    if (v % 2 == 0) {
        ret = memset_s(f4, BYTE_128, 0xff, BYTE_128);
    } else {
        ret = memset_s(f4, BYTE_128, 0x0, BYTE_128);
    }
    RETURN_IFERR(ret);

    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_INT32, &f1);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_STRING, f2);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_FIXED, f3);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_FIXED, f4);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_INT32, &f5);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT64, &f6);
    RETURN_IFERR(ret);
}

// %table inp3(F0:int4, F1:int4, F2:str, F3:byte1, F4:byte128, F5:int4, F6:int8)
int inp3Get(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t f0 = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "F0", &f0, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = inp3GetValue(stmt, f0);
    RETURN_IFERR(ret);

    int32_t dtlReservedCount = f0;
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);

    dataCheckIndexes[f0]++;
    return ret;
}

// %table inp3(F0:int4, F1:int4, F2:str, F3:byte1, F4:byte128, F5:int4, F6:int8)
int inp3GetProject(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t f0 = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "F0", &f0, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = inp3GetValue(stmt, f0);
    RETURN_IFERR(ret);

    int32_t dtlReservedCount = 1;
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);

    dataCheckIndexes[f0]++;
    return ret;
}

// %table inp3(F0:int4, F1:int4, F2:str, F3:byte1, F4:byte128, F5:int4, F6:int8)
int inp3SetDeltaIsNull(GmcStmtT *stmt, int32_t v)
{
    int32_t f0 = 0;
    int32_t f1 = 0;
    char f2[STR_LEN] = {0};
    uint8_t f3[BYTE_1] = {0};
    uint8_t f4[BYTE_128] = {0};
    int32_t f5 = 0;
    int64_t f6 = 0;
    int32_t dtlReservedCount = v;

    (void)snprintf((char *)f2, sizeof(f2), "aaaaaaa%0120d", 0);
    int ret = memset_s(f3, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    ret = memset_s(f4, BYTE_128, 0xff, BYTE_128);
    RETURN_IFERR(ret);

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, f2, strlen(f2));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3, sizeof(f3));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_FIXED, f4, sizeof(f4));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &f5, sizeof(f5));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &f6, sizeof(f6));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

int inp3GetValueDeltaIsNull(GmcStmtT *stmt, int32_t v)
{
    int32_t f0 = 0;
    int32_t f1 = 0;
    char f2[STR_LEN] = {0};
    uint8_t f3[BYTE_1] = {0};
    uint8_t f4[BYTE_128] = {0};
    int32_t f5 = 0;
    int64_t f6 = 0;

    (void)snprintf((char *)f2, sizeof(f2), "aaaaaaa%0120d", 0);
    int ret = memset_s(f3, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    ret = memset_s(f4, BYTE_128, 0xff, BYTE_128);
    RETURN_IFERR(ret);

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT32, &f0);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_INT32, &f1);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_STRING, f2);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_FIXED, f3);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_FIXED, f4);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_INT32, &f5);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT64, &f6);
    RETURN_IFERR(ret);
    return ret;
}

// %table inp3(F0:int4, F1:int4, F2:str, F3:byte1, F4:byte128, F5:int4, F6:int8)
int inp3GetDeltaIsNull(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int ret = inp3GetValueDeltaIsNull(stmt, 0);
    RETURN_IFERR(ret);
    int32_t dtlReservedCount = 500500;

    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);
    return ret;
}

int inp3GetDeltaIsNullProject(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int ret = inp3GetValueDeltaIsNull(stmt, 0);
    RETURN_IFERR(ret);
    int32_t dtlReservedCount = 1;

    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);
    return ret;
}

// %table inp0(a:int4, b:int4)
int inp0Set(GmcStmtT *stmt, int32_t v)
{
    int32_t a = v;
    int32_t b = v;
    int32_t dtlReservedCount = v;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

// %table inp0(a:int4, b:int4)
int inp0Get(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    int32_t b = a;
    int32_t dtlReservedCount = a;

    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT32, &b);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);

    dataCheckIndexes[a]++;
    return ret;
}

// %table inp0(a:int4, b:int4)
int inp0GetNoSub(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    int32_t b = a;
    int32_t dtlReservedCount = 2;

    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT32, &b);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);
    dataCheckIndexes[a]++;
    return ret;
}

// %table inp0(a:int4, b:int4)
int inp0SetDeltaIsNull(GmcStmtT *stmt, int32_t v)
{
    int32_t a = 1;
    int32_t b = 1;
    int32_t dtlReservedCount = v;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

// %table inp0(a:int4, b:int4)
int inp0GetDeltaIsNull(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 1;
    int32_t b = 1;
    int32_t dtlReservedCount = 500500;

    int ret = queryPropertyAndCompare(stmt, "a", GMC_DATATYPE_INT32, &a);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT32, &b);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);
    return ret;
}

// %resource rsc0(a:int4, b:int4 -> c:str, d:byte1, e:byte128, f:int4, g:int8)
int rsc0Set(GmcStmtT *stmt, int32_t v)
{
    int32_t a = v;
    int32_t b = v;
    char c[STR_LEN] = {0};
    uint8_t d[BYTE_1] = {0};
    uint8_t e[BYTE_128] = {0};
    int32_t f = v;
    int64_t g = v;
    int32_t dtlReservedCount = v;

    (void)snprintf((char *)c, sizeof(c), "aaaaaaa%0120d", v);
    int ret = memset_s(d, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    if (v % 2 == 0) {
        ret = memset_s(e, BYTE_128, 0xff, BYTE_128);
    } else {
        ret = memset_s(e, BYTE_128, 0x0, BYTE_128);
    }
    RETURN_IFERR(ret);

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_STRING, c, strlen(c));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_FIXED, d, sizeof(d));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, e, sizeof(e));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_INT32, &f, sizeof(f));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_INT64, &g, sizeof(g));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

// %resource rsc0(a:int4, b:int4 -> c:str, d:byte1, e:byte128, f:int4, g:int8)
int rsc0Get(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    int32_t b = a;
    char c[STR_LEN] = "ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
                      "fffffffffffffffffffffffffffffff";
    uint8_t d[BYTE_1] = {0};
    uint8_t e[BYTE_128] = {0};
    int32_t f = -1;
    int64_t g = -1;
    int32_t dtlReservedCount = 1;

    ret = memset_s(d, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    ret = memset_s(e, BYTE_128, 0xff, BYTE_128);
    RETURN_IFERR(ret);

    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT32, &b);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "c", GMC_DATATYPE_STRING, c);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "d", GMC_DATATYPE_FIXED, d);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "e", GMC_DATATYPE_FIXED, e);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "f", GMC_DATATYPE_INT32, &f);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "g", GMC_DATATYPE_INT64, &g);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);

    dataCheckIndexes[a]++;
    return ret;
}

int rsc0GetValue(GmcStmtT *stmt, int32_t v)
{
    int32_t b = v;
    char c[STR_LEN] = {0};
    uint8_t d[BYTE_1] = {0};
    uint8_t e[BYTE_128] = {0};
    int32_t f = v;
    int64_t g = v;

    (void)snprintf((char *)c, sizeof(c), "aaaaaaa%0120d", v);
    int ret = memset_s(d, BYTE_1, 0xff, BYTE_1);
    RETURN_IFERR(ret);
    if (v % 2 == 0) {
        ret = memset_s(e, BYTE_128, 0xff, BYTE_128);
    } else {
        ret = memset_s(e, BYTE_128, 0x0, BYTE_128);
    }
    RETURN_IFERR(ret);

    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT32, &b);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "c", GMC_DATATYPE_STRING, c);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "d", GMC_DATATYPE_FIXED, d);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "e", GMC_DATATYPE_FIXED, e);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "f", GMC_DATATYPE_INT32, &f);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "g", GMC_DATATYPE_INT64, &g);
    RETURN_IFERR(ret);
    return ret;
}

int rsc0GetAfterWrite(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = rsc0GetValue(stmt, a);
    RETURN_IFERR(ret);

    int32_t dtlReservedCount = a;
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);
    dataCheckIndexes[a]++;
    return ret;
}

int out00Get(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = rsc0GetValue(stmt, a);
    RETURN_IFERR(ret);

    int32_t dtlReservedCount = 1;
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);

    dataCheckIndexes[a]++;
    return ret;
}

// %table out4(a:int4, b:int4, c:byte4)
int out4Get(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int32_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    int32_t b = 100;
    uint8_t c[BYTE_4] = {0};
    int32_t dtlReservedCount = 1;

    ret = memset_s(c, BYTE_4, 0x1, BYTE_4);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT32, &b);
    RETURN_IFERR(ret);
    ret = GmcGetVertexPropertyByName(stmt, "c", c, BYTE_4, &isNull);
    RETURN_IFERR(ret);
    char byte[BYTE_4 * 2 + 1] = {0}, byteTmp[BYTE_4 * 2 + 1] = {0};
    char *cValue;
    for (uint32_t i = 0; i < BYTE_4; i++) {
        (void)sprintf(byte, "%x", c[i]);
        cValue = strncat(byteTmp, byte, 2);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, strcmp("12345678", cValue));
    ret = queryPropertyAndCompare(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount);
    RETURN_IFERR(ret);

    dataCheckIndexes[a]++;
    return ret;
}

int write2TableInABatch(GmcConnT *conn, GmcStmtT *stmt, const char *labelName1, const char *labelName2,
    GtlabelCfgT vertexCfg, FuncWrite func)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    uint32_t userDataIdx = vertexCfg.userDataIdx;

    int ret = GmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = batchPrepare(conn, &batch);
    RETURN_IFERR(ret);

    // 先写表1
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        SetUserData(vertexCfg, &userDataIdx, i);

        ret = func(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    // 再写表2
    ret = GmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        SetUserData(vertexCfg, &userDataIdx, i);

        ret = func(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    // 再写表1
    ret = GmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int32_t i = startPkVal + vertexCount; i < startPkVal + vertexCount * 2; i++) {
        SetUserData(vertexCfg, &userDataIdx, i);

        ret = func(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    GmcBatchDestroy(batch);
    return ret;
}

int write2TableInABatchWithDiffOutTable(GmcConnT *conn, GmcStmtT *stmt, const char *labelName1, const char *labelName2,
    GtlabelCfgT vertexCfg, FuncWrite func)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    SnUserDataT *data1 = vertexCfg.userData1;
    SnUserDataT *data2 = vertexCfg.userData2;
    uint32_t userDataIdx1 = 0, userDataIdx2 = 0;

    int ret = GmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = batchPrepare(conn, &batch);
    RETURN_IFERR(ret);

    // 先写表1
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ((int *)(data1->new_value))[userDataIdx1] = i;
        userDataIdx1++;

        ret = func(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    // 再写表2
    ret = GmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ((int *)(data2->new_value))[userDataIdx2] = i;
        userDataIdx2++;

        ret = func(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    // 再写表1
    ret = GmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int32_t i = startPkVal + vertexCount; i < startPkVal + vertexCount * 2; i++) {
        ((int *)(data1->new_value))[userDataIdx1] = i;
        userDataIdx1++;

        ret = func(stmt, i);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    GmcBatchDestroy(batch);
    return ret;
}

#pragma pack(1)
struct TestInp1StructT {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
};
#pragma pack()

#endif /* __PUBSUB_H__ */

