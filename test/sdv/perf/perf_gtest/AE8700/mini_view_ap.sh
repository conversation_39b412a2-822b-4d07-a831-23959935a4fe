###
 # Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 # @Description: AP设备小型化CTX看护
 # @Author: yuhonglei 30033205
### 
#!/bin/bash
function upload_file()
{
    local file_name="$1"
    local ftp_ip="$2"
    local ftp_name="$3"
    local ftp_pwd="$4"
    
    chmod 744 /opt/vrpv8/home/<USER>
    
    if [[ ! -f "/opt/vrpv8/home/<USER>" ]]; then
        echo "/opt/vrpv8/home/<USER>"
        return
    fi
    
    echo "download $file_name ...."
    /usr/local/bin/mdcli << EOF>/root/ftpget.log
edit-config
touch ftpc/client/enabled true
commit
return

ftpc-transfer-file
command-type put local-file-name $file_name remote-file-name $file_name server-ipv4-address $ftp_ip user-name $ftp_name password
$ftp_pwd
$ftp_pwd

emit
exit
EOF
    

    cnt=1
    while [[ $cnt -lt 120 ]]
    do
        /usr/local/bin/mdcli << EOF>/root/a.txt
display ftpc/transfer-tasks/
exit
EOF
    is_err=$(cat /root/a.txt | grep -A 2 $file_name | grep "status" | tail -n 1 | awk '{print $2}' | grep "failed" | wc -l)
    if [[ $is_err -gt 0 ]]; then
        break
    fi
    
    is_end=$(cat /root/a.txt | grep -A 2 $file_name | grep "percentage" | tail -n 1 | awk '{print $2}')
    if [[ $is_end -eq 100 ]]; then
        break
    fi
    sleep 5
    cnt=$((cnt+1))
    done
    
    echo "========== download $cc_pkg info: =============="
    cat /root/a.txt
}

function download_file()
{
    local cc_name="$1"
    local ftp_ip="$2"
    local ftp_name="$3"
    local ftp_pwd="$4"
    
    echo "download $cc_name ...."
    /usr/local/bin/mdcli << EOF>/root/ftpget.log
edit-config
touch ftpc/client/enabled true
commit
return

ftpc-transfer-file
command-type get local-file-name $cc_name remote-file-name $cc_name server-ipv4-address $ftp_ip user-name $ftp_name password
$ftp_pwd
$ftp_pwd

emit
exit
EOF
    

    cnt=1
    while [[ $cnt -lt 120 ]]
    do
        /usr/local/bin/mdcli << EOF>/root/a.txt
display ftpc/transfer-tasks/
exit
EOF
    is_err=$(cat /root/a.txt | grep -A 2 $cc_name | grep "status" | tail -n 1 | awk '{print $2}' | grep "failed" | wc -l)
    if [[ $is_err -gt 0 ]]; then
        break
    fi
    
    is_end=$(cat /root/a.txt | grep -A 2 $cc_name | grep "percentage" | tail -n 1 | awk '{print $2}')
    if [[ $is_end -eq 100 ]]; then
        break
    fi
    sleep 5
    cnt=$((cnt+1))
    done
    
    echo "========== download $cc_pkg info: =============="
    cat /root/a.txt
}

# 等待循环次数
max_attempts=7
# 每次等待s数
interval=30
target_file="/var/run/sys_p3s3.path"
found=0

attempt=1
while [ $attempt -le $max_attempts ]; do
    if [ -f "$target_file" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') 找到目录${target_file}了！"
        found=1
        break
    fi
    
    if [ $attempt -ne $max_attempts ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') 第${attempt}次检查未找到目录，等待${interval}秒..."
        sleep $interval
    fi

    attempt=$((attempt + 1))
done

if [ $found -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') 未找到目录"
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') 继续执行后续操作..."
echo "$(date '+%Y-%m-%d %H:%M:%S') 等待30s"
sleep 30
echo "$(date '+%Y-%m-%d %H:%M:%S') 等待30s"
sleep 30


# AE8700设备空配内存指标项看护,先上传至服务器
# 生成hpe_smaps.txt和hpe_smaps.log文件
cd /opt/vrpv8/home/
cp /proc/`sysdump.elf -p -M | grep 'bmm.elf' | awk '{print $2}'`/smaps hpe_smaps.txt
hpecli smaps dump
# cp /opt/vrpv8/home/<USER>/hpe_smaps.log /root/
hpecli task -a > phy_mem.txt

# 将生成的文件放到远端服务器
upload_file hpe_smaps.txt 10.67.194.115 6 6
upload_file hpe_smaps.log 10.67.194.115 6 6
upload_file phy_mem.txt 10.67.194.115 6 6

cd /root/

# 启动时间内部看护指标
echo -n "restart_time " > timeFile1.txt
echo -n "4.79s " >> timeFile1.txt
nctl blackbox s s > start_time.txt
tmp1=$(cat start_time.txt | grep 'NCTL config restore finished' | awk -F ':' '{gsub(/[][]/,"",$1); print $1}')
echo $tmp1 >> timeFile1.txt
echo "PASS" >> timeFile1.txt
cat timeFile1.txt | xargs >> reportFile.txt

# 1172 VM模式CPU底噪看护（hpecli task）
echo -n "ap1172_CPU_usage " > CPUFile1.txt
sleep 60
echo -n "1.2% " >> CPUFile1.txt
# 该指标需要静置设备2~3分钟后查询数据
sleep 60
temp1=$(hpecli task -a|grep 'user  gmserver'|awk '{print $12}')
echo |awk "{print $temp1}" >> CPUFile1.txt
echo "PASS" >> CPUFile1.txt
cat CPUFile1.txt | xargs >> reportFile.txt

# 动态内存视图COM_DYN_CTX
## Top Dynamic Memory Context:GLOBAL_DYN_MEM [5] MB [208] KB [607] Byte -> 5328.59 GLOBAL_ALLOC_SIZE [4] MB [79] KB [607] Byte -> 4175.59
echo -n "Top_Dynamic_Memory_Context:GLOBAL_DYN_MEM " > miniFile1.txt
echo -n "5328.59KB " >> miniFile1.txt
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="Top Dynamic Memory Context" > logfile.txt
tmp1=$(cat logfile.txt |grep "GLOBAL_DYN_MEM" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "GLOBAL_DYN_MEM" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "GLOBAL_DYN_MEM" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile1.txt
echo "PASS" >> miniFile1.txt
cat miniFile1.txt | xargs >> reportFile.txt

echo -n "Top_Dynamic_Memory_Context:GLOBAL_ALLOC_SIZE " > miniFile2.txt
echo "4175.59KB " >> miniFile2.txt
tmp1=$(cat logfile.txt |grep "GLOBAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "GLOBAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "GLOBAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile2.txt
echo "PASS" >> miniFile2.txt
cat miniFile2.txt | xargs >> reportFile.txt

# datalog plan cache memCtx:TOTAL_ALLOC_SIZE [0] MB [312] KB [272] Byte -> 312.27 ALLOC_TIMES 2747
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="datalog plan cache memCtx" > logfile.txt
echo -n "datalog_plan_cache_memCtx:TOTAL_ALLOC_SIZE " > miniFile3.txt
echo -n "1600KB " >> miniFile3.txt
tmp1=$(cat logfile.txt |grep -w "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep -w "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep -w "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile3.txt
echo "PASS" >> miniFile3.txt
cat miniFile3.txt | xargs >> reportFile.txt

echo -n "datalog_plan_cache_memCtx:ALLOC_TIMES " > miniFile4.txt
echo -n "10000 " >> miniFile4.txt
cat logfile.txt |grep "ALLOC_TIMES" |awk -F ':' '{print$2}' >> miniFile4.txt
echo "PASS" >> miniFile4.txt
cat miniFile4.txt | xargs >> reportFile.txt

# catalog dynamic memory context:TOTAL_ALLOC_SIZE [1] MB [330] KB [496] Byte -> 1354.49 ALLOC_TIMES 14429
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="catalog dynamic memory context" > logfile.txt
echo -n "catalog_dynamic_memory_context:TOTAL_ALLOC_SIZE " > miniFile5.txt
echo -n "1354.49KB " >> miniFile5.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile5.txt
echo "PASS" >> miniFile5.txt
cat miniFile5.txt | xargs >> reportFile.txt

echo -n "catalog_dynamic_memory_context:ALLOC_TIMES " > miniFile6.txt
echo -n "14429 " >> miniFile6.txt
cat logfile.txt |grep "ALLOC_TIMES" |awk -F ':' '{print$2}' >> miniFile6.txt
echo "PASS" >> miniFile6.txt
cat miniFile6.txt | xargs >> reportFile.txt

# catalog label dynamic memory context
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="catalog label dynamic memory context" > logfile.txt
echo -n "catalog_label_dynamic_memory_context:TOTAL_ALLOC_SIZE " > miniFile19.txt
echo -n "1800KB " >> miniFile19.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile19.txt
echo "PASS" >> miniFile19.txt
cat miniFile19.txt | xargs >> reportFile.txt

echo -n "catalog_label_dynamic_memory_context:ALLOC_TIMES " > miniFile20.txt
echo -n "16000 " >> miniFile20.txt
cat logfile.txt |grep "ALLOC_TIMES" |awk -F ':' '{print$2}' >> miniFile20.txt
echo "PASS" >> miniFile20.txt
cat miniFile20.txt | xargs >> reportFile.txt

# yang plan三项（yangPlanCaches）
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="yangPlanCaches" > logfile.txt
echo -n "yangPlanCaches:TOTAL_ALLOC_SIZE " > miniFile7.txt
echo -n "1800KB " >> miniFile7.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile7.txt
echo "PASS" >> miniFile7.txt
cat miniFile7.txt | xargs >> reportFile.txt

# yang plan三项（yangPlan_ns-omu-yang）
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="yangPlan_ns-omu-yang" > logfile.txt
echo -n "yangPlan_ns-omu-yang:TOTAL_ALLOC_SIZE " > miniFile8.txt
echo -n "1800KB " >> miniFile8.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile8.txt
echo "PASS" >> miniFile8.txt
cat miniFile8.txt | xargs >> reportFile.txt

# yang plan三项（yangPlanCache_ns-omu-yang）
gmsysview -q  V\$COM_DYN_CTX -f CTX_NAME="yangPlanCache_ns-omu-yang" > logfile.txt
echo -n "yangPlanCache_ns-omu-yang:TOTAL_ALLOC_SIZE " > miniFile18.txt
echo -n "1800KB " >> miniFile18.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile18.txt
echo "PASS" >> miniFile18.txt
cat miniFile18.txt | xargs >> reportFile.txt

# 共享内存视图COM_SHMEM_CTX
# SeHeapShmMemCtx:TOTAL_ALLOC_SIZE    [0] MB [611] KB [256] Byte -> 611.25
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="SeHeapShmMemCtx" > logfile.txt
echo -n "SeHeapShmMemCtx:TOTAL_ALLOC_SIZE " > miniFile9.txt
echo -n "611.25KB " >> miniFile9.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile9.txt
echo "PASS" >> miniFile9.txt
cat miniFile9.txt | xargs >> reportFile.txt

# SeHashShmMemCtx:TOTAL_ALLOC_SIZE    [0] MB [60] KB [256] Byte -> 60.25
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="SeHashShmMemCtx" > logfile.txt
echo -n "SeHashShmMemCtx:TOTAL_ALLOC_SIZE " > miniFile10.txt
echo -n "60.25KB " >> miniFile10.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile10.txt
echo "PASS" >> miniFile10.txt
cat miniFile10.txt | xargs >> reportFile.txt

# SeChainedHashShmMemCtx:TOTAL_ALLOC_SIZE    [0] MB [554] KB [352] Byte -> 554.34
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="SeChainedHashShmMemCtx" > logfile.txt
echo -n "SeChainedHashShmMemCtx:TOTAL_ALLOC_SIZE " > miniFile11.txt
echo -n "650KB " >> miniFile11.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile11.txt
echo "PASS" >> miniFile11.txt
cat miniFile11.txt | xargs >> reportFile.txt

# SeFixedHeapShmMemCtx:TOTAL_ALLOC_SIZE    [0] MB [78] KB [960] Byte -> 78.94
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="SeFixedHeapShmMemCtx" > logfile.txt
echo -n "SeFixedHeapShmMemCtx:TOTAL_ALLOC_SIZE " > miniFile12.txt
echo -n "78.94KB " >> miniFile12.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile12.txt
echo "PASS" >> miniFile12.txt
cat miniFile12.txt | xargs >> reportFile.txt

# HugeTLB_memory_context:TOTAL_ALLOC_SIZE    [0] MB [260] KB [104] Byte -> 260.1
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="HugeTLB memory context" > logfile.txt
echo -n "HugeTLB_memory_context:TOTAL_ALLOC_SIZE " > miniFile13.txt
echo -n "260.1KB " >> miniFile13.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile13.txt
echo "PASS" >> miniFile13.txt
cat miniFile13.txt | xargs >> reportFile.txt

# catalog_share_memory_context:TOTAL_ALLOC_SIZE    [3] MB [387] KB [184] Byte -> 3459.18
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="catalog share memory context" > logfile.txt
echo -n "catalog_share_memory_context:TOTAL_ALLOC_SIZE " > miniFile14.txt
echo -n "4400KB " >> miniFile14.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile14.txt
echo "PASS" >> miniFile14.txt
cat miniFile14.txt | xargs >> reportFile.txt

# catalog reusable share memory context:TOTAL_ALLOC_SIZE 920
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="catalog reusable share memory context" > logfile.txt
echo -n "catalog_reusable_share_memory_context:TOTAL_ALLOC_SIZE " > miniFile15.txt
echo -n "920KB " >> miniFile15.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile15.txt
echo "PASS" >> miniFile15.txt
cat miniFile15.txt | xargs >> reportFile.txt

# SeArtShmMemCtx：TOTAL_ALLOC_SIZE 150
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="SeArtShmMemCtx" > logfile.txt
echo -n "SeArtShmMemCtx:TOTAL_ALLOC_SIZE " > miniFile16.txt
echo -n "150KB " >> miniFile16.txt
tmp1=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "TOTAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile16.txt
echo "PASS" >> miniFile16.txt
cat miniFile16.txt | xargs >> reportFile.txt

# DbTopSharedMemoryContext:GLOBAL_ALLOC_SIZE 16000
gmsysview -q  V\$COM_SHMEM_CTX -f CTX_NAME="DbTopSharedMemoryContext" > logfile.txt
echo -n "DbTopSharedMemoryContext:GLOBAL_ALLOC_SIZE " > miniFile17.txt
echo -n "16000KB " >> miniFile17.txt
tmp1=$(cat logfile.txt |grep "GLOBAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$2}' |awk -F ']' '{print$1}')
tmp2=$(cat logfile.txt |grep "GLOBAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$3}' |awk -F ']' '{print$1}')
tmp3=$(cat logfile.txt |grep "GLOBAL_ALLOC_SIZE" |awk -F ':' '{print$2}'|awk -F '[' '{print$4}' |awk -F ']' '{print$1}')
echo | awk "{print ($tmp1*1024)+$tmp2+($tmp3/1024)}" >> miniFile17.txt
echo "PASS" >> miniFile17.txt
cat miniFile17.txt | xargs >> reportFile.txt

# 保留总视图信息
gmsysview -q V\$COM_DYN_CTX > dyn.view
gmsysview -q V\$COM_SHMEM_CTX > shmem.view
