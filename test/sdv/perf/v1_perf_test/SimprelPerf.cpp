/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SimprelPerf.cpp
 * Description: V1兼容性能
 * Create:
 */

#include "SimprelPerf.h"
#include "malloc.h"

using namespace std;

class SimprelPerf : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        DB_ERR_CODE ret = TestTPC_Init();
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        TestTPC_UnInit();
    };

public:
    virtual void SetUp() {};
    virtual void TearDown() {};
};

//  插2W条数据，按索引范围查询2W次(条件：F3 <= 60，符合条件50条)
TEST_F(SimprelPerf, perfTestAdapter4v1SelectRecByOrderWithIdx1_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "withIdxDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    uint16_t usRelId;
    char tableName[] = "withIdxTbl";
    CreateTblAndSetData4Select(tableName, ulDbId, &usRelId, CYCLES_20K);
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 1]\n"
        "[Select %d times by order with index]\n",
        CYCLES_20K);
    PrintLineLn();
    uint64_t startTime;
    uint64_t endTime;
    uint16_t i = 0;
    uint32_t ulLoop = 0;
    DB_ERR_CODE ret;

    DB_SORT_STRU pstSort;
    uint8_t ucFld = 1;
    pstSort.enSortType = DB_SORTTYPE_DESCEND;
    pstSort.ucSortNum = 1;
    pstSort.pSortFields = &ucFld;
    pstSort.pSortFields[0] = 3;
    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU stCond = {0};
    stCond.usCondNum = 1;
    stCond.aCond[0].ucFieldId = 3;
    stCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    DB_SELHANDLE phSelect;
    DB_DSBUF_STRU pstBuff;
    pstBuff.usRecNum = 50;
    pstBuff.StdBuf.ulBufLen = 5000;
    pstBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.StdBuf.ulBufLen);

    DB_DSBUF_STRU stBuff;
    stBuff.usRecNum = 1;
    stBuff.StdBuf.ulBufLen = 100;
    stBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(stBuff.StdBuf.ulBufLen);

    DB_BUF_STRU pstBuff1;
    pstBuff1.ulRecNum = 100;  // 返回实际返回数据条数
    pstBuff1.ulBufLen = 5000;
    pstBuff1.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff1.ulBufLen);

    uint64_t fetchTimeTaken = 0;
    uint64_t fetchTimeTakenAll = 0;
    uint32_t pulRecNum = 0;
    for (i = 1; i <= RUN_CNT; i++) {
        fetchTimeTaken = 0;
        uint32_t f3Value = 60;
        memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            memset(pstBuff.StdBuf.pucData, 0, pstBuff.StdBuf.ulBufLen);
            ret = TPC_BeginIdxSelectByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond,
                &stFldFilter, &phSelect);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            startTime = DbGetNsec();
            ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstBuff);
            endTime = DbGetNsec();
            fetchTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT((uint32_t)(f3Value - 10), pstBuff.usRecNum);
            ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        fetchTimeTakenAll += fetchTimeTaken;
    }
    TEST_V1_FREE(pstBuff.StdBuf.pucData);
    TEST_V1_FREE(stBuff.StdBuf.pucData);
    TEST_V1_FREE(pstBuff1.pBuf);
    TestPerfPrintf(PERF_TESTCASE_001, TRANS2_MS(fetchTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// local 索引等值查询2W次
TEST_F(SimprelPerf, perfTestAdapter4v1SelectRecWithIdx1_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "withIdxDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    uint16_t usRelId;
    char tableName[] = "withIdxTbl";
    CreateTblAndSetData4Select(tableName, ulDbId, &usRelId, CYCLES_20K);
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 2]\n"
        "[Select %d records from a table with index]\n",
        CYCLES_20K);
    PrintLineLn();
    uint64_t startTime;
    uint64_t endTime;
    uint16_t i = 0;
    uint32_t ulLoop = 0;
    DB_ERR_CODE ret;

    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU stCond = {0};
    stCond.usCondNum = 1;
    stCond.aCond[0].ucFieldId = 3;
    stCond.aCond[0].enOp = DB_OP_EQUAL;

    DB_DSBUF_STRU pstBuff;
    pstBuff.usRecNum = 100;
    pstBuff.StdBuf.ulBufLen = 1000;
    pstBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.StdBuf.ulBufLen);

    DB_SELHANDLE phSelect;
    uint64_t selectAllRecTimeTaken = 0;
    uint64_t selectAllRecTimeTakenAll = 0;
    uint32_t pulRecNum = 0;
    for (i = 1; i <= RUN_CNT; i++) {
        selectAllRecTimeTaken = 0;
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            uint32_t f3Value = ulLoop + 11;
            memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
            memset(pstBuff.StdBuf.pucData, 0, pstBuff.StdBuf.ulBufLen);
            startTime = DbGetNsec();
            ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
            endTime = DbGetNsec();
            selectAllRecTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT((VOS_UINT32)1, pstBuff.usRecNum);
        }
        selectAllRecTimeTakenAll += selectAllRecTimeTaken;
    }
    TEST_V1_FREE(pstBuff.StdBuf.pucData);
    TestPerfPrintf(PERF_TESTCASE_002, TRANS2_MS(selectAllRecTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  先在RDB插入2W条数据，单条删除2W次
TEST_F(SimprelPerf, perfTestAdapter4v1DeleteRec_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "deleteRec";
    uint32_t ulDbId = 0xFFFFFFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    char tableName[] = "deleteRec";
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;

    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = astIdx[0].aucFieldID[0];
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    uint32_t pulRecNum = 0;
    DB_ERR_CODE ret;
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 3]\n"
        "[Delete %d records from a table (one record in a loop)]\n",
        CYCLES_20K);
    PrintLineLn();

    uint16_t i;
    for (i = 1; i <= RUN_CNT; i++) {
        dTimeTaken = 0;
        for (uint16_t x = 0; x < CYCLES_20K / 200; x++) {
            uint16_t usRelId = 0;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
            DB_DSBUF_STRU stBuff = {0};
            CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
            char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
            for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
                CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
                stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
            }

            for (ulLoop = 0; ulLoop < 20; ulLoop++) {
                int32_t f3Value = x * 100 + ulLoop + 11;
                memcpy(pstCond.aCond[0].aucValue, &f3Value, astFlds[3].usSize);
                startTime = DbGetNsec();
                ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
                endTime = DbGetNsec();
                dTimeTaken += endTime - startTime;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, (int32_t)pulRecNum);
            }
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropTbl(ulDbId, usRelId, 1));
            TEST_V1_FREE(value);
        }
        dTimeTakenAll += dTimeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_003, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 业务表, 9字段1索引，写入数据2w,索引匹配删除操作100*（begin+delete20次+commit）的时间
TEST_F(SimprelPerf, perfTestAdapter4v1DeleteRecWithCDB_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "deleteRec";
    uint32_t ulDbId = 0xFFFFFFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    char tableName[] = "deleteRec";
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;

    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    uint64_t beginCdbTime = 0;
    uint64_t beginCdbTimeAll = 0;
    uint64_t commitCdbTime = 0;
    uint64_t commitCdbTimeAll = 0;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = astIdx[0].aucFieldID[0];
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    uint32_t pulRecNum = 0;
    uint32_t cdbId = 0;
    DB_ERR_CODE ret;
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 4]\n"
        "[Delete %d records from a table with CDB(one record in a loop)]\n",
        CYCLES_20K);
    PrintLineLn();

    DB_DSBUF_STRU stBuff = {0};
    CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    uint16_t i;
    for (i = 1; i <= RUN_CNT; i++) {
        dTimeTaken = 0;
        for (int x = 0; x < 100; x++) {
            uint16_t usRelId = 0;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
            for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
                CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
                stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
            }
            ret = TPC_BeginCDB(ulDbId, &cdbId);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

            for (ulLoop = 0; ulLoop < 20; ulLoop++) {
                int32_t f3Value = x * 100 + ulLoop + 11;
                memcpy(pstCond.aCond[0].aucValue, &f3Value, astFlds[3].usSize);
                startTime = DbGetNsec();
                ret = TPC_DeleteRec(cdbId, ulDbId, usRelId, &pstCond, &pulRecNum);
                endTime = DbGetNsec();
                dTimeTaken += endTime - startTime;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1, (int32_t)pulRecNum);
            }

            ret = TPC_CommitCDB(cdbId);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropTbl(ulDbId, usRelId, 1));
        }
        dTimeTakenAll += dTimeTaken;
    }
    TEST_V1_FREE(value);
    TestPerfPrintf(PERF_TESTCASE_004, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全表扫描数据删除，一次删除2W条数据
TEST_F(SimprelPerf, perfTestAdapter4v1DeleteAllRecOneTime_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "deleteAllRec";
    uint32_t ulDbId = 0xFFFFFFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    char tableName[] = "tblDeleteAllRec";
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;
    uint16_t usRelId = 0;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));

    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = astIdx[0].aucFieldID[0];
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    int32_t f3Value = 0;
    memcpy(pstCond.aCond[0].aucValue, &f3Value, astFlds[3].usSize);

    uint32_t pulRecNum = 0;
    DB_ERR_CODE ret;
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 5]\n"
        "[Delete %d records from a table (one record in a loop)]\n",
        CYCLES_20K);
    PrintLineLn();

    DB_DSBUF_STRU stBuff = {0};
    CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    uint16_t i;
    for (i = 1; i <= RUN_CNT; i++) {
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
            stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
        }

        dTimeTaken = 0;
        startTime = DbGetNsec();
        ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pulRecNum);
        endTime = DbGetNsec();
        dTimeTaken += endTime - startTime;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(CYCLES_20K, (int32_t)pulRecNum);
        DB_RELATION_INFO relInfo = {0};
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_GetTblInfo(ulDbId, usRelId, &relInfo));
        V1_AW_MACRO_ASSERT_EQ_INT(0, (int32_t)relInfo.ulActualRecNum);
        dTimeTakenAll += dTimeTaken;
    }
    TEST_V1_FREE(value);
    TestPerfPrintf(PERF_TESTCASE_005, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 测试单条插数据2W条 —— 9 flds，1 idx
TEST_F(SimprelPerf, perfTestAdapter4v1InsertRec_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "testPerfInsert";
    uint32_t ulDbId = 0xFFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.pstFldLst = astFlds;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstIdxLst = astIdx;

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 6]\n"
        "[Insert into a table %d records]\n",
        CYCLES_20K);
    PrintLineLn();
    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    uint16_t i;
    DB_ERR_CODE ret;
    uint16_t usRelId = 0;
    char tableName[DB_NAME_LEN];
    for (i = 1; i <= RUN_CNT; i++) {
        sprintf((char *)tableName, "insertTbl%d", i);
        strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));

        DB_DSBUF_STRU stBuff = {0};
        CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
        char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
        dTimeTaken = 0;
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
            stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
            startTime = DbGetNsec();
            ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff);
            endTime = DbGetNsec();
            dTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        TEST_V1_FREE(value);

        dTimeTakenAll += dTimeTaken;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropTbl(ulDbId, usRelId, 1));
    }
    TestPerfPrintf(PERF_TESTCASE_006, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 测试开启CDB单插数据2W条 —— 9 flds，1 idx
TEST_F(SimprelPerf, perfTestAdapter4v1InsertRecCdb_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "testPerfInsert";
    uint32_t ulDbId = 0xFFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 7]\n"
        "[Insert into a table %d records with cdb]\n",
        CYCLES_20K);
    PrintLineLn();
    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t iTimeTaken = 0;
    uint64_t iTimeTakenAll = 0;

    uint16_t i;
    DB_ERR_CODE ret;
    uint16_t usRelId = 0;
    char tableName[DB_NAME_LEN];
    for (i = 1; i <= RUN_CNT; i++) {
        sprintf((char *)tableName, "testTbl%d", i);
        strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
        iTimeTakenAll = 0;
        uint32_t cdbId = 0;
        ret = TPC_BeginCDB(ulDbId, &cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        DB_DSBUF_STRU stBuff = {0};
        CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
        char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
            stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
            startTime = DbGetNsec();
            ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &stBuff);
            endTime = DbGetNsec();
            iTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        TEST_V1_FREE(value);
        ret = TPC_CommitCDB(cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        iTimeTakenAll += iTimeTaken;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropTbl(ulDbId, usRelId, 1));
    }
    TestPerfPrintf(PERF_TESTCASE_007, TRANS2_MS(iTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// local 索引等值查询2W次
TEST_F(SimprelPerf, perfTestAdapter4v1SelectRecWithIdx_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "withIdxDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    uint16_t usRelId;
    char tableName[] = "withIdxTbl";
    CreateTblAndSetData4Select(tableName, ulDbId, &usRelId, CYCLES_20K);
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 8]\n"
        "[Select %d records from a table with index]\n",
        CYCLES_20K);
    PrintLineLn();
    uint64_t startTime;
    uint64_t endTime;
    uint16_t i = 0;
    uint32_t ulLoop = 0;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    DB_ERR_CODE ret;

    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU stCond = {0};
    stCond.usCondNum = 1;
    stCond.aCond[0].ucFieldId = 3;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    DB_BUF_STRU pstBuff = {0};
    pstBuff.ulRecNum = DB_SELECT_ALL;
    pstBuff.usRecLen = 1000;
    pstBuff.ulBufLen = 1000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.usRecLen);

    for (i = 1; i <= RUN_CNT; i++) {
        dTimeTaken = 0;
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            memset(pstBuff.pBuf, 0, pstBuff.usRecLen);
            uint32_t f3Value = ulLoop + 11;
            memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
            startTime = DbGetNsec();
            ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
            endTime = DbGetNsec();
            dTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT((VOS_UINT32)1, pstBuff.ulRecNum);
        }
        dTimeTakenAll += dTimeTaken;
    }
    TEST_V1_FREE(pstBuff.pBuf);
    TestPerfPrintf(PERF_TESTCASE_008, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  插2W条数据，按索引范围查询2W次(条件：F3 <= 60，符合条件50条)
TEST_F(SimprelPerf, perfTestAdapter4v1SelectRecByOrderWithIdx_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "withIdxDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    uint16_t usRelId;
    char tableName[] = "withIdxTbl";
    CreateTblAndSetData4Select(tableName, ulDbId, &usRelId, CYCLES_20K);
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 9]\n"
        "[Select %d times by order with index]\n",
        CYCLES_20K);
    PrintLineLn();
    uint64_t startTime;
    uint64_t endTime;
    uint16_t i = 0;
    uint32_t ulLoop = 0;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    DB_ERR_CODE ret;

    DB_SORT_STRU pstSort;
    uint8_t ucFld = 1;
    pstSort.enSortType = DB_SORTTYPE_DESCEND;
    pstSort.ucSortNum = 1;
    pstSort.pSortFields = &ucFld;
    pstSort.pSortFields[0] = 3;
    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU stCond = {0};
    stCond.usCondNum = 1;
    stCond.aCond[0].ucFieldId = 3;
    stCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    DB_DSBUF_STRU pstBuff;
    pstBuff.usRecNum = 50;
    pstBuff.StdBuf.ulBufLen = 5000;
    pstBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.StdBuf.ulBufLen);

    for (i = 1; i <= RUN_CNT; i++) {
        dTimeTaken = 0;
        uint32_t f3Value = 60;
        memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
        for (ulLoop = 0; ulLoop < CYCLES_20K; ulLoop++) {
            memset(pstBuff.StdBuf.pucData, 0, pstBuff.StdBuf.ulBufLen);
            startTime = DbGetNsec();
            ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &pstBuff);
            endTime = DbGetNsec();
            dTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT((uint32_t)(f3Value - 10), pstBuff.usRecNum);
        }
        dTimeTakenAll += dTimeTaken;
    }
    TEST_V1_FREE(pstBuff.StdBuf.pucData);
    TestPerfPrintf(PERF_TESTCASE_009, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 先插入2W条数据，单条更新2W次
TEST_F(SimprelPerf, perfTestAdapter4v1UpdateRec_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "updateRec";
    uint32_t ulDbId = 0xFFFFFFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    char tableName[] = "updateRec";
    uint16_t usRelId = 0;
    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.pstFldLst = astFlds;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstIdxLst = astIdx;

    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    uint16_t i = 0;
    DB_ERR_CODE ret;
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 10]\n"
        "[Update %d records from a table (one record in a loop)]\n",
        CYCLES_20K);
    PrintLineLn();

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    uint32_t pulRecNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    for (i = 1; i <= RUN_CNT; i++) {
        dTimeTaken = 0;
        for (uint16_t x = 0; x < CYCLES_20K / 200; x++) {
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
            DB_DSBUF_STRU stBuff = {0};
            CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
            char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
            for (ulLoop = 0; ulLoop < 2000; ulLoop++) {
                CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
                stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
            }
            TEST_V1_FREE(value);

            char *updateBuffer = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
            for (ulLoop = 0; ulLoop < 20; ulLoop++) {
                int32_t f3Value = ulLoop + 11;
                memcpy(pstCond.aCond[0].aucValue, &f3Value, astFlds[3].usSize);
                CommonSetDataBuf4Insert(updateBuffer, astFlds, ulLoop, stBuff.usRecLen, true);
                stBuff.StdBuf.pucData = (VOS_UINT8 *)updateBuffer;
                stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
                pulRecNum = 0;
                startTime = DbGetNsec();
                ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &pstFldFilter, &stBuff, &pulRecNum);
                endTime = DbGetNsec();
                dTimeTaken += endTime - startTime;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1u, pulRecNum);
            }
            TEST_V1_FREE(updateBuffer);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropTbl(ulDbId, usRelId, 1));
        }
        dTimeTakenAll += dTimeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_010, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// RDB插入2W条数据，开启CDB，一次一条更新2W次，提交CDB
TEST_F(SimprelPerf, perfTestAdapter4v1UpdateRecCdb_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "updateRec";
    uint32_t ulDbId = 0xFFFFFFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    uint16_t usRelId = 0;
    char tableName[] = "updateRecCDB";
    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = CYCLES_20K;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;

    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t dTimeTaken = 0;
    uint64_t dTimeTakenAll = 0;
    uint16_t i = 0;
    uint32_t cdbId = 0;
    DB_ERR_CODE ret;
    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 11]\n"
        "[Update cbd %d records from a table (one record in a loop)]\n",
        CYCLES_20K);
    PrintLineLn();

    uint32_t pulRecNum;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    for (i = 1; i <= RUN_CNT; i++) {
        dTimeTaken = 0;
        for (uint16_t x = 0; x < CYCLES_20K / 200; x++) {
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
            DB_DSBUF_STRU stBuff = {0};
            CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
            char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
            for (ulLoop = 0; ulLoop < 2000; ulLoop++) {
                CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
                stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stBuff));
            }
            TEST_V1_FREE(value);

            char *updateBuffer = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
            ret = TPC_BeginCDB(ulDbId, &cdbId);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

            for (ulLoop = 0; ulLoop < 20; ulLoop++) {
                int32_t f3Value = ulLoop + 11;
                memcpy(pstCond.aCond[0].aucValue, &f3Value, astFlds[3].usSize);
                CommonSetDataBuf4Insert(updateBuffer, astFlds, ulLoop, stBuff.usRecLen, true);
                stBuff.StdBuf.pucData = (VOS_UINT8 *)updateBuffer;
                pulRecNum = 0;
                startTime = DbGetNsec();
                ret = TPC_UpdateRec(cdbId, ulDbId, usRelId, &pstCond, &pstFldFilter, &stBuff, &pulRecNum);
                endTime = DbGetNsec();
                dTimeTaken += endTime - startTime;
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                V1_AW_MACRO_ASSERT_EQ_INT(1u, pulRecNum);
            }

            ret = TPC_CommitCDB(cdbId);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            TEST_V1_FREE(updateBuffer);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropTbl(ulDbId, usRelId, 1));
        }
        dTimeTakenAll += dTimeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_011, TRANS2_MS(dTimeTakenAll / (i - 1)));

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
测试base_config，36号表查询
    Scen 1：1个或若干个非索引列上的等值条件，走全表扫，返回部分列；

    [全表扫描] Scen 1
    Select 1~9 from tbl_36 where 2=xxx;
    Select 1~9 from tbl_36 where 2=xxx, 9=xxx (√)
*/
TEST_F(SimprelPerf, perfTestAdapter4v1_Tbl0036_Select_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "base_config";
    uint32_t ulDbId = 0xFFFF;
    CommCreateDB4Test(dbName, &ulDbId);
    // 构造表的列属性，索引的元信息
    const uint32_t fieldNum = 14;
    const uint32_t indexNum = 2;
    DB_FIELD_DEF_STRU astFlds[fieldNum];
    DB_INDEX_DEF_STRU astIdxs[indexNum];
    ConstructFldsAndIdxs4Tbl36(astFlds, astIdxs);
    // 填充表的结构化信息
    uint16_t usRelId = 0;
    char tableName[] = "OsNode";
    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 50000;
    stRelDef.ulMaxSize = 50000;
    stRelDef.ulNCols = fieldNum;
    stRelDef.ulNIdxs = indexNum;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdxs;

    // 建表
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));
    // 构造并插入初始数据
    char *value = (char *)TEST_V1_MALLOC(100);  // 此处写法单行记录长度不能超过100
    const uint32_t insertNum = CYCLES_20K;
    GenAndInsertData4Tbl36(astFlds, ulDbId, usRelId, insertNum, value);
    TEST_V1_FREE(value);

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO WITHOUT CDB]\n"
        "[Create table 36 and Insert %d records]\n",
        insertNum);
    PrintLineLn();

    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = 9;
    for (int i = 0; i < stFldFilter.ucFieldNum; i++) {
        stFldFilter.aucField[i] = i + 1;
    }
    DB_COND_STRU stCond = {0};
    stCond.usCondNum = 2;
    stCond.aCond[0].ucFieldId = 2;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[1].ucFieldId = 9;
    stCond.aCond[1].enOp = DB_OP_EQUAL;
    DB_BUF_STRU pstBuff = {0};
    pstBuff.ulRecNum = 200;  // 返回实际返回数据条数
    pstBuff.ulBufLen = 2000;
    pstBuff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.ulBufLen);

    DB_ERR_CODE ret;
    uint32_t ulLoop, i;
    uint64_t startTime, endTime;
    uint64_t dTimeTaken = 0, dTimeTakenAll = 0;
    // ----------------------------- Scen 1：非索引字段等值查询 ---------------------------------
    const uint32_t outLoop = RUN_CNT;
    const uint32_t exetimes = 200;
    uint32_t caseX = 1;
    for (i = 1; i <= outLoop; i++) {
        dTimeTaken = 0;
        for (ulLoop = 0; ulLoop < exetimes; ulLoop++) {
            pstBuff.ulRecNum = 200;
            memset(pstBuff.pBuf, 0, pstBuff.ulBufLen);
            // 指定f9列上条件的值
            uint32_t f9Value = (uint32_t)(ulLoop / 20);
            memcpy_s(stCond.aCond[1].aucValue, sizeof(uint32_t), &f9Value, sizeof(uint32_t));
            // 指定f2列上条件的值
            char f2Value[31];
            sprintf(f2Value, "string_%d", f9Value);
            memcpy_s(stCond.aCond[0].aucValue, strlen(f2Value), &f2Value, strlen(f2Value));
            // 统计查询时间
            startTime = DbGetNsec();
            ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
            endTime = DbGetNsec();
            dTimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            // 校验返回记录条数
            V1_AW_MACRO_ASSERT_EQ_INT((VOS_UINT32)20, pstBuff.ulRecNum);
        }
        dTimeTakenAll += dTimeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_012, TRANS2_MS(dTimeTakenAll / outLoop));

    TEST_V1_FREE(pstBuff.pBuf);
    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
    说明：base_config 1746号表，9个字段，2个索引
    索引 ：{ {1,2}，{5} }
    原始表导入23055条数据，查询条件 
    (F1 =, F2 >=)
    按 {1， 2} 号字段升序排列
    结果获取 1-4 号共4个字段
    结果查询到9条
*/
TEST_F(SimprelPerf, testRestoreTbl1746_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableReleaseDevice=1\"");
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucDbName = (VOS_UINT8 *)"testImport";
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./data/tbl1746.txt";
    sysviewArgs.importType = DB_RESTORETYPE_DISCARD;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_Sysview(DB_TPC_SYSVIEW_IMPORT_TXT, &sysviewArgs, &result));
    VOS_UINT8 *dbName = (VOS_UINT8 *)"testImport";
    uint32_t ulDbId;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, dbName, &ulDbId));

    uint16_t usRelId;
    DB_ERR_CODE ret = TPC_GetTblId(ulDbId, (VOS_UINT8 *)"LogParaTbl", &usRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond.aCond[0].aucValue = 152047634;
    pstCond.aCond[1].ucFieldId = 2;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)pstCond.aCond[1].aucValue = 1;

    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = 4;
    for (uint32_t i = 0; i < stFldFilter.ucFieldNum; i++) {
        stFldFilter.aucField[i] = i + 1;
    }

    DB_DSBUF_STRU queryBuff = {0};
    queryBuff.usRecNum = 0xFFFF;
    queryBuff.StdBuf.ulBufLen = 1000000;
    queryBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(queryBuff.StdBuf.ulBufLen);

    DB_SORT_STRU pstSort;
    uint8_t ucFld[2] = {1, 2};
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.ucSortNum = 2;
    pstSort.pSortFields = ucFld;

    uint64_t startTime;
    uint64_t dTimeTakenAll = 0;
    uint32_t outLoop;
    for (outLoop = 1; outLoop <= RUN_CNT; outLoop++) {
        for (uint32_t i = 0; i < 20000; i++) {
            memset(queryBuff.StdBuf.pucData, 0, queryBuff.StdBuf.ulBufLen);
            startTime = DbGetNsec();
            ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &pstCond, &stFldFilter, &queryBuff);
            dTimeTakenAll += (DbGetNsec() - startTime);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TestPerfPrintf(PERF_TESTCASE_013, TRANS2_MS(dTimeTakenAll / (outLoop - 1)));

    TEST_V1_FREE(queryBuff.StdBuf.pucData);
    CommonDropDB4Test(ulDbId, dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 1283号表，TPC_InsertRec接口，全等值查询条件操作次数最多的表，进行了有5w+操作
TEST_F(SimprelPerf, perfEqTestRestoreTbl1283_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucDbName = (VOS_UINT8 *)"testImport";
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./data/tbl1283.txt";
    sysviewArgs.importType = DB_RESTORETYPE_DISCARD;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_Sysview(DB_TPC_SYSVIEW_IMPORT_TXT, &sysviewArgs, &result));
    VOS_UINT8 *dbName = (VOS_UINT8 *)"testImport";
    uint32_t ulDbId;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, dbName, &ulDbId));

    uint16_t usRelId;
    DB_ERR_CODE ret = TPC_GetTblId(ulDbId, (VOS_UINT8 *)"SSItemCfg", &usRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    // 表中No.3数据
    *(uint32_t *)pstCond.aCond[0].aucValue = 136871938;

    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = 1;
    stFldFilter.aucField[0] = 3;

    DB_DSBUF_STRU queryBuff = {0};
    queryBuff.usRecNum = 0xFFFF;
    queryBuff.StdBuf.ulBufLen = 100;
    queryBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(queryBuff.StdBuf.ulBufLen);
    uint64_t nsecS;
    uint64_t nsecE;
    uint64_t nsecAll = 0;
    PrintLineLn();
    fprintf(stdout, "[Perf test equal cond select tbl1283 %d times]\n", CYCLES_20K);
    PrintLineLn();
    uint32_t outLoop;
    for (outLoop = 1; outLoop <= RUN_CNT; outLoop++) {
        nsecE = 0;
        for (uint32_t inLoop = 0; inLoop < CYCLES_20K; inLoop++) {
            nsecS = DbGetNsec();
            ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstCond, &stFldFilter, &queryBuff);
            nsecE += (DbGetNsec() - nsecS);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT((VOS_UINT32)1, queryBuff.usRecNum);
        }
        nsecAll += nsecE;
    }
    TestPerfPrintf(PERF_TESTCASE_014, TRANS2_MS(nsecAll) / (outLoop - 1));

    TEST_V1_FREE(queryBuff.StdBuf.pucData);
    CommonDropDB4Test(ulDbId, dbName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 测试等值查询1904号表等值查询性能
TEST_F(SimprelPerf, perfComplexTestRestoreTbl1904_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucDbName = (VOS_UINT8 *)"testImport";
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./data/tbl1904.txt";
    sysviewArgs.importType = DB_RESTORETYPE_DISCARD;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_Sysview(DB_TPC_SYSVIEW_IMPORT_TXT, &sysviewArgs, &result));
    VOS_UINT8 *dbName = (VOS_UINT8 *)"testImport";
    uint32_t ulDbId;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, dbName, &ulDbId));

    uint16_t usRelId;
    ret = TPC_GetTblId(ulDbId, (VOS_UINT8 *)"GreTunnel", &usRelId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    const uint32_t propNum = 38;
    DB_FIELD_INFO fldsInfo[propNum];
    ret = TPC_GetTblColInfo(ulDbId, usRelId, fldsInfo, sizeof(DB_FIELD_INFO) * propNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint32_t recNum = 0;
    DBS_GetRelActRec(ulDbId, usRelId, &recNum);
    fprintf(stdout, "rec num is %u\n", recNum);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 6;
    pstCond.aCond[0].ucFieldId = 1;  // uint32_t
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)pstCond.aCond[0].aucValue = 0;
    pstCond.aCond[1].ucFieldId = 2;  // uint32_t
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)pstCond.aCond[1].aucValue = 0;
    pstCond.aCond[2].ucFieldId = 11;  // uint32_t
    pstCond.aCond[2].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)pstCond.aCond[2].aucValue = 0;
    pstCond.aCond[3].ucFieldId = 3;  // uint32_t
    pstCond.aCond[3].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond.aCond[3].aucValue = 35;
    pstCond.aCond[4].ucFieldId = 5;  // uint32_t
    pstCond.aCond[4].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond.aCond[4].aucValue = 0;
    pstCond.aCond[5].ucFieldId = 13;  // uint32_t
    pstCond.aCond[5].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond.aCond[5].aucValue = 1;
    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = 33;
    for (uint32_t i = 0; i < stFldFilter.ucFieldNum; i++) {
        stFldFilter.aucField[i] = i + 1;
    }

    VOS_UINT8 sortFlds[3] = {1, 2, 11};
    DB_SORT_STRU pstSort;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.ucSortNum = 3;
    pstSort.pSortFields = sortFlds;

    DB_DSBUF_STRU queryBuff = {0};
    queryBuff.usRecNum = 0xFFFF;
    queryBuff.StdBuf.ulBufLen = 5000;
    queryBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(queryBuff.StdBuf.ulBufLen);
    uint64_t nsecS;
    uint64_t nsecE;
    uint64_t nsecAll = 0;
    PrintLineLn();
    fprintf(stdout, "[Perf test complex cond select tbl1904 %d times]\n", 10000);
    PrintLineLn();
    uint32_t outLoop;
    for (outLoop = 1; outLoop <= RUN_CNT; outLoop++) {
        nsecE = 0;
        for (uint32_t inLoop = 0; inLoop < 10000; inLoop++) {
            nsecS = DbGetNsec();
            ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstSort, &pstCond,
                &stFldFilter, &queryBuff);
            nsecE += (DbGetNsec() - nsecS);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT((VOS_UINT32)9, queryBuff.usRecNum);
        }
        nsecAll += nsecE;
    }
    TestPerfPrintf(PERF_TESTCASE_015, TRANS2_MS(nsecAll) / (outLoop - 1));

    TEST_V1_FREE(queryBuff.StdBuf.pucData);
    CommonDropDB4Test(ulDbId, dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// ./data/master.txt转为二进制后导入的稳态内存和峰值内存
TEST_F(SimprelPerf, testImportMasterMem_016_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableReleaseDevice=1\"");
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucDbName = (VOS_UINT8 *)"testImport";
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./data/master.txt";
    sysviewArgs.importType = DB_RESTORETYPE_DISCARD;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_Sysview(DB_TPC_SYSVIEW_IMPORT_TXT, &sysviewArgs, &result));

    VOS_UINT8 *dbName = (VOS_UINT8 *)"testImport";
    uint32_t ulDbId;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, dbName, &ulDbId));

    // 导出为二进制
    char exportFilePath[] = "./V5MasterData.txt";
    ret = TPC_BkpPhy(ulDbId, (uint8_t *)exportFilePath);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t usRelId1;
    ret = TPC_GetTblId(ulDbId, (VOS_UINT8 *)"userdatatype", &usRelId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t usRelId2;
    ret = TPC_GetTblId(ulDbId, (VOS_UINT8 *)"classinfo", &usRelId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    CommonDropDB4Test(ulDbId, dbName);

    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = TPC_Restore((uint8_t *)exportFilePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取内存信息
    uint32_t vmRss = 0;
    uint32_t vmHwm = 0;
    TestGetMemInfo(getpid(), &vmRss, &vmHwm);
    TestMemPrintf(PERF_TESTCASE_016_001, vmRss);
    TestMemPrintf(PERF_TESTCASE_016_002, vmHwm);

    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, dbName, &ulDbId));
    CommonDropDB4Test(ulDbId, dbName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// ./data/master.txt导入后的稳态内存和峰值内存
TEST_F(SimprelPerf, testImportMasterMem_016_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    VOS_UINT8 *dbName = (VOS_UINT8 *)"testImport";
    char exportFilePath[] = "./V5MasterData.txt";
    uint32_t ulDbId;

    // 导入数据
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    ret = TPC_Restore((uint8_t *)exportFilePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 打开数据库
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &ulDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    CommonDropDB4Test(ulDbId, dbName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 无数据查询/删除/更新
TEST_F(SimprelPerf, testNoDataOpt_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 17]\n"
        "[Select update delete from a no data table]\n");
    PrintLineLn();

    char dbName[] = "withIdxDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    uint16_t usRelId;
    char tableName[] = "withIdxTbl";
    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = 100;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));

    uint64_t startTime;
    uint64_t endTime;
    uint64_t timeTaken = 0;
    uint64_t timeTakenAll = 0;
    uint16_t i = 0;
    uint32_t ulLoop = 0;
    DB_ERR_CODE ret;

    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU stCond = {0};
    stCond.usCondNum = 1;
    stCond.aCond[0].ucFieldId = 3;
    stCond.aCond[0].enOp = DB_OP_EQUAL;

    DB_DSBUF_STRU pstBuff;
    CommonInitDsBufStru(&pstBuff, astFlds, stRelDef.ulNCols);
    pstBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.StdBuf.ulBufLen);
    if (pstBuff.StdBuf.pucData == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(-1, DB_SUCCESS_V1);
    }

    // 查询
    timeTakenAll = 0;
    for (i = 1; i <= 10; i++) {
        timeTaken = 0;
        for (ulLoop = 0; ulLoop < 20000; ulLoop++) {
            uint32_t f3Value = ulLoop + 11;
            memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
            memset(pstBuff.StdBuf.pucData, 0x00, pstBuff.StdBuf.ulBufLen);
            startTime = DbGetNsec();
            ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff);
            endTime = DbGetNsec();
            timeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
        }
        timeTakenAll += timeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_017_001, TRANS2_MS(timeTakenAll / (i - 1)));

    // 更新
    uint32_t pulRecNum = 0;
    timeTakenAll = 0;
    for (i = 1; i <= RUN_CNT; i++) {
        timeTaken = 0;
        for (ulLoop = 0; ulLoop < 20000; ulLoop++) {
            uint32_t f3Value = ulLoop + 11;
            memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
            memset(pstBuff.StdBuf.pucData, 0x00, pstBuff.StdBuf.ulBufLen);
            CommonSetDataBuf4Insert((char *)pstBuff.StdBuf.pucData, astFlds, ulLoop, pstBuff.usRecLen, true);
            pulRecNum = 0;
            startTime = DbGetNsec();
            ret = TPC_UpdateRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &stFldFilter, &pstBuff, &pulRecNum);
            endTime = DbGetNsec();
            timeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(0, pulRecNum);
        }
        timeTakenAll += timeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_017_002, TRANS2_MS(timeTakenAll / (i - 1)));

    // 删除
    timeTakenAll = 0;
    for (i = 1; i <= RUN_CNT; i++) {
        timeTaken = 0;
        for (ulLoop = 0; ulLoop < 20000; ulLoop++) {
            uint32_t f3Value = ulLoop + 11;
            memcpy_s(stCond.aCond[0].aucValue, sizeof(uint32_t), &f3Value, sizeof(uint32_t));
            pulRecNum = 0;
            startTime = DbGetNsec();
            ret = TPC_DeleteRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &stCond, &pulRecNum);
            endTime = DbGetNsec();
            timeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            V1_AW_MACRO_ASSERT_EQ_INT(0, pulRecNum);
        }
        timeTakenAll += timeTaken;
    }
    TestPerfPrintf(PERF_TESTCASE_017_003, TRANS2_MS(timeTakenAll / (i - 1)));

    TEST_V1_FREE(pstBuff.StdBuf.pucData);
    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 构想业务场景，undo优化
TEST_F(SimprelPerf, testUndo_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 18]\n"
        "[undo optimization test]\n");
    PrintLineLn();

    DB_ERR_CODE ret;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t timeTaken = 0;
    uint64_t timeTakenAll = 0;
    uint16_t i = 0;
    uint32_t ulLoop = 0;

    char dbName[] = "PerfDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    uint16_t usRelId1;
    DB_REL_DEF_STRU stRelDef1;
    ret = TestTPC_CreateTbl(ulDbId, "schemaFile/classinfo.json", &usRelId1, &stRelDef1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 tblRecLen1;
    ret = TestGetTblRecLen(ulDbId, usRelId1, &tblRecLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstBuff1;
    CommonInitDsBufStru(&pstBuff1, stRelDef1.pstFldLst, stRelDef1.ulNCols);
    pstBuff1.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff1.StdBuf.ulBufLen);
    if (pstBuff1.StdBuf.pucData == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(-1, DB_SUCCESS_V1);
    }

    // 插入数据
    memset(pstBuff1.StdBuf.pucData, 0x00, pstBuff1.StdBuf.ulBufLen);
    TestSetDataToClassInfoTblRecBuff(pstBuff1.StdBuf.pucData, &stRelDef1, 1);
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId1, &pstBuff1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启多个cdb
    uint32_t cdbId[2];
    for (ulLoop = 0; ulLoop < sizeof(cdbId) / sizeof(uint32_t); ulLoop++) {
        ret = TPC_BeginCDB(ulDbId, &cdbId[ulLoop]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 多cdb循环更新所有数据
    uint32_t pulRecNum;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 更新同一条数据
    *(uint32_t *)pstCond.aCond[0].aucValue = 1;

    timeTakenAll = 0;
    uint32_t vmRss = 0;
    uint32_t vmHwm = 0;
    uint32_t vmRssLast = 0;
    int32_t vmRssDiff = 0;
    uint32_t vmRssDiffAll = 0;
    uint32_t vmRssIncCnt = 0;
    for (i = 1; i <= 10; i++) {
        timeTaken = 0;
        for (ulLoop = 0; ulLoop < 1000; ulLoop++) {
            memset(pstBuff1.StdBuf.pucData, 0x00, pstBuff1.StdBuf.ulBufLen);
            TestSetDataToClassInfoTblRecBuff(pstBuff1.StdBuf.pucData, &stRelDef1, ulLoop + 2);
            *(uint32_t *)pstBuff1.StdBuf.pucData = 1;
            pulRecNum = 0;
            startTime = DbGetNsec();
            ret = TPC_UpdateRec(cdbId[0], ulDbId, usRelId1, &pstCond, &pstFldFilter, &pstBuff1, &pulRecNum);
            ret = TPC_UpdateRec(cdbId[1], ulDbId, usRelId1, &pstCond, &pstFldFilter, &pstBuff1, &pulRecNum);
            endTime = DbGetNsec();
            timeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        timeTakenAll += timeTaken;
        fprintf(stdout, "[%u] ---> %.2f ms\n", i, TRANS2_MS(timeTaken));
        fflush(stdout);

        // 获取内存信息
        vmRss = 0;
        vmHwm = 0;
        TestGetMemInfo(getpid(), &vmRss, &vmHwm);
        fprintf(stdout, "[%u] ---> vmRss: %u Kb\n", i, vmRss);
        fflush(stdout);
        if (i > 1) {
            vmRssDiff = vmRss - vmRssLast;
            fprintf(stdout, "[%u] ---> vmRssDiff: %d Kb\n", i, vmRssDiff);
            fflush(stdout);
            vmRssDiffAll += vmRssDiff;
            vmRssIncCnt++;
        }
        vmRssLast = vmRss;
    }
    TestPerfPrintf(PERF_TESTCASE_018_001, TRANS2_MS(timeTakenAll / (i - 1)));
    TestMemPrintf(PERF_TESTCASE_018_002, vmRssDiffAll / vmRssIncCnt, 1.00);

    // 提交多个cdb
    for (ulLoop = 0; ulLoop < sizeof(cdbId) / sizeof(uint32_t); ulLoop++) {
        ret = TPC_CommitCDB(cdbId[ulLoop]);
        if (ret != DB_SUCCESS_V1) {
            TPC_RollbackCDB(cdbId[ulLoop]);
        }
    }

    TEST_V1_FREE(pstBuff1.StdBuf.pucData);
    TestFreeTblStructDef(&stRelDef1);
    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 编译优化，编译，观察自身动态库大小（libgmdb*）（5%）
TEST_F(SimprelPerf, testGmdbSoSize_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 19]\n"
        "[build v1 gmdb so size test]\n");
    PrintLineLn();

    bool isGetGmdbHomePathSucc = false;
    char *gmdbHome = getenv("GMDB_HOME");
    if (gmdbHome == NULL) {
        TestSoSizePrintf(PERF_TESTCASE_019_001, 100000);
        V1_AW_MACRO_ASSERT_EQ_INT(-1, DB_SUCCESS_V1);
    } else {
        AW_FUN_Log(LOG_INFO, "gmdbHome: %s", gmdbHome);
        struct stat fileStat;
        char soPath[1024] = {0};
        (void)sprintf_s(soPath, sizeof(soPath), "%s/output/euler/aarch64/lib/libgmdbEmbed.so.5.1", gmdbHome);
        if (access(soPath, F_OK) !=0) {
            AW_FUN_Log(LOG_INFO, "libgmdbEmbedSoPath: %s not exist, set size = 100000", soPath);
            TestSoSizePrintf(PERF_TESTCASE_019_001, 100000);
            V1_AW_MACRO_ASSERT_EQ_INT(-1, DB_SUCCESS_V1);
        } else {
            stat(soPath, &fileStat);
            AW_FUN_Log(LOG_INFO, "libgmdbEmbedSoPath: %s, libgmdbEmbedSoSize: %lu", soPath, fileStat.st_size);
            TestSoSizePrintf(PERF_TESTCASE_019_001, fileStat.st_size / 1024);
        }
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 内存底噪，TPC初始化后的底噪内存看护
TEST_F(SimprelPerf, testAfterInitProcMem_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 20]\n"
        "[after TPC_Init mem test]\n");
    PrintLineLn();

    // 获取内存信息
    uint32_t vmRss = 0;
    uint32_t vmHwm = 0;
    TestGetMemInfo(getpid(), &vmRss, &vmHwm);
    TestMemPrintf(PERF_TESTCASE_020_001, vmRss, 1.10);
    TestMemPrintf(PERF_TESTCASE_020_002, vmHwm, 1.10);
    V1_AW_MACRO_EXPECT_GT_INT(vmRss, 0);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 回收验证，CDB写，rollback，预期内存回落，涨幅不大可解释
TEST_F(SimprelPerf, testCdbWriteThenRollbackProcMem_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    PrintLineLn();
    fprintf(stdout,
        "[SINGLE THREAD PERF SCENARIO 21]\n"
        "[cdb write and rollback mem test]\n");
    PrintLineLn();

    char dbName[] = "withIdxDB";
    uint32_t ulDbId = 0XFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    uint16_t usRelId;
    char tableName[] = "withIdxTbl";
    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = 1000000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId));

    uint64_t startTime;
    uint64_t endTime;
    uint64_t timeTaken = 0;
    uint64_t timeTakenAll = 0;
    uint16_t i = 0;
    uint32_t ulLoop = 0;
    DB_ERR_CODE ret;

    DB_DSBUF_STRU pstBuff;
    CommonInitDsBufStru(&pstBuff, astFlds, stRelDef.ulNCols);
    pstBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.StdBuf.ulBufLen);
    if (pstBuff.StdBuf.pucData == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(-1, DB_SUCCESS_V1);
    }

    // 插入数据
    for (ulLoop = 0; ulLoop < 200000; ulLoop++) {
        memset(pstBuff.StdBuf.pucData, 0x00, pstBuff.StdBuf.ulBufLen);
        CommonSetDataBuf4Insert((char *)pstBuff.StdBuf.pucData, astFlds, ulLoop, pstBuff.usRecLen, false);
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId, &pstBuff);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    uint32_t vmRss1 = 0;
    uint32_t vmHwm1 = 0;
    uint32_t vmPeak1 = 0;
    TestGetMemInfo(getpid(), &vmRss1, &vmHwm1, &vmPeak1);
    AW_FUN_Log(LOG_INFO, "1、cdb write 0-199999 record, vmRss: %u, vmHwm: %u, vmPeak: %u", vmRss1, vmHwm1, vmPeak1);

    VOS_UINT8 *pucResult1 = NULL;
    VOS_UINT8 *pucResult2 = NULL;
    uint32_t globalAllocSize = 0;
    uint32_t DbTopSharedMemoryContext = 0;
    char result[500] = { 0 };

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_DYN_MEMCTX, NULL, &pucResult1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestGetDfxMem((char *)pucResult1, "globalAllocSize", 1, '\n', result, sizeof(result));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    globalAllocSize = stoull(result, NULL, 10);
    TPC_FreeSysviewResult(&pucResult1);
    pucResult1 = NULL;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, NULL, &pucResult2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    memset_s(result, sizeof(result), 0x00, sizeof(result));
    ret = TestGetDfxMem((char *)pucResult2, "DbTopSharedMemoryContext", 1, '\n', result, sizeof(result));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DbTopSharedMemoryContext = stoull(result, NULL, 10);
    TPC_FreeSysviewResult(&pucResult2);
    pucResult2 = NULL;
    uint32_t totalSize1 = 0;
    totalSize1 = globalAllocSize + DbTopSharedMemoryContext;
    AW_FUN_Log(LOG_INFO, "globalAllocSize: %llu, DbTopSharedMemoryContext:%llu, totalSize1: %llu\n",
        globalAllocSize, DbTopSharedMemoryContext, totalSize1);
    globalAllocSize = 0;
    DbTopSharedMemoryContext = 0;

    // 插入额外数据
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(ulDbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (ulLoop = 200000; ulLoop < 250000; ulLoop++) {
        memset(pstBuff.StdBuf.pucData, 0x00, pstBuff.StdBuf.ulBufLen);
        CommonSetDataBuf4Insert((char *)pstBuff.StdBuf.pucData, astFlds, ulLoop, pstBuff.usRecLen, false);
        ret = TPC_InsertRec(cdbId, ulDbId, usRelId, &pstBuff);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    uint32_t vmRss2 = 0;
    uint32_t vmHwm2 = 0;
    uint32_t vmPeak2 = 0;
    TestGetMemInfo(getpid(), &vmRss2, &vmHwm2, &vmPeak2);
    AW_FUN_Log(LOG_INFO,
        "2、cdb write 200000-250000 record, vmRss: %u, vmHwm: %u, vmPeak: %u", vmRss2, vmHwm2, vmPeak2);

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_DYN_MEMCTX, NULL, &pucResult1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestGetDfxMem((char *)pucResult1, "globalAllocSize", 1, '\n', result, sizeof(result));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    globalAllocSize = stoull(result, NULL, 10);
    TPC_FreeSysviewResult(&pucResult1);
    pucResult1 = NULL;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, NULL, &pucResult2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    memset_s(result, sizeof(result), 0x00, sizeof(result));
    ret = TestGetDfxMem((char *)pucResult2, "DbTopSharedMemoryContext", 1, '\n', result, sizeof(result));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DbTopSharedMemoryContext = stoull(result, NULL, 10);
    TPC_FreeSysviewResult(&pucResult2);
    pucResult2 = NULL;
    uint32_t totalSize2 = 0;
    totalSize2 = globalAllocSize + DbTopSharedMemoryContext;
    AW_FUN_Log(LOG_INFO, "globalAllocSize: %llu, DbTopSharedMemoryContext:%llu, totalSize2: %llu\n",
        globalAllocSize, DbTopSharedMemoryContext, totalSize2);
    globalAllocSize = 0;
    DbTopSharedMemoryContext = 0;

    ret = TPC_RollbackCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    malloc_trim(0);
    uint32_t vmRss3 = 0;
    uint32_t vmHwm3 = 0;
    uint32_t vmPeak3 = 0;
    TestGetMemInfo(getpid(), &vmRss3, &vmHwm3, &vmPeak3);
    AW_FUN_Log(LOG_INFO, "3、roolback, vmRss: %u, vmHwm: %u, vmPeak: %u", vmRss3, vmHwm3, vmPeak3);
    int32_t vmRssDiff = vmRss3 - vmRss1;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_DYN_MEMCTX, NULL, &pucResult1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestGetDfxMem((char *)pucResult1, "globalAllocSize", 1, '\n', result, sizeof(result));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    globalAllocSize = stoull(result, NULL, 10);
    TPC_FreeSysviewResult(&pucResult1);
    pucResult1 = NULL;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_SHM_MEMCTX, NULL, &pucResult2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    memset_s(result, sizeof(result), 0x00, sizeof(result));
    ret = TestGetDfxMem((char *)pucResult2, "DbTopSharedMemoryContext", 1, '\n', result, sizeof(result));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DbTopSharedMemoryContext = stoull(result, NULL, 10);
    TPC_FreeSysviewResult(&pucResult2);
    pucResult2 = NULL;
    uint32_t totalSize3 = 0;
    totalSize3 = globalAllocSize + DbTopSharedMemoryContext;
    AW_FUN_Log(LOG_INFO, "globalAllocSize: %llu, DbTopSharedMemoryContext:%llu, totalSize3: %llu\n",
        globalAllocSize, DbTopSharedMemoryContext, totalSize3);
    globalAllocSize = 0;
    DbTopSharedMemoryContext = 0;

    int32_t totalSizeDiff = (totalSize3 - totalSize1) / 1024;
    if (totalSizeDiff > 0) {
        TestMemPrintf(PERF_TESTCASE_021, totalSizeDiff, 1.10);
    } else {
        TestMemPrintf(PERF_TESTCASE_021, 0, 1.10);
    }

    TEST_V1_FREE(pstBuff.StdBuf.pucData);
    ret = TPC_DropTbl(ulDbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 共享空间中存在多表时，对无索引表进行全表扫描
TEST_F(SimprelPerf, perfV1ShareSpacenoIndextblSelectTime_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char dbName[] = "deleteAllRec";
    uint32_t ulDbId = 0xFFFFFFFF;
    CommCreateDB4Test(dbName, &ulDbId);

    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);
    // 建立无索引表
    DB_REL_DEF_STRU stRelDef;
    char tableName[] = "noIndextbl";
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 20000;
    stRelDef.ulMaxSize = 20000;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = 0;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = NULL;
    uint16_t usRelId[10] = {0};
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId[9]));

    // 建立多张索引表
    for (int i = 0; i < 9; i++) {
        char tblName[30] = {0};
        sprintf(tblName, "withIdxTbl_%d", i);
        strncpy((char *)stRelDef.aucRelName, tblName, DB_NAME_LEN);
        stRelDef.ulNIdxs = 1;
        stRelDef.pstIdxLst = astIdx;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(ulDbId, &stRelDef, &usRelId[i]));
    }

    uint32_t ulLoop;
    uint64_t startTime;
    uint64_t endTime;
    uint64_t TimeTaken = 0;
    uint64_t TimeTakenAll = 0;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    
    DB_FIELDFILTER_STRU stFldFilter;
    stFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstBuff;
    CommonInitDsBufStru(&pstBuff, astFlds, stRelDef.ulNCols);
    pstBuff.StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstBuff.StdBuf.ulBufLen);
    if (pstBuff.StdBuf.pucData == NULL) {
        V1_AW_MACRO_ASSERT_EQ_INT(-1, DB_SUCCESS_V1);
    }
    DB_ERR_CODE ret;

    DB_DSBUF_STRU stBuff = {0};
    CommonInitDsBufStru(&stBuff, astFlds, stRelDef.ulNCols);
    char *value = (char *)TEST_V1_MALLOC(stBuff.usRecLen);
    uint16_t i;
    for (i = 0; i <= 9; i++) {
        for (ulLoop = 0; ulLoop < 200; ulLoop++) {
            CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
            stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, ulDbId, usRelId[i], &stBuff));
        }
    }
    // 查询
    TimeTakenAll = 0;
    for (i = 1; i <= 10; i++) {
        TimeTaken = 0;
        for (ulLoop = 0; ulLoop < 20000; ulLoop++) {
            memset(pstBuff.StdBuf.pucData, 0x00, pstBuff.StdBuf.ulBufLen);
            startTime = DbGetNsec();
            ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, ulDbId, usRelId[9], &pstCond, &stFldFilter, &pstBuff);
            endTime = DbGetNsec();
            TimeTaken += endTime - startTime;
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        TimeTakenAll += TimeTaken;
    }
    PrintLineLn();
    TestPerfPrintf(PERF_TESTCASE_022, TRANS2_MS(TimeTakenAll / (i - 1)));
    PrintLineLn();
    TEST_V1_FREE(value);
    TEST_V1_FREE(pstBuff.StdBuf.pucData);

    CommonDropDB4Test(ulDbId, (VOS_UINT8 *)dbName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
