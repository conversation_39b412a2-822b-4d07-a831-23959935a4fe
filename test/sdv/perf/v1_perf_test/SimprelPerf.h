/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SimprelPerf.cpp
 * Description: V1兼容性能
 * Create:
 */

#ifndef SIMPREL_PERF_H_
#define SIMPREL_PERF_H_

#include "t_rd_simplerel.h"

#define RUN_CNT 100

typedef struct {
    uint32_t execTimes;
    uint64_t timeStart;
    uint64_t timeAll;
} PerfStruT;

#define DB_CLOCK_MONOTONIC CLOCK_MONOTONIC
#define NSECONDS_IN_USECOND ((uint64_t)1000)
#define USECONDS_IN_MSECOND ((uint64_t)1000)
#define MSECONDS_IN_SECOND ((uint64_t)1000)
#define NSECONDS_IN_MSECOND (NSECONDS_IN_USECOND * USECONDS_IN_MSECOND)
#define NSECONDS_IN_SECOND (NSECONDS_IN_MSECOND * MSECONDS_IN_SECOND)
uint64_t DbGetNsec()
{
    struct timespec requestStart;
    (void)clock_gettime(DB_CLOCK_MONOTONIC, &requestStart);
    return (uint64_t)requestStart.tv_nsec + NSECONDS_IN_SECOND * (uint64_t)requestStart.tv_sec;
}

#define NS_TRANS2_MS(ns) ((ns) / (double)(1000000))
#define GET_START_TIME(perf) ((perf).timeStart = DbGetNsec())
#define GET_CYCLE_TIME(perf)                                \
    do {                                                    \
        (perf).timeAll += (DbGetNsec() - (perf).timeStart); \
        (perf).execTimes++;                                 \
    } while (0)

#define GET_TOTAL_TIME(perf) NS_TRANS2_MS((perf).timeAll)
#define GET_AVG_TIME(perf) (((perf).timeAll) / (double)((perf).execTimes))

typedef struct {
    uint64_t beginTime;
    uint64_t timeAll;
} PerfStruOT;

void PrintLineLn(void)
{
    (void)fprintf(stdout, "*****************************************************************************\n");
}

#define NSECONDS_IN_USECOND ((uint64_t)1000)
#define USECONDS_IN_MSECOND ((uint64_t)1000)
#define NSECONDS_IN_MSECOND (NSECONDS_IN_USECOND * USECONDS_IN_MSECOND)
#define TRANS2_MS(nsec) ((nsec) / (double)NSECONDS_IN_MSECOND)

const uint32_t g_defaultV1TestMaxRecordNums = 1000;

#define EPSILON_FLOAT 1e-6
int32_t TestCompareFloat(float input1, float input2)
{
    float diffValue = input1 - input2;
    if (diffValue > EPSILON_FLOAT) {
        return 1;
    } else if (diffValue < -EPSILON_FLOAT) {
        return -1;
    }

    return 0;
}

int32_t testGetCmdResult(const char *cmd, char *result, uint32_t len)
{
    if (result == NULL) {
        return -1;
    }
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }

    char *cmdOutput = result;
    while (fgets(cmdOutput, len, pf) != NULL) {
    }
    for (uint32_t i = 0; i < len; i++) {
        if (cmdOutput[i] == '\n') {
            cmdOutput[i] = '\0';
        }
        if (cmdOutput[i] == '\0') {
            break;
        }
    }
    pclose(pf);

    return 0;
}

#define PERF_TESTCASE_001 "PERF_001"
#define PERF_TESTCASE_002 "PERF_002"
#define PERF_TESTCASE_003 "PERF_003"
#define PERF_TESTCASE_004 "PERF_004"
#define PERF_TESTCASE_005 "PERF_005"
#define PERF_TESTCASE_006 "PERF_006"
#define PERF_TESTCASE_007 "PERF_007"
#define PERF_TESTCASE_008 "PERF_008"
#define PERF_TESTCASE_009 "PERF_009"
#define PERF_TESTCASE_010 "PERF_010"
#define PERF_TESTCASE_011 "PERF_011"
#define PERF_TESTCASE_012 "PERF_012"
#define PERF_TESTCASE_013 "PERF_013"
#define PERF_TESTCASE_014 "PERF_014"
#define PERF_TESTCASE_015 "PERF_015"
#define PERF_TESTCASE_016_001 "PERF_016_001"
#define PERF_TESTCASE_016_002 "PERF_016_002"
#define PERF_TESTCASE_017_001 "PERF_017_001"
#define PERF_TESTCASE_017_002 "PERF_017_002"
#define PERF_TESTCASE_017_003 "PERF_017_003"
#define PERF_TESTCASE_018_001 "PERF_018_001"
#define PERF_TESTCASE_018_002 "PERF_018_002"
#define PERF_TESTCASE_019_001 "PERF_019_001"
#define PERF_TESTCASE_020_001 "PERF_020_001"
#define PERF_TESTCASE_020_002 "PERF_020_002"
#define PERF_TESTCASE_020_003 "PERF_020_003"
#define PERF_TESTCASE_021 "PERF_021"
#define PERF_TESTCASE_022 "PERF_022"

#define PERF_TESTCASE_101 "PERF_101"
#define PERF_TESTCASE_102_001 "PERF_102_001"
#define PERF_TESTCASE_102_002 "PERF_102_002"

// 将结果记录到文件
#ifdef __x86_64__
#define PERF_EXPECT_FILE_NAME "v1_perf_expect_x86.txt"
#define PERF_ACTUAL_FILE_NAME "v1_perf_actual_x86.txt"
#else
#define PERF_EXPECT_FILE_NAME "v1_perf_expect.txt"
#define PERF_ACTUAL_FILE_NAME "v1_perf_actual.txt"
#endif

int32_t TestGetDfxMem(char *srcStr, const char *findStr, uint32_t cnt, char endCh, char *output, uint32_t size)
{
    uint32_t findCnt = 0;
    char *p = NULL;
    while (1) {
        p = strstr(srcStr, findStr);
        if (p != NULL) {
            findCnt++;
        } else {
            return -1;
        }
        // 如果找到的次数和要求的次数不一致，继续往下找
        if (findCnt != cnt) {
            continue;
        }

        // 获取值
        char getStr[1024] = { 0 };
        uint32_t len = strlen(p);
        uint32_t i;
        for (i = 0; i < len; i++) {
            if (*p != endCh && i < (sizeof(getStr) - 2)) {
                getStr[i] = *p;
                if (getStr[i] == '\0') {
                    getStr[i] = ' ';
                }
            } else {
                break;
            }
            p++;
        }

        // 获取数字
        len = strlen(getStr);
        uint32_t numCnt = 0;
        for (i = 0; i < len; i++) {
            if (getStr[i] >= '0' && getStr[i] <= '9') {
                if (i < size) {
                    output[numCnt] = getStr[i];
                    numCnt++;
                    continue;
                } else {
                    break;
                }
            }
        }
        output[numCnt] = '\0';

        return 0;
    }

    return 0;
}

void TestPerfPrintf(const char *testcaseNum, float actualVal, float deviation = 1.10)
{
    char cmd[1024] = { 0 };
    char descriptionStr[1024] = { 0 };
    char expectValStr[50] = { 0 };

    // 获取对应用例的描述信息
    (void)sprintf_s(cmd, sizeof(cmd), "cat %s | grep %s | awk '{ print $2 }'", PERF_EXPECT_FILE_NAME, testcaseNum);
    testGetCmdResult(cmd, descriptionStr, sizeof(descriptionStr));

    // 获取对应用例的预期值
    (void)sprintf_s(cmd, sizeof(cmd), "cat %s | grep %s | awk '{ print $3 }'", PERF_EXPECT_FILE_NAME, testcaseNum);
    testGetCmdResult(cmd, expectValStr, sizeof(expectValStr));

    // 允许波动10%
    float expectVal = atof(expectValStr);

    // 将实际的结果记录到文件
    FILE *fp = fopen(PERF_ACTUAL_FILE_NAME, "a+");
    if (fp == NULL) {
        fp = stdout;
    }
    bool isPass = false;
    if (TestCompareFloat(expectVal * deviation, actualVal) >= 0) {
        isPass = true;
    }
    (void)fprintf(fp, "%s %0.2fms %0.2fms %s\n", descriptionStr, expectVal, actualVal, isPass ? "PASS" : "FAILED");
    (void)fprintf(stdout, "%s %0.2fms %0.2fms %s\n", descriptionStr, expectVal, actualVal, isPass ? "PASS" : "FAILED");
}

void TestGetMemInfo(pid_t pid, uint32_t *vmRss, uint32_t *vmHwm, uint32_t *vmPeak = NULL)
{
    // 获取对应用例的内存信息
    char cmd[1024] = { 0 };
    (void)sprintf_s(cmd, sizeof(cmd), "cat /proc/%d/status | grep VmRSS | awk '{print $2}'", pid);
    char vmRssInfo[50] = { 0 };
    testGetCmdResult(cmd, vmRssInfo, sizeof(vmRssInfo));
    *vmRss = atoi(vmRssInfo);
    (void)sprintf_s(cmd, sizeof(cmd), "cat /proc/%d/status | grep VmHWM | awk '{print $2}'", pid);
    char vmHwmInfo[50] = { 0 };
    testGetCmdResult(cmd, vmHwmInfo, sizeof(vmHwmInfo));
    *vmHwm = atoi(vmHwmInfo);
    if (vmPeak != NULL) {
        (void)sprintf_s(cmd, sizeof(cmd), "cat /proc/%d/status | grep VmPeak | awk '{print $2}'", pid);
        char vmPeakInfo[50] = { 0 };
        testGetCmdResult(cmd, vmPeakInfo, sizeof(vmPeakInfo));
        *vmPeak = atoi(vmPeakInfo);
    }
}

void TestMemPrintf(const char *testcaseNum, uint32_t actualVal, float deviation = 1.20)
{
    char cmd[1024] = { 0 };
    char descriptionStr[1024] = { 0 };
    char expectValStr[50] = { 0 };

    // 获取对应用例的描述信息
    (void)sprintf_s(cmd, sizeof(cmd), "cat %s | grep %s | awk '{ print $2 }'", PERF_EXPECT_FILE_NAME, testcaseNum);
    testGetCmdResult(cmd, descriptionStr, sizeof(descriptionStr));

    // 获取对应用例的预期值
    (void)sprintf_s(cmd, sizeof(cmd), "cat %s | grep %s | awk '{ print $3 }'", PERF_EXPECT_FILE_NAME, testcaseNum);
    testGetCmdResult(cmd, expectValStr, sizeof(expectValStr));

    // 20%以内
    uint32_t expectVal = (uint32_t)((float)(atoi(expectValStr)));

    // 将实际的结果记录到文件
    FILE *fp = fopen(PERF_ACTUAL_FILE_NAME, "a+");
    if (fp == NULL) {
        fp = stdout;
    }
    bool isPass = false;
    if (TestCompareFloat(expectVal * deviation, actualVal) >= 0) {
        isPass = true;
    }
    (void)fprintf(fp, "%s %dKb %dKb %s\n", descriptionStr, expectVal, actualVal, isPass ? "PASS" : "FAILED");
    (void)fprintf(stdout, "%s %dKb %dKb %s\n", descriptionStr, expectVal, actualVal, isPass ? "PASS" : "FAILED");
}

void TestSoSizePrintf(const char *testcaseNum, uint32_t actualVal, float deviation = 1.05)
{
    char cmd[1024] = { 0 };
    char descriptionStr[1024] = { 0 };
    char expectValStr[50] = { 0 };

    // 获取对应用例的描述信息
    (void)sprintf_s(cmd, sizeof(cmd), "cat %s | grep %s | awk '{ print $2 }'", PERF_EXPECT_FILE_NAME, testcaseNum);
    testGetCmdResult(cmd, descriptionStr, sizeof(descriptionStr));

    // 获取对应用例的预期值
    (void)sprintf_s(cmd, sizeof(cmd), "cat %s | grep %s | awk '{ print $3 }'", PERF_EXPECT_FILE_NAME, testcaseNum);
    testGetCmdResult(cmd, expectValStr, sizeof(expectValStr));

    // 预期的大小
    uint32_t expectVal = (uint32_t)((float)(atoi(expectValStr)));

    // 将实际的结果记录到文件
    FILE *fp = fopen(PERF_ACTUAL_FILE_NAME, "a+");
    if (fp == NULL) {
        fp = stdout;
    }
    bool isPass = false;
    if (TestCompareFloat(expectVal * deviation, actualVal) >= 0) {
        isPass = true;
    }
    (void)fprintf(fp, "%s %dKb %dKb %s\n", descriptionStr, expectVal, actualVal, isPass ? "PASS" : "FAILED");
    (void)fprintf(stdout, "%s %dKb %dKb %s\n", descriptionStr, expectVal, actualVal, isPass ? "PASS" : "FAILED");
}

// 9个字段1个索引表结构初始化
static void DmlTestCreateTblInitRelDef(DB_REL_DEF_STRU *stRelDef, const char *tblName)
{
    const char tableName[] = "dmlTableName";
    const uint32_t fldNum = 9;
    const uint32_t idxNum = 0;

    DB_FIELD_DEF_STRU *astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_STRING, DBT_UINT32, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64, DBT_UINT64};
    const uint32_t fldSizes[fldNum] = {16, 4, 2, 4, 1, 2, 4, 8, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = g_defaultV1TestMaxRecordNums;
    stRelDef->ulMaxSize = g_defaultV1TestMaxRecordNums;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = NULL;
}

static void DmlTestCreateTblInitIndexRelDef(DB_REL_DEF_STRU *stRelDef, const char *tblName)
{
    const char tableName[] = "dmlTableName";
    const char indexName[] = "dmlIndexName";
    const uint32_t fldNum = 9;
    const uint32_t idxNum = 1;

    DB_FIELD_DEF_STRU *astFlds = (DB_FIELD_DEF_STRU *)TEST_V1_MALLOC(fldNum * sizeof(DB_FIELD_DEF_STRU));
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_STRING, DBT_UINT32, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64, DBT_UINT64};
    const uint32_t fldSizes[fldNum] = {16, 4, 2, 4, 1, 2, 4, 8, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }

    DB_INDEX_DEF_STRU *astIdx = (DB_INDEX_DEF_STRU *)TEST_V1_MALLOC(idxNum * sizeof(DB_INDEX_DEF_STRU));
    astIdx[0].ucUniqueFlag = 1;
    (void)strncpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 1;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = g_defaultV1TestMaxRecordNums;
    stRelDef->ulMaxSize = g_defaultV1TestMaxRecordNums;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

void DmlTestCreateDBAndCreateTbl(uint32_t *ulDbId, DB_REL_DEF_STRU *stRelDef, uint16_t *usRelId,
    uint32_t maxRecNum, bool isIndex = false)
{
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.ulMaxDBDescInfoSize = 100;
    const char dmlDbName[15] = "testDml";
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateDB((VOS_UINT8 *)dmlDbName, NULL, &dbCfg));

    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, (VOS_UINT8 *)dmlDbName, ulDbId));

    if (isIndex) {
        DmlTestCreateTblInitIndexRelDef(stRelDef, NULL);
    } else {
        DmlTestCreateTblInitRelDef(stRelDef, NULL);
    }
    // 设置表记录上限
    if (maxRecNum != 0) {
        stRelDef->ulIntialSize = maxRecNum;
        stRelDef->ulMaxSize = maxRecNum;
    }
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(*ulDbId, stRelDef, usRelId));
}

static void DmlTestFreeRelDef(DB_REL_DEF_STRU *stRelDef)
{
    if (stRelDef->pstFldLst != NULL) {
        TEST_V1_FREE(stRelDef->pstFldLst);
        stRelDef->pstFldLst = NULL;
    }
    if (stRelDef->pstIdxLst != NULL) {
        TEST_V1_FREE(stRelDef->pstIdxLst);
        stRelDef->pstIdxLst = NULL;
    }
}

void DmlTestDropDBAndFreeReldef(uint32_t ulDbId, DB_REL_DEF_STRU *stRelDef)
{
    DB_ERR_CODE ret = TPC_CloseDB(ulDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    const char dmlDbName[15] = "testDml";
    ret = TPC_DropDB((VOS_UINT8 *)dmlDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    DmlTestFreeRelDef(stRelDef);
}

uint16_t DmlTestGetRecLen(DB_REL_DEF_STRU *stRelDef)
{
    uint32_t recordLen = 0;
    DB_FIELD_DEF_STRU *pstFld = stRelDef->pstFldLst;
    for (uint32_t i = 0; i < stRelDef->ulNCols; i++, pstFld++) {
        if (pstFld->enDataType == DBT_STRING || pstFld->enDataType == DBT_MIBSTR) {
            ++pstFld->usSize;
        }
        recordLen += pstFld->usSize;
    }
    return recordLen;
}

// 9个字段1个索引初始化一条数据
void DmlTestInitOneRec(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t bufLen)
{
    (void)memset_s(recBuf, bufLen, 0x00, bufLen);
    uint32_t index = 0;
    const uint32_t v0Len = 17;
    char v0[v0Len];
    memset_s(v0, v0Len, 0x00, v0Len);
    (void)sprintf_s(v0, v0Len, "rec%u", base);
    uint32_t v1 = base;
    uint16_t v2 = base + 10;
    uint32_t v3 = base + 100;
    int8_t v4 = base % 128;
    int16_t v5 = base + 20;
    int32_t v6 = base + 200;
    int64_t v7 = base + 1000;
    uint64_t v8 = base + 2000;
    uint8_t *temp = recBuf;
    (void)memcpy_s(temp, astFlds[index].usSize, &v0, astFlds[index].usSize);
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v1, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v2, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v3, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v4, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v5, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v6, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v7, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v8, astFlds[index + 1].usSize);
}

void InitTblDef4TestDDLPerf(
    DB_REL_DEF_STRU *stRelDef, char *tableName, DB_FIELD_DEF_STRU *astFlds, DB_INDEX_DEF_STRU *astIdx)
{
    // base_config No.1042表
    char fldNames[][DB_FLD_NAME_LEN] = {"validflag", "InforId", "Infortype", "alarmType", "OidLen", "OID",
        "FeatureName", "InforName", "CurEnableFlag", "DefEnableFlag", "bldrnFlag", "TrapType", "TrapId", "iid",
        "lifeCycle", "visibleVR", "visibleLR"};
    for (uint32_t i = 0; i < 17; i++) {
        strcpy_s((char *)astFlds[i].aucFieldName, DB_FLD_NAME_LEN, fldNames[i]);
        astFlds[i].ulDefVal = 0;
    }
    uint32_t index = 0;
    astFlds[index].enDataType = DBT_BLOCK;
    astFlds[index++].usSize = 4;
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_UINT8;
    astFlds[index++].usSize = sizeof(uint8_t);
    astFlds[index].enDataType = DBT_UINT8;
    astFlds[index++].usSize = sizeof(uint8_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_STRING;
    astFlds[index++].usSize = 255;
    astFlds[index].enDataType = DBT_STRING;
    astFlds[index++].usSize = 31;
    astFlds[index].enDataType = DBT_STRING;
    astFlds[index++].usSize = 63;
    astFlds[index].enDataType = DBT_UINT8;
    astFlds[index++].usSize = sizeof(uint8_t);
    astFlds[index].enDataType = DBT_UINT8;
    astFlds[index++].usSize = sizeof(uint8_t);
    astFlds[index].enDataType = DBT_UINT8;
    astFlds[index++].usSize = sizeof(uint8_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    astFlds[index].enDataType = DBT_UINT32;
    astFlds[index++].usSize = sizeof(uint32_t);
    index = 0;

    strncpy((char *)astIdx[0].aucIndexName, "keyofcommontbl", DB_NAME_LEN);
    astIdx[0].ucUniqueFlag = 1;
    astIdx[0].ucIdxFldNum = 3;
    astIdx[0].aucFieldID[0] = 1;
    astIdx[0].aucFieldID[1] = 2;
    astIdx[0].aucFieldID[2] = 3;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;

    strncpy((char *)astIdx[1].aucIndexName, "iid_index", DB_NAME_LEN);
    astIdx[1].ucUniqueFlag = 0;
    astIdx[1].ucIdxFldNum = 1;
    astIdx[1].aucFieldID[0] = 13;
    astIdx[1].enIndexType = DBDDL_INDEXTYPE_TTREE;

    strncpy((char *)stRelDef->aucRelName, tableName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 10000;
    stRelDef->ulMaxSize = 20000;
    stRelDef->ulNCols = 17;
    stRelDef->ulNIdxs = 2;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
}

#define CYCLES_20K 20000
#define NINE_FLDS 9
#define ONE_IDX 1
#define NINETEEN_FLDS 19
#define EIGHT_IDXES 8

void CommonInitFldAndIdxDef4Test(DB_FIELD_DEF_STRU *astFlds, DB_INDEX_DEF_STRU *astIdx)
{
    uint32_t index = 0;
    uint32_t types[] = {
        DBT_STRING, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64, DBT_UINT64};
    uint32_t typeNum = sizeof(types) / sizeof(types[0]);
    uint32_t size[] = {16, 1, 2, 4, 1, 2, 4, 8, 8};
    for (index = 0; index < typeNum; index++) {
        (void)sprintf((char *)astFlds[index].aucFieldName, "F%d", index);
        astFlds[index].ulDefVal = 0;
        astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)types[index];
        astFlds[index].usSize = size[index];
    }
    index = 0;
    (void)sprintf((char *)astIdx[index].aucIndexName, "F%dIdx", 3);
    astIdx[index].ucUniqueFlag = 1;
    astIdx[index].ucIdxFldNum = 1;
    astIdx[index].aucFieldID[0] = 3;
    astIdx[index].enIndexType = DBDDL_INDEXTYPE_TTREE;
}

void CommonSetDataBuf4Insert(char *value, DB_FIELD_DEF_STRU *astFlds, uint32_t ulLoop, uint32_t bufLen, bool isUpdate)
{
    uint32_t index = 0;
    char v0[16];
    uint8_t v1;
    uint16_t v2;
    uint32_t v3;
    int8_t v4;
    int16_t v5;
    int32_t v6;
    int64_t v7;
    uint64_t v8;
    if (!isUpdate) {
        memset(value, 0x00, bufLen);
        sprintf(v0, "record%d", ulLoop);
        v1 = 23;
        v2 = ulLoop + 1;
        v3 = ulLoop + 11;
        v4 = -11;
        v5 = ulLoop - 100;
        v6 = ulLoop + 5000;
        v7 = ulLoop - 10000;
        v8 = ulLoop + 100001;
    } else {
        memset(value, 0x00, bufLen);
        sprintf(v0, "update%d", ulLoop);
        v1 = 11;
        v2 = ulLoop + 2;
        v3 = ulLoop + 120000;
        v4 = -5;
        v5 = ulLoop - 101;
        v6 = ulLoop + 5001;
        v7 = ulLoop - 9999;
        v8 = ulLoop + 12345;
    }
    char *temp = value;
    memcpy(temp, &v0, astFlds[index].usSize + 1);
    memcpy(temp += (astFlds[index].usSize + 1), &v1, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v2, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v3, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v4, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v5, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v6, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v7, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v8, astFlds[index + 1].usSize);
}

void CommonInitDsBufStru(DB_DSBUF_STRU *stBuff, DB_FIELD_DEF_STRU *astFlds, uint32_t fldNum)
{
    stBuff->usRecLen = 0;
    for (uint32_t i = 0; i < fldNum; i++) {
        // string类型需要添加结尾'\0'
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            stBuff->usRecLen++;
        }
        stBuff->usRecLen += astFlds[i].usSize;
    }
    stBuff->StdBuf.ulActLen = stBuff->usRecLen;
    stBuff->StdBuf.ulBufLen = stBuff->usRecLen;
    stBuff->usRecNum = 1;
}

void CommonInitDsBufStru2(DB_DSBUF_STRU *stBuff, DB_FIELD_DEF_STRU *astFlds, uint32_t fldNum)
{
    stBuff->usRecLen = 0;
    for (uint32_t i = 0; i < fldNum; i++) {
        // string类型需要添加结尾'\0'
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            astFlds[i].usSize++;
        }
        stBuff->usRecLen += astFlds[i].usSize;
    }
    stBuff->StdBuf.ulActLen = stBuff->usRecLen;
    stBuff->StdBuf.ulBufLen = stBuff->usRecLen;
    stBuff->usRecNum = 1;
}

void CommonSetData4Test(DB_FIELD_DEF_STRU *astFlds, uint32_t dbId, uint16_t usRelId, uint32_t insertCnt, char *value)
{
    DB_DSBUF_STRU stBuff = {0};
    CommonInitDsBufStru(&stBuff, astFlds, 9);

    for (uint32_t ulLoop = 0; ulLoop < insertCnt; ulLoop++) {
        CommonSetDataBuf4Insert(value, astFlds, ulLoop, stBuff.usRecLen, false);
        stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, dbId, usRelId, &stBuff));
    }
}

void CreateTblAndSetData4Select(char *tableName, uint32_t dbId, uint16_t *usRelId, uint32_t insertNum)
{
    DB_FIELD_DEF_STRU astFlds[NINE_FLDS];
    DB_INDEX_DEF_STRU astIdx[ONE_IDX];
    CommonInitFldAndIdxDef4Test(astFlds, astIdx);

    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = insertNum;
    stRelDef.ulNCols = NINE_FLDS;
    stRelDef.ulNIdxs = ONE_IDX;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;

    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(dbId, &stRelDef, usRelId));
    char *value = (char *)TEST_V1_MALLOC(100);
    CommonSetData4Test(astFlds, dbId, *usRelId, insertNum, value);
    TEST_V1_FREE(value);
}

void CommCreateDB4Test(char *dbName, uint32_t *dbId)
{
    DB_INST_CONFIG_STRU pstCfg = {0};
    pstCfg.ulInitialSize = 4 * 1024 * 1024;
    pstCfg.ulTempSize = 4 * 1024 * 1024;
    pstCfg.ulExtendSize = 4 * 1024 * 1024;
    pstCfg.ulMaxDBDescInfoSize = 1043;
    pstCfg.enPersistent = DB_CKP_NONE;

    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &pstCfg));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, dbId));
}

void CommonDropDB4Test(VOS_UINT32 ulDbId, VOS_UINT8 *dbName)
{
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CloseDB(ulDbId));
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_DropDB(dbName, 1));
}

void Tbl_1021_InitFldAndIdxDef4Test(DB_FIELD_DEF_STRU *astFlds, DB_INDEX_DEF_STRU *astIdx, const uint32_t fldNum,
    const uint32_t idxNum)
{
    uint32_t index = 0;
    uint32_t types[fldNum] = {8, 11, 8, 11, 8, 11, 11, 11, 11, 11, 11, 9, 11, 11, 11, 11, 11};
    uint32_t sizes[fldNum] = {4, 4, 16, 4, 16, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4};
    char fldNames[fldNum][DB_NAME_LEN] = {"validflag", "ifIndex", "ifIp6Addr", "addrPrefixLen", "netAddr", "addrType",
        "addrOrigin", "seq", "seq2", "vrId", "vrfId", "srvOccupied", "tag", "iid", "lifeCycle", "visibleVR",
        "visibleLR"};
    for (index = 0; index < fldNum; index++) {
        strcpy_s((char *)astFlds[index].aucFieldName, DB_FLD_NAME_LEN, fldNames[index]);
        astFlds[index].ulDefVal = 0;
        astFlds[index].enDataType = (DB_DATATYPE_ENUM_V1)types[index];
        astFlds[index].usSize = sizes[index];
    }

    char idxNames[idxNum][DB_NAME_LEN] = {"keyofcommontbl", "iid_index", "index1"};
    index = 0;
    strcpy_s((char *)astIdx[index].aucIndexName, DB_IDX_NAME_LEN, idxNames[index]);
    astIdx[index].ucUniqueFlag = 1;
    astIdx[index].ucIdxFldNum = 2;
    astIdx[index].aucFieldID[0] = 1;
    astIdx[index].aucFieldID[1] = 2;
    astIdx[index].enIndexType = DBDDL_INDEXTYPE_TTREE;
    index++;
    strcpy_s((char *)astIdx[index].aucIndexName, DB_IDX_NAME_LEN, idxNames[index]);
    astIdx[index].ucUniqueFlag = 0;
    astIdx[index].ucIdxFldNum = 1;
    astIdx[index].aucFieldID[0] = 13;
    astIdx[index].enIndexType = DBDDL_INDEXTYPE_TTREE;
    index++;
    strcpy_s((char *)astIdx[index].aucIndexName, DB_IDX_NAME_LEN, idxNames[index]);
    astIdx[index].ucUniqueFlag = 0;
    astIdx[index].ucIdxFldNum = 1;
    astIdx[index].aucFieldID[0] = 1;
    astIdx[index].enIndexType = DBDDL_INDEXTYPE_TTREE;
}

void Tbl_1021_SetData(DB_FIELD_DEF_STRU *astFlds, uint32_t dbId, uint16_t usRelId, uint32_t insertCnt,
    char *value, const uint32_t fldNum)
{
    DB_DSBUF_STRU stBuff = {0};
    for (uint32_t i = 0; i < fldNum; i++) {
        if (astFlds[i].enDataType == DBT_STRING || astFlds[i].enDataType == DBT_MIBSTR) {
            ++astFlds[i].usSize;
        }
        stBuff.usRecLen += astFlds[i].usSize;
    }
    stBuff.StdBuf.ulActLen = stBuff.usRecLen;
    stBuff.StdBuf.ulBufLen = stBuff.usRecLen;
    stBuff.usRecNum = 1;
    uint8_t v0[4] = {0}, v2[16] = {0}, v4[16] = {0};
    uint32_t v1, v3, v5, v6, v7, v8, v9, v10, v12, v13, v14, v15, v16;
    uint8_t v11, block, v2Block;
    uint32_t index = 0, j;
    for (uint32_t ulLoop = 0; ulLoop < insertCnt; ulLoop++) {
        index = 0;
        memset(value, 0x00, stBuff.StdBuf.ulBufLen);
        block = ulLoop % 256;
        for (j = 0; j < 4; j++) {
            memcpy(v0 + j * sizeof(uint8_t), &block, sizeof(uint8_t));
        }
        v1 = ulLoop + 100;  // [100, 200100]
        v2Block = (200 * (ulLoop / 200 + 1)) % 256;
        for (j = 0; j < 4; j++) {
            memcpy(v2 + j * sizeof(uint8_t), &v2Block, sizeof(uint8_t));
        }
        v3 = ulLoop + 10;  // [10, 200010]
        for (j = 0; j < 6; j++) {
            memcpy(v4 + j * sizeof(uint8_t), &block, sizeof(uint8_t));
        }
        v5 = ulLoop + 100;   // [100, 200100]
        v6 = ulLoop + 5000;  // [5000, 25000]
        v7 = ulLoop + 77895;
        v8 = ulLoop + 444623;
        v9 = 666666 + ulLoop / 70;
        v10 = 333333 + ulLoop / 80;
        v11 = ulLoop % 128;
        v12 = ulLoop + 11;
        v13 = 462598746 - ulLoop;
        v14 = 88956452 - ulLoop / 100;
        v15 = 77786521 - ulLoop / 60;
        v16 = 44465589 - ulLoop / 30;
        char *temp = value;
        memcpy(temp, v0, astFlds[index].usSize);
        memcpy(temp += astFlds[index].usSize, &v1, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, v2, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v3, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, v4, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v5, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v6, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v7, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v8, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v9, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v10, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v11, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v12, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v13, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v14, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v15, astFlds[index + 1].usSize);
        ++index;
        memcpy(temp += astFlds[index].usSize, &v16, astFlds[index + 1].usSize);

        stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, dbId, usRelId, &stBuff));
    }
}

void TestCreateTbl_1021_AndSetData(char *tableName, uint32_t dbId, uint16_t *usRelId, uint32_t insertNum)
{
    const int32_t fldNum = 17;
    const int32_t idxNum = 3;
    DB_FIELD_DEF_STRU astFlds[fldNum];
    DB_INDEX_DEF_STRU astIdx[idxNum];
    Tbl_1021_InitFldAndIdxDef4Test(astFlds, astIdx, fldNum, idxNum);

    DB_REL_DEF_STRU stRelDef;
    strncpy((char *)stRelDef.aucRelName, tableName, DB_NAME_LEN);
    stRelDef.enTableType = DB_TABLE_NORMAL;
    stRelDef.ulIntialSize = 100;
    stRelDef.ulMaxSize = insertNum;
    stRelDef.ulNCols = fldNum;
    stRelDef.ulNIdxs = idxNum;
    stRelDef.pstFldLst = astFlds;
    stRelDef.pstIdxLst = astIdx;

    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_CreateTbl(dbId, &stRelDef, usRelId));
    char *value = (char *)TEST_V1_MALLOC(100);
    Tbl_1021_SetData(astFlds, dbId, *usRelId, insertNum, value, fldNum);
    TEST_V1_FREE(value);
}

// *************************** Id41 cmpFunc
DB_ERR_CODE CpmEqual4TypeId41(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    *bMatch = *(char *)filedValue == *(char *)condValue;
    return DB_SUCCESS_V1;
}

DB_ERR_CODE CpmNotEqual4TypeId41(
    const void *filedValue, const void *condValue, uint16_t usLen, uint32_t *bMatch, uint8_t *ucDir)
{
    *bMatch = *(uint32_t *)filedValue != *(uint32_t *)condValue;
    return DB_SUCCESS_V1;
}

DB_ERR_CODE CpmLess4TypeId41(const void *filedValue, const void *condValue, uint16_t usLen,
    uint32_t *bMatch, uint8_t *ucDir)
{
    *bMatch = *(uint32_t *)filedValue < *(uint32_t *)condValue;
    return DB_SUCCESS_V1;
}

DB_ERR_CODE CpmLessEqual4TypeId41(const void *filedValue, const void *condValue, uint16_t usLen,
    uint32_t *bMatch, uint8_t *ucDir)
{
    *bMatch = *(uint32_t *)filedValue <= *(uint32_t *)condValue;
    return DB_SUCCESS_V1;
}

DB_ERR_CODE CpmLarger4TypeId41(const void *filedValue, const void *condValue, uint16_t usLen,
    uint32_t *bMatch, uint8_t *ucDir)
{
    *bMatch = *(uint32_t *)filedValue > *(uint32_t *)condValue;
    return DB_SUCCESS_V1;
}

DB_ERR_CODE CpmLargerEqual4TypeId41(const void *filedValue, const void *condValue, uint16_t usLen,
    uint32_t *bMatch, uint8_t *ucDir)
{
    *bMatch = *(uint32_t *)filedValue >= *(uint32_t *)condValue;
    return DB_SUCCESS_V1;
}

void SimpleSetCmpFunc41(DBTC_OPERATION_FUNC *cmpFunc)
{
    cmpFunc[DB_OP_EQUAL] = CpmEqual4TypeId41;
    cmpFunc[DB_OP_NOTEQUAL] = CpmNotEqual4TypeId41;
    cmpFunc[DB_OP_LESS] = CpmLess4TypeId41;
    cmpFunc[DB_OP_LESSEQUAL] = CpmLessEqual4TypeId41;
    cmpFunc[DB_OP_LARGER] = CpmLarger4TypeId41;
    cmpFunc[DB_OP_LARGEREQUAL] = CpmLargerEqual4TypeId41;
}

/*
  测试base_config 36号表
  --------------------------------------Table Info-------------------------------------------
    table id: 36, table name: OsNode, Actual Record Num: 0, Max Record Num: 131072, rec len: 84, fld num: 14,
    idx num: 2, size: 985 (Schema 960 Data 0 Index 0 Other 25)
  ***********************************Field Info*******************************
    Name:       validflag, ID:     0, Len:     4, Type:     8 (DBT_BLOCK)
    Name:      osInstance, ID:     1, Len:     4, Type:    11
    Name:       lrBoardid, ID:     2, Len:    31, Type:     7 (DBT_STRING)
    Name:            lrId, ID:     3, Len:     4, Type:    11
    Name:       nodeGrpId, ID:     4, Len:     4, Type:    11
    Name:     comPlaneId5, ID:     5, Len:     4, Type:    11
    Name:        routerID, ID:     6, Len:     4, Type:    11
    Name:          slotID, ID:     7, Len:     4, Type:    11
    Name:           cpuID, ID:     8, Len:     4, Type:    11 (DBT_UINT32)
    Name:          lrRole, ID:     9, Len:     4, Type:    11
    Name:             iid, ID:    10, Len:     4, Type:    11
    Name:       lifeCycle, ID:    11, Len:     4, Type:    11
    Name:       visibleVR, ID:    12, Len:     4, Type:    11
    Name:       visibleLR, ID:    13, Len:     4, Type:    11
  ***********************************Index Info*******************************
    Name:  keyofcommontbl, Unique: 1, type: 2, Fldnum:   1 :   1
    Name:       iid_index, Unique: 0, type: 2, Fldnum:   1 :  10
 */
void ConstructFldsAndIdxs4Tbl36(DB_FIELD_DEF_STRU *astFlds, DB_INDEX_DEF_STRU *astIdx)
{
    uint32_t types[] = {DBT_BLOCK, DBT_UINT32, DBT_STRING, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32,
        DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32, DBT_UINT32};
    uint32_t FieldNum = sizeof(types) / sizeof(types[0]);
    uint32_t Fieldsize[] = {4, 4, 31, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4};
    // 构造Field信息
    for (uint32_t i = 0; i < FieldNum; i++) {
        (void)sprintf((char *)astFlds[i].aucFieldName, "F%d", i);
        astFlds[i].ulDefVal = 0;
        astFlds[i].enDataType = (DB_DATATYPE_ENUM_V1)types[i];
        astFlds[i].usSize = Fieldsize[i];
    }
    // 构造第一个索引信息
    uint32_t idx = 0;
    (void)sprintf((char *)astIdx[idx].aucIndexName, "Idx%d_F%d", idx, 1);
    astIdx[idx].ucUniqueFlag = 1;
    astIdx[idx].ucIdxFldNum = 1;
    astIdx[idx].aucFieldID[0] = 1;
    astIdx[idx].enIndexType = DBDDL_INDEXTYPE_TTREE;
    // 构造第二个索引
    ++idx;
    (void)sprintf((char *)astIdx[idx].aucIndexName, "Idx%d_F%d", idx, 10);
    astIdx[idx].ucUniqueFlag = 0;
    astIdx[idx].ucIdxFldNum = 1;
    astIdx[idx].aucFieldID[0] = 10;
    astIdx[idx].enIndexType = DBDDL_INDEXTYPE_TTREE;
}

// 生成要插入的一条数据
void GenRecData4Tbl36(char *value, DB_FIELD_DEF_STRU *astFlds, uint32_t ulLoop, uint32_t bufLen, bool isUpdate)
{
    char v0[4] = "abc";
    char v2[31];
    uint32_t v1, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12, v13;

    // 构造关键字段的数据
    v1 = ulLoop;  // v1为unique索引列
    v6 = ulLoop - 10;
    v7 = ulLoop + 100;
    v9 = (uint32_t)(ulLoop / 20);  // V9的每个值有20个相同；
    (void)sprintf(v2, "string_%d", v9);

    // 依据每个字段的偏移，把字段的值拷贝到buf中，类似序列化的过程
    memset(value, 0x00, bufLen);
    char *temp = value;
    uint32_t index = 0;
    memcpy(temp, &v0, astFlds[index].usSize);
    memcpy(temp += astFlds[index].usSize, &v1, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v2, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v3, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v4, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v5, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v6, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v7, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v8, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v9, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v10, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v11, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v12, astFlds[index + 1].usSize);
    ++index;
    memcpy(temp += astFlds[index].usSize, &v13, astFlds[index + 1].usSize);
}

void GenAndInsertData4Tbl36(DB_FIELD_DEF_STRU *astFlds, uint32_t dbId, uint16_t usRelId,
    uint32_t insertCnt, char *value)
{
    DB_DSBUF_STRU stBuff = {0};
    CommonInitDsBufStru2(&stBuff, astFlds, 14);

    for (uint32_t ulLoop = 0; ulLoop < insertCnt; ulLoop++) {
        GenRecData4Tbl36(value, astFlds, ulLoop, stBuff.usRecLen, false);
        stBuff.StdBuf.pucData = (VOS_UINT8 *)value;
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, TPC_InsertRec(TPC_GLOBAL_CDB, dbId, usRelId, &stBuff));
    }
}

DB_ERR_CODE TestGetClassInfoTblAllData(uint32_t ulDbId, uint16_t usRelId)
{
    DB_ERR_CODE ret;

    char fldName[3][DB_FLD_NAME_LEN] = { "cid", "name", "fnum" };
    ret = TestGetTblAllData(ulDbId, usRelId, fldName, 3);

    return ret;
}

void TestSetDataToClassInfoTblRecBuff(VOS_UINT8 *pucData, DB_REL_DEF_STRU *stRelDef, uint32_t val)
{
    VOS_UINT16 fieldLen = 0;
    VOS_UINT8 *p = pucData;
    for (uint32_t i = 0; i < stRelDef->ulNCols; i++) {
        fieldLen = stRelDef->pstFldLst[i].usSize;
        if (stRelDef->pstFldLst[i].enDataType == DBT_STRING || stRelDef->pstFldLst[i].enDataType == DBT_MIBSTR) {
            fieldLen++;
        }
        if (stRelDef->pstFldLst[i].enDataType == DBT_UINT32) {
            *(uint32_t *)p = val;
        }
        if (stRelDef->pstFldLst[i].enDataType == DBT_UINT16) {
            *(uint8_t *)p = val % 65535;
        }
        if (stRelDef->pstFldLst[i].enDataType == DBT_UINT8) {
            *(uint8_t *)p = val % 127;
        }
        if (stRelDef->pstFldLst[i].enDataType == DBT_STRING) {
            (void)sprintf_s((char *)p, fieldLen, "str_%06u", val);
        }
        p += fieldLen;
    }
}

void TestSetDataToSysIndexMngTblRecBuff(VOS_UINT8 *pucData, DB_REL_DEF_STRU *stRelDef, uint32_t val)
{
    VOS_UINT16 fieldLen = 0;
    VOS_UINT8 *p = pucData;
    for (uint32_t i = 0; i < stRelDef->ulNCols; i++) {
        fieldLen = stRelDef->pstFldLst[i].usSize;
        if (stRelDef->pstFldLst[i].enDataType == DBT_UINT32) {
            *(uint32_t *)p = val;
        }
        if (stRelDef->pstFldLst[i].enDataType == DBT_UINT16) {
            *(uint8_t *)p = val % 65535;
        }
        if (stRelDef->pstFldLst[i].enDataType == DBT_UINT8) {
            *(uint8_t *)p = val % 127;
        }
        p += fieldLen;
    }
}

#endif
