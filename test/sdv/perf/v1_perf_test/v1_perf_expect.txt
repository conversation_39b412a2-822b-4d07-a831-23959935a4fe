PERF_001 TPC_BeginIdxSelectByOrder_Fetch_50_item_20k_Times 100.00
PERF_002 TPC_SelectAllRec_1_item_20k_Times 27.00
PERF_003 TPC_DeleteRec_1_item_20k_Times 5.00
PERF_004 TPC_DeleteRec_withCdb_1_item_20k_Times 6.50
PERF_005 TPC_DeleteRec_20k_item_1_Times 12.00
PERF_006 TPC_InsertRec_1_item_20k_Times 38.00
PERF_007 TPC_InsertRec_withCdb_1_item_20k_Times 48.00
PERF_008 TPC_SelectAllRecEx_1_item_20k_Times 27.00
PERF_009 TPC_SelectAllRecByOrder_50_item_20k_Times 135.00
PERF_010 TPC_UpdateRec_1_item_20k_Times 6.00
PERF_011 TPC_UpdateRec_withCdb_1_item_20k_Times 7.50
PERF_012 TPC_SelectAllRecEx_Tbl36_20_item_200_Times 75.00
PERF_013 TPC_SelectAllRecByOrder_9_item_20k_Times 145.50
PERF_014 TPC_SelectAllRec_Tbl1283_1_item_20k_Times 23.00
PERF_015 TPC_SelectAllRecByOrder_Tbl1904_9_item_10k_Times 79.00
PERF_016_001 Import_mster_mem_vmRss 44628
PERF_016_002 Import_mster_mem_vmHwm 55000
PERF_017_001 TableNoData_Select 27.00
PERF_017_002 TableNoData_Update 10.00
PERF_017_003 TableNoData_Delete 10.00
PERF_018_001 UndoOptimization_Update_Perf 10.00
PERF_018_002 UndoOptimization_Mem_Test 1000
PERF_019_001 GmdbEmbedSoSize 2400
PERF_020_001 TPC_Init_Mem_vmRss 11972
PERF_020_002 TPC_Init_Mem_vmHwm 11972
PERF_021 CdbRollback_50000_Rec_Mem_vmRss 1000
PERF_022 NoIndexTable_Select_Time 82.22

# VIST工具的测试
PERF_101 VIST_TOOL_TPC_GetTblInfo 7.50
PERF_102_001 VIST_TOOL_RDB_Update_Perf 470.00
PERF_102_002 VIST_TOOL_Undo_Mem_Recover 3000
