{"ietf-netconf:config": {"huawei-aaa:aaa": {"lam": {"users": {"user": [{"name": "admin", "group-name": "admin", "password": "$6$JwJQUhSYr$c51ldenTNva3J74RyJmrQhGHg2F3Z8R3wT9Nmru.7Zflu.MCQASBcToRL/rp5TLP0csvR5LePwgBO0YKAD8a/1", "service-terminal": true, "service-api": true, "password-force-change": true}, {"name": "hua<PERSON>", "group-name": "admin", "service-api": true}, {"name": "test1234"}]}}}, "huawei-network-instance:network-instance": {"instances": {"instance": [{"name": "_public_", "huawei-l3vpn:afs": {"af": [{"type": "ipv4-unicast", "huawei-routing:routing": {"routing-manage": {"topologys": {"topology": [{"name": "base"}]}}}}]}}]}}, "huawei-ifm:ifm": {"interfaces": {"interface": [{"name": "Vlanif1", "class": "main-interface", "type": "<PERSON><PERSON><PERSON>", "link-protocol": "ethernet", "huawei-ip:ipv4": {"addresses": {"address": [{"ip": "*************", "mask": "*************", "type": "main"}]}}, "huawei-dhcp:interface-ip-pool": {"select-interface": {"dns-list": {"ip-address": ["***************"]}}}}, {"name": "GE0/0/1", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/2", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/3", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/4", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": "1", "untag-vlans": "1"}}}}, {"name": "GE0/0/5", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "huawei-ip:ipv4": {"addresses": {"address": [{"ip": "*************", "mask": "*********", "type": "main"}]}}}, {"name": "NULL", "class": "main-interface", "type": "NULL", "link-protocol": "ethernet"}, {"name": "lo", "class": "main-interface", "type": "LoopBack", "link-protocol": "ethernet"}]}}, "huawei-vlan:vlan": {"vlans": {"vlan": [{"id": 1}]}}, "huawei-cli:cli": {"huawei-cli-lite:terminal": {"split-screen": true}}, "huawei-dhcp:dhcp": {"common": {"global": {"enable": true}}}, "huawei-pki:pki": {"domains": {"domain": [{"name": "default"}]}}, "huawei-lldp:lldp": {}, "huawei-easyweb-netmgmt:easyweb-netmgmt": {"network": {"device-granteds": {"device-granted": [{"mac-address": "000a-43fc-7d00"}]}}}, "huawei-ssl:ssl": {"ssl-policys": {"ssl-policy": [{"policy-name": "default", "pki-realm": "default"}]}}, "huawei-nat-policy:nat-policy": {"rules": {"rule": [{"name": "default", "action": {"source-nat": {"easy-ip": [null]}}, "egress-interface": ["GE0/0/5"]}]}}, "huawei-sshs:sshs": {"server": {"pki-domain": "default"}, "users": {"user": [{"name": "hua<PERSON>", "key-name": "default", "pub-key-type": "PKI"}]}, "server-enable": {"stelnet-ipv4-enable": "enable"}, "ipv4-server-sources": {"ipv4-server-source": [{"src-interface": "Vlanif1"}, {"src-interface": "GE0/0/5"}]}, "call-homes": {"call-home": [{"call-home-name": "c1", "end-points": {"end-point": [{"end-point-name": "e1", "address": "************", "port": 13160}]}}]}}, "huawei-tm:tm": {"timezone-configuration": {"timezone-name": "BEIJING", "option": "add", "timezone-offset": "08:00:00"}}}}