<?xml version="1.0" encoding="utf-8"?>
<Secray
        description="Network Time Protocol PIT DataModels"
        version="1.0">
<!--
{
	"max_record_count":100000,
	"writers":"abc",
	"max_record_count_check":false
}
-->

	<!--fuzz allowlist 数据建模 -->
  <DataModel name="reqCreateKvData">
       <Block name="reqCreateKvData">
        <String  name="FixPart01"   value="{&quot;max_record_count&quot;:"/>
        <String  name="CreateUserName01"   value="100000"/>
        <String  name="FixPart02"   value=",&quot;writers&quot;:&quot;"/>
        <String  name="CreateUserName02"   value="abc"/>
        <String  name="FixPart03"   value="&quot;,&quot;max_record_count_check&quot;:"/>
        <String  name="CreateUserName03"   value="false"/>
        <String  name="FixPart04"   value="}"/>
	    </Block>
   </DataModel>

</Secray>

