extern "C" {
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "async_sn_common.h"

#ifdef DEBUG
#undef DEBUG
#endif
#define DEBUG 1

GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
char g_label_name[] = "fuzzedschema";

/*****************************************************************************
 Description  : 安全测试-文件fuzz测试
 History      : 增加相关的操作, 方便fuzz测试, 配合fuzz脚本使用
 Author       : pwx623912
 Modification :
 Date         : 2021/12/09
*****************************************************************************/

int main(int argc, char *argv[])
{
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    if (argc == 2 && !(strcmp(argv[1], (char *)"DropTableFuzzedschema"))) {
        //创建同步连接
        ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
        EXPECT_EQ(GMERR_OK, ret);

        //删表断连
        ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
        LOG("The fuzz drop table: %d\n", ret);
        testGmcDisconnect(g_conn_sync, g_stmt_sync);
    }
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}
