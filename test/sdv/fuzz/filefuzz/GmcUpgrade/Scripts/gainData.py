# -*- coding: utf-8 -*-

import datetime
import time
import sys
import re
import os
from basicfunc import *
reload(sys)
sys.setdefaultencoding('utf8')



def get_timeAndPid_data(ctx):
    t=time.time()
    nowTime=long(round(t*1000000))
    os.system("ps -ef | grep secray.py |grep -v grep |awk '/secray.py/{print $2}' >/root/secray/pid.txt")
    with open('/root/secray/pid.txt', 'r') as f:
        list = f.readlines()
        for i in range(0, len(list)):
            list[i] = list[i].strip('\n')
            pidNum = int(list[i])
    t2=time.time()
    sendTime=long(round(t2*1000000))
    update_value_to_message(ctx, 'reqStartTime', nowTime)
    update_value_to_message(ctx, 'reqSendStartTime', sendTime)
    update_value_to_message(ctx, 'pidValue', pidNum)

def get_alloc_stmt_data(ctx):
    """
    获取申请stmt的响应报文的数据
    """
    stmtId = get_value_from_data_model(ctx, "AckAllocStmtMsg")
    print 'reqStartTime-------------'
    value = stmtId[22:24]
    hex_int = "0x" + "".join([str(hex(ord(i)))[2:] for i in value])
    print hex_int
    print int(hex_int, 16)
    set_store_value_iteration(ctx, 'stmtID', hex_int)



def get_time_data(ctx):
    t=time.time()
    nowTime=long(round(t*1000000))
    update_value_to_message(ctx, 'reqStartTime', nowTime)
    update_value_to_message(ctx, 'reqSendStartTime', nowTime)

def get_test_time_data(ctx):
    os.system("$GMDB_PATH/bin/gmsysview -u gmdbv5 -s usocket:/run/verona/unix_emserver -p passwd -f VERTEXLABEL_NAME='generalLabel' -q V\$STORAGE_VERTEX_LABEL_STAT > ./label.txt")
    with open('./label.txt', 'r') as f:
        list = f.readlines()
        for i in range(0, len(list)):
            list[i] = list[i].strip('\n')
            if("primary_key" in list[i]):
                indexId=list[i+1].split(":")[1]
                IndexID=int(indexId)
                break
    t=time.time()
    nowTime=long(round(t*1000000))
    update_value_to_message(ctx, 'TestreqStartTime', nowTime)
    update_value_to_message(ctx, 'TestreqSendStartTime', nowTime)
    update_value_to_message(ctx, 'UpdateIndexID', IndexID)

def gmrule_vertexlabel_import(ctx, filePath):
	absFilepath = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
	cmdStr = '$GMDB_PATH/bin/gmimport -c vschema -f ' + absFilepath + '/' +filePath
	os.system(cmdStr)

def gmrule_vertexlabel_upgrade(ctx, filePath):
	absFilepath = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
	cmdStr = '$GMDB_PATH/bin/gmimport -c alt_schema -f ' + absFilepath + '/' +filePath + ' -t generalLabel'
	os.system(cmdStr)
	
	
def get_create_table_data(ctx):
    """
    获取申请stmt的响应报文的数据
    """
    CreateProcessName = get_value_from_data_model(ctx, "CreateProcessName")
    print 'CreateProcessName------------- %s' % CreateProcessName
	
