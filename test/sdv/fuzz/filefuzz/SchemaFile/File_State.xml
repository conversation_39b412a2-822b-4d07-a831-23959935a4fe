<?xml version="1.0" encoding="utf-8"?>
<!--
<PERSON><PERSON><PERSON> PIT COPYRIGHT NOTICE AND LEGAL DISCLAIMER

COPYRIGHT
Copyright © 2011-2014 2014 Déjà vu Security, LLC.
All rights reserved.

Déjà vu Security is the sole proprietary owner of Peach Pits and related
definition files and documentation.

User may only use, copy, or modify Peach Pits and related definition files and
documentation for internal business purposes only, provided that this entire
notice and following disclaimer appear in all copies or modifications, and
subject to the following conditions:

(1) User maintains a current subscription to the Peach Pit library.
(2) User's use is restricted to commercially licensed version of Peach Fuzzer
    only. Running Peach Pits with the Peach Fuzzer Community edition or any
    other solution is strictly prohibited.
(3) The sale, transfer, or distribution of Peach Pits and related definition
    files and documentation, in any form, is not permitted, without Déjà vu
    Security's express permission.

Legal Disclaimer
PEACH PITS AND RELATED DEFINTIION FILES AND DOCUMENTATION ARE PROVIDED "AS IS",
DÉJÀ VU SECURITY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING, BUT
NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
PARTICULAR PURPOSE. DÉJÀ VU SECURITY HAS NO OBLIGATION TO PROVIDE MAINTENANCE,
SUPPORT, UPDATES, ENHANCEMENTS, OR MODIFICATIONS.

IN NO EVENT SHALL DÉJÀ VU SECURITY BE LIABLE TO ANY PARTY FOR ANY DIRECT,
INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES (INCLUDING LOSS OF USE,
DATA, OR PROFITS), ARISING OUT OF ANY USE OF PEACH PITS AND RELATED
DOCUMENTATION, EVEN IF DÉJÀ VU SECURITY HAS BEEN ADVISED OF THE POSSIBILITY OF
SUCH DAMAGE.
-->
<Peach xmlns="http://peachfuzzer.com/2012/Peach"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://peachfuzzer.com/2012/Peach peach.xsd"  
       author="Taoningzhangzhiqun, huawei"
 description="FILE PIT StateModels" version="0.0.1">


  <PythonPath path="##ExternPyPath##"/>
  <Import import="LiteFileFuzz" />
	<Include ns="FILE" src="file:##PitLibraryPath##/##ProjectPath##/File_Data.xml"/>
	<Import import="time" />
	
	<StateModel name="FileWriteAndLaunch" initialState="Initial">
    <State name="Initial">
      <Action type="call" method="StartIterationEvent" publisher="Peach.Agent" />

      <Action name="WriteFileFromSample" type="output" publisher="writer">
          <DataModel name="DBFFile" ref="FILE:DBFFile"/>
          <Data fileName="##SamplePath##/##Seed##"/>
      </Action>
      <Action name="CloseFile"  type="close" publisher="writer"/>
          <!--Action name="Next" type="changeState" ref="importSchema"/>			
          </State>
          <State name="importSchema"-->
      <Action type="call" onComplete="LiteFileFuzz.importFile(self,'SCHEMA','##FuzzedPath##/##FuzzedFile##')">
        <DataModel ref="DataModel" />
      </Action>
    </State>
  </StateModel>
</Peach>
<!--END-->
