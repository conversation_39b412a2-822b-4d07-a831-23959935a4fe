<?xml version="1.0" encoding="utf-8"?>
<Secray
        description="Network Time Protocol PIT DataModels"
        version="1.0">

	<!--服务端对usocket的连接请求响应-->
    <DataModel name="ConnectAck">
        <Blob name="ConnectAckMessage"/>
    </DataModel>

<!--
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2023-10-16
 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct B {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
} B;

typedef struct Inp1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} Inp1;

#pragma pack(0)

// 可更新表
int32_t dtl_compare_tuple_tb1(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    Inp1 *inp1 = (Inp1 *)tuple1;
    Inp1 *inp2 = (Inp1 *)tuple2;
    if (inp1->c < inp2->c) {
        return -1;
    } else if (inp1->c > inp2->c) {
        return 1;
    } else {
        if (inp1->b < inp2->b) {
            return -1;
        } else {
            return 1;
        }
        return 0;
    }
}

int32_t dtl_ext_func_func1(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;

    return GMERR_OK;
}

-->

    <!--fuzz .c-->
    <DataModel name="reqCreateC">
        <Block name="reqCreateC">
            <String  name="FixPart01"   value="#include &quot;gm_udf.h&quot;\n#include &quot;stdio.h&quot;\n#include &quot;unistd.h&quot;\n#pragma pack(1)\ntypedef struct "/>
            <String  name="Value01"   value="B"/>
            <String  name="FixPart02"   value=" {\n    int32_t "/>
            <String  name="Value02"   value="dtlReservedCount"/>
            <String  name="FixPart03"   value=";\n    int64_t "/>
            <String  name="Value03"   value="a"/>
            <String  name="FixPart04"   value=";\n    int64_t "/>
            <String  name="Value04"   value="b"/>
            <String  name="FixPart05"   value=";\n    int64_t "/>
            <String  name="Value05"   value="c"/>
            <String  name="FixPart06"   value=";\n} "/>
            <String  name="Value06"   value="B"/>
            <String  name="FixPart07"   value=";\ntypedef struct "/>
            <String  name="Value07"   value="Inp1"/>
            <String  name="FixPart08"   value=" {\n    int32_t "/>
            <String  name="Value07"   value="dtlReservedCount"/>
            <String  name="FixPart09"   value=";\n    int32_t "/>
            <String  name="Value09"   value="upgradeVersion"/>
            <String  name="FixPart10"   value=";\n    int64_t "/>
            <String  name="Value10"   value="a"/>
            <String  name="FixPart11"   value=";\n    int64_t "/>
            <String  name="Value11"   value="b"/>
            <String  name="FixPart12"   value=";\n    int64_t "/>
            <String  name="Value12"   value="c"/>
            <String  name="FixPart13"   value=";\n} "/>
            <String  name="Value13"   value="Inp1"/>
            <String  name="FixPart14"   value=";\n#pragma pack(0)\nint32_t dtl_compare_tuple_tb1(const void *"/>
            <String  name="Value14"   value="tuple1"/>
            <String  name="FixPart15"   value=", const void *"/>
            <String  name="Value15"   value="tuple2"/>
            <String  name="FixPart16"   value=", GmUdfCtxT *"/>
            <String  name="Value16"   value="ctx"/>
            <String  name="FixPart17"   value=")\n{\n    "/>
            <String  name="Value17"   value="Inp1"/>
            <String  name="FixPart18"   value=" *"/>
            <String  name="Value18"   value="inp1"/>
            <String  name="FixPart19"   value=" = ("/>
            <String  name="Value19"   value="Inp1"/>
            <String  name="FixPart20"   value=" *)"/>
            <String  name="Value20"   value="tuple1"/>
            <String  name="FixPart21"   value=";\n    "/>
            <String  name="Value21"   value="Inp1"/>
            <String  name="FixPart22"   value=" *"/>
            <String  name="Value22"   value="inp2"/>
            <String  name="FixPart23"   value=" = ("/>
            <String  name="Value23"   value="Inp1"/>
            <String  name="FixPart24"   value=" *)"/>
            <String  name="Value24"   value="tuple2"/>
            <String  name="FixPart25"   value=";\n    if ("/>
            <String  name="Value25"   value="inp1"/>
            <String  name="FixPart26"   value="->"/>
            <String  name="Value26"   value="c "/>
            <String  name="FixPart27"   value=" &lt; "/>
            <String  name="Value27"   value="inp2"/>
            <String  name="FixPart28"   value="->"/>
            <String  name="Value28"   value="c"/>
            <String  name="FixPart29"   value=") {\n        return -1;\n    } else if ("/>
            <String  name="Value29"   value="inp1"/>
            <String  name="FixPart30"   value="->"/>
            <String  name="Value30"   value="c"/>
            <String  name="FixPart31"   value=" \> "/>
            <String  name="Value31"   value="inp2"/>
            <String  name="FixPart32"   value="->"/>
            <String  name="Value32"   value="c"/>
            <String  name="FixPart33"   value=") {\n        return 1;\n    } else {\n        if ("/>
            <String  name="Value33"   value="inp1"/>
            <String  name="FixPart34"   value="->"/>
            <String  name="Value34"   value="b"/>
            <String  name="FixPart35"   value=" &gt; "/>
            <String  name="Value35"   value="inp2"/>
            <String  name="FixPart36"   value="->"/>
            <String  name="Value36"   value="b"/>
            <String  name="FixPart37"   value=") {\n            return -1;        } else {\n            return 1;\n        }\n        return 0;\n    }\n}\nint32_t dtl_ext_func_func1(void *"/>
            <String  name="Value37"   value="tuple"/>
            <String  name="FixPart38"   value=", "/>
            <String  name="Value38"   value="GmUdfCtxT"/>
            <String  name="FixPart39"   value=" *"/>
            <String  name="Value39"   value="ctx"/>
            <String  name="FixPart40"   value=")\n{\n    "/>
            <String  name="Value40"   value="B"/>
            <String  name="FixPart41"   value=" *"/>
            <String  name="Value41"   value="b"/>
            <String  name="FixPart42"   value=" = ("/>
            <String  name="Value42"   value="B"/>
            <String  name="FixPart43"   value=" *)"/>
            <String  name="Value43"   value="tuple"/>
            <String  name="FixPart44"   value=";\n    "/>
            <String  name="Value44"   value="b"/>
            <String  name="FixPart45"   value="->"/>
            <String  name="Value45"   value="c"/>
            <String  name="FixPart46"   value=" = "/>
            <String  name="Value46"   value="b"/>
            <String  name="FixPart47"   value="->"/>
            <String  name="Value47"   value="a"/>
            <String  name="FixPart48"   value=" + "/>
            <String  name="Value48"   value="b"/>
            <String  name="FixPart49"   value="->"/>
            <String  name="Value49"   value="b"/>
            <String  name="FixPart50"   value=";\n    return GMERR_OK;\n}\n"/>
	    </Block>
    </DataModel>

</Secray>
