#!/usr/bin/env python

import time,os
import hashlib, hmac, sys, binascii

def  importFile(ctx, filetype, filepath):
	resultFuzz=True
	num=ctx.parent.parent.parent.context.currentIteration
	path = os.path.dirname(__file__)
	importtype = 'vschema'
	if 'KV' == filetype:
		importtype = 'kvdata'
	elif 'GMDATA' == filetype:
		importtype = 'vdata'
	elif 'RESPOOL' == filetype:
		importtype = 'imp_respool'

	cmd = 'gmimport -c vschema -f '+ path +'/fuzzfiles/'+ 'fuzzedschema.gmjson'
	print cmd
	os.system(cmd)

	try:
		cmd = 'gmimport -c '+importtype+' -f '+ path +'/'+ filepath
		print cmd
		os.system(cmd)
	except Exception as e:
		print e
		resultFuzz=False
	finally:
		cmd = path + r'/DboperateFuzzTest DropTableFuzzedschema'
		print cmd
		os.system(cmd)
	return resultFuzz




