<?xml version="1.0" encoding="utf-8"?>
<Secray>

<Include ns="CommonModels" src="file:##PitLibraryPath##/GmcCreateRsmTablespace/DataModels/CommonModels.xml"/>
	<!--服务端对usocket的连接请求响应-->
    <DataModel name="ConnectAck">
        <Blob name="ConnectAckMessage"/>
    </DataModel>

    <!--客户端请求建立连接请求数据建模 -->
	<DataModel name="reqConnect" ref="CommonModels:commonConnect">  <!--引用公共 -->
	</DataModel>
    <!--服务端对客户端的连接请求响应-->
	<DataModel name="reqConnectAck" ref="CommonModels:commonConnectAck">
	</DataModel>

	<!--客户端请求建表数据建模 -->
  	<DataModel name="reqCreateLabel" ref="CommonModels:commonCreateLabel">
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000001"/>
		<String name="requestBody.operationBody.LabelJsonLen.LabelJson" value="[{
    &quot;comment&quot;:&quot;一般复杂表&quot;,
    &quot;type&quot;:&quot;record&quot;,
    &quot;name&quot;:&quot;vertex_01&quot;,
    &quot;fields&quot;:
        [
            {&quot;name&quot;:&quot;A0&quot;, &quot;type&quot;:&quot;int32&quot;, &quot;nullable&quot;:false},
            {&quot;name&quot;:&quot;A1&quot;, &quot;type&quot;:&quot;int64&quot;, &quot;nullable&quot;:false},
            {&quot;name&quot;:&quot;A2&quot;, &quot;type&quot;:&quot;uint32&quot;, &quot;nullable&quot;:false},
            {&quot;name&quot;:&quot;A3&quot;, &quot;type&quot;:&quot;uint64&quot;, &quot;nullable&quot;:false},
            {&quot;name&quot;:&quot;A4&quot;, &quot;type&quot;:&quot;float&quot;, &quot;nullable&quot;:true},
            {&quot;name&quot;:&quot;A5&quot;, &quot;type&quot;:&quot;double&quot;, &quot;nullable&quot;:true},
            {&quot;name&quot;:&quot;A6&quot;, &quot;type&quot;:&quot;bitmap&quot;, &quot;size&quot;:16, &quot;nullable&quot;:true},
            {&quot;name&quot;:&quot;A7&quot;, &quot;type&quot;:&quot;fixed&quot;, &quot;size&quot;:16, &quot;nullable&quot;:true},
            {&quot;name&quot;:&quot;A8&quot;, &quot;type&quot;:&quot;bytes&quot;, &quot;nullable&quot;:true},
            {&quot;name&quot;:&quot;A9&quot;, &quot;type&quot;:&quot;string&quot;, &quot;nullable&quot;:true},
            {&quot;name&quot;:&quot;M0&quot;, &quot;type&quot;:&quot;record&quot;, &quot;array&quot;:true, &quot;size&quot;:1024, &quot;fields&quot;:[
                {&quot;name&quot;:&quot;B0&quot;, &quot;type&quot;: &quot;int32&quot;, &quot;nullable&quot;:true},
                {&quot;name&quot;:&quot;B1&quot;, &quot;type&quot;: &quot;uint32&quot;, &quot;nullable&quot;:true},
                {&quot;name&quot;:&quot;B2&quot;, &quot;type&quot;: &quot;bytes&quot;, &quot;nullable&quot;:true},
                {&quot;name&quot;:&quot;B3&quot;, &quot;type&quot;: &quot;bytes&quot;, &quot;nullable&quot;:true},
                {&quot;name&quot;:&quot;B4&quot;, &quot;type&quot;: &quot;string&quot;, &quot;nullable&quot;:true},
                {&quot;name&quot;:&quot;B5&quot;, &quot;type&quot;: &quot;string&quot;, &quot;nullable&quot;:true}
            ]}
        ],
    &quot;keys&quot;:
        [
            {
                &quot;node&quot;:&quot;vertex_01&quot;,
                &quot;name&quot;:&quot;PrimaryKey&quot;,
                &quot;index&quot;:{&quot;type&quot;:&quot;primary&quot;},
                &quot;fields&quot;:[&quot;A0&quot;],
                &quot;constraints&quot;:{&quot;unique&quot;:true},
                &quot;comment&quot;: &quot;主键索引&quot;
            },
            {
                &quot;node&quot;:&quot;M0&quot;,
                &quot;name&quot;:&quot;M0MemberKey&quot;,
                &quot;fields&quot;:[&quot;B0&quot;],
                &quot;constraints&quot;:{&quot;unique&quot;:true},
                &quot;comment&quot;: &quot;M0节点成员索引&quot;
            },
            {
                &quot;node&quot;:&quot;vertex_01&quot;,
                &quot;name&quot;:&quot;local_key&quot;,
                &quot;fields&quot;:[&quot;A1&quot;, &quot;A3&quot;],
                &quot;index&quot;: { &quot;type&quot;: &quot;local&quot; },
                &quot;constraints&quot;:{&quot;unique&quot;:false},
                &quot;comment&quot;: &quot;local索引&quot;
            }
        ]
}]\n\000"/>
		<String name="requestBody.operationBody.LabelConfigJsonLen.ConfigJson" value="{&quot;max_record_count&quot;:100}\000"/>
   		<String name="requestBody.operationBody.LabelNameLen.LabelName"   value="vertex_01\000"/>  <!--重写表名 -->
   </DataModel>

	<!--服务端对客户端的创表请求响应-->
	<DataModel name="reqCreateLabelAck" ref="CommonModels:commonCreateLabelAck" />
	
	
	<DataModel name="reqGmcCreateRsmTablespace" ref="CommonModels:commonGmcCreateRsmTablespace" >
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000004"/>
    </DataModel>

	<!--服务端对客户端的删除数据请求响应-->
    <DataModel name="reqGmcCreateRsmTablespaceAck" ref="CommonModels:commonGmcCreateRsmTablespaceAck" />
	
	
	<!--客户端请求删表表数据建模 -->
  	<DataModel name="reqDropLabel" ref="CommonModels:commonDropLabel">
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000004"/>
   		<String name="requestBody.operationBody.DropLabelNameLen.DropLabelName" value="vertex_01\000"/>
   </DataModel>

	<!--服务端对客户端的删表请求响应-->
    <DataModel name="reqDropLabelAck" ref = "CommonModels:commonDropLabelAck">
    </DataModel>

	<!--客户端请求释放stmt数据建模-->
	<DataModel name="reqFreeStmt" ref="CommonModels:commonFreeStmt" >
	</DataModel>

	<!--客户端请求断开连接请求数据建模 -->
    <DataModel name="reqDisConnect" ref="CommonModels:commonDisConnect" >
   </DataModel>

	<!--服务端客户端的断连请求响应-->
	<DataModel name="reqDisConnectAck" ref="CommonModels:commonDisConnectAck" />
</Secray>

