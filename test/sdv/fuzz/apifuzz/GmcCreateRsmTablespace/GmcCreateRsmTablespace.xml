<?xml version="1.0" encoding="utf-8"?>
<Secray
        description="Network Time Protocol PIT StateModels"
        version="1.0">
    <!--加载状态模型文件， 一般状态模型文件为： XXXX_State.xml-->
    <Include ns="GmcCreateRsmTablespace" src="file:##PitLibraryPath##/GmcCreateRsmTablespace/StateModels/GmcCreateRsmTablespace_State.xml"/>
    <!--定义所使用的monitor，如下使用的是ssh monitor-->
    <Agent name="local">		
		<Monitor class="RunCommand">
			<Param name="Arguments" value="" />
            <Param name="Command" value="sh /root/secray/pits/GmcCreateRsmTablespace/Extensions/Monitor/check_gmserver.sh"/>
            <Param name="CheckValue" value="DOWN"/>
            <Param name="FaultOnMatch" value="true"/>
        </Monitor>
		
		<!--检测是否有asan日志-->
		<Monitor class="RunCommand">
			<Param name="Arguments" value="" />
            <Param name="Command" value="sh /root/secray/pits/GmcCreateRsmTablespace/Extensions/Monitor/check_asan.sh"/>
            <Param name="CheckValue" value="Have_ASAN"/>
            <Param name="FaultOnMatch" value="true"/>
        </Monitor>
    </Agent>

    <Test name="Default" maxOutputSize="65535" targetLifetime="session">
        <!--加载状态模型文件中的StateModel-->
        <StateModel ref="GmcCreateRsmTablespace:Server"/>
        <!--使用monitor来监控设备运行状态-->
        <Agent ref="local"/>

        <Publisher name="LeaderPublisher" class="TcpClient">
            <Param name="SocketFile" value="/run/verona/unix_emserver"/>
            <Param name="Timeout" value="##TimeOut##"/>
        </Publisher>

        <!--不变异的数据建模-->
        <Exclude xpath="//reqConnect"/>
        <Exclude xpath="//reqFreeStmt"/>
        <Exclude xpath="//reqDisConnect"/>
        <Exclude xpath="//reqCreateLabel"/>
		<Exclude xpath="//reqDropLabel"/>
        <!--不变异的报文头字段-->
        <Exclude xpath="//protocolVersion"/>
        <Exclude xpath="//serviceId"/>
        <Exclude xpath="//serialNumber"/>
        <Exclude xpath="//MsgLen"/>
        <Exclude xpath="//flowCtlQueuePri"/>
        <Exclude xpath="//flags"/>
        <Exclude xpath="//opNum"/>
        <Exclude xpath="//stmtId"/>
        <Exclude xpath="//flowCtlLevel"/>
        <Exclude xpath="//priority"/>
        <Exclude xpath="//successNum"/>
        <Exclude xpath="//planeId"/>
        <Exclude xpath="//connId"/>
        <Exclude xpath="//seq"/>
        <Exclude xpath="//reqTimeOut"/>
        <Exclude xpath="//isDatalogQueueOp"/>
        <Exclude xpath="//isFinish"/>
        <Exclude xpath="//bitSupplement"/>
        <Exclude xpath="//msgMagicNum"/>
        <Exclude xpath="//size"/>
        <Exclude xpath="//reqStartTime"/>
        <Exclude xpath="//reqSendStartTime"/>
        <!--不变异的测试接口的opCode-->
        <Exclude xpath="//GmcCreateRsmTablespaceopCode"/>

        <Logger class="PftReport">
            <Param name="Path" value="../report"/>
        </Logger>

        <Logger class="File">
            <Param name="Path" value="../logs"/>
        </Logger>
        <Strategy class="##Strategy##"/>
    </Test>

</Secray>

