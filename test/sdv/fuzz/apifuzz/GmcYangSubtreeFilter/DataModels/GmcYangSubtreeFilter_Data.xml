<?xml version="1.0" encoding="utf-8"?>
<Secray>

<Include ns="CommonModels" src="file:##PitLibraryPath##/GmcYangSubtreeFilter/DataModels/CommonModels.xml"/>
	<!--服务端对usocket的连接请求响应-->
    <DataModel name="ConnectAck">
        <Blob name="ConnectAckMessage"/>
    </DataModel>

    <!--客户端请求建立连接请求数据建模 -->
	<DataModel name="reqConnect" ref="CommonModels:commonConnect">  <!--引用公共 -->
	</DataModel>
    <!--服务端对客户端的连接请求响应-->
	<DataModel name="reqConnectAck" ref="CommonModels:commonConnectAck">
	</DataModel>

	<!--客户端请求Use Namespace数据建模 -->
  	<DataModel name="reqUseNamespace" ref="CommonModels:gmdbCommonReq"> <!--引用公共 -->
	<Number size="16" name="requestBody.MsgHeaderT.serviceId" value="0001" valueType="hex"/>
	<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000002"/>
		<Block name="requestBody.operationBody">
			<Number size="32" name="UseNamespaceopCode" value="##MSG_OP_RPC_USE_NAMESPACE##" mutable="false"/>
			<Number size="32" name="UseNamespaceMsgLen" valueType="hex">
				<Relation type="size" of="operationBody"/>
			</Number>
			<Number size="32" name="UseName" valueType="hex" value="00000000"/>
			<Number size="32" name="UseNamespaceLen1" valueType="hex">
				<Relation type="size" of="UseNamespaceLen"/>
			</Number>
			<Block name="UseNamespaceLen">
				<String name="UseNamespaceName" value="testNamespace\000"/>
			</Block>
			<Padding name="UseNameFiled" alignment="32" alignedTo="UseNamespaceLen"/>
		</Block>
    </DataModel>

   <!--服务端对客户端的Use Namespace请求响应-->
	<DataModel name="reqUseNamespaceAck" ref="CommonModels:gmdbCommonReqAck">
		<Block name="requestBodyAck.operationBodyAck"> <!--重写-->
			<Blob name="reqUseNamespaceAckMessage"/>
		</Block>
    </DataModel>

	<DataModel name="reqTransStart" ref="CommonModels:gmdbCommonReq">
		<Number name="requestBody.MsgHeaderT.serviceId" value="0001" valueType="hex" size="16"/>
		<Block name="requestBody.operationBody">
			<Number name="TransStartopCode" value="##MSG_OP_RPC_TX_START##" size="32" mutable="false"/>
			<Number name="TransStartMsgLen" valueType="hex" size="32">
			<Relation of="operationBody" type="size"/>
			</Number>
			<Number name="TransStartisolationType" value="00000002" valueType="hex" size="32"/>
			<Number name="TransStartreadOnly" value="00000000" valueType="hex" size="32"/>
			<Number name="TransStarttrxtype" value="00000001" valueType="hex" size="32"/>
			<Number name="TransStarttransmode" value="00" valueType="hex" size="8"/>
			<Padding name="TransStartFiled" alignedTo="operationBody" alignment="32"/>
		</Block>
	</DataModel>

	<DataModel name="reqTransStartAck" ref="CommonModels:gmdbCommonReqAck">
	<Block name="requestBodyAck.operationBodyAck">
		<Blob name="reqTransStartAckMessage"/>
	</Block>
	</DataModel>

    <!--客户端请求开表数据建模 -->
  	<DataModel name="reqOpenLabel1" ref="CommonModels:commonPrepareLabel" >
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000002"/>
		<String  name="requestBody.operationBody.LabelNameLen.OpenLabelName" value="root\000"/>
    </DataModel>

	<!--服务端对客户端的开表请求响应-->
    <DataModel name="reqOpenLabelAck1" ref="CommonModels:commonPrepareLabelAck" >
	</DataModel>

    <!--客户端请求开表数据建模 -->
  	<DataModel name="reqOpenLabel2" ref="CommonModels:commonPrepareLabel" >
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000002"/>
		<String  name="requestBody.operationBody.LabelNameLen.OpenLabelName" value="list_6\000"/>
    </DataModel>

	<!--服务端对客户端的开表请求响应-->
    <DataModel name="reqOpenLabelAck2" ref="CommonModels:commonPrepareLabelAck" >
	</DataModel>

    <!--客户端请求开表数据建模 -->
  	<DataModel name="reqOpenLabel3" ref="CommonModels:commonPrepareLabel" >
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000002"/>
		<String  name="requestBody.operationBody.LabelNameLen.OpenLabelName" value="list_7\000"/>
    </DataModel>

	<!--服务端对客户端的开表请求响应-->
    <DataModel name="reqOpenLabelAck3" ref="CommonModels:commonPrepareLabelAck" >
	</DataModel>

	<!--客户端发送BatchDML请求-->
    <DataModel name="reqBatchDML" ref="CommonModels:gmdbCommonReq"> <!--引用公共 -->
		<Number size="16" name="requestBody.MsgHeaderT.serviceId" valueType="hex" value="0001" />
		<Block name="requestBody.operationBody">
			<Number size="32" name="BatchDML_opCode" value="##MSG_OP_RPC_BATCH##" mutable="false"/>
			<Number size="32" name="BatchDML_MsgLen" valueType="hex">
				<Relation type="size" of="operationBody"/>
			</Number>
			<Block name="BatchHeaderT">
				<Number size="32" name="BatchDML_totalNum" valueType="hex" value="00000003"/>  <!---和dmlhead个数保持一致-->
				<Number size="32" name="BatchDML_batchErrCtl" valueType="hex" value="00000000"/>
				<Number size="32" name="BatchDML_batchOrder" valueType="hex" value="00000001"/>
				<Number size="32" name="BatchDML_totalOpNum" valueType="hex" value="00000005"/> <!---和dmlbody个数保持一致-->
				<Number size="32" name="batchType" valueType="hex" value="00000001"/>
				<Number size="32" name="diffType" valueType="hex" value="00000000"/>
			</Block>

			<Block name="BatchLabel1">
				<Number size="32" name="BatchOpCode1" value="##MSG_OP_RPC_INSERT_VERTEX##"/>
				<Number size="32" name="BatchLabel1ID" valueType="hex"/>  <!---开表响应报文中获取-->
				<Number size="32" name="BatchSchemaVersion1" valueType="hex" value="00000000"/>
				<Number size="32" name="uuid" valueType="hex" value="FFFFFFFF"/>
				<Number size="32" name="Batch_VertexNum" valueType="hex" value="00000001"/>  <!---这个dmlhead下面有几个body，这里就是几个-->
			</Block>
			<Block name="BatchInsert_body">
				<Number size="32" name="BatchInsert_DataLen" valueType="hex"> <!---需要四节对齐-->
					<Relation type="size" of="DatasLen"/>
				</Number>
				<Block name="DatasLen">
					<Blob  name="BatchInsertDatas" valueType="hex"  value="02407840604047010000006400000064000000000000000000000000000000000000000000000005050500050005000000050000000000000005000000000000000000a0400000000000001440001e0ffe070007737472696e670007737472696e67000a00000000000000000000000000000000000000000800000000"/>
				</Block>
				<Padding name="reserved01" alignment="32" alignedTo="DatasLen"/>
				<Number size="32" name="operateEdgeFlag" valueType="hex" value="ffffff00"/>
				<Number size="32" name="vertexTmpId1" valueType="hex" value="00000000"/>
				<Number size="32" name="YangListFlag1" valueType="hex" value="00000000"/>
			</Block>

			<Block name="BatchLabel2">
				<Number size="32" name="BatchOpCode2" value="##MSG_OP_RPC_INSERT_VERTEX##"/>
				<Number size="32" name="BatchLabel2ID" valueType="hex"/>  <!---开表响应报文中获取-->
				<Number size="32" name="SchemaVersion2" valueType="hex" value="00000000"/>
				<Number size="32" name="uuid" valueType="hex" value="FFFFFFFF"/>
				<Number size="32" name="VertexNum" valueType="hex" value="00000002"/>
			</Block>
			<Block name="BatchInsert1">
				<Number size="32" name="BatchInsert_DataLen" valueType="hex"> <!---需要四节对齐-->
					<Relation type="size" of="DatasLen1"/>
				</Number>
				<Block name="DatasLen1">
					<Blob  name="BatchInsertDatas" valueType="hex"  value="02221d110000000000000000000000000000000000071e07737472696e67000100000100000000"/>
				</Block>
				<Padding name="reserved02" alignment="32" alignedTo="DatasLen1"/>
				<Number size="32" name="operateEdgeFlag2" valueType="hex" value="ff000004"/>
				<Number size="32" name="vertexTmpId" valueType="hex" value="00000001"/>
				<Number size="32" name="YangListFlag" valueType="hex" value="00000001"/>
				<Number size="32" name="pos" valueType="hex" value="00000002"/>
				<Number size="32" name="fixBuff" valueType="hex" value="00000000"/>
			</Block>
			<Block name="BatchInsert2">
				<Number size="32" name="BatchInsert_DataLen" valueType="hex">
					<Relation type="size" of="DatasLen2"/>
				</Number>
				<Block name="DatasLen2">
					<Blob  name="BatchInsertDatas" valueType="hex"  value="02221d110000000000000000010000000100000000071e07737472696e67000100000100000000"/>
				</Block>
				<Padding name="reserved03" alignment="32" alignedTo="DatasLen2"/>
				<Number size="32" name="operateEdgeFlag3" valueType="hex" value="ff000004"/>
				<Number size="32" name="vertexTmpId2" valueType="hex" value="00000001"/>
				<Number size="32" name="YangListFlag2" valueType="hex" value="00000001"/>
				<Number size="32" name="pos2" valueType="hex" value="00000002"/>
				<Number size="32" name="fixBuff2" valueType="hex" value="00000000"/>
			</Block>

			<Block name="BatchLabel3">
				<Number size="32" name="BatchOpCode3" value="##MSG_OP_RPC_INSERT_VERTEX##"/>
				<Number size="32" name="BatchLabel3ID" valueType="hex"/>  <!---开表响应报文中获取-->
				<Number size="32" name="SchemaVersion3" valueType="hex" value="00000000"/>
				<Number size="32" name="uuid" valueType="hex" value="FFFFFFFF"/>
				<Number size="32" name="VertexNum2" valueType="hex" value="00000002"/>
			</Block>
			<Block name="BatchInsert3">
				<Number size="32" name="BatchInsert_DataLen" valueType="hex"> <!---需要四节对齐-->
					<Relation type="size" of="DatasLen3"/>
				</Number>
				<Block name="DatasLen3">
					<Blob  name="BatchInsertDatas" valueType="hex"  value="02221d110000000000000000000000000000000000071e07737472696e67000100000100000000"/>
				</Block>
				<Padding name="reserved04" alignment="32" alignedTo="DatasLen3"/>
				<Number size="32" name="operateEdgeFlag4" valueType="hex" value="ff000004"/>
				<Number size="32" name="vertexTmpId3" valueType="hex" value="00000002"/>
				<Number size="32" name="YangListFlag3" valueType="hex" value="00000001"/>
				<Number size="32" name="pos3" valueType="hex" value="00000002"/>
				<Number size="32" name="fixBuff3" valueType="hex" value="00000000"/>
			</Block>
			<Block name="BatchInsert4">
				<Number size="32" name="BatchInsert_DataLen" valueType="hex">
					<Relation type="size" of="DatasLen4"/>
				</Number>
				<Block name="DatasLen4">
					<Blob  name="BatchInsertDatas" valueType="hex"  value="02221d110000000000000000010000000100000000071e07737472696e67000100000100000000"/>
				</Block>
				<Padding name="reserved05" alignment="32" alignedTo="DatasLen4"/>
				<Number size="32" name="operateEdgeFlag5" valueType="hex" value="ff000004"/>
				<Number size="32" name="vertexTmpId4" valueType="hex" value="00000002"/>
				<Number size="32" name="YangListFlag4" valueType="hex" value="00000001"/>
				<Number size="32" name="pos4" valueType="hex" value="00000002"/>
				<Number size="32" name="fixBuff4" valueType="hex" value="00000000"/>
			</Block>
	    </Block>
    </DataModel>

	<!--服务端收到BatchDML请求信息后，对客户端的响应信息-->
    <DataModel name="reqBatchDMLAck" ref="CommonModels:gmdbCommonReqAck">
		<Block name="requestBodyAck.operationBodyAck"> <!--重写-->
			<Blob name="reqBatchDMLAckMessage"/>
		</Block>
    </DataModel>

	<DataModel name="reqYangSubtreeFilter" ref="CommonModels:gmdbCommonReq"> <!--引用公共 -->
		<Number size="16" name="requestBody.MsgHeaderT.serviceId" valueType="hex" value="0001" />
		<Block name="requestBody.operationBody">
			<Number size="32" name="SubtreeOpCode" value="##MSG_OP_RPC_SUBTREE_FILTER##" mutable="false"/>
			<Number size="32" name="SubtreeMsgLen" valueType="hex">
				<Relation type="size" of="operationBody"/>
			</Number>
			<Number size="32" name="cursor" valueType="hex" value="00000000"/>
			<Number size="32" name="filterMode" valueType="hex" value="00000002"/>
			<Number size="32" name="count" valueType="hex" value="00000001"/>
			<Number size="32" name="jsonFlag" valueType="hex" value="00000004"/>
			<Number size="32" name="maxDepth" valueType="hex" value="00000000"/>
			<Number size="32" name="isLocate" valueType="hex" value="00000000"/>
			<Number size="32" name="defaultMode" valueType="hex" value="00000000"/>
			<Number size="32" name="configFlag" valueType="hex" value="00000000"/>
	    </Block>
    </DataModel>

	<!--服务端收到SubtreeFilter请求信息后，对客户端的响应信息-->
    <DataModel name="reqYangSubtreeFilterAck" ref="CommonModels:gmdbCommonReqAck">
		<Block name="requestBodyAck.operationBodyAck"> <!--重写-->
			<Blob name="reqYangSubtreeFilterMessage"/>
		</Block>
    </DataModel>

	<DataModel name="reqYangSubtreeFilter2" ref="CommonModels:gmdbCommonReq"> <!--引用公共 -->
		<Number size="16" name="requestBody.MsgHeaderT.serviceId" valueType="hex" value="0001" />
		<Block name="requestBody.operationBody">
			<Number size="32" name="SubtreeOpCode" value="##MSG_OP_RPC_SUBTREE_FILTER##" mutable="false"/>
			<Number size="32" name="SubtreeMsgLen" valueType="hex">
				<Relation type="size" of="operationBody"/>
			</Number>
			<Number size="32" name="cursor" valueType="hex" value="00000000"/>
			<Number size="32" name="filterMode" valueType="hex" value="00000004"/>
			<Number size="32" name="count" valueType="hex" value="00000001"/>
			<Number size="32" name="jsonFlag" valueType="hex" value="00000004"/>
			<Number size="32" name="maxDepth" valueType="hex" value="00000000"/>
			<Number size="32" name="isLocate" valueType="hex" value="00000000"/>
			<Number size="32" name="defaultMode" valueType="hex" value="00000000"/>
			<Number size="32" name="configFlag" valueType="hex" value="00000000"/>
			<Number size="32" name="IshasRootName" valueType="hex" value="00000001"/>
			<Number size="32" name="rootNameLen" valueType="hex">
				<Relation type="size" of="rNameLen"/>
			</Number>
			<Block name="rNameLen">
				<String name="rootName" value="root\000"/>
			</Block>
			<Padding name="Filed" alignment="32" alignedTo="rNameLen"/>
	    </Block>
    </DataModel>

	<!--服务端收到SubtreeFilter请求信息后，对客户端的响应信息-->
    <DataModel name="reqYangSubtreeFilterAck2" ref="CommonModels:gmdbCommonReqAck">
		<Block name="requestBodyAck.operationBodyAck"> <!--重写-->
			<Blob name="reqYangSubtreeFilterMessage"/>
		</Block>
    </DataModel>

	<DataModel name="reqYangSubtreeFilter3" ref="CommonModels:gmdbCommonReq"> <!--引用公共 -->
		<Number size="16" name="requestBody.MsgHeaderT.serviceId" valueType="hex" value="0001" />
		<Block name="requestBody.operationBody">
			<Number size="32" name="SubtreeOpCode" value="##MSG_OP_RPC_SUBTREE_FILTER##" mutable="false"/>
			<Number size="32" name="SubtreeMsgLen" valueType="hex">
				<Relation type="size" of="operationBody"/>
			</Number>
			<Number size="32" name="cursor" valueType="hex" value="00000000"/>
			<Number size="32" name="filterMode" valueType="hex" value="00000000"/>
			<Number size="32" name="count" valueType="hex" value="00000001"/>
			<Number size="32" name="jsonFlag" valueType="hex" value="00000004"/>
			<Number size="32" name="maxDepth" valueType="hex" value="00000000"/>
			<Number size="32" name="isLocate" valueType="hex" value="00000000"/>
			<Number size="32" name="defaultMode" valueType="hex" value="00000000"/>
			<Number size="32" name="configFlag" valueType="hex" value="00000000"/>
			<Number size="32" name="rootNameLen" valueType="hex">
				<Relation type="size" of="rNameLen"/>
			</Number>
			<Block name="rNameLen">
				<String name="rootName" value="root\000"/>
			</Block>
			<Padding name="Filed" alignment="32" alignedTo="rNameLen"/>
			<Number size="32" name="filterJsonLen" valueType="hex">
				<Relation type="size" of="filterLen"/>
			</Number>
			<Block name="filterLen">
				<String name="filterJson" value="{&quot;A2&quot;: 5}\000"/>
			</Block>
			<Padding name="Filed2" alignment="32" alignedTo="filterLen"/>
	    </Block>
    </DataModel>

	<!--服务端收到SubtreeFilter请求信息后，对客户端的响应信息-->
    <DataModel name="reqYangSubtreeFilterAck3" ref="CommonModels:gmdbCommonReqAck">
		<Block name="requestBodyAck.operationBodyAck"> <!--重写-->
			<Blob name="reqYangSubtreeFilterMessage"/>
		</Block>
    </DataModel>

	<!--客户端请求TransCommit建模 -->
  <DataModel name="reqTransCommit" ref="CommonModels:gmdbCommonReq"> <!--引用公共 -->
		<Number size="16" name="requestBody.MsgHeaderT.serviceId" valueType="hex" value="0001" />
		<Number size="32" name="requestBody.MsgHeaderT.serialNumber" valueType="hex" value="00000002"/>
		<Block name="requestBody.operationBody"> <!--重写-->
			<Number size="32" name="TransCommitopCode" value="##MSG_OP_RPC_TX_COMMIT##" mutable="false"/>
			<Number size="32" name="TransCommitMsgLen" valueType="hex">
				<Relation type="size" of="operationBody"/>
			</Number>
	    </Block>
   </DataModel>

   	<!--服务端对客户端的TransCommit请求响应-->
    <DataModel name="reqTransCommitAck" ref="CommonModels:gmdbCommonReqAck" >
		<Block name="requestBodyAck.operationBodyAck"> <!--重写-->
			<Blob name="reqTransCommitAckMessage"/>
		</Block>
    </DataModel>

	<!--客户端请求释放stmt数据建模-->
	<DataModel name="reqFreeStmt" ref="CommonModels:commonFreeStmt" >
	</DataModel>

	<!--客户端请求断开连接请求数据建模 -->
    <DataModel name="reqDisConnect" ref="CommonModels:commonDisConnect" >
   </DataModel>

	<!--服务端客户端的断连请求响应-->
	<DataModel name="reqDisConnectAck" ref="CommonModels:commonDisConnectAck" />
</Secray>
