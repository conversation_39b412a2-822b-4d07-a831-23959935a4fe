# coding=utf-8
import argparse
import re
import os
import zipfile


def list_files(path):
    '''
    List all files in the path
    '''

    ret = []
    if not os.path.exists(path):
        print(u"Error:not exists path:{0}".format(path))
        return ret

    for root, dirs, files in os.walk(os.path.abspath(path)):
        for f in files:
            ret.append(os.path.join(root, f))
    return ret


def list_dirs(path):
    '''
    List all files in the path
    '''

    ret = []
    if not os.path.exists(path):
        print(u"Error:not exists path:{0}".format(path))
        return ret

    for root, dirs, files in os.walk(os.path.abspath(path)):
        for f in dirs:
            ret.append(os.path.join(root, f))
    return ret


def search_files(pattern, path):
    '''
    Search the matched files in the path
    :return:list of absolute path
    '''

    func = lambda f: re.match(pattern, f)
    return filter(func, list_files(path))


def search_dirs(pattern, path):
    '''
    Search the matched files in the path
    :return:list of absolute path
    '''

    func = lambda f: re.match(pattern, f)
    return filter(func, list_dirs(path))


def zip_file(case_dir, dst_dir):
    zip_name = os.path.join(dst_dir, os.path.basename(case_dir) + '.zip')
    z = zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED)
    for dir_path, dir_names, file_names in os.walk(case_dir):
        file_path = dir_path.replace(case_dir, '')
        file_path = file_path and file_path + os.sep or ''
        for file_name in file_names:
            z.write(os.path.join(dir_path, file_name), file_path + file_name)
    print(u'{0}修改压缩成功'.format(os.path.basename(case_dir)))
    z.close()


def unzip_file(src_dir):
    file_paths = list_files(src_dir)
    for i in file_paths:
        if i.endswith(u".zip"):
            unzip_to = os.path.join(os.path.dirname(i), os.path.basename(i).split(".")[0])
            f = zipfile.ZipFile(i, "r")
            for file in f.namelist():
                f.extract(file, unzip_to)
            f.close()
            os.remove(i)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(add_help=u"example: python multi_modify.py -s D:\V5\FUZZ\待整改 -o D:\V5\FUZZ\整改输出")
    parser.add_argument("-s", "--case_dir", type=str, help=u"待整改测试套和公共模型文件，配置文件目录")
    parser.add_argument("-o", "--out_dir", type=str, help=u"整改输出目录")
    args = parser.parse_args()

    out_dir = args.out_dir
    case_dir = args.case_dir

    out_dir = unicode(out_dir, "GB2312")
    case_dir = unicode(case_dir, "GB2312")

    if not os.path.exists(out_dir):
        os.makedirs(out_dir)

    # # 整改输出目录，整改后自动压缩，可以直接上传secray平台
    # out_dir = u"D:\V5\FUZZ\测试套整改\整改后\修改输出"
    #
    # # 待整改测试套和公共模型文件，配置文件目录
    # case_dir = u"D:\V5\FUZZ\测试套整改\整改后\测试套待整改"

    unzip_file(case_dir)
    file_paths = list_files(case_dir)

    count = 0
    for i in file_paths:
        if i.endswith(u".xml.config") and not i.endswith(u"Data.xml") and not i.endswith(
                u"State.xml") and os.path.basename(i) != "Common.xml.config":
            case_dir_path = os.path.dirname(i)
            case_name = os.path.basename(i).split(".")[0]

            DataModels_dir = os.path.join(case_dir_path, "DataModels")
            data_file = os.path.join(DataModels_dir, "{0}_Data.xml".format(case_name))
            dst_common_model = os.path.join(DataModels_dir, "CommonModels.xml")
            dst_config = os.path.join(case_dir_path, "{0}.xml.config".format(case_name))
            common_model = os.path.join(os.path.dirname(case_dir_path), "CommonModels.xml")
            common_config = os.path.join(os.path.dirname(case_dir_path), "Common.xml.config")

            config_text = ""
            common_model_text = ""
            data_text = ""
            with open(common_config, "r") as fp:
                config_text = fp.read()
            with open(common_model, "r") as fp:
                common_model_text = fp.read()

            with open(data_file, "r") as fp:
                data_text = fp.read()

            # 修改密码
            config_text = re.sub("ServerPassword.+?Password", "ServerPassword\" value=\"xxxxxx\" name=\"Password",
                                 config_text)
            config_text = re.sub("TargetIPv4.+?Target IPv4 Address",
                                 "TargetIPv4\" value=\"xx.xx.xx.xx\" name=\"Target IPv4 Address",
                                 config_text)
            # 修改路径
            data_text = re.sub("ns=\"CommonModels\".*?src=\"/root/.*?GMDBV5/test/sdv/fuzz/secFuzz/common/",
                               "ns=\"CommonModels\" src=\"file:##PitLibraryPath##/{0}/DataModels/".format(case_name),
                               data_text)

            with open(dst_common_model, "w") as fp:
                fp.write(common_model_text)

            with open(dst_config, "w") as fp:
                fp.write(config_text)

            with open(data_file, "w") as fp:
                fp.write(data_text)

            zip_file(case_dir_path, out_dir)
            count = count + 1
    print(u"完成修改：{0}".format(count))

