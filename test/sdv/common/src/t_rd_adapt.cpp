/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_adapt.c
 * Author: lushiguang
 * Create: 2023-06-15
 */

#include "t_rd_adapt.h"

#if defined RUN_DATACOM_HPE
int GmcSysview(int argc, char **argv, int(printFunc)(const char *format, ...))
{
    printf("[GmcSysview] in case of hpe_client, this function is removed.\n");
    return 0;
}
FILE *popen(const char *command, const char *type)
{
    printf("[popen] in case of hpe_client, this function is removed.\n");
    return NULL;
}
int system(const char *command)
{
    printf("[system] in case of hpe_client, this function is removed.\n");
    return 0;
}
double pow(double x, double y)
{
    printf("[pow] in case of hpe_client, this function is removed.\n");
    return 0;
}
double atof(const char *s)
{
    printf("[atof] in case of hpe_client, this function is removed.\n");
    return 3.14159;
}
#endif
