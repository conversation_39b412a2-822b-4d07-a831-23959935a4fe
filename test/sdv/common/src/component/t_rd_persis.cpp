/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 092_NEGRT
 */

#ifdef FEATURE_PERSISTENCE

#include "t_rd_persis.h"

int32_t TestBufferpoolPersistCompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    std::vector<uint8_t> buf(compressBound(srcSize), 0);
    unsigned long outLen = buf.size();
    auto ret = compress2(buf.data(), &outLen, src, srcSize, Z_BEST_SPEED);
    if (outLen > *destSize) {
        return -1;
    }
 
    (void)memcpy_s(dest, *destSize, buf.data(), outLen);
    *destSize = outLen;
    return ret;
}
 
int32_t TestBufferpoolPersistDecompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    unsigned long outLen = *destSize;
    auto ret = uncompress(dest, &outLen, src, srcSize);
    *destSize = outLen;
    return ret;
}
 
int32_t TestBufferpoolPersistFileNameFilterCallBack(const char *src, char *dest, uint32_t destSize)
{
    uint32_t i = 0;
    for (i = 0; src[i] != '\0'; i++) {
        dest[i] = tolower(src[i]);
    }
    dest[i] = '\0';
    return GMERR_OK;
}

void TestBufferpoolRegAdaptFuncsProc(Status expectRet)
{
    GmAdptFuncsHandle handle = NULL;
    int32_t ret = GmCreateAdptFuncsHandle(&handle);
    ASSERT_EQ(ret, STATUS_OK);
    // TCP
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_PERSIST_COMPRESS, (GmAdptFunc)TestBufferpoolPersistCompressCallBack);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_PERSIST_DECOMPRESS, (GmAdptFunc)TestBufferpoolPersistDecompressCallBack);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_PERSIST_FILE_FILTER, (GmAdptFunc)TestBufferpoolPersistFileNameFilterCallBack);
    ret |= GmRegAdaptFuncs(handle);
    GmDestroyAdptFuncsHandle(&handle);
    // 避免重复注册报错
    if (ret != GMERR_DUPLICATE_OBJECT) {
        ASSERT_EQ(ret, STATUS_OK);
    }
}

#endif
