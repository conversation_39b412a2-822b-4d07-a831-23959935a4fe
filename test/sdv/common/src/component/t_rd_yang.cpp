/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_yang.c
 * Author: lushiguang
 * Create: 2023-06-15
 */

#include "t_rd_yang.h"
#include "t_rd_tree.h"
#include "gmc_tablespace.h"
#include "gmc_namespace.h"

#ifdef FEATURE_YANG

bool g_printDiffFlag = true;

bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA); // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB); // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}
bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}

void AsyncFetchRetCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    EXPECT_EQ(param->expectStatus, status) << errMsg;
    if (param->expectStatus != status || status != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        param->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    uint32_t index = param->lastExpectIdx;
    if ((param->filterMode == GMC_FETCH_JSON) || (param->filterMode == GMC_FETCH_JSON_RFC7951)
        || (param->filterMode == GMC_FETCH_FULL_JSON_RFC7951)) {
        const char **jsonReply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(jsonReply != NULL);
        if (param->expectReply.size() != 0) {
            ASSERT_TRUE(testYangJsonIsEqual(jsonReply[0], param->expectReply[index].c_str())) <<
                "replyJson:\n" << jsonReply[0] << "\nexpectJson:\n" << param->expectReply[index] << endl;
        }

        if (isEnd) {
            param->step++;
            if (param->data != NULL) {
                param->data->recvNum++;
            }
            GmcYangFreeFetchRet(fetchRet);
            return;
        }

        param->lastExpectIdx = index + count;
        ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCb, param));
        return;
    }
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[i].c_str(), "{}");
            continue;
        }
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));
        ASSERT_TRUE(testYangJsonIsEqual(reply, param->expectReply[i].c_str())) << "\nreplyJson:\n" << reply <<
            "\nexpectJson:\n" << param->expectReply[i] << endl;
        GmcYangFreeTree(yangTree[i]);
    }
    param->step++;
    if (param->data != NULL) {
        param->data->recvNum++;
    }
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void AsyncFetchRetCbMulti(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    EXPECT_EQ(param->expectStatus, status) << errMsg;
    if (param->expectStatus != status || status != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        param->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    uint32_t index = param->lastExpectIdx;
    if ((param->filterMode == GMC_FETCH_JSON) || (param->filterMode == GMC_FETCH_JSON_RFC7951)
        || (param->filterMode == GMC_FETCH_FULL_JSON_RFC7951)) {
        const char **jsonReply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(jsonReply != NULL);
        if (param->expectReply.size() != 0) {
            ASSERT_TRUE(testYangJsonIsEqual(jsonReply[0], param->expectReply[index].c_str())) <<
                "replyJson:\n" << jsonReply[0] << "\nexpectJson:\n" << param->expectReply[index] << endl;
        }

        if (isEnd) {
            param->step++;
            if (param->data != NULL) {
                param->data->recvNum++;
            }
            GmcYangFreeFetchRet(fetchRet);
            return;
        }

        param->lastExpectIdx = index + count;
        ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCb, param));
        return;
    }
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));

    AW_FUN_Log(LOG_INFO, "index = %d, param->lastExpectIdx = %d,count = %d, isEnd = %d.",
        index, param->lastExpectIdx, count, isEnd);

    ASSERT_TRUE(yangTree != NULL);
    char *reply = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[0], &reply));
    ASSERT_TRUE(testYangJsonIsEqual(reply, param->expectReply[index].c_str())) << "\nreplyJson:\n" << reply <<
        "\nexpectJson:\n" << param->expectReply[index] << endl;
    GmcYangFreeTree(yangTree[0]);

    if (isEnd) {
        param->step++;
        if (param->data != NULL) {
            param->data->recvNum++;
        }
        GmcYangFreeFetchRet(fetchRet);
        return;
    }

    param->lastExpectIdx = index + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCbMulti, param));

    return;
}

void AsyncFetchRetCbNoData(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    ASSERT_EQ(param->expectStatus, status) << errMsg;
    if (param->expectStatus != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    if ((param->filterMode == GMC_FETCH_JSON) || (param->filterMode == GMC_FETCH_JSON_RFC7951)
        || (param->filterMode == GMC_FETCH_FULL_JSON_RFC7951)) {
        const char **jsonReply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(isEnd);
        ASSERT_EQ((uint32_t)param->expectReply.size(), count);
        ASSERT_TRUE(jsonReply != NULL);
        for (uint32_t i = 0; i < count; i++) {
            ASSERT_TRUE(testYangJsonIsEqual(jsonReply[i], param->expectReply[i].c_str())) <<
                "replyJson:\n" << jsonReply[i] << "\nexpectJson:\n" << param->expectReply[i] << endl;
        }

        param->step++;
        if (param->data != NULL) {
            param->data->recvNum++;
        }
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            printf("No data is found in the subtree query result.\n");
            GmcYangFreeTree(yangTree[i]);
            break;
        }
        // 可能出现预期查询结果为空，但实际查询到数据的情况
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));
        ASSERT_TRUE(testYangJsonIsEqual(reply, param->expectReply[i].c_str())) <<
            "replyJson:\n" << reply << "\nexpectJson:\n" << param->expectReply[i] << endl;
        GmcYangFreeTree(yangTree[i]);
    }
    param->step++;
    if (param->data != NULL) {
        param->data->recvNum++;
    }
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void AsyncFetchRetCbEncode(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    ASSERT_EQ(param->expectStatus, status) << errMsg;
    if (param->expectStatus != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        param->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    uint32_t index = param->lastExpectIdx;
    if ((param->filterMode == GMC_FETCH_JSON) || (param->filterMode == GMC_FETCH_JSON_RFC7951)
        || (param->filterMode == GMC_FETCH_FULL_JSON_RFC7951)) {
        const char **jsonReply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(jsonReply != NULL);

        if (param->expectReply.size() != 0) {
            ASSERT_TRUE(testYangJsonIsEqual(jsonReply[0], param->expectReply[index].c_str())) <<
                "replyJson:\n" << jsonReply[0] << "\nexpectJson:\n" << param->expectReply[index] << endl;
        }

        if (isEnd) {
            param->step++;
            if (param->data != NULL) {
                param->data->recvNum++;
            }
            GmcYangFreeFetchRet(fetchRet);
            return;
        }

        param->lastExpectIdx = index + count;
        ASSERT_EQ(GMERR_OK,
            GmcYangSubtreeFilterExtExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCbEncode, param));
        return;
    }
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[i].c_str(), "{}");
            continue;
        }
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));
        ASSERT_TRUE(testYangJsonIsEqual(reply, param->expectReply[i].c_str())) << "\nreplyJson:\n" << reply <<
            "\nexpectJson:\n" << param->expectReply[i] << endl;
        GmcYangFreeTree(yangTree[i]);
    }
    param->step++;
    if (param->data != NULL) {
        param->data->recvNum++;
    }
    GmcYangFreeFetchRet(fetchRet);
    return;
}

int testWaitAsyncSubtreeRecv_API(void *userData, int expRecvNum, int timeout, bool isAutoReset)
{
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    FetchRetCbParam *userdata1 = (FetchRetCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;  // duration单位为us
        if (duration >= 80 * 1000 * 1000) {  // 80s超时, 先不使用timeout参数, 观察对用例的影响
            printf("[INFO] Recv Timeout %lf s\n", (double)duration / 1000000);
            userdata1->step = 0;
            return FAILED;  // 接收超时
        }
    }
    userdata1->step = 0;
    return 0;
}

int TestWaitAsyncSubtreeRecvAPIOneThread(void *userData, int expRecvNum, int timeout, bool isAutoReset, int32_t epollFd)
{
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    TEST_EPOLL_EVENT events[MAX_EPOLL_EVENT_COUNT];
    FetchRetCbParam *uData = (FetchRetCbParam *)userData;
    while (uData->step != expRecvNum) {
        int fdCount = TEST_EPOLL_WAIT(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
        }
        usleep(10);
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;  // duration单位为us
        if (duration >= 80 * 1000 * 1000) {  // 80s超时, 先不使用timeout参数, 观察对用例的影响
            AW_FUN_Log(LOG_INFO, "[INFO] Recv Timeout %lf s, all OpNum : %d,actually recived num : %d\n",
                (double)duration / 1000000, expRecvNum, uData->step);
            if (isAutoReset) {
                uData->step = 0;
            }
            return FAILED;  // 接收超时
        }
    }
    if (isAutoReset) {
        uData->step = 0;
    }
    return 0;
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void TestYangValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;
        uData->recvNum++;
    }
}

int TestWaitYangValidateRecv(void *userData, int expRecvNum, int timeout, bool isAutoReset)
{
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
    while (uData->recvNum != expRecvNum) {
        usleep(10);
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;  // duration单位为us
        if (duration >= 80 * 1000 * 1000) {  // 80s超时, 先不使用timeout参数, 观察对用例的影响
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d, actually recived num : %d\n", (double)duration / 1000000,
                expRecvNum, uData->recvNum);
            if (isAutoReset) {
                uData->recvNum = 0;
            }
            return FAILED;  // 接收超时
        }
    }
    if (isAutoReset) {
        uData->recvNum = 0;
    }
    return 0;
}

int TestWaitYangValidateRecvOneThread(void *userData, int expRecvNum, bool isAutoReset, int timeout, int32_t epollFd)
{
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    TEST_EPOLL_EVENT events[MAX_EPOLL_EVENT_COUNT];
    YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
    while (uData->recvNum != expRecvNum) {
        int fdCount = TEST_EPOLL_WAIT(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
        }
        usleep(10);
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;  // duration单位为us
        if (duration >= 80 * 1000 * 1000) {  // 80s超时, 先不使用timeout参数, 观察对用例的影响
            AW_FUN_Log(LOG_INFO, "[INFO] Recv Timeout %lf s, all OpNum : %d,actually recived num : %d\n",
                (double)duration / 1000000, expRecvNum, uData->recvNum);
            if (isAutoReset) {
                uData->recvNum = 0;
            }
            return FAILED;  // 接收超时
        }
    }
    if (isAutoReset) {
        uData->recvNum = 0;
    }
    return 0;
}

void testYangAsyncFetchRetCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void testYangProcessSubtreeRet(TestFetchRetCbParamT *param, GmcFetchRetT *fetchRet);
void testYangPrintNodeValue(const GmcYangNodeValueT *nodeValue, const char *name);
void testYangGetPrveNode(GmcYangNodeT *info, GmcDiffOpTypeE opType, bool isNewData);
void testYangawYangGetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData);
void testYangGetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res);
int32_t testYangDFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr);
void testYangProcessDiffRet(TestFetchRetCbParamT *param, GmcFetchRetT *fetchRet);

void testYangResetAsyncData(AsyncUserDataT *data);

int32_t testYangBatchExecuteAsyncInner(GmcBatchT *batch, GmcBatchDoneT userCb = batch_execute_callback,
    void *userData = NULL);
int32_t testYangTransStartAsync(GmcConnT *conn, const GmcTxConfigT *config = NULL,
    GmcTransDoneT userCb = trans_start_callback, void *userData = NULL);
int32_t testYangTransCommitAsync(GmcConnT *conn, GmcTransDoneT userCb = trans_commit_callback, void *userData = NULL);
int32_t testYangTransRollbackAsync(GmcConnT *conn, GmcTransDoneT userCb = trans_rollback_callback,
    void *userData = NULL);

int32_t testYangCreateTablespaceAsync(GmcStmtT *stmt, const char *userName, const char *tablespaceName = NULL,
    GmcTspCfgT *tspCfg = NULL, GmcTransDoneT userCb = create_tablespace_callback, void *userData = NULL,
    int expectStatus = GMERR_OK);

int32_t testYangBindNamespaceToTableSpaceAsync(GmcStmtT *stmt, const char *namespaceName = NULL,
    const char *tablespaceName = NULL, GmcTransDoneT userCb = create_namespace_callback, void *userData = NULL,
    int expectStatus = GMERR_OK);
void testYangInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size);
int32_t testYangSetNodeProperty(GmcNodeT *node, GmcPropValueT *propValue, GmcYangPropOpTypeE optype);
int32_t testYangSetRootVertex(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt);
int32_t testYangBindChildVertex(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt,
    GmcStmtT *childStmt);

int32_t testYangSetVertexLabelAndBatchAddDML(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op,
    GmcPropValueT *propertyValue, GmcYangPropOpTypeE opType, TestYangLabelType type, GmcStmtT *rootStmt,
    GmcStmtT *childStmt);
int32_t testYangTransOperAsyncInner(TestYangTransOper op, GmcConnT *conn, const GmcTxConfigT *config = NULL,
    GmcTransDoneT userCb = NULL, void *userData = NULL);
void testYangSetTrxConfigDefaultValue(GmcTxConfigT *trxConfig);

static int g_epollFd = -1;
const uint32_t sleepTime = 1000;
const uint32_t timeoutTimes = 120;

const char *g_emptyJson = "{}";
std::vector<std::string> g_emptyReplys = {g_emptyJson};

const char *testYangDiffOpTypeString[] = {
    "invalid",
    "update",
    "remove",
    "create"
};

const char *testYangNodeTypeString[] = {
    "field",
    "container",
    "list",
    "choice",
    "case",
    "leaflist"
};

static bool g_extraTest = true;

void testYangPrintNodeValue(const GmcYangNodeValueT *nodeValue, const char *name)
{
    if (nodeValue->type == GMC_DATATYPE_STRING) {
        AW_FUN_Log(LOG_DEBUG_ALL, "propName is %s, propValue is %s", name, (char *)nodeValue->value);
    } else {
        AW_FUN_Log(LOG_DEBUG_ALL, "propName is %s, propValue is %d", name, *(int32_t *)(nodeValue->value));
    }
}

int32_t testYangPrintField(GmcYangNodeT *curInfo, const char *name)
{
    GmcYangNodeValueT *nodeValue = NULL;
    // subtree使用old value接口获取值
    int32_t ret = GmcYangNodeGetOldValue(curInfo, &nodeValue);
    if (ret != GMERR_OK || nodeValue == NULL) {
        AW_FUN_Log(LOG_ERROR, "get property value failed. retcode:%d", ret);
        return ret;
    }
    testYangPrintNodeValue(nodeValue, name);
    if (nodeValue->isDefault) {
        AW_FUN_Log(LOG_DEBUG_ALL, "this is default value");
    }
    return GMERR_OK;
}

int32_t testYangPrintCurrentNode(GmcYangNodeT *curInfo)
{
    GmcYangNodeTypeE nodeType;
    int32_t ret = GmcYangNodeGetType(curInfo, &nodeType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "get node type failed. retcode:%d", ret);
        return ret;
    }
    const char *nodeName = NULL;
    // 获取yang tree node节点名称，可能是属性字段，可能是node/container/list
    ret = GmcYangNodeGetName(curInfo, &nodeName);
    if (ret != GMERR_OK || nodeName == NULL) {
        AW_FUN_Log(LOG_ERROR, "get node name failed. retcode:%d", ret);
        return ret;
    }
    // 属性类型，其value就是属性值，打印属性名和属性值
    if (nodeType == GMC_YANG_FIELD) {
        return testYangPrintField(curInfo, nodeName);
    } else {
        AW_FUN_Log(LOG_DEBUG_ALL, "node type is %s, node name is %s", testYangNodeTypeString[nodeType], nodeName);
    }
    GmcYangNodeT *childInfo = NULL;
    GmcYangNodeT *prevChild = NULL;
    // 获取下一个节点，可能是属性字段，可能是node/container/list
    ret = GmcYangNodeGetNext(curInfo, prevChild, &childInfo);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "get next node failed. retcode:%d", ret);
        return ret;
    }

    while (childInfo != NULL) {
        ret = testYangPrintCurrentNode(childInfo);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "PrintCurrentNode failed. retcode:%d", ret);
            return ret;
        }
        prevChild = childInfo;
        ret = GmcYangNodeGetNext(curInfo, prevChild, &childInfo);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "get next node failed. retcode:%d", ret);
            return ret;
        }
    }
    return GMERR_OK;
}

int32_t testYangPrintYangTree(const GmcYangTreeT *reply)
{
    GmcYangNodeT *rootNode = NULL;
    int32_t ret = GmcYangGetRootNode(reply, &rootNode);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "get root node failed. retcode:%d", ret);
        return ret;
    }

    ret = testYangPrintCurrentNode(rootNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

void testYangProcessSubtreeJsonRet(TestFetchRetCbParamT *param, GmcFetchRetT *fetchRet)
{
    int32_t ret = GMERR_OK;
    uint32_t count = 0;
    bool isEnd = false;
    const char **jsonReply = NULL;
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeJsonRet]isValidate:%u", param->isValidate);
    EXPECT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    // EXPECT_TRUE(isEnd); // 临时注释掉
    EXPECT_TRUE(jsonReply != NULL);
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeJsonRet]isEnd:%d, count:%u", isEnd, count);
    if (param->isValidate && param->expectReply != NULL) {
        EXPECT_EQ((uint32_t)(*param->expectReply).size(), count);
    }
    for (uint32_t i = 0; i < count; i++) {
        if (param->isValidate && param->expectReply != NULL) {
            string &expect = (*param->expectReply)[i];
            EXPECT_TRUE(testYangJsonIsEqual(jsonReply[i], expect.c_str())) <<
                "replyJson:\n" << jsonReply[i] << "\nexpectJson:\n" << expect << endl;
        }
    }
    GmcYangFreeFetchRet(fetchRet);
    (*(param->received))++;
    param->data.recvNum = param->data.recvNum + 1;
    return;
}

void testYangProcessSubtreeObjRet(TestFetchRetCbParamT *param, GmcFetchRetT *fetchRet)
{
    int32_t ret = GMERR_OK;
    uint32_t count = 0;
    bool isEnd = false;

    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeObjRet]isValidate:%u", param->isValidate);
    const GmcYangTreeT **yangTree = NULL;
    ret = GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[testYangProcessSubtreeObjRet]Parse subtree filter result failed. ret:%d", ret);
        GmcYangFreeFetchRet(fetchRet);
        (*(param->received))++;
        param->data.recvNum = param->data.recvNum + 1;
        return;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeObjRet]isEnd:%d, count:%u", isEnd, count);
    if (!isEnd) {
        AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeObjRet]Parse subtree filter result not end. No process. ret:%d", ret);
        GmcYangFreeFetchRet(fetchRet);
        (*(param->received))++;
        param->data.recvNum = param->data.recvNum + 1;
        return;
    }
    // EXPECT_TRUE(isEnd); // 临时注释掉
    EXPECT_TRUE(yangTree != NULL);
    if (param->isValidate && param->expectReply != NULL) {
        EXPECT_EQ((uint32_t)(*param->expectReply).size(), count);
    }
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeObjRet]replyJson[%u]:\n%s\n-----------------------------",
                i, "{}");
            if (param->isValidate && param->expectReply != NULL) {
                string &expect = (*param->expectReply)[i];
                EXPECT_TRUE(testYangJsonIsEqual("{}", expect.c_str())) << "replyJson:\n{}\n"
                                                                       <<
                    "\nexpectJson:\n" << expect << endl;
            }
            continue;
        }
        char *reply = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));
        AW_MACRO_EXPECT_NOTNULL(reply);
        if (reply == NULL) {
            GmcYangFreeTree(yangTree[i]);
            continue;
        }
        if (param->isValidate && param->expectReply != NULL) {
            string &expect = (*param->expectReply)[i];
            EXPECT_TRUE(testYangJsonIsEqual(reply, expect.c_str())) <<
                "replyJson:\n" << reply << "\nexpectJson:\n" << expect << endl;
        }
        GmcYangFreeTree(yangTree[i]);
    }

    if (!isEnd) {
        // 若isEnd为false，代表需要再次执行查询接口
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK,
            GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, testYangAsyncFetchRetCb, param));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessSubtreeObjRet]GmcYangSubtreeFilterExecuteAsync failed. ret:%d", ret);
            return;
        }
    } else {
        // isEnd为true，销毁fetchRet，停止异步等待
        GmcYangFreeFetchRet(fetchRet);
        (*(param->received))++;
        param->data.recvNum = param->data.recvNum + 1;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessSubtreeObjRet]end recvNum:%d, step:%u", param->data.recvNum,
        (*(param->step)));
}

void testYangProcessSubtreeRet(TestFetchRetCbParamT *param, GmcFetchRetT *fetchRet)
{
    if (param->subtreeFilters->filterMode == GMC_FETCH_JSON || param->subtreeFilters->filterMode == GMC_FETCH_FULL_JSON) {
        testYangProcessSubtreeJsonRet(param, fetchRet);
    } else {
        testYangProcessSubtreeObjRet(param, fetchRet);
    }
}

void testYangGetPrveNode(GmcYangNodeT *info, GmcDiffOpTypeE opType, bool isNewData)
{
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    int32_t ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return;
    }
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "get new prev failed, ret:%d", ret);
        return;
    }
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "get old prev failed, ret:%d", ret);
        return;
    }
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            return;
        }
        uint32_t propNum = 0;
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return;
        }
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "get key prop num failed, ret:%d", ret);
            return;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            GmcYangNodeValueT *propValue = NULL;
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "get prev key failed, ret:%d", ret);
                return;
            }
            testYangPrintNodeValue(propValue, propValue->name);
        }
    }
    return;
}

void testYangawYangGetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    GmcDiffOpTypeE opType;
    int32_t ret = GmcYangNodeGetDiffOpType(info, &opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "get op type failed, ret:%d", ret);
        return;
    }
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        // 先获取类型

        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return;
        }
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "get key prop num failed, ret:%d", ret);
            return;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = GmcYangNodeGetKeyPropValue(info, i, &propValue);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "get key prop value failed, ret:%d", ret);
                return;
            }
            AW_FUN_Log(LOG_DEBUG_ALL, "this next %d is primary key:", propNum);
            testYangPrintNodeValue(propValue, propValue->name);
        }
    }
    testYangGetPrveNode(info, opType, isNewData);
}

string awYangGetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_BOOL:
            return (*(const bool *)value->value) ? "true" : "false";
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string awYangGetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    Status ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + awYangGetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + awYangGetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

string awYangGetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

void testYangGetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += awYangGetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + awYangGetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + awYangGetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + awYangGetValueString(newValue) + "," + awYangGetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(awYangGetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + awYangGetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(awYangGetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + awYangGetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
int32_t testYangDFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    do {
        int32_t ret = GmcYangNodeGetNext(parent, prevChild, &child);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "get next node failed ret:%d", ret);
            return ret;
        }
        prevChild = child;
        if (child != NULL) {
            const char *nodeName;
            ret = GmcYangNodeGetName(child, &nodeName);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "get node name failed ret:%d", ret);
                return ret;
            }
            testYangGetYangInfoString(stmt, child, prefix, resStr);
            ret = testYangDFSYangNode(stmt, child, prefix, resStr);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "get next node failed ret:%d", ret);
                return ret;
            }
        }
    } while (child != NULL);

    return GMERR_OK;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string awYangStrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void testYangProcessDiffRet(TestFetchRetCbParamT *param, GmcFetchRetT *fetchRet)
{
    int32_t ret = GMERR_OK;
    GmcYangNodeT *rootInfo = NULL;
    uint32_t count = 0;
    bool isEnd = false;
    const GmcYangTreeT **yangTree = NULL;

    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]isValidate:%u", param->isValidate);
    ret = GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
    if (ret != GMERR_OK) {
        (*(param->received))++;
        param->data.recvNum = param->data.recvNum + 1;
        if (ret == 1004005 && yangTree == NULL) {
            AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]Parse diff result yangTree NULL, ret:%d", ret);
        } else {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessDiffRet]Parse diff result failed. ret:%d", ret);
        }
        return;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]Parse diff result:isEnd:%d, count:%d", isEnd, count);
    if (yangTree == NULL) {
        AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]Parse diff reply yang tree NULL");
    }
    if (param->isValidate && param->expectReply != NULL) {
        ASSERT_EQ((uint32_t)(*param->expectReply).size(), count);
    }
    // diff tree可能有多棵
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcYangGetRootNode(yangTree[i], &rootInfo);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessDiffRet]get root node failed ret:%d", ret);
            return;
        }
        string res;
        const char *rootName = NULL;
        ret = GmcYangNodeGetName(rootInfo, &rootName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessDiffRet]get node name failed ret:%d", ret);
            EXPECT_EQ(GMERR_OK, GmcYangFreeTree(yangTree[i]));
            return;
        }
        testYangGetYangInfoString(param->stmt, rootInfo, rootName + string(""), res);
        ret = testYangDFSYangNode(param->stmt, rootInfo, rootName + string(""), res);
        if (ret == GMERR_OUT_OF_MEMORY) {
            EXPECT_EQ(GMERR_OK, GmcYangFreeTree(yangTree[i]));
            return;
        }
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessDiffRet]testYangDFSYangNode failed ret:%d", ret);
            EXPECT_EQ(GMERR_OK, GmcYangFreeTree(yangTree[i]));
            return;
        }
        char fileName[128] = {0};
        sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            printf("Open file error!\n");
            return;
        }
        fputs(res.c_str(), fp);
        fclose(fp);
        AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]diff reply[%u]:\n%s\n--------------------------", i, res.c_str());

        if (param->isValidate && param->expectReply != NULL) {
            AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]diff expect:\n%s\n--------------------------", (*param->expectReply)[i].c_str());
            ret = strcmp(awYangStrCmp((*param->expectReply)[i], res).c_str(), "");
            EXPECT_EQ(ret, 0);
            if ( ret != 0) {
                AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]diff validate:%s", "check fail");
            } else {
                AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]diff validate:%s", "check ok");
            }
        }
        ret = GmcYangFreeTree(yangTree[i]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessDiffRet]free yang tree failed ret:%d", ret);
            return;
        }
    }
    if (!isEnd) {
        // 若isEnd为false，代表需要再次执行查询接口
        AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]no end recvNum:%d, step:%u", param->data.recvNum, *(param->step));
        ret = GmcYangFetchDiffExecuteAsync(param->stmt, NULL, testYangAsyncFetchRetCb, param);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testYangProcessDiffRet]GmcYangFetchDiffExecuteAsync failed. ret:%d", ret);
            return;
        }
    } else {
        // isEnd为true，销毁fetchRet，停止异步等待
        GmcYangFreeFetchRet(fetchRet);
        (*(param->received))++;
        param->data.recvNum = param->data.recvNum + 1;
        AW_FUN_Log(LOG_DEBUG_ALL, "[testYangProcessDiffRet]end recvNum:%d, step:%u", param->data.recvNum, *(param->step));
    }
}

void testYangAsyncFetchRetCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    TestFetchRetCbParamT *param = (TestFetchRetCbParamT *)userData;
    EXPECT_NE((param != NULL), 0);
    (*(param->step))++;
    param->actualStatus = status;
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangAsyncFetchRetCb]expectStatus:%d, isSubtree:%u", param->expectStatus, param->isSubtree);
    if (param->expectStatus != GMERR_OK) {
        EXPECT_EQ(param->expectStatus, status);
        EXPECT_NE((errMsg != NULL), 0);
        EXPECT_NE(strcmp(errMsg, ""), 0);
        if (errMsg != NULL) {
            AW_FUN_Log(LOG_INFO, "[testYangAsyncFetchRetCb]status:%d, errMsg is %s", status, errMsg);
        }
        param->data.recvNum = param->data.recvNum + 1;
        return;
    }

    if (param->expectStatus != status) {
        EXPECT_NE((errMsg != NULL), 0);
        EXPECT_NE(strcmp(errMsg, ""), 0);
        if (errMsg != NULL) {
            AW_FUN_Log(LOG_ERROR, "[testYangAsyncFetchRetCb]status:%d, errMsg is %s", status, errMsg);
        }
    }

    // 获取subtree或diff返回结果
    if (param->isSubtree) {
        testYangProcessSubtreeRet(param, fetchRet);
    } else {
        testYangProcessDiffRet(param, fetchRet);
    }
    return;
}

void AW_YANG_ADD_ERR_WHITE_LIST(uint32_t errCode)
{
    const uint32_t errCodeLen = 256;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%u", errCode);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

int32_t awYangFetchDiff(GmcStmtT *stmt, AsyncUserDataT *data, std::vector<std::string> *expectReply, bool needCheckReply, int32_t expectStatus, GmcYangFetchExecuteDoneT userCb, YangConnOptionT *connOptionsInput, int32_t epollFd)
{
    int received = 0;
    uint32_t step = 0;
    TestFetchRetCbParamT param = {
        .received = &received,
        .stmt = stmt,
        .expectStatus = expectStatus,
        .actualStatus = -1,
        .isSubtree = false,
        .data = {0},
        .step = &step,
        .times = 0,
        .lastExpectIdx = 0,
        .expectReply = NULL,
        .isValidate = needCheckReply,
        .noParseTree = 0,
        .subtreeFilters = NULL
    };
    if (expectReply != NULL) {
        param.expectReply = expectReply;
    } else {
        param.isValidate = false;
    }
    if (userCb == NULL) {
        userCb = testYangAsyncFetchRetCb;
    }
    if (data != NULL) {
        memcpy_s(&(param.data), sizeof(param.data), data, sizeof(AsyncUserDataT));
    }
    param.data.status = -1;
    AW_YANG_ADD_ERR_WHITE_LIST(GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_DEBUG_ALL, "awYangFetchDiff begin #########################");
    // 执行diff查询，并通过回调函数解析查询结果
    int32_t ret = GMERR_OK;
    param.data.status = -1;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcYangFetchDiffExecuteAsync(stmt, NULL, userCb, &param));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcYangFetchDiffExecuteAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    int32_t epollFdInner = (connOptionsInput == NULL) ? epollFd : (*(connOptionsInput->epollFd));
    ret = testWaitAsyncRecvOneThread(&param.data, 1, true, timeoutTimes, epollFdInner);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, param.actualStatus);
    AW_FUN_Log(LOG_DEBUG_ALL, "awYangFetchDiff end ret:%d, status:%d #########################", ret, param.actualStatus);
    return param.actualStatus;
}

int32_t awYangQuerySubtree(GmcStmtT *stmt, AsyncUserDataT *data, GmcSubtreeFilterT *userFilters, std::vector<std::string> *expectReply, bool needCheckReply, int32_t expectStatus, GmcYangFetchExecuteDoneT userCb, YangConnOptionT *connOptionsInput, int32_t epollFd)
{
    int received = 0;
    uint32_t step = 0;
    TestFetchRetCbParamT param = {
        .received = &received,
        .stmt = stmt,
        .expectStatus = expectStatus,
        .actualStatus = -1,
        .isSubtree = true,
        .data = {0},
        .step = &step,
        .times = 0,
        .lastExpectIdx = 0,
        .expectReply = NULL,
        .isValidate = needCheckReply,
        .noParseTree = 0,
        .subtreeFilters = NULL
    };
    GmcSubtreeFilterT filters = { 0 };
    filters.filterMode = GMC_FETCH_FULL_OBJ;
    if (expectReply != NULL) {
        param.expectReply = expectReply;
    } else {
        param.isValidate = false;
    }
    if (userFilters == NULL) {
        param.subtreeFilters = &filters;
    } else {
        param.subtreeFilters = userFilters;
    }
    if (userCb == NULL) {
        userCb = testYangAsyncFetchRetCb;
    }
    if (data != NULL) {
        memcpy_s(&(param.data), sizeof(param.data), data, sizeof(AsyncUserDataT));
    }
    param.data.status = -1;

    AW_FUN_Log(LOG_DEBUG_ALL, "awYangQuerySubtree begin #########################");
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK,
        GmcYangSubtreeFilterExecuteAsync(stmt, param.subtreeFilters, NULL, userCb, &param));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcYangSubtreeFilterExecuteAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    int32_t epollFdInner = (connOptionsInput == NULL) ? epollFd : (*(connOptionsInput->epollFd));
    ret = testWaitAsyncRecvOneThread(&param.data, 1, true, timeoutTimes, epollFdInner);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.actualStatus);
    AW_FUN_Log(LOG_DEBUG_ALL, "awYangQuerySubtree end ret:%d, status:%d #########################", ret, param.actualStatus);
    return param.actualStatus;
}

void testYangResetAsyncData(AsyncUserDataT *data)
{
    memset(&data, 0, sizeof(AsyncUserDataT));
}

int32_t testYangBatchExecuteAsyncInner(GmcBatchT *batch, GmcBatchDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    if (userCb == NULL) {
        userCb = batch_execute_callback;
    }
    ((AsyncUserDataT *)userData)->succNum = 0;
    ((AsyncUserDataT *)userData)->totalNum = 0;
    ((AsyncUserDataT *)userData)->status = -1;
    ret = GmcBatchExecuteAsync(batch, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcBatchExecuteAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    if (((AsyncUserDataT *)userData)->status == GMERR_OK) {
        AW_MACRO_EXPECT_NE_INT(((AsyncUserDataT *)userData)->succNum, 0);
        AW_MACRO_EXPECT_NE_INT(((AsyncUserDataT *)userData)->totalNum, 0);
        AW_MACRO_EXPECT_EQ_INT(((AsyncUserDataT *)userData)->succNum, ((AsyncUserDataT *)userData)->totalNum);
    }
    return ret;
}

int32_t awYangBatchExecuteAsync(GmcConnT *conn, GmcStmtT *stmt, GmcBatchT *batch, GmcBatchDoneT userCb, void *userData, uint32_t mode)
{
    if (batch == NULL) {
        AW_FUN_Log(LOG_ERROR, "invalid parameter batch");
        return -1;
    }

    if (userData == NULL) {
        AW_FUN_Log(LOG_ERROR, "invalid parameter userData");
        return -1;
    }
    if (userCb == NULL) {
        userCb = batch_execute_callback;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] beigin ======================================");
    int32_t ret = GMERR_OK;
    bool isNewTrans = false;
    ret = awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, conn, "yang_0816_000");
    if (ret == GMERR_NO_ACTIVE_TRANSACTION) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_START, conn, stmt));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, conn, "yang_0816_000"));
        isNewTrans = true;
    }

    ret = testYangBatchExecuteAsyncInner(batch, userCb, userData);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (((AsyncUserDataT *)userData)->status != GMERR_OK) {
        return ret;
    }
    if (((AsyncUserDataT *)userData)->succNum <= 0) {
        return ret;
    }
    if (!g_extraTest || mode != 0) {
        return ret;
    }
    GmcBatchOptionT option;
    bool diffOn = true;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcGetBatchOption(batch, &option));
    diffOn = ((option.diffType == GMC_YANG_DIFF_DELAY_READ_ON) ? true : false );
    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] step 0");
    // 0、提交批操作，查询diff0
    if (diffOn) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmt, NULL, ((AsyncUserDataT *)userData)->expectDiff, ((AsyncUserDataT *)userData)->needCheckDiff));
    }

    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] step 1");
    // 1、创建还原点sp1，查询subtree1（namespace全查）
    ret = awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, conn, "yang_0816_001");
    if (ret == GMERR_NO_ACTIVE_TRANSACTION) {
        // 未开启事务
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_START, conn, stmt));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, conn, "yang_0816_001"));
        isNewTrans = true;
    } else if (ret != GMERR_OK){
        AW_FUN_Log(LOG_ERROR, "awYangTransSavepointOp ret:%d", ret);
        return ret;
    }

    // 开启事务2
    GmcConnT *connForTrx2 = NULL;
    GmcStmtT *stmtForTrx2 = NULL;
    YangConnOptionT connOptions = {0};
    uint32_t conn2Ret = TestYangGmcConnectWithEpollOneThread(&connForTrx2, &stmtForTrx2, GMC_CONN_TYPE_ASYNC, &connOptions);
    if (conn2Ret == GMERR_OK) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangUseNamespaceAsync(stmtForTrx2, ((AsyncUserDataT *)userData)->namespaceName));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_START, connForTrx2, stmtForTrx2));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, connForTrx2, "yang_0816_001"));
    }

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));

    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] step 2");
    // 2、对根节点 delete graph 或者 namespace删除，查询diff2（删除），查询subtree2（=空）
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangDMLDemoRemoveRootInner(conn, stmt, ((AsyncUserDataT *)userData)->rootName, option.diffType));
    if (diffOn) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmt));
    }

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));
    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] step 3");
    // 3、创建还原点sp2，查询diff3，查询subtree3
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, conn, "yang_0816_002"));
    if (diffOn) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmt));
    }

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));

    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] step 4");
    // 4、回滚还原点sp1，查询diff4（=diff0），查询subtree4
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_ROLLBACK, conn, "yang_0816_001"));
    if (diffOn) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmt));
    }

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));

    // 5、释放还原点sp1
    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] step 5");
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_RELEASE, conn, "yang_0816_001"));
    if (isNewTrans) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_ROLLBACK, conn, stmt));
    }

    // 事务2操作
    if (conn2Ret == GMERR_OK) {
        // AW_YANG_EXPECT_EQ_INT(ret, 1004000, awYangFetchDiff(stmtForTrx2, NULL, NULL, false, 1004000)); // 报错1004000，Data exception occurs. Diff function disabled, or no edit operations executed.
        // AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmtForTrx2)); // 上面失败会导致subtree报错 1008000 Transaction rollback. It needs rollback when begin transaction.
        AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] TRX2 step 2");
        // 2、对根节点 delete graph 或者 namespace删除，查询diff2（删除），查询subtree2（=空）
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangDMLDemoRemoveRootInner(connForTrx2, stmtForTrx2, ((AsyncUserDataT *)userData)->rootName));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmtForTrx2));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmtForTrx2));
        AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] TRX2 step 3");
        // 3、创建还原点sp2，查询diff3，查询subtree3
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_CREATE, connForTrx2, "yang_0816_002"));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmtForTrx2));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmtForTrx2));

        AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] TRX2 step 4");
        // 4、回滚还原点sp1，查询diff4（=diff0），查询subtree4
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransSavepointOp(AW_YANG_SAVEPT_ROLLBACK, connForTrx2, "yang_0816_001"));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangFetchDiff(stmtForTrx2));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmtForTrx2));

        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_ROLLBACK, connForTrx2, stmtForTrx2));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testGmcDisconnect(connForTrx2, stmtForTrx2));
    }

    AW_FUN_Log(LOG_DEBUG_ALL, "[awYangBatchExecuteAsync] end ======================================");
    return ret;
}

void testYangSetTrxConfigDefaultValue(GmcTxConfigT *trxConfig)
{
    trxConfig->readOnly = false;
    trxConfig->transMode = GMC_TRANS_USED_IN_CS;
    trxConfig->type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig->trxType = GMC_OPTIMISTIC_TRX;
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangSetTrxConfigDefaultValue]transMode:%d, trxType:%d, type:%d",
    trxConfig->transMode, trxConfig->trxType, trxConfig->type);
}

void testYangSetTablespaceConfigDefaultValue(GmcTspCfgT *tablespaceCfg)
{
    tablespaceCfg->tablespaceName = "t_tsp_yang";
    tablespaceCfg->initSize = 0;
    tablespaceCfg->stepSize = 0;
    tablespaceCfg->maxSize = 4;
}

void testYangSetNamespaceConfigDefaultValue(GmcNspCfgT *namespaceCfg)
{
    GmcTxConfigT trxConfig;
    testYangSetTrxConfigDefaultValue(&trxConfig);
    namespaceCfg->trxCfg.trxType = trxConfig.trxType;
    namespaceCfg->trxCfg.isolationLevel = trxConfig.type;
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangSetNamespaceConfigDefaultValue]trxCfg.trxType:%d, trxCfg.isolationLevel:%d",
    namespaceCfg->trxCfg.trxType, namespaceCfg->trxCfg.isolationLevel);
}

int32_t testYangTransStartAsync(GmcConnT *conn, const GmcTxConfigT *config, GmcTransDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = trans_start_callback;
    }
    GmcTxConfigT trxConfig = {0};
    if (config == NULL) {
        testYangSetTrxConfigDefaultValue(&trxConfig);
        config = &trxConfig;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangTransStartAsync]config trxType:%d, isolation:%d", config->trxType, config->type);
    ((AsyncUserDataT *)userData)->succNum = 0;
    ((AsyncUserDataT *)userData)->totalNum = 0;
    ((AsyncUserDataT *)userData)->status = -1;
    ret = GmcTransStartAsync(conn, config, userCb, userData);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcTransStartAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    if (ret == GMERR_OK){
        ret = ((AsyncUserDataT *)userData)->status;
    }
    return ret;
}

int32_t testYangTransCommitAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = trans_commit_callback;
    }
    ((AsyncUserDataT *)userData)->succNum = 0;
    ((AsyncUserDataT *)userData)->totalNum = 0;
    ((AsyncUserDataT *)userData)->status = -1;
    ret = GmcTransCommitAsync(conn, userCb, userData);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcTransCommitAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    if (ret == GMERR_OK){
        ret = ((AsyncUserDataT *)userData)->status;
    }
    return ret;
}

int32_t testYangTransRollbackAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = trans_rollback_callback;
    }
    ((AsyncUserDataT *)userData)->succNum = 0;
    ((AsyncUserDataT *)userData)->totalNum = 0;
    ((AsyncUserDataT *)userData)->status = -1;
    ret = GmcTransRollBackAsync(conn, userCb, userData);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcTransRollBackAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    if (ret == GMERR_OK){
        ret = ((AsyncUserDataT *)userData)->status;
    }
    return ret;
}

int32_t awYangTransSavepointOp(TestYangSavepointOper op, GmcConnT *conn, const char *spName, GmcTransDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    if ((uint32_t)op >= AW_YANG_SAVEPT_BUTT || conn == NULL || spName == NULL) {
        return -1;
    }
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = trans_start_callback;
    }
    ((AsyncUserDataT *)userData)->succNum = 0;
    ((AsyncUserDataT *)userData)->totalNum = 0;
    ((AsyncUserDataT *)userData)->status = -1;
    ((AsyncUserDataT *)userData)->expTotalNum = 1;
    if (op == AW_YANG_SAVEPT_CREATE) {
        ret = GmcTransCreateSavepointAsync(conn, spName, userCb, userData);
    } else if (op == AW_YANG_SAVEPT_RELEASE) {
        ret = GmcTransReleaseSavepointAsync(conn, spName, userCb, userData);
    } else {
        ret = GmcTransRollBackSavepointAsync(conn, spName, userCb, userData);
    }
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "trans oper error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    if (ret == GMERR_OK){
        ret = ((AsyncUserDataT *)userData)->status;
    }
    return ret;
}

int32_t testYangTransOperAsyncInner(TestYangTransOper op, GmcConnT *conn, const GmcTxConfigT *config,
    GmcTransDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangTransOperAsyncInner]beigin, op:%d", op);
    if (op == AW_YANG_TRANS_COMMIT) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangTransCommitAsync(conn, userCb, userData));
    } else if (op == AW_YANG_TRANS_ROLLBACK) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangTransRollbackAsync(conn, userCb, userData));
    } else if (op == AW_YANG_TRANS_START) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangTransStartAsync(conn, config, userCb, userData));
    } else {
        ret = -1;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "[testYangTransOperAsyncInner] ret:%d, op:%d", ret, op);
    return ret;
}

int32_t awYangTransOpAsync(TestYangTransOper op, GmcConnT *conn, GmcStmtT *stmt, const GmcTxConfigT *config,
    GmcTransDoneT userCb, void *userData)
{
    int32_t ret = GMERR_OK;
    AW_FUN_Log(LOG_DEBUG_ALL, "awYangTransOpAsync begin, op:%d", op);

    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        asyncData.expStatus = GMERR_OK;
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (op != AW_YANG_TRANS_START && g_extraTest == true) {
        if (userData != NULL) {
            AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt, NULL, NULL, ((AsyncUserDataT *)userData)->expectReply, ((AsyncUserDataT *)userData)->needCheckSubtree));
        } else {
            AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));
        }
        AW_FUN_Log(LOG_DEBUG_ALL, "awYangTransOpAsync inner subtree end, ret:%d", ret);
    }

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangTransOperAsyncInner(op, conn, config, userCb, userData));
    AW_FUN_Log(LOG_DEBUG_ALL, "awYangTransOpAsync end, op:%d", op);

    if (!g_extraTest) {
        return ret;
    }
    if (op == AW_YANG_TRANS_START) {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));
    } else {
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangTransOperAsyncInner(AW_YANG_TRANS_START, conn));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangQuerySubtree(stmt));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangTransOperAsyncInner(AW_YANG_TRANS_ROLLBACK, conn));
    }

    return ret;
}

int32_t testYangClearNamespaceAsync(GmcStmtT *stmt, const char *namespaceName, GmcClearNamespaceDoneT userCb, void *userData,
    int expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = ClearNSCallbak;
    }
    ret = GmcClearNamespaceAsync(stmt, namespaceName, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcClearNamespaceAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t testYangTruncateNamespaceAsync(GmcStmtT *stmt, const char *namespaceName, GmcTruncateNamespaceDoneT userCb, void *userData,
    int expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = ClearNSCallbak;
    }
    ret = GmcTruncateNamespaceAsync(stmt, namespaceName, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "testYangTruncateNamespaceAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t testYangDropNamespaceAsync(GmcStmtT *stmt, const char *namespaceName,
    GmcDropNamespaceDoneT userCb, void *userData, int expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = ClearNSCallbak;
    }
    ret = GmcDropNamespaceAsync(stmt, namespaceName, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcDropNamespaceAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t testYangCreateTablespaceAsync(GmcStmtT *stmt, const char *userName, const char *tablespaceName,
    GmcTspCfgT *tspCfg, GmcTablespaceDoneT userCb, void *userData, int32_t expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = create_tablespace_callback;
    }

    GmcTspCfgT tablespaceCfg;
    if (tspCfg == NULL) {
        testYangSetTablespaceConfigDefaultValue(&tablespaceCfg);
        tspCfg = &tablespaceCfg;
    }
    ret = GmcCreateTablespaceAsync(stmt, tspCfg, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcCreateTablespaceAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t testYangCreateNamespaceWithCfgAsync(GmcStmtT *stmt, GmcNspCfgT *nspCfg, GmcCreateNamespaceDoneT userCb, void *userData, int32_t expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = create_namespace_callback;
    }

    GmcNspCfgT namespaceCfg = {0};
    if (nspCfg == NULL) {
        testYangSetNamespaceConfigDefaultValue(&namespaceCfg);
        nspCfg = &namespaceCfg;
    } else {
        testYangSetNamespaceConfigDefaultValue(nspCfg);
    }
    ret = GmcCreateNamespaceWithCfgAsync(stmt, nspCfg, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcCreateNamespaceWithCfgAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t testYangBindNamespaceToTableSpaceAsync(GmcStmtT *stmt, const char *namespaceName, const char *tablespaceName,
    GmcTablespaceDoneT userCb, void *userData, int expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = create_namespace_callback;
    }

    ret = GmcBindNamespaceToTableSpaceAsync(stmt, namespaceName, tablespaceName, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcBindNamespaceToTableSpaceAsync error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t testYangUseNamespaceAsync(GmcStmtT *stmt, const char *namespaceName, GmcUseNamespaceDoneT userCb, void *userData,
    int expectStatus)
{
    int32_t ret = GMERR_OK;
    AsyncUserDataT asyncData = { 0 };
    if (userData == NULL) {
        userData = (void *)(AsyncUserDataT *)&asyncData;
    }
    if (userCb == NULL) {
        userCb = use_namespace_callback;
    }
    ret = GmcUseNamespaceAsync(stmt, namespaceName, userCb, userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "use namespace error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }
    ret = testWaitAsyncRecvOneThread(userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ret);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, ((AsyncUserDataT *)userData)->status);
    return ret;
}

int32_t awYangBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType,
    GmcYangDiffTypeE diffType, GmcBatchOrderTypeE batchOrder, uint32_t batchLimitSize)
{
    int32_t ret = GMERR_OK;
    if (conn == NULL || batch == NULL) {
        return -1;
    }
    GmcBatchOptionT innerBatchOption;
    // 准备批量操作
    ret = GmcBatchOptionInit(&innerBatchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&innerBatchOption, batchOrder);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&innerBatchOption, batchLimitSize);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&innerBatchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&innerBatchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &innerBatchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    AW_FUN_Log(LOG_DEBUG_ALL, "batchOption:batchType(%d),diffType(%d),batchOrder(%d)",
        batchType, diffType, batchOrder);
    return ret;
}

int32_t testYangBatchDestroy(GmcBatchT *batch, AsyncUserDataT *userData)
{
    if (batch == NULL)
        return -1;
    int ret = GMERR_OK;
    ret = GmcBatchDestroy(batch);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcBatchDestroy error code:%d\n", ret);
        int getLasterrorRet = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, getLasterrorRet);
        return ret;
    }

    if (userData == NULL)
        return ret;
    memset(userData, 0, sizeof(AsyncUserDataT));
    userData = NULL;
    return ret;
}

void testYangInitPropValue(GmcPropValueT *propertyValue, const char *name, GmcDataTypeE type, void *value,
    uint32_t size)
{
    if (propertyValue == NULL || value == NULL) {
        return;
    }
    strcpy_s(propertyValue->propertyName, strlen(name) + 1, name);
    propertyValue->type = type;
    propertyValue->size = size;
    propertyValue->value = (void *)value;
}

int32_t awYangInitNodeProperty(GmcNodeT *node, const char *propName, void *value, GmcDataTypeE type, uint32_t size)
{
    int32_t ret = GMERR_OK;
    GmcPropValueT propValue = { 0 };
    testYangInitPropValue(&propValue, propName, type, value, size);
    GmcYangPropOpTypeE propOpType = GMC_YANG_PROPERTY_OPERATION_MERGE;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangSetNodeProperty(node, &propValue, propOpType));
    return ret;
}

int32_t testYangSetRootVertex(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt)
{
    int32_t ret = GMERR_OK;
    AW_YANG_PrepareLabel(ret, GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, labelName, op));
    AW_YANG_SetRoot(ret, GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    return ret;
}

// 绑定子vertex
int32_t testYangBindChildVertex(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt,
    GmcStmtT *childStmt)
{
    int32_t ret = GMERR_OK;
    AW_YANG_PrepareLabel(ret, GMERR_OK, GmcPrepareStmtByLabelName(childStmt, labelName, op));
    AW_YANG_BindChild(ret, GMERR_OK, GmcYangBindChild(batch, rootStmt, childStmt));
    return ret;
}

int32_t awYangPrepareVertexNode(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, TestYangLabelType type,
    GmcStmtT *rootStmt, GmcStmtT *childStmt, GmcNodeT **node)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *tmpStmt = NULL;
    if (node == NULL) {
        return -1;
    }
    *node = NULL;
    if (type == AW_YANG_TYPE_ROOT) {
        AW_YANG_PrepareLabel(ret, GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, labelName, op));
        AW_YANG_SetRoot(ret, GMERR_OK, GmcYangSetRoot(batch, rootStmt));
        tmpStmt = rootStmt;
    } else if ((type != AW_YANG_TYPE_ROOT) && (rootStmt != NULL)) {
        AW_YANG_PrepareLabel(ret, GMERR_OK, GmcPrepareStmtByLabelName(childStmt, labelName, op));
        AW_YANG_BindChild(ret, GMERR_OK, GmcYangBindChild(batch, rootStmt, childStmt));
        tmpStmt = childStmt;
    } else {
        return -1;
    }
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcGetRootNode(tmpStmt, node));

    return ret;
}

// 编辑node节点
int32_t awYangEditChildNode(GmcOperationTypeE opType, GmcNodeT *parentNode, const char *childName,
    GmcNodeT **childNode)
{
    int32_t ret = GMERR_OK;
    if (parentNode == NULL || childNode == NULL || *childNode != NULL) {
        AW_FUN_Log(LOG_ERROR, "invalid argument 111");
        return -1;
    }
    GmcNodeT *node = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcYangEditChildNode(parentNode, childName, opType, &node));
    *childNode = node;
    return ret;
}

int32_t testYangSetVertexProperty(GmcStmtT *stmt, GmcPropValueT *propertyValue, GmcYangPropOpTypeE opType)
{
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcYangSetVertexProperty(stmt, propertyValue, opType));
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", propertyValue->propertyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }
    return ret;
}

int32_t testYangSetVertexPropertyNew(GmcStmtT *stmt, GmcPropValueT *propertyValue, GmcYangPropOpTypeE opType)
{
    int32_t ret = GMERR_OK;
    GmcNodeT *parentNode = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcGetRootNode(stmt, &parentNode));
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "GmcGetRootNode, ret:%d\n", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcYangSetNodeProperty(parentNode, propertyValue, opType));
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", propertyValue->propertyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }
    return ret;
}

int32_t testYangSetNodeProperty(GmcNodeT *node, GmcPropValueT *propertyValue, GmcYangPropOpTypeE opType)
{
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcYangSetNodeProperty(node, propertyValue, opType));
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", propertyValue->propertyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }
    return ret;
}

int32_t testYangSetNodePropertyNew(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

int32_t testYangSetIndex(GmcStmtT *stmt, uint32_t index, GmcDataTypeE type, const char *keyName, const void *value, uint32_t valueSize)
{
    int32_t ret = 0;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcSetIndexKeyName(stmt, keyName));
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcSetIndexKeyValue(stmt, index, type, value, valueSize));
    return ret;
}

int32_t testYangSetVertexLabelAndBatchAddDML(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op,
    GmcPropValueT *propertyValue, GmcYangPropOpTypeE opType, TestYangLabelType type, GmcStmtT *rootStmt,
    GmcStmtT *childStmt)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *tmpStmt = NULL;
    if (type == AW_YANG_TYPE_ROOT) {
        AW_YANG_PrepareLabel(ret, GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, labelName, op));
        AW_YANG_SetRoot(ret, GMERR_OK, GmcYangSetRoot(batch, rootStmt));
        tmpStmt = rootStmt;
    } else if ((type != AW_YANG_TYPE_ROOT) && (rootStmt != NULL)) {
        AW_YANG_PrepareLabel(ret, GMERR_OK, GmcPrepareStmtByLabelName(childStmt, labelName, op));
        AW_YANG_BindChild(ret, GMERR_OK, GmcYangBindChild(batch, rootStmt, childStmt));
        tmpStmt = childStmt;
    } else {
        return -1;
    }
    GmcNodeT *parentNode = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcGetRootNode(tmpStmt, &parentNode));
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcYangSetNodeProperty(parentNode, propertyValue, opType));
    AW_YANG_BatchAdd(ret, GMERR_OK, GmcBatchAddDML(batch, tmpStmt));
    return ret;
}

int32_t testYangDMLRoot(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt,
    GmcNodeT **rootNode)
{
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK,
        awYangPrepareVertexNode(batch, labelName, op, AW_YANG_TYPE_ROOT, rootStmt, NULL, rootNode));

    // 根节点的属性
    uint32_t f0 = 0;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangInitNodeProperty(*rootNode, "F0", &f0, GMC_DATATYPE_UINT32, sizeof(GMC_DATATYPE_UINT32)));

    // 编辑子node
    GmcNodeT *node1 = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangEditChildNode(op, *rootNode, "con_3", &node1));

    GmcNodeT *node2 = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangEditChildNode(op, node1, "choice_3_1", &node2));

    GmcNodeT *node3 = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangEditChildNode(op, node2, "case_3_1_1", &node3));

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangInitNodeProperty(node3, "F0", &f0, GMC_DATATYPE_UINT32, sizeof(GMC_DATATYPE_UINT32)));

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcBatchAddDML(batch, rootStmt));
    return ret;
}

int32_t testYangDMLRootRemove(GmcBatchT *batch, const char *labelName, GmcStmtT *rootStmt, GmcNodeT **rootNode)
{
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK,
        awYangPrepareVertexNode(batch, labelName, GMC_OPERATION_REMOVE_GRAPH, AW_YANG_TYPE_ROOT, rootStmt, NULL,
        rootNode));

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcBatchAddDML(batch, rootStmt));
    return ret;
}

int32_t testYangDMLList(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt,
    GmcStmtT *listStmt, uint32_t listEleCount)
{
    int32_t ret = GMERR_OK;
    uint32_t loop = 0;
    for (; loop < listEleCount; loop++) {
        GmcNodeT *listNode = NULL;
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK,
            awYangPrepareVertexNode(batch, labelName, op, AW_YANG_TYPE_CHILD, rootStmt, listStmt, &listNode));
        // list的属性
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangInitNodeProperty(listNode, "PK", &loop, GMC_DATATYPE_UINT32, sizeof(GMC_DATATYPE_UINT32)));

        // 编辑子node
        GmcNodeT *node1 = NULL;
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangEditChildNode(op, listNode, "con_12_1", &node1));
        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangInitNodeProperty(node1, "F0", &loop, GMC_DATATYPE_UINT32, sizeof(GMC_DATATYPE_UINT32)));

        AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcBatchAddDML(batch, listStmt));
    }
    return ret;
}

int32_t testYangDMLDemoRemoveRootInner(GmcConnT *connAsync, GmcStmtT *stmtAsync, const char *rootName, GmcYangDiffTypeE diffType)
{
    int32_t ret = GMERR_OK;
    GmcBatchT *batch = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangBatchPrepare(connAsync, &batch, GMC_BATCH_YANG, diffType));

    GmcStmtT *rootStmt = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcAllocStmt(connAsync, &rootStmt));

    GmcNodeT *rootNode = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK,
    awYangPrepareVertexNode(batch, rootName, GMC_OPERATION_REMOVE_GRAPH, AW_YANG_TYPE_ROOT, rootStmt, NULL,
        &rootNode));

    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    AsyncUserDataT asyncData = { 0 };
    asyncData.expTotalNum = 1;
    asyncData.expSuccNum = 1;
    AW_FUN_Log(LOG_DEBUG_ALL, "################################ root remove 111\n");
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangBatchExecuteAsyncInner(batch, batch_execute_callback, &asyncData));
    AW_MACRO_EXPECT_EQ_INT(asyncData.status, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(asyncData.totalNum, asyncData.succNum);
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangBatchDestroy(batch, &asyncData));

    GmcFreeStmt(rootStmt);
    return ret;
}

int32_t testYangDMLDemoRemoveRoot(GmcConnT *connAsync, GmcStmtT *stmtAsync, GmcYangDiffTypeE diffType)
{
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_START, connAsync, stmtAsync));
    GmcBatchT *batch = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangBatchPrepare(connAsync, &batch, GMC_BATCH_YANG, diffType));

    GmcStmtT *rootStmt = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcAllocStmt(connAsync, &rootStmt));

    GmcNodeT *rootNode = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangDMLRootRemove(batch, "root", rootStmt, &rootNode));

    AsyncUserDataT asyncData = { 0 };
    std::vector<std::string> expect;
    expect.push_back("root:remove(300)");
    asyncData.expectDiff = &expect;
    asyncData.needCheckDiff = true;
    asyncData.expTotalNum = 1;
    asyncData.expSuccNum = 1;
    asyncData.rootName = "root";
    asyncData.namespaceName = "NamespaceABC01802";
    AW_FUN_Log(LOG_DEBUG_ALL, "################################ root remove 111\n");
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangBatchExecuteAsync(connAsync, stmtAsync, batch, NULL, &asyncData));
    AW_MACRO_EXPECT_EQ_INT(asyncData.status, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(asyncData.totalNum, asyncData.succNum);
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangBatchDestroy(batch, &asyncData));

    std::vector<std::string> expect2;
    expect2.push_back("{}");
    asyncData.expectReply = &expect2;
    asyncData.needCheckSubtree = true;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_COMMIT, connAsync, stmtAsync, NULL, NULL, &asyncData));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_MACRO_EXPECT_EQ_INT(asyncData.expTotalNum, asyncData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(asyncData.expSuccNum, asyncData.succNum);
    GmcFreeStmt(rootStmt);
    return ret;
}

int32_t testYangDMLDemoFullTree(GmcConnT *connAsync, GmcStmtT *stmtAsync, GmcYangDiffTypeE diffType)
{
    int32_t ret = GMERR_OK;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_START, connAsync, stmtAsync));
    GmcBatchT *batch = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangBatchPrepare(connAsync, &batch, GMC_BATCH_YANG, diffType));

    GmcStmtT *rootStmt = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcAllocStmt(connAsync, &rootStmt));
    GmcNodeT *rootNode = NULL;
    GmcStmtT *listStmt = NULL;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, GmcAllocStmt(connAsync, &listStmt));
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangDMLRoot(batch, "root", GMC_OPERATION_INSERT, rootStmt, &rootNode));
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangDMLList(batch, "list_12", GMC_OPERATION_INSERT, rootStmt, listStmt, 5));

    AsyncUserDataT asyncData = { 0 };
    std::vector<std::string> expect;
    expect.push_back("root:create(4)");
    asyncData.expectDiff = &expect;
    asyncData.needCheckDiff = true;
    asyncData.expTotalNum = 6;
    asyncData.expSuccNum = 6;
    asyncData.rootName = "root";
    asyncData.namespaceName = "NamespaceABC01802";
    AW_FUN_Log(LOG_DEBUG_ALL, "################################ root insert 222\n");
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangBatchExecuteAsync(connAsync, stmtAsync, batch, NULL, &asyncData));
    AW_MACRO_EXPECT_EQ_INT(asyncData.status, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(asyncData.totalNum, asyncData.succNum);
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, testYangBatchDestroy(batch, &asyncData));

    std::vector<std::string> expect2;
    const char *expectJson2 = R"(
{
    "ID": 1,
    "F0": 0,
    "con_3": {
        "choice_3_1": {
            "case_3_1_1": {
                "F0": 0
            }
        }
    },
    "list_12": [
        {
            "ID": 1,
            "PID": 1,
            "PK": 0,
            "con_12_1": {
                "F0": 0
            }
        },
        {
            "ID": 2,
            "PID": 1,
            "PK": 1,
            "con_12_1": {
                "F0": 1
            }
        },
        {
            "ID": 3,
            "PID": 1,
            "PK": 2,
            "con_12_1": {
                "F0": 2
            }
        },
        {
            "ID": 4,
            "PID": 1,
            "PK": 3,
            "con_12_1": {
                "F0": 3
            }
        },
        {
            "ID": 5,
            "PID": 1,
            "PK": 4,
            "con_12_1": {
                "F0": 4
            }
        }
    ]
})";
    expect2.push_back(expectJson2);
    asyncData.expectReply = &expect2;
    asyncData.needCheckSubtree = true;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangTransOpAsync(AW_YANG_TRANS_COMMIT, connAsync, stmtAsync, NULL, NULL, &asyncData));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    AW_MACRO_EXPECT_EQ_INT(asyncData.expTotalNum, asyncData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(asyncData.expSuccNum, asyncData.succNum);
    GmcFreeStmt(rootStmt);
    GmcFreeStmt(listStmt);
    return ret;
}

// node、field和vertex设置值时，都通过GmcYangGetChoiceCasePath获取路径
int TestEditChildNodeFieldVertex(TestEditOpT config)
{
    GmcAttributePropertyT attrProperty;
    attrProperty.type = config.attrType;
    attrProperty.size = config.size;
    attrProperty.value = config.value;

    GmcPropValueT propValue;
    strcpy_s(propValue.propertyName, strlen(config.childName) + 1, config.childName);
    propValue.type = config.type;
    if (config.attrType == GMC_ATTRIBUTE_NAME || config.attrType == GMC_ATTRIBUTE_VALUE) {
        propValue.value = (void *)&attrProperty;
        propValue.size = sizeof(attrProperty);
    } else {
        propValue.size = config.size;
        propValue.value = config.value;
    }

    char path[1024] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = 1024, .actualPathLen = 0};
    GmcNodeT *caseNode = NULL;

    GmcYangChildElementTypeE childType[] = {
        GMC_YANG_TYPE_NODE,    // 表示操作 case 下的 node
        GMC_YANG_TYPE_FIELD,   // 表示操作 case 下的字段
        GMC_YANG_TYPE_VERTEX,  // 表示操作 case 下的 vertex
    };

    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        int ret = GmcYangGetChoiceCasePath(config.parentNode, config.childName, childType[i], &pathInfo);
        if (GMERR_OK == ret) {  // parentNode 和 childName 之间为choice-case路径
            // 将GmcYangGetChoiceCasePath获取到的choiceCase的路径补全
            ret = GmcYangEditChildNode(config.parentNode, pathInfo.path, config.opType, &caseNode);
            RETURN_IFERR(ret);
            // 传入的路径非空的情况下，比较获取的路径和预期的路径是否一致
            if (config.expectPath != NULL) {
                ret = strcmp(config.expectPath, pathInfo.path);
                if (ret != GMERR_OK) {
                    AW_FUN_Log(LOG_ERROR,
                        "[TestEditChildNodeFieldVertex] config.expectPath : %s, pathInfo.path : %s.",
                        config.expectPath,
                        pathInfo.path);
                    return ret;
                }
            }

            // node-choice-case-node
            if (childType[i] == GMC_YANG_TYPE_NODE) {
                ret = GmcYangEditChildNode(caseNode, config.childName, config.opType, config.childNode);
                if (ret != GMERR_OK) {
                    AW_FUN_Log(
                        LOG_ERROR, "[TestEditChildNodeFieldVertex] childType : %d, ret : %d.", childType[i], ret);
                }
                return ret;
            }
            // node-choice-case-field
            if (childType[i] == GMC_YANG_TYPE_FIELD) {
                ret = GmcYangSetNodeProperty(caseNode, &propValue, config.fieldOptype);
                if (ret != GMERR_OK) {
                    AW_FUN_Log(
                        LOG_ERROR, "[TestEditChildNodeFieldVertex] childType : %d, ret : %d.", childType[i], ret);
                }
                return ret;
            }
            // node-choice-case-vertex
            if (config.opType == GMC_OPERATION_SUBTREE_FILTER) {
                ret = GmcYangEditChildNode(caseNode, config.childName, config.opType, config.childNode);
            } else {
                ret = testGmcPrepareStmtByLabelName(config.stmt, config.childName, config.opType);
            }
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR,
                    "[TestEditChildNodeFieldVertex] opType : %d, childType : %d, ret : %d.",
                    config.opType,
                    childType[i],
                    ret);
            }
            return ret;
        }
    }
    // 非choiceCase下传入路径会报错
    if (config.expectPath != NULL) {
        RETURN_IFERR(FAILED);
    }
    // parentNode和childName之间非choice-case路径
    int ret = GmcYangEditChildNode(config.parentNode, config.childName, config.opType, config.childNode);
    if (ret == GMERR_OK) {
        // 为node或vertex(仅限于subtree filter场景下, 可能为vertex), 返回后外界可继续set property
        return GMERR_OK;
    }
    // 传propValue
    ret = GmcYangSetNodeProperty(config.parentNode, &propValue, config.fieldOptype);
    if (ret == GMERR_OK) {
        // 为field, 返回后外界可继续set别的property或edit node或batch add dml
        return GMERR_OK;
    }
    // 为vertex, 返回后外界可继续bind child
    ret = testGmcPrepareStmtByLabelName(config.stmt, config.childName, config.opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TestEditChildNodeFieldVertex] no choice-case path, ret : %d.", ret);
    }
    return ret;
}

// GmcYangSetNodeProperty函数
int32_t TestGmcYangSetNodeProperty(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName,
    GmcYangPropOpTypeE opType, const char *expectPath, GmcAttributeTypeE attrType)
{
    TestEditOpT config = {.opType = GMC_OPERATION_MERGE,   // 规避错误日志，此opType无实际意义
        .childName = fieldName,
        .parentNode = node,
        .childNode = NULL,
        .stmt = NULL,
        .fieldOptype = opType,
        .type = type,
        .size = size,
        .value = value,
        .expectPath = expectPath,
        .attrType=attrType};
    return TestEditChildNodeFieldVertex(config);
}

// GmcYangEditChildNode函数
int32_t TestGmcYangEditChildNode(
    GmcNodeT *node, const char *name, GmcOperationTypeE opType, GmcNodeT **child, const char *expectPath)
{
    TestEditOpT config = {.opType = opType,
        .childName = name,
        .parentNode = node,
        .childNode = child,
        .stmt = NULL,
        .fieldOptype = GMC_YANG_PROPERTY_OPERATION_MERGE,  // 规避错误日志，此opType无实际意义
        .type = GMC_DATATYPE_BUTT,
        .size = 0,
        .value = NULL,
        .expectPath = expectPath};
    return TestEditChildNodeFieldVertex(config);
}

// testGmcPrepareStmtByLabelName函数在yang场景时的用法
int TestYangGmcPrepareStmtByLabelName(GmcNodeT *parentNode, GmcStmtT *stmt, const char *vertexLabelName,
    GmcOperationTypeE operationType, const char *expectPath)
{
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    if (parentNode == NULL) {
        // root container
        return testGmcPrepareStmtByLabelName(stmt, vertexLabelName, operationType);
    }
    // 非root container的其他vertex场景
    TestEditOpT config = {.opType = operationType,
        .childName = vertexLabelName,
        .parentNode = parentNode,
        .childNode = NULL,
        .stmt = stmt,
        .fieldOptype = GMC_YANG_PROPERTY_OPERATION_MERGE,  // 规避错误日志，此opType无实际意义
        .type = GMC_DATATYPE_BUTT,
        .size = 0,
        .value = NULL,
        .expectPath = expectPath};
    return TestEditChildNodeFieldVertex(config);
}

/******************************Diff*******************************************/
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTreePrint(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        if (g_printDiffFlag) {
            cout << "actual diff: \n" << res;
        }
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void TestFetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);

            ASSERT_TRUE(isEnd);
            TestCheckYangTreePrint(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
void TestYangFetchDiffExtExecuteAsync(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data,
    GmcFetchDiffModeE diffMode, int rets)
{
    int ret = 0;
    data.stmt = stmt;
    data.expectDiff = &expectDiff;

    // 设置diff查询模式
    GmcFetchDiffOptionT *option = NULL;
    ret = GmcYangDiffFetchOptionCreate(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangDiffFetchOptionSetMode(option, diffMode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询diff
    ret = GmcYangFetchDiffExtExecuteAsync(stmt, NULL, option, TestFetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
    GmcYangDiffFetchOptionDestroy(option);
}

void FetchDiffBuf_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &userData1->diffBufLen));
            ASSERT_TRUE(isEnd);
            if (userData1->diffBufLen != 0) {
                userData1->diffBuf = (uint8_t *)malloc(userData1->diffBufLen);
                ASSERT_TRUE(userData1->diffBuf != NULL);
                ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBuf(fetchRet, userData1->diffBufLen, userData1->diffBuf));
            } else {
                AW_FUN_Log(LOG_STEP, "userData1->diffBufLen = %d", userData1->diffBufLen);
            }

            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void TestYangDiffFetchRetFromBuf(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT *data,
    int rets)
{
    if (data->diffBufLen == 0) {
        ASSERT_TRUE(expectDiff.size() == 0);
        return;
    }
    GmcFetchRetT *fetchRet = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangDiffFetchRetFromBuf(stmt, data->diffBufLen, data->diffBuf, &fetchRet));
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    TestCheckYangTreePrint(stmt, yangTree, count, expectDiff);
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void TestYangFetchDiffBufExtExecuteAsync(GmcStmtT *stmt, AsyncUserDataT *data,
    GmcFetchDiffModeE diffMode, int rets)
{
    int ret = 0;

    // 设置diff查询模式
    GmcFetchDiffOptionT *option = NULL;
    ret = GmcYangDiffFetchOptionCreate(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangDiffFetchOptionSetMode(option, diffMode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangFetchDiffBufExtExecuteAsync(stmt, NULL, option, FetchDiffBuf_callback, data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data->status);
    GmcYangDiffFetchOptionDestroy(option);
}

void TestCheckYangTreeAttach(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t index,
    vector<string> &expectReply)
{
    ASSERT_TRUE(expectReply.size() >= (index + 1));
    GmcYangNodeT *rootInfo = NULL;
    uint32_t i = 0;
    ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
    string res;
    const char *rootName;
    ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
    ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
    res += "\n";
    ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
    char fileName[128] = {0};
    int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", index);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
        return;
    }
    FILE *fp = fopen(fileName, "w");
    if (fp == NULL) {
        AW_FUN_Log(LOG_INFO, "fopen error\n");
        return;
    }
    ret = fputs(res.c_str(), fp);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "fputs error\n");
        return;
    }
    ret = fclose(fp);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "fclose error\n");
        return;
    }

    if (g_printDiffFlag) {
        cout << "actual diff: \n" << res;
    }
    ASSERT_STREQ(StrCmp(expectReply[index], res).c_str(), "") << i;
    ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
}

void FetchDiffBatch_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    int ret = 0;
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            EXPECT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTreeAttach(userData1->stmt, yangTree, idx, *userData1->expectDiff);
            if (isEnd) {
                userData1->recvNum++;
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;

            // 设置diff查询模式
            GmcFetchDiffOptionT *option = NULL;
            ret = GmcYangDiffFetchOptionCreate(&option);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangDiffFetchOptionSetMode(option, userData1->diffMode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            EXPECT_EQ(GMERR_OK,
                GmcYangFetchDiffExtExecuteAsync(userData1->stmt, fetchRet, option, FetchDiffBatch_callback, userData1));
            GmcYangDiffFetchOptionDestroy(option);
            return;
        }
    }
}

void TestFetchDiffModeBatch(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data,
    GmcFetchDiffModeE diffMode, int rets)
{
    int ret = 0;
    data.stmt = stmt;
    data.expectDiff = &expectDiff;

    // 设置diff查询模式
    GmcFetchDiffOptionT *option = NULL;
    ret = GmcYangDiffFetchOptionCreate(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangDiffFetchOptionSetMode(option, diffMode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询diff
    ret = GmcYangFetchDiffExtExecuteAsync(stmt, NULL, option, FetchDiffBatch_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
    GmcYangDiffFetchOptionDestroy(option);
}

/***************************************** 移植YANG DT编辑框架 start *****************************************/
typedef struct {
    const char *key;         // json节点的key，vertex对应labelname，字段对应字段名
    const json_t *jsonNode;  // 节点对应的数据json
    const json_t *jsonCfg;   // 节点对应的属性配置json
    uint32_t listIndex;  // 节点在list中的位置，从0开始，非list节点默认为0，字段时代表其所在节点的位置
    bool isRoot;  // 是否为根节点，用于递归开始创建表头
    bool isTree;  // 是否为部分打散的方案
} TestParseInfo;

typedef struct {
    GmcDataTypeE type;  // prop type
    uint32_t size;      // prop size
} PropeInfoT;

static const std::map<string, GmcOperationTypeE> g_op_map = {{"insert", GMC_OPERATION_INSERT},
    {"merge", GMC_OPERATION_MERGE}, {"remove_graph", GMC_OPERATION_REMOVE_GRAPH},
    {"delete_graph", GMC_OPERATION_DELETE_GRAPH}, {"replace_graph", GMC_OPERATION_REPLACE_GRAPH},
    {"none", GMC_OPERATION_NONE}};

const std::map<string, GmcYangPropOpTypeE> g_field_op_map = {{"none", GMC_YANG_PROPERTY_OPERATION_NONE},
    {"create", GMC_YANG_PROPERTY_OPERATION_CREATE}, {"merge", GMC_YANG_PROPERTY_OPERATION_MERGE},
    {"replace", GMC_YANG_PROPERTY_OPERATION_REPLACE}, {"delete", GMC_YANG_PROPERTY_OPERATION_DELETE},
    {"remove", GMC_YANG_PROPERTY_OPERATION_REMOVE}};

static const std::map<string, GmcYangListPositionE> g_pos_map = {{"first", GMC_YANG_LIST_POSITION_FIRST},
    {"remain", GMC_YANG_LIST_POSITION_REMAIN}, {"before", GMC_YANG_LIST_POSITION_BEFORE},
    {"after", GMC_YANG_LIST_POSITION_AFTER}, {"last", GMC_YANG_LIST_POSITION_LAST}};

static const std::map<DbDataTypeE, PropeInfoT> g_prope_map = {
    {DB_DATATYPE_CHAR, {.type = GMC_DATATYPE_CHAR, .size = sizeof(char)}},
    {DB_DATATYPE_UCHAR, {.type = GMC_DATATYPE_UCHAR, .size = sizeof(unsigned char)}},
    {DB_DATATYPE_INT8, {.type = GMC_DATATYPE_INT8, .size = sizeof(int8_t)}},
    {DB_DATATYPE_UINT8, {.type = GMC_DATATYPE_UINT8, .size = sizeof(uint8_t)}},
    {DB_DATATYPE_INT16, {.type = GMC_DATATYPE_INT16, .size = sizeof(int16_t)}},
    {DB_DATATYPE_UINT16, {.type = GMC_DATATYPE_UINT16, .size = sizeof(uint16_t)}},
    {DB_DATATYPE_INT32, {.type = GMC_DATATYPE_INT32, .size = sizeof(int32_t)}},
    {DB_DATATYPE_UINT32, {.type = GMC_DATATYPE_UINT32, .size = sizeof(int32_t)}},
    {DB_DATATYPE_INT64, {.type = GMC_DATATYPE_INT64, .size = sizeof(int64_t)}},
    {DB_DATATYPE_UINT64, {.type = GMC_DATATYPE_UINT64, .size = sizeof(uint64_t)}},
    {DB_DATATYPE_TIME, {.type = GMC_DATATYPE_TIME, .size = sizeof(uint64_t)}},
    {DB_DATATYPE_FLOAT, {.type = GMC_DATATYPE_FLOAT, .size = sizeof(float)}},
    {DB_DATATYPE_DOUBLE, {.type = GMC_DATATYPE_DOUBLE, .size = sizeof(double)}},
    {DB_DATATYPE_ENUM, {.type = GMC_DATATYPE_ENUM, .size = sizeof(int32_t)}},
    {DB_DATATYPE_IDENTITY, {.type = GMC_DATATYPE_IDENTITY, .size = sizeof(int32_t)}},
    {DB_DATATYPE_BYTES, {.type = GMC_DATATYPE_BYTES, .size = sizeof(uint64_t)}},
    {DB_DATATYPE_STRING, {.type = GMC_DATATYPE_STRING, .size = sizeof(uint64_t)}},
    {DB_DATATYPE_FIXED, {.type = GMC_DATATYPE_FIXED, .size = sizeof(uint64_t)}},
    {DB_DATATYPE_BOOL, {.type = GMC_DATATYPE_BOOL, .size = sizeof(bool)}}};

void TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcYangDiffTypeE diffOps)
{
    GmcBatchOptionT batchOption;
    // 准备批量操作
    ASSERT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    ASSERT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    uint32_t bufferMaxSize = (conn->isLobConn) ? 32768 : 2048;
    ASSERT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, bufferMaxSize));
    ASSERT_NO_FATAL_FAILURE(GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    ASSERT_EQ(GMERR_OK, GmcYangBatchOptionSetDiffType(&batchOption, diffOps));
    ASSERT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, batch));
}

int32_t TestSetListPosKeyValue(GmcStmtT *stmt, uint32_t index, GmcDataTypeE type, const void *value, uint32_t valueSize,
    GmcYangListLocatorT *listLocator)
{
    GmcPropValueT refKey = {0};
    refKey.type = type;
    refKey.value = (void *)value;
    refKey.size = valueSize;
    refKey.propertyId = index;
    GmcPropValueT *refKeys[1] = {&refKey};
    listLocator->refKeyFields = refKeys;
    listLocator->refKeyFieldsCount = 1;
    return GmcYangSetListLocator(stmt, listLocator);
}

void TestEditChildNode(GmcNodeT *parentNode, const char *childName, GmcOperationTypeE opType, GmcNodeT **childNode)
{
    if (GmcYangEditChildNode(parentNode, childName, opType, childNode) == GMERR_OK) {
        return;
    }
    GmcYangChildElementTypeE childType[] = {
        GMC_YANG_TYPE_NODE,    // 表示操作 case 下的 node
        GMC_YANG_TYPE_VERTEX,  // 表示操作 case 下的 vertex
        GMC_YANG_TYPE_FIELD    // 表示操作 case 下的字段
    };
    char path[1024] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = 1024, .actualPathLen = 0};
    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        if (GMERR_OK == GmcYangGetChoiceCasePath(parentNode, childName, childType[i], &pathInfo)) {
            break;
        }
    }
    GmcNodeT *caseNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(parentNode, pathInfo.path, opType, &caseNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(caseNode, childName, opType, childNode));
}

void TestEditChildField(GmcStmtT *stmt, GmcNodeT *parentNode, const char *childFieldName, GmcNodeT **childNode)
{
    GmcNodeT *parent = parentNode;
    if (parent == NULL) {
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &parent));
    }
    uint32_t fieidId;
    uint32_t propertyType;
    if (GmcYangGetPropTypeByName(parent, childFieldName, &fieidId, &propertyType) == GMERR_OK) {
        return;
    }
    GmcYangChildElementTypeE childType[] = {
        GMC_YANG_TYPE_NODE,    // 表示操作 case 下的 node
        GMC_YANG_TYPE_VERTEX,  // 表示操作 case 下的 vertex
        GMC_YANG_TYPE_FIELD    // 表示操作 case 下的字段
    };
    char path[1024] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = 1024, .actualPathLen = 0};
    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        if (GMERR_OK == GmcYangGetChoiceCasePath(parent, childFieldName, childType[i], &pathInfo)) {
            break;
        }
    }
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(parent, pathInfo.path, parent->operationType, childNode));
}

void TestEditListChildNodeParent(
    GmcNodeT *parentNode, const char *childName, GmcOperationTypeE opType, GmcNodeT **childNode)
{
    char path[1024] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = 1024, .actualPathLen = 0};
    if (GMERR_OK != GmcYangGetChoiceCasePath(parentNode, childName, GMC_YANG_TYPE_VERTEX, &pathInfo)) {
        return;
    }
    GmcNodeT *caseNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(parentNode, pathInfo.path, opType, &caseNode));
}

/* 带cfg的高级lltbatch操作，解析cfgjson */
bool TestGetBoolValueWithDefault(const json_t *jsonCfg, const char *key, bool defaultValue)
{
    if (jsonCfg == NULL) {
        return defaultValue;
    }
    json_t *jsonValue = json_object_get(jsonCfg, key);
    if (jsonValue == NULL) {
        return defaultValue;
    }
    json_type type = json_typeof(jsonValue);

    return (type == JSON_TRUE) ? true : false;
}

uint32_t TestGetIntValueWithDefault(const json_t *jsonCfg, const char *key, uint32_t defaultValue)
{
    if (jsonCfg == NULL) {
        return defaultValue;
    }
    json_t *jsonValue = json_object_get(jsonCfg, key);
    if (jsonValue == NULL || json_typeof(jsonValue) != JSON_INTEGER) {
        return defaultValue;
    }

    return json_integer_value(jsonValue);
}

// 从cfg中取字段级的原语操作类型，默认create
GmcYangPropOpTypeE TestGetFieldOpType(const json_t *jsonCfg)
{
    if (jsonCfg == NULL) {
        return GMC_YANG_PROPERTY_OPERATION_MERGE;
    }
    json_t *jsonValue = json_object_get(jsonCfg, "propOp");
    if (jsonValue == NULL || json_typeof(jsonValue) != JSON_STRING) {
        return GMC_YANG_PROPERTY_OPERATION_MERGE;
    }

    const char *opString = json_string_value(jsonValue);
    auto iter = g_field_op_map.find(opString);
    EXPECT_NE(iter, g_field_op_map.end()) << "invalid field op type, op=" << opString;

    return iter->second;
}

// 从cfg中取list的位置枚举，默认remain
GmcYangListPositionE TestGetListPos(const json_t *jsonCfg)
{
    if (jsonCfg == NULL) {
        return GMC_YANG_LIST_POSITION_REMAIN;
    }
    json_t *jsonValue = json_object_get(jsonCfg, "pos");
    if (jsonValue == NULL || json_typeof(jsonValue) != JSON_STRING) {
        return GMC_YANG_LIST_POSITION_REMAIN;
    }

    const char *posString = json_string_value(jsonValue);
    auto iter = g_pos_map.find(posString);
    EXPECT_NE(iter, g_pos_map.end()) << "invalid field op type, op=" << posString;

    return iter->second;
}

void TestSetPropertyInner(GmcStmtT *stmt, GmcNodeT *node, TestParseInfo *propInfo, GmcPropValueT *propValue)
{
    GmcYangPropOpTypeE opType = (node != NULL && node->operationType == GMC_OPERATION_SUBTREE_FILTER) ?
                                    GMC_YANG_PROPERTY_OPERATION_CREATE :
                                    TestGetFieldOpType(propInfo->jsonCfg);
    Status expect = TestGetIntValueWithDefault(propInfo->jsonCfg, "expectStatus", 0);
    memcpy_s(propValue->propertyName, GMC_PROPERTY_NAME_MAX_LEN, propInfo->key, strlen(propInfo->key) + 1);

    if (node != NULL) {
        ASSERT_EQ(expect, GmcYangSetNodeProperty(node, propValue, opType))
            << "set prop " << propInfo->key << " error:" << GmcGetLastError() << endl;
    } else {
        if (stmt->operationType == GMC_OPERATION_MERGE) {
            DmVertexT *vertex = CltGetVertexInStmt(stmt);
            if (DmIsPkPropOfDesc(vertex->vertexDesc, propValue->propertyId)) {
                return;
            }
        }
        ASSERT_EQ(expect, GmcYangSetVertexProperty(stmt, propValue, opType))
            << "set prop " << propInfo->key << " error:" << GmcGetLastError() << endl;
    }
}

void TestSetPropertyTimeString(GmcStmtT *stmt, GmcNodeT *node, TestParseInfo *propInfo, GmcPropValueT *propValue)
{
    int64_t timeValue;
    ASSERT_EQ(GMERR_OK, DmConvertStrTimeToIntTime((const char *)propValue->value, &timeValue));
    propValue->size = sizeof(timeValue);
    propValue->value = &timeValue;
    ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, node, propInfo, propValue));
}

void TestSetPropertyHexString(GmcStmtT *stmt, GmcNodeT *node, TestParseInfo *propInfo, GmcPropValueT *propValue)
{
    // 验证一下是不是带有前缀的合法字符串
    const char *strNoPrefix = (const char *)propValue->value;
    ASSERT_EQ(strlen(strNoPrefix) > 2, 1);
    uint8_t *bytesValue = (uint8_t *)malloc((uint32_t)strlen(strNoPrefix) + 1);
    ASSERT_EQ(GMERR_OK, DbStrIsHexPrefixed((const char *)propValue->value, &strNoPrefix));
    uint32_t size = DbStrGetHexToBytesSize(strlen((const char *)propValue->value) - 2);  // 取数据长度，不包含前缀和\0
    // 转为数据类型
    ASSERT_EQ(GMERR_OK, DbStrHexToBytes(strNoPrefix, bytesValue, (uint32_t)strlen(strNoPrefix)));
    propValue->value = bytesValue;
    propValue->size = size;
    ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, node, propInfo, propValue));
    free(bytesValue);
}

void TestSetPropertyFromString(GmcStmtT *stmt, GmcNodeT *node, TestParseInfo *propInfo, GmcPropValueT *propValue)
{
    const uint32_t remainBufferSize = 100;

    switch (propValue->type) {
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, node, propInfo, propValue));
            break;
        case GMC_DATATYPE_TIME:
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyTimeString(stmt, node, propInfo, propValue));
            break;
        case GMC_DATATYPE_FIXED: {
            // 如果是以16进制（0x开头）形式传入，先转换后再设值，否则直接按传入的字符串设值
            const char *originalStr = (const char *)propValue->value;
            if ((originalStr[0] == '0') && (originalStr[1] == 'x' || originalStr[1] == 'X')) {
                ASSERT_NO_FATAL_FAILURE(TestSetPropertyHexString(stmt, node, propInfo, propValue));
                break;
            }
        }
        case GMC_DATATYPE_ENUM:
        case GMC_DATATYPE_IDENTITY:
        case GMC_DATATYPE_STRING: {
            char *buffer = NULL;
            if (TestGetBoolValueWithDefault(propInfo->jsonCfg, "hasIdSuffix", false)) {
                buffer = (char *)malloc(propValue->size + remainBufferSize);
                uint32_t bufferSize = propValue->size + remainBufferSize;
                memset_s(buffer, bufferSize, 0, bufferSize);
                memcpy_s(buffer, bufferSize, (char *)propValue->value, propValue->size);
                sprintf_s(buffer + propValue->size, remainBufferSize, "%u", propInfo->listIndex + 1);
                propValue->value = buffer;
                propValue->size = strlen(buffer);
            }
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, node, propInfo, propValue));
            free(buffer);
            break;
        }
        case GMC_DATATYPE_BYTES:
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyHexString(stmt, node, propInfo, propValue));
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_UINT64: {
            uint64_t intValue = atoi((char *)propValue->value);
            propValue->value = (void *)&intValue;
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, node, propInfo, propValue));
            break;
        }
        default:
            ASSERT_EQ(0, 1) << "json should not be string, type=" << propValue->type << "key=" << propInfo->key;
    }
}

uint32_t GetPkInfoIndex(DmVertexDescT *vertexDesc, uint32_t i)
{
    uint32_t keyPropeNum = vertexDesc->indexKeyBufInfos[0].keyPropeNum;
    uint32_t list[keyPropeNum];
    for (uint32_t j = 0; j < (uint32_t)keyPropeNum; j++) {
        list[j] = vertexDesc->indexKeyBufInfos[0].keyPropeIds[j];
    }
    for (uint32_t j = 0; j < (uint32_t)keyPropeNum - 1; j++) {
        for (uint32_t k = j; k < (uint32_t)keyPropeNum - 1; k++) {
            if (list[k] > list[k + 1]) {
                uint32_t temp = list[k];
                list[k] = list[k + 1];
                list[k + 1] = temp;
            }
        }
    }
    return list[i];
}

void TestSetIndexValueByJson(
    GmcStmtT *stmt, const json_t *value, DmVertex *vertex, bool posFlag, GmcYangListLocatorT *listLocator)
{
    ASSERT_EQ(json_typeof(value), JSON_ARRAY) << "index value should be json array";
    uint32_t size = (uint32_t)json_array_size(value);
    ASSERT_NE(0u, size) << "INVALID_JSON_CONTENT, index value array size=0";
    GmcAttributePropertyT attrProperty;
    void *valuePtr;
    uint32_t valueSize;
    DmIndexKeyBufInfoT *pkInfo = &(vertex->vertexDesc->indexKeyBufInfos[0]);
    DmPropertyInfoT *propeInfos = vertex->vertexDesc->recordDesc->propeInfos;
    ASSERT_EQ(size, pkInfo->keyPropeNum) << "index num of json is wrong";
    for (uint32_t i = 0; i < size; ++i) {
        json_t *item = json_array_get(value, i);
        DbDataTypeE type = propeInfos[GetPkInfoIndex(vertex->vertexDesc, i)].dataType;
        auto iter = g_prope_map.find(type);
        ASSERT_NE(iter, g_prope_map.end()) << "invalid data type of index, type=" << type;
        if (json_typeof(item) == JSON_STRING) {
            char *stringValue = (char *)json_string_value(item);
            if (strcmp(stringValue, "NULL") == 0) {
                continue;
            }
            valueSize = strlen(stringValue);
            valuePtr = stringValue;
            if (iter->second.type == GMC_DATATYPE_ENUM || iter->second.type == GMC_DATATYPE_IDENTITY) {
                attrProperty.type = GMC_ATTRIBUTE_NAME;
                attrProperty.size = strlen(stringValue);
                attrProperty.value = (void *)stringValue;
                valuePtr = &attrProperty;
                valueSize = sizeof(GmcAttributePropertyT);
            } else {
                valuePtr = stringValue;
                valueSize = strlen(stringValue);
            }
            if (!posFlag) {
                ASSERT_NO_FATAL_FAILURE(GmcSetIndexKeyValue(stmt, i, iter->second.type, valuePtr, valueSize));
            } else {
                ASSERT_NO_FATAL_FAILURE(
                    TestSetListPosKeyValue(stmt, i, iter->second.type, valuePtr, valueSize, listLocator));
            }
        } else if (json_typeof(item) == JSON_INTEGER) {
            json_int_t intValue = json_integer_value(item);
            if (iter->second.type == GMC_DATATYPE_ENUM || iter->second.type == GMC_DATATYPE_IDENTITY) {
                attrProperty.type = GMC_ATTRIBUTE_VALUE;
                attrProperty.size = sizeof(int32_t);
                attrProperty.value = (void *)&intValue;
                valuePtr = &attrProperty;
                valueSize = sizeof(GmcAttributePropertyT);
            } else {
                valuePtr = &intValue;
                valueSize = iter->second.size;
            }
            if (!posFlag) {
                ASSERT_NO_FATAL_FAILURE(GmcSetIndexKeyValue(stmt, i, iter->second.type, valuePtr, valueSize));
            } else {
                ASSERT_NO_FATAL_FAILURE(
                    TestSetListPosKeyValue(stmt, i, iter->second.type, &intValue, iter->second.size, listLocator));
            }
        } else if (json_typeof(item) == JSON_REAL) {
            if (type == DB_DATATYPE_DOUBLE) {
                double doubleValue = json_real_value(item);
                if (!posFlag) {
                    ASSERT_NO_FATAL_FAILURE(
                        GmcSetIndexKeyValue(stmt, i, GMC_DATATYPE_DOUBLE, &doubleValue, sizeof(double)));
                } else {
                    ASSERT_NO_FATAL_FAILURE(TestSetListPosKeyValue(
                        stmt, i, GMC_DATATYPE_DOUBLE, &doubleValue, sizeof(double), listLocator));
                }
            } else {
                float floatValue = (float)json_real_value(item);
                if (!posFlag) {
                    ASSERT_NO_FATAL_FAILURE(
                        GmcSetIndexKeyValue(stmt, i, GMC_DATATYPE_FLOAT, &floatValue, sizeof(float)));
                } else {
                    ASSERT_NO_FATAL_FAILURE(
                        TestSetListPosKeyValue(stmt, i, GMC_DATATYPE_FLOAT, &floatValue, sizeof(float), listLocator));
                }
            }
        } else {
            if (type == DB_DATATYPE_BOOL) {
                bool doubleValue = (json_typeof(item) == JSON_TRUE) ? true : false;
                if (!posFlag) {
                    ASSERT_NO_FATAL_FAILURE(
                        GmcSetIndexKeyValue(stmt, i, GMC_DATATYPE_BOOL, &doubleValue, sizeof(bool)));
                } else {
                    ASSERT_NO_FATAL_FAILURE(
                        TestSetListPosKeyValue(stmt, i, GMC_DATATYPE_BOOL, &doubleValue, sizeof(bool), listLocator));
                }
            } else {
                ASSERT_EQ(0, 1) << "index type is not support now, type=" << type;
            }
        }
    }
}

void TestSetListPosByJson(GmcStmtT *stmt, const char *posValue, GmcYangListLocatorT *listLocator)
{
    auto iter = g_pos_map.find(posValue);
    ASSERT_NE(iter, g_pos_map.end()) << "invalid edit pos, pos=" << posValue;
    listLocator->position = iter->second;
    bool isReferenceKeyNeeded =
        listLocator->position == GMC_YANG_LIST_POSITION_BEFORE || listLocator->position == GMC_YANG_LIST_POSITION_AFTER;
    if (!isReferenceKeyNeeded) {
        listLocator->refKeyFields = NULL;
        ASSERT_NO_FATAL_FAILURE(GmcYangSetListLocator(stmt, listLocator));
    }
}

static void TestInitGmcPropVal(TestParseInfo *propInfo, DmVertexT *vertex, GmcNodeT *node, GmcPropValueT *propValue)
{
    const DmRecordT *record;
    const RecordDescT *recordDesc;
    if (node != NULL) {
        if (node->operationType == GMC_OPERATION_SUBTREE_FILTER) {
            DmSubtreeT *tree = node->tree;
            ASSERT_NE(nullptr, tree) << "Filter tree in node is NULL, key=" << propInfo->key;
            bool isVertex = DmIsVertexSubtree(tree);
            record = isVertex ? tree->vertex->record : tree->node->currRecord;
            recordDesc = isVertex ? tree->vertex->vertexDesc->recordDesc : tree->node->nodeDesc->recordDesc;
        } else {
            record = node->dmNode->currRecord;
            recordDesc = node->dmNode->nodeDesc->recordDesc;
        }
    } else {
        record = vertex->record;
        recordDesc = vertex->vertexDesc->recordDesc;
    }

    uint32_t index;
    ASSERT_EQ(GMERR_OK, GetPropertyIdByName(record, propInfo->key, &index)) << "keyName = " << propInfo->key << endl;
    auto iter = g_prope_map.find(recordDesc->propeInfos[index].dataType);
    ASSERT_NE(iter, g_prope_map.end()) << "invalid data type of int, key=" << propInfo->key;
    propValue->type = iter->second.type;
    propValue->size = iter->second.size;
    propValue->propertyId = index;
}

void TestSetPropertyByJson(GmcStmtT *stmt, TestParseInfo *propInfo, DmVertexT *vertex, GmcNodeT *node)
{
    GmcPropValueT propValue = {0};
    GmcAttributePropertyT attrProperty;
    GmcNodeT *realNode = node;
    ASSERT_NO_FATAL_FAILURE(TestEditChildField(stmt, node, propInfo->key, &realNode))
        << "edit prop failed, key=" << propInfo->key;
    ASSERT_NO_FATAL_FAILURE(TestInitGmcPropVal(propInfo, vertex, realNode, &propValue));

    // 根据不同数据类型，生成合适的propValue
    switch (json_typeof(propInfo->jsonNode)) {
        case JSON_STRING:
            if (propValue.type == GMC_DATATYPE_ENUM || propValue.type == GMC_DATATYPE_IDENTITY) {
                attrProperty.type = GMC_ATTRIBUTE_NAME;
                attrProperty.size = strlen(json_string_value(propInfo->jsonNode));
                attrProperty.value = (void *)json_string_value(propInfo->jsonNode);
                propValue.size = sizeof(attrProperty);
                propValue.value = (void *)&attrProperty;
            } else {
                propValue.value = (void *)json_string_value(propInfo->jsonNode);
                if (propValue.type == GMC_DATATYPE_STRING || propValue.type == GMC_DATATYPE_FIXED) {
                    propValue.size = strlen(json_string_value(propInfo->jsonNode));
                }
            }
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyFromString(stmt, realNode, propInfo, &propValue));
            break;
        case JSON_INTEGER: {
            json_int_t intValue = json_integer_value(propInfo->jsonNode);
            if (propValue.type == GMC_DATATYPE_ENUM || propValue.type == GMC_DATATYPE_IDENTITY) {
                attrProperty.type = GMC_ATTRIBUTE_VALUE;
                attrProperty.size = sizeof(int32_t);
                attrProperty.value = (void *)&intValue;
                propValue.value = (void *)&attrProperty;
            } else {
                propValue.value = &intValue;
            }
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, realNode, propInfo, &propValue));
            break;
        }
        case JSON_REAL: {
            double doubleValue = json_real_value(propInfo->jsonNode);
            float floatValue = (float)doubleValue;
            propValue.value = (propValue.type == GMC_DATATYPE_FLOAT) ? (void *)&floatValue : (void *)&doubleValue;
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, realNode, propInfo, &propValue));
            break;
        }
        case JSON_TRUE: {
            bool trueValue = true;
            propValue.value = &trueValue;
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, realNode, propInfo, &propValue));
            break;
        }
        case JSON_FALSE: {
            bool falseValue = false;
            propValue.value = &falseValue;
            ASSERT_NO_FATAL_FAILURE(TestSetPropertyInner(stmt, realNode, propInfo, &propValue));
            break;
        }
        default:
            ASSERT_EQ(0, 1) << "INVALID_JSON_CONTENT, invalid json type, key=" << propInfo->key;
    }
}

void TestSetVertexPropByJson(
    GmcStmtT *stmt, TestParseInfo *propInfo, DmVertexT *vertex, GmcYangListLocatorT *listLocator)
{
    if (strcmp(propInfo->key, "pos") == 0) {
        ASSERT_NO_FATAL_FAILURE(TestSetListPosByJson(stmt, json_string_value(propInfo->jsonNode), listLocator));
    } else if (strcmp(propInfo->key, "key_name") == 0) {
        ASSERT_NO_FATAL_FAILURE(GmcSetIndexKeyName(stmt, json_string_value(propInfo->jsonNode)));
    } else {
        ASSERT_NO_FATAL_FAILURE(TestSetPropertyByJson(stmt, propInfo, vertex, NULL));
    }
}

void TestSetNodePropByJson(GmcStmtT *stmt, TestParseInfo *propInfo, GmcNodeT *node)
{
    ASSERT_NO_FATAL_FAILURE(TestSetPropertyByJson(stmt, propInfo, NULL, node));
}

void TestSetListPropertyByJson(
    GmcStmtT *stmt, TestParseInfo *listInfo, DmVertexT *vertex, GmcYangListLocatorT *listLocator)
{
    // 数据json中的json数组
    uint32_t size = (uint32_t)json_array_size(listInfo->jsonNode);
    ASSERT_NE(0u, size) << "INVALID_JSON_CONTENT, array size=0, key=" << listInfo->key;
    if (strcmp(listInfo->key, "key_value") == 0) {
        ASSERT_NO_FATAL_FAILURE(TestSetIndexValueByJson(stmt, listInfo->jsonNode, vertex, false, listLocator));
        return;
    }
    if (strcmp(listInfo->key, "pos_value") == 0) {
        ASSERT_NO_FATAL_FAILURE(TestSetIndexValueByJson(stmt, listInfo->jsonNode, vertex, true, listLocator));
        return;
    }
    // cfgJson中的json数组
    json_t *keyValue = json_object_get(listInfo->jsonCfg, "key_value");
    if (keyValue != NULL && json_typeof(keyValue) == JSON_ARRAY) {
        ASSERT_NO_FATAL_FAILURE(TestSetIndexValueByJson(stmt, keyValue, vertex, false, listLocator));
        return;
    }
    json_t *posValue = json_object_get(listInfo->jsonCfg, "pos_value");
    if (posValue != NULL && json_typeof(posValue) == JSON_ARRAY) {
        ASSERT_NO_FATAL_FAILURE(TestSetIndexValueByJson(stmt, posValue, vertex, true, listLocator));
        return;
    }
    ASSERT_EQ(0, 1) << "set property failed, not support leaflist, key = " << listInfo->key;
}

void TestBatchParseVertex(GmcConnT *conn, GmcBatchT *batch, GmcStmtT *stmt, TestParseInfo *vertexInfo);

void TestSetListVertexByJson(GmcConnT *conn, GmcBatchT *batch, GmcStmtT *stmt, TestParseInfo *listInfo)
{
    uint32_t i;
    uint32_t j;
    uint32_t dupNum;
    uint32_t size = (uint32_t)json_array_size(listInfo->jsonNode);

    ASSERT_NE(0u, size) << "INVALID_JSON_CONTENT, array size=0, key=" << listInfo->key;
    TestParseInfo listMemberInfo = {listInfo->key, NULL, NULL, 0, false, listInfo->isTree};
    for (i = 0; i < size; ++i) {
        listMemberInfo.jsonNode = json_array_get(listInfo->jsonNode, i);
        listMemberInfo.jsonCfg = json_array_get(listInfo->jsonCfg, i);
        dupNum = TestGetIntValueWithDefault(listMemberInfo.jsonCfg, "dupNum", 1);
        for (j = 0; j < dupNum; j++) {
            ASSERT_NO_FATAL_FAILURE(TestBatchParseVertex(conn, batch, stmt, &listMemberInfo));
            listMemberInfo.listIndex++;
        }
    }
}

void TestGetOpTypeByJson(const char *opString, GmcOperationTypeE *opType)
{
    auto iter = g_op_map.find(opString);
    ASSERT_NE(iter, g_op_map.end()) << "invalid op type, op=" << opString;
    *opType = iter->second;
}

GmcOperationTypeE TestGetOpTypeByCfg(const json_t *jsonCfg)
{
    if (jsonCfg == NULL) {
        return GMC_OPERATION_INSERT;
    }
    json_t *op = json_object_get(jsonCfg, "op");
    if (op == NULL || json_typeof(op) != JSON_STRING) {
        return GMC_OPERATION_INSERT;
    }

    const char *opString = json_string_value(op);
    auto iter = g_op_map.find(opString);
    EXPECT_NE(iter, g_op_map.end()) << "invalid op type, op=" << opString;
    return iter->second;
}

// 本函数递归执行vertex的解析并add到batch中，一个函数体处理一个vertex，注意json中要先放该节点的属性，再放该节点的子节点
Status TestBatchAddDmlOnlyOnce(GmcBatchT *batch, GmcStmtT *stmt, bool *hasAddOnce)
{
    if (*hasAddOnce) {
        return GMERR_OK;
    }
    *hasAddOnce = true;
    return GmcBatchAddDML(batch, stmt);
}

void TestSetVertexByCfgJson(GmcStmtT *stmt, TestParseInfo *vertexInfo, DmVertexT *vertex, GmcOperationTypeE opType,
    GmcYangListLocatorT *listLocator)
{
    // list位置信息
    if (vertex->vertexDesc->yangInfoDesc->type == DM_YANG_LIST) {
        listLocator->position = TestGetListPos(vertexInfo->jsonCfg);
        if (listLocator->position == GMC_YANG_LIST_POSITION_BEFORE ||
            listLocator->position == GMC_YANG_LIST_POSITION_AFTER) {
            ASSERT_NO_FATAL_FAILURE(TestSetListPropertyByJson(stmt, vertexInfo, vertex, listLocator))
                << "set list pos value fail, key=" << vertexInfo->key;
        }
        ASSERT_NO_FATAL_FAILURE(GmcYangSetListLocator(stmt, listLocator));
    }
    if (opType == GMC_OPERATION_NONE || opType == GMC_OPERATION_MERGE) {
        json_t *keyName = json_object_get(vertexInfo->jsonCfg, "key_name");
        if (keyName == NULL || json_typeof(keyName) != JSON_STRING) {
            return;
        }
        ASSERT_NO_FATAL_FAILURE(GmcSetIndexKeyName(stmt, json_string_value(keyName)));
        ASSERT_NO_FATAL_FAILURE(TestSetListPropertyByJson(stmt, vertexInfo, vertex, listLocator))
            << "set list pos value fail, key=" << vertexInfo->key;
    }
}

// 因为转向list需要重新prepare，所以部分打散场景下进行两轮遍历，先遍历node子树，再遍历子树中的list
void TestBatchParseList(GmcConnT *conn, GmcBatchT *batch, GmcStmtT *stmt, TestParseInfo *nodeInfo)
{
    void *it = json_object_iter((json_t *)(nodeInfo->jsonNode));

    for (; it; it = json_object_iter_next((json_t *)(nodeInfo->jsonNode), it)) {
        const char *key = json_object_iter_key(it);
        json_t *nodeCfg = json_object_get(nodeInfo->jsonCfg, key);
        ASSERT_NE(key, nullptr) << "key is null, label=" << nodeInfo->key;
        json_t *value = json_object_iter_value(it);
        ASSERT_NE(value, nullptr) << "value is null, key=" << key;
        TestParseInfo childInfo = {key, value, nodeCfg, nodeInfo->listIndex, false, nodeInfo->isTree};

        if (json_typeof(value) == JSON_OBJECT) {
            ASSERT_NO_FATAL_FAILURE(TestBatchParseList(conn, batch, stmt, &childInfo));
        } else if (json_typeof(value) == JSON_ARRAY) {
            if (json_typeof(json_array_get(value, 0)) != JSON_OBJECT) {
                continue;
            }
            ASSERT_NO_FATAL_FAILURE(TestSetListVertexByJson(conn, batch, stmt, &childInfo))
                << "set list fail, key=" << childInfo.key;
        }
    }
}

void TestBatchParseNode(GmcConnT *conn, GmcBatchT *batch, GmcStmtT *stmt, TestParseInfo *nodeInfo, GmcNodeT *parentNode)
{
    void *it = json_object_iter((json_t *)(nodeInfo->jsonNode));
    const char *firstKey = json_object_iter_key(it);
    GmcOperationTypeE opType;
    if (firstKey != NULL && strcmp(firstKey, "op") == 0) {
        json_t *opValue = json_object_iter_value(it);
        ASSERT_NO_FATAL_FAILURE(TestGetOpTypeByJson(json_string_value(opValue), &opType));
        it = json_object_iter_next((json_t *)(nodeInfo->jsonNode), it);
    } else {
        opType = TestGetOpTypeByCfg(nodeInfo->jsonCfg);
    }

    GmcNodeT *node = NULL;
    if (parentNode == NULL) {  // 第一次传进来的是vertex节点本身，此时要取的是它的直接子node
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &parentNode));
    }
    ASSERT_NO_FATAL_FAILURE(TestEditChildNode(parentNode, nodeInfo->key, opType, &node))
        << "edit node fail, key=" << nodeInfo->key;

    for (; it; it = json_object_iter_next((json_t *)(nodeInfo->jsonNode), it)) {
        const char *key = json_object_iter_key(it);
        json_t *nodeCfg = json_object_get(nodeInfo->jsonCfg, key);
        ASSERT_NE(key, nullptr) << "key is null, label=" << nodeInfo->key;
        json_t *value = json_object_iter_value(it);
        ASSERT_NE(value, nullptr) << "value is null, key=" << key;
        TestParseInfo childInfo = {key, value, nodeCfg, nodeInfo->listIndex, false, nodeInfo->isTree};

        if (json_typeof(value) == JSON_OBJECT) {
            ASSERT_NO_FATAL_FAILURE(TestBatchParseNode(conn, batch, stmt, &childInfo, node));
        } else if (json_typeof(value) == JSON_ARRAY) {
            ASSERT_NO_FATAL_FAILURE(TestEditListChildNodeParent(node, key, opType, &node));
            continue;
        } else {
            ASSERT_NO_FATAL_FAILURE(TestSetNodePropByJson(stmt, &childInfo, node))
                << "set node property fail, key=" << nodeInfo->key;
        }
    }
}

void TestBatchParseVertex(GmcConnT *conn, GmcBatchT *batch, GmcStmtT *stmt, TestParseInfo *vertexInfo)
{
    Status ret = GMERR_OK;
    GmcStmtT *exeStmt = NULL;
    bool hasAddBatch = false;
    GmcOperationTypeE opType = GMC_OPERATION_INSERT;
    const json_t *item = json_object_get((json_t *)(vertexInfo->jsonNode), "op");
    if (item != NULL) {
        ASSERT_NO_FATAL_FAILURE(TestGetOpTypeByJson(json_string_value(item), &opType));
    }

    if ((vertexInfo->isRoot)) {
        exeStmt = stmt;
        ret = GmcPrepareStmtByLabelName(exeStmt, vertexInfo->key, opType);
        /* 未移植建表AW, 所以暂不支持别名解析
        if (ret == GMERR_UNDEFINED_TABLE) {
            ASSERT_EQ(GMERR_OK, TestPrepareAndSetRootByAlias(batch, exeStmt, vertexInfo->key, opType))
                << "prepare fail, label=" << vertexInfo->key;
        } else { */
        ASSERT_EQ(GMERR_OK, ret) << "prepare fail, label=" << vertexInfo->key;
        ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, exeStmt));
    } else {
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &exeStmt));
        ret = GmcPrepareStmtByLabelName(exeStmt, vertexInfo->key, opType);
        /* 未移植建表AW, 所以暂不支持别名解析
        if (ret == GMERR_UNDEFINED_TABLE) { // 未移植建表AW, 所以暂不支持别名解析
            ASSERT_EQ(GMERR_OK, TestPrepareAndBindChildByAlias(batch, stmt, exeStmt, vertexInfo->key, opType));
        } else { */
        ASSERT_EQ(GMERR_OK, ret) << "prepare fail, label=" << vertexInfo->key;
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, stmt, exeStmt));
    }
    DmVertexT *vertex = CltGetVertexInStmt(exeStmt);

    GmcYangListLocatorT listLocator;
    ASSERT_NO_FATAL_FAILURE(TestSetVertexByCfgJson(exeStmt, vertexInfo, vertex, opType, &listLocator));

    void *it = json_object_iter((json_t *)(vertexInfo->jsonNode));
    for (; it; it = json_object_iter_next((json_t *)(vertexInfo->jsonNode), it)) {
        const char *key = json_object_iter_key(it);
        if (strcmp(key, "op") == 0) {
            continue;
        }
        json_t *nodeCfg = json_object_get(vertexInfo->jsonCfg, key);
        ASSERT_NE(key, nullptr) << "key is null, label=" << vertexInfo->key;
        json_t *value = json_object_iter_value(it);
        ASSERT_NE(value, nullptr) << "value is null, key=" << key;
        TestParseInfo childInfo = {key, value, nodeCfg, vertexInfo->listIndex, false, vertexInfo->isTree};
        if (json_typeof(value) == JSON_OBJECT) {
            if (vertexInfo->isTree) {
                ASSERT_NO_FATAL_FAILURE(TestBatchParseNode(conn, batch, exeStmt, &childInfo, NULL));
            } else {
                ASSERT_EQ(GMERR_OK, TestBatchAddDmlOnlyOnce(batch, exeStmt, &hasAddBatch));
                ASSERT_NO_FATAL_FAILURE(TestBatchParseVertex(conn, batch, exeStmt, &childInfo));
            }
        } else if (json_typeof(value) == JSON_ARRAY) {
            if (json_typeof(json_array_get(value, 0)) == JSON_OBJECT) {
                if (vertexInfo->isTree) {
                    continue;
                }
                ASSERT_EQ(GMERR_OK, TestBatchAddDmlOnlyOnce(batch, exeStmt, &hasAddBatch));
                ASSERT_NO_FATAL_FAILURE(TestSetListVertexByJson(conn, batch, exeStmt, &childInfo))
                    << "set list fail, key=" << childInfo.key;
            } else {
                ASSERT_NO_FATAL_FAILURE(TestSetListPropertyByJson(exeStmt, &childInfo, vertex, &listLocator))
                    << "set json array fail, key=" << childInfo.key;
            }
        } else {
            ASSERT_NO_FATAL_FAILURE(TestSetVertexPropByJson(exeStmt, &childInfo, vertex, &listLocator))
                << "set property fail, key=" << childInfo.key;
        }
    }
    ASSERT_EQ(GMERR_OK, TestBatchAddDmlOnlyOnce(batch, exeStmt, &hasAddBatch));
    // vertex遍历结束，对于部分打散方案，进行list遍历
    if (vertexInfo->isTree) {
        ASSERT_NO_FATAL_FAILURE(TestBatchParseList(conn, batch, exeStmt, vertexInfo));
    }
    if (!vertexInfo->isRoot) {
        GmcFreeStmt(exeStmt);
    }
}

void TestYangBatchExecuteByJsonAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, const char *dataJson,
    const char *fieldJson, GmcYangDiffTypeE diffOps, bool isOneThread)
{
    // json字符串转化为对象
    json_error_t jsonError;
    json_t *jsonRoot = json_loads(dataJson, JSON_REJECT_DUPLICATES, &jsonError);
    ASSERT_NE(jsonRoot, nullptr) << "jsonRoot is null, label=" << labelName;
    json_t *jsonCfg = (fieldJson != NULL) ? json_loads(fieldJson, JSON_REJECT_DUPLICATES, &jsonError) : NULL;

    // 预处理batch
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(TestBatchPrepare(conn, &batch, diffOps));

    // 解析json对象
    TestParseInfo rootInfo = {labelName, jsonRoot, jsonCfg, 0, true, true};
    ASSERT_NO_FATAL_FAILURE(TestBatchParseVertex(conn, batch, stmt, &rootInfo));
    json_decref(jsonRoot);
    json_decref(jsonCfg);

    AsyncUserDataT data = {0};
    int ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isOneThread) {
        ret = testWaitAsyncRecvOneThread(&data);
    } else {
        ret = testWaitAsyncRecv(&data);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
}
/***************************************** 移植YANG DT编辑框架 end *****************************************/
#endif
