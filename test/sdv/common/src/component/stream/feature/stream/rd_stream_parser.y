/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: STREAM SQL语法分析
 * Author: guopanpan
 * Create: 2024-09-09
 */
%{
#include <pthread.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <malloc.h>

#include "rd_stream_parser.h"
#include "rd_stream_parser.tab.h"

int yylex();
void yyerror(char *s, ...);
void emit(char *s, ...);

typedef struct yy_buffer_state *YY_BUFFER_STATE;
YY_BUFFER_STATE yy_scan_string (const char *yy_str);
void yy_switch_to_buffer(YY_BUFFER_STATE new_buffer);
YY_BUFFER_STATE yy_create_buffer(FILE *file,int size);
void yy_delete_buffer(YY_BUFFER_STATE b);
void yy_flush_buffer(YY_BUFFER_STATE b);
%}

%union {
    int intval;
    double floatval;
    char *strval;
    RdStreamSchemaT *schema;
    RdStreamColumnT column;
    RdListT columns;
}

%token <strval> NAME
%token <strval> STRING
%token <intval> INTNUM
%token <floatval> APPROXNUM

%token <strval> DATA_TYPE

%token CREATE
%token STREAM
%token TABLE
%token DEFAULT
%token IF
%token NOT
%token EXISTS

%token INSERT
%token INTO
%token VALUES

%token WATERMARK
%token FOR
%token AS
%token INTERVAL
%token WATERMARK_TIME
%token WATERMARK_STRATEGY

%token DISPATCH
%token BY
%token REF

%type <schema> create_stmt
%type <column> column_def
%type <columns> column_def_list

%start stmt_list

%%
stmt_list: stmt ';'
    ;

stmt: create_stmt {
        RdStreamSqlParserCtxT *ctx = RdStreamSqlParserCtxGet();
        ctx->parser.type = RD_STREAM_SQL_TYPE_CREATE;
        ctx->parser.schema = $1;
    }
    ;

create_stmt: CREATE STREAM TABLE exists_def NAME '(' column_def_list watermark_def ')' dispatch_def {
        $$ = (RdStreamSchemaT *)malloc(sizeof(RdStreamSchemaT));
        $$->tableNameLen = strlen($5) + 1;
        $$->tableName = $5;
        $$->columns = $7;
    }
    ;

exists_def: {
    }
    | IF NOT EXISTS {    
    }
    ;

column_def_list: column_def {
        $$ = RdListCreate(8);
        $1.id = RdListElementCount($$),
        RdListAppend(&$$, &$1, sizeof(RdStreamColumnT));
    }
    | column_def_list ',' column_def {
        $3.id = RdListElementCount($$),
        RdListAppend(&$$, &$3, sizeof(RdStreamColumnT));
    }
    ;

column_def: NAME DATA_TYPE column_atts {
        $$ = (RdStreamColumnT){
            .id = 0,                        // 列id在column_def_list中赋值
            .nameLen = strlen($1) + 1,
            .name = $1,
            .dataType = RdStreamDataTypeEnum($2),
            .dataSize = RdStreamDataSize($2),
            .isFixed = RdStreamDataIsFixed($2),
        };
        free($2);
    }
    ;

column_atts: {
    }
    | DEFAULT STRING {
        free($2);
    }
    | DEFAULT INTNUM {
    }
    | DEFAULT APPROXNUM {
    }
    | DEFAULT NAME '(' ')' {
        free($2);
    }
    ;

watermark_def: {
    }
    | ',' WATERMARK FOR NAME AS NAME '-' INTERVAL STRING WATERMARK_TIME WATERMARK_STRATEGY {
        free($4);
        free($6);
        free($9);
    }
    | ',' WATERMARK FOR NAME AS NAME '-' INTERVAL STRING WATERMARK_TIME {
        free($4);
        free($6);
        free($9);
    }
    | ',' WATERMARK FOR NAME AS NAME WATERMARK_STRATEGY {
        free($4);
        free($6);
    }
    | ',' WATERMARK FOR NAME AS NAME {
        free($4);
        free($6);
    }
    ;

dispatch_def: { 
    }
    | DISPATCH BY column_list {
    }
    ;

column_list: NAME {
        free($1);
    }
    |ref_def {

    }
    |column_list ',' NAME {
        free($3);
    }
    |column_list ',' ref_def {

    }
    ;

ref_def: REF '[' STRING ']' '[' NAME ']'{
    free($3);
    free($6);
    }
    ;
%%

void emit(char *s, ...)
{
    extern int yylineno;
    va_list ap;
    va_start(ap, s);
    fprintf(stdout, "[RD EMIT] [line: %d] ", yylineno);
    vfprintf(stdout, s, ap);
    fprintf(stdout, "\n");
    RdStreamSqlParserCtxT *ctx = RdStreamSqlParserCtxGet();
    ctx->status = RD_FAILED;
}

void yyerror(char *s, ...)
{
    extern char* yytext;
    extern int yylineno;
    char errMsg[128] = {0};

    va_list ap;
    va_start(ap, s);
    (void)vsnprintf(errMsg, sizeof(errMsg), s, ap);
    va_end(ap);

    RD_ERROR("%s at line %d: '%s'", errMsg, yylineno, yytext);
    RdStreamSqlParserCtxT *ctx = RdStreamSqlParserCtxGet();
    ctx->status = RD_FAILED;
}

RdStreamSqlParserT *RdStreamParseSql(const char *sql)
{
    RdStreamSqlParserCtxT *ctx = RdStreamSqlParserCtxGet();
    (void)pthread_mutex_lock(&ctx->mutex);
    ctx->status = RD_OK;

    void *bp = yy_scan_string(sql);
    yy_switch_to_buffer(bp);
    yyparse();
    yy_flush_buffer(bp);
    yy_delete_buffer(bp);

    if (ctx->status != RD_OK) {
        RD_ERROR("Unable to parse sql: %s", sql);
        (void)pthread_mutex_unlock(&ctx->mutex);
        return NULL;
    }
    RdStreamSqlParserT *parser = (RdStreamSqlParserT *)malloc(sizeof(RdStreamSqlParserT));
    if (parser == NULL) {
        RD_ERROR("Unable to alloc parser buffer.");
        (void)pthread_mutex_unlock(&ctx->mutex);
        return NULL;
    }
    (void)memcpy(parser, &ctx->parser, sizeof(RdStreamSqlParserT));
    (void)pthread_mutex_unlock(&ctx->mutex);

    return parser;
}
