/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: STREAM SQL解析
 * Author: guopanpan
 * Create: 2024-09-09
 */
#include <stdio.h>
#include "rd_stream_parser.h"

#define RD_STREAM_SQL_PARSER_ERROR(format, ...) printf("[ERROR] " format "\n", ##__VA_ARGS__)

#define RD_STREAM_DATA_TYPE_SIZE_BLOB 65536u            // 对应DB内部的byte类型，长度上限为64*1024
#define RD_STREAM_DATA_TYPE_SIZE_TEXT 65537u            // 对应DB内部的string类型，长度上限在64*1024的基础上加1字节结尾符
#define RD_STREAM_DATA_TYPE_SIZE_SPECIFIY UINT32_MAX    // 建表时指定大小

RdStreamSqlParserCtxT g_rdStreamSqlParserCtx = {.mutex = PTHREAD_MUTEX_INITIALIZER, .status = RD_OK, .parser = {0}};

static RdStreamDataTypeDescT g_rdStreamDataTypeDesc[] = {
    [RD_STREAM_DATA_INTEGER] = {"integer", true, sizeof(int64_t)},
    [RD_STREAM_DATA_TEXT] = {"text", false, RD_STREAM_DATA_TYPE_SIZE_TEXT},
    [RD_STREAM_DATA_BLOB] = {"blob", false, RD_STREAM_DATA_TYPE_SIZE_BLOB},
    [RD_STREAM_DATA_CHAR] = {"char", true, RD_STREAM_DATA_TYPE_SIZE_SPECIFIY},
    [RD_STREAM_DATA_REAL] = {"real", true, sizeof(double)},
};

RdStreamSqlParserCtxT *RdStreamSqlParserCtxGet()
{
    return &g_rdStreamSqlParserCtx;
}

const char *RdStreamDataTypeName(RdStreamDataTypeE type)
{
    return g_rdStreamDataTypeDesc[type].name;
}

RdStreamDataTypeE RdStreamDataTypeEnum(const char *typeName)
{
    for (uint32_t i = 0; i < sizeof(g_rdStreamDataTypeDesc) / sizeof(g_rdStreamDataTypeDesc[0]); i++) {
        // UPCO 只匹配数据类型的前缀，如CHAR和CHARACTER都会被匹配成CHAR，有空再优化
        if (strncasecmp(g_rdStreamDataTypeDesc[i].name, typeName, strlen(g_rdStreamDataTypeDesc[i].name)) == 0) {
            return (RdStreamDataTypeE)i;
        }
    }
    RD_STREAM_SQL_PARSER_ERROR("Unabel to parse data type (%s) to enum.", typeName);
    return RD_STREAM_DATA_BUTT;
}

bool RdStreamDataIsFixed(const char *typeName)
{
    for (uint32_t i = 0; i < sizeof(g_rdStreamDataTypeDesc) / sizeof(g_rdStreamDataTypeDesc[0]); i++) {
        if (strncasecmp(g_rdStreamDataTypeDesc[i].name, typeName, strlen(g_rdStreamDataTypeDesc[i].name)) == 0) {
            return g_rdStreamDataTypeDesc[i].fixed;
        }
    }
    RD_STREAM_SQL_PARSER_ERROR("Unabel to parse data type (%s) fixed.", typeName);
    return false;
}

uint32_t RdStreamDataSize(const char *typeName)
{
    for (uint32_t i = 0; i < sizeof(g_rdStreamDataTypeDesc) / sizeof(g_rdStreamDataTypeDesc[0]); i++) {
        if (strncasecmp(g_rdStreamDataTypeDesc[i].name, typeName, strlen(g_rdStreamDataTypeDesc[i].name)) == 0) {
            // UPCO 获取CHAR类型的长度，没时间细化，先写死，有空再通过正则匹配优化
            if (i == (int32_t)RD_STREAM_DATA_CHAR) {
                char tmpName[64] = {0};
                (void)strncpy(tmpName, typeName + 5, strlen(typeName) - 6);
                return (uint32_t)atoi(tmpName);
            }
            return g_rdStreamDataTypeDesc[i].size;
        }
    }
    RD_STREAM_SQL_PARSER_ERROR("Unabel to parse data type (%s) size.", typeName);
    return 0;
}

void RdStreamFreeSchema(RdStreamSchemaT *schema)
{
    if (schema == NULL) {
        return;
    }
    free(schema->tableName);
    uint32_t columnCnt = RdListElementCount(schema->columns);
    for (uint32_t i = 0; i < columnCnt; i++) {
        RdStreamColumnT *column = (RdStreamColumnT *)RdListGet(schema->columns, i);
        free(column->name);
    }
    RdListDestroy(schema->columns);
    free(schema);
}

void RdStreamFreeRecord(RdStreamRecordT *record)
{
    if (record == NULL) {
        return;
    }
    free(record->tableName);
    uint32_t valueCnt = RdListElementCount(record->values);
    for (uint32_t i = 0; i < valueCnt; i++) {
        RdStreamValueT *value = (RdStreamValueT *)RdListGet(record->values, i);
        switch (value->type) {
            case RD_STREAM_DATA_INTEGER: break;
            case RD_STREAM_DATA_REAL: break;
            case RD_STREAM_DATA_CHAR:
            case RD_STREAM_DATA_TEXT:
            case RD_STREAM_DATA_BLOB: free(value->value.addr); break;
            default: break;
        }
    }
    RdListDestroy(record->values);
    free(record);
}

void RdStreamFreeParser(RdStreamSqlParserT *parser)
{
    if (parser == NULL) {
        return;
    }
    switch (parser->type) {
        case RD_STREAM_SQL_TYPE_CREATE: RdStreamFreeSchema(parser->schema); break;
        case RD_STREAM_SQL_TYPE_INSERT: RdStreamFreeRecord(parser->record); break;
        default: break;
    }
    free(parser);
}

void RdStreamPrintColumn(RdStreamColumnT column)
{
    printf("  [\n");
    printf("    id = %u\n", column.id);
    printf("    name = %s (size: %u)\n", column.name, column.nameLen);
    printf("    dataType = %s (enum: %d)\n", RdStreamDataTypeName(column.dataType), column.dataType);
    printf("    dataSize = %u\n", column.dataSize);
    printf("    isFixed = %s\n", column.isFixed ? "true" : "false");
    printf("  ]\n");
}

void RdStreamPrintSchema(RdStreamSchemaT schema)
{
    uint32_t columnNum = RdListElementCount(schema.columns);
    printf("table_name: %s (size: %d)\n", schema.tableName, schema.tableNameLen);
    printf("column_num: %u\n", columnNum);
    printf("{\n");
    for (uint32_t i = 0; i < columnNum; i++) {
        RdStreamColumnT *column = (RdStreamColumnT *)RdListGet(schema.columns, i);
        RdStreamPrintColumn(*column);
    }
    printf("}\n");
}

void RdStreamPrintParser(RdStreamSqlParserT parser)
{
    switch (parser.type) {
        case RD_STREAM_SQL_TYPE_CREATE: RdStreamPrintSchema(*parser.schema); break;
        default: break;
    }
}
