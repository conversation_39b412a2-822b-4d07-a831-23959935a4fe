/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: STREAM SQL解析
 * Author: guopanpan
 * Create: 2024-09-09
 */
#ifndef RD_STREAM_PARSER_H
#define RD_STREAM_PARSER_H 1

#include <stdbool.h>
#include <string.h>
#include <stdint.h>
#include <pthread.h>
#include "rd_log.h"
#include "rd_list.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    RD_STREAM_DATA_INTEGER = 0,
    RD_STREAM_DATA_TEXT,
    RD_STREAM_DATA_BLOB,
    RD_STREAM_DATA_CHAR,
    RD_STREAM_DATA_REAL,
    RD_STREAM_DATA_BUTT,
} RdStreamDataTypeE;

typedef enum RdStreamTableType {
    RD_STREAM_TABLE_FIXED = 0,  // 平的vertex，字段均为定长，不包含变长
    RD_STREAM_TABLE_FLAT,       // 平的vertex，包含有变长
    RD_STREAM_TABLE_TREE,       // tree vertex，有node节点（只要是带子树节点的都是树模型）
} RdStreamTableTypeE;

typedef struct {
    const char *name;
    bool fixed;
    uint32_t size;
} RdStreamDataTypeDescT;

// 当前版本支持的建表语法: create stream table-name (id integer, name char(20));
typedef struct RdStreamColumn {
    uint32_t id;                    // 列id，从0开始计数，按列的定义顺序递增编号
    uint16_t nameLen;               // 列名长度
    char *name;                     // 列名
    RdStreamDataTypeE dataType;     // 数据类型
    uint32_t dataSize;              // 数据长度，定长列是固定长度，变长列是最大长度
    bool isFixed;                   // 是否是定长类型
} RdStreamColumnT;

typedef struct RdStreamSchema {
    uint32_t tableNameLen;      // 表名长度，包含结尾符
    char *tableName;            // 表名
    RdListT columns;            // 列定义数组 (RdStreamColumnT)
} RdStreamSchemaT;

// 当前版本支持的插入语法: insert into table-name values (1, 'aaa');
typedef struct {
    RdStreamDataTypeE type;     // 数据类型
    union {
        int64_t intVal;         // 整形数据
        double doubleVal;       // 浮点型数据
        struct {
            uint32_t size;      // 变长数据长度
            void *addr;         // 变长数据地址
        };
    } value;
} RdStreamValueT;

typedef struct {
    uint32_t tableNameLen;  // 表名长度，包含结尾符
    char *tableName;        // 表名
    RdListT values;         // 列值数组 (RdStreamValueT)
} RdStreamRecordT;

typedef enum {
    RD_STREAM_SQL_TYPE_INVALID = 0,
    RD_STREAM_SQL_TYPE_CREATE,
    RD_STREAM_SQL_TYPE_INSERT,
} RdStreamSqlTypeE;

typedef struct {
    RdStreamSqlTypeE type;
    union {
        RdStreamSchemaT *schema;
        RdStreamRecordT *record;
    };
} RdStreamSqlParserT;

typedef struct {
    pthread_mutex_t mutex;
    int32_t status;
    RdStreamSqlParserT parser;
} RdStreamSqlParserCtxT;

extern RdStreamSqlParserT *RdStreamParseSql(const char *sql);
extern void RdStreamFreeParser(RdStreamSqlParserT *parser);

void RdStreamPrintParser(RdStreamSqlParserT ctx);

RdStreamSqlParserCtxT *RdStreamSqlParserCtxGet();
uint32_t RdStreamDataSize(const char *typeName);
RdStreamDataTypeE RdStreamDataTypeEnum(const char *typeName);
bool RdStreamDataIsFixed(const char *typeName);

#ifdef __cplusplus
}
#endif

#endif /* end of RD_STREAM_PARSER_H */
