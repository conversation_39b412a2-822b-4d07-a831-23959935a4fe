/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: STREAM SQL词法分析
 * Author: guopanpan
 * Create: 2024-09-09
 */
%option noyywrap nodefault yylineno case-insensitive
%{
#include "rd_stream_parser.h"
#include "rd_stream_parser.tab.h"

#include <stdarg.h>
#include <string.h>
#include <stdio.h>

extern void yyerror(char *s, ...);
extern void emit(char *s, ...);
extern int yylex();
%}

%%
    /* 创建流表: CREATE STREAM TABLE IF NOT EXISTS table_name (column_name data_type [, ...]); */
CREATE { return CREATE; }
STREAM { return STREAM; }
TABLE { return TABLE; }
DEFAULT { return DEFAULT; }
IF { return IF; }
NOT { return NOT; }
EXISTS { return EXISTS; }

    /* WATERMARK: WATERMARK FOR time_column_name AS time_column_name [ - INTERVAL 'num' SECONDS] [STRICT | TOLERANT] */
WATERMARK { return WATERMARK; }
FOR { return FOR; }
AS { return AS; }
INTERVAL { return INTERVAL; }
SECONDS {
    return WATERMARK_TIME;
}
STRICT |
TOLERANT {
    return WATERMARK_STRATEGY;
}

    /* DISPATCH: DISPATCH BY [column_name,REF['ref_name'][column_name]] */
DISPATCH { return DISPATCH; }
BY { return BY; }
REF { return REF; }

    /* 数据类型 */
INTEGER |
CHAR\([0-9]*\) |
TEXT |
BLOB |
REAL {
    yylval.strval = strdup(yytext);
    return DATA_TYPE;
}

    /* 插入数据: INSERT INTO table_name VALUES (value [, ..]); */
INSERT { return INSERT; }
INTO { return INTO; }
VALUES { return VALUES; }

    /* 数字 */
-?[0-9]+ { yylval.intval=atoi(yytext); return INTNUM; }
-?[0-9]+"."[0-9]* |
-?"."[0-9]+ |
-?[0-9]+E[-+]?[0-9]+ |
-?[0-9]+"."[0-9]*E[-+]?[0-9]+ |
-?"."[0-9]+E[-+]?[0-9]+ { yylval.floatval=atof(yytext); return APPROXNUM; }

    /* 字符串 */
'(\\.|''|[^'\n])*' |
\"(\\.|\"\"|[^"\n])*?\" {
        yylval.strval = strdup(yytext+1);
        yylval.strval[yyleng-2]=0;
        return STRING;
    }

'(\\.|[^'\n])*$ { yyerror("Unterminated string %s", yytext); }
\"(\\.|[^"\n])*$ { yyerror("Unterminated string %s", yytext); }

    /* 十六进制字符串 */
X'[0-9A-F]+' |
0X[0-9A-F]+ { yylval.strval = strdup(yytext); return STRING; }

    /* 二进制字符串 */
0B[01]+ |
B'[01]+' { yylval.strval=strdup(yytext); return STRING; }

    /* 名字 */
[A-Za-z\200-\377_][A-Za-z\200-\377_0-9]* {
    yylval.strval = strdup(yytext);
    return NAME;
}

`[^`/\\.\n]+` {
    yylval.strval=strdup(yytext+1);
    yylval.strval[yyleng-2]=0;
    return NAME;
}

    /* 操作符 */
[-+&~|^/%*(),.;!\[\]] { return yytext[0]; }

`[^`\n]*$ { yyerror("unterminated quoted name %s", yytext); }

    /* 注释 */
#.* ;
"--"[ \t].* ;

    /* 其他 */
[ \t\n]
. { yyerror("unknown character '%c'", *yytext); }
%%
