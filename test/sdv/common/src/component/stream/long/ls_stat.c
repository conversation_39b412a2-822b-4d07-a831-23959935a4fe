/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 统计资源开销
 * Author: guopanpan
 * Create: 2024-03-12
 */
#include "ls_stat.h"

// 获取并打印磁盘的使用情况，单位GB
void LsResPrintDiskUsage(const char *mountPath)
{
    uint64_t totalSize = RdDiskGetTotalSize(mountPath) / 1024 / 1024 / 1024;  // GB
    uint64_t freeSize = RdDiskGetFreeSize(mountPath) / 1024 / 1024 / 1024; // GB
    uint64_t usedSize = totalSize - freeSize;
    printf("%-10s, total = %-2lu GB, used = %-2lu GB, free = %-2lu GB\n", mountPath, totalSize, usedSize, freeSize);
}

// 获取并打印目录的大小
void LsResPrintDirSize(const char *dirPath)
{
    double size = (double)RdDirSize(dirPath) / 1024;  // KB
    if (size / 1024 / 1024 >= 1) {
        printf("[%s] size = %-8.3lf GB\n", dirPath, size / 1024 / 1024);
    } else if (size / 1024 >= 1) {
        printf("[%s] size = %-8.3lf MB\n", dirPath, size / 1024);
    } else {
        printf("[%s] size = %-8.3lf KB\n", dirPath, size);
    }
}

void LsResGet(pid_t pid, const char *dataDir, RdResInfoT *res)
{
    memset(res, 0x0, sizeof(RdResInfoT));
    time_t t = time(NULL);
    if (t >= 0) {
        (void)strftime(res->datetime, RD_MEM_MAX_DATE_TIME_LEN, "%Y-%m-%d %H:%M:%S", localtime(&t));
    }
    RdMemGetProcInfo(pid, &res->mem);

    // NOTICE 持久化文件路径需要通过读取配置JSON获取（转换为MB）
    res->dataDirSize = (double)RdDirSize(dataDir) / 1024 / 1024;

    // CPU采样频率为100000微妙(100毫秒)
    res->cpuUsageRatio = RdCpuProcUsageRatio(pid, 100000);
}

/**
 * @brief 打印资源开销
 */
void LsResPrint(const char *log, int32_t pid, RdResInfoT res)
{
    // 单位转换为MB
    printf("[%s] [%s] [pid-%d] VmRss = %-12.3lf MB, DataDirSize = %-12.3lf MB, CpuUsageRation = %-12.3lf%%\n",
        res.datetime, log, pid, res.mem.vmRss / (double)RD_KIBI, res.dataDirSize, res.cpuUsageRatio);
}

void LsResWriteKb(const char *filePath, int32_t pid, RdResInfoT res)
{
    const char *head = "Datetime           , pid        , "
                       "VmPeak (KB), VmSize (KB), VmHwm (KB), VmRss (KB), DataFileSize (MB), CpuUsageRatio (%)\n";
    const char *bodyFmt = "%-19s, %-11d, %-11d, %-11d, %-10d, %-10d, %-17.3lf, %-18.3lf\n";

    FILE *fp = fopen(filePath, "a+");
    if (fp == NULL) {
        RD_ERROR("Unable to open file (%s).", filePath);
        return;
    }

    char buff[RD_STAT_MAX_BUFF_SIZE] = {0};
    (void)fseek(fp, 0, SEEK_SET);
    (void)fread(buff, sizeof(char), strlen(head), fp);
    if (strncmp(buff, head, strlen(head)) != 0) {
        (void)fwrite(head, sizeof(char), strlen(head), fp);
    }

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wformat-nonliteral" // 编译检查不支持将格式化字符传入
    (void)fprintf(fp, bodyFmt, res.datetime, pid,
        res.mem.vmPeak, res.mem.vmSize, res.mem.vmHwm, res.mem.vmRss, res.dataDirSize, res.cpuUsageRatio);
#pragma GCC diagnostic pop
    (void)fclose(fp);
}

void LsResWriteMb(const char *filePath, int32_t pid, RdResInfoT res)
{
    const char *head = "Datetime           , pid        , "
                       "VmPeak (MB), VmSize (MB), VmHwm (MB), VmRss (MB), DataFileSize (MB), CpuUsageRatio (%)\n";
    const char *bodyFmt = "%-19s, %-11d, %-11.3lf, %-11.3lf, %-10.3lf, %-10.3lf, %-17.3lf, %-18.3lf\n";

    FILE *fp = fopen(filePath, "a+");
    if (fp == NULL) {
        RD_ERROR("Unable to open file (%s).", filePath);
        return;
    }

    char buff[RD_STAT_MAX_BUFF_SIZE] = {0};
    (void)fseek(fp, 0, SEEK_SET);
    (void)fread(buff, sizeof(char), strlen(head), fp);
    if (strncmp(buff, head, strlen(head)) != 0) {
        (void)fwrite(head, sizeof(char), strlen(head), fp);
    }

    // 单位转换为MB
    double vmPeak = res.mem.vmPeak / (double)RD_KIBI;
    double vmSize = res.mem.vmSize / (double)RD_KIBI;
    double vmRss = res.mem.vmRss / (double)RD_KIBI;
    double vmHwm = res.mem.vmHwm / (double)RD_KIBI;
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wformat-nonliteral" // 编译检查不支持将格式化字符传入
    (void)fprintf(fp, bodyFmt,
        res.datetime, pid, vmPeak, vmSize, vmHwm, vmRss, res.dataDirSize, res.cpuUsageRatio);
#pragma GCC diagnostic pop
    (void)fclose(fp);
}

/**
 * @brief 将资源开销写入文件
 */
void LsResWrite(const char *filePath, int32_t pid, RdResInfoT res)
{
    LsResWriteMb(filePath, pid, res);
}

/**
 * Function: 打印当前资源开销
 * From: LsPrintCurRes
 */
void LsResCollectAndSave(const char *log, RdResThreadT th, uint64_t *lastWriteTime)
{
    RD_UNUSED(lastWriteTime);
    RdResInfoT res;
    LsResGet(th.pid, th.dataDir, &res);
    if (th.isPrint) {
        LsResPrint(log, th.pid, res);
    }
    LsResWrite(th.filePath, th.pid, res);

#ifdef RD_RES_ENABLE_PART_FILE
    // 如果资源采集数据过多，导致文件过大，允许每隔十分钟写一次文件
    uint64_t writeInterval = 60 * 10;
    uint64_t curTime = RdGetSec();
    if (curTime - *lastWriteTime > writeInterval) {
        char partFileName[RD_STAT_MAX_NAME_LEN] = {0};
        (void)snprintf(partFileName, sizeof(partFileName), "%s.part.csv", th.filePath);
        RdResWrite(partFileName, res);
        *lastWriteTime = curTime;
    }
#endif
}

void *LsResCollectEntity(void *thread)
{
    RdSetThreadName("ResCollect");
    RdResThreadT *th = (RdResThreadT *)thread;
    uint64_t lastWriteTime = 0;
    while (!th->isStop) {
        LsResCollectAndSave("RESOURCE", *th, &lastWriteTime);
        sleep(th->interval);
    }
    return NULL;
}

void LsResCreateCollectThread(RdResThreadT *thread)
{
    thread->isStop = false;
    int32_t ret = pthread_create(&thread->thread, NULL, LsResCollectEntity, thread);
    if (ret != RD_OK) {
        RD_ERROR("Unable to create collect thread.");
    }
}

void LsResDestroyCollectThread(RdResThreadT *thread)
{
    thread->isStop = true;
    int32_t ret = pthread_join(thread->thread, NULL);
    if (ret != RD_OK) {
        RD_ERROR("Unable to join collect thread.");
    }
}
