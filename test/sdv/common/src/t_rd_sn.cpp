/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_sn.c
 * Author: lushiguang
 * Create: 2023-06-15
 */

#include "t_rd_sn.h"
#include "t_rd_assert.h"

sem_t g_sem;

int close_usr_epoll_thread(EpollThreadDataT *epollData)
{
    if (epollData->isInited) {
        epollData->epollThreadExit = true;
        pthread_join(epollData->epollThreadId, NULL);
        epollData->epollThreadId = -1;
        epollData->isInited = false;
    }
    return 0;
}

int create_timeout_epoll_thread()
{
    int ret = 0;
    pthread_mutex_lock(&g_timeoutEpollLock);
    if (g_timeoutEpollData.isInited) {
        pthread_mutex_unlock(&g_timeoutEpollLock);
        return 0;
    }
    sem_init(&g_timeoutEpollData.sem, 0, 0);
    // 创建超时epoll对象
    ret = pthread_create(&g_timeoutEpollData.epollThreadId, NULL, EpollThreadFunc, &g_timeoutEpollData);
    if (ret != 0) {
        const char *err_info = strerror(errno);
        printf("create time out epoll thread failed, errmsg = %s\n", err_info);
        pthread_mutex_unlock(&g_timeoutEpollLock);
        return ret;
    }
    g_timeoutEpollData.isInited = true;
    sem_wait(&g_timeoutEpollData.sem);
    pthread_mutex_unlock(&g_timeoutEpollLock);

    ret = GmcRegTimeoutEpollFunc(epollRegTimeout);
    if (ret != 0) {
        printf("GmcRegTimeoutEpollFunc failed, ret = %d\n", ret);
        return ret;
    }
    return ret;
}

int close_timeout_epoll_thread()
{
    int ret = 0;
    pthread_mutex_lock(&g_timeoutEpollLock);
    if (g_timeoutEpollData.isInited) {
        ret = close_usr_epoll_thread(&g_timeoutEpollData);
        if (ret != 0) {
            pthread_mutex_unlock(&g_timeoutEpollLock);
            printf("close time out epoll thread failed, ret = %d\n", ret);
            return ret;
        }
    }
    pthread_mutex_unlock(&g_timeoutEpollLock);
    return ret;
}

int close_epoll_thread(EpollThreadDataT *epollData)
{
    int ret = close_timeout_epoll_thread();
    if (ret != 0) {
        printf("close_timeout_epoll_thread failed, ret = %d\n", ret);
        return ret;
    }
    return close_usr_epoll_thread(epollData);
}

void closeUsrEpollOneThread(EpollThreadDataT *epollData)
{
    if (epollData->isInited) {
        TEST_EPOLL_CLOSE(epollData->userEpollFd);
        epollData->userEpollFd = -1;
        epollData->isInited = false;
    }
}

// createEpollOneThread 和 closeEpollOneThread 用于 单线程单epoll场景 或 多线程单epoll场景
int createEpollOneThread(EpollThreadDataT *epollData)
{
    if (epollData->isInited) {
        return 0;
    }
    epollData->userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (epollData->userEpollFd < 0) {
        printf("[createEpollOneThread] TEST_EPOLL_CREATE failed, epollFd : %d\n", epollData->userEpollFd);
        return -1;
    }
    epollData->isInited = true;
    g_epollRegInfoOneThread = epollRegWithUserdata;
    int ret = GmcRegHeartBeatEpollFuncWithUserData(epollRegWithUserdata, &epollData->userEpollFd);
    if (ret == GMERR_DUPLICATE_HEARTBEAT_REGISTER) {
        AddWhiteList(GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    }
    if (ret != 0 && ret != GMERR_DUPLICATE_HEARTBEAT_REGISTER) {
        printf("[createEpollOneThread] GmcRegHeartBeatEpollFuncWithUserData failed, ret : %d\n", ret);
        return ret;
    }
    g_epollDataOneThread.createInCurThread = true;
    if (epollData->notCreateGlobalTimerFd) {
        return 0;
    }
    return create_timeout_epoll_thread();
}

// createEpollOneThread 和 closeEpollOneThread 用于 单线程单epoll场景 或 多线程单epoll场景
void closeEpollOneThread(EpollThreadDataT *epollData)
{
    int ret = close_timeout_epoll_thread();
    if (ret != 0) {
        printf("close_timeout_epoll_thread failed, ret = %d\n", ret);
    }
    closeUsrEpollOneThread(epollData);
}

void *EpollThreadFunc(void *args)
{
    EpollThreadDataT *epollData = (EpollThreadDataT *)args;
    epollData->epollThreadExit = false;
    epollData->userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    sem_post(&epollData->sem);
    if (epollData->userEpollFd < 0) {
        printf("[EpollThreadFunc] TEST_EPOLL_CREATE failed, epollFd : %d\n", epollData->userEpollFd);
        return NULL;
    }

    epollData->events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    while (!epollData->epollThreadExit) {
        int32_t fdCount =
            TEST_EPOLL_WAIT(epollData->userEpollFd, epollData->events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        for (int32_t i = 0; i < fdCount; i++) {
            TestEpollDoEvent(epollData->events[i].data.fd, epollData->events[i].events);
        }
    }

    free(epollData->events);
    TEST_EPOLL_CLOSE(epollData->userEpollFd);
    epollData->userEpollFd = -1;
    return NULL;
}

// create_epoll_thread 与 close_epoll_thread配套使用
int create_epoll_thread(EpollThreadDataT *epollData)
{
    if (epollData->isInited) {
        return 0;
    }
    sem_init(&epollData->sem, 0, 0);

    int ret = pthread_create(&epollData->epollThreadId, NULL, EpollThreadFunc, epollData);
    if (ret != 0) {
        const char *errInfo = strerror(errno);
        printf("create epoll thread failed, errmsg = %s\n", errInfo);
        return ret;
    }
    epollData->isInited = true;
    sem_wait(&epollData->sem);

    ret = GmcRegHeartBeatEpollFuncWithUserData(epollRegWithUserdata, &epollData->userEpollFd);
    if (ret == GMERR_DUPLICATE_HEARTBEAT_REGISTER) {
        AddWhiteList(GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    }
    if (ret != 0 && ret != GMERR_DUPLICATE_HEARTBEAT_REGISTER) {
        printf("[create_epoll_thread] GmcRegHeartBeatEpollFuncWithUserData failed, ret : %d\n", ret);
        return ret;
    }
    g_epollData.createBgThread = true;
    if (epollData->notCreateGlobalTimerFd) {
        return 0;
    }
    return create_timeout_epoll_thread();
}

// 以下回调用法区分：
// 名称以 _callback 结尾的，将 status、affectedRows、totalNum、successNum 赋给 userData，不在回调中判断，在回调外面判断
// 名称以 _callback_verify 结尾的，在回调中判断 EXPECT_EQ(GMERR_OK, status)，其余暂不判断，有需求可讨论增加
// 新增 _callback_verify 结尾的回调函数 目的在于
// 一次发送多个异步请求的情况，后面统一接收的情况下，可直接在回调中比较状态值，不再在用例里判断
void dml_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg, char *opMsg)
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
}

void TransSavePointCb(void *userData, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->status = status;
        userData1->historyRecvNum++;
        if (userData1->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(userData1->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", userData1->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userData1->recvNum++;
    }
}

void create_vertex_label_callback(void *userData, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
#ifdef FEATURE_YANG
    GmcYangFreeErrorPathInfo();
#endif
}

void create_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    EXPECT_EQ(GMERR_OK, status);
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
#ifdef FEATURE_YANG
    GmcYangFreeErrorPathInfo();
#endif
}

void drop_vertex_label_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void drop_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void get_vertex_label_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void get_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void insert_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->affectRows = affectedRows;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
}

void insert_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"INSERT");
}

void update_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void update_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"UPDATE");
}

void delete_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void delete_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"DELETE");
}

void batch_execute_callback(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
#if defined FEATURE_FASTPATH
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
#ifdef FEATURE_YANG
        if (user_data->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
            // 结果检查
            EXPECT_EQ(user_data->expectedErrorCode, msg.errorCode);
            EXPECT_STREQ(user_data->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(user_data->expectedErrPath, msg.errorPath);
        }
#endif
#endif
        user_data->recvNum++;
    }
}

void merge_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void merge_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"MERGE");
}

void replace_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void replace_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"REPLACE");
}

void truncate_vertex_label_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void truncate_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void create_kv_table_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void create_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void drop_kv_table_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void drop_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void get_kv_table_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void get_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void set_kv_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void set_kv_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"KV SET");
}

void delete_kv_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void delete_kv_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"KV DELETE");
}

void truncate_kv_table_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void truncate_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void delete_vertex_by_cond_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void delete_vertex_by_cond_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"DELETE BY COND");
}

void update_vertex_by_cond_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}

void update_vertex_by_cond_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    dml_callback_verify(userData, affectedRows, status, errMsg, (char *)"UPDATE BY COND");
}

void use_namespace_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void use_namespace_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void create_namespace_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void create_namespace_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void ClearNSCallbak(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void ClearNSVerifyCallbak(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void drop_namespace_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void drop_namespace_callback_verify(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_verify(userData, status, errMsg);
}

void trans_start_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void trans_commit_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void trans_rollback_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void savepoint_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void trans_checkConflict_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}
void FetchAndDeparseDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        if (userData1->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(userData1->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", userData1->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userData1->recvNum++;
    }
}

#ifdef EXPERIMENTAL_GUANGQI
void trans_start_ext_callback(void *userData, GmcStartExtResT *startExtRes, int32_t status, const char *errMsg)
{
    int ret;

    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        AW_MACRO_EXPECT_EQ_INT(userData1->expStatus, status);
        userData1->status = status;
        userData1->historyRecvNum++;

        // 获取cloneId句柄
        if (startExtRes != NULL) {
            ret = GmcTransStartExtResGetCloneId(startExtRes, &userData1->cloneId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (userData1->lastError != NULL) {
            if (errMsg) {
                ret = strcmp(userData1->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", userData1->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userData1->recvNum++;
    }
}
#endif

void create_edge_label_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void drop_edge_label_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

// 表空间回调函数
void create_tablespace_callback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void drop_tablespace_callback(void *userData, int32_t status, const char *errMsg)
{
    create_tablespace_callback(userData, status, errMsg);
}

int testSubConnect(GmcConnT **connOut, GmcStmtT **stmt, bool needEpoll, EpollRegFunctionT epollReg,
    const char *connName, const void *chanRingLen, ConnOptionT *connOptions,
    const int32_t *packShrinkThresholdSize, int runMode, const char *userName)
{
    return testGmcConnect(connOut, stmt, GMC_CONN_TYPE_SUB, needEpoll, epollReg, connName, chanRingLen, connOptions,
        packShrinkThresholdSize, runMode, 0, &g_epollData.userEpollFd, false, userName);
}

int testSubDisConnect(GmcConnT *conn, GmcStmtT *stmt)
{
    return testGmcDisconnect(conn, stmt);
}

/* **********************************************************************
 * 函数: testWaitSnRecv
 * 功能: 老订阅/状态合并订阅 推送次数稳定可预期的情况下, 验证推送次数的正确性。
 * 参数:
 * userData[in] -- 订阅回调用户上下文。
 * eventType[in] -- 要校验的推送事件。
 * expRecvNum[in] -- 预期该推送事件收到的推送次数。
 * timeout[in] -- 推送等待超时时间, 默认1min。
 * isAutoReset[in] -- 验证完推送次数后, 是否清空计数。
 * ********************************************************************** */
int testWaitSnRecv(void *userData, GmcSubEventTypeE eventType, int32_t expRecvNum, int timeout, bool isAutoReset)
{
    int waitCnt = 0, ret = 0, *recvNum = NULL;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char typeMsg[128] = {0};

    switch (eventType) {
        case GMC_SUB_EVENT_INSERT: {
            recvNum = &user_data->insertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INSERT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            recvNum = &user_data->deleteNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_DELETE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_UPDATE: {
            recvNum = &user_data->updateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_REPLACE: {
            recvNum = &user_data->replaceNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_REPLACE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_REPLACE_INSERT: {
            recvNum = &user_data->replaceInsertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_REPLACE_INSERT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_REPLACE_UPDATE: {
            recvNum = &user_data->replaceUpdateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_REPLACE_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MERGE: {
            recvNum = &user_data->mergeNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MERGE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MERGE_INSERT: {
            recvNum = &user_data->mergeInsertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MERGE_INSERT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MERGE_UPDATE: {
            recvNum = &user_data->mergeUpdateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MERGE_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_KV_SET: {
            recvNum = &user_data->kvSetNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_KV_SET";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            recvNum = &user_data->scanNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INITIAL_LOAD";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            recvNum = &user_data->scanEofNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INITIAL_LOAD_EOF";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_AGED: {
            recvNum = &user_data->agedNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_AGED";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN: {
            recvNum = &user_data->triggerScanBeginNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN: {
            recvNum = &user_data->triggerScanNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN_END: {
            recvNum = &user_data->triggerScanEndNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN_END";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MODIFY: {
            // 由于存量用例的MODIFY事件推送已使用insertNum, 故不新增modifyNum
            recvNum = &user_data->insertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MODIFY";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_DIFF: {
            recvNum = &user_data->diffNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_DIFF";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_DIFF_EXPLICIT: {
            recvNum = &user_data->diffExplicitNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_DIFF_EXPLICIT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        default: {
            printf("default: invalid eventType\r\n");
            assert(0);
            break;
        }
    }
    struct timeval start;
    struct timeval end;
    unsigned long duration;
    gettimeofday(&start, NULL);

    while (*recvNum != expRecvNum) {
        usleep(500);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            AW_FUN_Log(LOG_INFO, "%s: Recv Timeout %lf s. Expected to receive %d times, actually received %d times",
                typeMsg, (double)duration / 1000000, expRecvNum, *recvNum);
            ret = -1;
            break;
        }
    }
    if (isAutoReset) {
        *recvNum = 0;
    }
    return ret;
}

/* **********************************************************************
 * 函数: testWaitStMgSnRecv
 * 功能: 状态合并订阅 推送次数不稳定的情况下, 验证推送的次数符合某个范围。
 * 参数:
 * userData[in] -- 订阅回调用户上下文。
 * eventType[in] -- 要校验的推送事件。
 * expMinRecvNum[in] -- 预期该推送事件收到的最小的推送次数。
 * expMaxRecvNum[in] -- 预期该推送事件收到的最大的推送次数。
 * timeout[in] -- 推送等待超时时间, 默认1min。
 * isAutoReset[in] -- 验证完推送次数后, 是否清空计数。
 * ********************************************************************** */
int testWaitStMgSnRecv(void *userData, GmcSubEventTypeE eventType, int32_t expMinRecvNum, int32_t expMaxRecvNum,
    int timeout, bool isAutoReset)
{
    int waitCnt = 0, ret = 0, *recvNum = NULL;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char typeMsg[128] = {0};

    if (expMaxRecvNum != -1) {
        if (expMinRecvNum > expMaxRecvNum) {
            AW_FUN_Log(LOG_ERROR, "expMinRecvNum(%d) GT expMaxRecvNum(%d)", expMinRecvNum, expMaxRecvNum);
            return FAILED;
        }
    }

    switch (eventType) {
        case GMC_SUB_EVENT_MODIFY: {
            // 由于存量用例的MODIFY事件推送已使用insertNum, 故不新增modifyNum
            recvNum = &user_data->insertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MODIFY";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            recvNum = &user_data->deleteNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_DELETE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_AGED: {
            recvNum = &user_data->agedNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_AGED";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            recvNum = &user_data->scanNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INITIAL_LOAD";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            recvNum = &user_data->scanEofNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INITIAL_LOAD_EOF";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN: {
            recvNum = &user_data->triggerScanNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        // 此处用于老订阅预期不稳定的用例
        case GMC_SUB_EVENT_UPDATE: {
            recvNum = &user_data->updateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        default: {
            printf("default: invalid eventType\r\n");
            assert(0);
            break;
        }
    }
    struct timeval start;
    struct timeval end;
    unsigned long duration;
    gettimeofday(&start, NULL);

    int revTemp = 0;
    int eqTimes = 0;
    while (eqTimes < 10) {
        usleep(100000);
        if (*recvNum == revTemp) {
            eqTimes ++;
        }
        revTemp = *recvNum;
    }

    while (*recvNum < expMinRecvNum) {
        usleep(500);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            ret = -1;
            break;
        }
    }

    if (expMaxRecvNum != -1 && *recvNum > expMaxRecvNum) {
        ret = -2;
    }
    if (ret != 0) {
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        AW_FUN_Log(LOG_INFO, "%s: Recv %lf s. Expected to receive %d to %d times, actually received %d times\r\n",
            typeMsg, (double)duration / 1000000, expMinRecvNum, expMaxRecvNum, *recvNum);
    }
    if (isAutoReset) {
        *recvNum = 0;
    }
    return ret;
}

// 申请订阅的UserData内存
int testSnMallocUserData(SnUserDataT **userData, uint32_t sizeMalloc, uint32_t checkNum)
{
    SnUserDataT *user_data = NULL;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (!user_data) {
        printf("[testSnMallocUserData] user_data malloc failed\n");
        return -1;
    }
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * sizeMalloc);
    if (!user_data->new_value) {
        printf("[testSnMallocUserData] user_data->new_value malloc failed\n");
        return -1;
    }
    memset(user_data->new_value, 0, sizeof(int) * sizeMalloc);

    user_data->old_value = (int *)malloc(sizeof(int) * sizeMalloc);
    if (!user_data->old_value) {
        printf("[testSnMallocUserData] user_data->old_value malloc failed\n");
        return -1;
    }
    memset(user_data->old_value, 0, sizeof(int) * sizeMalloc);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * sizeMalloc);
    if (!user_data->isReplace_insert) {
        printf("[testSnMallocUserData] user_data->isReplace_insert malloc failed\n");
        return -1;
    }
    memset(user_data->isReplace_insert, 0, sizeof(bool) * sizeMalloc);

    user_data->stmgSubCtx = (StmgSubCtxT *)malloc(sizeof(StmgSubCtxT) * sizeMalloc);
    if (!user_data->stmgSubCtx) {
        printf("[testSnMallocUserData] user_data->isReplace_insert malloc failed\n");
        return -1;
    }
    memset(user_data->stmgSubCtx, 0, sizeof(StmgSubCtxT) * sizeMalloc);

    if (checkNum > 0) {
        user_data->dataCheckIndexes = (uint8_t *)malloc(sizeof(uint8_t) * checkNum);
        if (!user_data->dataCheckIndexes) {
            printf("[testSnMallocUserData] user_data->dataCheckIndexes malloc failed\n");
            return -1;
        }
        memset(user_data->dataCheckIndexes, 0, sizeof(uint8_t) * checkNum);
    }

    *userData = user_data;
    return 0;
}

// 释放 订阅UserData malloc申请的内存
void testSnFreeUserData(SnUserDataT *userData)
{
    free(userData->new_value);
    free(userData->old_value);
    free(userData->isReplace_insert);
    free(userData->stmgSubCtx);
    if (userData->dataCheckIndexes != NULL) {
        free(userData->dataCheckIndexes);
    }
    free(userData);
}

// 申请异步的UserData内存
int testAsyncMallocUserData(AsyncUserDataT **userData)
{
    AsyncUserDataT *user_data = NULL;
    user_data = (AsyncUserDataT *)malloc(sizeof(AsyncUserDataT));
    if (!user_data) {
        printf("[testAsyncMallocUserData] user_data malloc failed\n");
        return -1;
    }
    memset(user_data, 0, sizeof(AsyncUserDataT));

    *userData = user_data;
    return 0;
}

// 释放 异步UserData malloc申请的内存
void testAsyncFreeUserData(AsyncUserDataT *userData)
{
    free(userData);
}

void TEST_AsyncDataShow(AsyncUserDataT *asyncData)
{
    if (asyncData == NULL) {
        return;
    }
    AW_FUN_Log(LOG_DEBUG,
        "status:%d, threadId: %d, recvNum:%d, historyRecvNum:%d, affectRows:%d, totalNum:%d, succNum:%d, expStatus:%d, "
        "expAffectRows:%d, expTotalNum:%d, expSuccNum:%d",
        asyncData->status, asyncData->threadId, asyncData->recvNum, asyncData->historyRecvNum, asyncData->affectRows,
        asyncData->totalNum, asyncData->succNum, asyncData->expStatus, asyncData->expAffectRows, asyncData->expTotalNum,
        asyncData->expSuccNum);
    if (asyncData->lastError != NULL) {
        AW_FUN_Log(LOG_ERROR, "lastError:%s", asyncData->lastError);
    }
}

int TEST_TransStartAsync(GmcConnT *conn, bool isYang, const GmcTxConfigT *trxConfig, int32_t timeout)
{
    GmcTxConfigT defaultTrxCfg = { 0 };
    if (trxConfig == NULL) {
        if (isYang) {
            defaultTrxCfg.transMode = GMC_TRANS_USED_IN_CS;
            defaultTrxCfg.readOnly = false;
            defaultTrxCfg.type = GMC_TX_ISOLATION_REPEATABLE;
            defaultTrxCfg.trxType = GMC_OPTIMISTIC_TRX;
            trxConfig = &defaultTrxCfg;
        } else {
            defaultTrxCfg.transMode = GMC_TRANS_USED_IN_CS;
            defaultTrxCfg.readOnly = false;
            defaultTrxCfg.type = GMC_TX_ISOLATION_COMMITTED;
            defaultTrxCfg.trxType = GMC_DEFAULT_TRX;
            trxConfig = &defaultTrxCfg;
        }
    }
    AsyncUserDataT data = { 0 };
    int ret = GmcTransStartAsync(conn, trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = testWaitAsyncRecv(&data, 1, timeout);
    TEST_AsyncDataShow(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    return ret;
}

int TEST_TransCommitAsync(GmcConnT *conn, int32_t timeout)
{
    AsyncUserDataT data = { 0 };
    int ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = testWaitAsyncRecv(&data, 1, timeout);
    TEST_AsyncDataShow(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    return ret;
}

int TestYangGmcConnectWithEpollOneThread(GmcConnT **connOut, GmcStmtT **stmt, int syncMode,
    YangConnOptionT *connOptionsInput, EpollThreadDataT *epollData, int runMode)
{
    int ret = 0;
    // 强制使用单线程EPOLL
    YangConnOptionT connOptions = {0};
    if (connOptionsInput == NULL) {
        connOptionsInput = &connOptions;
    }
    connOptionsInput->isOneThreadEpoll = true;
    connOptionsInput->epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptionsInput->epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptionsInput->epollFd = &epollData->userEpollFd;
    AW_FUN_Log(LOG_DEBUG_ALL, "[TestYangGmcConnectWithEpollOneThread]epollfd:%d\n", (int32_t)*(int32_t*)(connOptionsInput->epollFd));
    ret = TestYangGmcConnect(connOut, stmt, syncMode, connOptionsInput);
    return ret;
}
