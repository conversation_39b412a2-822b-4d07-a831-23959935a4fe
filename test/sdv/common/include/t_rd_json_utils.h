/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_json_utils.h
 * Author: lushiguang
 * Create: 2023-06-15
 */
#ifndef T_RD_JSON_UTILS_H
#define T_RD_JSON_UTILS_H

#include <math.h>
#include <string.h>
#include "jansson.h"

#ifdef __cplusplus
extern "C" {
#endif

// json比较
bool TEST_JsonIsEqualReal(const json_t *valueA, const json_t *valueB);
bool TEST_JsonIsEqualField(const json_t *valueA, const json_t *valueB);
bool TEST_JsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool TEST_JsonIsEqualArray(const json_t *valueA, const json_t *valueB);
bool TEST_JsonIsEqual(const char *json1, const char *json2);

#ifdef __cplusplus
}
#endif
#endif /* T_RD_JSON_UTILS_H */

