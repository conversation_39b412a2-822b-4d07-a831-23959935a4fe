/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_sn.h
 * Author: lushiguang
 * Create: 2023-06-15
 */
#ifndef T_RD_SN_H
#define T_RD_SN_H

#include <sys/timerfd.h>
#include "t_rd_common.h"
#include "t_rd_log.h"
#include "semaphore.h"

#ifdef __cplusplus
extern "C" {
#endif

using namespace std;
#ifndef errno_t
typedef int errno_t;
#endif

extern sem_t g_sem;

typedef enum {
    TEST_OP_INSERT = 1,
    TEST_OP_DELETE,
    TEST_OP_UPDATE,
} StmtSubOpTypeE;

typedef struct TagStmgSubCtxT {
    uint32_t index;  // 不一定要主键，连续唯一int类型值即可
    uint32_t diffListIndex;  // 构造从任一区间开始的增量值
    uint32_t updateAddVal; // 更新时传入的增量值
    StmtSubOpTypeE opType;   //  推送modify时，区分时insert，还是merge、replace，update
    bool isPushed;  // 是否已经推送
    bool isEndStatus;  // 是不是最终状态
} StmgSubCtxT;

typedef struct TagSnUserDataT {
    void *old_value;
    void *new_value;
    bool *isReplace_insert; // 0|replace已存在的数据， 1|replace不存在的数据
    int subIndex;           // 用于存储预期数据(old_value、new_value、isReplace_insert);的数组下标
    int threadId;           // 用户自定义的线程ID，用于区分线程
    int insertNum;
    int updateNum;
    int deleteNum;
    int replaceNum;
    int replaceInsertNum;
    int replaceUpdateNum;
    int mergeNum;
    int mergeInsertNum;
    int mergeUpdateNum;
    int kvSetNum;
    int scanNum;
    int scanEofNum;
    int agedNum;
    int triggerScanBeginNum;
    int triggerScanNum;
    int triggerScanEndNum;
    uint32_t callbackTimes;
    uint8_t *dataCheckIndexes;
    bool delay4StMgSn;
    char *subscriptionName;
    char *connectionName;
    bool isEnd;
    bool isFetchNull;
    bool isFetchExpNull;
    int diffNum;
    int diffExplicitNum;
    std::vector<std::string> *expectDiff;  // diffTree预期返回值
    std::vector<std::string> *expectDiffExp;  // diffTree预期返回值
    GmcStmtT *stmt;
    sem_t sem;
    StmgSubCtxT *stmgSubCtx;
} SnUserDataT;

int close_usr_epoll_thread(EpollThreadDataT *epollData);
int create_timeout_epoll_thread();
int close_timeout_epoll_thread();

void closeUsrEpollOneThread(EpollThreadDataT *epollData);
int createEpollOneThread(EpollThreadDataT *epollData = &g_epollDataOneThread);
void closeEpollOneThread(EpollThreadDataT *epollData = &g_epollDataOneThread);
void *EpollThreadFunc(void *args);
// create_epoll_thread 与 close_epoll_thread配套使用
int create_epoll_thread(EpollThreadDataT *epollData = &g_epollData);
int close_epoll_thread(EpollThreadDataT *epollData = &g_epollData);

void dml_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg, char *opMsg);
void TransSavePointCb(void *userData, int32_t status, const char *errMsg);
void create_vertex_label_callback(void *userData, int32_t status, const char *errMsg);
void create_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg);
void drop_vertex_label_callback(void *userData, int32_t status, const char *errMsg);
void drop_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg);
void get_vertex_label_callback(void *userData, int32_t status, const char *errMsg);
void get_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg);
void insert_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void insert_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void update_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void update_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void delete_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void delete_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void batch_execute_callback(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg);
void merge_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void merge_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void replace_vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void replace_vertex_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void truncate_vertex_label_callback(void *userData, int32_t status, const char *errMsg);
void truncate_vertex_label_callback_verify(void *userData, int32_t status, const char *errMsg);
void create_kv_table_callback(void *userData, int32_t status, const char *errMsg);
void create_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg);
void drop_kv_table_callback(void *userData, int32_t status, const char *errMsg);
void drop_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg);
void get_kv_table_callback(void *userData, int32_t status, const char *errMsg);
void get_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg);
void set_kv_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void set_kv_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void delete_kv_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void delete_kv_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void truncate_kv_table_callback(void *userData, int32_t status, const char *errMsg);
void truncate_kv_table_callback_verify(void *userData, int32_t status, const char *errMsg);
void delete_vertex_by_cond_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void delete_vertex_by_cond_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void update_vertex_by_cond_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void update_vertex_by_cond_callback_verify(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg);
void use_namespace_callback(void *userData, int32_t status, const char *errMsg);
void use_namespace_callback_verify(void *userData, int32_t status, const char *errMsg);
void create_namespace_callback(void *userData, int32_t status, const char *errMsg);
void create_namespace_callback_verify(void *userData, int32_t status, const char *errMsg);
void ClearNSCallbak(void *userData, int32_t status, const char *errMsg);
void ClearNSVerifyCallbak(void *userData, int32_t status, const char *errMsg);
void drop_namespace_callback(void *userData, int32_t status, const char *errMsg);
void drop_namespace_callback_verify(void *userData, int32_t status, const char *errMsg);
void trans_start_callback(void *userData, int32_t status, const char *errMsg);
#ifdef EXPERIMENTAL_GUANGQI
void trans_start_ext_callback(void *userData, GmcStartExtResT *startExtRes, int32_t status, const char *errMsg);
#endif
void trans_commit_callback(void *userData, int32_t status, const char *errMsg);
void trans_rollback_callback(void *userData, int32_t status, const char *errMsg);
void savepoint_callback(void *userData, int32_t status, const char *errMsg);
void trans_checkConflict_callback(void *userData, int32_t status, const char *errMsg);
void FetchAndDeparseDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void create_edge_label_callback(void *userData, int32_t status, const char *errMsg);
void drop_edge_label_callback(void *userData, int32_t status, const char *errMsg);
void create_tablespace_callback(void *userData, int32_t status, const char *errMsg);
void drop_tablespace_callback(void *userData, int32_t status, const char *errMsg);
int testSubConnect(GmcConnT **connOut, GmcStmtT **stmt = NULL, bool needEpoll = 0, EpollRegFunctionT epollReg = NULL,
    const char *connName = NULL, const void *chanRingLen = NULL, ConnOptionT *connOptions = NULL,
    const int32_t *packShrinkThresholdSize = NULL, int runMode = -1, const char *userName = NULL);
int testSubDisConnect(GmcConnT *conn, GmcStmtT *stmt = NULL);
/* **********************************************************************
 * 函数: testWaitSnRecv
 * 功能: 老订阅/状态合并订阅 推送次数稳定可预期的情况下, 验证推送次数的正确性。
 * 参数:
 * userData[in] -- 订阅回调用户上下文。
 * eventType[in] -- 要校验的推送事件。
 * expRecvNum[in] -- 预期该推送事件收到的推送次数。
 * timeout[in] -- 推送等待超时时间, 默认1min。
 * isAutoReset[in] -- 验证完推送次数后, 是否清空计数。
 * ********************************************************************** */
int testWaitSnRecv(void *userData, GmcSubEventTypeE eventType, int32_t expRecvNum, int timeout = RECV_TIMEOUT,
    bool isAutoReset = true);
/* **********************************************************************
 * 函数: testWaitStMgSnRecv
 * 功能: 状态合并订阅 推送次数不稳定的情况下, 验证推送的次数符合某个范围。
 * 参数:
 * userData[in] -- 订阅回调用户上下文。
 * eventType[in] -- 要校验的推送事件。
 * expMinRecvNum[in] -- 预期该推送事件收到的最小的推送次数。
 * expMaxRecvNum[in] -- 预期该推送事件收到的最大的推送次数。
 * timeout[in] -- 推送等待超时时间, 默认1min。
 * isAutoReset[in] -- 验证完推送次数后, 是否清空计数。
 * ********************************************************************** */
int testWaitStMgSnRecv(void *userData, GmcSubEventTypeE eventType, int32_t expMinRecvNum, int32_t expMaxRecvNum = -1,
    int timeout = RECV_TIMEOUT, bool isAutoReset = true);
int testSnMallocUserData(SnUserDataT **userData, uint32_t sizeMalloc, uint32_t checkNum = 0);
void testSnFreeUserData(SnUserDataT *userData);
int testAsyncMallocUserData(AsyncUserDataT **userData);
void testAsyncFreeUserData(AsyncUserDataT *userData);
void TEST_AsyncDataShow(AsyncUserDataT *asyncData);
int TEST_TransStartAsync(GmcConnT *conn, bool isYang = false, const GmcTxConfigT *trxConfig = NULL,
    int32_t timeout = 100000);
int TEST_TransCommitAsync(GmcConnT *conn, int32_t timeout = 100000);
int TestYangGmcConnectWithEpollOneThread(GmcConnT **connOut, GmcStmtT **stmt = NULL, int syncMode = 0,
    YangConnOptionT *connOptionsInput = NULL, EpollThreadDataT *epollData = &g_epollDataOneThread, int runMode = -1);

#ifdef __cplusplus
}
#endif
#endif /* T_RD_SN_H */

