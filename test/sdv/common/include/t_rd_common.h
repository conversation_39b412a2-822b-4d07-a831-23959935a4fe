/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_common.h
 * Author: lushiguang
 * Create: 2023-06-15
 */
#ifndef T_RD_COMMON_H
#define T_RD_COMMON_H

#include <sys/epoll.h>
#include <dlfcn.h>
#include <semaphore.h>
#include <math.h>
#include <libgen.h>
#include <unistd.h>
#include <string>
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <atomic>
#include <vector>
#include <map>
// for x86: gettimeofday
#include <sys/time.h>
#ifdef ENV_RTOSV2X
#include <sys/wait.h>
#endif
#include "jansson.h"
#include "t_rd_adapt.h"
#include "t_rd_log.h"
#include "t_rd_assert.h"
#include "t_rd_config.h"
#include "gmc.h"
#include "gmc_internal.h"
#if !defined FEATURE_STREAM || defined TS_MULTI_INST
#include "gmc_yang.h"
#endif
#ifdef FEATURE_PERSISTENCE
#include "gmc_persist.h"
#include "t_rd_nerg.h"
#endif

#if FEATURE_PERSISTENCE && defined ENV_SUSE
#include "t_rd_persis.h"
#endif

#if !defined PRODUCT_USG_TS && !defined FEATURE_STREAM
#include "testutil.h"
#include "gmc_test.h"
#endif

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
#include "gms.h"
#include "t_rd_nerg.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

using namespace std;

// 暂时保留后续统一替换为T_FAILED
#define FAILED (-1)
#define RUN_STAT_FREE 0
#define RUN_STAT_INIT 1
#define RUN_STAT_SUCC 2
#define RUN_STAT_FAIL 3
#define GMC_MODEL_TYPE_DEF_GQL 6

// STREAM序列化去除了失效的系统预留字段，详细原因见问题单: DTS2024091022777
#ifdef FEATURE_STREAM
#define TEST_RESERVED_SIZE 0
#define RESERVED_FIELD_COUNT 0
#else
#define TEST_RESERVED_SIZE 1
#define RESERVED_FIELD_COUNT 1
#endif

extern int g_runStat;
extern pthread_mutex_t g_connConcurrent;
extern pthread_mutex_t g_connLock;

extern int g_connRequest;
extern int g_connOnline;
extern bool g_needRestartWhenFailed;
extern bool g_needCheckWhenSucc;
extern char g_hpeLibDir[LOG_PATH_MAX_LEN];
extern char g_sysGMDBCfgPath[LOG_PATH_MAX_LEN];

/* for sysconfig.ini */
#if defined RUN_INDEPENDENT
extern char g_connServer[64];
extern char g_connServerSlave[64];
extern char g_connServerTsdb[64];
extern char g_sysGMDBCfg[256];
extern char g_sysTsGMDBCfg[256];
extern char g_toolPath[256];
extern char g_testNameSpace[32];
#elif defined ENV_RTOSV2X
extern char g_connServer[64];
extern char g_connServerSlave[64];
extern char g_connServerTsdb[64];
extern char g_sysGMDBCfg[256];
extern char g_sysTsGMDBCfg[256];
extern char g_toolPath[256];
extern char g_testNameSpace[32];
#else  // defined ENV_RTOSV2
extern char g_connServer[64];
extern char g_connServerSlave[64];
extern char g_connServerTsdb[64];
extern char g_sysGMDBCfg[256];
extern char g_sysTsGMDBCfg[256];
extern char g_toolPath[256];
extern char g_testNameSpace[32];
#endif
extern char g_runEnv[3][12];
extern char g_userName[32];
extern char g_passwd[64];

typedef union {
    void *ptr;
    int32_t fd;
    uint32_t u32;
    uint64_t u64;
} VpollData;
typedef struct tagVpollEvent {
    uint32_t events;
    VpollData data;
} __attribute__((__packed__)) VpollEvent;

extern int32_t (*g_hpeVpollCreate)(int32_t size);
extern int32_t (*g_hpeVpollCtl)(int32_t vpId, int32_t op, int32_t fd, VpollEvent *event);
extern int32_t (*g_hpeVpollWait)(int32_t vpId, VpollEvent *events, int32_t maxEvents, int32_t timeout);
extern int32_t (*g_hpeVpollDestroy)(int32_t vpId);

#if defined RUN_DATACOM_HPE
#define TEST_EPOLL_CREATE g_hpeVpollCreate
#define TEST_EPOLL_CTL g_hpeVpollCtl
#define TEST_EPOLL_WAIT g_hpeVpollWait
#define TEST_EPOLL_CLOSE g_hpeVpollDestroy
#define TEST_EPOLL_EVENT VpollEvent
#else
#define TEST_EPOLL_CREATE epoll_create
#define TEST_EPOLL_CTL epoll_ctl
#define TEST_EPOLL_WAIT epoll_wait
#define TEST_EPOLL_CLOSE close
#define TEST_EPOLL_EVENT epoll_event
#endif
#define MAX_EPOLL_EVENT_COUNT 2048
#define EPOLL_TIME_OUT_MS 1000
#define RECV_TIMEOUT 120000

extern bool g_isReadConfig;
extern int g_flowCnt;
extern bool g_tcpType;

typedef struct {
    bool isInited;
    bool epollThreadExit;
    int32_t userEpollFd;
    pthread_t epollThreadId;
    TEST_EPOLL_EVENT *events;
    sem_t sem;
    bool notCreateGlobalTimerFd;
    bool createBgThread;
    bool createInCurThread;
} EpollThreadDataT;

#define MAX_EP_ASYNC_NUM 36
extern EpollThreadDataT g_epAsync[MAX_EP_ASYNC_NUM];
extern bool g_isOneThreadEpoll;
extern EpollThreadDataT g_epollData, g_epollDataOneThread, g_timeoutEpollData;
extern pthread_mutex_t g_timeoutEpollLock;
extern GmcEpollRegWithUserDataT g_epoll_reg_info;
extern GmcEpollRegWithUserDataT g_epollRegInfoOneThread;

int epoll_ctrl(
    int32_t fd, GmcEpollCtlTypeE type, uint32_t events = EPOLLIN, void *userData = NULL, const GmcConnT *conn = NULL);
int epollRegWithNoPara(int32_t fd, GmcEpollCtlTypeE type);
int epollRegWithUserdata(int32_t fd, GmcEpollCtlTypeE type, uint32_t event, void *userData);
int epollRegWithConn(int32_t fd, GmcEpollCtlTypeE type, uint32_t event, const GmcConnT *conn, void *userData);
int epollRegTimeout(int32_t fd, GmcEpollCtlTypeE type);
int epollRegOneThreadHeartbeat(int32_t fd, GmcEpollCtlTypeE type);
void TestEpollDoEvent(int32_t fd, uint32_t events);

typedef struct ConnOption {
    const char *serverLocator;
    const char *passwd;
    uint32_t requestTimeout;
    uint32_t msgReadTimeout;
    uint32_t msgWriteTimeout;
    uint32_t requestConnWeight;
    uint32_t subCallBackTimeout;  // true: 单条回调阈值 异步和订阅有效；  false: 平均回调阈值，仅订阅有效；
    uint32_t subCallBackAvgTimeout;
    uint32_t subMsgRingSize = 16 * 1024;  // GmcConnOptionsSetSubMsgRingSize
    GmcSubFailedCallbackT subFailedCb;
    void *subFailedData;
    bool useReservedConn = false;
} ConnOptionT;

typedef struct TagAsyncUserDataT {
    int threadId;                           // 用户自定义的线程ID，用于区分线程
    int recvNum;                            // 此次共接收的消息数
    int historyRecvNum;                     // 历史接收的消息数
    int status;                             // 实际返回值
    int affectRows;                         // 实际返回值
    int totalNum;                           // 实际返回值
    int succNum;                            // 实际返回值
    int expStatus;                          // 预期返回值
    int expAffectRows;                      // 预期返回值
    int expTotalNum;                        // 预期返回值
    int expSuccNum;                         // 预期返回值
    bool isValidErrorPathInfo;              // 是否校验 error path 信息
    GmcErrorPathCodeE expectedErrorCode;    // 预期 error code 信息
    const char *expectedErrMsg;             // 预期 error msg 信息
    const char *expectedErrPath;            // 预期 error path 信息
    const char *lastError;                  // lastError的值
    uint32_t lastExpectIdx;                 // 分批查询diff上次查询期望结果的最后索引
    GmcStmtT *stmt;                         // diff回调调用
    std::vector<std::string> *expectDiff;   // diffTree预期返回值
    uint32_t filterMode;                    // 过滤模式,使用枚举GmcSubtreeFilterModeE设置值
    std::vector<std::string> *expectReply;  // subtree obj模式过滤预期返回的查询结果, 校验用的字符串
    const char *subtreeJson;  // subtree json模式过滤预期返回的查询结果, 校验用的字符串
    uint32_t nestDeep;        // 记录异步回调嵌套的深度
    GmcConnT *conn;           // 异步嵌套使用
    bool validateRes;         // mandatory返回值
    bool needCheckDiff;
    bool needCheckSubtree;
    const char *rootName;
    const char *namespaceName;
    uint64_t startTime;
    uint32_t diffBufLen;            // 导出diffBuf调用
    uint8_t *diffBuf;               // 导出diffBuf调用
    GmcLeafrefPathT leafrefPathes;  // 获取leafrefPath调用
    GmcFetchDiffModeE diffMode;     // 获取DiffFetchOption
    uint32_t cloneId;               // 被克隆事务开启后返回的cloneId
} AsyncUserDataT;

typedef enum TestEpollRegTypeE {
    TEST_EPOLLREG_INVALID = 0,
    TEST_EPOLLREG = 1,
    TEST_EPOLLREG_WITH_USD = 2,
    TEST_EPOLLREG_WITH_CONN = 3,
} TestEpollRegTypeE;

void ConnCtionFlowCtrlNotice(void *args, GmcDbFlowCtrlLevelE flowCtrllevel);

// 20230328 暂时命名为YangConnOption, 后续统一整改完后, 再改回ConnOption
typedef struct YangConnOption {
    const char *serverLocator = NULL;  // GmcConnOptionsSetServerLocator
    const char *passwd = NULL;
    uint32_t requestTimeout = 0;               // GmcConnOptionsSetRequestTimeout
    uint32_t msgReadTimeout = 60000;           // GmcConnOptionsSetMsgReadTimeout
    uint32_t msgWriteTimeout = 60000;          // GmcConnOptionsSetMsgWriteTimeout
    uint32_t requestConnWeight = 1;            // GmcConnOptionsSetRequestWeight
    GmcSubFailedCallbackT subFailedCb = NULL;  // GmcConnOptionsSetSubFailedCallback
    void *subFailedData = NULL;
    bool useReservedConn = false;  // GmcConnOptionsSetReservedFlag

    const char *connName = NULL;                // GmcConnOptionsSetConnName
    uint32_t msgQueueSize = 64;                 // GmcConnOptionsSetMsgQueueSize
    uint32_t packShrinkThreshold = UINT32_MAX;  // GmcConnOptionsSetPackShrinkThreshold
    bool needEpoll = true;
    bool isOneThreadEpoll = false;
    TestEpollRegTypeE epollRegType = TEST_EPOLLREG_WITH_CONN;
    union {
        GmcEpollRegT epollRegFunc;                                     // GmcConnOptionsSetEpollRegFunc
        GmcEpollRegWithUserDataT epollRegWithUsDFunc;                  // GmcConnOptionsSetEpollRegFuncWithUserData
        GmcEpollRegWithConnT epollRegWithConnFunc = epollRegWithConn;  // GmcConnOptionsSetEpollRegFuncWithConn
    };
    int32_t *epollFd = &g_epollData.userEpollFd;
    GmcConnFlowCtrlLevelNoticeT flowCtrlNoticeCb = ConnCtionFlowCtrlNotice;  // GmcConnOptionsSetFlowCtrlCallback
    void *flowCtrlNoticeData = NULL;                                         // user data of flow control callback
    uint16_t maxStmtCount = 10240;                                           // GmcConnOptionsSetMaxStmtCount
    bool openDirectRead = true;                                              // GmcConnOptionsSetCSRead
    uint32_t timeInterval = 5;                                               // GmcConnOptionsSetTimeInterval
    int64_t timeThreshold = -1;                                              // GmcConnOptionsSetSlowOpLogThreshold
    uint64_t bigObjectStand = 512;                                           // GmcConnOptionsSetBigObjThreshold
    uint32_t connMemCtxLimit = 1024;                                         // GmcConnOptionsSetConnMemCtxLimit
    uint32_t singleCallbackTimeoutMs = 200;  // GmcConnOptionsSetCallbackTimeout 单条回调函数超时阈值,订阅、异步共用
    uint32_t avgcallbackTimeoutMs = 1;  // GmcConnOptionsSetCallbackTimeout 平均回调函数超时阈值，仅订阅回调使用
    bool isLobConn = false;             // GmcConnOptionsSetBigMessage
    uint32_t timeoutMs = 30000;  // GmcConnOptionsSetAsyncTimeoutThreshold 客户端异步回调函数超时的时间阈值
    uint32_t logThreshold = 0;            // GmcConnOptionsSetTrxMonitorThreshold
    uint32_t rollBackThreshold = 0;       // GmcConnOptionsSetTrxMonitorThreshold
    uint32_t subMsgRingSize = 16 * 1024;  // GmcConnOptionsSetSubMsgRingSize
    bool isLocalAsyncTimeout = false;     // GmcConnOptionsSetLocalAsyncTimeout
    uint16_t srvMemCtxLimit = 0;          // GmcConnOptionsSetServerMemCtxLimit
    bool isCsMode = false;                // GmcConnOptionsSetCSMode
    const char *userName = NULL;          // GmcConnOptionsSetUserName
} YangConnOptionT;

typedef int32_t (*EpollRegFunctionT)(int32_t fd, GmcEpollCtlTypeE type, uint32_t event, void *userData);

int testGmcGetLastError(const char *expect = NULL, bool isFullMatch = true);
void getSysConfig();
/* **********************************************************************
 * 函数: testWaitAsyncRecv(void *userData, int expRecvNum, int timeout, bool isAutoReset)
 * 功能: epoll单独创建线程的场景使用。等待异步消息接收, 直至接收的消息数量和预期一致。
 * 参数:
 * userData[in] -- 和异步接口传参使用同一个userData，异步回调中会使用userData->recvNum进行计数。
 * expRecvNum[in] -- 预期接收到的异步消息数量。
 * timeout[in]：卸载模式默认值为-1, 会进行死等, 直到接收的消息数达到预期; 非卸载模式默认等待90s, 超时则失败退出。
 * isAutoReset[in] -- 默认值为true，每调用一次testWaitAsyncRecv, 都会把userData->recvNum清零。
 * ********************************************************************** */
int testWaitAsyncRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true);

/* **********************************************************************
 * 函数: testWaitAsyncRecvOneThread(void *userData, int expRecvNum, bool isAutoReset, int timeout, int32_t epollFd)
 * 功能: epoll不单独创建线程, 直接在用例中创建epoll对象的场景使用。接收异步消息, 直至接收的消息数量和预期一致。
 * 参数:
 * userData[in] -- 和异步接口传参使用同一个userData，异步回调中会使用userData->recvNum进行计数。
 * expRecvNum[in] -- 预期接收到的异步消息数量。
 * timeout[in]：卸载模式默认值为-1, 会进行死等, 直到接收的消息数达到预期; 非卸载模式默认等待90s, 超时则失败退出。
 * isAutoReset[in] -- 默认值为true，每调用一次testWaitAsyncRecvOneThread, 都会把userData->recvNum清零。
 * epollFd[in] -- 使用的epoll fd
 * ********************************************************************** */
int testWaitAsyncRecvOneThread(void *userData, int expRecvNum = 1, bool isAutoReset = true, int timeout = -1,
    int32_t epollFd = g_epollDataOneThread.userEpollFd);

// syncMode:   0|sync  1|async 2|sub
int testGmcConnect(GmcConnT **connOut, GmcStmtT **stmt = NULL, int syncMode = 0, bool needEpoll = 1,
    EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL, const void *chanRingLen = NULL,
    ConnOptionT *connOptions = NULL, const int32_t *packShrinkThresholdSize = NULL, int runMode = -1, int csMode = 0,
    int *epollFd = &g_epollData.userEpollFd, bool isBigObj = false, const char *userName = NULL);

int testPrepareNameSpace(void);
void useNameSpaceCallback(void *userData, int32_t status, const char *errMsg);
int testSetNameSpace(GmcConnT *conn, int syncMode = 0, int *epollFd = &g_epollData.userEpollFd, char *nameSpace = NULL,
    YangConnOptionT *connOptions = NULL);
int OpenEpollFunFromHPE();
int GrantGmsysviewPrivs();
int testEnvInit(int runMode = -1, bool needStart = true);
int testEnvClean();
int testMallocConnOptions(ConnOptionT **connOptions, const char *serverLocator = NULL, const char *userName = NULL,
    const char *passwd = NULL, uint32_t *requestTimeout = NULL, uint32_t *msgReadTimeout = NULL,
    uint32_t *msgWriteTimeout = NULL, GmcSubFailedCallbackT subFailedCb = NULL, void *subFailedData = NULL,
    bool *useReservedConn = NULL, uint32_t *requestConnWeight = NULL, uint32_t *subCallBackTimeout = NULL,
    uint32_t *subCallBackAvgTimeout = NULL, uint32_t *subMsgRingSize = NULL);
void testFreeConnOptions(ConnOptionT *connOptions);
int testSetAsyncQueueSize(GmcConnOptionsT *connOptions, uint32_t *value);
int TestYangGmcConnect(GmcConnT **connOut, GmcStmtT **stmt = NULL, int syncMode = 0,
    YangConnOptionT *connOptions = NULL, int runMode = -1);
int testGmcDisconnect(GmcConnT *conn, GmcStmtT *stmt = NULL);
int32_t CompareVertexPropertyValue(GmcDataTypeE ttype, void *insertValue, void *queryValue, unsigned int size = 0);
int testGmcGetStmtAttr(GmcStmtT *stmt, GmcStmtAttrTypeE attr, int32_t expect);
int testGetConnNum(uint32_t *connNum);
int testGetTableNum(uint32_t *tableNum);
int TestGetYangTableNum(uint32_t *tableNum);
int testGetSubsNum(uint32_t *subsNum);
int TestGetResultCommand(const char *cmd, int *value, char *str = NULL, int strLen = 0);
int TestGetV5CfgValueInt(const char *configName, int *value);
int TestGetV5CfgValueStr(const char *configName, char *value, int len);
int TestGetTsCfgValueInt(const char *configName, int *value);
int TestGetTsCfgValueStr(const char *configName, char *value, int len);
int TestGetConfigValueInt(const char *configName, int *value);
int TestGetConfigValueStr(const char *configName, char *value, int len);
int GetViewValueByField(const char *view, const char *field);
int TestVertexLabelIsExit(const char *labelName, bool *isExit);
int testScanSysview(const char *sysname, const char *name1 = NULL, const char *value1 = NULL, const char *filter = NULL,
    bool checkvalue = true, bool checkfilte = true, const char *typs = (char *)"string");

int TestRDGetSysviewDataTypeString(
    const char *sysview, const char *propname, char dest[][100], int *count, const char *filter = NULL);
int TestRDGetSysviewDataTypeInt(
    const char *sysview, const char *propname, int *dest, int *count, const char *filter = NULL);
int TestRDGetCfg(char *cfgName, void *value);
int TestRDUpdateCfg(char *cfgName, GmcDataTypeE type, void *value);

#if !defined PRODUCT_USG_TS && !defined FEATURE_STREAM

/* *********************************************************************************************
 * Description  : 扫描某些条件下的视图
 * Input        : stmt:客户端stmt句柄 viewName:系统视图名 filter:过滤条件
 * Notes        : 1.filter过滤条件相关约束说明，请参见手册Filter过滤条件规格约束
                  2.条件支持括号、and、or，使用and或者or连接的条件总数不能超过8个
                  3.GmcSetFilter最新约束变更，过滤string类型需加上单引号，否则报错12000并滑屏
 * History      : 2023-3-13
 * ***************************************************************************************** */
int32_t TestRDScanSysview(GmcStmtT *stmt, const char *viewName, const char *filter);

/* *********************************************************************************************
 * Description  : 读取视图数据
 * Input        : stmt:客户端stmt句柄 num:待查询属性的数量 fieldName:属性名称
 * Output       : value:要获取的视图属性的值
 * Notes        : 1.先调用testRDScanSysview接口扫描和过滤系统视图
                  2.该函数为可变函数，调用方法如下：TestRDGetSysviewData("V$DB_SERVEE",
                  "instanceID=0", 3, "fieldName1", &v1, "fieldName2", &v2, "fieldName3", &v3);
                  3.视图的属性类型需要调用函数前自定义，参考手册的数据类型
                  4.支持一次性返回多个属性值，支持自动转换到对应的数据类型，查询属性个数通过num控制
                  5.当有多条记录时，调用一次，返回第一条记录；再调用一次，返回第二条记录；当没有记录可
                  调用时，返回错误码
                  6.函数具体用法和测试用例参考testcases/TestRD sysview_test.cpp文件

 * History      : 2023-3-13
 * ***************************************************************************************** */
int32_t TestRDGetSysviewData(GmcStmtT *stmt, int num, ...);

/* *********************************************************************************************
 * Description  : 比对视图数据
 * Input        : proptype:视图的属性类型 checkValue:待检查的值 queryValue:查询视图获取的值
 * Return       : statusRet:是否一致
 * Notes        : 1.在调用TestRDGetSysviewData之后使用，用于比对结果
                  2.proptype属性的数据类型，请参见GmcDataTypeE结构体
                  3.checkValue是待检查的值，需提前设置

 * History      : 2023-3-20
 * ***************************************************************************************** */
int32_t TestRDCompareSysviewValue(GmcDataTypeE proptype, void *checkValue, void *queryValue);

/* *********************************************************************************************
 * Description  : 查询满足条件的视图数据记录数
 * Input        : stmt:客户端stmt句柄 succNum:查询到的全部记录数
 * Notes        : 1.先调用testRDScanSysview接口扫描和过滤系统视图
                  2.不可与TestRDGetSysviewData和TestRDSumSysviewFieldName混用

 * History      : 2023-3-23
 * ***************************************************************************************** */
int TestRDGetSysviewRecordNum(GmcStmtT *stmt, int *succNum);

/* *********************************************************************************************
 * Description  : 累加满足条件的视图数据字段值
 * Input        : stmt:客户端stmt句柄 succNum:查询到的全部记录数
 * Notes        : 1.先调用testRDScanSysview接口扫描和过滤系统视图
                  2.不可与TestRDGetSysviewData和TestRDGetSysviewRecordNum混用
 * History      : 2023-3-23
 * ***************************************************************************************** */
int32_t TestRDSumSysviewFieldValue(GmcStmtT *stmt, const char *fieldName, void *fieldValue, int *sumFieldValue);

/* *********************************************************************************************
 * Description  : 检查表（vertex，kv，edge）总数
 * Input        : stmt:客户端stmt句柄 type:表类型vertex/kv/edge count:表总数
 * History      : 2023-3-23
 * ***************************************************************************************** */
int32_t TestRDCheckLabelNum(GmcStmtT *stmt, const char *type, int *count);

/* *********************************************************************************************
 * Description  : 检查vertex表总数
 * Input        : stmt:客户端stmt句柄 type:表类型vertex count:表总数
 * History      : 2023-4-19
 * ***************************************************************************************** */
int32_t TestRDCheckVertexLabelNum(GmcStmtT *stmt, int *count);

/* *********************************************************************************************
 * Description  : 检查KV表总数
 * Input        : stmt:客户端stmt句柄 type:表类型KV count:表总数
 * History      : 2023-4-19
 * ***************************************************************************************** */
int32_t TestRDCheckKVLabelNum(GmcStmtT *stmt, int *count);

/* *********************************************************************************************
 * Description  : 检查edge表总数
 * Input        : stmt:客户端stmt句柄 type:表类型edge count:表总数
 * History      : 2023-4-19
 * ***************************************************************************************** */
int32_t TestRDCheckEdgeLabelNum(GmcStmtT *stmt, int *count);

typedef enum {
    T_RECORD,
    T_ARRAY,
    TEST_FIELD,
} TNodeTypeE;

typedef struct VertexNode {
    char name[50];
    TNodeTypeE nodeType;
    GmcDataTypeE dataType;
    int arrayIndex;
    struct VertexNode *next;
} VertexNodeT;

typedef struct TFieldValue {
    VertexNodeT *node;
    char format[50];
    char *value;
} TFieldValueT;

#define MAX_FORMAT_FIELDS 50

int GetSysDataType(char *dataType);
int GetFieldNodeDataType(VertexNodeT *node);
int ParseFieldValue(TFieldValueT *field, int maxFieldLen, const char *formatStr, int *parseCount);
void FreeVertexNode(VertexNodeT *node);
void FreeFieldValue(TFieldValueT *field, int fieldCount);
// 根据类型，转换char并setProperty
int SetIndexKeyValueBaseType(GmcStmtT *stmt, uint32_t index, GmcDataTypeE type, char *value);
void OprVertexCallback(void *userData, uint32_t affectedRows, Status status, const char *errMsg);
// 递归tree模型层级结构字段，并预期值判断
int CheckTreeRecord(GmcStmtT *stmt, GmcNodeT *root, VertexNodeT *node, char *format, int formatIndex);
// 查询vertex字段，并预期值判断
int CheckVertexRecord(GmcStmtT *stmt, TFieldValueT *expectField, int expectFieldCount, int formatIndex);
int TransformValue(GmcDataTypeE dateType, char *value, int *size);
// 递归tree模型层级结构字段，setProperty
int SetTreeProperty(GmcStmtT *stmt, GmcNodeT *root, VertexNodeT *node, char *setValue);
int SetPropertyBaseFormat(GmcStmtT *stmt, TFieldValueT *field, int fieldCount, int formatIndex);

/* *********************************************************************************************
 * 写数据(同步接口)
 * setJson同GmcSetVertexByJson中json参数，区别在于可以是常量，也可以是表达式；如果是常量，那么就是重复执行
 * 常量如：{ "F0":0, "F1":1, "s1":"somestring"}
 * 表达式如：{ "F0":%r{0,100}, "F1":%i{0,100}, "s1":"somestring%f{10}a"}
 * ***************************************************************************************** */
int TestInsVertexSync(
    GmcStmtT *stmt, const char *tableName, char *setJson, int beginVal, int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
 * 写数据(异步接口)
 * setJson同GmcSetVertexByJson中json参数，区别在于可以是常量，也可以是表达式；如果是常量，那么就是重复执行
 * 常量如：{ "F0":0, "F1":1, "s1":"somestring"}
 * 表达式如：{ "F0":%r{0,100}, "F1":%i{0,100}, "s1":"somestring%f{10}a"}
 * ***************************************************************************************** */
int TestInsVertexAsync(
    GmcStmtT *stmt, const char *tableName, char *setJson, int beginVal, int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
* 索引删除/条件删除(同步接口)
* indexName为NULL时，表示按cond条件删除，否则为主键删除或索引删除
* 条件删除场景：
*   cond 同条件接口参数，可以是常量，也可以是表达式；如果是常量，那么就是重复执行
*   如："resource_label.F0 = 11"或"resource_label.F0 = %i{0,100}"
*   cond NULL时表示全表删除
*
* 索引或主键删除场景：
*   indexName为主键或索引名，cond为索引字段信息，如：
*   { "name": "primary_key", "index": { "type": "primary" },
      "node": "ip4forward",
      "fields": [ "f0", "f1" ],
      "constraints": { "unique": true }
    }
  indexName = "ip4forward"， cond = "f0(uint32)=0;f1(uint32)=1" 表示主键删除，重复删除f0=0,f1=1的数据
  indexName = "ip4forward"， cond = "f0(uint32)=%i{0,10};f1(uint32)=1" 表示主键删除，按表达式删除数据
  注：cond中f0,f1顺序需跟索引中fields中顺序一致
* ***************************************************************************************** */
int TestdelVertexSync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond, int beginVal,
    int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
* 索引删除/条件删除(异步接口)
* indexName为NULL时，表示按cond条件删除，否则为主键删除或索引删除
* 条件删除场景：
*   cond 同条件接口参数，可以是常量，也可以是表达式；如果是常量，那么就是重复执行
*   如："resource_label.F0 = 11"或"resource_label.F0 = %i{0,100}"
*   cond NULL时表示全表删除
*
* 索引或主键删除场景：
*   indexName为主键或索引名，cond为索引字段信息，如：
*   { "name": "primary_key", "index": { "type": "primary" },
      "node": "ip4forward",
      "fields": [ "f0", "f1" ],
      "constraints": { "unique": true }
    }
  indexName = "ip4forward"， cond = "f0(uint32)=0;f1(uint32)=1" 表示主键删除，重复删除f0=0,f1=1的数据
  indexName = "ip4forward"， cond = "f0(uint32)=%i{0,10};f1(uint32)=1" 表示主键删除，按表达式删除数据
  注：cond中f0,f1顺序需跟索引中fields中顺序一致
* ***************************************************************************************** */
int TestdelVertexAsync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond, int beginVal,
    int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
* 索引扫描/条件扫描(同步接口)
* indexName为NULL时，表示条件扫描，否则为索引扫描
* 条件扫描场景：
*   cond 同条件接口参数，可以是常量，也可以是表达式；如果是常量，那么就是重复执行
*   如："resource_label.F0 = 11"或"resource_label.F0 = %i{0,100}"
*   cond NULL时表示全表扫描
* 索引扫描场景：
*   indexName为索引名，cond为索引字段信息，如：
*   { "name": "primary_key", "index": { "type": "primary" },
      "node": "ip4forward",
      "fields": [ "f0", "f1" ],
      "constraints": { "unique": true }
    }
  indexName = "ip4forward"， cond = "f0(uint32)=0;f1(uint32)=1" 表示按索引值（固定）扫描查询
  indexName = "ip4forward"， cond = "f0(uint32)=%i{0,10};f1(uint32)=1" 表示按索引值（表达式生成）扫描查询
  注：cond中f0,f1顺序需跟索引中fields中顺序一致
* ***************************************************************************************** */
int TestSelVertexCount(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond, int expectCount,
    int beginVal, int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
* 查询的数据，判断满足预期(同步接口)
* indexName为NULL时，按条件查询，否则为索引查询
* 条件查询场景：
*   cond 同条件接口参数，可以是常量，也可以是表达式；如果是常量，那么就是重复执行
*   如："resource_label.F0 = 11"或"resource_label.F0 = %i{0,100}"
*   cond为NULL，全表查询
* 索引查询场景：
*   indexName为索引名，cond为索引字段信息，如：
*   { "name": "primary_key", "index": { "type": "primary" },
      "node": "ip4forward",
      "fields": [ "f0", "f1" ],
      "constraints": { "unique": true }
    }
  indexName = "ip4forward"， cond = "f0(uint32)=0;f1(uint32)=1" 表示按索引值（固定）查询，
  indexName = "ip4forward"， cond = "f0(uint32)=%i{0,10};f1(uint32)=1" 表示按索引值（表达式生成）查询
  注：cond中f0,f1顺序需跟索引中fields中顺序一致
* expect 用于跟查询出来的数据比较，可以为常量或表达式
*   root下字段表示 f0(int16)=1
*   record(c2)下字段表示 c2.f0(int16)=1
*   list(c2)下字段表示 c2[0].f0(int16)=1
* ***************************************************************************************** */
int TestSelVertexRecord(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond,
    const char *expect, int beginVal, int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
 * 索引更新/条件更新(同步接口)
 * indexName为NULL时，表示按cond条件更新，否则为主键更新或索引更新
 * updateVal可以是常量，也可以是表达式；如果是常量，那么就是重复执行
 * 常量如：""f0(int16)=1,f1(int32)=2""
 * 表达式如：""f0(int16)=%r{0,100},f1(int32)=%i{0,100}""
 * record(c2)下字段表示 c2.f0(int16)=1
 * list(c2)下字段表示 c2[0].f0(int16)=1
 * ***************************************************************************************** */
int TestUpdVertexSync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond,
    const char *updateVal, int beginVal, int endVal, uint32_t versionId = 0);

/* *********************************************************************************************
 * 索引更新/条件更新(异步接口)
 * indexName为NULL时，表示按cond条件更新，否则为主键更新或索引更新
 * updateVal可以是常量，也可以是表达式；如果是常量，那么就是重复执行
 * 常量如：""f0(int16)=1,f1(int32)=2""
 * 表达式如：""f0(int16)=%r{0,100},f1(int32)=%i{0,100}""
 * record(c2)下字段表示 c2.f0(int16)=1
 * list(c2)下字段表示 c2[0].f0(int16)=1
 * ***************************************************************************************** */
int TestUpdVertexAsync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond,
    const char *updateVal, int beginVal, int endVal, uint32_t versionId = 0);

#endif  // PRODUCT_USG_TS FEATURE_STREAM

int ChangeGmserverCfg(char *item, char *value);
int ChangeTsGmserverCfg(char *item, char *value);

/******************************************* 结构化 start *******************************************/
#define INIT_STACK_SIZE 20
#define STACK_SMALL_CAPACITY_GROW_MULTIPLE 10
#define STACK_BIG_CAPACITY_GROW_STEP 10000
#define STACK_GROW_METHOD_BOUNDARY 20000
#define INIT_DYN_MEM_SIZE 10
#define DYN_MEM_GROW_MULTIPLE 2
#define STRUCT_VAR_SIZE (sizeof(uint16_t) + sizeof(uint8_t *))
#define MAX_KEY_FIELD_NUM 8
#define VERTEX_TYPE_SIZE 1
#define SYS_VERSION_SIZE 1
#define STATUS_MERGE_NODE_ADDR_SIZE 8
#define STATUS_MERGE_DELETE_MARK_SIZE 1
#define STATUS_MERGE_SYS_SIZE (SYS_VERSION_SIZE + STATUS_MERGE_NODE_ADDR_SIZE + STATUS_MERGE_DELETE_MARK_SIZE)
#define IS_CREATE_SIZE 1
#define CALC_ALIGN_SIZE(size, alignSize) (((size) + ((alignSize)-1)) & (~((alignSize)-1)))

#define MAX_VARIABLE_TYPE_LENGTH 65536u
#define UINT8_SIZE 8
#define UINT16_SIZE 16
#define UINT32_SIZE 32
#define UINT64_SIZE 64
#define BYTE_LENGTH 8u

#define VECTOR_ARRAY_INIT_SIZE 16u
#define VECTOR_EXTEND_SIZE 128u
#define VECTOR_MAX_SIZE 1024u

typedef struct TagTestArrayT {
    uint32_t memMax;
    uint32_t memSize;
    void **memAddr;
} TestArrayT;

typedef struct TagTestParseSchemaCtxT {
    TestArrayT array;
    pthread_mutex_t threadLock;
    bool hasFoundCompatibleV3Config;
    int32_t compatibleV3Value;
} TestParseSchemaCtxT;
extern TestParseSchemaCtxT g_parseSchemaCtx;

typedef struct TestLabelInfo {
    char *labelName;
    uint32_t schemaVersion = 0;
    char *nsName = g_testNameSpace;
} TestLabelInfoT;

typedef struct {
    const char *strType;
    GmcDataTypeE dataType;
} TestDataTypePairT;

typedef struct {
    GmcDataTypeE typeId;
    uint32_t typeSize;
} TestBasicTypeSizeT;

typedef enum TestVertexType {
    FIXED_VERTEX,  // 平的vertex,字段均为定长，不包含变长
    FLAT_VERTEX,   // 平的vertex,包含有变长
    TREE_VERTEX,   // tree vertex，有node节点（只要是带子树节点的都是树模型）
} TestVertexTypeE;

typedef struct TestSchema TestSchemaT;

typedef struct TestNodeSchema {
    GmcTreeNodeTypeE nodeType;  // Node的类型
    char *name;                 // Node名称
    uint32_t nameLen;           // Node名称的长度
    uint32_t id;                // 表示node在该层所有属性中的下标，从0开始计数
    uint32_t arraySize;         // 静态数组长度，当nodeType为array时有效
    uint32_t vectorInitSize;    // 动态数组初始容量，当nodeType为vector时有效
    uint32_t vectorMaxSize;     // 动态数组支持的最大容量，当nodeType为vector时有效
    uint32_t vectorExtendSize;  // 动态数组每次扩充的容量，当nodeType为vector时有效
    TestSchemaT *schema;        // Node节点对应的schema信息
} TestNodeSchemaT;

typedef struct TestPropertySchema {
    uint32_t propeId;       // 标志当前propertySchema在vertexLabel中的下标
    GmcDataTypeE dataType;  // 数据类型
    uint32_t size;          // 属性的长度，可能是定长长度，也可能是最大长度，由isFixed标识
    // Note：位域数据类型和BitMap数据类型时，单位为bit，其他数据类型为Byte
    uint16_t nameLen;  // 属性名称长度
    char *name;        // 属性名称
    bool isValid;  // 标识当前PropertySchema是否有效，如果无效表明该属性是Node类型，此时下面成员无意义
    bool isNullable;  // 当前属性是否可为空
    bool isFixed;  // 标识当前属性是否为定长，如果为真，则成员size为定长长度，否则为最大长度
    bool isResource;         // 当前属性是否是资源字段
    uint8_t bitfieldOffset;  // 当前数据类型为位域时才有效, 记录在位域中的偏移量
} TestPropertySchemaT;

struct TestSchema {
    bool isFlat;        // 是否为平的，如果该Schema不带有NodeSchema就是平的，即不含子树
    uint16_t level;     // 该Schema在树模型中所属节点的层级
    uint32_t propeNum;  // 属性数目，包括property和node
    TestPropertySchemaT *properties;  // 属性数组，其中有些PropertySchema是无效的，相应的属性为NodeSchema
    uint32_t nodeNum;                 // nodes数组的长度
    TestNodeSchemaT *nodes;           // nodes数组，支持Tree模型，每个元素表示一个子树
};

typedef enum {
    TEST_NODE_MEMBER_INDEX,
    TEST_HASH_INDEX,
    TEST_HASH_LINKLIST_INDEX,
    TEST_ART_INDEX_LOCAL,        // 用ART索引支持原LOCAL排序索引
    TEST_ART_INDEX_HASHCLUSTER,  // 用ART索引支持原HASHCLUSTER索引
    TEST_LPM4_INDEX,
    TEST_LPM6_INDEX,
    TEST_HASHCLUSTER_INDEX,
    TEST_CHAINED_HASH_INDEX,
    TEST_HAC_HASH_INDEX = 13,
    TEST_MULTI_HASH_INDEX = 14,
} TestIndexTypeE;

// 索引的约束条件枚举
typedef enum {
    TEST_UNIQUE,      // 主键肯定是唯一的，此处主要是针对非主键。
    TEST_PRIMARY,     // 是否是主键，主键肯定是唯一的。
    TEST_NON_UNIQUE,  // 非主键，也非唯一
} TestIndexConstraintE;

// memberkey暂无诉求, 不解析
typedef struct IndexLabel {
    uint32_t indexNameLen;     // 索引的名称长度
    char *indexName;           // 索引的名称
    bool isNullable;           // 该索引上属性是否可允许为空
    bool isLabelLatchMode;     // 该索引是否处于大表锁模式下
    uint32_t maxKeyLen;        // 按照每个索引属性取最大空间而计算的keyBuf长度，包含bitsLen
    uint32_t indexId;          // 索引的id
    uint16_t hcIndexId;        // hashcluster索引ID
    TestIndexTypeE indexType;  // 索引的类型
    TestIndexConstraintE indexConstraint;  // 索引的约束
    char *sourceLabelName;                 // 索引所属点或边的标签名称
    uint32_t sourceLabelNameLen;           // 索引所属点或边的标签名称长度
    uint32_t propeNum;                     // 索引的属性个数
    uint16_t fixedPropeNum;                // 定长索引属性的个数
    TestPropertySchemaT *properties;       // 在哪些属性上建立了该索引
} TestIndexLabelT;

typedef struct TestVertexLabel {
    char *topRecordName;
    uint32_t topRecordNameLen;
    uint32_t schemaVersion;
    char *nsName;
    TestSchemaT *schema;
    TestVertexTypeE vertexType;
    TestIndexLabelT *pkIndex;     // 主键索引
    uint32_t secIndexNum;         // 二级索引的数目
    TestIndexLabelT *secIndexes;  // 二级索引数组
    uint16_t hcIndexNum;          // hashcluster索引的数量
} TestVertexLabelT;

struct structTestCtx {
    TestVertexLabelT *vertexLabel;
    bool haveBitmap;
    bool isFixKey;
    bool useExternalMem;
    int value;
    uint32_t keyId;
    uint32_t vertexSize;
    uint32_t keySize;
    uint32_t idx;
    uint32_t useSize;
    uint32_t totalSize;
    uint32_t memMax;
    uint32_t memSize;
    void **memAddr;
    GmcStmtT *stmt;
    uint32_t *lenStack;
    uint32_t initStack[INIT_STACK_SIZE];
    bool *keyNullInfo;
    bool *fieldNullInfo;  // 标记该列是否写null值，该字段为空指针时采用默认nullinfo序列化规则
};

static TestDataTypePairT g_testBitDataType[] = {
    {"uint16", GMC_DATATYPE_BITFIELD16},
    {"uint32", GMC_DATATYPE_BITFIELD32},
    {"uint64", GMC_DATATYPE_BITFIELD64},
    {"uint8", GMC_DATATYPE_BITFIELD8},
};

// order by TestDataTypePairT.strType
static TestDataTypePairT g_testDataType[] = {
    {"bitmap", GMC_DATATYPE_BITMAP},
    {"boolean", GMC_DATATYPE_BOOL},
    {"bytes", GMC_DATATYPE_BYTES},
    {"char", GMC_DATATYPE_CHAR},
    {"double", GMC_DATATYPE_DOUBLE},
    {"enum", GMC_DATATYPE_ENUM},
    {"fixed", GMC_DATATYPE_FIXED},
    {"float", GMC_DATATYPE_FLOAT},
    {"identity", GMC_DATATYPE_IDENTITY},
    {"int", GMC_DATATYPE_INT32},
    {"int16", GMC_DATATYPE_INT16},
    {"int32", GMC_DATATYPE_INT32},
    {"int64", GMC_DATATYPE_INT64},
    {"int8", GMC_DATATYPE_INT8},
    {"long", GMC_DATATYPE_INT64},
    {"partition", GMC_DATATYPE_PARTITION},
    {"resource", GMC_DATATYPE_RESOURCE},
    {"string", GMC_DATATYPE_STRING},
    {"time", GMC_DATATYPE_TIME},
    {"uchar", GMC_DATATYPE_UCHAR},
    {"uint16", GMC_DATATYPE_UINT16},
    {"uint32", GMC_DATATYPE_UINT32},
    {"uint64", GMC_DATATYPE_UINT64},
    {"uint8", GMC_DATATYPE_UINT8},
};

const static TestBasicTypeSizeT G_TEST_BASIC_TYPE_SIZE[] = {
    {
        GMC_DATATYPE_CHAR,
        sizeof(char),
    },
    {
        GMC_DATATYPE_UCHAR,
        sizeof(uint8_t),
    },
    {
        GMC_DATATYPE_INT8,
        sizeof(int8_t),
    },
    {
        GMC_DATATYPE_UINT8,
        sizeof(uint8_t),
    },
    {
        GMC_DATATYPE_INT16,
        sizeof(int16_t),
    },
    {
        GMC_DATATYPE_UINT16,
        sizeof(uint16_t),
    },
    {
        GMC_DATATYPE_INT32,
        sizeof(int32_t),
    },
    {
        GMC_DATATYPE_UINT32,
        sizeof(uint32_t),
    },
    {
        GMC_DATATYPE_BOOL,
        sizeof(bool),
    },
    {
        GMC_DATATYPE_INT64,
        sizeof(int64_t),
    },
    {
        GMC_DATATYPE_UINT64,
        sizeof(uint64_t),
    },
    {
        GMC_DATATYPE_FLOAT,
        sizeof(float),
    },
    {
        GMC_DATATYPE_DOUBLE,
        sizeof(double),
    },
    {
        GMC_DATATYPE_RESOURCE,
        sizeof(uint64_t),
    },
    {
        GMC_DATATYPE_TIME,
        sizeof(uint64_t),
    },
    {
        GMC_DATATYPE_BITFIELD8,
        sizeof(uint8_t),
    },
    {
        GMC_DATATYPE_BITFIELD16,
        sizeof(uint16_t),
    },
    {
        GMC_DATATYPE_BITFIELD32,
        sizeof(uint32_t),
    },
    {
        GMC_DATATYPE_BITFIELD64,
        sizeof(uint64_t),
    },
    {
        GMC_DATATYPE_PARTITION,
        sizeof(uint8_t),
    },
};

int TestGetVertexLabelFromSchema(char *schema, const char *nsName);
int TestGetVertexLabelFromCtx(TestLabelInfoT *labelInfo, TestVertexLabelT **vertexLabel);
void OutOfMemoryPanicRaise(const char *paincInfo, ...);

// 流计算同时依赖时序和结构化写，此处的宏隔离范围需要缩小，最好的方式是将序列化的代码单独提取出来
#if defined FEATURE_STREAM || !defined PRODUCT_USG_TS

void ExtendLenStack(structTestCtx *c);
// ######## Entry function 1: release the dynamic memory when deSeri vertex
void deSeriFreeDynMem(structTestCtx *c, bool isAll = false);
void ExtendDynArray(structTestCtx *c);
int GetBitFieldSize(TestPropertySchemaT *p, uint32_t *begin, uint32_t end);
void SeriVertexRecord(
    GmcSeriT *s, TestSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot = true, uint32_t *size = NULL);
void SeriVertexSubNode(GmcSeriT *s, TestSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot = true);
// ######## Entry function 2: SeriStructVertex
int32_t SeriStructVertex(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize);
int getRecordSize(GmcSeriT *s, TestSchemaT *schema, uint8_t *addr, bool isRoot = true, uint32_t *size = NULL);
// ------>> inner function
int getSubNodeSize(GmcSeriT *s, TestSchemaT *schema, uint8_t *addr);
// ######## Entry function 3: getSerialVertexLength
void getSerialVertexLength(GmcSeriT *s);
// ######## Entry function 4: SeriPrimaryKey
int32_t SeriPrimaryKey(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize);
// ######## Entry function 5: getSerialKeyLength
void getSerialKeyLength(GmcSeriT *s);
void GetVarNodePos(TestSchemaT *schema, int *fixedLast, int *varFirst, int *varCount, int *nodeFirst, int *nodeCount);
void deSeriStructRecord(
    GmcDeseriT *d, TestSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data, uint32_t *fixedLen);
void deSeriStructSubNode(GmcDeseriT *d, TestSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data);
// ######## Entry function 6: deSeriStructVertex
int32_t deSeriStructVertex(void *deSeri, const uint8_t *srcBuf, uint32_t srcLen, GmcStructureResvT *reservedSize);

// 释放资源
void FreeDynMemOfVertexLabel(TestVertexLabelT **testVertexLabel);
void FreeDynMemOfSchema(TestSchemaT *schema);

#endif /* end of !defined FEATURE_STREAM && !defined PRODUCT_USG_TS */
#if !defined PRODUCT_USG_TS && !defined FEATURE_STREAM

// ######## Entry function common: common interface
int32_t testStructSetVertexWithBuf(GmcStmtT *stmt, void *structObj, TestLabelInfoT *labelInfo);
void structSetKeySeri(
    GmcStmtT *stmt, void *structObj, GmcSeriT *seri, structTestCtx *seriCtx, uint32_t keyId, bool *keyNullInfo);
int32_t testStructSetIndexKeyWithBuf(
    GmcStmtT *stmt, void *structObj, uint32_t keyId, bool *keyNullInfo, TestLabelInfoT *labelInfo);
int32_t testStructSetKeyRangeStructure(GmcStmtT *stmt, void *structLeftKey, void *structRightKey,
    const GmcRangeItemFlagT *items, uint32_t itemNumber, uint32_t keyId, bool *keyNullInfo, TestLabelInfoT *labelInfo);
void testStructSetDeseri(GmcStmtT *stmt, void *structObj, GmcDeseriT *deseri, structTestCtx *deseriCtx,
    bool useExternalMem, TestLabelInfoT *labelInfo);
int32_t testStructGetVertexDeseri(GmcStmtT *stmt, GmcDeseriT *deseri);
void structSetInputBufInfo(GmcStmtT *stmt, void *structObj, GmcSeriT *keySeri, GmcStructBufferT *inputBufInfo);
int32_t testStructGetVertexBuf(GmcStmtT *stmt, void *structObj, uint32_t keyId, GmcSeriT *keySeri,
    GmcStructBufferT *inputBufInfo, TestLabelInfoT *labelInfo);

/* **********************************************************************
 * 函数: TestGmcVertexGetRecordBuf(GmcStmtT *stmt, void *structObj, structTestCtx *c, TestLabelInfoT *labelInfo)
 * 功能: 获取vertex的record buf的地址和长度, 并且将buf内容解析到对应结构体中。
 * 参数:
 * stmt[in] -- 连接句柄。
 * structObj[out] -- 用户预先定义好的表对应的结构体, 要求按照 先定长字段, 再变长字段
 * 的顺序来定义结构体(node节点直接忽略)。 c[out] -- 读取的数据中变长字段有值时, 自动申请内存存放变长字段内容,
 * 并存放在此上下文变量中, 用户从structObj中验证完数据后, 需手动调用deSeriFreeDynMem接口释放内存。
 * ********************************************************************** */
int32_t TestGmcVertexGetRecordBuf(GmcStmtT *stmt, void *structObj, structTestCtx *c, TestLabelInfoT *labelInfo);

/* **********************************************************************
 * 函数: TestGmcNodeGetNodeRecordBuf(GmcNodeT *node, const char *nodeName, void *structObj,
 * structTestCtx *c, TestLabelInfoT *labelInfo)
 * 功能: 获取当前node节点的record buf的地址和长度, 并且将buf内容解析到对应结构体中。
 * 参数:
 * node[in] -- 节点句柄。
 * structObj[out] -- 用户预先定义好的表对应的结构体, 要求按照 先定长字段, 再变长字段
 * 的顺序来定义结构体(node节点直接忽略)。 c[out] -- 读取的数据中变长字段有值时, 自动申请内存存放变长字段内容,
 * 并存放在此上下文变量中, 用户从structObj中验证完数据后, 需手动调用deSeriFreeDynMem接口释放内存。
 * ********************************************************************** */
int32_t TestGmcNodeGetNodeRecordBuf(
    GmcNodeT *node, const char *nodeName, void *structObj, structTestCtx *c, TestLabelInfoT *labelInfo);

/******************************************* 结构化 end *******************************************/

#endif  // PRODUCT_USG_TS FEATURE_STREAM

long readJanssonFile(const char *path, char **buf, bool convertArray = false, char *nsName = g_testNameSpace,
    bool needStructParse = true);

// 用例执行失败时重启db
void RestartWhenFailed();
// 用例执行成功时检测资源残留
int CheckWhenSucc();
int TestWaitRsmRecoverFinish(int timeout = 30);

/******************************************* client Register Signal start *********************************/
void TestWriteLog(LogLevelID level, const char *format, ...);
int GetProcessIdByName(const char *proName);
typedef void (*TestCrashHandlerFunc)(int signo, siginfo_t *info, void *context);
void TestCrashHandler(int signo, siginfo_t *info, void *context);
int TestTryRegisterSignal(TestCrashHandlerFunc crashHandler);
/******************************************* client Register Signal end **********************************/

typedef struct {
    uint64_t subConnRingFailTimes;
} TestAlarmDataT;
/* **********************************************************************
 * 函数: TestGmcGetAlarmData
 * 功能: 获取告警的信息。
 * ********************************************************************** */
int TestGmcGetAlarmData(TestAlarmDataT *testAlarmData);

int testGmcDropGraphLabel(
    GmcStmtT *stmt, const char *srcLabelName, const char *dstLabelName, const char *edgeLabelName);

#ifdef __cplusplus
}
#endif

#endif /* T_RD_COMMON_H */
