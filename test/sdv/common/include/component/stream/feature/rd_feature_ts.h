/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 时序公共AW
 * Author: guopanpan
 * Create: 2024-09-19
 */
#ifndef RD_TS_H
#define RD_TS_H 1

#ifdef FEATURE_STREAM

#include "gmc.h"
#include "gmc_sql.h"
#include "gmc_graph.h"
#include "gmc_types.h"
#include "gmc_connection.h"
#include "securec.h"
#include "rd_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RD_TS_MAX_SQL_SIZE 512
#define RD_TS_MAX_CHAR_BUFF_SIZE 65535

// 以下接口在执行后将改变stmt的模型为时序模型
int32_t RdTsGetRecordCount(GmcStmtT *stmt, const char *tableName, uint32_t *count);
int32_t RdTsCheckRecordCount(GmcStmtT *stmt, const char *tableName, uint32_t expectCount);

// 以下接口均需要在执行查询后调用，用于从stmt中快速获取状态信息
int32_t RdTsCheckResultRows(GmcStmtT *stmt, uint32_t expectCount);
uint32_t RdTsGetResultRows(GmcStmtT *stmt);

// 以下接口均需要在执行查询和GmcFetch后调用，用于从stmt中快速获取和校验数据
int32_t RdTsCheckPropertyInt64(GmcStmtT *stmt, uint32_t index, int64_t expectVal);
int32_t RdTsCheckPropertyText(GmcStmtT *stmt, uint32_t index, const char *expectVal, void (*freeFunc)(void *));

#ifdef __cplusplus
}
#endif

#endif /* FEATURE_STREAM */
#endif /* end of RD_TS_H */
