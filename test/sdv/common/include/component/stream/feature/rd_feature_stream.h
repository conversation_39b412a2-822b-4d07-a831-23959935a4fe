/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 流计算客户端接口封装
 * Author: guopanpan
 * Create: 2024-09-09
 * Note: 编写测试用例时建议使用该接口替换DB原生对外接口，当DB原生接口产生变更时，能极大提高适配用例的效率
 */
#ifndef RD_FEATURE_STREAM_H
#define RD_FEATURE_STREAM_H 1

#ifdef FEATURE_STREAM

#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include "rd_log.h"
#include "rd_public_client.h"
#include "gmc_graph.h"
#include "gmc_types.h"
#include "gmc_sql.h"
#include "t_rd_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct TestVertexLabel RdVertexLabelT;

int32_t RdStreamEnvInit(bool initServerCfg = true, bool startServer = true);
int32_t RdStreamEnvClean(bool stopServer = false);

RdVertexLabelT *RdStreamParseTableSchema(const char *sql);
void RdStreamFreeTableSchema(RdVertexLabelT *vertexLabel);
int32_t RdStreamSetVertexWithBuf(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, void *data, bool *nullInfo = NULL);

// 所有查询结果均被转换成字符串存储和校验
typedef std::vector<std::vector<std::string>> RdtSqlTableT;
typedef std::vector<std::string> RdtSqlRowT;

typedef struct {
    const char *sql;
    int32_t model = GMC_MODEL_STREAM;
    int32_t status = GMERR_OK;
    RdtSqlTableT output = {};
} RdStreamExecDescT;

int32_t RdStreamExecSql(GmcStmtT *stmt, RdStreamExecDescT *desc, uint32_t descSize);
int32_t RdStreamDropMetadata(GmcStmtT *stmt, RdStreamExecDescT *desc, uint32_t descSize);

// 以下接口当前仅流模型使用，如果其他模型测试需要，可移动到公共文件中
int32_t RdGmserverStart();
int32_t RdGmserverStop();
int32_t RdGmserverRecoverCfg();
int32_t RdGmserverModifyCfg(const char *item, const char *value);
int32_t RdGmserverInitCfg();
int32_t RdCmdExecute(const char *format, ...);

#ifdef __cplusplus
}
#endif

#endif /* FEATURE_STREAM */
#endif /* RD_FEATURE_STREAM_H */
