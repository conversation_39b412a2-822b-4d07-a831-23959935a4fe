/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 090_NEGRT
 */

#ifndef T_RD_PERSIS_H
#define T_RD_PERSIS_H

#ifdef FEATURE_PERSISTENCE
#include <sys/epoll.h>
#include <sys/prctl.h>
#include <thread>
#include <securec.h>
#include <string.h>
#include <zlib.h>
#include <gmc_errno.h>
#include <gm_adpt.h>
#include "gms.h"
#include <t_rd_log.h>
#include "t_rd_common.h"

#ifdef __cplusplus
extern "C" {
#endif

int32_t TestBufferpoolPersistCompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize);
int32_t TestBufferpoolPersistDecompressCallBack(
    uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize);
int32_t TestBufferpoolPersistFileNameFilterCallBack(const char *src, char *dest, uint32_t destSize);

void TestBufferpoolRegAdaptFuncsProc(Status expectRet);

#ifdef __cplusplus
}
#endif

#endif /* FEATURE_PERSISTENCE */
#endif /* T_RD_PERSIS_H */
