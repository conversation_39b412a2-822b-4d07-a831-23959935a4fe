/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_yang.h
 * Author: lushiguang
 * Create: 2023-06-15
 */
#ifndef T_RD_YANG_H
#define T_RD_YANG_H
#ifdef FEATURE_YANG
#include "gmc_yang.h"
#include "gmc_yang_types.h"
#include "t_rd_common.h"
#include "t_rd_log.h"
#include "t_rd_sn.h"

/********* 移植的YANG DT编辑框架引用 start *********/
#include "dm_data_record.h"
#include "clt_types.h"
#include "dm_yang_subtree.h"
#include "dm_yang_common.h"
#include "clt_stmt.h"
#include "adpt_string.h"
/********* 移植的YANG DT编辑框架引用 end *********/

#ifdef __cplusplus
extern "C" {
#endif

using namespace std;
struct FetchRetCbParam {
    int step;
    GmcStmtT *stmt;
    int32_t expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式，使用枚举GmcSubtreeFilterModeE设置值
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
    AsyncUserDataT *data;
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
};

struct YangValidateUserDataT {
    int recvNum;                // 此次共接收的消息数
    int32_t status;  // 预期的操作状态
    bool validateRes;   // 校验结果，true表示全部成功，false表示有失败
    uint32_t failCount; // 模型校验时表示失败的count数目，数据校验时暂未使用
    bool isValidErrorPathInfo;
    GmcErrorPathCodeE expectedErrCode;
    uint32_t expectedErrClauseIndex;
    const char *expectedErrMsg;            // 预期 error msg 信息
    const char *expectedErrPath;           // 预期 error path 信息
};

typedef struct testFetchRetCbParam {
    int *received;
    GmcStmtT *stmt;
    int32_t expectStatus; // 预期的操作状态
    int32_t actualStatus; // 实际的操作状态
    bool isSubtree;
    AsyncUserDataT data;

    uint32_t *step;
    uint32_t times;                        // 记录分批返回结果时是第几次查询
    uint32_t lastExpectIdx;                // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> *expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
    bool isValidate;                       // 是否校验返回值
    bool noParseTree;                      // 不解析查询结果
    GmcSubtreeFilterT *subtreeFilters;
} TestFetchRetCbParamT;

extern bool g_printDiffFlag;

bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB);
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB);
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB);
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJsonIsEqual(const char *json1, const char *json2);
void AsyncFetchRetCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void AsyncFetchRetCbMulti(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void AsyncFetchRetCbNoData(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void AsyncFetchRetCbEncode(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
int testWaitAsyncSubtreeRecv_API(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true);
int TestWaitAsyncSubtreeRecvAPIOneThread(void *userData, int expRecvNum = 1, int timeout = 120000,
    bool isAutoReset = true, int32_t epollFd = g_epollDataOneThread.userEpollFd);
void TestYangValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);
int TestWaitYangValidateRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true);
int TestWaitYangValidateRecvOneThread(void *userData, int expRecvNum = 1, bool isAutoReset = true, int timeout = -1,
    int32_t epollFd = g_epollDataOneThread.userEpollFd);

typedef enum {
    AW_YANG_TYPE_ROOT = 0,
    AW_YANG_TYPE_CHILD = 1,
    AW_YANG_TYPE_BUTT = AW_YANG_TYPE_CHILD
} TestYangLabelType;

typedef enum {
    AW_YANG_TRANS_START = 0,
    AW_YANG_TRANS_COMMIT = 1,
    AW_YANG_TRANS_ROLLBACK = 2,
    AW_YANG_TRANS_BUTT = AW_YANG_TRANS_ROLLBACK
} TestYangTransOper;

typedef enum {
    AW_YANG_SAVEPT_CREATE = 0,
    AW_YANG_SAVEPT_RELEASE = 1,
    AW_YANG_SAVEPT_ROLLBACK = 2,
    AW_YANG_SAVEPT_BUTT
} TestYangSavepointOper;

typedef struct {
    GmcOperationTypeE opType;        // 节点的操作类型
    const char *childName;           // node、field或vertex的名字
    GmcNodeT *parentNode;            // parent的node名
    GmcNodeT **childNode;            // child的node名
    GmcStmtT *stmt;                  // 传入的stmt
    GmcYangPropOpTypeE fieldOptype;  // 字段的操作类型
    GmcDataTypeE type;               // 字段的数据类型
    uint32_t size;                   // 字段的属性值长度
    void *value;                     // 字段的值
    const char *expectPath;          // 预期的choiceCase路径
    GmcAttributeTypeE attrType;      // enum或identity类型的name或value
} TestEditOpT;

int awYangFetchDiff(GmcStmtT *stmt, AsyncUserDataT *data = NULL, std::vector<std::string> *expectReply = NULL,
    bool needCheckReply = false, int32_t expectStatus = GMERR_OK, GmcYangFetchExecuteDoneT userCb = NULL,
    YangConnOptionT *connOptionsInput = NULL, int32_t epollFd = g_epollDataOneThread.userEpollFd);

int awYangQuerySubtree(GmcStmtT *stmt, AsyncUserDataT *data = NULL, GmcSubtreeFilterT *userFilters = NULL,
    std::vector<std::string> *expectReply = NULL, bool needCheckReply = false, int32_t expectStatus = GMERR_OK,
    GmcYangFetchExecuteDoneT userCb = NULL, YangConnOptionT *connOptionsInput = NULL,
    int32_t epollFd = g_epollDataOneThread.userEpollFd);

int awYangBatchExecuteAsync(GmcConnT *conn, GmcStmtT *stmt, GmcBatchT *batch,
    GmcBatchDoneT userCb = batch_execute_callback, void *userData = NULL, uint32_t mode = 0);
int32_t awYangBatchPrepare(GmcConnT *conn, GmcBatchT **batch = NULL, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON, GmcBatchOrderTypeE batchOrder = GMC_BATCH_ORDER_STRICT,
    uint32_t batchLimitSize = 2048);

int awYangTransOpAsync(TestYangTransOper op, GmcConnT *conn, GmcStmtT *stmt, const GmcTxConfigT *config = NULL,
    GmcTransDoneT userCb = trans_rollback_callback, void *userData = NULL);

int32_t awYangTransSavepointOp(TestYangSavepointOper op, GmcConnT *conn, const char *spName,
    GmcTransDoneT userCb = savepoint_callback, void *userData = NULL);

int32_t awYangPrepareVertexNode(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, TestYangLabelType type,
    GmcStmtT *rootStmt, GmcStmtT *childStmt, GmcNodeT **node);
int32_t awYangEditChildNode(
    GmcOperationTypeE opType, GmcNodeT *parentNode, const char *childName, GmcNodeT **childNode);
int32_t awYangInitNodeProperty(GmcNodeT *node, const char *propName, void *value, GmcDataTypeE type, uint32_t size);

int32_t testYangDMLDemoFullTree(GmcConnT *connAsync, GmcStmtT *stmtAsync,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON);
int32_t testYangDMLDemoRemoveRoot(GmcConnT *connAsync, GmcStmtT *stmtAsync,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON);
int32_t testYangDMLDemoRemoveRootInner(GmcConnT *connAsync, GmcStmtT *stmtAsync, const char *rootName,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON);

int32_t testYangDMLList(GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt,
    GmcStmtT *listStmt, uint32_t listEleCount);
int32_t testYangDMLRootRemove(GmcBatchT *batch, const char *labelName, GmcStmtT *rootStmt, GmcNodeT **rootNode);
int32_t testYangDMLRoot(
    GmcBatchT *batch, const char *labelName, GmcOperationTypeE op, GmcStmtT *rootStmt, GmcNodeT **rootNode);
int32_t testYangCreateNamespaceWithCfgAsync(GmcStmtT *stmt, GmcNspCfgT *nspCfg = NULL,
    GmcCreateNamespaceDoneT userCb = create_namespace_callback, void *userData = NULL, int expectStatus = GMERR_OK);
int32_t testYangUseNamespaceAsync(GmcStmtT *stmt, const char *namespaceName,
    GmcUseNamespaceDoneT userCb = use_namespace_callback, void *userData = NULL, int expectStatus = GMERR_OK);
int32_t testYangClearNamespaceAsync(GmcStmtT *stmt, const char *namespaceName,
    GmcClearNamespaceDoneT userCb = ClearNSCallbak, void *userData = NULL, int expectStatus = GMERR_OK);
int32_t testYangTruncateNamespaceAsync(GmcStmtT *stmt, const char *namespaceName,
    GmcTruncateNamespaceDoneT userCb = ClearNSCallbak, void *userData = NULL, int expectStatus = GMERR_OK);
int32_t testYangDropNamespaceAsync(GmcStmtT *stmt, const char *namespaceName,
    GmcDropNamespaceDoneT userCb = ClearNSCallbak, void *userData = NULL, int expectStatus = GMERR_OK);
int32_t testYangBatchDestroy(GmcBatchT *batch, AsyncUserDataT *userData);
int32_t testYangSetNodePropertyNew(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName, GmcYangPropOpTypeE opType);
int32_t testYangSetIndex(
    GmcStmtT *stmt, uint32_t index, GmcDataTypeE type, const char *keyName, const void *value, uint32_t valueSize);

int TestEditChildNodeFieldVertex(TestEditOpT config);
int32_t TestGmcYangSetNodeProperty(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName,
    GmcYangPropOpTypeE opType, const char *expectPath = NULL, GmcAttributeTypeE attrType = GMC_ATTRIBUTE_BUTT);
int32_t TestGmcYangEditChildNode(
    GmcNodeT *node, const char *name, GmcOperationTypeE opType, GmcNodeT **child, const char *expectPath = NULL);
int TestYangGmcPrepareStmtByLabelName(GmcNodeT *parentNode, GmcStmtT *stmt, const char *vertexLabelName,
    GmcOperationTypeE operationType, const char *expectPath = NULL);

void TestFetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void TestYangFetchDiffExtExecuteAsync(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data,
    GmcFetchDiffModeE diffMode = GMC_FETCH_DIFF_EXPLICIT, int rets = GMERR_OK);
void FetchDiffBuf_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void TestYangFetchDiffBufExtExecuteAsync(GmcStmtT *stmt, AsyncUserDataT *data,
    GmcFetchDiffModeE diffMode = GMC_FETCH_DIFF_EXPLICIT, int rets = GMERR_OK);
void TestYangDiffFetchRetFromBuf(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT *data,
    int rets = GMERR_OK);
void TestCheckYangTreeAttach(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t index,
    vector<string> &expectReply);
void FetchDiffBatch_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg);
void TestFetchDiffModeBatch(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data,
    GmcFetchDiffModeE diffMode = GMC_FETCH_DIFF_EXPLICIT, int rets = GMERR_OK);

/* **********************************************************************
 * 函数: TestYangBatchExecuteByJsonAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, const char *dataJson,
const char *fieldJson = NULL, GmcYangDiffTypeE diffOps = GMC_YANG_DIFF_OFF, bool isOneThread = false);
 * 功能: 移植YANG的DT编辑框架, 原入口LltBatchExecuteByJsonAsync, 便于移植开发的用例。
 * 参数:
 * conn[in] -- 连接。
 * stmt[in] -- 连接。
 * labelName[in]：根节点表名。
 * dataJson[in]：数据json, 没有指定op时, 六原语默认为GMC_OPERATION_INSERT操作, 暂不支持解析别名。
 * fieldJson[in]：字段属性配置, 没有指定propOp时, 五原语默认为GMC_YANG_PROPERTY_OPERATION_MERGE操作, 暂不支持解析别名。
 * diffOps[in]：编辑是否开启diff。
 * isOneThread[in]：true-调用testWaitAsyncRecvOneThread; false-调用testWaitAsyncRecv。
 * ********************************************************************** */
void TestYangBatchExecuteByJsonAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, const char *dataJson,
    const char *fieldJson = NULL, GmcYangDiffTypeE diffOps = GMC_YANG_DIFF_OFF, bool isOneThread = false);

#define AW_YANG_EXPECT_OK(func, ...)                                                                                   \
    do {                                                                                                               \
        int actualCode = (func);                                                                                       \
        EXPECT_EQ(actualCode, GMERR_OK);                                                                               \
        if ((actualCode) != (GMERR_OK)) {                                                                              \
            AW_FUN_Log(                                                                                                \
                LOG_ERROR, "%s:%d:%s, %s, expect:%d, actual:%d.", __FILE__, __LINE__, __func__, GMERR_OK, actualCode); \
        }                                                                                                              \
    } while (0)

#define AW_YANG_EXPECT_ERR(actualCode, expectCode, func)                                                           \
    do {                                                                                                           \
        actualCode = (func);                                                                                       \
        EXPECT_EQ(actualCode, expectCode);                                                                         \
        if ((actualCode) != (expectCode)) {                                                                        \
            AW_FUN_Log(LOG_ERROR, "%s:%d:%s, %s, expect:%d, actual:%d.", __FILE__, __LINE__, __func__, expectCode, \
                actualCode);                                                                                       \
        }                                                                                                          \
    } while (0)

#define AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, func)                                                        \
    do {                                                                                                           \
        actualCode = (func);                                                                                       \
        EXPECT_EQ(actualCode, expectCode);                                                                         \
        if ((actualCode) != (expectCode)) {                                                                        \
            AW_FUN_Log(LOG_ERROR, "%s:%d:%s, %s, expect:%d, actual:%d.", __FILE__, __LINE__, __func__, expectCode, \
                actualCode);                                                                                       \
        }                                                                                                          \
        if ((actualCode) != GMERR_OK) {                                                                            \
            int _getLastErrorRet = testGmcGetLastError();                                                          \
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, _getLastErrorRet);                                                    \
        }                                                                                                          \
    } while (0)

#define AW_YANG_ASSERT_EQ_INT(actualCode, expectCode, func)                                                        \
    do {                                                                                                           \
        actualCode = (func);                                                                                       \
        if ((actualCode) != (expectCode)) {                                                                        \
            AW_FUN_Log(LOG_ERROR, "%s:%d:%s, %s, expect:%d, actual:%d.", __FILE__, __LINE__, __func__, expectCode, \
                actualCode);                                                                                       \
        }                                                                                                          \
        ASSERT_EQ(actualCode, expectCode);                                                                         \
    } while (0)

#define AW_YANG_CHECK_AND_RETURN(actualCode, expectCode)                                                             \
    do {                                                                                                             \
        EXPECT_EQ(actualCode, expectCode);                                                                           \
        if ((actualCode) != (expectCode)) {                                                                          \
            AW_FUN_Log(                                                                                              \
                LOG_ERROR, "%s:%d:%s, expect:%d, actual:%d.", __FILE__, __LINE__, __func__, expectCode, actualCode); \
            return;                                                                                                  \
        }                                                                                                            \
    } while (0)

#define AW_YANG_CHECK_AND_RETURN_RET(actualCode, expectCode)                                                         \
    do {                                                                                                             \
        EXPECT_EQ(actualCode, expectCode);                                                                           \
        if ((actualCode) != (expectCode)) {                                                                          \
            AW_FUN_Log(                                                                                              \
                LOG_ERROR, "%s:%d:%s, expect:%d, actual:%d.", __FILE__, __LINE__, __func__, expectCode, actualCode); \
            return actualCode;                                                                                       \
        }                                                                                                            \
    } while (0)

#define AW_YANG_FetchDiff(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#define AW_YANG_AllocStmt(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#define AW_YANG_SetRoot(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#define AW_YANG_BindChild(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#define AW_YANG_PrepareLabel(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#define AW_YANG_SetProperty(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#define AW_YANG_BatchAdd(actualCode, expectCode, ...) AW_YANG_EXPECT_EQ_INT(actualCode, expectCode, ##__VA_ARGS__)

#ifdef __cplusplus
}
#endif
#endif /* FEATURE_YANG */
#endif /* T_RD_YANG_H */
