if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)

    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then     cd $VAR_GMDB_HOME_PATH/test/sdv/;     sh build.sh euler -p testcases/23_DirectWrite/ || exit 2; fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/016_DWSupportUniSecIndexUpdateAndDelete;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_HeapDML --gtest_repeat=$repeat_time --gtest_filter=*001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/016_DWSupportUniSecIndexUpdateAndDelete;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_HeapDML --gtest_repeat=$repeat_time --gtest_filter=*002 
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/016_DWSupportUniSecIndexUpdateAndDelete;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_HeapDML --gtest_repeat=$repeat_time --gtest_filter=*003 
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/016_DWSupportUniSecIndexUpdateAndDelete;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_HeapDML --gtest_repeat=$repeat_time --gtest_filter=*005 
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/017_DwenableTableLock;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dwEnableTableLock01 --gtest_repeat=$repeat_time --gtest_filter=*004 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/017_DwenableTableLock;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dwEnableTableLock02 --gtest_repeat=$repeat_time --gtest_filter=*001 
