if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)
    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then
    cd $VAR_GMDB_HOME_PATH/test/sdv/;
    sh tools/deploy.sh euler -p ts
    source ./autotest_env.sh
    sh build.sh -R euler -d USG_TS -c STREAM,TS -p testcases/34_stream/ || exit 2;
fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1" -p ts;

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/001_create_drop_stream_table;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_stream_table_basic --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/002_stream_struct_write;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_write_stream_data_basic --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/003_create_and_drop_stream_sink;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_create_stream_sink --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/003_create_and_drop_stream_sink;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./03_drop_stream_sink --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/004_write_into_tsdb;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_write_into_tsdb --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/005_create_drop_stream_view;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_stream_view_basic --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/006_write_from_view_into_tsdb;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_create_sink_with_timeout --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/007_distinct_count;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_distinct_count_basic --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/008_create_drop_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_stream_reference_basic --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/008_create_drop_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_stream_reference_basic --gtest_repeat=$repeat_time --gtest_filter=*002

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/009_upsert_into_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_upsert_into_stream_reference --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/010_select_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_select_stream_reference_basic --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/010_select_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_select_stream_reference_basic --gtest_repeat=$repeat_time --gtest_filter=*002

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/011_create_window_view;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_window_view_basic --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/011_create_window_view;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_window_view_basic --gtest_repeat=$repeat_time --gtest_filter=*002

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/012_create_drop_view_base_view_with_where;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_stream_view_basic --gtest_repeat=$repeat_time --gtest_filter=*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/012_create_drop_view_base_view_with_where;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./07_create_sink_and_view_with_where --gtest_repeat=$repeat_time --gtest_filter=*002

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/013_over_aggregate_calculation;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_over_aggregate_calculation_basic --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/013_over_aggregate_calculation;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_over_aggregate_calculation_basic --gtest_repeat=$repeat_time --gtest_filter=*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/013_over_aggregate_calculation;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_over_aggregate_calculation_basic --gtest_repeat=$repeat_time --gtest_filter=*003
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/013_over_aggregate_calculation;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_over_aggregate_calculation_basic --gtest_repeat=$repeat_time --gtest_filter=*004

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/014_view_trigger_calculation;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_view_trigger_calculation_basic --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/015_support_printf_format;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_printf_format --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/016_set_service_model_automatically;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./001_set_service_model_automatically --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/016_set_service_model_automatically;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./001_set_service_model_automatically --gtest_repeat=$repeat_time --gtest_filter=*002

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/017_support_line_num;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_line_num --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/017_support_line_num;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_line_num --gtest_repeat=$repeat_time --gtest_filter=*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/017_support_line_num;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_line_num --gtest_repeat=$repeat_time --gtest_filter=*003

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/018_stream_get_dlr_buffer;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_get_dlr_buffer_basic --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/019_stream_replay_dlr_buffer;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_replay_dlr_buffer_basic_function --gtest_repeat=$repeat_time --gtest_filter=*001

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/020_create_drop_subscribtion;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./02_create_subscribtion_interaction --gtest_repeat=$repeat_time --gtest_filter=*019
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/020_create_drop_subscribtion;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./02_create_subscribtion_interaction --gtest_repeat=$repeat_time --gtest_filter=*020

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/021_distribute_data_by_subscribtion;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_distribute_data_by_subscribtion --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/021_distribute_data_by_subscribtion;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_distribute_data_by_subscribtion --gtest_repeat=$repeat_time --gtest_filter=*002

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/022_auto_fill_hostname;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_auto_fill_hostname --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/022_auto_fill_hostname;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_auto_fill_hostname --gtest_repeat=$repeat_time --gtest_filter=*002

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/023_auto_fill_system_time;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_auto_fill_system_time --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/023_auto_fill_system_time;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_auto_fill_system_time --gtest_repeat=$repeat_time --gtest_filter=*002


# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/024_support_text;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_text_with_sub --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/024_support_text;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./02_support_text_with_sink --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/024_support_text;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_text_basic --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/024_support_text;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_text_basic --gtest_repeat=$repeat_time --gtest_filter=*002

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/025_create_sink_support_server_socket;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./001_create_sink_support_server_socket --gtest_repeat=$repeat_time --gtest_filter=*001

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/026_file_input;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_file_input --gtest_repeat=$repeat_time --gtest_filter=*011
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/026_file_input;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_file_input --gtest_repeat=$repeat_time --gtest_filter=*012

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/027_in_and_not_in;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_operator_in --gtest_repeat=$repeat_time --gtest_filter=*001

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/028_lightweight_union;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_lightweight_union --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/028_lightweight_union;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_lightweight_union --gtest_repeat=$repeat_time --gtest_filter=*002

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/029_mini_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_mini_stream_reference_basic --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/029_mini_stream_reference;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_mini_stream_reference_basic --gtest_repeat=$repeat_time --gtest_filter=*002

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/030_support_dispatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_dispatch --gtest_repeat=$repeat_time --gtest_filter=*001

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/031_group_by;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_group_by_basic --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/031_group_by;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_group_by_basic --gtest_repeat=$repeat_time --gtest_filter=*002

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/032_async_write;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./t01_async_write --gtest_repeat=$repeat_time --gtest_filter=*001

# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/033_support_alter_where;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_alter_where --gtest_repeat=$repeat_time --gtest_filter=*001
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/34_stream/033_support_alter_where;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./01_support_alter_where --gtest_repeat=$repeat_time --gtest_filter=*002

