if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)

    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then     cd $VAR_GMDB_HOME_PATH/test/sdv/;     sh build.sh euler -p testcases/04_Tool/ || exit 2; fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/015_Tools_Optimization;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./Optimization --gtest_repeat=$repeat_time --gtest_filter=*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/015_Tools_Optimization;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./Optimization01 --gtest_repeat=$repeat_time --gtest_filter=*020;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/016_BatchExport;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./BatchExport --gtest_repeat=$repeat_time --gtest_filter=*002 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/016_BatchExport;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./BatchExport --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/016_BatchExport;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./BatchExport --gtest_repeat=$repeat_time --gtest_filter=*005 ;

cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/017_gmimport_support_nest_folder;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmimportSupNest --gtest_repeat=$repeat_time --gtest_filter=*001 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/017_gmimport_support_nest_folder;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmimportSupNest --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/019_Gmstat_Compatible;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./Memdata --gtest_repeat=$repeat_time --gtest_filter=*001;                           
# cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/020_Alarm_SysView;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./Conn4_20 --gtest_repeat=$repeat_time --gtest_filter=*.*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/021_EstimatedMemorySize;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./ETTMemorySize --gtest_repeat=$repeat_time --gtest_filter=*001 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/021_EstimatedMemorySize;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./ETTMemorySize --gtest_repeat=$repeat_time --gtest_filter=*002 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/021_EstimatedMemorySize;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./ETTMemorySize --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdTest --gtest_repeat=$repeat_time --gtest_filter=*.Tool_023_GmcmdTest_001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdTest --gtest_repeat=$repeat_time --gtest_filter=*.Tool_023_GmcmdTest_002;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdTest --gtest_repeat=$repeat_time --gtest_filter=*.Tool_023_GmcmdTest_003;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdTruncate --gtest_repeat=$repeat_time --gtest_filter=*.*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdTruncate --gtest_repeat=$repeat_time --gtest_filter=*.*002;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdDeleteTest --gtest_repeat=$repeat_time --gtest_filter=*.*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdDeleteTest --gtest_repeat=$repeat_time --gtest_filter=*.*011;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdDropTest --gtest_repeat=$repeat_time --gtest_filter=*.*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdDropTest --gtest_repeat=$repeat_time --gtest_filter=*.*002;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*004;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*005;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*023;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*024;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*025;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdUpdateTest --gtest_repeat=$repeat_time --gtest_filter=*.*026;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdGenTest --gtest_repeat=$repeat_time --gtest_filter=*.*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdGenTest --gtest_repeat=$repeat_time --gtest_filter=*.*002;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdGenTest --gtest_repeat=$repeat_time --gtest_filter=*.*003;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdGenTest --gtest_repeat=$repeat_time --gtest_filter=*.*004;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdGenTest --gtest_repeat=$repeat_time --gtest_filter=*.*005;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/023_gmcmd;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./GmcmdGenTest --gtest_repeat=$repeat_time --gtest_filter=*.*006;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/024_gmimport_update;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./gmimport_test --gtest_repeat=$repeat_time --gtest_filter=*Tool_024_006 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/024_gmimport_update;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./gmimport_test --gtest_repeat=$repeat_time --gtest_filter=*Tool_024_007 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/024_gmimport_update;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./gmimport_test --gtest_repeat=$repeat_time --gtest_filter=*Tool_024_010 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/024_gmimport_update;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./gmimport_test --gtest_repeat=$repeat_time --gtest_filter=*Tool_024_013
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/04_Tool/024_gmimport_update;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./gmimport_test --gtest_repeat=$repeat_time --gtest_filter=*Tool_024_014
