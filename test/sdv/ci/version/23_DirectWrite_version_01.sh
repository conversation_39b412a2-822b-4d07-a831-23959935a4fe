if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)

    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then     cd $VAR_GMDB_HOME_PATH/test/sdv/;     sh build.sh euler -p testcases/23_DirectWrite/ || exit 2; fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/001_clusterDW;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./clusterDWFun --gtest_repeat=$repeat_time --gtest_filter=*.*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/002_DirectWrite;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DirectDefrag --gtest_repeat=$repeat_time --gtest_filter=*DW_002_002_068;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/002_DirectWrite;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DirectWrite --gtest_repeat=$repeat_time --gtest_filter=*DW_002_001_002;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/002_DirectWrite;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DirectWrite --gtest_repeat=$repeat_time --gtest_filter=*DW_002_001_003;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/002_DirectWrite;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DirectWrite --gtest_repeat=$repeat_time --gtest_filter=*DW_002_001_009;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/002_DirectWrite;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DirectWrite --gtest_repeat=$repeat_time --gtest_filter=*DW_002_001_042;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/003_DirectWriteOldSub;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./HeapFunNoSim --gtest_repeat=$repeat_time --gtest_filter=*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/005_DirectWriteNewSub;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./NewHeapFunNoSim --gtest_repeat=$repeat_time --gtest_filter=*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/011_DWDFX;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DWDFX --gtest_repeat=$repeat_time --gtest_filter=*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/004_DwAlterVertexLabel;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwUpgradeHeap --gtest_repeat=$repeat_time --gtest_filter=*.DW_004_001_001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/004_DwAlterVertexLabel;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwUpgradeHeap --gtest_repeat=$repeat_time --gtest_filter=*.DW_004_001_008 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/004_DwAlterVertexLabel;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwUpgradeHeap --gtest_repeat=$repeat_time --gtest_filter=*.DW_004_001_012 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/004_DwAlterVertexLabel;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwDegradeHeap --gtest_repeat=$repeat_time --gtest_filter=*.DW_004_002_003 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/006_DwClusterOther;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwUpgradeClst --gtest_repeat=$repeat_time --gtest_filter=*.DW_006_001_001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/006_DwClusterOther;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwUpgradeClst --gtest_repeat=$repeat_time --gtest_filter=*.DW_006_001_008 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/006_DwClusterOther;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwUpgradeClst --gtest_repeat=$repeat_time --gtest_filter=*.DW_006_001_012 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/006_DwClusterOther;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwDegradeClst --gtest_repeat=$repeat_time --gtest_filter=*.DW_006_002_003 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/006_DwClusterOther;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwClusterAge --gtest_repeat=$repeat_time --gtest_filter=*.DW_006_005_002 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/007_dwSecRelAva;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dwAudit --gtest_repeat=$repeat_time --gtest_filter=*.*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/007_dwSecRelAva;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dwSub --gtest_repeat=$repeat_time --gtest_filter=*.*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/014_DwSubBackpressProducer;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwSnSupportFlowControl --gtest_repeat=$repeat_time --gtest_filter=*003 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/014_DwSubBackpressProducer;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwSnSupportFlowControl --gtest_repeat=$repeat_time --gtest_filter=*009 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/014_DwSubBackpressProducer;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwSnSupportFlowControl --gtest_repeat=$repeat_time --gtest_filter=*018 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwAuto_inc --gtest_repeat=$repeat_time --gtest_filter=*001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwAuto_inc --gtest_repeat=$repeat_time --gtest_filter=*003 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwAuto_inc --gtest_repeat=$repeat_time --gtest_filter=*005 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwAuto_inc --gtest_repeat=$repeat_time --gtest_filter=*007 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwBitmap --gtest_repeat=$repeat_time --gtest_filter=*001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwRespool --gtest_repeat=$repeat_time --gtest_filter=*001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwRespool --gtest_repeat=$repeat_time --gtest_filter=*002 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/23_DirectWrite/015_DwSupportRBA;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DwRespool --gtest_repeat=$repeat_time --gtest_filter=*005 
