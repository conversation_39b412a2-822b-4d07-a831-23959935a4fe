if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)

    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then     cd $VAR_GMDB_HOME_PATH/test/sdv/;     sh build.sh euler -p testcases/03_DML/ || exit 2; fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcInsertEdge_Test;./InsertEdge --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_001_InsertEdge;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcInsertEdge_Test;./InsertEdge --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_018_InsertEdge;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcSetEdgeDstVertexByPkValue_Test;./SetEdgeDst --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_025_SetDstPkValue;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcSetEdgeDstVertexByPkValue_Test;./SetEdgeDst --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_031_SetDstPkValue;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcSetEdgeDstVertexPkName_Test;./SetEdgeDstPk --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_032_SetDstPkName;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcSetEdgeSrcVertexByPkValue_Test;./SetEdgeSrcVal --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_035_SetSrcPkValue;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcSetEdgeSrcVertexByPkValue_Test;./SetEdgeSrcVal --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_041_SetSrcPkValue;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/008_InsertEdgeSync/GmcSetEdgeSrcVertexPkName_Test;./SetEdgeSrcName --gtest_repeat=$repeat_time --gtest_filter=*.DML_008_042_SetSrcPkName;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/009_DeleteEdgeSync;./DeleteEdge --gtest_repeat=$repeat_time --gtest_filter=*.DML_009_001;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/010_QueryEdgeSync/001_GmcFetch;./GmcFetch_test --gtest_repeat=$repeat_time --gtest_filter=*.DML_010_001_001;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/010_QueryEdgeSync/001_GmcFetch;./GmcFetch_test --gtest_repeat=$repeat_time --gtest_filter=*.DML_010_001_006;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/010_QueryEdgeSync/002_GmcOpenVertexNeighbors;./OpenNeighbors --gtest_repeat=$repeat_time --gtest_filter=*.DML_010_002_001;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/010_QueryEdgeSync/003_GmcCloseVertexNeighbors;./CloseNeighbors --gtest_repeat=$repeat_time --gtest_filter=*.DML_010_003_001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/011_ScanVertexSync;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./DML_scanTest --gtest_repeat=$repeat_time --gtest_filter=DML_011_scanVertexTest.*002;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/011_ScanVertexSync;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./DML_scanTest --gtest_repeat=$repeat_time --gtest_filter=DML_011_scanVertexTest.*007;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/011_ScanVertexSync;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./DML_scanTest --gtest_repeat=$repeat_time --gtest_filter=DML_011_scanVertexTest.*008;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/012_HashIndexCreate;./HashCreate --gtest_repeat=$repeat_time --gtest_filter=*003;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/012_HashIndexCreate;./HashCreate --gtest_repeat=$repeat_time --gtest_filter=*004;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/012_HashIndexCreate;./HashCreate --gtest_repeat=$repeat_time --gtest_filter=*007;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;source ../autotest_env.sh;  cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/012_HashIndexCreate;./HashCreate --gtest_repeat=$repeat_time --gtest_filter=*008;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/013_HashIndexScan;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./HashScan  --gtest_repeat=$repeat_time --gtest_filter=*001;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/015_BatchDml;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./BatchDml_test --gtest_repeat=$repeat_time --gtest_filter=*001 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/015_BatchDml;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./BatchDml_test --gtest_repeat=$repeat_time --gtest_filter=*002 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/015_BatchDml;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./BatchDml_test --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/015_BatchDml;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./BatchDml_test --gtest_repeat=$repeat_time --gtest_filter=*047 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/015_BatchDml;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./BatchDml_test --gtest_repeat=$repeat_time --gtest_filter=*048 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/015_BatchDml;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./BatchDml_test --gtest_repeat=$repeat_time --gtest_filter=*049 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/018_FixedHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./FixedHpTrans --gtest_repeat=$repeat_time --gtest_filter=*001 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/018_FixedHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./FixedHpTrans --gtest_repeat=$repeat_time --gtest_filter=*002 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/018_FixedHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./FixedHpTrans --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/018_FixedHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./FixedHpTrans --gtest_repeat=$repeat_time --gtest_filter=*004 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/018_FixedHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./FixedHpTrans --gtest_repeat=$repeat_time --gtest_filter=*005 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/018_FixedHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./FixedHpTrans --gtest_repeat=$repeat_time --gtest_filter=*006 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*001 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*002 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*004 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*005 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*006 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*007 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/019_VariableHeapTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./VarHpTrans --gtest_repeat=$repeat_time --gtest_filter=*008 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/020_ReadAndCommitTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ReadAndCommit  --gtest_repeat=$repeat_time --gtest_filter=*019 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/020_ReadAndCommitTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ReadAndCommit  --gtest_repeat=$repeat_time --gtest_filter=*020 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/020_ReadAndCommitTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ReadAndCommit  --gtest_repeat=$repeat_time --gtest_filter=*021 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/020_ReadAndCommitTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ReadAndCommit  --gtest_repeat=$repeat_time --gtest_filter=*022 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/021_ConcurrencyTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ConcurrencyT --gtest_repeat=$repeat_time --gtest_filter=*001 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/021_ConcurrencyTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ConcurrencyT --gtest_repeat=$repeat_time --gtest_filter=*002 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/021_ConcurrencyTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ConcurrencyT --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/021_ConcurrencyTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./ConcurrencyT --gtest_repeat=$repeat_time --gtest_filter=*004 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/022_RedologTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./RedologT --gtest_repeat=$repeat_time --gtest_filter=*003 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/022_RedologTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./RedologT --gtest_repeat=$repeat_time --gtest_filter=*006 ;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/03_DML/022_RedologTransaction;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;  ./RedologT --gtest_repeat=$repeat_time --gtest_filter=*009 ;
