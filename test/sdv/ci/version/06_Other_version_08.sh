###
 # @Author: z00619264 <EMAIL>
 # @Date: 2024-02-27 20:36:19
 # @LastEditors: z00619264 <EMAIL>
 # @LastEditTime: 2024-06-17 11:20:51
 # @FilePath: \GMDBV5\test\sdv\ci\version\06_Other_version_08.sh
### 
if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)
 
    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
 
if [ -f /.dockerenv ];then     
    cd $VAR_GMDB_HOME_PATH/test/sdv/;
    sh build.sh euler -d GuangQi -p testcases/06_Other/059_CheckpointView || exit 2;
    sh build.sh euler --ignore_common -d GuangQi -p testcases/06_Other/060_PersistenceVertexUpgrade || exit 2;
    sh build.sh euler --ignore_common -d GuangQi -p testcases/06_Other/071_PstPlan || exit 2;
    sh build.sh euler --ignore_common -d GuangQi -p testcases/06_Other/072_YangModelPersist || exit 2;
    sh build.sh euler --ignore_common -d GuangQi -p testcases/06_Other/073_YangDdataPersistence || exit 2;    
fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1" -p demand_pst;
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/059_CheckpointView;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./CheckpointView2 --gtest_repeat=$repeat_time --gtest_filter=*003
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/059_CheckpointView;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./CheckpointView2 --gtest_repeat=$repeat_time --gtest_filter=*004
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/060_PersistenceVertexUpgrade;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./PerUpgrade --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/071_PstPlan;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./pstPlanBase --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/071_PstPlan;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./pstPlanBase --gtest_repeat=$repeat_time --gtest_filter=*049
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/072_YangModelPersist;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./YangModelPer --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/072_YangModelPersist;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./YangModelPer --gtest_repeat=$repeat_time --gtest_filter=*005
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/072_YangModelPersist;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./YangModelPer --gtest_repeat=$repeat_time --gtest_filter=*011
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/072_YangModelPersist;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./YangModelPer --gtest_repeat=$repeat_time --gtest_filter=*015
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/073_YangDdataPersistence;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./ddlPst --gtest_repeat=$repeat_time --gtest_filter=*004
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/073_YangDdataPersistence;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./edgePst --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/073_YangDdataPersistence;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./pstContain --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/06_Other/073_YangDdataPersistence;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./BigDataPer32 --gtest_repeat=$repeat_time --gtest_filter=*001
