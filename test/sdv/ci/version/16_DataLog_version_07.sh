if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)

    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then     cd $VAR_GMDB_HOME_PATH/test/sdv/;     sh build.sh euler -p testcases/16_DataLog/ || exit 2; fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;
cd $VAR_GMDB_HOME_PATH/test/sdv/tools/DatalogFile;     python3 datalog_version_file.py euler
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/044_SupTransientFinish;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./SupTrsFinishFun --gtest_repeat=$repeat_time --gtest_filter=*.*019
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/044_SupTransientFinish;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./SupTrsFinishFun --gtest_repeat=$repeat_time --gtest_filter=*.*025
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/045_DatalogMemoryOpi;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./PkisCcehOrChain --gtest_repeat=$repeat_time --gtest_filter=*012
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/046_SupMultipleNotJoin;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./SupMltNotJoinFu --gtest_repeat=$repeat_time --gtest_filter=*.*013
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/047_CountInFuncTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./countInFuncTest --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/047_CountInFuncTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./countInFuncTest --gtest_repeat=$repeat_time --gtest_filter=*017
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/047_CountInFuncTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./countInFuncTest --gtest_repeat=$repeat_time --gtest_filter=*029
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/048_datalogRunLinkLogTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dtlRunLinkT --gtest_repeat=$repeat_time --gtest_filter=*005
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/048_datalogRunLinkLogTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dtlRunLinkT --gtest_repeat=$repeat_time --gtest_filter=*013
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/049_HotPatchCompilation;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DLhotpatchcompile --gtest_repeat=$repeat_time --gtest_filter=*062
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/050_HotPatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatch_001 --gtest_repeat=$repeat_time --gtest_filter=*009
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/050_HotPatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatch_002 --gtest_repeat=$repeat_time --gtest_filter=*004
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/050_HotPatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatch_003 --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/050_HotPatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatch_004 --gtest_repeat=$repeat_time --gtest_filter=*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/050_HotPatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatch_005 --gtest_repeat=$repeat_time --gtest_filter=*005
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/050_HotPatch;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatch_005 --gtest_repeat=$repeat_time --gtest_filter=*013
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/051_DatalogHotPatchSpecificationConstraints;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DlgSpeCon2 --gtest_repeat=$repeat_time --gtest_filter=*133
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/051_DatalogHotPatchSpecificationConstraints;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DlgSpeCon2 --gtest_repeat=$repeat_time --gtest_filter=*134
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/051_DatalogHotPatchSpecificationConstraints;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./DlgSpeCon2 --gtest_repeat=$repeat_time --gtest_filter=*135
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/052_MemLimitSupportRollBack;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./SupRollBack1 --gtest_repeat=$repeat_time --gtest_filter=*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/052_MemLimitSupportRollBack;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./SupRollBack1 --gtest_repeat=$repeat_time --gtest_filter=*017
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/053_HotPatchDfx;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./HotPatchDfx --gtest_repeat=$repeat_time --gtest_filter=*.*001
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/053_HotPatchDfx;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./HotPatchDfx --gtest_repeat=$repeat_time --gtest_filter=*.*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/054_HotPatchUnload;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatchrol_002 --gtest_repeat=$repeat_time --gtest_filter=*.*003
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/054_HotPatchUnload;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./hotpatchrol_003 --gtest_repeat=$repeat_time --gtest_filter=*.*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/055_PatchCheckTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./PatchCheckTest --gtest_repeat=$repeat_time --gtest_filter=*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/056_HotPatchSupMemFoolproof;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./MemFoolproofFun --gtest_repeat=$repeat_time --gtest_filter=*.*016
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/057_dtlLoaderTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./dtlLoaderTest --gtest_repeat=$repeat_time --gtest_filter=*009
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/058_TablesSupRedo;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./TablesSupRedo1 --gtest_repeat=$repeat_time --gtest_filter=*002
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/058_TablesSupRedo;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./TablesSupRedo1 --gtest_repeat=$repeat_time --gtest_filter=*009
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/058_TablesSupRedo;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./TablesSupRedo1 --gtest_repeat=$repeat_time --gtest_filter=*010
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/072_GmprecompilerEndhance;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./patchText --gtest_repeat=$repeat_time --gtest_filter=*004 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/073_LoadSoTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./LoadSoTest --gtest_repeat=$repeat_time --gtest_filter=*001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/073_LoadSoTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./LoadSoTest --gtest_repeat=$repeat_time --gtest_filter=*014 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/074_PrePostTest;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./PrePostTest1 --gtest_repeat=$repeat_time --gtest_filter=*001 
cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/16_DataLog/075_DtlSupOutTableNotDistribute;source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;./OutTableFun --gtest_repeat=$repeat_time --gtest_filter=*.*013
