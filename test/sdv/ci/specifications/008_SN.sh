if [ $# -lt 1 ];then    exit 1 ;    fi
if [ X"${VAR_GMDB_HOME_PATH}" == X ];then
    SHELL_PATH=$(cd $(dirname $0);pwd)
    VAR_GMDB_HOME_PATH=$SHELL_PATH/../../../../
fi
if [ -f /.dockerenv ];then     cd $VAR_GMDB_HOME_PATH/test/sdv/;     sh build.sh euler -p testcases/11_BasicSpecifications/ || exit 2; fi
if [ X"$2" == "X" ];then  repeat_time=1 ;  else  repeat_time=$2 ;    fi
cd $VAR_GMDB_HOME_PATH/test/sdv/tools;./deploy.sh "$1";

source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*001
source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*002
source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*003
source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*004
source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*005
source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*006
source $VAR_GMDB_HOME_PATH/test/sdv/autotest_env.sh;cd $VAR_GMDB_HOME_PATH/test/sdv/testcases/11_BasicSpecifications/001_SpecificationBaseline_V5/008_SN;./BasicLineSN --gtest_filter=*007
