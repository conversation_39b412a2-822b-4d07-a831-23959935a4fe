/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: GaussDB RD SQL交互式工具
 * Author: guopanpan
 * Create: 2023-07-13
 */
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <sys/stat.h>
#include "securec.h"
#include "rdcli.h"

// 去除字符串头尾的空白字符
void RdStrTrim(char *input, char *output, uint32_t outputSize)
{
    if (input == NULL || strlen(input) <= 0) {
        return;
    }

    int32_t len = strlen(input);
    int32_t startIdx = 0;
    int32_t endIdx = len - 1;
    while (input[startIdx] == ' ' || input[startIdx] == '\t') {
        if (startIdx >= len - 1) {
            break;
        }
        startIdx++;
    }
    while (input[endIdx] == ' ' || input[endIdx] == '\t') {
        if (endIdx <= 0) {
            break;
        }
        endIdx--;
    }
    if (startIdx > endIdx) {
        return;
    }

    errno_t err = strncpy_s(output, outputSize, input + startIdx, endIdx - startIdx + 1);
    if (err != EOK) {
        RD_LOG_ERROR("Unable to copy string.\n");
        return;
    }
    output[endIdx - startIdx + 1] = '\0';
}

// 准备用户命令
char *RdQueryPrepare(char *input)
{
    char *query = (char *)malloc(RD_MAX_BUFF_SIZE);
    if (query == NULL) {
        RD_LOG_ERROR("Unable to malloc buffer.\n");
        return NULL;
    }
    (void)memset_s(query, RD_MAX_BUFF_SIZE, '\0', RD_MAX_BUFF_SIZE);

    char *prompt = RdOptionGetPromptCmd();
    if (!RdCmdEnablePrompt()) {
        prompt = (char *)"";
    }
    RdStrTrim(input, query, RD_MAX_BUFF_SIZE);
    uint32_t offset = 0;
    while (strlen(query) >= 1 && query[strlen(query) - 1] != ';') {
        char *line = RdReadLine(prompt);
        if (line == NULL || strlen(line) <= 0) {
            free(line);
            continue;
        }

        // 获取buffer的长度，新增内容追加在buffer后面
        offset = strlen(query);
        query[offset++] = ' ';
        RdStrTrim(line, query + offset, RD_MAX_BUFF_SIZE - offset);
        free(line);
    }
    return query;
}

// 释放用户命令
void RdQueryFinalize(char *query)
{
    free(query);
}

// 轮询等待并处理用户命令
int32_t RdExecuteLoop(void *conn)
{
    RdDbEntityT db = RdDbEntityGet();
    RdHistoryInit();

    while (true) {
        char *prompt = RdOptionGetPromptCli();
        if (!RdCmdEnablePrompt()) {
            prompt = (char *)"";
        }
        char *input = RdReadLine(prompt);
        if (input == NULL || strlen(input) <= 0) {
            free(input);
            continue;
        }
        if (strcmp(input, ".q") == 0 || strcmp(input, ".quit") == 0) {
            RdHistoryAdd(input);
            free(input);
            break;
        }
        if (strncmp(input, ".", 1) == 0) {
            RdHistoryAdd(input);
            RdCmdEntry(input);
            free(input);
            continue;
        }

        char *queryString = RdQueryPrepare(input);
        if (queryString == NULL) {
            RD_LOG_ERROR("Unable to prepare command.\n");
            free(input);
            continue;
        }
        RdHistoryAdd(queryString);
        if (RdCmdEnableEcho()) {
            printf("%s\n", queryString);
        }
        db.rdExecute(conn, queryString);
        if (RdCmdEnableChanges()) {
            db.rdChanges(conn);
        }
        free(input);
        RdQueryFinalize(queryString);
    }
    return RD_OK;
}

// 通过非交互式命令传入查询指令（在支持持久化能力前，无实际使用场景）
int32_t RdExecuteOnce(void *conn, int32_t argc, char *argv[])
{
    char queryString[RD_MAX_SQL_LEN] = {0};
    for (int32_t i = 1; i < argc; i++) {
        (void)strcat_s(queryString, sizeof(queryString), argv[i]);
        (void)strcat_s(queryString, sizeof(queryString), " ");
    }
    if (RdCmdEnableEcho()) {
        printf("%s\n", queryString);
    }

    RdDbEntityT db = RdDbEntityGet();
    db.rdExecute(conn, queryString);
    if (RdCmdEnableChanges()) {
        db.rdChanges(conn);
    }
    return RD_OK;
}

int32_t RdMain(int32_t argc, char *argv[])
{
    RdOptionInit();
    RdOptionParse(argc, argv);
    int32_t ret = RdCmdCreateMgr();
    if (ret != RD_OK) {
        return ret;
    }
    printf("%s\n", RD_WELCOME_TEXT);

    void *conn = NULL;
    RdDbEntityT db = RdDbEntityGet();
    ret = db.rdRegisterCmd();
    if (ret != RD_OK) {
        return ret;
    }

    ret = db.rdConnect(&conn);
    if (ret != RD_OK) {
        return ret;
    }
    ret = RdExecuteLoop(conn);
    if (ret != RD_OK) {
        return ret;
    }
    ret = db.rdDisconn(conn);
    if (ret != RD_OK) {
        return ret;
    }

    RdCmdDestroyMgr();
    return ret;
}

int32_t main(int32_t argc, char *argv[])
{
    return RdMain(argc, argv);
}
