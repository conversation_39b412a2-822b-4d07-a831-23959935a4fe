/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: GaussDB RD 交互式工具
 * Author: guopanpan
 * Create: 2023-07-13
 */
#ifndef RDCLI_H
#define RDCLI_H 1

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <assert.h>
#include "securec.h"

#ifndef ARCH
#define ARCH "aarch64"
#endif

#ifndef RD_ENABLE_DEBUG
#define RD_ENABLE_DEBUG 0
#endif

#if RD_ENABLE_DEBUG
#define RD_DEBUG(format, ...) printf(format, ##__VA_ARGS__)
#else
#define RD_DEBUG(format, ...)
#endif

#define RD_ASSERT(exp) assert(exp)

#define RD_DB_NAME "testdb"
#define RD_WELCOME_TEXT "Enter \".help\" for usage hints."
#define RD_CONN_TEXT_FMT "Connected to GaussDB_RD using configuration file \"%s\".\n"

#define RD_SPLIT_80 "-------------------------------------------------------------------------------"
#define RD_SPLIT    RD_SPLIT_80 RD_SPLIT_80 RD_SPLIT_80

#define RD_MAX_SQL_LEN (512 * 1024)
#define RD_MAX_STR_LEN 512
#define RD_MAX_ROW_NUM 1024
#define RD_MAX_COL_NUM 1024
#define RD_MAX_PATH_LEN 1024

#define RD_MAX_NAME_LEN 128
#define RD_MAX_BUFF_SIZE (1024 * 1024)

#define RD_ELEMENT_NUM(x) (sizeof(x) / sizeof((x)[0]))

#define RD_OK 0
#define RD_ERR 1

#define RD_ENABLE_LINENO 0

#if RD_ENABLE_LINENO == 1
#define RD_LOG_ERROR(format, ...) printf("[ERROR] [%s:%d] " format, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#else
#define RD_LOG_ERROR(format, ...) printf("[ERROR] " format, ##__VA_ARGS__)
#endif

#define RD_RETURN_IFERR(ret, errMsg) \
    do { \
        if ((ret) != RD_OK) { \
            if ((errMsg) == NULL) { RD_LOG_ERROR("" #ret " = %d\n", ret); } \
            else { RD_LOG_ERROR("" #ret " = %d, %s", ret, errMsg); } \
            return ret; \
        } \
    } while (0)

// 公共工具
int32_t RdGetDefaultCfgFilePath(char *filePath, int32_t size);
int32_t RdGetSqlCfgFilePath(char *filePath, int32_t size, const char *storage);
bool RdFileIsExist(const char *filePath);
bool RdDirIsExist(const char *dirPath);
void RdStrTrim(char *input, char *output, uint32_t outputSize);

char *RdTrans2Lowercase(char *x);
char *RdTrans2Uppercase(char *x);
int64_t RdGetNumFromStr(char *input);

// 命令行入参
void RdOptionInit();
void RdOptionParse(int argc, char *argv[]);

char *RdOptionGetModel();
char *RdOptionGetCfgFilePath();
char *RdOptionGetPromptCli();
char *RdOptionGetPromptCmd();
char *RdOptionGetDbName();

// 用户输入命令处理
char *RdQueryPrepare(char *input);
void RdQueryFinalize(char *query);

// 运行时命令
typedef struct RdCommand RdCommandT;
typedef void (*RdCmdEntryT)(char *input, RdCommandT *command);

typedef enum {
    RD_CMD_TYPE_VALUE = 0,
    RD_CMD_TYPE_ACTION,
    RD_CMD_TYPE_BUTT
} RdCmdTypeE;

struct RdCommand {
    RdCmdEntryT entry;
    const char *longCmd;
    const char *args;
    const char *description;
    RdCmdTypeE type;
    uint32_t value;
};

int32_t RdCmdCreateMgr();
void RdCmdDestroyMgr();
int32_t RdCmdRegister(RdCommandT *commands, uint32_t size);

void RdCmdEntry(char *input);
RdCommandT *RdCmdGet(const char *cmd);

uint32_t RdCmdGetWidth();
bool RdCmdEnableEcho();
bool RdCmdEnableChanges();
bool RdCmdDisplayNullVal();
bool RdCmdEnablePrompt();

// 多模入口
typedef struct {
    const char *model;
    int32_t (*rdConnect)(void **);
    int32_t (*rdDisconn)(void *);
    int32_t (*rdExecute)(void *, char *);
    int32_t (*rdChanges)(void *);
    int32_t (*rdRegisterCmd)(void);
} RdDbEntityT;

RdDbEntityT RdDbEntityGet();
bool RdDbModelIsRegistered(char *model);

// 建议优化为编译时指定模型
#define RDCLI_SQL
#define RDCLI_GQL

// SQL模型
#ifdef RDCLI_SQL
int32_t RdSqlConnect(void **);
int32_t RdSqlDisconn(void *);
int32_t RdSqlExecute(void *, char *);
int32_t RdSqlChanges(void *);
int32_t RdSqlRegisterCmd();
#endif /* end of RDCLI_SQL */

// TS模型
#ifdef RDCLI_TS
int32_t RdTsConnect(void **);
int32_t RdTsDisconn(void *);
int32_t RdTsExecute(void *, char *);
int32_t RdTsChanges(void *);
int32_t RdTsRegisterCmd();
#endif /* end of RDCLI_TS */

// GQL模型
#ifdef RDCLI_GQL
int32_t RdGqlConnect(void **);
int32_t RdGqlDisconn(void *);
int32_t RdGqlExecute(void *, char *);
int32_t RdGqlChanges(void *);
int32_t RdGqlRegisterCmd();
#endif /* end of RDCLI_GQL */

// 列表
typedef uint8_t** RdListT;
RdListT RdListCreate(uint32_t capacity);
void RdListDestroy(RdListT list);
void *RdListGet(RdListT list, uint32_t index);
int32_t RdListSet(RdListT *list, uint32_t index, const void *value, uint32_t length);
int32_t RdListAppend(RdListT *list, const void *value, uint32_t length);
uint32_t RdListCapacity(RdListT list);
uint32_t RdListElementCount(RdListT list);

// 读取用户输入
void RdHistoryInit();
void RdHistoryAdd(const char *history);
char *RdReadLine(const char *prompt);

#endif /* RDCLI_H */
