/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 解析和处理运行时命令
 * Author: guopanpan
 * Create: 2023-07-13
 */
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <inttypes.h>
#include <sys/stat.h>
#include "securec.h"
#include "rdcli.h"

void RdCmdUsage(char *input, RdCommandT *command);
void RdCmdShow(char *input, RdCommandT *command);
void RdCmdSetWidth(char *input, RdCommandT *command);
void RdCmdSetEcho(char *input, RdCommandT *command);
void RdCmdSetChanges(char *input, RdCommandT *command);
void RdCmdSetNullVal(char *input, RdCommandT *command);
void RdCmdSetPrompt(char *input, RdCommandT *command);

typedef struct RdCommandMgr {
    RdCommandT *commands;
    uint32_t numItem;
} RdCommandMgrT;

RdCommandMgrT g_commandMgr = {0};

// 出于可读性考虑，结构体数组初始化部分成员对齐，且同一个命令不换行
RdCommandT g_commonCmds[] = {
    // 配置函数，命令，帮助信息，类型，值
    {RdCmdUsage,      ".help",    NULL,    "print help information",                          RD_CMD_TYPE_ACTION,  0},
    {RdCmdShow,       ".show",    NULL,    "show config value",                               RD_CMD_TYPE_ACTION,  0},
    {RdCmdSetWidth,   ".width",   "<num>", "set width, 0 means auto width",                   RD_CMD_TYPE_VALUE,   0},
    {RdCmdSetEcho,    ".echo",    "<0|1>", "set command echo, 0 means off, 1 means on",       RD_CMD_TYPE_VALUE,   0},
    {RdCmdSetChanges, ".changes", "<0|1>", "set changes echo, 0 means off, 1 means on",       RD_CMD_TYPE_VALUE,   0},
    {RdCmdSetNullVal, ".nullval", "<0|1>", "set null value display, 0 means off, 1 means on", RD_CMD_TYPE_VALUE,   1},
    {RdCmdSetPrompt,  ".prompt",  "<0|1>", "set client prompt, 0 means off, 1 means on",      RD_CMD_TYPE_VALUE,   1},
};

// 创建命令管理句柄
int32_t RdCmdCreateMgr()
{
    if (g_commandMgr.commands != NULL || g_commandMgr.numItem != 0) {
        RD_LOG_ERROR("Cmd manager has been created.\n");
        return RD_ERR;
    }

    uint32_t size = sizeof(g_commonCmds);
    RdCommandT *cmds = (RdCommandT *)malloc(size);
    if (cmds == NULL) {
        RD_LOG_ERROR("Unable to malloc cmds.\n");
        return RD_ERR;
    }
    errno_t err = memcpy_s(cmds, size, g_commonCmds, size);
    if (err != EOK) {
        RD_LOG_ERROR("Unable to copy commands.\n");
        return RD_ERR;
    }

    uint32_t numItem = sizeof(g_commonCmds) / sizeof(g_commonCmds[0]);
    g_commandMgr = (RdCommandMgrT) {
        .commands = cmds,
        .numItem = numItem,
    };
    return RD_OK;
}

// 销毁命令管理句柄
void RdCmdDestroyMgr()
{
    if (g_commandMgr.commands == NULL) {
        return;
    }

    free(g_commandMgr.commands);
    g_commandMgr.commands = NULL;
    g_commandMgr.numItem = 0;
}

// 注册命令
int32_t RdCmdRegister(RdCommandT *commands, uint32_t size)
{
    if (g_commandMgr.commands == NULL || g_commandMgr.numItem == 0) {
        RD_LOG_ERROR("Cmd manager is null.\n");
        return RD_ERR;
    }

    uint32_t oldSize = g_commandMgr.numItem * sizeof(RdCommandT);
    uint32_t newSize = oldSize + size;
    RdCommandT *newCmds = (RdCommandT *)malloc(newSize);
    if (newCmds == NULL) {
        RD_LOG_ERROR("Unable to malloc commands.\n");
        return RD_ERR;
    }
    errno_t err = memcpy_s(newCmds, oldSize, g_commandMgr.commands, oldSize);
    if (err != EOK) {
        RD_LOG_ERROR("Unable to copy old commands.\n");
        free(newCmds);
        return RD_ERR;
    }

    uint32_t oldNumItem = g_commandMgr.numItem;
    uint32_t newNumItem = oldNumItem + size / sizeof(RdCommandT);
    for (int32_t i = oldNumItem, j = 0; i < newNumItem; i++, j++) {
        newCmds[i] = commands[j];
    }

    free(g_commandMgr.commands);
    g_commandMgr = (RdCommandMgrT) {
        .commands = newCmds,
        .numItem = newNumItem,
    };
    return RD_OK;
}

// 获取命令
RdCommandT *RdCmdGet(const char *cmd)
{
    RdCommandT *cmds = g_commandMgr.commands;
    int32_t numItems = g_commandMgr.numItem;
    for (int32_t i = 0; i < numItems; i++) {
        if (strcmp(cmd, cmds[i].longCmd) == 0) {
            return &(cmds[i]);
        }
    }
    return NULL;
}

// 设置整形值
void RdCmdSetNum(char *input, char *longCmd, RdCommandT *command)
{
    int64_t changes = RdGetNumFromStr(input);
    if (changes != 0 && changes != 1) {
        RD_LOG_ERROR("Unable to set %s: %" PRId64 ".\n", longCmd, changes);
        return;
    }
    command->value = changes;
    printf("%s = %" PRIu32 "\n", longCmd,  command->value);
}

// 执行命令
void RdCmdEntry(char *input)
{
    RdCommandT *cmds = g_commandMgr.commands;
    int32_t numItems = g_commandMgr.numItem;
    for (int32_t i = 0; i < numItems; i++) {
        if (strncmp(input, cmds[i].longCmd, strlen(cmds[i].longCmd)) == 0) {
            cmds[i].entry(input, &cmds[i]);
            return;
        }
    }
    RD_LOG_ERROR("Unable to parse command: %s\n", input);
}

// 显示帮助信息
void RdCmdUsage(char *input, RdCommandT *command)
{
    RdCommandT *cmds = g_commandMgr.commands;
    int32_t numItems = g_commandMgr.numItem;
    for (int32_t i = 0; i < numItems; i++) {
        char cmdStr[RD_MAX_STR_LEN] = {0};
        (void)strcat_s(cmdStr, sizeof(cmdStr), cmds[i].longCmd);
        if  (cmds[i].args != NULL) {
            (void)strcat_s(cmdStr, sizeof(cmdStr), " ");
            (void)strcat_s(cmdStr, sizeof(cmdStr), cmds[i].args);
        }
        printf("%-32s %s\n", cmdStr, cmds[i].description);
    }
    printf("%-32s %s\n", ".quit", "exit the current client");
}

// 显示用户配置
void RdCmdShow(char *input, RdCommandT *command)
{
    RdCommandT *cmds = g_commandMgr.commands;
    int32_t numItems = g_commandMgr.numItem;
    for (int32_t i = 0; i < numItems; i++) {
        if (cmds[i].type == RD_CMD_TYPE_VALUE) {
            printf("%s = %" PRIu32 "\n", cmds[i].longCmd, cmds[i].value);
        }
    }
}

// 表格宽度
void RdCmdSetWidth(char *input, RdCommandT *command)
{
    int64_t width = RdGetNumFromStr(input);
    if (width < 0) {
        RD_LOG_ERROR("Unable to set width: %" PRId64 ".\n", width);
        return;
    }
    command->value = width;
    printf("width = %" PRIu32 "\n", command->value);
}

uint32_t RdCmdGetWidth()
{
    RdCommandT *command = RdCmdGet(".width");
    if (command == NULL) {
        RD_LOG_ERROR("Unable to get value of width.\n");
        return 16;
    }
    return command->value;
}

// 命令回显
void RdCmdSetEcho(char *input, RdCommandT *command)
{
    int64_t echo = RdGetNumFromStr(input);
    if (echo != 0 && echo != 1) {
        RD_LOG_ERROR("Unable to set echo: %" PRId64 ".\n", echo);
        return;
    }
    command->value = echo;
    printf("echo = %" PRIu32 "\n", command->value);
}

bool RdCmdEnableEcho()
{
    RdCommandT *command = RdCmdGet(".echo");
    if (command == NULL) {
        RD_LOG_ERROR("Unable to get value of echo.\n");
        return false;
    }
    return command->value == 1;
}

// 影响行数回显
void RdCmdSetChanges(char *input, RdCommandT *command)
{
    int64_t changes = RdGetNumFromStr(input);
    if (changes != 0 && changes != 1) {
        RD_LOG_ERROR("Unable to set changes: %" PRId64 ".\n", changes);
        return;
    }
    command->value = changes;
    printf("changes = %" PRIu32 "\n", command->value);
}

bool RdCmdEnableChanges()
{
    RdCommandT *command = RdCmdGet(".changes");
    if (command == NULL) {
        RD_LOG_ERROR("Unable to get value of changes.\n");
        return 0;
    }
    return command->value == 1;
}

// 空值显示
void RdCmdSetNullVal(char *input, RdCommandT *command)
{
    int64_t num = RdGetNumFromStr(input);
    if (num != 0 && num != 1) {
        RD_LOG_ERROR("Unable to set null value display: %" PRId64 ".\n", num);
        return;
    }
    command->value = num;
    printf("nullval = %" PRIu32 "\n", command->value);
}

bool RdCmdDisplayNullVal()
{
    RdCommandT *command = RdCmdGet(".nullval");
    if (command == NULL) {
        RD_LOG_ERROR("Unable to get value of nullval.\n");
        return 0;
    }
    return command->value == 1;
}

// 客户端提示符
void RdCmdSetPrompt(char *input, RdCommandT *command)
{
    int64_t num = RdGetNumFromStr(input);
    if (num != 0 && num != 1) {
        RD_LOG_ERROR("Unable to set client prompt: %" PRId64 ".\n", num);
        return;
    }
    command->value = num;
    printf("prompt = %" PRIu32 "\n", command->value);
}

bool RdCmdEnablePrompt()
{
    RdCommandT *command = RdCmdGet(".prompt");
    if (command == NULL) {
        RD_LOG_ERROR("Unable to get value of prompt.\n");
        return 0;
    }
    return command->value == 1;
}
