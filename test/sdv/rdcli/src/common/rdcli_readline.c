/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 读取用户输入
 * Author: guopanpan
 * Create: 2024-09-19
 */
#ifndef RDCLI_USE_READLINE
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <sys/stat.h>
#include "securec.h"
#include "rdcli.h"

#define RD_HISTFILE_MAX_LEN 1000
#define RD_FILE_PATH_LEN 512
char g_histfile[RD_FILE_PATH_LEN] = {0};

// 初始化历史命令
void RdHistoryInit()
{
}

// 添加并持久化历史命令
void RdHistoryAdd(const char *history)
{
    (void)history;
}

char *RdReadLine(const char *prompt)
{
    printf("%s", prompt);
    char *buffer = (char *)malloc(RD_MAX_BUFF_SIZE);
    if (fgets(buffer, RD_MAX_BUFF_SIZE, stdin) == NULL) {
        RD_LOG_ERROR("Unable to read line.");
        return NULL;
    }
    uint32_t bufLen = strlen(buffer);
    if (bufLen > 0) {
        buffer[bufLen - 1] = '\0';
    }
    return buffer;
}

#endif /* end of RDCLI_USE_READLINE */
