/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 File Name: 014_Support_PATH_search_with_condition.cpp
 Description: ConditionalTrigger测试
 Author: tianyihui t30050699
 Create: 2023-10-08
 *****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>

#include "gtest/gtest.h"
#include "gmc_errno.h"

#include "t_rd_adapt.h"
#include "t_rd_assert.h"
#include "t_rd_sn.h"
#include "t_rd_common.h"
#include "gmc_gql.h"
#include "path_search.h"
#include "../001_vertexlabel/vertex_util.h"

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
using namespace std;

class ConditionalTrigger : public testing::Test {
protected:
    static void SetUpTestCase()
    {

        system("sh ${TEST_HOME}/tools/stop.sh -f");

        //初始化配置文件
        InitCfg();

        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
       
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh  recover");
        GmcDetachAllShmSeg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ConditionalTrigger::SetUp()
{
    // 创建同步客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //预置待订阅的path
    InitAllLabels(stmt);

    AW_FUN_Log(LOG_STEP, "path创建完成.");

}

void ConditionalTrigger::TearDown()
{
    // 释放被定义的复杂path/边/点, 与InitAllLabels配对使用
    RemoveAllLabels(stmt);

    // 关闭同步客户端连接
    int ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 用例001：创建触发器，UPDATED_ANY(  )包含单个表字段
TEST_F(ConditionalTrigger,GQL_014_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_01
        ON REPLACE v1
        WHEN UPDATED_ANY(c7) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_01;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例002：创建触发器，UPDATED_ANY(  )包含多个表字段
TEST_F(ConditionalTrigger,GQL_014_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_02
        ON REPLACE v2
        WHEN UPDATED_ANY(c1,c2,c3,c4,c5) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_02;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例003：创建触发器，同时包含多个有条件action
TEST_F(ConditionalTrigger,GQL_014_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_03
        ON REPLACE v2
        WHEN UPDATED_ANY(c1) DO SEARCH path1
        WHEN UPDATED_ANY(c2,c3) DO SEARCH path1
        WHEN UPDATED_ANY(c4,c5) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_03;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例004：创建触发器，同时包含有条件和无条件action
TEST_F(ConditionalTrigger,GQL_014_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_04
        ON REPLACE v2
        WHEN UPDATED_ANY(c1,c2,c3,c4,c5) DO SEARCH path1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_04;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例005：创建触发器，只包含无条件action
TEST_F(ConditionalTrigger,GQL_014_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_05
        ON REPLACE v2
        WHEN TRUE DO SEARCH path2
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_05;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例006：创建触发器，只包含有条件action
TEST_F(ConditionalTrigger,GQL_014_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_06
        ON REPLACE v2
        WHEN UPDATED_ANY(c4,c5) DO SEARCH path3
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_06;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例007：创建触发器，有条件情况包含多个path_name
TEST_F(ConditionalTrigger,GQL_014_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_07
        ON REPLACE v1
        WHEN UPDATED_ANY(c4,c5) DO SEARCH path1,path2,path3
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_07;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例008：创建触发器，有条件情况只包含1个path_name
TEST_F(ConditionalTrigger,GQL_014_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_08
        ON REPLACE v1
        WHEN UPDATED_ANY(c4,c5) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER trigger_08;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例009：创建触发器，UPDATED_ANY(  )包含的表字段不存在（名字不对）
TEST_F(ConditionalTrigger,GQL_014_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_09
        ON REPLACE v1
        WHEN UPDATED_ANY(c9) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINE_COLUMN, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}


// 用例010：创建触发器，UPDATED_ANY(  )包含的表字段存在但不属于触发器关联的table
TEST_F(ConditionalTrigger,GQL_014_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_10
        ON REPLACE v1
        WHEN UPDATED_ANY(c8) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINE_COLUMN, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例011：创建触发器，关联的table 不在关联的Path上
TEST_F(ConditionalTrigger,GQL_014_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger_10
        ON REPLACE v3
        WHEN UPDATED_ANY(c1) DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例012：创建触发器，关键字WHEN/TRUE/SEARCH缺失
TEST_F(ConditionalTrigger,GQL_014_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER trigger12_01
        ON REPLACE v3
        UPDATED_ANY(c1) DO SEARCH path3
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger12_02
        ON REPLACE v3
        WHEN UPDATED_ANY(c1) DO path3
    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger12_03
        ON REPLACE v3
        WHEN DO SEARCH path3

    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger12_04
        ON REPLACE v3
        DO path3

    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger04);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例013：创建触发器，WHEN/TRUE/SEARCH单个关键字大小写混合
TEST_F(ConditionalTrigger,GQL_014_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER trigger13_01
        ON REPLACE v3
        When UPDATED_ANY(c1) DO SEARCH path3
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger13_02
        ON REPLACE v3
        WHEN UPDATED_ANY(c1) DO Search path3
    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger13_03
        ON REPLACE v3
        WHEN True DO SEARCH path3

    )";
    //创建触发器
    ret = GmcExecGql(stmt, createTrigger03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例014：创建触发器，action字段缺失
TEST_F(ConditionalTrigger,GQL_014_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER trigger14_01
        ON REPLACE v3
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例015：创建触发器，关键字WHEN/TRUE/SEARCH拼写错误
TEST_F(ConditionalTrigger,GQL_014_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER trigger15_01
        ON REPLACE v3
        WHENN UPDATED_ANY(c1) DO SEARCH path3
    )";

    //创建触发器,WHEN拼写错误
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger15_02
        ON REPLACE v3
        WHEN UPDATED_ANY(c1) DO SEARCHH path3
    )";

    //创建触发器,SEARCH拼写错误
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger15_03
        ON REPLACE v3
        WHEN TRUEE DO SEARCH path3

    )";
    //创建触发器,TRUEE拼写错误
    ret = GmcExecGql(stmt, createTrigger03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例016：修改部分表字段的值使生成path实例，触发path搜索，查看返回的path实例是否符合预期
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1,1),(2,2,2)
 * 5. v2插入(1,1,1),(2,2,2)
 * 6. 修改v2数据为(1,3,1),(2,3,2)
 * 7. 接收pathNum,应为2
 */
TEST_F(ConditionalTrigger,GQL_014_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 2, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 发送订阅请求
    const char *subGQL = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};

    ret = GmcSubscribeComplexPath(stmt, subGQL, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t16_2 ON REPLACE v2 WHEN UPDATED_ANY(c1,c2) DO SEARCH path1;
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    // replace v1 (1,1,1),(2,2,2)
    BatchReplaceDataVertexForPath(conn, stmt, "v1", 2);
    
    // replace v2 (1,1,1),(2,2,2)
    BatchReplaceDataVertexForPath(conn, stmt, "v2", 2);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM = 2;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathName = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //modify v2 (1,2,1),(2,3,1)
    uint32_t c1PropArr[] = {1, 2};
    uint32_t changePropArr[] = {3, 3};
    
    BatchReplaceVertexForPathWithC2(conn, stmt, "v2", 2, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 4;
    while (pathNum != EXPECT_PATH_NUM01) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathName = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t16_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例017：修改已定义的触发器未涉及的字段的值，未触发path搜索，查看返回的path实例是否符合预期
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1,1),(2,2,2)
 * 5. v2插入(1,1,1),(2,2,2)
 * 6. v3插入(1,1,1),(2,2,2)
 * 7. 修改v3数据为(1,3,1),(2,3,1)
 * 6. 接收pathNum,应为0
 */
TEST_F(ConditionalTrigger,GQL_014_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 2, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 发送订阅请求
    const char *subGQL = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};

    ret = GmcSubscribeComplexPath(stmt, subGQL, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t17_1 ON REPLACE v1 WHEN TRUE DO SEARCH path1;
        CREATE TRIGGER t17_2 ON REPLACE v2 WHEN UPDATED_ANY(c1,c2) DO SEARCH path1;
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    
    // replace v2 (1,1,1),(2,2,2)
    BatchReplaceDataVertexForPath(conn, stmt, "v2", 2);

    // replace v3 (1,1,1),(2,2,2)
    BatchReplaceDataVertexForPath(conn, stmt, "v3", 2);

    // replace v1 (1,1,1),(2,2,2)
    BatchReplaceDataVertexForPath(conn, stmt, "v1", 2);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM = 2;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathName = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //modify v3 (1,3,1),(2,3,1)
    uint32_t c1PropArr[] = {1, 2};
    uint32_t changePropArr[] = {3, 3};
    
    BatchReplaceVertexForPathWithC2(conn, stmt, "v3", 2, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 0;
    while (pathNum - EXPECT_PATH_NUM != EXPECT_PATH_NUM01) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathName = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum - EXPECT_PATH_NUM);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t17_1;
        DROP TRIGGER t17_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例018：一个Path只被多个订阅者通过一个订阅通道订阅，修改部分/全部表字段的值使生成path实例，触发path搜索，查看返回的path实例是否符合预期
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、2个订阅者、2个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v2插入v2 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 5. v3插入v3 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 6. v4插入v4 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 7. v1插入v1 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 8. 接收pathNum01应为5, pathNum02应为10
 * 9. 修改v2 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)->(1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5) modify 4 records
 * 10. 接收pathNum01应为14, pathNum02应为18
 * 11. 修改v2 (1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5)->(1,1,3),(2,2,3),(3,3,3),(4,4,3),(5,5,3) modify 4 records
 * 12. 接收pathNum01应为22, pathNum02应为26
 * 13. 修改v2 (1,1,3),(2,2,3),(3,3,3),(4,4,3),(5,5,3)->(1,3,3),(2,3,3),(3,3,3),(4,3,3),(5,3,3) modify 4 records
 * 14. 接收pathNum01应为30, pathNum02应为34
 */ 
TEST_F(ConditionalTrigger,GQL_014_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath1
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t18_1 ON REPLACE v2 WHEN UPDATED_ANY(c2,c3) DO SEARCH path3;
        CREATE TRIGGER t18_2 ON REPLACE v1 WHEN TRUE DO SEARCH path3;
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    uint32_t c1PropArr[] = {1, 2, 3, 4, 5};
    uint32_t changePropArr[] = {3, 3, 3, 3, 3};
    
    //replace v2 (1,1),(2,2),(3,3),(4,4),(5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v2", 5);

    //replace v3 (1,1),(2,2),(3,3),(4,4),(5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v3", 5);

    //replace v4 (1,1),(2,2),(3,3),(4,4),(5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v4", 5);

    //replace v1 (1,1),(2,2),(3,3),(4,4),(5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v1", 5);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t EXPECT_PATH_NUM01 = 5;
    uint32_t EXPECT_PATH_NUM02 = 5;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d\n", pathNum01, pathNum02);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d pathNum02 = %d\n", pathNum01, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);

    // replace v2 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)->(1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5) modify 4 records
    BatchReplaceVertexForPathWithC2(conn, stmt, "v2", 5, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    EXPECT_PATH_NUM01 = 9;
    EXPECT_PATH_NUM02 = 9;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d\n", pathNum01, pathNum02);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d pathNum02 = %d\n", pathNum01, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);

    // replace v2 (1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5)->(1,1,3),(2,2,3),(3,3,3),(4,4,3),(5,5,3) modify 4 records
    BatchReplaceVertexForPathWithC3(conn, stmt, "v2", 5, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    EXPECT_PATH_NUM01 = 13;
    EXPECT_PATH_NUM02 = 13;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d\n", pathNum01, pathNum02);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d pathNum02 = %d\n", pathNum01, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);

    // replace v2 (1,1,3),(2,2,3),(3,3,3),(4,4,3),(5,5,3)->(1,3,3),(2,3,3),(3,3,3),(4,3,3),(5,3,3) modify 4 records
    BatchReplaceVertexForPathWithC2AndC3(conn, stmt, "v2", 5, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    EXPECT_PATH_NUM01 = 17;
    EXPECT_PATH_NUM02 = 17;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d\n", pathNum01, pathNum02);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d pathNum02 = %d\n", pathNum01, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t18_1;
        DROP TRIGGER t18_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除用户1发送订阅请求subByUser1OnPath1
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除用户1发送订阅请求subByUser2OnPath1
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例019：多个Path只被一个订阅者通过一个订阅通道订阅，修改部分/全部表字段的值使生成path实例，触发path搜索，查看返回的path实例是否符合预期

 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v2插入v2 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 5. v3插入v3 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 6. v4插入v4 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 7. v1插入v1 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
 * 8. 接收pathNum应为15
 * 9. 修改v2的c2,(1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)->(1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5) modify 5 records
 * 10. 接收pathNum应为15
 * 11. 修改v2的c3,(1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5)->(1,1,3),(2,2,3),(3,3,3),(4,4,3),(5,5,3) modify 5 records
 * 12. 接收pathNum应为30
 */ 
TEST_F(ConditionalTrigger,GQL_014_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 分别发送3个订阅请求：分别订阅path1、path2、path3
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};
    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser1OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser1OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //创建触发器
    const char *createTrigger = R"(
        CREATE TRIGGER t19_1
        ON REPLACE v3
        WHEN UPDATED_ANY(c1,c3) DO SEARCH path2,path3;
        CREATE TRIGGER t19_2 ON REPLACE v1 WHEN TRUE DO SEARCH path1,path2,path3;

    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTrigger));

    uint32_t c1PropArr[] = {1, 2, 3, 4, 5};
    uint32_t changePropArr[] = {6, 7, 8, 9, 10};
   
    //replace v2 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v2", 5);

    //replace v3 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v3", 5);

    //replace v4 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v4", 5);

    //replace v1 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)
    BatchReplaceDataVertexForPath(conn, stmt, "v1", 5);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    uint32_t EXPECT_PATH_NUM = 15;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d \n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    // replace v3 (1,1,1),(2,2,2),(3,3,3),(4,4,4),(5,5,5)->(1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5) modify 4 records
    BatchReplaceVertexForPathWithC2(conn, stmt, "v3", 5, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    EXPECT_PATH_NUM = 15;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d \n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    // replace v3 (1,3,1),(2,3,2),(3,3,3),(4,3,4),(5,3,5)->(1,1,3),(2,2,3),(3,3,3),(4,4,3),(5,5,3) modify 5 records
    BatchReplaceVertexForPathWithC3(conn, stmt, "v3", 5, 0, c1PropArr, changePropArr);

    // 检查返回的pathName是否符合预期
    sleepCnt = 0;
    EXPECT_PATH_NUM = 25;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d \n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t19_1;
        DROP TRIGGER t19_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
