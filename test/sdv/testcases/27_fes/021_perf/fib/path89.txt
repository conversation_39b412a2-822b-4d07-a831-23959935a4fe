CREATE PATH P89 (
    MATCH
    (:T7)-[:E4|:E84300|:E83016|:E87275|:E83017|:E84]->(
        (:T10)|
        (V3452:T3452)-[:E84301]->(V36:T36)-[:E108|:E203423|:E107]->(
            (V1:T1)-[:E83030]->(:T31),
            (V1:T1)-[:E83031]->(:T279)|
            (:T359)-[:E83013]->(:T357)|
            (NULL)
        ),
        (V36:T36)-[:E83557|:E86614|:E83169|:E121|:E82498|:E84110]->(
            (:T3378)|
            (:T3860)|
            (:T2169)|
            (:T279)|
            (:T2726)|
            (:T2153)
        ),
        (V3452:T3452)-[:E203319]->(:T4171)|
        (:T1)|
        (:T2009)-[:E81132|:E87797|:E81151|:E81152|:E83240|:E81401|:E82449]->(
            (:T2337)-[:E81133]->(:T2336)|
            (:T279)|
            (:T2010)|
            (:T2011)|
            (:T2869)|
            (:T2563)|
            (:T2745)
        )|
        (:T813)-[:E83019]->(:T279)|
        (:T37)-[:E85640]->(:T10)
    )
    RETURN
    (
        REPLACE T7.uiVrIndex 300, T7.uiVerNo 300, T7.uiFVrfIndex 300, T7.uiDestAddr 300, T7.ucMaskLen 300, T7.ucIIDGFlag 300, T7.usRouteFlags 300, T7.uiIIDindex 300, T7.uiPrimaryLabel 300, T7.uiAttributeId 300, T7.usRes 300, T10.uiVrfIndex 200, T357.uiPdtInfo 660, T357.usVlanId 660, T357.uiVrId 660, T2010.uiRingIndex 4672, T2010.uiNRBIndex 4672, T3378.ucSRPrefer 5509, T813.uiIpAddr 656, T813.usPevid 656, T813.uiIfIndex 656, T813.uiFvrfId 656, T813.uiToken 656, T813.uiVrfId 656, T813.usCevid 656
    )
);
