/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 057_PessimisiticRRWithHac
 * Author: hanyang
 * Create: 2023-9-13
 */
#include "PessRR.h"
#include "../../001_vertexlabel/vertex_util.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

class PessimisiticRRWithHac : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void PessimisiticRRWithHac::SetUpTestCase()
{
        //初始化配置文件
        InitCfgForHac();

        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void PessimisiticRRWithHac::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh  recover");
}

void PessimisiticRRWithHac::SetUp()
{
    int ret;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void PessimisiticRRWithHac::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/*****************************************************************************
 Description  : 001.配置文件默认配置，建表时配置“开启事务”，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRRWithHac, OtherWithHac_057_Cfg_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建Vertex
    char *vLabelSchema = NULL;
    readJanssonFile("schema_file/Vertex_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 删除表
    TestDropLabel(g_stmt, "Vertex_01");
}

/*****************************************************************************
 Description  : 002.配置文件默认配置，建表时配置“开启事务”，开启“悲观可重复读”事务，事务开启失败
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRRWithHac, OtherWithHac_057_Cfg_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建Vertex
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);

    // 删除表
    TestDropLabel(g_stmt, "Vertex_01");
}

/*****************************************************************************
 Description  : 003.namespace配置“悲观读已提交”，建表时配置“开启事务”，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRRWithHac, OtherWithHac_057_Cfg_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建namespace，读已提交+悲观
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};

    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt, g_namespace);

    // 创建Vertex
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 删除表
    TestDropLabel(g_stmt, "Vertex_01");

    TestDropNamespace(g_stmt, g_namespace);
}

/*****************************************************************************
 Description  : 004.namespace配置“悲观读已提交”，建表时配置“开启事务”，开启“悲观可重复读”事务，事务开启失败
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRRWithHac, OtherWithHac_057_Cfg_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建namespace，读已提交+悲观
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};

    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt, g_namespace);

    // 创建Vertex
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);

    // 删除表
    TestDropLabel(g_stmt, "Vertex_01");

    TestDropNamespace(g_stmt, g_namespace);
}
