/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: data structure for tables in path
 * Create: 2023-08-29
*/

#ifndef FES_TABLES_DATA_STRUCTURE_H
#define FES_TABLES_DATA_STRUCTURE_H

#ifdef __cplusplus
extern "C" {
#endif

#pragma pack(1)
typedef struct {
    uint8_t property1;
    uint16_t property2;
    uint32_t property3;
    int8_t property5;
    int16_t property6;
    int32_t property7;
} TableT;
#pragma pack()

typedef struct {
    uint32_t property1;
    uint32_t property2;
    uint32_t property3;
    uint32_t property4;
} Table2T;

typedef struct {
    uint32_t property1;
    uint32_t property2;
    uint32_t property3;
    uint32_t property4;
} Table3T;

typedef struct {
    uint32_t property1;
    uint32_t property2;
} Table4T;

typedef struct {
    uint32_t property1;
    uint32_t property2;
} Table5T;

typedef struct {
    uint8_t property1;
    uint16_t property2;
} TestTableT;

typedef struct {
    uint32_t expectedTotalNum;
    uint32_t expectedSuccessNum;
    uint32_t received;
} UserBatchDataT;

#ifdef __cplusplus
}
#endif

#endif

