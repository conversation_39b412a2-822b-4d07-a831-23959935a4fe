/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * @Description: tablespace空间大小可配置
 * @Author: tangguangming/wx1148729
 * @Create: 2023-04-10
功能约束
027 创建一个tsp，写数据直到写满，查看表空间视图，看内存使用率
028 不修改defaultTablespaceMaxSize的大小，循环创建tsp，只能创建31个，预期第32个失败
 */
#include "TspMaxSize.h"

class MaxSizeFunc : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MaxSizeFunc::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void MaxSizeFunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void MaxSizeFunc::SetUp()
{
    int ret;
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void MaxSizeFunc::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 027 创建一个tsp，写数据直到写满，查看表空间视图，看内存使用率
TEST_F(MaxSizeFunc, DDL_044_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    const char *tspName = (const char *)"tsp027";
    const char *tbName = "simpleLabel001";
    const char *cfgJson = R"({"max_record_count":1000000, "auto_increment":1,
        "isFastReadUncommitted":1, "yang_model":0})";
    char *labelJson = NULL;

    // 创建表空间
    testCreateTablespace(tspName);
    // 查询defaultTablespaceMaxSize的值
    testValueCheck("name: defaultTablespaceMaxSize", "current value: 32");

#if defined ENV_RTOSV2X
    // tsp校验
    TspInfoCheck("TABLESPACE_NAME: public", "MAX_SIZE: [16] MB");
    TspInfoCheck("TABLESPACE_NAME: tsp027", "MAX_SIZE: [16] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");
#else
    // tsp校验
    TspInfoCheck("TABLESPACE_NAME: public", "MAX_SIZE: [992] MB");
    TspInfoCheck("TABLESPACE_NAME: tsp027", "MAX_SIZE: [32] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");
#endif  
    
    // 建表
    readJanssonFile("./schemaFile/simpleLabel001.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    labelJson = NULL;

    // 写数据
    int32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, tbName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        index++;
    }

#if defined ENV_RTOSV2X
    // tsp校验
    TspInfoCheck("TABLESPACE_NAME: public", "MAX_SIZE: [16] MB");
    TspInfoCheck("TABLESPACE_NAME: tsp027", "MAX_SIZE: [16] MB", "CUR_USED_SIZE: [16] MB [0] KB [0] Byte",
        "USED_RATIO: 100.00%");
#else
    // tsp校验
    TspInfoCheck("TABLESPACE_NAME: public", "MAX_SIZE: [992] MB");
    TspInfoCheck("TABLESPACE_NAME: tsp027", "MAX_SIZE: [32] MB", "CUR_USED_SIZE: [32] MB [0] KB [0] Byte",
        "USED_RATIO: 100.00%");
#endif

    // 删表
    ret = GmcDropVertexLabel(g_stmt, tbName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 不修改defaultTablespaceMaxSize的大小，循环创建tsp，只能创建31个，预期第32个失败
TEST_F(MaxSizeFunc, DDL_044_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

#if defined ENV_RTOSV2X
    int tspNum = 2;
#else
    int tspNum = 33;
#endif
    char tspName[128] = {0};
    GmcTspCfgT tspCfg;
    for (int i = 1; i < tspNum; i++) {
        sprintf((char *)tspName, "tsp0%d", i);
        tspCfg.tablespaceName = tspName;
        tspCfg.initSize = 0;
        tspCfg.stepSize = 0;
        tspCfg.maxSize = 0;

        ret = GmcCreateTablespace(g_stmt, &tspCfg);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create tablespace num: %d", i - 1);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // tsp校验
#if defined ENV_RTOSV2X
    tspNum = 2;
    TspInfoCheck("TABLESPACE_NAME: tsp01", "MAX_SIZE: [16] MB");
#else
    tspNum = 33;
    TspInfoCheck("TABLESPACE_NAME: tsp031", "MAX_SIZE: [32] MB");
#endif
    
    // 删除表空间
    for (int i = 1; i < tspNum; i++) {
        sprintf((char *)tspName, "tsp0%d", i);
        tspCfg.tablespaceName = tspName;
        ret = GmcDropTablespace(g_stmt, tspName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "drop tablespace num: %d", i - 1);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
