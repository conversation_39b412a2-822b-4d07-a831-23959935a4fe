[{"version": "2.0", "type": "record", "name": "V5test_10", "max_record_count": 0, "fields": [{"name": "function", "type": "string", "size": 32}, {"name": "type", "type": "string", "size": 64}, {"name": "qualifier", "type": "string", "size": 256}, {"name": "severity", "type": "int32"}, {"name": "indication", "type": "int32"}, {"name": "eventTime", "type": "string", "size": 32}, {"name": "params", "type": "string", "size": 512}, {"name": "ledctrl", "type": "boolean"}], "keys": [{"name": "PK", "node": "alarm_active_status", "fields": ["type", "qualifier"], "index": {"type": "primary"}}]}]