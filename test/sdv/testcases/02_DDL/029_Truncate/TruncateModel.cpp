#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
unsigned int isNull;
unsigned int posValue;
int affectvalue;
unsigned int affectRows, len;
char *truncate_label_schema = NULL;
char *sys_label_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":100000
    }
)";
class TruncatemodelTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        readJanssonFile("schema_file/truncate_test_schema.gmjson", &truncate_label_schema);
        ASSERT_NE((void *)NULL, truncate_label_schema);
        readJanssonFile("schema_file/sys.gmjson", &sys_label_schema);
        ASSERT_NE((void *)NULL, sys_label_schema);
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        free(truncate_label_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TruncatemodelTest::SetUp()
{
    //同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void TruncatemodelTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *insertThread(void *arg)
{
    GmcConnT *g_conn3;
    GmcStmtT *g_stmt3;
    int ret = testGmcConnect(&g_conn3, &g_stmt3);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt3, "sys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt3, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "a1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "a2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "a3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0_value = 58;
    uint32_t f1_value = f0_value + 1, f2_value = f0_value + 2, f3_value = f0_value + 3, f4_value = f0_value + 4;
    ret = GmcNodeSetPropertyByName(root, (char *)"a0", GMC_DATATYPE_UINT32, &f0_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, (char *)"b1", GMC_DATATYPE_UINT32, &f1_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, (char *)"b2", GMC_DATATYPE_UINT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 插入array节点
    for (uint32_t j = 0; j < 2; j++) {
        ret = GmcNodeSetPropertyByName(T2, (char *)"b3", GMC_DATATYPE_UINT32, &f3_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T2, (char *)"b4", GMC_DATATYPE_UINT32, &f4_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (j < 1) {
            ret = GmcNodeGetNextElement(T2, &T2);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    uint32_t f5_value = f0_value + 5;
    uint32_t f6_value = f0_value + 6;
    // 插入vector节点
    for (uint32_t j = 0; j < 2; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, (char *)"b5", GMC_DATATYPE_UINT32, &f5_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, (char *)"b6", GMC_DATATYPE_UINT32, &f6_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt3);
    if (ret == GMERR_OK) {
        ret = testGmcPrepareStmtByLabelName(g_stmt3, "sys", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt3);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt3, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            cnt++;
        }
        printf("cnt=%d\n", cnt);
        if (cnt == 0 || cnt == 11 || cnt == 1) {
            ret = 0;
            printf("cnt=%d\n", cnt);
        } else {
            ret = 1;
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        printf("insert ret=%d\n", ret);
    }
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
void *truncateThread(void *arg)
{
    GmcConnT *g_conn2;
    GmcStmtT *g_stmt2;
    void *vertexLabel = NULL;
    int ret = testGmcConnect(&g_conn2, &g_stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt2, "sys");
    if (ret == GMERR_OK) {
        ret = testGmcPrepareStmtByLabelName(g_stmt2, "sys", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt2);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt2, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            cnt++;
        }
        if (cnt == 0 || cnt == 1) {
            ret = 0;
        } else {
            ret = 1;
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else if (ret == GMERR_UNEXPECTED_NULL_VALUE) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf("truncate fail ,label is drop \n");
    } else if (ret == GMERR_RESTRICT_VIOLATION) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf("truncate fail ,edgelabel exist\n");
    } else {
        printf("truncate ret=%d\n", ret);
    }
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
void TreeInsertVertex(
    GmcStmtT *stmt, int index, int start_num, int end_num, int array_num, int vector_num, const char *labelName)
{
    void *label = NULL;
    int ret;
    GmcNodeT *root, *T1, *T2, *T3;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        uint32_t f0_value = index + i;
        uint32_t f1_value = f0_value + 1, f2_value = f0_value + 2, f3_value = f0_value + 3, f4_value = f0_value + 4;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "a1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "a2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "a3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root, (char *)"a0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, (char *)"b1", GMC_DATATYPE_UINT32, &f1_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, (char *)"b2", GMC_DATATYPE_UINT32, &f2_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetPropertyByName(T2, (char *)"b3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T2, (char *)"b4", GMC_DATATYPE_UINT32, &f4_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        uint32_t f5_value = f0_value + 5;
        uint32_t f6_value = f0_value + 6;
        // 插入vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, (char *)"b5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, (char *)"b6", GMC_DATATYPE_UINT32, &f6_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TreeReadVertex(GmcStmtT *stmt, int index, int start_num, int end_num, int array_num, int vector_num,
    const char *labelName, const char *keyName)
{

    void *label = NULL;
    bool isNull, isFinish = false;
    int ret;
    GmcNodeT *root, *T1, *T2, *T3;
    // 查询顶点
    for (int i = start_num; i < end_num; i++) {
        uint32_t f0_value = index + i;
        uint32_t f1_value = f0_value + 1, f2_value = f0_value + 2, f3_value = f0_value + 3, f4_value = f0_value + 4,
                 f5_value = f0_value + 5, f6_value = f0_value + 6;
        uint32_t expectValue0, expectValue1, expectValue2, expectValue3, expectValue4, expectValue5, expectValue6;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a2", &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            bool isnull;
            ret = GmcNodeGetPropertyByName(root, (char *)"a0", &expectValue0, sizeof(uint32_t), &isnull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(false, isnull);
            ASSERT_EQ(f0_value, expectValue0);
            ret = GmcNodeGetPropertyByName(T1, (char *)"b1", &expectValue1, sizeof(uint32_t), &isnull);
            ASSERT_EQ(GMERR_OK, ret);
            EXPECT_EQ(false, isnull);
            ASSERT_EQ(f1_value, expectValue1);
            ret = GmcNodeGetPropertyByName(T1, (char *)"b2", &expectValue2, sizeof(uint32_t), &isnull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(false, isnull);
            ASSERT_EQ(f2_value, expectValue2);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetPropertyByName(T2, (char *)"b3", &expectValue3, sizeof(uint32_t), &isnull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(false, isnull);
                ASSERT_EQ(f3_value, expectValue3);
                ret = GmcNodeGetPropertyByName(T2, (char *)"b4", &expectValue4, sizeof(uint32_t), &isnull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(false, isnull);
                ASSERT_EQ(f4_value, expectValue4);
                if (j < array_num - 1) {
                    ret = GmcNodeGetNextElement(T2, &T2);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            // 读取vector节点
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetPropertyByName(T3, (char *)"b5", &expectValue5, sizeof(uint32_t), &isnull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(false, isnull);
                ASSERT_EQ(f5_value, expectValue5);
                ret = GmcNodeGetPropertyByName(T3, (char *)"b6", &expectValue6, sizeof(uint32_t), &isnull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(false, isnull);
                ASSERT_EQ(f6_value, expectValue6);
            }
        }
    }
}

void normal_insert_layer1(GmcStmtT *stmtVsys, int v1_id)
{
    int ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_insert_layer2(GmcStmtT *stmtRule, int v2_vsys_id, int v2_id, void *v2_name)
{
    int ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "vsys::id", GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "id", GMC_DATATYPE_INT32, &v2_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char *)v2_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void truncate_test(GmcStmtT *stmt, const char *labelName)
{

    int ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(1, cnt);
}
// 001 tree 模型建表插入多条数据查询，truncate，查询 预期结果：truncate成功
TEST_F(TruncatemodelTest, DDL_029_TruncatemodelTest_001)
{
    // Create VertexLabel
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    GmcDropVertexLabel(g_stmt, labelName);
    int ret = GmcCreateVertexLabel(g_stmt, sys_label_schema, normal_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TreeInsertVertex(g_stmt, 1, 0, 10, 3, 3, labelName);
    TreeReadVertex(g_stmt, 1, 0, 10, 3, 3, labelName, keyName);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(0, cnt);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 002 tree模型 多线程并发插入vertex和truncate
TEST_F(TruncatemodelTest, DDL_029_TruncatemodelTest_002)
{
    // Create VertexLabel
    GmcStmtT *stmt_t = NULL;
    GmcAllocStmt(g_conn, &stmt_t);
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    GmcDropVertexLabel(stmt_t, labelName);
    int ret = GmcCreateVertexLabel(stmt_t, sys_label_schema, normal_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TreeInsertVertex(stmt_t, 1, 0, 10, 3, 3, labelName);
    TreeReadVertex(stmt_t, 1, 0, 10, 3, 3, labelName, keyName);
    GmcFreeStmt(stmt_t);
    pthread_t tid_insert;
    pthread_t tid_truncate;
    ret = pthread_create(&tid_insert, NULL, insertThread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate, NULL, truncateThread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid_insert, NULL);
    pthread_join(tid_truncate, NULL);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 003
// tree模型建表插入多条数据和truncate，然后创建同名label，预期结果：truncate成功，建同名表失败（truncate只是删除表数据，不影响表的元数据）
TEST_F(TruncatemodelTest, DDL_029_TruncatemodelTest_003)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009013");
    // Create VertexLabel
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    GmcDropVertexLabel(g_stmt, labelName);
    int ret = GmcCreateVertexLabel(g_stmt, sys_label_schema, normal_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TreeInsertVertex(g_stmt, 1, 0, 10, 3, 3, labelName);
    TreeReadVertex(g_stmt, 1, 0, 10, 3, 3, labelName, keyName);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(0, cnt);
    ret = GmcCreateVertexLabel(g_stmt, sys_label_schema, normal_config_json);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

