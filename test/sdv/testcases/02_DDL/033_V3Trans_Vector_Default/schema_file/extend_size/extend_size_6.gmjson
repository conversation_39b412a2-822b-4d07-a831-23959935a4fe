[{"version": "2.0", "type": "record", "name": "T5", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint64"}, {"name": "T1", "type": "record", "vector": true, "size": 1024, "extend_size": 1024, "fields": [{"name": "V1", "type": "uint32"}, {"name": "V2", "type": "float"}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "T5", "fields": ["F1"], "constraints": {"unique": true}}]}]