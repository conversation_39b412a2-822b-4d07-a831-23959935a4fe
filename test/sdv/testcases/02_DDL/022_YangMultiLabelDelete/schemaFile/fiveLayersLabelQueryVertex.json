[
    {
        "type":"record",
        "name":"sec-policy",
        "fields":[
            {"name":"id", "type":"int32", "nullable":false},
			{"name":"F0", "type":"uint32", "nullable":false}
        ],
        "keys":[
            {
                "node":"id",
                "name":"id",
                "fields":["id"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    },

    {
        "type":"record",
        "name":"sec-policy/vsys",
        "fields":[
            {"name":"sec-policy/id", "type":"int32", "nullable":false},
            {"name":"id", "type":"int32", "nullable":false},
            {"name":"F0", "type":"uint32", "nullable":false}
        ],
        "keys":[
            {
                "node":"sec-policy/vsys",
                "name":"sec-policy/vsys",
                "fields":["sec-policy/id","id"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    },
	
	{
        "type":"record",
        "name":"sec-policy/vsys/static-policy",
        "fields":[
            {"name":"vsys/sec-policy/id", "type":"int32", "nullable":false},
            {"name":"vsys/id", "type":"int32", "nullable":false},
			{"name":"id", "type":"int32", "nullable":false},
            
        ],
        "keys":[
            {
                "node":"vsys/static-policy",
                "name":"vsys/static-policy",
                "fields":["vsys/sec-policy/id","vsys/id","id"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    },
	
	{
        "type":"record",
        "name":"sec-policy/vsys/static-policy/rule",
        "fields":[
            {"name":"static-policy/vsys/sec-policy/id", "type":"int32", "nullable":false},
            {"name":"static-policy/vsys/id", "type":"int32", "nullable":false},
			{"name":"static-policy/id", "type":"int32", "nullable":false},
			{"name":"id", "type":"int32", "nullable":false},
			{"name":"name", "type":"string","size":8, "nullable":false},
			{"name":"policy-log", "type":"string", "size":255, "nullable":false},
			{"name":"session-aging-time", "type":"time", "nullable":false},
			{"name":"valan-id", "type":"int32", "nullable":false},
			{"name":"enable", "type":"boolean", "nullable":false}
        ],
        "keys":[
            {
                "node":"static-policy/rule",
                "name":"static-policy/rule",
                "fields":["static-policy/vsys/sec-policy/id","static-policy/vsys/id","static-policy/id","id"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    },
	
	{
        "type":"record",
        "name":"sec-policy/vsys/static-policy/rule/description",
        "fields":[
            {"name":"rule/static-policy/vsys/sec-policy/id", "type":"int32", "nullable":false},
            {"name":"rule/static-policy/vsys/id", "type":"int32", "nullable":false},
			{"name":"rule/static-policy/id", "type":"int32", "nullable":false},
			{"name":"rule/id", "type":"int32", "nullable":false},
			{"name":"id", "type":"int32", "nullable":false},
            {"name":"F0", "type":"int32", "nullable":false}
        ],
        "keys":[
            {
                "node":"rule/description",
                "name":"rule/description",
                "fields":["rule/static-policy/vsys/sec-policy/id","rule/static-policy/vsys/id","rule/static-policy/id","rule/id","id"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }

]
