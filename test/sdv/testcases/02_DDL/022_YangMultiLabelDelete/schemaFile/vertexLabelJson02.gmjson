[{"type": "record", "name": "vertexLabel02", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "string", "nullable": false, "size": 100}], "keys": [{"node": "vertexLabel02", "name": "vertexLabel02_pk", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]