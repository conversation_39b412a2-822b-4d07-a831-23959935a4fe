[{"name": "label0", "type": "record", "version": "2.0", "fields": [{"name": "F0", "type": "int"}, {"name": "IP", "type": "string", "size": 10}, {"name": "PREFIX", "type": "uint8"}, {"name": "F1", "type": "int"}, {"name": "F2", "type": "int"}], "keys": [{"fields": ["IP", "PREFIX"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "name": "label0_LPM", "node": "label0", "constraints": {"unique": true}}, {"fields": ["F0"], "index": {"type": "primary"}, "name": "label0_K0", "node": "label0", "constraints": {"unique": true}}]}]