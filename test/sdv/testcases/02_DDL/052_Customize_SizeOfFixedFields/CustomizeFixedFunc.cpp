#include "Fixed.h"

class CustomizeFixedFunc : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CustomizeFixedFunc::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CustomizeFixedFunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void CustomizeFixedFunc::SetUp()
{
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "fixedTree");
}
void CustomizeFixedFunc::TearDown()
{
    free(g_schema);
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 不设置modifiable字段，config_json中修改fixed字段大小
TEST_F(CustomizeFixedFunc, DDL_052_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"modifiable\": true,", "");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置modifiable字段为false，config_json中修改fixed字段大小
TEST_F(CustomizeFixedFunc, DDL_052_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"modifiable\": true,", "\"modifiable\":false,");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置modifiable字段为true，config_json中修改fixed字段的大小大于schema中设置的大小
TEST_F(CustomizeFixedFunc, DDL_052_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 500",
        "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 3");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置modifiable字段为true，config_json中修改fixed字段的大小等于schema中设置的大小，后DML操作验证
TEST_F(CustomizeFixedFunc, DDL_052_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 500",
        "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 4");
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "abcd";
    char nodepath[] = "c6/t2/h1";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中fixedSize不指定任何属性，建表
TEST_F(CustomizeFixedFunc, DDL_052_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": []})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中不设置属性名称，建表
TEST_F(CustomizeFixedFunc, DDL_052_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINE_COLUMN);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"size":4}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINE_COLUMN, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置属性名称为空，建表
TEST_F(CustomizeFixedFunc, DDL_052_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "", "size":4}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置属性名称为数字，建表
TEST_F(CustomizeFixedFunc, DDL_052_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": 123, "size":4}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置属性的路径长度为128，建表
TEST_F(CustomizeFixedFunc, DDL_052_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    g_schema = (char *)realloc(g_schema, 1.5 * strlen(g_schema));
    const char *config_tmp = "{\"max_record_count\":1000,"
                             "\"fixedSize\": [{\"property\": \"c6aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaa/t2/h1\", \"size\":4}]}";
    // 将节点名称变长
    const char *findstr = R"("name": "c6")";
    const char *replacestr = "\"name\": \"c6aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa\"";
    ReplaceStr(g_schema, findstr, replacestr);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置属性的路径长度为129，建表
TEST_F(CustomizeFixedFunc, DDL_052_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    g_schema = (char *)realloc(g_schema, 1.5 * strlen(g_schema));
    const char *config_tmp = "{\"max_record_count\":1000,"
                             "\"fixedSize\": [{\"property\": \"c6aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaa/t2/h1\", \"size\":4}]}";
    // 将节点名称变长
    const char *findstr = R"("name": "c6")";
    const char *replacestr = "\"name\": \"c6aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                             "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa\"";
    ReplaceStr(g_schema, findstr, replacestr);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置size为15360，建表
TEST_F(CustomizeFixedFunc, DDL_052_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 500",
        "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 15360");
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1", "size":15360}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置size为15361，建表
TEST_F(CustomizeFixedFunc, DDL_052_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 500",
        "\"name\": \"h1\", \"type\": \"fixed\", \"size\": 15361");
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1", "size":15361}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中不设置size，建表
TEST_F(CustomizeFixedFunc, DDL_052_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINE_COLUMN);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1"}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINE_COLUMN, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置size为空，建表
TEST_F(CustomizeFixedFunc, DDL_052_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1", "size": }]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置size的值为-1，建表
TEST_F(CustomizeFixedFunc, DDL_052_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1", "size": -1}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置size的值为0，建表
TEST_F(CustomizeFixedFunc, DDL_052_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1", "size": 0}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// config_json中设置size的值不为数字，建表
TEST_F(CustomizeFixedFunc, DDL_052_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c6/t2/h1", "size": "1"}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// fixed类型字段中设置了default属性，config_json设置的属性值和schema中相等
TEST_F(CustomizeFixedFunc, DDL_052_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c1/f2", "size":6}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// fixed类型字段中设置了default属性，config_json设置的属性值小于schema中设置的值
TEST_F(CustomizeFixedFunc, DDL_052_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c1/f2", "size":4}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// schema中设置modifiable字段时不为bool型数值
TEST_F(CustomizeFixedFunc, DDL_052_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"modifiable\": true,", "\"modifiable\": 23,");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 在schema中的config字段配置fixedsize，DML操作校验
TEST_F(CustomizeFixedFunc, DDL_052_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    g_schema = (char *)realloc(g_schema, 1.5 * strlen(g_schema));

    const char *findstr = R"("modifiable": true,)";
    const char *replacestr = R"("modifiable": true,
    "config": {"fixedSize": [
        {"property": "c3", "size":5}
    ]},)";
    ReplaceStr(g_schema, findstr, replacestr);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML操作校验size大小
    char fixedValue[] = "helloworld";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置1024个fixed字段，建表
TEST_F(CustomizeFixedFunc, DDL_052_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    // 构造有1024个fixed字段的labeljson
    AW_FUN_Log(LOG_STEP, "开始构造labeljson");
    char prestr[] = R"({
    "version": "2.0", 
    "type": "record", 
    "name": "fixedTree",
    "modifiable": true,
    "fields": [
    { "name": "c0", "type": "fixed", "size": 4 },)";
    char suffixstr[] = R"(],
    "keys": [
        { "name": "table_pk", 
            "index": { "type": "primary" },
            "node": "fixedTree",
            "fields": [ "c0" ],
            "constraints": { "unique": true }
        }
    ]
    })";
    char *repeteStr1 = (char *)"\n{ \"name\": \"c";
    char *repeteStr2 = (char *)R"(", "type": "fixed", "size": 20, "nullable":true })";
    g_schema = (char *)malloc(strlen(prestr) * 1024);
    char count_str[5];
    strcpy(g_schema, prestr);
    for (int i = 1; i < 1024; i++) {
        strcat(g_schema, repeteStr1);
        snprintf(count_str, 5, "%d", i);
        strcat(g_schema, count_str);
        strcat(g_schema, repeteStr2);
        if (i < 1023) {
            strcat(g_schema, ",");
        }
    }
    strcat(g_schema, suffixstr);
    AW_FUN_Log(LOG_STEP, "构造完成");

    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c1", "size":6}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询视图校验
    ret = ViewCheck(6, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置1025个fixed字段，建表
TEST_F(CustomizeFixedFunc, DDL_052_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    const char *labelName = "fixedTree";
    // 构造有1025个fixed字段的labeljson
    AW_FUN_Log(LOG_STEP, "开始构造labeljson");
    char prestr[] = R"({
    "version": "2.0", 
    "type": "record", 
    "name": "fixedTree",
    "modifiable": true,
    "fields": [
    { "name": "c0", "type": "fixed", "size": 4 },)";
    char suffixstr[] = R"(],
    "keys": [
        { "name": "table_pk", 
            "index": { "type": "primary" },
            "node": "fixedTree",
            "fields": [ "c0" ],
            "constraints": { "unique": true }
        }
    ]
    })";
    char *repeteStr1 = (char *)"\n{ \"name\": \"c";
    char *repeteStr2 = (char *)R"(", "type": "fixed", "size": 20, "nullable":true })";
    g_schema = (char *)malloc(strlen(prestr) * 1025);
    char count_str[5];
    strcpy(g_schema, prestr);
    for (int i = 1; i < 1025; i++) {
        strcat(g_schema, repeteStr1);
        snprintf(count_str, 5, "%d", i);
        strcat(g_schema, count_str);
        strcat(g_schema, repeteStr2);
        if (i < 1024) {
            strcat(g_schema, ",");
        }
    }
    strcat(g_schema, suffixstr);
    AW_FUN_Log(LOG_STEP, "构造完成");

    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c1", "size":6}]})";
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 最初fixed字段在中间，对表进行升级，新的schema值大于元数据中的size且大于原schema中的size
TEST_F(CustomizeFixedFunc, DDL_052_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 最初fixed字段在中间，对表进行升级，新的schema值大于元数据中的size但小于原schema中的size
TEST_F(CustomizeFixedFunc, DDL_052_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ReplaceStr(content, "\"size\": 12,", "\"size\": 8,");
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 对表进行升级，新的schema值等于元数据中的size，最后DML操作验证
TEST_F(CustomizeFixedFunc, DDL_052_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ReplaceStr(content, "\"size\": 12,", "\"size\": 5,");
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 对表进行升级，新的schema值小于元数据中的size
TEST_F(CustomizeFixedFunc, DDL_052_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade unsuccessfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ReplaceStr(content, "\"size\": 12,", "\"size\": 4,");
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/***最低版本没有fixed字段且没有在config_json中进行size大小设置的情况，最低版本modifiable字段设置为true，
后续版本在schema中新增fixed字段，升级8个版本每个版本升级都修改已有fixed字段的size，并新增fixed字段，
后进行DML校验
**/
TEST_F(CustomizeFixedFunc, DDL_052_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表6张
    char tmpcontent[1024] = {0};

    // 创建六个修改好的表
    for (int i = 1; i < 8; i++) {
        snprintf(tmpcontent, MAX_CMD_SIZE, "./schema/updatefixed_label%d.gmjson", i);
        ret = TestUpdateVertexLabel(tmpcontent, expectValue, labelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 视图校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    ret = ViewCheck(5, 8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML操作校验size大小
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 降到指定版本后DML校验，降到最低版本后再DML校验
TEST_F(CustomizeFixedFunc, DDL_052_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";
    char *expectValue2 = (char *)"Alter schema degrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表1
    char updatefilename1[] = "./schema/updatefixed_label12.gmjson";
    char updatefilename2[] = "./schema/updatefixed_label13.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ret = CreateUpdateLabel(updatefilename1, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级到表1
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(content);
    // 创建升级表2
    char *content1 = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content1);
    content1 = (char *)realloc(content1, 1.5 * strlen(content1));
    ReplaceStr(content1, "\"schema_version\": 1,", "\"schema_version\": 2,");
    const char *findstr = R"({ "name": "c7", "type": "fixed", "size": 10, "nullable":true })";
    const char *replacestr = R"({ "name": "c7", "type": "fixed", "size": 10, "nullable":true },
            { "name": "c8", "type": "fixed", "size": 10, "nullable":true })";
    ReplaceStr(content1, findstr, replacestr);
    ret = CreateUpdateLabel(updatefilename2, content1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级到表2
    schemaUpdatePath = (char *)"./schema/updatefixed_label13.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 指定版本号降级到version1
    uint32_t schemaVersion = 1;
    ret = TestDownGradeVertexLabel(labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 视图校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    ret = ViewCheck(5, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 指定版本号降级到初始版本
    schemaVersion = 0;
    ret = TestDownGradeVertexLabel(labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename1);
    remove(updatefilename2);
    free(content1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 降级后再升级版本后DML校验
TEST_F(CustomizeFixedFunc, DDL_052_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";
    char *expectValue2 = (char *)"Alter schema degrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 指定版本号降级到version0
    uint32_t schemaVersion = 0;
    ret = TestDownGradeVertexLabel(labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 低版本没有modifiable字段，高版本存在
TEST_F(CustomizeFixedFunc, DDL_052_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade unsuccessfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000})";
    // 建表
    ReplaceStr(g_schema, "\"modifiable\": true,", " ");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 低版本有modifiable字段，高版本不存在
TEST_F(CustomizeFixedFunc, DDL_052_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade unsuccessfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ReplaceStr(content, "\"modifiable\": true,", " ");
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label12.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 对表进行默认降级，DML操作验证
TEST_F(CustomizeFixedFunc, DDL_052_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";
    char *expectValue = (char *)"Alter schema upgrade successfully";
    char *expectValue2 = (char *)"Alter schema degrade successfully";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *config_tmp = R"({"max_record_count":1000,
    "fixedSize": [{"property": "c3", "size":5}]})";
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, config_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 升级表
    char *schemaUpdatePath = (char *)"./schema/updatefixed_label1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 指定版本号降级到version0
    char cmd[512] = {0};
    char *dWay = (char *)"sync";
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -d %s -ns %s", g_toolPath, labelName, dWay, g_testNameSpace);
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML操作校验size大小
    char fixedValue[] = "hello";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 使用gmasst工具进行schema升级评估，升级的schema设置的大小大于元数据
TEST_F(CustomizeFixedFunc, DDL_052_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *expectValue = (char *)"Compatible schema alter!";
    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);

    char cmd[1024] = {0};
    char oldpath[] = "./schema/fixed_label.gmjson";
    char newpath[] = "./schema/updatefixed_label1.gmjson";
    // 创建配置表
    char configfilename[] = "./schema/config_label.gmjson";
    char *content = (char *)g_config;
    ret = CreateUpdateLabel(configfilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char configpath[] = "./schema/config_label.gmjson";

    (void)snprintf(cmd, 1024, "%s/gmasst schema_alter -o %s -n %s -f %s", g_toolPath, oldpath, newpath, configpath);
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remove(configfilename);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 使用gmasst工具进行schema升级评估，升级的schema设置的大小小于元数据
TEST_F(CustomizeFixedFunc, DDL_052_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *expectValue = (char *)"Compatible schema alter!";
    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);

    char cmd[1024] = {0};
    char oldpath[] = "./schema/fixed_label.gmjson";
    // 创建升级表
    char updatefilename[] = "./schema/updatefixed_label12.gmjson";
    char *content = NULL;
    readJanssonFile("./schema/updatefixed_label1.gmjson", &content);
    ReplaceStr(content, "\"size\": 12,", "\"size\": 8,");
    ret = CreateUpdateLabel(updatefilename, content);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char newpath[] = "./schema/updatefixed_label12.gmjson";
    // 创建配置表
    char configfilename[] = "./schema/config_label.gmjson";
    char *content1 = (char *)g_config;
    ret = CreateUpdateLabel(configfilename, content1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char configpath[] = "./schema/config_label.gmjson";

    (void)snprintf(cmd, 1024, "%s/gmasst schema_alter -o %s -n %s -f %s", g_toolPath, oldpath, newpath, configpath);
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    remove(configfilename);
    remove(updatefilename);
    free(content);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置modifiable字段为true，config_json中修改fixed字段的大小小于schema中设置的大小，后通过元数据视图校验size大小
TEST_F(CustomizeFixedFunc, DDL_052_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询视图校验
    ret = ViewCheck(4, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 设置modifiable字段为true，config_json中修改fixed字段的大小小于schema中设置的大小，后DML操作验证
TEST_F(CustomizeFixedFunc, DDL_052_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "abcd";
    char nodepath[] = "c6/t2/h1";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

#ifdef FEATURE_YANG
// yang表定义fixed字段并在config_json中修改size，后DML操作验证
TEST_F(CustomizeFixedFunc, DDL_052_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    int ret = 0;
    const char *labelName = "node1";

    // 异步建连
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = "NamespaceA";
    nspCfg.userName = "abc";
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    GmcTxConfigT g_mSTrxConfig;
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, "NamespaceA", use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    // 建表
    GmcStmtT *stmt_root = NULL;
    ret = GmcAllocStmt(g_conn_async, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *config_tmp = R"({"max_record_count":1000,"auto_increment":1,"isFastReadUncommitted":0,
    "fixedSize": [{"property": "node1/node2/F1", "size":5}],"yang_model":1})";
    readJanssonFile("./schema/yang_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, config_tmp, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    // DML操作校验size大小
    GmcBatchT *batch = NULL;
    // 开启事务
    ret = start_trans_async(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置插入操作
    char value[] = "hello";
    addDmlOption(stmt_root, batch, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, value);
    // 设置更新操作
    char evalue[] = "hello1";
    bool expect_ret = false;
    addDmlOption(stmt_root, batch, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE, evalue, expect_ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestBatchExecuteAsync(batch, userData);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcClearNamespaceAsync(g_stmt_async, "NamespaceA", ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, "NamespaceA", drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
#endif
// 设置fixed字段为索引字段，后DML操作验证，并进行索引查询
TEST_F(CustomizeFixedFunc, DDL_052_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *labelName = "fixedTree";

    readJanssonFile("./schema/fixed_label.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    // 建表
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DML操作校验size大小
    char fixedValue[] = "helloworld";
    char nodepath[] = "c3";
    FixedSizeCheck(g_stmt, labelName, fixedValue, nodepath, true);
    // 索引查询
    SelectByIndex(g_stmt, labelName, fixedValue);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
