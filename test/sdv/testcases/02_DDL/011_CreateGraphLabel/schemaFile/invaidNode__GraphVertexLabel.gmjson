[{"type": "record", "name": "vsys", "fields": [{"name": "id", "type": "int32", "nullable": false}], "keys": [{"node": "vsys", "name": "id", "fields": ["id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "vsys::rule", "fields": [{"name": "vsys::id", "type": "int32", "nullable": false}, {"name": "id", "type": "int32", "nullable": false}, {"name": "name", "type": "string", "size": 8, "nullable": false}], "keys": [{"node": "vsys::rule", "name": "vsys::id_id", "fields": ["vsys::id", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "vsys@rule@source_ip", "fields": [{"name": "rule::vsys::id", "type": "int32", "nullable": false}, {"name": "rule::id", "type": "int32", "nullable": false}, {"name": "ipLen", "type": "int32", "nullable": false}, {"name": "maskLen", "type": "int32", "nullable": false}], "keys": [{"node": "vsys@rule@source_ip", "name": "vsys.rule.source_ip_K0", "fields": ["rule::vsys::id", "rule::id", "ipLen", "maskLen"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]