[{"type": "record", "name": "vsys", "fields": [{"name": "id", "type": "int32", "nullable": false}], "keys": [{"node": "vsys", "name": "id", "fields": ["id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "vsys::rule", "fields": [{"name": "vsys::id", "type": "int32", "nullable": false}, {"name": "id", "type": "int32", "nullable": false}, {"name": "name", "type": "string", "size": 8, "nullable": false}], "keys": [{"node": "vsys::rule", "name": "vsys::id_id", "fields": ["vsys::id", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "vsys::rule::source_ip", "fields": [{"name": "rule::vsys::id", "type": "int32", "nullable": false}, {"name": "rule::id", "type": "int32", "nullable": false}, {"name": "ipLen", "type": "int32", "nullable": false}, {"name": "maskLen", "type": "int32", "nullable": false}, {"name": "H0", "type": "char", "nullable": false}, {"name": "H1", "type": "uchar", "nullable": false}, {"name": "H2", "type": "int8", "nullable": false}, {"name": "H3", "type": "uint8", "nullable": false}, {"name": "H4", "type": "int16", "nullable": false}, {"name": "H5", "type": "uint16", "nullable": false}, {"name": "H6", "type": "int32", "nullable": false}, {"name": "H7", "type": "uint32", "nullable": false}, {"name": "H8", "type": "boolean", "nullable": false}, {"name": "H9", "type": "int64", "nullable": false}, {"name": "H10", "type": "uint64", "nullable": false}, {"name": "H11", "type": "float", "nullable": false}, {"name": "H12", "type": "double", "nullable": false}, {"name": "H13", "type": "time", "nullable": false}, {"name": "H14", "type": "string", "nullable": false, "size": 100}, {"name": "H15", "type": "bytes", "size": 12}, {"name": "H16", "type": "fixed", "size": 12}], "keys": [{"node": "vsys::rule::source_ip", "name": "vsys.rule.source_ip_K0", "fields": ["rule::vsys::id", "rule::id", "ipLen", "maskLen"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]