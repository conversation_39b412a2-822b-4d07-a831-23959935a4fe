/*****************************************************************************
 Description  : GmcMergeVertex接口单点异步功能测试
 Notes        : DDL_015_MergeVertexAsync_001	001.merge部分字段，merge的属性为最新值，未merge的属性为旧值
                DDL_015_MergeVertexAsync_002	002.正常merge一个不存在的vetex,affectRows=1,读取成功
                DDL_015_MergeVertexAsync_003	003.正常merge一个已存在的vertex,affectRows=2,读到merge后的值
                DDL_015_MergeVertexAsync_004
004.merge一个不存在的顶点，一个属性值有默认值，merge时不设置该数据，读数据，merge时设置该属性，读数据
                DDL_015_MergeVertexAsync_005
005.merge一个不存在的顶点，一个属性值设置为nullable=false，merge时不设置该数据，merge失败 DDL_015_MergeVertexAsync_006
006.insert100个顶点，merge该顶点，replace该顶点，delete该顶点，remove该顶点 DDL_015_MergeVertexAsync_007
007.merge一个pk不冲突的顶点，二级 索引unique=true,存在二级索引冲突，merge失败 DDL_015_MergeVertexAsync_008
008.merge一个pk冲突的顶点，二级索引unique=true,存在二级索引冲突，merge失败 DDL_015_MergeVertexAsync_009
009.merge一个pk不冲突的顶点，二级索引unique=Flase，构造相同的二级索引属性值，merge成功 DDL_015_MergeVertexAsync_010
010.merge一个pk不冲突的顶点，二级索引unique=Flase，构造不相同的二级索引属性值，merge成功 DDL_015_MergeVertexAsync_011
011.循环merge同一顶点1w次，循环remove该顶点1w次 DDL_015_MergeVertexAsync_012	012.多线程并发merge同一顶点
                DDL_015_MergeVertexAsync_013	013.创建驻留deltaStore的vertexLabel,merge一个已存在的顶点，读取数据
                DDL_015_MergeVertexAsync_014	014.使用superfiled给属性赋值，执行merge操作，读取数据
                DDL_015_MergeVertexAsync_015
015.创建GraphVertexLabel/GraphEdgeLabel，插入顶点建边，对顶点进行merge操作，通过邻点查询到merge后的值
                DDL_015_MergeVertexAsync_016	016.创建GraphVertexLabel/GraphEdgeLabel，merge顶点建边，通过邻点查询顶点

 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2020/11/18
*****************************************************************************/
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

int affectRows;
unsigned int len;
char *normal_vertexlabel_schema = NULL;
char *normal_vertexlabel_schema_test = NULL;
char *normal_graph_vertex_label_schema = NULL;
char *normal_graph_edge_label_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":10000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

void set_VertexProperty_PK(GmcStmtT *stmt, int i);
void set_VertexProperty_SK(GmcStmtT *stmt, int i);
void set_VertexProperty(GmcStmtT *stmt, int i);
void query_VertexProperty(GmcStmtT *stmt, int i);

class MergeAsync : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MergeAsync::SetUp()
{
    printf("[INFO] MergeAsync Start.\n");
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);

    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    readJanssonFile("schemaFile/NormalVertexLabel_test.gmjson", &normal_vertexlabel_schema_test);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema_test);
    AW_CHECK_LOG_BEGIN();
}

void MergeAsync::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
    free(normal_vertexlabel_schema_test);
    printf("[INFO] MergeAsync End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.merge部分字段，merge的属性为最新值，未merge的属性为旧值
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_001)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t priK = 1;
    int64_t SKValue = 1;
    int OldVal = 1;
    set_VertexProperty_PK(g_stmt, priK);
    set_VertexProperty_SK(g_stmt, SKValue);
    set_VertexProperty(g_stmt, OldVal);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // merge 部分字段，不merge F9
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    uint32_t pk = 1;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(g_stmt_async, priK);
    set_VertexProperty(g_stmt_async, NewVal);
    data = {0};
    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(2, data.affectRows);

    // Query Vertex  其它字段为新值，F9为旧值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    query_VertexProperty(g_stmt, NewVal);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.正常merge一个不存在的vetex,affectRows=1,读取成功
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_002)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    // merge noexist Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t priK = 1;
    int64_t SKValue = 1;
    int OldVal = 1;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(g_stmt_async, priK);
    set_VertexProperty_SK(g_stmt_async, SKValue);
    set_VertexProperty(g_stmt_async, OldVal);
    data = {0};
    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);

    // Query Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    query_VertexProperty(g_stmt, OldVal);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.正常merge一个已存在的vertex,affectRows=2,读到merge后的值
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_003)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t priK = 1;
    int64_t SKValue = 1;
    int OldVal = 1;
    set_VertexProperty_PK(g_stmt, priK);
    set_VertexProperty_SK(g_stmt, SKValue);
    set_VertexProperty(g_stmt, OldVal);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // merge exist Vertex
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(g_stmt_async, priK);
    set_VertexProperty_SK(g_stmt_async, SKValue);
    set_VertexProperty(g_stmt_async, OldVal);
    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(2, data.affectRows);
    // Query Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    query_VertexProperty(g_stmt, OldVal);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.merge一个不存在的顶点，一个属性值有默认值，merge时不设置该数据，读数据，merge时设置该属性，读数据
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_004)
{
    int ret = 0;
    char *vertexlabel_schema = NULL;
    const char *vertexlabel_name = "T39_D";
    const char *pk_name = "T39_D_K0";
    const char *sk_name = "T39_D_hash";
    readJanssonFile("schemaFile/DefaultVertexLabel.gmjson", &vertexlabel_schema);
    ASSERT_NE((void *)NULL, vertexlabel_schema);
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexlabel_schema);
    AsyncUserDataT data = {0};

    //不merge有默认值的F0，只merge F9
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t priK = 1;
    int64_t NewSKValue = 2;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T39_D_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(g_stmt_async, priK);
    set_VertexProperty_SK(g_stmt_async, NewSKValue);
    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);
    // F0为默认值，F9为新值
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char defaultF0V = 'a';
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_CHAR, &defaultF0V);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &NewSKValue);
    EXPECT_EQ(GMERR_OK, ret);
    // merge F0
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    char newF0V = 'b';
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T39_D_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(g_stmt_async, priK);
    set_VertexProperty(g_stmt_async, newF0V);
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(2, data.affectRows);
    // read F0
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_CHAR, &newF0V);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.merge一个不存在的顶点，一个属性值设置为nullable=false，merge时不设置该数据，merge失败
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_005)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *vertexlabel_schema = NULL;
    const char *vertexlabel_name = "T39_NF";
    const char *pk_name = "T39_NF_K0";
    const char *sk_name = "T39_NF_hash";
    readJanssonFile("schemaFile/NullFalseVertexLabel.gmjson", &vertexlabel_schema);
    ASSERT_NE((void *)NULL, vertexlabel_schema);
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexlabel_schema);
    AsyncUserDataT data = {0};

    //不merge nullable=false的F0
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t priK = 1;
    int64_t SKValue = 1;
    int i = 1;
    set_VertexProperty_PK(g_stmt_async, priK);
    set_VertexProperty_SK(g_stmt_async, SKValue);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(g_stmt_async, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(g_stmt_async, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(g_stmt_async, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(g_stmt_async, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(0, data.affectRows);

    // free
    ret = GmcDropVertexLabel(g_stmt, vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.insert100个顶点，merge该顶点，replace该顶点，delete该顶点，remove该顶点
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_006)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 100;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt_async, i);
        set_VertexProperty_SK(g_stmt_async, newVal);
        set_VertexProperty(g_stmt_async, newVal);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(2, data.affectRows);
        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // replace Vertex

    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 200;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // remove Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.merge一个pk不冲突的顶点，二级 索引unique=true,存在二级索引冲突，merge失败
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_007)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema_test, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 100;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &newVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt_async, newVal);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, newVal);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.merge一个pk冲突的顶点，二级索引unique=true,存在二级索引冲突，merge失败
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_008)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema_test, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count + 1; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 100;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.merge一个pk不冲突的顶点，二级索引unique=Flase，构造相同的二级索引属性值，merge成功
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_009)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    char *vertexlabel_schema = NULL;
    const char *vertexlabel_name = "T39_SF";
    const char *pk_name = "T39_SF_K0";
    const char *sk_name = "T39_SF_hash";
    readJanssonFile("schemaFile/SkNotUniqueVertexLabel.gmjson", &vertexlabel_schema);
    ASSERT_NE((void *)NULL, vertexlabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexlabel_schema);

    int count = 100;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 100;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &newVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_SF_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt_async, newVal);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, newVal);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.merge一个pk不冲突的顶点，二级索引unique=Flase，构造不相同的二级索引属性值，merge成功
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_010)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    char *vertexlabel_schema = NULL;
    const char *vertexlabel_name = "T39_SF";
    const char *pk_name = "T39_SF_K0";
    const char *sk_name = "T39_SF_hash";
    readJanssonFile("schemaFile/SkNotUniqueVertexLabel.gmjson", &vertexlabel_schema);
    ASSERT_NE((void *)NULL, vertexlabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexlabel_schema);

    int count = 100;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 100;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &newVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_SF_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt_async, newVal);
        set_VertexProperty_SK(g_stmt_async, newVal);
        set_VertexProperty(g_stmt_async, newVal);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011.循环merge同一顶点1w次，循环remove该顶点1w次
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_011)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 10000;
    uint32_t val = 1;
    AsyncUserDataT data = {0};
    // merge same Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt_async, val);
        set_VertexProperty_SK(g_stmt_async, val);
        set_VertexProperty(g_stmt_async, val);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        if (i == 0) {
            EXPECT_EQ(1, data.affectRows);
        } else {
            EXPECT_EQ(2, data.affectRows);
        }
    }
    // remove same Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 0);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

void *mergeVertex(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn_t, &stmt_t, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    int count = 10;
    uint32_t val = 1000;
    for (uint32_t num = 0; num < count; num++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        printf("--------merge num:%d----------------\n", num);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(stmt_t, num);
        set_VertexProperty(stmt_t, val);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt_t, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 012.多线程并发merge同一顶点
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_012)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t count = 10;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        printf("--------insert num:%d----------\n", i);
    }
    printf("--------insert OK----------");
    // multi threads merge
    int tdNum = 8;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, mergeVertex, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014.使用superfiled给属性赋值，执行merge操作，读取数据
TEST_F(MergeAsync, DDL_015_MergeVertexAsync_014)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    char *vertexlabel_schema = NULL;
    const char *vertexlabel_name = "T39_SP";
    const char *pk_name = "T39_SP_K0";
    const char *sk_name = "T39_SP_hash";
    readJanssonFile("schemaFile/SuperfiledVertexLabel.gmjson", &vertexlabel_schema);
    ASSERT_NE((void *)NULL, vertexlabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexlabel_schema);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 3; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // use superfiled set property and merge Vertex
    uint32_t priK = 0;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T39_SP_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(g_stmt_async, priK);
    char *sp_1 = (char *)malloc(2);
    *(char *)sp_1 = 'c';
    *(unsigned char *)(sp_1 + 1) = 'd';
    ret = GmcSetSuperfieldById(g_stmt_async, 0, sp_1, 2);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(sp_1);

    // read F0/F1
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(2);
    ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 2);
    EXPECT_EQ('c', *(char *)sp_1_get);
    EXPECT_EQ('d', *(unsigned char *)(sp_1_get + 1));
    free(sp_1_get);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}
