[{"type": "record", "name": "vsys", "fields": [{"name": "id", "type": "int32", "nullable": false}, {"name": "F0", "type": "char", "nullable": true}, {"name": "F1", "type": "int64", "nullable": false}], "keys": [{"name": "id", "fields": ["id"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "vsys_sk", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "vsys::rule", "fields": [{"name": "vsys::id", "type": "int32", "nullable": false}, {"name": "id", "type": "int32", "nullable": false}, {"name": "name", "type": "string", "size": 8, "nullable": false}, {"name": "K0", "type": "char", "nullable": false}, {"name": "K1", "type": "int64", "nullable": false}], "keys": [{"name": "vsys::id_id", "fields": ["vsys::id", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "rule_sk", "fields": ["K1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "vsys::rule::source_ip", "fields": [{"name": "rule::vsys::id", "type": "int32", "nullable": false}, {"name": "rule::id", "type": "int32", "nullable": false}, {"name": "ipLen", "type": "int32", "nullable": false}, {"name": "maskLen", "type": "int32", "nullable": false}, {"name": "H0", "type": "char", "nullable": false}, {"name": "H1", "type": "int64", "nullable": false}], "keys": [{"name": "vsys.rule.source_ip_K0", "fields": ["rule::vsys::id", "rule::id", "ipLen", "maskLen"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "source_ip_sk", "fields": ["H1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}]