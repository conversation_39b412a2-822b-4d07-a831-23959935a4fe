[{"type": "list", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"type": "container", "name": "ListCon2", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}]}, {"type": "container", "name": "ListCon3", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}]}, {"type": "choice", "name": "choice4", "fields": [{"type": "case", "name": "case1", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}]}]}, {"type": "choice", "name": "choice5", "fields": [{"type": "case", "name": "case2", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "node": "T0", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T0", "name": "list_localhash<PERSON>ey", "fields": ["PID", "F0", "F1", "F2", "F3", "F4", "F5", "F6", "ListCon2/F0", "ListCon2/F1", "ListCon2/F2", "ListCon2/F3", "ListCon2/F4", "ListCon2/F5", "ListCon2/F6", "ListCon3/F0", "ListCon3/F1", "ListCon3/F2", "ListCon3/F3", "ListCon3/F4", "ListCon3/F5", "ListCon3/F6", "choice4/case1/F0", "choice4/case1/F1", "choice4/case1/F2", "choice4/case1/F3", "choice4/case1/F4", "choice4/case1/F5", "choice4/case1/F6", "choice5/case2/F1", "choice5/case2/F2", "choice5/case2/F3"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}]