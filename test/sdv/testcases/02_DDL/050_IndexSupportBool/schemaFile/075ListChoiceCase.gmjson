[{"type": "container", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"type": "container", "name": "company", "fields": [{"name": "comment", "type": "string"}, {"name": "name", "type": "string"}]}], "keys": [{"node": "T0", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "T0::T2", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "choice", "name": "choice1", "fields": [{"type": "case", "name": "case1", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case2", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case3", "fields": [{"name": "F0", "type": "boolean", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}]}]}], "keys": [{"name": "PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "UK", "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}, "fields": ["choice1/case1/F0", "choice1/case2/F0", "choice1/case3/F0"]}]}]