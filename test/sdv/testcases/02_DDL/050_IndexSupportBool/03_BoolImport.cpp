/*
唯一索引
    033 bool类型字段设置为primary key，预期导入表失败
    034 bool类型字段设置为localhash key，localhash唯一，预期导入表失败
    035 bool类型字段设置为local key，local唯一，预期导入表失败
    036 bool类型字段设置为hashcluster key，hashcluster唯一，预期导入表失败
    037 bool类型字段设置为LPM4，预期导入表失败
    038 bool类型字段设置为LPM6，预期导入表失败
    039 bool类型字段设置为member key，member唯一，预期导入表成功
    040 多个bool类型字段设置为member key，member唯一，预期导入表成功

非唯一索引
    041 bool类型字段设置为localhash key，localhash非唯一，预期导入表失败
    042 bool类型字段设置为local key，local非唯一，预期导入表失败
    043 bool类型字段设置为hashcluster key，hashcluster非唯一，预期导入表失败
    044 bool类型字段设置为member key，member非唯一，预期导入表成功
    045 多个bool类型字段设置为member key，member非唯一，预期导入表成功

多类型字段索引
    046 bool类型字段和int类型字段设置为localhash key，localhash唯一，预期导入表失败
    047 bool类型字段和int类型字段设置为local key，local唯一，预期导入表失败
    048 bool类型字段和int字段类型设置为hashcluster key，hashcluster唯一，预期导入表失败
    049 bool类型字段和int类型字段设置为LPM4，预期导入表失败
    050 bool类型字段和int类型字段设置为LPM6，预期导入表失败
    051 bool类型字段和int类型字段设置为member key，member唯一，预期导入表成功
*/

#include "IndexBool.h"

class BoolImport : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BoolImport::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void BoolImport::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void BoolImport::SetUp()
{
    // 建连
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
}

void BoolImport::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 唯一索引
// 033 bool类型字段设置为primary key，预期导入表失败
TEST_F(BoolImport, DDL_050_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"001PrinmaryBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
      files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 034 bool类型字段设置为localhash key，localhash唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"002LocalhashBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 035 bool类型字段设置为local key，local唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"003LocalBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
       files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 036 bool类型字段设置为hashcluster key，hashcluster唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"004HashclusterBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 037 bool类型字段设置为LPM4，预期导入表失败
TEST_F(BoolImport, DDL_050_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"005LPM4Bool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 038 bool类型字段设置为LPM6，预期导入表失败
TEST_F(BoolImport, DDL_050_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"006LPM6Bool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 039 bool类型字段设置为member key，member唯一，预期导入表成功
TEST_F(BoolImport, DDL_050_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"007OneMemberKeyBool";
    const char *errMsg = "successfully";

    GmcDropVertexLabel(g_stmt, g_labelName4);
    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 040 多个bool类型字段设置为member key，member唯一，预期导入表成功
TEST_F(BoolImport, DDL_050_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"008MultiMemberKeyBool";
    const char *errMsg = "successfully";

    GmcDropVertexLabel(g_stmt, g_labelName4);
    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 非唯一索引
// 041 bool类型字段设置为localhash key，localhash非唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"009LocalhashUniBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 042 bool类型字段设置为local key，local非唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"010LocalUniBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 043 bool类型字段设置为hashcluster key，hashcluster非唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"011HashclusterUniBool";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 044 bool类型字段设置为member key，member非唯一，预期导入表成功
TEST_F(BoolImport, DDL_050_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"012OneMemberKeyUniBool";
    const char *errMsg = "successfully";

    GmcDropVertexLabel(g_stmt, g_labelName4);
    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 045 多个bool类型字段设置为member key，member非唯一，预期导入表成功
TEST_F(BoolImport, DDL_050_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"013MultiMemberKeyUniBool";
    const char *errMsg = "successfully";

    GmcDropVertexLabel(g_stmt, g_labelName4);
    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 多类型字段索引
// 046 bool类型字段和int类型字段设置为localhash key，localhash唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"014LocalhashBoolint";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 047 bool类型字段和int类型字段设置为local key，local唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"015LocalBoolint";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 048 bool类型字段和int字段类型设置为hashcluster key，hashcluster唯一，预期导入表失败
TEST_F(BoolImport, DDL_050_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"016HashclusterBoolint";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 049 bool类型字段和int类型字段设置为LPM4，预期导入表失败
TEST_F(BoolImport, DDL_050_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"017LPM4Boolint";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 050 bool类型字段和int类型字段设置为LPM6，预期导入表失败
TEST_F(BoolImport, DDL_050_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"018LPM6Boolint";
    const char *errMsg = "import batch exec unsucc. ret = 1009003";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 051 bool类型字段和int类型字段设置为member key，member唯一，预期导入表成功
TEST_F(BoolImport, DDL_050_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "导入表");
    const char *files = (const char *)"019MultiMemberKeyBoolint";
    const char *errMsg = "successfully";

    memset(g_cmd, 0, sizeof(g_cmd));
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "gmimport -c vschema -f ./schemaFile/%s.gmjson -ns %s",
        files, g_testNameSpace);
    ret = executeCommand(g_cmd, errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
}
