set(compile_list "")
if(FEATURE_FASTPATH)
list(APPEND compile_list 001_CreateVertexLabel)
list(APPEND compile_list 002_OpenVertexLabel)
list(APPEND compile_list 003_DropVertexLabel)
list(APPEND compile_list 004_CreateEdgeLabel)
list(APPEND compile_list 006_DropEdgeLabel)
list(APPEND compile_list 011_CreateGraphLabel)
list(APPEND compile_list 012_BatchOperDDL)
list(APPEND compile_list 015_MergeVertex)
list(APPEND compile_list 016_ReplaceVertex)
list(APPEND compile_list 020_YangAllIndexTest)
list(APPEND compile_list 021_VariableLengthStorage)
list(APPEND compile_list 023_BitFieldUint32Or64)
list(APPEND compile_list 024_AutoIncrement)
list(APPEND compile_list 025_ByteBitfieldAndu16)
list(APPEND compile_list 026_GmcCreateJson)
list(APPEND compile_list 027_SupportResourceTypeDataFields)
list(APPEND compile_list 028_BitmapDataType)
list(APPEND compile_list 029_Truncate)
list(APPEND compile_list 030_namespace)
list(APPEND compile_list 031_GetChangeStatCount)
list(APPEND compile_list 032_Basic_Functions)
list(APPEND compile_list 033_V3Trans_Vector_Default)
list(APPEND compile_list 035_SchemaDefaultImportString)
list(APPEND compile_list 036_SchemeNodeName)
list(APPEND compile_list 037_BitmapCompatibleV3)
list(APPEND compile_list 038_Check_Validity)
list(APPEND compile_list 039_SupportCoverSchemaLabelName)
list(APPEND compile_list 040_TableID_CompatibleV3)
list(APPEND compile_list 041_ResourceVertexDropTruncate)
list(APPEND compile_list 042_Discriminate_Merge_Replace)
list(APPEND compile_list 043_GmddlDrop)
list(APPEND compile_list 044_AlterTablespaceMaxSize)
list(APPEND compile_list 045_Create_Schema_OnDemand)
list(APPEND compile_list 046_DropTableCompatibleV3)
list(APPEND compile_list 047_DDL_Atomicity)
list(APPEND compile_list 048_ClusteredEnhance)
list(APPEND compile_list 049_chainHash)
list(APPEND compile_list 050_IndexSupportBool)
list(APPEND compile_list 051_CreateTable_AsRequired)
list(APPEND compile_list 052_Customize_SizeOfFixedFields)
list(APPEND compile_list 053_CreateTable_OnDemand)
list(APPEND compile_list 054_NsClearTest)
endif()

verify_and_add_directory(${compile_list})
