[{"type": "record", "name": "TestResource022", "fields": [{"name": "F0", "type": "char", "nullable": false}, {"name": "F1", "type": "uchar", "nullable": true}, {"name": "F2", "type": "int8", "nullable": true}, {"name": "F3", "type": "uint8", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "int64", "nullable": true}, {"name": "F10", "type": "uint64", "nullable": true}, {"name": "F11", "type": "float", "nullable": true}, {"name": "F12", "type": "double", "nullable": true}, {"name": "F13", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "nullable": true, "size": 100}, {"name": "F15", "type": "bytes", "nullable": true, "size": 10}, {"name": "F16", "type": "fixed", "nullable": true, "size": 5}, {"name": "F17", "type": "uint32", "nullable": true}, {"name": "F18", "type": "resource", "nullable": true}, {"name": "F19", "type": "resource", "nullable": true}, {"name": "F20", "type": "resource", "nullable": true}, {"name": "F21", "type": "resource", "nullable": true}], "keys": [{"node": "T35", "name": "T35_PK", "fields": ["F6"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]