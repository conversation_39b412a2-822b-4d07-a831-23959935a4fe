extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#define MAX_VERTEX_NUM 10000
#define MAX_NAME_LENGTH 128
#define MAX_CMD_SIZE 1024
#define READ_LENGTH 1024

GmcConnT *conn;
GmcConnT *conn2;
GmcStmtT *stmt;
GmcStmtT *stmt2;
GmcConnT *connectionAsync = NULL;
char label_name1[] = "OP_T0";
char lalable_name_PK1[] = "OP_PK";
char label_name2[] = "DST_T0";
char lalable_name_PK2[] = "DST_PK";
char label_name3[] = "edgelabel_testEdge";
char g_label_config_test[] = "{\"max_record_num\":10000}";

int g_subIndex = 0;
int start_num = 0;
int end_num = 10;
int array_num = 3;
int vector_num = 3;
int g_data_num = 8;

void *label = NULL;
char *test_schema1 = NULL;
char *test_schema2 = NULL;
char *test_schema3 = NULL;
char *g_sub_info = NULL;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
const char *edgeLabelName = "edgelabel_testEdge";
char g_command[MAX_CMD_SIZE];
char *g_schema = NULL, *g_schema_2 = NULL;
GmcConnT *g_subChan = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
AsyncUserDataT data = {0};

GmcConnT *g_sub_conn = NULL;
const char *expect = NULL;

typedef enum EnumOpTypeNum {
    OpTypeNum_1 = 1,  // 只有1种dml类型，申请内存时只需申请1个g_data_nussssm的大小
    OpTypeNum_2,      // 有2种dml类型
    OpTypeNum_3,
} OpTypeNumE;

static const char *resPoolTestNameNULL = "resource_pool_testNULL";
static const char *gResPoolTestNULL =
    R"({
        "name" : "resPoolTestNameNULL",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 0,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *gResPoolExtendedName1 = "resource_pool_extended1";
static const char *gResPoolExtendedName2 = "resource_pool_extended2";
static const char *gResPoolExternal1 =
    R"({
        "name" : "resource_pool_extended1",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *gResPoolExternal2 =
    R"({
        "name" : "resource_pool_extended2",
        "pool_id" : 10002,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *resPoolTestName = "resource_pool_test";

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *resPoolTestName1 = "resource_pool_test1";

static const char *gResPoolTest1 =
    R"({
        "name" : "resource_pool_test1",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 65335,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *resPoolDumpName = "resource_pool_dump";
const char *deltaStoreJsonNormalSingle = "{  \
    \"delta_stores\":                        \
    [{                                       \
        \"name\": \"dsdml1\",    \
        \"init_mem_size\": 10485760,         \
        \"max_mem_size\": 20971520,          \
        \"extend_mem_size\": 6291456,        \
        \"page_size\": 16384                 \
    }]                                       \
}";

const char *test_delta_config_json = R"(
  {
      "max_record_num":10000,
      "delta_store_name":"dsdml1",
      "writers":"abc"     
  })";

void TestGmcSetNodePropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(
    GmcNodeT *node, int i, bool bool_value, char *f14_value, uint64_t respoolId, uint64_t count, uint64_t startIndex)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(2 * i, f2_value);

    /*     uint32_t f3_value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        EXPECT_EQ(3 * i, f3_value); */

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);
}

int TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName, uint64_t respoolId, uint64_t count, uint64_t startIndex)
{
    int32_t ret = 0;
    void *label = NULL;
    /*printf("labelName=%s, start_num=%d, end_num=%d, array_num=%d, vector_num=%d, respoolId=%d, count=%d,
     * startIndex=%d\n", labelName, start_num, end_num, array_num, vector_num, respoolId, count, startIndex);*/
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i * index);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value, respoolId, count, startIndex);
        TestGmcSetNodePropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i * index, bool_value, f14_value);
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i * index, bool_value, f14_value);
        }

        ret = GmcExecute(stmt);
        if (ret != 0) {
            printf("--------insert num =%d, ret = %d-------\n", i, ret);
        }
    }

    return ret;
}

void TestGmcExecScanVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int array_num, int vector_num)
{
    int32_t ret = 0;
    unsigned int isNull;
    int8_t f0_value;

    /* ret = GmcGetNodePropertyByName(stmt, (char *)"F0", &f0_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((unsigned int)0, isNull);
    EXPECT_EQ(1 * index, f0_value); */

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count;
    ret = GmcNodeGetElementCount(t3, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(vector_num, count);

    TestGmcGetNodePropertyByName_R(root, index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(t1, index, bool_value, f14_value);

    // 读取array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(t2, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(array_num, count);

    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(t2, j, &t2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_A(t2, index, bool_value, f14_value);
    }

    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V(t3, index, bool_value, f14_value);
    }
}

class ResourceFullMemoryTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ResourceFullMemoryTest::SetUpTestCase()
{}

void ResourceFullMemoryTest::TearDownTestCase()
{}
void ResourceFullMemoryTest::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void ResourceFullMemoryTest::TearDown()
{
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : 满内存的情况下，创建资源池
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : liuli lwx1035319
 Modification :
**************************************************************************** */
TEST_F(ResourceFullMemoryTest, DDL_027_224)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    if (g_envType == 2) {
        system("sh $TEST_HOME/tools/stop.sh");                       //修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=15\"");  //内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f ");
    } else {
        system("sh $TEST_HOME/tools/stop.sh");                        //修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  //内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f ");
    }
    int32_t ret = 0;
    uint64_t count = 1;

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ALREADY_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    GmcDropVertexLabel(stmt, label_name1);
    char label_name1[] = "OP_224";
    readJanssonFile("schema_file/resource_complex_op224.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDestroyResPool(stmt, resPoolTestName1);
    ret = GmcCreateResPool(stmt, gResPoolTest1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName1, label_name1);
    EXPECT_EQ(GMERR_OK, ret);

    int k = 0;
    // 普通同步插入数据
    while (1) {
        ret = TestGmcInsertVertex(
            stmt, 1, 0, (char *)"string", k, k + 1, array_num, vector_num, label_name1, 0xFFFF, count, 0xFFFFFFFF);
        if (ret != 0) {
            /*printf("-----total num = %d,ret = %d----\n", k, ret);*/
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        k++;
        if (k % 50000 == 0) {
            printf("-----k = %d,ret = %d----\n", k, ret);
        }
    }

    ret = GmcCreateResPool(stmt, gResPoolTest1);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ALREADY_EXIST, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    int start_num = 0;
    int end_num = k;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    free(test_schema1);

    ret = GmcUnbindResPoolFromLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(stmt, resPoolTestName1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
    GmcDetachAllShmSeg();
    testEnvClean();
}
