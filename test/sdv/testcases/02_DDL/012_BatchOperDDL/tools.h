#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include "gtest/gtest.h"
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <malloc.h>
#include <math.h>


using namespace std;

#define MAX_CONN 10
#define THR_NUM 100

// void *g_conn, *g_stmt;
void *g_conn[THR_NUM];
void *g_stmt[THR_NUM];

void *vertexLabel1 = NULL, *vertexLabel2 = NULL;
void *vertexLabel = NULL;
void *vtx_chd = NULL;
void *vtx_prt = NULL;
// void *g_vertexLabel = NULL;
void *g_vertexLabel[THR_NUM];
int32_t ret;
int conn_id = 0;
bool isNull;
unsigned int valueSize;
int total_num = 0;
int g_subIndex = 0;
unsigned int totalNum = 0;
unsigned int successNum = 0;
unsigned int len;
int affectRows;

/** test datatype label: write vertexLabel **/
char wr_int8 = 1;
// unsigned char wr_uint8 = '1';
uint8_t wr_uint8 = 24;
short wr_int16 = -1111;
unsigned short wr_uint16 = 1111;
int int32_tmp = -111111;
unsigned int uint32_tmp = 1111;
unsigned long long wr_uint64 = 11111111;
long long int64_tmp = -11111111;
int wr_int32 = -111111;
long long wr_long = -11111111;
float float_tmp = 1111.111;
double double_tmp = 1111.111;
bool bool_tmp = true;
char bytes_tmp[6] = "14";
char string_tmp[64] = "10.157.123.25";
char fixed_tmp[10] = "16";
char wr_fixed[36] = "write";
long long time_tmp = 369852147;
char char_tmp = -1;
unsigned char uchar_tmp = 11;
bool isFinish;
uint32_t vr_id = 0;

#define MAX_NAME_LENGTH 128

#define LABELNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024

#define NONE "\033[0m"
#define BLACK "\033[0;30m"
#define L_BLACK "\033[1;30m"
#define RED_S "\033[0;31m"

#define L_GREEN "\033[1;32m"
#define YELLOW_S "\033[1;33m"

#define L_BLUE "\033[1;34m"
#define PURPLE "\033[0;35m"
#define L_PURPLE "\033[1;35m"
#define CYAN "\033[0;36m"
#define L_CYAN "\033[1;36m"
#define GRAY "\033[0;37m"
#define WHITE "\033[1;37m"

#define TEST_INFO_1(testItem, fieldName, fieldvalue, thread_id, isprint)                              \
    do {                                                                                              \
        if (isprint == 1) {                                                                           \
            printf("[INFO][Thread_%d][%7s][%15s] %d \n", thread_id, testItem, fieldName, fieldvalue); \
        }                                                                                             \
        if () {                                                                                       \
        }                                                                                             \
    } while (0)

#define TEST_INFO(testItem, fieldName, fieldvalue, thread_id, isprint, isUniquleHash)                             \
    do {                                                                                                          \
        if (isprint == 1) {                                                                                       \
            if (isUniquleHash == 1)                                                                               \
                printf("[INFO][Thread_%d][%7s][" L_PURPLE "%17s" NONE "] %d \n", thread_id, testItem, fieldName,  \
                    fieldvalue);                                                                                  \
            else if (isUniquleHash == 0)                                                                          \
                printf("[INFO][Thread_%d][%17s][" YELLOW_S "%14s" NONE "] %d \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                  \
            else                                                                                                  \
                printf("[INFO][Thread_%d][%7s][%17s] %d \n", thread_id, testItem, fieldName, fieldvalue);         \
        }                                                                                                         \
    } while (0)

#define TEST_INFO_SUB(testItem, labelName, fieldName, fieldvalue, thread_id, isprint, isUniquleHash)                   \
    do {                                                                                                               \
        if (isprint == 1) {                                                                                            \
            if (isUniquleHash == 1)                                                                                    \
                printf("[INFO][Thread_%d][%15s][%7s][" L_PURPLE "%17s" NONE "] %d \n", thread_id, labelName, testItem, \
                    fieldName, fieldvalue);                                                                            \
            else if (isUniquleHash == 0)                                                                               \
                printf("[INFO][Thread_%d][%15s][%17s][" YELLOW_S "%14s" NONE "] %d \n", thread_id, labelName,          \
                    testItem, fieldName, fieldvalue);                                                                  \
            else                                                                                                       \
                printf("[INFO][Thread_%d][%15s][%7s][%17s] %d \n", thread_id, labelName, testItem, fieldName,          \
                    fieldvalue);                                                                                       \
        }                                                                                                              \
    } while (0)

#define TEST_INFO_STR(testItem, fieldName, fieldvalue, thread_id, isprint, isUniquleHash)                        \
    do {                                                                                                         \
        if (isprint == 1) {                                                                                      \
            if (isUniquleHash == 1)                                                                              \
                printf("[INFO][Thread_%d][%7s][" L_PURPLE "%17s" NONE "] %s \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else if (isUniquleHash == 0)                                                                         \
                printf("[INFO][Thread_%d][%7s][" YELLOW_S "%17s" NONE "] %s \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else                                                                                                 \
                printf("[INFO][Thread_%d][%7s][%17s] %s \n", thread_id, testItem, fieldName, fieldvalue);        \
        }                                                                                                        \
    } while (0)

#define TEST_SCAN_DEL_RES(expect_status, ret, loop)                                              \
    do {                                                                                         \
        if ((ret) != (expect_status)) {                                                          \
            fprintf(stderr,                                                                      \
                "[" CYAN "Test" NONE "][" RED_S "Error" NONE                                     \
                "][oper_num: %d][File: %s:%d Func: %s] expect %lu, real %lu\n",                  \
                loop, __FILE__, __LINE__, __func__, (uint64_t)(expect_status), (uint64_t)(ret)); \
            break;                                                                               \
        }                                                                                        \
    } while (0)

#define TEST_ASSERT_EQ(expect_status, ret)                                                                     \
    do {                                                                                                       \
        if ((ret) != (expect_status)) {                                                                        \
            fprintf(stderr,                                                                                    \
                "[" CYAN "Test" NONE "][" RED_S "Error" NONE "][File: %s:%d Func: %s] expect %lu, real %lu\n", \
                __FILE__, __LINE__, __func__, (uint64_t)(expect_status), (uint64_t)(ret));                     \
            continue;                                                                                          \
        }                                                                                                      \
    } while (0)

int32_t queryFieldValueAndCompare(
    GmcStmtT *stmt, const char *fieldName, void *readValueOut, unsigned int *getValueSizeOut)
{
    if (0 == strcmp(fieldName, "port.on_board")) {
        ret = GmcGetVertexPropertySizeByName(stmt, (char *)fieldName, getValueSizeOut);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetVertexPropertyByName(stmt, (char *)fieldName, readValueOut, *getValueSizeOut, &isNull);
        EXPECT_EQ(GMERR_OK, ret);

        return ret;
    }
    ret = GmcGetVertexPropertySizeByName(stmt, fieldName, getValueSizeOut);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, fieldName, readValueOut, *getValueSizeOut, &isNull);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcGetVertexPropertyByName(stmt, fieldName, readValueOut, valueSize, &isNull);
    // EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

/* 写数据 label_name:  schema_datatype */
void test_insert_vertex_datatype(GmcStmtT *stmt, int oper_nums)
{
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, "schema_datatype", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &wr_int8, sizeof(wr_int8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &wr_int16, sizeof(wr_int16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        // hash_2   unique = true
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash_2   unique = true
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &int64_tmp, sizeof(int64_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT32, &wr_int32, sizeof(wr_int32));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &wr_long, sizeof(wr_long));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &float_tmp, sizeof(float_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &double_tmp, sizeof(double_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_BOOL, &bool_tmp, sizeof(bool_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        char bytes_tmp[6] = "14";
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_BYTES, bytes_tmp, strlen(bytes_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, fixed_tmp, strlen(fixed_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_TIME, &time_tmp, sizeof(time_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_CHAR, &char_tmp, sizeof(char_tmp));
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UCHAR, &uchar_tmp, sizeof(uchar_tmp));
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
}
/* 主键读 label_name:  schema_datatype */
int32_t PK_read_fieldValue_datatype(
    GmcConnT *conn, const char *keyName, GmcDataTypeE datatype, int oper_nums, int isPrint = 1)
{
    GmcStmtT *stmt1;
    ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt1, "schema_datatype", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);

        int valueF0;
        ret = queryFieldValueAndCompare(stmt1, "F0", &valueF0, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        EXPECT_EQ(loop, valueF0);

        char r_int8;
        ret = queryFieldValueAndCompare(stmt1, "F1", &r_int8, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(wr_int8, r_int8);
        EXPECT_EQ(1, valueSize);

        unsigned char rd_uint8;
        ret = queryFieldValueAndCompare(stmt1, "F2", &rd_uint8, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(wr_uint8, rd_uint8);
        EXPECT_EQ(1, valueSize);

        short rd_int16;
        ret = queryFieldValueAndCompare(stmt1, "F3", &rd_int16, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(wr_int16, rd_int16);
        EXPECT_EQ(2, valueSize);

        unsigned short r_uint16;
        ret = queryFieldValueAndCompare(stmt1, "F4", &r_uint16, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(wr_uint16, r_uint16);
        EXPECT_EQ(2, valueSize);

        int r_F5;
        ret = queryFieldValueAndCompare(stmt1, "F5", &r_F5, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(loop, r_F5);
        EXPECT_EQ(4, valueSize);

        unsigned int r_F6;
        ret = queryFieldValueAndCompare(stmt1, "F6", &r_F6, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(loop, r_F6);
        EXPECT_EQ(4, valueSize);

        if (isPrint == 1) {
            printf("\n[INFO] ======== Write value ======== \n");
            printf("[ F0  int32 ]  %d \n[ F1   int8 ]  %d \n[ F2  uint8 ]  %d \n[ F3  int16 ]  %d \n[ F4 uint16 ]  %d "
                   "\n[ F5  int16 ]  %d \n[ F6  int16 ]  %d \n",
                valueF0, r_int8, rd_uint8, rd_int16, r_uint16, r_F5, r_F6);
        }
    }
    return ret;
}

/******* ipv4地址 转换为 uint32 *******/
unsigned int trans_ip(const char *ip_str)
{
    //我当前的IP是: *************** (增加一个实例来判断)
    // char s[] = ip_str;
    char *ipstr = NULL;
    char str_ip_index[4] = {'\0'};
    unsigned int ip_int, ip_add = 0, ip_int_index[4], ip_temp_numbr = 24;
    int j = 0, a = 3;
    for (unsigned int i = 0; i <= strlen(ip_str); i++)  //要用到'\0'
    {
        if (ip_str[i] == '\0' || ip_str[i] == '.') {
            ip_int = atoi(str_ip_index);
            if (ip_int < 0 || ip_int > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ip_add += (ip_int * ((unsigned int)pow(256.0, a)));
            a--;
            memset(str_ip_index, 0, sizeof(str_ip_index));
            j = 0;
            continue;
        }
        str_ip_index[j] = ip_str[i];
        j++;
    }
    // printf("[INFO] IP str: %s, uint32 value: %u\n", ip_str, ip_add);
    return ip_add;
}

/******* 写数据 ip4forward #7 *******/
char ip_addr[16];
int32_t test_insert_vertex_ip4forward(GmcStmtT *stmt, int conn_id, int oper_begin, int oper_end)
{
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t mask_len = wr_uint8 + loop;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // printf("[ERROR][WRITE_%d] vr_id: %d, vrf_index: %d, ip_str: %lu, mask_len: %d \n", loop, conn_id, conn_id,
        // trans_val, wr_uint8);

        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &mask_len, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        // true
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // true
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // [7] ip4forward: nhp_group_id
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        // EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        // fixed 36
        // char *wr_fixed = (char *)"write";
        // ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
        // EXPECT_EQ(GMERR_OK, ret);

        // fixed 16
        // ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
        // EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

/******* 写数据 nhp_group #8 *******/
int32_t test_insert_vertex_nhp_group(GmcStmtT *stmt, int oper_begin, int oper_end)
{

    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {

        // pk   [8] nhp_group: nhp_group_id
        uint32_t vr_id = 0;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "ref_count", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "new_vrf", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_number", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_type_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
#if 0				
		ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);

		ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
		
		// fixed 36
		char *wr_fixed = (char *)"write";
		ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
		EXPECT_EQ(GMERR_OK, ret);
		
		// fixed 16		
		ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
		EXPECT_EQ(GMERR_OK, ret);

		ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
		EXPECT_EQ(GMERR_OK, ret);
#endif
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

/******* 写数据 nhp_group_node #9 *******/
int32_t test_insert_vertex_nhp_group_node(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    uint32_t vr_id = 0;
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {

        // pk         [9] nhp_group_node: nhp_group_id
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

#if 0				
		ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);

		ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
		
		// fixed 36
		char *wr_fixed = (char *)"write";
		ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
		EXPECT_EQ(GMERR_OK, ret);
		
		// fixed 16		
		ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
		EXPECT_EQ(GMERR_OK, ret);

		ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
		EXPECT_EQ(GMERR_OK, ret);
#endif
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

/******* 写数据 nhp #10 *******/
int32_t test_insert_vertex_nhp(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {

        // pk   [10] nhp: nhp_index
        ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "origin_nhp", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_num", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        // hashcluster name: vrfid_hashcluster_key[0]
        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
#if 0
		ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);

		ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
		EXPECT_EQ(GMERR_OK, ret);
		
		// fixed 36
		char *wr_fixed = (char *)"write";
		ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
		EXPECT_EQ(GMERR_OK, ret);
		
		// fixed 16		
		ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, strlen(wr_fixed));
		EXPECT_EQ(GMERR_OK, ret);
	
		ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
				
		ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
		EXPECT_EQ(GMERR_OK, ret);
		
		ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
		EXPECT_EQ(GMERR_OK, ret);

		ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
		EXPECT_EQ(GMERR_OK, ret);
#endif
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

/* 主键读 label_name:  ip4forward */
GmcStmtT *g_stmt_rd[THR_NUM];
void *g_vertexLabel_rd[THR_NUM];
int32_t test_PK_read_ip4forward(GmcConnT *conn, const char *keyName, int read_begin, int read_end,
    GmcDataTypeE datatype, int thread_id, const char *comment, int isPrint = 1)
{
    GmcStmtT *stmt1;
    ret = GmcAllocStmt(conn, &g_stmt_rd[thread_id]);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int loop = read_begin; loop < read_end; loop++) {

        ret = testGmcPrepareStmtByLabelName(g_stmt_rd[thread_id], "ip4forward", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_rd[thread_id], keyName);
        EXPECT_EQ(GMERR_OK, ret);

        // "vr_id", "vrf_index", "dest_ip_addr", "mask_len"
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 3, GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_rd[thread_id]);
        TEST_SCAN_DEL_RES(0, ret, loop);
        if (ret != 0) {
            return ret;
        }

        // EXPECT_EQ(GMERR_OK,ret);

        unsigned int rd_vr_id;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "vr_id", &rd_vr_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "vr_id", rd_vr_id, thread_id, isPrint, 999);

        unsigned int rd_vrf_index;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "vrf_index", &rd_vrf_index, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "vrf_index", rd_vrf_index, thread_id, isPrint, 999);

        unsigned int rd_dest_ip_addr;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "dest_ip_addr", &rd_dest_ip_addr, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "dest_ip_addr", rd_dest_ip_addr, thread_id, isPrint, 999);

        unsigned short rd_uint16;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "qos_profile_id", &rd_uint16, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(wr_uint8, rd_uint16);
        EXPECT_EQ(2, valueSize);
        TEST_INFO(comment, "qos_profile_id", rd_uint16, thread_id, isPrint, 0);

        unsigned int rd_nhp_group_id;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "nhp_group_id", &rd_nhp_group_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "nhp_group_id", rd_nhp_group_id, thread_id, isPrint, 0);

        unsigned char rd_mask_len;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "mask_len", &rd_mask_len, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);
        TEST_INFO(comment, "mask_len", rd_mask_len, thread_id, isPrint, 999);

        unsigned int rd_path_flags;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "path_flags", &rd_path_flags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "path_flags", rd_path_flags, thread_id, isPrint, 999);

        unsigned int rd_flags;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "flags", &rd_flags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "flags", rd_flags, thread_id, isPrint, 999);

        unsigned int rd_app_version;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "app_version", &rd_app_version, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "app_version", rd_app_version, thread_id, isPrint, 999);

        unsigned int rd_primary_label;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "primary_label", &rd_primary_label, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "primary_label", rd_primary_label, thread_id, isPrint, 1);

        unsigned int rd_attribute_id;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "attribute_id", &rd_attribute_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "attribute_id", rd_attribute_id, thread_id, isPrint, 1);

        unsigned char rd_nhp_group_flag;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "nhp_group_flag", &rd_nhp_group_flag, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(wr_uint8, rd_nhp_group_flag);
        EXPECT_EQ(1, valueSize);
        TEST_INFO(comment, "nhp_group_flag", rd_nhp_group_flag, thread_id, isPrint, 999);

        unsigned long long rd_app_obj_id;
        ret = queryFieldValueAndCompare(g_stmt_rd[thread_id], "app_obj_id", &rd_app_obj_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(wr_uint8, rd_app_obj_id);
        EXPECT_EQ(8, valueSize);
        TEST_INFO(comment, "app_obj_id", rd_app_obj_id, thread_id, isPrint, 999);

        char *rd_svc_ctx_high_prio_fixed = (char *)malloc(sizeof(char) * 36);
        ret = queryFieldValueAndCompare(
            g_stmt_rd[thread_id], "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(36, valueSize);
        TEST_INFO_STR(comment, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, thread_id, isPrint, 999);
        free(rd_svc_ctx_high_prio_fixed);

#if 0		
		char *rd_svc_ctx_high_prio_fixed = (char *)malloc(sizeof(char) * 36);
		ret = GmcGetVertexPropertySizeByName(stmt1, "svc_ctx_high_prio", &valueSize);
		EXPECT_EQ(GMERR_OK,ret);
		ret = GmcGetVertexPropertyByName(stmt1, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, valueSize, &isNull);    
		EXPECT_EQ(GMERR_OK, ret); 
		printf("[INFO][ERROR] fixed value %s \n", rd_svc_ctx_high_prio_fixed);
#endif
        // char rd_fixed_1[37];
        // ret = GmcGetVertexPropertySizeByName(stmt1, "svc_ctx_high_prio", &valueSize);
        // EXPECT_EQ(GMERR_OK,ret);
        // ret = GmcGetVertexPropertyByName(stmt1, "svc_ctx_high_prio", rd_fixed_1, valueSize, &isNull);
        // EXPECT_EQ(GMERR_OK, ret);
        // printf("[INFO][ERROR] fixed value %s \n", rd_fixed_1);

        if (isPrint == 1)
            printf("\n");
    }

    GmcFreeIndexKey(g_stmt_rd[thread_id]);
    GmcFreeStmt(g_stmt_rd[thread_id]);
    return ret;
}

/***** check value with pk *****/
// void *vertexLabel2 = NULL;
GmcStmtT *stmt2;
int32_t check_value_prepare(GmcConnT *conn, const char *labelName, const char *keyName, int keyValue)
{

    ret = GmcAllocStmt(conn, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2, 2, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int32_t check_value(
    const char *fieldName, GmcDataTypeE datatype, void *expectValue, const char *comment, int isPrint = 1)
{

    switch (datatype) {
        case GMC_DATATYPE_UINT32:
            unsigned int rd_uint32;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint32, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(uint32_t *)expectValue, rd_uint32);
            EXPECT_EQ(4, valueSize);
            TEST_INFO(comment, fieldName, rd_uint32, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_CHAR:
            char rd_int8;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_int8, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(char *)expectValue, rd_int8);
            EXPECT_EQ(4, valueSize);
            TEST_INFO(comment, fieldName, rd_int8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT16:
            unsigned short rd_uint16;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint16, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned short *)expectValue, rd_uint16);
            EXPECT_EQ(2, valueSize);
            TEST_INFO(comment, fieldName, rd_uint16, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT8:
            unsigned char rd_uint8;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint8, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned char *)expectValue, rd_uint8);
            EXPECT_EQ(1, valueSize);
            TEST_INFO(comment, fieldName, rd_uint8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT64:
            unsigned long long rd_uint64;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint64, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned long long *)expectValue, rd_uint64);
            EXPECT_EQ(8, valueSize);
            TEST_INFO(comment, fieldName, rd_uint64, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_FIXED:
            char rd_fixed[37] = {};
            ret = queryFieldValueAndCompare(stmt2, fieldName, rd_fixed, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ((char *)expectValue, rd_fixed);
            EXPECT_EQ(36, valueSize);
            TEST_INFO_STR(comment, fieldName, rd_fixed, 0, isPrint, 0);
            break;

            // case GMC_DATATYPE_UCHAR :
            // case GMC_DATATYPE_INT8 :
            // case GMC_DATATYPE_INT16 :
            // case GMC_DATATYPE_INT32 :
            // case GMC_DATATYPE_BOOL :
            // case GMC_DATATYPE_INT64 :
            // case GMC_DATATYPE_FLOAT :
            // case GMC_DATATYPE_DOUBLE :
            // case GMC_DATATYPE_TIME :
            // case GMC_DATATYPE_STRING :
            // case GMC_DATATYPE_BYTES :
    }
    return ret;
}

/* hash索引 Scan */
int scan_end = 0;
extern unsigned char up_nhp_group_flag;
extern unsigned short up_qos_profile_id;
int test_hashInx_scan_ip4forward(
    GmcStmtT *stmt, const char *keyName, int oper_nums, int thread_id, const char *comment, int isPrint = 1)
{

    scan_end = 0;
    // ret = GmcExecScanVertex(stmt, vertexLabel, keyName);
    // TEST_ASSERT_EQ(0, ret);
    int cnt = 0;
    // while(!isFinish){
    for (int loop = 0; loop < oper_nums + 1; loop++) {
        cnt++;
        // scan_end = cnt;
        ret = GmcFetch(stmt, &isFinish);
        TEST_SCAN_DEL_RES(0, ret, cnt);
        // printf("[INFO] LOOP_%d gmcfetch num: %d, GmcFetch status %d \n", cnt, scan_end, ret);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", cnt, ret);
            scan_end = cnt;
            return GMERR_OK;
        }
        unsigned int sc_vr_id;
        ret = queryFieldValueAndCompare(stmt, "vr_id", &sc_vr_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "vr_id", sc_vr_id, thread_id, isPrint, 999);
#if 1
        unsigned int sc_vrf_index;
        ret = queryFieldValueAndCompare(stmt, "vrf_index", &sc_vrf_index, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "vrf_index", sc_vrf_index, thread_id, isPrint, 999);

        unsigned int sc_dest_ip_addr;
        ret = queryFieldValueAndCompare(stmt, "dest_ip_addr", &sc_dest_ip_addr, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "dest_ip_addr", sc_dest_ip_addr, thread_id, isPrint, 999);

        unsigned short sc_qos_profile_id;
        ret = queryFieldValueAndCompare(stmt, "qos_profile_id", &sc_qos_profile_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(up_qos_profile_id, sc_qos_profile_id);  // update field
        EXPECT_EQ(2, valueSize);
        TEST_INFO(comment, "qos_profile_id", sc_qos_profile_id, thread_id, isPrint, 0);

        unsigned int sc_nhp_group_id;
        ret = queryFieldValueAndCompare(stmt, "nhp_group_id", &sc_nhp_group_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        // EXPECT_EQ(uint32_tmp, sc_nhp_group_id);
        TEST_INFO(comment, "nhp_group_id", sc_nhp_group_id, thread_id, isPrint, 0);

        // batch update 3 fields
        unsigned int sc_path_flags;
        ret = queryFieldValueAndCompare(stmt, "path_flags", &sc_path_flags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "path_flags", sc_path_flags, thread_id, isPrint, 999);

        unsigned int sc_flags;
        ret = queryFieldValueAndCompare(stmt, "flags", &sc_flags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "flags", sc_flags, thread_id, isPrint, 999);

        unsigned int sc_app_version;
        ret = queryFieldValueAndCompare(stmt, "app_version", &sc_app_version, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "app_version", sc_app_version, thread_id, isPrint, 999);

        unsigned int sc_table_smooth_id;
        ret = queryFieldValueAndCompare(stmt, "table_smooth_id", &sc_table_smooth_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "table_smooth_id", sc_table_smooth_id, thread_id, isPrint, 999);

        unsigned int sc_primary_label;
        ret = queryFieldValueAndCompare(stmt, "primary_label", &sc_primary_label, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "primary_label", sc_primary_label, thread_id, isPrint, 1);

        unsigned int sc_attribute_id;
        ret = queryFieldValueAndCompare(stmt, "attribute_id", &sc_attribute_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "attribute_id", sc_attribute_id, thread_id, isPrint, 1);

        unsigned char sc_nhp_group_flag;
        ret = queryFieldValueAndCompare(stmt, "nhp_group_flag", &sc_nhp_group_flag, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(up_nhp_group_flag, sc_nhp_group_flag);  // update field
        EXPECT_EQ(1, valueSize);
        TEST_INFO(comment, "nhp_group_flag", sc_nhp_group_flag, thread_id, isPrint, 999);

        char rd_svc_ctx_high_prio_fixed[37] = {};
        ret = queryFieldValueAndCompare(stmt, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(36, valueSize);
        TEST_INFO_STR(comment, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, thread_id, isPrint, 999);

        if (isPrint == 1)
            printf("\n");
#endif
    }
    GmcResetStmt(stmt);
    // EXPECT_EQ(GMERR_OK, ret);
    // GmcFreeIndexKey(stmt);
    return ret;
}

/******* 批量写 ip4forward #7 *******/
void set_vtxLabel_pk_field_ip4forward(GmcStmtT *stmt, int pk_value)
{

    uint32_t vr_id = 0;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    sprintf(ip_addr, "192.168.%d.0", pk_value);
    uint32_t trans_val = trans_ip(ip_addr);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &trans_val, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_vtxLabel_pk_field_ip4forward_batch(GmcStmtT *stmt, int pk_value)
{

    // uint32_t vr_id = 0;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // sprintf(ip_addr, "192.168.%d.0", pk_value);
    // uint32_t trans_val = trans_ip(ip_addr);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_vtxLabel_field_ip4forward(GmcStmtT *stmt, int set_value)
{

    // ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    // EXPECT_EQ(GMERR_OK, ret);
    int res = 0;
    res = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, res);

    // hash index: unique = false
    res = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, res);

    // hash index: unique = false  [7] ip4forward: nhp_group_id
    res = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, res);
    // fixed 16
    char fixed_tmp[36] = "aaaaaa";
    res = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, fixed_tmp, 36);
    EXPECT_EQ(GMERR_OK, res);
    // fixed 16
    res = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, fixed_tmp, 36);
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, res);

    res = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, res);
}

/******* 批量写 nhp_group #8 *******/
void set_vtxLabel_pk_field_nhp_group(GmcStmtT *stmt, int pk_value)
{

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_vtxLabel_field_nhp_group(GmcStmtT *stmt, int set_value)
{

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "ref_count", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "new_vrf", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);
}

/******* 批量写 nhp_group_node #9 *******/
void set_vtxLabel_pk_field_nhp_group_node(GmcStmtT *stmt, int pk_value)
{

    // [9] nhp_group_node: nhp_group_id
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
    // [9] nhp_group_node: primary_nhp_id --> [10] nhp: nhp_index
    ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
    // [9] nhp_group_node: backup_nhp_id --> [10] nhp: nhp_index
    ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_vtxLabel_field_nhp_group_node(GmcStmtT *stmt, int set_value)
{

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "group_smooth_id", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);
}

/******* 批量写 nhp #10 *******/
void set_vtxLabel_pk_field_nhp(GmcStmtT *stmt, int pk_value)
{

    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_vtxLabel_hash_field_nhp(GmcStmtT *stmt, int hash_value)
{

    // "app_source_id", "vr_id", "vrf_index"
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &hash_value, sizeof(hash_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_vtxLabel_field_nhp(GmcStmtT *stmt, int set_value)
{

    ret = GmcSetVertexProperty(stmt, "origin_nhp", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_num", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "ref_cnt", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "iid_flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);
}

/************* SN 条件订阅 *************/
int32_t check_value_sn1(GmcStmtT *sub_stmt, const char *labelName, const char *fieldName, int thread_id,
    GmcDataTypeE datatype, void *expectValue, const char *comment, int isPrint = 1)
{

    switch (datatype) {
        case GMC_DATATYPE_UINT32:
            unsigned int rd_uint32;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint32, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(uint32_t *)expectValue, rd_uint32);
            EXPECT_EQ(4, valueSize);
            TEST_INFO_SUB(comment, labelName, fieldName, rd_uint32, thread_id + 1, isPrint, 0);
            break;
        case GMC_DATATYPE_CHAR:
            char rd_int8;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_int8, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(char *)expectValue, rd_int8);
            EXPECT_EQ(4, valueSize);
            TEST_INFO_SUB(comment, labelName, fieldName, rd_int8, thread_id + 1, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT16:
            unsigned short rd_uint16;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint16, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned short *)expectValue, rd_uint16);
            EXPECT_EQ(2, valueSize);
            TEST_INFO_SUB(comment, labelName, fieldName, rd_uint16, thread_id + 1, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT8:
            unsigned char rd_uint8;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint8, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned char *)expectValue, rd_uint8);
            EXPECT_EQ(1, valueSize);
            TEST_INFO_SUB(comment, labelName, fieldName, rd_uint8, thread_id + 1, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT64:
            unsigned long long rd_uint64;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint64, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned long long *)expectValue, rd_uint64);
            EXPECT_EQ(8, valueSize);
            TEST_INFO_SUB(comment, labelName, fieldName, rd_uint64, thread_id + 1, isPrint, 0);
            break;
        case GMC_DATATYPE_FIXED:
            char rd_fixed[37] = {};
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, rd_fixed, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ((char *)expectValue, rd_fixed);
            EXPECT_EQ(36, valueSize);
            TEST_INFO_STR(comment, fieldName, rd_fixed, thread_id + 1, isPrint, 0);
            // TEST_INFO_STR(comment, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, thread_id, isPrint, 999);
            break;

            // case GMC_DATATYPE_UCHAR :
            // case GMC_DATATYPE_INT8 :
            // case GMC_DATATYPE_INT16 :
            // case GMC_DATATYPE_INT32 :
            // case GMC_DATATYPE_BOOL :
            // case GMC_DATATYPE_INT64 :
            // case GMC_DATATYPE_FLOAT :
            // case GMC_DATATYPE_DOUBLE :
            // case GMC_DATATYPE_TIME :
            // case GMC_DATATYPE_STRING :
            // case GMC_DATATYPE_BYTES :
    }
    return ret;
}

int check_value_sn(GmcStmtT *sub_stmt, const char *fieldName, GmcDataTypeE datatype, void *expectValue,
    const char *comment, int isPrint = 1)
{

    switch (datatype) {
        case GMC_DATATYPE_UINT32:
            unsigned int rd_uint32;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint32, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(uint32_t *)expectValue, rd_uint32);
            EXPECT_EQ(4, valueSize);
            TEST_INFO(comment, fieldName, rd_uint32, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_CHAR:
            char rd_int8;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_int8, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(char *)expectValue, rd_int8);
            EXPECT_EQ(4, valueSize);
            TEST_INFO(comment, fieldName, rd_int8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT16:
            unsigned short rd_uint16;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint16, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            // EXPECT_EQ(*(unsigned short *)expectValue, rd_uint16);
            EXPECT_EQ(2, valueSize);
            TEST_INFO(comment, fieldName, rd_uint16, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT8:
            unsigned char rd_uint8;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint8, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned char *)expectValue, rd_uint8);
            EXPECT_EQ(1, valueSize);
            TEST_INFO(comment, fieldName, rd_uint8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT64:
            unsigned long long rd_uint64;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint64, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned long long *)expectValue, rd_uint64);
            EXPECT_EQ(8, valueSize);
            TEST_INFO(comment, fieldName, rd_uint64, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_FIXED:
            char rd_fixed[37] = {};
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, rd_fixed, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ((char *)expectValue, rd_fixed);
            EXPECT_EQ(34, valueSize);
            TEST_INFO_STR(comment, fieldName, rd_fixed, 0, isPrint, 0);
            break;

            // case GMC_DATATYPE_UCHAR :
            // case GMC_DATATYPE_INT8 :
            // case GMC_DATATYPE_INT16 :
            // case GMC_DATATYPE_INT32 :
            // case GMC_DATATYPE_BOOL :
            // case GMC_DATATYPE_INT64 :
            // case GMC_DATATYPE_FLOAT :
            // case GMC_DATATYPE_DOUBLE :
            // case GMC_DATATYPE_TIME :
            // case GMC_DATATYPE_STRING :
            // case GMC_DATATYPE_BYTES :
    }
    return ret;
}

#if 0

void* conn_sn_sync;
// void *g_label = NULL;
void sn_push_check12(void *sn_conn, void *g_vertex, uint32_t sub_type, const char *vtx_labelName, const char *fieldName,GmcDataTypeE fieldType, int oper_nums, void* oldValue, void* newValue, const char *comment, int isprint = 0)
{
	printf("[INFO] into sn check. \n");
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
			
	for (int i = 0; i < oper_nums; i++) {
        
		void *subStmt = NULL;
		void *new_vtx;
		ret = GmcAllocStmt(sn_conn, &subStmt);
		EXPECT_EQ(GMERR_OK, ret);
		// ret = GmcOpenVertexLabelByName(stmt, vtx_labelName, &g_vertex);
		// EXPECT_EQ(GMERR_OK,ret);
		
		// ret = GmcOpenVertexLabelByName(subStmt, vtx_labelName, &new_vtx);
		// EXPECT_EQ(GMERR_OK,ret);	
		
        // check RecvPushMsg: event_type, vrtxLabelNum
		char pushVrtxName[MAX_NAME_LENGTH] = {0};
		uint32_t vrtxNameLen = MAX_NAME_LENGTH;
		uint32_t eventType, vrtxLabelNum, msgType;
		//ret = GmcRecvSubPushMsg(subStmt, &eventType, &vrtxLabelNum, &msgType);
		EXPECT_EQ(GMERR_OK, ret);
		// EXPECT_EQ((uint32_t)sub_type, eventType);
		EXPECT_EQ((uint32_t)1, vrtxLabelNum);
		printf("[INFO] recv msg event_type: %d \n", eventType);
		
        ret = GmcGetSubPushVertexLabelName(subStmt, 0, pushVrtxName, &vrtxNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(vtx_labelName, pushVrtxName);
        EXPECT_EQ(strlen(vtx_labelName), vrtxNameLen);
		if(ret == 0)
			 sub_num++;

        bool eof = false;    
		while (!eof){
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true ) {
                break;
            }    
            switch(eventType)
            {
				case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
                {		
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, newValue, comment, isprint);
					sub_push_add++;
					break;
				}
				case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
                {				
					// check old value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
					EXPECT_EQ(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, oldValue, comment, isprint);
					sub_push_del++;
					break;
				}			
				case GMC_SUB_EVENT_UPDATE:
                {
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, newValue, comment, isprint);
					// check old value				
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, oldValue, comment, isprint);
					sub_push_updt++;
					break;
				}
				default:
                {
                    printf("default: invalid eventType\r\n");
                    break;
                }
			} 
			GmcFreeStmt(subStmt);
		}
	}
		
	fprintf(stderr, "[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n\n", vtx_labelName, sub_push_add, sub_push_updt, sub_push_del);
}

void sn_push_check(GmcConnT *sn_conn, void *g_vertex, int thread_id, uint32_t sub_type, const char *vtx_labelName, const char *fieldName, GmcDataTypeE fieldType, int oper_nums, void* oldValue, void* newValue, const char *comment, int isprint = 0)
{
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
	
	for (int i = 0; i < oper_nums; i++) {
		
         *subStmt = NULL;	
        ret = GmcAllocStmt(sn_conn, &subStmt);
        EXPECT_EQ(GMERR_OK, ret);
		
        // check RecvPushMsg: event_type, vrtxLabelNum
		char pushVrtxName[MAX_NAME_LENGTH] = {0};
		uint32_t vrtxNameLen = MAX_NAME_LENGTH;
		uint32_t eventType, vrtxLabelNum, msgType;
		//ret = GmcRecvSubPushMsg(subStmt, &eventType, &vrtxLabelNum, &msgType);
		EXPECT_EQ(GMERR_OK, ret);
		// EXPECT_EQ((uint32_t)sub_type, eventType);
		EXPECT_EQ((uint32_t)1, vrtxLabelNum);
		
        ret = GmcGetSubPushVertexLabelName(subStmt, 0, pushVrtxName, &vrtxNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(vtx_labelName, pushVrtxName);
        EXPECT_EQ(strlen(vtx_labelName), vrtxNameLen);
		if(ret == 0)
			 sub_num++;

        bool eof = false;    
		while (!eof){
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true ) {
                break;
            }    
            switch(eventType)
            {
				case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value, isFetchOldVrtx = false
                {		
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, thread_id, fieldType, newValue, comment, isprint);
					sub_push_add++;
					break;
				}
				case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
                {				
					// check old value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, thread_id, fieldType, oldValue, comment, isprint);
					sub_push_del++;
					break;
				}			
				case GMC_SUB_EVENT_UPDATE:
                {
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, thread_id, fieldType, newValue, comment, isprint);
					// check old value				
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, thread_id, fieldType, oldValue, comment, isprint);
					sub_push_updt++;
					break;
				}
				default:
                {
                    printf("default: invalid eventType\r\n");
                    break;
                }
			}			
		}
		GmcFreeStmt(subStmt);
    }
	// printf("\n[INFO] push sub num: %d \n", sub_num);
	// printf("\n[INFO][SUB PUSH] insert: %d, update: %d, delete: %d, num: %d \n", sub_del_push);
	fprintf(stderr, "\n[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n", vtx_labelName, sub_push_add, sub_push_updt, sub_push_del);
}



void sn_push_check_path1(void *sn_conn, int labelIndex, uint32_t sub_type, const char *vtx_labelName, const char *fieldName,GmcDataTypeE fieldType, int begin, int end, void* oldValue, void* newValue, const char *comment, int isprint = 0)
{
	printf("[INFO] into sn check. \n");
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
			
	for (int i = begin; i < end; i++) {
        printf("[INFO] into for: loop_%d  \n", i);
		GmcStmtHandle *subStmt = NULL;
		void *new_vtx;
		ret = GmcAllocStmt(sn_conn, &subStmt);
		EXPECT_EQ(GMERR_OK, ret);
		// ret = GmcOpenVertexLabelByName(stmt, vtx_labelName, &g_vertex);
		// EXPECT_EQ(GMERR_OK,ret);
		
		// ret = GmcOpenVertexLabelByName(subStmt, vtx_labelName, &new_vtx);
		// EXPECT_EQ(GMERR_OK,ret);	
		
        // check RecvPushMsg: event_type, vrtxLabelNum
		char pushVrtxName[MAX_NAME_LENGTH] = {0};
		uint32_t vrtxNameLen = MAX_NAME_LENGTH;
		// uint32_t eventType, vrtxLabelNum, msgType;
		// //ret = GmcRecvSubPushMsg(subStmt, &eventType, &vrtxLabelNum, &msgType);
		// EXPECT_EQ(GMERR_OK, ret);
		// EXPECT_EQ((uint32_t)sub_type, eventType);
		// EXPECT_EQ((uint32_t)1, vrtxLabelNum);
		
        ret = GmcGetSubPushVertexLabelName(subStmt, labelIndex, pushVrtxName, &vrtxNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(vtx_labelName, pushVrtxName);
        EXPECT_EQ(strlen(vtx_labelName), vrtxNameLen);
		if(ret == 0)
			 sub_num++;

        bool eof = false;    
		while (!eof){
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true ) {
                break;
            }    
            switch(sub_type)
            {
				case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
                {		
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
					EXPECT_EQ(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, newValue, comment, isprint);              
					sub_push_add++;
					break;
				}
				case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
                {				
					// check old value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, oldValue, comment, isprint);
					sub_push_del++;
					break;
				}			
				case GMC_SUB_EVENT_UPDATE:
                {
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, newValue, comment, isprint);
					// check old value				
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
					EXPECT_EQ(GMERR_OK, ret);
					ret = check_value_sn(subStmt, pushVrtxName, fieldName, 0, fieldType, oldValue, comment, isprint);
					sub_push_updt++;
					break;
				}
				default:
                {
                    printf("default: invalid eventType\r\n");
                    break;
                }
			} 
			GmcFreeStmt(subStmt);
		}
	}
		
	fprintf(stderr, "[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n\n", vtx_labelName, sub_push_add, sub_push_updt, sub_push_del);
}

void sn_push_check_path(GmcStmtHandle *subStmt, uint32_t eventType, const char *fieldName, GmcDataTypeE fieldType, void* oldValue, void* newValue, int isprint = 0)
{
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
	
	bool eof = false;    
	while (!eof){
		ret = GmcFetch(subStmt, &eof);
		// EXPECT_EQ(GMERR_OK, ret);
		if (ret != GMERR_OK || eof == true ) {
            printf("****** path push fetch finish! (status: %d)****\n", ret);
			break;
		}    
		switch(eventType)
		{
			case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
			{		
				// #7
				// ret = GmcSubSetFetchVertexLabel(subStmt, "ip4forward", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "ip4forward", "nhp_group_id", 0, fieldType, newValue, "#7 ip4forward", isprint);
				sub_push_add++;
				
				// #8
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group", "nhp_group_id", 0, fieldType, newValue, "#8 nhp_group", isprint);
				
				// #9
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group_node", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group_node", "nhp_group_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "primary_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "backup_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				
				// #10
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp", "nhp_index", 0, fieldType, newValue, "#10 nhp", isprint);
				break;
			}
			case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
			{				
				// #7
				// ret = GmcSubSetFetchVertexLabel(subStmt, "ip4forward", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "ip4forward", "nhp_group_id", 0, fieldType, newValue, "#7 ip4forward", isprint);
				sub_push_add++;
				
				// #8
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group", "nhp_group_id", 0, fieldType, newValue, "#8 nhp_group", isprint);
				
				// #9
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group_node", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group_node", "nhp_group_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "primary_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "backup_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				
				// #10
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp", "nhp_index", 0, fieldType, newValue, "#10 nhp", isprint);
				break;
			}			
			case GMC_SUB_EVENT_UPDATE:
			{
#if 0				
				// check new value
				// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, fieldName, fieldType, newValue, pushVrtxName, isprint);
				// check old value				
				// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
#endif
				sub_push_updt++;
				break;
			}
			default:
			{
				printf("default: invalid eventType\r\n");
				break;
			}
		} 
		
	}
    
    // GmcFreeStmt(subStmt);
	// fprintf(stderr, "[INFO][" L_PURPLE "SUB PUSH" NONE "][label_name: %s] insert: %d \n\n", pushVrtxName, sub_push_add);
}


void sn_push_check_path_78(GmcStmtHandle *subStmt, uint32_t eventType, const char *fieldName, GmcDataTypeE fieldType, void* oldValue, void* newValue, int isprint = 0)
{
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
	
	bool eof = false;    
	while (!eof){
		ret = GmcFetch(subStmt, &eof);
		EXPECT_EQ(GMERR_OK, ret);
		if (ret != GMERR_OK || eof == true ) {
			break;
		}    
		switch(eventType)
		{
			case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
			{		
				// #7
				// ret = GmcSubSetFetchVertexLabel(subStmt, "ip4forward", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "ip4forward", "nhp_group_id", 0, fieldType, newValue, "#7 ip4forward", isprint);
				sub_push_add++;
				
				// #8
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group", "nhp_group_id", 0, fieldType, newValue, "#8 nhp_group", isprint);
				break;
			}
			case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
			{				
				// #7
				// ret = GmcSubSetFetchVertexLabel(subStmt, "ip4forward", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "ip4forward", "nhp_group_id", 0, fieldType, newValue, "#7 ip4forward", isprint);
				sub_push_add++;
				
				// #8
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group", "nhp_group_id", 0, fieldType, newValue, "#8 nhp_group", isprint);
				break;
			}			
			case GMC_SUB_EVENT_UPDATE:
			{
#if 0				
				// check new value
				// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, fieldName, fieldType, newValue, pushVrtxName, isprint);
				// check old value				
				// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
#endif
				sub_push_updt++;
				break;
			}
			default:
			{
				printf("default: invalid eventType\r\n");
				break;
			}
		} 
		GmcFreeStmt(subStmt);
	}
	// fprintf(stderr, "[INFO][" L_PURPLE "SUB PUSH" NONE "][label_name: %s] insert: %d \n\n", pushVrtxName, sub_push_add);
}

void sn_push_check_path_7910(GmcStmtHandle *subStmt, uint32_t eventType, const char *fieldName, GmcDataTypeE fieldType, void* oldValue, void* newValue, int isprint = 0)
{
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
	
	bool eof = false;    
	while (!eof){
		ret = GmcFetch(subStmt, &eof);
		// EXPECT_EQ(GMERR_OK, ret);
		if (ret != GMERR_OK || eof == true ) {
			break;
		}    
		switch(eventType)
		{
			case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
			{		
				// #7
				// ret = GmcSubSetFetchVertexLabel(subStmt, "ip4forward", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "ip4forward", "nhp_group_id", 0, fieldType, newValue, "#7 ip4forward", isprint);
				
				// #9
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group_node", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group_node", "nhp_group_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "primary_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "backup_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				
				// #10
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp", false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp", "nhp_index", 0, fieldType, newValue, "#10 nhp", isprint);
				break;
			}
			case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
			{				
				// #7
				// ret = GmcSubSetFetchVertexLabel(subStmt, "ip4forward", true);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "ip4forward", "nhp_group_id", 0, fieldType, newValue, "#7 ip4forward", isprint);
				
				// #9
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp_group_node", true);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp_group_node", "nhp_group_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "primary_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				ret = check_value_sn(subStmt, "nhp_group_node", "backup_nhp_id", 0, fieldType, newValue, "#9 nhp_group_node", isprint);
				
				// #10
				// ret = GmcSubSetFetchVertexLabel(subStmt, "nhp", true);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, "nhp", "nhp_index", 0, fieldType, newValue, "#10 nhp", isprint);
				break;
			}			
			case GMC_SUB_EVENT_UPDATE:
			{
#if 0				
				// check new value
				// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, fieldName, fieldType, newValue, pushVrtxName, isprint);
				// check old value				
				// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
				EXPECT_EQ(GMERR_OK, ret);
				ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
#endif
				sub_push_updt++;
				break;
			}
			default:
			{
				printf("default: invalid eventType\r\n");
				break;
			}
		} 
		GmcFreeStmt(subStmt);
	}
	// fprintf(stderr, "[INFO][" L_PURPLE "SUB PUSH" NONE "][label_name: %s] insert: %d \n\n", pushVrtxName, sub_push_add);
}

int32_t  sn_push_path_labelName_check(GmcStmtT *subStmt, int labelIndex, const char *expLabelName, uint32_t expVtxNameLen, int isPrint = 0) {
	
	char pushVtxName[MAX_NAME_LENGTH] = {0};
	uint32_t vtxNameLen = MAX_NAME_LENGTH;			
	ret = GmcGetSubPushVertexLabelName(subStmt, labelIndex, pushVtxName, &vtxNameLen); 
	EXPECT_EQ(GMERR_OK, ret);
	EXPECT_STREQ(expLabelName, pushVtxName);
	// EXPECT_EQ(expVtxNameLen, vtxNameLen);
	if(isPrint == 1)
		printf("[INFO] GmcGetSubPushVertexLabelName status %d, pushVtxName: %s \n", ret, pushVtxName);
	return ret;
}
#endif

/************* 订阅线程分割函数 ********
int32_t test_createSub(GmcStmtT *stmt, int begin, int end)
{
    // 创建订阅关系
    readJanssonFile("./schema_file/ip4forward_subinfo_insert_update_delete_or_test.gmjson", &sub_info[conn_id]);
    ret = GmcSubscribe(g_stmt[conn_id], sub_info[conn_id], g_conn_sn[conn_id], sn_callback_to_be_deleted, NULL);
    // EXPECT_EQ(GMERR_OK, ret); // 59031 STATUS_CATA_SUBS_ALREADY_EXIST
    printf("[INFO][THREAD_%d] GmcSubscribe status %d \n", conn_id, ret);
}

*****/
#if 0
void sn_callback_simple2(GmcStmtT *subStmt, const GmcSubMsgInfo *info, void *userData)
{
    // int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    const char *fieldName = "qos_profile_id";
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
         for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcGetSubPushVertexLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
             //默认推送new object和old object
            switch(info->eventType)
            {
                case GMC_SUB_EVENT_INSERT:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[0];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %lu\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push insert", 1);
                    // ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, (void *)((user_data->new_value))[0], "sn push insert", 1);
                    // ret = check_value_sn(subStmt, fieldName, fieldType, ((int *)user_data->new_value), comment, isprint);
                    
                     //读old
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                    // EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE:
                {
                    //读new
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
                    // EXPECT_EQ(GMERR_OK, ret);
                     //读old
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[0];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push delete", 1);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
                    EXPECT_EQ(GMERR_OK, ret);
                    // index = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push update", 1);
                     //读old
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                    EXPECT_EQ(GMERR_OK, ret);
                    // index = ((int *)user_data->old_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push update", 1);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
                    EXPECT_EQ(GMERR_OK, ret);
                    // index = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                     //读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        // printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        uint32_t isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t) , &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        // printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                        EXPECT_EQ(GMERR_OK, ret);
                        // index = ((int *)user_data->old_value)[g_subIndex];
                        // test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                default:
                {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch(info->eventType)
        {
            case GMC_SUB_EVENT_INSERT:
            {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE:
            {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE:
            {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE:
            {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET:
            {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD:
            {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
            {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED:
            {
                user_data->agedNum++;
                break;
            }
        }
    }
}
#endif
void sn_callback_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int res = 0;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    const char *fieldName = "qos_profile_id";
    while (!eof) {
        res = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, res);
        if (res != GMERR_OK || eof == true) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            res = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, res);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            //默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, res);
                    index = ((int *)user_data->new_value)[0];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %lu\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);

                    res = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push insert", 1);
                    // res = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, (void
                    // *)((user_data->new_value))[0], "sn push insert", 1); res = check_value_sn(subStmt, fieldName,
                    // fieldType, ((int *)user_data->new_value), comment, isprint);

                    //读old
                    // res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    // EXPECT_EQ(GMERR_OK, res);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    // res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    // EXPECT_EQ(GMERR_OK, res);
                    //读old
                    res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, res);
                    index = ((int *)user_data->old_value)[0];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    res = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push delete", 1);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, res);
                    // index = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    res = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push update", 1);
                    //读old
                    res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, res);
                    // index = ((int *)user_data->old_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    res = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push update", 1);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, res);
                    // index = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    //读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        // printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, res);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull;
                        res = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, res);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        // printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        res = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, res);
                        // index = ((int *)user_data->old_value)[g_subIndex];
                        // test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
