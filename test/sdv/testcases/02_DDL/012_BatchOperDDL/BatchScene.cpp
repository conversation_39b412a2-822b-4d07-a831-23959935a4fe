extern "C" {
}

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"

/*****************************************************************************
 Description  : DDL batch操作，支持同异步创建、删除 vertex Label 和 edge Label
 Notes        :
 History      :
 Author       : houjia hwx390087
 Modification :
*****************************************************************************/

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
GmcConnT *conn = NULL;
GmcStmtT *stmt;
int affectRows;
unsigned int len;

int ret;
GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL;
void *g_label = NULL, *g_label_2 = NULL;
char *g_schema = NULL, *g_schema_2 = NULL;

char *vertexLabel_schema1, *vertexLabel_schema2, *edgeLabel_schema, *vtxLable_long_name = NULL;
char labelName1[128] = "T39";
char labelName2[128] = "T39_02";
char edgeName[128] = "T39_to_T39_2";
char g_configJson[] = "{\"max_record_count\" : 10000,\"isFastReadUncommitted\":0}";
bool isNull;

int res = 0;

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

char deltaStoreJson[] = "{\
	\"version\": \"1.0\",\
	\"delta_stores\": \
		[\
			{\
				\"name\": \"ccbdp1\",\
				\"init_mem_size\": 32768,\
				\"max_mem_size\": 131072,\
				\"extend_mem_size\": 8192,\
				\"page_size\": 4096\
			}\
		]\
	}";

class Batch_DDL_scene_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
    static void SetUpTestCase()
    {

        // 重启server
        system("sh $TEST_HOME/tools/start.sh");

        res = testEnvInit();
        ASSERT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);

        // 读取两个顶点label和一个边label的schema json
        readJanssonFile("./schema_file/DML_insertVertex_test.gmjson", &vertexLabel_schema1);
        ASSERT_NE((void *)NULL, vertexLabel_schema1);

        readJanssonFile("./schema_file/DML_insertVertex_02_test.gmjson", &vertexLabel_schema2);
        ASSERT_NE((void *)NULL, vertexLabel_schema2);

        readJanssonFile("./schema_file/edge_insert_schema.gmjson", &edgeLabel_schema);
        ASSERT_NE((void *)NULL, edgeLabel_schema);

        readJanssonFile("./schema_file/DML_insertVertex_long_name_test.gmjson", &vtxLable_long_name);
        ASSERT_NE((void *)NULL, vtxLable_long_name);
    };

    static void TearDownTestCase()
    {

        // 释放schema json资源
        free(vertexLabel_schema1);
        free(vertexLabel_schema2);
        free(edgeLabel_schema);
        free(vtxLable_long_name);

        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
        testEnvClean();
    };
};

void Batch_DDL_scene_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
#ifdef ENV_EULER
    AsyncUserDataT data = {0};
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    GmcClearNamespaceAsync(stmt_async, "public", drop_namespace_callback, &data);
    memset(&data, 0, sizeof(AsyncUserDataT));
    testGmcDisconnect(conn_async, stmt_async);
#endif
    AW_CHECK_LOG_BEGIN(0);
}

void Batch_DDL_scene_test::TearDown()
{
    printf("\n======================TEST:END========================\n");

    AW_CHECK_LOG_END();
    // 关闭 client connection
    GmcDropVertexLabel(stmt, "ip4forward");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 01.一次批量操作，调用GmcBatchAddDDL接口添加批量操作次数为1025，预期失败；（最大操作数1024）
// 2021.6.24 modify: 一次batch 操作add
// 1025次，原execute会批量全部失败，修改为前1024次是可以执行成功的，最后一次超过1024规格后为失败
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_001)
{
    // 准备1024个1kb 大小的schema json文件
#ifdef ENV_EULER
    system("sh create_multi_label_1k.sh 1025");
    int files = 1025;
#else
    system("sh create_multi_label_1k.sh 100");
    int files = 100;
#endif

    // 预准备
    int ret = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 添加批量执行操作1025个
    uint32_t exitvertex = 0;
    testGetTableNum(&exitvertex);
    char *schema_json;
    char labelName[512];
    char schema_path[512];
#ifdef ENV_EULER
    if (exitvertex > files) {
        AW_FUN_Log(LOG_STEP, "Too many table exit, exit:%d", exitvertex);
    } else {
        for (int i = 1; i <= files - exitvertex; i++) {
            schema_json = NULL;
            sprintf(labelName, "T39_%d", i);
            sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
            readJanssonFile(schema_path, &schema_json);
            ASSERT_NE((void *)NULL, schema_json);
            ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
            if (ret != 0) {
                EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
                AW_FUN_Log(LOG_STEP, "[INFO] Add cmd labelName: %s, status is %d \n", labelName, ret);
                free(schema_json);
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            free(schema_json);
        }

            // 执行批量操作
            unsigned int totalNum = 0;
            unsigned int successNum = 0;
            if (exitvertex != 0) {
                ret = GmcBatchExecute(batch, &batchRet);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(files - exitvertex, totalNum);
                EXPECT_EQ(files - exitvertex, successNum);
                AW_FUN_Log(LOG_STEP, 
                        "\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n",
                        ret, totalNum, successNum);
            } else {
                ret = GmcBatchExecute(batch, &batchRet);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(files -1 - exitvertex, totalNum);
                EXPECT_EQ(files -1 - exitvertex, successNum);
                AW_FUN_Log(LOG_STEP, 
                        "\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n",
                        ret, totalNum, successNum);
            }

            GmcBatchDestroy(batch);

            // 循环删表
            for (int i = 1; i <= files - exitvertex; i++) {
                sprintf(labelName, "T39_%d", i);
                GmcDropVertexLabel(stmt, labelName);
            }
    }
#else
    if (exitvertex > files) {
        AW_FUN_Log(LOG_STEP, "Too many table exit, exit:%d", exitvertex);
    } else {
        for (int i = 1; i <= files - exitvertex; i++) {
            schema_json = NULL;
            sprintf(labelName, "T39_%d", i);
            sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
            readJanssonFile(schema_path, &schema_json);
            ASSERT_NE((void *)NULL, schema_json);
            ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
            if (ret != 0) {
                EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
                AW_FUN_Log(LOG_STEP, "[INFO] Add cmd labelName: %s, status is %d \n", labelName, ret);
                free(schema_json);
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            free(schema_json);
        }

            // 执行批量操作
            unsigned int totalNum = 0;
            unsigned int successNum = 0;
            if (exitvertex != 0) {
                ret = GmcBatchExecute(batch, &batchRet);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(files - exitvertex, totalNum);
                EXPECT_EQ(files - exitvertex, successNum);
                AW_FUN_Log(LOG_STEP, 
                        "\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n",
                        ret, totalNum, successNum);
            } else {
                ret = GmcBatchExecute(batch, &batchRet);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(files - exitvertex, totalNum);
                EXPECT_EQ(files - exitvertex, successNum);
                AW_FUN_Log(LOG_STEP, 
                        "\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n",
                        ret, totalNum, successNum);
            }

            GmcBatchDestroy(batch);

            // 循环删表
            for (int i = 1; i <= files - exitvertex; i++) {
                sprintf(labelName, "T39_%d", i);
                GmcDropVertexLabel(stmt, labelName);
            }
    }
#endif
}

// 02.一次批量操作，调用GmcBatchAddDDL接口添加批量操作次数为1024，预期成功；
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_002)
{
#ifdef ENV_EULER
    system("sh create_multi_label_1k.sh 1024");
#else
    // 准备1024个1kb 大小的schema json文件
    system("sh create_multi_label_1k.sh 100");
#endif

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t exitvertex = 0;
    testGetTableNum(&exitvertex);

#ifdef ENV_EULER
    // 添加批量执行操作1025个
    char labelName[512];
    char schema_path[512];
    for (int i = 1; i <= 1024 - exitvertex; i++) {
        char *schema_json = NULL;
        sprintf(labelName, "T39_%d", i);
        sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
        if (ret != 0) {
            EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);  // 第1025次添加操作时预期返回报错 81502
            printf("[INFO] Add cmd labelName: %s, status is %d \n", labelName, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1024 - exitvertex, totalNum);
    EXPECT_EQ(1024 - exitvertex, successNum);
    AW_FUN_Log(LOG_STEP,
        "[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    GmcBatchDestroy(batch);

    // 循环删表
    for (int i = 1; i <= 1024 -exitvertex; i++) {
        sprintf(labelName, "T39_%d", i);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
#else
    if (exitvertex > 100) {
        AW_FUN_Log(LOG_STEP, "Too many table exit, exit:%d", exitvertex);
    }

    if (exitvertex < 100) {
        // 添加批量执行操作
        char labelName[512];
        char schema_path[512];
        for (int i = 1; i <= 100 - exitvertex; i++) {
            char *schema_json = NULL;
            sprintf(labelName, "T39_%d", i);
            sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
            readJanssonFile(schema_path, &schema_json);
            ASSERT_NE((void *)NULL, schema_json);
            ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
            if (ret != 0) {
                EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            free(schema_json);
        }

        // 执行批量操作
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(100 - exitvertex, totalNum);
        EXPECT_EQ(100 - exitvertex, successNum);
        GmcBatchDestroy(batch);

        // 循环删表
        for (int i = 1; i <= 100 -exitvertex; i++) {
            sprintf(labelName, "T39_%d", i);
            ret = GmcDropVertexLabel(stmt, labelName);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
#endif
}

// 03. 一次批量操作的请求报文大小超过1M；
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_003)
{
    // 准备1024个schema json
    system("sh create_multi_label.sh 1024");

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 添加批量执行操作1025个
    char labelName[512];
    char schema_path[512];
    for (int i = 1; i <= 500; i++) {
        char *schema_json = NULL;
        sprintf(labelName, "ip4forward%d", i);
        sprintf(schema_path, "./multi-gmimport/%s.gmjson", labelName);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_MEMORY_OPERATE_FAILED, ret);
            AW_FUN_Log(LOG_STEP,"[INFO] Add cmd labelName: %s, status is %d \n", labelName, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(500, totalNum);
    EXPECT_EQ(500, successNum);
    AW_FUN_Log(LOG_STEP,
        "[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    GmcBatchDestroy(batch);

    // 循环删表
    for (int i = 1; i <= successNum; i++) {
        sprintf(labelName, "ip4forward%d", i);
        GmcDropVertexLabel(stmt, labelName);
    }
}

// 04.一次批量操作的请求报文大小临界小于1M；
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_004)
{
    // 准备1024个schema json
    system("sh create_multi_label.sh 1024");

    // 预准备
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 添加批量执行操作1025个
    char labelName[512];
    char schema_path[512];
    for (int i = 1; i <= 465; i++) {
        char *schema_json = NULL;
        sprintf(labelName, "ip4forward%d", i);
        sprintf(schema_path, "./multi-gmimport/%s.gmjson", labelName);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(465, totalNum);
    EXPECT_EQ(465, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);

    for (int i = 1; i <= 465; i++) {
        sprintf(labelName, "ip4forward%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
}

// 05.一次batch execute操作中，调用GmcBatchAddDDL接口添加批量操作时，同时包含DDL和DML操作
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_005)
{
    char *vertexLabel_schema1 = NULL;
    readJanssonFile("./schema_file/DML_insertVertex_test.gmjson", &vertexLabel_schema1);
    ASSERT_NE((void *)NULL, vertexLabel_schema1);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 一次批量操作 add cmd 同时包含DDL和DML操作
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcBatchAddVertexDML(batch, GMC_OPERATION_INSERT);
    ret = GmcBatchAddDML(batch, stmt);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    // EXPECT_EQ(GMERR_OK, ret);  // 81507 GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE
    // ret = testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema1);
}

// 06.调用GmcBatchAddDDL接口create vertexLabel，configJson关联deltaStore
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_006)
{
    GmcDropVertexLabel(stmt, labelName1);

    // 批量操作预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // add cmd 配置deltaS
    char configJson[512] = "{\"max_record_count\" : 1000, \"writers\":\"abc\"}";
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] GmcBatchAddDDL with deltaS status is %d \n", ret);

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);

    // drop vertex label
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 07. GmcBatchExecute同步接口使用异步stmt
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int res = 0;
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    /************** 批量创建vertex label和edge label**************/

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // DDL add cmd: 2 vetex label, 1 edge label
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP,
        "\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);

    // async disconn
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 08. GmcBatchExecuteAsync异步接口正常场景
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcDropEdgeLabel(stmt, edgeName);
    GmcDropVertexLabel(stmt, labelName1);
    GmcDropVertexLabel(stmt, labelName2);

    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";
    // DDL add cmd: 2 vetex label, 1 edge label
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // 执行异步批量操作
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
#ifdef NRELEASE
    EXPECT_EQ(2, data.totalNum);
    EXPECT_EQ(2, data.succNum);
#else
    EXPECT_EQ(3, data.totalNum);
    EXPECT_EQ(3, data.succNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO][CLIENT] DDL batch execute callback status %d, totalNum is %d, succNum is %d\n",
        data.status, data.totalNum, data.succNum);

    // drop edge, vertex label
#ifdef NRELEASE
#else
    ret = GmcDropEdgeLabel(stmt, edgeName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);

    // async disconn
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 09. GmcBatchExecuteAsync异步接口使用同步stmt
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    int res = 0;
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    // 预准备
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // DDL add cmd: 2 vetex label, 1 edge label
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "\n[INFO]GmcBatchExecuteAsync with sync stmt, status %d \n", ret);

    // async disconn
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 10.一次批量操作中包含多个add cmd操作，执行batch execute时，遇到异常add cmd时，batch操作不会继续进行；
// 2020.11.23 取消labelname最长128字节规格约束
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // drop label1, label2
    GmcDropVertexLabel(stmt, labelName1);
    GmcDropVertexLabel(stmt, labelName2);

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // DDL add cmd: 2 vetex label, 1 edge label
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // 构造异常的add cmd：labelName oversize
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL,
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        vtxLable_long_name, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[INFO] GmcBatchAddDDL labelName overSize status is %d \n", ret);

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    AW_FUN_Log(LOG_STEP, "\n[INFO] DDL batch execute status %d, totalNum %d, succNum %d\n", ret, totalNum, successNum);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, totalNum);
    EXPECT_EQ(0, successNum);

    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_EDGE_LABEL, edgeName, edgeLabel_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName2, vertexLabel_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL,
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        vtxLable_long_name, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行批量操作
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, totalNum);
    EXPECT_EQ(0, successNum);
}

// 11.三个接口调用顺序验证(多次执行prepare/多次执行execute/无prepare时执行addDDLcmd/prepare之后直接执行execute)
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_011)
{
    // (1) 多次执行prepare
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName1, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);

    // (2) 多次执行execute
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName1, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);

    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    // EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);  // 81507
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);  // 81507
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // (3) 无prepare只执行addDDLcmd, batchExecute（addCmd接口就会被校验住）
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    // EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName1, NULL, NULL);
    // EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    // EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // (4) prepare之后直接执行execute
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 12.batch_ execute批量创建两个顶点label和一个边label（相关联），顶点label写入数据，pk更新顶点label，pk查询，batch
// execute删除顶点label和边label
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    /*************** add comd normal: 2 vetex label, 1 edge label ***************/
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);
#else
    EXPECT_EQ(3, totalNum);
    EXPECT_EQ(3, successNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO] DDL(create) batch execute status %d, totalNum is %d, succNum is %d\n",
        ret, totalNum, successNum);

    // vertexLabel_1 插入数据  uint32 F0 = 100, string F1 = "testver"
    void *vertexLabel = NULL;
    unsigned int wr_F0 = 123;
    char wr_F1[64] = "testver";

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, wr_F1, strlen(wr_F1));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // vertexLabel_2 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, wr_F1, strlen(wr_F1));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

#ifdef NRELEASE
#else
    //构造src和dst点
    void *edgeLabel = NULL;
    ret = GmcOpenEdgeLabelByName(stmt, edgeName, &edgeLabel);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, "T39_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    ASSERT_EQ(GMERR_OK, ret);

    // 插入edge
    /******** 主键查询各字段值 ********/
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);

    // F0 主键读取写入的各字段值
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F0
    unsigned int sizeF0;
    ret = GmcGetVertexPropertySizeByName(stmt, "F0", &sizeF0);
    EXPECT_EQ(GMERR_OK, ret);
    char *valueF0 = (char *)malloc(sizeF0);
    ret = GmcGetVertexPropertyByName(stmt, "F0", valueF0, sizeF0, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[INFO] read_F0 value is %d, size is %d \n", *(uint32_t *)valueF0, sizeF0);
    EXPECT_EQ(wr_F0, *(uint32_t *)valueF0);
    EXPECT_EQ(4, sizeF0);
    free(valueF0);

    // get F1
    unsigned int sizeF1;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &sizeF1);
    EXPECT_EQ(GMERR_OK, ret);
    char *valueF1 = (char *)malloc(sizeF1);
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F1", valueF1, sizeF1, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[INFO] read_F1 value is %s, size is %d \n", valueF1, sizeF1);
    free(valueF1);

    /******** 主键同步更新 ********/
    GmcStmtT *stmt1;
    ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, labelName1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);

    // 主键同步更新  string F1 = "testver" --> "test123"
    char update_str[64] = "test123";
    ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_STRING, update_str, strlen(update_str));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    /******** 主键读 ********/
    GmcStmtT *stmt2;
    void *vertexLabel2;
    ret = GmcAllocStmt(conn, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);

    // F0 主键读取写入的各字段值
    ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &wr_F0, sizeof(wr_F0));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(stmt);

    ret = GmcGetVertexPropertySizeByName(stmt1, "F0", &sizeF0);
    EXPECT_EQ(GMERR_OK, ret);

    int r_valueF0;
    ret = GmcGetVertexPropertyByName(stmt1, "F0", &r_valueF0, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[INFO] read_F0 value is %d, size is %d \n", r_valueF0, sizeF0);

    // get F1
    // unsigned int sizeF1;
    ret = GmcGetVertexPropertySizeByName(stmt1, "F1", &sizeF1);
    EXPECT_EQ(GMERR_OK, ret);
    char r_valueF1[128];
    ret = GmcGetVertexPropertyByName(stmt1, "F1", r_valueF1, sizeF1, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(*update_str, *r_valueF1);
    printf("[INFO] read_F1 value is %s, size is %d \n", r_valueF1, sizeF1);

    char const *view_name = "V\\$STORAGE_HEAP_STAT";
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s | grep -A 24 '\"pageCnt\": 1' > ./sysview.log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    GmcFreeIndexKey(stmt2);
#endif
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // Batch execute： drop edgeLabel, vertexLabel
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_EDGE_LABEL, edgeName, edgeLabel_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName2, vertexLabel_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 执行批量操作
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);
#else
    EXPECT_EQ(3, totalNum);
    EXPECT_EQ(3, successNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO] DDL(drop) batch execute status %d, totalNum is %d, succNum is %d\n",
        ret, totalNum, successNum);
}

// 13.DDL批量操作与gmimport工具交互。gmimport批量创建，调用批量接口drop vertex/edge label
#define MAX_CONN 100
void *thread_gmimport_tool_test(void *args)
{
    GmcConnT *g_conn[MAX_CONN * 2];
    GmcStmtT *g_stmt[MAX_CONN * 2];

    // gmimport工具导入创建600个vertexLabel
    char cmd[512];
    if (g_envType == 2) {
        snprintf(cmd, 512, "%s/gmimport -c cache -f multi-gmimport -ns %s -s %s",
            g_toolPath, g_testNameSpace, g_connServer);
    } else {
        snprintf(cmd, 512, "%s/gmimport -c cache -f multi-gmimport -s %s", g_toolPath, g_connServer);
    }
    system(cmd);
    printf(cmd);

    // 创建连接
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn[conn_id], &g_stmt[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
#ifdef ENV_RTOSV2X
    // drop顶点label
    char labelName[128];
    for (int i = 1; i <= 10; i++) {
        sprintf(labelName, "ip4forward%d", i);
        GmcDropVertexLabel(g_stmt[conn_id], labelName);
    }
    testGmcDisconnect(g_conn[conn_id], g_stmt[conn_id]);
#else
    // drop顶点label
    char labelName[128];
    for (int i = 1; i <= 127; i++) {
        sprintf(labelName, "ip4forward%d", i);
        GmcDropVertexLabel(g_stmt[conn_id], labelName);
    }
    // 关闭 client connection
    ret = testGmcDisconnect(g_conn[conn_id], g_stmt[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    return ((void *)0);
}
void *thread_batch_execute_test(void *args)
{
    GmcConnT *g_conn_1[MAX_CONN * 2];
    GmcStmtT *g_stmt_1[MAX_CONN * 2];

    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_1[conn_id], &g_stmt_1[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    /******************** batch create 顶点label ********************/
    // ret = GmcBatchPrepare(g_stmt_1[conn_id]);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_1[conn_id], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[512];
    char schema_path[512];
#ifdef ENV_RTOSV2X
    for (int i = 1; i <= 10; i++) {
        char *schema_json = NULL;
        sprintf(labelName, "T39_%d", i);
        sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        // ASSERT_NE((void *)NULL,schema_json);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }
    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // ret = GmcBatchExecute(g_stmt_1[conn_id], &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, totalNum);
    EXPECT_EQ(10, successNum);
    GmcBatchReset(batch);

    /******************** batch drop 顶点label ********************/
    ret = GmcBatchPrepare(g_conn_1[conn_id], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= 10; i++) {
        sprintf(labelName, "T39_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, totalNum);
    EXPECT_EQ(10, successNum);
    GmcBatchDestroy(batch);
    testGmcDisconnect(g_conn_1[conn_id], g_stmt_1[conn_id]);

#else
    for (int i = 1; i <= 127; i++) {
        char *schema_json = NULL;
        sprintf(labelName, "T39_%d", i);
        sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        // ASSERT_NE((void *)NULL,schema_json);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }
    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // ret = GmcBatchExecute(g_stmt_1[conn_id], &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(127, totalNum);
    EXPECT_EQ(127, successNum);
    printf("\n[INFO] [thread_%d] DDL batch create execute status %d, totalNum is %d, succNum is %d\n", conn_id + 1, ret,
        totalNum, successNum);
    GmcBatchReset(batch);

    /******************** batch drop 顶点label ********************/
    ret = GmcBatchPrepare(g_conn_1[conn_id], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= 127; i++) {
        sprintf(labelName, "T39_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(127, totalNum);
    EXPECT_EQ(127, successNum);
    printf("\n[INFO] [thread_%d] DDL batch drop execute status %d, totalNum is %d, succNum is %d\n", conn_id + 1, ret,
        totalNum, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcDisconnect(g_conn_1[conn_id], g_stmt_1[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    return ((void *)0);
}
TEST_F(Batch_DDL_scene_test, DDL_012_Batch_DDL_scene_test_013)
{
#ifdef ENV_RTOSV2X
    system("sh multi-label.sh 10");
    system("sh create_multi_label_1k.sh 10");
#else
    // 数据准备600个label gmjson、gmconfig文件，路径 ./multi-gmimport
    system("sh multi-label.sh 127");
    system("sh create_multi_label_1k.sh 127");
#endif

    // 多线程：1) thread_gmimport_tool_test  2)thread_batch_execute_test
    pthread_t thr_arr[32];
    pthread_t thr_arr2[32];
    void *thr_ret[32];
    int index[3] = {0};
    int index2[3] = {0};

    for (int i = 0; i < 1; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_gmimport_tool_test, (void *)&index[i]);
        pthread_create(&thr_arr2[i], NULL, thread_batch_execute_test, (void *)&index[i]);
    }

    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
        pthread_join(thr_arr2[i], &thr_ret[i]);
    }
}
