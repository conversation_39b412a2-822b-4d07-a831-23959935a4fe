{"name": "subVertexLabel", "label_name": "OP_TX", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "replace", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "and", "conditions": [{"property": "T1/P0", "value": 1}, {"property": "T1/P8", "value": false}, {"property": "T1/P14", "value": "string1"}, {"property": "T1/P15", "value": "string1"}, {"property": "T1/P16", "value": "string1"}]}}