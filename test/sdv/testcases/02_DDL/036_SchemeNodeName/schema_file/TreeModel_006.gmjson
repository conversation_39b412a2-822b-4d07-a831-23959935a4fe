[{"type": "record", "name": "TEST_T5", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}, {"name": "P1", "type": "uint64", "nullable": true}, {"name": "P2", "type": "int32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "T2", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "A0", "type": "int64", "nullable": true}, {"name": "A1", "type": "uint64", "nullable": true}, {"name": "A2", "type": "int32", "nullable": true}, {"name": "A3", "type": "uint32", "nullable": true}]}], "super_fields": [{"name": "superfiled0", "comment": "test", "fields": {"begin": "P0", "end": "P2"}}]}, {"name": "T3", "type": "record", "vector": true, "size": 1024, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "T2", "type": "record", "vector": true, "size": 1024, "fields": [{"name": "H0", "type": "int64", "nullable": true}, {"name": "H1", "type": "uint64", "nullable": true}, {"name": "H2", "type": "int32", "nullable": true}, {"name": "H3", "type": "uint32", "nullable": true}]}], "super_fields": [{"name": "superfiled2", "comment": "test", "fields": {"begin": "V0", "end": "V2"}}]}], "keys": [{"node": "TEST_T5", "name": "TEST_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "TEST_T5", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0"], "constraints": {"unique": true}}]}]