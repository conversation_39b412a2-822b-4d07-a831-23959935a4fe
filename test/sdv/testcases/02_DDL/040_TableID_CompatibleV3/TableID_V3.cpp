/*****************************************************************************
 Description  : 040_table ID 的范围兼容V3范围
 Notes        :
            功能场景：
                001.不建表,查看视图,展示正确
                002.不指定ID,建vertex表成功,查看视图,展示正确
                003.不指定ID,连续建vertex表成功,连续查看视图,展示正确
                004.不指定ID,建KV表成功,查看视图,展示正确
                005.不指定ID,连续建KV表成功,连续查看视图,展示正确
                006.指定ID数据为983039,建表成功,查看视图,展示正确
                007.指定ID数据为983040,建表失败
                008.不指定ID,导入表成功,查看视图,展示正确
                009.指定ID数据为983039,导入表成功,查看视图,展示正确
                010.不指定ID,使用不同namespace,创建相同的表,建表成功
                011.指定ID数据为983039,使用不同namespace,创建相同的表,建表失败
                012.指定ID数据为0xF0000,建表失败
                013.指定ID数据为0xF0000-1,建表失败
                014.指定ID数据为0xF0000+1,建表失败
                015.指定ID数据为0,建表失败
                016.指定ID数据为-1,建表失败
                017.指定ID数据为1,建表成功,查看视图,展示正确
                018.指定ID为正确值,创建两不同张表,建表失败
                019.指定ID为正确值,创建表,删除表后再用同一ID建表,建表失败
            异常场景：
                001.指定ID数据首位数为大于0的数字,建表失败
                002.指定ID数据首位数为字母,建表失败
                003.指定ID数据首位数为特殊字符,建表失败
                004.指定ID数据为纯字母类型,建表失败
                005.指定ID数据为纯特殊字符类型,建表失败
                006.指定ID数据为数字+字母类型,建表失败
                007.指定ID数据为数字+特殊字符类型,建表失败
                008.指定ID数据为字母+特殊字符类型,建表失败
                009.指定ID数据为数字+字母+特殊字符类型,建表失败



 History      :
 Author       : wensiqi wwx1060458
 Modification :
 Date         : 2021/10/22
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"


#define MAX_CMD_SIZE 1024
#define LABELNAME_MAX_LENGTH 128
char const *view_name = "V\\$CATA_VERTEX_LABEL_INFO";
char g_command[MAX_CMD_SIZE];

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

class TableID_CompatibleV3_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = 0;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp();
    virtual void TearDown();
};

void TableID_CompatibleV3_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void TableID_CompatibleV3_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

int GetPrintBycmd(char *cmd, int *vaule)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
    char cmdOutput[64] = {0};
    while (NULL != fgets(cmdOutput, 64, pf))
        ;
    *vaule = atoi(cmdOutput);
    pclose(pf);
    return 0;
}
// 001.不建表,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_001)
{
    uint32_t ret;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.不指定ID,建vertex表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_002)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.不指定ID,连续建vertex表成功,连续查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_003)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    for (int i = 0; i < 10; i++) {
        ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
        EXPECT_EQ(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.不指定ID,建KV表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_004)
{
    uint32_t ret;
    char g_tableName[128] = "KV3";
    char g_configJson[128] = "{\"max_record_num\":10000}";

    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 005.不指定ID,连续建KV表成功,连续查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_005)
{
    uint32_t ret;
    char g_tableName[128] = "KV3";
    char g_configJson[128] = "{\"max_record_num\":10000}";

    for (int i = 0; i < 10; i++) {
        ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
            view_name);

        ret = executeCommand(g_command, "VERTEX_LABEL_ID");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcKvDropTable(g_stmt, g_tableName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

// 006.指定ID数据为983039,建表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_006)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.指定ID数据为983040,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_007)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983040,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
}

// 008.不指定ID,导入表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_008)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":100000}";
    // 1048590
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema/schema_datatype.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 接口创建VertexLabel
    char labelName[LABELNAME_MAX_LENGTH] = "schema_datatype";
    ret = GmcCreateVertexLabel(g_stmt, schema_json, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    /** gmimport工具导入同名vertexLabel **/
    // system("${GMDB_PATH}/bin/gmimport -f schema_file/schema_datatype.gmjson > ./log/import_sameLabelName.log");
    char schema_file[128] = "schema/schema_datatype.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t %s -s %s > ./log/import_sameLabelName.log",
        g_toolPath, schema_file, labelName, g_connServer);
    printf("\ncmd is: %s\n\n", cmd);
    sleep(1);
    system(cmd);

    printf("\n[INFO] print import_sameLabelName.log: \n");
    system("cat ./log/import_sameLabelName.log");

    printf("\n[INFO][Expect]: \n");
    system("cat ${GMDB_HOME}/pub/include/gmc_errno.h | grep 59026");

    free(schema_json);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 009.指定ID,导入表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_009)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.指定ID,使用不同namespace,创建相同的表,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_010)
{
#if defined FEATURE_PERSISTENCE
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
#else
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
#endif
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char const *nameSpace = "abc";
    char const *nameSpace_userName = "myname";
    // create and use namespace
    ret = GmcCreateNamespace(g_stmt, nameSpace, nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    // difference namespace create same name vertex and insert record
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011.不指定ID,使用不同namespace,创建相同的表,建表成功
TEST_F(TableID_CompatibleV3_test, DDL_040_011)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char const *nameSpace = "abc";
    char const *nameSpace_userName = "myname";
    // create and use namespace
    ret = GmcCreateNamespace(g_stmt, nameSpace, nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    // difference namespace create same name vertex and insert record
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.指定ID数据为0xF0000,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_012)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":0xF0000,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    GmcDropVertexLabel(g_stmt, NULL);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 013.指定ID数据为0xF0000-1,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_013)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":0xF0000-1,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 014.指定ID数据为0xF0000+1,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_014)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":0xF0000+1,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 015.指定ID数据为0,建表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_015)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    int res = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
}

// 016.指定ID数据为-1,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_016)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":-1,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
}

// 017.指定ID数据为1,建表成功,查看视图,展示正确
TEST_F(TableID_CompatibleV3_test, DDL_040_017)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    int res = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);

    ret = executeCommand(g_command, "VERTEX_LABEL_ID");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.指定ID数据首位数为大于0的数字,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_018)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":1xF00000,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 002.指定ID数据首位数为字母,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_019)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":axF00000,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 003.指定ID数据首位数为特殊字符,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_020)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":$xF00000,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 004.指定ID数据为纯字母类型,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_021)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":abcd,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 005.指定ID数据为纯特殊字符类型,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_022)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":#$%^,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 006.指定ID数据为数字+字母类型,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_023)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":123a,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 007.指定ID数据为数字+特殊字符类型,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_024)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":123$,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 018.指定ID数据为字母+特殊字符类型,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_025)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":1$,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 009.指定ID数据为数字+字母+特殊字符类型,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_026)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":12a$,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
}

// 指定ID为正确值,创建两张不同表,建表失败
TEST_F(TableID_CompatibleV3_test, DDL_040_027)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelJson1 = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId1",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId1");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// 指定ID为正确值,创建表,删除表后再用同一ID建表,建表成功
TEST_F(TableID_CompatibleV3_test, DDL_040_028)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);
}

void test_close_and_drop_label_async(GmcStmtT *stmt, void *label, const char *labelName)
{
    int ret;
    AsyncUserDataT data = {0};
    ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
}

// 指定ID为正确值,异步建表成功
TEST_F(TableID_CompatibleV3_test, DDL_040_029)
{
    uint32_t ret;
    GmcConnT *conn_async = 0;
    GmcStmtT *stmt_async = 0;
    AsyncUserDataT data = {0};
    void *vertexLabel = NULL;
    char *cfgJson = (char *)"{\"max_record_num\":1000}";
    // 983039 对应十六进制 (0xF0000 - 1), 0xF0000为用户指定tableid时的边界,即必须小于0xF0000
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertexErrId",
        "id":983039,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint64"}
        ],
        "keys":
        [
            {"node":"labelvertexErrId", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"labelvertexErrId", "name":"T39_K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabelAsync(stmt_async, labelJson, cfgJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    test_close_and_drop_label_async(stmt_async, vertexLabel, "labelvertexErrId");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}
