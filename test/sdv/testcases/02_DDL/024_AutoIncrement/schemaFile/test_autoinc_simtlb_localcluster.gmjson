[{"type": "record", "name": "AutoIncrementVertex", "fields": [{"name": "F0", "type": "uint32", "auto_increment": true, "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "nullable": true, "size": 100}], "keys": [{"node": "AutoIncrementVertex", "name": "PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "AutoIncrementVertex", "name": "AutoIncrementVertexHashIdx", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}]}]