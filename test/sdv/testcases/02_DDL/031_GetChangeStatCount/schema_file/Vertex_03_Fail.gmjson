[{"version": "2.0", "type": "record", "name": "vertex_03_fail", "fields": [{"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "P0", "type": "string", "size": 512, "nullable": true}, {"name": "P1", "type": "string", "size": 512, "nullable": true}, {"name": "P2", "type": "string", "size": 512, "nullable": true}, {"name": "P3", "type": "string", "size": 512, "nullable": true}, {"name": "P4", "type": "string", "size": 512, "nullable": true}, {"name": "P5", "type": "string", "size": 512, "nullable": true}, {"name": "P6", "type": "string", "size": 512, "nullable": true}, {"name": "P7", "type": "string", "size": 512, "nullable": true}, {"name": "P8", "type": "string", "size": 512, "nullable": true}, {"name": "P9", "type": "string", "size": 512, "nullable": true}, {"name": "P10", "type": "string", "size": 512, "nullable": true}, {"name": "P11", "type": "string", "size": 512, "nullable": true}, {"name": "P12", "type": "string", "size": 512, "nullable": true}, {"name": "P13", "type": "string", "size": 512, "nullable": true}, {"name": "P14", "type": "string", "size": 512, "nullable": true}, {"name": "P15", "type": "string", "size": 512, "nullable": true}], "keys": [{"name": "vertex_03_fail_key", "index": {"type": "primary"}, "node": "vertex_03_fail", "fields": ["PK"], "constraints": {"unique": true}, "comment": "主键索引"}]}]