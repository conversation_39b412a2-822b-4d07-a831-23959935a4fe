[{"type": "record", "name": "T21", "fields": [{"name": "F5", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint8: 3", "nullable": false}, {"name": "F1", "type": "uint8: 3", "nullable": false}, {"name": "F2", "type": "uint8: 2", "nullable": false}, {"name": "F3", "type": "uint8: 4", "nullable": false}, {"name": "F4", "type": "uint8: 2", "nullable": false}, {"name": "F6", "type": "uint8: 2", "nullable": false}], "keys": [{"node": "T21", "name": "T20_PK", "fields": ["F5"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]