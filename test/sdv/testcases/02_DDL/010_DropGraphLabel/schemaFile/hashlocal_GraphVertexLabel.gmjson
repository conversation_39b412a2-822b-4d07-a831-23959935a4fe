[{"type": "record", "name": "a1", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "int32", "nullable": false}], "keys": [{"node": "a1", "name": "pk1", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "a1", "name": "hash_key1", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}, {"type": "record", "name": "a2", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "string", "size": 8, "nullable": false}, {"name": "F3", "type": "int32", "nullable": false}], "keys": [{"node": "a2", "name": "pk2", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "a2", "name": "hash_key2", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}, {"type": "record", "name": "a3", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "int32", "nullable": false}], "keys": [{"node": "a3", "name": "pk3", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "a3", "name": "hash_key3", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]