extern "C" {
}
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"

/*****************************************************************************
 Description  : 支持位图bitmap数据类型(异常场景测试)
 Notes        :
 History      :
 Author       : qinqianbao qwx995465
 Modification :
*****************************************************************************/
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
const char *g_cfgJson = R"({"max_record_count":1000})";
char *g_labelJson = NULL;

class BitmapAb : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
        system("sh $TEST_HOME/tools/stop.sh -f");
    };
    virtual void SetUp();
    virtual void TearDown();
};

void BitmapAb::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    // 创建客户端同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void BitmapAb::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();
    // 关闭 client connection
    // free(g_labelJson);
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 01.异常场景：Schema中bitmap字段不设置size，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_001)
{
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    char *lackSzieSchema = NULL;
    const char *labelName = "T0_bitmap";
    readJanssonFile("schema_file/NoSizeVertexLabel.gmjson", &lackSzieSchema);
    EXPECT_NE((void *)NULL, lackSzieSchema);
    int ret = GmcCreateVertexLabel(g_stmt, lackSzieSchema, g_cfgJson);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(lackSzieSchema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 02.异常场景：Schema中bitmap字段设置size超过最大值(32769)，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_002)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *labelJson = NULL;
    const char *labelName = "T1_bitmap";
    readJanssonFile("schema_file/AboveMaxSizeVertexLabel.gmjson", &labelJson);
    // printf("%s\n", labelJson);  // 设置size
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);  // 预期GMERR_DATATYPE_MISMATCH，实际GMERR_INVALID_PROPERTY
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(labelJson);
}

// 03.异常场景：Schema中bitmap字段包含default字段，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_003)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *labelJson = NULL;
    const char *labelName = "T2_bitmap";
    readJanssonFile("schema_file/IncludeDefaultVertexLabel.gmjson", &labelJson);
    // printf("%s\n", labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    // 【ORM兼容联调】建表错误码GMERR_FEATURE_NOT_SUPPORTED全部调整为GMERR_INVALID_TABLE_DEFINITION
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(labelJson);
}

// 04.异常场景：Schema中已bitmap字段为主键索引，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_004)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *labelJson = NULL;
    const char *labelName = "T3_bitmap";
    readJanssonFile("schema_file/BitmapOfPkVertexLabel.gmjson", &labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    
    free(labelJson);
}

// 05.异常场景：Schema中已bitmap字段为hash索引，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_005)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *labelJson = NULL;
    const char *labelName = "T4_bitmap";
    readJanssonFile("schema_file/BitmapOfSkVertexLabel.gmjson", &labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // printf("%s\n", labelJson);
    free(labelJson);
}

// 06.异常场景：Schema中包含superfield，superfield中包含bitmap字段，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_006)
{
    char *labelJson = NULL;
    const char *labelName = "T5_bitmap";
    readJanssonFile("schema_file/BitmapOfSuperfieldsVertexLabel.gmjson", &labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
}

// 07.异常场景：在schema中的bitmap字段中设置size范围，在size有效范围外插入数据
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_007)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    bool isNull = 1;
    char *errdataSchema = NULL;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 63, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    void *vertexLabel = NULL;
    const char *labelName = "T20_bitmap";
    readJanssonFile("schema_file/NormalDMLVertexLabel.gmjson", &errdataSchema);
    EXPECT_NE((void *)NULL, errdataSchema);
    int ret = GmcCreateVertexLabel(g_stmt, errdataSchema, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(errdataSchema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);  // 错误码适配，不支持局部插入
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 08.异常场景：对应字段GmcSetVertexProperty传入错误的GmcDataTypeE type
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_008)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *errTypeSchema = NULL;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    void *vertexLabel = NULL;
    const char *labelName = "T20_bitmap";
    readJanssonFile("schema_file/NormalDMLVertexLabel.gmjson", &errTypeSchema);
    EXPECT_NE((void *)NULL, errTypeSchema);
    int ret = GmcCreateVertexLabel(g_stmt, errTypeSchema, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(errTypeSchema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 09.异常场景: 不支持重复写
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_009)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    bool isNull = 1;
    char *errdataSchema = NULL;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    void *vertexLabel = NULL;
    const char *labelName = "T20_bitmap";
    readJanssonFile("schema_file/NormalDMLVertexLabel.gmjson", &errdataSchema);
    EXPECT_NE((void *)NULL, errdataSchema);
    int ret = GmcCreateVertexLabel(g_stmt, errdataSchema, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(errdataSchema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (uint32_t i = 0; i < 2; i++) {
        uint32_t F0Val = 1;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F1Val = 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcExecute(g_stmt);
        if (i == 0) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            printf("not suport\n");
        }
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}


// 012.异常场景：在schema中的bitmap字段中设置size范围，insert时bitmap的size不一致
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_012)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    bool isNull = 1;
    char *errdataSchema = NULL;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 1023, NULL};
    uint8_t bits[1024 / 8];
    memset(bits, 0xffff, 1024 / 8);
    bits[1024 / 8 - 1] = '\0';
    bitMap.bits = bits;
    void *vertexLabel = NULL;
    const char *labelName = "T20_bitmap";
    readJanssonFile("schema_file/NormalDMLVertexLabel.gmjson", &errdataSchema);
    EXPECT_NE((void *)NULL, errdataSchema);
    int ret = GmcCreateVertexLabel(g_stmt, errdataSchema, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(errdataSchema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013.异常场景：Schema中bitmap字段设置size为0，创建vertexlabel
TEST_F(BitmapAb, DDL_028_BitmapDataAbnormal_013)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *lackSzieSchema = NULL;
    const char *labelName = "T13_bitmap";
    readJanssonFile("schema_file/NullSizeVertexLabel.gmjson", &lackSzieSchema);
    EXPECT_NE((void *)NULL, lackSzieSchema);
    int ret = GmcCreateVertexLabel(g_stmt, lackSzieSchema, g_cfgJson);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);  // 预期GMERR_DATATYPE_MISMATCH，实际GMERR_INVALID_PROPERTY
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(lackSzieSchema);
}
