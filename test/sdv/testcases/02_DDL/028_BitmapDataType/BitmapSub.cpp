extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "sub_tools.h"

char g_label_name[] = "T20_all_type";
char g_lable_PK[] = "T20_PK";
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

using namespace std;

class BitmapSub : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;

    virtual void SetUp();
    virtual void TearDown();
};

void BitmapSub::SetUp()
{
    printf("BitmapSub Start.\n");
    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info = NULL;
    int ret;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);

    GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void BitmapSub::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    // 删表断连
    test_close_and_drop_label(g_stmt_sync, g_label, g_label_name);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(g_schema);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
    printf("BitmapSub End.\n");
}

bool isMatchPushCond(int value)
{
    return (value == 1) && (value == 51);
}

// 01 订阅、写数据、更新数据(条件订阅)
TEST_F(BitmapSub, DDL_028_BitmapConditionalSubAndPush_test_001)
{
    int ret;
    int i;
    int userDataIdx = 0;
    // bitmap值的insert
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    readJanssonFile("schema_file/all_type_schema_subinfo_uint32.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    // printf("%s\n", g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // insert data
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond(i)) {
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("%d\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcSetVertexProperty(g_stmt_sync, "F18", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        // ASSERT_EQ(sizeof(unsigned int), len);
        ASSERT_EQ(1, affectRows);
    }

    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        if (isMatchPushCond(i)) {
            ((int *)(user_data->old_value))[userDataIdx] = i;
            ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
            userDataIdx++;
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i + g_data_num);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);

        // update bitmap
        uint8_t tmpBits[2];
        tmpBits[0] = 0;
        tmpBits[1] = '\0';
        bitMap.beginPos = 1;
        bitMap.endPos = 9;
        bitMap.bits = tmpBits;
        // 设置bitmap字段新值
        ret = GmcSetVertexProperty(g_stmt_sync, "F18", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        // ASSERT_EQ(sizeof(unsigned int), len);
        ASSERT_EQ(1, affectRows);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int pkVal = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        bool isNull = 1;
        uint32_t propSize = 0;
        // query bitmap
        memset(bits, 0, 128 / 8);  // test bitmap value
        bits[128 / 8 - 1] = '\0';
        ret = GmcGetVertexPropertySizeByName(g_stmt_sync, "F18", &propSize);
        ASSERT_EQ(propSize, 128);
        ret = GmcGetVertexPropertyByName(g_stmt_sync, "F18", bits, propSize, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((uint32_t)0, isNull);
        for (int i = 0; i < 128 / 8 - 1; i++) {
            if (i == 0) {
                ASSERT_EQ(1, *bits);  // 10000000 00111111
                continue;
            }
            if (i == 1) {
                ASSERT_EQ(252, *(bits + 1));  // 根据设置的更新值的起始位
                continue;
            }
            ASSERT_EQ(true, 0xFF == *(bits + i));
        }
    }

    int tmp;
    // delete data
    for (i = 0; i < g_data_num; i++) {
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        // ASSERT_EQ(sizeof(unsigned int), len);
        ASSERT_EQ(1, affectRows);
        // 检查所删除
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
    }

    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    ASSERT_EQ(GMERR_OK, ret);  // 根据schema符合条件的数据
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 0);
    ASSERT_EQ(GMERR_OK, ret);
    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 02订阅、写数据、更新数据(全表订阅)
TEST_F(BitmapSub, DDL_028_BitmapConditionalSubAndPush_test_002)
{
    int ret;
    int i;
    int userDataIdx = 0;
    // bitmap值的insert
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    readJanssonFile("schema_file/all_type_schema_subinfo_insert.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    for (i = 0; i < g_data_num; i++) {
        // printf("[INFO] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcSetVertexProperty(g_stmt_sync, "F18", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        //等待insert事件推送完成
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新数据
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i + g_data_num);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        // update bitmap
        uint8_t tmpBits[2];
        tmpBits[0] = 0;
        tmpBits[1] = '\0';
        bitMap.beginPos = 1;
        bitMap.endPos = 9;
        bitMap.bits = tmpBits;
        // 设置bitmap字段新值
        ret = GmcSetVertexProperty(g_stmt_sync, "F18", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        // 等待update事件推送完成
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int tmp;
    // 删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
        EXPECT_EQ(GMERR_OK, ret);

        GmcFreeIndexKey(g_stmt_sync);
    }

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 03.全量+增量事件, 写入数据, 取消订阅, 重新下发订阅, (sleep(1))取消订阅
TEST_F(BitmapSub, DDL_028_BitmapConditionalSubAndPush_test_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret, i;
    int userDataIdx = 0;
    // bitmap值的insert
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    readJanssonFile("schema_file/subInfoV3.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    EXPECT_EQ(GMERR_OK, ret);
    
    // 写数据
    for (i = 0; i < g_data_num / 10; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcSetVertexProperty(g_stmt_sync, "F18", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        //等待insert事件推送完成
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(100000);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
