extern "C" {
}
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"

/*****************************************************************************
 Description  : 支持位图bitmap数据类型(同步连接)
 Notes        :
 History      :
 Author       : qinqianbao qwx995465
 Modification :
*****************************************************************************/
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char label_name1[] = "OP_T0";
char lalable_name_PK1[] = "OP_PK";
const char *g_label_T0 = "T0_bitmap";
const char *g_label_T20 = "T20_bitmap";
const char *g_label_T21 = "T21_bitmap";
const char *g_label_T22 = "T22_bitmap";
const char *g_label_T23 = "T23_bitmap";
const char *g_label_T24 = "T24_bitmap";
const char *g_cfgJson = R"({"max_record_count":1000})";
char *g_labelJson_T0 = NULL;
char *g_labelJson_T20 = NULL;
char *g_labelJson_T21 = NULL;
char *g_labelJson_T22 = NULL;
char *g_labelJson_T23 = NULL;
char *g_labelJson_T24 = NULL;
char *g_labelJson_graph = NULL;
char *g_labelJson_edge = NULL;
char *g_labelJson_tree = NULL;
// bitmap
class BitmapSync : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
        system("sh $TEST_HOME/tools/stop.sh -f");
    };
    virtual void SetUp();
    virtual void TearDown();
};

void BitmapSync::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    // 创建客户端同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &g_labelJson_T0);
    EXPECT_NE((void *)NULL, g_labelJson_T0);
    readJanssonFile("schema_file/NormalDMLVertexLabel.gmjson", &g_labelJson_T20);
    EXPECT_NE((void *)NULL, g_labelJson_T20);
    readJanssonFile("schema_file/NormalGraphVertexLabel.gmjson", &g_labelJson_graph);
    EXPECT_NE((void *)NULL, g_labelJson_graph);
    readJanssonFile("schema_file/NormalGraphEdgeLabel.gmjson", &g_labelJson_edge);
    EXPECT_NE((void *)NULL, g_labelJson_edge);
    readJanssonFile("schema_file/TreeModelCapabilityEnhancement_op.gmjson", &g_labelJson_tree);
    EXPECT_NE((void *)NULL, g_labelJson_tree);
    readJanssonFile("schema_file/NormalMutilBitmapVertexLabel.gmjson", &g_labelJson_T21);
    EXPECT_NE((void *)NULL, g_labelJson_T21);
    readJanssonFile("schema_file/MaxSizeVertexLabel.gmjson", &g_labelJson_T22);
    EXPECT_NE((void *)NULL, g_labelJson_T22);
    readJanssonFile("schema_file/MinVertexLabel.gmjson", &g_labelJson_T23);
    EXPECT_NE((void *)NULL, g_labelJson_T23);
    readJanssonFile("schema_file/SKDMLVertexLabel.gmjson", &g_labelJson_T24);
    EXPECT_NE((void *)NULL, g_labelJson_T24);
    AW_CHECK_LOG_BEGIN();
}

void BitmapSync::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();
    // 关闭 client connection
    free(g_labelJson_T0);
    free(g_labelJson_T20);
    free(g_labelJson_graph);
    free(g_labelJson_edge);
    free(g_labelJson_tree);
    free(g_labelJson_T21);
    free(g_labelJson_T22);
    free(g_labelJson_T23);
    free(g_labelJson_T24);
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 01.schema新增bitmap数据类型,创建vertexlabel
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_001)
{
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T0, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("%s\n", g_labelJson_T0);
    ret = GmcDropVertexLabel(g_stmt, g_label_T0);
    EXPECT_EQ(GMERR_OK, ret);
}

// 02.schema新增bitmap数据类型,设置size为最大值1024(8*4*1024),创建vertexlabel
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_002)
{
    char *labelJson = NULL;
    const char *labelName = "T1_bitmap";
    readJanssonFile("schema_file/NormalVertexLabel_maxsize.gmjson", &labelJson);
    // printf("%s\n", labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 03.schema新增bitmap数据类型,设置nullabel,创建vertexlabel
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_003)
{
    char *labelJson = NULL;
    const char *labelName = "T2_bitmap";
    readJanssonFile("schema_file/NormalVertexLabel_nullabel.gmjson", &labelJson);
    // printf("%s\n", labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(g_stmt, labelJson, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 05.创建vertelable,不插入bitmap,bitmap的值为NULL
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_005)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的更新
    GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // printf("%s\n", g_labelJson_T20);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(1, f1);
    // query bitmap
    uint32_t propSize = 0;
    // memset(bits, 0, 128/8); // test bitmap value
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);  // schema 中的size
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0, *(bits + i));
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 06.创建vertelable,设置bitmap的值,并查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_006)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的更新
    GmcBitMapT bitMap = {100, 227, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // printf("%s\n", g_labelJson_T20);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 07.创建vertelable,insert时不设置bitmap的值,update时设置bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_007)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};  // 128 10000000  1024可以存储8*128个1
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex
    uint8_t tmpBits[2];
    memset(tmpBits, 0xffff, sizeof(tmpBits));
    tmpBits[1] = '\0';
    bitMap.beginPos = 1;
    bitMap.endPos = 7;
    bitMap.bits = tmpBits;
    // uint32_t F0New = 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);  // 部分更新(设置起始位)
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // query
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(2, f1);
    // query bitmap
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {  // bitmap从第1位开始写，写入后读不符合预期。(数组从0开始计数，即向右偏移1位)
                                             // 适配问题单：DTS2021120124602
        if (i == 0) {
            EXPECT_EQ(254, *bits);  // 127 二进制 01111111
            continue;
        }
        EXPECT_EQ(0, *(bits + i));
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 08.创建vertelable,insert时设置bitmap的值,update时设置bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_008)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex
    uint8_t tmpBits[2];
    tmpBits[0] = 0;
    tmpBits[1] = '\0';
    bitMap.beginPos = 1;
    bitMap.endPos = 9;
    bitMap.bits = tmpBits;
    // uint32_t F0New = 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // query
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool IsFinish = true;
    ret = GmcFetch(g_stmt, &IsFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(2, f1);
    // query bitmap
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        if (i == 0) {
            EXPECT_EQ(1, *bits);  // 10000000 00111111
            continue;
        }
        if (i == 1) {
            EXPECT_EQ(252, *(bits + 1));  // 根据设置的更新值的起始位
            continue;
        }
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.创建vertelable,insert时设置bitmap的值,update时多次设置bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_010)
{
    bool isNull = 1;
    void *vertexLabel = NULL;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};  // 128
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }

    // update Vertex
    bitMap.beginPos = 1;
    for (uint32_t i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t tmpBits[2];
        tmpBits[0] = 0;
        tmpBits[1] = '\0';
        bitMap.endPos = bitMap.beginPos + 6;  // 设置前五个字节的首位都为1  ---128
        bitMap.bits = tmpBits;
        // uint32_t F0New = 2;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F1New = i + 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // query
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(i + 2, f1);
        // query bitmap
        memset(bits, 0, 128 / 8);  // test bitmap value
        bits[128 / 8 - 1] = '\0';
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
        EXPECT_EQ(propSize, 128);
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)0, isNull);
        switch (i) {
            case 0:
                EXPECT_EQ(1, *bits);
                break;
            case 1:
                EXPECT_EQ(1, *bits);
                break;
            case 2:
                EXPECT_EQ(1, *bits);
                break;
            case 3:
                EXPECT_EQ(1, *bits);
                break;
            case 4:
                EXPECT_EQ(1, *bits);
                break;
        }
        bitMap.beginPos = bitMap.endPos + 1;
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.创建vertelable,设置bitmap的值,remove顶点
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_012)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013.创建vertelable,设置bitmap的值,merge不存在的顶点
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_013)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(1, f1);
    // query bitmap
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0xff, *(bits + i));
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014.创建vertelable,开启事务,设置bitmap的值,提交事务
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_014)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;

    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(1, f1);
    // query bitmap
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0xff, *(bits + i));
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 015.创建vertelable,insert bitmap的值,然后全表扫描
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_015)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    int num = 2;
    while (num) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        uint32_t val = 2;
        ret = queryPropertyAndCompare(g_stmt, "F1", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t propSize = 0;
        memset(bits, 0, 128 / 8);  // test bitmap value
        bits[128 / 8 - 1] = '\0';
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
        EXPECT_EQ(propSize, 128);
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)0, isNull);
        for (int i = 0; i < 128 / 8 - 1; i++) {
            EXPECT_EQ(0xff, *(bits + i));
        }
        cnt++;
        num--;
    }

    GmcResetStmt(g_stmt);
    EXPECT_EQ(1, cnt);
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}


// 017.创建tree模型的vertelable,insert bitmap的值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_017)
{
    bool eof;
    bool &isNull = eof;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    void *label = NULL;

    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_tree, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *root;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点 part data
    int64_t F0Val = 1;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &F0Val, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t P0Val = 1;
    ret = GmcNodeSetPropertyByName(T1, (char *)"P0", GMC_DATATYPE_INT64, &P0Val, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t P1Val = 1;
    ret = GmcNodeSetPropertyByName(T1, (char *)"P1", GMC_DATATYPE_UINT64, &P1Val, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 插入array节点 part data
    // ret = GmcSetNodeRecordIndex(g_stmt, (char *)"T1.T2", 0);
    // EXPECT_EQ(GMERR_OK, ret);
    int64_t A0Val = 1;
    ret = GmcNodeSetPropertyByName(T2, (char *)"A0", GMC_DATATYPE_INT64, &A0Val, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t A1Val = 1;
    ret = GmcNodeSetPropertyByName(T2, (char *)"A1", GMC_DATATYPE_UINT64, &A1Val, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, (char *)"A17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    // 插入vector节点,part data
    // ret = GmcAddNodeVectorRecord(g_stmt, (char *)"T3");
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T3, &T3);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t V0Val = 1;
    ret = GmcNodeSetPropertyByName(T3, (char *)"V0", GMC_DATATYPE_INT64, &V0Val, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t V1Val = 1;
    ret = GmcNodeSetPropertyByName(T3, (char *)"V1", GMC_DATATYPE_UINT64, &V1Val, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T3, (char *)"V17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &F0Val, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // 读取array节点
    // ret = GmcSetNodeRecordIndex(g_stmt, (char *)"T1.T2", 0);
    // ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(T2, (char *)"A17", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(T2, (char *)"A17", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0xff, *(bits + i));
    }

    // 读取vector节点
    // ret = GmcSetNodeRecordIndex(g_stmt, (char *)"T3", 0);
    // ASSERT_EQ(GMERR_OK, ret);

    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(T3, (char *)"V17", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(T3, (char *)"V17", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0xff, *(bits + i));
    }

    ret = GmcDropVertexLabel(g_stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.创建vertelable,包含多个bitmap字段并设置bitmap的值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_018)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的更新
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T21, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // printf("%s\n", g_labelJson_T21);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T21, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T21, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T21_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // query bitmap
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0xff, *(bits + i));
    }

    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(0xff, *(bits + i));
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_T21);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.maxsize时创建vertelable,insert时全量设置bitmap的值,update时设置全量bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_019)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 32767, NULL};
    uint8_t bits[32768 / 8];
    memset(bits, 0xffff, 32768 / 8);
    bits[32768 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T22, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T22, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T22, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T22_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 32768 / 8);
    bits[32768 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 32768);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 32768 / 8 - 1; i++) {
        ASSERT_EQ(true, 0xFF == *(bits + i));
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T22, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex
    uint8_t tmpBits[32768];
    for (int i = 0; i < 32768 - 1; i++) {
        tmpBits[i] = 0;
    }
    tmpBits[32767] = '\0';
    bitMap.beginPos = 1;
    bitMap.endPos = 32766;  //
    bitMap.bits = tmpBits;
    // uint32_t F0New = 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T22_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T22, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T22_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(2, f1);
    // query bitmap
    uint8_t bits1[32768 / 8];
    memset(bits1, 0, 32768 / 8);  // test bitmap value
    bits[32768 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 32768);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 32768 / 8 - 1; i++) {
        if (i == 0) {
            ASSERT_EQ(1, *(bits + i));
            continue;
        }
        ASSERT_EQ(0, *(bits + i));
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T22);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBitMapT bitMap = {0, 32767, NULL};

void *thread_bitmap_1(void *args)
{
    // update Vertex
    GmcBitMapT bitMap = {0, 32767, NULL};
    GmcConnT *conn;
    GmcStmtT *stmt;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isNull = 1;
    void *vertexLabel = NULL;
    uint8_t tmpBits[32768 / 8];  // 32768为4096字节的位数，/8 说明bitmap按位更新
    for (int i = 0; i < 32768 / 8 - 1; i++) {
        tmpBits[i] = 0;
    }
    tmpBits[32768 / 8 - 1] = '\0';
    for (uint32_t i = 0; i < 1000; i++) {
        bitMap.beginPos = 0;
        bitMap.endPos = i;  //
        bitMap.bits = tmpBits;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_T22, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("-------------test start -----------------i = %d\n", i);
        uint32_t F0Val = 1;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F1New = 2 + i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T22_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 020.maxsize时创建vertelable,insert时全量设置bitmap的值,update时并发设置bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_020)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 32767, NULL};
    uint8_t bits[32768 / 8];
    memset(bits, 0xffff, 32768 / 8);
    bits[32768 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T22, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("%s\n", g_labelJson_T22);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T22, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T22, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T22_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 32768 / 8);
    bits[32768 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 32768);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 32768 / 8 - 1; i++) {
        ASSERT_EQ(true, 0xFF == *(bits + i));
    }

    // multi threads merge
    int tdNum = 10;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, thread_bitmap_1, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_T22);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.携带bitmap字段创建vertexlabel，设置size小于10,insert bitmap的值
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_021)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 7, NULL};  // size设置为9
    uint8_t bits[2];
    memset(bits, 0xffff, 2);
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T23, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T23, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert 一次性不能插入超过8K
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T23, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T23_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 2);
    // bits[1024 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 8);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);  // size设置为8, 返回50010
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 1; i++) {
        ASSERT_EQ(true, 0xff == *(bits + i));
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T23);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.创建vertelable,insert时设置bitmap的值,merge时设置bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_022)
{
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex
    uint8_t tmpBits[2];
    tmpBits[0] = 0;
    tmpBits[1] = '\0';
    bitMap.beginPos = 1;
    bitMap.endPos = 9;
    bitMap.bits = tmpBits;
    // uint32_t F0New = 2;
    // ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1;
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(isNull, 0);
    EXPECT_EQ(2, f1);
    // query bitmap 预期成功，但失败，开发定位中
    memset(bits, 0, 128 / 8);  // test bitmap value
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        if (i == 0) {
            EXPECT_EQ(1, *bits);  // 10000000 00111111
            continue;
        }
        if (i == 1) {
            EXPECT_EQ(252, *(bits + 1));  // 根据设置的更新值的起始位
            continue;
        }
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023.创建vertelable,insert时设置bitmap的值,replace时设置bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_023)
{
    bool isNull = 1;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex
    uint8_t tmpBits[2];
    tmpBits[0] = 0;
    tmpBits[1] = '\0';
    bitMap.beginPos = 1;
    bitMap.endPos = 9;
    bitMap.bits = tmpBits;
    // uint32_t F0New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);  // 这里应该增加报错
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024.创建vertelable,批量插入bitmap的值,批量更新bitmap的新值,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_024)
{
    int ret;
    bool isNull = 1;
    void *vertexLabel = NULL;
    int start_num = 0;
    int end_num = 2;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);
    // 批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);

    // 批量更新数据
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // update Vertex
        uint8_t tmpBits[2];
        tmpBits[0] = 0;
        tmpBits[1] = '\0';
        bitMap.beginPos = 1;
        bitMap.endPos = 9;
        bitMap.bits = tmpBits;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);

    // query
    for (int i = 0; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(i, f1);
        // query bitmap
        uint32_t propSize = 0;
        memset(bits, 0, 128 / 8);  // test bitmap value
        bits[128 / 8 - 1] = '\0';
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
        EXPECT_EQ(propSize, 128);
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)0, isNull);
        for (int i = 0; i < 128 / 8 - 1; i++) {
            if (i == 0) {
                EXPECT_EQ(1, *bits);  // 10000000 00111111
                continue;
            }
            if (i == 1) {
                EXPECT_EQ(252, *(bits + 1));  // 根据设置的更新值的起始位
                continue;
            }
            EXPECT_EQ(true, 0xFF == *(bits + i));
        }
    }
    GmcBatchDestroy(batch);
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025.创建vertelable,批量插入bitmap的值,批量merge,然后查询
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_025)
{
    int ret;
    bool isNull = 1;
    void *vertexLabel = NULL;
    int start_num = 0;
    int end_num = 2;
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);
    // 批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcExecute(g_stmt);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);

    // 批量更新数据
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        // update Vertex
        uint8_t tmpBits[2];
        tmpBits[0] = 0;
        tmpBits[1] = '\0';
        bitMap.beginPos = 1;
        bitMap.endPos = 9;
        bitMap.bits = tmpBits;

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcExecute(g_stmt);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);

    // query
    for (int i = 0; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f1;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1, sizeof(uint32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(i, f1);
        // query bitmap
        uint32_t propSize = 0;
        memset(bits, 0, 128 / 8);  // test bitmap value
        bits[128 / 8 - 1] = '\0';
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
        EXPECT_EQ(propSize, 128);
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)0, isNull);
        for (int i = 0; i < 128 / 8 - 1; i++) {
            if (i == 0) {
                EXPECT_EQ(1, *bits);  // 10000000 00111111
                continue;
            }
            if (i == 1) {
                EXPECT_EQ(252, *(bits + 1));  // 根据设置的更新值的起始位
                continue;
            }
            EXPECT_EQ(true, 0xFF == *(bits + i));
        }
    }
    GmcBatchDestroy(batch);
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
}

// 更新的起始位大于定义的size
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_026)
{	
    bool isNull = 1;
    AW_FUN_Log(LOG_STEP, "test start.");
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128/8];
    memset(bits, 0xffff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
	int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T20, g_cfgJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert 
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t propSize = 0;
    ret = GmcSetIndexKeyValue(g_stmt,0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt,  "T20_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);  
    EXPECT_EQ(GMERR_OK, ret);
    // query insert data
    memset(bits, 0, 256/8);
    bits[256 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }
    system("gmsysview record T20_bitmap");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T20, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex   超过size范围进行更新，预期返回错误码  128bits
    uint8_t tmpBits[256/8];
    for(int i = 0; i < 256/8 - 1; i++) {
        tmpBits[i] = 0;
    }
    tmpBits[256/8 - 1] = '\0';
    bitMap.beginPos= 1;
    bitMap.endPos = 150;
    bitMap.bits = tmpBits;
    // uint32_t F0New = 2;
    ret = GmcSetIndexKeyValue(g_stmt,0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyName(g_stmt,  "T20_PK");
	EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1New = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1New, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError("BitMap param mistake, beginPos is 1, endPos is 150.");
    EXPECT_EQ(GMERR_OK, ret);
	ret = GmcExecute(g_stmt);  // update时预期报错
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview record T20_bitmap");
    ret = GmcDropVertexLabel(g_stmt, g_label_T20);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 27.创建vertelable,insert时设置bitmap的值,update时根据二级索引
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_027)
{	
    bool isNull = 1;
    void *vertexLabel = NULL;

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
	int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T24, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T24, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert 
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // check insert
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T24, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt,0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt,  "T24_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);  
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t propSize = 0;
    memset(bits, 0, 128/8);
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcGetVertexPropertyByName(g_stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(true, 0xFF == *(bits + i));
    }
    system("gmsysview record T24_bitmap");

    // 二级索引更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T24, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // update Vertex   
    uint8_t tmpBits[2];
    tmpBits[0] = 0;
    tmpBits[1] = '\0';
    bitMap.beginPos= 0;
    bitMap.endPos = 7;
    bitMap.bits = tmpBits;
    uint32_t secIndexVal = 2;
    ret = GmcSetIndexKeyValue(g_stmt,0, GMC_DATATYPE_UINT32, &secIndexVal, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyName(g_stmt,  "T24_SK");
	EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
	ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record T24_bitmap");
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "%s/gmsysview record %s", g_toolPath, g_label_T24);
    ret = executeCommand(cmd, "0000 0000 1111");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_label_T24);
    EXPECT_EQ(GMERR_OK, ret);
}

// 28.bitmap数据导入导出: gmimport & gmexport
TEST_F(BitmapSync, DDL_028_BitmapDataTypeSync_test_028)
{	
    AW_FUN_Log(LOG_STEP, "test start.");
    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
	int ret = GmcCreateVertexLabel(g_stmt, g_labelJson_T24, g_cfgJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T24, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // bitmap insert 
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 导出数据
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "%s/gmexport -c vdata -t %s", g_toolPath, g_label_T24);
    ret = executeCommand(cmd, "Command type: export_vdata, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_T24, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T24_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入数据
    const char *file = "./T24_bitmap.gmdata";
    (void)snprintf(cmd, 1024, "%s/gmimport -c vdata -f %s -t %s", g_toolPath, file, g_label_T24);
    ret = executeCommand(cmd, "Insert data succeed. totalNum: 1, successNum: 1, duplicateNum: 0");
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));

    (void)snprintf(cmd, 1024, "%s/gmexport -c vdata -t %s", g_toolPath, g_label_T24);
    ret = executeCommand(cmd, "Command type: export_vdata, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));

    ret = GmcDropVertexLabel(g_stmt, g_label_T24);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
