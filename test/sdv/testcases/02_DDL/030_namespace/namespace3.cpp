/*****************************************************************************
 Description  : namespace迭代1基本功能测试
 Notes        : 001.创建一个namespace并use
                002.创建一个已经存在的namespace
                003.namespace a下切换回namespace public
                004.namespace public下切换到namespace a
                005.namespace a下切换到namespace b再切换到a
                006.重复删除同名namespace
                007.删除一个空的namespace
                008.切换到不存在的namespace
                009.namespace a下创建一张vertexLabel
                010.同一个namespace下重复创建同名vertexLabel
                011.两个不同namespace 下分别创建同名的vertexLabel
                012.同一个namespace下重复创建同名edgeLabel
                013.namespace a下对它下面的vertexLabel进行同步DML操作
                014.同步创建label异步切换namespace下对vertexLabel进行异步DML操作
                015.namespace a下对它下面的edgelabel进行自动增删边操作
                016.namespace a下进行path订阅操作
                017.namespace a下进行全表订阅操作
                018.edgelabel关联的vertexLabel在两个namespace下进行创建
                019.创建订阅的vertexLabel在另一个namespace中
                020.在多个namespace下创建同名的ds表并写操作
                021.创建删除空的namespace重复100000次
                022.连接满时创建切换namespace并ddl/dml操作
                023 在nameSpace a下访问nameSpace b下的表
                024 namespace下创建tree表并写表
                025 异步创建切换删除namespace并异步ddl/dml操作
                026 创建namespace 创表打开表删表删除空的namespace重复5000次
                027 连接句柄不一致去访问namespace下的表
 History      :
 Author       : wuxiaochun wwx753022
 Modification : [2021.03.16]
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"

#include "namespace_test.h"

GmcConnT *g_conn = NULL, *g_connAsync = NULL;
;
GmcStmtT *g_stmt = NULL, *g_stmtAsync = NULL;
const char *g_namespace = (const char *)"userA";
const char *g_namespaceUserName = (const char *)"abc";

class test_namespace03 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void test_namespace03::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int32_t ret = 0;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void test_namespace03::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void test_namespace03::SetUp()
{
    int32_t ret = 0;
    //封装的创建异步连接
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    //同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespace(g_stmt, g_namespace, g_namespaceUserName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void test_namespace03::TearDown()
{
    AW_CHECK_LOG_END();
    AsyncUserDataT data = {0};
    int ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_stmt = NULL;
    g_conn = NULL;
    g_connAsync = NULL;
    g_stmtAsync = NULL;
}

// 001.创建一个namespace并use
TEST_F(test_namespace03, DDL_030_003_001)
{
    int32_t ret = 0;
    const char *nameSpace = (const char *)"user001";
    ret = GmcCreateNamespace(g_stmt, nameSpace, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.创建一个已经存在的namespace
TEST_F(test_namespace03, DDL_030_003_002)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    ret = GmcCreateNamespace(g_stmt, g_namespace, g_namespaceUserName);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace, g_namespaceUserName);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.namespace a下切换回namespace public
TEST_F(test_namespace03, DDL_030_003_003)
{
    int32_t ret = 0;
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.namespace public下切换到namespace a
TEST_F(test_namespace03, DDL_030_003_004)
{
    int32_t ret = 0;
    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.namespace a下切换到namespace b再切换到a
TEST_F(test_namespace03, DDL_030_003_005)
{
    int32_t ret = 0;
    const char *name1 = (const char *)"user005";
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.删除一个空的namespace
TEST_F(test_namespace03, DDL_030_003_006)
{
    int32_t ret = 0;
    const char *name1 = (const char *)"user006";
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.重复删除同名namespace
TEST_F(test_namespace03, DDL_030_003_007)
{
    int32_t ret = 0;
    const char *name1 = (const char *)"user007";
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_NE(GMERR_OK, ret);
    printf("ret:%d\n", ret);
}

// 008.切换到不存在的namespace
TEST_F(test_namespace03, DDL_030_003_008)
{
    int32_t ret = 0;
    const char *name1 = (const char *)"user008";
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.namespace a下创建一张vertexLabel
TEST_F(test_namespace03, DDL_030_003_009)
{
    int32_t ret = 0;
    char *schema_test = NULL;
    char *label_name = (char *)"namespace_test";
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/namespace_test_schema.gmjson", &schema_test);
    ASSERT_NE((void *)NULL, schema_test);
    //创建vertexLabel(main_store)
    ret = GmcCreateVertexLabel(g_stmt, schema_test, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_test);
}

// 010.同一个namespace下重复创建同名vertexLabel
TEST_F(test_namespace03, DDL_030_003_010)
{
#if defined FEATURE_PERSISTENCE
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
#else
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
#endif
    int32_t ret = 0;
    char *schema_test = NULL;
    char *label_name = (char *)"namespace_test";
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/namespace_test_schema.gmjson", &schema_test);
    ASSERT_NE((void *)NULL, schema_test);
    //创建vertexLabel(main_store)
    ret = GmcCreateVertexLabel(g_stmt, schema_test, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema_test, NULL);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_test);
}

// 011.两个不同namespace 下分别创建同名的vertexLabel
TEST_F(test_namespace03, DDL_030_003_011)
{
    int32_t ret = 0;
    char *schema_test = NULL;
    void *vertexLabel = NULL;
    char *label_name = (char *)"namespace_test";
    const char *name1 = (const char *)"user011_a";
    const char *name2 = (const char *)"user011_b";

    GmcDropNamespace(g_stmt, name1);
    GmcDropNamespace(g_stmt, name2);
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, name2, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    // name1创建
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/namespace_test_schema.gmjson", &schema_test);
    ASSERT_NE((void *)NULL, schema_test);
    //创建vertexLabel(main_store)
    GmcDropVertexLabel(g_stmt, label_name);
    ret = GmcCreateVertexLabel(g_stmt, schema_test, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // name2创建
    ret = GmcUseNamespace(g_stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, label_name);
    ret = GmcCreateVertexLabel(g_stmt, schema_test, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // name1
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    // name2
    ret = GmcUseNamespace(g_stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_test);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
}


// 013.namespace a下对它下面的vertexLabel进行同步DML操作
TEST_F(test_namespace03, DDL_030_003_013)
{
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    void *label = NULL;
    char VertexLabel_name[] = "testdml";
    int num = 100;
    ret = GmcUseNamespace(g_stmt, g_namespace);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/vertex_all_type.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);

    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    //创建vertex_label
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    //释放vertexLabel_schema
    free(vertexLabel_schema);
    //同步 dml操作
    namespace_dml_sync(g_stmt, num);
    test_scan_testdml(g_stmt, VertexLabel_name, num);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014.namespace下对vertexLabel进行异步DML操作
TEST_F(test_namespace03, DDL_030_003_014)
{
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    char VertexLabel_name[] = "testdml";
    void *label = NULL;
    char *teststr = (char *)"vertexs";
    char *name1 = (char *)"userB";
    int affectRows = 0;
    unsigned int len = 0;
    AsyncUserDataT *tdata = NULL;
    void *label_async = NULL;
    char *pkName = (char *)"PkName";
    bool isFinish = false;

    ret = GmcUseNamespace(g_stmt, name1);
    if (ret != GMERR_OK) {
        ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, name1);
        ASSERT_EQ(GMERR_OK, ret);
    }

    readJanssonFile("schemaFile/vertex_all_type.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);

    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    //创建vertex_label
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    //释放vertexLabel_schema
    free(vertexLabel_schema);
    //异步切换namespace
    tdata = (AsyncUserDataT *)malloc(sizeof(AsyncUserDataT));
    memset(tdata, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmtAsync, name1, use_namespace_callback, tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata->status);

    //插入
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.insertCb = insert_vertex_callback;
    requestCtx.userData = tdata;
    for (int i = 0; i < TEST_INSERT_VERTEX_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync, VertexLabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetVertexPropertyByName_PK(g_stmtAsync, i);
        TestGmcSetVertexPropertyByName(g_stmtAsync, i, teststr);
        ret = GmcExecuteAsync(g_stmtAsync, &requestCtx);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata->status);
        EXPECT_EQ(1, tdata->affectRows);
    }
    //更新
    requestCtx.updateCb = update_vertex_callback;
    requestCtx.userData = tdata;
    for (int i = 0; i < TEST_INSERT_VERTEX_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync, VertexLabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtAsync, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetVertexPropertyByName(g_stmtAsync, (i + 1), (char *)"testdml");
        ret = GmcExecuteAsync(g_stmtAsync, &requestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata->status);
        EXPECT_EQ(1, tdata->affectRows);
    }
    //删除
    requestCtx.deleteCb = delete_vertex_callback;
    requestCtx.userData = tdata;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, VertexLabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {

        ret = GmcSetIndexKeyValue(g_stmtAsync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtAsync, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecuteAsync(g_stmtAsync, &requestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, tdata->status);
        ASSERT_EQ(1, tdata->affectRows);
    }
    //查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 10; i < TEST_INSERT_VERTEX_NUM; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = GmcSetIndexKeyName(g_stmt, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryPropertyByNameAndCheck(g_stmt, i + 1, (char *)"testdml");
    }
    //全表扫描
    uint32_t fetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    //记录数check：总记录数减去删掉的记录数
    EXPECT_EQ((TEST_INSERT_VERTEX_NUM - 10), fetchNum);
    free(tdata);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// src insert 同步
void T80_insert_srcVertex_records(GmcStmtT *stmt, int num, char *value_str)
{
    uint32_t F0_value = 1;
    uint32_t F2_value = 10;
    int16_t F3_value = 10;
    int32_t ret = 0;
    void *srcLabel = NULL;
    char src_VertexLabel_name[] = "T80";

    for (int i = 0; i < num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, value_str, (strlen(value_str)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ++F0_value;
        ++F2_value;
        ++F3_value;
        // printf("i: %d\n",i);
    }
}

// dst insert 同步
void T90_insert_dstVertex_records(GmcStmtT *stmt, int num, char *value_str)
{
    uint32_t F0_value = 1;
    uint32_t F2_value = 10;
    int16_t F3_value = 10;
    int32_t F4_value = 10;
    int32_t ret = 0;
    void *dstLabel = NULL;
    char dst_VertexLabel_name[] = "T90";

    for (int i = 0; i < num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, dst_VertexLabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, value_str, (strlen(value_str)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // printf("%d\n",i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ++F0_value;
        ++F2_value;
        ++F3_value;
        ++F4_value;
    }
}

void T90_delete_dstVertex_records(GmcStmtT *stmt, int num)
{
    uint32_t F0_value = 1;
    int32_t ret = 0;
    void *dstLabel = NULL;
    char dst_VertexLabel_name[] = "T90";
    char *pkname = (char *)"T90_PK";

    for (int i = 0; i < num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, dst_VertexLabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, pkname);
        EXPECT_EQ(GMERR_OK, ret);
        // 主键同步删除
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ++F0_value;
    }
}


GmcConnT *g_testSubConn = NULL;
int g_data_num = 50;
char *g_sub_info_insert = NULL, *g_sub_info_delete = NULL;
char vertexLabelNameT7[] = "ip4forward";
char vertexLabelNameT8[] = "nhp_group";
char lableT7_PK_name[] = "ip4_key";
char lableT8_PK_name[] = "primary_key";
char edgeLabelName_7_8[] = "from_7_to_8";
const char *g_subName_insert = "subPath_7_insert";
const char *g_subName_delete = "subPath_7_delete";
#if 0
//016.namespace a下进行path订阅操作
TEST_F(test_namespace03, DDL_030_003_016)
{
    int ret, i;
    int userDataIdx = 0;
    char *vertexLabelJsonT7 = NULL;
    char *vertexLabelJsonT8 =NULL;
    char *edgeLabelJson_7_8 = NULL;
    void *labelT8=NULL,*labelT7=NULL;
    char *edgelabelName=(char *)"from_7_to_8";
    char *vertexlabel7=(char *)"ip4forward";
    char *vertexlabel8=(char *)"nhp_group";
    int affectRows;
    unsigned int len;  
    GmcStmtT *stmt_sub=NULL;
    g_subIndex_delete_01=0;
    g_subIndex_insert_01=0;
  
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

       //创建#7
    readJanssonFile("schemaFile/ip4forward.gmjson", &vertexLabelJsonT7);
    EXPECT_NE((void *)NULL,vertexLabelJsonT7);
    ret=GmcUseNamespace(g_stmt,g_namespace);
    EXPECT_EQ(GMERR_OK, ret);     
    ret=GmcDropEdgeLabel(g_stmt,edgelabelName);   
    ret=GmcDropVertexLabel(g_stmt,vertexlabel7);  
    ret=GmcDropVertexLabel(g_stmt,vertexlabel8);      

    ret =  GmcCreateVertexLabel(g_stmt, vertexLabelJsonT7, NULL);
    EXPECT_EQ(GMERR_OK,ret);
    //创建#8
    readJanssonFile("schemaFile/nhp_group.gmjson", &vertexLabelJsonT8);
    EXPECT_NE((void *)NULL,vertexLabelJsonT8);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJsonT8, NULL);
    EXPECT_EQ(GMERR_OK,ret);

    //创建edge_7_8
    readJanssonFile("schemaFile/edge_7_8.gmjson", &edgeLabelJson_7_8);
    EXPECT_NE((void *)NULL,edgeLabelJson_7_8);
    ret =GmcCreateEdgeLabel(g_stmt, edgeLabelJson_7_8, NULL);
    EXPECT_EQ(GMERR_OK,ret);
    free(vertexLabelJsonT7);
    free(vertexLabelJsonT8);
    free(edgeLabelJson_7_8);        
    readJanssonFile("schemaFile/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL,g_sub_info_insert);
    readJanssonFile("schemaFile/fib_path_7_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL,g_sub_info_delete);

    g_data_num = 10;
    int chanRingLen = 256;
    const char *subConnName = (const char*)"subConnName";
    //创建订阅连接  
    ret = testSubConnect(&g_testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK,ret);   

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info_insert, g_testSubConn, test_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#7的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info_delete, g_testSubConn,test_sn_callback, user_data); 
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_delete);
    free(g_sub_info_insert);    
    //写#8的数据
    for (i = 0; i < g_data_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt,vertexLabelNameT8, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK,ret);
        test_setVertexProperty_8(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK,ret);
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    test_scan_8(g_stmt, vertexLabelNameT8, 0, g_data_num);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    for (i = 0; i < g_data_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt,vertexLabelNameT7, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK,ret);
        printf(" userDataIdx  is %d \n",userDataIdx);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK,ret);
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    test_scan_7(g_stmt, vertexLabelNameT7, 0, g_data_num);

    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
    
    userDataIdx = 0;
    //删除#7的数据
    for (i = 0; i < g_data_num; i++)
    {

        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt,vertexLabelNameT7, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK,ret);
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret=GmcSetIndexKeyName(g_stmt,lableT7_PK_name);
        EXPECT_EQ(GMERR_OK,ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK,ret); 
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
    ret=GmcDropEdgeLabel(g_stmt,edgelabelName);   
    ret=GmcDropVertexLabel(g_stmt,vertexlabel7);  
    ret=GmcDropVertexLabel(g_stmt,vertexlabel8);  
    // 释放订阅连接
    ret = testSubDisConnect(g_testSubConn, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);

}
#endif
// 017.namespace a下进行全表订阅操作:三种订阅
//先订阅再写数据、更新数据、删除数据
TEST_F(test_namespace03, DDL_030_003_017)
{
    int ret = 0;
    int i = 0;
    int userDataIdx = 0;
    int affectRows = 0;
    unsigned int len = 0;
    char *vertexLabel_schema = NULL;
    char *VertexLabel_name = (char *)"testdml";
    char *test_str = (char *)"testdml";
    const char *subName = "subVertexLabel";
    char *g_sub_info = NULL;
    char *NamespaceC = (char *)"userC";
    void *label = NULL;
    char *labelPk_name = (char *)"PkName";
    int chanRingLen = 256;
    const char *subConnName = (const char *)"subConnName";
    g_data_num = 1000;
    GmcStmtT *stmt_sub = NULL;
    bool isFinish = false;

    GmcDropNamespace(g_stmt, NamespaceC);
    ret = GmcCreateNamespace(g_stmt, NamespaceC, g_namespaceUserName);
    ret = GmcUseNamespace(g_stmt, NamespaceC);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/vertex_all_type.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    //创建vertex_label
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    //释放vertexLabel_schema
    free(vertexLabel_schema);
    //创建订阅连接
    ret = testSubConnect(&g_testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);
    //全表订阅
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_testSubConn, namespace_sn_callback, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    free(g_sub_info);

    //插入
    uint32_t tmp = 0;
    g_subIndex_insert = 0;
    g_subIndex_delete = 0;
    g_subIndex_update = 0;
    for (uint32_t i = 0; i < g_data_num; i++) {
        tmp = i;
        ((int *)(user_data->new_value))[userDataIdx] = tmp;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetVertexPropertyByName_PK(g_stmt, tmp);
        TestGmcSetVertexPropertyByName(g_stmt, tmp, test_str);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);

        ASSERT_EQ(1, affectRows);
        usleep(1000);
    }
    //更新
    for (uint32_t i = 0; i < g_data_num; i++) {
        // pk[i]=i;
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        ((int *)(user_data->new_value))[userDataIdx] = tmp + 1;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        ret = GmcSetIndexKeyName(g_stmt, labelPk_name);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetVertexPropertyByName(g_stmt, (tmp + 1), (char *)"testxxx");
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // get affect row
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);

        ASSERT_EQ(1, affectRows);
        // check
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        ret = GmcSetIndexKeyName(g_stmt, labelPk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryPropertyByNameAndCheck(g_stmt, tmp + 1, (char *)"testxxx");
        usleep(1000);
    }
    //删除
    for (int i = 0; i < g_data_num; i++) {
        tmp = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        ret = GmcSetIndexKeyName(g_stmt, labelPk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // get affect row
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);

        ASSERT_EQ(1, affectRows);
        // check
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, labelPk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(true, isFinish);
        usleep(1000);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    ASSERT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt, subName);
    ASSERT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(g_testSubConn, stmt_sub);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, NamespaceC);
    ASSERT_EQ(GMERR_OK, ret);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

// 018.edgelabel关联的vertexLabel在两个namespace下进行创建
TEST_F(test_namespace03, DDL_030_003_018)
{
    int32_t ret = 0;
    char *edge_label_schema = NULL;
    char *dst_vertexLabel_schema = NULL;
    char *src_vertexLabel_schema = NULL;
    char src_VertexLabel_name[] = "T80";
    char dst_VertexLabel_name[] = "T90";
    void *edgelabe = NULL;
    char EdgeLabelName[] = "from_T80_to_T90";

    readJanssonFile("schemaFile/vertexSrcLabel_test_schema.gmjson", &src_vertexLabel_schema);
    ASSERT_NE((void *)NULL, src_vertexLabel_schema);
    readJanssonFile("schemaFile/vertexDstLabel_test_schema.gmjson", &dst_vertexLabel_schema);
    ASSERT_NE((void *)NULL, dst_vertexLabel_schema);
    readJanssonFile("schemaFile/EdgeLabel_from_T80_to_T90.gmjson", &edge_label_schema);
    ASSERT_NE((void *)NULL, edge_label_schema);

    ret = GmcUseNamespace(g_stmt, g_namespace);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, src_VertexLabel_name);
    //创建src_vertex_label
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, src_vertexLabel_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    //切换namespace
    ret = GmcUseNamespace(g_stmt, "public");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, dst_VertexLabel_name);
    ret = GmcDropVertexLabel(g_stmt, src_VertexLabel_name);
    GmcDropEdgeLabel(g_stmt, EdgeLabelName);
    //创建dst_vertex_label
    ret = GmcCreateVertexLabel(g_stmt, dst_vertexLabel_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    //创建edgelabel
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, NULL);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, dst_VertexLabel_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, src_VertexLabel_name);

    free(src_vertexLabel_schema);
    free(dst_vertexLabel_schema);
    free(edge_label_schema);
}

// 019.创建订阅的vertexLabel在另一个namespace中
TEST_F(test_namespace03, DDL_030_003_019)
{
    int ret = 0;
    char *vertexLabel_schema = NULL;
    char *VertexLabel_name = (char *)"testdml";
    char *g_sub_info = NULL;
    char *Namespace = (char *)"userD";
    void *label = NULL;
    char *labelPk_name = (char *)"PkName";
    //创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = "subConnName";
    GmcStmtT *stmt_sub = NULL;
    const char *subName = "subVertexLabel";

    ret = GmcDropNamespace(g_stmt, Namespace);
    ret = GmcCreateNamespace(g_stmt, Namespace, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/vertex_all_type.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    //创建vertex_label
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    //释放vertexLabel_schema
    free(vertexLabel_schema);
    ret = GmcUseNamespace(g_stmt, Namespace);
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅连接
    ret = testSubConnect(&g_testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    //全表订阅失败
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_testSubConn, namespace_callback, NULL);  // 回调函数不能为NULL需要适配
    EXPECT_EQ(
        GMERR_UNDEFINED_TABLE, ret);  // 原来错误码STATUS_QUERY_INVALID_LABEL_NAME  适配错误码为GMERR_UNDEFINED_TABLE
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_sub_info);
    // 释放订阅连接
    ret = testSubDisConnect(g_testSubConn, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, Namespace);
    EXPECT_EQ(GMERR_OK, ret);
}

#if defined ENV_RTOSV2X
#define REPEAT_CREATE_DROP_NAMESPACE_NUM 5000
#define REPEAT_CREATE_DROP_NAMESPACE_NUM2 2000
#elif defined TEST_STATIC_ASAN
#define REPEAT_CREATE_DROP_NAMESPACE_NUM 5000
#define REPEAT_CREATE_DROP_NAMESPACE_NUM2 1000
#else
#define REPEAT_CREATE_DROP_NAMESPACE_NUM 100000
#define REPEAT_CREATE_DROP_NAMESPACE_NUM2 5000
#endif

// 021.重复创建删除空的namespace100000次
TEST_F(test_namespace03, DDL_030_003_021)
{
    int32_t ret = 0;
    const char *name1 = (const char *)"user021";
    for (int i = 0; i < REPEAT_CREATE_DROP_NAMESPACE_NUM; i++) {
        ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, name1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 022.连接达上限后继续create/use namespace并ddl/dml
TEST_F(test_namespace03, DDL_030_003_022)
{
    int32_t ret = 0;
    GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
    char *vertexLabel_schema = NULL;
    void *label = NULL;
    int num = 100;
    char VertexLabel_name[] = "testdml";
    const char *name1 = (const char *)"user022";
    int connSuccessNum = 0;
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    // setuptestcase中已经有一个同步和异步的建连
    for (int i = 0; i < (MAX_CONN_SIZE - 2 - existConnNum); i++) {
        ret = testGmcConnect(&conn[i]);
        if (ret != GMERR_OK) {
            printf("i:%d ret:%d\n", i, ret);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        connSuccessNum++;
    }
    GmcDropNamespace(g_stmt, name1);
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    // ddl
    readJanssonFile("schemaFile/vertex_all_type.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);

    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    //创建vertex_label
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    //释放vertexLabel_schema
    free(vertexLabel_schema);
    //同步 dml操作
    namespace_dml_sync(g_stmt, num);
    test_scan_testdml(g_stmt, VertexLabel_name, num);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    // dml
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < connSuccessNum; i++) {
        ret = testGmcDisconnect(conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 023 在nameSpace a下访问nameSpace b下的表
TEST_F(test_namespace03, DDL_030_003_023)
{
    int32_t ret = 0;
    GmcConnT *conn[1024] = {NULL};
    char *vertexLabel_schema = NULL;
    void *label = NULL;
    char VertexLabel_name[] = "testnamespace";
    const char *name1 = (const char *)"user023";

    GmcDropNamespace(g_stmt, name1);
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    // ddl
    readJanssonFile("schemaFile/vertex_label_test.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);

    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    //创建vertex_label
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    //释放vertexLabel_schema
    free(vertexLabel_schema);

    ret = GmcUseNamespace(g_stmt, (char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024 namespace下创建tree表并写表
TEST_F(test_namespace03, DDL_030_003_024)
{
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    void *vertexLabel = NULL;
    char VertexLabel_name[] = "TreeLabel";
    const char *name1 = (const char *)"user024";
    char pk_name[] = "Tree_PK";
    int start_num1 = 0;
    int end_num1 = 1000;
    int end_num2 = 2000;
    char *teststr1 = (char *)"testve";
    char *teststr2 = (char *)"vetest";
    char *teststr3 = (char *)"aaaaaa";
    char *schemaTest = NULL;
    int array_num = 3;
    int vector_num = 3;
    bool bool_value = true;
    bool isFinish = true;

    GmcDropNamespace(g_stmt, name1);
    ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt, VertexLabel_name);
    readJanssonFile("./schemaFile/TreeModelSchema.gmjson", &schemaTest);
    ASSERT_NE((void *)NULL, schemaTest);

    ret = GmcCreateVertexLabel(g_stmt, schemaTest, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schemaTest);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    //插入记录1
    TestGmcInsertVertex(g_stmt, false, teststr1, start_num1, end_num1, array_num, VertexLabel_name);
    //插入记录2
    TestGmcInsertVertex(g_stmt, true, teststr2, end_num1, end_num2, array_num, VertexLabel_name);
    // check
    TestGmcDirectFetchVertex(g_stmt, false, teststr1, start_num1, end_num1, array_num, VertexLabel_name, pk_name, true);
    TestGmcDirectFetchVertex(g_stmt, true, teststr2, end_num1, end_num2, array_num, VertexLabel_name, pk_name, true);
    //更新
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;
    TestGetRootAndChild_A(g_stmt, &root, &T1, &T2);
    for (int i = start_num1; i < end_num2; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TestGetRootAndChild_A(g_stmt, &root, &T1, &T2);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_R(root, i + 1, bool_value, teststr3);
        TestGmcSetNodePropertyByName_P(T1, i + 1, bool_value, teststr3);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, i + 1, bool_value, teststr3);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // check
    for (int i = start_num1; i < end_num2; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        TestGetRootAndChild_A(g_stmt, &root, &T1, &T2);
        TestGetUniqueLocalHash(root, i);
        TestGmcGetNodePropertyByName_R(root, i + 1, bool_value, teststr3);
        TestGmcGetNodePropertyByName_P(T1, i + 1, bool_value, teststr3);
        // 读取array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, i + 1, bool_value, teststr3);
        }
    }

    //删除
    for (int i = start_num1; i < end_num1; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // check
    for (int i = start_num1; i < end_num1; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(true, isFinish);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //全表扫描
    uint32_t fetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    while (1) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    EXPECT_EQ(end_num1, fetchNum);

    ret = GmcUseNamespace(g_stmt, (char *)"public");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025 异步创建切换删除namespace并异步ddl/dml操作
TEST_F(test_namespace03, DDL_030_003_025)
{
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    char VertexLabel_name[] = "testdml";
    void *label = NULL;
    char *teststr = (char *)"vertexs";
    char *name1 = (char *)"user025";
    int affectRows = 0;
    unsigned int len = 0;
    AsyncUserDataT *tdata = NULL;
    void *label_async = NULL;
    char *pkName = (char *)"PkName";
    bool isFinish = false;

    //异步切换namespace
    tdata = (AsyncUserDataT *)malloc(sizeof(AsyncUserDataT));
    memset(tdata, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateNamespaceAsync(g_stmtAsync, name1, g_namespaceUserName, create_namespace_callback, tdata);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata->status);
    ret = GmcUseNamespaceAsync(g_stmtAsync, name1, use_namespace_callback, tdata);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata->status);
    readJanssonFile("schemaFile/vertex_all_type.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, VertexLabel_name, drop_vertex_label_callback, tdata);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    ASSERT_EQ(GMERR_OK, ret);
    //创建vertex_label
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertexLabel_schema, NULL, create_vertex_label_callback, tdata);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata->status);
    //释放vertexLabel_schema
    free(vertexLabel_schema);

    //插入
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.insertCb = insert_vertex_callback;
    requestCtx.userData = tdata;
    for (int i = 0; i < TEST_INSERT_VERTEX_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync, VertexLabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetVertexPropertyByName_PK(g_stmtAsync, i);
        TestGmcSetVertexPropertyByName(g_stmtAsync, i, teststr);
        ret = GmcExecuteAsync(g_stmtAsync, &requestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata->status);
        EXPECT_EQ(1, tdata->affectRows);
    }
    //更新
    requestCtx.updateCb = update_vertex_callback;
    requestCtx.userData = tdata;
    for (int i = 0; i < TEST_INSERT_VERTEX_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync, VertexLabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TestGmcSetVertexPropertyByName(g_stmtAsync, (i + 1), (char *)"testdml");
        ret = GmcSetIndexKeyName(g_stmtAsync, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecuteAsync(g_stmtAsync, &requestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata->status);
        EXPECT_EQ(1, tdata->affectRows);
    }
    //删除
    requestCtx.deleteCb = delete_vertex_callback;
    requestCtx.userData = tdata;
    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync, VertexLabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = GmcSetIndexKeyName(g_stmtAsync, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecuteAsync(g_stmtAsync, &requestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata->status);
        EXPECT_EQ(1, tdata->affectRows);
    }
    ret = GmcUseNamespace(g_stmt, name1);
    ASSERT_EQ(GMERR_OK, ret);
    //同步查询
    for (int i = 10; i < TEST_INSERT_VERTEX_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = GmcSetIndexKeyName(g_stmt, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryPropertyByNameAndCheck(g_stmt, i + 1, (char *)"testdml");
    }
    //全表扫描
    uint32_t fetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    //记录数check：总记录数减去删掉的记录数
    EXPECT_EQ((TEST_INSERT_VERTEX_NUM - 10), fetchNum);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, VertexLabel_name, drop_vertex_label_callback, tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata->status);
    //删namespace
    ret = GmcDropNamespaceAsync(g_stmtAsync, name1, drop_namespace_callback, tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata->status);
    free(tdata);
}

// 026.重复创建namespace 创表打开表删表删除空的namespace 5000次
TEST_F(test_namespace03, DDL_030_003_026)
{
    int32_t ret = 0;
    const char *name1 = (const char *)"user026";
    char VertexLabel_name[] = "TreeLabel";
    char *schemaTest = NULL;
    void *vertexLabel = NULL;
    char pk_name[] = "Tree_PK";
    int start_num1 = 0;
    int end_num1 = 10;
    char *teststr1 = (char *)"testve";

    for (int i = 0; i < REPEAT_CREATE_DROP_NAMESPACE_NUM2; i++) {
        if (i % 100 == 0) {
            AW_FUN_Log(LOG_INFO, "---test id = %d---", i);
        }
        ret = GmcCreateNamespace(g_stmt, name1, g_namespaceUserName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, name1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcDropVertexLabel(g_stmt, VertexLabel_name);
        readJanssonFile("./schemaFile/TreeModelSchema_vector.gmjson", &schemaTest);
        ASSERT_NE((void *)NULL, schemaTest);

        ret = GmcCreateVertexLabel(g_stmt, schemaTest, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        free(schemaTest);

        //插入记录1
        TestGmcInsertVertex_V(g_stmt, false, teststr1, start_num1, end_num1, 3, 3, VertexLabel_name);
        // check
        TestGmcDirectFetchVertex_V(
            g_stmt, false, teststr1, start_num1, end_num1, 3, 3, VertexLabel_name, pk_name, true);
        ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, name1);
        EXPECT_EQ(GMERR_OK, ret);
        #if defined ENV_RTOSV2X
        if (i % 200 == 0) {
            sleep(2);
        }
        #endif
    }
}

// 027 非同一个连接句柄下访问namespace下的表
TEST_F(test_namespace03, DDL_030_003_027)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char VertexLabel_name[] = "TreeLabel";
    char *schemaTest = NULL;
    void *vertexLabel = NULL;

    //同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VertexLabel_name);
    readJanssonFile("./schemaFile/TreeModelSchema_vector.gmjson", &schemaTest);
    ASSERT_NE((void *)NULL, schemaTest);

    ret = GmcCreateVertexLabel(g_stmt, schemaTest, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schemaTest);
    ret = testGmcPrepareStmtByLabelName(stmt, VertexLabel_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}


// 029 public namespace的测试
TEST_F(test_namespace03, DDL_030_003_029)
{
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    void *vertexLabel = NULL;
    char VertexLabel_name[] = "TreeLabel";
    char pk_name[] = "Tree_PK";
    int start_num1 = 0;
    int end_num1 = 1000;
    int end_num2 = 2000;
    char *teststr1 = (char *)"testve";
    char *teststr2 = (char *)"vetest";
    char *teststr3 = (char *)"aaaaaa";
    char *schemaTest = NULL;
    int array_num = 3;
    int vector_num = 3;
    bool bool_value = true;
    bool isFinish = true;

    GmcDropVertexLabel(g_stmt, VertexLabel_name);
    readJanssonFile("./schemaFile/TreeModelSchema_vector.gmjson", &schemaTest);
    ASSERT_NE((void *)NULL, schemaTest);

    ret = GmcCreateVertexLabel(g_stmt, schemaTest, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaTest);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    //插入记录1
    TestGmcInsertVertex(g_stmt, false, teststr1, start_num1, end_num1, array_num, VertexLabel_name);
    //插入记录2
    TestGmcInsertVertex(g_stmt, true, teststr2, end_num1, end_num2, array_num, VertexLabel_name);
    // check
    TestGmcDirectFetchVertex(g_stmt, false, teststr1, start_num1, end_num1, array_num, VertexLabel_name, pk_name, true);
    TestGmcDirectFetchVertex(g_stmt, true, teststr2, end_num1, end_num2, array_num, VertexLabel_name, pk_name, true);
    //更新
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL;
    //获取根节点和子节点
    TestGetRootAndChild_A(g_stmt, &root, &T1, &T2);
    for (int i = start_num1; i < end_num2; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TestGetRootAndChild_A(g_stmt, &root, &T1, &T2);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_R(root, i + 1, bool_value, teststr3);
        TestGmcSetNodePropertyByName_P(T1, i + 1, bool_value, teststr3);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, i + 1, bool_value, teststr3);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // check
    for (int i = start_num1; i < end_num2; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        TestGetRootAndChild_A(g_stmt, &root, &T1, &T2);
        TestGetUniqueLocalHash(root, i);
        TestGmcGetNodePropertyByName_R(root, i + 1, bool_value, teststr3);
        TestGmcGetNodePropertyByName_P(T1, i + 1, bool_value, teststr3);
        // 读取array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, i + 1, bool_value, teststr3);
        }
    }

    //删除
    for (int i = start_num1; i < end_num1; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // check
    for (int i = start_num1; i < end_num1; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(true, isFinish);
    }
    //全表扫描
    uint32_t fetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, VertexLabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    while (1) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    EXPECT_EQ(end_num1, fetchNum);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

#if 0
//028 public namespace下创建kv表并写kv表,迭代一不支持，未做隔离
const char *g_KVconfigJson = R"({"max_record_count":100000})";
TEST_F(test_namespace03, DDL_030_003_028)
{
    int32_t ret=0;  
    char  KVLabel_name[]="KVLabel";  
    char * schemaTest=NULL; 
    void * vertexLabel=NULL;
    char  pk_name[]="KV_PK";  
    uint32_t insertNum=100;
    char *teststr1=(char *)"testve";
    void *kvLabel=NULL;
    const char *namespaceName=(const char *)"public";

    ret = GmcKvDropTable(g_stmt,KVLabel_name); 
    ret = GmcKvCreateTable (g_stmt, KVLabel_name, g_KVconfigJson);
    ASSERT_EQ(GMERR_OK,ret); 
    ret=GmcUseNamespace(g_stmt,g_namespace);
    ASSERT_EQ(GMERR_OK,ret); 
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVLabel_name);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE,ret);    
    ret=testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret=GmcUseNamespace(g_stmt,namespaceName);
    ASSERT_EQ(GMERR_OK,ret); 
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVLabel_name);
    ASSERT_EQ(GMERR_OK,ret); 
    //写表
    char keyT[10]="F0";
    char * qvalue[32]={0};
    uint32_t outValueLen=0;    
    GmcKvTupleT kvInfo = { 0 };    
    for(uint32_t i=0;i<insertNum;i++)
    {
        sprintf((char *)keyT,"F%d",i);         
        uint32_t fvalue=i;
        //插入记录
        kvInfo.key = keyT;
        kvInfo.keyLen = strlen(keyT);
        kvInfo.value = &fvalue;
        kvInfo.valueLen = sizeof(uint32_t);
        ret = GmcKvSet(g_stmt, keyT, strlen(keyT), &fvalue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询
        ret=GmcKvGet(g_stmt, keyT, strlen(keyT),qvalue,&outValueLen); 
        ASSERT_EQ(GMERR_OK, ret); 
        ASSERT_EQ(i, *(uint32_t*)qvalue);
        ASSERT_EQ(4, outValueLen);    
    }
    //删除
     for(uint32_t i=0;i<(insertNum/2);i++)
    {
        sprintf((char *)keyT,"F%d",i);  
        ret = GmcKvRemove(g_stmt, kvLabel, strlen(keyT), GMC_CONN_TYPE_SYNC);
        ASSERT_EQ(GMERR_OK, ret);
        //查询
        ret=GmcKvGet(g_stmt, keyT, strlen(keyT),qvalue,&outValueLen); 
        ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);  
        ret=testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);

    }
           
    // ret=GmcCloseKvTable(g_stmt); 
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcKvDropTable(g_stmt,KVLabel_name); 
    ASSERT_EQ(GMERR_OK,ret); 
}
#endif
