#include <stdlib.h>
#include <stdio.h>
#include <string>
#include <unistd.h>
#include "gtest/gtest.h"
#include "sub_tools.h"

int ret = 0;
GmcConnT *g_conn;
GmcStmtT *g_stmt;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

const char *g_label_config_local = R"(
        {
            "max_record_count":10000,
            "writers":"abc"
        })";

const char *g_label_config2 = R"(
        {
            "max_record_count":10000,
            "writers":"abc"
        })";

class BitFieldUint32Or64 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    static int server_restart_flag;
    virtual void SetUp();
    virtual void TearDown();
};

void BitFieldUint32Or64::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    AW_CHECK_LOG_BEGIN();
}
void BitFieldUint32Or64::TearDown()
{
    AW_CHECK_LOG_END();
    GmcFreeStmt(g_stmt);
    int ret = testGmcDisconnect(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
}

int BitFieldUint32Or64::server_restart_flag = 0;

void set_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t pk_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_Property(GmcStmtT *stmt, int i, bool bool_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    char f14_value[10];
    snprintf(f14_value, 10, "%d", 14 * i);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f15_value = i;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BITFIELD32, &f15_value, sizeof(f15_value));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_Property_12(GmcStmtT *stmt, int i, bool bool_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    char f14_value[10];
    snprintf(f14_value, 10, "%d", 14 * i);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f15_value = i;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BITFIELD32, &f15_value, sizeof(f15_value));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f16_value = i;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(f16_value));
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint32位域字段；插入数据，查询数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_001)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint32位域字段；
                  插入数据，查询数据，更新数据，查询数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_002)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 更新
    for (uint32_t i = 0; i < 8; i++) {
        uint32_t value = 7;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //设置F15属性值
        ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD32, &value, sizeof(value));
        ASSERT_EQ(GMERR_OK, ret);
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(g_stmt);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }

        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == 7) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint32位域字段；
                  插入数据，查询数据，删除数据，查询数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_003)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < 8; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool eof = true;
        ret = GmcFetch(g_stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(eof, true);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

void *pro4(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int32_t value0 = 0;
    uint32_t value1 = 0;
    const char *labelName = "demo";

    int ret = testGmcConnect(&conn_t, &stmt_t);

    for (int i = 0; i < 1000; i++) {
        for (unsigned int i = 0; i < 8; i++) {
            //设置过滤条件
            ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            //查询顶点
            ret = GmcSetIndexKeyName(stmt_t, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt_t);
            EXPECT_EQ(GMERR_OK, ret);
            // Get F15
            uint32_t size;
            uint32_t value;
            bool isNull;
            while (true) {
                bool isFinish;
                ret = GmcFetch(stmt_t, &isFinish);
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }

                GmcGetVertexPropertySizeByName(stmt_t, "F15", &size);
                ret = GmcGetVertexPropertyByName(stmt_t, "F15", &value, size, &isNull);
                EXPECT_EQ(GMERR_OK, ret);
            }
            // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
            if (isNull == 0) {
                ret = 1;
                if (value == i) {
                    ret = 0;
                }
                EXPECT_EQ(0, ret);  //预期获取插入值
            } else {
                EXPECT_EQ(0, 1);
            }
        }
    }

    GmcFreeIndexKey(stmt_t);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint32位域字段；
                  多线程同时对位域字段进行增删改查dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_004)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro4, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建mainstore；创建label，带有uint32位域字段
                  单线程对位域字段进行增删改查dml操作;循环1000次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_005)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int loop = 0; loop < 1000; loop++) {
        // 插入顶点
        for (int i = 0; i < 8; i++) {
            set_PK(g_stmt, i);
            set_Property(g_stmt, i, 0);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // update
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        for (uint32_t i = 0; i < 8; i++) {
            //更新数据前先reset
            // ret = GmcResetVertex(g_stmt, false);
            // ASSERT_EQ(GMERR_OK,ret);
            uint32_t value = 7;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F15属性值
            ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD32, &value, sizeof(value));
            ASSERT_EQ(GMERR_OK, ret);
            //更新顶点
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcFreeIndexKey(g_stmt);
        }

        // query
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        for (unsigned int i = 0; i < 8; i++) {
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    LOG("");
    //关闭顶点label
    LOG("");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

void *pro6(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int32_t value0 = 0;
    uint32_t value1 = 0;
    const char *labelName = "demo";

    int ret = testGmcConnect(&conn_t, &stmt_t);
    uint32_t arg = *(uint32_t *)args;
    int affectRows;
    unsigned int len;

    srand(time(0));
    for (unsigned int i = 0; i < 1000; i++) {
        int num = rand() % 8;

        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        // merge Vertex
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyId(stmt_t, 0);  // 2021.11.15 merge时，必须设置 key value 和 key name
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        set_Property(stmt_t, num, 0);

        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }

        num = rand() % 8;

        // replace Vertex
        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_PK(stmt_t, num);
        set_Property(stmt_t, num, 0);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }
    }

    GmcFreeIndexKey(stmt_t);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/*****************************************************************************
 * Description  : 创建mainstore；创建label，带有uint32位域字段；
                  多线程同时对位域字段进行增删改查dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_006)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro6, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建deltastore;创建label，带有多个uint32位域字段(占用内存空间=32bit);
                  执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_007)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_007.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 1000; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);


    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建mainstore创建label，带有多个uint32位域字段(占用内存空间=32bit);
                  执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_008)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_007.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 1000; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，带有string位域字段
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_009)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_009.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label,superfield中带有uint32位域字段
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_010)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_010.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，同时有uint8和uint32位域字段；对位域字段进行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_011)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_011.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int loop = 0; loop < 1000; loop++) {
        // 插入顶点
        for (int i = 0; i < 8; i++) {
            set_PK(g_stmt, i);
            set_Property(g_stmt, i, 0);

            uint8_t f16_value = i;
            ret = GmcSetVertexProperty(g_stmt, "F16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(f16_value));
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // update
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        for (uint32_t i = 0; i < 8; i++) {
            //更新数据前先reset
            // ret = GmcResetVertex(g_stmt, false);
            // ASSERT_EQ(GMERR_OK,ret);
            uint32_t value1 = 7;
            uint8_t value2 = 7;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F15属性值
            ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD32, &value1, sizeof(value1));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F16属性值
            ret = GmcSetVertexProperty(g_stmt, "F16", GMC_DATATYPE_BITFIELD8, &value2, sizeof(value2));
            ASSERT_EQ(GMERR_OK, ret);
            //更新顶点
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcFreeIndexKey(g_stmt);
        }

        // query
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        for (unsigned int i = 0; i < 8; i++) {
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

void *pro12(void *args)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int32_t value0 = 0;
    uint32_t value1 = 0;
    const char *labelName = "demo";
    int insertNum = 1000;

#ifdef ENV_RTOSV2X
    insertNum = 100;
#endif

    int ret = testGmcConnect(&conn_t, &stmt_t);
    ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    srand(time(0));
    for (int i = 0; i < insertNum; i++) {
        int num = rand() % 8;
        // merge Vertex
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyId(stmt_t, 0);  // 2021.11.15 merge时，必须设置 key value 和 key name
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        set_Property_12(stmt_t, num, 0);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_UNIQUE_VIOLATION) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }

        // replace Vertex
        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_PK(stmt_t, num);
        set_Property_12(stmt_t, num, 0);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_UNIQUE_VIOLATION) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }

        if (i == 999) {
            LOG("i = %d", i);
        }
    }

    GmcFreeIndexKey(stmt_t);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
/*****************************************************************************
 * Description  : 创建label，同时有多个uint8和uint32位域字段；多线程同时对位域字段进行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_012)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_012.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro12, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        ret = pthread_join(td[i], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，uint32位域字段为主键
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/

TEST_F(BitFieldUint32Or64, DDL_023_013)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_013.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，uint32位域字段为localhash
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/

TEST_F(BitFieldUint32Or64, DDL_023_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_014.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}


char *label_schema = NULL;
const char *labelName = "OP_T0";
void *vertexLabel = NULL;
int start_num = 0;
int end_num = 1;
int array_num = 3;
int vector_num = 3;

class TreeModel : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TreeModel::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void TreeModel::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    GmcDetachAllShmSeg();
}

void TreeModel::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    AW_CHECK_LOG_BEGIN();
}

void TreeModel::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    free(label_schema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_P(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F1", &f1_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F2", &f2_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F3", &f3_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F4", &f4_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F5", &f5_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F6", &f6_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F7", &f7_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"F14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"F15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"F16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_p(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P0", &f0_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P1", &f1_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P2", &f2_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P3", &f3_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P4", &f4_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P5", &f5_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P6", &f6_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P7", &f7_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"P14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"P15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"P16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_A(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A0", &f0_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A1", &f1_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A2", &f2_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A3", &f3_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A4", &f4_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A5", &f5_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A6", &f6_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A7", &f7_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"A14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"A15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"A16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_V(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V0", &f0_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V1", &f1_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V2", &f2_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V3", &f3_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V4", &f4_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V6", &f6_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V7", &f7_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"V14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"V14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"V15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"V16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    GmcNodeT *root, *T1, *T2, *T3;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        //设置根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i * index);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);

        //设置T1节点
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);

        // 插入array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);  // T2 是在 T1 下，故第一个参数应该是 T1
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i * index, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                ASSERT_EQ(GMERR_OK, ret);
            } else {
                ASSERT_EQ(GMERR_NO_DATA, ret);
            }
        }

        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/*****************************************************************************
 * Description  : tree模型中，一个节点下含有一个uint32位域字段；执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(TreeModel, DDL_023_017)
{
    readJanssonFile("schemaFile/NormalTreeModel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcInsertVertex(g_stmt, 1, 0, (char *)"string", start_num, 21, array_num, vector_num, labelName);
}

/*****************************************************************************
 * Description  : deltastore中，循环插入一万条数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_018)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_018.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 10000; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);


    free(label_schema);
}

/*****************************************************************************
 * Description  : mainstore中，循环插入一万条数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_019)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_018.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 10000; i++) {
        set_PK(g_stmt, i);
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint32_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，带有uint32位域字段；开启事务，进行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_020)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label

    for (int loop = 0; loop < 1000; loop++) {
        // 插入顶点
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        for (int i = 0; i < 8; i++) {
            ret = GmcTransCommit(g_conn);
            ASSERT_EQ(GMERR_OK, ret);
            set_PK(g_stmt, i);
            set_Property(g_stmt, i, 0);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);

            //更新数据前先reset
            ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
            ASSERT_EQ(GMERR_OK, ret);

            uint32_t value = 7;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F15属性值
            ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD32, &value, sizeof(value));
            ASSERT_EQ(GMERR_OK, ret);
            //更新顶点
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcFreeIndexKey(g_stmt);

            // delete
            ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcTransCommit(g_conn);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

char g_label_name[] = "T20_all_type";
char g_lable_PK[] = "T20_PK";
char g_lable_hash[] = "T20_hash";
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

class ConditionalSubscriptionAndPush : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void ConditionalSubscriptionAndPush::SetUp()
{
    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info = NULL;
    int ret;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt_sync, g_label_name);  // 避免环境影响，执行用例前删除可能存在的表
    AW_CHECK_LOG_BEGIN();
}
void ConditionalSubscriptionAndPush::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //删表断连
    test_close_and_drop_label(g_stmt_sync, g_label, g_label_name);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(g_schema);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

void test_setVertexPK(GmcStmtT *stmt, int index)
{
    int ret;
    uint64_t value7 = index;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_setVertexProperty_32(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = index;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = index;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = index;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = index;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)index;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + index;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = index;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = index;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = index;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = index;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = index;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_setVertexProperty_64(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = index;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = index;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = index;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = index;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)index;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + index;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = index;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = index;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = index;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = index;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value17 = index;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD64, &value17, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : uint32位域字段支持条件订阅
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(ConditionalSubscriptionAndPush, DDL_023_021)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schemaFile/all_type_schema_021.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/all_type_schema_subinfo_003_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty_32(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        //等待delete事件推送完成
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

void set_Property_64(GmcStmtT *stmt, int i, bool bool_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    char f14_value[10];
    snprintf(f14_value, 10, "%d", 14 * i);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f15_value = i;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BITFIELD64, &f15_value, sizeof(f15_value));
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint64位域字段；插入数据，查询数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_022)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);


    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint64位域字段；
                  插入数据，查询数据，更新数据，查询数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_023)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // update
    for (uint32_t i = 0; i < 8; i++) {
        //更新数据
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t value = 7;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //设置F15属性值
        ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD64, &value, sizeof(value));
        ASSERT_EQ(GMERR_OK, ret);
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(g_stmt);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == 7) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint64位域字段；
                  插入数据，查询数据，删除数据，查询数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_024)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }
    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < 8; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool eof = true;
        ret = GmcFetch(g_stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(eof, true);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

void *pro25(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int32_t value0 = 0;
    uint32_t value1 = 0;
    const char *labelName = "demo";

    int ret = testGmcConnect(&conn_t, &stmt_t);

    for (int i = 0; i < 1000; i++) {
        for (unsigned int i = 0; i < 8; i++) {
            //设置过滤条件
            ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            //查询顶点
            ret = GmcSetIndexKeyName(stmt_t, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt_t);
            EXPECT_EQ(GMERR_OK, ret);
            // Get F15
            uint32_t size;
            uint64_t value;
            bool isNull;
            while (true) {
                bool isFinish;
                ret = GmcFetch(stmt_t, &isFinish);
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }

                GmcGetVertexPropertySizeByName(stmt_t, "F15", &size);
                ret = GmcGetVertexPropertyByName(stmt_t, "F15", &value, size, &isNull);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    GmcFreeIndexKey(stmt_t);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有uint64位域字段；
                  多线程同时对位域字段进行增删改查dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_025)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro25, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建mainstore；创建label，带有uint64位域字段
                  单线程对位域字段进行增删改查dml操作;循环1000次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_026)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int loop = 0; loop < 1000; loop++) {
        // 插入顶点
        for (int i = 0; i < 8; i++) {
            set_PK(g_stmt, i);
            set_Property_64(g_stmt, i, 0);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // update
        for (uint32_t i = 0; i < 8; i++) {
            //更新
            ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
            ASSERT_EQ(GMERR_OK, ret);
            uint64_t value = 7;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F15属性值
            ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD64, &value, sizeof(value));
            ASSERT_EQ(GMERR_OK, ret);
            //更新顶点
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcFreeIndexKey(g_stmt);
        }

        // 查询
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        for (unsigned int i = 0; i < 8; i++) {
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

void *pro27(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int32_t value0 = 0;
    uint32_t value1 = 0;
    const char *labelName = "demo";

    int ret = testGmcConnect(&conn_t, &stmt_t);

    uint32_t arg = *(uint32_t *)args;
    int affectRows;
    unsigned int len;

    srand(time(0));
    for (unsigned int i = 0; i < 1000; i++) {
        int num = rand() % 8;
        // LOG("merge num = %d", num);
        // merge Vertex
        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyId(stmt_t, 0);  // 2021.11.15 merge时，必须设置 key value 和 key name
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        set_Property_64(stmt_t, num, 0);

        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }

        num = rand() % 8;
        // LOG("replace num = %d", num);
        // replace Vertex
        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_PK(stmt_t, num);
        set_Property_64(stmt_t, num, 0);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }
    }

    GmcFreeIndexKey(stmt_t);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/*****************************************************************************
 * Description  : 创建mainstore；创建label，带有uint64位域字段；
                  多线程同时对位域字段进行增删改查dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_027)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 8; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 8; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro27, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建deltastore;创建label，带有多个uint64位域字段(占用内存空间=64bit);
                  执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_028)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_028.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 1000; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 1000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);


    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建mainstore创建label，带有多个uint32位域字段(占用内存空间=64bit);
                  执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_029)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_028.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 1000; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 1000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，带有string位域字段
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_030)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_030.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label,superfield中带有uint32位域字段
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_031)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_031.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，同时有uint8和uint64位域字段；对位域字段进行dml操作

 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_032)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_032.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int loop = 0; loop < 1000; loop++) {
        // 插入顶点
        for (int i = 0; i < 8; i++) {
            set_PK(g_stmt, i);
            set_Property_64(g_stmt, i, 0);

            uint8_t f16_value = i;
            ret = GmcSetVertexProperty(g_stmt, "F16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(f16_value));
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // update
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        for (uint32_t i = 0; i < 8; i++) {
            //更新数据前先reset
            // ret = GmcResetVertex(g_stmt, false);
            // ASSERT_EQ(GMERR_OK,ret);
            uint64_t value1 = 7;
            uint8_t value2 = 7;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F15属性值
            ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD64, &value1, sizeof(value1));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F16属性值
            ret = GmcSetVertexProperty(g_stmt, "F16", GMC_DATATYPE_BITFIELD8, &value2, sizeof(value2));
            ASSERT_EQ(GMERR_OK, ret);
            //更新顶点
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcFreeIndexKey(g_stmt);
        }

        // 查询
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        for (unsigned int i = 0; i < 8; i++) {
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

void set_Property_33(GmcStmtT *stmt, int i, bool bool_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    char f14_value[10];
    snprintf(f14_value, 10, "%d", 14 * i);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f15_value = i;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BITFIELD64, &f15_value, sizeof(f15_value));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f16_value = i;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(f16_value));
    ASSERT_EQ(GMERR_OK, ret);
}

void *pro33(void *args)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int32_t value0 = 0;
    uint32_t value1 = 0;
    const char *labelName = "demo";
    int insertNum = 1000;

#ifdef ENV_RTOSV2X
    insertNum = 100;
#endif

    int ret = testGmcConnect(&conn_t, &stmt_t);
    ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    srand(time(0));
    for (int i = 0; i < insertNum; i++) {
        int num = rand() % 8;
        // LOG("loopNum = %d, randNum = %d", i, num);
        // merge Vertex
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyId(stmt_t, 0);  // 2021.11.15 merge时，必须设置 key value 和 key name
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        set_Property_33(stmt_t, num, 0);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_UNIQUE_VIOLATION) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }

        // replace Vertex
        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_PK(stmt_t, num);
        set_Property_33(stmt_t, num, 0);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_UNIQUE_VIOLATION) {
            ret = testGmcGetLastError(NULL);
        } else if(ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "ret: %d\n", ret);
        }
    }

    GmcFreeIndexKey(stmt_t);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
/*****************************************************************************
 * Description  : 创建label，同时有多个uint8和uint64位域字段；多线程同时对位域字段进行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_033)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_033.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro33, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，uint64位域字段为主键
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/

TEST_F(BitFieldUint32Or64, DDL_023_034)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_034.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，uint64位域字段为localhash
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/

TEST_F(BitFieldUint32Or64, DDL_023_035)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_035.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}


/*****************************************************************************
 * Description  : tree模型中，节点下含有一个uint64位域字段；执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(TreeModel, DDL_023_038)
{
    readJanssonFile("schemaFile/NormalTreeModel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    char label_name1[] = "OP_T0";
    LOG("label_schema:");
    LOG("%s", label_schema);
    GmcDropVertexLabel(g_stmt, label_name1);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcInsertVertex(g_stmt, 1, 0, (char *)"string", start_num, 21, array_num, vector_num, label_name1);
}

/*****************************************************************************
 * Description  : deltastore中，循环插入一万条数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_039)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_039.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 10000; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);


    free(label_schema);
}

/*****************************************************************************
 * Description  : mainstore中，循环插入一万条数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_040)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_039.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 10000; i++) {
        set_PK(g_stmt, i);
        set_Property_64(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F15
        uint32_t size;
        uint64_t value;
        bool isNull;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F15", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F15", &value, size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            ASSERT_EQ(0, ret);  //预期获取插入值
        } else {
            ASSERT_EQ(0, 1);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : 创建label，带有uint32位域字段；开启事务，进行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_041)
{
    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_022.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config2);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    for (int loop = 0; loop < 1000; loop++) {
        // 插入顶点
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        for (int i = 0; i < 8; i++) {
            ret = GmcTransCommit(g_conn);
            ASSERT_EQ(GMERR_OK, ret);
            set_PK(g_stmt, i);
            set_Property_64(g_stmt, i, 0);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);

            //更新数据
            ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
            ASSERT_EQ(GMERR_OK, ret);

            uint64_t value = 7;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            //设置F15属性值
            ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_BITFIELD64, &value, sizeof(value));
            ASSERT_EQ(GMERR_OK, ret);
            //更新顶点
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcFreeIndexKey(g_stmt);

            // delete
            ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcTransCommit(g_conn);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    free(label_schema);
}

/*****************************************************************************
 * Description  : uint64位域字段支持条件订阅
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(ConditionalSubscriptionAndPush, DDL_023_042)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schemaFile/all_type_schema_042.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/all_type_schema_subinfo_003_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty_64(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple_64, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 创建label时，所有的字段都为uint32位域字段
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(BitFieldUint32Or64, DDL_023_043)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *labelName = "demo";
    void *vertexLabel = NULL;

    readJanssonFile("schemaFile/BitFieldUint32Or64_043.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config_local);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(label_schema);
}
