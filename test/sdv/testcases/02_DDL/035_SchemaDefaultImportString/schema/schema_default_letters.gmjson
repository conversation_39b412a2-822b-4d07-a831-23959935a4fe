[{"version": "2.0", "type": "record", "name": "schema_datatype", "fields": [{"name": "F1", "type": "int8", "default": "aaa"}, {"name": "F2", "type": "uint8", "default": "1"}, {"name": "F3", "type": "int16", "default": "1"}, {"name": "F4", "type": "uint16", "default": "1"}, {"name": "F5", "type": "int32", "default": "1"}, {"name": "F6", "type": "uint32", "default": "1"}, {"name": "F7", "type": "int64", "default": "1"}, {"name": "F8", "type": "uint64", "default": "1"}, {"name": "F9", "type": "int", "default": "1"}, {"name": "F10", "type": "double", "nullable": true, "default": "1"}, {"name": "F11", "type": "float", "nullable": true, "default": "1"}, {"name": "F13", "type": "char", "nullable": true, "default": "1"}, {"name": "F14", "type": "uchar", "nullable": true, "default": "1"}, {"name": "F15", "type": "string", "size": 7, "nullable": true, "default": "1"}, {"name": "F16", "type": "bytes", "size": 7, "nullable": true, "default": "1"}, {"name": "F17", "type": "fixed", "size": 7, "nullable": true, "default": "1111111"}, {"name": "F18", "type": "uint8: 4", "nullable": true, "default": "0x01"}, {"name": "F19", "type": "uint16: 4", "nullable": true, "default": "0x0f"}, {"name": "F20", "type": "uint32: 8", "nullable": true, "default": "0xff"}, {"name": "F21", "type": "uint64: 16", "nullable": true, "default": "0xffff"}], "keys": [{"name": "<PERSON><PERSON><PERSON>", "node": "schema_datatype", "fields": ["F6"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"name": "hashcluster", "node": "schema_datatype", "fields": ["F6"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}, {"name": "localkey", "node": "schema_datatype", "fields": ["F6"], "index": {"type": "local"}, "constraints": {"unique": false}}]}]