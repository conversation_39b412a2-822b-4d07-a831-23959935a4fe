/*****************************************************************************
 Description  : Yang 支持label和node名称超过128字符变长存储
 Notes        : 当前仅支持label_name和property_name，尚未支持key_name
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2020/11/17
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

bool isNull;
unsigned int posValue;
int affectvalue;
int affectRows;
unsigned int len;
char *overlong_vertexlabel_schema = NULL;
char *overlong_graph_vertex_label_schema = NULL;
char *overlong_graph_edge_label_schema = NULL;

const char *normal_config_json = "{\"max_record_num\":1000, \"isFastReadUncommitted\":1}";

const char *g_overlong_vertexlabel_name = "vsysxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                          "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
const char *g_overlong_vertexlabel2_name = "vsys/"
                                           "rulexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                           "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
char g_overlong_pk_name[] = "id";
char g_overlong_sk_name[] = "vsys_sk";
const char *g_pro_id = "idxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
const char *g_pro_F0 = "F0xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
const char *g_pro_F1 = "F1xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
const char *g_pro_H7 = "H7xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";

class VariableLengthStorage : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void VariableLengthStorage::SetUp()
{
    printf("[INFO] VariableLengthStorage Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void VariableLengthStorage::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] VariableLengthStorage End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.vertexLabel.gmjson中label_name,property_name均设为150字节，同步创建vertexLabel,增删改查，全表扫描，删除vertexLabel
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_001)
{
    //创建
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    int i = 100;
    int v1_id = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_id, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F0Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F1Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    int32_t new_v1_id = 88;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, g_pro_id, GMC_DATATYPE_INT32, &new_v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex 只测open
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &new_v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(g_stmt);
    // delete vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &new_v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.vertexLabel.gmjson中label_name,property_name均设为150字节，异步创建vertexLabel,增删改，删除vertexLabel
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_002)
{
    //创建
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, overlong_vertexlabel_schema, normal_config_json, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    int i = 100;
    int v1_id = i;
    ret = GmcSetVertexProperty(g_stmt_async, g_pro_id, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F0Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F1Value = i;
    ret = GmcSetVertexProperty(g_stmt_async, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);

    // update vertex
    int32_t new_v1_id = 88;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, g_pro_id, GMC_DATATYPE_INT32, &new_v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    data = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);

    // delete vertex
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &new_v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}


// 005.vertexLabel.gmjson中label_name,property_name均设为150字节，调用GmcBatchAddDDL接口,批量建vertexLabel
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_005)
{
    // start
    int ret = 0;
    char *overlong_src_vertexlabel_schema = NULL;
    char *overlong_dst_vertexlabel_schema = NULL;
    const char *src_vertexlabel_name = "vsysxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *dst_vertexlabel_name = "vsys/"
                                       "rulexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    // read json
    readJanssonFile("schemaFile/OverLongSrcVertexLabel.gmjson", &overlong_src_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_src_vertexlabel_schema);
    readJanssonFile("schemaFile/OverLongDstVertexLabel.gmjson", &overlong_dst_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_dst_vertexlabel_schema);

    // batch create label
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, src_vertexlabel_name,
        overlong_src_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, dst_vertexlabel_name,
        overlong_dst_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);
    GmcBatchDestroy(batch);

    // free var
    free(overlong_src_vertexlabel_schema);
    free(overlong_dst_vertexlabel_schema);

    // drop label
    ret = GmcDropVertexLabel(g_stmt, src_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, dst_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 015.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcCreateVertexLabel接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_015)
{
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}


// 018.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcDropVertexLabel接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_018)
{
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}


// 020.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcCreateVertexLabelAsync接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_020)
{
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, overlong_vertexlabel_schema, normal_config_json, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(overlong_vertexlabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcOpenVertexLabelByName接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_021)
{
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcStoreVertexLabelToCache接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_022)
{
    //创建
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, overlong_vertexlabel_schema, normal_config_json, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcSetVertexProperty接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_023)
{
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    int i = 100;
    int v1_id = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_id, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F0Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F1Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcGetVertexPropertySizeByName接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_024)
{
    //创建
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    int i = 100;
    int v1_id = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_id, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F0Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F1Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcGetVertexPropertyByName接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_025)
{
    //创建
    readJanssonFile("schemaFile/OverLongVertexLabel.gmjson", &overlong_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_vertexlabel_schema);
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, overlong_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(overlong_vertexlabel_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_overlong_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    int i = 100;
    int v1_id = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_id, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F0Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F1Value = i;
    ret = GmcSetVertexProperty(g_stmt, g_pro_F1, GMC_DATATYPE_INT64, &F1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query vertex
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_overlong_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, g_pro_F0, GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_overlong_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 026.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcCreateEdgeLabel接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_026)
{
    // start
    int ret = 0;
    char *overlong_src_vertexlabel_schema = NULL;
    char *overlong_dst_vertexlabel_schema = NULL;
    char *overlong_edge_schema = NULL;
    const char *src_vertexlabel_name = "vsysxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *dst_vertexlabel_name = "vsys/"
                                       "rulexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *edgelabel_name = "vsys_"
                                 "rulexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                 "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    char src_pk_name[] = "id";
    char dst_pk_name[] = "vsys/id_id";
    const char *pro_id = "idxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                         "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *pro_vsys_id = "vsys/"
                              "idxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                              "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";

    // read json
    readJanssonFile("schemaFile/OverLongSrcVertexLabel.gmjson", &overlong_src_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_src_vertexlabel_schema);
    readJanssonFile("schemaFile/OverLongDstVertexLabel.gmjson", &overlong_dst_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_dst_vertexlabel_schema);
    readJanssonFile("schemaFile/OverLongEdgeLabel.gmjson", &overlong_edge_schema);
    ASSERT_NE((void *)NULL, overlong_edge_schema);

    // create label
    ret = GmcCreateVertexLabel(g_stmt, overlong_src_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, overlong_dst_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, overlong_edge_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    free(overlong_src_vertexlabel_schema);
    free(overlong_dst_vertexlabel_schema);
    free(overlong_edge_schema);

    // drop label
    ret = GmcDropEdgeLabel(g_stmt, edgelabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, src_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, dst_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}


// 028.vertexLabel.gmjson中,label_name,property_name设为150字节，调用GmcDropEdgeLabel接口
TEST_F(VariableLengthStorage, DDL_021_VariableLengthStorage_028)
{
    // start
    int ret = 0;
    char *overlong_src_vertexlabel_schema = NULL;
    char *overlong_dst_vertexlabel_schema = NULL;
    char *overlong_edge_schema = NULL;
    const char *src_vertexlabel_name = "vsysxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *dst_vertexlabel_name = "vsys/"
                                       "rulexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                       "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *edgelabel_name = "vsys_"
                                 "rulexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                 "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    char src_pk_name[] = "id";
    char dst_pk_name[] = "vsys/id_id";
    const char *pro_id = "idxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                         "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *pro_vsys_id = "vsys/"
                              "idxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                              "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";

    // read json
    readJanssonFile("schemaFile/OverLongSrcVertexLabel.gmjson", &overlong_src_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_src_vertexlabel_schema);
    readJanssonFile("schemaFile/OverLongDstVertexLabel.gmjson", &overlong_dst_vertexlabel_schema);
    ASSERT_NE((void *)NULL, overlong_dst_vertexlabel_schema);
    readJanssonFile("schemaFile/OverLongEdgeLabel.gmjson", &overlong_edge_schema);
    ASSERT_NE((void *)NULL, overlong_edge_schema);

    // create label
    ret = GmcCreateVertexLabel(g_stmt, overlong_src_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, overlong_dst_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, overlong_edge_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // free var
    free(overlong_src_vertexlabel_schema);
    free(overlong_dst_vertexlabel_schema);
    free(overlong_edge_schema);

    // drop label
    ret = GmcDropEdgeLabel(g_stmt, edgelabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, src_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, dst_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

