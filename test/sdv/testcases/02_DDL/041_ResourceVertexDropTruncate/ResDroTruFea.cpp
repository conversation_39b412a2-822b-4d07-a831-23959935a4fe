extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "resource_drop_truncate.h"

using namespace std;

class ResDroTruFea : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ResDroTruFea::SetUp()
{
    //权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //导入白名单
    const char *allow_list_file = "./allow_list/resource_allow_list.gmuser";
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath,
        allow_list_file, g_connServer);
    printf("[INFO]g_command=%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}
void ResDroTruFea::TearDown()
{
    AW_CHECK_LOG_END();

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 003.创建资源池和资源表对设置drop和不设置truncate权限删除表
TEST_F(ResDroTruFea, DDL_041_05_003)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_dropnotruc_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 004.创建资源池和资源表对不设置drop和不设置truncate权限删除表
TEST_F(ResDroTruFea, DDL_041_05_004)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_nodrnotruc_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 赋权删表权限后再次删除
    sysPolicyFile = "gmpolicy_file/resource_priv_drop_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(result);
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 005.创建资源池和资源表不对资源表设置truncate对象权限删除表
TEST_F(ResDroTruFea, DDL_041_05_005)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_dropnotruc_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 007.创建资源池和资源表不对资源表设置drop对象权限删除表
TEST_F(ResDroTruFea, DDL_041_05_007)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_trucnodrop_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 赋权删表权限后再次删除
    sysPolicyFile = "gmpolicy_file/resource_priv_drop_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(result);
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 001.创建资源池和资源表对设置truncate和设置drop权限清空表
TEST_F(ResDroTruFea, DDL_041_05_001)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_dropandtru_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 002.创建资源池和资源表对设置truncate和不设置drop权限清空表
TEST_F(ResDroTruFea, DDL_041_05_002)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_trucnodrop_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 赋权删表权限后再次删除
    sysPolicyFile = "gmpolicy_file/resource_priv_drop_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(result);
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 006.创建资源池和资源表对资源表设置truncate对象权限删除表
TEST_F(ResDroTruFea, DDL_041_05_006)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_dropandtru_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

// 008.创建资源池和资源表对资源表设置drop对象权限删除表
TEST_F(ResDroTruFea, DDL_041_05_008)
{
    const char *ResPoolExternalName = "ResPoolFeatureExtended";
    const char *ResPoolName = "ResPoolFeature";
    const char *ResPoolFeature =
        R"({
        "name" : "ResPoolFeature",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *ResPoolFeatureExternal =
        R"({
        "name" : "ResPoolFeatureExtended",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelName = "ResVtxDelTrucFI";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucFI",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":true},
                {"name":"F2", "type":"int32", "nullable":true},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    //赋予用户Resource的所有权限
    const char *sysPolicyFile = "gmpolicy_file/resource_sys_cbdu_policy.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    char *result = NULL;
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    sysPolicyFile = "gmpolicy_file/resource_priv_dropandtru_vtx.gmpolicy";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, RESOURCE_MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile,
        g_connServer);
    ret = ExecuteShellCommand(&result, g_command);
    printf("[INFO]:\ng_command=%s;\nresult=%s\n", g_command, result);
    EXPECT_EQ(GMERR_OK, ret);
    free(result);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    //创建资源表
    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);
    //清空资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    //创建资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeature);
    EXPECT_EQ(GMERR_OK, ret);
    //创建扩展资源池
    ret = GmcCreateResPool(g_stmt_sync, ResPoolFeatureExternal);
    EXPECT_EQ(GMERR_OK, ret);

    //绑定到扩展资源池
    ret = GmcBindExtResPool(g_stmt_sync, ResPoolName, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, ResPoolExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}
