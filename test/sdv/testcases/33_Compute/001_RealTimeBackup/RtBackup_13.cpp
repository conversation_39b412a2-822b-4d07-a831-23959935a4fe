/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: DB主备基础数据实时同步复制 - 可靠性
 * Author: lushiguang
 * Create: 2025-01-08
 */

#include "RtBackup.h"

int g_beginIndex = 0;
int g_endIndex = 1000;

class RtBackup13 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void StartEnvAndConn(char *masterCfg = NULL, char *slaveCfg = NULL)
{
    int ret;
    char config[3][200];

    // 主节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (masterCfg) {
        ret = ChangeGmserverCfg(masterCfg, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("sh $TEST_HOME/tools/start.sh -m");

    // 备节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (slaveCfg) {
        ret = ChangeGmserverCfg(slaveCfg, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("sh $TEST_HOME/tools/start.sh -s");

    // 配置主备
    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建连接 主节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_masterConn[i], &g_masterStmt[i], g_connServer);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 建连接 备节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_slaveConn[i], &g_slaveStmt[i], g_connServerSlave);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void RtBackup13::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");

    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    ret = ChangeGmserverCfg((char *)"enableClusterHash", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"pageSize", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"workerHungThreshold", (char *)"20,299,300");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i + 1);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < MAX_THREAD_COUNT; i++) {
        g_rollBackFlag[i] = false;
    }
    AW_CHECK_LOG_BEGIN();
}

void DisConnMaster()
{
    int ret;
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_masterConn[i], g_masterStmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void DisConnSlave()
{
    int ret;
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_slaveConn[i], g_slaveStmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void RtBackup13::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 001、设置单主，无备，主节点写数据  -- 业务操作成功 数据不同步
TEST_F(RtBackup13, Compute_001_001_13_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    StartEnvAndConn();
    // 备下线
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnMaster();
    DisConnSlave();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、主备重复上线、下线  -- 再次上线后，正常同步
TEST_F(RtBackup13, Compute_001_001_13_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    StartEnvAndConn();
    // 重复上线
    ret = GmcSlaveOnline(g_connServer, g_url);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSlaveOnline(g_connServer, g_url);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_slaveConn[i], g_slaveStmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("sh $TEST_HOME/tools/stop.sh -s");
    // 重复下线
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInitByInstance(g_instance2);

    // 再次上线
    system("sh $TEST_HOME/tools/start.sh -s");

    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建连接 备节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_slaveConn[i], &g_slaveStmt[i], g_connServerSlave);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、异步复制表、快速频繁操作  -- 正常同步
TEST_F(RtBackup13, Compute_001_001_13_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StartEnvAndConn();
    g_endIndex = 20;
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // replace
    ret = ReplaceVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主板update数据
    ret = UpdateVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主板delete数据
    ret = CommonDelete(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    int expectCount = g_endIndex / 2 - g_beginIndex;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckUpdateVarTb(g_masterStmt[0], g_tableName, g_endIndex / 2, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateVarTb(g_slaveStmt[0], g_tableName, g_endIndex / 2, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、同步复制模式，小事务同步过程，备板下线  -- 不影响主节点业务操作
TEST_F(RtBackup13, Compute_001_001_13_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 500;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn();
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 备板下线
    system("ps ux|grep gmserver_2|grep -v grep|awk '{print $2}'|xargs kill -9");
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnSlave();

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、异步复制模式，小事务同步过程，备板下线  -- 不影响主节点业务操作
TEST_F(RtBackup13, Compute_001_001_13_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 500;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn();
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 备板下线
    system("ps ux|grep gmserver_2|grep -v grep|awk '{print $2}'|xargs kill -9");
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnSlave();

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    DisConnMaster();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、同步复制模式，大事务同步过程，备板下线  -- 不影响主节点业务操作
TEST_F(RtBackup13, Compute_001_001_13_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 大事务
    g_endIndex = 5000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn();
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 备板下线
    system("ps ux|grep gmserver_2|grep -v grep|awk '{print $2}'|xargs kill -9");
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnSlave();

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、异步复制模式，大事务同步过程，备板下线  -- 不影响主节点业务操作
TEST_F(RtBackup13, Compute_001_001_13_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 大事务
    g_endIndex = 5000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn();
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 备板下线
    system("ps ux|grep gmserver_2|grep -v grep|awk '{print $2}'|xargs kill -9");
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnSlave();

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnMaster();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、主备dbFileSize配置差异，备<主，同步复制模式，备板满后，小事务提交  --
// 4、备板满后，同步失败，主节点写数据返回错误
TEST_F(RtBackup13, Compute_001_001_13_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=1048576", (char *)"dbFileSize=4096");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);

    int id = 0;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, g_rollBackFlag[id]);
    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、主备dbFileSize配置差异，备<主，异步复制模式，备板满后，小事务提交  -- 4、备板满后，不影响主节点写数据
TEST_F(RtBackup13, Compute_001_001_13_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=1048576", (char *)"dbFileSize=4096");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int id = 0;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, g_rollBackFlag[id]);
    ret = WaitReplicateFinish(5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 100;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    expectCount = 0;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、主备dbFileSize配置差异，备<主，同步复制模式，备板满后，大事务提交  --
// 4、备板满后，同步失败，主节点写数据返回错误
TEST_F(RtBackup13, Compute_001_001_13_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=1048576", (char *)"dbFileSize=4096");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);

    int id = 0;
    int lobTrxCount = 2000;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, g_rollBackFlag[id]);

    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、主备dbFileSize配置差异，备<主，异步复制模式，备板满后，大事务提交  -- 4、备板满后，不影响主节点写数据
TEST_F(RtBackup13, Compute_001_001_13_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=1048576", (char *)"dbFileSize=4096");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int id = 0;
    int lobTrxCount = 2000;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, g_rollBackFlag[id]);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = lobTrxCount;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    expectCount = 0;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、主备pageSize配置差异，备<主，写入数据，构造同步对象转变 -- 4、正常同步
TEST_F(RtBackup13, Compute_001_001_13_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 100;
    StartEnvAndConn((char *)"pageSize=32", (char *)"pageSize=16");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex18k(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、主备pageSize配置差异，备>主，写入数据，构造同步对象转变 -- 4、正常同步
TEST_F(RtBackup13, Compute_001_001_13_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 100;
    StartEnvAndConn((char *)"pageSize=16", (char *)"pageSize=32");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex18k(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、主备maxNormalTableNum配置差异，备<主，同步复制模式，构造备表数量满 -- 4、备板满后，同步失败，主节点建表返回错误
TEST_F(RtBackup13, Compute_001_001_13_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 100;
    StartEnvAndConn((char *)"maxNormalTableNum=1002", (char *)"maxNormalTableNum=1000");
    int ret;
    int maxTableCount = 1000;
    char tableName[50];
    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "lable_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/var_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    // 备验证
    uint32_t labelType = 0;
    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "lable_%d", i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "lable_%d", i);
        ret = GmcDropVertexLabel(g_masterStmt[0], tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、主备maxNormalTableNum配置差异，备<主，异步复制模式，构造备板满 -- 4、备板满后，同步失败，主节点建表返回错误
TEST_F(RtBackup13, Compute_001_001_13_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 100;
    StartEnvAndConn((char *)"maxNormalTableNum=1002", (char *)"maxNormalTableNum=1000");
    int ret;
    int maxTableCount = 1000;
    char tableName[50];
    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "lable_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/var_schema.gmjson", g_config1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    // 备验证
    uint32_t labelType = 0;
    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "lable_%d", i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "lable_%d", i);
        ret = GmcDropVertexLabel(g_masterStmt[0], tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、主备不同redo落盘方式，主redoFlushByTrx、备redoFlushByTime，写入数据 -- 4、正常同步
TEST_F(RtBackup13, Compute_001_001_13_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 2000;
    StartEnvAndConn((char *)"redoFlushByTrx=1", (char *)"redoFlushByTrx=0");
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    int ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、主备不同redo落盘方式，主redoFlushByTime、备redoFlushByTrx，写入数据 -- 4、正常同步
TEST_F(RtBackup13, Compute_001_001_13_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 2000;
    StartEnvAndConn((char *)"redoFlushByTrx=0", (char *)"redoFlushByTrx=1");
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    int ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckInsertVarTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、主备dbFileSize配置差异，备>主，同步复制模式，写满主板满，构造小事务，验证写满后数据是否同步到备板  --
// 4、主板满后，同步失败，备板无相关数据
TEST_F(RtBackup13, Compute_001_001_13_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=4096", (char *)"dbFileSize=1048576");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
    int id = 0;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, g_rollBackFlag[id]);

    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、主备dbFileSize配置差异，备>主，异步复制模式，，写满主板满，构造小事务，验证写满后数据是否同步到备板  --
// 4、主板满后，同步失败，备板无相关数据
TEST_F(RtBackup13, Compute_001_001_13_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=4096", (char *)"dbFileSize=1048576");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int id = 0;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, g_rollBackFlag[id]);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + 100, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、主备dbFileSize配置差异，备>主，同步复制模式，写满主板满，构造大事务，验证写满后数据是否同步到备板  --
// 4、主板满后，同步失败，备板无相关数据
TEST_F(RtBackup13, Compute_001_001_13_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=4096", (char *)"dbFileSize=1048576");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);

    int id = 0;
    int lobTrxCount = 2000;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, g_rollBackFlag[id]);
    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、主备dbFileSize配置差异，备>主，异步复制模式，，写满主板满，构造大事务，验证写满后数据是否同步到备板  --
// 4、主板满后，同步失败，备板无相关数据
TEST_F(RtBackup13, Compute_001_001_13_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 50000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    StartEnvAndConn((char *)"dbFileSize=4096", (char *)"dbFileSize=1048576");
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int id = 0;
    int lobTrxCount = 2000;
    ret = DoReplaceVarTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, g_rollBackFlag[id]);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + lobTrxCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DisConnMaster();
    DisConnSlave();

    AW_FUN_Log(LOG_STEP, "test end.");
}

