/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: DB主备同步DFX - REPLICATION 通用预置条件
 * Author: liwenhai
 * Create: 2025-03-20
 */

#include "tool.h"

class backup_dfx : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void backup_dfx::SetUp()
{
    g_isFinish = false;

    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");

    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    StartEnvAndConn();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建连接
    ret = TestGmcConnectLocator(&g_conn[MASTER_INDEX], &g_stmt[MASTER_INDEX], g_connServer);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_conn[SLAVE_INDEX], &g_stmt[SLAVE_INDEX], g_connServerSlave);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void backup_dfx::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    // 断开连接
    ret = testGmcDisconnect(g_conn[MASTER_INDEX], g_stmt[MASTER_INDEX]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn[SLAVE_INDEX], g_stmt[SLAVE_INDEX]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 002.enableReplication=1，REPLICATION查询
TEST_F(backup_dfx, Compute_011_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ReplicationInfo replication;
    replication.GetInfo("", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, strcmp(replication.enable, "Yes"));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.启主服务、备服务，REPLICATION查询
TEST_F(backup_dfx, Compute_011_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ReplicationInfo replication;
    replication.GetInfo("", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, strcmp(replication.slaveURL, g_url));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.全量复制时，REPLICATION查询
TEST_F(backup_dfx, Compute_011_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmt[MASTER_INDEX], "vertexLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_label_1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt[MASTER_INDEX], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 备服务下线
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    ret = testGmcDisconnect(g_conn[SLAVE_INDEX], g_stmt[SLAVE_INDEX]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnInitByInstance(2);
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -s");
    CleanSlavePstFile();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt[MASTER_INDEX], "vertexLabel", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 100000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i + 1;
        ret = GmcSetVertexProperty(g_stmt[MASTER_INDEX], "id", GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt[MASTER_INDEX], "F1", GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt[MASTER_INDEX]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 另启线程持续REPLICATION视图查询，直到查到预期结果或批备完成
    pthread_t thrGetView;
    g_isFinish = false;
    ret  = pthread_create(&thrGetView, NULL, &ThrDoGetView, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 备服务重新上线
    system("sh $TEST_HOME/tools/start.sh -s");
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_conn[SLAVE_INDEX], &g_stmt[SLAVE_INDEX], g_connServerSlave);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isFinish = true;
    ret = pthread_join(thrGetView, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt[MASTER_INDEX], "vertexLabel", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i + 1;
        ret = GetVertexProperty(g_stmt[MASTER_INDEX], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt[SLAVE_INDEX], "vertexLabel", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i + 1;
        ret = GetVertexProperty(g_stmt[SLAVE_INDEX], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmt[MASTER_INDEX], "vertexLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.全量复制20次，REPLICATION查询
TEST_F(backup_dfx, Compute_011_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmt[MASTER_INDEX], "vertexLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_label_1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt[MASTER_INDEX], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    int repeatTimes = 20;
    int insertCnt = 10000;
    Clock timer[repeatTimes];
    ReplicationInfo replication[repeatTimes];
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    ReplicationInfo firstReplication;
    firstReplication.GetInfo("", true);
    for (int i = 0; i < repeatTimes; i++) {
        // 备服务下线
        ret = testGmcDisconnect(g_conn[SLAVE_INDEX], g_stmt[SLAVE_INDEX]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcUnInitByInstance(2);
        ret = GmcSlaveOffline(g_connServer);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/stop.sh -s");
        CleanSlavePstFile();
        // 写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt[MASTER_INDEX], "vertexLabel", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < insertCnt; j++) {
            uint32_t idVal = i * insertCnt + j;
            uint32_t f1Val = i * insertCnt + j + 1;
            ret = GmcSetVertexProperty(g_stmt[MASTER_INDEX], "id", GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt[MASTER_INDEX], "F1", GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(g_stmt[MASTER_INDEX]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 备服务重新上线
        timer[i].Record();
        system("sh $TEST_HOME/tools/start.sh -s");
        ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 30);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 20);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGmcConnectLocator(&g_conn[1], &g_stmt[1], g_connServerSlave);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        timer[i].Record();

        replication[i].GetInfo("", true);
        AW_MACRO_EXPECT_EQ_INT(i + 2, replication[i].fullReplicationCount);
        AW_MACRO_EXPECT_GE_INT(timer[i].interval, replication[i].fullReplicationDuration);
        AW_MACRO_EXPECT_GE_INT(replication[i].fullReplicationDuration * 1000, replication[i].maxDuration);
        AW_MACRO_EXPECT_GE_INT(replication[i].maxDuration, replication[i].avgDuration);
        AW_MACRO_EXPECT_GE_INT(replication[i].avgDuration, replication[i].minDuration);
    }

    // 主服务检查数据
    ret = testGmcPrepareStmtByLabelName(g_stmt[MASTER_INDEX], "vertexLabel", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < repeatTimes * insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i + 1;
        ret = GetVertexProperty(g_stmt[MASTER_INDEX], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt[SLAVE_INDEX], "vertexLabel", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < repeatTimes * insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i + 1;
        ret = GetVertexProperty(g_stmt[SLAVE_INDEX], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmt[MASTER_INDEX], "vertexLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.REPLICATION查询，主备倒换，再REPLICATION查询
TEST_F(backup_dfx, Compute_011_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ReplicationInfo replication;
    replication.GetInfo("", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, strcmp(replication.slaveURL, g_url));

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    replication.GetInfo("", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, strcmp(replication.slaveURL, g_url));

    AW_FUN_Log(LOG_STEP, "test end.");
}
