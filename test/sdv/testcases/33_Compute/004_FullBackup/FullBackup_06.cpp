/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 主备复制-支持批量数据复制DML - 06、可靠性
 * Author: lushiguang
 * Create: 2025-02-05
 */

#include "FullBackup.h"

class FullBackup06 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void StartEnvAndConn()
{
    int ret;
    // 主节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -m");
    // 备节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -s");

    // 配置主备
    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnMaster();
    ConnSlave();
}

void FullBackup06::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    g_endIndex = 200;
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"pageSize", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"workerHungThreshold", (char *)"20,299,300");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i + 1);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 并发可能有抢锁操作，错误加白名单
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_LOCK_NOT_AVAILABLE);
    AW_CHECK_LOG_BEGIN();
}

void FullBackup06::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    // 断开连接
    DisConnMaster();
    DisConnSlave();
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 001、批备过程中，备板故障后下线，拉起重新备份  -- 主备数据一致
TEST_F(FullBackup06, Compute_004_001_06_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StartEnvAndConn();
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 备板故障后下线
    system("sh $TEST_HOME/tools/stop.sh -s");
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInitByInstance(g_instance2);

    // 清空备板持久化文件
    CleanSlavePstFile();
    system("sh $TEST_HOME/tools/start.sh -s");

    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主板业务操作
    int nsCount = 20;
    char nameSpace[50] = {0};
    const char *userName = "user";
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcDropNamespace(g_masterStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ConnSlave();
    // 备板验证已同步
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcUseNamespace(g_slaveStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    }
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcUseNamespace(g_masterStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    }
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_OBJECT, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、反复多次批备  -- 主备数据一致
TEST_F(FullBackup06, Compute_004_001_06_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StartEnvAndConn();
    int ret;
    DoOperateInMaster(2);
    for (int i = 0; i < 5; i++) {
        OffAndStartSlave();
        // 配置主备，不等待备份完成
        ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ConnSlave();
    }

    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、备板配置差异，批备过程，备板资源不足  -- 主板业务操作失败
TEST_F(FullBackup06, Compute_004_001_06_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StartEnvAndConn();
    int ret;
    DoOperateInMaster(2);
    DisConnSlave();
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -s");
    GmcUnInitByInstance(g_instance2);
    // 清空备板持久化文件
    CleanSlavePstFile();
    ret = ChangeGmserverCfg((char *)"dbFileSize", (char *)"4096");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -s");
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    // 创建
    int tableCount = 200;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "empty_lable3_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/var_schema.gmjson", g_config2);
        if (ret == GMERR_INSUFFICIENT_RESOURCES) {
            break;
        }
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
    ConnSlave();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INSUFFICIENT_RESOURCES);
    AW_FUN_Log(LOG_STEP, "test end.");
}

