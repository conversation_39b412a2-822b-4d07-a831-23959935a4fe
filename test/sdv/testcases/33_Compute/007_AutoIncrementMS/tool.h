/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: RtBackup.h
 * Author: lushiguang
 * Create: 2025-01-08
 */
#ifndef TOOL_H
#define TOOL_H

#include <iostream>
#include <fstream>
#include "gtest/gtest.h"
#include "t_light.h"
#include "test_ha.h"

#define MAX_INSTANCE_COUNT 5
#define CONN_COUNT 5

char g_serverLocation[MAX_INSTANCE_COUNT][200];

GmcConnT *g_connListSync[MAX_INSTANCE_COUNT];
GmcStmtT *g_stmtListSync[MAX_INSTANCE_COUNT];

int g_instance0 = 0;
int g_instance1 = 1;
int g_instance2 = 2;
GmcTxConfigT g_trxConfig;
const char *g_url = "tcp:host=127.0.0.1,port=2234";

bool g_ignoreConflict = false;
bool g_ignoreTimeout = false;

#ifdef __cplusplus
extern "C" {
#endif

char g_dbFilePath[MAX_INSTANCE_COUNT][1024] = {0};

const char *g_config = (char *)R"(
{
    "max_record_count":100000000,
    "writers":"abc",
    "auto_increment":1,
    "isFastReadUncommitted":0,
    "replication":2
})";

const char *g_configAI100 = (char *)R"(
{
    "max_record_count":100000000,
    "writers":"abc",
    "auto_increment":100,
    "isFastReadUncommitted":0,
    "replication":2
})";

const char *g_configAINearToMaxUINT32 = (char *)R"(
{
    "max_record_count":100000000,
    "writers":"abc",
    "auto_increment":4294966796,
    "isFastReadUncommitted":0,
    "replication":2
})";

void AddReplicationCfg()
{
    (void)GtExecSystemCmd("sed -i '/enableReplication/d' %s", g_sysGMDBCfg);
    (void)GtExecSystemCmd("echo 'enableReplication=0' >> %s", g_sysGMDBCfg);
    (void)ChangeGmserverCfg((char *)"enableReplication", (char *)"1");
}


int HaConfig(const char *masterLocation, const char *slaveLocation, const char *url, int waitTimeout,
             bool isBatch = false)
{
    int ret = GmcSetDbRole(masterLocation, GMC_DB_ROLE_MASTER, url);
    RETURN_IFERR(ret);
    ret = GmcSetDbRole(slaveLocation, GMC_DB_ROLE_SLAVE, url);
    RETURN_IFERR(ret);
    // 等待备可访问
    ret = GmcWait(slaveLocation, GMC_DB_STATUS_ACCESSIBLE, waitTimeout);
    RETURN_IFERR(ret);
    ret = GmcSlaveOnline(masterLocation, url);
    RETURN_IFERR(ret);
    if (!isBatch) {
        // 等待主可同步备份
        ret = GmcWait(masterLocation, GMC_DB_STATUS_BACKUP_ENABLED, waitTimeout);
    }
    return ret;
}

int TestGmcConnectLocator(GmcConnT **connOut, GmcStmtT **stmt = NULL, const char *serverLocator = NULL,
    int syncMode = 0, bool needEpoll = 1, EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL,
    const void *chanRingLen = NULL)
{
    int ret;

    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, serverLocator);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testMallocConnOptions failed, ret = %d.\n", ret);
        return ret;
    }

    ret = testGmcConnect(connOut, stmt, syncMode, needEpoll, epollReg, connName, chanRingLen, connOption);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testGmcConnect failed, ret = %d.\n", ret);
        testFreeConnOptions(connOption);
        return ret;
    }
    testFreeConnOptions(connOption);
    return ret;
}

void CleanSlavePstFile()
{
    (void)Rmdir(g_dbFilePath[1]);
    int ret = mkdir(g_dbFilePath[1], S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void OffAndStartSlave()
{
    int ret;
    ret = testGmcDisconnect(g_connListSync[1], g_stmtListSync[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInitByInstance(2);
    system("sh $TEST_HOME/tools/stop.sh -s");
    // 清空备板持久化文件
    CleanSlavePstFile();
    system("sh $TEST_HOME/tools/start.sh -s");
}

int HaConfigOnlySlave(const char *masterLocation, const char *slaveLocation, const char *url, int waitTimeout)
{
    int ret = GmcSetDbRole(slaveLocation, GMC_DB_ROLE_SLAVE, url);
    RETURN_IFERR(ret);
    // 等待备可访问
    ret = GmcWait(slaveLocation, GMC_DB_STATUS_ACCESSIBLE, waitTimeout);
    RETURN_IFERR(ret);
    ret = GmcSlaveOnline(masterLocation, url);
    RETURN_IFERR(ret);
    return ret;
}

void ReOnlineSlave(int waitTimeout = 15)
{
    int ret;
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    OffAndStartSlave();
    // 配置备服务
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, waitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 预置数据
int SetVertexProperty(GmcStmtT *stmt, const char *fieldName, uint32_t Val)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, fieldName, GMC_DATATYPE_UINT32, &Val, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    return ret;
}

int SetVertexPropertyFail(GmcStmtT *stmt, const char *fieldName, uint32_t Val, int expRet)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, fieldName, GMC_DATATYPE_UINT32, &Val, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    return ret;
}

int GetVertexProperty(GmcStmtT *stmt, uint32_t idVal, const char *fieldName, uint32_t expVal)
{
    int ret = 0;
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    unsigned int scanCount = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        uint32_t fieldVal = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, fieldName, &fieldVal, sizeof(uint32_t), &isNull);
        RETURN_IFERR(ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(expVal, fieldVal);
        scanCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, scanCount);
    return ret;
}

int GetVertexProperty64PK(GmcStmtT *stmt, uint64_t idVal, const char *fieldName, uint32_t expVal)
{
    int ret = 0;
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &idVal, sizeof(uint64_t));
   RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    unsigned int scanCount = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        uint32_t fieldVal = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, fieldName, &fieldVal, sizeof(uint32_t), &isNull);
        RETURN_IFERR(ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(expVal, fieldVal);
        scanCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, scanCount);
    return ret;
}

int GetVertexProperty64F(GmcStmtT *stmt, uint32_t idVal, const char *fieldName, uint64_t expVal)
{
    int ret = 0;
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    unsigned int scanCount = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        uint64_t fieldVal = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, fieldName, &fieldVal, sizeof(uint64_t), &isNull);
        RETURN_IFERR(ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(expVal, fieldVal);
        scanCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, scanCount);
    return ret;
}

int GetUnKwonVertexProperty(GmcStmtT *stmt, uint32_t idVal, const char *fieldName, uint32_t *getVal)
{
    int ret = 0;
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    unsigned int scanCount = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, fieldName, getVal, sizeof(uint32_t), &isNull);
        RETURN_IFERR(ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        scanCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, scanCount);
    return ret;
}

int GetVertexPropertyNoData(GmcStmtT *stmt, uint32_t idVal)
{
    int ret = 0;
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    unsigned int scanCount = 0;
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    return ret;
}

void *ThrDoDMLSameLable(void *args)
{
    int ret = 0;
    int thrIndex = *((int *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestGmcConnectLocator(&conn, &stmt, g_connServer);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = thrIndex * insertCnt; i < (thrIndex + 1) * insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(stmt, "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThrDoDMLDiffLable(void *args)
{
    int ret = 0;
    int thrIndex = *((int *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestGmcConnectLocator(&conn, &stmt, g_connServer);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    string labelName = "vertexLable" + to_string(thrIndex);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName.c_str(), GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(stmt, "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// CPlus
using namespace std;

void ReplaceSomeWhere(const char *modePath, string *changes, string examplePath = "")
{
    examplePath = (examplePath.compare("") == 0) ? modePath : examplePath;
    char *modeJson = NULL;
    readJanssonFile(modePath, &modeJson);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, modeJson);
    string modeStr = modeJson;
    string exampleStr = "";
    while (modeStr.find("#(") != string::npos) {
        exampleStr += modeStr.substr(0, modeStr.find("#("));
        modeStr = modeStr.substr(modeStr.find("#(") + strlen("#("));
        exampleStr += *(changes + stoi(modeStr));
        modeStr = modeStr.substr(modeStr.find(")#") + strlen(")#"));
    }
    exampleStr += modeStr;
    free(modeJson);
    string cmd = "echo \"\" > " + examplePath;
    system(cmd.c_str());
    ofstream fout;
    fout.open(examplePath);
    fout << exampleStr << endl;
    fout.close();
}

#ifdef __cplusplus
}
#endif

#endif /* RTBACKUP_H */

