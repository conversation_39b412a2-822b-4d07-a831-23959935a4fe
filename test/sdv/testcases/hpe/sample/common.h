/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 * File Name: common.h
 * Description: common definition
 * Please keep new function in its corresponding position.
 * Create: 2022-5-23
 */

#ifndef COMMON_H
#define COMMON_H

#include <stdio.h>
#include <unistd.h>
#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>
#include <time.h>
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SERVER_LOCATOR "channel:ctl_channel"

int32_t CreateSyncConnAndStmt(GmcConnT **connSync, GmcStmtT **stmtSync)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnOptionsT *connOptions = NULL;
    int32_t ret = GmcConnOptionsCreate(&connOptions);
    if (ret != GMERR_OK) {
        printf("GmcConnOptionsCreate failed! ret = %d\n", ret);
        return ret;
    }

    ret = GmcConnOptionsSetServerLocator(connOptions, SERVER_LOCATOR);
    if (ret != GMERR_OK) {
        printf("GmcConnOptionsSetServerLocator failed! ret=%d\n", ret);
        GmcConnOptionsDestroy(connOptions);
        return ret;
    }

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcConnOptionsDestroy(connOptions);
    if (ret != GMERR_OK) {
        printf("GmcConnect failed! ret=%d\n", ret);
        return ret;
    }
    ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        printf("GmcAllocStmt failed! ret=%d\n", ret);
        GmcDisconnect(conn);
        return ret;
    }
    *connSync = conn;
    *stmtSync = stmt;
    return ret;
}

void DestroyConnectionAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    if (stmt != NULL) {
        GmcFreeStmt(stmt);
    }
    if (conn != NULL) {
        GmcDisconnect(conn);
    }
    return;
}

typedef enum {
    VERTEX_SCHEMA = 0,
    KV_SCHEMA = 1
}SchemaType;

bool inited = false;
#define LISTENED_CHANNEL_NAME "test_channel"

bool HasSchemaPrepared(GmcStmtT *stmt, SchemaType schemaType, const char *schemaName)
{
    switch (schemaType) {
        case VERTEX_SCHEMA:
            return testGmcPrepareStmtByLabelName(stmt, schemaName, GMC_OPERATION_SCAN) != GMERR_UNDEFINED_TABLE;
        case KV_SCHEMA:
            return GmcKvPrepareStmtByLabelName(stmt, schemaName) != GMERR_UNDEFINED_TABLE;
        default:
            return false;
    }
    return false;
}

#define MAX_WAIT_TIME 60
void WaitServerReady()
{
    int32_t waitTime = 0;
    while (waitTime++ < MAX_WAIT_TIME) {
        sleep(1);
    }
}

int32_t WaitSchemaPrepared(GmcStmtT *stmt, const char *schema, SchemaType schemaType, uint32_t timeout)
{
    printf("Wait schema prepared. timeout=%d\n", timeout);
    if (HasSchemaPrepared(stmt, schemaType, schema)) {
        return GMERR_OK;
    }
    uint32_t waitTime = 0;
    while (waitTime++ < timeout) {
        sleep(1);
        if (HasSchemaPrepared(stmt, schemaType, schema)) {
            return GMERR_OK;
        }
    }
    return GMERR_NO_DATA;
}

#ifdef __cplusplus
}
#endif

#endif /* COMMON_H */
