/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "op.h"

class cliServerPriv : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        // 持久化模式下，清理持久化数据文件，避免其他用例修改配置项，影响当前用例启动
#ifdef FEATURE_PERSISTENCE
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
#endif
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" ");
        // 以共进程方式起服务
        char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
        int ret = GmsServerMain(3, cmdString);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testEnvInit(-1, false);
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void cliServerPriv::SetUp()
{
    g_conn = NULL;
    g_stmt = NULL;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    const char *allowFile = "./allowList/allow_list.gmuser";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowFile);  // 导入白名单
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, MAX_CMD_SIZE);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void cliServerPriv::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *allowFile = "./allowList/allow_list.gmuser";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowFile);  // 删除白名单
    ret = executeCommand(g_command, "remove db user. success: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_CHECK_LOG_END();
}

// 001.共进程方式启动服务, 未授予系统权限, DDL操作失败(vertex&resource), 权限授予以及追加后, DDL操作成功
TEST_F(cliServerPriv, SEC_022_cliServerPriv_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    ret = TestCliServerCreateLabel(g_stmt, num);  // 建表失败
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file01 = "./sysFile/sysPolicyCreate.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);  // 权限追加
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel(g_stmt, num);  // 建表成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "Tx0");  // 删表
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file06 = "./sysFile/sysPolicyDrop.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file06);  // 权限追加
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcDropVertexLabel(g_stmt, "Tx0");  // 删表
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.未授予系统权限, DDL&DML操作失败, 权限授予以及追加后, DDL&DML操作成功
TEST_F(cliServerPriv, SEC_022_cliServerPriv_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *nameSpace = (const char *)"use";
    const char *userName1 = (const char *)"abc";
    const char *file01 = "./sysFile/sysPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcCreateNamespace(g_stmt, nameSpace, userName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file02 = "./sysFile/sysPolicyUseNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestCliServerCreateLabel01(g_stmt, num);  // 建表成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F0Value = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F1Value = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file03 = "./sysFile/sysPolicyInsertAny.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = GmcTruncateVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    const char *file04 = "./sysFile/sysPolicyAllAny.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "Txx0");  // 删表
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file0x = "./sysFile/sysPolicyDroNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file0x);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 004.未授予对象权限, DML操作失败, 权限授予以及追加后, DML操作成功
TEST_F(cliServerPriv, SEC_022_cliServerPriv_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *file01 = "./sysFile/sysPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel01(g_stmt, num);  // 建表成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F0Value = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F1Value = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file03 = "./objFile/objPolicyInsert.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    const char *file04 = "./objFile/objPolicyAll.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, "Txx0");  // 删表
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.未授予对象权限, DDL操作失败(nsp对象权限), 权限授予以及追加后, DDL&DML操作成功
TEST_F(cliServerPriv, SEC_022_cliServerPriv_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *nameSpace = (const char *)"nspx";
    const char *userName1 = (const char *)"abc";
    const char *file01 = "./sysFile/sysPolicyCreateDropInNsp.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcCreateNamespace(g_stmt, nameSpace, userName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file02 = "./objFile/objPolicyUseNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestCliServerCreateLabel01(g_stmt, num);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file03 = "./objFile/objPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel01(g_stmt, num);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcTruncateVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file04 = "./objFile/objPolicyGetAndTrunNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file05 = "./objFile/objPolicyDroNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file05);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcDropVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file05);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.未授予对象权限, DML操作失败(nsp对象权限), 权限授予以及追加后, DDL&DML操作成功
TEST_F(cliServerPriv, SEC_022_cliServerPriv_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *nameSpace = (const char *)"nspx";
    const char *userName1 = (const char *)"abc";
    const char *file01 = "./sysFile/sysPolicyCreateDropInNsp.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcCreateNamespace(g_stmt, nameSpace, userName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *file02 = "./objFile/objPolicyUseNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *file03 = "./objFile/objPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel01(g_stmt, num);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *file04 = "./objFile/objPolicyGetAndTrunNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F0Value = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F1Value = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    const char *file033 = "./objFile/objPolicyInsertNsp.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file033);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    const char *file05 = "./objFile/objPolicyAllNsp.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file05);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *file06 = "./objFile/objPolicyDroNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file06);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = GmcDropVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file04);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file033);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file05);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file06);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *threadInsert(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int thrId = *(int *)args;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "Txx0";
    int count = 10;
    for (int i = thrId * count; i < thrId * count + count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.授予权限后，另起新的业务操作线程，验证新线程是否继承权限
TEST_F(cliServerPriv, SEC_022_cliServerPriv_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *file01 = "./sysFile/sysPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel01(g_stmt, num);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *file02 = "./objFile/objPolicyInsert.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);

    int thrNum = 5;
    int index[thrNum] = {0};
    pthread_t thr_arr_01[thrNum];
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, threadInsert, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *threadReplace(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int thrId = *(int *)args;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "Txx0";
    int count = 10;
    for (int i = thrId * count; i < thrId * count + count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.授予权限后，起新的业务操作线程, 再追加其他权限, 另起新的业务操作线程，验证新线程是否继承权限
TEST_F(cliServerPriv, SEC_022_cliServerPriv_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *file01 = "./sysFile/sysPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel01(g_stmt, num);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *file02 = "./objFile/objPolicyInsert.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);

    int thrNum = 5;
    int index[thrNum] = {0};
    pthread_t thr_arr_01[thrNum];
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, threadInsert, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    const char *file03 = "./objFile/objPolicyAll.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);

    pthread_t thr_arr_02[thrNum];
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_02[i], NULL, threadReplace, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr_02[i], NULL);
    }

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file03);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.共进程启动, 并测试独立进程的鉴权(是否受到影响)
TEST_F(cliServerPriv, SEC_022_cliServerPriv_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int num = 0;
    const char *file01 = "./sysFile/sysPolicyCreNs.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file01);
    ret = executeCommand(g_command, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = TestCliServerCreateLabel01(g_stmt, num);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn01;
    GmcStmtT *stmt01;
    char killPid[20] = {0};
    ret = testGmcConnect(&conn01, &stmt01);
    EXPECT_EQ(GMERR_OK, ret);
    const char *file02 = "./objFile/objPolicyInsert.gmpolicy";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s", g_toolPath, file02);
    ret = executeCommand(g_command, "object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, 1024);
    ret = testGmcPrepareStmtByLabelName(stmt01, "Txx0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F0Value = 1;
    ret = GmcSetVertexProperty(stmt01, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F1Value = 1;
    ret = GmcSetVertexProperty(stmt01, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn01, stmt01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Txx0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 2;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    const char *file022 = "./objFile/objPolicyInsert.gmpolicy";
    (void)snprintf(g_command, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, file022);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, "Txx0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
