/*****************************************************************************
 Description  : Vertex对象权限check
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/07/08
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "ObjPrivsTest.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class SEC_004_ObjPrivsVertex : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        //权限校验改为强制模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        readJanssonFile("schemaFile/config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        //恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_004_ObjPrivsVertex::SetUp()
{
    printf("[INFO] ObjPrivsVertex Start.\n");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void SEC_004_ObjPrivsVertex::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    const char *allow_list_file = "schemaFile/allow_list/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    GmcDetachAllShmSeg();
    printf("[INFO] ObjPrivsVertex End.\n");
}

// 001.Vertex对象权限Insert校验，同步插入，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_001)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        ret = testGmcGetLastError(VertexPrivError);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002. Vertex对象权限Insert校验，异步插入，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_002)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);
    // AsyncUserDataT data = {0};
    // ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // before grant
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        data.lastError = VertexPrivError;
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003. Vertex对象权限Insert校验，通过json插入，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_003)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/T39_all_type.vertexdata", GMERR_INSUFFICIENT_PRIVILEGE);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/T39_all_type.vertexdata", GMERR_OK);

    // scan vertex
    // scan_verex(g_stmt, g_normal_vertexlabel_name, count); 暂时使用以下方式查询
    scan_verex_test(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004. Vertex对象权限Insert校验，批量插入，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_004)
{
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
    // EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // insert
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005. Vertex对象权限update校验，同步更新，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_005)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        ret = testGmcGetLastError(VertexPrivError);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcResetStmt(g_stmt);

    // grant update
    const char *obj_policy_file2 = "schemaFile/policy/objUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count, true);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006. Vertex对象权限update校验，异步更新，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_006)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // before grant
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt_async, NewVal);
        data = {0};
        data.lastError = VertexPrivError;
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant update
    const char *obj_policy_file2 = "schemaFile/policy/objUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt_async, NewVal);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count, true);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007. Vertex对象权限update校验，同步条件更新，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_007)
{
    int ret = 0;
    int NewVal = 2;
    int count = 100;
    int expectAffectRows = 1;
    const char *cond = "F7<100";
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    set_VertexProperty(g_stmt, NewVal);
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, cnt);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    GmcResetStmt(g_stmt);

    // grant update
    const char *obj_policy_file2 = "schemaFile/policy/objUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = count;
    set_VertexProperty(g_stmt, NewVal);
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, NewVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.Vertex对象权限update校验，异步条件更新，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_008)
{
    int ret = 0;
    int count = 100;
    int NewVal = 2;
    const char *cond = "F7<100";
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    set_VertexProperty(g_stmt_async, NewVal);
    data = {0};
    data.lastError = VertexPrivError;
    ret = GmcSetFilter(g_stmt_async, cond);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateByCondRequestCtx;
    updateByCondRequestCtx.updateCb = update_vertex_by_cond_callback;
    updateByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &updateByCondRequestCtx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    EXPECT_EQ(expectAffectRows, data.affectRows);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, cnt);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);

    // grant update
    const char *obj_policy_file2 = "schemaFile/policy/objUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = count;
    set_VertexProperty(g_stmt_async, NewVal);
    data = {0};
    ret = GmcSetFilter(g_stmt_async, cond);
    EXPECT_EQ(GMERR_OK, ret);
    // GmcAsyncRequestDoneContextT updateByCondRequestCtx;
    updateByCondRequestCtx.updateCb = update_vertex_by_cond_callback;
    updateByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &updateByCondRequestCtx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(expectAffectRows, data.affectRows);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, NewVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009. Vertex对象权限update校验，批量更新，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_009)
{
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
    // EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant update
    const char *obj_policy_file2 = "schemaFile/policy/objUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // update vertex
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count, true);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010. Vertex对象权限delete校验，同步删除，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_010)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        ret = testGmcGetLastError(VertexPrivError);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011. Vertex对象权限delete校验，异步删除，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_011)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        data.lastError = VertexPrivError;
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012. Vertex对象权限delete校验，同步条件删除，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_012)
{
    int ret = 0;
    int NewVal = 2;
    int count = 100;
    int expectAffectRows = 1;
    const char *cond = "F7<100";
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    set_VertexProperty(g_stmt, NewVal);
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = count;
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013. Vertex对象权限delete校验，异步条件删除，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_013)
{
    int ret = 0;
    int count = 100;
    int NewVal = 2;
    const char *cond = "F7<100";
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    data = {0};
    data.lastError = VertexPrivError;
    ret = GmcSetFilter(g_stmt_async, cond);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteByCondRequestCtx;
    deleteByCondRequestCtx.deleteCb = delete_vertex_by_cond_callback;
    deleteByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteByCondRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    EXPECT_EQ(expectAffectRows, data.affectRows);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = count;
    data = {0};
    ret = GmcSetFilter(g_stmt_async, cond);
    EXPECT_EQ(GMERR_OK, ret);
    // GmcAsyncRequestDoneContextT deleteByCondRequestCtx;
    deleteByCondRequestCtx.deleteCb = delete_vertex_by_cond_callback;
    deleteByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteByCondRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(expectAffectRows, data.affectRows);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}


// 015. Vertex对象权限delete校验，批量删除顶点，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_015)
{
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
    // EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // delete vertex
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 016.创建多个vertexLabel，check只有授权对象权限的veterxlabel的DML操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_016)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant T39_all_type all obj privs, T39_1 no obj privs
    const char *obj_policy_file1 = "schemaFile/policy/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // check T39_all_type obj privs
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // check T39_1 obj privs
    void *vertexLabel1 = NULL;
    const char *labelName = "T39_1";
    char *labelJson = NULL;
    readJanssonFile("schemaFile/VertexLabel1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.创建多张表，分多次导入逐个赋予用户这几张表的对象权限，check权限
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_017)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);
    // grant T39_all_type all obj privs
    const char *obj_policy_file1 = "schemaFile/policy/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // check T39_all_type obj privs
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // check T39_1 obj privs
    void *vertexLabel1 = NULL;
    const char *labelName = "T39_1";
    char *labelJson = NULL;
    readJanssonFile("schemaFile/VertexLabel1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // grant T39_1 all obj privs
    const char *obj_policy_file2 = "schemaFile/policy/allObjVertexT39_1.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, labelName, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.Vertex对象权限replace校验，同步replace，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_018)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetLastError(VertexPrivError);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant replace
    const char *obj_policy_file2 = "schemaFile/policy/objReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019. Vertex对象权限replace校验，异步replace，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_019)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        data.lastError = VertexPrivError;
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant replace
    const char *obj_policy_file2 = "schemaFile/policy/objReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 020. Vertex对象权限replace校验，批量replace，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_020)
{
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
    // EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant replace
    const char *obj_policy_file2 = "schemaFile/policy/objReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.Vertex对象权限merge校验，同步merge，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_021)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetLastError(VertexPrivError);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant merge
    const char *obj_policy_file2 = "schemaFile/policy/objMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022. Vertex对象权限replace校验，异步merge，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_022)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        data.lastError = VertexPrivError;
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant merge
    const char *obj_policy_file2 = "schemaFile/policy/objMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, data.status == GMERR_UNIQUE_VIOLATION || data.status == GMERR_OK);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1005000");
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023. Vertex对象权限merge校验，批量merge，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_023)
{
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
    // EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant merge
    const char *obj_policy_file2 = "schemaFile/policy/objMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // merge
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024. Vertex对象权限select校验，全表扫描，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_024)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    ret = testGmcGetLastError("Wrong client state object. Current stmt type is 0, need stmt action type is 1.");
    EXPECT_EQ(GMERR_OK, ret);

    // grant select
    const char *obj_policy_file2 = "schemaFile/policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025. Vertex对象权限select校验，条件扫描，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_025)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    const char *condStr = "F7<100";
    ret = GmcSetFilter(g_stmt, condStr);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    ret = GmcSetOutputFormat(g_stmt, NULL);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");

    // grant select
    const char *obj_policy_file2 = "schemaFile/policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 026. Vertex对象权限select校验，主键读，权限导入前返回错误码
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_026)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    for (uint32_t i = 0; i < count; i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 027.Vertex对象权限select校验，获取变更记录数，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_027)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    unsigned int CountNum = 1;
    uint64_t opCount[CountNum];
    const char *batch[1] = {};
    batch[0] = g_normal_vertexlabel_name;
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_REPLACE, opCount, CountNum);
    EXPECT_EQ(GMERR_OK, ret);  // 适配DTS2021122808195 CATA_VERTEX_LABEL_CHECK_INFO字段名称修改

    // grant select
    const char *obj_policy_file2 = "schemaFile/policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // GmcGetOperStatsCnt
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_REPLACE, opCount, CountNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, opCount[0]);  // enableDmlOperStat参数默认开启
    memset(opCount, 0, sizeof(uint64_t));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 029. Vertex对象权限select校验，获取记录数，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_029)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // before grant
    uint64_t result;
    ret = GmcGetVertexCount(g_stmt, g_normal_vertexlabel_name, NULL, &result);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = testGmcGetLastError(VertexPrivError);
    EXPECT_EQ(GMERR_OK, ret);

    // grant select
    const char *obj_policy_file2 = "schemaFile/policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // get record count
    ret = GmcGetVertexCount(g_stmt, g_normal_vertexlabel_name, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, result);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 030. Vertex对象权限混合校验，授权所有对象权限之后进行相关操作
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_030)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant T39_all_type all obj privs
    const char *obj_policy_file1 = "schemaFile/policy/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 031. Vertex对象权限select校验，主键读，权限导入后操作OK
TEST_F(SEC_004_ObjPrivsVertex, SEC_004_ObjPrivsVertex_031)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant select
    const char *obj_policy_file2 = "schemaFile/policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //授权后再开表，否则用的是授权前缓存的对象权限
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    //直连读
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
        EXPECT_EQ(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}
