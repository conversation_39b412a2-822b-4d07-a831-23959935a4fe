/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SenSDatalogFun.cpp
 * Description: sensitive能力增强(datalog功能测试)
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2024-08-10
 */
#include "SenSDatalog.h"

class SenSDatalogFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SenSDatalogFun::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = 0;
    uint32_t enableLogFold = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal %d", g_toolPath, enableLogFold);
    ret = executeCommand(g_command, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t datalogRunLinkLog = 64;
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName datalogRunLinkLog -cfgVal %d", g_toolPath, datalogRunLinkLog);
    ret = executeCommand(g_command, "after setting config:", "config current value: 64");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SenSDatalogFun::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t enableLogFold = 1;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal %d", g_toolPath, enableLogFold);
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t datalogRunLinkLog = 0;
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName datalogRunLinkLog -cfgVal %d", g_toolPath, datalogRunLinkLog);
    ret = executeCommand(g_command, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 024.普通表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput[] = "inp1";
    char tableMid[] = "mid1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_024";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_024.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 025.notify表设置sensitive_fields，插入数据，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_025";

    AW_FUN_Log(LOG_STEP, "创建订阅连接, 创建订阅关系, 卸载Datalog");
    TestLoadDatalog("./datalogFile/SenSDatalogFun_025.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_subConn, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(
        g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "取消订阅关系, 断开订阅连接, 卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 026.可更新表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableMid[] = "mid1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_026";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_026.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 027.部分可更新表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableMid[] = "mid1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_027";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_027.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 028.fast_insert表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_028";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_028.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 029.timeout表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_029";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_029.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t dValue1 = currentTime + (int64_t)(1 * 60 * 60 * 1000);
    int32_t value1 = 666;
    int32_t value2 = 8888;
    int32_t dtlReservedCountValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "filedy1", GMC_DATATYPE_INT64, &dValue1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "filedy2", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "filedy3", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCountValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r -E 'record:1,0,[0-9]+,666,8888' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 030.transient(tuple)表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_030";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_030.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 031.transient(finish)表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_031";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_031.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 5; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 032.transient(field)表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableMid[] = "mid1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_032";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_032.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 0; i <= 0; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10000,0,0' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 033.msgnotify表设置sensitive_fields，插入数据，gmsysview
// record查看记录，查看链路日志，查看CATA_MSG_NOTIFY_TABLE_INFO视图
TEST_F(SenSDatalogFun, SEC_025_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_033";

    system("rm -rf /root/msgNotifyRunLogSensitive.txt");
    TestLoadDatalog("./datalogFile/SenSDatalogFun_033.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "filedy2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "filedy3");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/msgNotifyRunLogSensitive.txt");
    ret = executeCommand(g_command, "filedy1: 10001, filedy2: 1, filedy3: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_MSG_NOTIFY_TABLE_INFO -f MSG_NOTIFY_TABLE_NAME=out1");
    ret = executeCommand(g_command, "FIELD_NAME: null");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 034.tbm表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志，查看CATA_TBM_TABLE_INFO视图
TEST_F(SenSDatalogFun, SEC_025_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_034";

    system("rm -rf /root/TbmRunLogSensitive.txt");
    TestLoadDatalog("./datalogFile/SenSDatalogFun_034.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "filedy2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "filedy3");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/TbmRunLogSensitive.txt");
    ret = executeCommand(g_command, "filedy1 = 10001", "filedy2 = 1", "filedy3 = 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_TBM_TABLE_INFO -f TBM_TABLE_NAME=out1");
    ret = executeCommand(g_command, "FIELD_NAME: null");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 035.外部表(schema中)设置sensitive_fields字段含true，false，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_035";

    char *schemaFile = NULL;
    char const *labelConfig = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    readJanssonFile("schemaFile/out1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);
    TestLoadDatalog("./datalogFile/SenSDatalogFun_035.so");

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    ret = GmcDropVertexLabel(g_stmt, tableOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(soName);
}

// 036.固定型表设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput[] = "inp1";
    char tableMid[] = "rsc1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_036";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_036.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "filedy3");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName);
}

// 037.pub型资源表(中间表)设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableRsc1[] = "rsc1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_037";

    AW_FUN_Log(LOG_STEP, "创建订阅连接, 创建订阅关系, 卸载Datalog");
    TestLoadDatalog("./datalogFile/SenSDatalogFun_037.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_subConn, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSub, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, tableRsc1, g_connServer);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "filedy4");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "取消订阅关系, 断开订阅连接, 卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 038.64个字段，设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput[] = "inp1";
    char tableMid[] = "mid1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_038";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_038.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert64Field(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    char *result1 = NULL;
    ret = TestViewData(g_command, &result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result1));
    if (strstr(g_expectJson, result1) == NULL) {
        system(g_command);
    }
    free(result1);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    char *result2 = NULL;
    ret = TestViewData(g_command, &result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result2));
    if (strstr(g_expectJson, result2) == NULL) {
        system(g_command);
    }
    free(result2);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    char *result3 = NULL;
    ret = TestViewData(g_command, &result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result3));
    if (strstr(g_expectJson, result3) == NULL) {
        system(g_command);
    }
    free(result3);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,0,10001[,1]{124}' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 039.32个索引，设置sensitive_fields，插入数据，gmsysview record查看记录，查看CATA_VERTEX_LABEL_INFO视图，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput[] = "inp1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_039";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_039.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert32IndexField(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    char *result1 = NULL;
    ret = TestViewData(g_command, &result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result1));
    if (strstr(g_expectJson, result1) == NULL) {
        system(g_command);
    }
    free(result1);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    char *result2 = NULL;
    ret = TestViewData(g_command, &result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result2));
    if (strstr(g_expectJson, result2) == NULL) {
        system(g_command);
    }
    free(result2);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,0,10001[,1]{62}' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 040.block 0模式，原含有sensitive_fields的表进行热补丁升级，新增update表带sensitive_fields，
// 插入数据，gmsysview record查看记录，热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableInput2[] = "inp1_1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_040";
    char patchSoName[] = "datalogFile/SenSDatalogFun_040_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_040.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 1; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput2, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_040_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 041.block 0模式，原含有sensitive_fields的表进行热补丁升级，新增update_partial表带sensitive_fields，
// 插入数据，gmsysview record查看记录，热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableInput2[] = "inp1_1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_041";
    char patchSoName[] = "datalogFile/SenSDatalogFun_041_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_041.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 1; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput2, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_041_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 042.block 0模式，原含有sensitive_fields的表进行热补丁升级，修改fun实现，插入数据，gmsysview record查看记录，
// 热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_042";
    char patchSoName[] = "datalogFile/SenSDatalogFun_042_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_042.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_042_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 043.block 0模式，原含有sensitive_fields的表进行热补丁升级，修改规则，插入数据，gmsysview record查看记录，热补丁降级，
// 插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_043";
    char patchSoName[] = "datalogFile/SenSDatalogFun_043_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_043.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_043_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 044.block 1模式，原含有sensitive_fields的表进行热补丁升级，新增update表带sensitive_fields，插入数据，
// gmsysview record查看记录，热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableInput2[] = "inp1_1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_044";
    char patchSoName[] = "datalogFile/SenSDatalogFun_044_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_044.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 1; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput2, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_044_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 045.block 1模式，原含有sensitive_fields的表进行热补丁升级，新增update_partial表带sensitive_fields，插入数据，
// gmsysview record查看记录，热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableInput2[] = "inp1_1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_045";
    char patchSoName[] = "datalogFile/SenSDatalogFun_045_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_045.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 1; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput2, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_045_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 046.block 1模式，原含有sensitive_fields的表进行热补丁升级，修改fun实现，插入数据，gmsysview record查看记录，
// 热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_046";
    char patchSoName[] = "datalogFile/SenSDatalogFun_046_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_046.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_046_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 047.block 1模式，原含有sensitive_fields的表进行热补丁升级，修改规则，插入数据，gmsysview record查看记录，
// 热补丁降级，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_047";
    char patchSoName[] = "datalogFile/SenSDatalogFun_047_patchV2.so";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_047.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_047_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut1, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 048.block 1模式，原含有sensitive_fields的表进行热补丁升级(新增规则tbm表)，插入数据，gmsysview
// record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput1[] = "inp1";
    char tableInput2[] = "inp1_1";
    char tableOut1[] = "out1";
    char soName[] = "SenSDatalogFun_048";
    char patchSoName[] = "datalogFile/SenSDatalogFun_048_patchV2.so";

    system("rm -rf /root/TbmRunLogSensitive.txt");
    TestLoadDatalog("./datalogFile/SenSDatalogFun_048.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    for (int32_t i = 2; i <= 2; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = singleRecordInsert(g_stmt, tableInput2, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    char rollbackSoName[] = "datalogFile/SenSDatalogFun_048_rollbackV2.so";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    for (int32_t i = 3; i <= 3; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput1, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "dtlReservedCount", "upgradeVersion", "filedy2", "filedy3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "dtlReservedCount");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,[0-1],1000[1-9],1,1' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 049.datalog覆盖全类型字段设置sensitive_fields
TEST_F(SenSDatalogFun, SEC_025_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char tableInput[] = "inp1";
    char tableMid[] = "mid1";
    char tableOut[] = "out1";
    char soName[] = "SenSDatalogFun_049";

    TestLoadDatalog("./datalogFile/SenSDatalogFun_049.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsertAllType(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "check sensitive_fields");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    char *result1 = NULL;
    ret = TestViewData(g_command, &result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result1));
    if (strstr(g_expectJson, result1) == NULL) {
        system(g_command);
    }
    free(result1);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableMid, g_connServer,
        g_testNameSpace);
    char *result2 = NULL;
    ret = TestViewData(g_command, &result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result2));
    if (strstr(g_expectJson, result2) == NULL) {
        system(g_command);
    }
    free(result2);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOut, g_connServer,
        g_testNameSpace);
    char *result3 = NULL;
    ret = TestViewData(g_command, &result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result3));
    if (strstr(g_expectJson, result3) == NULL) {
        system(g_command);
    }
    free(result3);

    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -E -r 'record:1,0,1,1,10001,1,1,1,1,1,string' %s", g_serverlogPath);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    TestUninstallDatalog(soName);
}

// 050.pub型资源表(输出表)设置sensitive_fields，插入数据，gmsysview record查看记录，查看链路日志
TEST_F(SenSDatalogFun, SEC_025_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableInput[] = "inp1";
    char tableRsc1[] = "rsc1";
    char soName[] = "SenSDatalogFun_050";

    AW_FUN_Log(LOG_STEP, "创建订阅连接, 创建订阅关系, 卸载Datalog");
    TestLoadDatalog("./datalogFile/SenSDatalogFun_050.so");
    int ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_subConn, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSub, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t value = i;
        ret = singleRecordInsert(g_stmt, tableInput, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, tableRsc1, g_connServer);
    ret = executeCommand(g_command, "filedy1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    ret = executeCommand(g_command, "filedy4");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    system(g_command);
    AW_FUN_Log(LOG_STEP, "check datalogRunLinkLog");
#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'record:1,0,10001,1,1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "record:1,0,10001,1,1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "取消订阅关系, 断开订阅连接, 卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}
