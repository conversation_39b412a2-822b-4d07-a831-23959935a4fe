/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tbm_udf
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2024-08-14
 */

#include "gm_udf.h"
#include "stdio.h"

#pragma pack(1)
typedef struct Tbm {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} Tbm;
#pragma pack(0)

const char *g_logName = "/root/TbmRunLogSensitive.txt";

int32_t dtl_ext_func_init()
{
    FILE* fp = fopen(g_logName, "w");
    if (fp == NULL) {
        return 0;
    }
    (void)fprintf(fp, "dtl_ext_func_init.\n");
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit()
{
    FILE* fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    (void)fprintf(fp, "dtl_ext_func_uninit.\n");
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_out1(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    (void)fprintf(fp, "op = %d, filedy1 = %d.\n", op, ((Tbm *)tuple)->a);
    (void)fprintf(fp, "op = %d, filedy2 = %d.\n", op, ((Tbm *)tuple)->b);
    (void)fprintf(fp, "op = %d, filedy3 = %d.\n", op, ((Tbm *)tuple)->c);
    (void)fprintf(fp, "op = %d, dtlReservedCount = %d.\n", op, ((Tbm *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}
