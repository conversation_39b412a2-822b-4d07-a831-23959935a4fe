/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :不保留内存复制表时需要复制表权限功能测试
 Author       : g60053317
 Modification :
 Date         : 2024/12/27
**************************************************************************** */

#include "NoRsMCopyTable.h"
int affectRows;

class NoRsMCopyTable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void NoRsMCopyTable::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh");                             // 修改配置，先停服务
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");            // 恢复配置
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");  // 设置用户强制鉴权模式
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = system("gmrule -c import_allowlist -f ./policy/NoResMemory/dup_label.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void NoRsMCopyTable::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");  // 恢复配置
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void NoRsMCopyTable::SetUp()
{
    // 建连
    int ret = 0;
    // 设置用户强制鉴权模式后需要 导入白名单 才能建连
    char g_command[1024];
    const char *allow_list_file = "./policy/NoResMemory/sub_allow_list.gmuser";
    snprintf(g_command, 1024, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file, g_connServer);
    AW_FUN_Log(LOG_INFO, "%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INSUFFICIENT_PRIVILEGE);
}

void NoRsMCopyTable::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
}

/* ****************************************************************************
001、INSERT 插入对象的权限
步骤：
1、创建原始表，配置 INSERT 权限
2、创建复制表，校验权限是否同步
3、复制表insert一条数据
4、修改复制表的对象权限为 UPDATE，并update一条数据，insert一条数据
预期结果：
1、原始表创建成功，权限导入成功
2、复制表创建成功，权限与原始表相同
3、复制表成功insert一条数据
4、复制表修改权限成功，正常update一条数据，insert一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_001)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 INSERT 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限，检查原始表权限为 INSERT");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限，检查复制表权限为 INSERT");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表可以成功插入数据, 1-5条数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表修改权限为update");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_update.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表权限修改为Update后 update一条数据成功");
    ret = TestUpdateVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "[info] 复制表撤销 insert权限, 只剩下update权限 ");
    ret = system("gmrule -c revoke_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表撤销 insert权限 insert一条数据失败");
    TestInsertValueOne(g_stmt_sync, g_labelName2);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
002、UPDATE 更新对象的权限（有数据）
步骤：
1、创建原始表，配置UPDATE权限
2、创建复制表，校验权限是否同步
3、修改复制表的权限为（UPDATE、INSERT ），并insert一条数据
4、复制表update一条数据，delete一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、复制表修改权限成功，正常insert一条数据
4、复制表成功update一条数据，校验affectRows为1条数据，delete一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_002)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 UPDATE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_update.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 UPDATE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 UPDATE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP,
        "[info] 修改复制表的权限为 (UPDATE、INSERT), 并insert一条数据");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表可以成功插入数据, 1-5条数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    AW_FUN_Log(LOG_STEP, "[info] 复制表撤销 INSERT权限, 只剩下update权限 ");
    ret = system("gmrule -c revoke_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表权限修改为Update后 update一条数据成功");
    ret = TestUpdateVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表delete一条数据失败");
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
003、UPDATE 更新对象的权限（没数据）
步骤：
1、创建原始表，配置UPDATE权限
2、创建复制表，校验权限是否同步
3、update一条数据
4、修改复制表的权限为（UPDATE、INSERT ），并insert一条数据
5、复制表update一条数据，delete一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、update一条数据不会报错，校验affectRows为0条数据
4、复制表修改权限成功，正常insert一条数据
5、复制表成功update一条数据，校验affectRows为1条数据，delete一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_003)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 UPDATE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_update.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 UPDATE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 UPDATE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: update一条数据不会报错,校验affectRows为0条数据");
    ret = TestUpdateVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    AW_FUN_Log(
        LOG_STEP, "[info] 修改复制表的权限为 (INSERT), 并insert一条数据");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表成功update一条数据,校验affectRows为1条数据,delete一条数据失败");
    ret = TestUpdateVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表delete一条数据失败");
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
004、DELETE 删除对象的权限（有数据）
步骤：
1、创建原始表，配置 DELETE 权限
2、创建复制表，校验权限是否同步，
4、修改复制表的权限为（INSERT、DELETE） ，并insert两条数据
5、复制表delete一条数据，merge一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
4、复制表修改权限成功，正常insert两条数据
5、复制表成功delete一条数据，校验affectRows为1条数据，merge一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_004)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 DELETE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_delete.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 DELETE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "DELETE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 DELETE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "DELETE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP,
        "[info] 修改复制表的权限为 (DELETE、INSERT), 并insert一条数据");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,DELETE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表可以成功插入数据, 1-5条数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    AW_FUN_Log(LOG_STEP, "[info] 复制表撤销 INSERT权限, 只剩下delete权限 ");
    ret = system("gmrule -c revoke_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表成功delete一条数据,校验affectRows为1条数据,");
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "[info] test point: merge一条数据失败");
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
005、DELETE 删除对象的权限（没数据）
步骤：
1、创建原始表，配置 DELETE 权限
2、创建复制表，校验权限是否同步，
3、delete一条数据
4、修改复制表的权限为（INSERT、DELETE） ，并insert数据
5、复制表delete一条数据，merge一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、delete一条数据不会报错，校验affectRows为0条数据
4、复制表修改权限成功，正常insert数据
5、复制表成功delete一条数据，校验affectRows为1条数据，merge一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_005)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 DELETE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_delete.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 DELETE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "DELETE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 DELETE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "DELETE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: delete一条不存在的数据不会报错,校验affectRows为0条数据");
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    AW_FUN_Log(
        LOG_STEP, "[info] 修改复制表的权限为 (INSERT), 并insert一条数据");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,DELETE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表成功delete一条数据,校验affectRows为1条数据");
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "[info] test point: merge一条数据失败");
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
006、MERGE 合并对象的权限
步骤：
1、创建原始表，配置 MERGE 权限
2、创建复制表，校验权限是否同步
3、复制表merge一条数据
4、修改复制表的权限为 INSERT ，并insert一条数据，merge一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、复制表成功merge一条数据
4、复制表修改权限成功，正常insert一条数据，merge一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_006)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 MERGE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_merge.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 MERGE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "MERGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 MERGE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "MERGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表merge一条数据,校验affectRows为1条数据");
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(
        LOG_STEP, "[info] 修改复制表的权限为 (INSERT), 并insert一条数据");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,MERGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    TestInsertVertex(g_stmt_sync, g_labelName2, 2, 5);

    AW_FUN_Log(LOG_STEP, "[info] 复制表撤销 MERGE权限, 只剩下INSERT权限 ");
    ret = system("gmrule -c revoke_policy -f ./policy/NoResMemory/DUP2/dup_label_object_merge.gmpolicy");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 复制表修改权限成功,正常insert一条数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 6, 9);

    AW_FUN_Log(LOG_STEP, "[info] test point: merge一条数据失败");
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
007、REPLACE 替换对象的权限
步骤：
1、创建原始表，配置 REPLACE 权限
2、创建复制表，校验权限是否同步
3、复制表replace一条数据
4、修改复制表的权限为 INSERT ，并insert一条数据，replace一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、复制表成功replace一条数据
4、复制表修改权限成功，正常insert一条数据，replace一条数据失败
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_007)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 REPLACE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_replace.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 REPLACE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "REPLACE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 REPLACE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "REPLACE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表REPLACE一条数据,校验affectRows为1条数据");
    ret = TestReplaceVertex(g_stmt_sync, g_labelName2, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(
        LOG_STEP, "[info] 修改复制表的权限为 (INSERT), 并insert一条数据");  // 如果不撤销权限在修改权限，会追加权限
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT,REPLACE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    TestInsertVertex(g_stmt_sync, g_labelName2, 2, 5);

    AW_FUN_Log(LOG_STEP, "[info] 复制表撤销 REPLACE权限, 只剩下INSERT权限 ");
    ret = system("gmrule -c revoke_policy -f ./policy/NoResMemory/DUP2/dup_label_object_replace.gmpolicy");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 复制表修改权限成功,正常insert一条数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 6, 9);

    AW_FUN_Log(LOG_STEP, "[info] test point: REPLACE一条数据失败");
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
008、SELECT 查询对象的权限
步骤：
1、创建原始表，配置 SELECT权限
2、创建复制表，校验权限是否同步
3、select查询复制表数据
4、修改复制表的权限为（SELECT、INSERT ），并insert一条数据，delete一条数据
5、select查询复制表数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、select查询复制表数据为空
4、复制表修改权限成功，正常insert一条数据，delete一条数据失败
5、select查询复制表数据成功
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_008)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 (SELECT)权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_select.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 SELECT");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "SELECT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 SELECT");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "SELECT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] select查询复制表数据为0");
    ret = TestSelectVertex(g_stmt_sync, g_labelName2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 修改复制表的权限为 SELECT、INSERT,正常insert一条数据,delete一条数据失败");
    system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "SELECT,INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据ok
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 9);

    // delete数据失败
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // select数据ok
    ret = TestSelectVertex(g_stmt_sync, g_labelName2, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
009、包含所有 INSERT、UPDATE、DELETE、MERGE、REPLACE、SELECT 、INVOKE 对象的权限
步骤：
1、创建原始表，配置 （INSERT、UPDATE、DELETE、MERGE、REPLACE、SELECT、INVOKE）权限
2、创建复制表，校验权限是否同步
3、对复制表进行insert、update、delete、merge、replace、select数据1条数据
4、修改复制表的权限为（DELETE、MERGE、REPLACE、SELECT），并insert、update一条数据
预期结果：
1、原始表创建成功，权限导入成功；
2、复制表创建成功，权限与原始表相同；
3、对复制表进行insert、update、delete、merge、replace、select数据1条数据成功
4、复制表修改权限成功，insert、update一条数据失败，delete、merge、replace、select成功
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_009)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 (INSERT、UPDATE、DELETE、MERGE、REPLACE、SELECT)权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_all.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表权限为 INSERT、UPDATE、DELETE、MERGE、REPLACE、SELECT");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "SELECT,INSERT,UPDATE,DELETE,REPLACE,MERGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(
        LOG_STEP, "[info] test point: 复制表查询权限, 检查复制表权限为 SELECT,INSERT,UPDATE,DELETE,REPLACE,MERGE");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "SELECT,INSERT,UPDATE,DELETE,REPLACE,MERGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 对复制表进行insert、update、delete、merge、replace、select数据1条数据");
    // 插入数据ok
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);
    // update数据ok
    ret = TestUpdateVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    // delete数据ok
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    // merge数据ok
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    // replace数据ok
    ret = TestReplaceVertex(g_stmt_sync, g_labelName2, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, affectRows);
    // select数据ok
    ret = TestSelectVertex(g_stmt_sync, g_labelName2, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 复制表撤销 INSERT update权限, 只剩下DELETE、MERGE、REPLACE、SELECT权限 ");
    ret = system("gmrule -c revoke_policy -f ./policy/NoResMemory/DUP2/dup_label_object_update_insert.gmpolicy");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "SELECT,DELETE,REPLACE,MERGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据失败
    TestInsertValueOne(g_stmt_sync, g_labelName2);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // update数据失败
    ret = TestUpdateVertex(g_stmt_sync, g_labelName2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // delete数据ok
    ret = TestDeleteVertex(g_stmt_sync, g_labelName2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    // merge数据ok
    ret = TestMergeVertex(g_stmt_sync, g_labelName2, 2, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
010、INVOKE  对象的权限。
步骤：
1、创建原始表，配置 INVOKE 权限，但不生效，insert一条数据
2、创建复制表，校验权限是否同步
3、修改复制表的权限为 insert，并insert一条数据
预期结果：
1、原始表创建成功，权限导入成功，但不生效；insert一条数据失败
2、复制表创建成功，权限与原始表相同，没有INVOKE 权限不生效，insert一条数据失败
3、复制表修改权限成功，正常insert一条数据
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_010)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表，配置 INVOKE 权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP1/dup_label_object_invoke.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询原始表权限, 检查原始表INVOKE权限不生效,");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "INVOKE", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 原始表insert一条数据失败");
    TestInsertValueOne(g_stmt_sync, g_labelName);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表查询权限, 没有INVOKE 权限不生效");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INVOKE", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 复制表insert一条数据失败");
    TestInsertValueOne(g_stmt_sync, g_labelName2);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    AW_FUN_Log(LOG_STEP, "[info] 修改复制表的权限为 insert");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 正常insert数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}

/* ****************************************************************************
011、原始表没权限，创建复制表
步骤：
1、创建原始表，不配置权限
2、创建复制表，校验权限是否同步
3、复制表insert一条数据
4、修改复制表的权限为INSERT，insert一条数据
预期结果：
1、原始表创建成功，没有对象权限
2、复制表创建成功，没有对象权限
3、复制表 insert一条数据失败
4、复制表修改权限成功，正常insert一条数据
**************************************************************************** */
TEST_F(NoRsMCopyTable, NoRsMCopyTable_015_011)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 导入系统权限");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/dup_label_system.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 创建原始表");
    char *schemaParth = (char *)"./schema_file/DUP1.gmjson";
    ret = TestCreateLabel(g_stmt_sync, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 创建原始表，不配置权限,没有对象权限");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP1.txt");
    ret = CheckLabelNamePolicy("DUP1.txt", g_labelName, "", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 创建子表复制表");
    const char *testConfigJson2 = R"({"max_record_count":10,"max_record_num_check":false})";
    ret = GmcDuplicateVertexLabelWithName(g_stmt_sync, g_labelName, g_labelName2, testConfigJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test point: 复制表创建成功，没有对象权限");
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "", true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 复制表insert一条数据失败");
    TestInsertValueOne(g_stmt_sync, g_labelName2);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    AW_FUN_Log(LOG_STEP, "[info] 修改复制表的权限为 insert");
    ret = system("gmrule -c import_policy -f ./policy/NoResMemory/DUP2/dup_label_object_insert.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$PRIVILEGE_ROLE_STAT > DUP2.txt");
    ret = CheckLabelNamePolicy("DUP2.txt", g_labelName2, "INSERT");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 正常insert数据");
    TestInsertVertex(g_stmt_sync, g_labelName2, 0, 5);

    // 结束清理表
    // test point: 删除原始表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
    // test point: 删除子表
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // test point: 获取子表失败
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(g_stmt_sync, g_labelName2, GMC_OPERATION_INSERT));
}
