#include "GmcImpFlashBin.h"

void *threadRaiseFunc(void *args)
{
    uint32_t *singo = (uint32_t *)args;
    uint32_t randNum = 0;
    TestRangeRandNum(&randNum, 10, 100);
    usleep(randNum * 1000);
    AW_FUN_Log(LOG_INFO, "thread raise start, will raise %d, randNum is %d.", *singo, randNum);
    raise(*singo);
    return NULL;
}

int main(int argc, char **argv)
{
    AW_FUN_Log(LOG_INFO, "CliImpExist begin .");

    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    int32_t ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *kvName = "Warmboot019Kv";
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    // 2对应信号SIGINT
    uint32_t raiseSigno = 2;
    pthread_t thrRaise;
    ret = pthread_create(&thrRaise, NULL, threadRaiseFunc, (void *)&raiseSigno);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcImportTables(stmt, &importPara);

    pthread_join(thrRaise, NULL);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    close_epoll_thread();
    testEnvClean();
    AW_FUN_Log(LOG_INFO, "CliImpExist end .");
}
