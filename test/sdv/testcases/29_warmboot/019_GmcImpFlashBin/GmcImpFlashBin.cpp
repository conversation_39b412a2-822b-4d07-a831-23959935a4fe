#include "GmcImpFlashBin.h"

class GmcImpFlashBin : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmcImpFlashBin::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void GmcImpFlashBin::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void GmcImpFlashBin::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void GmcImpFlashBin::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    int ret = 0;
    GtExecSystemCmd("ls -la %s", g_exportDir);
    if (g_isRmExpFile) {
        GtExecSystemCmd("rm -rf %s", g_exportDir);
    }
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 001 GmcImportTables接口参数都传入正常值，正常调用
TEST_F(GmcImpFlashBin, warmboot_019_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

class GmcImpPara : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmcImpPara::SetUpTestCase()
{
    system("sh getBInData.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void GmcImpPara::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void GmcImpPara::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void GmcImpPara::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 002 stmt参数传入NULL，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir);
    ret = GmcImportTables(NULL, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcImportTables(g_stmtSync, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 importDirPath参数传入NULL，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = NULL;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 importDirPath参数传入非法字符
TEST_F(GmcImpPara, warmboot_019_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = "@#!#$$%zzhond中";
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 importDirPath参数长度超过256不包含结尾符，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "g_exportDir : %s", g_exportDir);
    (void)snprintf(g_exportDir2, 256, "%s", g_exportDir);
    for (int i = strlen(g_exportDir2); i < 256; i++) {
        g_exportDir2[i] = 'a';
    }
    g_exportDir2[256] = '\0';
    AW_FUN_Log(LOG_STEP, "g_exportDir2 path is %s, strlen(g_exportDir2) = %d.", g_exportDir2, strlen(g_exportDir2));
    ret = GtExecSystemCmd("mkdir -p %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cp -rf %s/* %s", g_exportDir, g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("ls -la %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir2;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir2);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 importDirPath参数长度256包含结尾符，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "g_exportDir : %s", g_exportDir);
    const char *binName = "Warmboot019HeapVertex public Warmboot019HeapVertex 0.bin_data";
    (void)snprintf(g_exportDir2, 255, "%s", g_exportDir);
    for (int i = strlen(g_exportDir2); i < 255 - strlen(binName) - 1; i++) {
        g_exportDir2[i] = 'a';
    }
    g_exportDir2[255 - strlen(binName) - 1] = '\0';
    AW_FUN_Log(LOG_STEP, "g_exportDir2 path is %s, strlen(g_exportDir2) = %d.", g_exportDir2, strlen(g_exportDir2));
    ret = GtExecSystemCmd("mkdir -p %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cp -rf %s/* %s", g_exportDir, g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("ls -la %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir2;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir2);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 importDirPath路径超过10层，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "g_exportDir : %s", g_exportDir);
    ret = GtExecSystemCmd("mkdir -p %s", g_dirEle);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cp -rf %s/* %s", g_exportDir, g_dirEle);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("ls -la %s", g_dirEle);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_dirEle;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_dirEle);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_dirEle);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 importDirPath路径10层，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "g_exportDir : %s", g_exportDir);
    ret = GtExecSystemCmd("mkdir -p %s", g_dirTen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cp -rf %s/* %s", g_exportDir, g_dirTen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("ls -la %s", g_dirTen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_dirTen;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_dirTen);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_dirTen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 importDirPath下文件命名不符合规范，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "g_exportDir : %s", g_exportDir);
    const char *binName = "Warmboot019HeapVertex public Warmboot019HeapVertex 0.bin_data";
    (void)snprintf(g_exportDir2, 255, "%s", g_exportDir);
    for (int i = strlen(g_exportDir2); i < 255 - strlen(binName) - 1; i++) {
        g_exportDir2[i] = 'a';
    }
    g_exportDir2[255 - strlen(binName) - 1] = '\0';
    AW_FUN_Log(LOG_STEP, "g_exportDir2 path is %s, strlen(g_exportDir2) = %d.", g_exportDir2, strlen(g_exportDir2));
    ret = GtExecSystemCmd("mkdir -p %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cp -rf %s/* %s", g_exportDir, g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cd %s;mv '%s' abc.txt -f;cd -;", g_exportDir2, binName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("ls -la %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir2;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir2);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 importDirPath传 "/"，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "g_exportDir : %s", g_exportDir);
    ret = GtExecSystemCmd("cp -rf %s/* /", g_exportDir);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("ls -la /*.bin_data");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_dirRoot;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_dirRoot);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    GtExecSystemCmd("rm -rf /*.bin_data");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 importDirPath传相对路径"./"，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GtExecSystemCmd("ls -la ./binData", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = "./binData";
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 labelsName参数传入NULL，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = NULL;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 labelsName参数传入非法字符，其他参数传入正常值
TEST_F(GmcImpPara, warmboot_019_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {"12@#%$^&*()_+=-{}|:<>?/.,;[]`~!@#"};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 threadNum参数传入错误参数[1,10]，超过uint32_t范围
TEST_F(GmcImpPara, warmboot_019_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    // 范围 [1, 10]
    importPara.threadNum = 0;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    importPara.threadNum = 11;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    importPara.threadNum = 4294967296;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 tableNum参数传入错误参数，超过uint32_t范围
TEST_F(GmcImpPara, warmboot_019_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = -1;
    // 范围 [1, 10]
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    importPara.tableNum = 4294967296;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    importPara.tableNum = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 timeoutMs参数传入错误参数，超过uint32_t范围
TEST_F(GmcImpPara, warmboot_019_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = -1;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    importPara.timeoutMs = 4294967296;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 200张表，threadNum设为10，GmcImportTables导入（2000张耗时较长，超过1000s，改为200）
TEST_F(GmcImpFlashBin, warmboot_019_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    const char *exportTables[2000];
    uint32_t i = 0;
    for (i = 0; i < 200; i++) {
        char *heapName = (char *)malloc(64);
        if (heapName == NULL) {
            AW_FUN_Log(LOG_ERROR, "Memory allocation failed.");
            break;
        }
        (void)snprintf(heapName, 64, "heap_%d", i);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, heapName, 0, g_heapConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create failed ret is %d, heapName is %s.", ret, heapName);
            free(heapName);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestInsertVertexLabel(g_stmtSync, 0, 2, 0, heapName);
        exportTables[i] = heapName;
    }

    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = 0;
    int count = 0;
    while ((vertexNum != i) && (count < 60)) {
        sleep(1);
        count++;
        vertexNum = TestGetViewCount(viewTableInfo, "VERTEX_LABEL_NAME | grep heap_");
        AW_FUN_Log(LOG_INFO, "vertexNum is %d, count is %d.", vertexNum, count);
    }
    AW_MACRO_EXPECT_EQ_INT(vertexNum, i);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_017");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = i,
        .threadNum = 10,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    for (uint32_t j = 0; j < i; j++) {
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, exportTables[j], 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = exportTables;
    importPara.tableNum = i;
    importPara.threadNum = 10;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmtSync, exportTables[j], NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, vertexCount);
        TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 2, 0, exportTables[j]);
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free((char *)exportTables[j]);
    }
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 目录中3个bin_data文件，参数指定4个lebelsName，tableNum传入4，有一张表名无对应的bin_data，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    const char *exportTables[2000];
    uint32_t i = 0;
    for (i = 0; i < 3; i++) {
        char *heapName = (char *)malloc(64);
        if (heapName == NULL) {
            AW_FUN_Log(LOG_ERROR, "Memory allocation failed.");
            break;
        }
        (void)snprintf(heapName, 64, "heap_%d", i);
        AW_FUN_Log(LOG_INFO, "heapName is %s.", heapName);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, heapName, 0, g_heapConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create failed ret is %d, heapName is %s.", ret, heapName);
            free(heapName);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestInsertVertexLabel(g_stmtSync, 0, 100, 0, heapName);
        exportTables[i] = heapName;
    }

    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = 0;
    int count = 0;
    while ((vertexNum != i) && (count < 60)) {
        sleep(1);
        count++;
        vertexNum = TestGetViewCount(viewTableInfo, "VERTEX_LABEL_NAME | grep heap_");
        AW_FUN_Log(LOG_INFO, "vertexNum is %d, count is %d.", vertexNum, count);
    }
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 3);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_018");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = (uint32_t)vertexNum,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    for (uint32_t j = 0; j < i; j++) {
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, exportTables[j], 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcImportParaT importPara;
    exportTables[i] = g_heapName;
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = exportTables;
    importPara.tableNum = i + 1;
    importPara.threadNum = 3;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "import table is %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2],
        exportTables[3], importPara.tableNum);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint64_t vertexCount = 0;
    for (uint32_t j = 0; j < i; j++) {
        ret = GmcGetVertexCount(g_stmtSync, exportTables[j], NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(vertexCount, 0);
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free((char *)exportTables[j]);
    }
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, 0);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 目录中包含3个bin_data文件，参数指定4个lebelsName，tableNum传入4，有一个表名重复，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    const char *exportTables[2000];
    uint32_t i = 0;
    for (i = 0; i < 3; i++) {
        char *heapName = (char *)malloc(64);
        if (heapName == NULL) {
            AW_FUN_Log(LOG_ERROR, "Memory allocation failed.");
            break;
        }
        (void)snprintf(heapName, 64, "heap_%d", i);
        AW_FUN_Log(LOG_INFO, "heapName is %s.", heapName);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, heapName, 0, g_heapConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create failed ret is %d, heapName is %s.", ret, heapName);
            free(heapName);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestInsertVertexLabel(g_stmtSync, 0, 100, 0, heapName);
        exportTables[i] = heapName;
    }

    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = 0;
    int count = 0;
    while ((vertexNum != i) && (count < 60)) {
        sleep(1);
        count++;
        vertexNum = TestGetViewCount(viewTableInfo, "VERTEX_LABEL_NAME | grep heap_");
        AW_FUN_Log(LOG_INFO, "vertexNum is %d, count is %d.", vertexNum, count);
    }
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 3);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_019");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = i,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    for (uint32_t j = 0; j < i; j++) {
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, exportTables[j], 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcImportParaT importPara;
    // 会对tableName list去重
    exportTables[i] = exportTables[i - 1];
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = exportTables;
    importPara.tableNum = i + 1;
    importPara.threadNum = 3;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmtSync, exportTables[j], NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
        TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 100, 0, exportTables[j]);
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free((char *)exportTables[j]);
    }
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 目录中包含3个bin_data文件，参数指定3个lebelsName，tableNum传入2，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    const char *exportTables[2000];
    uint32_t i = 0;
    for (i = 0; i < 3; i++) {
        char *heapName = (char *)malloc(64);
        if (heapName == NULL) {
            AW_FUN_Log(LOG_ERROR, "Memory allocation failed.");
            break;
        }
        (void)snprintf(heapName, 64, "heap_%d", i);
        AW_FUN_Log(LOG_INFO, "heapName is %s.", heapName);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, heapName, 0, g_heapConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create failed ret is %d, heapName is %s.", ret, heapName);
            free(heapName);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestInsertVertexLabel(g_stmtSync, 0, 100, 0, heapName);
        exportTables[i] = heapName;
    }

    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = 0;
    int count = 0;
    while ((vertexNum != i) && (count < 60)) {
        sleep(1);
        count++;
        vertexNum = TestGetViewCount(viewTableInfo, "VERTEX_LABEL_NAME | grep heap_");
        AW_FUN_Log(LOG_INFO, "vertexNum is %d, count is %d.", vertexNum, count);
    }
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 3);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_020");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = i,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    for (uint32_t j = 0; j < i; j++) {
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, exportTables[j], 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcImportParaT importPara;
    // 以传入的tableNum为准
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = exportTables;
    importPara.tableNum = i - 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmtSync, exportTables[j], NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (j == i - 1) {
            AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
        } else {
            AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
            TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 100, 0, exportTables[j]);
        }
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free((char *)exportTables[j]);
    }
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 目录中包含3个bin_data文件，只创建2张表，参数指定3个lebelsName，tableNum传入3，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINED_TABLE);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    const char *exportTables[2000];
    uint32_t i = 0;
    for (i = 0; i < 3; i++) {
        char *heapName = (char *)malloc(64);
        if (heapName == NULL) {
            AW_FUN_Log(LOG_ERROR, "Memory allocation failed.");
            break;
        }
        (void)snprintf(heapName, 64, "heap_%d", i);
        AW_FUN_Log(LOG_INFO, "heapName is %s.", heapName);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, heapName, 0, g_heapConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create failed ret is %d, heapName is %s.", ret, heapName);
            free(heapName);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestInsertVertexLabel(g_stmtSync, 0, 100, 0, heapName);
        exportTables[i] = heapName;
    }

    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = 0;
    int count = 0;
    while ((vertexNum != i) && (count < 60)) {
        sleep(1);
        count++;
        vertexNum = TestGetViewCount(viewTableInfo, "VERTEX_LABEL_NAME | grep heap_");
        AW_FUN_Log(LOG_INFO, "vertexNum is %d, count is %d.", vertexNum, count);
    }
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 3);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_021");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = i,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    for (uint32_t j = 0; j < i - 1; j++) {
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, exportTables[j], 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = exportTables;
    importPara.tableNum = i;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i - 1; j++) {
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmtSync, exportTables[j], NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(100, vertexCount);
        TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 100, 0, exportTables[j]);
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free((char *)exportTables[j]);
    }
    free((char *)exportTables[i - 1]);
    // 校验错误日志
    bool retOfLog = AW_CHECK_LOG_EXIST(CLIENT, 2, "GMERR-1009010", "Undefined table");
    EXPECT_TRUE(retOfLog);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 bin_data中包含1张表，参数指定1个lebelsName，threadNum指定2，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_022");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 100,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 2;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 参数指定timeoutMs设为1ms，GmcImportTables导入多张表耗时超过1ms
TEST_F(GmcImpFlashBin, warmboot_019_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    const char *exportTables[2000];
    uint32_t i = 0;
    for (i = 0; i < 3; i++) {
        char *heapName = (char *)malloc(64);
        if (heapName == NULL) {
            AW_FUN_Log(LOG_ERROR, "Memory allocation failed.");
            break;
        }
        (void)snprintf(heapName, 64, "heap_%d", i);
        AW_FUN_Log(LOG_INFO, "heapName is %s.", heapName);
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, heapName, 0, g_heapConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create failed ret is %d, heapName is %s.", ret, heapName);
            free(heapName);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestInsertVertexLabel(g_stmtSync, 0, 100, 0, heapName);
        exportTables[i] = heapName;
    }

    const char *viewTableInfo = "V\\$CATA_VERTEX_LABEL_INFO";
    int vertexNum = 0;
    int count = 0;
    while ((vertexNum != i) && (count < 60)) {
        sleep(1);
        count++;
        vertexNum = TestGetViewCount(viewTableInfo, "VERTEX_LABEL_NAME | grep heap_");
        AW_FUN_Log(LOG_INFO, "vertexNum is %d, count is %d.", vertexNum, count);
    }
    AW_MACRO_EXPECT_EQ_INT(vertexNum, 3);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_023");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = i,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < i; j++) {
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    for (uint32_t j = 0; j < i; j++) {
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, exportTables[j], 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcImportParaT importPara;
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = exportTables;
    importPara.tableNum = i;
    importPara.threadNum = 1;
    importPara.timeoutMs = 1;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    uint64_t vertexCount = 0;
    for (uint32_t j = 0; j < i; j++) {
        ret = GmcGetVertexCount(g_stmtSync, exportTables[j], NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "%s vertexCount is %d.", exportTables[j], vertexCount);
        ret = GmcDropVertexLabel(g_stmtSync, exportTables[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free((char *)exportTables[j]);
    }
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 跨磁盘，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);

    // 创建导出的路径
    (void)Rmdir(g_dirPath);
    ret = mkdir(g_dirPath, S_IRUSR | S_IWUSR | S_IXUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_dirPath);
    GmcExportParaT exportPara = {
        .exportDirPath = g_dirPath,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 100,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_dirPath;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    ret = GtExecSystemCmd("rm -rf %s", g_dirPath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 importDirPath路径指定为文件名，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DIRECTORY_OPERATE_FAILED);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_025");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *binName = "Warmboot019HeapVertex public Warmboot019HeapVertex 0.bin_data";
    (void)snprintf(g_exportDir2, 256, "%s/%s", g_exportDir, binName);
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir2;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 篡改bin_data中的内容，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_026");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *binName = "Warmboot019HeapVertex public Warmboot019HeapVertex 0.bin_data";
    (void)snprintf(g_exportDir2, 256, "%s/%s", g_exportDir, binName);
    AW_FUN_Log(LOG_INFO, "imp file path: %s", g_exportDir2);
    DestoryDataFile(g_exportDir2, 10);

    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    // 校验错误日志
    bool retOfLog = AW_CHECK_LOG_EXIST(CLIENT, 2, "GMERR-1004000", "Data exception occurs");
    EXPECT_TRUE(retOfLog);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class GmcImpNotUseRsm : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmcImpNotUseRsm::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void GmcImpNotUseRsm::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void GmcImpNotUseRsm::SetUp()
{
    system("sh getBInData.sh");
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void GmcImpNotUseRsm::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 027 配置isUseRsm = 0，GmcImportTables导入
TEST_F(GmcImpNotUseRsm, warmboot_019_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    AW_FUN_Log(LOG_INFO, "gmc imp: %s, %s", g_heapName, g_exportDir);
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName isUseRsm");

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 GmcImportTables导入多张表, 包含kv表，简单表，复杂表
TEST_F(GmcImpFlashBin, warmboot_019_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10, 0, g_heapName);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, 10, 0, g_labelName);

    const char *kvName = "Warmboot019Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, 10, 0);

    system("gmsysview count_table_kv Warmboot019Kv");
    system("gmsysview count Warmboot019Vertex1");

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_028");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_labelName, g_heapName, kvName};
    AW_FUN_Log(
        LOG_INFO, "exportTables: %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 10, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 10; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, j);
    }
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanKV(g_stmtSync, kvName, 10);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 GmcImportTables导入复制表
TEST_F(GmcImpFlashBin, warmboot_019_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);
    const char *vertexChild = "Warmboot019HeapVertexChild";
    ret = GmcDuplicateVertexLabelWithName(g_stmtSync, g_heapName, vertexChild, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexChild);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_029");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {vertexChild};
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {vertexChild};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexChild, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, vertexChild);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 GmcImportTables导入存在多个版本表
TEST_F(GmcImpFlashBin, warmboot_019_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_sysPath, g_sysName, 0, g_sysConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestReplaceVertexSys(g_stmtSync, 0, 300, 0, g_sysName);

    // gmddl 工具升级表操作
    char cmd[MAX_CMD_SIZE] = {0};
    const char *filePath1 = "schemaFile/sysTable0Alter1.gmjson";
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, g_sysName, filePath1);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUpdateVertexSys(g_stmtSync, g_sysPk, 100, 200, 100, g_sysName, 1);
    const char *filePath2 = "schemaFile/sysTable0Alter2.gmjson";
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, g_sysName, filePath2);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUpdateVertexSys(g_stmtSync, g_sysPk, 200, 300, 200, g_sysName, 2);
    TestReadVertexSysByPk(g_stmtSync, g_sysPk, 0, 100, 0, g_sysName, 0);
    TestReadVertexSysByPk(g_stmtSync, g_sysPk, 100, 200, 100, g_sysName, 1);
    TestReadVertexSysByPk(g_stmtSync, g_sysPk, 200, 300, 200, g_sysName, 2);
    system("gmsysview record sysTable0 > 30_b.txt");
    system("gmsysview count sysTable0");

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_030");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_sysName};
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_sysName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_sysPath, g_sysName, 0, g_sysConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, g_sysName, filePath1);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -f %s -u online", g_toolPath, g_sysName, filePath2);
    ret = executeCommand(cmd, "upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_sysName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record sysTable0 > 30_a.txt");

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_sysName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(300, vertexCount);
    TestReadVertexSysByPk(g_stmtSync, g_sysPk, 0, 100, 0, g_sysName, 0);
    TestReadVertexSysByPk(g_stmtSync, g_sysPk, 100, 200, 100, g_sysName, 1);
    TestReadVertexSysByPk(g_stmtSync, g_sysPk, 200, 300, 200, g_sysName, 2);
    ret = GmcDropVertexLabel(g_stmtSync, g_sysName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 GmcImportTables重复导入
TEST_F(GmcImpFlashBin, warmboot_019_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_031");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 GmcImportTables多次导入，无数据重复
TEST_F(GmcImpFlashBin, warmboot_019_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);
    const char *vertexChild = "Warmboot019HeapVertexChild";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, vertexChild, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexChild);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_032");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName, vertexChild};
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 2,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, vertexChild, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {vertexChild};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara2;
    const char *importTables2[] = {g_heapName};
    importPara2.importDirPath = g_exportDir;
    importPara2.tablesName = importTables2;
    importPara2.tableNum = 1;
    importPara2.threadNum = 1;
    importPara2.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexChild, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, vertexChild);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033 GmcImportTables多次导入，部分数据重复
TEST_F(GmcImpFlashBin, warmboot_019_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, g_heapName);
    const char *vertexChild = "Warmboot019HeapVertexChild";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, vertexChild, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexChild);
    const char *vertexChild2 = "Warmboot019HeapVertexChild2";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, vertexChild2, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, g_recordCount, 0, vertexChild2);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_033");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName, vertexChild, vertexChild2};
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, vertexChild, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, vertexChild2, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {vertexChild, vertexChild2};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 2;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara2;
    const char *importTables2[] = {g_heapName, vertexChild};
    importPara2.importDirPath = g_exportDir;
    importPara2.tablesName = importTables2;
    importPara2.tableNum = 2;
    importPara2.threadNum = 1;
    importPara2.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, vertexChild, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, vertexChild);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, vertexChild2, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, vertexChild2);
    ret = GmcDropVertexLabel(g_stmtSync, vertexChild2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034 GmcImportTables反复导入，删除表
TEST_F(GmcImpPara, warmboot_019_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();

    int dynvalue = 0;
    ret = GetSizeofPeakDynMemory(&dynvalue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "dynvalue is %d", dynvalue);
    for (int i = 0; i < 100; i++) {
        // 准备导入的表
        ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 拼接导入路径
        ret = CreatExportFilePath("binData", false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcImportParaT importPara;
        const char *importTables[] = {g_heapName};
        importPara.importDirPath = g_exportDir;
        importPara.tablesName = importTables;
        importPara.tableNum = 1;
        importPara.threadNum = 1;
        importPara.timeoutMs = 0;
        ret = GmcImportTables(g_stmtSync, &importPara);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
        TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_recordCount, 0, g_heapName);
        ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int dynvalue2 = 0;
    ret = GetSizeofPeakDynMemory(&dynvalue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "dynvalue2 is %d", dynvalue2);
    EXPECT_LE(dynvalue2, dynvalue * 1.05);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

class GmcImpPara2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmcImpPara2::SetUpTestCase()
{
    system("sh getBInData.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void GmcImpPara2::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void GmcImpPara2::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh userPolicyMode=2");
    system("${TEST_HOME}/tools/start.sh");
    AW_CHECK_LOG_BEGIN();
}

void GmcImpPara2::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 035 没有操作数据权限的情况下，GmcImportTables导入
TEST_F(GmcImpPara2, warmboot_019_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    TestImportGroupAndUser();
    TestImportPolicy();
    system("gmsysview -q V\\$PRIVILEGE_USER_STAT");
    TestPrepareEnvAndConn();
    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 拼接导入路径
    ret = CreatExportFilePath("binData", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count Warmboot019HeapVertex");

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 GmcImportTables导入后，工具再次导入
TEST_F(GmcImpFlashBin, warmboot_019_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10, 0, g_heapName);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, 10, 0, g_labelName);

    const char *kvName = "Warmboot019Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, 10, 0);

    system("gmsysview count_table_kv Warmboot019Kv");
    system("gmsysview count Warmboot019Vertex1");

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_036");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_labelName, g_heapName, kvName};
    AW_FUN_Log(
        LOG_INFO, "exportTables: %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmimport -c bin_data -f %s", g_toolPath, g_exportDir);
    AW_FUN_Log(LOG_INFO, "cmd: %s", cmd);
    ret = executeCommand(cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 10, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 10; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, j);
    }
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanKV(g_stmtSync, kvName, 10);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037 工具导入的导入后，GmcImportTables再次导入
TEST_F(GmcImpFlashBin, warmboot_019_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10, 0, g_heapName);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, 10, 0, g_labelName);

    const char *kvName = "Warmboot019Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, 10, 0);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_037");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_labelName, g_heapName, kvName};
    AW_FUN_Log(
        LOG_INFO, "exportTables: %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmimport -c bin_data -f %s", g_toolPath, g_exportDir);
    AW_FUN_Log(LOG_INFO, "cmd: %s", cmd);
    ret = executeCommand(cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 10, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 10; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, j);
    }
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanKV(g_stmtSync, kvName, 10);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038 导入非默认rsmTsp下的表
TEST_F(GmcImpFlashBin, warmboot_019_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "tspB",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig =
        "{\"max_record_count\":10000, \"is_support_reserved_memory\":true, \"tablespace_name\": \"tspB\"}";
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10, 0, g_heapName);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, 10, 0, g_labelName);

    const char *kvName = "Warmboot019Kv";
    ret = GmcKvCreateTable(g_stmtSync, kvName, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, 10, 0);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system("gmsysview -q V\\$CATA_KV_TABLE_INFO");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    system("gmsysview -q V\\$CATA_NAMESPACE_INFO");

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_038");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_labelName, g_heapName, kvName};
    AW_FUN_Log(
        LOG_INFO, "exportTables: %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "tspB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = GmcCreateTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmtSync, kvName, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd[MAX_CMD_SIZE] = {0};
    (void)sprintf_s(cmd, MAX_CMD_SIZE, "%s/gmimport -c bin_data -f %s", g_toolPath, g_exportDir);
    AW_FUN_Log(LOG_INFO, "cmd: %s", cmd);
    ret = executeCommand(cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 10, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 10; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, j);
    }
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanKV(g_stmtSync, kvName, 10);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "tspB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039 GmcImportTables导入过程中，客户端异常退出
TEST_F(GmcImpFlashBin, warmboot_019_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10, 0, g_heapName);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, 10, 0, g_labelName);

    const char *kvName = "Warmboot019Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, 10, 0);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_039");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_labelName, g_heapName, kvName};
    AW_FUN_Log(
        LOG_INFO, "exportTables: %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *systemPrcLog = "CliImpExit.log";
    system("./CliImpExit > CliImpExit.log");
    TestWaitProExit("CliImpExit");
    CheckSystemPrcLog(systemPrcLog);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 10, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 10; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, j);
    }
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanKV(g_stmtSync, kvName, 10);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040 写满数据后导出，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10000000, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_fullCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_fullCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041 导出bin_data,修改bin_data权限为000，GmcImportTables导入
TEST_F(GmcImpFlashBin, warmboot_019_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10000000, 0, g_heapName);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_041");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_heapName};
    AW_FUN_Log(LOG_INFO, "exportTables: %s, %s", exportTables[0], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 1,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改导入文件权限为000
    const char *binName = "Warmboot019HeapVertex public Warmboot019HeapVertex 0.bin_data";
    (void)snprintf(g_exportDir2, 256, "%s/%s", g_exportDir, binName);
    (void)GtExecSystemCmd("ls -al '%s'", g_exportDir2);
    ret = GtExecSystemCmd("chmod 000 -R '%s'", g_exportDir2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)GtExecSystemCmd("ls -al '%s'", g_exportDir2);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 1;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_fullCount, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, g_fullCount, 0, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *thrDmlHeapFunc(void *args)
{
    AW_FUN_Log(LOG_INFO, "thrDmlHeapFunc begin .");
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    uint64_t count = 0;
    while ((vertexCount != 10) && (count < 60)) {
        ret = GmcGetVertexCount(stmt, g_heapName, NULL, &vertexCount);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "%s vertexCount is %d, count is %d, ret is %d.", g_heapName, vertexCount, count, ret);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        count++;
    }
    AW_MACRO_EXPECT_EQ_INT(vertexCount, 10);

    TestUpdateVertexLabelByKey(stmt, g_heapPk, 0, 10, 10, g_heapName);
    TestReplaceVertexLabel(stmt, 0, 10, 20, g_heapName);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "thrDmlHeapFunc end .");
    return NULL;
}

void *thrDmlFunc(void *args)
{
    AW_FUN_Log(LOG_INFO, "thrDmlFunc begin .");
    int32_t ret = 0;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t vertexCount = 0;
    uint64_t count = 0;
    while ((vertexCount != 10) && (count < 60)) {
        ret = GmcGetVertexCount(stmt, g_labelName, NULL, &vertexCount);
        if (ret != GMERR_OK) {
            AW_FUN_Log(
                LOG_ERROR, "%s vertexCount is %d, count is %d, ret is %d.", g_labelName, vertexCount, count, ret);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        count++;
    }
    AW_MACRO_EXPECT_EQ_INT(vertexCount, 10);

    g_connAsync = NULL;
    g_stmtAsync = NULL;
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestBatchMergeAsync(g_connAsync, g_stmtAsync, 0, 10, 20, g_labelName);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "thrDmlFunc end .");
    return NULL;
}

void *thrDmlKvFunc(void *args)
{
    AW_FUN_Log(LOG_INFO, "thrDmlKvFunc begin .");
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *kvName = "Warmboot019Kv";
    ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t kvCount = 0;
    uint64_t count = 0;
    while ((kvCount != 10) && (count < 60)) {
        ret = GmcKvTableRecordCount(stmt, &kvCount);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "%s kvCount is %d, count is %d, ret is %d.", kvName, kvCount, count, ret);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        count++;
    }
    AW_MACRO_EXPECT_EQ_INT(kvCount, 10);

    TestBatchSetKV(conn, stmt, kvName, 10, 20);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "thrDmlKvFunc end .");
    return NULL;
}

// 042 主线程导入，其它线程dml操作
TEST_F(GmcImpFlashBin, warmboot_019_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    TestPrepareEnvAndConn();
    // 准备导入的文件
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexLabel(g_stmtSync, 0, 10, 0, g_heapName);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, 10, 0, g_labelName);

    const char *kvName = "Warmboot019Kv";
    const char *kvConfig = "{\"max_record_count\":10000, \"is_support_reserved_memory\":true}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetKV(g_stmtSync, kvName, 10, 0);

    // 创建导出的路径
    ret = CreatExportFilePath("warmboot_019_042");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *exportTables[] = {g_labelName, g_heapName, kvName};
    AW_FUN_Log(
        LOG_INFO, "exportTables: %s, %s, %s, %s.", exportTables[0], exportTables[1], exportTables[2], g_exportDir);
    GmcExportParaT exportPara = {
        .exportDirPath = g_exportDir,
        .tablesName = exportTables,
        .tableNum = 3,
        .threadNum = 1,
        .timeoutMs = 0,
    };
    ret = GmcExportTables(g_stmtSync, &exportPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    // 新起服务导入数据
    TestRebootDb(false);
    TestPrepareEnvAndConn();

    // 准备导入的表
    ret = testGmcCreateVertexLabel(g_stmtSync, g_heapPath, g_heapName, 0, g_heapConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thrDmlHeap, thrDml, thrDmlKv;
    ret = pthread_create(&thrDmlHeap, NULL, thrDmlHeapFunc, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thrDml, NULL, thrDmlFunc, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thrDmlKv, NULL, thrDmlKvFunc, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcImportParaT importPara;
    const char *importTables[] = {g_heapName, g_labelName, kvName};
    importPara.importDirPath = g_exportDir;
    importPara.tablesName = importTables;
    importPara.tableNum = 3;
    importPara.threadNum = 1;
    importPara.timeoutMs = 0;
    ret = GmcImportTables(g_stmtSync, &importPara);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thrDmlHeap, NULL);
    pthread_join(thrDml, NULL);
    pthread_join(thrDmlKv, NULL);
    system("gmsysview  record_table_kv Warmboot019Kv");

    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_heapName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, vertexCount);
    TestScanLabelByKey(g_stmtSync, g_heapPk, 0, 10, 20, g_heapName);
    ret = GmcDropVertexLabel(g_stmtSync, g_heapName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 10; j++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, j, 20);
    }
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestScanKV(g_stmtSync, kvName, 10, 20);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}
