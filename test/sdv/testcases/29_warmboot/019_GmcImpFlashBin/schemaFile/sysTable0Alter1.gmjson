[{"schema_version": 1, "type": "record", "tablespace": "public", "name": "sysTable0", "special_complex": false, "config": {"check_validity": false}, "fields": [{"name": "RealDbID", "type": "uint32", "nullable": false}, {"name": "RealTableID", "type": "uint16", "nullable": false}, {"name": "TableName", "type": "string", "size": 65536, "nullable": false}, {"name": "AppID", "type": "uint32", "nullable": false}, {"name": "MaxRecNum", "type": "uint32", "nullable": false}, {"name": "KeyLen", "type": "uint16", "nullable": false}, {"name": "ValueLen", "type": "uint16", "nullable": false}, {"name": "Reserve1", "type": "uint16", "nullable": false}, {"name": "Reserve2", "type": "uint32", "nullable": false}, {"name": "Reserve3", "type": "uint32", "nullable": true}], "keys": [{"node": "sysTable0", "name": "Index", "fields": ["RealDbID", "RealTableID"], "index": {"type": "primary"}, "constraints": {"unique": true}, "config": {"hash_type": "HASH_INDEX", "init_hash_capacity": 0}}]}]