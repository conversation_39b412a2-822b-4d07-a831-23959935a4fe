[{"version": "2.0", "type": "record", "name": "VertexHeap", "fields": [{"name": "PK", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "P0", "type": "fixed", "size": 400, "nullable": true}, {"name": "F5", "type": "uint32", "nullable": false, "comment": "Vs索引"}, {"name": "F6", "type": "uint32", "nullable": false, "comment": "VpnInstace索引"}, {"name": "F7", "type": "uint32", "nullable": false, "comment": "目的地址"}, {"name": "F8", "type": "uint8", "nullable": false, "comment": "掩码长度"}, {"name": "T1_V", "type": "record", "array": true, "fields": [{"name": "V1", "type": "uint32", "nullable": true}, {"name": "V2", "type": "uint32", "nullable": true}]}], "keys": [{"name": "Heap_pk", "index": {"type": "primary"}, "node": "VertexHeap", "fields": ["PK"], "constraints": {"unique": true}}, {"name": "Heap_<PERSON><PERSON>h", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "VertexHeap", "fields": ["F1"], "constraints": {"unique": true}}, {"name": "Heap_hashcluster", "index": {"type": "hashcluster"}, "node": "VertexHeap", "fields": ["F2"], "constraints": {"unique": true}}, {"name": "Heap_local", "index": {"type": "local"}, "node": "VertexHeap", "fields": ["F3"], "constraints": {"unique": true}}, {"name": "Heap_lpm4", "index": {"type": "lpm4_tree_bitmap"}, "node": "VertexHeap", "fields": ["F5", "F6", "F7", "F8"], "constraints": {"unique": true}}, {"name": "Heap_memkey", "index": {"type": "none"}, "node": "T1_V", "fields": ["V1"], "constraints": {"unique": true}}]}]