/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 01、heap容器,空db软重启后操作
 * Author: lushiguang
 * Create: 2025-07-3
 */


#include "rsm_extend.h"


class RsmExtend01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

int StartEnvWithConfig(int num, ...)
{
    AW_FUN_Log(LOG_STEP, "[INFO] RsmExtend01 Start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableReleaseDevice=1\"");

    va_list varList;
    char tempCmd[1024] = {0};
    const char *strKey;
    va_start(varList, num);
    for (int i = 0; i < num; i++) {
        strKey = va_arg(varList, const char *);
        (void)sprintf(tempCmd, "sh $TEST_HOME/tools/modifyCfg.sh \"%s\"", strKey);
        printf("tempcmd: %s\n", tempCmd);
        system(tempCmd);
    }
    va_end(varList);

    int ret = InitRsmCfg();
    RETURN_IFERR(ret);

    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    // 空DB软重启
    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    AW_CHECK_LOG_BEGIN();
    return ret;
}

void RsmExtend01::SetUp() {}

void RsmExtend01::TearDown()
{
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "[INFO] RsmExtend01 End.");
}

// 001、heap模式，空DB软重启后升级过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VarTbUpgrade4Version(g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导入所有schema
    ret = VarTbUpgrade4Version(g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    ret = CommonUpdateVarTb(g_stmt, g_varTableName, g_version3, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version3, startIndex, startIndex + insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、heap模式，空DB软重启后降级过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_insertCount = 50000;
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_withNewField = true;
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VarTbUpgrade4Version(g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version4, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DowngradeSchemaAsync(g_varTableName, g_version0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    insertCount = 1000;
    ret = CommonUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int timeout = 10;
    for (int i = 0; i < timeout; i++) {
        sleep(2);
        ret = GtExecSystemCmd("gmsysview -q 'V$QRY_SHOW_SCHEMA_DEGRADE' | grep 'SCHEMA_VERSIONS_COUNT: 1'");
        if (ret == GMERR_OK) {
            break;
        }
    }

    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、heap模式，空DB软重启后DML过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_insertCount = 50000;
    uint32_t startIndex = 0;
    g_asyncOperateCount = 0;
    uint32_t insertCount = g_insertCount;
    g_withNewField = false;
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(g_stmt, g_varTableName, g_version0, startIndex, insertCount, false, T_RSM_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        usleep(1000);
        if (g_asyncOperateCount > 0) {
            break;
        }
    }
    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + g_insertCount,
        g_asyncOperateCount);
    int ret2 = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + g_insertCount,
        g_asyncOperateCount + 1);
    bool status = (ret == GMERR_OK || ret2 == GMERR_OK);
    AW_MACRO_ASSERT_EQ_BOOL(true, status);

    // 更新
    int count = g_asyncOperateCount;
    ret = CommonUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, count, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex, startIndex + count, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、heap模式，空DB软重启后老化对账过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_insertCount = 50000;
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_withNewField = false;
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt, g_varTableName, 0xff);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 更新20000~22200记录，其余老化
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex + 20000, 200, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmt, g_varTableName, 0xff, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount, 200);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    ret = CommonUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex + 20000, 200, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version0, startIndex + 20000, startIndex + 20000 + 200,
        g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、heap模式，空DB软重启后触发页搬迁过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(4, "deviceSize=1", "defaultTablespaceMaxSize=8", "RsmKeyRange=0,2", "RsmBlockSize=32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *tspName1 = (const char *)"tspName1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tspName1;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 0;

    ret = GmcCreateRsmTablespace(g_stmt, &tspCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_insertCount = 100000;
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_withNewField = false;
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_STEP, "g_fullIndex: %d.", g_fullIndex);
    ret = CommonDelete(g_stmt, g_varTableName, g_version0, startIndex, g_fullIndex / 2 + 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateRsmTablespace(g_stmt, &tspCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        g_fullIndex - g_fullIndex / 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    int updateCount = 200;
    ret = CommonUpdateVarTb(g_stmt, g_varTableName, g_version0, g_fullIndex / 2 + 1, updateCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableName, g_version0, g_fullIndex / 2 + 1,
        g_fullIndex / 2 + 1 + updateCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmt, tspName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OUT_OF_MEMORY);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、heap模式，空DB软重启后触发后台缩容过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(2, "minFragmentationRateThreshold=2", "minFragmentationMemThreshold=1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_insertCount = 50000;
    uint32_t startIndex = 0;
    uint32_t insertCount = g_insertCount;
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_withNewField = false;
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_asyncDeleteCount = 0;
    ret =
        AsyncVarTbOperate(g_stmt, g_varTableName, g_version0, startIndex, insertCount / 2 + 10000, false, T_RSM_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitActiveScaleInHeap(g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableName, (char *)"./schema/var_vertex_0.gmjson", g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableName, g_version0, startIndex, startIndex + insertCount,
        insertCount - g_asyncDeleteCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    ret = CommonReplaceVarTb(g_stmt, g_varTableName, g_version0, startIndex, insertCount, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_varTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、heap模式，空DB软重启后触发cache写过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(1, "maxWriteCacheSize=100");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_insertCount = 50000;
    uint32_t startIndex = 0;
    g_asyncOperateCount = 0;
    uint32_t insertCount = g_insertCount;
    g_withNewField = false;
    ret = CommonCreateTable(g_stmt, g_varTableNameCw, (char *)"./schema/var_vertex_0.gmjson", g_cwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(g_stmt, g_varTableNameCw, g_version0, startIndex, insertCount, false, T_RSM_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        usleep(10);
        if (g_asyncOperateCount > 0) {
            break;
        }
    }
    WarmReboot();
    ret = CleanAndReConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "g_asyncOperateCount: %d.", g_asyncOperateCount);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableNameCw, (char *)"./schema/var_vertex_0.gmjson", g_cwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableNameCw, g_version0, startIndex, startIndex + g_insertCount,
        g_asyncOperateCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 更新
    int count = g_asyncOperateCount;
    ret = CommonUpdateVarTb(g_stmt, g_varTableNameCw, g_version0, startIndex, count, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableNameCw, g_version0, startIndex, startIndex + count, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_varTableNameCw);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、heap模式，空DB软重启后直连写过程再软重启，预期：数据成功恢复
TEST_F(RsmExtend01, warmboot_020_001_01_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = StartEnvWithConfig(1, "directWrite=1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_insertCount = 50000;
    uint32_t startIndex = 0;
    g_asyncOperateCount = 0;
    uint32_t insertCount = g_insertCount;
    g_withNewField = false;
    ret = CommonCreateTable(g_stmt, g_varTableNameDw, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncVarTbOperate(g_stmt, g_varTableNameDw, g_version0, startIndex, insertCount, false, T_RSM_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        usleep(10);
        if (g_asyncOperateCount > 0) {
            break;
        }
    }

    (void)system("sh $TEST_HOME/tools/stop.sh -f");

    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    (void)system("sh $TEST_HOME/tools/start.sh -f -rb");

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重新建表
    ret = CommonCreateTable(g_stmt, g_varTableNameDw, (char *)"./schema/var_vertex_0.gmjson", g_dwConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DFX查询等待恢复
    int tableCount = 1;
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonRecordCount(g_stmt, g_varTableNameDw, g_version0, startIndex, startIndex + g_insertCount,
        g_asyncOperateCount);
    int ret2 = CommonRecordCount(g_stmt, g_varTableNameDw, g_version0, startIndex, startIndex + g_insertCount,
        g_asyncOperateCount + 1);
    bool status = (ret == GMERR_OK || ret2 == GMERR_OK);
    AW_MACRO_ASSERT_EQ_BOOL(true, status);

    // 更新
    int count = g_asyncOperateCount;
    ret = CommonUpdateVarTb(g_stmt, g_varTableNameDw, g_version0, startIndex, count, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckUpdateVarTb(g_stmt, g_varTableNameDw, g_version0, startIndex, startIndex + count, g_withNewField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_varTableNameDw);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

