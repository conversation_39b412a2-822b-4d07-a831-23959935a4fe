/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 导出导入相关接口测试
 * Author: lushiguang
 * Create: 2023-10-23
 */


#include "../../common/warm_reboot_common.h"

int g_beginIndex = 0;
int g_endIndex = 100;
char g_exportDir[512] = {0};

class InterfaceTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void InterfaceTest::SetUp()
{
    printf("[INFO] check interface Start.\n");

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(g_exportDir, "%s/export_dir", pwdDir);
    (void)Rmdir(g_exportDir);
    int ret = mkdir(g_exportDir, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void InterfaceTest::TearDown()
{
    int ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] check interface End.\n");
}

// 001.建表，reboot_persistence配置true
TEST_F(InterfaceTest, imexport_003_001_01_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.建表，reboot_persistence配置其他值，建表
TEST_F(InterfaceTest, imexport_003_001_01_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": "xxxxx"
        }
    )";
    int ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    const char *tbConfig2 = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATATYPE_MISMATCH);
    AW_FUN_Log(LOG_STEP, "test end.");
}
 

// gmexport 004. 参数正常-c bin_data -f exportdir -st disk, 其余可选缺省
TEST_F(InterfaceTest, imexport_003_001_01_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 005.参数正常-c bin_data -f exportdir -st disk -tn 1
TEST_F(InterfaceTest, imexport_003_001_01_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 1", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 006.参数正常-c bin_data -f exportdir -st disk -tn 1 -rc crc
TEST_F(InterfaceTest, imexport_003_001_01_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 1 -rc crc", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 007.参数-tn 设置为3
TEST_F(InterfaceTest, imexport_003_001_01_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 3 -rc crc", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 009.参数-c 非法选值
TEST_F(InterfaceTest, imexport_003_001_01_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_datax -f %s -st disk -tn 3 -rc crc", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 010.参数-f 路径不存在
TEST_F(InterfaceTest, imexport_003_001_01_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);

    int ret = Rmdir(g_exportDir);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 3 -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 011.参数-f 路径为文件路径，非目录
TEST_F(InterfaceTest, imexport_003_001_01_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char filePath[512] = {0};
    (void)snprintf(filePath, 512, "%s/exportfile", g_exportDir);
    int ret = GtExecSystemCmd("touch %s", filePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 3 -rc crc", filePath);
    ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 012.参数-st 非法选值
TEST_F(InterfaceTest, imexport_003_001_01_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st diskX1 -tn 3 -rc crc", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 013.参数-tn 非法选值0，11
TEST_F(InterfaceTest, imexport_003_001_01_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 0 -rc crc", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 11 -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 10 -rc crc", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 014.参数-rc 非法选值
TEST_F(InterfaceTest, imexport_003_001_01_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -tn 2 -rc xxxcrc", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 015.非法格式
TEST_F(InterfaceTest, imexport_003_001_01_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -a xs -t sdfs -fsdf -*&@(*^(^# -c bin_data -f %s -st disk -tn 2 -rc crc",
        g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 016.参数正常-c bin_data -f exportdir, 其余可选缺省
TEST_F(InterfaceTest, imexport_003_001_01_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc none", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 017.参数正常-c bin_data -f exportdir -tn 1
TEST_F(InterfaceTest, imexport_003_001_01_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc none", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 1", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 018.参数正常-c bin_data -f exportdir -tn 2
TEST_F(InterfaceTest, imexport_003_001_01_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc none", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 2", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 019.参数-c 非法选值
TEST_F(InterfaceTest, imexport_003_001_01_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk -rc none", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c xxxbin_data -f %s -tn 2", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    // 查询数据
    int expectCount = 0;
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 正常导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 2", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 020.参数-f 路径不存在
TEST_F(InterfaceTest, imexport_003_001_01_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tempCmd[512] = {0};
    int ret = Rmdir(g_exportDir);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestdelVertexSync(g_stmt, g_simpleLabel, g_indexName, g_cond, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 2", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    // 查询数据
    int expectCount = 0;
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 021.参数-tn 非法选值0，11
TEST_F(InterfaceTest, imexport_003_001_01_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 0", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 11", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    // 无数据
    int expectCount = 0;
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新正常导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 10", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 022.非法格式
TEST_F(InterfaceTest, imexport_003_001_01_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导出
    char expect[200] = {0};
    (void)snprintf(expect, 200, "export label %s successfully", g_simpleLabel);
    char tempCmd[512] = {0};
    (void)snprintf(tempCmd, 512, "gmexport -c bin_data -f %s -st disk", g_exportDir);
    int ret = ExecuteAndExpect(tempCmd, expect);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartServerAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *tbConfig = (char *)R"(
        {
            "max_record_count":1000000,
            "reboot_persistence": true
        }
    )";
    ret = CommonCreateTable(g_stmt, g_simpleLabel, (char *)"../../schema/vl_simple.gmjson", tbConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导入
    (void)snprintf(tempCmd, 512, "gmimport -d -d -c -c sdf -c bin_data  -f %s -tn 0 ^*^@9SDOIYU", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(T_FAILED, ret);

    // 无数据
    int expectCount = 0;
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新正常导入
    (void)snprintf(tempCmd, 512, "gmimport -c bin_data -f %s -tn 2 ", g_exportDir);
    ret = ExecuteAndExpect(tempCmd, (char *)"Insert data succeed. successNum: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    ret = TestSelVertexRecord(g_stmt, g_simpleLabel, g_indexName, g_cond, g_expect, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

