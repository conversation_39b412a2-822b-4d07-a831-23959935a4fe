/*****************************************************************************
 Description  : 全表订阅推送
 Notes        :
SN_007_001 接口参数验证
SN_007_002 插入订阅，先订阅再写数据
SN_007_003 插入与更新订阅，先订阅再写数据、更新数据、删除数据
SN_007_004 插入与删除订阅，先订阅再写数据、删除数据
SN_007_005 三种订阅，先订阅再写数据、更新数据、删除数据
SN_007_006 插入与更新订阅，先订阅再批量写数据、普通更新数据
SN_007_007 三种订阅，先订阅再批量写数据、普通删除数据
SN_007_008 三种订阅，先订阅再批量写数据、批量更新数据、批量删除数据
SN_007_009 更新订阅，先写数据再订阅、更新数据
SN_007_010 删除订阅，先写数据再订阅、删除数据
SN_007_011 三种订阅，先写数据、更新数据、删除数据、写数据，再订阅、更新数据、删除数据
SN_007_012 三种订阅，先批量写数据再订阅、批量更新数据、普通删除数据
SN_007_013 三种订阅，先写数据再订阅、普通更新数据、批量删除数据
SN_007_014 三种订阅，先批量写数据再订阅、批量更新数据、批量删除数据
SN_007_015 插入订阅，创建表，创建订阅关系, 写入数据，提交后删除表，再建表，重新创建订阅关系，重写数据
SN_007_016 插入订阅，创建表，创建订阅关系,
写入数据，不提交，删除表(检验是否删除的数据推送完再drop)，再建表，重新创建订阅关系，重写数据
SN_007_017 三种订阅，取消订阅后，写入数据、更新数据、删除数据
SN_007_018 三种订阅，先开启事务、写数据、再订阅，预期失败
SN_007_019 三种订阅，hash索引表先订阅再写数据、更新数据、删除数据
SN_007_020 三种订阅，hash索引表先写数据，再订阅、更新数据、删除数据
SN_007_021 三种订阅，yang模型表先订阅再写数据、更新数据、删除数据   yang场景没有订阅推送的需求，测试不覆盖
SN_007_022 三种订阅，yang模型表先写数据再订阅、更新数据、删除数据   yang场景没有订阅推送的需求，测试不覆盖
SN_007_023 先订阅，插入已存在的数据，插入订阅失败，检查订阅推送内容，取消订阅
SN_007_024 先订阅，更新不存在的数据，更新订阅失败，检查订阅推送内容，取消订阅
SN_007_025 先订阅，删除不存在的数据，删除订阅失败，检查订阅推送内容，取消订阅
SN_007_054 更新订阅，先写数据再订阅、写数据、更新数据，插入不推送
SN_007_055 先写数据再订阅、更新数据(更新成自己)
 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2020/11/27
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "../002_CreateAndDeleteFullLabelSubscription/sub_tools.h"

char g_label_name[] = "T20_all_type";
char g_lable_PK[] = "T20_PK";
char g_lable_hash[] = "T20_hash";
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

using namespace std;

class FullLabelSubscriptionAndPush : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    virtual void SetUp();
    virtual void TearDown();
};

void FullLabelSubscriptionAndPush::SetUp()
{
    printf("FullLabelSubscriptionAndPush Start.\n");

    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info = NULL;
    int ret;

    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    ASSERT_EQ(GMERR_OK, ret);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    // 前置用例失败中断，会导致订阅关系存在,删表失败
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    AW_FUN_Log(LOG_STEP, "GmcUnSubscribe##%d.", ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    AW_FUN_Log(LOG_STEP, "GmcDropVertexLabel##%d.", ret);

    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
void FullLabelSubscriptionAndPush::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //删表断连
    test_close_and_drop_label(g_stmt_sync, g_label, g_label_name);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(g_schema);
    testSnFreeUserData(user_data);
    printf("FullLabelSubscriptionAndPush End.\n");
}

void sn_callback_abnormal_01(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {  // info->labelCount为 1
            //异常场景1：labelNameLen小于实际长度
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = 12;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            printf("[INFO] vrtxLabelIdx : %d, labelName : %s, labelNameLen : %d\r\n", i, labelName, labelNameLen);
            EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    test_checkVertexProperty_sub(subStmt, 0);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_abnormal_02(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {  // info->labelCount为 1
            //异常场景2：vrtxLabelIdx超出推送表的数量

            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, info->labelCount + 1, labelName, &labelNameLen);
            printf("[INFO] vrtxLabelIdx : %d, labelName : %s\r\n", info->labelCount + 1, labelName);
            EXPECT_EQ(GMERR_OK, ret);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    test_checkVertexProperty_sub(subStmt, 1);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_abnormal_03(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {  // info->labelCount为 1
            //异常场景3：insert推送时isFetchOldVrtx取true
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            printf("[INFO] vrtxLabelIdx : %d, labelName : %s, labelNameLen : %d\r\n", i, labelName, labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    pk = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", pk);
                    test_checkVertexProperty_sub(subStmt, pk);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", pk);
                    test_checkVertexProperty_sub(subStmt, pk);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//接口参数验证
TEST_F(FullLabelSubscriptionAndPush, SN_007_001)
{
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret;
    int chanRingLen = 64;
    GmcConnT *conn_sub = 0;
    GmcStmtT *stmt_sub = 0;
    const char *subConnName = "tmpsubConnName";
    uint32_t msgType;

    //创建订阅连接
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    //创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    char pushVrtxName[128] = {0};
    uint32_t vrtxNameLen = 0;
    uint32_t eventType, recvMsgType;

    //接口传NULL
    ret = GmcSubGetLabelName(NULL, 0, pushVrtxName, &vrtxNameLen);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSubGetLabelName(stmt_sub, 0, NULL, &vrtxNameLen);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSubGetLabelName(stmt_sub, 0, pushVrtxName, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSubSetFetchMode(NULL, GMC_SUB_FETCH_NEW);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSubGetLabelName(g_stmt_sync, 0, pushVrtxName, &vrtxNameLen);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(g_stmt_sync, GMC_SUB_FETCH_NEW);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSubGetLabelName(g_stmt_async, 0, pushVrtxName, &vrtxNameLen);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(g_stmt_async, GMC_SUB_FETCH_NEW);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //写1条数据测试接口参数异常场景
    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    //创建订阅关系
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_abnormal_01, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //异常场景1：labelNameLen小于实际长度
    //设置推送上下文，预期推送单表的1条数据
    int userDataIdx = 0;
    ((int *)(user_data->new_value))[userDataIdx] = 0;
    userDataIdx++;
    //写1条数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    test_setVertexProperty(g_stmt_sync, 0);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    //等待推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_abnormal_02, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //异常场景2：vrtxLabelIdx超出推送表的数量
    //设置推送上下文，预期推送单表的1条数据
    ((int *)(user_data->new_value))[userDataIdx] = 1;
    userDataIdx++;

    //写1条数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    test_setVertexProperty(g_stmt_sync, 1);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    //等待推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_abnormal_03, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //异常场景3：insert推送时isFetchOldVrtx取true,delete推送时isFetchOldVrtx取false
    //设置推送上下文，预期推送单表的1条数据
    ((int *)(user_data->new_value))[userDataIdx] = 2;
    userDataIdx++;

    //写1条数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    test_setVertexProperty(g_stmt_sync, 2);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    //等待推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);

    ((int *)(user_data->old_value))[userDataIdx] = 2;
    userDataIdx++;
    uint32_t key = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &key, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    //等待推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    testSubDisConnect(conn_sub, stmt_sub);
    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // sdv 0722
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
}

//先订阅再写数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_002)
{
    int ret;
    int32_t i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_insert.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);

        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //弱校验，仅校验推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅再写数据、更新数据（删除不订阅）
TEST_F(FullLabelSubscriptionAndPush, SN_007_003)
{
    int ret;
    int i;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo_insert_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        // printf("[INFO] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 2021.11.23: 主键不可更新
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // 2021.11.23: 主键不可更新
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // 2021.11.23: 主键不可更新
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅再写数据、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_004)
{
    int ret;
    int i;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo_insert_delete.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //删除数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        // ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetIndexKeyName(g_stmt_sync,  g_lable_PK);
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcExecute(g_stmt_sync);
        // EXPECT_EQ(GMERR_NO_DATA, ret);
        // GmcFreeIndexKey(g_stmt_sync);
    }
    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅再写数据、更新数据、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_005)
{
    int ret;
    int i;
    int userDataIdx = 0;
#if defined ENV_RTOSV2X || defined ENV_RTOSV2
    g_data_num = 1000;
#else
    g_data_num = 10000;
#endif

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        // printf("[GmcInsertVertex] i = %d\r\n", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        // printf("[GmcUpdateVertexByIndexKey] i = %d\r\n", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    int tmp;
    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i;

        // printf("[GmcDeleteVertexByIndexKey] i = %d\r\n", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    g_data_num = 50;
}

//先订阅再批量写数据、普通更新数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_006)
{
    int ret;
    int i;
    int start_num = 0;
    int end_num = g_data_num;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_insert_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅再批量写数据、普通删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_007)
{
    int ret;
    int i;
    int start_num = 0;
    int end_num = g_data_num;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    //删除数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅再批量写数据、批量更新数据、批量删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_008)
{
    int ret;
    int i;
    int start_num = 0;
    int end_num = g_data_num;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    //批量更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    int tmp;
    //批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先写数据再订阅、更新数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_009)
{
    int ret;
    int i;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }
    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先写数据再订阅、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_010)
{
    int ret;
    int i;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo_delete.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    printf("g_sub_info: %s\r\n", g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }
    
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //删除数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        // ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetIndexKeyName(g_stmt_sync,  g_lable_PK);
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcExecute(g_stmt_sync);
        // EXPECT_EQ(GMERR_NO_DATA, ret);
        // GmcFreeIndexKey(g_stmt_sync);
    }
    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先写数据、更新数据、删除数据、写数据，再订阅、更新数据、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_011)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    int tmp;
    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先批量写数据再订阅、批量更新数据、普通删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_012)
{
    int ret;
    int i;
    int start_num = 0;
    int end_num = g_data_num;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    //订阅
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //批量更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    int tmp;
    //普通删除数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先写数据再订阅、普通更新数据、批量删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_013)
{
    int ret;
    int i;
    int start_num = 0;
    int end_num = g_data_num;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    int tmp;
    //批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先批量写数据再订阅、批量更新数据、批量删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_014)
{
    int ret;
    int i;
    int start_num = 0;
    int end_num = g_data_num;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //批量更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    int tmp;
    //批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建表，创建订阅关系, 写入数据，提交后删除表，再建表，重新创建订阅关系，重写数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_015)
{
    int ret = 0;
    int i;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo_insert.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }
    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //删表
    test_close_and_drop_label(g_stmt_sync, g_label, g_label_name);
    //重建表
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //重新创建订阅关系
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //重新创建订阅关系
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //重写数据
    for (i = 0; i < g_data_num; i++) {
        // printf("[INFO] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }
    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建表，创建订阅关系, 写入数据，不提交，删除表(检验是否有删除数据的推送)，再建表，重新创建订阅关系，重写数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_016)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_insert.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //删表
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    //重建表
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //重新创建订阅关系
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //重新创建订阅关系
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //重写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//写入数据，取消订阅后，更新数据、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_017)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    int tmp;
    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 0);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 0);
    EXPECT_EQ(0, ret);
}

//先开启事务、写数据、再订阅，预期失败
TEST_F(FullLabelSubscriptionAndPush, SN_007_018)
{
    int ret = 0;
    int i;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        // test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    // EXPECT_EQ(STATUS_QUERY_INVALID_DDL_TRANS, ret);
    EXPECT_EQ(GMERR_OK, ret);  // 20210607 代码变更，事务中做DDL，会在DDL之前检查事务状态，然后自动回滚或提交

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
}

// hash索引表先订阅再写数据、更新数据、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_019)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    free(g_schema);
    g_schema = 0;
    char label_name[] = "T20_all_type_hash";
    void *label = 0;

    readJanssonFile("schema_file/all_type_schema_hash.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    free(g_sub_info);
    g_sub_info = 0;
    readJanssonFile("schema_file/all_type_schema_subinfo_hash.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    //建hash索引表
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = "subVertexLabel_hash";
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, label, g_lable_PK, i);
    }

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_hash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, label, g_lable_PK, i + g_data_num);
    }

    int tmp;
    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &tmp, sizeof(tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_hash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    const char *subName = "subVertexLabel_hash";
    ret = GmcUnSubscribe(g_stmt_sync, subName);
    EXPECT_EQ(GMERR_OK, ret);
    test_close_and_drop_label(g_stmt_sync, label, label_name);
}

// hash索引表先写数据，再订阅、更新数据、删除数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_020)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    free(g_schema);
    g_schema = 0;
    char label_name[] = "T20_all_type_hash";
    void *label = 0;

    readJanssonFile("schema_file/all_type_schema_hash.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    free(g_sub_info);
    g_sub_info = 0;
    readJanssonFile("schema_file/all_type_schema_subinfo_hash.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    //建hash索引表
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, label, g_lable_PK, i);
    }

    const char *subName = "subVertexLabel_hash";
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_hash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, label, g_lable_PK, i + g_data_num);
    }

    int tmp;
    //删除数据
    for (i = 0; i < g_data_num; i++) {
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &tmp, sizeof(tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_hash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, subName);
    EXPECT_EQ(GMERR_OK, ret);
    test_close_and_drop_label(g_stmt_sync, label, label_name);
}


//先订阅，插入已存在的数据，插入订阅失败，检查订阅推送内容，取消订阅
TEST_F(FullLabelSubscriptionAndPush, SN_007_023)
{
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret;
    int i;

    readJanssonFile("schema_file/all_type_schema_subinfo_insert.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写入已存在的数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅，更新不存在的数据，更新订阅失败，检查订阅推送内容，取消订阅
TEST_F(FullLabelSubscriptionAndPush, SN_007_024)
{
    int ret;
    int i;

    readJanssonFile("schema_file/all_type_schema_subinfo_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //删除数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新不存在的数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 0);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先订阅，删除不存在的数据，删除订阅失败，检查订阅推送内容，取消订阅
TEST_F(FullLabelSubscriptionAndPush, SN_007_025)
{
    int ret;
    int i;

    readJanssonFile("schema_file/all_type_schema_subinfo_delete.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //删除数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //删除不存在的数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
        //检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 0);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先写数据再订阅、写数据、更新数据(只订阅更新,插入不推送)
TEST_F(FullLabelSubscriptionAndPush, SN_007_054)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num / 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    for (i = g_data_num / 2; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i + g_data_num);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//先写数据再订阅、更新数据(更新成自己)
TEST_F(FullLabelSubscriptionAndPush, SN_007_055)
{
    int ret;
    int i;
    int userDataIdx = 0;

    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_subinfo_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_checkVertexProperty(g_stmt_sync, g_label, g_lable_PK, i);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        //检查所更新
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}
