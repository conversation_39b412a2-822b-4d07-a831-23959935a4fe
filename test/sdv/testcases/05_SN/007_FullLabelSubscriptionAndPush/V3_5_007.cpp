/*****************************************************************************
 Description  : 全表订阅推送
 Notes        : SN_007_V3Trans_001 更新订阅，先写数据再订阅、更新大对象数据
                SN_007_V3Trans_002 删除订阅，先写数据再订阅、删除大对象数据
                SN_007_V3Trans_003 三种订阅，先写数据再订阅、将recode下的唯一字段置为null，触发推送
                SN_007_V3Trans_004 三种订阅，先写数据再订阅、truncate table数据，触发推送
                SN_007_V3Trans_005 三种订阅，先订阅再写数据、truncate table数据，触发推送
 History      :
 Author       : hanyang
 Modification :
 Date         : 2021/09/17
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "../002_CreateAndDeleteFullLabelSubscription/sub_tools.h"

const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

// label和key的name
// MS Vertex
const char *MS_VLabel_Name = "Vertex_01_LargeObject";
const char *MS_VLabel_Key_Name = "Vertex_01_LargeObject_Key";

// MS Tree
const char *MS_VLabel_Tree_Name = "Vertex_02_Tree";
const char *MS_VLabel_Tree_Key_Name = "Vertex_02_Tree_Key";
// MS config
const char *MS_config = "{\"max_record_count\" : 30000}";

int ret = 0;

using namespace std;

// 以下两个函数update订阅专用
void test_checkVertexProperty_old(GmcStmtT *stmt, int pk)
{
    uint32_t value = pk;
    ret = queryPropertyAndCompare(stmt, "PK", GMC_DATATYPE_UINT32, &value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT32, &value);
    EXPECT_EQ(GMERR_OK, ret);

    //写string数据
    uint32_t SuperSize = 10240;
    char *SuperValue = (char *)malloc(SuperSize);
    memset(SuperValue, 'A', (SuperSize - 1));
    SuperValue[SuperSize - 1] = '\0';

    ret = queryPropertyAndCompare(stmt, "P0", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P1", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P2", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P3", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P4", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P5", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P6", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);

    free(SuperValue);
}

void test_checkVertexProperty_new(GmcStmtT *stmt, int pk)
{
    uint32_t value = pk;
    ret = queryPropertyAndCompare(stmt, "PK", GMC_DATATYPE_UINT32, &value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT32, &value);
    EXPECT_EQ(GMERR_OK, ret);

    //写string数据
    uint32_t SuperSize = 10240;
    char *SuperValue = (char *)malloc(SuperSize);
    memset(SuperValue, 'B', (SuperSize - 1));
    SuperValue[SuperSize - 1] = '\0';

    ret = queryPropertyAndCompare(stmt, "P0", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P1", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P2", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P3", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P4", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P5", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "P6", GMC_DATATYPE_STRING, SuperValue);
    EXPECT_EQ(GMERR_OK, ret);

    free(SuperValue);
}

void sn_callback_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    test_checkVertexProperty_new(subStmt, index);

                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);

                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    test_checkVertexProperty_old(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    test_checkVertexProperty_new(subStmt, index);

                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    test_checkVertexProperty_old(subStmt, index);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
        }
    }
}

void testInsertVertex_LargeObject(GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    uint32_t i = 0;
    uint32_t value = 0;

    // insert vertex
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        //写数据
        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        //写string数据
        uint32_t SuperSize = 10240;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'A', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void testUpdateVertex_LargeObject(
    GmcStmtT *stmt, const char *KeyName, uint32_t times, uint32_t initValue, uint32_t newValue, const char *labelname)
{
    uint32_t i = 0;
    uint32_t value = 0;

    // insert vertex
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        // 设置Filter
        value = initValue + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        //写string数据
        uint32_t SuperSize = 10240;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void testDeleteVertex_LargeObject(
    GmcStmtT *stmt, const char *KeyName, uint32_t times, uint32_t initValue, const char *labelname)
{
    uint32_t i = 0;
    uint32_t value = 0;

    // delete vertex
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

class FullLabelSubscriptionAndPush : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;
    virtual void SetUp();
    virtual void TearDown();
};

void FullLabelSubscriptionAndPush::SetUp()
{
    printf("FullLabelSubscriptionAndPush Start.\n");

    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info = NULL;

    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    char *MS_VLabel_schema = NULL;
    char *MS_VLabel_Tree_schema = NULL;

    readJanssonFile("schema_file/Vertex_01_LargeObject.gmjson", &MS_VLabel_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_schema);
    readJanssonFile("schema_file/Vertex_02_Tree.gmjson", &MS_VLabel_Tree_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_Tree_schema);

    ret = GmcCreateVertexLabel(g_stmt_sync, MS_VLabel_schema, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, MS_VLabel_Tree_schema, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    free(MS_VLabel_schema);
    free(MS_VLabel_Tree_schema);

    AW_CHECK_LOG_BEGIN();
}
void FullLabelSubscriptionAndPush::TearDown()
{
    AW_CHECK_LOG_END();
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //删表断连
    ret = GmcDropVertexLabel(g_stmt_sync, MS_VLabel_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, MS_VLabel_Tree_Name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_sub_info);
    testSnFreeUserData(user_data);
}

// 更新订阅，先写数据再订阅、更新大对象数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_V3Trans_001)
{
    int userDataIdx = 0;
    uint32_t times;
    uint32_t initValue;
    uint32_t newValue;

    // 写入数据
    times = 1;
    initValue = 1;
    testInsertVertex_LargeObject(g_stmt_sync, times, initValue, MS_VLabel_Name);

    // 创建订阅条件
    readJanssonFile("./schema_file/Vertex_01_LargeObject_subinfo_update.gmjson", &g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    ASSERT_NE((void *)NULL, g_sub_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 更新数据, 只更新大对象部分
    times = 1;
    initValue = 1;
    newValue = 100;
    ((int *)(user_data->old_value))[userDataIdx] = initValue;
    ((int *)(user_data->new_value))[userDataIdx] = initValue;
    userDataIdx++;
    testUpdateVertex_LargeObject(g_stmt_sync, MS_VLabel_Key_Name, times, initValue, newValue, MS_VLabel_Name);

    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 删除订阅，先写数据再订阅、删除大对象数据
TEST_F(FullLabelSubscriptionAndPush, SN_007_V3Trans_002)
{
    int userDataIdx = 0;
    uint32_t times;
    uint32_t initValue;
    uint32_t newValue;

    // 写入数据
    times = 1;
    initValue = 1;
    testInsertVertex_LargeObject(g_stmt_sync, times, initValue, MS_VLabel_Name);

    // 创建订阅条件
    readJanssonFile("./schema_file/Vertex_01_LargeObject_subinfo_delete.gmjson", &g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    ASSERT_NE((void *)NULL, g_sub_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 删除数据
    times = 1;
    initValue = 1;
    ((int *)(user_data->old_value))[userDataIdx] = initValue;
    userDataIdx++;
    testDeleteVertex_LargeObject(g_stmt_sync, MS_VLabel_Key_Name, times, initValue, MS_VLabel_Name);

    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 更新订阅，先写数据再订阅、将recode下的唯一字段置为null，触发推送
TEST_F(FullLabelSubscriptionAndPush, SN_007_V3Trans_003)
{
    int userDataIdx = 0;
    GmcNodeT *root = NULL, *T1 = NULL;

    // 写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, MS_VLabel_Tree_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt_sync, &root);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t value = 1;
    ret = GmcNodeSetPropertyByName(root, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    //设置T1节点
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T1, "P0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/Vertex_02_Tree_subinfo_all.gmjson", &g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_NE((void *)NULL, g_sub_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, MS_VLabel_Tree_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt_sync, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcNodeSetPropertyByName(T1,"P0",GMC_DATATYPE_UINT32,&value,sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(T1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_sync, MS_VLabel_Tree_Key_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, MS_VLabel_Tree_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_sync, MS_VLabel_Tree_Key_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt_sync, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);

    // 这里给getP0赋值，在GmcNodeGetPropertyByName校验该值，代表没有取到值。
    uint32_t getP0 = 100;
    bool isNull;
    ret = GmcNodeGetPropertyByName(T1, "P0", &getP0, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, getP0);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 三种订阅，先写数据再订阅、truncate table数据，触发推送
TEST_F(FullLabelSubscriptionAndPush, SN_007_V3Trans_004)
{
    int userDataIdx = 0;
    GmcNodeT *root = NULL, *T1 = NULL;

    // 写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, MS_VLabel_Tree_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt_sync, &root);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t value = 1;
    ret = GmcNodeSetPropertyByName(root, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    //设置T1节点
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T1, "P0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/Vertex_02_Tree_subinfo_all.gmjson", &g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_NE((void *)NULL, g_sub_info);
    ASSERT_EQ(GMERR_OK, ret);

    // Truncate数据
    ret = GmcDeleteAllFast(g_stmt_sync, MS_VLabel_Tree_Name);
    EXPECT_EQ(GMERR_OK, ret);

    //等待DELETE事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 三种订阅，先订阅再写数据、truncate table数据，触发推送
TEST_F(FullLabelSubscriptionAndPush, SN_007_V3Trans_005)
{
    int userDataIdx = 0;
    GmcNodeT *root = NULL, *T1 = NULL;

    // 创建订阅条件
    readJanssonFile("./schema_file/Vertex_02_Tree_subinfo_all.gmjson", &g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_NE((void *)NULL, g_sub_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, MS_VLabel_Tree_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt_sync, &root);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t value = 1;
    ret = GmcNodeSetPropertyByName(root, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    //设置T1节点
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T1, "P0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // Truncate数据
    ret = GmcDeleteAllFast(g_stmt_sync, MS_VLabel_Tree_Name);
    EXPECT_EQ(GMERR_OK, ret);

    //等待DELETE事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
}
