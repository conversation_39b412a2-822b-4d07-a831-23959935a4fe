[{"version": "2.0", "type": "record", "name": "Vertex_02_Tree", "fields": [{"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "uint32", "nullable": true}]}], "keys": [{"name": "Vertex_02_Tree_Key", "index": {"type": "primary"}, "node": "Vertex_02_Tree", "fields": ["PK"], "constraints": {"unique": true}, "comment": "主键索引"}]}]