[{"type": "record", "name": "labelNormal", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "string", "size": 100, "nullable": true}], "keys": [{"node": "labelNormal", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "labelNormal", "name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1"], "constraints": {"unique": true}}, {"node": "labelNormal", "name": "localhash_key", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "labelNormal", "name": "local_key", "fields": ["F1"], "index": {"type": "local"}, "constraints": {"unique": false}}], "subs_type": "message_queue"}]